# printStackTrace 问题汇总

本文档汇总了代码库中所有使用 `printStackTrace()` 的位置，这些都需要替换为适当的日志记录。

## 统计信息
- 总计发现 **1083** 个 printStackTrace 调用
- 涉及 **186** 个 Java 文件

## 需要修复的文件列表

### 饿了么配送相关
- `src/main/java/org/haier/eledelivery/service/impl/EleDeliveryServiceImpl.java`
- `src/main/java/org/haier/eledelivery/util/HttpClient.java`
- `src/main/java/org/haier/eledelivery/util/MD5Utils.java`
- `src/main/java/org/haier/eledelivery/util/ObtainTokenUtil.java`

### 饿了么监听器
- `src/main/java/org/haier/ele/listener/EleMessagePushListtener.java`
- `src/main/java/org/haier/ele/util/EleTokenUtil.java`
- `src/main/java/org/haier/ele/util/Qutil.java`

### 日志工具
- `src/main/java/org/haier/log/util/ServletUtils.java`

### 美团配送相关
- `src/main/java/org/haier/meituanpeisong/banma/util/TencentMapUtil.java`
- `src/main/java/org/haier/meituanpeisong/service/impl/PeisongOrderServiceImpl.java`

### 美团服务
- `src/main/java/org/haier/meituan/service/impl/ShopGoodsServiceImpl.java`
- `src/main/java/org/haier/meituan/service/impl/ShopMappingServiceImpl.java`
- `src/main/java/org/haier/meituan/util/MUtil.java`

### 核心框架
- `src/main/java/org/haier/shop/aop/ValidateReqAspect.java`
- `src/main/java/org/haier/shop/controller/exception/GlobalExceptionController.java`
- `src/main/java/org/haier/shop/oss/OSSUtil.java`
- `src/main/java/org/haier/shop/realm/SystemLogoutFilter.java`
- `src/main/java/org/haier/shop/redis/RedisCache.java`
- `src/main/java/org/haier/shop/redis/RedisCacheUtil.java`

### 控制器层 (Controllers) - 第1部分
- `src/main/java/org/haier/shop/controller/ActivityController.java`
- `src/main/java/org/haier/shop/controller/AdController.java`
- `src/main/java/org/haier/shop/controller/app/AppPurchaseOrderController.java`
- `src/main/java/org/haier/shop/controller/BankController.java`
- `src/main/java/org/haier/shop/controller/BeanRechargeConfigController.java`
- `src/main/java/org/haier/shop/controller/BeansController.java`
- `src/main/java/org/haier/shop/controller/BusinessController.java`
- `src/main/java/org/haier/shop/controller/config/SwiftpassConfig.java`
- `src/main/java/org/haier/shop/controller/CustomerCheckOutController.java`
- `src/main/java/org/haier/shop/controller/CustomerRechargeConfigController.java`
- `src/main/java/org/haier/shop/controller/CustomerRefundsController.java`
- `src/main/java/org/haier/shop/controller/DeliveryController.java`
- `src/main/java/org/haier/shop/controller/DictController.java`
- `src/main/java/org/haier/shop/controller/DisController.java`
- `src/main/java/org/haier/shop/controller/ExpressController.java`
- `src/main/java/org/haier/shop/controller/FarmController.java`
- `src/main/java/org/haier/shop/controller/FarmProductController.java`
- `src/main/java/org/haier/shop/controller/GasOilStationControlle.java`
- `src/main/java/org/haier/shop/controller/GlobalThemeController.java`
- `src/main/java/org/haier/shop/controller/GoldController.java`
- `src/main/java/org/haier/shop/controller/GoodsController.java`
- `src/main/java/org/haier/shop/controller/GoodsDictController.java`
- `src/main/java/org/haier/shop/controller/GoodsShelfStateController.java`
- `src/main/java/org/haier/shop/controller/h5/RegisterController.java`
- `src/main/java/org/haier/shop/controller/InventoryController.java`
- `src/main/java/org/haier/shop/controller/InventoryTaskController.java`
- `src/main/java/org/haier/shop/controller/LogisticsController.java`
- `src/main/java/org/haier/shop/controller/LTController.java`
- `src/main/java/org/haier/shop/controller/ManagerController.java`
- `src/main/java/org/haier/shop/controller/PayCallbackController.java`
- `src/main/java/org/haier/shop/controller/PayTypeController.java`
- `src/main/java/org/haier/shop/controller/pc/CdkeyCodeController.java`
- `src/main/java/org/haier/shop/controller/PeisongController.java`
- `src/main/java/org/haier/shop/controller/PurchaseListController.java`
- `src/main/java/org/haier/shop/controller/PurchaseOrderController.java`
- `src/main/java/org/haier/shop/controller/RotationController.java`
- `src/main/java/org/haier/shop/controller/SalelistController.java`
- `src/main/java/org/haier/shop/controller/SelfPurchaseController.java`
- `src/main/java/org/haier/shop/controller/ShopAddressController.java`
- `src/main/java/org/haier/shop/controller/ShopBatchUtilController.java`
- `src/main/java/org/haier/shop/controller/ShopController.java`
- `src/main/java/org/haier/shop/controller/ShopDeviceController.java`
- `src/main/java/org/haier/shop/controller/ShopSoftController.java`
- `src/main/java/org/haier/shop/controller/ShopStatisticsController.java`
- `src/main/java/org/haier/shop/controller/StaffController.java`
- `src/main/java/org/haier/shop/controller/SupplierShoppingController.java`
- `src/main/java/org/haier/shop/controller/SysAgreementController.java`
- `src/main/java/org/haier/shop/controller/SysLoginController.java`
- `src/main/java/org/haier/shop/controller/SysRoleController.java`
- `src/main/java/org/haier/shop/controller/TestController.java`
- `src/main/java/org/haier/shop/controller/TVController.java`
- `src/main/java/org/haier/shop/controller/UnicomAbleController.java`
- `src/main/java/org/haier/shop/controller/UnicomMonitorInfoController.java`
- `src/main/java/org/haier/shop/controller/UtilController.java`
- `src/main/java/org/haier/shop/controller/WeChatController.java`
- `src/main/java/org/haier/shop/controller/WeiShiPingController.java`

### 服务层 (Services) - 第1部分
- `src/main/java/org/haier/shop/service/ActivityServiceImpl.java`
- `src/main/java/org/haier/shop/service/AdServiceImpl.java`
- `src/main/java/org/haier/shop/service/BankServiceImpl.java`
- `src/main/java/org/haier/shop/service/BeanRechargeConfigServiceImpl.java`
- `src/main/java/org/haier/shop/service/BeanServiceImpl.java`
- `src/main/java/org/haier/shop/service/BranchStoreServiceImpl.java`
- `src/main/java/org/haier/shop/service/BusinessServiceImpl.java`
- `src/main/java/org/haier/shop/service/CustomerCheckOutServiceImpl.java`
- `src/main/java/org/haier/shop/service/DataScreenServiceImpl.java`
- `src/main/java/org/haier/shop/service/DepositServiceImpl.java`
- `src/main/java/org/haier/shop/service/DisServiceImpl.java`
- `src/main/java/org/haier/shop/service/ExpressServiceImpl.java`
- `src/main/java/org/haier/shop/service/FarmProductService.java`
- `src/main/java/org/haier/shop/service/FeedBackServiceImpl.java`
- `src/main/java/org/haier/shop/service/FtpUtils.java`
- `src/main/java/org/haier/shop/service/GasOilServiceImpl.java`
- `src/main/java/org/haier/shop/service/GlobalThemeService.java`
- `src/main/java/org/haier/shop/service/GoodsBatchServiceImpl.java`
- `src/main/java/org/haier/shop/service/GoodsBindingServiceImpl.java`
- `src/main/java/org/haier/shop/service/GoodsDictServiceImpl.java`
- `src/main/java/org/haier/shop/service/GoodsKindIconServiceImpl.java`
- `src/main/java/org/haier/shop/service/GoodsKindServiceImpl.java`
- `src/main/java/org/haier/shop/service/GoodsServiceImpl.java`
- `src/main/java/org/haier/shop/service/GoodsShelfStateServiceImpl.java`
- `src/main/java/org/haier/shop/service/H5ShopRegisterServiceImpl.java`

### 服务层 (Services) - 第2部分
- `src/main/java/org/haier/shop/service/IMPORT.java`
- `src/main/java/org/haier/shop/service/ImportServiceImpl.java`
- `src/main/java/org/haier/shop/service/InfoPushServiceImpl.java`
- `src/main/java/org/haier/shop/service/InventoryTaskServiceImpl.java`
- `src/main/java/org/haier/shop/service/LoanMoneyServiceImpl.java`
- `src/main/java/org/haier/shop/service/LTServiceImpl.java`
- `src/main/java/org/haier/shop/service/MonitorInfoServiceImpl.java`
- `src/main/java/org/haier/shop/service/MqttServiceImpl.java`
- `src/main/java/org/haier/shop/service/NoticeServiceImpl.java`
- `src/main/java/org/haier/shop/service/PayTypeServiceImpl.java`
- `src/main/java/org/haier/shop/service/PurchaseListServiceImpl.java`
- `src/main/java/org/haier/shop/service/SaleListServiceImpl.java`
- `src/main/java/org/haier/shop/service/SelfPurchaseServiceImpl.java`
- `src/main/java/org/haier/shop/service/ShopDeviceServiceImpl.java`
- `src/main/java/org/haier/shop/service/ShopServiceImpl.java`
- `src/main/java/org/haier/shop/service/ShopSoftServiceImpl.java`
- `src/main/java/org/haier/shop/service/ShopTitleServiceImpl.java`
- `src/main/java/org/haier/shop/service/StaffServiceImpl.java`
- `src/main/java/org/haier/shop/service/StockServiceImpl.java`
- `src/main/java/org/haier/shop/service/SupOrderServiceImpl.java`
- `src/main/java/org/haier/shop/service/SupplierInfoServiceImpl.java`
- `src/main/java/org/haier/shop/service/SupplierServiceImpl.java`
- `src/main/java/org/haier/shop/service/SupplierShoppingService.java`
- `src/main/java/org/haier/shop/service/supplier/ShopSupBillServiceImpl.java`
- `src/main/java/org/haier/shop/service/SystemManagerServiceImpl.java`
- `src/main/java/org/haier/shop/service/TVServiceImpl.java`
- `src/main/java/org/haier/shop/service/UnicomAbleServiceImpl.java`
- `src/main/java/org/haier/shop/service/UpdateServiceImpl.java`
- `src/main/java/org/haier/shop/service/UtilServiceImpl.java`
- `src/main/java/org/haier/shop/service/WeishiProductService.java`

### 定时任务 (Tasks)
- `src/main/java/org/haier/shop/task/DataDelivery.java`
- `src/main/java/org/haier/shop/task/DataScreen.java`
- `src/main/java/org/haier/shop/task/DisAutoDown.java`
- `src/main/java/org/haier/shop/task/FtpUtils.java`
- `src/main/java/org/haier/shop/task/GoodsAutoPurchase.java`
- `src/main/java/org/haier/shop/task/LakalaTask.java`
- `src/main/java/org/haier/shop/task/TVDownload.java`
- `src/main/java/org/haier/shop/task/UnicomRedPacket.java`

### 测试类
- `src/main/java/org/haier/shop/test/MyThread.java`
- `src/main/java/org/haier/shop/test/TestClass.java`

### 线程类
- `src/main/java/org/haier/shop/thread/NYCustomerRechargeThread.java`
- `src/main/java/org/haier/shop/thread/SortThread.java`

### 工具类 (Utils) - 第1部分
- `src/main/java/org/haier/shop/util/AddOrderTask.java`
- `src/main/java/org/haier/shop/util/AlipayConfig.java`
- `src/main/java/org/haier/shop/util/BarCodeImage.java`
- `src/main/java/org/haier/shop/util/Base64DecodeMultipartFile.java`
- `src/main/java/org/haier/shop/util/ChineseFristLetter.java`
- `src/main/java/org/haier/shop/util/common/HttpDownloadUtil.java`
- `src/main/java/org/haier/shop/util/CreateLine.java`
- `src/main/java/org/haier/shop/util/DateUtils.java`
- `src/main/java/org/haier/shop/util/excel/FileMd5Util.java`
- `src/main/java/org/haier/shop/util/excel/inpl/ExcelOutput.java`
- `src/main/java/org/haier/shop/util/excel/inpl/ExcelOutputSheet.java`
- `src/main/java/org/haier/shop/util/excel/inpl/ExcelReader.java`
- `src/main/java/org/haier/shop/util/Excel.java`
- `src/main/java/org/haier/shop/util/ExcelUtil.java`
- `src/main/java/org/haier/shop/util/ExcelUtilNew.java`
- `src/main/java/org/haier/shop/util/ExtractUtils.java`
- `src/main/java/org/haier/shop/util/FTPConfig.java`
- `src/main/java/org/haier/shop/util/GoodsImport.java`
- `src/main/java/org/haier/shop/util/HandleMessyCode.java`
- `src/main/java/org/haier/shop/util/HttpClientUtil.java`
- `src/main/java/org/haier/shop/util/HttpGetUtil.java`
- `src/main/java/org/haier/shop/util/HttpsUtil.java`
- `src/main/java/org/haier/shop/util/ImageBinary.java`
- `src/main/java/org/haier/shop/util/ImageZipUtils.java`
- `src/main/java/org/haier/shop/util/IPGet.java`

## 修复建议

1. **替换为日志记录**: 将所有 `e.printStackTrace()` 替换为适当的日志记录，如 `logger.error("错误描述", e)`
2. **优先级排序**:
   - 高优先级：核心框架、控制器层、服务层
   - 中优先级：工具类、定时任务
   - 低优先级：测试类、展示工具
3. **统一日志框架**: 确保所有文件使用统一的日志框架（如 SLF4J + Logback）
4. **添加有意义的错误信息**: 不仅记录异常堆栈，还要添加业务上下文信息

## 注意事项

- 修复时需要确保不影响现有业务逻辑
- 建议分批次进行修复，避免一次性修改过多文件
- 修复后需要进行充分的测试验证
