# 业务流程文档

本文档描述了金圈平台的核心业务流程和业务逻辑。

## 目录
- [1. 用户管理流程](#1-用户管理流程)
- [2. 店铺管理流程](#2-店铺管理流程)
- [3. 商品管理流程](#3-商品管理流程)
- [4. 订单管理流程](#4-订单管理流程)
- [5. 支付流程](#5-支付流程)
- [6. 配送管理流程](#6-配送管理流程)
- [7. 库存管理流程](#7-库存管理流程)
- [8. 会员管理流程](#8-会员管理流程)
- [9. 供货商管理流程](#9-供货商管理流程)
- [10. 财务管理流程](#10-财务管理流程)
- [11. 数据统计分析流程](#11-数据统计分析流程)

---

## 1. 用户管理流程

### 1.1 用户注册流程
> **核心代码**: [`ShopServiceImpl.java:764`](../src/main/java/org/haier/shop/service/ShopServiceImpl.java#L764)

**业务流程**:
1. 用户填写注册信息（店铺名称、管理员账号、密码、地址、电话等）
2. 系统验证信息完整性和唯一性
3. 生成店铺唯一标识（shop_unique）
4. 创建店铺基础信息记录
5. 设置初始审核状态（待审核）
6. 发送注册成功通知

**关键参数**:
- `shop_name`: 店铺名称
- `manager_account`: 管理员账号
- `manager_pwd`: 管理员密码（MD5加密）
- `shop_address_detail`: 店铺详细地址
- `shop_phone`: 店铺联系电话
- `examinestatus`: 审核状态（1:未提交，2:已提交，3:未通过，4:通过，5:已撤回）

### 1.2 用户登录流程
> **核心代码**: [`CustomRealm.java:112`](../src/main/java/org/haier/shop/realm/CustomRealm.java#L112) | [`SysLoginController.java:113`](../src/main/java/org/haier/shop/controller/SysLoginController.java#L113)

**业务流程**:
1. 用户输入账号密码
2. 系统验证账号是否存在
3. 验证密码是否正确（MD5加密对比）
4. 检查账号审核状态（必须为已通过状态）
5. 创建用户会话（Session）
6. 记录登录日志
7. 返回登录成功信息

**安全机制**:
- 使用Apache Shiro进行身份认证
- 密码MD5加密存储
- 会话管理和权限控制

### 1.3 员工管理流程
> **核心代码**: [`StaffController.java:306`](../src/main/java/org/haier/shop/controller/StaffController.java#L306) | [`StaffServiceImpl.java:40`](../src/main/java/org/haier/shop/service/StaffServiceImpl.java#L40)

**业务流程**:
1. **添加员工**: 管理员创建员工账号，设置基本信息和权限
2. **员工登录**: 员工使用分配的账号密码登录系统
3. **权限管理**: 根据员工职位分配不同的系统权限
4. **业绩统计**: 系统记录和统计员工销售业绩

---

## 2. 店铺管理流程

### 2.1 店铺审核流程
> **核心代码**: [`ShopController.java:1022`](../src/main/java/org/haier/shop/controller/ShopController.java#L1022)

**业务流程**:
1. 店铺提交注册申请
2. 平台管理员审核店铺资质
3. 审核通过后，店铺状态变更为"已通过"
4. 店铺可正常使用平台功能
5. 审核不通过，返回原因并允许重新提交

**审核状态**:
- `1`: 未提交
- `2`: 已提交
- `3`: 未通过
- `4`: 通过
- `5`: 已撤回

### 2.2 店铺功能配置流程
> **核心代码**: [`ShopController.java:463`](../src/main/java/org/haier/shop/controller/ShopController.java#L463)

**业务流程**:
1. 店铺管理员进入功能配置页面
2. 选择需要开启的业务功能模块
3. 配置相关参数（如自动采购天数、缺货预警等）
4. 保存配置信息
5. 系统根据配置启用相应功能

**可配置功能**:
- 鲜花业务、送水业务、洗衣业务
- 快递业务、家政业务、蛋糕业务
- 水果业务、采购功能
- 负库存销售、低于成本价销售
- 自动采购、缺货预警等

---

## 3. 商品管理流程

### 3.1 商品添加流程
> **核心代码**: [`GoodsController.java:484`](../src/main/java/org/haier/shop/controller/GoodsController.java#L484) | [`GoodsServiceImpl.java:2110`](../src/main/java/org/haier/shop/service/GoodsServiceImpl.java#L2110)

**业务流程**:
1. 店铺管理员录入商品基本信息
2. 设置商品分类、品牌、价格等属性
3. 上传商品图片（可选）
4. 设置库存数量和供货商信息
5. 系统生成商品唯一标识
6. 商品信息同步到云库（如果启用）

**关键信息**:
- `goods_barcode`: 商品条码（唯一标识）
- `goods_name`: 商品名称
- `goods_in_price`: 进货价
- `goods_sale_price`: 销售价
- `goods_count`: 库存数量
- `default_supplier_unique`: 默认供货商

### 3.2 商品分类管理流程
> **核心代码**: [`GoodsKindController.java:111`](../src/main/java/org/haier/shop/controller/GoodsKindController.java#L111)

**业务流程**:
1. 创建商品分类层级结构（一级、二级分类）
2. 为商品分配对应分类
3. 支持分类的增删改查操作
4. 分类信息用于商品检索和统计

### 3.3 库存管理流程
> **核心代码**: [`InventoryManagerServiceImpl.java:46`](../src/main/java/org/haier/shop/service/InventoryManagerServiceImpl.java#L46)

**业务流程**:
1. **入库管理**: 商品采购入库，更新库存数量
2. **出库管理**: 销售出库，减少库存数量
3. **库存盘点**: 定期盘点实际库存
4. **预警机制**: 库存不足时自动预警
5. **批次管理**: 支持先进先出等库存策略

**库存策略**:
- `0`: 最近入库价
- `1`: 移动加权平均
- `2`: 先进先出

---

## 4. 订单管理流程

### 4.1 销售订单流程
> **核心代码**: [`SalelistController.java:1220`](../src/main/java/org/haier/shop/controller/SalelistController.java#L1220)

**业务流程**:
1. **订单创建**: 客户选择商品，生成销售订单
2. **订单确认**: 店员确认订单信息和库存
3. **支付处理**: 客户完成支付（现金/移动支付）
4. **订单履行**: 准备商品，安排配送或自提
5. **订单完成**: 客户确认收货，订单状态更新

**订单状态**:
- 待处理、已确认、配送中、已完成、已取消

### 4.2 采购订单流程
> **核心代码**: [`SupplierShoppingController.java:1305`](../src/main/java/org/haier/shop/controller/SupplierShoppingController.java#L1305) | [`SupplierShoppingService.java:1537`](../src/main/java/org/haier/shop/service/SupplierShoppingService.java#L1537)

**业务流程**:
1. **需求分析**: 系统分析库存情况，生成采购需求
2. **供货商选择**: 选择合适的供货商
3. **订单创建**: 创建采购订单，包含商品清单和数量
4. **订单提交**: 向供货商提交采购订单
5. **订单跟踪**: 跟踪订单状态（待发货、配送中、已到货）
6. **收货确认**: 确认收货，更新库存
7. **财务结算**: 处理采购款项结算

**支付方式**:
- 现金支付
- 金圈币支付
- 赊销（支持分期）

### 4.3 外卖订单流程
> **核心代码**: [`TakeoutOrderContorller.java:35`](../src/main/java/org/haier/meituan/controller/TakeoutOrderContorller.java#L35) | [`EleOrderServiceImpl.java:104`](../src/main/java/org/haier/ele/service/impl/EleOrderServiceImpl.java#L104)

**业务流程**:
1. **订单接收**: 从美团、饿了么等平台接收外卖订单
2. **订单处理**: 店铺确认接单，准备商品
3. **配送安排**: 选择配送方式（自配送/第三方配送）
4. **订单跟踪**: 实时跟踪订单状态
5. **订单完成**: 客户确认收货

---

## 5. 支付流程

### 5.1 微信支付流程
> **核心代码**: [`BeansController.java:728`](../src/main/java/org/haier/shop/controller/BeansController.java#L728)

**业务流程**:
1. 客户选择微信支付
2. 系统生成支付订单
3. 调用微信支付API，生成支付二维码
4. 客户扫码完成支付
5. 微信回调通知支付结果
6. 系统更新订单状态
7. 发送支付成功通知

### 5.2 合利宝支付流程
> **核心代码**: [`SupplierShoppingController.java:298`](../src/main/java/org/haier/shop/controller/SupplierShoppingController.java#L298)

**业务流程**:
1. 客户选择合利宝支付（支持支付宝、微信）
2. 系统调用合利宝API创建支付订单
3. 生成支付二维码或跳转支付页面
4. 客户完成支付
5. 合利宝回调通知支付结果
6. 系统处理支付结果，更新订单状态

### 5.3 金圈币支付流程
> **核心代码**: [`SupplierShoppingService.java:1537`](../src/main/java/org/haier/shop/service/SupplierShoppingService.java#L1537)

**业务流程**:
1. 客户选择使用金圈币支付
2. 系统验证金圈币余额
3. 扣除相应金圈币数量
4. 更新客户金圈币账户
5. 完成支付，更新订单状态

---

## 6. 配送管理流程

### 6.1 美团配送流程
> **核心代码**: [`PeisongOrderController.java:27`](../src/main/java/org/haier/meituanpeisong/controller/PeisongOrderController.java#L27)

**业务流程**:
1. 订单确认后，系统判断是否使用美团配送
2. 调用美团配送API创建配送订单
3. 美团分配配送员
4. 实时跟踪配送状态
5. 配送完成，更新订单状态

### 6.2 蜂鸟配送流程
> **核心代码**: [`EleDeliveryOrderController.java:28`](../src/main/java/org/haier/eledelivery/controller/EleDeliveryOrderController.java#L28)

**业务流程**:
1. 商户请求推单到蜂鸟配送平台
2. 蜂鸟平台接收订单并分配配送员
3. 配送员取货并开始配送
4. 实时同步配送状态
5. 配送完成或异常处理

**配送状态回调**:
> **核心代码**: [`EleDeliveryCallBackController.java:26`](../src/main/java/org/haier/eledelivery/controller/EleDeliveryCallBackController.java#L26)

### 6.3 自配送流程
> **核心代码**: [`PeisongController.java:72`](../src/main/java/org/haier/shop/controller/PeisongController.java#L72)

**业务流程**:
1. 店铺选择自配送模式
2. 分配店内配送员
3. 配送员接单并配送
4. 手动更新配送状态
5. 确认配送完成

---

## 7. 库存管理流程

### 7.1 库存入库流程
> **核心代码**: [`InventoryManagerService.java:25`](../src/main/java/org/haier/shop/service/InventoryManagerService.java#L25)

**业务流程**:
1. 采购商品到货
2. 验收商品质量和数量
3. 录入入库信息（商品、数量、批次、成本价）
4. 更新库存数量
5. 生成入库单据

### 7.2 库存出库流程
> **核心代码**: [`InventoryManagerService.java:32`](../src/main/java/org/haier/shop/service/InventoryManagerService.java#L32)

**业务流程**:
1. 销售订单确认
2. 检查库存是否充足
3. 按照出库策略选择批次（先进先出等）
4. 扣减库存数量
5. 生成出库单据

### 7.3 库存预警流程
**业务流程**:
1. 系统定期检查商品库存
2. 对比预警阈值
3. 库存不足时发送预警通知
4. 自动生成采购建议（如果启用自动采购）

---

## 8. 会员管理流程

### 8.1 会员注册流程
> **核心代码**: [`CustomerCheckOutController.java:1683`](../src/main/java/org/haier/shop/controller/CustomerCheckOutController.java#L1683) | [`CustomerCheckOutServiceImpl.java:997`](../src/main/java/org/haier/shop/service/CustomerCheckOutServiceImpl.java#L997)

**业务流程**:
1. 客户提供基本信息（姓名、电话、地址等）
2. 店员录入会员信息
3. 选择会员类型（普通会员、储值会员、共享会员）
4. 设置会员等级和权限
5. 生成会员卡号
6. 发放会员卡

**会员类型**:
- `会`: 普通会员
- `储`: 储值会员
- `共`: 共享会员（同时创建普通和储值账户）

### 8.2 会员充值流程
> **核心代码**: [`CustomerCheckOutServiceImpl.java:1047`](../src/main/java/org/haier/shop/service/CustomerCheckOutServiceImpl.java#L1047)

**业务流程**:
1. 会员选择充值金额
2. 选择支付方式完成支付
3. 系统更新会员账户余额
4. 记录充值流水
5. 发送充值成功通知

### 8.3 会员消费流程
**业务流程**:
1. 会员购买商品时出示会员卡
2. 系统识别会员身份
3. 计算会员折扣和积分
4. 扣减会员余额（如使用储值）
5. 更新会员积分和消费记录

### 8.4 会员等级管理
> **核心代码**: [`CustomerCheckOutServiceImpl.java:1208`](../src/main/java/org/haier/shop/service/CustomerCheckOutServiceImpl.java#L1208)

**业务流程**:
1. 根据会员消费金额自动升级等级
2. 不同等级享受不同折扣和权益
3. 支持手动调整会员等级
4. 等级变更通知

---

## 9. 供货商管理流程

### 9.1 供货商注册流程
> **核心代码**: [`ShopSupplierServiceImpl.java:232`](../src/main/java/org/haier/shop/service/supplier/ShopSupplierServiceImpl.java#L232)

**业务流程**:
1. 供货商提交申请信息
2. 填写企业资质和联系方式
3. 平台审核供货商资质
4. 审核通过后，供货商可以上架商品
5. 建立供货关系

### 9.2 供货商商品管理流程
> **核心代码**: [`ShopSupplierServiceImpl.java:395`](../src/main/java/org/haier/shop/service/supplier/ShopSupplierServiceImpl.java#L395)

**业务流程**:
1. 供货商上传商品信息
2. 设置商品价格和库存
3. 店铺选择需要的商品
4. 建立供货关系
5. 定期更新商品信息和价格

### 9.3 供货商绑定流程
> **核心代码**: [`ShopSupplierServiceImpl.java:592`](../src/main/java/org/haier/shop/service/supplier/ShopSupplierServiceImpl.java#L592)

**业务流程**:
1. 店铺搜索并选择供货商
2. 发送合作申请
3. 供货商确认合作
4. 系统建立供货关系
5. 开始商品供应合作

---

## 10. 财务管理流程

### 10.1 店铺结算流程
> **核心代码**: [`ShopServiceImpl.java:1814`](../src/main/java/org/haier/shop/service/ShopServiceImpl.java#L1814)

**业务流程**:
1. 店铺申请提现
2. 系统验证账户余额
3. 计算手续费
4. 生成提现订单
5. 财务审核
6. 转账到店铺银行账户

### 10.2 百货豆管理流程
> **核心代码**: [`BeanServiceImpl.java:626`](../src/main/java/org/haier/shop/service/BeanServiceImpl.java#L626)

**业务流程**:
1. 客户消费获得百货豆奖励
2. 百货豆可用于抵扣消费
3. 店铺可申请百货豆提现
4. 系统按比例兑换现金
5. 更新百货豆账户余额

### 10.3 赊销管理流程
> **核心代码**: [`LoanMoneyController.java:163`](../src/main/java/org/haier/shop/controller/LoanMoneyController.java#L163)

**业务流程**:
1. 客户申请赊销额度
2. 系统评估信用状况
3. 批准赊销额度
4. 客户使用赊销购买商品
5. 定期还款
6. 逾期处理和催收

---

## 11. 数据统计分析流程

### 11.1 销售统计流程
> **核心代码**: [`SaleListServiceImpl.java:59`](../src/main/java/org/haier/shop/service/SaleListServiceImpl.java#L59) | [`CountMsgYiNongServiceImpl.java:266`](../src/main/java/org/haier/shop/service/CountMsgYiNongServiceImpl.java#L266)

**业务流程**:
1. 系统实时收集销售数据
2. 按时间维度统计销售额和销量
3. 生成销售趋势图表
4. 对比历史同期数据
5. 生成销售分析报告

### 11.2 商品分析流程
> **核心代码**: [`DataGoodsController.java:16`](../src/main/java/org/haier/shop/controller/DataGoodsController.java#L16)

**业务流程**:
1. 统计商品销量排行
2. 分析商品热力图
3. 客户年龄分布分析
4. 消费能力分析
5. 生成商品推荐策略

### 11.3 财务报表流程
**业务流程**:
1. 收集各类财务数据
2. 生成收入支出报表
3. 计算利润和成本
4. 分析财务趋势
5. 生成财务分析报告

---

## 业务流程总结

金圈平台是一个综合性的零售管理系统，涵盖了从用户注册、商品管理、订单处理、支付结算到配送履行的完整业务链条。系统支持多种业务模式：

1. **B2C零售**: 直接面向消费者的零售业务
2. **B2B供应**: 店铺之间的供货业务
3. **O2O服务**: 线上下单线下服务的业务模式
4. **外卖配送**: 与第三方平台集成的外卖业务

系统具有高度的可配置性和扩展性，能够适应不同规模和类型的零售业务需求。通过完善的权限管理、财务结算和数据分析功能，为商户提供全方位的经营管理支持。