# MQ消息队列业务逻辑文档

本文档详细分析项目中涉及WEBSOCKET、SOCKET、MQTT、消息推送等MQ相关的业务逻辑，包括配置、连接管理、消息发送、消息接收等核心功能。

## 目录
- [1. 概述](#1-概述)
- [2. MQTT消息系统](#2-mqtt消息系统)
- [3. GoEasy实时消息推送](#3-goeasy实时消息推送)
- [4. WebSocket通信系统](#4-websocket通信系统)
- [5. 饿了么消息推送监听](#5-饿了么消息推送监听)
- [6. 消息业务场景](#6-消息业务场景)
- [7. 异步线程处理](#7-异步线程处理)
- [8. 配置管理](#8-配置管理)
- [9. 监听器管理](#9-监听器管理)
- [10. 极光推送系统](#10-极光推送系统)
- [11. 信息推送系统](#11-信息推送系统)
- [12. 消息队列定时任务](#12-消息队列定时任务)
- [13. 消息流转架构图](#13-消息流转架构图)
- [14. 总结](#14-总结)

## 1. 概述

项目采用多种消息传递机制来实现不同业务场景的消息通信：

### 1.1 消息系统架构
- **MQTT消息系统**: 用于与收银设备的双向通信
- **GoEasy实时推送**: 用于Web端实时消息通知
- **WebSocket通信**: 用于电视设备的实时控制和图片传输
- **饿了么消息监听**: 处理外卖平台订单状态变更（已禁用）
- **极光推送**: 用于移动端消息推送
- **异步线程处理**: 用于后台任务和消息处理

### 1.2 主要业务场景
- 商品信息同步到收银设备
- 订单状态实时通知
- 电视设备远程控制和截图
- 外卖平台订单处理
- 系统消息推送
- 设备升级通知
- 支付结果通知

### 1.3 技术栈说明
**注意**: 项目中虽然文档标题提到RabbitMQ，但实际代码中并未使用RabbitMQ、ActiveMQ、Kafka等传统消息队列中间件。主要使用的是：
- Eclipse Paho MQTT客户端
- GoEasy云端推送服务
- 极光推送服务
- 原生WebSocket
- Java多线程处理

## 2. MQTT消息系统

### 2.1 MQTT客户端配置

#### 2.1.1 配置文件
**文件路径：** [`src/main/resources/mqtt.properties`](../src/main/resources/mqtt.properties)

<augment_code_snippet path="src/main/resources/mqtt.properties" mode="EXCERPT">
````properties
mqtt.host=tcp://**************:1883
mqtt.TOPICMAIN=win_qt_cash_1.0
mqtt.topic=win_qt_cash_
##以服务器IP地址为尾号
mqtt.clientid=client_shop
mqtt.mqttpubkey=topic_
mqtt.logDayCount=logDayCount_
````
</augment_code_snippet>

#### 2.1.2 连接参数说明
- **mqtt.host**: MQTT服务器地址和端口
- **mqtt.TOPICMAIN**: 主订阅主题，用于接收消息
- **mqtt.topic**: 发送消息的主题前缀
- **mqtt.clientid**: 客户端ID前缀，会自动添加服务器IP
- **mqtt.mqttpubkey**: Redis中存储设备MAC地址的键前缀

### 2.2 MQTT客户端实现

#### 2.2.1 ClientMQTT核心类
**文件路径：** [`src/main/java/org/haier/shop/util/mqtt/ClientMQTT.java`](../src/main/java/org/haier/shop/util/mqtt/ClientMQTT.java)

<augment_code_snippet path="src/main/java/org/haier/shop/util/mqtt/ClientMQTT.java" mode="EXCERPT">
````java
@Component
public class ClientMQTT {
    private static String HOST;
    private static String TOPICMAIN;
    private static String TOPIC;
    private static String clientid;
    private static String MQTTPUBKEY;
    private static String LOGDAYCOUNT;

    private MqttClient client;
    private MqttConnectOptions options;
    private String userName = "mqtt";
    private String passWord = "mqtt";

    public static RedisCache redis;
````
</augment_code_snippet>

#### 2.2.2 连接初始化
**执行代码：** [`ClientMQTT.java:47-88`](../src/main/java/org/haier/shop/util/mqtt/ClientMQTT.java#L47-L88)

<augment_code_snippet path="src/main/java/org/haier/shop/util/mqtt/ClientMQTT.java" mode="EXCERPT">
````java
public void start() {
    try {
        // 创建MQTT客户端
        client = new MqttClient(HOST, clientid, new MemoryPersistence());

        // 配置连接选项
        options = new MqttConnectOptions();
        options.setCleanSession(false);  // 保留会话
        options.setUserName(userName);
        options.setPassword(passWord.toCharArray());
        options.setConnectionTimeout(10);  // 连接超时10秒
        options.setKeepAliveInterval(20);  // 心跳间隔20秒
        options.setAutomaticReconnect(true);  // 自动重连

        // 设置回调处理器
        client.setCallback(new PushCallback(redis,client,options));

        // 设置遗嘱消息
        MqttTopic topic = client.getTopic(TOPICMAIN);
        options.setWill(topic, "close".getBytes(), 1, true);

        // 连接并订阅主题
        client.connect(options);
        int[] Qos = {2}; // QoS级别：只有一次
        String[] topic1 = {TOPICMAIN};
        client.subscribe(topic1, Qos);
    } catch (Exception e) {
        System.err.println("----连接MQTT----异常：" + e.getMessage());
        e.printStackTrace();
    }
}
````
</augment_code_snippet>

#### 2.2.3 消息发送
**执行代码：** [`ClientMQTT.java:91-99`](../src/main/java/org/haier/shop/util/mqtt/ClientMQTT.java#L91-L99)

<augment_code_snippet path="src/main/java/org/haier/shop/util/mqtt/ClientMQTT.java" mode="EXCERPT">
````java
public void sendMsg(String msg,String topic) {
    try {
        MqttMessage message = new MqttMessage(msg.getBytes());
        message.setQos(1);  // QoS级别：最少一次
        client.publish(TOPIC + topic, message);  // 发送到指定主题
    }catch (Exception e) {
        e.printStackTrace();
    }
}
````
</augment_code_snippet>

### 2.3 MQTT工具类

#### 2.3.1 MqttxUtil工具类
**文件路径：** [`src/main/java/org/haier/shop/util/mqtt/MqttxUtil.java`](../src/main/java/org/haier/shop/util/mqtt/MqttxUtil.java)

<augment_code_snippet path="src/main/java/org/haier/shop/util/mqtt/MqttxUtil.java" mode="EXCERPT">
````java
@Component
public class MqttxUtil {
    @Autowired
    private RedisCache rc;

    public static ClientMQTT cm = new ClientMQTT();

    @PostConstruct
    public void startCM() {
        if(null != rc) {
            ClientMQTT.redis = rc;
        }
        cm.start();  // 启动MQTT客户端
    }

    // 发送字符串消息
    public static void sendMsg(String msg,String macId) {
        System.out.println("本次发送的消息" + msg);
        cm.sendMsg(msg,macId);
    }

    // 发送Map对象消息（转换为JSON）
    public static void sendMapMsg(Map<String,Object> map,String macId) {
        JSONObject jo = JSONObject.fromObject(map);
        sendMsg(jo.toString(),macId);
    }
}
````
</augment_code_snippet>

### 2.4 MQTT回调处理

#### 2.4.1 PushCallback回调类
**文件路径：** [`src/main/java/org/haier/shop/util/mqtt/PushCallback.java`](../src/main/java/org/haier/shop/util/mqtt/PushCallback.java)

<augment_code_snippet path="src/main/java/org/haier/shop/util/mqtt/PushCallback.java" mode="EXCERPT">
````java
public class PushCallback implements MqttCallback{
    private MqttClient client;
    private MqttConnectOptions options;
    private RedisCache redis;

    // 连接丢失处理
    public void connectionLost(Throwable cause) {
        System.out.println("连接断开，可以做重连");
        if(null != client && null != options) {
            try {
                System.out.println("开始重连");
                Thread.sleep(3000);  // 等待3秒后重连
                client.connect(options);
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
    }

    // 消息发送完成
    public void deliveryComplete(IMqttDeliveryToken token) {
        System.out.println("deliveryComplete---------" + token.isComplete());
    }

    // 消息到达处理
    public void messageArrived(String topic, MqttMessage message) throws Exception {
        // 此处不再对接受的消息进行处理，所有的消息放在shopmanager项目进行处理
    }
}
````
</augment_code_snippet>

### 2.5 MQTT服务接口

#### 2.5.1 MqttService接口
**文件路径：** [`src/main/java/org/haier/shop/service/MqttService.java`](../src/main/java/org/haier/shop/service/MqttService.java)

<augment_code_snippet path="src/main/java/org/haier/shop/service/MqttService.java" mode="EXCERPT">
````java
/**
 * @Description MQTT服务
 * @ClassName MqttService
 * <AUTHOR>
 * @Date 2024/9/24 17:50
 **/
public interface MqttService {

    /**
     * 发送mqtt商品更新通知
     * @param goodsUpdates
     * @param shopUnique
     */
    void sendGoodsUpdate(List<MqttForGoodsUpdate> goodsUpdates, String shopUnique);
}
````
</augment_code_snippet>

#### 2.5.2 MqttServiceImpl实现类
**文件路径：** [`src/main/java/org/haier/shop/service/MqttServiceImpl.java`](../src/main/java/org/haier/shop/service/MqttServiceImpl.java)

<augment_code_snippet path="src/main/java/org/haier/shop/service/MqttServiceImpl.java" mode="EXCERPT">
````java
@Service("mqttServiceImpl")
public class MqttServiceImpl implements MqttService {

    @Resource
    private RedisCache redis;

    @Override
    public void sendGoodsUpdate(List<MqttForGoodsUpdate> goodsUpdates, String shopUnique) {
        try {
            //获取需要通知的店铺列表
            Object mac = redis.getObject("topic_" + shopUnique);
            if(null != mac) {
                List<String> macIdList = (List<String>) mac;
                // 2 MQTT 发送消息
                for (String macid : macIdList) {
                    //依次发送MQTT消息
                    Map<String, Object> data = new HashMap<String, Object>();
                    data.put("ctrl", "msg_goods_update");
                    data.put("ID", macid);
                    data.put("status", 200);
                    data.put("data", goodsUpdates);
                    data.put("count", goodsUpdates.size());
                    MqttxUtil.sendMapMsg(data, macid);
                }
            }
        }catch (Exception e) {
            e.printStackTrace();
        }
    }
}
````
</augment_code_snippet>

### 2.6 MQTT消息数据模型

#### 2.6.1 MqttForGoodsUpdate商品更新消息
**文件路径：** [`src/main/java/org/haier/shop/mqtt/MqttForGoodsUpdate.java`](../src/main/java/org/haier/shop/mqtt/MqttForGoodsUpdate.java)

<augment_code_snippet path="src/main/java/org/haier/shop/mqtt/MqttForGoodsUpdate.java" mode="EXCERPT">
````java
/**
* @author: 作者:王恩龙
* @version: 2023年6月17日 上午10:51:45
* 本类仅用于商品修改后，向收银设备发送商品更新MQTT消息，不做其他用
*
*/
public class MqttForGoodsUpdate implements Serializable {
    private Long goods_id;
    private String goods_barcode;
    private Long shop_unique;
    private Integer goodsChengType;
    private BigDecimal goods_contain;
    private BigDecimal goods_in_price;
    private BigDecimal goodStockPrice;
    private String goods_standard;
    private String goods_name;
    private String goods_alias;
    private String goods_brand;
    private Integer pc_shelf_state;
    private Integer shelf_state;
    private BigDecimal goods_sale_price;
    private Long foreign_key;
    private String goods_cus_price;
    private Long goods_kind_unique;
    private BigDecimal goods_web_sale_price;
    private String update_time;
    private BigDecimal goods_count;
    private String goods_unit;
    private String supplierUnique;
    // ... getter/setter方法
}
````
</augment_code_snippet>

### 2.7 MQTT服务器端实现（已禁用）

#### 2.7.1 ServerMQTT服务器类
**文件路径：** [`src/main/java/org/haier/shop/util/mqtt/ServerMQTT.java`](../src/main/java/org/haier/shop/util/mqtt/ServerMQTT.java)

<augment_code_snippet path="src/main/java/org/haier/shop/util/mqtt/ServerMQTT.java" mode="EXCERPT">
````java
public class ServerMQTT {
    private static String HOST;
    private static String TOPIC;
    private static String clientid = "server" + (int)(Math.random() * 100000);

    private MqttClient client;
    private static MqttTopic topic11;
    private static MqttMessage message;

    public static void sendMessage(String clieId,String msg)throws Exception{
        ServerMQTT server = new ServerMQTT();
        server.message = new MqttMessage();
        server.message.setQos(1);  //保证消息能到达一次
        server.message.setRetained(true);
        String str ="{\"clieId\":\""+clieId+"\",\"mag\":\""+msg+"\"}";
        server.message.setPayload(str.getBytes());
        try{
            publish(server.topic11 , server.message);
        }catch (Exception e){
            e.printStackTrace();
        }
    }
}
````
</augment_code_snippet>

## 3. GoEasy实时消息推送

### 3.1 GoEasy配置与使用

#### 3.1.1 前端JavaScript实现
**文件路径：** [`src/main/webapp/WEB-INF/main.jsp`](../src/main/webapp/WEB-INF/main.jsp)

<augment_code_snippet path="src/main/webapp/WEB-INF/main.jsp" mode="EXCERPT">
````javascript
//后台新订单消息推送接受
var goEasy = new GoEasy({
    appkey: "BC-93cd649e09404a8e9427fd540fdf5acf"
});
goEasy.subscribe({
    channel: "8302016134121",
    onMessage: function (message) {
        querySupplierMsg();
        alertMessage(message.content);
    }
});
goEasy.subscribe({
    channel: "${sessionScope.staff.shop_unique }",
    onMessage: function (message) {
        alertMessage2(message.content,shop_unique);
    }
});
````
</augment_code_snippet>

#### 3.1.2 多页面支持
GoEasy推送在多个页面中都有实现，包括：

**主页面：** [`src/main/webapp/WEB-INF/main.jsp`](../src/main/webapp/WEB-INF/main.jsp)
**主页面1：** [`src/main/webapp/WEB-INF/main1.jsp`](../src/main/webapp/WEB-INF/main1.jsp)
**主页面2：** [`src/main/webapp/WEB-INF/main2.jsp`](../src/main/webapp/WEB-INF/main2.jsp)
**安装扩展页：** [`src/main/webapp/WEB-INF/InstallAndExtension/main.jsp`](../src/main/webapp/WEB-INF/InstallAndExtension/main.jsp)
**易农主页：** [`src/main/webapp/WEB-INF/yinongMain.jsp`](../src/main/webapp/WEB-INF/yinongMain.jsp)
**首页：** [`src/main/webapp/js/index/index.js`](../src/main/webapp/js/index/index.js)

#### 3.1.3 后端Java实现
**文件路径：** [`src/main/java/org/haier/shop/controller/TestController.java`](../src/main/java/org/haier/shop/controller/TestController.java)

<augment_code_snippet path="src/main/java/org/haier/shop/controller/TestController.java" mode="EXCERPT">
````java
@RequestMapping("/testPush.do")
@ResponseBody
public ShopsResult testPush(String shopUnique){
    ShopsResult sr=new ShopsResult();
    GoEasy goeasy=new GoEasy("BC-91e94f22780e4ed697c2f0837f1de9c5",null);
    goeasy.publish("8302016134121", "ceshi");
    sr.setStatus(1);
    sr.setMsg("chaxunchengg!");
    return sr;
}
````
</augment_code_snippet>

#### 3.1.4 支付成功推送
**文件路径：** [`src/main/java/org/haier/shop/controller/BeansController.java`](../src/main/java/org/haier/shop/controller/BeansController.java)

<augment_code_snippet path="src/main/java/org/haier/shop/controller/BeansController.java" mode="EXCERPT">
````java
// 微信支付回调成功后推送消息
PageData order = beanService.findOneByTradeCode(out_trade_no);
if(order != null && order.getInt("receive_state") != 1) {
    goeasy.publish(shop_unique,"支付成功，订单号："+out_trade_no);
    order.put("receive_state", 1);
    beanService.payOrder(order);
}
````
</augment_code_snippet>

### 3.2 GoEasy应用场景

#### 3.2.1 订单消息推送
- **频道**: `8302016134121` - 全局订单通知频道
- **频道**: `${shop_unique}` - 店铺专属消息频道
- **用途**: 新订单提醒、订单状态变更通知

#### 3.2.2 测试页面
**文件路径：** [`src/main/webapp/html/test/GoEasy.html`](../src/main/webapp/html/test/GoEasy.html)

<augment_code_snippet path="src/main/webapp/html/test/GoEasy.html" mode="EXCERPT">
````html
<script type="text/javascript" src="https://cdn.goeasy.io/goeasy.js"></script>
<script type="text/javascript">
    var goEasy=new GoEasy({
        appkey:"BC-91e94f22780e4ed697c2f0837f1de9c5"
    });
    goEasy.subscribe({
        channel:"测试频道",
        onMessage:function(message){
            console.log(message.content);
        }
    })
</script>
````
</augment_code_snippet>

## 4. WebSocket通信系统

### 4.1 WebSocket客户端实现

#### 4.1.1 电视设备WebSocket连接
**文件路径：** [`src/main/webapp/WEB-INF/tv/tvList.jsp`](../src/main/webapp/WEB-INF/tv/tvList.jsp)

<augment_code_snippet path="src/main/webapp/WEB-INF/tv/tvList.jsp" mode="EXCERPT">
````javascript
//socket
var websocket = null;
//判断浏览器是否支持websocket
if('WebSocket' in window) {
   websocket = new WebSocket("ws://*************:81/websocket/"+shop_unique);
}else{
   layer.msg("该浏览器不支持实时通信功能");
}
websocket.onopen= function() {
}

websocket.onclose= function() {
   console.log("websocket连接关闭");
}

websocket.onmessage= function(event) {
    try {
         var img = "<img src='"+imgHtml+"' id='img1' width='1366px' height='768px'/>";
        layer.open({
            type: 1,
            shade: 0.8,
            offset: 'auto',
            area: [1366 + 'px',768+'px'],
            shadeClose:true,
            scrollbar: false,
            title: "图片预览",
            content: img,
            cancel: function () {
            }
        });
        layer.close(index);
        clearInterval(timer);
    } catch (e) {
        console.log(e.message);
    }
}
````
</augment_code_snippet>

#### 4.1.2 WebSocket消息发送
**执行代码：** [`tvList.jsp:522-528`](../src/main/webapp/WEB-INF/tv/tvList.jsp#L522-L528)

<augment_code_snippet path="src/main/webapp/WEB-INF/tv/tvList.jsp" mode="EXCERPT">
````javascript
function sendMsg(msg) {
   websocket.send(msg);
}

function closeWebSocket(){
   websocket.close();
}
````
</augment_code_snippet>

### 4.2 WebSocket业务场景

#### 4.2.1 电视设备控制
- **连接地址**: `ws://*************:81/websocket/{shop_unique}`
- **主要功能**:
  - 电视设备远程控制
  - 实时截图传输
  - 设备状态监控
  - 图片预览显示

#### 4.2.2 其他WebSocket应用
**打印组件WebSocket：** [`src/main/webapp/js/LodopFuncs.js`](../src/main/webapp/js/LodopFuncs.js)
**打印组件WebSocket2：** [`src/main/webapp/js/pub/LodopFuncs2.js`](../src/main/webapp/js/pub/LodopFuncs2.js)

<augment_code_snippet path="src/main/webapp/js/LodopFuncs.js" mode="EXCERPT">
````javascript
// Lodop打印组件WebSocket连接
if (!window.WebSocket && window.MozWebSocket) window.WebSocket=window.MozWebSocket;
try {
    var WSK1=new WebSocket(URL_WS1);
    WSK1.onopen = function(e) { setTimeout("checkOrTryHttp();",200); }
    WSK1.onmessage = function(e) {if (!window.getCLodop) eval(e.data);}
    WSK1.onerror = function(e) {
         var WSK2=new WebSocket(URL_WS2);
         WSK2.onopen = function(e) {setTimeout("checkOrTryHttp();",200);}
         WSK2.onmessage = function(e) {if (!window.getCLodop) eval(e.data);}
         WSK2.onerror= function(e) {checkOrTryHttp();}
    }
} catch(e){
    checkOrTryHttp();
}
````
</augment_code_snippet>

**功能说明：** 用于Lodop打印组件的WebSocket通信，实现打印功能的远程调用。

### 4.3 WebSocket配置说明

#### 4.3.1 服务器配置
- **WebSocket服务器**: `*************:81`
- **连接路径**: `/websocket/{shop_unique}`
- **协议**: WebSocket (ws://)

#### 4.3.2 客户端特性
- **自动重连**: 支持连接断开检测
- **消息处理**: 支持图片数据传输
- **错误处理**: 完善的异常捕获机制
- **浏览器兼容**: 支持WebSocket和MozWebSocket

**注意**: 项目中的WebSocket主要用于电视设备控制和打印组件通信，服务器端实现不在当前项目代码中。

## 5. 饿了么消息推送监听

### 4.1 饿了么消息监听器

#### 4.1.1 EleMessagePushListtener监听器
**文件路径：** [`src/main/java/org/haier/ele/listener/EleMessagePushListtener.java`](../src/main/java/org/haier/ele/listener/EleMessagePushListtener.java)

<augment_code_snippet path="src/main/java/org/haier/ele/listener/EleMessagePushListtener.java" mode="EXCERPT">
````java
/**
 * 饿了么-消息推送监听
 */
public class EleMessagePushListtener implements ServletContextListener{

    @Override
    public void contextInitialized(ServletContextEvent sce) {

        final EleOrderService eleOrderService = WebApplicationContextUtils.getWebApplicationContext(sce.getServletContext()).getBean(EleOrderService.class);

        Account account = new Account(EleConfig.key, EleConfig.secret);
        List<Account> accounts = new ArrayList<Account>();
        accounts.add(account);
        Config config = new Config(accounts,
            new BusinessHandle() {
                @Override
                public boolean onMessage(String message) {
                    //处理业务
                    Map<String ,Object> pushMap = MUtil.jsonToMap(message);
                    String type = MUtil.strObject(pushMap.get("type"));
                    ShopsResult shopsResult = new ShopsResult();

                    if(type != null && type.equals("10")){//订单生效，店铺可以看到新订单,收银系统创建订单
                        Map<String ,Object> orderMap =  MUtil.jsonToMap(pushMap.get("message").toString());
                        shopsResult = eleOrderService.addSaleList(orderMap);
                    }
                    if(type != null && type.equals("12")){//商户已经接单,修改订单状态为2 待发货
                        Map<String ,Object> orderMap =  MUtil.jsonToMap(pushMap.get("message").toString());
                        shopsResult = eleOrderService.updateSaleList(orderMap, 2);
                    }
                    // ... 更多订单状态处理

                    if(shopsResult.getStatus()==1){
                        return true;
                    }else{
                        return false;
                    }
                }
            }, new ElemeSdkLogger() {
                @Override
                public void info(String message) {
                    Qutil.getLogger().info("饿了么消息推送监听："+message);
                }

                @Override
                public void error(String message) {
                    Qutil.getLogger().info("饿了么消息推送监听异常："+message);
                }
            });
        try {
            Bootstrap.start(config);
        } catch (UnableConnectionException e) {
            e.printStackTrace();
        }
    }
}
````
</augment_code_snippet>

### 4.2 饿了么订单状态处理

#### 4.2.1 订单状态映射
**执行代码：** [`EleMessagePushListtener.java:47-94`](../src/main/java/org/haier/ele/listener/EleMessagePushListtener.java#L47-L94)

| 消息类型 | 业务含义 | 订单状态 | 处理方法 |
|---------|---------|---------|---------|
| type=10 | 订单生效，店铺可以看到新订单 | 创建订单 | `addSaleList()` |
| type=12 | 商户已经接单 | 2-待发货 | `updateSaleList(orderMap, 2)` |
| type=52 | 待分配配送员 | 7-配送单待确认 | `updateSaleList(orderMap, 7)` |
| type=53 | 已分配给配送员，配送员取餐中 | 2-待发货 | `updateSaleList(orderMap, 2)` |
| type=55 | 配送员已取餐，配送中 | 3-待收货 | `updateSaleList(orderMap, 3)` |
| type=56 | 配送成功 | 4-已完成 | `updateSaleList(orderMap, 4)` |
| type=14/15/17 | 订单取消 | 5-已取消 | `updateSaleList(orderMap, 5)` |
| type=32/36 | 拒绝退款 | 付款状态7 | `updatSaleListState(orderMap, 7)` |
| type=33/35 | 同意退款 | 付款状态6 | `updatSaleListState(orderMap, 6)` |

#### 4.2.2 监听器配置状态
**配置文件：** [`src/main/webapp/WEB-INF/web.xml`](../src/main/webapp/WEB-INF/web.xml)

<augment_code_snippet path="src/main/webapp/WEB-INF/web.xml" mode="EXCERPT">
````xml
<!--   <listener> -->
<!--     <listener-class>org.haier.ele.listener.EleMessagePushListtener</listener-class> -->
<!--   </listener> -->
````
</augment_code_snippet>

**状态说明：** 饿了么消息推送监听器当前已被注释禁用，不会自动启动。

## 6. 异步线程处理

### 6.1 MQTT相关线程

#### 6.1.1 PC升级通知线程 (PcUploadThread)
**文件路径：** [`src/main/java/org/haier/shop/thread/PcUploadThread.java`](../src/main/java/org/haier/shop/thread/PcUploadThread.java)

<augment_code_snippet path="src/main/java/org/haier/shop/thread/PcUploadThread.java" mode="EXCERPT">
````java
/**
 * 后台升级成功后，向收银设备发送升级指令
 */
public class PcUploadThread implements Runnable{
    @Override
    public void run() {
        //获取需要升级的店铺设备信息
        List<Map<String,Object>> list = utilDao.queryEquipmentList();
        for(Integer i = 0; i < list.size(); i++) {
            Map<String,Object> shop = list.get(i);
            String shopUnique = shop.get("shopUnique").toString();
            String macId = shop.get("macId") == null ? "" : shop.get("macId").toString();
            macId = macId.replace(":", "").toUpperCase();

            Object o = redis.getObject(ClientMQTT.MQTTPUBKEY + shopUnique);
            if(null == o || o.toString().equals("")) continue;

            List<String> macList = (List<String>)o;
            for(Integer j = 0; j < macList.size(); j++) {
                if(macId.equals(macList.get(j))) {
                    //发送升级指令
                    Map<String,Object> ctrlMap = new HashMap<String,Object>();
                    ctrlMap.put("ctrl", "msg_pc_update");
                    ctrlMap.put("ID", macId);
                    ctrlMap.put("status", "200");
                    ctrlMap.put("errorcode", 0);
                    ctrlMap.put("count", 0);
                    ctrlMap.put("msg", "您的收银设备需要升级到最新版本");
                    ctrlMap.put("data", null);

                    MqttxUtil.sendMapMsg(ctrlMap, macId);
                    break;
                }
            }
        }
    }
}
````
</augment_code_snippet>

**功能说明：**
- **触发时机**: 后台升级成功后
- **主要功能**: 向收银设备发送升级指令
- **消息格式**: `{"ctrl":"msg_pc_update","ID":"设备MAC","status":"200","msg":"升级提示"}`

### 6.2 业务处理线程

#### 6.2.1 分拣排序线程 (SortThread)
**文件路径：** [`src/main/java/org/haier/shop/thread/SortThread.java`](../src/main/java/org/haier/shop/thread/SortThread.java)

<augment_code_snippet path="src/main/java/org/haier/shop/thread/SortThread.java" mode="EXCERPT">
````java
/**
 * 支付完成后，调用该线程，重新修改分拣排序问题
 */
public class SortThread extends Thread{
    private String url;
    private String path = "/purchase-app/shopping/createSortingMsg.do";
    private String mainOrderNo;

    @Override
    public void run() {
        //需要等待订单保存完成后再操作
        try {
            Thread.sleep(2000);
            //发送get请求
            HttpGetUtil.sendGet(url + path, "mainOrderNo=" + mainOrderNo);
        }catch (Exception e) {
            e.printStackTrace();
        }
    }
}
````
</augment_code_snippet>

**功能说明：**
- **触发时机**: 支付完成后
- **主要功能**: 调用分拣系统API重新排序
- **延迟处理**: 等待2秒确保订单保存完成

#### 6.2.2 极光推送线程 (PushThread)
**文件路径：** [`src/main/java/org/haier/shop/util/PushThread.java`](../src/main/java/org/haier/shop/util/PushThread.java)

<augment_code_snippet path="src/main/java/org/haier/shop/util/PushThread.java" mode="EXCERPT">
````java
/**
 * 后台极光推送线程
 */
public class PushThread implements Runnable{
    public List<String> userId;      //用户id
    public String smsTitle;          //标题
    public String smsContent;        //推送内容
    public Map<String, String> extra; //扩展字段
    String pushType;                 //推送类型  1 全部用户推送  2只定用户推送

    public void run() {
        if("2".equals(pushType)) {
            JiguangPush.push(userId,smsContent,smsTitle,extra);
        }
        if("1".equals(pushType)) {
            JiguangPush.pushAll(smsContent,smsTitle,extra);
        }
    }
}
````
</augment_code_snippet>

**功能说明：**
- **推送类型**: 支持指定用户推送和全员广播
- **异步处理**: 避免推送操作阻塞主业务流程
- **扩展支持**: 支持自定义扩展字段

### 6.3 异步事件处理

#### 6.3.1 日志事件监听器 (LogEventListener)
**文件路径：** [`src/main/java/org/haier/log/event/LogEventListener.java`](../src/main/java/org/haier/log/event/LogEventListener.java)

<augment_code_snippet path="src/main/java/org/haier/log/event/LogEventListener.java" mode="EXCERPT">
````java
/**
 * 保存系统日志记录
 */
@Async
@EventListener
public void onApplicationEvent(OperLogEvent operLogEvent) {
    operLogEvent.setProjectCode(remoteLogConfig.getProjectCode());
    operLogEvent.setEnv(remoteLogConfig.getEnv());
    String body = JSONUtil.toJsonStr(operLogEvent);
    String url = remoteLogConfig.getUrl();
    try {
        if (StrUtil.isNotBlank(url)) {
            logger.info("---存储日志接口：----url: " + url + "----body:--" + body);
            String result = HttpUtil.post(url, body);
            logger.info("--------存储日志接口返回值：" + result);
        }
    } catch (Exception e) {
        logger.error("---存储日志接口异常----url: " + url + "----body:--" + body + "-----" + e.getMessage());
    }
}
````
</augment_code_snippet>

**功能说明：**
- **异步处理**: 使用`@Async`注解实现异步日志记录
- **事件驱动**: 通过`@EventListener`监听日志事件
- **远程存储**: 将日志发送到远程日志服务

### 6.4 商品分类MQTT通知

#### 6.4.1 商品分类更新通知
**文件路径：** [`src/main/java/org/haier/shop/service/GoodsKindServiceImpl.java`](../src/main/java/org/haier/shop/service/GoodsKindServiceImpl.java)

<augment_code_snippet path="src/main/java/org/haier/shop/service/GoodsKindServiceImpl.java" mode="EXCERPT">
````java
//20220909 新增MQTT -通知收银机有商品分类更新----start
RedisCache rc = new RedisCache("");
Object mac = rc.getObject("topic_"+map.get("shopUnique"));
List<String> macIdList = (List<String>)mac;
List<Map<String,Object>> mqttData=kindDao.queryMqttKind(map);
//2 MQTT 发送消息
if(macIdList!=null) {
    for(String macid : macIdList) {
        Map<String,Object> data=new HashMap<String,Object>();
        data.put("ctrl", "msg_goods_kind_add");//添加成功
        data.put("ID", macid);
        data.put("status", 200);
        data.put("data",mqttData);
        data.put("count",1 );
        MqttxUtil.sendMapMsg(data, macid);
    }
}
````
</augment_code_snippet>

**功能说明：**
- **触发时机**: 商品分类添加/修改后
- **消息类型**: `msg_goods_kind_add`
- **同步范围**: 店铺下所有收银设备

## 7. 消息业务场景

### 5.1 商品信息同步

#### 5.1.1 商品更新MQTT消息发送
**执行代码：** [`MqttServiceImpl.java:26-47`](../src/main/java/org/haier/shop/service/MqttServiceImpl.java#L26-L47)

**业务流程：**
1. 从Redis获取店铺对应的设备MAC地址列表：`topic_{shopUnique}`
2. 遍历每个设备MAC地址
3. 构造商品更新消息：
   - `ctrl`: "msg_goods_update"
   - `ID`: 设备MAC地址
   - `status`: 200
   - `data`: 商品更新数据列表
   - `count`: 商品数量
4. 通过MQTT发送到对应设备

#### 5.1.2 供应商商品同步
**文件路径：** [`src/main/java/org/haier/shop/service/supplier/ShopSupBillServiceImpl.java`](../src/main/java/org/haier/shop/service/supplier/ShopSupBillServiceImpl.java)

<augment_code_snippet path="src/main/java/org/haier/shop/service/supplier/ShopSupBillServiceImpl.java" mode="EXCERPT">
````java
private void sendMqtt(StorageGoodsParams params, List<Map<String, Object>> returnList) {
    //mqtt 同步商品
    try {
        Object mac = redis.getObject("topic_" + params.getShopUnique());
        if (mac != null) {
            @SuppressWarnings("unchecked")
            List<String> macIdList = (List<String>) mac;
            //2 MQTT 发送消息
            for (String macid : macIdList) {
                Map<String, Object> data = new HashMap<>();
                data.put("ctrl", "msg_goods_update_v2.0");
                data.put("ID", macid);
                data.put("status", 200);
                data.put("data", returnList);
                data.put("count", 1);
                MqttxUtil.sendMapMsg(data, macid);
            }
        }
        //清除缓存
        String rcId = "pcGoods" + params.getShopUnique() + 1 + 3 + null;
        Object res = redis.getObject(rcId);
        if (null != res) {
            redis.removeObject(rcId);
        }
    } catch (Exception e) {
        e.printStackTrace();
    }
}
````
</augment_code_snippet>

#### 5.1.3 美团配送订单状态同步
**文件路径：** [`src/main/java/org/haier/meituanpeisong/service/impl/PeisongOrderServiceImpl.java`](../src/main/java/org/haier/meituanpeisong/service/impl/PeisongOrderServiceImpl.java)

<augment_code_snippet path="src/main/java/org/haier/meituanpeisong/service/impl/PeisongOrderServiceImpl.java" mode="EXCERPT">
````java
/**
 * 订单状态修改后，向收银机发送订单修改通知
 * @param shopUnique 店铺唯一标识
 * @param saleListUnique 订单唯一标识
 * @param saleListHandlestate 订单处理状态
 */
public void sendMsgToPc(String shopUnique, String saleListUnique, Integer saleListHandlestate) {
    //向收银设备推送消息
    Map<String,Object> mqttMap = new HashMap<String,Object>();
    mqttMap.put("ctrl", "msg_order_change");
    mqttMap.put("shopUnique", shopUnique);
    Map<String,Object> detailMap = new HashMap<String,Object>();
    detailMap.put("saleListUnique", saleListUnique);
    detailMap.put("saleListHandlestate", saleListHandlestate);

    mqttMap.put("status", 200);
    mqttMap.put("errcode", 0);
    mqttMap.put("msg", "订单状态修改");

    //获取各个状态的订单数量
    List<Map<String, Object>> countList = queryOrderCount(shopUnique);
    detailMap.put("orderCount", countList);
    mqttMap.put("data", detailMap);
    if(null != redis.getObject("topic_" + shopUnique)) {
        //缓存信息不为空
        List<String> idList = (List<String>)redis.getObject("topic_" + shopUnique);
        for(String ID : idList) {
            MqttxUtil.sendMapMsg(mqttMap, ID);
        }
    }
}
````
</augment_code_snippet>

**业务流程：**
1. 配送订单状态发生变更时触发
2. 构造订单状态变更消息：
   - `ctrl`: "msg_order_change"
   - `shopUnique`: 店铺标识
   - `status`: 200
   - `data`: 包含订单详情和各状态订单数量统计
3. 从Redis获取店铺对应的设备列表
4. 向每个设备发送MQTT消息通知

### 5.2 订单消息推送

#### 5.2.1 Web端实时通知
**应用场景：**
- 新订单到达通知
- 订单状态变更提醒
- 系统消息推送

**实现方式：**
- 使用GoEasy实时推送服务
- 支持多频道订阅
- 前端JavaScript实时接收

#### 5.2.2 移动端推送
**文件路径：** [`src/main/java/org/haier/shop/service/TVServiceImpl.java`](../src/main/java/org/haier/shop/service/TVServiceImpl.java)

<augment_code_snippet path="src/main/java/org/haier/shop/service/TVServiceImpl.java" mode="EXCERPT">
````java
/**
 * 控制电视 1. 关机 2 重启 3截图  4
 */
@Override
public PushResult pushTVMessage(Map<String, String> map) throws Exception {
    PushResult result = new PushResult();
    try {
        List<String> list=new ArrayList<String>();
        list.add(map.get("mac").toString());

        result=JPushClientUtil.notifyTV(map.get("mac").toString(),"TV",map);

    } catch (Exception e) {
        e.printStackTrace();
    }
    return result;
}
````
</augment_code_snippet>

## 8. 配置管理

### 8.1 MQTT配置集成

#### 8.1.1 Spring配置集成
**文件路径：** [`src/main/resources/conf/spring-mvc.xml`](../src/main/resources/conf/spring-mvc.xml)

<augment_code_snippet path="src/main/resources/conf/spring-mvc.xml" mode="EXCERPT">
````xml
<bean id="sysConfig" class="org.haier.shop.config.SysConfig">
    <property name="REDISTESTCode" value="${system.REDISTESTCode}"/>
    <property name="URLPATHCode" value="${system.URLPATHCode}"/>
    <property name="sendJGPushToAppCode" value="${system.sendJGPushToAppCode}"/>
    <property name="FILEPATHCode" value="${system.FILEPATHCode}"/>
    <property name="confirmReceiptUrlCode" value="${system.confirmReceiptUrlCode}"/>
    <property name="HELIBAOIMGURLCode" value="${system.HELIBAOIMGURLCode}"/>
    <property name="publicMqttUrlCode" value="${system.publicMqttUrlCode}"/>
    <property name="ykz_cancelDeliveryCode" value="${system.ykz_cancelDeliveryCode}"/>
    <property name="HELIBAONOTIFYURLCode" value="${system.HELIBAONOTIFYURLCode}"/>
</bean>
````
</augment_code_snippet>

#### 8.1.2 应用配置文件
**文件路径：** [`src/main/resources/application.properties`](../src/main/resources/application.properties)

<augment_code_snippet path="src/main/resources/application.properties" mode="EXCERPT">
````properties
system.publicMqttUrlCode=http://test170.buyhoo.cc/shopmanager/util/sendMQTTMsg.do
````
</augment_code_snippet>

### 8.2 消息队列相关配置

#### 8.2.1 MQTT服务器配置
- **服务器地址**: `tcp://**************:1883`
- **认证信息**: 用户名/密码均为 `mqtt`
- **QoS级别**: 发送消息使用QoS 1，订阅使用QoS 2
- **自动重连**: 启用，连接断开后3秒重试

#### 8.2.2 GoEasy配置
- **生产环境AppKey**: `BC-93cd649e09404a8e9427fd540fdf5acf`
- **测试环境AppKey**: `BC-91e94f22780e4ed697c2f0837f1de9c5`
- **全局订单频道**: `8302016134121`
- **店铺专属频道**: 使用店铺唯一标识 `shop_unique`

#### 8.2.3 WebSocket配置
- **WebSocket服务器**: `*************:81`
- **连接路径**: `/websocket/{shop_unique}`
- **协议**: WebSocket (ws://)
- **用途**: 电视设备控制和图片传输

## 9. 监听器管理

### 9.1 MQTT监听器状态

#### 9.1.1 MqttxServiceListener（已禁用）
**文件路径：** [`src/main/java/org/haier/shop/util/mqtt/MqttxServiceListener.java`](../src/main/java/org/haier/shop/util/mqtt/MqttxServiceListener.java)

<augment_code_snippet path="src/main/java/org/haier/shop/util/mqtt/MqttxServiceListener.java" mode="EXCERPT">
````java
/**
* @author: 作者:王恩龙
* @version: 2022年8月25日 下午5:32:09
*
*/
public class MqttxServiceListener implements ServletContextListener{
    public void contextInitialized(ServletContextEvent arg0) {
        ClientMQTT cm = new ClientMQTT();
        cm.start();
    }

    @Override
    public void contextDestroyed(ServletContextEvent sce) {
        // TODO Auto-generated method stub
    }
}
````
</augment_code_snippet>

**状态说明：** 该监听器已被Spring的`@PostConstruct`方式替代，不再使用ServletContextListener方式启动。

#### 9.1.2 当前启动方式
**文件路径：** [`src/main/java/org/haier/shop/util/mqtt/MqttxUtil.java`](../src/main/java/org/haier/shop/util/mqtt/MqttxUtil.java)

<augment_code_snippet path="src/main/java/org/haier/shop/util/mqtt/MqttxUtil.java" mode="EXCERPT">
````java
@Component
public class MqttxUtil {
    @Autowired
    private RedisCache rc;

    public static ClientMQTT cm = new ClientMQTT();

    @PostConstruct
    public void startCM() {
        if(null != rc) {
            ClientMQTT.redis = rc;
        }
        cm.start();  // Spring容器启动时自动初始化MQTT客户端
    }
}
````
</augment_code_snippet>

### 9.2 外部平台监听器状态

#### 9.2.1 饿了么监听器（已禁用）
**配置状态：** 在`web.xml`中已注释禁用
**原因：** 可能由于业务调整或技术架构变更

#### 9.2.2 Session监听器（已禁用）
**配置状态：** 在`web.xml`中已注释禁用
**文件路径：** [`src/main/java/org/haier/shop/test/SessionListener.java`](../src/main/java/org/haier/shop/test/SessionListener.java)

## 10. 极光推送系统

### 10.1 极光推送工具类

#### 10.1.1 JiguangPush通用推送类
**文件路径：** [`src/main/java/org/haier/shop/util/JiguangPush.java`](../src/main/java/org/haier/shop/util/JiguangPush.java)

<augment_code_snippet path="src/main/java/org/haier/shop/util/JiguangPush.java" mode="EXCERPT">
````java
public class JiguangPush {
    private static final String masterSecret = "c5a0380339c1539d37d7e99b";
    private static final String appKey = "4b3943dfc6e97847952d386e";
    private static final Logger log = LoggerFactory.getLogger(JiguangPush.class);

    /**
     * 极光推送方法(采用java SDK)
     * @param alias 用户别名列表
     * @param alert 推送内容
     * @param title 推送标题
     * @param extra 扩展字段
     * @return PushResult
     */
    public static PushResult push(List<String> alias,String alert,String title,Map<String, String> extra){
        ClientConfig clientConfig = ClientConfig.getInstance();
        JPushClient jpushClient = new JPushClient(masterSecret, appKey, null, clientConfig);
        PushPayload payload = buildPushObject_android_ios_alias_alert(alias,alert,title,extra);
        try {
            return jpushClient.sendPush(payload);
        } catch (APIConnectionException e) {
            log.error("Connection error. Should retry later. ", e);
            return null;
        } catch (APIRequestException e) {
            log.error("Error response from JPush server. Should review and fix it. ", e);
            log.info("HTTP Status: " + e.getStatus());
            log.info("Error Code: " + e.getErrorCode());
            log.info("Error Message: " + e.getErrorMessage());
            return null;
        }
    }

    /**
     * 广播推送
     */
    public static PushResult pushAll(String alert,String title,Map<String, String> extra){
        ClientConfig clientConfig = ClientConfig.getInstance();
        JPushClient jpushClient = new JPushClient(masterSecret, appKey, null, clientConfig);
        PushPayload payload = buildPushObject_android_ios_all_alert(alert,title,extra);
        try {
            return jpushClient.sendPush(payload);
        } catch (APIConnectionException e) {
            log.error("Connection error. Should retry later. ", e);
            return null;
        } catch (APIRequestException e) {
            log.error("Error response from JPush server. Should review and fix it. ", e);
            return null;
        }
    }
}
````
</augment_code_snippet>

#### 10.1.2 JiguangPushTV电视推送类
**文件路径：** [`src/main/java/org/haier/shop/util/JiguangPushTV.java`](../src/main/java/org/haier/shop/util/JiguangPushTV.java)

<augment_code_snippet path="src/main/java/org/haier/shop/util/JiguangPushTV.java" mode="EXCERPT">
````java
public class JiguangPushTV {
    private static final String masterSecret = "2bbeeb72cc3cb20f7f67f028";
    private static final String appKey = "f2d359af994a544e28bdbec1";
    private static final Logger log = LoggerFactory.getLogger(JiguangPushTV.class);

    /**
     * TV设备推送方法
     * @param alias 设备别名列表
     * @param alert 推送内容
     * @param title 推送标题
     * @param extra 扩展字段
     * @return PushResult
     */
    public static PushResult push(List<String> alias,String alert,String title,Map<String, String> extra){
        ClientConfig clientConfig = ClientConfig.getInstance();
        JPushClient jpushClient = new JPushClient(masterSecret, appKey, null, clientConfig);
        PushPayload payload = buildPushObject_android_ios_alias_alert(alias,alert,title,extra);
        try {
            return jpushClient.sendPush(payload);
        } catch (APIConnectionException e) {
            log.error("Connection error. Should retry later. ", e);
            return null;
        } catch (APIRequestException e) {
            log.error("Error response from JPush server. Should review and fix it. ", e);
            return null;
        }
    }
}
````
</augment_code_snippet>

#### 10.1.3 JPushClientUtil专用推送工具
**文件路径：** [`src/main/java/org/haier/shop/util/JPushClientUtil.java`](../src/main/java/org/haier/shop/util/JPushClientUtil.java)

<augment_code_snippet path="src/main/java/org/haier/shop/util/JPushClientUtil.java" mode="EXCERPT">
````java
public class JPushClientUtil {

    /**
     * 推送到APP端
     * @param registrationId 设备注册ID
     * @param alert 推送内容
     * @param registration_phone_type 设备类型 1-iOS 2-Android
     * @param outTradeNo 订单号
     */
    public static void notifyApp(String registrationId,String alert,Integer registration_phone_type, String outTradeNo){
        JPushClient jpushClient = new JPushClient("09ddff131ddca59554d9e5df", "5ab7b771681d7d4f64632b8e", null, ClientConfig.getInstance());
        PushPayload payload = buildPushObject_android_tag_alertWithTitle(registrationId,alert,"百货商家端",registration_phone_type,outTradeNo);
        try {
            PushResult result = jpushClient.sendPush(payload);
            System.out.println("Got result - " + result);
        } catch (APIConnectionException e) {
            e.printStackTrace();
        } catch (APIRequestException e) {
            e.printStackTrace();
        }
    }

    /**
     * 推送到TV设备
     * @param mac 设备MAC地址
     * @param alert 推送内容
     * @param params 扩展参数
     * @return PushResult
     */
    public static PushResult notifyTV(String mac,String alert, Map<String,String> params ){
        String masterSecret="2bbeeb72cc3cb20f7f67f028";
        String appKey="f2d359af994a544e28bdbec1";
        JPushClient jpushClient = new JPushClient(masterSecret,appKey, null, ClientConfig.getInstance());
        PushPayload payload = buildPushObject_android_tag_alertWithTitle2(mac,alert,"电视",params);
        PushResult result=new PushResult();
        try {
            result = jpushClient.sendPush(payload);
            System.out.println("Got result - " + result);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return result;
    }
}
````
</augment_code_snippet>

### 10.2 推送线程处理

#### 10.2.1 PushThread异步推送线程
**文件路径：** [`src/main/java/org/haier/shop/util/PushThread.java`](../src/main/java/org/haier/shop/util/PushThread.java)

<augment_code_snippet path="src/main/java/org/haier/shop/util/PushThread.java" mode="EXCERPT">
````java
/**
 * 后台极光推送线程
 */
public class PushThread implements Runnable{
    //用户id
    public List<String> userId;
    //标题
    public String smsTitle;
    //推送内容
    public String smsContent;
    //扩展字段
    public Map<String, String> extra;
    //推送类型  1 全部用户推送  2只定用户推送
    String pushType;

    public PushThread(List<String> userId, String smsTitle, String pushType,String smsContent,Map<String, String> extra) {
        this.userId = userId;
        this.smsTitle = smsTitle;
        this.pushType = pushType;
        this.smsContent = smsContent;
        this.extra = extra;
    }

    public void run() {
        if("2".equals(pushType)) {
            JiguangPush.push(userId,smsContent,smsTitle,extra);
        }
        if("1".equals(pushType)) {
            JiguangPush.pushAll(smsContent,smsTitle,extra);
        }
    }
}
````
</augment_code_snippet>

### 10.3 极光推送配置

#### 10.3.1 推送应用配置
**配置文件：** [`src/main/resources/config.properties`](../src/main/resources/config.properties)

<augment_code_snippet path="src/main/resources/config.properties" mode="EXCERPT">
````properties
# Android推送配置
ANDRIODapiKey=ygXEDFyzEn73UtXGn2i9uyDlNzvTIK3c
ANDRIODsecretKey=mUj3O4mQECN8dLEaUwqgCkdjwxNKFvOV

# iOS推送配置
IOSapiKey=nZSW8erBqVqHvrkkAH9zW7MIBn8rvW88
IOSsecretKey=6paRyUAqg0pTdweAvoGBEchRin5kmkko
IOSdeployStatus=2

# iOS供货端推送配置
IOSAPIKEY=4eKVFtPp9TnYX41xiAgnsMm7I2d3UjBR
IOSSECRETKEY=v3CloaIHh1IugQAm6XWUnyeOKg6ZanXO
````
</augment_code_snippet>

#### 10.3.2 推送应用密钥说明

| 应用类型 | AppKey | MasterSecret | 用途 |
|---------|--------|--------------|------|
| 通用推送 | `4b3943dfc6e97847952d386e` | `c5a0380339c1539d37d7e99b` | 移动端消息推送 |
| TV推送 | `f2d359af994a544e28bdbec1` | `2bbeeb72cc3cb20f7f67f028` | 电视设备控制 |
| 商家端 | `5ab7b771681d7d4f64632b8e` | `09ddff131ddca59554d9e5df` | 商家APP推送 |

### 10.4 推送业务场景

#### 10.4.1 供应商订单推送
**执行代码：** [`SupplierShoppingService.java:2860-2874`](../src/main/java/org/haier/shop/service/SupplierShoppingService.java#L2860-L2874)

<augment_code_snippet path="src/main/java/org/haier/shop/service/SupplierShoppingService.java" mode="EXCERPT">
````java
//推送消息
Map<String, String> extra = new HashMap<String, String>();
extra.put("msgType", "1");
extra.put("source_id", map.get("order_code").toString());
System.out.println("推送的列表" + extra + userList);
PushThread pushThread = new PushThread(userList, "智慧云商", "2", "您有一条新的云商订单，请查看！", extra);
Thread t = new Thread(pushThread);
t.start();
````
</augment_code_snippet>

#### 10.4.2 TV设备控制推送
**执行代码：** [`TVServiceImpl.java:598-611`](../src/main/java/org/haier/shop/service/TVServiceImpl.java#L598-L611)

<augment_code_snippet path="src/main/java/org/haier/shop/service/TVServiceImpl.java" mode="EXCERPT">
````java
/**
 * 控制电视 1. 关机 2 重启 3截图  4
 */
@Override
public PushResult pushTVMessage(Map<String, String> map) throws Exception {
    PushResult result = new PushResult();
    try {
        List<String> list=new ArrayList<String>();
        list.add(map.get("mac").toString());
        result=JPushClientUtil.notifyTV(map.get("mac").toString(),"TV",map);
    } catch (Exception e) {
        e.printStackTrace();
    }
    return result;
}
````
</augment_code_snippet>

## 11. 信息推送系统

### 11.1 信息推送服务

#### 11.1.1 InfoPushService接口
**文件路径：** [`src/main/java/org/haier/shop/service/InfoPushService.java`](../src/main/java/org/haier/shop/service/InfoPushService.java)

#### 11.1.2 InfoPushServiceImpl实现类
**文件路径：** [`src/main/java/org/haier/shop/service/InfoPushServiceImpl.java`](../src/main/java/org/haier/shop/service/InfoPushServiceImpl.java)

<augment_code_snippet path="src/main/java/org/haier/shop/service/InfoPushServiceImpl.java" mode="EXCERPT">
````java
@Override
@Transactional
public PurResult sendInfo(String info_id) {
    PurResult result = new PurResult();
    try {
        //获取消息详情
        Map<String ,Object> info = infoPushDao.queryInfoDetail(info_id);
        Integer push_area = Integer.parseInt(MUtil.strObject(info.get("push_area")));
        List<Map<String ,Object>> memberList = new ArrayList<Map<String ,Object>>();
        if(push_area == 1) {//全部
            memberList = infoPushDao.queryAllMemberList();
        }else {//按区域
            memberList = infoPushDao.queryMemberListByArea(info_id);
        }
        if(memberList.size() > 0) {
            for(int i=0;i<memberList.size();i++) {
                memberList.get(i).put("info_id", info_id);
                memberList.get(i).put("info_title", MUtil.strObject(info.get("info_title")));
                memberList.get(i).put("info_content", MUtil.strObject(info.get("info_content")));
            }
            //添加会员推送信息
            infoPushDao.addInfoPushMemberList(memberList);
        }
        //修改推送消息已发送
        Map<String ,Object> params = new HashMap<String ,Object>();
        params.put("info_id", info_id);
        params.put("status", 2);
        infoPushDao.updateInfo(params);
        result.setStatus(1);
        result.setMsg("成功");
    } catch (Exception e) {
        e.printStackTrace();
        result.setStatus(0);
        result.setMsg("异常");
    }
    return result;
}
````
</augment_code_snippet>

### 11.2 消息推送控制器

#### 11.2.1 InfoPushController推送控制器
**文件路径：** [`src/main/java/org/haier/shop/controller/InfoPushController.java`](../src/main/java/org/haier/shop/controller/InfoPushController.java)

#### 11.2.2 H5端消息接口
**文件路径：** [`src/main/java/org/haier/shop/controller/h5/NewsController.java`](../src/main/java/org/haier/shop/controller/h5/NewsController.java)

<augment_code_snippet path="src/main/java/org/haier/shop/controller/h5/NewsController.java" mode="EXCERPT">
````java
/**
 * 查询会员消息列表
 * @param cus_unique 会员编号
 * @param pageNum
 * @param pageSize
 * @return
 */
@RequestMapping("/getInfoPushMemberList.do")
@ResponseBody
public PurResult getInfoPushMemberList(
        @RequestParam(value="cus_unique",required=true)String cus_unique,
        @RequestParam(value="pageNum",defaultValue="1")Integer pageNum,
        @RequestParam(value="pageSize",defaultValue="8")Integer pageSize
        ){
    Map<String ,Object> params = new HashMap<String, Object>();
    params.put("cus_unique", cus_unique);
    params.put("startNum", (pageNum-1)*pageSize);
    params.put("pageSize", pageSize);
    return infoPushService.getInfoPushMemberList(params);
}
````
</augment_code_snippet>

## 12. 消息队列定时任务

### 12.1 消息推送定时任务

#### 12.1.1 SentMsgToAndroid定时任务
**文件路径：** [`src/main/java/org/haier/shop/task/SentMsgToAndroid.java`](../src/main/java/org/haier/shop/task/SentMsgToAndroid.java)

<augment_code_snippet path="src/main/java/org/haier/shop/task/SentMsgToAndroid.java" mode="EXCERPT">
````java
public void run() {
    /*
     * 依次查询各个店铺的消息数量，并推送到各个收银设备上
     */

    //从缓存中获取需要推送消息的店铺
    Set<String> shopList = new HashSet<String>();

    if(null != redis.getListKet(ClientMQTT.MQTTPUBKEY)) {
        shopList = (Set<String>)redis.getListKet(ClientMQTT.MQTTPUBKEY);
    }

    if(null == shopList || shopList.isEmpty()) {
        System.out.println("没有要发送信息的店铺");
        return;
    }

    Map<String,Object> map = new HashMap<String,Object>();
    for(String shopUnique : shopList) {
        //获取该店铺的未读消息数量
        Integer count = noticeDao.queryNoticeCount(shopUnique.replace(ClientMQTT.MQTTPUBKEY, ""));
        //
        List<String> macIds = (List<String>)redis.getObject(shopUnique);

        for(String mac : macIds) {
            //依次向收银设备发送信息
            map.put("ctrl", "msg_news_count");
            map.put("status", "200");
            map.put("errcode", 0);
            map.put("count", count);

            System.out.println("当前发送的店铺shopUnique==" + shopUnique + ";;;mac ====" + mac + "====" + map);

            MqttxUtil.sendMapMsg(map, mac);
        }
    }
}
````
</augment_code_snippet>

**功能说明：**
- **触发时机：** 定时执行
- **功能：** 查询各店铺未读消息数量并推送到收银设备
- **消息格式：** `{"ctrl":"msg_news_count","status":"200","errcode":0,"count":消息数量}`

## 13. 消息流转架构图

```
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│   Web前端       │    │   Spring应用     │    │   MQTT Broker   │
│                 │    │                  │    │                 │
│ GoEasy订阅      │◄───┤ GoEasy发布       │    │ **************  │
│ - 订单通知      │    │ - 测试推送       │    │ :1883           │
│ - 状态更新      │    │                  │    │                 │
└─────────────────┘    │ MqttxUtil        │◄───┤ win_qt_cash_1.0 │
                       │ - 商品同步       │    │ (订阅主题)      │
┌─────────────────┐    │ - 设备通信       │    │                 │
│   收银设备      │◄───┤                  │───►│ win_qt_cash_    │
│                 │    │ MqttService      │    │ (发布主题前缀)  │
│ MAC地址识别     │    │ - 商品更新推送   │    │                 │
│ 消息接收处理    │    │                  │    └─────────────────┘
└─────────────────┘    │                  │
                       │ EleListener      │    ┌─────────────────┐
┌─────────────────┐    │ (已禁用)         │    │   饿了么平台    │
│   移动端APP     │◄───┤                  │◄───┤                 │
│                 │    │ 极光推送         │    │ 订单状态推送    │
│ 推送通知        │    │ - 移动端推送     │    │ (WebSocket)     │
│ 订单提醒        │    │ - TV控制         │    │                 │
│                 │    │ - 信息推送       │    └─────────────────┘
└─────────────────┘    │                  │
                       │ InfoPushService  │    ┌─────────────────┐
┌─────────────────┐    │ - 会员消息推送   │    │   极光推送平台  │
│   电视设备      │◄───┤ - 区域推送       │◄───┤                 │
│                 │    │                  │    │ 多应用支持      │
│ TV控制          │    │ PushThread       │    │ - 通用推送      │
│ 设备管理        │    │ - 异步推送处理   │    │ - TV推送        │
└─────────────────┘    └──────────────────┘    │ - 商家端推送    │
                                               └─────────────────┘
```

## 14. 总结

### 14.1 当前活跃的消息系统
1. **MQTT消息系统**: 用于与收银设备通信，支持商品信息同步和订单状态通知
2. **GoEasy实时推送**: 用于Web端订单和系统消息推送
3. **WebSocket通信**: 用于电视设备控制和图片传输
4. **极光推送系统**:
   - 移动端APP消息推送
   - TV设备控制推送
   - 供应商订单通知
5. **信息推送系统**: 会员消息推送，支持全员和区域推送
6. **异步线程处理**: 后台任务和消息处理
7. **定时任务系统**: 定时推送消息数量统计到收银设备

### 14.2 已禁用的系统
1. **饿了么消息监听**: 外卖平台订单状态监听（已注释禁用）
2. **MQTT ServletContextListener**: 已被Spring @PostConstruct方式替代
3. **Session监听器**: 已注释禁用

### 14.3 消息系统分类

#### 14.3.1 实时通信系统
- **MQTT**: 设备间双向通信，支持商品同步、订单通知
- **GoEasy**: Web端实时推送，支持多频道订阅
- **WebSocket**: 电视设备控制和图片传输

#### 14.3.2 推送通知系统
- **极光推送**: 移动端、TV端推送通知
- **信息推送**: 会员消息推送系统

#### 14.3.3 外部平台集成
- **饿了么**: 外卖订单状态监听（已禁用）
- **美团配送**: 配送订单状态通知

#### 14.3.4 异步处理系统
- **线程池处理**: 支付后分拣排序、设备升级通知
- **事件监听**: 异步日志记录
- **定时任务**: 消息统计和推送

### 14.4 技术特点
- **高可用性**: MQTT支持自动重连，QoS保证消息可靠传递
- **实时性**: GoEasy提供毫秒级实时推送，WebSocket支持实时双向通信
- **扩展性**: 支持多设备、多频道、多平台的消息分发
- **容错性**: 异常处理完善，支持连接断开重试
- **异步处理**: 使用线程池处理推送任务，避免阻塞主业务
- **多应用支持**: 极光推送支持多个应用密钥配置
- **多协议支持**: 同时支持MQTT、WebSocket、HTTP等多种通信协议

### 14.5 消息类型总览

| 消息类型 | 传输方式 | 目标设备 | 业务场景 | 控制字段 | 状态 |
|---------|---------|---------|---------|---------|------|
| 商品更新 | MQTT | 收银设备 | 商品信息同步 | `msg_goods_update` | ✅ 活跃 |
| 商品更新V2 | MQTT | 收银设备 | 供应商商品同步 | `msg_goods_update_v2.0` | ✅ 活跃 |
| 商品分类更新 | MQTT | 收银设备 | 商品分类同步 | `msg_goods_kind_add` | ✅ 活跃 |
| 订单状态变更 | MQTT | 收银设备 | 配送订单状态通知 | `msg_order_change` | ✅ 活跃 |
| 设备升级通知 | MQTT | 收银设备 | PC升级指令 | `msg_pc_update` | ✅ 活跃 |
| 消息数量统计 | MQTT | 收银设备 | 未读消息统计 | `msg_news_count` | ✅ 活跃 |
| 实时通知 | GoEasy | Web前端 | 订单、系统消息 | - | ✅ 活跃 |
| 支付通知 | GoEasy | Web前端 | 支付成功通知 | - | ✅ 活跃 |
| 电视控制 | WebSocket | 电视设备 | 设备控制和截图 | - | ✅ 活跃 |
| 打印通信 | WebSocket | 打印组件 | Lodop打印功能 | - | ✅ 活跃 |
| APP推送 | 极光推送 | 移动端 | 订单、消息通知 | - | ✅ 活跃 |
| TV控制推送 | 极光推送 | 电视设备 | 设备控制指令 | - | ✅ 活跃 |
| 供应商推送 | 极光推送 | 移动端 | 云商订单通知 | `msgType=1` | ✅ 活跃 |
| 会员消息 | 数据库+H5 | 会员端 | 营销、通知消息 | - | ✅ 活跃 |
| 外卖订单 | WebSocket | 系统内部 | 饿了么订单状态 | - | ❌ 已禁用 |

### 14.6 维护建议

#### 14.6.1 监控建议
1. **MQTT连接监控**: 定期检查MQTT连接状态和消息发送成功率
2. **GoEasy服务监控**: 监控GoEasy推送服务的可用性和响应时间
3. **WebSocket连接监控**: 监控WebSocket连接状态和消息传输质量
4. **极光推送监控**: 监控推送成功率和到达率
5. **Redis缓存监控**: 监控设备MAC地址缓存的有效性
6. **线程池监控**: 监控异步线程池的执行状态和性能

#### 14.6.2 性能优化建议
1. **批量处理**: 对于大量设备的消息推送，考虑批量处理机制
2. **消息队列**: 引入专业的消息队列中间件（如RabbitMQ、Kafka）处理高并发场景
3. **缓存优化**: 优化Redis缓存策略，减少数据库查询
4. **异步处理**: 扩展异步处理机制，提高系统响应速度
5. **连接池管理**: 优化WebSocket和MQTT连接池配置
6. **消息压缩**: 对大容量消息进行压缩传输

#### 14.6.3 安全建议
1. **密钥管理**: 定期更换极光推送和GoEasy的密钥
2. **访问控制**: 加强MQTT服务器和WebSocket服务器的访问控制和认证机制
3. **消息加密**: 对敏感消息内容进行加密传输
4. **日志审计**: 完善消息推送的日志记录和审计机制
5. **防重放攻击**: 实现消息去重和防重放机制
6. **权限控制**: 细化设备和用户的消息接收权限

#### 14.6.4 业务扩展建议
1. **消息模板**: 建立统一的消息模板管理系统
2. **推送策略**: 实现更灵活的推送策略（如定时推送、条件推送）
3. **多渠道整合**: 整合短信、邮件等多种推送渠道
4. **数据分析**: 建立消息推送效果分析和统计系统
5. **消息路由**: 实现智能消息路由，根据设备类型和状态选择最优传输方式
6. **故障转移**: 建立多种通信方式的故障转移机制

#### 14.6.5 架构升级建议
1. **微服务化**: 将消息系统拆分为独立的微服务
2. **容器化部署**: 使用Docker容器化部署提高可维护性
3. **负载均衡**: 实现消息服务的负载均衡和高可用
4. **监控告警**: 建立完善的监控告警体系
5. **文档完善**: 持续完善API文档和操作手册