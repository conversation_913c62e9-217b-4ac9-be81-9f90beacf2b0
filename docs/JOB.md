# 定时任务管理文档 (Job Management)

本文档详细描述了海尔商城系统中所有的定时任务和后台作业。

## 目录
- [1. 任务配置](#1-任务配置)
- [2. 核心任务类](#2-核心任务类)
- [3. 工具类任务](#3-工具类任务)
- [4. 线程任务](#4-线程任务)
- [5. 监听器任务](#5-监听器任务)
- [6. 配置初始化任务](#6-配置初始化任务)
- [7. 任务调度配置](#7-任务调度配置)
- [8. 任务状态说明](#8-任务状态说明)
- [9. 业务流程总结](#9-业务流程总结)
- [10. 维护建议](#10-维护建议)
- [11. 快速导航](#11-快速导航)

## 1. 任务配置

### 1.1 Spring配置文件
**配置文件：** [`src/main/resources/conf/spring-mvc.xml`](../src/main/resources/conf/spring-mvc.xml)

系统使用Spring的`@Scheduled`注解和XML配置来管理定时任务。主要配置包括：

```xml
<task:annotation-driven /> <!-- 定时器开关 -->
<task:scheduled-tasks>
    <!-- 各种定时任务配置 -->
</task:scheduled-tasks>
```

## 2. 核心任务类

### 2.1 分销业务任务 (DisWork)
**文件路径：** [`src/main/java/org/haier/shop/task/DisWork.java`](../src/main/java/org/haier/shop/task/DisWork.java)

#### 主要功能：
- **分销佣金结算** (`checkDisTrading`)
  - **配置点：** [`spring-mvc.xml:126`](../src/main/resources/conf/spring-mvc.xml#L126)
  - **Bean定义：** [`spring-mvc.xml:97`](../src/main/resources/conf/spring-mvc.xml#L97)
  - **执行时间：** 每天凌晨 00:01 (`cron="1 0 0 * * ?"`)
  - **执行代码：** [`DisWork.java:47-93`](../src/main/java/org/haier/shop/task/DisWork.java#L47-L93)
  - **功能描述：** 查询一周前的分销信息，处理已完成配送的订单，增加可提现金额，减少待收益
  - **处理流程：**
    1. 查询七日之前未处理的订单信息
    2. 将已完成但未处理的信息设置为已处理状态
    3. 统计各个商家的佣金和值
    4. 修改团长在各个店铺的待收益佣金和可提现佣金

- **联通月度优惠券返还** (`ltReturnMonthCoupon`)
  - **配置点：** [`spring-mvc.xml:128`](../src/main/resources/conf/spring-mvc.xml#L128)
  - **执行时间：** 每月1号凌晨 01:00 (`cron="0 0 1 * * ?"`)
  - **执行代码：** [`DisWork.java:141-151`](../src/main/java/org/haier/shop/task/DisWork.java#L141-L151)
  - **功能描述：** 定时每月返还普通用户优惠券

- **联通特派员月度奖励** (`ltReturnParentMonthMoney`)
  - **配置点：** [`spring-mvc.xml:130`](../src/main/resources/conf/spring-mvc.xml#L130)
  - **执行时间：** 每月1号凌晨 01:00 (`cron="0 0 1 * * ?"`)
  - **执行代码：** [`DisWork.java:152-162`](../src/main/java/org/haier/shop/task/DisWork.java#L152-L162)
  - **功能描述：** 定时每月返还特派员奖励

- **会员积分清理** (`clearCusPoint`)
  - **配置点：** [`spring-mvc.xml:132`](../src/main/resources/conf/spring-mvc.xml#L132)
  - **执行时间：** 每小时整点 (`cron="0 0 0/1 * * ?"`)
  - **执行代码：** [`DisWork.java:153-162`](../src/main/java/org/haier/shop/task/DisWork.java#L153-L162)
  - **功能描述：** 定时清理会员积分

- **服务到期提醒** (`serverEndReport`)
  - **配置点：** [`spring-mvc.xml:134`](../src/main/resources/conf/spring-mvc.xml#L134)
  - **执行时间：** 每天凌晨 01:00 (`cron="0 0 1 * * ?"`)
  - **执行代码：** [`DisWork.java:125-135`](../src/main/java/org/haier/shop/task/DisWork.java#L125-L135)
  - **功能描述：** 服务到期短信提醒

### 2.2 商品自动采购任务 (GoodsAutoPurchase)
**文件路径：** [`src/main/java/org/haier/shop/task/GoodsAutoPurchase.java`](../src/main/java/org/haier/shop/task/GoodsAutoPurchase.java)

#### 主要功能：
- **商品自动采购** (`goodsAutoPurchase`)
  - **配置点：** [`spring-mvc.xml:151`](../src/main/resources/conf/spring-mvc.xml#L151)
  - **Bean定义：** [`spring-mvc.xml:94`](../src/main/resources/conf/spring-mvc.xml#L94)
  - **执行时间：** 每2小时执行一次 (`cron="0 0 0/2 * * ?"`)
  - **执行代码：** [`GoodsAutoPurchase.java:25-37`](../src/main/java/org/haier/shop/task/GoodsAutoPurchase.java#L25-L37)
  - **功能描述：** 自动采购商品，减少人工干预
  - **安全机制：** 仅在正式环境执行，通过`SysConfig.FORMAL`判断

### 2.3 电视下载任务 (TVDownload)
**文件路径：** [`src/main/java/org/haier/shop/task/TVDownload.java`](../src/main/java/org/haier/shop/task/TVDownload.java)

#### 主要功能：
- **电视内容下载** (`downLoadTvMsg`)
  - **配置点：** [`spring-mvc.xml:159`](../src/main/resources/conf/spring-mvc.xml#L159)
  - **Bean定义：** [`spring-mvc.xml:95`](../src/main/resources/conf/spring-mvc.xml#L95)
  - **执行时间：** 每3小时的第1分钟 (`cron="0 1 */3 * * ?"`)
  - **执行代码：** [`TVDownload.java:20-38`](../src/main/java/org/haier/shop/task/TVDownload.java#L20-L38)
  - **FTP工具类：** [`FtpUtils.java:68-73`](../src/main/java/org/haier/shop/task/FtpUtils.java#L68-L73)
  - **功能描述：** 从FTP服务器下载电视相关内容
  - **处理流程：**
    1. 查询FTP配置信息
    2. 建立FTP连接
    3. 下载指定路径的文件

### 2.4 数据统计任务 (DataDelivery)
**文件路径：** [`src/main/java/org/haier/shop/task/DataDelivery.java`](../src/main/java/org/haier/shop/task/DataDelivery.java)

#### 主要功能：
- **支付方式统计** (`addPayMethod`)
  - **配置点：** [`spring-mvc.xml:140`](../src/main/resources/conf/spring-mvc.xml#L140)
  - **执行时间：** 每天凌晨 01:01:11 (`cron="11 1 1 * * ?"`)
  - **执行代码：** [`DataDelivery.java:688-726`](../src/main/java/org/haier/shop/task/DataDelivery.java#L688-L726)
  - **功能描述：** 统计支付方式相关数据

- **总体数据统计** (`statisticsTotalYn`)
  - **配置点：** [`spring-mvc.xml:141`](../src/main/resources/conf/spring-mvc.xml#L141)
  - **执行时间：** 每天凌晨 01:01:01 (`cron="1 1 1 * * ?"`)
  - **执行代码：** [`DataDelivery.java:475-482`](../src/main/java/org/haier/shop/task/DataDelivery.java#L475-L482)
  - **功能描述：** 统计昨日的订单数据

- **宁宇会员统计** (`addNewCusStatisticsNy`)
  - **配置点：** [`spring-mvc.xml:142`](../src/main/resources/conf/spring-mvc.xml#L142)
  - **执行时间：** 每天凌晨 00:01:01 (`cron="1 1 0 * * ?"`)
  - **执行代码：** [`DataDelivery.java:37-54`](../src/main/java/org/haier/shop/task/DataDelivery.java#L37-L54)
  - **宁宇对账：** [`DataDelivery.java:237-470`](../src/main/java/org/haier/shop/task/DataDelivery.java#L237-L470)
  - **餐厅对账：** [`DataDelivery.java:55-234`](../src/main/java/org/haier/shop/task/DataDelivery.java#L55-L234)
  - **功能描述：** 新增宁宇会员信息统计，包括餐厅和宁宇两个店铺的对账
  - **对账内容：**
    - 会员余额对账
    - 充值、消费、退款数据核对
    - 自动发送钉钉通知对账结果

### 2.5 金圈币任务 (GoldTask) - 已迁移
**文件路径：** [`src/main/java/org/haier/shop/task/GoldTask.java`](../src/main/java/org/haier/shop/task/GoldTask.java)

#### 状态：已迁移到member项目
- **设备返金圈币** (`updateShopGold`)
  - **原配置点：** [`spring-mvc.xml:156`](../src/main/resources/conf/spring-mvc.xml#L156) (已注释)
  - **原Bean定义：** [`spring-mvc.xml:107`](../src/main/resources/conf/spring-mvc.xml#L107) (已注释)
  - **原执行时间：** 每天凌晨 02:00 (`cron="0 0 2 * * ?"`)
  - **执行代码：** [`GoldTask.java:40-74`](../src/main/java/org/haier/shop/task/GoldTask.java#L40-L74)
  - **功能描述：** 设备按月返金圈币
  - **当前状态：** 已迁移到member项目，本项目中已禁用

### 2.6 拉卡拉任务 (LakalaTask) - 已禁用
**文件路径：** [`src/main/java/org/haier/shop/task/LakalaTask.java`](../src/main/java/org/haier/shop/task/LakalaTask.java)

#### 状态：已禁用
- **未支付订单处理** (`handleUnpaidOrder`)
  - **原配置点：** [`spring-mvc.xml:143`](../src/main/resources/conf/spring-mvc.xml#L143) (已注释)
  - **原Bean定义：** [`spring-mvc.xml:103`](../src/main/resources/conf/spring-mvc.xml#L103) (已注释)
  - **原执行时间：** 每5秒执行一次 (`cron="*/5 * * * * ?"`)
  - **执行代码：** [`LakalaTask.java:33-120`](../src/main/java/org/haier/shop/task/LakalaTask.java#L33-L120)
  - **功能描述：** 检查未处理的订单
  - **当前状态：** 已在配置中注释禁用

### 2.7 联通红包任务 (UnicomRedPacket) - 已禁用
**文件路径：** [`src/main/java/org/haier/shop/task/UnicomRedPacket.java`](../src/main/java/org/haier/shop/task/UnicomRedPacket.java)

#### 状态：已禁用
- **会员红包发放** (`grantCusRedPacket`)
  - **原配置点：** [`spring-mvc.xml:144`](../src/main/resources/conf/spring-mvc.xml#L144) (已注释)
  - **原Bean定义：** [`spring-mvc.xml:104`](../src/main/resources/conf/spring-mvc.xml#L104) (已注释)
  - **原执行时间：** 每月1号凌晨 (`cron="0 0 0 1 1/1 ?"`)
  - **执行代码：** [`UnicomRedPacket.java:30-50`](../src/main/java/org/haier/shop/task/UnicomRedPacket.java#L30-L50)
  - **功能描述：** 每月发放红包

- **红包过期处理** (`redPacketOverdue`)
  - **原配置点：** [`spring-mvc.xml:145`](../src/main/resources/conf/spring-mvc.xml#L145) (已注释)
  - **原执行时间：** 每日凌晨 (`cron="0 0 0 1/1 * ?"`)
  - **执行代码：** [`UnicomRedPacket.java:51-61`](../src/main/java/org/haier/shop/task/UnicomRedPacket.java#L51-L61)
  - **功能描述：** 修改红包过期状态

### 2.8 分销自动降级任务 (DisAutoDown) - 已废弃
**文件路径：** [`src/main/java/org/haier/shop/task/DisAutoDown.java`](../src/main/java/org/haier/shop/task/DisAutoDown.java)

#### 状态：功能已废弃
- **分销自动降级** (`disLevelAutoDown`)
  - **原配置点：** [`spring-mvc.xml:146`](../src/main/resources/conf/spring-mvc.xml#L146) (已注释)
  - **原Bean定义：** [`spring-mvc.xml:105`](../src/main/resources/conf/spring-mvc.xml#L105) (已注释)
  - **原执行时间：** 每天凌晨 (`cron="0 0 0 1/1 * ?"`)
  - **执行代码：** [`DisAutoDown.java:30-50`](../src/main/java/org/haier/shop/task/DisAutoDown.java#L30-L50)
  - **功能描述：** 分销商自动降级
  - **当前状态：** 功能已废弃，配置中已注释

### 2.9 数据大屏任务 (DataScreen) - 已禁用
**文件路径：** [`src/main/java/org/haier/shop/task/DataScreen.java`](../src/main/java/org/haier/shop/task/DataScreen.java)

#### 状态：已禁用
- **到店人数统计** (`peopleArrivingAdd`)
  - **原配置点：** [`spring-mvc.xml:147`](../src/main/resources/conf/spring-mvc.xml#L147) (已注释)
  - **执行代码：** [`DataScreen.java:30-50`](../src/main/java/org/haier/shop/task/DataScreen.java#L30-L50)
- **在线店铺数量更新** (`resetShopOnlineCount`)
  - **原配置点：** [`spring-mvc.xml:148`](../src/main/resources/conf/spring-mvc.xml#L148) (已注释)
  - **执行代码：** [`DataScreen.java:51-70`](../src/main/java/org/haier/shop/task/DataScreen.java#L51-L70)
- **特殊日期数据处理** (`resetShopOnlineCountFour`)
  - **原配置点：** [`spring-mvc.xml:149`](../src/main/resources/conf/spring-mvc.xml#L149) (已注释)
  - **执行代码：** [`DataScreen.java:71-90`](../src/main/java/org/haier/shop/task/DataScreen.java#L71-L90)

#### 其他未调度方法（工具方法）：
- **最优店铺统计** (`theBestSallerShop`) - [`DataScreen.java:113-126`](../src/main/java/org/haier/shop/task/DataScreen.java#L113-L126)
- **店铺数据记录** (`addShopStartRecord`) - [`DataScreen.java:130-136`](../src/main/java/org/haier/shop/task/DataScreen.java#L130-L136)
- **收款额订单统计** (`resetListCountStage`) - [`DataScreen.java:141-159`](../src/main/java/org/haier/shop/task/DataScreen.java#L141-L159)
- **热力图数据更新** (`addNewHeatMap`) - [`DataScreen.java:164-173`](../src/main/java/org/haier/shop/task/DataScreen.java#L164-L173)
- **热销产品统计** (`addTopGoods`) - [`DataScreen.java:178-184`](../src/main/java/org/haier/shop/task/DataScreen.java#L178-L184)
- **会员年龄段消费统计** (`queryGroupByAgeNum`) - [`DataScreen.java:189-195`](../src/main/java/org/haier/shop/task/DataScreen.java#L189-L195)
- **会员年龄段人数统计** (`queryGroupByAgeCount`) - [`DataScreen.java:200-206`](../src/main/java/org/haier/shop/task/DataScreen.java#L200-L206)

### 2.10 消息推送任务 (SentMsgToAndroid)
**文件路径：** [`src/main/java/org/haier/shop/task/SentMsgToAndroid.java`](../src/main/java/org/haier/shop/task/SentMsgToAndroid.java)

#### 状态：线程任务（非定时调度）
- **Android消息推送** (`run`)
  - **触发方式：** 手动调用线程执行
  - **功能描述：** 向Android收银设备推送消息通知
  - **处理流程：**
    1. 从Redis缓存获取需要推送消息的店铺列表
    2. 查询各店铺的未读消息数量
    3. 通过MQTT向收银设备推送消息计数

## 3. 工具类任务

### 3.1 订单任务工具 (AddOrderTask)
**文件路径：** [`src/main/java/org/haier/shop/util/AddOrderTask.java`](../src/main/java/org/haier/shop/util/AddOrderTask.java)

#### 主要功能：
- **数据库连接保持** (`keepLive`)
  - **配置点：** [`spring-mvc.xml:115`](../src/main/resources/conf/spring-mvc.xml#L115)
  - **Bean定义：** [`spring-mvc.xml:99`](../src/main/resources/conf/spring-mvc.xml#L99)
  - **执行时间：** 每分钟第1秒 (`cron="1 * * * * ?"`)
  - **执行代码：** [`AddOrderTask.java:40-48`](../src/main/java/org/haier/shop/util/AddOrderTask.java#L40-L48)
  - **功能描述：** 保持数据库连接状态正常，防止回调时数据库连接异常

- **自动确认收货** (`confirmReceipt`)
  - **配置点：** [`spring-mvc.xml:117`](../src/main/resources/conf/spring-mvc.xml#L117)
  - **执行时间：** 每天6点每30秒执行一次 (`cron="*/30 6 * * * ?"`)
  - **执行代码：** [`AddOrderTask.java:49-85`](../src/main/java/org/haier/shop/util/AddOrderTask.java#L49-L85)
  - **功能描述：** 将未确认收货的订单自动确认收货

- **订单综合统计** (`statisticsTotal`)
  - **配置点：** [`spring-mvc.xml:123`](../src/main/resources/conf/spring-mvc.xml#L123)
  - **执行时间：** 每天凌晨 00:01:01 (`cron="1 1 0 * * ?"`)
  - **执行代码：** [`AddOrderTask.java:86-120`](../src/main/java/org/haier/shop/util/AddOrderTask.java#L86-L120)
  - **功能描述：** 每天定时统计订单综合数据

- **新增店铺数量统计** (`newShopCount`)
  - **配置点：** [`spring-mvc.xml:161`](../src/main/resources/conf/spring-mvc.xml#L161)
  - **执行时间：** 每12小时的第1分钟 (`cron="0 1 */12 * * ?"`)
  - **执行代码：** [`AddOrderTask.java:121-140`](../src/main/java/org/haier/shop/util/AddOrderTask.java#L121-L140)
  - **功能描述：** 定期修改新增店铺数量

- **清理新增店铺统计** (`clearNewShopCount`)
  - **配置点：** [`spring-mvc.xml:162`](../src/main/resources/conf/spring-mvc.xml#L162)
  - **执行时间：** 每周二凌晨 00:01 (`cron="0 1 0 * * 2"`)
  - **执行代码：** [`AddOrderTask.java:141-155`](../src/main/java/org/haier/shop/util/AddOrderTask.java#L141-L155)
  - **功能描述：** 清理新增店铺数量统计

### 3.2 天气数据任务 (WeatherUtil)
**文件路径：** [`src/main/java/org/haier/shop/util/WeatherUtil.java`](../src/main/java/org/haier/shop/util/WeatherUtil.java)

#### 主要功能：
- **天气数据获取** (`getWeatherUtil`)
  - **配置点：** [`spring-mvc.xml:146`](../src/main/resources/conf/spring-mvc.xml#L146)
  - **Bean定义：** [`spring-mvc.xml:90`](../src/main/resources/conf/spring-mvc.xml#L90)
  - **执行时间：** 每天凌晨 00:01 (`cron="1 0 0 * * ?"`)
  - **执行代码：** [`WeatherUtil.java:30-80`](../src/main/java/org/haier/shop/util/WeatherUtil.java#L30-L80)
  - **功能描述：** 定时统计各市的天气质量状况
  - **数据来源：** 京东万象API
  - **处理流程：**
    1. 获取城市列表
    2. 调用天气API获取数据
    3. 解析并存储天气信息和空气质量数据

### 3.3 商品销售统计 (StatisticsShopsMessage)
**文件路径：** [`src/main/java/org/haier/shop/util/StatisticsShopsMessage.java`](../src/main/java/org/haier/shop/util/StatisticsShopsMessage.java)

#### 主要功能：
- **商品销售统计** (`lastWeekGoodsSaleStatistics`)
  - **配置点：** [`spring-mvc.xml:137`](../src/main/resources/conf/spring-mvc.xml#L137)
  - **Bean定义：** [`spring-mvc.xml:91`](../src/main/resources/conf/spring-mvc.xml#L91)
  - **执行时间：** 每天凌晨 00:01 (`cron="0 1 0 * * ?"`)
  - **执行代码：** [`StatisticsShopsMessage.java:30-60`](../src/main/java/org/haier/shop/util/StatisticsShopsMessage.java#L30-L60)
  - **功能描述：** 清空goods_sale表并添加新的一周销售统计数据

- **店铺信息统计** (`statisticsShopsMessage`) - 已禁用
  - **原配置点：** [`spring-mvc.xml:147`](../src/main/resources/conf/spring-mvc.xml#L147) (已注释)
  - **执行代码：** [`StatisticsShopsMessage.java:61-120`](../src/main/java/org/haier/shop/util/StatisticsShopsMessage.java#L61-L120)
  - **功能描述：** 统计店铺的各项信息
  - **当前状态：** 配置中已注释

- **外键信息整理** (`collateForeignKey`) - 已禁用
  - **原配置点：** [`spring-mvc.xml:148`](../src/main/resources/conf/spring-mvc.xml#L148) (已注释)
  - **原执行时间：** 每年1月1日凌晨 05:01 (`cron="1 0 5 1 1 ?"`)
  - **执行代码：** [`StatisticsShopsMessage.java:121-180`](../src/main/java/org/haier/shop/util/StatisticsShopsMessage.java#L121-L180)
  - **功能描述：** 更新各店铺商品外键信息，整理商品规格数据
  - **当前状态：** 配置中已注释

- **百货豆统计** (`statisticsGoodsBeansMsg`) - 已禁用
  - **原配置点：** [`spring-mvc.xml:139`](../src/main/resources/conf/spring-mvc.xml#L139) (已注释)
  - **原执行时间：** 每天凌晨 00:01 (`cron="0 1 0 * * ?"`)
  - **执行代码：** [`StatisticsShopsMessage.java:181-240`](../src/main/java/org/haier/shop/util/StatisticsShopsMessage.java#L181-L240)
  - **功能描述：** 统计已开通百货豆的店铺每天的营收信息
  - **当前状态：** 配置中已注释

### 3.4 饿了么Token更新 (EleTokenUtil) - 已禁用
**文件路径：** [`src/main/java/org/haier/ele/util/EleTokenUtil.java`](../src/main/java/org/haier/ele/util/EleTokenUtil.java)

#### 状态：已禁用
- **饿了么授权Token更新** (`updateEleAuthToken`)
  - **原配置点：** [`spring-mvc.xml:171`](../src/main/resources/conf/spring-mvc.xml#L171) (已注释)
  - **原Bean定义：** [`spring-mvc.xml:99`](../src/main/resources/conf/spring-mvc.xml#L99) (已注释)
  - **原执行时间：** 每小时执行一次 (`cron="0 0 0/1 * * ?"`)
  - **执行代码：** [`EleTokenUtil.java:30-50`](../src/main/java/org/haier/ele/util/EleTokenUtil.java#L30-L50)
  - **功能描述：** 更新饿了么授权token
  - **当前状态：** 配置中已注释

### 3.5 蜂鸟配送Token更新 (ObtainTokenUtil) - 已禁用
**文件路径：** [`src/main/java/org/haier/eledelivery/util/ObtainTokenUtil.java`](../src/main/java/org/haier/eledelivery/util/ObtainTokenUtil.java)

#### 状态：已禁用
- **蜂鸟配送Token更新** (`updateAccessToken`)
  - **原配置点：** [`spring-mvc.xml:172`](../src/main/resources/conf/spring-mvc.xml#L172) (已注释)
  - **原Bean定义：** [`spring-mvc.xml:100`](../src/main/resources/conf/spring-mvc.xml#L100) (已注释)
  - **原执行时间：** 每小时执行一次 (`cron="0 0 0/1 * * ?"`)
  - **执行代码：** [`ObtainTokenUtil.java:30-50`](../src/main/java/org/haier/eledelivery/util/ObtainTokenUtil.java#L30-L50)
  - **功能描述：** 更新蜂鸟配送平台token
  - **当前状态：** 配置中已注释

### 3.6 Spring自动采购任务 (Spring_task) - 已禁用
**文件路径：** [`src/main/java/org/haier/shop/util/Spring_task.java`](../src/main/java/org/haier/shop/util/Spring_task.java)

#### 状态：已禁用
- **自动采购商品** (`autuPur`)
  - **原配置点：** [`spring-mvc.xml:173`](../src/main/resources/conf/spring-mvc.xml#L173) (已注释)
  - **原Bean定义：** [`spring-mvc.xml:101`](../src/main/resources/conf/spring-mvc.xml#L101) (已注释)
  - **原执行时间：** 每天执行一次 (`cron="0 0 0 * * ?"`)
  - **执行代码：** [`Spring_task.java:30-60`](../src/main/java/org/haier/shop/util/Spring_task.java#L30-L60)
  - **功能描述：** 自动采购商品
  - **当前状态：** 配置中已注释，功能已被GoodsAutoPurchase替代

## 4. 线程任务

### 4.1 分拣排序线程 (SortThread)
**文件路径：** [`src/main/java/org/haier/shop/thread/SortThread.java`](../src/main/java/org/haier/shop/thread/SortThread.java)

#### 功能描述：
- **触发时机：** 支付完成后调用
- **触发点：** [`OrderController.java:支付回调方法`](../src/main/java/org/haier/shop/controller/OrderController.java)
- **执行代码：** [`SortThread.java:15-35`](../src/main/java/org/haier/shop/thread/SortThread.java#L15-L35)
- **功能：** 重新修改分拣排序问题
- **执行方式：** 异步线程，等待2秒后发送GET请求

### 4.2 PC升级通知线程 (PcUploadThread)
**文件路径：** [`src/main/java/org/haier/shop/thread/PcUploadThread.java`](../src/main/java/org/haier/shop/thread/PcUploadThread.java)

#### 功能描述：
- **触发时机：** 后台升级成功后
- **触发点：** [`UploadController.java:升级接口`](../src/main/java/org/haier/shop/controller/UploadController.java)
- **执行代码：** [`PcUploadThread.java:20-60`](../src/main/java/org/haier/shop/thread/PcUploadThread.java#L20-L60)
- **功能：** 向收银设备发送升级指令
- **处理流程：**
  1. 查询需要升级的店铺和设备信息
  2. 对比缓存的macId登录信息
  3. 向收银设备发送升级指令

### 4.3 宁宇会员充值线程 (NYCustomerRechargeThread)
**文件路径：** [`src/main/java/org/haier/shop/thread/NYCustomerRechargeThread.java`](../src/main/java/org/haier/shop/thread/NYCustomerRechargeThread.java)

#### 功能描述：
- **触发时机：** 会员充值操作时
- **触发点：** [`CustomerController.java:充值接口`](../src/main/java/org/haier/shop/controller/CustomerController.java)
- **执行代码：** [`NYCustomerRechargeThread.java:25-80`](../src/main/java/org/haier/shop/thread/NYCustomerRechargeThread.java#L25-L80)
- **功能：** 批量处理宁宇会员充值
- **处理流程：**
  1. 查询会员信息
  2. 增加余额，扣除积分
  3. 创建充值记录
  4. 发送微信通知

### 4.4 文件进度监控线程 (FileProgressMonitor)
**文件路径：** [`src/main/java/org/haier/shop/util/sftp/FileProgressMonitor.java`](../src/main/java/org/haier/shop/util/sftp/FileProgressMonitor.java)

#### 功能描述：
- **功能：** SFTP文件传输进度监控
- **执行方式：** 继承TimerTask，使用ScheduledThreadPoolExecutor定时执行
- **执行代码：** [`FileProgressMonitor.java:12-149`](../src/main/java/org/haier/shop/util/sftp/FileProgressMonitor.java#L12-L149)
- **监控内容：** 文件传输进度、传输速度、完成状态
- **监控机制：**
  - 每1秒检查一次传输进度
  - 实时计算传输百分比
  - 通过单例模式提供进度查询接口
  - 传输完成后自动停止监控

### 4.5 定时任务测试类 (TimedTaskTest)
**文件路径：** [`src/main/java/org/haier/shop/util/TimedTaskTest.java`](../src/main/java/org/haier/shop/util/TimedTaskTest.java)

#### 功能描述：
- **功能：** Timer和TimerTask的测试示例
- **状态：** 测试代码，非生产环境使用

### 4.6 日志事件监听器 (LogEventListener)
**文件路径：** [`src/main/java/org/haier/log/event/LogEventListener.java`](../src/main/java/org/haier/log/event/LogEventListener.java)

#### 功能描述：
- **功能：** 异步处理系统日志记录
- **触发方式：** `@Async` + `@EventListener`
- **处理内容：** 将操作日志异步发送到远程日志服务

### 4.7 微信支付报告线程池 (WXPayReport)
**文件路径：** [`src/main/java/org/haier/shop/wxPay/WXPayReport.java`](../src/main/java/org/haier/shop/wxPay/WXPayReport.java)

#### 功能描述：
- **功能：** 微信支付相关报告处理
- **执行方式：** 使用ExecutorService线程池
- **处理内容：** 微信支付数据统计和报告生成

### 4.8 极光推送线程 (PushThread)
**文件路径：** [`src/main/java/org/haier/shop/util/PushThread.java`](../src/main/java/org/haier/shop/util/PushThread.java)

#### 功能描述：
- **触发时机：** 消息推送需求时
- **触发点：** [`MessageController.java:推送接口`](../src/main/java/org/haier/shop/controller/MessageController.java)
- **执行代码：** [`PushThread.java:20-80`](../src/main/java/org/haier/shop/util/PushThread.java#L20-L80)
- **功能：** 后台极光推送处理
- **推送类型：**
  - 全部用户推送 (pushType=1)
  - 指定用户推送 (pushType=2)
- **处理内容：** 向移动端推送消息通知

### 4.9 短信发送线程 (SmsThread)
**文件路径：** [`src/main/java/org/haier/shop/controller/SmsThread.java`](../src/main/java/org/haier/shop/controller/SmsThread.java)

#### 功能描述：
- **触发时机：** 短信发送需求时
- **触发点：** [`SmsController.java:发送短信接口`](../src/main/java/org/haier/shop/controller/SmsController.java)
- **执行代码：** [`SmsThread.java:15-40`](../src/main/java/org/haier/shop/controller/SmsThread.java#L15-L40)
- **功能：** 异步发送短信
- **处理内容：** 通过短信模板向指定手机号发送短信

### 4.10 FTP文件下载线程 (FtpUtils)
**文件路径：** [`src/main/java/org/haier/shop/task/FtpUtils.java`](../src/main/java/org/haier/shop/task/FtpUtils.java)

#### 功能描述：
- **触发时机：** 定时任务调用
- **触发点：** [`TVDownload.java:downLoadTvMsg方法`](../src/main/java/org/haier/shop/task/TVDownload.java#L20-L38)
- **执行代码：** [`FtpUtils.java:68-150`](../src/main/java/org/haier/shop/task/FtpUtils.java#L68-L150)
- **功能：** FTP文件下载处理
- **处理流程：**
  1. 连接FTP服务器
  2. 遍历远程目录文件
  3. 检查本地是否已存在
  4. 下载不存在的文件
- **文件类型：** 主要用于电视内容文件下载

### 4.11 钉钉通知线程 (SendDingDingTalkUtils)
**文件路径：** [`src/main/java/org/haier/shop/util/common/SendDingDingTalkUtils.java`](../src/main/java/org/haier/shop/util/common/SendDingDingTalkUtils.java)

#### 功能描述：
- **触发时机：** 对账任务完成后
- **触发点：** [`DataDelivery.java:对账方法`](../src/main/java/org/haier/shop/task/DataDelivery.java)
- **执行代码：** [`SendDingDingTalkUtils.java:55-62`](../src/main/java/org/haier/shop/util/common/SendDingDingTalkUtils.java#L55-L62)
- **功能：** 异步发送钉钉通知消息
- **执行方式：** 使用`cn.hutool.core.thread.ThreadUtil.execAsync()`异步执行
- **通知内容：** 对账结果、店铺信息、操作时间等
- **支持店铺类型：** 宁宇店铺和餐厅店铺（不同的钉钉群）

### 4.12 商品更新MQTT推送线程
**触发位置：** [`StockServiceImpl.java`](../src/main/java/org/haier/shop/service/StockServiceImpl.java)

#### 功能描述：
- **触发时机：** 商品入库操作完成后
- **执行方式：** 使用`cn.hutool.core.thread.ThreadUtil.execAsync()`异步执行
- **执行代码：** [`StockServiceImpl.java:352-354`](../src/main/java/org/haier/shop/service/StockServiceImpl.java#L352-L354)
- **功能：** 向收银设备推送商品更新信息
- **处理内容：** 将商品变更信息通过MQTT发送到对应店铺的收银设备

### 4.13 测试线程类 (MyThread) - 测试用
**文件路径：** [`src/main/java/org/haier/shop/test/MyThread.java`](../src/main/java/org/haier/shop/test/MyThread.java)

#### 功能描述：
- **功能：** 测试用的简单线程类
- **执行代码：** [`MyThread.java:6-15`](../src/main/java/org/haier/shop/test/MyThread.java#L6-L15)
- **状态：** 测试代码，非生产环境使用
- **功能：** 简单的线程睡眠测试

## 5. 监听器任务

### 5.1 饿了么消息推送监听器 (EleMessagePushListtener) - 已禁用
**文件路径：** [`src/main/java/org/haier/ele/listener/EleMessagePushListtener.java`](../src/main/java/org/haier/ele/listener/EleMessagePushListtener.java)

#### 状态：已禁用
- **监听器类型：** `ServletContextListener`
- **配置位置：** [`web.xml:72`](../src/main/webapp/WEB-INF/web.xml#L72) (已注释)
- **执行代码：** [`EleMessagePushListtener.java:31-117`](../src/main/java/org/haier/ele/listener/EleMessagePushListtener.java#L31-L117)
- **功能描述：** 监听饿了么平台的订单状态变化消息
- **处理内容：**
  - 订单生效通知 (type=10)
  - 商户接单通知 (type=12)
  - 配送状态变化 (type=52,53,55,56)
  - 订单取消通知 (type=14,15,17)
  - 退款相关通知 (type=30-36)
- **当前状态：** 已在web.xml中注释禁用

### 5.2 MQTT服务监听器 (MqttxServiceListener) - 已禁用
**文件路径：** [`src/main/java/org/haier/shop/util/mqtt/MqttxServiceListener.java`](../src/main/java/org/haier/shop/util/mqtt/MqttxServiceListener.java)

#### 状态：已禁用
- **监听器类型：** `ServletContextListener`
- **执行代码：** [`MqttxServiceListener.java:13-16`](../src/main/java/org/haier/shop/util/mqtt/MqttxServiceListener.java#L13-L16)
- **功能描述：** 在应用启动时初始化MQTT客户端连接
- **当前状态：** 代码存在但未在web.xml中配置，已被Spring的@PostConstruct方式替代

### 5.3 Session监听器 (SessionListener) - 已禁用
**文件路径：** [`src/main/java/org/haier/shop/test/SessionListener.java`](../src/main/java/org/haier/shop/test/SessionListener.java)

#### 状态：已禁用
- **监听器类型：** `HttpSessionListener`
- **配置位置：** [`web.xml:76`](../src/main/webapp/WEB-INF/web.xml#L76) (已注释)
- **执行代码：** [`SessionListener.java:13-23`](../src/main/java/org/haier/shop/test/SessionListener.java#L13-L23)
- **功能描述：** 统计在线Session数量
- **处理内容：**
  - Session创建时计数器+1
  - Session销毁时计数器-1
  - 提供静态方法获取当前在线数量
- **当前状态：** 已在web.xml中注释禁用

### 5.4 MQTT客户端初始化 (MqttxUtil) - 活跃
**文件路径：** [`src/main/java/org/haier/shop/util/mqtt/MqttxUtil.java`](../src/main/java/org/haier/shop/util/mqtt/MqttxUtil.java)

#### 状态：活跃
- **初始化方式：** `@PostConstruct`
- **执行代码：** [`MqttxUtil.java:25-31`](../src/main/java/org/haier/shop/util/mqtt/MqttxUtil.java#L25-L31)
- **功能描述：** 在Spring容器启动时自动初始化MQTT客户端连接
- **处理内容：**
  - 注入Redis缓存实例
  - 启动MQTT客户端连接
  - 提供静态方法发送MQTT消息
- **替代关系：** 替代了原来的`MqttxServiceListener`监听器方式

## 6. 配置初始化任务

### 6.1 系统配置初始化 (SysConfig)
**文件路径：** [`src/main/java/org/haier/shop/config/SysConfig.java`](../src/main/java/org/haier/shop/config/SysConfig.java)

#### 功能描述：
- **初始化方式：** `@PostConstruct`
- **执行代码：** [`SysConfig.java:66-90`](../src/main/java/org/haier/shop/config/SysConfig.java#L66-L90)
- **功能：** 系统启动时初始化各种配置参数
- **初始化内容：**
  - Redis测试环境配置
  - URL路径配置
  - 极光推送配置
  - 文件路径配置
  - 支付相关URL配置
  - MQTT服务器配置

### 6.2 金圈2.0配置初始化 (JQVersionTwoConfig)
**文件路径：** [`src/main/java/org/haier/shop/config/JQVersionTwoConfig.java`](../src/main/java/org/haier/shop/config/JQVersionTwoConfig.java)

#### 功能描述：
- **初始化方式：** `@PostConstruct`
- **执行代码：** [`JQVersionTwoConfig.java:27-37`](../src/main/java/org/haier/shop/config/JQVersionTwoConfig.java#L27-L37)
- **功能：** 初始化金圈2.0相关API地址
- **初始化内容：**
  - 基础URL配置
  - 卡片管理API地址
  - 文件上传API地址

### 6.3 项目配置初始化 (ProjectConfig)
**文件路径：** [`src/main/java/org/haier/shop/config/ProjectConfig.java`](../src/main/java/org/haier/shop/config/ProjectConfig.java)

#### 功能描述：
- **初始化方式：** `@PostConstruct`
- **执行代码：** [`ProjectConfig.java:30-34`](../src/main/java/org/haier/shop/config/ProjectConfig.java#L30-L34)
- **功能：** 初始化项目相关配置
- **初始化内容：**
  - 项目地址配置
  - 纳统地址配置

### 6.4 远程配置初始化 (RemoteConfig)
**文件路径：** [`src/main/java/org/haier/shop/controller/config/RemoteConfig.java`](../src/main/java/org/haier/shop/controller/config/RemoteConfig.java)

#### 功能描述：
- **初始化方式：** `@PostConstruct`
- **执行代码：** [`RemoteConfig.java:17-21`](../src/main/java/org/haier/shop/controller/config/RemoteConfig.java#L17-L21)
- **功能：** 初始化AI相关配置
- **初始化内容：**
  - AI服务URL配置

## 7. 任务调度配置

### 7.1 配置文件位置
- **主配置文件：** [`spring-mvc.xml`](../src/main/resources/conf/spring-mvc.xml)
- **任务开关：** [`spring-mvc.xml:86`](../src/main/resources/conf/spring-mvc.xml#L86) (`<task:annotation-driven />`)
- **Bean定义区域：** [`spring-mvc.xml:90-111`](../src/main/resources/conf/spring-mvc.xml#L90-L111)
- **调度配置区域：** [`spring-mvc.xml:113-164`](../src/main/resources/conf/spring-mvc.xml#L113-L164)

### 7.2 当前活跃任务列表

| 任务名称 | 执行时间 | 状态 | 配置行号 | 描述 |
|---------|---------|------|---------|------|
| keepLive | 每分钟第1秒 | ✅ 活跃 | [L115](../src/main/resources/conf/spring-mvc.xml#L115) | 保持数据库连接 |
| confirmReceipt | 每天6点每30秒 | ✅ 活跃 | [L117](../src/main/resources/conf/spring-mvc.xml#L117) | 自动确认收货 |
| statisticsTotal | 每天00:01:01 | ✅ 活跃 | [L123](../src/main/resources/conf/spring-mvc.xml#L123) | 订单综合统计 |
| checkDisTrading | 每天00:01 | ✅ 活跃 | [L126](../src/main/resources/conf/spring-mvc.xml#L126) | 分销佣金结算 |
| ltReturnMonthCoupon | 每月1号01:00 | ✅ 活跃 | [L128](../src/main/resources/conf/spring-mvc.xml#L128) | 联通月度优惠券 |
| ltReturnParentMonthMoney | 每月1号01:00 | ✅ 活跃 | [L130](../src/main/resources/conf/spring-mvc.xml#L130) | 联通特派员奖励 |
| clearCusPoint | 每小时整点 | ✅ 活跃 | [L132](../src/main/resources/conf/spring-mvc.xml#L132) | 清理会员积分 |
| serverEndReport | 每天01:00 | ✅ 活跃 | [L134](../src/main/resources/conf/spring-mvc.xml#L134) | 服务到期提醒 |
| lastWeekGoodsSaleStatistics | 每天00:01 | ✅ 活跃 | [L137](../src/main/resources/conf/spring-mvc.xml#L137) | 商品销售统计 |
| addPayMethod | 每天01:01:11 | ✅ 活跃 | [L140](../src/main/resources/conf/spring-mvc.xml#L140) | 支付方式统计 |
| statisticsTotalYn | 每天01:01:01 | ✅ 活跃 | [L141](../src/main/resources/conf/spring-mvc.xml#L141) | 总体数据统计 |
| addNewCusStatisticsNy | 每天00:01:01 | ✅ 活跃 | [L142](../src/main/resources/conf/spring-mvc.xml#L142) | 宁宇会员统计 |
| getWeatherUtil | 每天00:01 | ✅ 活跃 | [L146](../src/main/resources/conf/spring-mvc.xml#L146) | 天气数据获取 |
| goodsAutoPurchase | 每2小时 | ✅ 活跃 | [L151](../src/main/resources/conf/spring-mvc.xml#L151) | 商品自动采购 |
| downLoadTvMsg | 每3小时第1分钟 | ✅ 活跃 | [L159](../src/main/resources/conf/spring-mvc.xml#L159) | 电视内容下载 |
| newShopCount | 每12小时第1分钟 | ✅ 活跃 | [L161](../src/main/resources/conf/spring-mvc.xml#L161) | 新增店铺统计 |
| clearNewShopCount | 每周二00:01 | ✅ 活跃 | [L162](../src/main/resources/conf/spring-mvc.xml#L162) | 清理店铺统计 |

### 7.3 已禁用任务列表

| 任务名称 | 原执行时间 | 状态 | 配置行号 | 禁用原因 |
|---------|-----------|------|---------|----------|
| grantCusRedPacket | 每月1号凌晨 | ❌ 禁用 | [L120](../src/main/resources/conf/spring-mvc.xml#L120) | 联通红包功能暂停 |
| redPacketOverdue | 每日凌晨 | ❌ 禁用 | [L122](../src/main/resources/conf/spring-mvc.xml#L122) | 联通红包功能暂停 |
| handleUnpaidOrder | 每5秒 | ❌ 禁用 | [L143](../src/main/resources/conf/spring-mvc.xml#L143) | 拉卡拉功能不再使用 |
| autuPur | 每天执行一次 | ❌ 禁用 | [L145](../src/main/resources/conf/spring-mvc.xml#L145) | 已被新的自动采购替代 |
| statisticsShopsMessage | 每天00:01 | ❌ 禁用 | [L147](../src/main/resources/conf/spring-mvc.xml#L147) | 店铺统计功能调整 |
| collateForeignKey | 每年1月1日05:01 | ❌ 禁用 | [L148](../src/main/resources/conf/spring-mvc.xml#L148) | 外键整理功能暂停 |
| statisticsGoodsBeansMsg | 每天00:01 | ❌ 禁用 | [L139](../src/main/resources/conf/spring-mvc.xml#L139) | 百货豆统计功能暂停 |
| updateEleAuthToken | 每小时 | ❌ 禁用 | [L149](../src/main/resources/conf/spring-mvc.xml#L149) | 饿了么集成暂停 |
| updateAccessToken | 每小时 | ❌ 禁用 | [L150](../src/main/resources/conf/spring-mvc.xml#L150) | 蜂鸟配送暂停 |
| disLevelAutoDown | 每天凌晨 | ❌ 废弃 | [L153](../src/main/resources/conf/spring-mvc.xml#L153) | 分销降级功能废弃 |
| updateShopGold | 每天02:00 | ❌ 迁移 | [L156](../src/main/resources/conf/spring-mvc.xml#L156) | 已迁移到member项目 |

### 5.4 未调度的功能方法

DataDelivery类中还包含一些未被定时调度但可能被手动调用的方法：

| 方法名称 | 功能描述 | 代码位置 | 状态 |
|---------|---------|---------|------|
| insertOrder | 添加模拟订单数据 | [`DataDelivery.java:727-759`](../src/main/java/org/haier/shop/task/DataDelivery.java#L727-L759) | 🔧 工具方法 |
| updateLogisticsTimeDistance | 更新物流配送时间和距离 | [`DataDelivery.java:483-520`](../src/main/java/org/haier/shop/task/DataDelivery.java#L483-L520) | 🔧 工具方法 |
| updateOneMinute | 更新一刻钟配送信息 | [`DataDelivery.java:521-560`](../src/main/java/org/haier/shop/task/DataDelivery.java#L521-L560) | 🔧 工具方法 |
| updateOneMinuteZero | 一刻钟配送信息订单数清零 | [`DataDelivery.java:561-580`](../src/main/java/org/haier/shop/task/DataDelivery.java#L561-L580) | 🔧 工具方法 |

AddOrderTask类中还包含多个模拟数据生成方法（用于演示和测试）：

| 方法名称 | 功能描述 | 代码位置 | 状态 |
|---------|---------|---------|------|
| addOrderTaskNormal | 生成正常数据模式的订单 | [`AddOrderTask.java:156-200`](../src/main/java/org/haier/shop/util/AddOrderTask.java#L156-L200) | 🔧 演示方法 |
| jbaddOrderTask | 生成加倍数据模式的订单 | [`AddOrderTask.java:201-250`](../src/main/java/org/haier/shop/util/AddOrderTask.java#L201-L250) | 🔧 演示方法 |
| addOrderTaskFour | 生成除夕数据模式（减半） | [`AddOrderTask.java:251-300`](../src/main/java/org/haier/shop/util/AddOrderTask.java#L251-L300) | 🔧 演示方法 |
| addOrderTaskFive | 生成正月初一数据（停工） | [`AddOrderTask.java:301-350`](../src/main/java/org/haier/shop/util/AddOrderTask.java#L301-L350) | 🔧 演示方法 |
| addOrderTaskSix | 生成正月初二数据（恢复） | [`AddOrderTask.java:351-400`](../src/main/java/org/haier/shop/util/AddOrderTask.java#L351-L400) | 🔧 演示方法 |
| addOrderTaskSeven | 生成正月初三数据（恢复） | [`AddOrderTask.java:401-450`](../src/main/java/org/haier/shop/util/AddOrderTask.java#L401-L450) | 🔧 演示方法 |
| addNewOnlineCount | 添加在线数量统计数据 | [`AddOrderTask.java:451-480`](../src/main/java/org/haier/shop/util/AddOrderTask.java#L451-L480) | 🔧 工具方法 |

## 8. 任务状态说明

### 8.1 环境控制
大部分任务都有环境控制机制，通过以下配置确保任务只在指定环境执行：

```java
// 正式环境执行
if(SysConfig.FORMAL.equals(SysConfig.REDISTEST)) {
    // 执行任务逻辑
}

// 测试环境跳过
if(SysConfig.TEST.equals(SysConfig.REDISTEST)) {
    return;
}
```

### 8.2 IP地址限制
部分任务有IP地址限制，防止多服务器并发执行：

```java
String ip = IPGet.ExtranetIP;
if(IPGet.ExtranetIP.equals(AddOrderTask.IP18)) {
    // 只在指定IP的服务器执行
}
```

### 8.3 异常处理
所有任务都包含完善的异常处理机制：
- 日志记录任务开始和结束
- 异常捕获和日志输出
- 关键任务失败时的通知机制（如钉钉通知）

### 8.4 监控和通知
- **钉钉通知：** 重要任务（如对账）会发送钉钉消息通知结果
- **日志记录：** 所有任务都有详细的日志记录
- **数据库监控：** 通过keepLive任务监控数据库连接状态

## 9. 业务流程总结

### 9.1 核心业务流程时间线

#### 每日凌晨任务流程（00:00-02:00）
1. **00:01** - 分销佣金结算 (`checkDisTrading`)
2. **00:01** - 天气数据获取 (`getWeatherUtil`)
3. **00:01** - 商品销售统计 (`lastWeekGoodsSaleStatistics`)
4. **00:01:01** - 宁宇会员统计 (`addNewCusStatisticsNy`)
5. **00:01:01** - 订单综合统计 (`statisticsTotal`)
6. **01:00** - 联通月度优惠券返还 (`ltReturnMonthCoupon`, 每月1号)
7. **01:00** - 联通特派员奖励 (`ltReturnParentMonthMoney`, 每月1号)
8. **01:00** - 服务到期提醒 (`serverEndReport`)
9. **01:01:01** - 总体数据统计 (`statisticsTotalYn`)
10. **01:01:11** - 支付方式统计 (`addPayMethod`)

#### 定期维护任务
- **每小时整点** - 清理会员积分 (`clearCusPoint`)
- **每2小时** - 商品自动采购 (`goodsAutoPurchase`)
- **每3小时第1分钟** - 电视内容下载 (`downLoadTvMsg`)
- **每12小时第1分钟** - 新增店铺统计 (`newShopCount`)
- **每周二00:01** - 清理店铺统计 (`clearNewShopCount`)

### 9.2 关键业务依赖关系
- **分销业务链：** 订单完成 → 分销佣金结算 → 可提现金额更新
- **数据统计链：** 订单数据 → 各类统计任务 → 数据大屏展示
- **会员服务链：** 会员操作 → 积分/余额变更 → 微信通知推送
- **商品管理链：** 库存变化 → 自动采购 → MQTT设备通知

## 10. 维护建议

### 10.1 任务监控
- 定期检查任务执行日志
- 监控任务执行时间和性能
- 关注异常任务的错误日志
- 重点关注凌晨时段的任务集中执行情况

### 10.2 配置管理
- 新增任务时注意环境控制
- 禁用任务时使用注释而非删除
- 重要任务变更需要充分测试
- 注意IP地址限制的任务部署

### 10.3 性能优化
- 避免在高峰期执行重量级任务
- 合理设置任务执行间隔
- 对于数据量大的任务考虑分批处理
- 监控凌晨任务集中执行的服务器负载

## 11. 快速导航

### 11.1 配置文件快速链接
- **主配置文件：** [`spring-mvc.xml`](../src/main/resources/conf/spring-mvc.xml)
- **任务开关：** [`<task:annotation-driven />`](../src/main/resources/conf/spring-mvc.xml#L86)
- **Bean定义区域：** [`Lines 90-111`](../src/main/resources/conf/spring-mvc.xml#L90-L111)
- **调度配置区域：** [`Lines 113-164`](../src/main/resources/conf/spring-mvc.xml#L113-L164)

### 11.2 核心任务类快速链接
- **分销业务：** [`DisWork.java`](../src/main/java/org/haier/shop/task/DisWork.java)
- **数据统计：** [`DataDelivery.java`](../src/main/java/org/haier/shop/task/DataDelivery.java)
- **订单工具：** [`AddOrderTask.java`](../src/main/java/org/haier/shop/util/AddOrderTask.java)
- **商品采购：** [`GoodsAutoPurchase.java`](../src/main/java/org/haier/shop/task/GoodsAutoPurchase.java)
- **电视下载：** [`TVDownload.java`](../src/main/java/org/haier/shop/task/TVDownload.java)
- **天气数据：** [`WeatherUtil.java`](../src/main/java/org/haier/shop/util/WeatherUtil.java)
- **销售统计：** [`StatisticsShopsMessage.java`](../src/main/java/org/haier/shop/util/StatisticsShopsMessage.java)

### 11.3 线程任务快速链接
- **分拣排序：** [`SortThread.java`](../src/main/java/org/haier/shop/thread/SortThread.java)
- **PC升级：** [`PcUploadThread.java`](../src/main/java/org/haier/shop/thread/PcUploadThread.java)
- **会员充值：** [`NYCustomerRechargeThread.java`](../src/main/java/org/haier/shop/thread/NYCustomerRechargeThread.java)
- **消息推送：** [`PushThread.java`](../src/main/java/org/haier/shop/util/PushThread.java)
- **短信发送：** [`SmsThread.java`](../src/main/java/org/haier/shop/controller/SmsThread.java)

### 11.4 已禁用任务快速链接
- **金圈币任务：** [`GoldTask.java`](../src/main/java/org/haier/shop/task/GoldTask.java) (已迁移)
- **拉卡拉任务：** [`LakalaTask.java`](../src/main/java/org/haier/shop/task/LakalaTask.java) (已禁用)
- **联通红包：** [`UnicomRedPacket.java`](../src/main/java/org/haier/shop/task/UnicomRedPacket.java) (已禁用)
- **分销降级：** [`DisAutoDown.java`](../src/main/java/org/haier/shop/task/DisAutoDown.java) (已废弃)
- **数据大屏：** [`DataScreen.java`](../src/main/java/org/haier/shop/task/DataScreen.java) (已禁用)

---

**最后更新时间：** 2024年12月
**维护人员：** 系统管理员
**文档版本：** v2.1 - 新增业务流程总结和异步任务详细说明
**相关文档：** [业务模块文档](BIZ.md) | [API文档](API.md)

> 💡 **提示：** 点击任何代码链接可直接跳转到对应的文件和行号，便于快速定位和修改。本文档以业务流程为主线，详细记录了所有定时任务、异步任务和后台作业的配置、触发点和执行逻辑。