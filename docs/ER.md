# 数据库ER图文档

本文档展示了金圈平台的完整数据库实体关系图(ER图)，描述了各表之间的关联关系。

## 目录
- [1. 核心业务概览](#1-核心业务概览)
- [2. 店铺管理模块](#2-店铺管理模块)
- [3. 商品管理模块](#3-商品管理模块)
- [4. 订单管理模块](#4-订单管理模块)
- [5. 会员管理模块](#5-会员管理模块)
- [6. 第三方集成模块](#6-第三方集成模块)
- [7. 系统管理模块](#7-系统管理模块)
- [8. 统计分析模块](#8-统计分析模块)

---

## 数据库架构概述

系统采用**多数据源架构**，主要包含以下数据库：
- **主数据库(shop)**: 存储核心业务数据
- **采购数据库(purchase)**: 存储采购相关数据
- **第三方数据库**: 存储外部平台集成数据

> **配置文件**: [`spring-mybatis.xml`](../src/main/resources/conf/spring-mybatis.xml#L26)

---

## 1. 核心业务概览

以下ER图展示了系统核心业务实体之间的主要关系：

```mermaid
erDiagram
    shops ||--o{ goods : "店铺拥有商品"
    shops ||--o{ sale_list : "店铺产生订单"
    shops ||--o{ customer_checkout : "店铺拥有会员"
    shops ||--o{ shop_staff : "店铺拥有员工"

    goods ||--o{ sale_list_detail : "商品销售明细"
    goods }o--|| goods_kind : "商品属于分类"

    sale_list ||--o{ sale_list_detail : "订单包含明细"
    sale_list ||--o{ return_list : "订单可退货"
    sale_list }o--o| customer_checkout : "会员下单"

    return_list ||--o{ return_list_detail : "退货包含明细"

    customer_checkout ||--o{ customer_recharge : "会员充值记录"
    customer_checkout }o--|| customer_level : "会员等级"

    area_dict ||--o{ shops : "区域包含店铺"

    shops {
        bigint shop_unique PK "店铺唯一标识"
        varchar shop_name "店铺名称"
        varchar manager_account "管理员账号"
        varchar shop_address_detail "详细地址"
        varchar area_dict_num FK "区域编号"
        int shop_status "营业状态"
        decimal shop_balance "店铺余额"
    }

    goods {
        int goods_id PK "商品ID"
        bigint shop_unique FK "店铺标识"
        varchar goods_barcode UK "商品条码"
        varchar goods_name "商品名称"
        varchar goods_kind_unique FK "分类标识"
        decimal goods_sale_price "售价"
        decimal goods_count "库存数量"
    }

    sale_list {
        bigint sale_list_unique PK "订单唯一标识"
        bigint shop_unique FK "店铺标识"
        varchar cus_unique FK "客户标识"
        decimal sale_list_total "订单总金额"
        int sale_list_state "付款状态"
        int sale_list_handlestate "处理状态"
        datetime sale_list_datetime "订单时间"
    }

    customer_checkout {
        varchar cus_unique PK "会员唯一标识"
        bigint shop_unique FK "店铺标识"
        varchar cus_name "会员姓名"
        varchar cus_phone "会员电话"
        decimal cus_balance "会员余额"
        int level_id FK "会员等级"
    }
```

---

## 2. 店铺管理模块

店铺管理模块包含店铺基础信息、配置、员工和区域等相关表：

```mermaid
erDiagram
    shops ||--|| shops_config : "一对一配置"
    shops ||--o{ shop_staff : "一对多员工"
    shops }o--|| area_dict : "属于区域"
    area_dict ||--o{ area_dict : "层级关系"
    shop_staff }o--|| sys_role : "员工角色"

    shops {
        int shop_id PK "主键ID"
        bigint shop_unique UK "店铺唯一标识"
        varchar shop_name "店铺名称"
        int examinestatus "审核状态"
        int shop_type "店铺类型"
        varchar manager_unique "管理员标识"
        varchar manager_account "管理员账号"
        varchar manager_pwd "管理员密码"
        varchar shop_alias "店铺别名"
        decimal shop_latitude "纬度"
        decimal shop_longitude "经度"
        varchar shop_address_detail "详细地址"
        varchar shop_phone "联系电话"
        varchar area_dict_num FK "区域编号"
        int shop_status "营业状态"
        decimal shop_balance "店铺余额"
        bigint shop_beans "百货豆数量"
        int auto_purchase "自动补货开关"
        int kind_type "分类类型"
        varchar company_code "连锁总店代码"
    }

    shops_config {
        int id PK "主键ID"
        bigint shop_unique UK,FK "店铺标识"
        varchar cus_unique "关联客户"
        int shop_flower "鲜花功能"
        int shop_deliverwater "送水功能"
        int shop_laundry "洗衣功能"
        int shop_express "快递功能"
        int shop_homemarking "家政功能"
        int shop_cake "蛋糕功能"
        int shop_fruit "水果功能"
        int shop_pur "采购功能"
        int negative_sale "负库存销售"
        int below_cost "低于成本价销售"
        int auto_pur "自动采购"
        int auto_pur_days "自动采购天数"
        int unsalable_days "滞销天数"
        int out_stock_warning_days "缺货预警天数"
        int out_stock_days "缺货天数"
    }

    shop_staff {
        int staff_id PK "员工ID"
        bigint shop_unique FK "店铺标识"
        varchar staff_account UK "员工账号"
        varchar staff_pwd "员工密码"
        varchar staff_name "员工姓名"
        varchar staff_phone "员工电话"
        varchar manager_unique "管理员标识"
        int staff_position "员工职位"
        varchar role_code FK "角色代码"
        int examinestatus "审核状态"
        datetime create_time "创建时间"
        datetime update_time "更新时间"
    }

    area_dict {
        int area_dict_id PK "区域ID"
        varchar area_dict_num UK "区域编号"
        varchar area_dict_content "区域名称"
        varchar area_dict_parent_num FK "父级区域编号"
        int area_dict_level "区域级别"
    }

    sys_role {
        int role_id PK "角色ID"
        varchar role_code UK "角色编码"
        varchar role_name "角色名称"
        varchar role_desc "角色描述"
        datetime create_time "创建时间"
        int del_flag "删除标志"
    }
```

---

## 3. 商品管理模块

商品管理模块包含商品信息、分类、库存和基础字典等相关表：

```mermaid
erDiagram
    shops ||--o{ goods : "店铺拥有商品"
    goods }o--|| goods_kind : "商品属于分类"
    goods_kind ||--o{ goods_kind : "分类层级关系"
    goods ||--o{ goods_online_setting : "商品线上设置"
    goods ||--o{ shop_stock : "库存变动记录"
    goods_dict ||--o{ goods : "字典商品被添加"

    goods {
        int goods_id PK "商品ID"
        bigint shop_unique FK "店铺标识"
        varchar goods_barcode UK "商品条码"
        varchar goods_name "商品名称"
        varchar goods_alias "商品别名"
        varchar goods_kind_unique FK "分类标识"
        varchar goods_brand "商品品牌"
        decimal goods_in_price "进价"
        decimal goods_sale_price "售价"
        decimal goods_web_sale_price "网购价格"
        decimal goods_cus_price "会员价格"
        int goods_life "保质期天数"
        int goods_points "积分"
        varchar goods_picturepath "图片路径"
        text goods_remarks "商品备注"
        bigint foreign_key "包装分类外键"
        decimal goods_count "库存数量"
        decimal goods_sold "已售数量"
        int goods_hits "点击率"
        varchar goods_standard "商品规格"
        varchar goods_unit "计价单位"
        varchar default_supplier_unique "默认供应商"
        int sametype "同步状态"
        datetime update_time "更新时间"
        int shelf_state "线上上架状态"
        int pc_shelf_state "PC收银上架状态"
        decimal goods_contain "换算比例"
        int goods_cheng_type "称重类型"
        int order_sort "排序序号"
        varchar goods_address "商品产地"
    }

    goods_kind {
        int goods_kind_id PK "分类ID"
        bigint shop_unique FK "店铺标识"
        varchar goods_kind_unique UK "分类唯一标识"
        varchar goods_kind_name "分类名称"
        varchar goods_kind_parunique FK "父级分类标识"
        int goods_kind_level "分类级别"
        int order_sort "排序序号"
        int kind_type "分类类型"
    }

    goods_dict {
        int id PK "字典ID"
        varchar barcode UK "商品条码"
        varchar name "商品名称"
        varchar standard "商品规格"
        varchar unit "计价单位"
        varchar picture_name "图片名称"
        decimal inprice "建议进价"
        decimal saleprice "建议售价"
        datetime create_time "创建时间"
    }

    goods_online_setting {
        int id PK "设置ID"
        int goods_id UK,FK "商品ID"
        decimal min_sale_count "最小起购量"
        decimal increment_count "递增数量"
    }

    shop_stock {
        int id PK "流水ID"
        varchar goods_barcode FK "商品条码"
        decimal goods_count "商品当前库存"
        decimal stock_count "变动数量"
        int stock_type "变动类型"
        datetime stock_time "变动时间"
        bigint shop_unique FK "店铺标识"
        varchar stock_resource "变动来源"
        varchar list_unique "关联单据号"
        decimal stock_price "变动价格"
        int stock_origin "操作来源"
        int staff_id "操作员工ID"
    }
```

---

## 4. 订单管理模块

订单管理模块包含销售订单、退货订单及其明细等相关表：

```mermaid
erDiagram
    shops ||--o{ sale_list : "店铺产生订单"
    customer_checkout ||--o{ sale_list : "会员下单"
    sale_list ||--o{ sale_list_detail : "订单包含明细"
    sale_list ||--o{ return_list : "订单可退货"
    return_list ||--o{ return_list_detail : "退货包含明细"
    goods ||--o{ sale_list_detail : "商品销售明细"
    goods ||--o{ return_list_detail : "商品退货明细"

    sale_list {
        int sale_list_id PK "订单ID"
        bigint sale_list_unique UK "订单唯一标识"
        bigint shop_unique FK "店铺标识"
        datetime sale_list_datetime "订单生成时间"
        decimal sale_list_total "销售金额总计"
        decimal sale_list_pur "订单进货价总计"
        int sale_list_totalCount "销售商品总数量"
        varchar cus_unique FK "客户唯一标识"
        int sale_type "订单类型"
        varchar sale_list_name "收货人姓名"
        varchar sale_list_phone "收货人电话"
        varchar sale_list_address "收货地址"
        decimal sale_list_delfee "配送费"
        decimal sale_list_discount "订单折扣率"
        int sale_list_state "付款状态"
        int sale_list_handlestate "处理状态"
        int sale_list_payment "支付方式"
        text sale_list_remarks "订单备注"
        int sale_list_number "订单每日序号"
        int sale_list_cashier "收银员ID"
        int machine_num "收银机器编号"
        decimal sale_list_actually_received "实际收到金额"
        decimal member_card "会员卡支付金额"
        decimal beans_money "百货豆支付金额"
        decimal card_deduction "储值卡扣款金额"
        decimal coupon_amount "优惠券金额"
        datetime pay_time "支付时间"
        varchar trade_no "第三方交易流水号"
        varchar cancel_reason "取消订单原因"
    }

    sale_list_detail {
        int sale_list_detail_id PK "明细ID"
        bigint sale_list_unique FK "订单唯一标识"
        varchar goods_barcode FK "商品条码"
        varchar goods_name "商品名称"
        decimal sale_list_detail_count "销售数量"
        decimal sale_list_detail_price "销售单价"
        decimal sale_list_detail_total "明细总金额"
        decimal goods_pur_price "商品进价"
        bigint goods_id FK "商品ID"
        varchar goods_standard "商品规格"
        varchar goods_unit "商品单位"
    }

    return_list {
        int id PK "退货ID"
        varchar ret_list_unique UK "退货单唯一标识"
        bigint sale_list_unique FK "原销售订单号"
        bigint shop_unique FK "店铺标识"
        datetime ret_list_datetime "退货申请时间"
        decimal ret_list_total "退货总金额"
        decimal ret_list_total_money "实际退款金额"
        int ret_list_count "退货商品总数量"
        int ret_list_state "退款状态"
        int ret_list_handlestate "处理状态"
        text ret_list_remarks "退货备注"
        int staff_id "处理员工ID"
        varchar mac_id "操作设备MAC"
        int ret_origin "退货来源"
        int ret_money_type "退款方式"
        datetime ret_back_datetime "退款完成时间"
        decimal ret_list_bean "退回百货豆数量"
        varchar ret_list_reason "退货原因"
        decimal ret_list_delfee "退回配送费"
        decimal ret_list_origin_total "原订单总金额"
        varchar out_refund_no "第三方退款单号"
    }

    return_list_detail {
        int ret_list_detail_id PK "退货明细ID"
        varchar ret_list_unique FK "退货单唯一标识"
        varchar goods_barcode FK "商品条码"
        varchar goods_name "商品名称"
        decimal ret_list_detail_count "退货数量"
        decimal ret_list_detail_price "退货单价"
        int handle_way "处理方式"
        decimal ret_list_origin_price "原销售价格"
        int rsale_list_detail_id "关联销售明细ID"
    }
```

---

## 5. 会员管理模块

会员管理模块包含会员信息、等级、充值记录和平台会员等相关表：

```mermaid
erDiagram
    shops ||--o{ customer_checkout : "店铺拥有会员"
    customer_checkout }o--|| customer_level : "会员属于等级"
    customer_checkout ||--o{ customer_recharge : "会员充值记录"
    customer_checkout ||--|| platform_cus : "店铺会员关联平台会员"
    customer_checkout ||--o{ sale_list : "会员下单"

    customer_checkout {
        int cus_id PK "会员ID"
        bigint shop_unique FK "店铺标识"
        varchar cus_unique UK "会员唯一标识"
        varchar cus_name "会员姓名"
        varchar cus_phone "会员电话"
        varchar cus_qq "会员QQ"
        varchar cus_weixin "会员微信"
        varchar cus_email "会员邮箱"
        date cus_birthday "会员生日"
        int cus_sex "性别"
        varchar cus_occupation "职业"
        varchar cus_address "地址"
        varchar cus_type "会员类型"
        int level_id FK "会员等级ID"
        decimal cus_balance "会员余额"
        int cus_points "会员积分"
        varchar cus_pwd "会员密码"
        date validity_start "有效期开始"
        date validity_end "有效期结束"
        datetime create_time "创建时间"
        int same_type "同步状态"
    }

    customer_level {
        int level_id PK "等级ID"
        bigint shop_unique FK "店铺标识"
        varchar level_name "等级名称"
        decimal level_discount "等级折扣"
        decimal level_points_ratio "积分比例"
        decimal upgrade_amount "升级所需消费金额"
        int level_sort "等级排序"
    }

    customer_recharge {
        int recharge_id PK "充值记录ID"
        int cus_id FK "会员ID"
        datetime recharge_datetime "充值时间"
        decimal recharge_money "充值金额"
        decimal cus_balance "充值后余额"
        decimal cus_amount "累计充值金额"
        int cus_type "记录类型"
        int recharge_method "充值方式"
        text remarks "备注信息"
        int data_type "数据类型"
        decimal give_money "赠送金额"
        bigint shop_unique FK "店铺标识"
        int recharge_status "充值状态"
        bigint sale_list_unique FK "关联订单号"
        int saleListCashier "操作收银员ID"
    }

    platform_cus {
        int pc_id PK "平台会员ID"
        varchar cus_unique UK "会员唯一标识"
        varchar pc_nick_name "昵称"
        decimal pc_balance "平台余额"
        bigint pc_buyhoo_bean "百货豆数量"
        varchar pc_phone "手机号"
        varchar pc_protrait "头像路径"
        varchar pc_openid "微信OpenID"
        varchar pc_unionid "微信UnionID"
        datetime create_time "创建时间"
    }
```

---

## 6. 第三方集成模块

第三方集成模块包含外卖平台、配送平台和支付平台等相关表：

```mermaid
erDiagram
    shops ||--o{ takeout_order : "店铺接收外卖订单"
    sale_list ||--|| ele_delivery_order : "订单关联配送"
    shops ||--o{ wechat_pay_order : "店铺微信支付"
    shops ||--o{ helibao_pay_order : "店铺合利宝支付"

    takeout_order {
        int id PK "外卖订单ID"
        bigint shop_unique FK "店铺标识"
        varchar platform_order_id UK "平台订单号"
        int platform_type "平台类型"
        int order_status "订单状态"
        varchar customer_name "客户姓名"
        varchar customer_phone "客户电话"
        varchar delivery_address "配送地址"
        decimal order_total "订单总金额"
        decimal delivery_fee "配送费"
        decimal platform_fee "平台服务费"
        datetime order_time "下单时间"
        datetime expect_time "期望送达时间"
        datetime create_time "创建时间"
    }

    ele_delivery_order {
        int id PK "配送订单ID"
        bigint sale_list_unique UK,FK "关联销售订单号"
        bigint shop_unique FK "店铺标识"
        varchar delivery_id "蜂鸟配送单号"
        decimal order_weight "订单重量"
        int delivery_status "配送状态"
        varchar courier_name "配送员姓名"
        varchar courier_phone "配送员电话"
        datetime create_time "创建时间"
        datetime update_time "更新时间"
    }

    wechat_pay_order {
        int id PK "支付订单ID"
        bigint shop_unique FK "店铺标识"
        varchar out_trade_no UK "商户订单号"
        varchar transaction_id "微信支付订单号"
        int total_fee "订单金额分"
        int pay_status "支付状态"
        varchar openid "用户OpenID"
        varchar trade_type "交易类型"
        datetime create_time "创建时间"
        datetime pay_time "支付完成时间"
        datetime notify_time "通知时间"
    }

    helibao_pay_order {
        int id PK "支付订单ID"
        bigint shop_unique FK "店铺标识"
        varchar out_trade_no UK "商户订单号"
        varchar trade_no "合利宝交易号"
        decimal total_amount "订单金额"
        int pay_status "支付状态"
        varchar pay_type "支付方式"
        datetime create_time "创建时间"
        datetime pay_time "支付完成时间"
    }
```

---

## 7. 系统管理模块

系统管理模块包含菜单、角色、字典等系统配置相关表：

```mermaid
erDiagram
    sys_menu ||--o{ sys_menu : "菜单层级关系"
    sys_role ||--o{ shop_staff : "角色分配给员工"
    sys_dict_data }o--|| sys_dict_type : "字典数据属于字典类型"

    sys_menu {
        int id PK "菜单ID"
        varchar code UK "菜单编码"
        varchar name "菜单名称"
        varchar parent_code FK "父级菜单编码"
        int level "菜单级别"
        varchar url "菜单URL"
        varchar icon "菜单图标"
        int seq "排序序号"
        varchar version "版本号"
        int terminal "终端类型"
        int type "菜单类型"
        varchar remark "备注"
        datetime create_time "创建时间"
        int del_flag "删除标志"
    }

    sys_role {
        int role_id PK "角色ID"
        varchar role_code UK "角色编码"
        varchar role_name "角色名称"
        varchar role_desc "角色描述"
        datetime create_time "创建时间"
        int del_flag "删除标志"
    }

    sys_dict_data {
        int dict_id PK "字典ID"
        varchar dict_type FK "字典类型"
        varchar dict_label "字典标签"
        varchar dict_value "字典值"
        int dict_sort "排序"
        int status "状态"
        datetime create_time "创建时间"
    }

    sys_dict_type {
        varchar dict_type PK "字典类型"
        varchar dict_name "字典名称"
        varchar dict_desc "字典描述"
        int status "状态"
        datetime create_time "创建时间"
    }
```

---

## 8. 统计分析模块

统计分析模块包含各种业务数据统计和分析相关表：

```mermaid
erDiagram
    shops ||--o{ count_msg_yinong : "店铺统计数据"
    shops ||--o{ data_goods_analysis : "店铺商品分析"
    shops ||--o{ supplier_shopping : "店铺采购订单"
    shops ||--o{ loan_money : "店铺赊销记录"
    goods ||--o{ data_goods_analysis : "商品分析数据"
    supplier_shopping ||--o{ loan_money : "采购订单赊销"

    count_msg_yinong {
        int id PK "统计ID"
        bigint shop_unique FK "店铺标识"
        date count_date "统计日期"
        decimal sale_amount "销售金额"
        int sale_count "销售笔数"
        int customer_count "客户数量"
        int goods_count "商品种类数"
        decimal profit_amount "利润金额"
        datetime create_time "创建时间"
    }

    data_goods_analysis {
        int id PK "分析ID"
        bigint shop_unique FK "店铺标识"
        varchar goods_barcode FK "商品条码"
        date analysis_date "分析日期"
        decimal sale_count "销售数量"
        decimal sale_amount "销售金额"
        decimal profit_amount "利润金额"
        decimal customer_age_avg "客户平均年龄"
        int hot_level "热度等级"
        datetime create_time "创建时间"
    }

    supplier_shopping {
        int id PK "采购订单ID"
        bigint shop_unique FK "店铺标识"
        varchar supplier_unique "供应商标识"
        varchar order_unique UK "采购订单号"
        decimal order_total "订单总金额"
        int order_status "订单状态"
        int pay_status "支付状态"
        int pay_type "支付方式"
        datetime create_time "创建时间"
        datetime delivery_time "发货时间"
        datetime receive_time "收货时间"
    }

    loan_money {
        int id PK "赊销记录ID"
        bigint shop_unique FK "店铺标识"
        varchar supplier_unique "供应商标识"
        decimal loan_amount "赊销金额"
        decimal repay_amount "已还金额"
        decimal remain_amount "剩余金额"
        int loan_type "类型"
        int loan_status "状态"
        date due_date "到期日期"
        datetime create_time "创建时间"
        varchar order_unique FK "关联订单号"
    }
```

---

## 数据库设计总结

### 核心特点
1. **多租户架构**: 通过`shop_unique`字段实现多店铺数据隔离
2. **业务完整性**: 覆盖零售业务全流程，从商品管理到订单履行
3. **扩展性强**: 支持多种业务模式和第三方平台集成
4. **数据一致性**: 通过外键约束和事务保证数据一致性

### 主要业务流程
```mermaid
flowchart TD
    A[店铺注册] --> B[商品管理]
    B --> C[会员注册]
    C --> D[下单购买]
    D --> E[订单处理]
    E --> F[配送/自提]
    F --> G[订单完成]
    G --> H[数据统计]

    D --> I[退货申请]
    I --> J[退货处理]
    J --> K[退款完成]

    C --> L[会员充值]
    L --> M[积分累计]

    B --> N[库存管理]
    N --> O[自动补货]
    O --> P[采购订单]
```

### 表关系概览
- **店铺表(shops)** 是核心主表，与大部分业务表都有关联
- **商品表(goods)** 通过条码与销售明细、库存流水等关联
- **订单表(sale_list)** 与订单明细、退货、配送等表形成完整的订单生命周期
- **会员表(customer_checkout)** 与充值记录、等级、平台会员等形成会员体系
- **第三方集成表** 实现与外部平台的数据交互

### 性能优化建议
- 关键字段建立索引，提高查询性能
- 分表设计，订单主表和明细表分离
- 时间字段索引，支持按时间范围查询
- 唯一约束，保证数据唯一性
- 考虑分库分表，应对大数据量场景

### 数据安全
- 密码字段MD5加密存储
- 敏感数据访问控制
- 操作日志记录
- 数据备份和恢复机制

---

## 配置文件参考

### 数据源配置
> **配置文件**: [`spring-mybatis.xml`](../src/main/resources/conf/spring-mybatis.xml#L26)

### MyBatis配置
> **配置文件**: [`mybatis-config.xml`](../src/main/resources/conf/mybatis-config.xml)

### 实体类映射
- **店铺实体**: [`ShopsEntity.java`](../src/main/java/org/haier/shop/entity/ShopsEntity.java#L9)
- **商品实体**: [`GoodsEntity.java`](../src/main/java/org/haier/shop/entity/GoodsEntity.java#L84)
- **订单实体**: [`SaleList.java`](../src/main/java/org/haier/ele/entity/SaleList.java#L12)
- **订单明细**: [`SaleListDetail.java`](../src/main/java/org/haier/shop/entity/SaleListDetail.java)

### Mapper文件
- **店铺Mapper**: [`shopsMapper.xml`](../src/main/resources/mapper/shopsMapper.xml#L91)
- **商品Mapper**: [`goodsMapper.xml`](../src/main/resources/mapper/goodsMapper.xml#L7)
- **订单Mapper**: [`sale_listMapper.xml`](../src/main/resources/mapper/sale_listMapper.xml#L7)
- **会员Mapper**: [`customerMapper.xml`](../src/main/resources/mapper/customerMapper.xml#L275)