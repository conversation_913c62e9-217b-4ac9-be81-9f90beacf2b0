<%@ page language="java" contentType="text/html; charset=UTF-8"  pageEncoding="UTF-8"%>
<%@ taglib prefix="shiro" uri="http://shiro.apache.org/tags" %> 
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>打印价签</title>
    <style type="text/css">
    	.layui-table-cell{
    		height: 100%!important;
    		margin-top: 10px;
    	}
    </style>
</head>
<%@ include file="../common/common_inc.jsp" %>
<script type="text/javascript" src="${path }/shop/static/js/pub.js?v=<%=System.currentTimeMillis()%>"></script>
<script type="text/javascript" src="${path }/shop/static/js/util/kinds-util.js?v=<%= System.currentTimeMillis()%>"></script>
<body> 
<div class="layui-fluid layui-anim layui-anim-fadein" id="formSearch">
   <div class="layui-card">
      <div class="layui-form layui-card-header layuiadmin-card-header-auto">
         <div class="layui-form-item">
            <div class="layui-inline">
               <label class="layui-form-label">商品信息</label>
               <div class="layui-input-inline">
                  <input type="text" id="search_name" placeholder="商品名称/条码/首字母" autocomplete="off" class="layui-input">
               </div>
            </div>
            <div class="layui-inline">
               <label class="layui-form-label">商品大类：</label>    
               <div class="layui-input-inline">
                   <select  id="goods_kind_parunique" lay-filter="change-kinds">
                       
                   </select>
               </div>
            </div>
            <div class="layui-inline">
               <label class="layui-form-label">商品小类：</label>    
               <div class="layui-input-inline">
                   <select  id="goods_kind_unique">
                      
                   </select>
               </div>
            </div>
            <input type="hidden" id="goodsBarcode">
            <div class="layui-inline">
               <button class="layui-btn layuiadmin-btn-useradmin" lay-submit="" id="search_button" data-type="reload">
                  <i class="layui-icon layui-icon-search layuiadmin-button-btn"></i>
               </button> 
            </div>
            <div class="layui-inline">
            	<button class="layui-btn layui-btn-warm" onclick="choseTemplate()">打印</button>
            </div>
            <div class="layui-inline">
	             <label class="layui-form-label">连续打印：</label>
			     <div class="layui-input-inline">
			      <input type="checkbox" name="close" lay-skin="switch"  lay-filter="printType" lay-text="开|关">
			      <input type="hidden" id="printType" value="0">
			    </div>
		    </div>
         </div>
      </div>
      
      <!-- table -->
      <div class="layui-card-body">
        <table class="layui-hide" id="LAY_table_user" lay-filter="user"></table> 
      </div>
   </div>
</div>  
<div id="templateList" style="display: none;">
	<table style="width: 100%;">
		<tr>
			<td style="padding: 20px;width: 25%;" title="打印机设置：80*160mm,普通喷墨打印机，单次打印上限，4张">
				<img style="width: 100%;cursor:pointer;" onclick="print('templateZero')" src="${path }/shop/static/images/jq0.png">
			</td>
			
			<td style="padding: 20px;width: 25%;" title="打印机设置：95*38mm，热转印，有标记的标签，标记高度：5mm；打印后操作：撕去">
				<img style="width: 100%;cursor:pointer;" onclick="print('templateOne')" src="${path }/shop/static/images/jq1.jpg">
			</td>
			<td style="padding: 20px;width: 25%;">
				<img style="width: 100%;cursor:pointer;" onclick="print('templateTwo')" src="${path }/shop/static/images/jq2.jpg">
			</td>
			<td style="padding: 20px;width: 25%;">
				<img style="width: 100%;cursor:pointer;" onclick="print('templateThree')" src="${path }/shop/static/images/jq3.png">
			</td>
			<td style="padding: 20px;width: 25%;">
				<img style="width: 80%;cursor:pointer;" onclick="print('templateFour')" src="${path }/shop/static/images/jq4.jpg">
			</td>
		</tr>
		<tr>
			<td style="padding: 20px;width: 25%;">
				<img style="width: 100%;cursor:pointer;border: 1px solid #000000; " onclick="print('templateFive')" src="${path }/shop/static/images/jq5.png">
			</td>
			<td style="padding: 20px;width: 25%;" title="打印机设置：70×38mm，连续纸（可变长度）；暴露衬纸宽度：2mm/2mm/2mm/2mm;卷：方式：热敏；类型：有间距的标签；间距高度：3.5mm;间距便宜：0mm">
				<img style="width: 100%;cursor:pointer;border: 1px solid #000000; " onclick="print('templateSix')" src="${path }/shop/static/images/jq6.jpg">
			</td>
			<td style="padding: 20px;width: 25%;">
				<img style="width: 100%;cursor:pointer;border: 1px solid #000000; " onclick="print('templateSeven')" src="${path }/shop/static/images/jq7.jpg">
			</td>
			<td style="padding: 20px;width: 25%;">
				<img style="width: 100%;cursor:pointer;border: 1px solid #000000; " onclick="print('templateEight')" src="${path }/shop/static/images/jq8.png">
			</td>
		</tr>
		<tr>
			<td style="padding: 20px;width: 25%;">
				<img style="width: 100%;cursor:pointer;border: 1px solid #000000; " onclick="print('templateNine')" src="${path }/shop/static/images/jq9.png">
			</td>
			<td style="padding: 20px;width: 25%;" title="打印机设置：83*32mm纸张;连续纸（可变长度）;左右：1.5mm；上下：1mm">
				<img style="width: 100%;cursor:pointer;border: 1px solid #000000; " onclick="print('templateTen')" src="${path }/shop/static/images/jq10.png">
			</td>
				<td style="padding: 20px;width: 25%;" title="打印机设置：102*20.8mm纸张;连续纸（可变长度）;左右：1.0mm；上下：0.8、0mm">
				<img style="width: 100%;cursor:pointer;border: 1px solid #000000; " onclick="print('template11')" src="${path }/shop/static/images/jq11.png">
			</td>
				<td style="padding: 20px;width: 25%;" title="打印机设置：102*20.8mm纸张;连续纸（可变长度）;左右：1.0mm；上下：0.8、0mm">
				<img style="width: 100%;cursor:pointer;border: 1px solid #000000; " onclick="print('template12')" src="${path }/shop/static/images/jq11.png">
			</td>
		</tr>
	</table>


	<div id="printArea" style="width: 100%;height:100px;color: #000;margin: 0px 0px;border: 1px solid yellow;">
	
	</div>  
</div>
			
<script type="text/javascript" src="${path }/shop/static/js/jquery.jqprint-0.3.js"></script>
<script type="text/javascript" src="${path }/shop/static/js/JsBarcode.all.min.js"></script>
<script type="text/javascript" src="${path }/shop/static/js/jquery-migrate-1.2.1.min.js"></script>
<script>
var goodsList = new Array();
var selectedGoods = new Array();
var oldData="Y";
layui.use(['table','form'], function(){
  var table = layui.table;
  var form = layui.form;
  var $=layui.$;
	//查询大类
	 queryGoodsKinds('${sessionScope.staff.shop_unique}',"0","goods_kind_parunique",form);
	 form.on('select(change-kinds)', function(data){
		  //查询该大类下的小类
		  queryGoodsKinds('${sessionScope.staff.shop_unique}',data.value,"goods_kind_unique",form);
	 });
  
  var formHeight = parseInt(window.document.getElementById('formSearch').offsetHeight)+10;
  //方法级渲染
  var tableIns = table.render({
    elem: '#LAY_table_user'
    ,url: '${path}/shop/html/goods/getGoodPrintList.do'
    ,where:{
    	shop_unique:"${sessionScope.staff.shop_unique}"
    }
    ,method:"POST"
    ,response: {
    	 statusName: 'status' //规定数据状态的字段名称，默认：code
    	 ,statusCode: 1 //规定成功的状态码，默认：0
    	 ,msgName: 'msg' //规定状态信息的字段名称，默认：msg
    	 ,countName: 'count' //规定数据总数的字段名称，默认：count
    	 ,dataName: 'data' //规定数据列表的字段名称，默认：data
    } 
    ,cols: [[
		{field :'action',type:'checkbox',title : '操作',width : 160,align : 'center'},
		{field : 'goods_name',title : '商品名称',align : 'center'},
		{field : 'goods_barcode',title : '商品编号',align : 'center'},
		{field : 'goods_unit',title : '单位',align : 'center'},
		{field : 'goods_standard',title : '规格',align : 'center'},
		{field : 'goods_sale_price',title : '销售价',align : 'center',sort:true},
		{field : 'goods_cus_price',title : '会员价',align : 'center'},
    ]]
    ,id: 'testReload'
    ,page : true
    ,limit : 20
    ,limits : [20,50,100]
    ,height: 'full-'+formHeight //高度最大化减去差值
    ,loading: false //请求数据时，是否显示loading
    ,autoSort: false //禁用前端自动排序
    ,done: function(res, curr, count){
        //如果是异步请求数据方式，res即为你接口返回的信息。
//         console.log(res);

        goodsList = res.data;
        var printType=$("#printType").val();
        if(printType=="1")
        	{
        	selectedGoods=res.data;
        	}else
        		{
        		selectedGoods=[];
        		}
        var search_name=$("#search_name").val("")
        if(goodsList!=null&&(search_name!=null||search_name!=""))
  	  	{
  	   	oldData=JSON.stringify(res.data);
  	  	}
        $("#search_name").val("");
        $("#goods_kind_parunique").val("-1");
        $("#goods_kind_unique").val("-1");
        $("#search_name").focus();//指定输入框 ，获取焦点
        form.render('select');
        
     }
  });
  

  
 
   var $ = layui.$, active = {
    reload: function(){
    	    var oldData1 =  table.cache["testReload"];  
	        var recodeLimit = $(".layui-laypage-limits").find("option:selected").val();//分页数

    		var printType= $("#printType").val();
    		  if(printType=="1")
    			{
        	      table.reload('testReload', {
        	    	  page: {curr: 1 },//重新从第 1 页开始
        	    	  where: { //设定异步数据接口的额外参数，任意设
        	    		    goods_kind_parunique: $('#goods_kind_parunique').val(),
        	 	        	goods_kind_unique: $('#goods_kind_unique').val(),
        	 	        	goodsMessage: $('#search_name').val(),
        	 	        	shop_unique:"${sessionScope.staff.shop_unique}",
        	 	        	oldData:oldData,
        	 	        	printType: $("#printType").val()
        	    	    }
        	    	  ,cols: [[ {type:'checkbox',LAY_CHECKED:true}]]

        	      });
    		    }else
    		    	{
    		    	
    	    	      table.reload('testReload', {
    	    	    	  
    	    	    	  page: {
    	    	   	          curr: 1 //重新从第 1 页开始
    	    	   	        }
    	    	    	  ,where: { //设定异步数据接口的额外参数，任意设
    	    	    		    goods_kind_parunique: $('#goods_kind_parunique').val(),
    	    	 	        	goods_kind_unique: $('#goods_kind_unique').val(),
    	    	 	        	goodsMessage: $('#search_name').val(),
    	    	 	        	shop_unique:"${sessionScope.staff.shop_unique}",
    	    	 	        	oldData:oldData,
    	    	 	        	printType: $("#printType").val()
    	    	    	    }
    	    	    	  ,cols: [[ {type:'checkbox',LAY_CHECKED:false}]]
    	    	      });
    		    	}

    }
  };
   //监听指定开关
   form.on('switch(printType)', function(data){
     /* layer.msg('开关checked：'+ (this.checked ? 'true' : 'false'), {
       offset: '6px'
     }); */
     
     if(this.checked)
    	 {
    	  $("#printType").val("1");
    	  $("#search_name").focus();//指定输入框 ，获取焦点
    	  layer.tips('温馨提示：每次查询不清空商品，（注：累计商品数量超过分页数量，自动清空）！', data.othis)

    	  
    	 }else
    		 {
    		 $("#printType").val("0");
    		 layer.tips('温馨提示：每次查询清空商品！', data.othis)    		 }
     
   });
  $('#search_button').on('click', function(){
    var type = $(this).data('type');
    active[type] ? active[type].call(this) : '';
  });
  
  //监听排序事件
  table.on('sort(user)', function(obj){ 
	  
		 	  table.reload('testReload', { 
		 	    initSort: obj 
		 	    ,where: { 
		 	      field: obj.field //排序字段
		 	      ,order: obj.type //排序方式
		 	    }
		 	  });
		   }	  
  );
  
  table.on('checkbox(user)', function(obj){
// 	  console.log(obj.checked); //当前是否选中状态
// 	  console.log(obj.data); //选中行的相关数据
// 	  console.log(obj.type); //如果触发的是全选，则为：all，如果触发的是单选，则为：one
	  if(obj.checked == true){//选中
		  if(obj.type == 'one'){//选中一个
			  selectedGoods.push(obj.data);console.log(selectedGoods);
		  }else{//选中多个
			  selectedGoods = goodsList;
		  }
	  }else{
		  if(obj.type == 'one'){
			  selectedGoods.splice(inArray(obj.data,selectedGoods),1);
		  }else{//选中多个
			  selectedGoods = new Array(); 
		  }
	  }
	});
  
  $("#formSearch").keydown(function(e){
	   if(e.keyCode==13){
		   $('#search_button').click();
	   }
	});
});


function queryGoodsList()
{
	

	
}
var choseTemplateIndex;
//选择打印模板
function choseTemplate(){
	if(selectedGoods.length==0){
		layer.msg('请先选择商品！');
		return false;
	}
	choseTemplateIndex = layer.open({
		  type: 1,
		  title: '选择打印模板',
		  shadeClose: false,
		  shade: 0.1,
		  area: ['100%', '100%'],
		  maxmin: false, //开启最大化最小化按钮
		  content: $('#templateList')
	}); 
}

//打印
function print(templateName){
// 	console.log(selectedGoods);
	//关闭选择模板页
	layer.close(choseTemplateIndex);
	$("#printArea").css("display","");
	if(templateName == "templateZero"){
		for(var i=0;i<selectedGoods.length;i++){
			var html ='<table style="font-size: 10px;width: 100%;color:#000;margin-bottom: 1mm;">'
			html += '<tr>'
					+'		<td colspan="2" style="font-size:16px;text-align: left;line-height: 8mm;padding-left: 12mm;padding-top: 0px;">'+selectedGoods[i].goods_name+'</td>'
					+'	</tr>'
					+'	<tr style="text-align: center;">'
					+'		<td style="width:50%;font-size: 16px;line-height:54px;text-align: left;padding-left: 12mm;padding-bottom: -20mm;">'
					+'			<div style="position: relative;bottom: -2mm;">' + selectedGoods[i].goods_barcode + '</div>'
					+ '</td>'
					+'		<td style="width:50%;line-height: 29mm;text-align: right;font-size: 45px;">'
					+'			<div style="position: relative;top: -5mm;right: 1mm;">'+selectedGoods[i].goods_sale_price.toFixed(2)+'</div>'
					+'		</td>'
					+'	</tr>'
			html += '</table>';
			$("#printArea").append(html);
		}
	}else if(templateName == "templateOne"){
		for(var i=0;i<selectedGoods.length;i++){
			var html ='<table style="font-size: 10px;width: 100%;color:#000;'
				if(i>0){
					html +='margin-top:15px;'
				}
				html +='">'
				+'	<tr>'
				+'		<td colspan="2" style="padding-left: 50px;padding-top:20px;font-weight:bold;">'+selectedGoods[i].goods_name+'</td>'
				+'	</tr>'
				+'	<tr style="text-align: center;">'
				+'		<td style="width:50%;font-size: 25px;line-height: 54px;">'+selectedGoods[i].goods_sale_price+'元</td>'
				+'		<td style="width:30%;line-height: 20px;padding-top: 2px;">'
				+'			<div style="font-weight:bold;padding-left: 25px">'+selectedGoods[i].goods_barcode+'</div>'
				+'			<div>&nbsp;&nbsp;'+selectedGoods[i].goods_standard+'</div>'
				+'			<div>&nbsp;&nbsp;'+selectedGoods[i].goods_unit+'</div>'
				+'		</td>'
				+'		<td style="width:20%;"></td>'
				+'	</tr>'
				+'	<tr>'
				+'		<td style="padding-left:35px;"><img style="margin-top:-5px;" id="'+selectedGoods[i].goods_barcode+'"/></td>'
				+'		<td></td>'
				+'	</tr>'
				+'</table>';
			$("#printArea").append(html);
			var barcode = document.getElementById(selectedGoods[i].goods_barcode),
	 	    str = selectedGoods[i].goods_barcode,
	 	    options = {
	 	        format: "CODE128",
	 	        displayValue: false,
	 	        width:1,
	 	        height: 25
	 	    };
	 	   JsBarcode(barcode, str, options); //原生
		}
	}else if(templateName == "templateTwo"){
		for(var i=0;i<selectedGoods.length;i++){
			var html ='<table style="font-size: 12px;width: 100%;color:#000;'
						if(i>0){
							html +='margin-top:20px;'
						}
					html +='">'
						+'<tr>'
						+'	<td style="padding-left: 35px;padding-top:15px;" colspan="2">&nbsp;'+selectedGoods[i].goods_name+'</td>'
						+'</tr>'
						+'<tr>'
						+'	<td style="padding-left: 35px;width: 50%;padding-top:10px;line-height: 22px;">'
						+'		<div style="margin-top:-5px;">&nbsp;'+selectedGoods[i].goods_unit+'</div>'
						+'		<div>&nbsp;'+selectedGoods[i].goods_standard+'</div>'
						+'		<div><img style="margin-top:5px;margin-left:-5px;width:140%;" id="'+selectedGoods[i].goods_barcode+'"/></div>'
						+'	</td>'
						+'	<td style="padding-left: 30px;width: 50%;font-size: 25px;line-height: 50px;">'
						+'		<div style="margin-left: 10px;margin-top:-15px;">&nbsp;'+selectedGoods[i].goods_sale_price+'</div>'
						+'		<div style="margin-top:-10px;">&nbsp;'+selectedGoods[i].goods_cus_price+'</div>'
						+'	</td>'
						+'</tr>'
					+'</table>';
			$("#printArea").append(html);
			var barcode = document.getElementById(selectedGoods[i].goods_barcode),
	 	    str = selectedGoods[i].goods_barcode,
	 	    options = {
	 	        format: "CODE128",
	 	        displayValue: true,
	 	        width:1,
	 	        height: 25,
	 	        fontSize: 15,
	 	    };
	 	   JsBarcode(barcode, str, options); //原生
		}
	}else if (templateName == "templateThree"){
		for(var i=0;i<selectedGoods.length;i++){
			var html ='<table style="font-size: 15px;width: 100%;color:#000;'
				if(i>0){
					html +='margin-top:25px;'
				}
				html +='">'
				+'	<tr>'
				+'		<td colspan="2" style="padding-left: 60px;padding-top:30px;">'+selectedGoods[i].goods_name+'</td>'
				+'	</tr>'
				+'	<tr style="text-align: center;">'
				+'		<td style="width:70%;font-size: 40px;line-height: 80px; padding-top:20px;text-align:center;">'+selectedGoods[i].goods_sale_price.toFixed(2)+'</td>'
				+'		<td style="width:30%;line-height: 18px;padding-top: 20px;text-align:left;">'
				+'			<div style="font-size:4px;line-height: 16px ;padding-top:-6px;">&nbsp;&nbsp;'+selectedGoods[i].goods_unit+'</div>'
				+'			<div style="font-size:4px;line-height: 16px ;padding-top:4px;">&nbsp;&nbsp;'+selectedGoods[i].goods_standard+'</div>'
				+'			<div style="font-size:4px;line-height: 16px ;padding-top:4px;margin-left:-20px;">&nbsp;&nbsp;'+selectedGoods[i].goods_barcode+'</div>'
				+'		</td>'
				+'	</tr>'
				+'</table>';
			$("#printArea").append(html);
// 			var barcode = document.getElementById(selectedGoods[i].goods_barcode),
// 	 	    str = selectedGoods[i].goods_barcode,
// 	 	    options = {
// 	 	        format: "CODE128",
// 	 	        displayValue: false,
// 	 	        width:1,
// 	 	        height: 25
// 	 	    };
// 	 	   JsBarcode(barcode, str, options); //原生
		}
	}else if(templateName == "templateFour"){
		for(var i=0;i<selectedGoods.length;i++){
			var html ='<table style="font-size: 12px;width: 100%;color:#000;'
					if(i>0){
						html +='margin-top:10px;'
					}
					html +='">'
						+'<tr style="margin-top:-10px;">'
						+'	<td style="padding-left: 40px;padding-top:12px;" colspan="2">&nbsp;'+selectedGoods[i].goods_name+'</td>'
						+'</tr>'
						+'<tr>'
						+'	<td style="width: 60%;padding-left:10px;padding-top:25px;">'
						if(selectedGoods[i].goods_unit != ''){
							html +='		<div style="font-size: 8px;">单位：'+selectedGoods[i].goods_unit+'</div>'
						}else{
							html +='		<div>&nbsp;</div>'
						}
						if(selectedGoods[i].goods_standard != ''){
							html +='		<div style="font-size: 8px;">规格：'+selectedGoods[i].goods_standard+'</div>'
						}else{
							html +='		<div>&nbsp;</div>'
						}
						html +='	</td>'
						+'	<td style="width: 40%;font-size: 30px;padding-top:25px;">'
						+'		<div style="">&nbsp;'+selectedGoods[i].goods_sale_price+'</div>'
						+'	</td>'
						+'</tr>'
						+'<tr>'
						+' <td style="text-align:center;" colspan="2">'
						+'		<div><img style="width:100%;margin-top:5px;" id="'+selectedGoods[i].goods_barcode+'"/></div>'
						+' </td>'
						+'</tr>'
					+'</table>';
					
			$("#printArea").append(html);
			var barcode = document.getElementById(selectedGoods[i].goods_barcode),
	 	    str = selectedGoods[i].goods_barcode,
	 	    options = {
	 	        format: "CODE128",
	 	        displayValue: false,
	 	        width:1,
	 	        height: 10
	 	    };
	 	   JsBarcode(barcode, str, options); //原生
		}
	}else if(templateName == "templateFive"){
		for(var i=0;i<selectedGoods.length;i++){
			var html ='<table style="font-size: 12px;width: 100%;height:100%;color:#000;'
					html +='">'
						+'<tr>'
						+'	<td style="padding:0px 15px;height:20%;">品名：'+selectedGoods[i].goods_name+'</td>'
						+'</tr>'
						+'<tr>'
						+'	<td style="padding:0px 15px;font-size:40px;height:60%;">￥'+selectedGoods[i].goods_sale_price+'</td>'
						+'</tr>'
						+'<tr>'
						+' <td style="padding:0px 15px;height:20%;">'
						+'		<div style="float:left;">'+selectedGoods[i].goods_barcode+'</div>'
						+'		<div style="float:right;font-size:10px;margin-top:-3px;">&nbsp;单位：'+selectedGoods[i].goods_unit+'</div>'
						+' </td>'
						+'</tr>'
					+'</table>';
			$("#printArea").append(html);
		}
	}
	else if(templateName == "templateSix"){
		for(var i=0;i<selectedGoods.length;i++){
			var html ='<table style="font-size: 12px;width: 100%;height:100%;color:#000;'
					html +='">'
						+'<tr>'
						+'	<td style="height:15%;padding-left:125px;padding-top:5px;" colspan="2">${sessionScope.staff.shop_name}</td>'
						+'</tr>'
						+'<tr>'
						+'	<td style="height:15%;padding-left:48px;padding-top:10px;" colspan="2">&nbsp;'+selectedGoods[i].goods_name+'</td>'
						+'</tr>'
						+'<tr>'
						+'	<td style="height:70%;width: 60%;">'
						+'		<div style="line-height:10px;font-size: 3px;padding-left:110px;margin-top:-10px;">&nbsp;'+selectedGoods[i].goods_unit+'</div>'
						+'		<div style="line-height:20px;height:20%;font-size: 5px;padding-left:40px;">&nbsp;'+selectedGoods[i].goods_standard+'</div>'
						+'		<div><img style="width:80%;line-height:20px;padding-left:30px;" id="'+selectedGoods[i].goods_barcode+'"/></div>'
						+'      <div style="line-height:20px;height:20%;font-size: 15px;padding-left:40px;">&nbsp;'+selectedGoods[i].goods_barcode+'</div>'
						+'  </td>'
						+'	<td style="height:70%;width: 40%;font-size: 50px;padding-left:30px;padding-top:30px;">'
						+'		<div>'+selectedGoods[i].goods_sale_price+'</div>'
// 						+'		<div style="">&nbsp;'+selectedGoods[i].goods_cus_price+'</div>'
						+'	</td>'
						+'</tr>'
					+'</table>';
			$("#printArea").append(html);
			var barcode = document.getElementById(selectedGoods[i].goods_barcode),
	 	    str = selectedGoods[i].goods_barcode,
	 	    options = {
	 	        format: "CODE128",
	 	        displayValue: false,
	 	        width:1,
	 	        height: 15
	 	    };
	 	   JsBarcode(barcode, str, options); //原生
		}
	}
	else if(templateName == "templateSeven"){
		for(var i=0;i<selectedGoods.length;i++){
		    var goods_name=selectedGoods[i].goods_name.substring(0,12);
		    var goods_standard=selectedGoods[i].goods_standard.substring(0,8);
			var html ="<div style=\"height:22%\"><span style='margin-left:15px;line-height:48px;font-size: 14px;'>${sessionScope.staff.shop_name} 沂蒙人 大有可为</span></div>\n" +
			"<div style=\"height:24%\" ><span style='margin-left:52px;line-height:45px;font-size: 14px;'>"+goods_name+"</span></div>\n" +
			"<table   style='font-size: 12px;color:#000;margin-top:1.5px;' height='54%' width='100%'  cellspacing=\"0\">\n" +
			"  <tr>\n" +
			"    <td style=\"width:50%;padding-left:28px;padding-top:26px;font-size: 25px;\" rowspan=\"5\">"+selectedGoods[i].goods_sale_price.toFixed(2)+"</td>\n" +
			"    <td style=\"font-size: 11px;height:45%;padding-left:30px;\">"+goods_standard+"&nbsp;</td>\n" +
			"  </tr>\n" +
			"  <tr>\n" +
			"    <td style=\"font-size: 11px;height:45%;padding-left:30px;\">"+selectedGoods[i].goods_unit+"&nbsp;</td>\n" +
			"  </tr>\n" +
			"  <tr>\n" +
			"    <td style=\"font-size: 11px;height:25%;padding-left:40px;\">&nbsp;</td>\n" +
			"  </tr>\n" +
			"  <tr>\n" +
			"    <td rowspan='2' style=\"font-size: 12px;padding-top:8px;padding-left:10px;\">"+selectedGoods[i].goods_barcode+"</td>\n" +
			"  </tr>\n" +
			"  <tr>\n" +

			"  </tr>\n" +
			"</table>";
			$("#printArea").append(html);
		}
	}
	else if(templateName == "templateEight"){
		for(var i=0;i<selectedGoods.length;i++){
		    var goods_name=selectedGoods[i].goods_name.substring(0,12);
		    var goods_standard=selectedGoods[i].goods_standard.substring(0,4);
			var html ="<div  style=\"width:300px;\">\n" +
			"      <div style=\"line-height:46px;\">\n" +
			"        <h3 style=\"margin-bottom:0;text-align:left;margin-left:70px;\">"+goods_name+"</h3>\n" +
			"      </div>\n" +
			"      <div id=\"content\" style=\"height:125px;width:54%;float:left;\">\n" +
			"        <div style=\"height:50%;\">\n" +
			"          <table style=\"width:100%;font-size: 12px;color:#000;\">\n" +
			"            <tr>\n" +
			"              <td style=\"width:50%;height:36px;padding-left:46px;padding-bottom:8px;\"></td>\n" +
			"              <td style=\"width:50%;height:36px;padding-left:46px;padding-bottom:8px;\">"+selectedGoods[i].goods_unit+"</td>\n" +
			"            </tr>\n" +
			"            <tr>\n" +
			"              <td style=\"width:50%;padding-left:45px;\">"+goods_standard+"</td>\n" +
			"              <td style=\"width:50%;padding-left:45px;\">一级</td>\n" +
			"            </tr>\n" +
			"          </table>\n" +
			"        </div>\n" +
			"        <div style=\"height:50%;margin-top:18px;margin-left:32px;\">\n" +
			          selectedGoods[i].goods_barcode+
			"        </div>\n" +
			"      </div>\n" +
			"      <div  style=\"background-color:#000000;height:125px;width:46%;float:right;\">\n" +
			"          <div style=\"height:40%;margin-left:30px;margin-top:30px;font-size:28px;\">"+selectedGoods[i].goods_sale_price.toFixed(2)+"</div>\n" +
			"          <div style=\"margin-left:45px;font-size: 15px;\">"+selectedGoods[i].goods_cus_price+"</div>\n" +
			"      </div>\n" +
			"      <div  style=\"background-color:#F0F8FF;clear:both;text-align:center;\">\n" +
			"        \n" +
			"      </div>\n" +
			"  </div>";console.log(html);
			$("#printArea").append(html);
		}
	}else if(templateName == "templateNine"){
		for(var i=0;i<selectedGoods.length;i++){
		    var goods_name=selectedGoods[i].goods_name.substring(0,12);
		    var goods_standard=selectedGoods[i].goods_standard.substring(0,8);
			var html ="<div style=\"height:22%\"><span style='margin-left:15px;line-height:48px;font-size: 14px;'> 乐佳购物</span></div>\n" +
			"<div style=\"height:24%\" ><span style='margin-left:52px;line-height:45px;font-size: 14px;'>"+goods_name+"</span></div>\n" +
			"<table   style='font-size: 12px;color:#000;margin-top:1.5px;' height='54%' width='100%'  cellspacing=\"0\">\n" +
			"  <tr>\n" +
			"    <td style=\"width:50%;padding-left:28px;padding-top:26px;font-size: 25px;\" rowspan=\"5\">"+selectedGoods[i].goods_sale_price.toFixed(2)+"</td>\n" +
			"    <td style=\"font-size: 11px;height:45%;padding-left:30px;\">"+goods_standard+"&nbsp;</td>\n" +
			"  </tr>\n" +
			"  <tr>\n" +
			"    <td style=\"font-size: 11px;height:45%;padding-left:30px;\">"+selectedGoods[i].goods_unit+"&nbsp;</td>\n" +
			"  </tr>\n" +
			"  <tr>\n" +
			"    <td style=\"font-size: 11px;height:25%;padding-left:40px;\">&nbsp;</td>\n" +
			"  </tr>\n" +
			"  <tr>\n" +
			"    <td rowspan='2' style=\"font-size: 12px;padding-top:8px;padding-left:10px;\">"+selectedGoods[i].goods_barcode+"</td>\n" +
			"  </tr>\n" +
			"  <tr>\n" +

			"  </tr>\n" +
			"</table>";
			$("#printArea").append(html);
		}
	}else if(templateName == "templateTen"){
		for(var i=0;i<selectedGoods.length;i++){
			var goodsName = selectedGoods[i].goods_name;
			console.log(goodsName.length);
			if(goodsName.length > 10){
				goodsName = goodsName.substr(0,10);
			}
			var html ='<table cellspacing="0" style="font-size: 12px;width: 100%;color:#000;margin-left:0px;margin-top:0px;margin-bottom:0px;">';
				html 	+='<tr>'
						+'	<td style="padding-left: 0px;padding-top:6px;text-align:center;line-height:32px;" >&nbsp;'+goodsName+'</td>'
						+'	<td style="padding-left: 0px;padding-top:6px;text-align:center;line-height:32px;" >&nbsp;'+goodsName+'</td>'
						+'</tr>'
						+'<tr>'
						+'	<td style="padding-left: 0px;width: 49%;padding-top:0px;line-height: 15px;">'
						+'		<div><img style="margin-top:0px;margin-left:-0px;width:99%;" id="'+selectedGoods[i].goods_barcode+'"/></div>'
						+'	</td>'
						+'	<td style="padding-left: 0px;width: 49%;padding-top:0px;line-height: 15px;">'
						+'		<div><img style="margin-top:0px;margin-left:-0px;width:99%;" id="d'+selectedGoods[i].goods_barcode+'"/></div>'
						+'	</td>'
						+'</tr>'
						+'<tr>'
						+'	<td style="padding-left: 0px;padding-top:0px;text-align:center;line-height : 28px;">价格：&nbsp;'+selectedGoods[i].goods_sale_price+'</td>'
						+'	<td style="padding-left: 0px;padding-top:0px;text-align:center;line-height : 28px;">价格：&nbsp;'+selectedGoods[i].goods_sale_price+'</td>'
						+'</tr>'
					+'</table>';
			$("#printArea").append(html);
			var barcode = document.getElementById(selectedGoods[i].goods_barcode),
	 	    str = selectedGoods[i].goods_barcode,
	 	    options = {
	 	        format: "CODE128",
	 	        displayValue: true,
	 	        width:1,
	 	        height: 15,
	 	        fontSize: 10,
	 	    };
	 	   if(str.length < 13){
	 		   console.log(str.length);
	 		   let lengths = 13- str.length;
	 		   for(var j = 0 ; j <lengths ; j++){
	 			   console.log(j%2 == 0);
	 			   if(j%2 == 0){
	 				   str = " " + str;
	 			   }else{
	 				   str = str + " ";
	 			   }
	 		   }
	 	   }
	 	   JsBarcode(barcode, str, options); //原生
	 	   var s = "d" +  selectedGoods[i].goods_barcode
	 	   var dbarcode = document.getElementById(s);
	 	   JsBarcode(dbarcode, str, options); //原生
		}
	}else if(templateName == "template11"){
		for(var i=0;i<selectedGoods.length;i++){
			var goodsName = selectedGoods[i].goods_name;
			console.log(goodsName.length);
			if(goodsName.length > 10){
				goodsName = goodsName.substr(0,10);
			}
			var html ='<table cellspacing="0" style="font-size: 8px;width: 100%;color:#000;margin-left:0px;margin-top:0px;margin-bottom:0px;">';
				html 	+='<tr>'
						+'	<td style="padding-left: 0px;padding-top:10px;text-align:center;line-height:16px;" >&nbsp;'+goodsName+'</td>'
						+'	<td style="padding-left: 0px;padding-top:10px;text-align:center;line-height:16px;" >&nbsp;'+goodsName+'</td>'
						+'	<td style="padding-left: 0px;padding-top:10px;text-align:center;line-height:16px;" >&nbsp;'+goodsName+'</td>'
						+'</tr>'
						+'<tr>'
						+'	<td style="padding-left: 0px;width: 33%;padding-top:0px;line-height: 20px;">'
						+'		<div><img style="margin-top:0px;margin-left:-0px;width:99%;" id="'+selectedGoods[i].goods_barcode+'"/></div>'
						+'	</td>'
						+'	<td style="padding-left: 0px;width: 33%;padding-top:0px;line-height: 20px;">'
						+'		<div><img style="margin-top:0px;margin-left:-0px;width:99%;" id="d'+selectedGoods[i].goods_barcode+'"/></div>'
						+'	</td>'
						+'	<td style="padding-left: 0px;width: 33%;padding-top:0px;line-height: 20px;">'
						+'		<div><img style="margin-top:0px;margin-left:-0px;width:99%;" id="e'+selectedGoods[i].goods_barcode+'"/></div>'
						+'	</td>'
						+'</tr>'
						+'<tr>'
						+'	<td style="padding-left: 0px;padding-top:0px;text-align:center;line-height : 16px;">价格：&nbsp;'+selectedGoods[i].goods_sale_price+'</td>'
						+'	<td style="padding-left: 0px;padding-top:0px;text-align:center;line-height : 16px;">价格：&nbsp;'+selectedGoods[i].goods_sale_price+'</td>'
						+'	<td style="padding-left: 0px;padding-top:0px;text-align:center;line-height : 16px;">价格：&nbsp;'+selectedGoods[i].goods_sale_price+'</td>'
						+'</tr>'
					+'</table>';
			$("#printArea").append(html);
			var barcode = document.getElementById(selectedGoods[i].goods_barcode),
	 	    str = selectedGoods[i].goods_barcode,
	 	    options = {
	 	        format: "CODE128",
	 	        displayValue: false,
	 	        width:1,
	 	        height: 20,
	 	        fontSize: 10,
	 	    };
	 	   if(str.length < 13){
	 		   console.log(str.length);
	 		   let lengths = 13- str.length;
	 		   for(var j = 0 ; j <lengths ; j++){
	 			   console.log(j%2 == 0);
	 			   if(j%2 == 0){
	 				   str = " " + str;
	 			   }else{
	 				   str = str + " ";
	 			   }
	 		   }
	 	   }
	 	   JsBarcode(barcode, str, options); //原生
	 	   var d = "d" +  selectedGoods[i].goods_barcode
	 	   var dbarcode = document.getElementById(d);
	 	   JsBarcode(dbarcode, str, options); //原生
			var e = "e" +  selectedGoods[i].goods_barcode
			var ebarcode = document.getElementById(e);
			JsBarcode(ebarcode, str, options); //原生
		}
	}else if(templateName == "template12"){
		for(var i=0;i<selectedGoods.length;i++){
			var html ='<table style="font-size: 12px;width: 100%;height:100%;color:#000;'
					html +='">'
						+'<tr>'
						+'	<td style="height:15%;padding-left:125px;padding-top:5px;" colspan="2">${sessionScope.staff.shop_name}</td>'
						+'</tr>'
						+'<tr>'
						+'	<td style="height:15%;padding-left:48px;padding-top:10px;" colspan="2">&nbsp;'+selectedGoods[i].goods_name+'</td>'
						+'</tr>'
						+'<tr>'
						+'	<td style="height:70%;width: 60%;">'
						+'		<div style="line-height:10px;font-size: 3px;padding-left:110px;margin-top:-10px;">&nbsp;'+selectedGoods[i].goods_unit+'</div>'
						+'		<div style="line-height:20px;height:20%;font-size: 5px;padding-left:40px;">&nbsp;'+selectedGoods[i].goods_standard+'</div>'
						+'		<div><img style="width:80%;line-height:20px;padding-left:30px;" id="'+selectedGoods[i].goods_barcode+'"/></div>'
						+'      <div style="line-height:20px;height:20%;font-size: 15px;padding-left:40px;">&nbsp;'+selectedGoods[i].goods_barcode+'</div>'
						+'  </td>'
						+'	<td style="height:70%;width: 40%;font-size: 50px;padding-left:20px;">'
						+'		<div style="font-size: 20px;height:20%;line-heigth:20px;margin-top:10px;">'+selectedGoods[i].goods_sale_price+'</div>'
						+'		<div style="font-size: 30px;margin-top:10px;">'+selectedGoods[i].goods_cus_price+'</div>'
						+'	</td>'
						+'</tr>'
					+'</table>';
			$("#printArea").append(html);
			var barcode = document.getElementById(selectedGoods[i].goods_barcode),
	 	    str = selectedGoods[i].goods_barcode,
	 	    options = {
	 	        format: "CODE128",
	 	        displayValue: false,
	 	        width:1,
	 	        height: 15
	 	    };
	 	   JsBarcode(barcode, str, options); //原生
		}
	}
	$("#printArea").jqprint();
	$("#printArea").css("display","none");
	$("#printArea").empty();
}
</script>

</body>
</html>