<%@ page language="java" contentType="text/html; charset=UTF-8"  pageEncoding="UTF-8"%>
<%@ taglib prefix="shiro" uri="http://shiro.apache.org/tags" %> 
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>销售查询</title>
<style type="text/css">
	.layui-table-cell{
    	height: 100%!important;
    	margin-top: 10px;
    }
    .layui-form-label {
    	width: auto;
    }
    .layui-form-item .layui-input-inline {
	    width: 170px;
	}
</style>
</head>
<%@ include file="../../common/common_inc.jsp" %>
<script type="text/javascript" src="${path }/shop/static/js/jquery-form.js"></script>
<body> 
<div class="layui-fluid layui-anim layui-anim-fadein" id="formSearch">
   <div class="layui-card">
      <div class="layui-form layui-card-header layuiadmin-card-header-auto">
      		<div class="layui-form-item">
<%--					<div class="layui-inline">--%>
<%--	                  <input type="text" class="layui-input" id="startTime" autocomplete="off" placeholder="开始日期 ">--%>
<%--	               </div>--%>
<%--	               <div class="layui-inline">--%>
<%--	                  <input type="text" class="layui-input" id="endTime" autocomplete="off" placeholder="结束日期 ">--%>
<%--	               </div>--%>
	               <div class="layui-inline">
						<label class="layui-form-label">APP类型</label>
						<div class="layui-input-block">
						   <select id="appId">
						  		<option value="-1">全部</option>
						  		<option value="1">APP商城</option>
						  		<option value="2">百货商家端</option>
						  		<option value="3">供货商（金圈云商）</option>
						  		<option value="4">物流端APP</option>
						  		<option value="5">PC收银机</option>
						  		<option value="6">一刻钟到家</option>
						  		<option value="7">云商系统</option>
						  </select>
						</div>
		            </div>
		            
		            <div class="layui-inline">
						<label class="layui-form-label">手机类型</label>
						<div class="layui-input-block">
						   <select id="appType">
						  		<option value="-1">全部</option>
						  		<option value="1">Android</option>
						  		<option value="2">IOS</option>
							    <option value="3">Windows</option>
						  </select>
						</div>
		            </div>
	            
		            <div class="layui-inline">
						<button class="layui-btn layuiadmin-btn-useradmin" lay-submit=""
							id="search_button" data-type="reload" style="margin-left: 20px;">
							搜索</button>
							
						<button class="layui-btn layuiadmin-btn-useradmin" lay-submit=""
							id="upload_button" data-type="reload" style="margin-left: 20px;">
							上传</button>
					</div>
	         </div>
      </div>
      
      <!-- table -->
      <div class="layui-card-body">
        <table class="layui-hide" id="LAY_table_user" lay-filter="user"></table> 
      </div>
   </div>
</div> 

 <div id="outerdiv" style="position:fixed;top:0;left:0;background:rgba(0,0,0,0.7);z-index:2;width:100%;height:100%;display:none;">
  <div id="innerdiv" style="position:absolute;">
    <img id="bigimg" style="border:5px solid #fff;" src="" />
  </div>
  </div>            

<script type="text/javascript" src="/shop/js/pub/xm-select.js"></script>
<script>
var moreSearchType = false;
$(".more_search").fadeOut(100);
var shopUnique = "${sessionScope.staff.shop_unique}";
var staffId = "${sessionScope.staff.staff_id}";
var staffMsg;
layui.use(['layer', 'form','element','laydate','table'], function(){
	
	 var layer = layui.layer,
	  	 form = layui.form,
	     element = layui.element,
	     table=layui.table;

	 

 	
  var formHeight = parseInt(window.document.getElementById('formSearch').offsetHeight)+10;
  //方法级渲染
  var tableIns = table.render({
    elem: '#LAY_table_user'
    ,url: '${path}/shop/systemManager/queryAppVersionList.do'
    ,where:{}
    ,response: {
    	 statusName: 'status' //规定数据状态的字段名称，默认：code
    	 ,statusCode: 1 //规定成功的状态码，默认：0
    	 ,msgName: 'msg' //规定状态信息的字段名称，默认：msg
    	 ,countName: 'count' //规定数据总数的字段名称，默认：count
    	 ,dataName: 'data' //规定数据列表的字段名称，默认：data
    } 
    ,method:"POST"
    ,cols: [[
        {field :'action',title : '操作',align : 'center',templet: '#barDemo',width:100},
		{field : 'app_name',title : 'app名称',align : 'center',width:120},
		{field : 'appType',title : '手机类型',align : 'center',width:120},
		{field : 'update_version',title : '版本号',align : 'center',width:120},
		{field : 'update_url',title : 'url',align : 'center'},
		{field : 'code',title : 'code',align : 'center',width:120},
		{field : 'update_des',title : '版本简述',align : 'center'},
		{field : 'updateInstall',title : '强制升级',align : 'center'},
		{field : 'updateTime',title : '发布时间',align : 'center'},
    ]]
    ,id: 'testReload'
    ,page : true
    ,limit : 15
    ,limits : [15,20,25,50,100]
    ,height: 'full-'+formHeight //高度最大化减去差值
    ,loading: false //请求数据时，是否显示loading
    ,autoSort: false //禁用前端自动排序
  });
  
   var active = {
    reload: function(){
      table.reload('testReload', {
    	  page: { curr: 1} //重新从第 1 页开始      
    	  ,where: { //设定异步数据接口的额外参数，任意设
	  			startTime : $("#startTime").val(),
	  			endTime : $("#endTime").val(),
	  			appType : $("#appType").val(),
	  			appId : $("#appId").val()
    	    }
      });
    }
  };
	$('#search_button').on('click', function(){
    var type = $(this).data('type');
    active[type] ? active[type].call(this) : '';
  });
	
	tableReload = function(page){
		if(null == page ){
			page = $(".layui-laypage-em").next().html();
		}
		table.reload('testReload', {
	    	  page: { curr: page} //重新从第 1 页开始      
	    	  ,where: { //设定异步数据接口的额外参数，任意设
		  			startTime : $("#startTime").val(),
		  			endTime : $("#endTime").val(),
		  			retListHandlestate : $("#ret_list_handlestate option:selected").val(),
		  			shopUnique : "${sessionScope.staff.shop_unique}",
	    	    }
	      });
	}
  
  //监听排序事件 
  
  //监听table事件
  $("#formSearch").keydown(function(e){
	   if(e.keyCode==13){
		   $('#search_button').click();
	   }
	});
  
  $("#upload_button").click(toUploadFile);

    //监听table事件
    table.on('tool(user)', function(obj){
        var data = obj.data;
        //编辑
        if(obj.event === 'edit'){
            layer.open({
                type: 2,
                title: '详情',
                shadeClose: false,
                shade: 0.1,
                area: ['80%', '90%'],
                maxmin: false, //开启最大化最小化按钮
                content: '${path}/shop/systemManager/uploadAppDetail.do?app_update_id='+data.app_update_id
            });
        }
    });

});

function toUploadFile(){
   	layer.open({
		  type: 2,
		  title: '上传文件',
		  shadeClose: false,
		  shade: 0.1,
		  area: ['80%', '80%'],
		  maxmin: false, //开启最大化最小化按钮
		  content: '${path}/shop/systemManager/toUploadFile.do',
		}); 
}



</script>
<script type="text/html" id="barDemo">
    {{# if(d.app_type == '3'){}}
    <button class="layui-btn layui-btn-xs" lay-event="edit">详情</button>
    {{# } }}
</script>
</body>
</html>