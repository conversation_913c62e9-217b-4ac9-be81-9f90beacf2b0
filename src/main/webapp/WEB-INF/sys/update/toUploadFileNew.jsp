<%@ page language="java" contentType="text/html; charset=UTF-8"  pageEncoding="UTF-8"%>
<!DOCTYPE html>
<html lang="en">
<head>
	<meta charset="UTF-8">
	<title>添加银行</title>
</head>
<style>
	html{
		background-color: #fff!important;
	}
</style>
<%@ include file="../../common/common_inc.jsp" %>
<body style="width:100%;height:100%;overflow-x:hidden;overflow-y:auto">
<form id="bankForm" class="layui-form layui-form-pane layui-anim layui-anim-fadein" style="width:80%;margin:0 auto;margin-top: 4%;">
	<div class="layui-form-item">
		<div class="layui-inline">
			<label class="layui-form-label">APP名称</label>
			<div class="layui-input-inline">
				<select id="appId">
					<option value="2">百货商家端</option>
					<option value="1">APP商城</option>
					<option value="3">供货商（金圈云商）</option>
					<option value="4">物流端APP</option>
					<option value="5">PC收银机</option>
					<option value="6">一刻钟到家</option>
					<option value="7">云商系统</option>
					<option value="8">分拣系统</option>
				</select>
			</div>
		</div>

		<div class="layui-inline">
			<label class="layui-form-label">APP名称</label>
			<div class="layui-input-inline">
				<select id="appType">
					<option value="1">ANDRIOD</option>
					<option value="2">IOS</option>
					<option value="3">Windows</option>
				</select>
			</div>
		</div>

		<div class="layui-inline">
			<label class="layui-form-label">版本号*</label>
			<div class="layui-input-inline">
				<input type="text" class="layui-input" id="updateVersion" value="" placeholder="输入当前软件版本号">
			</div>
		</div>

		<div class="layui-inline">
			<label class="layui-form-label">code</label>
			<div class="layui-input-inline">
				<input type="text" class="layui-input" id="code" value="" placeholder="APP专用，PC端不需要">
			</div>
		</div>

		<div class="layui-inline">
			<label class="layui-form-label">强制升级</label>
			<div class="layui-input-inline">
				<input type="radio" lay-filter="updateInstall" name="updateInstall" value="0" title="不强制" checked="checked">
				<input type="radio" lay-filter="updateInstall" name="updateInstall" value="1" title="强制">
			</div>
		</div>

		<div class="layui-inline">
			<label class="layui-form-label">升级简介</label>
			<div class="layui-input-inline">
				<input type="text" class="layui-input" id="updateDes" value="">
			</div>
		</div>

		<div class="layui-inline">
			<label class="layui-form-label">具体内容</label>
			<div class="layui-input-inline">
				<textarea class="layui-textarea"  id="updateLog"  rows="5" cols="" ></textarea>
			</div>
		</div>
	</div>
	<div class="layui-form">
		<div class="layui-upload">
			<button type="button" class="layui-btn layui-btn-normal" id="test10">选择文件</button>
			<div class="layui-upload-list" style="max-width: 1000px;">
				<table class="layui-table">
					<colgroup>
						<col>
						<col width="150">
						<col width="260">
					</colgroup>
					<thead>
					<tr><th>文件名</th>
						<th>大小</th>
						<th>操作</th>
					</tr>

					</thead>
					<tbody id="demoList"></tbody>
				</table>
			</div>
		</div>
	</div>
	<div class="layui-form">
		<label class="layui-form-label">下载地址</label>
		<div class="layui-input-inline">
			<input type="text" class="layui-input" id="updateUrl" value="" disabled>
		</div>
	</div>
	<div class="layui-form-item">
		<div class="layui-input-block">
			<button class="layui-btn" lay-submit="" lay-filter="demo1">立即提交</button>
			<button type="reset" class="layui-btn layui-btn-primary" onclick="cancelSubmit()">取消</button>
		</div>
	</div>
	<!--弹出进度条-->
			<div id="uploadLoadingDiv" style="display: none;">
				<div class="layui-progress" lay-showpercent="true" lay-filter="lock_progress" style="margin: 20px 10px;">
					<div class="layui-progress-bar layui-bg-blue" lay-percent="0%"></div>
				</div>
			</div>

</form>
</body>
<script type="text/javascript" src="${path }/shop/static/js/jquery-form.js"></script>
<script>
	var index = parent.layer.getFrameIndex(window.name); //获取窗口索引
	layui.use(['form','upload','element'], function(){
		var form = layui.form,layer = layui.layer,upload=layui.upload,element = layui.element,$=layui.jquery;

		//监听提交
		form.on('submit(demo1)', function(data){
			var updateUrl=$('#updateUrl').val();
			if(updateUrl==null || updateUrl=='')
			{
				return  layer.msg('文件为上传！');
			}
			$("#bankForm").ajaxSubmit({
				url: '${path}/shop/systemManager/uploadApp.do', //用于文件上传的服务器端请求地址
				type: 'post',
				data: {
					appId : $("#appId").val(),
					appType : $('#appType').val(),
					appName : $("#appId option:checked").text(),
					updateVersion : $("#updateVersion").val(),
					updateInstall : $("input[name=updateInstall]").val(),
					updateUrl:updateUrl,
					updateDes : $("#updateDes").val(),
					updateLog : $("#updateLog").val(),
					code : $("#code").val(),
				},
				dataType: 'json',
				success: function (d){
					if(d.status == 1){
						parent.layer.msg("添加成功");
						//如果成功关闭弹窗并刷新父页面列表
						parent.$('#search_button').click();
						parent.layer.close(index);
					}else{
						//如果失败弹出提示
						layer.msg(d.message);
					}
				}
			});
			return false;
		});

		var uploadListIns= upload.render({
			elem: '#test10'
			, url: '${path}/shop/systemManager/uploadAppPacket.do'
			, multiple: false
			, elemList: $('#demoList')
			, size: 307200
			, accept: 'file'
			, exts: 'zip|7z|apk'
			, number: 1
			,choose: function(obj){
				var that = this;
				var files = this.files = obj.pushFile(); //将每次选择的文件追加到文件队列
				//读取本地文件
				obj.preview(function(index, file, result){

					var tr = $(['<tr id="upload1">'
						,'<td>'+ file.name +'</td>'
						,'<td>'+ (file.size/1014).toFixed(1) +'kb</td>'
						,'<td><div class="layui-progress" lay-filter="progress-demo-1"><div class="layui-progress-bar" lay-percent=""></div></div></td>'
						,'<td>'
						,'<button class="layui-btn layui-btn-xs layui-btn-danger demo-delete">删除</button>'
						,'</td>'
						,'</tr>'].join(''));

					//删除
					tr.find('.demo-delete').on('click', function(){
						delete files[1]; //删除对应的文件
						tr.remove();
						$('#updateUrl').val('');
						uploadListIns.config.elem.next()[0].value = ''; //清空 input file 值，以免删除后出现同名文件不可选
					});

					that.elemList.append(tr);
					element.render('progress'); //渲染新加的进度条组件
				});
			}
			, done: function (res) {
				layer.closeAll();
				if (res.status != 1) {
					return layer.msg('上传失败:' + res.message);
				}
				if (res.status == 1) {

					$("#updateUrl").val(res.data);
					return layer.msg('上传成功！');

				}
			}
			, progress: function(n, elem, res, index){
					var percent = n + '%' //获取进度百分比
					element.progress('progress-demo-1', percent); //可配合 layui 进度条元素使用
					//console.log(elem); //得到当前触发的元素 DOM 对象。可通过该元素定义的属性值匹配到对应的进度条。
					//console.log(res); //得到 progress 响应信息
					//console.log(index); //得到当前上传文件的索引，多文件上传时的进度条控制，如：
					element.progress('progress-demo-1', n + '%'); //进度条
					element.render();

			}
			,error:function (index,upload){
				console.log(index,upload);
			}
		});



	});
	$(function (){

		$("#uploadFile2").click(function(){
			$("#file").click();
		});

		$("#file").change(function(){
			var objUrl = getObjectURL(this.files[0]) ;
			if (objUrl){
				$("#uploadFile2").html("");
				$("#uploadFile2").html(this.files[0].name);
			}
		}) ;
	});

	function cancelSubmit(){
		parent.layer.close(index);
	}


	//建立一個可存取到該file的url
	function getObjectURL(file) {
		var url = null ;
		// 下面函数执行的效果是一样的，只是需要针对不同的浏览器执行不同的 js 函数而已
		if (window.createObjectURL!=undefined) { // basic
			url = window.createObjectURL(file) ;
		} else if (window.URL!=undefined) { // mozilla(firefox)
			url = window.URL.createObjectURL(file) ;
		} else if (window.webkitURL!=undefined) { // webkit or chrome
			url = window.webkitURL.createObjectURL(file) ;
		}
		return url ;
	}
</script>
</html>