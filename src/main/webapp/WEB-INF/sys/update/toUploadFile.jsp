<%@ page language="java" contentType="text/html; charset=UTF-8"  pageEncoding="UTF-8"%>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>添加银行</title>
</head>
<style>
html{
background-color: #fff!important;
}

</style>
<%@ include file="../../common/common_inc.jsp" %>
<link rel="stylesheet" type="text/css" href="${path}/shop/static/layui-v2.6.8/css/modules/laydate/default/laydate.css" />
<link rel="stylesheet" type="text/css" href="${path}/shop/static/layui-v2.6.8/css/layui.css" />
<!-- layui核心类库 -->
<script type="text/javascript" src="${path}/shop/static/layui-v2.6.8/layui.js" charset="utf-8"></script>
<body style="width:100%;height:100%;overflow-x:hidden;overflow-y:auto">
	<form id="bankForm" class="layui-form layui-form-pane layui-anim layui-anim-fadein" style="width:80%;margin:0 auto;margin-top: 4%;">
	   <div class="layui-form-item">
	   		<div class="layui-inline">
	   			<label class="layui-form-label">APP名称</label>
	   			<div class="layui-input-inline">
	   				<select id="appId">
				  		<option value="2">百货商家端</option>
				  		<option value="1">APP商城</option>
				  		<option value="3">供货商（金圈云商）</option>
				  		<option value="4">物流端APP</option>
				  		<option value="5">PC收银机</option>
				  		<option value="6">一刻钟到家</option>
				  		<option value="7">云商系统</option>
				  		<option value="8">分拣系统</option>
				  </select>
	   			</div>
	   		</div>

	   		<div class="layui-inline">
	   			<label class="layui-form-label">APP名称</label>
	   			<div class="layui-input-inline">
	   				<select id="appType">
				  		<option value="1">ANDRIOD</option>
				  		<option value="2">IOS</option>
						<option value="3">Windows</option>
				  </select>
	   			</div>
   			</div>

   			<div class="layui-inline">
   				<label class="layui-form-label">版本号*</label>
   				<div class="layui-input-inline">
                	<input type="text" class="layui-input" id="updateVersion" value="" placeholder="输入当前软件版本号">
                </div>
             </div>

             <div class="layui-inline">
   				<label class="layui-form-label">code</label>
   				<div class="layui-input-inline">
                	<input type="text" class="layui-input" id="code" value="" placeholder="APP专用，PC端不需要">
                </div>
             </div>

             <div class="layui-inline">
   				<label class="layui-form-label">强制升级</label>
   				<div class="layui-input-inline">
                	<input type="radio" lay-filter="updateInstall" name="updateInstall" value="0" title="不强制" checked="checked">
					<input type="radio" lay-filter="updateInstall" name="updateInstall" value="1" title="强制">
                </div>
             </div>

             <div class="layui-inline">
   				<label class="layui-form-label">升级简介</label>
   				<div class="layui-input-inline">
                	<input type="text" class="layui-input" id="updateDes" value="">
                </div>
             </div>

             <div class="layui-inline">
   				<label class="layui-form-label">具体内容</label>
   				<div class="layui-input-inline">
                	<textarea class="layui-textarea"  id="updateLog"  rows="5" cols="" ></textarea>
                </div>
             </div>

		    <div class="layui-inline">
			    <label class="layui-form-label">文件</label>
			    <div class="layui-input-inline">
			        <div class="layui-upload-drag" id="uploadFile2" style="width: 100px;">
			        	<i class="layui-icon"></i>
  						<p>点击上传</p>
					</div>
					<input type="file" id="file" name="file" style="display: none;" value="">
<%--					<div id="progress_bar_top"></div>--%>
			    </div>
		    </div>
	   </div>
	  <div class="layui-form-item">
	    <div class="layui-input-block">
	        <button class="layui-btn" lay-submit="" lay-filter="demo1">立即提交</button>
      		<button type="reset" class="layui-btn layui-btn-primary" onclick="cancelSubmit()">取消</button>
	    </div>
	  </div>
		<!--弹出进度条-->
				<div id="uploadLoadingDiv" style="display: none;">
					<div class="layui-progress" lay-showpercent="true" lay-filter="lock_progress" style="margin: 20px 10px;">
						<div class="layui-progress-bar layui-bg-blue" lay-percent="0%"></div>
					</div>
				</div>
</form>
	</body>
<script type="text/javascript" src="${path }/shop/static/js/jquery-form.js"></script>
<script>
	var index = parent.layer.getFrameIndex(window.name); //获取窗口索引
	layui.use(['form','upload','element'], function(){
		var form = layui.form,layer = layui.layer,upload=layui.upload,element = layui.element,$=layui.jquery;

	  //监听提交
	  form.on('submit(demo1)', function(data){


	    $("#bankForm").ajaxSubmit({
            url: '${path}/shop/systemManager/uploadApp.do', //用于文件上传的服务器端请求地址
            type: 'post',
            data: {
            	appId : $("#appId").val(),
            	appType : $('#appType').val(),
            	appName : $("#appId option:checked").text(),
            	updateVersion : $("#updateVersion").val(),
            	updateInstall : $("input[name=updateInstall]").val(),
            	updateDes : $("#updateDes").val(),
            	updateLog : $("#updateLog").val(),
            	code : $("#code").val(),
            },
            dataType: 'json',
			beforeSend: function () {
				var html='<div id="progress"></div>';
				this.layerIndex = layer.load(0,
						{
							shade: [0.5, '#666c7f'],
							content:html,
						});
				var appId= $("#appId").val();
				setTimeout(completeHandle, 2000);
			},
            success: function (d){
				layer.closeAll();
            	if(d.status == 1){
					$('.progress-body percentage').html("100.00%");
		  			parent.layer.msg("添加成功");
		          	//如果成功关闭弹窗并刷新父页面列表
					parent.$('#search_button').click();
					parent.layer.close(index);
		          }else{
		          	//如果失败弹出提示
					layer.msg(d.msg);
		          }
            }
        });
		return false;
	  });
});
	$(function (){

		$("#uploadFile2").click(function(){
			$("#file").click();
		});

		$("#file").change(function(){
	        var objUrl = getObjectURL(this.files[0]) ;
	        if (objUrl){
	        	$("#uploadFile2").html("");
	        	$("#uploadFile2").html(this.files[0].name);
	        }
	    }) ;
	});
	function completeHandle(appId){

		$.ajax({
			url: "${path}/shop/systemManager/getprogressMonitor.do",
			type: 'POST',
			dataType: "json",
			success: function(json){
				percent = json.plan;
				var transfered=sizeTostr(json.transfered);
				var fileSize=sizeTostr(json.fileSize);
				$("#progress").html(percent  + '%'+"  "+transfered+"/"+fileSize);
				if(json.transfered == json.fileSize && json.fileSize != 0){
					if(appId=='5')
					{
						$("#progress").html("开始解压...........");
					}
					return;
				}
					completeHandle(appId);
			},
			error: function(data){

			}
		});
	}


	function sizeTostr(size) {
		var data = "";
		if (size < 0.1 * 1024) { //如果小于0.1KB转化成B
			data = size.toFixed(2) + "B";
		} else if (size < 0.1 * 1024 * 1024) {//如果小于0.1MB转化成KB
			data = (size / 1024).toFixed(2) + "KB";
		} else if (size < 0.1 * 1024 * 1024 * 1024) { //如果小于0.1GB转化成MB
			data = (size / (1024 * 1024)).toFixed(3) + "MB";
		} else { //其他转化成GB
			data = (size / (1024 * 1024 * 1024)).toFixed(3) + "GB";
		}
		var sizestr = data + "";
		var len = sizestr.indexOf("\.");
		var dec = sizestr.substr(len + 1, 2);
		if (dec == "00") {//当小数点后为00时 去掉小数部分
			return sizestr.substring(0, len) + sizestr.substr(len + 3, 2);
		}
		return sizestr;
	}


	function cancelSubmit(){
		parent.layer.close(index);
	}


	//建立一個可存取到該file的url
	function getObjectURL(file) {
	  var url = null ;
	  // 下面函数执行的效果是一样的，只是需要针对不同的浏览器执行不同的 js 函数而已
	  if (window.createObjectURL!=undefined) { // basic
	    url = window.createObjectURL(file) ;
	  } else if (window.URL!=undefined) { // mozilla(firefox)
	    url = window.URL.createObjectURL(file) ;
	  } else if (window.webkitURL!=undefined) { // webkit or chrome
	    url = window.webkitURL.createObjectURL(file) ;
	  }
	  return url ;
	}
</script>
</html>