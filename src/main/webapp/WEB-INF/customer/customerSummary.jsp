<%@ page language="java" contentType="text/html; charset=UTF-8"  pageEncoding="UTF-8"%>
<%@ taglib prefix="shiro" uri="http://shiro.apache.org/tags" %> 
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>会员消费记录</title>
	<%@ include file="../common/common_inc.jsp" %>
	<style type="text/css">
		.layui-form-item .width90{width: 100px;}
    	fieldset{width: 60%;}
    	table{width: 100%;}
    	.table th, .table td{border: 1px solid #AAA;line-height: 60px;font-size: 18px;text-align: center;}
    </style>
</head>
<body> 
<div class="layui-fluid layui-anim layui-anim-fadein" id="formSearch">
	<form action="/shop//html/customer/downLoadCustomerSummaryList.do" id='downloadExcel'>
		<input type="hidden" name="dshopUnique" value="222333">
		<input type="hidden" name="dstartTime">
		<input type="hidden" name="dendTime">
		<input type="hidden" name="dgroupType">
	</form>
   <div class="layui-card">
      <div class="layui-form layui-card-header layuiadmin-card-header-auto">
		<div class="layui-form-item">
		    <div class="layui-inline">
				<div class="layui-input-inline width90">
					      <input type="radio" name="groupType" value="1" title="按店铺" checked="">
				</div>
				<div class="layui-input-inline width90">
      					  <input type="radio" name="groupType" value="2" title="按日期">
				</div>
			</div>
		</div>
       	 <div class="layui-form-item">
			 <div class="layui-inline">	  
				<div class="layui-btn-group">			
			 		<!-- <button class="layui-btn btn_group" id="allTime">全部</button> -->
			 		<button class="layui-btn btn_group layui-btn-primary" id="todayTime">今天</button>
			 		<button class="layui-btn btn_group" id="yesTodayTime">昨天</button>
			 		<button class="layui-btn btn_group" id="thisWeekTime">本周</button>
			 		<button class="layui-btn btn_group" id="lastWeekTime">上周</button>
				</div>
            </div>
			 <div class="layui-inline">
				 <div class="layui-btn-group">
					 <button class="layui-btn btn_group" id="lastDay">上一天</button>
				 </div>
			 </div>
			<div class="layui-inline">
				<input type="text" class="layui-input" id="startTime" placeholder="开始日期 " autocomplete="off">
			</div>
			<div class="layui-inline">
				<input type="text" class="layui-input" id="endTime" placeholder="结束日期 " autocomplete="off">
			</div>
			 <div class="layui-inline">
				 <div class="layui-btn-group">
					 <button class="layui-btn btn_group" id="nextDay">下一天</button>
				 </div>
			 </div>
            <div class="layui-inline">
               <label class="layui-form-label">店铺：</label>    
               <div class="layui-input-inline">
                   <select id="shops" name="shops" lay-search="">
                   	
                   </select>
               </div>
            </div>	
            <div class="layui-inline">
               <button class="layui-btn layuiadmin-btn-useradmin" lay-submit="" id="search_button" data-type="reload">
                 	 搜索
               </button>
                 <button class="layui-btn layuiadmin-btn-useradmin" lay-submit="" id="download" data-type="reload">导出</button>
            </div>
         </div>
      </div>
      <!-- table -->
       <div class="layui-card-body">
      	<fieldset class="layui-elem-field site-demo-button" style="margin-top: 30px;">
		  <legend>主账户</legend>
		  <table class="table">
		  	<thead>
		  		<tr>
		  			<th>充值金额（元）</th>
		  			<th>活动金额（元）</th>
		  			<th>消费金额（元）</th>
		  			<th>退卡金额（元）</th>
		  			<th>主账户汇总（元）</th>
		  		</tr>
		  	</thead>
		  	<tbody>
		  		<tr>
		  			<td id="rechargeMoney">0.00</td>
		  			<td id="actMoney">0.00</td>
		  			<td id="consumeMoney">0.00</td>
		  			<td id="refundsCard">0.00</td>
		  			<td id="mainMoney">-0.00</td>
		  		</tr>
		  	</tbody>
		  </table>
		</fieldset>
		<fieldset class="layui-elem-field site-demo-button" style="margin-top: 30px;">
		  <legend>赠送账户</legend>
		   <table  class="table">
		  	<thead>
		  		<tr>
		  			<th>充值金额（元）</th>
		  			<th>活动金额（元）</th>
		  			<th>消费金额（元）</th>
		  			<th>退卡金额（元）</th>
		  			<th>赠送户汇总（元）</th>
		  		</tr>
		  	</thead>
		  	<tbody>
		  		<tr>
		  			<td id="giveMoney">0.00</td>
		  			<th id="actGiveMoney">0.00</th>
		  			<td id="consumeGiveMoney">0.00</td>
		  			<td id="refundsCardMoney">0.00</td>
		  			<td id="giftMoney">-0.00</td>
		  		</tr>
		  	</tbody>
		  </table>
		</fieldset>
      </div>
      <div class="layui-card-body">
        <table class="layui-hide" id="detailList" lay-filter="detailList"></table> 
      </div>
   </div>
</div>             
          
<script>
var shopUnique="${sessionScope.staff.shop_unique}",baseShopUnique = "${sessionScope.staff.shop_unique}";
var shop_type = '${sessionScope.staff.shop_type}';
var date=new Date();
var dateStr;
layui.use(['layer', 'form','element','laydate','table'], function(){
	 var layer = layui.layer,
	  	 form = layui.form,
	     element = layui.element,
	     table=layui.table,
	     laydate = layui.laydate;
	 var $ = layui.$;
	 form.render();
	 queryCusRechargeStatic();
	  var date = new Date();
	  dateStr = date.getFullYear()+'-'+(date.getMonth()+1)+'-'+date.getDate();
	  var formHeight = parseInt(window.document.getElementById('formSearch').offsetHeight)+10;
	  //日期
	  laydate.render({
	    elem: '#startTime',
	    min:'2019-01-01',
	    value : dateStr,
	    trigger:"click",
	  });
	  laydate.render({
		 elem: '#endTime',
		 min:'2019-01-01',
		 value : dateStr,
		 trigger:"click",
	  });
	  //加载店铺信息
		if(shop_type==6){
			 queryShops(form);
		}
	  	form.on('select(shops)', function(data){
		    form.render('select');
		});
	  //方法级渲染
	  var tableIns = table.render({
	    elem: '#detailList'
	    ,url: '${path}/shop/html/customer/customerSummaryByGroup.do'
	    ,where:{
	    	shopUnique : baseShopUnique,
	    	startTime : dateStr,
	    	endTime : dateStr,
	    	groupType : $("input[name=groupType]:checked").val(),
	    }
	    ,response: {
	    	 statusName: 'status' //规定数据状态的字段名称，默认：code
	    	 ,statusCode: 1 //规定成功的状态码，默认：0
	    	 ,msgName: 'msg' //规定状态信息的字段名称，默认：msg
	    	 ,countName: 'count' //规定数据总数的字段名称，默认：count
	    	 ,dataName: 'data' //规定数据列表的字段名称，默认：data
	    } 
	    ,method:"POST"
	    ,cols: [[
	    	{field : 'name',title : '店铺名称',align : 'center',sort: true},
			{field : 'rechargeMoney',title : '主账户充值金额',align : 'center',templet:function(d){
				return (d.rechargeMoney - d.actMoney ).toFixed(2);
			}},
			{field : 'giveMoney',title : '赠送账户充值金额',align : 'center',sort: true},
			{field : 'actMoney',title : '活动赠送金额',align : 'center',sort: true},
			{field : 'giveMoney',title : '充值合计',align : 'center',sort: true,templet:function(d){
				return (d.rechargeMoney + d.giveMoney ).toFixed(2);
			}},
			{field : 'consumptionMoney',title : '主账户消费金额',align : 'center',sort: true},
			{field : 'consumptionGiveMoney',title : '赠送账户消费金额',align : 'center',sort: true},
			{field : 'giveMoney',title : '消费合计',align : 'center',sort: true,templet:function(d){
				return (d.consumptionMoney + d.consumptionGiveMoney).toFixed(2);
			}},
			{field : 'refundsMoney',title : '主账户退卡金额',align : 'center',sort: true},
			{field : 'refundsGiveMoney',title : '赠送账户退卡金额',align : 'center',sort: true},
			{field : 'refundsGiveMoney',title : '退卡合计',align : 'center',sort: true,templet:function(d){
				return (d.refundsMoney + d.refundsGiveMoney).toFixed(2);
			}},
			{field : 'refundsGiveMoney',title : '储值汇总',align : 'center',sort: true,templet:function(d){
				return (d.rechargeMoney + d.giveMoney-(d.consumptionMoney + d.consumptionGiveMoney)-(d.refundsMoney + d.refundsGiveMoney)).toFixed(2);
			}},
	    ]]
	    ,id: 'detailList'
	    ,page : true
	    ,limit : 15
	    ,limits : [15,20,25]
	    ,loading: false //请求数据时，是否显示loading
	    ,autoSort: false //禁用前端自动排序
	    ,done: function(res, curr, count){
	      }
	  });
	  
	  var  active = {
	    reload: function(){
	      table.reload('detailList', {
	    	  page: {
	   	          curr: 1 //重新从第 1 页开始
	   	      },
	    	  where: { //设定异步数据接口的额外参数，任意设
	    		  shopUnique : $("#shops").val() == "" ? baseShopUnique : $("#shops").val(),
	    		  groupType : $("input[name=groupType]:checked").val(),
	    		  startTime : $("#startTime").val(),
	    		  endTime : $("#endTime").val(),
	    	    }
	      });
	    }
	  };
  
	  $('#search_button').on('click', function(){
		  var selectShopUnique = $("#shops").val();
		  if("" == selectShopUnique){
			  selectShopUnique = baseShopUnique;
		  }
		  $("input[name=dgroupType]").val($("input[name=groupType]:checked").val());
		  $("input[name=dshopUnique]").val(selectShopUnique);
		  $("input[name=dstartTime]").val($("#startTime").val());
		  $("input[name=dendTime]").val($("#endTime").val());
		  
	    var type = $(this).data('type');
	    active[type] ? active[type].call(this) : '';
	    queryCusRechargeStatic();
	  });
	  
	  $("#download").on("click",function(){
		  if($("#dstartTime").val() == ""){
			  layer.confrim("请先查询",{
				  btn:["确定","好的"]
			  })
		  }
		  $("#downloadExcel").submit();
	  });
	  
	  $("#todayTime").click(function(){
	      $('.btn_group').removeClass("layui-btn-primary");
		  $(this).addClass("layui-btn-primary");
	 	  $("#startTime").val(dateStr);
	 	  $("#endTime").val(dateStr);
	 	  $('#search_button').click();
	    });
	 	$("#yesTodayTime").click(function(){
	 		$('.btn_group').removeClass("layui-btn-primary");
		    $(this).addClass("layui-btn-primary");
	 		var date = new Date();
	 		date.setDate(date.getDate()-1);
	 		$("#startTime").val(date.getFullYear()+"-"+(date.getMonth()+1)+"-"+date.getDate());
	 		$("#endTime").val(date.getFullYear()+"-"+(date.getMonth()+1)+"-"+date.getDate());
	 		$('#search_button').click();
	 	});
	 	$("#thisWeekTime").click(function(){
	 		$('.btn_group').removeClass("layui-btn-primary");
		    $(this).addClass("layui-btn-primary");
	 		var date = new Date();
	 		date.setDate(date.getDate()-date.getDay()+1);
	 		$("#startTime").val(date.getFullYear()+"-"+(date.getMonth()+1)+"-"+date.getDate());
	 		date.setDate(date.getDate()+6);
	 		$("#endTime").val(date.getFullYear()+"-"+(date.getMonth()+1)+"-"+date.getDate());
	 		$('#search_button').click();
	 	});
	 	$("#lastWeekTime").click(function(){
	 		$('.btn_group').removeClass("layui-btn-primary");
		    $(this).addClass("layui-btn-primary");
	 		var date = new Date();
	 		date.setDate(date.getDate()-date.getDay()-6);
	 		$("#startTime").val(date.getFullYear()+"-"+(date.getMonth()+1)+"-"+date.getDate());
	 		date.setDate(date.getDate()+6);
	 		$("#endTime").val(date.getFullYear()+"-"+(date.getMonth()+1)+"-"+date.getDate());
	 		$('#search_button').click();
	 	})
});

$("#lastDay").click(lastDay);
$("#nextDay").click(nextDay);


function lastDay(){
	var startTime = $("#startTime").val();
	var endTime = $("#endTime").val();
	$("#startTime").val(addDay(startTime,-1));
	$("#endTime").val(addDay(endTime,-1));
	$('#search_button').click();
}

function nextDay(){
	var startTime = $("#startTime").val();
	var endTime = $("#endTime").val();
	$("#startTime").val(addDay(startTime,1));
	$("#endTime").val(addDay(endTime,1));
	$('#search_button').click();
}

//加载店铺
function queryShops(form){
		$.ajax({
			url:"/shop/shopsStaff/queryShopManager.do",
			type:"post",
			data:{"managerUnique":shopUnique},
			dataType : "json",
	    	async : false,
			success : function(d) {
				if(d.status == 0){
			  		var data = d.data;
			  		var html="<option value=''>全部</option>";
			  		for(var i=0;i<data.length;i++){
						var option="<option value='"+data[i].shopUnique+"'";
						option+=">"+data[i].shopName+"</option>";
						html += option;
					}
					$("#shops").append(html);	
					form.render('select');
			  		if(data.length>1){
			  			$('#shop_y').removeClass('layui-hide');
			  		}					
			    }else{
			        //如果失败弹出提示
					layer.msg("加载店铺失败！");
			    }
		  }
		  });	
}

function queryCusRechargeStatic(){
	var date = new Date();
	var dateStr = date.getFullYear()+'-'+(date.getMonth()+1)+'-'+date.getDate();
	var startStr = $("#startTime").val();
	var endStr = $("#endTime").val();
	
	$.ajax({
		url:"/shop/html/customer/queryCusRechargeStatic.do",
		type:"POST",
		data:{
			"shopUnique" : shopUnique,
			"startTime" : (startStr == "" ? dateStr : startStr) + " 00:00:00",
			"endTime" : (endStr == "" ? dateStr : endStr) + " 23:59:59",
		},
		dataType : "JSON",
		success:function(res){
			if(res.status == 1){
				$("#rechargeMoney").html((res.data.rechargeMoney - res.data.actMoney).toFixed(2));	
				$("#giveMoney").html((res.data.giveMoney - res.data.actGiveMoney).toFixed(2));
				$("#consumeMoney").html((res.data.consumeMoney - res.data.refundsMoney).toFixed(2));
				$("#consumeGiveMoney").html((res.data.consumeGiveMoney - res.data.refundsGiveMoney).toFixed(2));
				$("#refundsCard").html(res.data.refundsCard.toFixed(2));
				$("#refundsCardMoney").html(res.data.refundsCardMoney.toFixed(2));
				$("#cusBalance").html(res.data.cusBalance.toFixed(2));
				$("#cusGiveBalance").html(res.data.cusGiveBalance.toFixed(2));
				$("#actMoney").html(res.data.actMoney.toFixed(2));
				$("#actGiveMoney").html(res.data.actGiveMoney.toFixed(2));
				$("#mainMoney").html((res.data.rechargeMoney*1 - res.data.consumeMoney*1 - res.data.refundsCard*1 + res.data.refundsMoney*1).toFixed(2));
				$("#giftMoney").html((res.data.giveMoney*1 - res.data.consumeGiveMoney*1 - res.data.refundsCardMoney*1 + res.data.refundsGiveMoney*1).toFixed(2));
			}else{
				
			}
		},
		error:function(){
			layer.msg("查询失败！");
		}
	})
}
</script>
<script type="text/html" id="barDemo">

</script>
</body>
</html>