<%@ page language="java" contentType="text/html; charset=UTF-8"  pageEncoding="UTF-8"%>
<%@ taglib prefix="shiro" uri="http://shiro.apache.org/tags" %> 
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c"%>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>员工管理</title>
</head>
<%@ include file="../common/common_inc.jsp" %>
<body> 
<div class="layui-fluid layui-anim layui-anim-fadein" id="formSearch">
   <div class="layui-card">
      <div class="layui-form layui-card-header layuiadmin-card-header-auto">
         <div class="layui-form-item">
            <div class="layui-inline">
               <label class="layui-form-label" style="width: 120px;">员工电话/姓名/编号</label>
               <div class="layui-input-block" style="margin-left: 150px;">
                  <input type="text" id="staffMessage" placeholder="请输入员工电话/姓名/编号" autocomplete="off" class="layui-input">
               </div>
            </div>
            <c:if test="${sessionScope.staff.shop_class == 1}">
            	<div class="layui-inline">
	               <label class="layui-form-label">店铺</label>
	               <div class="layui-input-block">
	                  	<select id="staff_shops">
	                  	
	                  	</select>
	               </div>
	            </div>
            </c:if>
            <div class="layui-inline">
               <button class="layui-btn layuiadmin-btn-useradmin" lay-submit="" id="search_button" data-type="reload">
                  <i class="layui-icon layui-icon-search layuiadmin-button-btn"></i>
               </button>
               <shiro:hasPermission name="staff_add">
               <button class="layui-btn layuiadmin-btn-useradmin" onClick="add()">添加</button>
               </shiro:hasPermission>
               <shiro:hasPermission name="staff_add_ordinary">
               <button class="layui-btn layuiadmin-btn-useradmin" onClick="add()">添加</button>
               </shiro:hasPermission>
               <shiro:hasPermission name="staff_add_ordinary_yn">
               <button class="layui-btn layuiadmin-btn-useradmin" onClick="add()">添加</button>
               </shiro:hasPermission>
            </div>
         </div>
      </div>
      <a href="http://test170.buyhoo.cc/small/image/suppliers/GS371306159/2f79467b-4e67-4271-8526-a03e0920cc3b.jpg" download="161.jpg" id="downLoad"></a>
      
      <!-- table -->
      <div class="layui-card-body">
        <table class="layui-hide" id="LAY_table_user" lay-filter="user"></table> 
      </div>
   </div>
</div>             
          
<script>
layui.use(['table','laydate','form'], function(){
  var table = layui.table;
  var laydate = layui.laydate;
  var form = layui.form;
  
  var shop_class = '${sessionScope.staff.shop_class}';
  if(shop_class == '1'){
	  queryShopManagerPower(form);
  }
  
  var formHeight = parseInt(window.document.getElementById('formSearch').offsetHeight)+10;
  //方法级渲染
  var tableIns = table.render({
    elem: '#LAY_table_user'
    ,url: '${path}/shop/shopsStaff/queryStaffByPage.do'
    ,where:{
    	managerUnique:"${sessionScope.staff.manager_unique}",
    	shopUnique:"${sessionScope.staff.shop_unique}",
    	shopClass:"${sessionScope.staff.shop_class}"
    }
    ,response: {
    	 statusName: 'status' //规定数据状态的字段名称，默认：code
    	 ,statusCode: 1 //规定成功的状态码，默认：0
    	 ,msgName: 'msg' //规定状态信息的字段名称，默认：msg
    	 ,countName: 'count' //规定数据总数的字段名称，默认：count
    	 ,dataName: 'data' //规定数据列表的字段名称，默认：data
    } 
    ,cols: [[
		{field :'action',title : '操作',width : 280,align : 'center',templet: '#barDemo'},
		{field : 'staffId',title : '员工编号',align : 'center'},
		{field : 'staffAccount',title : '员工账号',align : 'center'},
		{field : 'staffName',title : '员工名称',align : 'center'},
		{field : 'city_name',title : '区域',align : 'center'},
		{field : 'staffPhone',title : '联系方式',align : 'center'},
		{field : 'shopName',title : '所属店铺',align : 'center'},
		{field : 'staffPosition',title : '职位',align : 'center',templet:function(d){
			if(d.staffPosition == 3 && d.shopClass == 0){
				return "店主";
			}else if(d.staffPosition == 3 && d.shopClass != 0 && d.shopClass != 3){
				return "分店店长";
			}else if(d.staffPosition == 3 && d.shopClass == 3){
				return "超级管理员";
			}else{
				return "职工";
			}
		}},
		{field : 'roleName',title : '员工角色',align : 'center'}
    ]]
    ,id: 'testReload'
    ,page : true
    ,limit : 15
    ,limits : [15,20,25]
    ,height: 'full-'+formHeight //高度最大化减去差值
    ,loading: false //请求数据时，是否显示loading
  });
  
   var $ = layui.$, active = {
    reload: function(){
      table.reload('testReload', {
    	  page: {curr: 1 },//重新从第 1 页开始
    	  where: { //设定异步数据接口的额外参数，任意设
    		  staffMessage: $('#staffMessage').val(),
    		  staffShops:$('#staff_shops').val()
    	    }
      });
    }
  };
  
  $('#search_button').on('click', function(){
    var type = $(this).data('type');
    active[type] ? active[type].call(this) : '';
  });
  
  //监听table事件
  table.on('tool(user)', function(obj){
    var data = obj.data;
    //查看
    if(obj.event === 'see'){
    	layer.open({
    		  type: 2,
    		  title: '查看员工',
    		  shadeClose: false,
    		  shade: 0.1,
    		  area: ['80%', '90%'],
    		  maxmin: false, //开启最大化最小化按钮
    		  content: '${path}/shop/shopsStaff/detailStaffPage.do?staffId='+data.staffId
    		}); 
    }
    
  //编辑
   else if(obj.event === 'edit'){
    	layer.open({
    		  type: 2,
    		  title: '编辑员工',
    		  shadeClose: false,
    		  shade: 0.1,
    		  area: ['80%', '90%'],
    		  maxmin: false, //开启最大化最小化按钮
    		  content: '${path}/shop/shopsStaff/editStaffPage.do?staffId='+data.staffId
    		}); 
    }
 	
  //删除
   else if(obj.event === 'del'){
   	layer.confirm('确定删除此数据？',{icon:3, title:'提示信息'},function(){
   			$.post('${path}/shop/shopsStaff/deleteStaff.do?staffId='+data.staffId,function(data){
   		   		if(data.status == 1){
   		           	//刷新页面
   		           	layer.msg('删除成功');
   		   			$('#search_button').click();
   		        }else{
   		        	layer.msg(data.msg);
   		        }
   			});
   		});
   }else if(obj.event === "down"){
	   console.log(obj.data);
	   $.ajax({
		   url : "/shop/shopsStaff/downLoadStaffCode.do",
		   type : "POST",
		   data : {
			   shopUnique : "${sessionScope.staff.shop_unique}",
			   staffId : obj.data.staffId,
			   staffName : obj.data.staffName,
		   },
		   dataType : "JSON",
		   success : function(res){
			   if(res.status == 1){
				   fetch(res.data.downLoadUrl).then(res => res.blob().then(blob => {
					   const a = document.createElement('a'),	// 动态创建a标签，防止下载大文件时，用户没看到下载提示连续点击
					         url = window.URL.createObjectURL(blob),
					         filename = obj.data.staffName;
					  
					   a.href = url;
					   a.download = filename;
					   a.click();
					   window.URL.revokeObjectURL(url);
					 }));

			   }
		   },
		   error : function(){
			   
		   }
	   });
   }
    
  });
  $("#formSearch").keydown(function(e){
	   if(e.keyCode==13){
		   $('#search_button').click();
	   }
	});
});




//新增
function add(){
	layer.open({
	  type: 2,
	  title: '添加员工',
	  shadeClose: false,
	  shade: 0.1,
	  area: ['80%', '90%'],
	  maxmin: false, //开启最大化最小化按钮
	  content: '${path}/shop/shopsStaff/addStaffPage.do'
	}); 
}

//获取店铺列表
function queryShopManagerPower(form){
	$.ajax({
		url:"${path}/shop/shopsStaff/queryShopManager.do",
		type:"post",
		data:{
			"managerUnique":"${sessionScope.staff.manager_unique}"
		},
		dataType:"json",
		success:function(result){
			if(result.status==0){
				var data = result.data;
				$("#staff_shops").append("<option value=''>全部</option>");
				for(var i=0;i<data.length;i++){
					var option = "<option value='"+data[i].shopUnique+"'>"+data[i].shopName+"</option>";
					$("#staff_shops").append(option);
				}
				form.render('select');
			}
		}
	});
}

</script>
<script type="text/html" id="barDemo">
<shiro:hasPermission name="staff_details">
  <button class="layui-btn layui-btn-xs" lay-event="see">查看</button>
</shiro:hasPermission>
<shiro:hasPermission name="staff_update">
  <button class="layui-btn layui-btn-xs" lay-event="edit">编辑</button>
</shiro:hasPermission>

{{# if(d.staffId != '${sessionScope.staff.staff_id}'){ }}
<shiro:hasPermission name="staff_delete">
  	<button class="layui-btn layui-btn-xs layui-btn-danger" lay-event="del">删除</button>
</shiro:hasPermission>
{{# } }}

<shiro:hasPermission name="staff_details_ordinary">
  <button class="layui-btn layui-btn-xs" lay-event="see">查看</button>
</shiro:hasPermission>
<shiro:hasPermission name="staff_update_ordinary">
  <button class="layui-btn layui-btn-xs" lay-event="edit">编辑</button>
</shiro:hasPermission>
{{# if(d.staffId != '${sessionScope.staff.staff_id}'){ }}
<shiro:hasPermission name="staff_delete_ordinary">
  	<button class="layui-btn layui-btn-xs layui-btn-danger" lay-event="del">删除</button>
</shiro:hasPermission>
{{# } }}

<shiro:hasPermission name="staff_details_ordinary_yn">
  <button class="layui-btn layui-btn-xs" lay-event="see">查看</button>
</shiro:hasPermission>
<shiro:hasPermission name="staff_update_ordinary_yn">
  <button class="layui-btn layui-btn-xs" lay-event="edit">编辑</button>
</shiro:hasPermission>

<shiro:hasPermission name="staff_download_code">
  <button class="layui-btn layui-btn-xs" lay-event="down">下载二维码</button>
</shiro:hasPermission>


{{# if(d.staffId != '${sessionScope.staff.staff_id}'){ }}
<shiro:hasPermission name="staff_delete_ordinary_yn">
  	<button class="layui-btn layui-btn-xs layui-btn-danger" lay-event="del">删除</button>
</shiro:hasPermission>
{{# } }}
</script>
</body>
</html>