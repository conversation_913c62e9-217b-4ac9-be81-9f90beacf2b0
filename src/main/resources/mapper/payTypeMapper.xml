<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//ibatis.apache.org//DTD Mapper 3.0//EN"
		"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.haier.shop.dao.PayTypeDao">

	<!-- 平台支付方式管理 -->
	<!-- 查询所有可选择的支付方式 -->
	<select id="queryPlatPayType" resultType="map">
		SELECT
			id AS id
			,pay_type_name AS payTypeName
			,pc_valid_type AS pcValidType
			,app_valid_type AS appValidType
			,valid_type AS validType
		FROM shop_pay_type_config
		<where>
			<if test="pcValidType != null and pcValidType !='-1'"> AND pc_valid_type = #{pcValidType}</if>
			<if test="appValidType != null and appValidType != '-1'"> AND app_valid_type = #{appValidType}</if>
			<if test="id != null and id != '-1'"> AND id = #{id}</if>
		</where>
	</select>
	
	<!-- 修改平台支付方式类型信息 -->
	<update id="modifyPlatPayType">
		UPDATE shop_pay_type_config
		<set>
			<if test="pcValidType != null and pcValidType != ''">pc_valid_type = #{pcValidType},</if>
			<if test="appValidType != null and appValidType != ''">app_valid_type = #{appValidType},</if>
			<if test="validType != null and validType != ''">valid_type = #{validType}</if>
		</set>
		<where>
			<if test="id != null"> AND id = #{id}</if>
		</where>
	</update>
	
	<sql id="sptPayType">
		,CASE pay_type WHEN 1 THEN "银联免密" WHEN 2 THEN "拉卡拉支付" WHEN "3" THEN "易通支付" WHEN "4" THEN "微信支付" WHEN 5 THEN "其他支付"  WHEN 6 THEN "合力宝" WHEN 7 THEN "中国银联" END AS payType
	</sql>
	
	<!-- 查询店铺现有支付方式 -->
	<select id="queryPayType" resultType="map">
		SELECT
			id AS id
			,shop_unique AS shopUnique
			,pay_type AS payTypeCode
			<include refid="sptPayType"/>
			,mch_id AS mchId
			,mch_key AS mchKey
			,other_set AS otherSet
			,valid_type AS validType
			,default_type AS defaultType
			,rate+"‰" AS rate
			,app_valid_type AS appValidType
			,app_default_type AS appDefaultType
			,app_rate+"‰" AS appRate
		FROM
			shop_pay_type spt
		<where>
			(valid_type = 1 OR app_valid_type = 1)
			<if test="shopUnique != null">
				AND shop_unique = #{shopUnique}
			</if>
		</where>	
	</select>
	<select id="queryPayTypeHave" resultType="int">
		SELECT
			COUNT(*)
		FROM
			shop_pay_type
		<where>
			<if test="validType != null and validType != ''"> AND valid_type = #{validType}</if>
			<if test="appValidType != null and appValidType != ''"> AND app_valid_type = #{appValidType}</if>
			<if test="shopUnique != null and shopUnique != ''"> AND shop_unique = #{shopUnique}</if>
			<if test="payType != null and payType != ''"> AND pay_type = #{payType}</if>
		</where>
	</select>
	
	<!-- 修改店铺支付方式信息 -->
	<update id="modiyfPayType">
		UPDATE shop_pay_type 
		<set>
			<if test="mchId != null and mchId !=''">mch_id = #{mchId},</if>
			<if test="mchKey != null and mckKey != ''">mch_key = #{mchKey},</if>
			<if test="otherSet != null and otherSet != ''">other_set = #{otherSet},</if>
			<if test="validType != null and validType != ''">valid_type = #{validType},</if>
			<if test="defaultType != null and defaultType != ''">default_type = #{defaultType},</if>
			<if test="rate != null and rate !=''">rate = #{rate},</if>
			<if test="appValidType != null and appValidType != ''">app_valid_type = #{appValidType},</if>
			<if test="appDefaultType != null and appDefaultType != ''">app_default_type = #{appDefaultType},</if>
			<if test="appRate != null and appRate != ''">app_rate = #{appRate}</if>
			<if test="subAccountNo != null and subAccountNo != ''">,sub_account = #{subAccountNo}</if>
		</set>
		<where>
			<if test="id != null and id != ''">AND id = #{id}</if>
			<if test="shopUnique != null and shopUnique != ''"> AND shop_unique = #{shopUnique}</if>
			<if test="noId != null and noId !=''"> AND id != #{noId}</if>
		</where>
	</update>
	
	<!-- 查询该支付方式所有店铺列表 -->
	<select id="queryPayTypeShopList" resultType="java.util.HashMap" parameterType="java.util.HashMap"> 
		SELECT 
		  p.*,
		  IFNULL(s.`shop_name`,"系统配置") AS shop_name 
		FROM
		  shop_pay_type p 
		  LEFT JOIN shops s 
		    ON p.`shop_unique` = s.`shop_unique` 
		WHERE p.`pay_type` = #{pay_type} 
		  <if test="shop_message != null and shop_message != ''">
		  AND (
		    s.`shop_unique` LIKE CONCAT('%', #{shop_message}, '%') 
		    OR s.`shop_name` LIKE CONCAT('%', #{shop_message}, '%')
		  )
		  </if>
		  LIMIT #{page}, #{limit}
	</select>
	
	<!-- 查询该支付方式所有店铺列表总条数 -->
	<select id="queryPayTypeShopListCount" resultType="java.lang.Integer" parameterType="java.util.HashMap"> 
		SELECT 
		  COUNT(p.`id`) 
		FROM
		  shop_pay_type p 
		  LEFT JOIN shops s 
		    ON p.`shop_unique` = s.`shop_unique` 
		WHERE p.`pay_type` = #{pay_type} 
		  <if test="shop_message != null and shop_message != ''">
		  AND (
		    s.`shop_unique` LIKE CONCAT('%', #{shop_message}, '%') 
		    OR s.`shop_name` LIKE CONCAT('%', #{shop_message}, '%')
		  )
		  </if>
	</select>
	
	<!-- 添加店铺支付方式 -->
	<insert id="addShopPayType" parameterType="java.util.HashMap">
		INSERT INTO shop_pay_type (
		  shop_unique,
		  pay_type
		  <if test="mchId != null and mchId != ''">,mch_id</if>
		  <if test="mchKey != null and mchKey != ''">,mch_key</if>
		  <if test="otherSet != null and otherSet != ''">,other_set</if>
		  <if test="validType != null and validType != ''">,valid_type</if>
		  <if test="defaultType != null and defaultType != ''">,default_type</if>
		  <if test="rate != null and rate != ''">,rate</if>
		  <if test="appValidType != null and appValidType != ''">,app_valid_type</if>
		  <if test="appDefaultType != null and appDefaultType != ''">,app_default_type</if>
		  <if test="appRate != null and appRate != ''">,app_rate</if>
		  <if test="subAccountNo != null and subAccountNo != ''">,sub_account</if>
		) VALUE (
		  #{shopUnique},
		  #{payType}
		  <if test="mchId != null and mchId != ''">,#{mchId}</if>
		  <if test="mchKey != null and mchKey != ''">,#{mchKey}</if>
		  <if test="otherSet != null and otherSet != ''">,#{otherSet}</if>
		  <if test="validType != null and validType != ''">,#{validType}</if>
		  <if test="defaultType != null and defaultType != ''">,#{defaultType}</if>
		  <if test="rate != null and rate != ''">,#{rate}</if>
		  <if test="appValidType != null and appValidType != ''">,#{appValidType}</if>
		  <if test="appDefaultType != null and appDefaultType != ''">,#{appDefaultType}</if>
		  <if test="appRate != null and appRate != ''">,#{appRate}</if>
		  <if test="subAccountNo != null and subAccountNo != ''">,#{subAccountNo}</if>
		)
	</insert>
	
	<!-- 获取店铺支付方式详情 -->
	<select id="queryShopPayType" resultType="java.util.HashMap" parameterType="java.lang.String">
		SELECT 
		  * 
		FROM
		  shop_pay_type p 
		WHERE p.`id` = #{shop_pay_type_id} 
	</select>
	
	<insert id="addNewShopPayMsg">
		INSERT INTO shop_pay_msg
		(shop_unique,datetime,legalPerson,userPhone,license,doorPhoto,identityFront,identityBlack,bankCardFront
			,bankCardBlack,shop_address,email,subBranch<if test="examineStatus != null and examineStatus != ''">,examine_status</if>)
		VALUES
		(#{shopUnique},NOW(),#{legalPerson},#{userPhone},#{license},#{doorPhoto},#{identityFront},#{identityBlack},#{bankCardFront}
			,#{bankCardBlack},#{shopAddress},#{email},#{subBranch}<if test="examineStatus != null and examineStatus != ''">,#{examineStatus}</if>)
	</insert>
	
	<select id="getShopPayMsg" resultMap="shopPayMsg">
		SELECT
			id AS id
			,shop_unique AS shopUnique
			,DATE_FORMAT(datetime,"%Y-%m-%d %T") AS datetime
			,legalPerson AS legalPerson
			,userPhone AS userPhone
			,license AS license
			,doorPhoto AS doorPhoto
			,identityFront AS identityFront
			,identityBlack AS identityBlack
			,bankCardFront AS bankCardFront
			,bankCardBlack AS bankCardBlack
			,examine_status AS examineStatus
			,DATE_FORMAT(examine_date,"%Y-%m-%d %T") AS examineDate
			,valid_type AS validType
			,examine_remarks AS examineRemarks
			,shop_address AS shopAddress
			,email AS email
			,subBranch AS subBranch
		FROM
			shop_pay_msg
		WHERE
			shop_unique = #{shopUnique}
		<if test="validType != null and validType != ''">
			AND valid_type = #{validType}
		</if>
		ORDER BY id DESC LIMIT 1
	</select>
	
	<resultMap type="org.haier.shop.entity.ShopPayMsg" id="shopPayMsg">
		<id property="id" column="id"/>
		<result property="shopUnique" column="shopUnique"/>
		<result property="datetime" column="datetime"/>
		<result property="legalPerson" column="legalPerson"/>
		<result property="userPhone" column="userPhone"/>
		<result property="license" column="license"/>
		<result property="doorPhoto" column="doorPhoto"/>
		<result property="identityFront" column="identityFront"/>
		<result property="identityBlack" column="identityBlack"/>
		<result property="bankCardFront" column="bankCardFront"/>
		<result property="bankCardBlack" column="bankCardBlack"/>
		<result property="examineStatus" column="examineStatus"/>
		<result property="examineDate" column="examineDate"/>
		<result property="validType" column="validType"/>
		<result property="examineRemarks" column="examineRemarks"/>
		<result property="shopAddress" column="shopAddress"/>
		<result property="email" column="email"/>
	</resultMap>
	
	<update id="modifyShopPayMsg">
		UPDATE shop_pay_msg
		<set>
			<if test="shopUnique != null and shopUnique !=''">shop_unique = #{shopUnique},</if>			
			<if test="legalPerson != null and legalPerson != ''">legalPerson = #{legalPerson},</if>
			<if test="userPhone != null and userPhone !=''">userPhone = #{userPhone},</if>			
			<if test="license != null and license != ''">license = #{license},</if>
			<if test="doorPhoto != null and doorPhoto !=''">doorPhoto = #{doorPhoto},</if>			
			<if test="identityFront != null and identityFront != ''">identityFront = #{identityFront},</if>
			<if test="identityBlack != null and identityBlack !=''">identityBlack = #{identityBlack},</if>			
			<if test="bankCardFront != null and bankCardFront != ''">bankCardFront = #{bankCardFront},</if>
			<if test="bankCardBlack != null and bankCardBlack !=''">bankCardBlack = #{bankCardBlack},</if>	
			<if test="examineStatus != null and examineStatus != ''">examine_status = #{examineStatus},</if>
			<if test="examineStatus != null and examineStatus != '' and examineStatus != '3'"> examine_date = NOW(),</if>
			<if test="shopAddress != null and shopAddress != ''">shop_address = #{shopAddress},</if>
			<if test="email != null and email != ''">email = #{email},</if>
			<if test="subBranch != null and subBranch != ''">subBranch = #{subBranch}</if>
			<if test="examineRemarks !=null and examineRemarks !=''">examine_remarks = #{examineRemarks}</if>		
		</set>
		<where>
			<if test="id != null and id != ''"> AND id = #{id}</if>
			<if test="shopUnique != null and shopUnique != '' "> AND shop_unique = #{shopUnique}</if>
		</where>
	</update>
	<update id="setSubAccountByShopUnique" parameterType="org.haier.shop.params.SetSubAccountParams">
		UPDATE shop_pay_type SET
			sub_account = #{subAccount}
		WHERE shop_unique =#{shopUnique}
	</update>


	<select id="queryShopYiTongPayMsg" resultType="map">
		SELECT 
			id AS id,
			mch_id  AS mchId,
			mch_key AS mchKey,
			other_set AS otherSet,
			default_type AS defaultType
		FROM
			shop_pay_type
		WHERE
			pay_type = 3
		AND shop_unique= #{shopUnique}
		
	</select>
	
	<select id="queryShopPayMsgByPage" resultType="map">
		SELECT
			spm.id AS id,
			s.shop_name AS shopName,
			s.shop_unique AS shopUnique,
			DATE_FORMAT(spm.datetime,"%Y-%m-%d") AS datetime,
			spm.legalPerson AS legalPerson,
			spm.userPhone AS userPhone,
			spm.license AS license,
			spm.doorPhoto,
			spm.identityFront,
			spm.identityBlack,
			spm.bankCardFront,
			spm.bankCardBlack,
			CASE spm.examine_status WHEN 1 THEN "待审核" WHEN 2 THEN "审核不通过" WHEN 3 THEN "重新提交" WHEN 4 THEN "审核通过" WHEN 5 THEN "已拒绝" ELSE "未知" END AS examineStatus,
			spm.shop_address AS shopAddress,
			spm.email,
			spm.subBranch
		FROM
			shop_pay_msg spm LEFT JOIN shops s ON spm.shop_unique = s.shop_unique
		WHERE
			id IN
		(	SELECT
				max(id) AS id
			FROM
				shop_pay_msg spm
			<if test="map.shopMsg != null and map.shopMsg != ''">
				LEFT JOIN shops s ON spm.shop_unique = s.shop_unique
			</if>
			<where>
				<if test="map.examineStatus != '-1' and map.examineStatus != null and map.examineStatus != ''"> AND examine_status = #{map.examineStatus}</if>
				<if test="map.startTime != null and map.startTime != ''"> AND datetime > #{map.startTime}</if>
				<if test="map.endTime != null and map.endTime != ''"> AND #{map.endTime} > datetime</if>
				<if test="map.shopMsg != null and map.shopMsg != ''"> AND (s.shop_unique LIKE #{map.shopMsg} OR s.shop_name LIKE #{map.shopMsg})</if>
			</where>
			GROUP BY spm.shop_unique
		) AND s.examinestatus = 4
		<if test="ps.pageNum != null and ps.pageSize != null"> LIMIT #{ps.startNum},#{ps.pageSize}</if>
	</select>
	
	<select id="queryShopPayMsgCount" resultType="int">
		SELECT
			COUNT(DISTINCT spm.shop_unique)
		FROM
			shop_pay_msg spm 
		<if test="shopMsg != null and shopMsg != ''"> LEFT JOIN shops s ON spm.shop_unique = s.shop_unique</if>
		<where>
			<if test="examineStatus != '-1' and examineStatus != null and examineStatus != ''"> AND examine_status = #{examineStatus}</if>
			<if test="startTime != null and startTime != ''"> AND datetime > #{startTime}</if>
			<if test="endTime != null and endTime != ''"> AND #{endTime}> datetime</if>
			<if test="shopMsg != null and shopMsg != ''"> AND (s.shop_unique LIKE #{shopMsg} OR s.shop_name LIKE #{shopMsg})</if>
		</where>
	</select>
	
	<select id="queryShopPayMsgExamineStatus" resultType="map">
		SELECT
			examine_status AS examineStatus,
			examine_explain AS examineExplain
		FROM
			shop_pay_msg_status
	</select>
	<select id="getShopPayMsgBySubAccount" resultType="java.util.Map">
		select shop_unique from shop_pay_type where sub_account = #{subAccount}
	</select>

</mapper>