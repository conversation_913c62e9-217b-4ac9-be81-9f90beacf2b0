<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xsi:schemaLocation="http://www.springframework.org/schema/beans
        http://www.springframework.org/schema/beans/spring-beans-4.3.xsd">

    <!-- 自定义域realm -->
    <bean id="custom_Realm" class="org.haier.shop.realm.CustomRealm"></bean>
    <!--自定义LogoutFilter,退出-->  
	  <bean id="logoutFilter" class="org.haier.shop.realm.SystemLogoutFilter">  
	      <property name="redirectUrl" value="/loginMain.do"/>
	  </bean>  
	  <bean id="logoutFilterYN" class="org.haier.shop.realm.SystemLogoutFilter">  
	      <property name="redirectUrl" value="/loginYn.do"/>  
	  </bean>  
	  
	 <bean id="redisManager" class="org.haier.shop.shiro.RedisManager"></bean>
    <!-- Session ID 生成器 -->
 	 <bean id="sessionIdGenerator" class="org.apache.shiro.session.mgt.eis.JavaUuidSessionIdGenerator"></bean>
     
     <bean id="sessionDAO" class="org.haier.shop.shiro.MySessionDao">
        <property name="sessionIdGenerator" ref="sessionIdGenerator"></property>
    </bean>      
    <!-- 会话管理器 -->  
    <bean id="sessionManager" class="org.apache.shiro.web.session.mgt.DefaultWebSessionManager">  
        <!-- session的失效时长，单位毫秒 -->  
        <property name="globalSessionTimeout" value="43200000"/>  
        <!-- 删除失效的session -->  
        <property name="deleteInvalidSessions" value="true"/>
        
        <property name="sessionDAO" ref="sessionDAO"></property>
        <!-- sessionIdCookie的实现,用于重写覆盖容器默认的JSESSIONID -->
        <property name="sessionIdCookie" ref="sharesession" />  
    </bean>  
        <!-- sessionIdCookie的实现,用于重写覆盖容器默认的JSESSIONID -->
    <bean id="sharesession" class="org.apache.shiro.web.servlet.SimpleCookie">
        <!-- cookie的name,对应的默认是 JSESSIONID -->
        <constructor-arg name="name" value="SHAREJSESSIONID" />
        <!-- jsessionId的path为 / 用于多个系统共享jsessionId -->
        <property name="path" value="/" />
        <property name="httpOnly" value="true"/>
    </bean>
    

    
     <!-- 用户授权/认证信息Cache, 采用EhCache  缓存 -->  
    <bean id="shiroEhcacheManager" class="org.apache.shiro.cache.ehcache.EhCacheManager">  
        <property name="cacheManagerConfigFile" value="classpath:conf/ehcache-shiro.xml"/>  
    </bean>
    
    <!-- 安全管理器  ref对象-->
    <bean id="securityManager" class="org.apache.shiro.web.mgt.DefaultWebSecurityManager">
        <property name="realm" ref="custom_Realm"/>
         <!-- 缓存 -->  
         <property name="rememberMeManager" ref="rememberMeManager"/>
         <property name="cacheManager" ref="shiroEhcacheManager"/>
        <!-- 注入session管理器 -->  
        <property name="sessionManager" ref="sessionManager" />
    </bean>
     <!-- rememberMe管理器 -->
	<bean id="rememberMeManager"  class="org.apache.shiro.web.mgt.CookieRememberMeManager">
	    <property name="cipherKey" value="org.haier.shop.shiro.MySymmetricCipherService.getCipherKey()"/>
	    <property name="cookie" ref="rememberMeCookie"/>
	</bean>
    <bean id="rememberMeCookie" class="org.apache.shiro.web.servlet.SimpleCookie">
	    <constructor-arg value="rememberMe"/>
	    <property name="httpOnly" value="true"/>
	    <property name="maxAge" value="2592000"/><!-- 30天 -->
	</bean>
    <!-- shiro filter -->
    <bean id="shiroFilter" class="org.apache.shiro.spring.web.ShiroFilterFactoryBean">
        <!-- 安全管理器必须的 -->
        <property name="securityManager" ref="securityManager"/>
        <!-- 身份认证失败   认证提交的地址 -->
        <property name="loginUrl" value="/loginMain.do"/>
        <!-- 登录成功后 -->        
        <property name="successUrl" value="/manager/mainPage.do"></property>
        <!-- 权限认证失败    没有权限认证提交的地址 -->
        <property name="unauthorizedUrl" value="/unauthorized"/>
        <property name="filters">
            <map>
                <!--退出过滤器-->
                <entry key="logout" value-ref="logoutFilter" />
                <entry key="logoutYN" value-ref="logoutFilterYN" />
            </map>
        </property>
        <!-- Shiro连接约束配置,即过滤链的定义 -->
        <property name="filterChainDefinitions">
            <value>
                <!-- anon表示此地址不需要任何权限即可访问 -->
                /MP_verify_2FCa3oJpjetCJxDj.txt=anon
                /payType/setSubAccount.do=anon
                /subAccount/addOrder.do=anon
                /logout.do=logout
                /logoutYN.do=logoutYN  
                /loginYn.do=anon
                /unicomMonitor/toLivePage.do=anon
                /activity/newsIndex.do=anon
                /activity/queryNewsManagerList.do=anon
                /activity/lookNewsManagerListPage.do=anon
                /activity/lookNewsDetailPage.do=anon
                //businessCard/** = anon
                /activity/upload.do=anon
                /farm/**=anon<!-- 益农 -->
                /branchStore/**=anon
                /note/** = anon
                /speech/** = anon
                /html/shop/register.do=anon
                /html/shop/verifyUser.do=anon
                /html/shop/verifyShop.do=anon
                /shopping/scanCodeCallBack.do=anon <!--商城回调接口  -->
                /shopping/callBack**.do=anon<!-- 支付回调合集 -->
                /shopping/scanCodeCallBackWJ.do=anon<!-- 五金商城回调接口 -->
                /shopping/wxPayCallBack.do=anon <!--商城回调接口  -->
                /shopping/aliPayCallBack.do=anon <!--商城回调接口  -->
                /shopping/purchaseHelibaoCallBack.do=anon <!--商城回调接口  -->
                /shopping/orderSettlementCallBack.do=anon<!-- 月结回调接口 -->
                /shopping/getShopGold.do=anon <!--获取店铺金圈币接口  -->
                /shopping/queryShopMessage.do=anon
                /shopping/callBackHelibaoPayYN.do=anon
                /shopping/callBackHelibaoPayReturnMoney.do=anon
                /shopping/saveOrderYN.do=anon
                /shopping/confirmYNOrder.do=anon
                /shopping/cancelOrderYN.do=anon
                /shopping/generateCodeHLBRetrunMoneyYN.do=anon
                /shopping/queryYNOrderStatus.do=anon
                /shopping/saveOrderYNPC.do=anon
                /beans/addShopBeanPromation.do=anon
                /beans/addCardRecord.do=anon
                /beans/queryBeansConfig.do=anon
                /beans/updatePromationStatus.do=anon
                /beans/updateDiKou.do=anon
                /beans/weixinNotifyReturnMoney.do=anon
                /beans/weixinNotifyReturnMoneyYN.do=anon
                /shopping/shopUpdateAuthPayCallBack.do=anon<!-- shopUpdate供货商网页支付回调 -->
                /shopping/getGoodsKindList.do=anon
                /supperlierInfo/querySchoolContentList.do=anon
                /supperlierInfo/querySchoolContent.do=anon
                /shopping/updateShopGold.do=anon
                /shopping/addShopGold.do=anon
                /shelfState/updatePcShelfState.do=anon
                /countMsgYiNong/**=anon
                /agreement/**=anon<!-- 故障处理 -->
                /html/shop/updateShop.do=anon
                /shelfState/updateShelfState.do=anon
                /html/purchase/**=anon
                /html/test/**=anon
                /ele/**=anon <!--饿了么相关接口  -->
                /eleDelivery/**=anon <!-- 蜂鸟配送相关接口 -->
                /mtTakeaway/**=anon <!-- 美团外卖相关接口 -->
                /peisong/**=anon <!-- 美团配送费相关接口 -->
                /app/**=anon <!-- 进货订单查询控制器-商家app -->
                /pc/**=anon <!-- 收银端接口 -->
                /callback/**=anon <!-- 支付回调 -->
                /register.do=anon <!-- 注册页面 -->
                /registerYn.do=anon<!-- 益农注册地址 -->
                /registerApp.do=anon<!-- 手机注册h5页面 -->
                /html/shop/registerApp.do=anon<!-- 手机注册h5页面 -->
                /perfectInfoPageApp.do=anon<!-- 完善信息页面 -->
                /perfectInfoPage.do=anon<!-- 完善信息页面 -->
                /payType/saveShopPayMsg.do=anon<!-- 完善信息 -->
                /registerSuccess.do=anon<!-- 注册成功页面 -->
                /shopsStaff/selectMap.do=anon <!-- 选择地图 -->
                /shopsStaff/selectMapApp.do=anon <!-- 选择地图 -->
                /html/supplier/getMapCenter.do=anon <!-- 选择地图中心 -->
                /testHtml/**=anon<!-- 测试用的网页 -->
                /images/**=anon<!-- 静态图片文件 -->
                /area/**=anon <!-- 获取省市区 -->
                /data/**=anon
                /**/dataScreen/**=anon<!-- 数据屏 -->
                /**/dataScreen.do=anon<!-- 数据屏 -->
                /**/dataSearch/**=anon<!-- 数据屏 -->
                /css/**=anon<!-- css文件 -->
                /js/pub/**=anon<!-- css文件 -->
                /dataGoods/**=anon<!-- 数据屏 -->
                /logistics/**=anon<!-- 数据屏 -->
                /h5/**=anon<!-- h5页面 -->
                /new/**=anon
                /resources/**=anon
                /js/**=anon
                /static/**=anon
                /util/**=anon
                /feedBack/saveShopExamine.do=anon
				/feedBack/queryShopExamineDetail.do=anon
				/feedBack/queryShopExamineList.do=anon
				/loan/queryShopLoanList.do=anon 
				/loan/updateShopLoanMsg.do=anon   
				/loan/updateShopLoanMsg.do=anon   
				/loan/queryLoanShopDetail.do=anon  
				/loan/queryWindControl.do=anon   
				/loan/queryWindControlDetail.do=anon  
				/loan/queryWindControlDetail3.do=anon   
				/loan/queryWindControlDetail2.do=anon
				/feedBack/queryShopExamineListPage.do=anon
				/feedBack/queryShopDetailPage.do=anon
				/loan/toShopLoanList.do=anon
				/loan/toShopLoanDetail.do=anon
				/loan/toEditQuota.do=anon
				/loan/toStopLoan.do=anon
				/loan/toWindControl.do=anon
				/loan/toWindControlDetail.do=anon
				/loan/toWindControlDetail3.do=anon
				/loan/toWindControlDetail2.do=anon
				/util/queryShopsMessage.do=anon
				/rotation/addNewRotationImg.do=anon
                /shopsBatch/register.do=anon
                /shopsBatch/batchPayCode.do=anon
                /aiLogin/loginMain.do=anon
                /aiLogin/loginOut.do=anon
                /aiLogin/checkLogin.do=anon
                /** = authc
                
            </value>
        </property>
    </bean>
    <!-- Shiro生命周期处理器 -->
    <bean id="lifecycleBeanPostProcessor" class="org.apache.shiro.spring.LifecycleBeanPostProcessor"></bean>
    <bean class="org.apache.shiro.spring.security.interceptor.AuthorizationAttributeSourceAdvisor">
        <property name="securityManager" ref="securityManager"/>
    </bean>
   
</beans>
