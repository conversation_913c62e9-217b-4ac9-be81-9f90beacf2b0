package org.haier.eledelivery.request;

import java.io.IOException;

import org.haier.eledelivery.util.JsonUtils;
import org.haier.eledelivery.util.URLUtils;

/**
 * 查询骑手位置对应的 字段
 */
public class CarrierOrderRequest extends AbstractRequest {
   
	private CarrierOrderRequestData data ;

    public static class CarrierOrderRequestData {
        private String partner_order_code;
       
		public String getPartner_order_code() {
			return partner_order_code;
		}

		public void setPartner_order_code(String partner_order_code) {
			this.partner_order_code = partner_order_code;
		}

		@Override
		public String toString() {
			return "CarrierOrderRequestData [partner_order_code=" + partner_order_code +"]";
		}

    }

    public String getData() throws IOException {
        return URLUtils.getInstance().urlEncode(JsonUtils.getInstance().objectToJson(data));
    }

    public void setData(CarrierOrderRequestData data) {
        this.data = data;
    }
}
