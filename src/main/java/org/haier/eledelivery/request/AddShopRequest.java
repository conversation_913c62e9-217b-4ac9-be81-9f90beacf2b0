package org.haier.eledelivery.request;

import java.io.IOException;

import org.haier.eledelivery.util.JsonUtils;
import org.haier.eledelivery.util.URLUtils;

/**
 * 添加门店对应的 字段
 */
public class AddShopRequest extends AbstractRequest {
   
	private AddShopRequestData data ;

    public static class AddShopRequestData {
        private String chain_store_code;
        private String chain_store_name;
        private String contact_phone;
        private String address;
        private int position_source;
        private String longitude;
        private String latitude;
        private int service_code;
        
		public String getChain_store_code() {
			return chain_store_code;
		}

		public void setChain_store_code(String chain_store_code) {
			this.chain_store_code = chain_store_code;
		}

		public String getChain_store_name() {
			return chain_store_name;
		}

		public void setChain_store_name(String chain_store_name) {
			this.chain_store_name = chain_store_name;
		}

		public String getContact_phone() {
			return contact_phone;
		}

		public void setContact_phone(String contact_phone) {
			this.contact_phone = contact_phone;
		}

		public String getAddress() {
			return address;
		}

		public void setAddress(String address) {
			this.address = address;
		}

		public int getPosition_source() {
			return position_source;
		}

		public void setPosition_source(int position_source) {
			this.position_source = position_source;
		}

		public String getLongitude() {
			return longitude;
		}

		public void setLongitude(String longitude) {
			this.longitude = longitude;
		}

		public String getLatitude() {
			return latitude;
		}

		public void setLatitude(String latitude) {
			this.latitude = latitude;
		}

		public int getService_code() {
			return service_code;
		}

		public void setService_code(int service_code) {
			this.service_code = service_code;
		}

		@Override
		public String toString() {
			return "AddShopRequestData [chain_store_code=" + chain_store_code + ", chain_store_name="
					+ chain_store_name + ", contact_phone=" + contact_phone
					+ ", address=" + address + ", position_source="
					+ position_source + ", longitude=" + longitude + ",latitude="+latitude+",service_code="+service_code+"]";
		}

    }

    public String getData() throws IOException {
        return URLUtils.getInstance().urlEncode(JsonUtils.getInstance().objectToJson(data));
    }

    public void setData(AddShopRequestData data) {
        this.data = data;
    }
}
