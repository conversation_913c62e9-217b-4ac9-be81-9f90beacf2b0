package org.haier.eledelivery.request;

import java.io.IOException;

import org.haier.eledelivery.util.JsonUtils;
import org.haier.eledelivery.util.URLUtils;

/**
 * 查询门店配送服务对应的 字段
 */
public class DeliveryShopRequest extends AbstractRequest {
   
	private DeliveryShopRequestData data ;

    public static class DeliveryShopRequestData {
        private String chain_store_code;
        private int position_source;
        private String receiver_longitude;
        private String receiver_latitude;
        
		public String getChain_store_code() {
			return chain_store_code;
		}

		public void setChain_store_code(String chain_store_code) {
			this.chain_store_code = chain_store_code;
		}

		public int getPosition_source() {
			return position_source;
		}

		public void setPosition_source(int position_source) {
			this.position_source = position_source;
		}

		public String getReceiver_longitude() {
			return receiver_longitude;
		}

		public void setReceiver_longitude(String receiver_longitude) {
			this.receiver_longitude = receiver_longitude;
		}

		public String getReceiver_latitude() {
			return receiver_latitude;
		}

		public void setReceiver_latitude(String receiver_latitude) {
			this.receiver_latitude = receiver_latitude;
		}

		@Override
		public String toString() {
			return "DeliveryShopRequestData [chain_store_code=" + chain_store_code + ", position_source="
					+ position_source + ", receiver_longitude=" + receiver_longitude + ",receiver_latitude="+receiver_latitude+"]";
		}

    }

    public String getData() throws IOException {
        return URLUtils.getInstance().urlEncode(JsonUtils.getInstance().objectToJson(data));
    }

    public void setData(DeliveryShopRequestData data) {
        this.data = data;
    }
}
