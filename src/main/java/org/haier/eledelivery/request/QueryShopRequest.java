package org.haier.eledelivery.request;

import java.io.IOException;

import org.haier.eledelivery.util.JsonUtils;
import org.haier.eledelivery.util.URLUtils;

/**
 * 查询门店对应的 字段
 */
public class QueryShopRequest extends AbstractRequest {
   
	private QueryShopRequestData data ;

    public static class QueryShopRequestData {
        private String[] chain_store_code;
        private String[] chain_store_name;
        	
		public String[] getChain_store_code() {
			return chain_store_code;
		}

		public void setChain_store_code(String[] chain_store_code) {
			this.chain_store_code = chain_store_code;
		}

		public String[] getChain_store_name() {
			return chain_store_name;
		}

		public void setChain_store_name(String[] chain_store_name) {
			this.chain_store_name = chain_store_name;
		}

		@Override
		public String toString() {
			return "QueryShopRequestData [chain_store_code=" + chain_store_code + ", chain_store_name="
					+ chain_store_name +"]";
		}

    }

    public String getData() throws IOException {
        return URLUtils.getInstance().urlEncode(JsonUtils.getInstance().objectToJson(data));
    }

    public void setData(QueryShopRequestData data) {
        this.data = data;
    }
}
