package org.haier.eledelivery.util;

import java.io.IOException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.logging.Logger;

import javax.annotation.Resource;

import org.apache.http.message.BasicNameValuePair;
import org.haier.eledelivery.config.ElemeOpenConfig;
import org.haier.eledelivery.config.RequestConstant;
import org.haier.eledelivery.response.TokenResponse;
import org.haier.eledelivery.response.TokenResponse.TokenData;
import org.haier.eledelivery.service.EleDeliveryService;
import org.haier.eledelivery.sign.OpenSignHelper;
import org.haier.meituan.util.MUtil;

/**
 * 定时任务，更新蜂鸟配送平台token
 *
 */
public class ObtainTokenUtil {
	
	private static final Logger logger = Logger.getGlobal();
	
	@Resource
	private EleDeliveryService eleDeliveryService;
	
	/**
	 * 获取蜂鸟配送平台token，判断是否即将过期，对即将过期的token进行刷新
	 */
	public void updateAccessToken(){
		logger.info("定时任务，更新蜂鸟配送平台token开始");
		try {
			//获取蜂鸟配送平台access_token
			Map<String ,Object> tokenMap = eleDeliveryService.getAccessToken();
			SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
			if(tokenMap != null){
				String expire_time = MUtil.strObject(tokenMap.get("expire_time"));
				expire_time = expire_time.substring(0, expire_time.indexOf("."));
				Date failure_date = sdf.parse(expire_time);
				Date date = new Date();
				if(date.getTime()+60*60*1000 >= failure_date.getTime()){//当前时间+1小时 大于等于失效时间，刷新token
					TokenData data = getTokenData();
					if(data != null){
						Map<String ,Object> params = new HashMap<String, Object>();
						String d = sdf.format(data.getExpire_time());  
						params.put("access_token", data.getAccess_token());
						params.put("expire_time", d);
						eleDeliveryService.updateAccessToken(params);
					}
				}
			}else{
				TokenData data = getTokenData();
				if(data != null){
					Map<String ,Object> params = new HashMap<String, Object>();
					String d = sdf.format(data.getExpire_time());  
					Date date = sdf.parse(d);  
					params.put("token_type", "0");
					params.put("access_token", data.getAccess_token());
					params.put("expire_time", date);
					eleDeliveryService.insertAccessToken(params);
				}
			}
		} catch (Exception e) {
			logger.info("定时任务，更新蜂鸟配送平台token异常");
			e.printStackTrace();
		}
		logger.info("定时任务，更新蜂鸟配送平台token结束");
	}
	
	public TokenData getTokenData() throws IOException{
		String url = ElemeOpenConfig.API_URL + RequestConstant.obtainToken;

        String salt = String.valueOf(RandomUtils.getInstance().generateValue(1000, 10000));
        String sig = OpenSignHelper.generateSign(ElemeOpenConfig.appId, salt, ElemeOpenConfig.secretKey);

        // 请求token
        List<BasicNameValuePair> paramsToken = new ArrayList<>();
        paramsToken.add(new BasicNameValuePair("app_id", ElemeOpenConfig.appId));
        paramsToken.add(new BasicNameValuePair("salt", salt));
        paramsToken.add(new BasicNameValuePair("signature", sig));

        String tokenRes = HttpClient.get(url, paramsToken);

        //生成token
        TokenData data = JsonUtils.getInstance().readValue(tokenRes, TokenResponse.class).getData();
        
        return data;
	}
}
