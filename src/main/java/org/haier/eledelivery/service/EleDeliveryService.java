package org.haier.eledelivery.service;

import java.util.Map;

import org.haier.shop.util.ShopsResult;

public interface EleDeliveryService {

	/**
	 * 获取蜂鸟配送平台access_token
	 */
	public Map<String ,Object> getAccessToken();
	
	/**
	 * 添加蜂鸟配送平台access_token
	 */
	public void insertAccessToken(Map<String ,Object> params);
	
	/**
	 * 修改蜂鸟配送平台access_token
	 */
	public void updateAccessToken(Map<String ,Object> params);
	
	/**
	 * 商户请求推单
	 * @param sale_list_unique 订单编号
	 * @param order_weight 订单总重量（kg）
	 */
	public ShopsResult createOrder(String sale_list_unique,String order_weight);
	
	/**
	 * 取消配送单
	 * @param sale_list_unique 订单编号
	 * @param order_cancel_code 订单取消编码（0:其他, 1:联系不上商户, 2:商品已经售完, 3:用户申请取消, 4:运力告知不配送 让取消订单, 5:订单长时间未分配, 6:接单后骑手未取件）
	 * @param order_cancel_description  订单取消描述（order_cancel_code为0时必填）
	 */
	public ShopsResult cancelOrder(String sale_list_unique,int order_cancel_code,String order_cancel_description);
	
	/**
	 * 查询配送单
	 * @param sale_list_unique 订单编号
	 * @return
	 */
	public ShopsResult queryOrder(String sale_list_unique);
	
	/**
	 * 投诉配送单
	 * @param sale_list_unique 订单编号
	 * @param order_complaint_code 订单投诉编码（230:其他, 150:未保持餐品完整, 160:服务态度恶劣, 190:额外索取费用, 170:诱导收货人或商户退单, 140:提前点击送达, 210:虚假标记异常, 220:少餐错餐, 200:虚假配送, 130:未进行配送）
	 * @param order_complaint_desc 订单投诉描述（order_complaint_code为230时必填）
	 * @return
	 */
	public ShopsResult complaintOrder(String sale_list_unique,String order_complaint_code,String order_complaint_desc);
	
	/**
	 * 订单状态回调
	 */
	public void deliveryStatus(String data,String salt,String signature);
	
	/**
	 * 添加门店信息
	 * @param shop_unique 店铺编号
	 * @param shop_name 店铺名称
	 * @param shop_phone 店铺联系方式
	 * @param shop_address_detail 店铺详细地址
	 * @param shop_longitude 店铺经度
	 * @param shop_latitude 店铺纬度
	 * @param service_code 配送服务(1:蜂鸟配送, 2:蜂鸟优送, 3:蜂鸟快送)
	 */
	public ShopsResult addShop(String shop_unique,String shop_name,String shop_phone,String shop_address_detail,String shop_longitude,String shop_latitude,String service_code);
	
	/**
	 * 查询门店信息
	 * @param shop_unique 店铺编号
	 */
	public ShopsResult queryShop(String shop_unique);
	
	/**
	 * 更新门店信息
	 * @param shop_unique 店铺编号
	 * @param shop_name 店铺名称
	 * @param shop_phone 店铺联系方式
	 * @param shop_address_detail 店铺详细地址
	 * @param shop_longitude 店铺经度
	 * @param shop_latitude 店铺纬度
	 * @param service_code 配送服务(1:蜂鸟配送, 2:蜂鸟优送, 3:蜂鸟快送)
	 */
	public ShopsResult updateShop(String shop_unique,String shop_name,String shop_phone,String shop_address_detail,String shop_longitude,String shop_latitude,String service_code);
	
	
	/**
	 * 查询门店配送服务
	 * @param shop_unique 店铺编号
	 * @param receiver_longitude 收货点经度
	 * @param receiver_latitude  收货点纬度
	 * @return
	 */
	public ShopsResult deliveryShop(String shop_unique,String receiver_longitude,String receiver_latitude);
	
	/**
	 * 查询骑手位置
	 * @param sale_list_unique 订单编号
	 * @return
	 */
	public ShopsResult carrierOrder(String sale_list_unique);
}
