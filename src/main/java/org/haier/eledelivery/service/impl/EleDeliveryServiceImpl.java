package org.haier.eledelivery.service.impl;

import java.math.BigDecimal;
import java.net.URLDecoder;
import java.util.Date;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;

import org.haier.eledelivery.config.ElemeOpenConfig;
import org.haier.eledelivery.config.RequestConstant;
import org.haier.eledelivery.dao.EleDeliveryDao;
import org.haier.eledelivery.request.AddShopRequest;
import org.haier.eledelivery.request.CancelOrderRequest;
import org.haier.eledelivery.request.CarrierOrderRequest;
import org.haier.eledelivery.request.DeliveryShopRequest;
import org.haier.eledelivery.request.ElemeCreateOrderRequest;
import org.haier.eledelivery.request.ElemeQueryOrderRequest;
import org.haier.eledelivery.request.OrderComplaintRequest;
import org.haier.eledelivery.request.QueryShopRequest;
import org.haier.eledelivery.request.UpdateShopRequest;
import org.haier.eledelivery.service.EleDeliveryService;
import org.haier.eledelivery.sign.OpenSignHelper;
import org.haier.eledelivery.util.HttpClient;
import org.haier.eledelivery.util.JsonUtils;
import org.haier.eledelivery.util.RandomUtils;
import org.haier.meituan.util.MUtil;
import org.haier.shop.util.ShopsResult;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Service
@Transactional
public class EleDeliveryServiceImpl implements EleDeliveryService{

	@Resource
	private EleDeliveryDao eleDeliveryDao;
	
	@Override
	public Map<String, Object> getAccessToken() {
		return eleDeliveryDao.getAccessToken();
	}

	@Override
	public void updateAccessToken(Map<String, Object> params) {
		eleDeliveryDao.updateAccessToken(params);
	}

	@Override
	public void insertAccessToken(Map<String, Object> params) {
		eleDeliveryDao.insertAccessToken(params);
	}

	@Override
	public ShopsResult createOrder(String sale_list_unique,String order_weight) {
		ShopsResult shopsResult = new ShopsResult();
		try {
			//获取订单信息
			Map<String ,Object> orderInfo = eleDeliveryDao.getOrderInfo(sale_list_unique);
			if(orderInfo == null){
				shopsResult.setStatus(3);
		        shopsResult.setMsg("订单信息为空");
		        return shopsResult;
			}
	        ElemeCreateOrderRequest.ElemeCreateRequestData data = new ElemeCreateOrderRequest.ElemeCreateRequestData();
	        //取货点信息
	        ElemeCreateOrderRequest.TransportInfo  transportInfo = new ElemeCreateOrderRequest.TransportInfo();
	        transportInfo.setTransport_name(MUtil.strObject(orderInfo.get("shop_name")));
	        transportInfo.setTransport_address(MUtil.strObject(orderInfo.get("shop_address_detail")));//取货点地址
	        transportInfo.setTransport_tel(MUtil.strObject(orderInfo.get("shop_phone")));//取货点联系方式
	        transportInfo.setTransport_longitude(new BigDecimal(MUtil.strObject(orderInfo.get("shop_longitude"))));//取货点经度
	        transportInfo.setTransport_latitude(new BigDecimal(MUtil.strObject(orderInfo.get("shop_latitude"))));//取货点纬度
	        transportInfo.setTransport_remark(MUtil.strObject(orderInfo.get("shop_remark")));//取货点备注
	        transportInfo.setPosition_source(2);//取货点经纬度来源（1:腾讯地图, 2:百度地图, 3:高德地图）
	        //收货点信息
	        ElemeCreateOrderRequest.ReceiverInfo receiverInfo = new ElemeCreateOrderRequest.ReceiverInfo();
	        receiverInfo.setReceiver_address(MUtil.strObject(orderInfo.get("sale_list_address")));//收货人地址
	        receiverInfo.setReceiver_name(MUtil.strObject(orderInfo.get("sale_list_name")));//收货人姓名
	        receiverInfo.setReceiver_primary_phone(MUtil.strObject(orderInfo.get("sale_list_phone")));//收货人联系方式
	        receiverInfo.setReceiver_longitude(new BigDecimal(MUtil.strObject(orderInfo.get("addr_longitude"))));//收货人经度
	        receiverInfo.setReceiver_latitude(new BigDecimal(MUtil.strObject(orderInfo.get("addr_latitude"))));//收货人纬度
	        receiverInfo.setPosition_source(2);//收货人经纬度来源（1:腾讯地图, 2:百度地图, 3:高德地图）
	        
	        //获取订单详细信息列表
	        List<Map<String ,Object>> orderDetailList = eleDeliveryDao.getOrderDetailList(sale_list_unique);
	        ElemeCreateOrderRequest.ItemsJson[] itemsJsons = new ElemeCreateOrderRequest.ItemsJson[orderDetailList.size()];
	        for(int i=0;i<orderDetailList.size();i++){
	        	 ElemeCreateOrderRequest.ItemsJson item = new ElemeCreateOrderRequest.ItemsJson();
	 	        item.setItem_name(MUtil.strObject(orderDetailList.get(i).get("goods_name")));//商品名称
	 	        int quantity = Double.valueOf(MUtil.strObject(orderDetailList.get(i).get("sale_list_detail_count"))).intValue();
	 	        item.setItem_quantity(quantity);//商品数量
	 	        item.setItem_actual_price(new BigDecimal(MUtil.strObject(orderDetailList.get(i).get("sale_list_detail_price"))));//商品实际支付金额
	 	        item.setItem_price(new BigDecimal(MUtil.strObject(orderDetailList.get(i).get("sale_list_detail_price"))));//商品原价
	 	        item.setIs_agent_purchase(0);//是否代购 0:否
	 	        item.setIs_need_package(0);//是否需要ele打包 0:否 1:是
	 	        itemsJsons[i] = item;
	        }
	        data.setTransport_info(transportInfo);
	        data.setReceiver_info(receiverInfo);
	        data.setItems_json(itemsJsons);

	        data.setPartner_order_code(MUtil.strObject(orderInfo.get("sale_list_unique")));//商户订单号
	        data.setNotify_url(ElemeOpenConfig.CALLBACK_URL);//回调地址,订单状态变更时会调用此接口传递状态信息

	        /**
	         * 1: 蜂鸟配送, 未向饿了么物流平台查询过站点的订单，支持两小时送达
	         * 2: 定点次日达, 提前向饿了么物流平台查询过配送站点的订单，支持次日送达
	         */
	        data.setOrder_type(1);// 订单类型
	        data.setOrder_weight(new BigDecimal(order_weight));
	        data.setOrder_total_amount(new BigDecimal(MUtil.strObject(orderInfo.get("sale_list_total"))));//订单总金额
	        data.setOrder_actual_amount(new BigDecimal(MUtil.strObject(orderInfo.get("sale_list_actually_received"))));//客户需要支付的金额
	        data.setOrder_remark(MUtil.strObject(orderInfo.get("sale_list_remarks")));//用户备注
	        data.setIs_invoiced(0); // 是否需要发票0：不需要；1：需要
	        data.setOrder_payment_status(1);//订单支付状态 0:未支付 1:已支付
	        data.setOrder_payment_method(1);//订单支付方式 1:在线支付
	        data.setIs_agent_payment(0); // 是否需要承运商代收货款 0：否 1：是
	        data.setGoods_count(4);//订单货物件数
	        //data.setRequire_receive_time(LocalDateTime.now().plusHours(1).atZone(ZoneId.systemDefault()).toInstant().toEpochMilli());   // 预计送达时间 要大于当前时间
	        data.setOrder_add_time(new Date().getTime());
	        
	        ElemeCreateOrderRequest request  = new ElemeCreateOrderRequest();
	        request.setData(data);
	        int salt = RandomUtils.getInstance().generateValue(1000, 10000);
	        request.setApp_id(ElemeOpenConfig.appId);
	        request.setSalt(salt);
	        
	        //获取token
	        Map<String ,Object> tokenMap = eleDeliveryDao.getAccessToken();
	        
	        //生成签名
	        Map<String, Object> sigStr = new LinkedHashMap<String,Object>();//注意添加的顺序, 应该如下面一样各个key值顺序一致
	        sigStr.put("app_id", ElemeOpenConfig.appId);
	        sigStr.put("access_token", MUtil.strObject(tokenMap.get("access_token")));//需要使用前面请求生成的token
	        sigStr.put("data", request.getData());
	        sigStr.put("salt", salt);
	        // 生成签名
	        String sig = OpenSignHelper.generateBusinessSign(sigStr);
	        request.setSignature(sig);

	        String requestJson = JsonUtils.getInstance().objectToJson(request);

	        String url = ElemeOpenConfig.API_URL + RequestConstant.orderCreate;
	        String resultJson = HttpClient.postBody(url, requestJson);
	        Map<String ,Object> resultMap = MUtil.jsonToMap(resultJson);
	        String code = MUtil.strObject(resultMap.get("code"));
	        if(code != null && code.equals("200")){
	        	//本地创建配送订单
	        	Map<String ,Object> params = new HashMap<String, Object>();
	        	params.put("sale_list_unique", sale_list_unique);
	        	params.put("delivery_type", "0");
	        	params.put("delivery_weight", order_weight);
	        	//查询配送单
	        	ShopsResult orderResult = queryOrder(sale_list_unique);
	        	if(orderResult.getStatus() == 1){
	        		Map<String ,Object> orderMap = (Map<String, Object>) orderResult.getData();
	        		params.put("delivery_price", orderMap.get("order_total_delivery_cost"));
	        	}
	        	eleDeliveryDao.addDeliveryOrder(params);
	        	//修改订单状态为7配送单待确认
	        	params.put("sale_list_handlestate", 7);
	        	eleDeliveryDao.updateOrderStatus(params);
	        	shopsResult.setStatus(1);
		        shopsResult.setMsg("成功");
	        }else{
	        	shopsResult.setStatus(2);
		        shopsResult.setMsg(MUtil.strObject(resultMap.get("msg")));
	        }
		} catch (Exception e) {
			shopsResult.setStatus(0);
			shopsResult.setMsg("蜂鸟配送平台创建推单异常："+e.getMessage());
		}
		return shopsResult;
	}

	@Override
	public void deliveryStatus(String data, String salt, String signature) {
		try {
			//获取token信息
			Map<String ,Object> tokenMap = eleDeliveryDao.getAccessToken();
			data = URLDecoder.decode(data, "UTF-8");
			
			Map<String, Object> sigStr = new LinkedHashMap<String,Object>();//注意添加的顺序, 应该如下面一样各个key值顺序一致
		    sigStr.put("app_id", ElemeOpenConfig.appId);
		    sigStr.put("access_token", MUtil.strObject(tokenMap.get("access_token")));//需要使用前面请求生成的token
		    sigStr.put("data", data);
		    sigStr.put("salt", salt);
		    // 生成签名
		    String sig = OpenSignHelper.generateBusinessSign(sigStr);
		    if(signature.equals(sig)){//验证签名通过
		    	Map<String ,Object> resultMap = MUtil.jsonToMap(data);
		    	String partner_order_code = MUtil.strObject(resultMap.get("partner_order_code"));//订单编号
		    	String order_status = MUtil.strObject(resultMap.get("order_status"));//蜂鸟配送单状态
		    	
		    	Map<String ,Object> params = new HashMap<String, Object>();
		    	params.put("sale_list_unique", partner_order_code);
		    	params.put("delivery_type", "0");
		    	if(order_status.equals("1")){//系统已接单
		    		params.put("delivery_status", 1);
		    	}else if(order_status.equals("20")){//已分配骑手
		    		params.put("delivery_status", 2);
		    		params.put("driver_name", MUtil.strObject(resultMap.get("carrier_driver_name")));
		    		params.put("driver_phone", MUtil.strObject(resultMap.get("carrier_driver_phone")));
		    	}else if(order_status.equals("80")){//骑手已到店
		    		params.put("delivery_status", 3);
		    	}else if(order_status.equals("2")){//配送中
		    		params.put("delivery_status", 4);
		    	}else if(order_status.equals("3")){//已送达
		    		params.put("delivery_status", 5);
		    		//修改订单状态为4已完成
		        	params.put("sale_list_handlestate", 4);
		        	eleDeliveryDao.updateOrderStatus(params);
		    	}else if(order_status.equals("5")){//系统拒单/配送异常
		    		params.put("delivery_status", 6);
		    	}
		    	//修改配送单状态
		    	eleDeliveryDao.updateDeliveryOrder(params);
		    	
		    }else{//验证签名失败
		    	throw new Exception("签名验证不通过");
		    }
		} catch (Exception e) {
			e.printStackTrace();
		}
	}

	@Override
	public ShopsResult cancelOrder(String sale_list_unique,int order_cancel_code, String order_cancel_description) {
		ShopsResult shopsResult = new ShopsResult();
		try {
	        CancelOrderRequest.CancelOrderRequstData data = new CancelOrderRequest.CancelOrderRequstData();
	        data.setOrder_cancel_description(order_cancel_description);
	        data.setOrder_cancel_reason_code(2);//订单取消原因代码(2:商家取消)
	        data.setOrder_cancel_code(order_cancel_code);
	        data.setPartner_order_code(sale_list_unique);
	        data.setOrder_cancel_time(new Date().getTime());

	        CancelOrderRequest cancelOrderRequest = new CancelOrderRequest();
	        cancelOrderRequest.setData(data);

	        int salt = RandomUtils.getInstance().generateValue(1000, 10000);

	        //获取token
	        Map<String ,Object> tokenMap = eleDeliveryDao.getAccessToken();
	        
	        Map<String, Object> sigStr = new LinkedHashMap<String,Object>() ;     // 注意添加的顺序, 应该如下面一样各个key值顺序一致
	        sigStr.put("app_id", ElemeOpenConfig.appId);
	        sigStr.put("access_token", MUtil.strObject(tokenMap.get("access_token")));        // 需要使用前面请求生成的token
	        sigStr.put("data", cancelOrderRequest.getData());
	        sigStr.put("salt", salt);

	        // 生成签名
	        String sig = OpenSignHelper.generateBusinessSign(sigStr);
	        cancelOrderRequest.setSignature(sig);
	        cancelOrderRequest.setApp_id(ElemeOpenConfig.appId);
	        cancelOrderRequest.setSalt(salt);

	        String requestJson = JsonUtils.getInstance().objectToJson(cancelOrderRequest);
	        String url = ElemeOpenConfig.API_URL + RequestConstant.orderCancel;
	        String resultJson = HttpClient.postBody(url, requestJson);
	        Map<String ,Object> resultMap = MUtil.jsonToMap(resultJson);
	        String code = MUtil.strObject(resultMap.get("code"));
	        if(code != null && code.equals("200")){
	        	//本地修改配送订单状态为已取消
	        	Map<String ,Object> params = new HashMap<String, Object>();
	        	params.put("sale_list_unique", sale_list_unique);
	        	params.put("delivery_type", "0");
	        	params.put("delivery_status", 7);
	        	eleDeliveryDao.updateDeliveryOrder(params);
	        	
	        	shopsResult.setStatus(1);
		        shopsResult.setMsg("成功");
	        }else{
	        	shopsResult.setStatus(2);
		        shopsResult.setMsg(MUtil.strObject(resultMap.get("msg")));
	        }
		} catch (Exception e) {
			shopsResult.setStatus(0);
			shopsResult.setMsg("蜂鸟配送平台取消配送单异常："+e.getMessage());
		}
		return shopsResult;
	}

	@Override
	public ShopsResult queryOrder(String sale_list_unique) {
		ShopsResult shopsResult = new ShopsResult();
		try {
	        ElemeQueryOrderRequest request = new ElemeQueryOrderRequest();
	        ElemeQueryOrderRequest.ElemeQueryRequestData data = new ElemeQueryOrderRequest.ElemeQueryRequestData();
	        data.setPartner_order_code(sale_list_unique);
	        request.setData(data);

	        int salt = RandomUtils.getInstance().generateValue(1000, 10000);
	        request.setApp_id(ElemeOpenConfig.appId);
	        request.setSalt(salt);
	        
	        //获取token
	        Map<String ,Object> tokenMap = eleDeliveryDao.getAccessToken();
	        /**
	         * 生成签名
	         */
	        Map<String, Object> sigStr = new LinkedHashMap<String,Object>();      // 注意添加的顺序, 应该如下面一样各个key值顺序一致
	        sigStr.put("app_id", ElemeOpenConfig.appId);
	        sigStr.put("access_token", MUtil.strObject(tokenMap.get("access_token")));        // 需要使用前面请求生成的token
	        sigStr.put("data", request.getData());
	        sigStr.put("salt", salt);
	        // 生成签名
	        String sig = OpenSignHelper.generateBusinessSign(sigStr);
	        request.setSignature(sig);

	        String requestJson = JsonUtils.getInstance().objectToJson(request);

	        String url = ElemeOpenConfig.API_URL + RequestConstant.orderQuery;
	        String resultJson = HttpClient.postBody(url, requestJson);
	        Map<String ,Object> resultMap = MUtil.jsonToMap(resultJson);
	        String code = MUtil.strObject(resultMap.get("code"));
	        if(code != null && code.equals("200")){
	        	String dataStr = MUtil.strObject(resultMap.get("data"));
	        	Map<String ,Object> deleveryOrder = MUtil.jsonToMap(dataStr);
	        	shopsResult.setData(deleveryOrder);
	        	shopsResult.setStatus(1);
		        shopsResult.setMsg("成功");
	        }else{
	        	shopsResult.setStatus(2);
		        shopsResult.setMsg(MUtil.strObject(resultMap.get("msg")));
	        }
		} catch (Exception e) {
			shopsResult.setStatus(0);
			shopsResult.setMsg("蜂鸟配送平台查询配送单异常："+e.getMessage());
		}
		return shopsResult;
	}

	@Override
	public ShopsResult complaintOrder(String sale_list_unique,String order_complaint_code, String order_complaint_desc) {
		ShopsResult shopsResult = new ShopsResult();
		try {
	        OrderComplaintRequest.OrderComplaintRequstData data = new OrderComplaintRequest.OrderComplaintRequstData();
	        data.setPartner_order_code(sale_list_unique);
	        data.setOrder_complaint_code(Integer.parseInt(order_complaint_code));
	        data.setOrder_complaint_desc(order_complaint_desc);
	        data.setOrder_complaint_time(new Date().getTime());

	        OrderComplaintRequest orderComplaintRequest = new OrderComplaintRequest();
	        orderComplaintRequest.setData(data);

	        int salt = RandomUtils.getInstance().generateValue(1000, 10000);

	        //获取token
	        Map<String ,Object> tokenMap = eleDeliveryDao.getAccessToken();
	        
	        Map<String, Object> sigStr = new LinkedHashMap<String,Object>();      // 注意添加的顺序, 应该如下面一样各个key值顺序一致
	        sigStr.put("app_id", ElemeOpenConfig.appId);
	        sigStr.put("access_token", MUtil.strObject(tokenMap.get("access_token")));        // 需要使用前面请求生成的token
	        sigStr.put("data", orderComplaintRequest.getData());
	        sigStr.put("salt", salt);

	        // 生成签名
	        String sig = OpenSignHelper.generateBusinessSign(sigStr);
	        orderComplaintRequest.setSignature(sig);

	        orderComplaintRequest.setApp_id(ElemeOpenConfig.appId);
	        orderComplaintRequest.setSalt(salt);

	        String requestJson = JsonUtils.getInstance().objectToJson(orderComplaintRequest);
	        String url = ElemeOpenConfig.API_URL + RequestConstant.orderComplaint;
	        String resultJson = HttpClient.postBody(url, requestJson);
	        Map<String ,Object> resultMap = MUtil.jsonToMap(resultJson);
	        String code = MUtil.strObject(resultMap.get("code"));
	        if(code != null && code.equals("200")){
	        	shopsResult.setStatus(1);
		        shopsResult.setMsg("成功");
	        }else{
	        	shopsResult.setStatus(2);
		        shopsResult.setMsg(MUtil.strObject(resultMap.get("msg")));
	        }
		} catch (Exception e) {
			shopsResult.setStatus(0);
			shopsResult.setMsg("蜂鸟配送平台查询配送单异常："+e.getMessage());
		}
		return shopsResult;
	}

	@Override
	public ShopsResult addShop(String shop_unique, String shop_name,
			String shop_phone, String shop_address_detail,
			String shop_longitude, String shop_latitude, String service_code) {
		ShopsResult shopsResult = new ShopsResult();
		try {
			AddShopRequest.AddShopRequestData data = new AddShopRequest.AddShopRequestData();
			data.setChain_store_code(shop_unique);
			data.setChain_store_name(shop_name);
			data.setContact_phone(shop_phone);
			data.setAddress(shop_address_detail);
			data.setLongitude(shop_longitude);
			data.setLatitude(shop_latitude);
			data.setPosition_source(2);//百度地图
			data.setService_code(Integer.parseInt(service_code));

			AddShopRequest addShopRequest = new AddShopRequest();
			addShopRequest.setData(data);

	        int salt = RandomUtils.getInstance().generateValue(1000, 10000);

	        //获取token
	        Map<String ,Object> tokenMap = eleDeliveryDao.getAccessToken();
	        
	        Map<String, Object> sigStr = new LinkedHashMap<String,Object>();      // 注意添加的顺序, 应该如下面一样各个key值顺序一致
	        sigStr.put("app_id", ElemeOpenConfig.appId);
	        sigStr.put("access_token", MUtil.strObject(tokenMap.get("access_token")));        // 需要使用前面请求生成的token
	        sigStr.put("data", addShopRequest.getData());
	        sigStr.put("salt", salt);

	        // 生成签名
	        String sig = OpenSignHelper.generateBusinessSign(sigStr);
	        addShopRequest.setSignature(sig);

	        addShopRequest.setApp_id(ElemeOpenConfig.appId);
	        addShopRequest.setSalt(salt);

	        String requestJson = JsonUtils.getInstance().objectToJson(addShopRequest);
	        String url = ElemeOpenConfig.API_URL + RequestConstant.addShop;
	        String resultJson = HttpClient.postBody(url, requestJson);
	        Map<String ,Object> resultMap = MUtil.jsonToMap(resultJson);
	        String code = MUtil.strObject(resultMap.get("code"));
	        if(code != null && code.equals("200")){
	        	//修改店铺是否加入蜂鸟配送：0否 1是
	        	Map<String ,Object> params = new HashMap<String, Object>();
	        	params.put("shop_unique", shop_unique);
	        	params.put("is_hummer", 1);
	        	eleDeliveryDao.updateShopIsHummer(params);
	        	shopsResult.setStatus(1);
		        shopsResult.setMsg("成功");
	        }else{
	        	shopsResult.setStatus(2);
		        shopsResult.setMsg(MUtil.strObject(resultMap.get("msg")));
	        }
		} catch (Exception e) {
			shopsResult.setStatus(0);
			shopsResult.setMsg("蜂鸟配送平台添加门店异常："+e.getMessage());
		}
		return shopsResult;
	}

	@Override
	public ShopsResult queryShop(String shop_unique) {
		ShopsResult shopsResult = new ShopsResult();
		try {
			String[] shop_unique_arr = new String[1];
			shop_unique_arr[0]= shop_unique;
			QueryShopRequest.QueryShopRequestData data = new QueryShopRequest.QueryShopRequestData();
			data.setChain_store_code(shop_unique_arr);

			QueryShopRequest queryShopRequest = new QueryShopRequest();
			queryShopRequest.setData(data);

	        int salt = RandomUtils.getInstance().generateValue(1000, 10000);

	        //获取token
	        Map<String ,Object> tokenMap = eleDeliveryDao.getAccessToken();
	        
	        Map<String, Object> sigStr = new LinkedHashMap<String,Object>();     // 注意添加的顺序, 应该如下面一样各个key值顺序一致
	        sigStr.put("app_id", ElemeOpenConfig.appId);
	        sigStr.put("access_token", MUtil.strObject(tokenMap.get("access_token")));        // 需要使用前面请求生成的token
	        sigStr.put("data", queryShopRequest.getData());
	        sigStr.put("salt", salt);

	        // 生成签名
	        String sig = OpenSignHelper.generateBusinessSign(sigStr);
	        queryShopRequest.setSignature(sig);

	        queryShopRequest.setApp_id(ElemeOpenConfig.appId);
	        queryShopRequest.setSalt(salt);

	        String requestJson = JsonUtils.getInstance().objectToJson(queryShopRequest);
	        String url = ElemeOpenConfig.API_URL + RequestConstant.queryShop;
	        String resultJson = HttpClient.postBody(url, requestJson);
	        Map<String ,Object> resultMap = MUtil.jsonToMap(resultJson);
	        String code = MUtil.strObject(resultMap.get("code"));
	        if(code != null && code.equals("200")){
	        	List<Map<String ,Object>> resultList = (List<Map<String, Object>>) resultMap.get("data");
	        	shopsResult.setData(resultList.get(0));
	        	shopsResult.setStatus(1);
		        shopsResult.setMsg("成功");
	        }else{
	        	shopsResult.setStatus(2);
		        shopsResult.setMsg(MUtil.strObject(resultMap.get("msg")));
	        }
		} catch (Exception e) {
			shopsResult.setStatus(0);
			shopsResult.setMsg("蜂鸟配送平台查询门店异常："+e.getMessage());
		}
		return shopsResult;
	}

	@Override
	public ShopsResult updateShop(String shop_unique, String shop_name,
			String shop_phone, String shop_address_detail,
			String shop_longitude, String shop_latitude, String service_code) {
		ShopsResult shopsResult = new ShopsResult();
		try {
			UpdateShopRequest.UpdateShopRequestData data = new UpdateShopRequest.UpdateShopRequestData();
			data.setChain_store_code(shop_unique);
			data.setChain_store_name(shop_name);
			data.setContact_phone(shop_phone);
			data.setAddress(shop_address_detail);
			data.setLongitude(shop_longitude);
			data.setLatitude(shop_latitude);
			data.setPosition_source(2);//百度地图
			data.setService_code(Integer.parseInt(service_code));

			UpdateShopRequest updateShopRequest = new UpdateShopRequest();
			updateShopRequest.setData(data);

	        int salt = RandomUtils.getInstance().generateValue(1000, 10000);

	        //获取token
	        Map<String ,Object> tokenMap = eleDeliveryDao.getAccessToken();
	        
	        Map<String, Object> sigStr = new LinkedHashMap<String,Object>();      // 注意添加的顺序, 应该如下面一样各个key值顺序一致
	        sigStr.put("app_id", ElemeOpenConfig.appId);
	        sigStr.put("access_token", MUtil.strObject(tokenMap.get("access_token")));        // 需要使用前面请求生成的token
	        sigStr.put("data", updateShopRequest.getData());
	        sigStr.put("salt", salt);

	        // 生成签名
	        String sig = OpenSignHelper.generateBusinessSign(sigStr);
	        updateShopRequest.setSignature(sig);

	        updateShopRequest.setApp_id(ElemeOpenConfig.appId);
	        updateShopRequest.setSalt(salt);

	        String requestJson = JsonUtils.getInstance().objectToJson(updateShopRequest);
	        String url = ElemeOpenConfig.API_URL + RequestConstant.updateShop;
	        String resultJson = HttpClient.postBody(url, requestJson);
	        Map<String ,Object> resultMap = MUtil.jsonToMap(resultJson);
	        String code = MUtil.strObject(resultMap.get("code"));
	        if(code != null && code.equals("200")){
	        	shopsResult.setStatus(1);
		        shopsResult.setMsg("成功");
	        }else{
	        	shopsResult.setStatus(2);
		        shopsResult.setMsg(MUtil.strObject(resultMap.get("msg")));
	        }
		} catch (Exception e) {
			shopsResult.setStatus(0);
			shopsResult.setMsg("蜂鸟配送平台更新门店异常："+e.getMessage());
		}
		return shopsResult;
	}
	
	@Override
	public ShopsResult deliveryShop(String shop_unique,String receiver_longitude,String receiver_latitude) {
		ShopsResult shopsResult = new ShopsResult();
		try {
			DeliveryShopRequest.DeliveryShopRequestData data = new DeliveryShopRequest.DeliveryShopRequestData();
			data.setChain_store_code(shop_unique);
			data.setPosition_source(2);
			data.setReceiver_longitude(receiver_longitude);
			data.setReceiver_latitude(receiver_latitude);

			DeliveryShopRequest deliveryShopRequest = new DeliveryShopRequest();
			deliveryShopRequest.setData(data);

	        int salt = RandomUtils.getInstance().generateValue(1000, 10000);

	        //获取token
	        Map<String ,Object> tokenMap = eleDeliveryDao.getAccessToken();
	        
	        Map<String, Object> sigStr = new LinkedHashMap<String,Object>();     // 注意添加的顺序, 应该如下面一样各个key值顺序一致
	        sigStr.put("app_id", ElemeOpenConfig.appId);
	        sigStr.put("access_token", MUtil.strObject(tokenMap.get("access_token")));        // 需要使用前面请求生成的token
	        sigStr.put("data", deliveryShopRequest.getData());
	        sigStr.put("salt", salt);

	        // 生成签名
	        String sig = OpenSignHelper.generateBusinessSign(sigStr);
	        deliveryShopRequest.setSignature(sig);

	        deliveryShopRequest.setApp_id(ElemeOpenConfig.appId);
	        deliveryShopRequest.setSalt(salt);

	        String requestJson = JsonUtils.getInstance().objectToJson(deliveryShopRequest);
	        String url = ElemeOpenConfig.API_URL + RequestConstant.deliveryShop;
	        String resultJson = HttpClient.postBody(url, requestJson);
	        Map<String ,Object> resultMap = MUtil.jsonToMap(resultJson);
	        String code = MUtil.strObject(resultMap.get("code"));
	        if(code != null && code.equals("200")){
	        	shopsResult.setStatus(1);
		        shopsResult.setMsg("成功");
	        }else{
	        	shopsResult.setStatus(2);
		        shopsResult.setMsg(MUtil.strObject(resultMap.get("msg")));
	        }
		} catch (Exception e) {
			shopsResult.setStatus(0);
			shopsResult.setMsg("蜂鸟配送平台更新门店异常："+e.getMessage());
		}
		return shopsResult;
	}
	
	@Override
	public ShopsResult carrierOrder(String sale_list_unique) {
		ShopsResult shopsResult = new ShopsResult();
		try {
			CarrierOrderRequest.CarrierOrderRequestData data = new CarrierOrderRequest.CarrierOrderRequestData();
			data.setPartner_order_code(sale_list_unique);

			CarrierOrderRequest carrierOrderRequest = new CarrierOrderRequest();
			carrierOrderRequest.setData(data);

	        int salt = RandomUtils.getInstance().generateValue(1000, 10000);

	        //获取token
	        Map<String ,Object> tokenMap = eleDeliveryDao.getAccessToken();
	        
	        Map<String, Object> sigStr = new LinkedHashMap<String,Object>();      // 注意添加的顺序, 应该如下面一样各个key值顺序一致
	        sigStr.put("app_id", ElemeOpenConfig.appId);
	        sigStr.put("access_token", MUtil.strObject(tokenMap.get("access_token")));        // 需要使用前面请求生成的token
	        sigStr.put("data", carrierOrderRequest.getData());
	        sigStr.put("salt", salt);

	        // 生成签名
	        String sig = OpenSignHelper.generateBusinessSign(sigStr);
	        carrierOrderRequest.setSignature(sig);

	        carrierOrderRequest.setApp_id(ElemeOpenConfig.appId);
	        carrierOrderRequest.setSalt(salt);

	        String requestJson = JsonUtils.getInstance().objectToJson(carrierOrderRequest);
	        String url = ElemeOpenConfig.API_URL + RequestConstant.carrierOrder;
	        String resultJson = HttpClient.postBody(url, requestJson);
	        Map<String ,Object> resultMap = MUtil.jsonToMap(resultJson);
	        String code = MUtil.strObject(resultMap.get("code"));
	        if(code != null && code.equals("200")){
	        	String resultData = MUtil.strObject(resultMap.get("data"));
	        	shopsResult.setData(MUtil.jsonToMap(resultData));
	        	shopsResult.setStatus(1);
		        shopsResult.setMsg("成功");
	        }else{
	        	shopsResult.setStatus(2);
		        shopsResult.setMsg(MUtil.strObject(resultMap.get("msg")));
	        }
		} catch (Exception e) {
			shopsResult.setStatus(0);
			shopsResult.setMsg("蜂鸟配送平台更新门店异常："+e.getMessage());
		}
		return shopsResult;
	}
}
