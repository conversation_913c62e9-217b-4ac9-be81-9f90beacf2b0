package org.haier.eledelivery.config;

/**
 * 请求常量
 */
public class RequestConstant {
    /**
     * 获取token
     */
    public static final String obtainToken = "/get_access_token";

    /**
     * 创建订单
     */
    public static final String orderCreate = "/v2/order";

    /**
     * 取消 订单
     */
    public static final String orderCancel = "/v2/order/cancel";

    /**
     * 订单查询
     */
    public static final String orderQuery = "/v2/order/query";
    
    /**
     * 订单投诉
     */
    public static final String orderComplaint = "/v2/order/complaint";
    
    /**
     * 添加门店
     */
    public static final String  addShop= "/v2/chain_store";
    
    /**
     * 查询门店
     */
    public static final String  queryShop= "/v2/chain_store/query";
    
    /**
     * 更新门店
     */
    public static final String  updateShop= "/v2/chain_store/update";
    
    /**
     * 查询门店配送服务
     */
    public static final String  deliveryShop= "/v2/chain_store/delivery/query";
    
    /**
     * 查询骑手位置
     */
    public static final String  carrierOrder= "/v2/order/carrier";
}
