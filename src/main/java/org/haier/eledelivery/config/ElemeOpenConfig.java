package org.haier.eledelivery.config;

/**
 * 基础配置类
 */
public class ElemeOpenConfig {

	/**
	 * 饿了么开放平台api<br>
	 * 联调环境地址 https://exam-anubis.ele.me/anubis-webapi <br>
	 * 线上环境地址 https://open-anubis.ele.me/anubis-webapi
	 */
	public static final String API_URL = "https://exam-anubis.ele.me/anubis-webapi";

	/**
	 * 第三方平台 app_id
	 */
	public static final String appId = "63713439-c1ac-4c37-938e-93035f4eee23";

	/**
	 * 第三方平台 secret_key
	 */
	public static final String secretKey = "65f38c7f-659b-454d-9725-9852a33b78fb";
	
	/**
	 * 配送单状态变化回调地址
	 */
	public static final String CALLBACK_URL = "https://24e94252.ngrok.io/shop/eleDelivery/callback/deliveryStatus.do";
}
