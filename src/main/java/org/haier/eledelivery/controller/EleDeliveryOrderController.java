package org.haier.eledelivery.controller;

import java.util.logging.Logger;

import javax.annotation.Resource;

import org.haier.eledelivery.service.EleDeliveryService;
import org.haier.shop.util.ShopsResult;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

@Controller
@RequestMapping("/eleDelivery/order")
public class EleDeliveryOrderController {
	
	private static final Logger logger = Logger.getGlobal();
	
	@Resource
	private EleDeliveryService eleDeliveryService;
	
	/**
	 * 商户请求推单
	 * @param sale_list_unique 订单编号
	 * @param order_weight 订单总重量（kg）
	 * @return
	 */
	@RequestMapping("/createOrder.do")
	@ResponseBody
	public ShopsResult orderCreate(String sale_list_unique,String order_weight){
		logger.info("蜂鸟配送平台，商户请求推单");
		
		ShopsResult shopsResult = eleDeliveryService.createOrder(sale_list_unique,order_weight);
		
		return shopsResult;
	}
	
	/**
	 * 取消配送单
	 * @param sale_list_unique 订单编号
	 * @param order_cancel_code 订单取消编码（0:其他, 1:联系不上商户, 2:商品已经售完, 3:用户申请取消, 4:运力告知不配送 让取消订单, 5:订单长时间未分配, 6:接单后骑手未取件）
	 * @param order_cancel_description  订单取消描述（order_cancel_code为0时必填）
	 * @return
	 */
	@RequestMapping("/cancelOrder.do")
	@ResponseBody
	public ShopsResult cancelOrder(String sale_list_unique,int order_cancel_code,String order_cancel_description){
		logger.info("蜂鸟配送平台，取消配送单");
		
		ShopsResult shopsResult = eleDeliveryService.cancelOrder(sale_list_unique,order_cancel_code,order_cancel_description);
		
		return shopsResult;
	}
	
	
	/**
	 * 查询配送单
	 * @param sale_list_unique 订单编号
	 * @return
	 */
	@RequestMapping("/queryOrder.do")
	@ResponseBody
	public ShopsResult queryOrder(String sale_list_unique){
		logger.info("蜂鸟配送平台，查询配送单");
		
		ShopsResult shopsResult = eleDeliveryService.queryOrder(sale_list_unique);
		
		return shopsResult;
	}
	
	/**
	 * 投诉配送单
	 * @param sale_list_unique 订单编号
	 * @param order_complaint_code 订单投诉编码（230:其他, 150:未保持餐品完整, 160:服务态度恶劣, 190:额外索取费用, 170:诱导收货人或商户退单, 140:提前点击送达, 210:虚假标记异常, 220:少餐错餐, 200:虚假配送, 130:未进行配送）
	 * @param order_complaint_desc 订单投诉描述（order_complaint_code为230时必填）
	 * @return
	 */
	@RequestMapping("/complaintOrder.do")
	@ResponseBody
	public ShopsResult complaintOrder(String sale_list_unique,String order_complaint_code,String order_complaint_desc){
		logger.info("蜂鸟配送平台，投诉配送单");
		
		ShopsResult shopsResult = eleDeliveryService.complaintOrder(sale_list_unique,order_complaint_code,order_complaint_desc);
		
		return shopsResult;
	}
	
	/**
	 * 查询骑手位置
	 * @param sale_list_unique 订单编号
	 * @return
	 */
	@RequestMapping("/carrierOrder.do")
	@ResponseBody
	public ShopsResult carrierOrder(String sale_list_unique){
		logger.info("蜂鸟配送平台，查询骑手位置");
		
		ShopsResult shopsResult = eleDeliveryService.carrierOrder(sale_list_unique);
		
		return shopsResult;
	}
}
