package org.haier.eledelivery.controller;

import java.util.logging.Logger;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;

import org.haier.eledelivery.service.EleDeliveryService;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;

@Controller
@RequestMapping("/eleDelivery/callback")
public class EleDeliveryCallBackController {
	
	private static final Logger logger = Logger.getGlobal();
	
	@Resource
	private EleDeliveryService eleDeliveryService;
	
	/**
	 * 订单状态回调
	 * @param sale_list_unique 订单编号
	 * @return
	 */
	@RequestMapping("/deliveryStatus.do")
	public void deliveryStatus(HttpServletRequest request){
		logger.info("蜂鸟配送平台，订单状态回调");
		String data = request.getParameter("data");
		String salt = request.getParameter("salt");
		String signature = request.getParameter("signature");
		
		eleDeliveryService.deliveryStatus(data, salt, signature);
	}
	
}
