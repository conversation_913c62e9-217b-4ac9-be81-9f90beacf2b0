package org.haier.eledelivery.controller;

import java.util.logging.Logger;

import javax.annotation.Resource;

import org.haier.eledelivery.service.EleDeliveryService;
import org.haier.shop.util.ShopsResult;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

@Controller
@RequestMapping("/eleDelivery/shop")
public class EleDeliveryShopController {
	
	private static final Logger logger = Logger.getGlobal();
	
	@Resource
	private EleDeliveryService eleDeliveryService;
	
	/**
	 * 添加门店信息
	 * @param shop_unique 店铺编号
	 * @param shop_name 店铺名称
	 * @param shop_phone 店铺联系方式
	 * @param shop_address_detail 店铺详细地址
	 * @param shop_longitude 店铺经度
	 * @param shop_latitude 店铺纬度
	 * @param service_code 配送服务(1:蜂鸟配送, 2:蜂鸟优送, 3:蜂鸟快送)
	 * @return
	 */
	@RequestMapping("/addShop.do")
	@ResponseBody
	public ShopsResult addShop(String shop_unique,String shop_name,String shop_phone,String shop_address_detail,String shop_longitude,String shop_latitude,String service_code){
		logger.info("蜂鸟配送平台，添加门店信息");
		
		ShopsResult shopsResult = eleDeliveryService.addShop(shop_unique, shop_name, shop_phone, shop_address_detail, shop_longitude, shop_latitude, service_code);
		
		return shopsResult;
	}
	
	/**
	 * 查询门店信息
	 * @param shop_unique 店铺编号
	 * @return
	 */
	@RequestMapping("/queryShop.do")
	@ResponseBody
	public ShopsResult queryShop(String shop_unique){
		logger.info("蜂鸟配送平台，查询门店信息");
		
		ShopsResult shopsResult = eleDeliveryService.queryShop(shop_unique);
		
		return shopsResult;
	}
	
	/**
	 * 更新门店信息
	 * @param shop_unique 店铺编号
	 * @param shop_name 店铺名称
	 * @param shop_phone 店铺联系方式
	 * @param shop_address_detail 店铺详细地址
	 * @param shop_longitude 店铺经度
	 * @param shop_latitude 店铺纬度
	 * @param service_code 配送服务(1:蜂鸟配送, 2:蜂鸟优送, 3:蜂鸟快送)
	 * @return
	 */
	@RequestMapping("/updateShop.do")
	@ResponseBody
	public ShopsResult updateShop(String shop_unique,String shop_name,String shop_phone,String shop_address_detail,String shop_longitude,String shop_latitude,String service_code){
		logger.info("蜂鸟配送平台，更新门店信息");
		
		ShopsResult shopsResult = eleDeliveryService.updateShop(shop_unique, shop_name, shop_phone, shop_address_detail, shop_longitude, shop_latitude, service_code);
		
		return shopsResult;
	}
	
	/**
	 * 查询门店配送服务
	 * @param shop_unique 店铺编号
	 * @param receiver_longitude 收货点经度
	 * @param receiver_latitude  收货点纬度
	 * @return
	 */
	@RequestMapping("/deliveryShop.do")
	@ResponseBody
	public ShopsResult deliveryShop(String shop_unique,String receiver_longitude,String receiver_latitude){
		logger.info("蜂鸟配送平台，查询门店信息");
		
		ShopsResult shopsResult = eleDeliveryService.deliveryShop(shop_unique,receiver_longitude,receiver_latitude);
		
		return shopsResult;
	}
}
