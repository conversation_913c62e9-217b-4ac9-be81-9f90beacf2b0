package org.haier.eledelivery.dao;

import java.util.List;
import java.util.Map;

/**
 * 描述：蜂鸟配送平台Dao
 * <AUTHOR>
 * @version v1.0
 *
 */
public interface EleDeliveryDao {
	
	/**
	 * 获取蜂鸟配送平台access_token
	 */
	public Map<String ,Object> getAccessToken();
	
	/**
	 * 添加蜂鸟配送平台access_token
	 */
	public void insertAccessToken(Map<String ,Object> params);
	
	/**
	 * 修改蜂鸟配送平台access_token
	 */
	public void updateAccessToken(Map<String ,Object> params);
	
	/**
	 * 获取订单信息
	 */
	public Map<String ,Object> getOrderInfo(String sale_list_unique);
	
	/**
	 * 获取订单详情信息列表
	 */
	public List<Map<String ,Object>> getOrderDetailList(String sale_list_unique);
	
	/**
	 * 创建配送单
	 */
	public void addDeliveryOrder(Map<String ,Object> params);
	
	/**
	 * 修改配送单
	 */
	public void updateDeliveryOrder(Map<String ,Object> params);
	
	/**
	 * 修改订单发货状态
	 */
	public void updateOrderStatus(Map<String ,Object> params);
	
	/**
	 * 修改店铺是否加入蜂鸟配送：0否 1是
	 */
	public void updateShopIsHummer(Map<String ,Object> params);
}
