package org.haier.meituan.dao;

import java.util.List;
import java.util.Map;

public interface ShopMappingDao {
	/**
	 * 获取店铺信息
	 * @param shop_unique 店铺编号
	 * @return
	 */
	public Map<String ,Object> getShopInfo(String shop_unique);
	
	/**
	 * 获取收银系统未同步美团外卖的商品列表
	 * @param shop_unique 店铺唯一标示
	 * @return 
	 */	
	public List<Map<String ,Object>> getGoodsList(String shop_unique);
	
	/**
	 * 获取商品信息
	 * @param goods_barcode 商品唯一标示
	 * @return 
	 */	
	public Map<String ,Object> getGoodsInfo(String goods_barcode);
	
	/**
	 * 修改商品信息
	 * @param goods_barcode 商品唯一标示
	 * @param synchronous_platform 同步外卖平台：0未同步，1美团，2饿了么，3美团、饿了么同时同步
	 * @return 
	 */	
	public int updateGoodsInfo(Map<String ,Object> params);
}
