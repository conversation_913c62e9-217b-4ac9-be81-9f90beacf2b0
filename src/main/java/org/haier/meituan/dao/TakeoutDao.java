package org.haier.meituan.dao;

import java.util.Map;

/**
 * 类名：com.palmshop.meituan.dao.TakeoutDao;
 * 描述：美团外卖对接dao
 * <AUTHOR>
 * @version v1.0
 *
 */
public interface TakeoutDao {

	/**
	 * 修改店铺美团外卖绑定授权token
	 * @param takeoutAppAuthToken  授权token
	 * @param shopUnique 店铺唯一标识
	 * @return int
	 */
	public int updateShopsTakeoutAppAuthToken(Map<String ,Object> params);
	
	/**
	 * 修改店铺营业状态
	 * @param shopOperatingStatus  营业状态：0，营业 1，休息 2，上线 3，下线
	 * @param shopUnique 店铺唯一标识
	 * @param takeoutAppAuthToken 授权token
	 * @return int
	 */
	public int updateShopOperatingStatus(Map<String ,Object> params);
}
