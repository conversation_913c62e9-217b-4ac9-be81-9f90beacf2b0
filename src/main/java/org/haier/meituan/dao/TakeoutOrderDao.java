package org.haier.meituan.dao;

import java.util.List;
import java.util.Map;

import org.apache.ibatis.annotations.Param;
import org.haier.ele.entity.SaleList;
import org.haier.ele.entity.SaleListDetail;

/**
 * 类名：com.palmshop.meituan.dao.TakeoutOrderDao;
 * 描述：外卖订单dao
 * <AUTHOR>
 * @version v1.0
 *
 */
public interface TakeoutOrderDao {
	
	/**
	 * 添加一条销售信息
	 * @param saleList:订单实体类
	 * @return int
	 */
	public int addSaleList(SaleList saleList);
	
	/**
	 * 添加一条销售单明细信息
	 * @param saleDetail：订单明细实体类
	 * @return int
	 */
	public int addSaleDetail(@Param("saleDetailList")List<SaleListDetail> saleDetailList);
	
	/**
	 * 获取商品信息
	 * @param 
	 * @return 
	 */
	public Map<String ,Object> getGoodsInfo(String goodsBarcode);
	
	/**
	 * 获取商家未接单外卖订单列表
	 * @param shopUnique 商店唯一标示
	 * @return 
	 */
	public List<Map<String ,Object>> getSaleInfoList(Map<String ,Object> params); 
	
	/**
	 * 修改销售发货状态信息
	 * @param saleList:订单实体类
	 * @return int
	 */
	public int updatSaleList(SaleList saleList);
	
	/**
	 * 修改销售付款状态信息
	 * @param saleList:订单实体类
	 * @return int
	 */
	public int updatSaleListState(SaleList saleList);
}
