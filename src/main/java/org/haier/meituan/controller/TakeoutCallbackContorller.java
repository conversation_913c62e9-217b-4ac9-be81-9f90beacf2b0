package org.haier.meituan.controller;

import java.util.HashMap;
import java.util.Map;
import java.util.logging.Logger;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;

import org.haier.ele.entity.SaleList;
import org.haier.meituan.service.TakeoutOrderService;
import org.haier.meituan.service.TakeoutService;
import org.haier.meituan.util.MUtil;
import org.haier.shop.util.ShopsResult;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;


/**
 * 类名：com.palmshop.meituan.controller.TakeoutCallbackContorller;
 * 描述：美团外卖回调类
 * 接收美团外卖回调数据
 * <AUTHOR>
 * @version v1.0
 *
 */
@Controller
@RequestMapping("/callback")
public class TakeoutCallbackContorller {
	
	private static final Logger logger = Logger.getGlobal();
	
	@Resource
	private TakeoutService takeoutService;
	
	@Resource
	private TakeoutOrderService takeoutOrderService;

	
	/**
	 * 门店映射绑定回调接口
	 * @param appAuthToken 门店绑定的授权token，将来的门店业务操作必须要传
	 * @param businessId 1团购、2外卖、3闪惠、5支付、7预定、8全渠道会员
	 * @param ePoiId 门店绑定时，传入的ERP厂商分配给门店的唯一标识
	 * @param timestamp 门店绑定的时间戳
	 * @param poiId 美团门店id
	 * @param poiName 美团门店名称
	 * @return json
	 */	
	@RequestMapping("/shopBinding.do")
	@ResponseBody
	public Map<String ,Object> shopBinding(HttpServletRequest request) {
		Map<String ,Object> resultJsonString = new HashMap<String, Object>();
		logger.info("美团外卖回调类>>>>>接收门店映射绑定回调");
		String appAuthToken = request.getParameter("appAuthToken");
		String ePoiId = request.getParameter("ePoiId");
		Map<String ,Object> params = new HashMap<String, Object>();
		params.put("takeout_appAuthToken", appAuthToken);
		params.put("shop_unique", ePoiId);
		//保存门店绑定的授权token
		ShopsResult ShopsResult = takeoutService.updateShopsTakeoutAppAuthToken(params);
		if(ShopsResult.getStatus() == 1){
			logger.info("美团外卖回调类>>>>>接收门店映射绑定回调成功");
			resultJsonString.put("data", "success");
		}
		return resultJsonString;
	}
	
	/**
	 * 门店映射解绑回调接口
	 * @param developerId 开发者i
	 * @param ePoiId 门店绑定时，传入的ERP厂商分配给门店的唯一标识
	 * @param businessId 1团购、2外卖、3闪惠、5支付、7预定、8全渠道会员
	 * @param timestamp 门店解绑的时间戳
	 * @return json
	 */	
	@RequestMapping("/untie.do")
	@ResponseBody
	public Map<String ,Object> untie(HttpServletRequest request) {
		Map<String ,Object> resultJsonString = new HashMap<String, Object>();
		logger.info("美团外卖回调类>>>>>接收门店映射解绑回调");
		String ePoiId = request.getParameter("ePoiId");
		Map<String ,Object> params = new HashMap<String, Object>();
		params.put("takeout_appAuthToken", "");
		params.put("shop_unique", ePoiId);
		//修改门店绑定的授权token为空
		ShopsResult ShopsResult = takeoutService.updateShopsTakeoutAppAuthToken(params);
		if(ShopsResult.getStatus() == 1){
			logger.info("美团外卖回调类>>>>>接收门店映射解绑回调成功");
			resultJsonString.put("data", "success");
		}
		return resultJsonString;
	}
	
	/**
	 * 接收订单数据接口
	 * @param developerId：ERP厂商入驻新美大餐饮平台得到的唯一身份表示
	 * @param ePoiId：ERP方门店id 最大长度100
	 * @param sign：数字签名
	 * @param order：json格式串，订单详情数据
	 * @return json
	 */	
	@RequestMapping("/receiveOrderInfo.do")
	@ResponseBody
	public Map<String ,Object> receiveOrderInfo(HttpServletRequest request) {
		Map<String ,Object> resultJsonString = new HashMap<String, Object>();
		logger.info("美团外卖回调类>>>>>接收订单数据");
		String ePoiId = request.getParameter("ePoiId");
		String orderJson = request.getParameter("order");
		if(ePoiId != null && !ePoiId.equals("") && orderJson != null && !orderJson.equals("")){
			Map<String ,Object> orderMap = MUtil.jsonToMap(orderJson);
			//添加外卖订单
			ShopsResult ShopsResult = takeoutOrderService.addSaleList(Long.valueOf(ePoiId), orderMap);
			if(ShopsResult.getStatus() == 1){
				logger.info("美团外卖回调类>>>>>接收订单数据成功");
				resultJsonString.put("data", "OK");
			}
		}
		return resultJsonString;
	}
	
	/**
	 * 接收订单已确认消息接口【指商家已确认】
	 * @param developerId：ERP厂商入驻新美大餐饮平台得到的唯一身份表示
	 * @param ePoiId：ERP方门店id 最大长度100
	 * @param sign：数字签名
	 * @param order：json格式串，订单详情数据
	 * @return json
	 */	
	@RequestMapping("/confirmOrder.do")
	@ResponseBody
	public Map<String ,Object> confirmOrder(HttpServletRequest request) {
		Map<String ,Object> resultJsonString = new HashMap<String, Object>();
		logger.info("美团外卖回调类>>>>>接收订单已确认消息接口【指商家已确认】");
		String ePoiId = request.getParameter("ePoiId");
		String orderJson = request.getParameter("order");
		if(ePoiId != null && !ePoiId.equals("") && orderJson != null && !orderJson.equals("")){
			Map<String ,Object> orderMap = MUtil.jsonToMap(orderJson);
			//修改订单发货状态为待发货
			ShopsResult ShopsResult = takeoutOrderService.updateSaleList(orderMap,2);
			if(ShopsResult.getStatus() == 1){
				logger.info("美团外卖回调类>>>>>接收订单已确认消息接口【指商家已确认】成功");
				resultJsonString.put("data", "OK");
			}
		}
		return resultJsonString;
	}
	
	/**
	 * 接收订单取消消息接口
	 * @param developerId：ERP厂商入驻新美大餐饮平台得到的唯一身份表示
	 * @param ePoiId：ERP方门店id 最大长度100
	 * @param sign：数字签名
	 * @param orderCancel：json格式串，订单详情数据
	 * @return json
	 */	
	@RequestMapping("/cancleOrderInfo.do")
	@ResponseBody
	public Map<String ,Object> cancleOrderInfo(HttpServletRequest request) {
		Map<String ,Object> resultJsonString = new HashMap<String, Object>();
		logger.info("美团外卖回调类>>>>>接收订单取消消息接口");
		String ePoiId = request.getParameter("ePoiId");
		String orderCancelJson = request.getParameter("orderCancel");
		if(ePoiId != null && !ePoiId.equals("") && orderCancelJson != null && !orderCancelJson.equals("")){
			Map<String ,Object> orderCancelMap = MUtil.jsonToMap(orderCancelJson);
			//修改订单发货状态为已取消
			ShopsResult ShopsResult = takeoutOrderService.updateSaleList(orderCancelMap,5);
			if(ShopsResult.getStatus() == 1){
				logger.info("美团外卖回调类>>>>>接收订单取消消息接口成功");
				resultJsonString.put("data", "OK");
			}
		}
		return resultJsonString;
	}
	
	
	/**
	 * 接收订单完成消息接口
	 * @param developerId：ERP厂商入驻新美大餐饮平台得到的唯一身份表示
	 * @param ePoiId：ERP方门店id 最大长度100
	 * @param sign：数字签名
	 * @param order：json格式串，订单详情数据
	 * @return json
	 */	
	@RequestMapping("/completeOrderInfo.do")
	@ResponseBody
	public Map<String ,Object> completeOrderInfo(HttpServletRequest request) {
		Map<String ,Object> resultJsonString = new HashMap<String, Object>();
		logger.info("美团外卖回调类>>>>>接收订单完成消息接口");
		String ePoiId = request.getParameter("ePoiId");
		String order = request.getParameter("order");
		if(ePoiId != null && !ePoiId.equals("") && order != null && !order.equals("")){
			Map<String ,Object> orderMap = MUtil.jsonToMap(order);
			//修改订单发货状态为已取消
			ShopsResult ShopsResult = takeoutOrderService.updateSaleList(orderMap,4);
			if(ShopsResult.getStatus() == 1){
				logger.info("美团外卖回调类>>>>>接收订单完成消息接口成功");
				resultJsonString.put("data", "OK");
			}
		}
		return resultJsonString;
	}
	
	/**
	 * 接收订单配送状态接口
	 * @param developerId：ERP厂商入驻新美大餐饮平台得到的唯一身份表示
	 * @param ePoiId：ERP方门店id 最大长度100
	 * @param sign：数字签名
	 * @param shippingStatus ：订单配送信息
	 * @return json
	 */	
	@RequestMapping("/statusOrderInfo.do")
	@ResponseBody
	public Map<String ,Object> statusOrderInfo(HttpServletRequest request) {
		Map<String ,Object> resultJsonString = new HashMap<String, Object>();
		logger.info("美团外卖回调类>>>>>接收订单配送状态接口");
		String ePoiId = request.getParameter("ePoiId");
		String shippingStatusJson = request.getParameter("shippingStatus");
		if(ePoiId != null && !ePoiId.equals("") && shippingStatusJson != null && !shippingStatusJson.equals("")){
			Map<String ,Object> orderMap = MUtil.jsonToMap(shippingStatusJson);
			//0-配送单发往配送;10-配送单已确认;20-骑手已取餐;40-骑手已送达;100-配送单已取消
			int shippingStatus = (int) orderMap.get("shippingStatus");
			int saleListHandlestate = 2;//0-已删除-1无效订单-2待发货-3待收货-4已完成-5已取消-6待评论-7配送单待确认
			if(shippingStatus == 0){
				saleListHandlestate = 7;
			}else if(shippingStatus == 10){
				saleListHandlestate = 3;
			}else if(shippingStatus == 20){
				saleListHandlestate = 3;
			}else if(shippingStatus == 40){
				saleListHandlestate = 4;
			}else if(shippingStatus == 100){
				saleListHandlestate = 2;
			}
			//修改订单发货状态
			ShopsResult ShopsResult = takeoutOrderService.updateSaleList(orderMap,saleListHandlestate);
			if(ShopsResult.getStatus() == 1){
				logger.info("美团外卖回调类>>>>>接收订单配送状态接口成功");
				resultJsonString.put("data", "OK");
			}
		}
		return resultJsonString;
	}
	
	/**
	 * 接收门店状态变更接口
	 * @param developerId：ERP厂商入驻新美大餐饮平台得到的唯一身份表示
	 * @param sign：数字签名
	 * @param poiStatus ：门店状态信息
	 * @return json
	 */	
	@RequestMapping("/updateShopOperatingStatus.do")
	@ResponseBody
	public Map<String ,Object> updateShopOperatingStatus(HttpServletRequest request) {
		Map<String ,Object> resultJsonString = new HashMap<String, Object>();
		logger.info("美团外卖回调类>>>>>接收门店状态变更接口");
		String poiStatusJson = request.getParameter("poiStatus");
		if(poiStatusJson != null && !poiStatusJson.equals("")){
			Map<String ,Object> shopMap = MUtil.jsonToMap(poiStatusJson);
			String ePoiId = MUtil.strObject(shopMap.get("ePoiId"));
			int poiStatus = (int) shopMap.get("poiStatus");//121-营业；120-休息；18-上线；19-下线
			int shopOperatingStatus = 0;//营业状态：0，营业 1，休息 2，上线 3，下线
			if(poiStatus == 121){
				shopOperatingStatus = 0;
			}else if(poiStatus == 120){
				shopOperatingStatus = 1;
			}else if(poiStatus == 18){
				shopOperatingStatus = 2;
			}else if(poiStatus == 19){
				shopOperatingStatus = 3;
			}
			//修改店铺营业状态
			Map<String ,Object> params = new HashMap<String, Object>();
			params.put("shopUnique", ePoiId);
			params.put("shopOperatingStatus", shopOperatingStatus);
			ShopsResult ShopsResult = takeoutService.updateShopOperatingStatus(params);
			if(ShopsResult.getStatus() == 1){
				logger.info("美团外卖回调类>>>>>接收门店状态变更接口成功");
				resultJsonString.put("data", "OK");
			}
		}
		return resultJsonString;
	}
	
	/**
	 * 部分退款回调接口
	 * @param money 部分退款金额	
	 * @param notifyType 通知类型part：商家/用户发起部分退款 agree：商家同意部分退款 reject ：商家拒绝部分退款
	 * @param orderId 订单号
	 * @param reason 退款原因
	 * @return json
	 */	
	@RequestMapping("/refundOrder.do")
	@ResponseBody
	public Map<String ,Object> refundOrder(HttpServletRequest request) {
		Map<String ,Object> resultJsonString = new HashMap<String, Object>();
		logger.info("美团外卖回调类>>>>>部分退款回调接口");
		String money = request.getParameter("money");//部分退款金额	
		String notifyType = request.getParameter("notifyType");//通知类型part：商家/用户发起部分退款 agree：商家同意部分退款 reject ：商家拒绝部分退款
		String orderId = request.getParameter("orderId");//	订单号
		String reason = request.getParameter("reason");//退款原因
		if(money != null && !money.equals("") && notifyType != null && !notifyType.equals("") && orderId != null && !orderId.equals("")){
			//修改订单付款状态
			SaleList saleList = new SaleList();
			saleList.setSaleListUnique(Long.valueOf(orderId));
			saleList.setRefundMoney(Double.valueOf(money));
			if(notifyType.equals("part")){
				saleList.setSaleListState(5);//申请退款
				saleList.setRefundReason(reason);
			}else if(notifyType.equals("agree")){
				saleList.setSaleListState(6);//同意退款
				saleList.setRefuntOperateReason(reason);
			}else if(notifyType.equals("reject")){
				saleList.setSaleListState(7);//拒绝退款
				saleList.setRefuntOperateReason(reason);
			}
			ShopsResult ShopsResult = takeoutOrderService.updatSaleListState(saleList);
			if(ShopsResult.getStatus() == 1){
				logger.info("美团外卖回调类>>>>>部分退款回调接口成功");
				resultJsonString.put("data", "OK");
			}
		}
		return resultJsonString;
	}
}
