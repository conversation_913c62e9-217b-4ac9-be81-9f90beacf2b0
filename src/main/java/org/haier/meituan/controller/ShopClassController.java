package org.haier.meituan.controller;

import java.util.logging.Logger;

import javax.annotation.Resource;

import org.haier.meituan.service.ShopClassService;
import org.haier.shop.util.ShopsResult;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

@Controller
@RequestMapping("/shopClass")
public class ShopClassController {
	
	private static final Logger logger = Logger.getGlobal();
	
	@Resource
	private ShopClassService shopClassService;
	
	/**
	 * 外卖商品分类列表页面
	 * @param shop_unique 店铺编号
	 * @return
	 */
	@RequestMapping("/shopClassList.do")
	public String shopClassList(String shop_unique,Model model){
		logger.info("外卖商品分类列表页面");
		ShopsResult shopsResult = shopClassService.getShopClassList(shop_unique);
		model.addAttribute("shopsResult", shopsResult);
		model.addAttribute("shop_unique", shop_unique);
		return "/html/meituan/shopClassList.jsp";
	}
	
	/**
	 * 外卖商品分类新增页面
	 * @param appAuthToken
	 * @return
	 */
	@RequestMapping("/addShopClassPage.do")
	public String addShopClassPage(String appAuthToken,Model model){
		logger.info("外卖商品分类新增页面");
		model.addAttribute("appAuthToken", appAuthToken);
		return "/html/meituan/shopClassForm.jsp";
	}
	
	/**
	 * 外卖商品分类修改页面
	 * @param appAuthToken 
	 * @param catName
	 * @param sequence
	 * @return
	 */
	@RequestMapping("/updateShopClassPage.do")
	public String updateShopClassPage(String appAuthToken,String catName, int sequence,Model model){
		logger.info("外卖商品分类修改页面");
		model.addAttribute("appAuthToken", appAuthToken);
		model.addAttribute("catName", catName);
		model.addAttribute("sequence", sequence);
		return "/html/meituan/shopClassForm.jsp";
	}
	
	/**
	 * 外卖商品分类新增/修改
	 * @param appAuthToken 美团外卖门店映射绑定授权token
	 * @param oldCatName 原始分类名称，更新是必须
	 * @param catName 分类名称
	 * @param sequence 排序，创建是必须
	 * @return 
	 */	
	@RequestMapping("/saveShopClass.do")
	@ResponseBody
	public ShopsResult saveShopClass(String appAuthToken,String oldCatName,String catName, String sequence) {
		logger.info("外卖商品分类新增/修改");
		
		ShopsResult shopsResult = shopClassService.saveShopClass(appAuthToken,oldCatName, catName, sequence);
		
		return shopsResult;
	}
	
	/**
	 * 外卖商品分类删除
	 * @param appAuthToken 美团外卖门店映射绑定授权token
	 * @param catName 分类名称
	 * @return 
	 */	
	@RequestMapping("/deleteShopClass.do")
	@ResponseBody
	public ShopsResult deleteShopClass(String appAuthToken,String catName) {
		logger.info("外卖商品分类删除");
		
		ShopsResult shopsResult = shopClassService.deleteShopClass(appAuthToken,catName);
		
		return shopsResult;
	}
}
