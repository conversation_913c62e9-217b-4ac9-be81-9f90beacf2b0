package org.haier.meituan.controller;

import java.util.logging.Logger;

import javax.annotation.Resource;

import org.haier.meituan.service.TakeoutOrderService;
import org.haier.shop.util.ShopsResult;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

/**
 * 类名：com.palmshop.meituan.controller.TakeoutOrderContorller;
 * 描述：外卖订单
 * 查询商家未接单外卖订单
 * <AUTHOR>
 * @version v1.0
 *
 */
@Controller
@RequestMapping("/takeoutOrder")
public class TakeoutOrderContorller {
	
	private static final Logger logger = Logger.getGlobal();
	
	@Resource
	private TakeoutOrderService takeoutOrderService;

	/**
	 * 获取商家未接单外卖订单，10s请求一次
	 * @param shopUnique 商家唯一标示
	 * @return 
	 */	
	@RequestMapping("/getShopNotReceiptOrder.do")
	@ResponseBody
	public ShopsResult getShopNotReceiptOrder(String shopUnique) {
		logger.info("获取商家未接单外卖订单，10s请求一次，shopUnique："+shopUnique);
		
		ShopsResult ShopsResult = takeoutOrderService.getSaleInfoList(shopUnique);
		
		return ShopsResult;
	}
	
	/**
	 * 商家确认订单
	 * @param saleListUnique 订单编号
	 * @param takeoutAppAuthToken 美团外卖门店映射绑定授权token
	 * @return 
	 */	
	@RequestMapping("/confirmOrder.do")
	@ResponseBody
	public ShopsResult confirmOrder(String saleListUnique,String takeoutAppAuthToken) {
		logger.info("商家确认订单，saleListUnique："+saleListUnique);

		ShopsResult ShopsResult = takeoutOrderService.confirmOrder(saleListUnique, takeoutAppAuthToken);
		
		return ShopsResult;
	}
	
	
	/**
	 * 商家取消订单
	 * @param saleListUnique 订单编号
	 * @param takeoutAppAuthToken 美团外卖门店映射绑定授权token
	 * @param reasonCode 取消原因码 
	 * @param reason 取消原因
	 * @return 
	 */	
	@RequestMapping("/cancelOrder.do")
	@ResponseBody
	public ShopsResult cancelOrder(String saleListUnique,String takeoutAppAuthToken,String reasonCode,String reason) {
		logger.info("商家取消订单，saleListUnique："+saleListUnique+",reasonCode:"+reasonCode+",reason："+reason);

		ShopsResult ShopsResult = takeoutOrderService.cancelOrder(saleListUnique, takeoutAppAuthToken, reasonCode, reason);
		
		return ShopsResult;
	}
	
	/**
	 * 商家自配送--发起自配送
	 * @param saleListUnique 订单编号
	 * @param takeoutAppAuthToken 美团外卖门店映射绑定授权token
	 * @param courierName 配送人名称
	 * @param courierPhone 配送人电话
	 * @return 
	 */	
	@RequestMapping("/delivering.do")
	@ResponseBody
	public ShopsResult delivering(String saleListUnique,String takeoutAppAuthToken,String courierName,String courierPhone) {
		logger.info("商家发起自配送，saleListUnique："+saleListUnique+",courierName: "+courierName+",courierPhone: "+courierPhone );

		ShopsResult ShopsResult = takeoutOrderService.delivering(saleListUnique, takeoutAppAuthToken, courierName, courierPhone);
		
		return ShopsResult;
	}
	
	/**
	 * 商家自配送--订单已送达
	 * @param saleListUnique 订单编号
	 * @param takeoutAppAuthToken 美团外卖门店映射绑定授权token
	 * @return 
	 */	
	@RequestMapping("/delivered.do")
	@ResponseBody
	public ShopsResult delivered(String saleListUnique,String takeoutAppAuthToken) {
		logger.info("商家自配送--订单已送达，saleListUnique："+saleListUnique);

		ShopsResult ShopsResult = takeoutOrderService.delivered(saleListUnique, takeoutAppAuthToken);
		
		return ShopsResult;
	}
	
	
	/**
	 * 众包配送--查询配送费
	 * @param saleListUnique 订单编号
	 * @param takeoutAppAuthToken 美团外卖门店映射绑定授权token
	 * @return data {
	 * 	 orderId 订单编号
         shippingFee 配送费
         shippingTip 配送费说明
	 *   }
	 */	
	@RequestMapping("/queryZbShippingFee.do")
	@ResponseBody
	public ShopsResult queryZbShippingFee(String saleListUnique,String takeoutAppAuthToken) {
		logger.info("众包配送--查询配送费，saleListUnique："+saleListUnique);

		ShopsResult ShopsResult = takeoutOrderService.queryZbShippingFee(saleListUnique, takeoutAppAuthToken);
		
		return ShopsResult;
	}
	
	/**
	 * 众包配送--预下单
	 * @param saleListUnique 订单编号
	 * @param takeoutAppAuthToken 美团外卖门店映射绑定授权token
	 * @param shippingFee 配送费，查询接口返回的配送费
	 * @param tipAmount  小费，不加小费输入0.0
	 * @return data 失败时返回
	 * 	{
	 * 	  code 1 价格有变动 2 异常 3 商家余额不足 5 已发配送
		  msg 说明
		  wm_order_view_id 订单展示id
		  new_shipping_fee 最新的配送费
		  count_start 倒计时开始时间戳
		  count_down 新运费保值时间段,在此时间段内保证运费不会再变更
	 *   }
	 */	
	@RequestMapping("/prepareZbDispatch.do")
	@ResponseBody
	public ShopsResult prepareZbDispatch(String saleListUnique,String takeoutAppAuthToken,Double shippingFee,Double tipAmount) {
		logger.info("众包配送--预下单，saleListUnique："+saleListUnique+",shippingFee: "+shippingFee+",tipAmount: "+tipAmount);

		ShopsResult ShopsResult = takeoutOrderService.prepareZbDispatch(saleListUnique, takeoutAppAuthToken,shippingFee,tipAmount);
		
		return ShopsResult;
	}
	
	/**
	 * 众包配送--确认下单
	 * @param saleListUnique 订单编号
	 * @param takeoutAppAuthToken 美团外卖门店映射绑定授权token
	 * @param tipAmount  小费，不加小费输入0.0
	 * @return 
	 */	
	@RequestMapping("/confirmZbDispatch.do")
	@ResponseBody
	public ShopsResult confirmZbDispatch(String saleListUnique,String takeoutAppAuthToken,Double tipAmount) {
		logger.info("众包配送--确认下单，saleListUnique："+saleListUnique+",tipAmount: "+tipAmount);

		ShopsResult ShopsResult = takeoutOrderService.confirmZbDispatch(saleListUnique, takeoutAppAuthToken,tipAmount);
		
		return ShopsResult;
	}
	
	/**
	 * 众包配送--配送单加小费
	 * @param saleListUnique 订单编号
	 * @param takeoutAppAuthToken 美团外卖门店映射绑定授权token
	 * @param tipAmount  小费
	 * @return 
	 */	
	@RequestMapping("/updateZbDispatchTip.do")
	@ResponseBody
	public ShopsResult updateZbDispatchTip(String saleListUnique,String takeoutAppAuthToken,Double tipAmount) {
		logger.info("众包配送--配送单加小费，saleListUnique："+saleListUnique+",tipAmount: "+tipAmount);

		ShopsResult ShopsResult = takeoutOrderService.updateZbDispatchTip(saleListUnique, takeoutAppAuthToken,tipAmount);
		
		return ShopsResult;
	}
	
	/**
	 * 取消美团众包配送和美团专送
	 * @param saleListUnique 订单编号
	 * @param takeoutAppAuthToken 美团外卖门店映射绑定授权token
	 * @return 
	 */	
	@RequestMapping("/cancelDispatch.do")
	@ResponseBody
	public ShopsResult cancelDispatch(String saleListUnique,String takeoutAppAuthToken) {
		logger.info("取消美团众包配送和美团专送，saleListUnique："+saleListUnique);

		ShopsResult ShopsResult = takeoutOrderService.cancelDispatch(saleListUnique, takeoutAppAuthToken);
		
		return ShopsResult;
	}
	
	/**
	 * 美团专送--发起配送
	 * @param saleListUnique 订单编号
	 * @param takeoutAppAuthToken 美团外卖门店映射绑定授权token
	 * @return 
	 */	
	@RequestMapping("/dispatchShip.do")
	@ResponseBody
	public ShopsResult dispatchShip(String saleListUnique,String takeoutAppAuthToken) {
		logger.info("美团专送--发起配送，saleListUnique："+saleListUnique);

		ShopsResult ShopsResult = takeoutOrderService.dispatchShip(saleListUnique, takeoutAppAuthToken);
		
		return ShopsResult;
	}
	
	/**
	 * 订单同意退款
	 * @param saleListUnique 订单编号
	 * @param takeoutAppAuthToken 美团外卖门店映射绑定授权token
	 * @param refundReason 原因
	 * @return 
	 */	
	@RequestMapping("/agreeRefund.do")
	@ResponseBody
	public ShopsResult agreeRefund(String saleListUnique,String takeoutAppAuthToken,String refundReason) {
		logger.info("订单同意退款，saleListUnique："+saleListUnique+",refundReason："+refundReason);

		ShopsResult ShopsResult = takeoutOrderService.agreeRefund(saleListUnique, takeoutAppAuthToken,refundReason);
		
		return ShopsResult;
	}
	
	/**
	 * 订单拒绝退款
	 * @param saleListUnique 订单编号
	 * @param takeoutAppAuthToken 美团外卖门店映射绑定授权token
	 * @param refundReason 原因
	 * @return 
	 */	
	@RequestMapping("/rejectRefund.do")
	@ResponseBody
	public ShopsResult rejectRefund(String saleListUnique,String takeoutAppAuthToken,String refundReason) {
		logger.info("订单拒绝退款，saleListUnique："+saleListUnique+",refundReason："+refundReason);

		ShopsResult ShopsResult = takeoutOrderService.rejectRefund(saleListUnique, takeoutAppAuthToken,refundReason);
		
		return ShopsResult;
	}
}
