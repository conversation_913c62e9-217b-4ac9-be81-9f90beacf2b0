package org.haier.meituan.controller;

import java.util.Map;
import java.util.logging.Logger;

import javax.annotation.Resource;

import org.haier.meituan.service.ShopMappingService;
import org.haier.shop.util.ShopsResult;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;


@Controller
@RequestMapping("/shopMapping")
public class ShopMappingController {
	
	private static final Logger logger = Logger.getGlobal();
	
	@Resource
	private ShopMappingService shopMappingService;
	
	/**
	 * 获取门店映射信息
	 * @param shop_unique 店铺编号
	 * @return
	 */
	@RequestMapping("/shopMapping.do")
	public String shopMapping(String shop_unique,Model model){
		logger.info("获取门店映射信息");
		Map<String ,Object> mappingInfo = shopMappingService.getShopMappingInfo(shop_unique);
		model.addAttribute("mappingInfo", mappingInfo);
		return "/html/meituan/shopMapping.jsp";
	}
	
	
	/**
	 * 门店设置信息
	 * @param shop_unique 店铺编号
	 * @return
	 */
	@RequestMapping("/shopSetting.do")
	public String shopSetting(String shop_unique,Model model){
		logger.info("获取门店设置信息");
		Map<String ,Object> settingInfo = shopMappingService.getShopSettingInfo(shop_unique);
		model.addAttribute("settingInfo", settingInfo);
		return "/html/meituan/shopSetting.jsp";
	}
	
	/**
	 * 设置门店营业状态
	 * @param appAuthToken 美团外卖门店映射绑定授权token
	 * @return 
	 */	
	@RequestMapping("/takeoutShopOpen.do")
	@ResponseBody
	public ShopsResult takeoutShopOpen(String appAuthToken) {
		logger.info("设置门店营业状态");
		
		ShopsResult shopsResult = shopMappingService.takeoutShopOpen(appAuthToken);
		
		return shopsResult;
	}
	
	/**
	 * 设置门店休息状态
	 * @param appAuthToken 美团外卖门店映射绑定授权token
	 * @return 
	 */	
	@RequestMapping("/takeoutShopClose.do")
	@ResponseBody
	public ShopsResult takeoutShopClose(String appAuthToken) {
		logger.info("设置门店休息状态");
		
		ShopsResult shopsResult = shopMappingService.takeoutShopClose(appAuthToken);
		
		return shopsResult;
	}
	
	/**
	 * 设置门店营业时间
	 * @param appAuthToken 美团外卖门店映射绑定授权token
	 * @param openTime 营业时间时间段06:00-08:00
	 * @return 
	 */	
	@RequestMapping("/updateOpenTime.do")
	@ResponseBody
	public ShopsResult updateOpenTime(String appAuthToken,String openTime) {
		logger.info("设置门店营业时间");
		
		ShopsResult shopsResult = shopMappingService.updateOpenTime(appAuthToken,openTime);
		
		return shopsResult;
	}
}
