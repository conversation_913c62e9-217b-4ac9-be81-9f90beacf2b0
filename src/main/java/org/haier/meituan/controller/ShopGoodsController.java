package org.haier.meituan.controller;

import java.util.List;
import java.util.Map;
import java.util.logging.Logger;

import javax.annotation.Resource;

import org.haier.meituan.service.ShopGoodsService;
import org.haier.shop.util.ShopsResult;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

@Controller
@RequestMapping("/shopGoods")
public class ShopGoodsController {
	private static final Logger logger = Logger.getGlobal();
	
	@Resource
	private ShopGoodsService shopGoodsService;
	
	/**
	 * 外卖商品列表页面
	 * @param shop_unique 店铺编号
	 * @return
	 */
	@RequestMapping("/shopGoodsList.do")
	public String shopGoodsList(String shop_unique,String page,Model model){
		logger.info("获取外卖商品列表");
		if(page == null){
			page = "1";//默认第一页
		}
		ShopsResult shopsResult = shopGoodsService.getShopGoodsList(shop_unique, page);
		model.addAttribute("shopsResult", shopsResult);
		model.addAttribute("shop_unique", shop_unique);
		return "/html/meituan/shopGoodsList.jsp";
	}
	
	/**
	 * 外卖商品新增页面
	 * @param appAuthToken
	 * @param shop_unique 店铺编号
	 * @return
	 */
	@RequestMapping("/addShopGoodsPage.do")
	public String addShopGoodsPage(String appAuthToken,String shop_unique,Model model){
		List<Map<String ,Object>> goodsList = shopGoodsService.getGoodsList(shop_unique);
		List<Map<String ,Object>> classList = shopGoodsService.getClassList(appAuthToken);
		model.addAttribute("goodsList", goodsList);
		model.addAttribute("classList", classList);
		model.addAttribute("appAuthToken", appAuthToken);
		model.addAttribute("shop_unique",shop_unique);
		return "/html/meituan/shopGoodsForm.jsp";
	}
	
	/**
	 * 外卖商品修改页面
	 * @param shop_unique 店铺编号
	 * @return
	 */
	@RequestMapping("/updateShopGoodsPage.do")
	public String updateShopGoodsPage(String shop_unique,Model model){
		List<Map<String ,Object>> goodsList = shopGoodsService.getGoodsList(shop_unique);
		model.addAttribute("goodsList", goodsList);
		return "/html/meituan/shopGoodsForm.jsp";
	}
	
	/**
	 * 外卖商品查看页面
	 * @param shop_unique 店铺编号
	 * @return
	 */
	@RequestMapping("/queryShopGoodsPage.do")
	public String queryShopGoodsPage(String shop_unique,Model model){
		
		return "/html/meituan/shopGoodsForm.jsp";
	}
	
	/**
	 * 外卖商品新增/修改
	 * @param appAuthToken 美团外卖门店映射绑定授权token
	 * @param ePoiId 门店唯一标示
	 * @param categoryName 商品分类
	 * @param description 商品描述
	 * @param dishName	商品名称
	 * @param EDishCode	商品唯一标示
	 * @param isSoldOut	是否售完	0-未售完，1-售完
	 * @param minOrderCount 最小购买数量	
	 * @param picture 图片id或地址
	 * @param sequence 分类下商品的顺序	
	 * @param price	商品价格
	 * @param unit 单位/规格
	 * @return 
	 */	
	@RequestMapping("/saveShopGoods.do")
	@ResponseBody
	public ShopsResult saveShopGoods(String appAuthToken,String ePoiId,String categoryName,String description,
			String dishName,String EDishCode,String isSoldOut,String minOrderCount,
			String picture,String sequence,String price,String unit) {
		logger.info("外卖商品新增/修改");
		
		ShopsResult shopsResult = shopGoodsService.saveShopGoods(appAuthToken,ePoiId,categoryName,description,
				dishName,EDishCode,isSoldOut,minOrderCount,
				picture,sequence,price,unit);
		
		return shopsResult;
	}
	
	/**
	 * 外卖商品删除
	 * @param appAuthToken 美团外卖门店映射绑定授权token
	 * @param ePoiId 门店唯一标示
	 * @param eDishCode 商品唯一标识
	 * @return 
	 */	
	@RequestMapping("/deleteShopGoods.do")
	@ResponseBody
	public ShopsResult deleteShopGoods(String appAuthToken,String ePoiId,String eDishCode) {
		logger.info("外卖商品删除");
		
		ShopsResult shopsResult = shopGoodsService.deleteShopGoods(appAuthToken,ePoiId,eDishCode);
		
		return shopsResult;
	}
	
	
}
