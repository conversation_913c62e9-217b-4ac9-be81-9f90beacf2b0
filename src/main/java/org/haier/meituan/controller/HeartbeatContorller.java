package org.haier.meituan.controller;

import java.util.logging.Logger;

import org.haier.shop.util.ShopsResult;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

/**
 * 类名：com.palmshop.meituan.controller.HeartbeatContorller;
 * 描述：心跳
 * 检测终端是否在线，上报美团
 * <AUTHOR>
 * @version v1.0
 *
 */
@Controller
@RequestMapping("/heartbeat")
public class HeartbeatContorller {
	
	private static final Logger logger = Logger.getGlobal();
	
	/**
	 * 上报接口,30s调用一次
	 * @param shopUnique 店铺唯一标识
	 * @return json
	 */	
	@RequestMapping("/report.do")
	@ResponseBody
	public ShopsResult report(String shopUnique) {
		ShopsResult shopsResult = new ShopsResult();
		logger.info("检测终端是否在线，上报美团");
		
		shopsResult.setStatus(1);
		return shopsResult;
	}
	
}
