package org.haier.meituan.service;

import java.util.Map;

import org.haier.shop.util.ShopsResult;

public interface ShopMappingService {

	/**
	 * 获取门店映射信息
	 * @param shop_unique 店铺编号
	 * @return
	 */
	public Map<String ,Object> getShopMappingInfo(String shop_unique);
	
	/**
	 * 获取门店设置信息
	 * @param shop_unique 店铺编号
	 * @return
	 */
	public Map<String ,Object> getShopSettingInfo(String shop_unique);
	
	/**
	 * 设置门店营业状态
	 * @param appAuthToken 美团外卖门店映射绑定授权token
	 * @return 
	 */
	public ShopsResult takeoutShopOpen(String appAuthToken);
	
	/**
	 * 设置门店休息状态
	 * @param appAuthToken 美团外卖门店映射绑定授权token
	 * @return 
	 */
	public ShopsResult takeoutShopClose(String appAuthToken);
	
	/**
	 * 设置门店营业时间
	 * @param appAuthToken 美团外卖门店映射绑定授权token
	 * @param openTime 营业时间时间段06:00-08:00
	 * @return 
	 */	
	public ShopsResult updateOpenTime(String appAuthToken,String openTime);
}
