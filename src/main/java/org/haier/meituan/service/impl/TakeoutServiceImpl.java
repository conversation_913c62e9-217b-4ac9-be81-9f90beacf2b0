package org.haier.meituan.service.impl;

import java.util.Map;

import javax.annotation.Resource;

import org.haier.meituan.dao.TakeoutDao;
import org.haier.meituan.service.TakeoutService;
import org.haier.shop.util.ShopsResult;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * 类名：com.palmshop.meituan.service.TakeoutService;
 * 描述：美团外卖对接service实现类
 * <AUTHOR>
 * @version v1.0
 *
 */
@Service
@Transactional
public class TakeoutServiceImpl implements TakeoutService {

	@Resource
	private TakeoutDao takeoutDao;

	@Override
	@Transactional
	public ShopsResult updateShopsTakeoutAppAuthToken(Map<String, Object> params) {
		ShopsResult shopsResult = new ShopsResult();
		int count = takeoutDao.updateShopsTakeoutAppAuthToken(params);
		if(count > 0){
			shopsResult.setStatus(1);
		}
		return shopsResult;
	}

	@Override
	@Transactional
	public ShopsResult updateShopOperatingStatus(Map<String, Object> params) {
		ShopsResult shopsResult = new ShopsResult();
		int count = takeoutDao.updateShopOperatingStatus(params);
		if(count > 0){
			shopsResult.setStatus(1);
		}
		return shopsResult;
	}

}
