package org.haier.meituan.service.impl;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;

import org.haier.ele.entity.SaleList;
import org.haier.ele.entity.SaleListDetail;
import org.haier.ele.util.Qutil;
import org.haier.meituan.dao.TakeoutOrderDao;
import org.haier.meituan.service.TakeoutOrderService;
import org.haier.meituan.util.MeituanConstantUtils;
import org.haier.meituan.util.MUtil;
import org.haier.shop.util.ShopsResult;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.sankuai.sjst.platform.developer.domain.RequestSysParams;
import com.sankuai.sjst.platform.developer.request.CipCaterTakeoutOrderCancelRequest;
import com.sankuai.sjst.platform.developer.request.CipCaterTakeoutOrderConfirmRequest;
import com.sankuai.sjst.platform.developer.request.CipCaterTakeoutOrderDeliveredRequest;
import com.sankuai.sjst.platform.developer.request.CipCaterTakeoutOrderDeliveringRequest;
import com.sankuai.sjst.platform.developer.request.CipCaterTakeoutOrderDispatchCancelRequest;
import com.sankuai.sjst.platform.developer.request.CipCaterTakeoutOrderDispatchRequest;
import com.sankuai.sjst.platform.developer.request.CipCaterTakeoutOrderQueryByIdRequest;
import com.sankuai.sjst.platform.developer.request.CipCaterTakeoutOrderRefundAcceptRequest;
import com.sankuai.sjst.platform.developer.request.CipCaterTakeoutOrderRefundRejectRequest;
import com.sankuai.sjst.platform.developer.request.CipCaterTakeoutOrderZbDispatchConfirmRequest;
import com.sankuai.sjst.platform.developer.request.CipCaterTakeoutOrderZbDispatchPrepareRequest;
import com.sankuai.sjst.platform.developer.request.CipCaterTakeoutOrderZbDispatchTipUpdateRequest;
import com.sankuai.sjst.platform.developer.request.CipCaterTakeoutOrderZbShippingFeeQueryRequest;


/**
 * 类名：com.palmshop.meituan.service.impl.TakeoutOrderServiceImpl;
 * 描述：外卖订单service实现类
 * <AUTHOR>
 *
 */
@Service
@Transactional
public class TakeoutOrderServiceImpl implements TakeoutOrderService {

	@Resource
	private TakeoutOrderDao takeoutOrderDao;

	@Override
	@Transactional
	public ShopsResult addSaleList(Long shopUnique,Map<String ,Object> orderMap) {
		ShopsResult result = new ShopsResult();
		SaleList saleList = new SaleList();
		Long saleListUnique = Long.valueOf(MUtil.strObject(orderMap.get("orderId")));
		saleList.setShopUnique(shopUnique);//店铺唯一标示
		saleList.setSaleListUnique(saleListUnique);//订单编号
		saleList.setSaleListDatetime(Qutil.getTime());//订单生成时间
		saleList.setSaleListTotal(Double.valueOf(MUtil.strObject(orderMap.get("originalPrice"))));//销售单金额（商品）
		saleList.setCusUnique("");//用户的唯一性标识
		saleList.setSaleType(4);//订单类型（4-美团外卖订单）
		saleList.setSaleListName(MUtil.strObject(orderMap.get("recipientName")));//收货人姓名
		saleList.setSaleListPhone(MUtil.strObject(orderMap.get("recipientPhone")));//收货人联系电话
		saleList.setSaleListAddress(MUtil.strObject(orderMap.get("recipientAddress")));//订单送货地址
		saleList.setSaleListDelfee(Double.valueOf(MUtil.strObject(orderMap.get("shippingFee"))));// 外送费
		saleList.setSaleListDiscount(1.0);// 折扣率
		saleList.setSaleListState(3);// 已付款
		saleList.setSaleListHandlestate(1);// 发货状态；1-无效订单 2-未发货 3-已发货 4-已收货
		saleList.setSaleListPayment(6);//美团外卖
		saleList.setSaleListRemarks(MUtil.strObject(orderMap.get("caution")));//订单备注
		saleList.setSaleListActuallyReceived(Double.valueOf(MUtil.strObject(orderMap.get("total"))));//订单实际收到金额

		List<Map<String ,Object>> detailList = (List<Map<String, Object>>) orderMap.get("detail");
		List<SaleListDetail> saleDetailList = new ArrayList<SaleListDetail>();
		Integer saleListTotalCount = 0;//商品总数量
	    for (int i = 0; i < detailList.size(); i++) {
	    	Map<String ,Object> detail = detailList.get(i);
	    	String app_food_code = MUtil.strObject(detail.get("app_food_code"));// ERP端菜品唯一标示
	    	String food_name = MUtil.strObject(detail.get("food_name"));//菜品名
	    	String quantity = MUtil.strObject(detail.get("quantity"));//商品数量
	    	saleListTotalCount = saleListTotalCount + Integer.parseInt(quantity);
	    	String price = MUtil.strObject(detail.get("price"));//商品原价
	    	String box_num = MUtil.strObject(detail.get("box_num"));//餐盒总个数
	    	String box_price = MUtil.strObject(detail.get("box_price"));//餐盒费，单价
	    	String food_discount = MUtil.strObject(detail.get("food_discount"));//菜品折扣
	    	Map<String, Object> goodsInfo = takeoutOrderDao.getGoodsInfo(app_food_code);
	    	
	    	SaleListDetail saleListDetail = new SaleListDetail();
	    	saleListDetail.setSaleListUnique(saleListUnique);
	    	saleListDetail.setGoodsBarcode(app_food_code);
	    	saleListDetail.setGoodsName(food_name);
	    	saleListDetail.setGoodsPicturepath(MUtil.strObject(goodsInfo.get("goods_picturepath")));
	    	saleListDetail.setSaleListDetailCount(Integer.parseInt(quantity));
	    	saleListDetail.setSaleListDetailPrice(Double.valueOf(price));
	    	//金额小计：商品原价*商品数量*商品折扣+餐盒总数量*餐盒费
	    	Double sale_list_detail_subtotal = Double.valueOf(price)*Integer.parseInt(quantity)*Double.valueOf(food_discount)+Integer.parseInt(box_num)*Double.valueOf(box_price);
	    	saleListDetail.setSaleListDetailSubtotal(sale_list_detail_subtotal);
	    	saleListDetail.setGoodsId(Long.valueOf(MUtil.strObject(goodsInfo.get("goods_id"))));
	    	saleListDetail.setGoodsPurprice(Double.valueOf(MUtil.strObject(goodsInfo.get("goods_in_price"))));
	    	
			saleDetailList.add(saleListDetail);
	    }
	    saleList.setSaleListTotalCount(saleListTotalCount);//商品总数量
	    int saleCount = takeoutOrderDao.addSaleList(saleList);
	    int detailCount = takeoutOrderDao.addSaleDetail(saleDetailList);
		
		if (saleCount > 0 && detailCount > 0) {
			result.setStatus(1);
			result.setData(saleListUnique);
		}
		
		return result;
	}

	@Override
	public ShopsResult getSaleInfoList(String shopUnique) {
		ShopsResult ShopsResult = new ShopsResult();
		try {
			Map<String ,Object> params = new HashMap<String, Object>();
			params.put("shopUnique", shopUnique);
			List<Map<String ,Object>> orderList = takeoutOrderDao.getSaleInfoList(params);
			ShopsResult.setData(orderList);
			ShopsResult.setStatus(1);
			ShopsResult.setMsg("成功");
		} catch (Exception e) {
			ShopsResult.setStatus(0);
			ShopsResult.setMsg("获取数据异常："+e.getMessage());
		}
		return ShopsResult;
	}

	@Override
	public ShopsResult confirmOrder(String saleListUnique,String takeoutAppAuthToken) {
		ShopsResult ShopsResult = new ShopsResult();
		try {
			RequestSysParams requestSysParams = new RequestSysParams(MeituanConstantUtils.signKey, takeoutAppAuthToken);// 声明公共参数
			//获取美团订单信息
			CipCaterTakeoutOrderQueryByIdRequest queryByIdRequest = new CipCaterTakeoutOrderQueryByIdRequest();
			queryByIdRequest.setRequestSysParams(requestSysParams);
			queryByIdRequest.setOrderId(Long.valueOf(saleListUnique));
			String resultJson = queryByIdRequest.doRequest();
			Map<String ,Object> orderMap = (Map<String, Object>) MUtil.jsonToMap(resultJson).get("data");
			//订单状态
			String status = MUtil.strObject(orderMap.get("status"));
			if(status != null && !status.equals("9")){//已取消
				//向美团发送商家确认订单消息
				CipCaterTakeoutOrderConfirmRequest confirmRequest = new CipCaterTakeoutOrderConfirmRequest();
				confirmRequest.setRequestSysParams(requestSysParams);
				confirmRequest.setOrderId(Long.valueOf(saleListUnique));
				String confirmResultJson = confirmRequest.doRequest();
				String data = MUtil.strObject(MUtil.jsonToMap(confirmResultJson).get("data"));
				if(data != null && data.equalsIgnoreCase("OK")){
					ShopsResult.setStatus(1);
					ShopsResult.setMsg("成功");
				}
			}else{
				ShopsResult.setStatus(2);
				ShopsResult.setMsg("订单已取消");
			}
		} catch (Exception e) {
			ShopsResult.setStatus(0);
			ShopsResult.setMsg("异常："+e.getMessage());
		}
		return ShopsResult;
	}

	
	@Override
	@Transactional
	public ShopsResult updateSaleList(Map<String, Object> orderMap,int saleListHandlestate) {
		ShopsResult ShopsResult = new ShopsResult();
		try {
			Long saleListUnique = Long.valueOf(MUtil.strObject(orderMap.get("orderId")));
			
			SaleList saleList = new SaleList();
			saleList.setSaleListHandlestate(saleListHandlestate);
			saleList.setSaleListUnique(saleListUnique);
			if(saleListHandlestate == 5){//订单取消
				saleList.setCancleReason(MUtil.strObject(orderMap.get("reason")));
			}
			takeoutOrderDao.updatSaleList(saleList);
			ShopsResult.setStatus(1);
			ShopsResult.setMsg("成功");
		} catch (Exception e) {
			ShopsResult.setStatus(0);
			ShopsResult.setMsg("异常："+e.getMessage());
		}
		return ShopsResult;
	}

	@Override
	public ShopsResult cancelOrder(String saleListUnique,String takeoutAppAuthToken, String reasonCode, String reason) {
		ShopsResult ShopsResult = new ShopsResult();
		try {
			RequestSysParams requestSysParams = new RequestSysParams(MeituanConstantUtils.signKey, takeoutAppAuthToken);// 声明公共参数
			//获取美团订单信息
			CipCaterTakeoutOrderQueryByIdRequest queryByIdRequest = new CipCaterTakeoutOrderQueryByIdRequest();
			queryByIdRequest.setRequestSysParams(requestSysParams);
			queryByIdRequest.setOrderId(Long.valueOf(saleListUnique));
			String resultJson = queryByIdRequest.doRequest();
			Map<String ,Object> orderMap = (Map<String, Object>) MUtil.jsonToMap(resultJson).get("data");
			//订单状态
			String status = MUtil.strObject(orderMap.get("status"));
			if(status != null && !status.equals("9")){//已取消
				//向美团发送商家取消订单消息
				CipCaterTakeoutOrderCancelRequest cancelRequest = new CipCaterTakeoutOrderCancelRequest();
				cancelRequest.setRequestSysParams(requestSysParams);
				cancelRequest.setOrderId(Long.valueOf(saleListUnique));
				cancelRequest.setReasonCode(reasonCode);
				cancelRequest.setReason(reason);
				String cancelResultJson = cancelRequest.doRequest();
				String data = MUtil.strObject(MUtil.jsonToMap(cancelResultJson).get("data"));
				if(data != null && data.equalsIgnoreCase("OK")){
					ShopsResult.setStatus(1);
					ShopsResult.setMsg("成功");
				}
			}else{
				ShopsResult.setStatus(2);
				ShopsResult.setMsg("订单已取消");
			}
		} catch (Exception e) {
			ShopsResult.setStatus(0);
			ShopsResult.setMsg("异常："+e.getMessage());
		}
		return ShopsResult;
	}

	@Override
	@Transactional
	public ShopsResult delivering(String saleListUnique,String takeoutAppAuthToken, String courierName, String courierPhone) {
		ShopsResult ShopsResult = new ShopsResult();
		try {
			RequestSysParams requestSysParams = new RequestSysParams(MeituanConstantUtils.signKey, takeoutAppAuthToken);// 声明公共参数
			//向美团发起自配送
			CipCaterTakeoutOrderDeliveringRequest request = new CipCaterTakeoutOrderDeliveringRequest();
			request.setRequestSysParams(requestSysParams);
			request.setOrderId(Long.valueOf(saleListUnique));
			request.setCourierName(courierName);
			request.setCourierPhone(courierPhone);
			String resultJson = request.doRequest();
			String data = MUtil.strObject(MUtil.jsonToMap(resultJson).get("data"));
			if(data != null && data.equalsIgnoreCase("OK")){
				//修改订单发货状态为待收货
				SaleList saleList = new SaleList();
				saleList.setSaleListHandlestate(3);//待收货
				saleList.setSaleListUnique(Long.valueOf(saleListUnique));
				takeoutOrderDao.updatSaleList(saleList);
				
				ShopsResult.setStatus(1);
				ShopsResult.setMsg("成功");
			}
				
		} catch (Exception e) {
			ShopsResult.setStatus(0);
			ShopsResult.setMsg("异常："+e.getMessage());
		}
		return ShopsResult;
	}

	@Override
	@Transactional
	public ShopsResult delivered(String saleListUnique,String takeoutAppAuthToken) {
		ShopsResult ShopsResult = new ShopsResult();
		try {
			RequestSysParams requestSysParams = new RequestSysParams(MeituanConstantUtils.signKey, takeoutAppAuthToken);// 声明公共参数
			//向美团发起自配送--订单已送达
			CipCaterTakeoutOrderDeliveredRequest request = new CipCaterTakeoutOrderDeliveredRequest();
			request.setRequestSysParams(requestSysParams);
			request.setOrderId(Long.valueOf(saleListUnique));
			String resultJson = request.doRequest();
			String data = MUtil.strObject(MUtil.jsonToMap(resultJson).get("data"));
			if(data != null && data.equalsIgnoreCase("OK")){
				//修改订单发货状态为已完成
				SaleList saleList = new SaleList();
				saleList.setSaleListHandlestate(4);//已完成
				saleList.setSaleListUnique(Long.valueOf(saleListUnique));
				takeoutOrderDao.updatSaleList(saleList);
				
				ShopsResult.setStatus(1);
				ShopsResult.setMsg("成功");
			}
				
		} catch (Exception e) {
			ShopsResult.setStatus(0);
			ShopsResult.setMsg("异常："+e.getMessage());
		}
		return ShopsResult;
	}

	@Override
	public ShopsResult queryZbShippingFee(String saleListUnique,String takeoutAppAuthToken) {
		ShopsResult ShopsResult = new ShopsResult();
		try {
			RequestSysParams requestSysParams = new RequestSysParams(MeituanConstantUtils.signKey, takeoutAppAuthToken);// 声明公共参数
			//众包配送场景－查询配送费
			CipCaterTakeoutOrderZbShippingFeeQueryRequest request = new CipCaterTakeoutOrderZbShippingFeeQueryRequest();
			request.setRequestSysParams(requestSysParams);
			request.setOrderIds(saleListUnique);
			String resultJson = request.doRequest();
			List<Map<String ,Object>> list = (List<Map<String, Object>>) MUtil.jsonToMap(resultJson).get("data");
			if(list.size()>0){
				ShopsResult.setData(list.get(0));
				ShopsResult.setStatus(1);
				ShopsResult.setMsg("成功");
			}
				
		} catch (Exception e) {
			ShopsResult.setStatus(0);
			ShopsResult.setMsg("异常："+e.getMessage());
		}
		return ShopsResult;
	}

	@Override
	public ShopsResult prepareZbDispatch(String saleListUnique,String takeoutAppAuthToken, Double shippingFee, Double tipAmount) {
		ShopsResult ShopsResult = new ShopsResult();
		try {
			RequestSysParams requestSysParams = new RequestSysParams(MeituanConstantUtils.signKey, takeoutAppAuthToken);// 声明公共参数
			//众包配送场景－预下单
			CipCaterTakeoutOrderZbDispatchPrepareRequest request = new CipCaterTakeoutOrderZbDispatchPrepareRequest();
			request.setRequestSysParams(requestSysParams);
			request.setOrderId(Long.valueOf(saleListUnique));
			request.setShippingFee(shippingFee);
			request.setTipAmount(tipAmount);
			String resultJson = request.doRequest();
			String data = MUtil.strObject(MUtil.jsonToMap(resultJson).get("data"));
			if(data != null && data.equalsIgnoreCase("OK")){
				ShopsResult.setStatus(1);
				ShopsResult.setMsg("成功");
			}else{
				Map<String ,Object> dataMap= MUtil.jsonToMap(resultJson);
				ShopsResult.setData(dataMap);
				ShopsResult.setStatus(2);
				ShopsResult.setMsg("预下单失败");
			}
				
		} catch (Exception e) {
			ShopsResult.setStatus(0);
			ShopsResult.setMsg("异常："+e.getMessage());
		}
		return ShopsResult;
	}

	@Override
	public ShopsResult confirmZbDispatch(String saleListUnique,String takeoutAppAuthToken, Double tipAmount) {
		ShopsResult ShopsResult = new ShopsResult();
		try {
			RequestSysParams requestSysParams = new RequestSysParams(MeituanConstantUtils.signKey, takeoutAppAuthToken);// 声明公共参数
			//众包配送场景－确认下单
			CipCaterTakeoutOrderZbDispatchConfirmRequest request = new CipCaterTakeoutOrderZbDispatchConfirmRequest();
			request.setRequestSysParams(requestSysParams);
			request.setOrderId(Long.valueOf(saleListUnique));
			request.setTipAmount(tipAmount);
			String resultJson = request.doRequest();
			String data = MUtil.strObject(MUtil.jsonToMap(resultJson).get("data"));
			if(data != null && data.equalsIgnoreCase("OK")){
				//修改订单发货状态为配送单待确认
				SaleList saleList = new SaleList();
				saleList.setSaleListHandlestate(7);//配送单待确认
				saleList.setSaleListUnique(Long.valueOf(saleListUnique));
				takeoutOrderDao.updatSaleList(saleList);
				ShopsResult.setStatus(1);
				ShopsResult.setMsg("成功");
			}
				
		} catch (Exception e) {
			ShopsResult.setStatus(0);
			ShopsResult.setMsg("异常："+e.getMessage());
		}
		return ShopsResult;
	}

	@Override
	public ShopsResult updateZbDispatchTip(String saleListUnique,String takeoutAppAuthToken, Double tipAmount) {
		ShopsResult ShopsResult = new ShopsResult();
		try {
			RequestSysParams requestSysParams = new RequestSysParams(MeituanConstantUtils.signKey, takeoutAppAuthToken);// 声明公共参数
			//众包配送场景－配送单加小费
			CipCaterTakeoutOrderZbDispatchTipUpdateRequest request = new CipCaterTakeoutOrderZbDispatchTipUpdateRequest();
			request.setRequestSysParams(requestSysParams);
			request.setOrderId(Long.valueOf(saleListUnique));
			request.setTipAmount(tipAmount);
			String resultJson = request.doRequest();
			String data = MUtil.strObject(MUtil.jsonToMap(resultJson).get("data"));
			if(data != null && data.equalsIgnoreCase("OK")){
				ShopsResult.setStatus(1);
				ShopsResult.setMsg("成功");
			}
				
		} catch (Exception e) {
			ShopsResult.setStatus(0);
			ShopsResult.setMsg("异常："+e.getMessage());
		}
		return ShopsResult;
	}

	@Override
	public ShopsResult cancelDispatch(String saleListUnique,String takeoutAppAuthToken) {
		ShopsResult ShopsResult = new ShopsResult();
		try {
			RequestSysParams requestSysParams = new RequestSysParams(MeituanConstantUtils.signKey, takeoutAppAuthToken);// 声明公共参数
			//取消美团众包配送和美团专送
			CipCaterTakeoutOrderDispatchCancelRequest request = new CipCaterTakeoutOrderDispatchCancelRequest();
			request.setRequestSysParams(requestSysParams);
			request.setOrderId(Long.valueOf(saleListUnique));
			String resultJson = request.doRequest();
			String data = MUtil.strObject(MUtil.jsonToMap(resultJson).get("data"));
			if(data != null && data.equalsIgnoreCase("OK")){
				ShopsResult.setStatus(1);
				ShopsResult.setMsg("成功");
			}
				
		} catch (Exception e) {
			ShopsResult.setStatus(0);
			ShopsResult.setMsg("异常："+e.getMessage());
		}
		return ShopsResult;
	}

	@Override
	public ShopsResult dispatchShip(String saleListUnique,String takeoutAppAuthToken) {
		ShopsResult ShopsResult = new ShopsResult();
		try {
			RequestSysParams requestSysParams = new RequestSysParams(MeituanConstantUtils.signKey, takeoutAppAuthToken);// 声明公共参数
			//美团专送--发起配送
			CipCaterTakeoutOrderDispatchRequest request = new CipCaterTakeoutOrderDispatchRequest();
			request.setRequestSysParams(requestSysParams);
			request.setOrderId(Long.valueOf(saleListUnique));
			String resultJson = request.doRequest();
			String data = MUtil.strObject(MUtil.jsonToMap(resultJson).get("data"));
			if(data != null && data.equalsIgnoreCase("OK")){
				//修改订单发货状态为配送单待确认
				SaleList saleList = new SaleList();
				saleList.setSaleListHandlestate(7);//配送单待确认
				saleList.setSaleListUnique(Long.valueOf(saleListUnique));
				takeoutOrderDao.updatSaleList(saleList);
				ShopsResult.setStatus(1);
				ShopsResult.setMsg("成功");
			}
				
		} catch (Exception e) {
			ShopsResult.setStatus(0);
			ShopsResult.setMsg("异常："+e.getMessage());
		}
		return ShopsResult;
	}

	@Override
	@Transactional
	public ShopsResult updatSaleListState(SaleList saleList) {
		ShopsResult ShopsResult = new ShopsResult();
		try {
			takeoutOrderDao.updatSaleListState(saleList);
			ShopsResult.setStatus(1);
			ShopsResult.setMsg("成功");
		} catch (Exception e) {
			ShopsResult.setStatus(0);
			ShopsResult.setMsg("异常："+e.getMessage());
		}
		return ShopsResult;
	}

	@Override
	public ShopsResult agreeRefund(String saleListUnique,String takeoutAppAuthToken,String refundReason) {
		ShopsResult ShopsResult = new ShopsResult();
		try {
			RequestSysParams requestSysParams = new RequestSysParams(MeituanConstantUtils.signKey, takeoutAppAuthToken);// 声明公共参数
			//订单同意退款
			CipCaterTakeoutOrderRefundAcceptRequest request = new CipCaterTakeoutOrderRefundAcceptRequest();
			request.setRequestSysParams(requestSysParams);
			request.setOrderId(Long.valueOf(saleListUnique));
			request.setReason(refundReason);
			String resultJson = request.doRequest();
			String data = MUtil.strObject(MUtil.jsonToMap(resultJson).get("data"));
			if(data != null && data.equalsIgnoreCase("OK")){
				ShopsResult.setStatus(1);
				ShopsResult.setMsg("成功");
			}
				
		} catch (Exception e) {
			ShopsResult.setStatus(0);
			ShopsResult.setMsg("异常："+e.getMessage());
		}
		return ShopsResult;
	}

	@Override
	public ShopsResult rejectRefund(String saleListUnique,String takeoutAppAuthToken, String refundReason) {
		ShopsResult ShopsResult = new ShopsResult();
		try {
			RequestSysParams requestSysParams = new RequestSysParams(MeituanConstantUtils.signKey, takeoutAppAuthToken);// 声明公共参数
			//订单拒绝退款
			CipCaterTakeoutOrderRefundRejectRequest request = new CipCaterTakeoutOrderRefundRejectRequest();
			request.setRequestSysParams(requestSysParams);
			request.setOrderId(Long.valueOf(saleListUnique));
			request.setReason(refundReason);
			String resultJson = request.doRequest();
			String data = MUtil.strObject(MUtil.jsonToMap(resultJson).get("data"));
			if(data != null && data.equalsIgnoreCase("OK")){
				ShopsResult.setStatus(1);
				ShopsResult.setMsg("成功");
			}
				
		} catch (Exception e) {
			ShopsResult.setStatus(0);
			ShopsResult.setMsg("异常："+e.getMessage());
		}
		return ShopsResult;
	}
	
}
