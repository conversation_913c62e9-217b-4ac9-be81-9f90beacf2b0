package org.haier.meituan.service.impl;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;

import net.sf.json.JSONArray;

import org.haier.meituan.dao.ShopMappingDao;
import org.haier.meituan.service.ShopGoodsService;
import org.haier.meituan.util.MUtil;
import org.haier.meituan.util.MeituanConstantUtils;
import org.haier.shop.util.ShopsResult;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.sankuai.sjst.platform.developer.domain.RequestSysParams;
import com.sankuai.sjst.platform.developer.request.CipCaterTakeoutDishBatchUploadRequest;
import com.sankuai.sjst.platform.developer.request.CipCaterTakeoutDishCatListQueryRequest;
import com.sankuai.sjst.platform.developer.request.CipCaterTakeoutDishDeleteRequest;
import com.sankuai.sjst.platform.developer.request.CipCaterTakeoutDishQueryByEPoiIdRequest;

@Service
@Transactional
public class ShopGoodsServiceImpl implements ShopGoodsService{
	
	@Resource
	private ShopMappingDao shopMappingDao;

	@Override
	public ShopsResult getShopGoodsList(String shop_unique,String page) {
		ShopsResult shopsResult = new ShopsResult();
		try {
			Map<String,Object> result = shopMappingDao.getShopInfo(shop_unique);
			if(result != null){
				String takeout_appAuthToken = MUtil.strObject(result.get("takeout_appAuthToken"));
				if(takeout_appAuthToken != null && !takeout_appAuthToken.equals("")){
					RequestSysParams requestSysParams = new RequestSysParams(MeituanConstantUtils.signKey, takeout_appAuthToken);// 声明公共参数
					//查询商品列表
					CipCaterTakeoutDishQueryByEPoiIdRequest request = new CipCaterTakeoutDishQueryByEPoiIdRequest();
					request.setRequestSysParams(requestSysParams);
					request.setePoiId(shop_unique);
					request.setOffset((Integer.parseInt(page)-1)*20);
					request.setLimit(20);
					String resultJson = request.doRequest();
					List<Map<String ,Object>> classList = (List<Map<String, Object>>) MUtil.jsonToMap(resultJson).get("data");
					if(classList != null && classList.size() > 0){
						Map<String ,Object> data = new HashMap<String, Object>();
						data.put("classList", classList);
						data.put("appAuthToken", takeout_appAuthToken);
						data.put("page", page);
						shopsResult.setData(data);
						shopsResult.setStatus(1);
						shopsResult.setMsg("成功");
					}
				}else{
					shopsResult.setStatus(2);
					shopsResult.setMsg("未映射");	
				}
			}
		} catch (Exception e) {
			shopsResult.setStatus(0);
			shopsResult.setMsg("异常："+e.getMessage());		
		}
		return shopsResult;
	}

	@Override
	public ShopsResult deleteShopGoods(String appAuthToken, String ePoiId,String eDishCode) {
		ShopsResult shopsResult = new ShopsResult();
		try {
			RequestSysParams requestSysParams = new RequestSysParams(MeituanConstantUtils.signKey, appAuthToken);// 声明公共参数
			//删除商品
			CipCaterTakeoutDishDeleteRequest request = new CipCaterTakeoutDishDeleteRequest();
			request.setRequestSysParams(requestSysParams);
			request.setePoiId(ePoiId);
			request.seteDishCode(eDishCode);
			String resultJson = request.doRequest();
			String data = MUtil.strObject(MUtil.jsonToMap(resultJson).get("data"));
			if(data != null && data.equalsIgnoreCase("OK")){
				shopsResult.setStatus(1);
				shopsResult.setMsg("成功");
			}
		} catch (Exception e) {
			shopsResult.setStatus(0);
			shopsResult.setMsg("异常："+e.getMessage());		
		}
		return shopsResult;
	}

	@Override
	public ShopsResult saveShopGoods(String appAuthToken,String ePoiId,String categoryName,String description,
			String dishName,String EDishCode,String isSoldOut,String minOrderCount,
			String picture,String sequence,String price,String unit) {
		ShopsResult shopsResult = new ShopsResult();
		try {
			List<Map<String ,Object>> dishesList = new ArrayList<Map<String,Object>>();
			Map<String ,Object> param = new HashMap<String, Object>();
			param.put("boxNum", 0);
			param.put("boxPrice", 0.0);
			param.put("categoryName", categoryName);
			param.put("description", description);
			param.put("dishName", dishName);
			param.put("EDishCode", EDishCode);
			param.put("ePoiId", ePoiId);
			param.put("isSoldOut", isSoldOut);
			param.put("minOrderCount", minOrderCount);
			param.put("picture", picture);
			param.put("sequence", sequence);
			param.put("price", price);
			param.put("unit", unit);
			dishesList.add(param);
			JSONArray json = JSONArray.fromObject(dishesList); 
			RequestSysParams requestSysParams = new RequestSysParams(MeituanConstantUtils.signKey, appAuthToken);// 声明公共参数
			//商品新增/修改
			CipCaterTakeoutDishBatchUploadRequest request = new CipCaterTakeoutDishBatchUploadRequest();
			request.setRequestSysParams(requestSysParams);
			request.setePoiId(ePoiId);
			request.setDishes(json.toString());
			String resultJson = request.doRequest();
			String data = MUtil.strObject(MUtil.jsonToMap(resultJson).get("data"));
			if(data != null && data.equalsIgnoreCase("OK")){
				//修改收银系统商品同步平台
				Map<String ,Object> params = shopMappingDao.getGoodsInfo(EDishCode);
				String synchronous_platform = MUtil.strObject(params.get("synchronous_platform"));
				if(synchronous_platform != null && synchronous_platform.equals("0")){
					params.put("synchronous_platform", "1");//美团
				}else if(synchronous_platform.equals("2")){
					params.put("synchronous_platform", "3");
				}
				shopMappingDao.updateGoodsInfo(params);
				shopsResult.setStatus(1);
				shopsResult.setMsg("成功");
			}
		} catch (Exception e) {
			shopsResult.setStatus(0);
			shopsResult.setMsg("异常："+e.getMessage());		
		}
		return shopsResult;
	}

	@Override
	public List<Map<String ,Object>> getGoodsList(String shop_unique) {
		return shopMappingDao.getGoodsList(shop_unique);
	}

	@Override
	public List<Map<String, Object>> getClassList(String appAuthToken) {
		List<Map<String ,Object>> classList = new ArrayList<Map<String,Object>>();
		try {
			RequestSysParams requestSysParams = new RequestSysParams(MeituanConstantUtils.signKey, appAuthToken);// 声明公共参数
			//查询商品分类
			CipCaterTakeoutDishCatListQueryRequest request = new CipCaterTakeoutDishCatListQueryRequest();
			request.setRequestSysParams(requestSysParams);
			String resultJson = request.doRequest();
			classList = (List<Map<String, Object>>) MUtil.jsonToMap(resultJson).get("data");
		} catch (Exception e) {
			e.printStackTrace();
		} 
		return classList;
	}

}
