package org.haier.meituan.service.impl;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;

import org.haier.meituan.dao.ShopMappingDao;
import org.haier.meituan.service.ShopClassService;
import org.haier.meituan.util.MUtil;
import org.haier.meituan.util.MeituanConstantUtils;
import org.haier.shop.util.ShopsResult;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.sankuai.sjst.platform.developer.domain.RequestSysParams;
import com.sankuai.sjst.platform.developer.request.CipCaterTakeoutDishCatDeleteRequest;
import com.sankuai.sjst.platform.developer.request.CipCaterTakeoutDishCatListQueryRequest;
import com.sankuai.sjst.platform.developer.request.CipCaterTakeoutDishCatUpdateRequest;

@Service
@Transactional
public class ShopClassServiceImpl implements ShopClassService{
	
	@Resource
	private ShopMappingDao shopMappingDao;

	@Override
	public ShopsResult getShopClassList(String shop_unique) {
		ShopsResult shopsResult = new ShopsResult();
		try {
			Map<String,Object> result = shopMappingDao.getShopInfo(shop_unique);
			if(result != null){
				String takeout_appAuthToken = MUtil.strObject(result.get("takeout_appAuthToken"));
				if(takeout_appAuthToken != null && !takeout_appAuthToken.equals("")){
					RequestSysParams requestSysParams = new RequestSysParams(MeituanConstantUtils.signKey, takeout_appAuthToken);// 声明公共参数
					//查询商品分类
					CipCaterTakeoutDishCatListQueryRequest request = new CipCaterTakeoutDishCatListQueryRequest();
					request.setRequestSysParams(requestSysParams);
					String resultJson = request.doRequest();
					List<Map<String ,Object>> classList = (List<Map<String, Object>>) MUtil.jsonToMap(resultJson).get("data");
					if(classList != null && classList.size() > 0){
						Map<String ,Object> data = new HashMap<String, Object>();
						data.put("classList", classList);
						data.put("appAuthToken", takeout_appAuthToken);
						shopsResult.setData(data);
						shopsResult.setStatus(1);
						shopsResult.setMsg("成功");
					}
				}else{
					shopsResult.setStatus(2);
					shopsResult.setMsg("未映射");	
				}
			}
		} catch (Exception e) {
			shopsResult.setStatus(0);
			shopsResult.setMsg("异常："+e.getMessage());		
		}
		return shopsResult;
	}

	@Override
	public ShopsResult saveShopClass(String appAuthToken,String oldCatName, String catName,String sequence) {
		ShopsResult shopsResult = new ShopsResult();
		try {
			RequestSysParams requestSysParams = new RequestSysParams(MeituanConstantUtils.signKey, appAuthToken);// 声明公共参数
			//新增/修改商品分类
			CipCaterTakeoutDishCatUpdateRequest request = new CipCaterTakeoutDishCatUpdateRequest();
			request.setRequestSysParams(requestSysParams);
			request.setOldCatName(oldCatName);
			request.setCatName(catName);
			request.setSequence(Integer.parseInt(sequence));
			String resultJson = request.doRequest();
			String data = MUtil.strObject(MUtil.jsonToMap(resultJson).get("data"));
			if(data != null && data.equalsIgnoreCase("OK")){
				shopsResult.setStatus(1);
				shopsResult.setMsg("成功");
			}
		} catch (Exception e) {
			shopsResult.setStatus(0);
			shopsResult.setMsg("异常："+e.getMessage());		
		}
		return shopsResult;
	}

	@Override
	public ShopsResult deleteShopClass(String appAuthToken, String catName) {
		ShopsResult shopsResult = new ShopsResult();
		try {
			RequestSysParams requestSysParams = new RequestSysParams(MeituanConstantUtils.signKey, appAuthToken);// 声明公共参数
			//删除商品分类
			CipCaterTakeoutDishCatDeleteRequest request = new CipCaterTakeoutDishCatDeleteRequest();
			request.setRequestSysParams(requestSysParams);
			request.setCatName(catName);
			String resultJson = request.doRequest();
			String data = MUtil.strObject(MUtil.jsonToMap(resultJson).get("data"));
			if(data != null && data.equalsIgnoreCase("OK")){
				shopsResult.setStatus(1);
				shopsResult.setMsg("成功");
			}
		} catch (Exception e) {
			shopsResult.setStatus(0);
			shopsResult.setMsg("异常："+e.getMessage());		
		}
		return shopsResult;
	}
	
}
