package org.haier.meituan.service.impl;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;

import org.haier.meituan.dao.ShopMappingDao;
import org.haier.meituan.service.ShopMappingService;
import org.haier.meituan.util.MUtil;
import org.haier.meituan.util.MeituanConstantUtils;
import org.haier.meituan.util.SignUtils;
import org.haier.shop.util.ShopsResult;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.sankuai.sjst.platform.developer.domain.RequestSysParams;
import com.sankuai.sjst.platform.developer.request.CipCaterTakeoutPoiCloseRequest;
import com.sankuai.sjst.platform.developer.request.CipCaterTakeoutPoiInfoQueryRequest;
import com.sankuai.sjst.platform.developer.request.CipCaterTakeoutPoiOpenRequest;
import com.sankuai.sjst.platform.developer.request.CipCaterTakeoutPoiOpenTimeUpdateRequest;

@Service
@Transactional
public class ShopMappingServiceImpl implements ShopMappingService{
	
	@Resource
	private ShopMappingDao shopMappingDao;

	@Override
	public Map<String, Object> getShopMappingInfo(String shop_unique) {
		Map<String, Object> resultParams = new HashMap<String, Object>();
		try {
			Map<String,Object> result = shopMappingDao.getShopInfo(shop_unique);
			if(result != null){
				String takeout_appAuthToken = MUtil.strObject(result.get("takeout_appAuthToken"));
				if(takeout_appAuthToken != null && !takeout_appAuthToken.equals("")){
					//已映射，获取美团外卖门店信息
					RequestSysParams requestSysParams = new RequestSysParams(MeituanConstantUtils.signKey, takeout_appAuthToken);// 声明公共参数
					CipCaterTakeoutPoiInfoQueryRequest request = new CipCaterTakeoutPoiInfoQueryRequest();
					request.setRequestSysParams(requestSysParams);
					request.setEPoiIds(shop_unique);
					String resultJson = request.doRequest();
					List<Map<String ,Object>> dataList = (List<Map<String, Object>>) MUtil.jsonToMap(resultJson).get("data");
					if(dataList != null && dataList.size()>0){
						resultParams = dataList.get(0);
					}
					resultParams.put("signKey", MeituanConstantUtils.signKey);
					resultParams.put("businessId", MeituanConstantUtils.businessId);
					resultParams.put("appAuthToken", takeout_appAuthToken);
					resultParams.put("is_realtion", "1");//是否映射,0 未映射  1已映射
				}else{
					//未映射，获取门店映射信息
					long timestamp = System.currentTimeMillis();
					resultParams.put("businessId", MeituanConstantUtils.businessId);
					resultParams.put("charset", "utf-8");
					resultParams.put("developerId", MeituanConstantUtils.developerId);
					resultParams.put("ePoiId", result.get("shop_unique").toString());
					resultParams.put("ePoiName", result.get("shop_name").toString());
					resultParams.put("timestamp", String.valueOf(timestamp));
					String sign = SignUtils.createSign(MeituanConstantUtils.signKey,resultParams);
					resultParams.put("developerId", MeituanConstantUtils.developerId);
					resultParams.put("sign", sign);
					resultParams.put("is_realtion", "0");//是否映射,0 未映射  1已映射
				}
			}
		} catch (Exception e) {
			e.printStackTrace();
		}
		return resultParams;
	}

	@Override
	public Map<String, Object> getShopSettingInfo(String shop_unique) {
		Map<String, Object> resultParams = new HashMap<String, Object>();
		try {
			Map<String,Object> result = shopMappingDao.getShopInfo(shop_unique);
			if(result != null){
				String takeout_appAuthToken = MUtil.strObject(result.get("takeout_appAuthToken"));
				if(takeout_appAuthToken != null && !takeout_appAuthToken.equals("")){
					//已映射，获取美团外卖门店信息
					RequestSysParams requestSysParams = new RequestSysParams(MeituanConstantUtils.signKey, takeout_appAuthToken);// 声明公共参数
					CipCaterTakeoutPoiInfoQueryRequest request = new CipCaterTakeoutPoiInfoQueryRequest();
					request.setRequestSysParams(requestSysParams);
					request.setEPoiIds(shop_unique);
					String resultJson = request.doRequest();
					List<Map<String ,Object>> dataList = (List<Map<String, Object>>) MUtil.jsonToMap(resultJson).get("data");
					if(dataList != null && dataList.size()>0){
						resultParams.put("appAuthToken", takeout_appAuthToken);
						resultParams.put("isOpen", MUtil.strObject(dataList.get(0).get("isOpen")));//是否营业：1营业 3休息中 0-未上线
						resultParams.put("openTime", MUtil.strObject(dataList.get(0).get("openTime")));//营业时间
					}
					resultParams.put("is_realtion", "1");//是否映射,0 未映射  1已映射
				}else{
					resultParams.put("is_realtion", "0");//是否映射,0 未映射  1已映射
					resultParams.put("shop_unique", shop_unique);
				}
			}
		} catch (Exception e) {
			e.printStackTrace();
		}
		return resultParams;
	}
	
	@Override
	public ShopsResult takeoutShopOpen(String appAuthToken) {
		ShopsResult shopsResult = new ShopsResult();
		try {
			RequestSysParams requestSysParams = new RequestSysParams(MeituanConstantUtils.signKey, appAuthToken);// 声明公共参数
			//设置门店营业
			CipCaterTakeoutPoiOpenRequest request = new CipCaterTakeoutPoiOpenRequest();
			request.setRequestSysParams(requestSysParams);
			String resultJson = request.doRequest();
			String data = MUtil.strObject(MUtil.jsonToMap(resultJson).get("data"));
			if(data != null && data.equalsIgnoreCase("OK")){
				shopsResult.setStatus(1);
				shopsResult.setMsg("成功");
			}
				
		} catch (Exception e) {
			shopsResult.setStatus(0);
			shopsResult.setMsg("异常："+e.getMessage());
		}
		return shopsResult;
	}
	
	@Override
	public ShopsResult takeoutShopClose(String appAuthToken) {
		ShopsResult salmResult = new ShopsResult();
		try {
			RequestSysParams requestSysParams = new RequestSysParams(MeituanConstantUtils.signKey, appAuthToken);// 声明公共参数
			//设置门店休息
			CipCaterTakeoutPoiCloseRequest request = new CipCaterTakeoutPoiCloseRequest();
			request.setRequestSysParams(requestSysParams);
			String resultJson = request.doRequest();
			String data = MUtil.strObject(MUtil.jsonToMap(resultJson).get("data"));
			if(data != null && data.equalsIgnoreCase("OK")){
				salmResult.setStatus(1);
				salmResult.setMsg("成功");
			}
				
		} catch (Exception e) {
			salmResult.setStatus(0);
			salmResult.setMsg("异常："+e.getMessage());
		}
		return salmResult;
	}

	@Override
	public ShopsResult updateOpenTime(String appAuthToken, String openTime) {
		ShopsResult shopsResult = new ShopsResult();
		try {
			RequestSysParams requestSysParams = new RequestSysParams(MeituanConstantUtils.signKey, appAuthToken);// 声明公共参数
			//设置门店营业时间
			CipCaterTakeoutPoiOpenTimeUpdateRequest request = new CipCaterTakeoutPoiOpenTimeUpdateRequest();
			request.setRequestSysParams(requestSysParams);
			request.setOpenTime(openTime);
			String resultJson = request.doRequest();
			String data = MUtil.strObject(MUtil.jsonToMap(resultJson).get("data"));
			if(data != null && data.equalsIgnoreCase("OK")){
				shopsResult.setStatus(1);
				shopsResult.setMsg("成功");
			}
				
		} catch (Exception e) {
			shopsResult.setStatus(0);
			shopsResult.setMsg("异常："+e.getMessage());
		}
		return shopsResult;
	}
	
}
