package org.haier.meituan.service;

import java.util.Map;

import org.haier.ele.entity.SaleList;
import org.haier.shop.util.ShopsResult;

/**
 * 类名:com.palmshop.meituan.service.TakeoutOrderService;
 * 描述:外卖订单service
 * <AUTHOR>
 *
 */
public interface TakeoutOrderService {
	
	/**
	 * 添加一条销售信息
	 * @param shopUnique 商店唯一标识符
	 * @param orderMap 美团返回订单信息
	 * @return result
	 */
	public ShopsResult addSaleList(Long shopUnique,Map<String ,Object> orderMap);
	
	/**
	 * 获取商家未接单外卖订单列表
	 * @param shopUnique 商店唯一标示
	 * @return 
	 */
	public ShopsResult getSaleInfoList(String shopUnique); 

	/**
	 * 商家确认订单
	 * @param saleListUnique 订单编号
	 * @param takeoutAppAuthToken 美团外卖门店映射绑定授权token
	 * @return 
	 */
	public ShopsResult confirmOrder(String saleListUnique,String takeoutAppAuthToken); 
	
	/**
	 * 商家取消订单
	 * @param saleListUnique 订单编号
	 * @param takeoutAppAuthToken 美团外卖门店映射绑定授权token
	 * @param reasonCode 取消原因码 
	 * @param reason 取消原因
	 * @return 
	 */
	public ShopsResult cancelOrder(String saleListUnique,String takeoutAppAuthToken,String reasonCode,String reason); 
	
	/**
	 * 商家发起自配送
	 * @param saleListUnique 订单编号
	 * @param takeoutAppAuthToken 美团外卖门店映射绑定授权token
	 * @param courierName 取消原因码 
	 * @param courierPhone 取消原因
	 * @return 
	 */
	public ShopsResult delivering(String saleListUnique,String takeoutAppAuthToken,String courierName,String courierPhone); 
	
	/**
	 * 商家自配送--订单已送达
	 * @param saleListUnique 订单编号
	 * @param takeoutAppAuthToken 美团外卖门店映射绑定授权token
	 * @return 
	 */
	public ShopsResult delivered(String saleListUnique,String takeoutAppAuthToken); 
	
	/**
	 * 修改订单信息
	 * @param orderMap 美团返回订单信息
	 * @param saleListHandlestate 订单发货状态
	 * @return result
	 */
	public ShopsResult updateSaleList(Map<String ,Object> orderMap,int saleListHandlestate);
	
	/**
	 * 修改销售付款状态信息
	 * @param saleList:订单实体类
	 * @return int
	 */
	public ShopsResult updatSaleListState(SaleList saleList);
	
	/**
	 * 众包配送--查询配送费
	 * @param saleListUnique 订单编号
	 * @param takeoutAppAuthToken 美团外卖门店映射绑定授权token
	 * @return 
	 */	
	public ShopsResult queryZbShippingFee(String saleListUnique,String takeoutAppAuthToken); 
	
	/**
	 * 众包配送--预下单
	 * @param saleListUnique 订单编号
	 * @param takeoutAppAuthToken 美团外卖门店映射绑定授权token
	 * @param shippingFee 配送费，查询接口返回的配送费
	 * @param tipAmount  小费，不加小费输入0.0
	 * @return 
	 */	
	public ShopsResult prepareZbDispatch(String saleListUnique,String takeoutAppAuthToken,Double shippingFee,Double tipAmount); 
	
	/**
	 * 众包配送--确认下单
	 * @param saleListUnique 订单编号
	 * @param takeoutAppAuthToken 美团外卖门店映射绑定授权token
	 * @param tipAmount  小费，不加小费输入0.0
	 * @return 
	 */	
	public ShopsResult confirmZbDispatch(String saleListUnique,String takeoutAppAuthToken,Double tipAmount); 
	
	/**
	 * 众包配送--配送单加消费
	 * @param saleListUnique 订单编号
	 * @param takeoutAppAuthToken 美团外卖门店映射绑定授权token
	 * @param tipAmount  小费
	 * @return 
	 */	
	public ShopsResult updateZbDispatchTip(String saleListUnique,String takeoutAppAuthToken,Double tipAmount); 
	
	/**
	 * 取消美团众包配送和美团专送
	 * @param saleListUnique 订单编号
	 * @param takeoutAppAuthToken 美团外卖门店映射绑定授权token
	 * @return 
	 */	
	public ShopsResult cancelDispatch(String saleListUnique,String takeoutAppAuthToken); 
	
	/**
	 * 美团专送--发起配送
	 * @param saleListUnique 订单编号
	 * @param takeoutAppAuthToken 美团外卖门店映射绑定授权token
	 * @return 
	 */	
	public ShopsResult dispatchShip(String saleListUnique,String takeoutAppAuthToken); 
	
	/**
	 * 订单同意退款
	 * @param saleListUnique 订单编号
	 * @param takeoutAppAuthToken 美团外卖门店映射绑定授权token
	 * @param refundReason 原因
	 * @return 
	 */	
	public ShopsResult agreeRefund(String saleListUnique,String takeoutAppAuthToken,String refundReason); 
	
	/**
	 * 订单拒绝退款
	 * @param saleListUnique 订单编号
	 * @param takeoutAppAuthToken 美团外卖门店映射绑定授权token
	 * @param refundReason 原因
	 * @return 
	 */	
	public ShopsResult rejectRefund(String saleListUnique,String takeoutAppAuthToken,String refundReason); 
}
