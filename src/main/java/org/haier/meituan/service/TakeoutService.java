package org.haier.meituan.service;

import java.util.Map;

import org.haier.shop.util.ShopsResult;

/**
 * 类名：com.palmshop.meituan.service.TakeoutService;
 * 描述：美团外卖对接service
 * <AUTHOR>
 * @version v1.0
 *
 */
public interface TakeoutService {
	
	/**
	 * 修改店铺美团外卖绑定授权token
	 * @param takeoutAppAuthToken  授权token
	 * @param shopUnique 店铺唯一标识
	 * @return 
	 */
	public ShopsResult updateShopsTakeoutAppAuthToken(Map<String ,Object> params);
	
	/**
	 * 修改店铺营业状态
	 * @param shopOperatingStatus 营业状态：0，营业 1，休息 2，上线 3，下线
	 * @param shopUnique 店铺唯一标识
	 * @return 
	 */
	public ShopsResult updateShopOperatingStatus(Map<String ,Object> params);
}
