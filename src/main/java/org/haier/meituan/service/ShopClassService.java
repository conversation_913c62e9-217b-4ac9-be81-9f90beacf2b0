package org.haier.meituan.service;

import org.haier.shop.util.ShopsResult;

public interface ShopClassService {

	/**
	 * 获取门店美团外卖商品分类列表
	 * @param shop_unique 店铺编号
	 * @return
	 */
	public ShopsResult getShopClassList(String shop_unique);
	
	/**
	 * 外卖商品分类新增/修改
	 * @param appAuthToken 美团外卖门店映射绑定授权token
	 * @param oldCatName 原始分类名称，更新是必须
	 * @param catName 分类名称
	 * @param sequence 排序，创建是必须
	 * @return 
	 */	
	public ShopsResult saveShopClass(String appAuthToken,String oldCatName,String catName, String sequence);
	
	/**
	 * 外卖商品分类删除
	 * @param appAuthToken 美团外卖门店映射绑定授权token
	 * @param catName 分类名称
	 * @return 
	 */	
	public ShopsResult deleteShopClass(String appAuthToken,String catName);
}
