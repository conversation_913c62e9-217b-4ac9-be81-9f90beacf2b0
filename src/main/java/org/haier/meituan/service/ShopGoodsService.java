package org.haier.meituan.service;

import java.util.List;
import java.util.Map;

import org.haier.shop.util.ShopsResult;
public interface ShopGoodsService {

	/**
	 * 获取门店美团外卖商品列表
	 * @param shop_unique 店铺编号
	 * @param page 当前页数
	 * @return
	 */
	public ShopsResult getShopGoodsList(String shop_unique,String page);
	
	/**
	 * 外卖商品新增/修改
	 * @param appAuthToken 美团外卖门店映射绑定授权token
	 * @param ePoiId 门店唯一标示
	 * @param categoryName 商品分类
	 * @param description 商品描述
	 * @param dishName	商品名称
	 * @param EDishCode	商品唯一标示
	 * @param isSoldOut	是否售完	0-未售完，1-售完
	 * @param minOrderCount 最小购买数量	
	 * @param picture 图片id或地址
	 * @param sequence 分类下商品的顺序	
	 * @param price	商品价格
	 * @param unit 单位/规格
	 * @return 
	 */	
	public ShopsResult saveShopGoods(String appAuthToken,String ePoiId,String categoryName,String description,
			String dishName,String EDishCode,String isSoldOut,String minOrderCount,
			String picture,String sequence,String price,String unit);
	
	/**
	 * 外卖商品删除
	 * @param appAuthToken 美团外卖门店映射绑定授权token
	 * @param ePoiId 门店唯一标示
	 * @param eDishCode 商品唯一标识
	 * @return 
	 */	
	public ShopsResult deleteShopGoods(String appAuthToken,String ePoiId,String eDishCode);
	
	/**
	 * 获取收银系统未同步美团外卖的商品列表
	 * @param shop_unique 店铺唯一标示
	 * @return 
	 */	
	public List<Map<String ,Object>> getGoodsList(String shop_unique);
	
	/**
	 * 获取美团外卖商品分类列表
	 * @param appAuthToken 美团外卖门店映射绑定授权token
	 * @return 
	 */	
	public List<Map<String ,Object>> getClassList(String appAuthToken);
}
