package org.haier.meituanpeisong.service;

import java.util.Map;

import javax.servlet.http.HttpServletRequest;

import org.haier.shop.util.ShopsResult;

public interface PeisongOrderService {

	/**
	 * 配送订单创建，把订单发送给美团配送平台
	 * @param shop_unique 店铺编号
	 */
	public ShopsResult createOrder(String sale_list_unique,String goods_weight,String sale_list_cashier);
	
	/**
	 * 取消配送单
	 * @param sale_list_unique 订单编号
	 * @param cancel_reason 详细取消原因
	 */
	public ShopsResult deleteOrder(String sale_list_unique,String cancel_reason);
	
	/**
	 * 查询配送单
	 * @param sale_list_unique 订单编号
	 */
	public ShopsResult queryOrder(String sale_list_unique);
	
	/**
	 * 配送能力校验
	 * @param shop_unique 取货门店编号
	 * @param receiver_address 收件人地址
	 * @param receiver_lng 收件人经度
	 * @param receiver_lat 收件人维度
	 */
	public ShopsResult orderCheck(String shop_unique,String receiver_address,String receiver_lng,String receiver_lat);
	
	/**
	 * 获取骑手当前位置
	 * @param sale_list_unique 订单编号
	 * @param mt_peisong_id 美团配送内部订单 id
	 */
	public ShopsResult getRiderLocation(String sale_list_unique,String mt_peisong_id);
	
	/**
	 * 订单状态回调
	 */
	public String deliveryStatus(HttpServletRequest request);
	
	/**
	 * 订单异常回调
	 */
	public String abnormalDelivery(HttpServletRequest request);

	/**
	 * 评价骑手
	 * @param sale_list_unique 订单编号
	 * @param score 评分（5分制）
	 * @param comment_content 评价内容
	 */
	public ShopsResult evaluate(String sale_list_unique, int score,String comment_content);
	
	/**
	 * 商家自配送
	 * @param sale_list_unique 订单编号
	 * @param goods_weight 商品重量
	 * @param shop_courier_id 自配送商家快递员id
	 * @param courier_name 配送员姓名
	 * @param courier_phone 配送员电话
	 */
	public ShopsResult shopSelf(String sale_list_unique,String goods_weight,String shop_courier_id,String courier_name,String courier_phone,String sale_list_cashier);
	
	/**
	 * 获取商家自配送骑手列表
	 * @param shop_unique 店铺编号
	 * @return
	 */
	public ShopsResult shopCourierList(String shop_unique);
	
	/**
	 * 创建一刻钟配送
	 * @param sale_list_unique 订单编号
	 */
	public ShopsResult createDelivery(String sale_list_unique,String sale_list_cashier,Integer count);
	
	/**
	 * 取消商家自配送
	 * @param sale_list_unique 订单编号
	 */
	public ShopsResult cancelShopDelivery(String sale_list_unique);
	
	/**
	 * 取消一刻钟自配送
	 * @param sale_list_unique 订单编号
	 */
	public ShopsResult cancelYkzDelivery(String sale_list_unique);
	
	/**
	 *订单核实
	 * @param sale_list_unique 订单编号
	 * @param return_price 退还差价
	 * @param verify_staff_id 核单员工id
	 * @param goodsList 核实订单商品信息，json字符串
	 * 		 [
	 * 			{
	 * 				goods_barcode 商品编码
	 * 				goods_name 商品名称
	 * 				goods_count 商品数量
	 * 				goods_price 商品单价
	 * 				goods_subtotal 商品价格小计
	 * 			}
	 * 		]
	 * @return
	 */
	public ShopsResult verifyOrder(Map<String ,Object> params);
	
	/**
	 *订单核实
	 * @param sale_list_unique 订单编号
	 * @param return_price 退还差价
	 * @param verify_staff_id 核单员工id
	 * @param goodsList 核实订单商品信息，json字符串
	 * 		 [
	 * 			{
	 * 				goods_barcode 商品编码
	 * 				goods_name 商品名称
	 * 				goods_count 商品数量
	 * 				goods_price 商品单价
	 * 				goods_subtotal 商品价格小计
	 * 			}
	 * 		]
	 * @return
	 */
	public void verifyOrderNew(Map<String ,Object> params);
	
}
