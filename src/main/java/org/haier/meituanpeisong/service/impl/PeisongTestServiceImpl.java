package org.haier.meituanpeisong.service.impl;

import java.util.HashMap;
import java.util.Map;

import javax.annotation.Resource;

import org.haier.meituan.util.MUtil;
import org.haier.meituanpeisong.banma.constants.OpenApiConfig;
import org.haier.meituanpeisong.banma.request.QueryOrderRequest;
import org.haier.meituanpeisong.banma.sign.SignHelper;
import org.haier.meituanpeisong.banma.util.DateUtil;
import org.haier.meituanpeisong.banma.util.HttpClient;
import org.haier.meituanpeisong.banma.util.ParamBuilder;
import org.haier.meituanpeisong.dao.MeiTuanPeiSongDao;
import org.haier.meituanpeisong.service.PeisongTestService;
import org.haier.shop.util.ShopsResult;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Service
@Transactional
public class PeisongTestServiceImpl implements PeisongTestService{
	
	@Resource
	private MeiTuanPeiSongDao meiTuanPeiSongDao;
	
	@Override
	public ShopsResult test(String sale_list_unique,String url) {
		ShopsResult shopsResult = new ShopsResult();
		try {
			//获取配送单详情
			Map<String ,Object> params1 = new HashMap<String, Object>();
			params1.put("sale_list_unique", sale_list_unique);
			params1.put("delivery_type", 1);
			Map<String ,Object> deliveryInfo = meiTuanPeiSongDao.getDeliveryOrder(params1);
			
			 QueryOrderRequest request = new QueryOrderRequest();
		     request.setAppkey(OpenApiConfig.TEST_APP_KEY);
		     request.setTimestamp(DateUtil.unixTime());
		     request.setVersion("1.0");
		     request.setDeliveryId(Long.valueOf(sale_list_unique));
		     request.setMtPeisongId(MUtil.strObject(deliveryInfo.get("mt_peisong_id")));

	        Map<String, String> params = ParamBuilder.convertToMap(request);
	        String sign = SignHelper.generateSign(params, OpenApiConfig.TEST_SECRET);

	        params.put("sign", sign);

	        String res = HttpClient.post(url, params);
	        Map<String ,Object> resMap = MUtil.jsonToMap(res);
	        Integer code = (Integer) resMap.get("code");
			if(code != null && code == 0){
				shopsResult.setStatus(1);
				shopsResult.setMsg("成功");
			}else{
				shopsResult.setStatus(code);
				shopsResult.setMsg(MUtil.strObject(resMap.get("message")));
			}
		} catch (Exception e) {
			shopsResult.setStatus(0);
			shopsResult.setMsg("异常："+e.getMessage());		
		}
		return shopsResult;
	}
	
}
