package org.haier.meituanpeisong.service.impl;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;

import org.haier.meituan.util.MUtil;
import org.haier.meituanpeisong.banma.constants.CancelOrderReasonId;
import org.haier.meituanpeisong.banma.constants.OpenApiConfig;
import org.haier.meituanpeisong.banma.constants.RequestConstant;
import org.haier.meituanpeisong.banma.request.CancelOrderRequest;
import org.haier.meituanpeisong.banma.request.CheckRequest;
import org.haier.meituanpeisong.banma.request.CreateOrderByShopRequest;
import org.haier.meituanpeisong.banma.request.OrderRiderLocationRequest;
import org.haier.meituanpeisong.banma.request.QueryOrderRequest;
import org.haier.meituanpeisong.banma.sign.SignHelper;
import org.haier.meituanpeisong.banma.util.DateUtil;
import org.haier.meituanpeisong.banma.util.HttpClient;
import org.haier.meituanpeisong.banma.util.ParamBuilder;
import org.haier.meituanpeisong.banma.util.TencentMapUtil;
import org.haier.meituanpeisong.banma.vo.OpenApiGood;
import org.haier.meituanpeisong.banma.vo.OpenApiGoods;
import org.haier.meituanpeisong.dao.MeiTuanPeiSongDao;
import org.haier.meituanpeisong.service.PeisongOrderService;
import org.haier.shop.config.SysConfig;
import org.haier.shop.redis.RedisCache;
import org.haier.shop.util.ShopsResult;
import org.haier.shop.util.UtilForJAVA;
import org.haier.shop.util.mqtt.MqttxUtil;
import org.haier.store.util.HttpsUtil;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.alibaba.fastjson.JSON;


@Service
@Transactional
public class PeisongOrderServiceImpl implements PeisongOrderService{
	
	@Resource
	private MeiTuanPeiSongDao meiTuanPeiSongDao;
	
	@Resource
	private RedisCache redis;
	
	@Override
	public ShopsResult createOrder(String sale_list_unique,String goods_weight,String sale_list_cashier) {
		ShopsResult shopsResult = new ShopsResult();
		try {
			//获取订单消息
			Map<String ,Object> orderInfo = meiTuanPeiSongDao.getOrderInfo(sale_list_unique);
			
	        CreateOrderByShopRequest request = new CreateOrderByShopRequest();
	        request.setAppkey(OpenApiConfig.TEST_APP_KEY);
	        request.setTimestamp(DateUtil.unixTime());
	        request.setVersion("1.0");
	        // 设置订单号及配送服务标识
	        request.setDeliveryId(Long.valueOf(sale_list_unique));
	        request.setOrderId(sale_list_unique);
	        //设置配送服务编码
	        request.setDeliveryServiceCode(OpenApiConfig.DeliveryServiceCode);
	        // 设置测试门店 id，测试门店的坐标地址为 97235456,31065079（高德坐标），配送范围3km
	        //request.setShopId(MUtil.strObject(orderInfo.get("shop_unique")));
	        request.setShopId("test_0001 ");
	        // 设置取货人信息，请根据测试门店地址 在测试发单时合理设置送货地址
	        request.setReceiverName(MUtil.strObject(orderInfo.get("sale_list_name")));
	        request.setReceiverAddress(MUtil.strObject(orderInfo.get("sale_list_address")));
	        request.setReceiverPhone(MUtil.strObject(orderInfo.get("sale_list_phone")));
	        BigDecimal addr_longitude = (BigDecimal) orderInfo.get("addr_longitude");
	        BigDecimal addr_latitude = (BigDecimal) orderInfo.get("addr_latitude");
	        request.setReceiverLng(addr_longitude.multiply(new BigDecimal(1000000)).intValue());
	        request.setReceiverLat(addr_latitude.multiply(new BigDecimal(1000000)).intValue());
	        //设置预计送达时间为1小时以后
	        request.setExpectedDeliveryTime(DateUtil.unixTime() + 3600L);
	        // 设置商品重量，单位为kg
	        request.setGoodsWeight(new BigDecimal(goods_weight));
	        // 设置商品详情
	        OpenApiGoods openApiGoods = new OpenApiGoods();
	        //获取订单详情列表
	        List<OpenApiGood> orderDetailList = meiTuanPeiSongDao.getOrderDetailList(sale_list_unique);
	        openApiGoods.setGoods(orderDetailList);
	        request.setGoodsDetail(openApiGoods);
	        // 设置备注信息
	        request.setNote(MUtil.strObject(orderInfo.get("sale_list_remarks")));

	        Map<String, String> params = ParamBuilder.convertToMap(request);
	        String sign = SignHelper.generateSign(params, OpenApiConfig.TEST_SECRET);

	        params.put("sign", sign);

	        String res = HttpClient.post(RequestConstant.ORDER_CREATE_BY_SHOP, params);
	        Map<String ,Object> resMap = MUtil.jsonToMap(res);
	        Integer code = (Integer) resMap.get("code");
			if(code != null && code == 0){
				@SuppressWarnings("unchecked")
				Map<String ,Object> data = (Map<String, Object>) resMap.get("data");
				//本地创建配送订单
	        	Map<String ,Object> params1 = new HashMap<String, Object>();
	        	params1.put("sale_list_unique", sale_list_unique);
	        	params1.put("delivery_type", "1");
	        	params1.put("delivery_price", orderInfo.get("sale_list_delfee"));
	        	//商家配送费
	        	Long shop_unique = (Long) orderInfo.get("shop_unique");
	        	Double shop_delivery_price = getShopDeliveryPrice(String.valueOf(shop_unique), addr_longitude.doubleValue(), addr_latitude.doubleValue(), Double.valueOf(goods_weight));
	        	params1.put("shop_delivery_price", shop_delivery_price);
	        	params1.put("shop_subsidy_delfee", orderInfo.get("shop_subsidy_delfee"));
				Map<String ,Object> deliveryInfo = meiTuanPeiSongDao.getDeliveryOrder(params1);
	        	if(deliveryInfo == null){
	        		params1.put("delivery_weight", goods_weight);
		        	params1.put("mt_peisong_id", data.get("mt_peisong_id"));
		        	meiTuanPeiSongDao.addDeliveryOrder(params1);
	        	}else{
	        		params1.put("delivery_status", "0");
		        	params1.put("mt_peisong_id", data.get("mt_peisong_id"));
		        	meiTuanPeiSongDao.updateDeliveryOrder(params1);
	        	}
	        	
	        	//修改订单状态为7配送单待确认
	        	params1.put("sale_list_handlestate", 7);
	        	params1.put("sale_list_cashier", sale_list_cashier);
	        	meiTuanPeiSongDao.updateOrderStatus(params1);
				shopsResult.setStatus(1);
				shopsResult.setMsg("成功");
			}else{
				shopsResult.setStatus(code);
				shopsResult.setMsg(MUtil.strObject(resMap.get("message")));
			}
		} catch (Exception e) {
			shopsResult.setStatus(0);
			shopsResult.setMsg("异常："+e.getMessage());
		}
		return shopsResult;
	}

	@Override
	public ShopsResult deleteOrder(String sale_list_unique, String cancel_reason) {
		ShopsResult shopsResult = new ShopsResult();
		try {
			//获取配送单详情
			Map<String ,Object> params1 = new HashMap<String, Object>();
			params1.put("sale_list_unique", sale_list_unique);
			params1.put("delivery_type", 1);
			Map<String ,Object> deliveryInfo = meiTuanPeiSongDao.getDeliveryOrder(params1);
			
			CancelOrderRequest request = new CancelOrderRequest();
	        request.setAppkey(OpenApiConfig.TEST_APP_KEY);
	        request.setTimestamp(DateUtil.unixTime());
	        request.setVersion("1.0");
	        request.setDeliveryId(Long.valueOf(sale_list_unique));
	        request.setMtPeisongId(MUtil.strObject(deliveryInfo.get("mt_peisong_id")));
	        request.setCancelOrderReasonId(CancelOrderReasonId.PARTNER_REASON);
	        request.setCancelReason(cancel_reason);

	        Map<String, String> params = ParamBuilder.convertToMap(request);
	        String sign = SignHelper.generateSign(params, OpenApiConfig.TEST_SECRET);

	        params.put("sign", sign);

	        String res = HttpClient.post(RequestConstant.ORDER_CANCEL, params);
	        Map<String ,Object> resMap = MUtil.jsonToMap(res);
	        Integer code = (Integer) resMap.get("code");
			if(code != null && code == 0){
//				//本地修改配送订单状态为已取消
//	        	Map<String ,Object> params2 = new HashMap<String, Object>();
//	        	params2.put("sale_list_unique", sale_list_unique);
//	        	params2.put("delivery_type", "1");
//	        	params2.put("delivery_status", 7);
//	        	meiTuanPeiSongDao.updateDeliveryOrder(params2);
	        	
				shopsResult.setStatus(1);
				shopsResult.setMsg("成功");
			}else{
				shopsResult.setStatus(code);
				shopsResult.setMsg(MUtil.strObject(resMap.get("message")));
			}
		} catch (Exception e) {
			shopsResult.setStatus(0);
			shopsResult.setMsg("异常："+e.getMessage());		
		}
		return shopsResult;
	}
	
	@Override
	public ShopsResult queryOrder(String sale_list_unique) {
		ShopsResult shopsResult = new ShopsResult();
		try {
			//获取配送单详情
			Map<String ,Object> params1 = new HashMap<String, Object>();
			params1.put("sale_list_unique", sale_list_unique);
			params1.put("delivery_type", 1);
			Map<String ,Object> deliveryInfo = meiTuanPeiSongDao.getDeliveryOrder(params1);
			
			 QueryOrderRequest request = new QueryOrderRequest();
		     request.setAppkey(OpenApiConfig.TEST_APP_KEY);
		     request.setTimestamp(DateUtil.unixTime());
		     request.setVersion("1.0");
		     request.setDeliveryId(Long.valueOf(sale_list_unique));
		     request.setMtPeisongId(MUtil.strObject(deliveryInfo.get("mt_peisong_id")));

	        Map<String, String> params = ParamBuilder.convertToMap(request);
	        String sign = SignHelper.generateSign(params, OpenApiConfig.TEST_SECRET);

	        params.put("sign", sign);

	        String res = HttpClient.post(RequestConstant.ORDER_QUERY, params);
	        Map<String ,Object> resMap = MUtil.jsonToMap(res);
	        Integer code = (Integer) resMap.get("code");
			if(code != null && code == 0){
				Map<String ,Object> data = (Map<String, Object>) resMap.get("data");
				
				shopsResult.setData(data);
				shopsResult.setStatus(1);
				shopsResult.setMsg("成功");
			}else{
				shopsResult.setStatus(code);
				shopsResult.setMsg(MUtil.strObject(resMap.get("message")));
			}
		} catch (Exception e) {
			shopsResult.setStatus(0);
			shopsResult.setMsg("异常："+e.getMessage());		
		}
		return shopsResult;
	}
	
	@Override
	public String deliveryStatus(HttpServletRequest request) {
		Map<String ,Object> resultjson = new HashMap<String, Object>();
		try {
			String delivery_id = request.getParameter("delivery_id");
			String mt_peisong_id = request.getParameter("mt_peisong_id");
			String order_id = request.getParameter("order_id");
			String status = request.getParameter("status");
			String courier_name = request.getParameter("courier_name");
			String courier_phone = request.getParameter("courier_phone");
			String cancel_reason_id = request.getParameter("cancel_reason_id");
			String cancel_reason = request.getParameter("cancel_reason");
			String timestamp = request.getParameter("timestamp");
			String sign = request.getParameter("sign");
			
			Map<String, String> params = new HashMap<String, String>();
			params.put("appkey", OpenApiConfig.TEST_APP_KEY);
			params.put("timestamp", timestamp);
			
			params.put("delivery_id", delivery_id);
			params.put("mt_peisong_id", mt_peisong_id);
			params.put("order_id", order_id);
			params.put("status", status);
			params.put("courier_name", courier_name);
			params.put("courier_phone", courier_phone);
			params.put("cancel_reason_id", cancel_reason_id);
			params.put("cancel_reason", cancel_reason);
			
		    // 生成签名
		    String new_sign = SignHelper.generateSign(params, OpenApiConfig.TEST_SECRET);
		    if(new_sign.equals(sign)){//验证签名通过
		    	Map<String ,Object> params1 = new HashMap<String, Object>();
		    	params1.put("sale_list_unique", delivery_id);
		    	params1.put("delivery_type", "1");
		    	if(status.equals("0")){//待调度
		    		params1.put("delivery_status", 1);//系统已接单
		    	}else if(status.equals("20")){//已接单
		    		params1.put("delivery_status", 2);//已分配骑手
		    		params1.put("driver_name", courier_name);
		    		params1.put("driver_phone", courier_phone);
		    		//修改订单状态为3待收货
		        	params1.put("sale_list_handlestate", 3);
		        	meiTuanPeiSongDao.updateOrderStatus(params1);
		    	}else if(status.equals("30")){//已取货
		    		params1.put("delivery_status", 4);//配送中
		    		//修改订单状态为3待收货
		        	params1.put("sale_list_handlestate", 3);
		        	meiTuanPeiSongDao.updateOrderStatus(params1);
		    	}else if(status.equals("50")){//已送达
		    		params1.put("delivery_status", 5);//已送达
		    		//确认自提和确认收货接口
		    		HttpsUtil.sendGet("http://buyhoo.cc/goBuy/my/confirmReceipt.do", "sale_list_unique="+delivery_id);
		    	}else if(status.equals("99")){//已取消
		    		params1.put("delivery_status", 7);
		    		//修改订单状态为10配送异常
		        	params1.put("sale_list_handlestate", 10);
		        	params1.put("delivery_error", "美团配送单取消");
		        	meiTuanPeiSongDao.updateOrderStatus(params1);
		    	}
		    	//修改配送单状态
		    	meiTuanPeiSongDao.updateDeliveryOrder(params1);
		    	
		    	resultjson.put("code", 0);
		    }else{//验证签名失败
		    	throw new Exception("签名验证不通过");
		    }
		} catch (Exception e) {
			e.printStackTrace();
		}
		return JSON.toJSONString(resultjson);
	}
	
	@Override
	public String abnormalDelivery(HttpServletRequest request) {
		Map<String ,Object> resultjson = new HashMap<String, Object>();
		try {
			String delivery_id = request.getParameter("delivery_id");
			String mt_peisong_id = request.getParameter("mt_peisong_id");
			String order_id = request.getParameter("order_id");
			String exception_id = request.getParameter("exception_id");
			String exception_code = request.getParameter("exception_code");
			String exception_descr = request.getParameter("exception_descr");
			String exception_time = request.getParameter("exception_time");
			String courier_name = request.getParameter("courier_name");
			String courier_phone = request.getParameter("courier_phone");
			String timestamp = request.getParameter("timestamp");
			String sign = request.getParameter("sign");
			
			Map<String, String> params = new HashMap<String, String>();
			params.put("appkey", OpenApiConfig.TEST_APP_KEY);
			params.put("timestamp", timestamp);
			params.put("version", "1.0");
			
			params.put("delivery_id", delivery_id);
			params.put("mt_peisong_id", mt_peisong_id);
			params.put("order_id", order_id);
			params.put("exception_id", exception_id);
			params.put("exception_code", exception_code);
			params.put("exception_descr", exception_descr);
			params.put("exception_time", exception_time);
			params.put("courier_name", courier_name);
			params.put("courier_phone", courier_phone);
			
		    // 生成签名
		    String new_sign = SignHelper.generateSign(params, OpenApiConfig.TEST_SECRET);
		    if(new_sign.equals(sign)){//验证签名通过
		    	Map<String ,Object> params1 = new HashMap<String, Object>();
		    	params1.put("sale_list_unique", delivery_id);
		    	params1.put("delivery_type", "1");
		    	params1.put("delivery_status", 6);
		    	params1.put("abnormal_desc", exception_descr);
		    	//修改配送单状态
		    	meiTuanPeiSongDao.updateDeliveryOrder(params1);
		    	//修改订单状态为10配送异常
	        	params1.put("sale_list_handlestate", 10);
	        	params1.put("delivery_error", exception_descr);
	        	meiTuanPeiSongDao.updateOrderStatus(params1);
		    	resultjson.put("code", 0);
		    }else{//验证签名失败
		    	throw new Exception("签名验证不通过");
		    }
		} catch (Exception e) {
			e.printStackTrace();
		}
		return JSON.toJSONString(resultjson);
	}

	@Override
	public ShopsResult orderCheck(String shop_unique, String receiver_address,String receiver_lng, String receiver_lat) {
		ShopsResult shopsResult = new ShopsResult();
		try {
			CheckRequest request = new CheckRequest();
			request.setAppkey(OpenApiConfig.TEST_APP_KEY);
			request.setTimestamp(DateUtil.unixTime());
			request.setVersion("1.0");
			/**
			 * 设置配送服务编码
			 *
			 * 光速达:4001
			 * 快速达:4011
			 * 及时达:4012
			 * 集中送:4013
			 * 当天达:4021
			 */
			request.setDeliveryServiceCode(4001);
			request.setShopId(shop_unique);
			request.setReceiverAddress(receiver_address);
			Double lng = Double.valueOf(receiver_lng)*1000000;
			Double lat = Double.valueOf(receiver_lat)*1000000;
			request.setReceiverLng(lng.intValue());
			request.setReceiverLat(lat.intValue());
			request.setCheckType(1);
			request.setMockOrderTime(new Date().getTime());

			Map<String, String> params = ParamBuilder.convertToMap(request);
			String sign = SignHelper.generateSign(params, OpenApiConfig.TEST_SECRET);

			params.put("sign", sign);

	        String res = HttpClient.post(RequestConstant.ORDER_CHECK_DELIVERY_ABILITY, params);
	        Map<String ,Object> resMap = MUtil.jsonToMap(res);
	        Integer code = (Integer) resMap.get("code");
			if(code != null && code == 0){
				shopsResult.setStatus(1);
				shopsResult.setMsg("成功");
			}else{
				shopsResult.setStatus(code);
				shopsResult.setMsg(MUtil.strObject(resMap.get("message")));
			}
		} catch (Exception e) {
			shopsResult.setStatus(0);
			shopsResult.setMsg("异常："+e.getMessage());		
		}
		return shopsResult;
	}

	@Override
	public ShopsResult getRiderLocation(String sale_list_unique,String mt_peisong_id) {
		ShopsResult shopsResult = new ShopsResult();
		try {
			OrderRiderLocationRequest request = new OrderRiderLocationRequest();
			request.setAppkey(OpenApiConfig.TEST_APP_KEY);
			request.setTimestamp(DateUtil.unixTime());
			request.setVersion("1.0");
			request.setDeliveryId(Long.valueOf(sale_list_unique));
			request.setMtPeisongId(mt_peisong_id);

			Map<String, String> params = ParamBuilder.convertToMap(request);
			String sign = SignHelper.generateSign(params, OpenApiConfig.TEST_SECRET);

			params.put("sign", sign);

	        String res = HttpClient.post(RequestConstant.RIDER_LOCATION, params);
	        Map<String ,Object> resMap = MUtil.jsonToMap(res);
	        Integer code = (Integer) resMap.get("code");
			if(code != null && code == 0){
				Map<String ,Object> data = (Map<String, Object>) resMap.get("data");
				
				shopsResult.setData(data);
				shopsResult.setStatus(1);
				shopsResult.setMsg("成功");
			}else{
				shopsResult.setStatus(code);
				shopsResult.setMsg(MUtil.strObject(resMap.get("message")));
			}
		} catch (Exception e) {
			shopsResult.setStatus(0);
			shopsResult.setMsg("异常："+e.getMessage());		
		}
		return shopsResult;
	}

	@Override
	public ShopsResult evaluate(String sale_list_unique, int score,String comment_content) {

		return null;
	}
	
	//获取美团配送费用
	public Double getShopDeliveryPrice(String shop_unique, Double addr_longitude, Double addr_latitude,Double goods_weight){
		Double shop_delivery_price = 0.00;
		//获取店铺配送信息
		Map<String ,Object> shopInfo = meiTuanPeiSongDao.getShopInfo(shop_unique);
		Double shop_longitude = Double.valueOf(shopInfo.get("shop_longitude").toString());
		Double shop_latitude = Double.valueOf(shopInfo.get("shop_latitude").toString());
		        
			//美团配送距离根据腾讯步行导航距离计算
			//1、将商铺地址经纬度和收货地址经纬度转换为腾讯地图坐标系
			//店铺地址经纬度转换
			String shoplocations = shop_latitude+","+shop_longitude;
			Map<String ,Object> shopAddMap = HttpsUtil.sendGet(TencentMapUtil.translate, "locations="+shoplocations+"&type=5&key="+TencentMapUtil.key);
			if((Integer) shopAddMap.get("status") == 0){
				List<Map<String ,Object>> shopLocations = (List<Map<String, Object>>) shopAddMap.get("locations");
				shop_longitude = Double.valueOf(shopLocations.get(0).get("lng").toString());
				shop_latitude = Double.valueOf(shopLocations.get(0).get("lat").toString());
			}
			
			//收货地址经纬度转换
			String addlocations = addr_latitude+","+addr_longitude;
			Map<String ,Object> addMap = HttpsUtil.sendGet(TencentMapUtil.translate, "locations="+addlocations+"&type=5&key="+TencentMapUtil.key);
			if((Integer) addMap.get("status") == 0){
				List<Map<String ,Object>> addLocations = (List<Map<String, Object>>) shopAddMap.get("locations");
				addr_longitude = Double.valueOf(addLocations.get(0).get("lng").toString());
				addr_latitude = Double.valueOf(addLocations.get(0).get("lat").toString());
			}
			//2、计算两点之间步行导航距离
			Double distance = 0.00;
			String from = shop_latitude+","+shop_longitude;
			String to = addr_latitude+","+addr_longitude;
			Map<String ,Object> distanceMap = HttpsUtil.sendGet(TencentMapUtil.distance, "mode=walking&from="+from+"&to="+to+"&key="+TencentMapUtil.key);
			if((Integer) distanceMap.get("status") == 0){
				Map<String ,Object> resultMap = (Map<String, Object>) distanceMap.get("result");
				List<Map<String ,Object>> elements = (List<Map<String, Object>>) resultMap.get("elements");
				distance = Double.valueOf(elements.get(0).get("distance").toString());
			}
			
			//3、计算配送费
			if(distance != null && distance != -1){
				shop_delivery_price = TencentMapUtil.getDeliverPrice(distance, goods_weight);
			}
		return shop_delivery_price;
	}

	@Override
	public ShopsResult shopSelf(String sale_list_unique, String goods_weight,String shop_courier_id,String courier_name,String courier_phone,String sale_list_cashier) {
		ShopsResult shopsResult = new ShopsResult();
		try {
			//获取订单消息
			Map<String ,Object> orderInfo = meiTuanPeiSongDao.getOrderInfo(sale_list_unique);
			String shopUnique = orderInfo.get("shop_unique").toString();
		    //本地创建配送订单
	        Map<String ,Object> params1 = new HashMap<String, Object>();
	        params1.put("sale_list_unique", sale_list_unique);
	        params1.put("delivery_type", "0");//商家自配送
	        params1.put("delivery_weight", goods_weight);
	        params1.put("delivery_price", orderInfo.get("sale_list_delfee"));
	        params1.put("shop_delivery_price", 0.00);
	        params1.put("shop_subsidy_delfee", 0.00);
	        params1.put("delivery_status", "4");
	        params1.put("driver_name", courier_name);
	        params1.put("driver_phone", courier_phone);
	        params1.put("shop_courier_id", shop_courier_id);
			Map<String ,Object> deliveryInfo = meiTuanPeiSongDao.getDeliveryOrder(params1);
	        if(deliveryInfo == null){
		        meiTuanPeiSongDao.addDeliveryOrder(params1);
	        }else{
		        meiTuanPeiSongDao.updateDeliveryOrder(params1);
	        }
	        	
	        //修改订单状态为3待收货
	        Map<String ,Object> params = new HashMap<String, Object>();
	        params.put("sale_list_unique", sale_list_unique);
	        params.put("delivery_type", 0);//商家自配送
	        params.put("sale_list_handlestate", 3);
	        params.put("sale_list_cashier", sale_list_cashier);
	        meiTuanPeiSongDao.updateOrderStatus(params);
			shopsResult.setStatus(1);
			shopsResult.setMsg("成功");
			
			sendMsgToPc(shopUnique, sale_list_unique, 3);
			
		} catch (Exception e) {
			shopsResult.setStatus(0);
			shopsResult.setMsg("异常："+e.getMessage());		
		}
		return shopsResult;
	}
	
	/**
	 * 订单状态修改后，向收银机发送订单修改通知
	 * @param shopUnique
	 * @param saleListUnique
	 * @param saleListHandlestate
	 */
	public void sendMsgToPc(String shopUnique, String saleListUnique, Integer saleListHandlestate) {
		//向收银设备推送消息
		Map<String,Object> mqttMap = new HashMap<String,Object>();
		mqttMap.put("ctrl", "msg_order_change");
		mqttMap.put("shopUnique", shopUnique);
		Map<String,Object> detailMap = new HashMap<String,Object>();
		detailMap.put("saleListUnique", saleListUnique);
		detailMap.put("saleListHandlestate", saleListHandlestate);
		
		mqttMap.put("status", 200);
		mqttMap.put("errcode", 0);
		mqttMap.put("msg", "订单状态修改");
		
		//获取各个状态的订单数量
		List<Map<String, Object>> countList = queryOrderCount(shopUnique);
		detailMap.put("orderCount", countList);
		mqttMap.put("data", detailMap);
		if(null != redis.getObject("topic_" + shopUnique)) {
			//缓存信息不为空
			List<String> idList = (List<String>)redis.getObject("topic_" + shopUnique);
			for(String ID : idList) {
				MqttxUtil.sendMapMsg(mqttMap, ID);
			}
		}
	}
	
	public List<Map<String,Object>> queryOrderCount(String shopUnique){
		
		Map<String,Object> parMap = new HashMap<String,Object>();
		parMap.put("shopUnique", shopUnique);
		List<Map<String,Object>> data = meiTuanPeiSongDao.shopsSaleListCount(parMap);
		if(null == data) {
			data = new ArrayList<Map<String,Object>>();
		}
		
		Integer retAllCount = 0;
		Map<String,Object> all_count = meiTuanPeiSongDao.shopsSaleListCountAll(parMap);
		List<Map<String,Object>> retList = meiTuanPeiSongDao.queryReturnListCount(parMap);
		
		Map<String,Object> tmp = new HashMap<String,Object>();
		tmp.put("handleState", "-5");
		tmp.put("count", retAllCount);
		retList.add(tmp);
		
		data.add(all_count);
		data.addAll(retList);
		
		return data;
	}


	@Override
	public ShopsResult shopCourierList(String shop_unique) {
		ShopsResult shopsResult = new ShopsResult();
		try {
			List<Map<String ,Object>> list = meiTuanPeiSongDao.shopCourierList(shop_unique);
			
			shopsResult.setData(list);
			shopsResult.setStatus(1);
			shopsResult.setMsg("成功");
		} catch (Exception e) {
			shopsResult.setStatus(0);
			shopsResult.setMsg("异常："+e.getMessage());		
		}
		return shopsResult;
	}
	
	@Override
	public ShopsResult createDelivery(String sale_list_unique,String sale_list_cashier,Integer count) {
		ShopsResult shopsResult = new ShopsResult();
		try {
			if(count == null){
				count = 1;
			}
			if (count > 1) {
				//如果不是第一次调用，说明前面有失败，延时处理一下
				Thread.sleep(1000);
			}
			if (count > 5) {
				return ShopsResult.fail("系统处理中，请稍后");
			}
			//获取订单消息
			Map<String ,Object> orderInfo = meiTuanPeiSongDao.getOrderInfo(sale_list_unique);
			
			String orderNum = MUtil.strObject(orderInfo.get("sale_list_unique"));//订单编号
			String shopUnique = MUtil.strObject(orderInfo.get("shop_unique"));//店铺唯一标识
			String shopName = MUtil.strObject(orderInfo.get("shop_name"));//店铺名称
			String shopPhone = MUtil.strObject(orderInfo.get("shop_phone"));//店铺联系方式
			String shopAddress = MUtil.strObject(orderInfo.get("shop_address_detail"));//店铺地址
			String shopLatitude = MUtil.strObject(orderInfo.get("shop_latitude"));//店铺纬度
			String shopLongitude = MUtil.strObject(orderInfo.get("shop_longitude"));//店铺经度
			String shippingAddress = MUtil.strObject(orderInfo.get("sale_list_address"));//送货地址
			String shippingLatitude = MUtil.strObject(orderInfo.get("addr_latitude"));//送货纬度
			String shippingLongitude = MUtil.strObject(orderInfo.get("addr_longitude"));//送货经度
			String payPrice = MUtil.strObject(orderInfo.get("sale_list_total"));//订单支付金额
			String orderTime = MUtil.strObject(orderInfo.get("sale_list_datetime"));//下单时间 yyyy-MM-dd HH:mm:ss
			String contactName = MUtil.strObject(orderInfo.get("sale_list_name"));//联系人姓名
			String contactPhone = MUtil.strObject(orderInfo.get("sale_list_phone"));//联系电话
			String remark = MUtil.strObject(orderInfo.get("sale_list_remarks"));//订单备注
			String subsidyDeliveryPrice = MUtil.strObject(orderInfo.get("subsidy_delivery_price"));//商家每单补贴配送费
			
			//调用一刻钟接口
			String paramsStr = "orderNum="+orderNum
					+"&shopUnique="+shopUnique
					+"&shopName="+shopName
					+"&shopPhone="+shopPhone
					+"&shopAddress="+shopAddress
					+"&shopLatitude="+shopLatitude
					+"&shopLongitude="+shopLongitude
					+"&shippingAddress="+shippingAddress
					+"&shippingLatitude="+shippingLatitude
					+"&shippingLongitude="+shippingLongitude
					+"&payPrice="+payPrice
					+"&orderTime="+orderTime
					+"&contactName="+contactName
					+"&contactPhone="+contactPhone
					+"&remark="+remark
					+"&subsidyDeliveryPrice="+subsidyDeliveryPrice;
			System.out.println("/createDelivery接口参数：" + paramsStr);
			System.out.println("/createDelivery接口：" + SysConfig.DELIVERYCREATEORDER);
			Map<String ,Object> resultMap = HttpsUtil.sendPost(SysConfig.DELIVERYCREATEORDER, paramsStr);
			String returnCode = MUtil.strObject(resultMap.get("returnCode"));
			if(returnCode.equals("SUCCESS")){
				Map<String ,Object> obj = (Map<String, Object>) resultMap.get("obj");
				String deliveryStatus = MUtil.strObject(obj.get("deliveryStatus"));
				if(deliveryStatus.equals("0") || deliveryStatus.equals("6") || deliveryStatus.equals("5")){//系统已接单||异常订单||配送单已取消
					//本地创建配送订单
		        	Map<String ,Object> params1 = new HashMap<String, Object>();
		        	params1.put("sale_list_unique", sale_list_unique);
		        	params1.put("delivery_type", "2");//一刻钟配送
		        	params1.put("delivery_price", orderInfo.get("sale_list_delfee"));
		        	//商家配送费
		        	Double shop_delivery_price = Double.valueOf(MUtil.strObject(obj.get("shopDeliveryPrice")));
		        	params1.put("shop_delivery_price", shop_delivery_price);	
		        	params1.put("shop_subsidy_delfee", orderInfo.get("shop_subsidy_delfee"));
					Map<String ,Object> deliveryInfo = meiTuanPeiSongDao.getDeliveryOrder(params1);
		        	if(deliveryInfo == null){
		        		params1.put("delivery_weight", 0.00);
			        	params1.put("mt_peisong_id", "");
			        	meiTuanPeiSongDao.addDeliveryOrder(params1);
		        	}else{
		        		params1.put("delivery_status", "0");
			        	meiTuanPeiSongDao.updateDeliveryOrder(params1);
		        	}
		        	
		        	//修改订单状态为7配送单待确认
		        	params1.put("sale_list_handlestate", 7);
		        	params1.put("sale_list_cashier", sale_list_cashier);
		        	meiTuanPeiSongDao.updateOrderStatus(params1);
				}
				shopsResult.setStatus(1);
				shopsResult.setMsg("成功");
				
				//发送MQTT消息
				sendMsgToPc(shopUnique, sale_list_unique, 7);
			}else{
				return ShopsResult.fail(resultMap.get("msg").toString());
			}
			
		} catch (Exception e) {
			e.printStackTrace();
			shopsResult.setStatus(0);
			shopsResult.setMsg("异常："+e.getMessage());
			System.out.println(sale_list_unique + "第" + count + "次调用异常");
			return createDelivery(sale_list_unique, sale_list_cashier, ++ count);
		}
		return ShopsResult.ok();
	}
	
	@Override
	public ShopsResult cancelShopDelivery(String sale_list_unique) {
		ShopsResult shopsResult = new ShopsResult();
		try {
			//修改订单状态为待发货
        	Map<String ,Object> params = new HashMap<String, Object>();
        	params.put("sale_list_unique", sale_list_unique);
        	params.put("sale_list_handlestate", 2);
        	meiTuanPeiSongDao.updateOrderStatus(params);
			
			//修改配送订单状态为已取消
        	Map<String ,Object> params2 = new HashMap<String, Object>();
        	params2.put("sale_list_unique", sale_list_unique);
        	params2.put("delivery_type", "0");
        	params2.put("delivery_status", 7);
        	meiTuanPeiSongDao.updateDeliveryOrder(params2);
        	
			shopsResult.setStatus(1);
			shopsResult.setMsg("成功");
		} catch (Exception e) {
			shopsResult.setStatus(0);
			shopsResult.setMsg("异常："+e.getMessage());		
		}
		return shopsResult;
	}
	
	@Override
	public ShopsResult cancelYkzDelivery(String sale_list_unique) {
		ShopsResult shopsResult = new ShopsResult();
		try {
			String paramsStr = "orderNum="+sale_list_unique;
			Map<String ,Object> resultMap = HttpsUtil.sendPost(SysConfig.ykz_cancelDelivery, paramsStr);
			String returnCode = MUtil.strObject(resultMap.get("returnCode"));
			if(returnCode.equals("SUCCESS")){
		        shopsResult.setStatus(1);
				shopsResult.setMsg("成功");
			}else{
				String delivery_status = MUtil.strObject(resultMap.get("obj"));
				shopsResult.setData(delivery_status);
				shopsResult.setStatus(0);
				shopsResult.setMsg(MUtil.strObject(resultMap.get("msg")));	
			}
		} catch (Exception e) {
			shopsResult.setStatus(0);
			shopsResult.setMsg("异常："+e.getMessage());		
		}
		return shopsResult;
	}

	@Override
	public ShopsResult verifyOrder(Map<String, Object> params) {
		ShopsResult shopsResult = new ShopsResult();
		try {
			String sale_list_unique = MUtil.strObject(params.get("sale_list_unique"));
			Double return_price = Double.valueOf(MUtil.strObject(params.get("return_price")));
			String verify_staff_id = MUtil.strObject(params.get("verify_staff_id"));
			String goodsList = MUtil.strObject(params.get("goodsList"));
					
			//获取订单消息
			Map<String ,Object> orderInfo = meiTuanPeiSongDao.getOrderInfo(sale_list_unique);
			String sale_list_handlestate = MUtil.strObject(orderInfo.get("sale_list_handlestate"));
			if(sale_list_handlestate != null && !sale_list_handlestate.equals("2")) {
				shopsResult.setStatus(2);
				shopsResult.setMsg("订单状态不是未发货状态");
				
				return shopsResult;
			}
			
	        //修改订单状态为11-已核单未发货
	        Map<String ,Object> statusParams = new HashMap<String, Object>();
	        statusParams.put("sale_list_unique", sale_list_unique);
//	        statusParams.put("sale_list_handlestate", 11);
	        statusParams.put("return_price", return_price);
	        statusParams.put("verify_staff_id", verify_staff_id);
	        meiTuanPeiSongDao.updateOrderStatus(statusParams);
	        
	        //添加订单核实信息
	        List<Map<String ,Object>> list = MUtil.strToList(goodsList);
	        for(int i=0;i<list.size();i++) {
	        	list.get(i).put("sale_list_unique", sale_list_unique);
	        }
	        meiTuanPeiSongDao.addSaleListVerify(list);
	        
	        //修改会员余额
	        if(return_price > 0.00) {
	        	String cus_unique = MUtil.strObject(orderInfo.get("cus_unique"));
	        	Map<String ,Object> cusParams = new HashMap<String, Object>();
	        	cusParams.put("cus_unique", cus_unique);
	        	cusParams.put("return_price", return_price);
	        	meiTuanPeiSongDao.updateCusAmount(cusParams);
	        	
	        	//添加会员余额交易流水
	        	Map<String ,Object> rechargeParams = new HashMap<String, Object>();
	        	rechargeParams.put("shop_unique", orderInfo.get("orderInfo"));
	        	rechargeParams.put("cus_unique", cus_unique);
	        	rechargeParams.put("money_type", 1);
	        	rechargeParams.put("sale_type", 11);
	        	rechargeParams.put("money", return_price);
	        	rechargeParams.put("sale_list_unique", sale_list_unique);
	        	meiTuanPeiSongDao.addCustomerRecharge(rechargeParams);
	        }
	        
			shopsResult.setStatus(1);
			shopsResult.setMsg("成功");
		} catch (Exception e) {
			shopsResult.setStatus(0);
			shopsResult.setMsg("异常："+e.getMessage());		
		}
		return shopsResult;
	}
	
	@Override
	@Transactional(timeout=30000)
	public void verifyOrderNew(Map<String, Object> params) {
		try {
			String sale_list_unique = MUtil.strObject(params.get("sale_list_unique"));
			Double return_price = Double.valueOf(MUtil.strObject(params.get("return_price")));
			String verify_staff_id = MUtil.strObject(params.get("verify_staff_id"));
			String goodsList = MUtil.strObject(params.get("goodsList"));
					
			//获取订单消息
			Map<String ,Object> orderInfo = meiTuanPeiSongDao.getOrderInfo(sale_list_unique);
			
	        //修改订单信息
	        Map<String ,Object> statusParams = new HashMap<String, Object>();
	        statusParams.put("sale_list_unique", sale_list_unique);
//	        statusParams.put("sale_list_handlestate", 11);
	        statusParams.put("return_price", return_price);
	        statusParams.put("verify_staff_id", verify_staff_id);
	        meiTuanPeiSongDao.updateOrderStatus(statusParams);
	        
	        //添加订单核实信息
	        List<Map<String ,Object>> list = MUtil.strToList(goodsList);
	        for(int i=0;i<list.size();i++) {
	        	list.get(i).put("sale_list_unique", sale_list_unique);
	        }
	        meiTuanPeiSongDao.addSaleListVerify(list);
	        
	        //修改会员余额
	        if(return_price > 0.00) {
	        	String cus_unique = MUtil.strObject(orderInfo.get("cus_unique"));
	        	Map<String ,Object> cusParams = new HashMap<String, Object>();
	        	cusParams.put("cus_unique", cus_unique);
	        	cusParams.put("return_price", return_price);
	        	meiTuanPeiSongDao.updateCusAmount(cusParams);
	        	
	        	//添加会员余额交易流水
	        	Map<String ,Object> rechargeParams = new HashMap<String, Object>();
	        	rechargeParams.put("shop_unique", orderInfo.get("shop_unique"));
	        	rechargeParams.put("cus_unique", cus_unique);
	        	rechargeParams.put("money_type", 1);
	        	rechargeParams.put("sale_type", 11);
	        	rechargeParams.put("money", return_price);
	        	rechargeParams.put("sale_list_unique", sale_list_unique);
	        	meiTuanPeiSongDao.addCustomerRecharge(rechargeParams);
	        }
	        
	        //查询团长佣金信息
	        List<Map<String,Object>> disList = meiTuanPeiSongDao.getOrderDisMsg(sale_list_unique);
	        System.out.println("团长订单信息核单");
	        System.out.println(disList);
	        if(null != disList && ! disList.isEmpty()) {
	        	//获取退单前后的商品信息
	        	List<Map<String,Object>> proList = meiTuanPeiSongDao.getOrderCheckMsg(sale_list_unique);
	        	Double sub_trading_amount = 0.00;
	        	if(null != proList && !proList.isEmpty()) {
	        		//修改详情记录
	        		System.out.println("核单后的信息");
	        		System.out.println(proList);
	        		meiTuanPeiSongDao.modifyCaptainProfit(proList);
	        		//修改总的记录
	        		for(Integer i = 0 ; i < proList.size() ;i ++) {
	        			sub_trading_amount = UtilForJAVA.addDouble(sub_trading_amount, proList.get(i).get("sub_trading_amount"), 6);
	        		}
	        	}
	        	if(sub_trading_amount != 0) {
	        		Map<String,Object> subMap = new HashMap<String,Object>();
	        		subMap.put("trading_amount", -sub_trading_amount);
	        		subMap.put("cus_unique",disList.get(0).get("cus_unique"));
	        		subMap.put("shop_unique", disList.get(0).get("shop_unique"));
	        		//修改店内待收益的信息
	        		
	        		meiTuanPeiSongDao.modifyCaptionProfitR(subMap);
	        	}
	        }
	        
		} catch (Exception e) {
			e.printStackTrace();
		}
	}
}
