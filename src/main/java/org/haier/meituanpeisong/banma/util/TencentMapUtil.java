package org.haier.meituanpeisong.banma.util;

import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Date;

public class TencentMapUtil {
	//开发密钥（Key）
	public static String key = "CDFBZ-TCAKU-ZJJVO-2LHNY-HBFVV-DTBNA";
	
	//坐标转换地址
	public static String translate = "https://apis.map.qq.com/ws/coord/v1/translate";
	
	//距离结算
	public static String distance = "https://apis.map.qq.com/ws/distance/v1/";
	
	/**
	 * 计算规则
	 * @param distance 距离m
	 * @param goods_weight 商品重量kg
	 */
	public static Double getDeliverPrice(Double distance,Double goods_weight){
		Double deliverPrice = 0.00;
		
		Double startingPrice = 4.5;//起步价
		
		//距离加价：（0-1KM]：不加价   （1KM-3KM]：1.0元/km  （3KM-5KM]：2.0元/km （5KM-7KM]：3.0元/km
		distance = distance/1000;
		Double distancePrice = 0.00;
		if(distance > 1 && distance <= 3){
			distancePrice = (distance-1)*1.0;
		}else if(distance > 3 && distance <= 5){
			distancePrice = 2*1.0+(distance-3)*2.0;
		}else if(distance > 5 && distance <= 7){
			distancePrice = 2*1.0+2*2.0+(distance-5)*3.0;
		}
		
		//重量加价 [0-5KG)：不加价   [5KG-10KG)：0.5元/kg [10KG-20KG)：1.0元/kg 20kg以上:2.0元/kg
		Double weightPrice = 0.00;
		if(goods_weight >= 5 && goods_weight < 10){
			weightPrice = (goods_weight-5)*0.5;
		}else if(goods_weight >= 10 && goods_weight <20){
			weightPrice = 5*0.5+(goods_weight-10)*1.0;
		}else if(goods_weight >= 20){
			weightPrice = 5*0.5+10*1.0+(goods_weight-20)*2.0;
		}
		
		//其他补贴 夜间(21:00-05:59)：2  午高峰加价（11:00-12:59）：2
		Double timePrice = 0.00;
		if(belongCalendar("21:00", "23:59")){
			timePrice = 2.00;
		}
		if(belongCalendar("00:00", "05:59")){
			timePrice = 2.00;
		}
		if(belongCalendar("11:00", "12:59")){
			timePrice = 2.00;
		}
		
		//配送费=起步价+距离加价+重量加价+其他补贴
		deliverPrice = startingPrice+distancePrice+weightPrice+timePrice;
		return deliverPrice;
	}
	
	/**
     * 判断时间是否在时间段内
     * @param beginTime
     * @param endTime
     * @return
     */
    public static boolean belongCalendar(String beginDate, String endDate) {
    	SimpleDateFormat df = new SimpleDateFormat("HH:mm");//设置日期格式
	    Date nowTime =null;
	    Date beginTime = null;
	    Date endTime = null;
	    try {
	    	nowTime = df.parse(df.format(new Date()));
	    	//nowTime = df.parse("22:09");
	        beginTime = df.parse(beginDate);
	        endTime = df.parse(endDate);
	    } catch (Exception e) {
	        e.printStackTrace();
	    }
	    
        Calendar date = Calendar.getInstance();
        date.setTime(nowTime);

        Calendar begin = Calendar.getInstance();
        begin.setTime(beginTime);

        Calendar end = Calendar.getInstance();
        end.setTime(endTime);

        if (date.after(begin) && date.before(end)) {
            return true;
        } else {
            return false;
        }
    }
}
