package org.haier.meituanpeisong.banma.constants;

/**
 * 开放平台账号配置信息
 */
public class OpenApiConfig {

    /**
     * 正式账号 appkey，用于线上环境真实发单
     */
    public static final String FORMAL_APP_KEY = "df14c297ec1842f58134e357ca347530";

    /**
     * 正式账号 secret，用于线上环境真实发单
     */
    public static final String FORMAL_SECRET = "#rz7sl4}Jgz/z.P&{H#9-B-2q-wVeKTrny+P*~fn8cc?gQD2)=2P[kw.[q|,S^0M";

    /**
     * 测试账号 appkey，用于线上环境联调测试
     */
    public static final String TEST_APP_KEY = "65bf9bc5d1514cdb853eef391dbab8fd";

    /**
     * 测试账号 secret，用于线上环境联调测试
     */
    public static final String TEST_SECRET = "d$[DoDL%n5|;SGRX5*sFdgD+^<fhGx(PtW}uuGmR7hl=2Q#q|W&dD{~QNz;21w+t";
    
    public static final Integer DeliveryServiceCode = 4011;//配送服务代码，详情见合同飞速达:4002快速达:4011及时达:4012集中送:4013
}
