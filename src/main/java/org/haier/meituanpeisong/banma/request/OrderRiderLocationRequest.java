package org.haier.meituanpeisong.banma.request;

/**
 * 获取骑手当前位置参数
 */
public class OrderRiderLocationRequest extends AbstractRequest {
    /**
     * 配送活动标识
     */
    private long deliveryId;

    /**
     * 美团配送内部订单id，最长不超过32个字符
     */
    private String mtPeisongId;

    public long getDeliveryId() {
        return deliveryId;
    }

    public void setDeliveryId(long deliveryId) {
        this.deliveryId = deliveryId;
    }

    public String getMtPeisongId() {
        return mtPeisongId;
    }

    public void setMtPeisongId(String mtPeisongId) {
        this.mtPeisongId = mtPeisongId;
    }

    @Override
    public String toString() {
        return "CancelOrderRequest [deliveryId=" + deliveryId + ", mtPeisongId="
                + mtPeisongId +"]";
    }
}
