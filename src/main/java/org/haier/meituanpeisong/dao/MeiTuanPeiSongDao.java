package org.haier.meituanpeisong.dao;

import java.util.List;
import java.util.Map;

import org.haier.meituanpeisong.banma.vo.OpenApiGood;

/**
 * 描述：美团配送平台Dao
 * <AUTHOR>
 * @version v1.0
 *
 */
public interface MeiTuanPeiSongDao {
	
	
	public List<Map<String,Object>> shopsSaleListCount(Map<String,Object> map);
	/**
	 * 查询各状态的退款订单数量
	 * @param map
	 * @return
	 */
	public List<Map<String,Object>> queryReturnListCount(Map<String,Object> map);
	/**
	 * 查询各状态的订单数量
	 * @param map
	 * @return
	 */
	public Map<String,Object> shopsSaleListCountAll(Map<String,Object> map);
	
	/**
	 * 修改团长的待收益信息
	 * @param map
	 * @return
	 */
	public Integer modifyCaptionProfitR(Map<String,Object> map);
	
	/**
	 * 核单后重新分配利润信息
	 * @param proList
	 * @return
	 */
	public Integer modifyCaptainProfit(List<Map<String,Object>> proList);
	
	/**
	 * 比对核单后的商品信息，查询核单后的具体团长利润信息
	 * @param sale_list_unique
	 * @return
	 */
	public List<Map<String,Object>> getOrderCheckMsg(String sale_list_unique);
	/**
	 * 获取定的团长佣金信息
	 * @param sale_list_unique
	 * @return
	 */
	public List<Map<String,Object>> getOrderDisMsg(String sale_list_unique);
	
    /**
	 * 获取订单信息
	 */
	public Map<String ,Object> getOrderInfo(String sale_list_unique);
	
	/**
	 * 获取订单详情信息列表
	 */
	public List<OpenApiGood> getOrderDetailList(String sale_list_unique);
	
	/**
	 * 创建配送单
	 */
	public void addDeliveryOrder(Map<String ,Object> params);
	
	/**
	 * 修改配送单
	 */
	public void updateDeliveryOrder(Map<String ,Object> params);
	
	/**
	 * 修改订单发货状态
	 */
	public void updateOrderStatus(Map<String ,Object> params);
	
	/**
	 * 修改店铺是否加入美团配送：0否 1是
	 */
	public void updateShopIsMtps(Map<String ,Object> params);
	
	/**
	 * 获取配送单详情
	 */
	public Map<String ,Object> getDeliveryOrder(Map<String ,Object> params);
	
	//获取店铺信息
	public Map<String, Object> getShopInfo(String shop_unique);
	
	//获取店铺自配送骑手列表
	public List<Map<String, Object>> shopCourierList(String shop_unique);
	
	//添加订单核实信息
	public void addSaleListVerify(List<Map<String ,Object>> list);
	
	//修改会员余额
	public void updateCusAmount(Map<String ,Object> params);
	
	//添加会员交易流水
	public void addCustomerRecharge(Map<String ,Object> params);
}
