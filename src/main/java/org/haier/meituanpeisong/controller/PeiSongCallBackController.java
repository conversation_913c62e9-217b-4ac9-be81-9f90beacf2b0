package org.haier.meituanpeisong.controller;

import java.util.logging.Logger;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;

import org.haier.meituanpeisong.service.PeisongOrderService;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

@Controller
@RequestMapping("/peisong/callback")
public class PeiSongCallBackController {
	
	private static final Logger logger = Logger.getGlobal();
	
	@Resource
	private PeisongOrderService peisongOrderService;
	
	/**
	 * 配送订单状态回调
	 * @param sale_list_unique 订单编号
	 * @return
	 */
	@RequestMapping("/deliveryStatus.do")
	@ResponseBody
	public String deliveryStatus(HttpServletRequest request){
		logger.info("美团配送平台，订单状态回调");
		
		return peisongOrderService.deliveryStatus(request);
	}
	
	
	/**
	 * 配送订单异常回调
	 * @param sale_list_unique 订单编号
	 * @return
	 */
	@RequestMapping("/abnormalDelivery.do")
	@ResponseBody
	public String abnormalDelivery(HttpServletRequest request){
		logger.info("美团配送平台，订单异常回调");
		
		return peisongOrderService.abnormalDelivery(request);
	}
}
