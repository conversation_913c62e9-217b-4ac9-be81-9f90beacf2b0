package org.haier.meituanpeisong.controller;

import java.util.logging.Logger;

import javax.annotation.Resource;

import org.haier.meituanpeisong.banma.constants.RequestConstant;
import org.haier.meituanpeisong.service.PeisongTestService;
import org.haier.shop.util.ShopsResult;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

@Controller
@RequestMapping("/peisong/test")
public class PeisongTestController {
	
	private static final Logger logger = Logger.getGlobal();

	@Resource
	private PeisongTestService peisongTestService;
	
	/**
	 * 模拟接单
	 * @param sale_list_unique 订单编号
	 * @return
	 */
	@RequestMapping("/arrange.do")
	@ResponseBody
	public ShopsResult arrange(String sale_list_unique){
		logger.info("模拟接单====================sale_list_unique："+sale_list_unique);
		
		ShopsResult shopsResult = peisongTestService.test(sale_list_unique,RequestConstant.MOCK_ORDER_ACCEPT);
		
		return shopsResult;
	}
	
	
	/**
	 * 模拟取货
	 * @param sale_list_unique 订单编号
	 * @return
	 */
	@RequestMapping("/pickup.do")
	@ResponseBody
	public ShopsResult pickup(String sale_list_unique){
		logger.info("模拟取货====================sale_list_unique："+sale_list_unique);
		
		ShopsResult shopsResult = peisongTestService.test(sale_list_unique,RequestConstant.MOCK_ORDER_PICKUP);
		
		return shopsResult;
	}
	
	/**
	 * 模拟送达
	 * @param sale_list_unique 订单编号
	 * @return
	 */
	@RequestMapping("/deliver.do")
	@ResponseBody
	public ShopsResult deliver(String sale_list_unique){
		logger.info("模拟送达====================sale_list_unique："+sale_list_unique);
		
		ShopsResult shopsResult = peisongTestService.test(sale_list_unique,RequestConstant.MOCK_ORDER_DELIVER);
		
		return shopsResult;
	}
	
	/**
	 * 模拟改派
	 * @param sale_list_unique 订单编号
	 * @return
	 */
	@RequestMapping("/rearrange.do")
	@ResponseBody
	public ShopsResult rearrange(String sale_list_unique){
		logger.info("模拟改派====================sale_list_unique："+sale_list_unique);
		
		ShopsResult shopsResult = peisongTestService.test(sale_list_unique,RequestConstant.MOCK_ORDER_REARRANGE);
		
		return shopsResult;
	}
}
