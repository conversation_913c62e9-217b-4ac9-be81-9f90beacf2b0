package org.haier.meituanpeisong.controller;

import java.util.logging.Logger;

import javax.annotation.Resource;
import org.haier.meituanpeisong.service.PeisongOrderService;
import org.haier.shop.util.ShopsResult;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

@Controller
@RequestMapping("/peisong/order")
public class PeisongOrderController {
	
	private static final Logger logger = Logger.getGlobal();

	@Resource
	private PeisongOrderService peisongOrderService;
	
	/**
	 * 配送订单创建，把订单发送给美团配送平台
	 * @param sale_list_unique 订单编号
	 * @param goods_weight 订单商品重量（kg）
	 * @return
	 */
	@RequestMapping("/createOrder.do")
	@ResponseBody
	public ShopsResult createOrder(String sale_list_unique,String goods_weight,String sale_list_cashier){
		logger.info("配送订单创建，把订单发送给美团配送平台");
		
		ShopsResult shopsResult = peisongOrderService.createOrder(sale_list_unique, goods_weight,sale_list_cashier);
		
		return shopsResult;
	}
	
	/**
	 * 取消配送单
	 * @param sale_list_unique 订单编号
	 * @param cancel_reason 详细取消原因
	 * @return
	 */
	@RequestMapping("/deleteOrder.do")
	@ResponseBody
	public ShopsResult deleteOrder(String sale_list_unique,String cancel_reason){
		logger.info("美团配送平台，取消配送单");
		
		ShopsResult shopsResult = peisongOrderService.deleteOrder(sale_list_unique, cancel_reason);
		
		return shopsResult;
	}
	
	/**
	 * 查询配送单
	 * @param sale_list_unique 订单编号
	 * @return
	 */
	@RequestMapping("/queryOrder.do")
	@ResponseBody
	public ShopsResult queryOrder(String sale_list_unique){
		logger.info("美团配送平台，查询配送单");
		
		ShopsResult shopsResult = peisongOrderService.queryOrder(sale_list_unique);
		
		return shopsResult;
	}
	
	/**
	 * 评价骑手
	 * @param sale_list_unique 订单编号
	 * @param score 评分（5分制）
	 * @param comment_content 评价内容
	 * @return
	 */
	@RequestMapping("/evaluate.do")
	@ResponseBody
	public ShopsResult evaluate(String sale_list_unique,int score,String comment_content){
		logger.info("美团配送平台，评价骑手");
		
		ShopsResult shopsResult = peisongOrderService.evaluate(sale_list_unique,score,comment_content);
		
		return shopsResult;
	}
	
	/**
	 * 配送能力校验
	 * @param shop_unique 取货门店编号
	 * @param receiver_address 收件人地址
	 * @param receiver_lng 收件人经度
	 * @param receiver_lat 收件人维度
	 * @return
	 */
	@RequestMapping("/orderCheck.do")
	@ResponseBody
	public ShopsResult orderCheck(String shop_unique,String receiver_address,String receiver_lng,String receiver_lat){
		logger.info("美团配送平台，配送能力校验");
		
		ShopsResult shopsResult = peisongOrderService.orderCheck(shop_unique, receiver_address, receiver_lng, receiver_lat);
		
		return shopsResult;
	}
	
	/**
	 * 获取骑手当前位置
	 * @param sale_list_unique 订单编号
	 * @param mt_peisong_id 美团配送内部订单 id
	 * @return
	 */
	@RequestMapping("/getRiderLocation.do")
	@ResponseBody
	public ShopsResult getRiderLocation(String sale_list_unique,String mt_peisong_id){
		logger.info("美团配送平台，获取骑手当前位置");
		
		ShopsResult shopsResult = peisongOrderService.getRiderLocation(sale_list_unique, mt_peisong_id);
		
		return shopsResult;
	}
	
	/**
	 * 商家自配送
	 * @param sale_list_unique 订单编号
	 * @param goods_weight 订单商品重量（kg）
	 * @param shop_courier_id 自配送商家快递员id
	 * @param courier_name 配送员姓名
	 * @param courier_phone 配送员电话
	 * @return
	 */
	@RequestMapping("/shopSelf.do")
	@ResponseBody
	public ShopsResult shopSelf(String sale_list_unique,String goods_weight,String shop_courier_id,String courier_name,String courier_phone,String sale_list_cashier){
		logger.info("商家自配送，配送订单创建");
		
		ShopsResult shopsResult = peisongOrderService.shopSelf(sale_list_unique, goods_weight,shop_courier_id,courier_name,courier_phone,sale_list_cashier);
		
		return shopsResult;
	}
	
	/**
	 * 获取商家自配送骑手列表
	 * @param shop_unique 店铺编号
	 * @return
	 */
	@RequestMapping("/shopCourierList.do")
	@ResponseBody
	public ShopsResult shopCourierList(String shop_unique){
		logger.info("获取商家自配送骑手列表");
		
		ShopsResult shopsResult = peisongOrderService.shopCourierList(shop_unique);
		
		return shopsResult;
	}


}
