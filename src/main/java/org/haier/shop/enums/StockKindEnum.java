package org.haier.shop.enums;

/**
 * 库存类型
 */
public enum StockKindEnum {
    DAMAGE("报损", 1),
    RETURN("退货", 2),
    EXCHANGE("换货", 3),
    INIT("初始化", 4),
    ;
    //名称
    private String name;
    //值
    private Integer value;

    StockKindEnum(String name, Integer value) {
        this.name = name;
        this.value = value;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public Integer getValue() {
        return value;
    }

    public void setValue(Integer value) {
        this.value = value;
    }
}
