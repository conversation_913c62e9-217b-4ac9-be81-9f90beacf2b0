package org.haier.shop.enums;

public enum StockTypeEnum {
    INSTORAGE("入库", 1),
    OUTSTORAGE("出库", 2),

    ;
    //名称
    private String name;
    //值
    private Integer value;

    StockTypeEnum(String name, Integer value) {
        this.name = name;
        this.value = value;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public Integer getValue() {
        return value;
    }

    public void setValue(Integer value) {
        this.value = value;
    }
}
