package org.haier.shop.enums;

/**
 * 操作来源
 */
public enum StockOriginEnum {
    APP("手机", 1),
    PC("PC端", 2),
    WEB("web网页端", 3),
    WECHAT("小程序", 4),

    ;
    //名称
    private String name;
    //值
    private Integer value;

    StockOriginEnum(String name, Integer value) {
        this.name = name;
        this.value = value;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public Integer getValue() {
        return value;
    }

    public void setValue(Integer value) {
        this.value = value;
    }
}
