package org.haier.shop.enums.goodsEnum;

public enum DeviceSourceEnum {
    PHONE_APP("手机APP", 1),
    PC_POS("PC_收银软件" , 2),
    PC_WEB("网页后台", 3),
    PC_APPLET("一刻钟到家小程序", 4),
    PHONE_SUPPLIER_APP("供应商", 5),
    PHONE_SUPPLIER_WEB("供应商后台", 6),
    ;

    DeviceSourceEnum(String label, Integer value){
        this.label = label;
        this.value = value;
    }

    private String label;
    private Integer value;

    public String getLabel() {
        return label;
    }

    public Integer getValue() {
        return value;
    }
}
