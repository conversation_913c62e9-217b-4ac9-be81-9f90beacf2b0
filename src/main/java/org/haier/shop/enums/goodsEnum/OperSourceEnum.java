package org.haier.shop.enums.goodsEnum;

public enum OperSourceEnum {
    GOODS_CHANGE("商品修改",1),
    CHANGE_SALE_LIST("订单销售", 2),
    CHANGE_REFUND("退款退货", 3),
    CHANGE_STOCK_CHANGE("商品出入库", 4),
    CHANGE_INVENTORY("库存盘点", 5),
    CHANGE_WEB_PUR("网页采购", 6),
    CHANGE_PUR_REFUND("采购退货", 7),
    CHANGE_TRANSFER("商品调拨", 8),
    CHANGE_PC_SHELF_STATE("收银机商品上下架", 9),
    CHANGE_PRICE_ADJUST("商品调价", 10),
    CHANGE_SHELF_STATE("小程序上下架", 11),
    CHANGE_GOODS_KIND("商品分类转移", 12),
    CHANGE_SUPPLIER_CONFIRM("供货商城订单确认", 13)
    ;

    OperSourceEnum(String label, Integer value){
        this.label = label;
        this.value = value;
    }
    public String getLabel() {
        return label;
    }

    public Integer getValue() {
        return value;
    }

    private String label;
    private Integer value;
}
