package org.haier.shop.enums.goodsEnum;

/**
 * 商品记录操作类型
 */
public enum OperTypeEnum {

    OPER_TYPE_ADD("新增", 1),
    OPER_TYPE_UPDATE("修改", 2),
    OPER_TYPE_DELETE("删除", 3)

    ;
    OperTypeEnum(String label, Integer value){
        this.label = label;
        this.value = value;
    }
    private String label;
    private Integer value;

    public String getLabel() {
        return label;
    }

    public void setLabel(String label) {
        this.label = label;
    }

    public Integer getValue() {
        return value;
    }

    public void setValue(Integer value) {
        this.value = value;
    }
}
