package org.haier.shop.enums;

/**
 * 入库类型
 */
public enum StockReourceEnum {
    BY_HAND("手动出入库", 1),
    BY_SALE("销售订单出入库", 2),
    BY_ORDER("进货订单出入库", 3),
    BY_CHECK("盘库", 4),
    BY_WEB_SALE("网上订单出库", 5),
    BY_DEPOSIT("寄存", 6),
    BY_YUN_PURCHASE("云商采购", 7),
    BY_ALLOCATION("调拨", 8),
    BT_RETURN("退货", 9),

    ;
    //名称
    private String name;
    //值
    private Integer value;

    StockReourceEnum(String name, Integer value) {
        this.name = name;
        this.value = value;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public Integer getValue() {
        return value;
    }

    public void setValue(Integer value) {
        this.value = value;
    }
}
