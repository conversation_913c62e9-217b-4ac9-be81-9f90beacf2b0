package org.haier.shop.enums;

public enum AduitStatusEnum {
    APPROVED("审核通过", 1),
    REJECTED("审核不通过", 2),
    WAITING("待审核", 0),
    ;
    //名称
    private String name;
    //值
    private Integer value;

    AduitStatusEnum(String name, Integer value) {
        this.name = name;
        this.value = value;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public Integer getValue() {
        return value;
    }

    public void setValue(Integer value) {
        this.value = value;
    }
}
