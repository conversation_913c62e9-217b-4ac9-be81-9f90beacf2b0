package org.haier.shop.enums;

public enum SourceTypeEnums {

    POS("收银机",1),
    PLUGIN("插件",2),
    APP("APP",3),
    SMALL_PRGORAM("小程序",4),
    HANDHLED_POS("手持POS",5),

    ;
    private String name;
    private Integer value;

    public String getName() {
        return name;
    }

    public Integer getValue() {
        return value;
    }

    SourceTypeEnums(String name, Integer value) {
        this.name = name;
        this.value = value;
    }

    /**
     * 根据value获取枚举
     * @param value
     * @return
     */
    public static String getSourceTypeName(Integer value){
        for(SourceTypeEnums sourceTypeEnums : SourceTypeEnums.values()){
            if(sourceTypeEnums.getValue().equals(value)){
                return sourceTypeEnums.getName();
            }
        }
        return null;
    }
}
