package org.haier.shop.customer;

import java.sql.Timestamp;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;

import org.haier.customer.entity.ShopResult;
import org.haier.shop.service.ManagerService;
import org.haier.shop.util.FrequencyLimit;
import org.haier.shop.util.ShopsResult;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

@Controller
@RequestMapping("/manager")
public class CustomerController {
	@Resource
	private ManagerService managerService;
	
	@RequestMapping("/queryMachineNums.do")
	@ResponseBody
	public ShopsResult queryMachineNums(
			@RequestParam(value="manager_unique",required=true,defaultValue="147571932788311")String manager_unique,
			HttpServletRequest request
			){
		System.out.println("/manager/queryMachineNums.do");
		boolean flag=FrequencyLimit.getNumOfCall(request.getRemoteAddr());
		if(!flag){
			return FrequencyLimit.tooManyRequst();
		}
		return managerService.queryMachineNums(manager_unique);
	}
	
	@RequestMapping("/queryShopsGoods.do")
	@ResponseBody
	/**
	 * 
	 * 商品接口
	 * @param shopID
	 * @param pageIndex
	 * @param pageSize
	 * @param startTime
	 * @param endTime
	 * @return
	 */
	public ShopResult queryShopsGoods(
			String shopID,
			@RequestParam(value="pageIndex",required=true,defaultValue="1")Integer pageIndex,
			@RequestParam(value="pageSize",defaultValue="20")Integer pageSize,
			Timestamp startTime, 
			Timestamp endTime,
			HttpServletRequest request){
		boolean flag=FrequencyLimit.getNumOfCall(request.getRemoteAddr());
		if(!flag){
			return FrequencyLimit.limitRequest();
		}
		return managerService.queryShopsGoods(shopID, pageIndex, pageSize, startTime, endTime);
	}
	
	/**
	 * 查询管理员管理的所有店铺
	 */
	@RequestMapping("/queryShops.do")
	@ResponseBody
	public ShopResult queryShops(
			Timestamp startTime,
			Timestamp endTime,
			HttpServletRequest request){
		System.out.println("/manager/queryShops.do");
		boolean flag=FrequencyLimit.getNumOfCall(request.getRemoteAddr());
		if(!flag){
			return FrequencyLimit.limitRequest();
		}
		return managerService.queryShops(startTime, endTime);
	}
	
	/**
	 * 3.销售接口（根据销售时间段返回所有销售记录）
	 * @param shopID
	 * @param startTime
	 * @param endTime
	 * @param pageIndex
	 * @param pageSize
	 * @return
	 */
	@RequestMapping("/queryOrderLists.do")
	@ResponseBody
	public ShopResult queryOrderLists(
			@RequestParam(value="shopID",defaultValue="0")String shopID,Timestamp startTime,Timestamp endTime,
			@RequestParam(value="pageIndex",defaultValue="1")Integer pageIndex,
			@RequestParam(value="pageSize",defaultValue="20")Integer pageSize,
			HttpServletRequest request){
		System.out.println("manager/queryOrderLists.do");
		boolean flag=FrequencyLimit.getNumOfCall(request.getRemoteAddr());
		if(!flag){
			return FrequencyLimit.limitRequest();
		}
		return managerService.queryOrderLists(shopID, startTime, endTime, pageIndex, pageSize);
	}
	
	/**
	 * 商品分页查询
	 * @param shopID
	 * @param pageIndex
	 * @param pageSize
	 * @param startTime
	 * @param endTime
	 * @return
	 */
	@RequestMapping("/queryGoodsByPage.do")
	@ResponseBody
	public ShopResult queryGoodsByPage(
			@RequestParam(value="shopID",defaultValue="0")String shopID,
			@RequestParam(value="pageIndex",defaultValue="1")Integer pageIndex,
			@RequestParam(value="pageSize",defaultValue="20")Integer pageSize,Timestamp startTime,Timestamp endTime,
			HttpServletRequest request){
		boolean flag=FrequencyLimit.getNumOfCall(request.getRemoteAddr());
		if(!flag){
			return FrequencyLimit.limitRequest();
		}
		System.out.println("/manager/queryGoodsByPage.do");
		return managerService.queryGoodsByPage(shopID, pageIndex, pageSize, startTime, endTime);
	}
	
	
}
