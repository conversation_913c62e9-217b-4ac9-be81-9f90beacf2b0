package org.haier.shop.params.goodsBatch;

import com.fasterxml.jackson.annotation.JsonFormat;
import org.haier.shop.entity.GoodsSaleBatch;

import java.math.BigDecimal;
import java.util.Date;

/**
 * @Description
 * @ClassName GoodsSaleBatchData
 * <AUTHOR>
 * @Date 2024/5/16 15:46
 **/
public class GoodsSaleBatchData extends GoodsSaleBatch {

    /**
     * 规格准换率
     */
    private BigDecimal goodsContain;

    /**
     * 入库单号
     */
    private String stockInListUnique;

    /**
     * 商品名称
     */
    private String goodsName;

    /**
     * 入库时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date stockInTime;

    public BigDecimal getGoodsContain() {
        return goodsContain;
    }

    public void setGoodsContain(BigDecimal goodsContain) {
        this.goodsContain = goodsContain;
    }

    public String getStockInListUnique() {
        return stockInListUnique;
    }

    public void setStockInListUnique(String stockInListUnique) {
        this.stockInListUnique = stockInListUnique;
    }

    public String getGoodsName() {
        return goodsName;
    }

    public void setGoodsName(String goodsName) {
        this.goodsName = goodsName;
    }

    public Date getStockInTime() {
        return stockInTime;
    }

    public void setStockInTime(Date stockInTime) {
        this.stockInTime = stockInTime;
    }
}
