package org.haier.shop.params.goodsBatch;

import javax.validation.constraints.NotNull;

/**
 * @Description 批次导出参数
 * @ClassName GoodsBatchQueryParams
 * <AUTHOR>
 * @Date 2024/4/30 9:30
 **/
public class GoodsBatchExportParams {

    /**
     * 店铺编码
     */
    @NotNull(message = "店铺编码不能为空")
    private Long shopUnique;

    /**
     * 开始日期
     */
    private String startDate;
    /**
     * 结束日期
     */
    private String endDate;
    /**
     * 批次号
     */
    private String batchUnique;

    /**
     * 商品名称/条码
     */
    private String goodsMessage;
    /**
     * 大类
     */
    private Long goodsKindFirst;
    /**
     * 小类
     */
    private Long goodsKindSecond;

    /**
     * 到期状态：0-正常，1-临期，2-过期
     */
    private Integer expStatus;

    private String goodsBarcode;

    public String getGoodsBarcode() {
        return goodsBarcode;
    }

    public void setGoodsBarcode(String goodsBarcode) {
        this.goodsBarcode = goodsBarcode;
    }

    public Long getShopUnique() {
        return shopUnique;
    }

    public void setShopUnique(Long shopUnique) {
        this.shopUnique = shopUnique;
    }

    public String getStartDate() {
        return startDate;
    }

    public void setStartDate(String startDate) {
        this.startDate = startDate;
    }

    public String getEndDate() {
        return endDate;
    }

    public void setEndDate(String endDate) {
        this.endDate = endDate;
    }

    public String getBatchUnique() {
        return batchUnique;
    }

    public void setBatchUnique(String batchUnique) {
        this.batchUnique = batchUnique;
    }

    public String getGoodsMessage() {
        return goodsMessage;
    }

    public void setGoodsMessage(String goodsMessage) {
        this.goodsMessage = goodsMessage;
    }

    public Long getGoodsKindFirst() {
        return goodsKindFirst;
    }

    public void setGoodsKindFirst(Long goodsKindFirst) {
        this.goodsKindFirst = goodsKindFirst;
    }

    public Long getGoodsKindSecond() {
        return goodsKindSecond;
    }

    public void setGoodsKindSecond(Long goodsKindSecond) {
        this.goodsKindSecond = goodsKindSecond;
    }

    public Integer getExpStatus() {
        return expStatus;
    }

    public void setExpStatus(Integer expStatus) {
        this.expStatus = expStatus;
    }
}
