package org.haier.shop.params.goodsBatch;

import org.hibernate.validator.constraints.NotBlank;

import javax.validation.constraints.NotNull;
import java.math.BigDecimal;

/**
 * @Description 批次查询参数
 * @ClassName GoodsBatchQueryParams
 * <AUTHOR>
 * @Date 2024/4/30 9:30
 **/
public class GoodsBatchChooseViewParams {

    /**
     * 店铺编码
     */
    @NotNull(message = "店铺编码不能为空")
    private Long shopUnique;

    /**
     * 出入库单号
     */
    @NotBlank(message = "出库单号不能为空")
    private String listUnique;

    @NotBlank(message = "商品条码不能为空")
    private String goodsBarcode;

    /**
     * 出库数量
     */
    @NotNull(message = "出库数量不能为空")
    private BigDecimal outCount;

    public String getGoodsBarcode() {
        return goodsBarcode;
    }

    public void setGoodsBarcode(String goodsBarcode) {
        this.goodsBarcode = goodsBarcode;
    }

    public Long getShopUnique() {
        return shopUnique;
    }

    public void setShopUnique(Long shopUnique) {
        this.shopUnique = shopUnique;
    }

    public String getListUnique() {
        return listUnique;
    }

    public void setListUnique(String listUnique) {
        this.listUnique = listUnique;
    }

    public BigDecimal getOutCount() {
        return outCount;
    }

    public void setOutCount(BigDecimal outCount) {
        this.outCount = outCount;
    }
}
