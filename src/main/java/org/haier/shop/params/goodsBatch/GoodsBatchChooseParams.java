package org.haier.shop.params.goodsBatch;

import org.haier.shop.params.goods.OutStockGoodsBatchParam;

import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.List;

/**
 * @Description 批次查询参数
 * @ClassName GoodsBatchQueryParams
 * <AUTHOR>
 * @Date 2024/4/30 9:30
 **/
public class GoodsBatchChooseParams {

    /**
     * 店铺编码
     */
    @NotNull(message = "店铺编码不能为空")
    private Long shopUnique;

    /**
     * 批次号
     */
    private String batchUnique;

    /**
     * 商品名称/条码
     */
    private String goodsMessage;

    /**
     * 商品条码
     */
    private String goodsBarcode;

    /**
     * 出入库单号
     */
    private String listUnique;

    /**
     * 出库数量
     */
    @NotNull(message = "出库数量不能为空")
    private BigDecimal outCount;

    /**
     * 商品ID
     */
    private Long goodsId;

    /**
     * 商品批次列表
     */
    private List<OutStockGoodsBatchParam> goodsBatchList;

    public Long getGoodsId() {
        return goodsId;
    }

    public void setGoodsId(Long goodsId) {
        this.goodsId = goodsId;
    }

    public List<OutStockGoodsBatchParam> getGoodsBatchList() {
        return goodsBatchList;
    }

    public void setGoodsBatchList(List<OutStockGoodsBatchParam> goodsBatchList) {
        this.goodsBatchList = goodsBatchList;
    }

    public Long getShopUnique() {
        return shopUnique;
    }

    public void setShopUnique(Long shopUnique) {
        this.shopUnique = shopUnique;
    }

    public String getBatchUnique() {
        return batchUnique;
    }

    public void setBatchUnique(String batchUnique) {
        this.batchUnique = batchUnique;
    }

    public String getGoodsMessage() {
        return goodsMessage;
    }

    public void setGoodsMessage(String goodsMessage) {
        this.goodsMessage = goodsMessage;
    }

    public String getGoodsBarcode() {
        return goodsBarcode;
    }

    public void setGoodsBarcode(String goodsBarcode) {
        this.goodsBarcode = goodsBarcode;
    }

    public String getListUnique() {
        return listUnique;
    }

    public void setListUnique(String listUnique) {
        this.listUnique = listUnique;
    }

    public BigDecimal getOutCount() {
        return outCount;
    }

    public void setOutCount(BigDecimal outCount) {
        this.outCount = outCount;
    }
}
