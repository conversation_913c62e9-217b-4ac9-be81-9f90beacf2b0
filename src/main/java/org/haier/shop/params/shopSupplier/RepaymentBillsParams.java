package org.haier.shop.params.shopSupplier;

import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.List;

public class RepaymentBillsParams extends CreateParams {
    /**
     * 购销单列表
     */
    @NotNull(message = "请输入购销单列表")
    private List<String> billIdList;
    /**
     * 供货商编号
     */
    @NotNull(message = "请输入供货商编号")
    private String supplierUnique;
    /**
     * 还款凭证
     */
    private List<String> voucherPicturepath;
    /**
     * 付款金额
     */
    @NotNull(message = "请输入付款金额")
    private BigDecimal paymentMoney;
    /**
     * 备注
     */
    private String remark;

    public List<String> getBillIdList() {
        return billIdList;
    }

    public void setBillIdList(List<String> billIdList) {
        this.billIdList = billIdList;
    }

    public String getSupplierUnique() {
        return supplierUnique;
    }

    public void setSupplierUnique(String supplierUnique) {
        this.supplierUnique = supplierUnique;
    }

    public List<String> getVoucherPicturepath() {
        return voucherPicturepath;
    }

    public void setVoucherPicturepath(List<String> voucherPicturepath) {
        this.voucherPicturepath = voucherPicturepath;
    }

    public BigDecimal getPaymentMoney() {
        return paymentMoney;
    }

    public void setPaymentMoney(BigDecimal paymentMoney) {
        this.paymentMoney = paymentMoney;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }
}
