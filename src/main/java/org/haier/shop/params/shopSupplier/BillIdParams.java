package org.haier.shop.params.shopSupplier;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;

public class BillIdParams extends ShopUniqueParams{
    /**
     * 购销单编号
     */
    @NotNull(message = "请输入购销单编号")
    @Min(message="请输入购销单编号",value=1)
    private String billId;

    public String getBillId() {
        return billId;
    }

    public void setBillId(String billId) {
        this.billId = billId;
    }
}
