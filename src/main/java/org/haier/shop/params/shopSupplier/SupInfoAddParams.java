package org.haier.shop.params.shopSupplier;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;

public class SupInfoAddParams extends CreateParams {
    /**
     * 供应商名称
     */
    @NotNull(message = "请输入供应商名称")
    private String supplierName;
    /**
     *联系人
     */
    @NotNull(message = "请输入联系人")
    private String contacts;
    /**
     *联系方式
     */
    @NotNull(message = "请输入联系方式")
    private String contactMobile;
    /**
     *地址
     */
    private String address;
    /**
     *所属分类
     */
    @NotNull(message = "请输入所属分类")
    private String supplierKindUnique;
    /**
     *采购类型
     */
    @NotNull(message = "请输入采购类型")
    @Min(message="请输入采购类型",value=1)
    private Integer purchaseType;
    /**
     *启用状态
     */
    @NotNull(message = "请输入启用状态")
    @Min(message="请输入启用状态",value=1)
    private Integer enableStatus;

    public String getSupplierName() {
        return supplierName;
    }

    public void setSupplierName(String supplierName) {
        this.supplierName = supplierName;
    }

    public String getContacts() {
        return contacts;
    }

    public void setContacts(String contacts) {
        this.contacts = contacts;
    }

    public String getContactMobile() {
        return contactMobile;
    }

    public void setContactMobile(String contactMobile) {
        this.contactMobile = contactMobile;
    }

    public String getAddress() {
        return address;
    }

    public void setAddress(String address) {
        this.address = address;
    }

    public String getSupplierKindUnique() {
        return supplierKindUnique;
    }

    public void setSupplierKindUnique(String supplierKindUnique) {
        this.supplierKindUnique = supplierKindUnique;
    }

    public Integer getPurchaseType() {
        return purchaseType;
    }

    public void setPurchaseType(Integer purchaseType) {
        this.purchaseType = purchaseType;
    }

    public Integer getEnableStatus() {
        return enableStatus;
    }

    public void setEnableStatus(Integer enableStatus) {
        this.enableStatus = enableStatus;
    }
}
