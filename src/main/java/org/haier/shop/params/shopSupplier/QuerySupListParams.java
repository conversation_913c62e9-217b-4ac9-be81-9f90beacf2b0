package org.haier.shop.params.shopSupplier;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;

public class QuerySupListParams extends ShopUniqueParams {
    /**
     * 供应商名称
     */
    private String supplierName;
    /**
     * 联系人
     */
    private String contacts;
    /**
     * 供货商手机号
     */
    private String contactMobile;
    /**
     * 类型：1-购销；2-自采
     */
    private String purchaseType;
    /**
     * 通过状态：0-未通过；1-通过
     */
    private String bindFlag;
    /**
     * 所属分类
     */
    private String supKindUnique;
    @NotNull(message = "请输入页码数")
    @Min(value = 1,message = "页码数最小为1")
    private Integer pageIndex;
    /**
     * 每页条数
     */
    @NotNull(message = "请输入每页条数")
    @Min(value = 1,message = "每页条数最小为1")
    private Integer pageSize;

    public String getSupplierName() {
        return supplierName;
    }

    public void setSupplierName(String supplierName) {
        this.supplierName = supplierName;
    }

    public String getContacts() {
        return contacts;
    }

    public void setContacts(String contacts) {
        this.contacts = contacts;
    }

    public String getContactMobile() {
        return contactMobile;
    }

    public void setContactMobile(String contactMobile) {
        this.contactMobile = contactMobile;
    }

    public String getPurchaseType() {
        return purchaseType;
    }

    public void setPurchaseType(String purchaseType) {
        this.purchaseType = purchaseType;
    }

    public String getBindFlag() {
        return bindFlag;
    }

    public void setBindFlag(String bindFlag) {
        this.bindFlag = bindFlag;
    }

    public String getSupKindUnique() {
        return supKindUnique;
    }

    public void setSupKindUnique(String supKindUnique) {
        this.supKindUnique = supKindUnique;
    }

    public Integer getPageIndex() {
        return pageIndex;
    }

    public void setPageIndex(Integer pageIndex) {
        this.pageIndex = pageIndex;
    }

    public Integer getPageSize() {
        return pageSize;
    }

    public void setPageSize(Integer pageSize) {
        this.pageSize = pageSize;
    }
}
