package org.haier.shop.params.shopSupplier;

public class SupInfoUpdateParams extends SupplierIdParams {
    /**
     * 供应商名称
     */
    private String supplierName;
    /**
     *联系人
     */
    private String contacts;
    /**
     *联系方式
     */
    private String contactMobile;
    /**
     *地址
     */
    private String address;
    /**
     *所属分类
     */
    private String supplierKindUnique;
    /**
     *采购类型
     */
    private Integer purchaseType;
    /**
     *启用状态
     */
    private Integer enableStatus;

    public String getSupplierName() {
        return supplierName;
    }

    public void setSupplierName(String supplierName) {
        this.supplierName = supplierName;
    }

    public String getContacts() {
        return contacts;
    }

    public void setContacts(String contacts) {
        this.contacts = contacts;
    }

    public String getContactMobile() {
        return contactMobile;
    }

    public void setContactMobile(String contactMobile) {
        this.contactMobile = contactMobile;
    }

    public String getAddress() {
        return address;
    }

    public void setAddress(String address) {
        this.address = address;
    }

    public String getSupplierKindUnique() {
        return supplierKindUnique;
    }

    public void setSupplierKindUnique(String supplierKindUnique) {
        this.supplierKindUnique = supplierKindUnique;
    }

    public Integer getPurchaseType() {
        return purchaseType;
    }

    public void setPurchaseType(Integer purchaseType) {
        this.purchaseType = purchaseType;
    }

    public Integer getEnableStatus() {
        return enableStatus;
    }

    public void setEnableStatus(Integer enableStatus) {
        this.enableStatus = enableStatus;
    }
}
