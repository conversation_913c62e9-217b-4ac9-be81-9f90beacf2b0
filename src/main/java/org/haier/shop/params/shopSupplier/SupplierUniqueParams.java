package org.haier.shop.params.shopSupplier;

import javax.validation.constraints.NotNull;

public class SupplierUniqueParams extends ShopUniqueParams {
    /**
     * 供应商编号
     */
    @NotNull(message = "请输入供应商编号")
    private String supplierUnique;

    public String getSupplierUnique() {
        return supplierUnique;
    }

    public void setSupplierUnique(String supplierUnique) {
        this.supplierUnique = supplierUnique;
    }
}
