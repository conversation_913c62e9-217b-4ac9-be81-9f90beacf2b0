package org.haier.shop.params.shopSupplier;

import org.hibernate.validator.constraints.Range;

import javax.validation.constraints.NotNull;

public class QueryRecordGoodsListParams extends SupplierUniqueParams {
    /**
     * 是否建档:0-未建档；1-已建档
     */
    @NotNull(message = "请输入建档状态")
    @Range(min = 0,max = 1)
    private Integer recordStatus;

    public Integer getRecordStatus() {
        return recordStatus;
    }

    public void setRecordStatus(Integer recordStatus) {
        this.recordStatus = recordStatus;
    }
}
