package org.haier.shop.params.common;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

public class CommonPageQueryParams implements Serializable {
    @NotNull(message = "page不能为空")
    private Integer page;
    @NotNull(message = "limit不能为空")
    private Integer limit;

    public Integer getPage() {
        return page;
    }

    public void setPage(Integer page) {
        this.page = page;
    }

    public Integer getLimit() {
        return limit;
    }

    public void setLimit(Integer limit) {
        this.limit = limit;
    }
    public Integer getStartNum() {
    	return (page - 1) * limit;
    }
}
