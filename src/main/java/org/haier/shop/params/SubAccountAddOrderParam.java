package org.haier.shop.params;

import java.io.Serializable;

/**
 * @ClassName SubAccountAddOrderParam
 * @Description 子账簿创建订单入参
 * <AUTHOR>
 * @Date 2025/3/29 15:34
 * @Version 1.0
 */

public class SubAccountAddOrderParam implements Serializable {
    private static final long serialVersionUID = 7704180427172516602L;
    /**
     *子账簿账号
     */
    private String accNo;
    /**
     *交易流水号
     * 入账流水
     */
    private String detSeq;


    /**
     *子账簿余额
     * RMB(单位为元)
     */
    private String balance;

    /**
     *交易金额
     * 单位为元
     */
    private String transAmount;

    /**
     * 订单唯一性标识
     */
    private Long saleListUnique;


    public String getAccNo() {
        return accNo;
    }

    public void setAccNo(String accNo) {
        this.accNo = accNo;
    }

    public String getDetSeq() {
        return detSeq;
    }

    public void setDetSeq(String detSeq) {
        this.detSeq = detSeq;
    }

    public String getBalance() {
        return balance;
    }

    public void setBalance(String balance) {
        this.balance = balance;
    }

    public String getTransAmount() {
        return transAmount;
    }

    public void setTransAmount(String transAmount) {
        this.transAmount = transAmount;
    }

    public Long getSaleListUnique() {
        return saleListUnique;
    }

    public void setSaleListUnique(Long saleListUnique) {
        this.saleListUnique = saleListUnique;
    }
}