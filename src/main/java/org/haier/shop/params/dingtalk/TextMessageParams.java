package org.haier.shop.params.dingtalk;


import java.io.Serializable;
import java.util.List;

public class TextMessageParams implements Serializable {
    private String sigh;
    private String timeStamp;
    private String content;
    private List<String> atMobiles;
    private List<String> atUserIds;
    private Boolean isAtAll;

    public TextMessageParams() {
    }

    public String getSigh() {
        return this.sigh;
    }

    public void setSigh(String sigh) {
        this.sigh = sigh;
    }

    public String getTimeStamp() {
        return this.timeStamp;
    }

    public void setTimeStamp(String timeStamp) {
        this.timeStamp = timeStamp;
    }

    public String getContent() {
        return this.content;
    }

    public void setContent(String content) {
        this.content = content;
    }

    public List<String> getAtMobiles() {
        return this.atMobiles;
    }

    public void setAtMobiles(List<String> atMobiles) {
        this.atMobiles = atMobiles;
    }

    public List<String> getAtUserIds() {
        return this.atUserIds;
    }

    public void setAtUserIds(List<String> atUserIds) {
        this.atUserIds = atUserIds;
    }

    public Boolean getIsAtAll() {
        return this.isAtAll;
    }

    public void setIsAtAll(Boolean atAll) {
        this.isAtAll = atAll;
    }
}
