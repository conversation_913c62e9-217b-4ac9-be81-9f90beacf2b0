package org.haier.shop.params.dingtalk;

import java.io.Serializable;

public class LinkMessageParams implements Serializable {
    private String sigh;
    private String timeStamp;
    private String title;
    private String text;
    private String messageUrl;
    private String picUrl;

    public LinkMessageParams() {
    }

    public String getSigh() {
        return this.sigh;
    }

    public void setSigh(String sigh) {
        this.sigh = sigh;
    }

    public String getTimeStamp() {
        return this.timeStamp;
    }

    public void setTimeStamp(String timeStamp) {
        this.timeStamp = timeStamp;
    }

    public String getTitle() {
        return this.title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getText() {
        return this.text;
    }

    public void setText(String text) {
        this.text = text;
    }

    public String getMessageUrl() {
        return this.messageUrl;
    }

    public void setMessageUrl(String messageUrl) {
        this.messageUrl = messageUrl;
    }

    public String getPicUrl() {
        return this.picUrl;
    }

    public void setPicUrl(String picUrl) {
        this.picUrl = picUrl;
    }
}
