package org.haier.shop.params.dingtalk;


import java.io.Serializable;

public class WholeActionCardMessageParams implements Serializable {
    private String sigh;
    private String timeStamp;
    private String title;
    private String text;
    private String singleTitle;
    private String singleURL;
    private String btnOrientation;

    public WholeActionCardMessageParams() {
    }

    public String getSigh() {
        return this.sigh;
    }

    public void setSigh(String sigh) {
        this.sigh = sigh;
    }

    public String getTimeStamp() {
        return this.timeStamp;
    }

    public void setTimeStamp(String timeStamp) {
        this.timeStamp = timeStamp;
    }

    public String getTitle() {
        return this.title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getText() {
        return this.text;
    }

    public void setText(String text) {
        this.text = text;
    }

    public String getSingleTitle() {
        return this.singleTitle;
    }

    public void setSingleTitle(String singleTitle) {
        this.singleTitle = singleTitle;
    }

    public String getSingleURL() {
        return this.singleURL;
    }

    public void setSingleURL(String singleURL) {
        this.singleURL = singleURL;
    }

    public String getBtnOrientation() {
        return this.btnOrientation;
    }

    public void setBtnOrientation(String btnOrientation) {
        this.btnOrientation = btnOrientation;
    }
}
