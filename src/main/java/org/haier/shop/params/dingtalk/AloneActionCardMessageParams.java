package org.haier.shop.params.dingtalk;
import java.io.Serializable;
import java.util.List;

public class AloneActionCardMessageParams implements Serializable {
    private String sigh;
    private String timeStamp;
    private String title;
    private String text;
    private List<org.haier.shop.params.dingtalk.AloneActionCardMessageParams.AloneActionCardBtnsMessageParams> btns;
    private String btnOrientation;

    public AloneActionCardMessageParams() {
    }

    public String getSigh() {
        return this.sigh;
    }

    public void setSigh(String sigh) {
        this.sigh = sigh;
    }

    public String getTimeStamp() {
        return this.timeStamp;
    }

    public void setTimeStamp(String timeStamp) {
        this.timeStamp = timeStamp;
    }

    public String getTitle() {
        return this.title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getText() {
        return this.text;
    }

    public void setText(String text) {
        this.text = text;
    }

    public List<org.haier.shop.params.dingtalk.AloneActionCardMessageParams.AloneActionCardBtnsMessageParams> getBtns() {
        return this.btns;
    }

    public void setBtns(List<org.haier.shop.params.dingtalk.AloneActionCardMessageParams.AloneActionCardBtnsMessageParams> btns) {
        this.btns = btns;
    }

    public String getBtnOrientation() {
        return this.btnOrientation;
    }

    public void setBtnOrientation(String btnOrientation) {
        this.btnOrientation = btnOrientation;
    }

    public static class AloneActionCardBtnsMessageParams implements Serializable {
        private String title;
        private String actionURL;

        public AloneActionCardBtnsMessageParams() {
        }

        public String getTitle() {
            return this.title;
        }

        public void setTitle(String title) {
            this.title = title;
        }

        public String getActionURL() {
            return this.actionURL;
        }

        public void setActionURL(String actionURL) {
            this.actionURL = actionURL;
        }
    }
}
