package org.haier.shop.params.dingtalk;


import java.io.Serializable;
import java.util.List;

public class FeedCardMessageParams implements Serializable {
    /**
     * 加密密文
     * HMac hmac = SecureUtil.hmac(HmacAlgorithm.HmacMD5, 密钥);
     * messageParams.setSigh(hmac.digestHex(timeStamp));
     */
    private String sigh;
    /**
     * 时间戳
     * String timeStamp = DateUtil.formatDateTime(new Date());
     * *         messageParams.setTimeStamp(timeStamp);
     */
    private String timeStamp;
    private List<org.haier.shop.params.dingtalk.FeedCardMessageParams.FeedCardLinksMessageParams> links;

    public String getSigh() {
        return sigh;
    }

    public void setSigh(String sigh) {
        this.sigh = sigh;
    }

    public String getTimeStamp() {
        return timeStamp;
    }

    public void setTimeStamp(String timeStamp) {
        this.timeStamp = timeStamp;
    }

    public List<org.haier.shop.params.dingtalk.FeedCardMessageParams.FeedCardLinksMessageParams> getLinks() {
        return links;
    }

    public void setLinks(List<org.haier.shop.params.dingtalk.FeedCardMessageParams.FeedCardLinksMessageParams> links) {
        this.links = links;
    }

    public static class FeedCardLinksMessageParams implements Serializable {
        /**
         * 单条信息文本
         * 必填
         */
        private String title;
        /**
         * 必填
         * 点击单条信息到跳转链接
         */
        private String messageURL;
        /**
         * 必填
         * 单条信息后面图片的URL
         */
        private String picURL;

        public String getTitle() {
            return title;
        }

        public void setTitle(String title) {
            this.title = title;
        }

        public String getMessageURL() {
            return messageURL;
        }

        public void setMessageURL(String messageURL) {
            this.messageURL = messageURL;
        }

        public String getPicURL() {
            return picURL;
        }

        public void setPicURL(String picURL) {
            this.picURL = picURL;
        }
    }
}