package org.haier.shop.params.customer;

import java.io.Serializable;
import java.math.BigDecimal;

public class RechargeForNYCusDetailParams implements Serializable {
    private static final long serialVersionUID = 1L;
    //店铺编号
    private String shopUnique;
    //需要操作的会员号
    private String cusUnique;
    //需要增加的余额
    private BigDecimal rechargeMoney;
    //需要扣除的积分
    private Integer deductPoint;
    //会员类型
    private String cusType;

    public String getShopUnique() {
        return shopUnique;
    }

    public void setShopUnique(String shopUnique) {
        this.shopUnique = shopUnique;
    }

    public String getCusUnique() {
        return cusUnique;
    }

    public void setCusUnique(String cusUnique) {
        this.cusUnique = cusUnique;
    }

    public BigDecimal getRechargeMoney() {
        return rechargeMoney;
    }

    public void setRechargeMoney(BigDecimal rechargeMoney) {
        this.rechargeMoney = rechargeMoney;
    }

    public Integer getDeductPoint() {
        return deductPoint;
    }

    public void setDeductPoint(Integer deductPoint) {
        this.deductPoint = deductPoint;
    }

    public String getCusType() {
        return cusType;
    }

    public void setCusType(String cusType) {
        this.cusType = cusType;
    }
}
