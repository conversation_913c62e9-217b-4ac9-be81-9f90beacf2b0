package org.haier.shop.params.purchase;

import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.List;

/**
 * @Description 退货参数
 * @ClassName PurchaseGoodsRetParam
 * <AUTHOR>
 * @Date 2024/5/20 8:58
 **/
public class PurchaseGoodsRetParam {

    /**
     * 店铺编号，方便统计退货信息
     */
    @NotNull(message = "店铺编码不能为空")
    private Long shopUnique;

    /**
     * 进货订单号
     */
    private String selfPurchaseUnique;

    /**
     * 供货商编号
     */
    private Long supplierUnique;

    /**
     * 本次退货商品总数量
     */
    private BigDecimal totalCount;

    /**
     * 本次退货总金额
     */
    private BigDecimal totalPrice;

    /**
     * 本次退货备注
     */
    private String remark;

    /**
     * 退货申请员工编号
     */
    private String createStaffId;

    /**
     * 退货商品列表
     */
    List<PurchaseGoodsDetailRetParam> goodsDetailList;

    public Long getShopUnique() {
        return shopUnique;
    }

    public void setShopUnique(Long shopUnique) {
        this.shopUnique = shopUnique;
    }

    public String getSelfPurchaseUnique() {
        return selfPurchaseUnique;
    }

    public void setSelfPurchaseUnique(String selfPurchaseUnique) {
        this.selfPurchaseUnique = selfPurchaseUnique;
    }

    public Long getSupplierUnique() {
        return supplierUnique;
    }

    public void setSupplierUnique(Long supplierUnique) {
        this.supplierUnique = supplierUnique;
    }

    public BigDecimal getTotalCount() {
        return totalCount;
    }

    public void setTotalCount(BigDecimal totalCount) {
        this.totalCount = totalCount;
    }

    public BigDecimal getTotalPrice() {
        return totalPrice;
    }

    public void setTotalPrice(BigDecimal totalPrice) {
        this.totalPrice = totalPrice;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public String getCreateStaffId() {
        return createStaffId;
    }

    public void setCreateStaffId(String createStaffId) {
        this.createStaffId = createStaffId;
    }

    public List<PurchaseGoodsDetailRetParam> getGoodsDetailList() {
        return goodsDetailList;
    }

    public void setGoodsDetailList(List<PurchaseGoodsDetailRetParam> goodsDetailList) {
        this.goodsDetailList = goodsDetailList;
    }
}
