package org.haier.shop.params.purchase;

import java.math.BigDecimal;
import java.util.Date;

/**
 * @Description 采购订单商品明细
 * @ClassName PurchaseGoodsDetailParam
 * <AUTHOR>
 * @Date 2024/5/18 15:18
 **/
public class PurchaseGoodsDetailParam {
    /**
     * 商品名称
     */
    private String goodsName;
    /**
     * 商品条码
     */
    private String goodsBarcode;
    /**
     * 进货数量
     */
    private BigDecimal goodsCount;
    /**
     * 进货单价
     */
    private BigDecimal goodsInPrice;
    /**
     * 商品单位
     */
    private String unitName;
    /**
     * 是否赠品：1非赠品 2赠品
     */
    private Integer giftType;

    /**
     * 生产日期
     */
    private Date goodsProd;

    /**
     * 保质期
     */
    private Integer goodsLife;

    /**
     * 过期日
     */
    private Date goodsExp;

    public String getGoodsName() {
        return goodsName;
    }

    public void setGoodsName(String goodsName) {
        this.goodsName = goodsName;
    }

    public String getGoodsBarcode() {
        return goodsBarcode;
    }

    public void setGoodsBarcode(String goodsBarcode) {
        this.goodsBarcode = goodsBarcode;
    }

    public BigDecimal getGoodsCount() {
        return goodsCount;
    }

    public void setGoodsCount(BigDecimal goodsCount) {
        this.goodsCount = goodsCount;
    }

    public BigDecimal getGoodsInPrice() {
        return goodsInPrice;
    }

    public void setGoodsInPrice(BigDecimal goodsInPrice) {
        this.goodsInPrice = goodsInPrice;
    }

    public String getUnitName() {
        return unitName;
    }

    public void setUnitName(String unitName) {
        this.unitName = unitName;
    }

    public Integer getGiftType() {
        return giftType;
    }

    public void setGiftType(Integer giftType) {
        this.giftType = giftType;
    }

    public Date getGoodsProd() {
        return goodsProd;
    }

    public void setGoodsProd(Date goodsProd) {
        this.goodsProd = goodsProd;
    }

    public Integer getGoodsLife() {
        return goodsLife;
    }

    public void setGoodsLife(Integer goodsLife) {
        this.goodsLife = goodsLife;
    }

    public Date getGoodsExp() {
        return goodsExp;
    }

    public void setGoodsExp(Date goodsExp) {
        this.goodsExp = goodsExp;
    }
}
