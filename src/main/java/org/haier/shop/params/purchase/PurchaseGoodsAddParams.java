package org.haier.shop.params.purchase;

import org.hibernate.validator.constraints.NotEmpty;

import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.List;

/**
 * @Description 采购商品新增参数
 * @ClassName PurchaseGoodsAddParams
 * <AUTHOR>
 * @Date 2024/5/18 15:09
 **/
public class PurchaseGoodsAddParams {
    /**
     * 店铺编码
     */
    @NotNull(message = "店铺编码不能为空")
    private Long shopUnique;
    /**
     * 供货商编码
     */
    private Long supplierUnique;
    /**
     * 总数量
     */
    private BigDecimal totalCount;
    /**
     * 总金额
     */
    private BigDecimal totalPrice;
    /**
     * 欠款金额
     */
    private BigDecimal arrearsPrice;
    /**
     *支付状态：0全部支付 1欠款
     */
    private Integer payStatus;
    /**
     * 采购单状态：0待收货 1已完成 2已取消
     */
    private Integer purchaseStatus;
    /**
     * 订单备注
     */
    private String remark;
    /**
     * 创建人ID
     */
    private Long createStaffId;
    /**
     * 修改人ID
     */
    private Long updateStaffId;
    /**
     * 订单明细列表
     */
    @NotEmpty(message = "商品列表不能为空")
    private List<PurchaseGoodsDetailParam> goodsDetails;
    /**
     * IP
     */
    private String networkIp;

    public Long getShopUnique() {
        return shopUnique;
    }

    public void setShopUnique(Long shopUnique) {
        this.shopUnique = shopUnique;
    }

    public Long getSupplierUnique() {
        return supplierUnique;
    }

    public void setSupplierUnique(Long supplierUnique) {
        this.supplierUnique = supplierUnique;
    }

    public BigDecimal getTotalCount() {
        return totalCount;
    }

    public void setTotalCount(BigDecimal totalCount) {
        this.totalCount = totalCount;
    }

    public BigDecimal getTotalPrice() {
        return totalPrice;
    }

    public void setTotalPrice(BigDecimal totalPrice) {
        this.totalPrice = totalPrice;
    }

    public BigDecimal getArrearsPrice() {
        return arrearsPrice;
    }

    public void setArrearsPrice(BigDecimal arrearsPrice) {
        this.arrearsPrice = arrearsPrice;
    }

    public Integer getPayStatus() {
        return payStatus;
    }

    public void setPayStatus(Integer payStatus) {
        this.payStatus = payStatus;
    }

    public Integer getPurchaseStatus() {
        return purchaseStatus;
    }

    public void setPurchaseStatus(Integer purchaseStatus) {
        this.purchaseStatus = purchaseStatus;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public Long getCreateStaffId() {
        return createStaffId;
    }

    public void setCreateStaffId(Long createStaffId) {
        this.createStaffId = createStaffId;
    }

    public Long getUpdateStaffId() {
        return updateStaffId;
    }

    public void setUpdateStaffId(Long updateStaffId) {
        this.updateStaffId = updateStaffId;
    }

    public List<PurchaseGoodsDetailParam> getGoodsDetails() {
        return goodsDetails;
    }

    public void setGoodsDetails(List<PurchaseGoodsDetailParam> goodsDetails) {
        this.goodsDetails = goodsDetails;
    }

    public String getNetworkIp() {
        return networkIp;
    }

    public void setNetworkIp(String networkIp) {
        this.networkIp = networkIp;
    }
}
