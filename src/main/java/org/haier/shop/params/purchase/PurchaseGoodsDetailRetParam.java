package org.haier.shop.params.purchase;

import org.haier.shop.params.goods.OutStockBatchParam;

import java.math.BigDecimal;
import java.util.List;

/**
 * @Description 退货商品列表
 * @ClassName PurchaseGoodsDetailRetParam
 * <AUTHOR>
 * @Date 2024/5/20 9:19
 **/
public class PurchaseGoodsDetailRetParam {

    /**
     * 商品ID
     */
    private Long goodsId;

    /**
     * 退货商品名称
     */
    private String goodsName;

    /**
     * 退货商品编码
     */
    private String goodsBarcode;

    /**
     * 退货商品退款时单价，可与进货时价格不同，但退款小计累计不应超过进货单价
     */
    private BigDecimal goodsInPrice;

    /**
     * 退货数量，同一个订单累计退货数量不应超过进货数量
     */
    private BigDecimal goodsCount;

    /**
     * 退货时商品单位，同进货
     */
    private String unitName;

    /**
     * 是否赠品：1、非赠品；2、赠品
     */
    private Integer giftType;

    /**
     * 批次列表
     */
    List<OutStockBatchParam> batchParamList;

    public Long getGoodsId() {
        return goodsId;
    }

    public void setGoodsId(Long goodsId) {
        this.goodsId = goodsId;
    }

    public String getGoodsName() {
        return goodsName;
    }

    public void setGoodsName(String goodsName) {
        this.goodsName = goodsName;
    }

    public String getGoodsBarcode() {
        return goodsBarcode;
    }

    public void setGoodsBarcode(String goodsBarcode) {
        this.goodsBarcode = goodsBarcode;
    }

    public BigDecimal getGoodsInPrice() {
        return goodsInPrice;
    }

    public void setGoodsInPrice(BigDecimal goodsInPrice) {
        this.goodsInPrice = goodsInPrice;
    }

    public BigDecimal getGoodsCount() {
        return goodsCount;
    }

    public void setGoodsCount(BigDecimal goodsCount) {
        this.goodsCount = goodsCount;
    }

    public String getUnitName() {
        return unitName;
    }

    public void setUnitName(String unitName) {
        this.unitName = unitName;
    }

    public Integer getGiftType() {
        return giftType;
    }

    public void setGiftType(Integer giftType) {
        this.giftType = giftType;
    }

    public List<OutStockBatchParam> getBatchParamList() {
        return batchParamList;
    }

    public void setBatchParamList(List<OutStockBatchParam> batchParamList) {
        this.batchParamList = batchParamList;
    }
}
