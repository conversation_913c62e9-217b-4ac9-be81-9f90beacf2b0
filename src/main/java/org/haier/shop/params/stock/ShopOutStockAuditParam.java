package org.haier.shop.params.stock;

import javax.validation.constraints.NotNull;

/**
 * @Description 出库单审核参数
 * @ClassName ShopOutStockAuditParam
 * <AUTHOR>
 * @Date 2024/5/10 14:05
 **/
public class ShopOutStockAuditParam {

    /**
     * 出库单ID
     */
    @NotNull(message = "出库单ID不能为空")
    private Long shopStockDetailId;

    /**
     * 审核状态
     */
    @NotNull(message = "审核结果不能为空")
    private Integer auditStatus;

    /**
     * 审核内容
     */
    private String auditContent;

    /**
     * 操作人ID
     */
    @NotNull(message = "操作人ID不能为空")
    private Long userId;

    public Long getShopStockDetailId() {
        return shopStockDetailId;
    }

    public void setShopStockDetailId(Long shopStockDetailId) {
        this.shopStockDetailId = shopStockDetailId;
    }

    public String getAuditContent() {
        return auditContent;
    }

    public void setAuditContent(String auditContent) {
        this.auditContent = auditContent;
    }

    public Long getUserId() {
        return userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }

    public Integer getAuditStatus() {
        return auditStatus;
    }

    public void setAuditStatus(Integer auditStatus) {
        this.auditStatus = auditStatus;
    }
}
