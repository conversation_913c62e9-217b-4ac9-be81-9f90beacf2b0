package org.haier.shop.params.stock;

import org.haier.shop.entity.ShopStockDetail;

import javax.validation.constraints.NotNull;

/**
 * @Description
 * @ClassName ShopStockDetailQueryParam
 * <AUTHOR>
 * @Date 2024/5/1 8:59
 **/
public class ShopStockDetailQueryParam extends ShopStockDetail {

    /**
     * 店铺编码
     */
    @NotNull(message = "店铺编码不能为空")
    private String shopUnique;

    /**
     * 页码
     */
    @NotNull(message = "页码不能为空")
    private Integer page;
    /**
     * 返回记录数量
     */
    @NotNull(message = "查询数量不能为空")
    private Integer limit;

    /**
     * 开始时间
     */
    private String startTime;

    /**
     * 结束时间
     */
    private String endTime;

    /**
     * 商品条码
     */
    private String goodsBarcode;

    @Override
    public String getShopUnique() {
        return shopUnique;
    }

    @Override
    public void setShopUnique(String shopUnique) {
        this.shopUnique = shopUnique;
    }

    public Integer getPage() {
        return page;
    }

    public void setPage(Integer page) {
        this.page = page;
    }

    public Integer getLimit() {
        return limit;
    }

    public void setLimit(Integer limit) {
        this.limit = limit;
    }

    public String getStartTime() {
        return startTime;
    }

    public void setStartTime(String startTime) {
        this.startTime = startTime;
    }

    public String getEndTime() {
        return endTime;
    }

    public void setEndTime(String endTime) {
        this.endTime = endTime;
    }

    public String getGoodsBarcode() {
        return goodsBarcode;
    }

    public void setGoodsBarcode(String goodsBarcode) {
        this.goodsBarcode = goodsBarcode;
    }
}
