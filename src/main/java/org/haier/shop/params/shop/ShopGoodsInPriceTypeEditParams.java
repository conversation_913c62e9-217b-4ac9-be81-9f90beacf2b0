package org.haier.shop.params.shop;

import org.hibernate.validator.constraints.NotBlank;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * @ClassName ShopGoodsInPriceTypeEditParams
 * <AUTHOR>
 * @Date 2024/4/25 16:22
 */
public class ShopGoodsInPriceTypeEditParams implements Serializable {
    @NotBlank(message = "请输入店铺编号")
    private String shopUnique;
    @NotNull(message = "请选择库存管理方式")
    private Integer goodsInPriceType;

    public String getShopUnique() {
        return shopUnique;
    }

    public void setShopUnique(String shopUnique) {
        this.shopUnique = shopUnique;
    }

    public Integer getGoodsInPriceType() {
        return goodsInPriceType;
    }

    public void setGoodsInPriceType(Integer goodsInPriceType) {
        this.goodsInPriceType = goodsInPriceType;
    }
}
