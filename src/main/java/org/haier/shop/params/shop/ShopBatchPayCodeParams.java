package org.haier.shop.params.shop;

import javax.validation.constraints.NotNull;

/**
 * @Description
 * @ClassName ShopBatchPayCodeParams
 * <AUTHOR>
 * @Date 2024/6/13 8:36
 **/
public class ShopBatchPayCodeParams {

    @NotNull(message = "店铺开始ID")
    private Long startShopId;
    @NotNull(message = "店铺结束ID")
    private Long endShopId;

    public Long getStartShopId() {
        return startShopId;
    }

    public void setStartShopId(Long startShopId) {
        this.startShopId = startShopId;
    }

    public Long getEndShopId() {
        return endShopId;
    }

    public void setEndShopId(Long endShopId) {
        this.endShopId = endShopId;
    }
}
