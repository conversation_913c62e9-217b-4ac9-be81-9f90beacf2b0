package org.haier.shop.params.shop;

import org.hibernate.validator.constraints.NotBlank;

/**
 * @Description 店铺批量注册参数
 * @ClassName ShopBatchRegisterParams
 * <AUTHOR>
 * @Date 2024/6/12 16:30
 **/
public class ShopBatchRegisterParams {


    @NotBlank(message = "店铺详细地址不能为空")
    private String shopAddressDetail;

    @NotBlank(message = "省不能为空")
    private String province;

    @NotBlank(message = "市不能为空")
    private String city;

    @NotBlank(message = "街道不能为空")
    private String district;

    @NotBlank(message = "商铺所在区县编号")
    private String areaDictNum;

    @NotBlank(message = "店铺所在纬度")
    private String shopLatitude;
    @NotBlank(message = "店铺所在经度")
    private String shopLongitude;
    @NotBlank(message = "地图街道信息code")
    private String townCode;
    @NotBlank(message = "邀请码不能为空")
    private String invitationCode;

    public String getShopAddressDetail() {
        return shopAddressDetail;
    }

    public void setShopAddressDetail(String shopAddressDetail) {
        this.shopAddressDetail = shopAddressDetail;
    }

    public String getProvince() {
        return province;
    }

    public void setProvince(String province) {
        this.province = province;
    }

    public String getCity() {
        return city;
    }

    public void setCity(String city) {
        this.city = city;
    }

    public String getDistrict() {
        return district;
    }

    public void setDistrict(String district) {
        this.district = district;
    }

    public String getAreaDictNum() {
        return areaDictNum;
    }

    public void setAreaDictNum(String areaDictNum) {
        this.areaDictNum = areaDictNum;
    }

    public String getShopLatitude() {
        return shopLatitude;
    }

    public void setShopLatitude(String shopLatitude) {
        this.shopLatitude = shopLatitude;
    }

    public String getShopLongitude() {
        return shopLongitude;
    }

    public void setShopLongitude(String shopLongitude) {
        this.shopLongitude = shopLongitude;
    }

    public String getTownCode() {
        return townCode;
    }

    public void setTownCode(String townCode) {
        this.townCode = townCode;
    }

    public String getInvitationCode() {
        return invitationCode;
    }

    public void setInvitationCode(String invitationCode) {
        this.invitationCode = invitationCode;
    }
}
