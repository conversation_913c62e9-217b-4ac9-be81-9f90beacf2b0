package org.haier.shop.params.restockPlan;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;

public class GetGoodsSupplierMsgParams {
    /**
     * 店铺编号
     */
    @NotNull(message = "请输入店铺编号")
    @Min(message="请输入店铺编号",value=1)
    private String shopUnique;

    public String getShopUnique() {
        return shopUnique;
    }

    public void setShopUnique(String shopUnique) {
        this.shopUnique = shopUnique;
    }
}
