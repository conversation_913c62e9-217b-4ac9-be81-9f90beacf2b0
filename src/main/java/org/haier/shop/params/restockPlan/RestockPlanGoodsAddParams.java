package org.haier.shop.params.restockPlan;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;

public class RestockPlanGoodsAddParams {
    /**
     * 计划编号
     */
    @NotNull(message = "请输入补货计划ID")
    @Min(message="请输入补货计划ID",value=1)
    private Long restockPlanId;
    /**
     * 店铺编号
     */
    @NotNull(message = "请输入店铺编号")
    @Min(message="请输入店铺编号",value=1)
    private Long shopUnique;
    /**
     * 供货商编号
     */
    @NotNull(message = "请输入供货商编号")
    private String supplierUnique;
    /**
     * 采购数量
     */
    @NotNull(message = "请输入采购数量")
    private BigDecimal goodsCount;
    /**
     * 采购商品进价
     */
    @NotNull(message = "请输入采购商品进价")
    private BigDecimal goodsInPrice;
    /**
     * 商品条码
     */
    @NotNull(message = "请输入商品条码")
    private String goodsBarcode;
    /**
     * 操作人姓名
     */
    @NotNull(message = "请输入创建人")
    private String createUser;

    public Long getRestockPlanId() {
        return restockPlanId;
    }

    public void setRestockPlanId(Long restockPlanId) {
        this.restockPlanId = restockPlanId;
    }

    public Long getShopUnique() {
        return shopUnique;
    }

    public void setShopUnique(Long shopUnique) {
        this.shopUnique = shopUnique;
    }

    public String getSupplierUnique() {
        return supplierUnique;
    }

    public void setSupplierUnique(String supplierUnique) {
        this.supplierUnique = supplierUnique;
    }

    public BigDecimal getGoodsCount() {
        return goodsCount;
    }

    public void setGoodsCount(BigDecimal goodsCount) {
        this.goodsCount = goodsCount;
    }

    public String getCreateUser() {
        return createUser;
    }

    public void setCreateUser(String createUser) {
        this.createUser = createUser;
    }

    public String getGoodsBarcode() {
        return goodsBarcode;
    }

    public void setGoodsBarcode(String goodsBarcode) {
        this.goodsBarcode = goodsBarcode;
    }

    public BigDecimal getGoodsInPrice() {
        return goodsInPrice;
    }

    public void setGoodsInPrice(BigDecimal goodsInPrice) {
        this.goodsInPrice = goodsInPrice;
    }
}
