package org.haier.shop.params.restockPlan;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;

public class ModifyGoodsParams {
    /**
     * 店铺编号
     */
    @NotNull(message = "请输入店铺编号")
    @Min(message="请输入店铺编号",value=1)
    private Long shopUnique;
    /**
     * 补货计划ID
     */
    @NotNull(message = "请输入补货计划ID")
    @Min(message="请输入补货计划ID",value=1)
    private Long shopRestockplanId;
    /**
     * 补货计划供货商ID
     */
    private Long shopRestockplanPresentId;
    /**
     * 商品条码
     */
    private String goodsBarcode;
    /**
     * 采购数量
     */
    private BigDecimal goodsCount;
    /**
     * 商品进价
     */
    private BigDecimal goodsInPrice;
    /**
     * 采购商品ID
     */
    @NotNull(message = "请输入采购商品编号")
    @Min(message="请输入采购商品编号",value=1)
    private Long shopRestockplanGoodsId;
    /**
     * 更新标志:1-修改；2-删除
     */
    @NotNull(message = "请输入更新标志")
    @Min(message="请输入更新标志",value=1)
    private Integer delFlag;

    public Long getShopUnique() {
        return shopUnique;
    }

    public void setShopUnique(Long shopUnique) {
        this.shopUnique = shopUnique;
    }

    public Long getShopRestockplanId() {
        return shopRestockplanId;
    }

    public void setShopRestockplanId(Long shopRestockplanId) {
        this.shopRestockplanId = shopRestockplanId;
    }

    public Long getShopRestockplanPresentId() {
        return shopRestockplanPresentId;
    }

    public void setShopRestockplanPresentId(Long shopRestockplanPresentId) {
        this.shopRestockplanPresentId = shopRestockplanPresentId;
    }

    public String getGoodsBarcode() {
        return goodsBarcode;
    }

    public void setGoodsBarcode(String goodsBarcode) {
        this.goodsBarcode = goodsBarcode;
    }

    public BigDecimal getGoodsCount() {
        return goodsCount;
    }

    public void setGoodsCount(BigDecimal goodsCount) {
        this.goodsCount = goodsCount;
    }

    public BigDecimal getGoodsInPrice() {
        return goodsInPrice;
    }

    public void setGoodsInPrice(BigDecimal goodsInPrice) {
        this.goodsInPrice = goodsInPrice;
    }

    public Long getShopRestockplanGoodsId() {
        return shopRestockplanGoodsId;
    }

    public void setShopRestockplanGoodsId(Long shopRestockplanGoodsId) {
        this.shopRestockplanGoodsId = shopRestockplanGoodsId;
    }

    public Integer getDelFlag() {
        return delFlag;
    }

    public void setDelFlag(Integer delFlag) {
        this.delFlag = delFlag;
    }
}
