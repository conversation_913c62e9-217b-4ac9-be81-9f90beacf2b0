package org.haier.shop.params.shopSupBill;

import javax.validation.constraints.NotNull;

public class AddSupRelParams {
    /**
     * 店铺编号
     */
    @NotNull(message = "请输入店铺编号")
    private String shopUnique;
    /**
     * 供应商编号
     */
    @NotNull(message = "请输入供应商编号")
    private String supplierUnique;
    /**
     * 供应商名称
     */
    @NotNull(message = "请输入供应商名称")
    private String supplierName;
    /**
     * 供应商联系人
     */
    @NotNull(message = "请输入供应商联系人")
    private String contacts;
    /**
     * 联系人手机号
     */
    @NotNull(message = "请输入联系方式")
    private String contactMobile;
    /**
     * 地址
     */
    private String address;

    public String getShopUnique() {
        return shopUnique;
    }

    public void setShopUnique(String shopUnique) {
        this.shopUnique = shopUnique;
    }

    public String getSupplierUnique() {
        return supplierUnique;
    }

    public void setSupplierUnique(String supplierUnique) {
        this.supplierUnique = supplierUnique;
    }

    public String getSupplierName() {
        return supplierName;
    }

    public void setSupplierName(String supplierName) {
        this.supplierName = supplierName;
    }

    public String getContacts() {
        return contacts;
    }

    public void setContacts(String contacts) {
        this.contacts = contacts;
    }

    public String getContactMobile() {
        return contactMobile;
    }

    public void setContactMobile(String contactMobile) {
        this.contactMobile = contactMobile;
    }

    public String getAddress() {
        return address;
    }

    public void setAddress(String address) {
        this.address = address;
    }
}
