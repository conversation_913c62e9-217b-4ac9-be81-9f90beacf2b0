package org.haier.shop.params.shopSupBill.supExternalCall;

import java.util.List;

/**
 * 店铺商品绑定供货商
 *
 * @ClassName CustomerGoodsAddParams
 * <AUTHOR>
 * @Date 2023/9/11 14:02
 **/
public class CustomerGoodsAddParams {

    /**
     * 供应商编码
     */
    private String supplierUnique;

    /**
     * 客户编码
     */
    private String customerUnique;

    /**
     * 商品列表
     */
    private List<CustomerGoodsParams> goodsList;

    public String getSupplierUnique() {
        return supplierUnique;
    }

    public void setSupplierUnique(String supplierUnique) {
        this.supplierUnique = supplierUnique;
    }

    public String getCustomerUnique() {
        return customerUnique;
    }

    public void setCustomerUnique(String customerUnique) {
        this.customerUnique = customerUnique;
    }

    public List<CustomerGoodsParams> getGoodsList() {
        return goodsList;
    }

    public void setGoodsList(List<CustomerGoodsParams> goodsList) {
        this.goodsList = goodsList;
    }
}
