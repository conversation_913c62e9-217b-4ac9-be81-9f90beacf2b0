package org.haier.shop.params.shopSupBill.supExternalCall;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.List;

/**
 * 补货单新增参数
 *
 * @ClassName OrderAddParams
 * <AUTHOR>
 * @Date 2023/9/11 11:39
 **/
public class OrderAddParams {
    /**
     * 供货商编码
     */
    @NotNull(message = "供货商编码不能为空")
    private String supplierUnique;

    /**
     * 商户编码
     */
    @NotNull(message = "客户编码不能为空")
    private String customerUnique;

    /**
     * 订单编码
     */
    @NotNull(message = "订单编码不能为空")
    private String orderNo;

    /**
     * 商品种类数量
     */
    @NotNull(message = "商品种类数量不能为空")
    @Min(value = 0, message = "商品种类数量不能小于等于0")
    private Integer categoryCount;

    /**
     * 商品总数量
     */
    @NotNull(message = "商品总数量不能为空")
    @Min(value = 0, message = "商品总数量不能小于等于0")
    private BigDecimal totalCount;

    /**
     * 补货单商品明细列表
     */
    @NotNull(message = "订单商品不能为空")
    private List<OrderDetailAddParams> detailList;

    public String getSupplierUnique() {
        return supplierUnique;
    }

    public void setSupplierUnique(String supplierUnique) {
        this.supplierUnique = supplierUnique;
    }

    public String getCustomerUnique() {
        return customerUnique;
    }

    public void setCustomerUnique(String customerUnique) {
        this.customerUnique = customerUnique;
    }

    public String getOrderNo() {
        return orderNo;
    }

    public void setOrderNo(String orderNo) {
        this.orderNo = orderNo;
    }

    public Integer getCategoryCount() {
        return categoryCount;
    }

    public void setCategoryCount(Integer categoryCount) {
        this.categoryCount = categoryCount;
    }

    public BigDecimal getTotalCount() {
        return totalCount;
    }

    public void setTotalCount(BigDecimal totalCount) {
        this.totalCount = totalCount;
    }

    public List<OrderDetailAddParams> getDetailList() {
        return detailList;
    }

    public void setDetailList(List<OrderDetailAddParams> detailList) {
        this.detailList = detailList;
    }
}
