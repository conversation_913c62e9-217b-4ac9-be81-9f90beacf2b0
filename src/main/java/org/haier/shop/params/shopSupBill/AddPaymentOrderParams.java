package org.haier.shop.params.shopSupBill;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.List;

public class AddPaymentOrderParams extends ShopUniqueParams{
    /**
     * 购销单编号
     */
    @NotNull(message = "请输入购销单编号")
    @Min(message="请输入购销单编号",value=1)
    private String billId;
    /**
     * 付款凭证
     */
    @NotNull(message = "请上传凭证")
    private List<String> voucherPicturepath;
    /**
     * 付款金额
     */
    @NotNull(message = "请输入付款金额")
    private BigDecimal paymentMoney;
    /**
     * 备注
     */
    private String remark;
    /**
     * 操作人ID
     */
    @NotNull(message = "请输入付款人ID")
    private Long createId;
    /**
     * 操作人姓名
     */
    @NotNull(message = "请输入付款人姓名")
    private String createBy;

    public String getBillId() {
        return billId;
    }

    public void setBillId(String billId) {
        this.billId = billId;
    }

    public List<String> getVoucherPicturepath() {
        return voucherPicturepath;
    }

    public void setVoucherPicturepath(List<String> voucherPicturepath) {
        this.voucherPicturepath = voucherPicturepath;
    }

    public BigDecimal getPaymentMoney() {
        return paymentMoney;
    }

    public void setPaymentMoney(BigDecimal paymentMoney) {
        this.paymentMoney = paymentMoney;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public Long getCreateId() {
        return createId;
    }

    public void setCreateId(Long createId) {
        this.createId = createId;
    }

    public String getCreateBy() {
        return createBy;
    }

    public void setCreateBy(String createBy) {
        this.createBy = createBy;
    }
}
