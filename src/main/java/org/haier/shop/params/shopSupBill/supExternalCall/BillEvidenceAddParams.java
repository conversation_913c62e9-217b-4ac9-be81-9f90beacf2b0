package org.haier.shop.params.shopSupBill.supExternalCall;

import java.util.List;

/**
 * 上传付款凭证
 *
 * @ClassName BillEvidenceAddParams
 * <AUTHOR>
 * @Date 2023/9/13 9:41
 **/
public class BillEvidenceAddParams {

    /**
     * 供货商编码
     */
    private String supplierUnique;
    /**
     * 客户编码
     */
    private String customerUnique;
    /**
     * 购销单编号
     */
    private String billNo;
    /**
     * 付款人
     */
    private String payUser;
    /**
     * 付款备注
     */
    private String payRemark;
    /**
     * 图片地址列表
     */
    private List<String> imageUrlList;

    public String getSupplierUnique() {
        return supplierUnique;
    }

    public void setSupplierUnique(String supplierUnique) {
        this.supplierUnique = supplierUnique;
    }

    public String getCustomerUnique() {
        return customerUnique;
    }

    public void setCustomerUnique(String customerUnique) {
        this.customerUnique = customerUnique;
    }

    public String getPayUser() {
        return payUser;
    }

    public void setPayUser(String payUser) {
        this.payUser = payUser;
    }

    public String getBillNo() {
        return billNo;
    }

    public void setBillNo(String billNo) {
        this.billNo = billNo;
    }

    public String getPayRemark() {
        return payRemark;
    }

    public void setPayRemark(String payRemark) {
        this.payRemark = payRemark;
    }

    public List<String> getImageUrlList() {
        return imageUrlList;
    }

    public void setImageUrlList(List<String> imageUrlList) {
        this.imageUrlList = imageUrlList;
    }
}
