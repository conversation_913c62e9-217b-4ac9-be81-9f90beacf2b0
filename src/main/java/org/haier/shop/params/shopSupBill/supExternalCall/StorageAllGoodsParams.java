package org.haier.shop.params.shopSupBill.supExternalCall;

import org.haier.shop.params.shopSupBill.QueryBillGoodsListParams;

import javax.validation.constraints.NotNull;

public class StorageAllGoodsParams extends QueryBillGoodsListParams {
    /**
     * 操作人
     */
    @NotNull(message = "请输入操作人")
    private Long createId;
    /**
     * 操作人姓名
     */
    @NotNull(message = "请输入操作人姓名")
    private String createBy;

    public Long getCreateId() {
        return createId;
    }

    public void setCreateId(Long createId) {
        this.createId = createId;
    }

    public String getCreateBy() {
        return createBy;
    }

    public void setCreateBy(String createBy) {
        this.createBy = createBy;
    }
}
