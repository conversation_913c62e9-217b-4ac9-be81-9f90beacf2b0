package org.haier.shop.params.shopSupBill;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;

public class CheckGoodsParams extends QueryBillGoodsListParams{
    /**
     *购销单明细ID
     */
    @NotNull(message = "请输入购销单明细ID")
    @Min(message="请输入购销单明细ID",value=1)
    private Long detailId;
    /**
     *供应商编号
     */
    @NotNull(message = "请输入供应商编号")
    private String supplierUnique;
    /**
     *入库数量
     */
    @NotNull(message = "请输入入库数量")
    private BigDecimal goodsActualCount;
    /**
     *进货价
     */
    @NotNull(message = "请输入进货价")
    private BigDecimal goodsInPrice;
    /**
     *零售价
     */
    @NotNull(message = "请输入零售价")
    private BigDecimal goodsSalePrice;
    /**
     *网单价
     */
    @NotNull(message = "请输入网单价")
    private BigDecimal goodsWebSalePrice;
    /**
     *会员价
     */
    @NotNull(message = "请输入会员价")
    private BigDecimal goodsCusPrice;
    /**
     *员工编号
     */
    @NotNull(message = "请输入员工编号")
    private Long createId;
    /**
     *员工姓名
     */
    @NotNull(message = "请输入员工姓名")
    private String createBy;

    public Long getDetailId() {
        return detailId;
    }

    public void setDetailId(Long detailId) {
        this.detailId = detailId;
    }

    public BigDecimal getGoodsActualCount() {
        return goodsActualCount;
    }

    public void setGoodsActualCount(BigDecimal goodsPurchaseCountFact) {
        this.goodsActualCount = goodsPurchaseCountFact;
    }

    public String getSupplierUnique() {
        return supplierUnique;
    }

    public void setSupplierUnique(String supplierUnique) {
        this.supplierUnique = supplierUnique;
    }

    public BigDecimal getGoodsInPrice() {
        return goodsInPrice;
    }

    public void setGoodsInPrice(BigDecimal goodsInPrice) {
        this.goodsInPrice = goodsInPrice;
    }

    public BigDecimal getGoodsSalePrice() {
        return goodsSalePrice;
    }

    public void setGoodsSalePrice(BigDecimal goodsSalePrice) {
        this.goodsSalePrice = goodsSalePrice;
    }

    public BigDecimal getGoodsWebSalePrice() {
        return goodsWebSalePrice;
    }

    public void setGoodsWebSalePrice(BigDecimal goodsWebSalePrice) {
        this.goodsWebSalePrice = goodsWebSalePrice;
    }

    public BigDecimal getGoodsCusPrice() {
        return goodsCusPrice;
    }

    public void setGoodsCusPrice(BigDecimal goodsCusPrice) {
        this.goodsCusPrice = goodsCusPrice;
    }

    public Long getCreateId() {
        return createId;
    }

    public void setCreateId(Long createId) {
        this.createId = createId;
    }

    public String getCreateBy() {
        return createBy;
    }

    public void setCreateBy(String createBy) {
        this.createBy = createBy;
    }
}
