package org.haier.shop.params;

import java.math.BigDecimal;

/**
 * 店铺商品绑定供货商
 *
 * @ClassName CustomerGoodsBingParams
 * <AUTHOR>
 * @Date 2023/9/11 14:02
 **/
public class CustomerGoodsBingParams {

    /**
     * 供应商编码
     */
    private String supplierUnique;

    /**
     * 原客户编码
     */
    private String oldSupplierUnique;
    /**
     * 客户编码
     */
    private String customerUnique;

    /**
     * 商品名称
     */
    private String goodsName;

    /**
     * 商品条码
     */
    private String goodsBarcode;

    /**
     * 商品单位
     */
    private String goodsUnit;

    /**
     * 保质期天数
     */
    private Integer expirationDate;

    /**
     * 商品销售价
     */
    private BigDecimal goodsSalePrice;

    /**
     * 商品图片保存路径
     */
    private String goodsImageUrl;

    public String getGoodsName() {
        return goodsName;
    }

    public void setGoodsName(String goodsName) {
        this.goodsName = goodsName;
    }

    public String getGoodsBarcode() {
        return goodsBarcode;
    }

    public void setGoodsBarcode(String goodsBarcode) {
        this.goodsBarcode = goodsBarcode;
    }

    public String getGoodsUnit() {
        return goodsUnit;
    }

    public void setGoodsUnit(String goodsUnit) {
        this.goodsUnit = goodsUnit;
    }

    public Integer getExpirationDate() {
        return expirationDate;
    }

    public void setExpirationDate(Integer expirationDate) {
        this.expirationDate = expirationDate;
    }

    public BigDecimal getGoodsSalePrice() {
        return goodsSalePrice;
    }

    public void setGoodsSalePrice(BigDecimal goodsSalePrice) {
        this.goodsSalePrice = goodsSalePrice;
    }

    public String getGoodsImageUrl() {
        return goodsImageUrl;
    }

    public void setGoodsImageUrl(String goodsImageUrl) {
        this.goodsImageUrl = goodsImageUrl;
    }

    public String getSupplierUnique() {
        return supplierUnique;
    }

    public void setSupplierUnique(String supplierUnique) {
        this.supplierUnique = supplierUnique;
    }

    public String getCustomerUnique() {
        return customerUnique;
    }

    public void setCustomerUnique(String customerUnique) {
        this.customerUnique = customerUnique;
    }

    public String getOldSupplierUnique() {
        return oldSupplierUnique;
    }

    public void setOldSupplierUnique(String oldSupplierUnique) {
        this.oldSupplierUnique = oldSupplierUnique;
    }
}
