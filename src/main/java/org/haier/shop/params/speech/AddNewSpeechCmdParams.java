package org.haier.shop.params.speech;

import java.io.Serializable;

public class AddNewSpeechCmdParams implements Serializable {
    private String appType;
    private String cmdType;
    private String pageIndex;
    private String cmdSys;
    private String remarks;
    private String cmdDescribe;

    public String getAppType() {
        return appType;
    }

    public void setAppType(String appType) {
        this.appType = appType;
    }

    public String getCmdType() {
        return cmdType;
    }

    public void setCmdType(String cmdType) {
        this.cmdType = cmdType;
    }

    public String getPageIndex() {
        return pageIndex;
    }

    public void setPageIndex(String pageIndex) {
        this.pageIndex = pageIndex;
    }

    public String getCmdSys() {
        return cmdSys;
    }

    public void setCmdSys(String cmdSys) {
        this.cmdSys = cmdSys;
    }

    public String getRemarks() {
        return remarks;
    }

    public void setRemarks(String remarks) {
        this.remarks = remarks;
    }

    public String getCmdDescribe() {
        return cmdDescribe;
    }

    public void setCmdDescribe(String cmdDescribe) {
        this.cmdDescribe = cmdDescribe;
    }

    @Override
    public String toString() {
        return "AddNewSpeechCmdParams{" +
                "appType='" + appType + '\'' +
                ", cmdType='" + cmdType + '\'' +
                ", pageIndex='" + pageIndex + '\'' +
                ", cmdSys='" + cmdSys + '\'' +
                ", remarks='" + remarks + '\'' +
                ", cmdDescribe='" + cmdDescribe + '\'' +
                '}';
    }
}
