package org.haier.shop.params.speech;

import org.haier.shop.params.common.CommonPageQueryParams;
import org.haier.shop.util.PageQuery;

import java.io.Serializable;

public class QuerySpeechCmdListParams extends CommonPageQueryParams implements Serializable {

    //应用类型
    private String appType;
    //搜索的指令
    private String cmdMsg;

    public String getAppType() {
        return appType;
    }

    public void setAppType(String appType) {
        this.appType = appType;
    }

    public String getCmdMsg() {
        return cmdMsg;
    }

    public void setCmdMsg(String cmdMsg) {
        this.cmdMsg = cmdMsg;
    }
}
