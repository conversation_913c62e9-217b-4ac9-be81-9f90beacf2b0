package org.haier.shop.params.speech;

import org.haier.shop.params.common.CommonTimeAndPageParams;

import java.io.Serializable;

public class QuerySpeechListParams extends CommonTimeAndPageParams implements Serializable {

    private String appType;
    private String shopUnique;
    private String cmdSys;
    private Integer progress;

    public String getAppType() {
        return appType;
    }

    public void setAppType(String appType) {
        this.appType = appType;
    }

    public String getShopUnique() {
        return shopUnique;
    }

    public void setShopUnique(String shopUnique) {
        this.shopUnique = shopUnique;
    }

    public String getCmdSys() {
        return cmdSys;
    }

    public void setCmdSys(String cmdSys) {
        this.cmdSys = cmdSys;
    }

    public Integer getProgress() {
        return progress;
    }

    public void setProgress(Integer progress) {
        this.progress = progress;
    }
}
