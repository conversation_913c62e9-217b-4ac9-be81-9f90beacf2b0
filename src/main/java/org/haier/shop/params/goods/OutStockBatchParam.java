package org.haier.shop.params.goods;

import java.math.BigDecimal;

/**
 * @Description 出库批次详情
 * @ClassName OutStockBatchParam
 * <AUTHOR>
 * @Date 2024/5/8 11:01
 **/
public class OutStockBatchParam {

    /**
     * 批次号
     */
    private String batchUnique;
    /**
     * 出库数量
     */
    private BigDecimal goodsCount;

    private BigDecimal goodsContain;

    public BigDecimal getGoodsContain() {
        return goodsContain;
    }

    public void setGoodsContain(BigDecimal goodsContain) {
        this.goodsContain = goodsContain;
    }

    public String getBatchUnique() {
        return batchUnique;
    }

    public void setBatchUnique(String batchUnique) {
        this.batchUnique = batchUnique;
    }

    public BigDecimal getGoodsCount() {
        return goodsCount;
    }

    public void setGoodsCount(BigDecimal goodsCount) {
        this.goodsCount = goodsCount;
    }
}
