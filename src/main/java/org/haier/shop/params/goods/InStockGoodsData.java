package org.haier.shop.params.goods;

import java.math.BigDecimal;

/**
 * @Description 入库商品详情
 * @ClassName OutStockGoodsParam
 * <AUTHOR>
 * @Date 2024/5/8 10:59
 **/
public class InStockGoodsData extends InStockGoodsParam {

    private String foreignKey;

    private BigDecimal goodsContain;

    public String getForeignKey() {
        return foreignKey;
    }

    public void setForeignKey(String foreignKey) {
        this.foreignKey = foreignKey;
    }

    public BigDecimal getGoodsContain() {
        return goodsContain;
    }

    public void setGoodsContain(BigDecimal goodsContain) {
        this.goodsContain = goodsContain;
    }
}
