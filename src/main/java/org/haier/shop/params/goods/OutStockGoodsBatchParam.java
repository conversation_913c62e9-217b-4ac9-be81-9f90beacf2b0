package org.haier.shop.params.goods;

import java.util.List;

/**
 * @Description 出库商品批次信息
 * @ClassName OutStockGoodsBatchParam
 * <AUTHOR>
 * @Date 2024/5/8 11:03
 **/
public class OutStockGoodsBatchParam {

    /**
     * 商品ID
     */
    private Long goodsId;
    /**
     * 批次列表
     */
    private List<OutStockBatchParam> batchList;

    public Long getGoodsId() {
        return goodsId;
    }

    public void setGoodsId(Long goodsId) {
        this.goodsId = goodsId;
    }

    public List<OutStockBatchParam> getBatchList() {
        return batchList;
    }

    public void setBatchList(List<OutStockBatchParam> batchList) {
        this.batchList = batchList;
    }
}
