package org.haier.shop.params.goods;

import java.util.List;

/**
 * @Description
 * @ClassName GoodsInventoryParams
 * <AUTHOR>
 * @Date 2024/4/28 15:11
 **/
public class GoodsInventoryParam {

    /**
     * 店铺编号
     */
    private Long shopUnique;
    /**
     * 入库单号
     */
    private String stockListUnique;

    /**
     * 操作人ID
     */
    private Long staffId;

    /**
     * 库存管理方式：0-最近入库价，1-移动加权平均，2-先进先出
     */
    private Integer goodsInPriceType;

    /**
     * 入库明细
     */
    private List<GoodsInventoryDetailParam> goodsList;

    public Long getShopUnique() {
        return shopUnique;
    }

    public void setShopUnique(Long shopUnique) {
        this.shopUnique = shopUnique;
    }

    public String getStockListUnique() {
        return stockListUnique;
    }

    public void setStockListUnique(String stockListUnique) {
        this.stockListUnique = stockListUnique;
    }

    public Long getStaffId() {
        return staffId;
    }

    public void setStaffId(Long staffId) {
        this.staffId = staffId;
    }

    public List<GoodsInventoryDetailParam> getGoodsList() {
        return goodsList;
    }

    public void setGoodsList(List<GoodsInventoryDetailParam> goodsList) {
        this.goodsList = goodsList;
    }

    public Integer getGoodsInPriceType() {
        return goodsInPriceType;
    }

    public void setGoodsInPriceType(Integer goodsInPriceType) {
        this.goodsInPriceType = goodsInPriceType;
    }
}
