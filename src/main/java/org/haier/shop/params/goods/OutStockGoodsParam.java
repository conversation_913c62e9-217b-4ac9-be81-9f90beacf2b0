package org.haier.shop.params.goods;

import java.math.BigDecimal;

/**
 * @Description 出库商品详情
 * @ClassName OutStockGoodsParam
 * <AUTHOR>
 * @Date 2024/5/8 10:59
 **/
public class OutStockGoodsParam {

    /**
     * 商品ID
     */
    private Long goodsId;
    /**
     * 商品名称
     */
    private String goodsName;
    /**
     * 商品条码
     */
    private String goodsBarcode;
    /**
     * 出库数量
     */
    private BigDecimal goodsCount;
    /**
     * 出库价格
     */
    private BigDecimal stockPrice;

    /**
     * 逻辑计算（非参数）
     */
    private String foreignKey;

    /**
     * 逻辑计算（非参数）
     */
    private BigDecimal goodsContain;

    /**
     * 逻辑计算（非参数）
     */
    private BigDecimal goodsAvgPrice;

    /**
     * 供货商编码
     */
    private String supplierUnique;

    public BigDecimal getGoodsAvgPrice() {
        return goodsAvgPrice;
    }

    public void setGoodsAvgPrice(BigDecimal goodsAvgPrice) {
        this.goodsAvgPrice = goodsAvgPrice;
    }

    public Long getGoodsId() {
        return goodsId;
    }

    public void setGoodsId(Long goodsId) {
        this.goodsId = goodsId;
    }

    public String getGoodsName() {
        return goodsName;
    }

    public void setGoodsName(String goodsName) {
        this.goodsName = goodsName;
    }

    public String getGoodsBarcode() {
        return goodsBarcode;
    }

    public void setGoodsBarcode(String goodsBarcode) {
        this.goodsBarcode = goodsBarcode;
    }

    public BigDecimal getGoodsCount() {
        return goodsCount;
    }

    public void setGoodsCount(BigDecimal goodsCount) {
        this.goodsCount = goodsCount;
    }

    public BigDecimal getStockPrice() {
        return stockPrice;
    }

    public void setStockPrice(BigDecimal stockPrice) {
        this.stockPrice = stockPrice;
    }

    public String getForeignKey() {
        return foreignKey;
    }

    public void setForeignKey(String foreignKey) {
        this.foreignKey = foreignKey;
    }

    public BigDecimal getGoodsContain() {
        return goodsContain;
    }

    public void setGoodsContain(BigDecimal goodsContain) {
        this.goodsContain = goodsContain;
    }

    public String getSupplierUnique() {
        return supplierUnique;
    }

    public void setSupplierUnique(String supplierUnique) {
        this.supplierUnique = supplierUnique;
    }
}
