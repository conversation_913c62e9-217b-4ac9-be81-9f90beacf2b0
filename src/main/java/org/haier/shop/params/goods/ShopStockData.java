package org.haier.shop.params.goods;

import org.haier.shop.entity.ShopStock;

import java.math.BigDecimal;

/**
 * @Description
 * @ClassName ShopStockData
 * <AUTHOR>
 * @Date 2024/5/15 14:20
 **/
public class ShopStockData extends ShopStock {

    private String foreignKey;

    private BigDecimal goodsContain;

    public String getForeignKey() {
        return foreignKey;
    }

    public void setForeignKey(String foreignKey) {
        this.foreignKey = foreignKey;
    }

    public BigDecimal getGoodsContain() {
        return goodsContain;
    }

    public void setGoodsContain(BigDecimal goodsContain) {
        this.goodsContain = goodsContain;
    }
}
