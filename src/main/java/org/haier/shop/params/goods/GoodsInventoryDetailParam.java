package org.haier.shop.params.goods;

import java.math.BigDecimal;
import java.util.Date;

/**
 * @Description
 * @ClassName GoodsInventoryDetailParams
 * <AUTHOR>
 * @Date 2024/4/28 15:13
 **/
public class GoodsInventoryDetailParam {
    /**
     * 商品条码
     */
    private String goodsBarcode;

    /**
     * 商品进价
     */
    private BigDecimal goodsInPrice;

    /**
     * 商品剩余数量
     */
    private BigDecimal goodsCount;

    /**
     * 商品入库数量
     */
    private BigDecimal goodsInCount;

    /**
     * 商品生产日期
     */
    private Date goodsProd;

    /**
     * 商品过期日期
     */
    private Date goodsExp;

    /**
     * 商品保质天数
     */
    private Integer goodsLife;

    public String getGoodsBarcode() {
        return goodsBarcode;
    }

    public void setGoodsBarcode(String goodsBarcode) {
        this.goodsBarcode = goodsBarcode;
    }

    public BigDecimal getGoodsInPrice() {
        return goodsInPrice;
    }

    public void setGoodsInPrice(BigDecimal goodsInPrice) {
        this.goodsInPrice = goodsInPrice;
    }

    public BigDecimal getGoodsCount() {
        return goodsCount;
    }

    public void setGoodsCount(BigDecimal goodsCount) {
        this.goodsCount = goodsCount;
    }

    public BigDecimal getGoodsInCount() {
        return goodsInCount;
    }

    public void setGoodsInCount(BigDecimal goodsInCount) {
        this.goodsInCount = goodsInCount;
    }

    public Date getGoodsProd() {
        return goodsProd;
    }

    public void setGoodsProd(Date goodsProd) {
        this.goodsProd = goodsProd;
    }

    public Date getGoodsExp() {
        return goodsExp;
    }

    public void setGoodsExp(Date goodsExp) {
        this.goodsExp = goodsExp;
    }

    public Integer getGoodsLife() {
        return goodsLife;
    }

    public void setGoodsLife(Integer goodsLife) {
        this.goodsLife = goodsLife;
    }
}
