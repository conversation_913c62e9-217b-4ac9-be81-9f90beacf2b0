package org.haier.shop.params.goods;

import org.hibernate.validator.constraints.NotBlank;
import org.hibernate.validator.constraints.NotEmpty;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * @Description 出库参数
 * @ClassName OutStockParam
 * <AUTHOR>
 * @Date 2024/5/8 10:57
 **/
public class OutStockParam {

    /**
     * 店铺编码
     */
    @NotNull(message = "店铺编码不能为空")
    private Long shopUnique;
    /**
     * 出库单号
     */
    @NotBlank(message = "出库单号不能为空")
    private String listUnique;
    /**
     * 来源单号
     */
    private String sourceUnique;
    /**
     * 出库备注
     */
    private String stockRemarks;
    /**
     * 出库商品列表
     */
    @NotEmpty(message = "出库商品不能为空")
    private List<OutStockGoodsParam> goodsList;
    /**
     * 商品批次列表
     */
    private List<OutStockGoodsBatchParam> goodsBatchList;

    /**
     * 操作人ID
     */
    private Long userId;

    /**
     * 1：手动出入库；2：销售订单出入库，3：进货订单出入库（出库为退货）；4：盘库\r\n5:网上订单出库;6：寄存；7、云商采购;8:调拨;9、退货
     */
    private Integer stockResource = 1;

    public Long getShopUnique() {
        return shopUnique;
    }

    public void setShopUnique(Long shopUnique) {
        this.shopUnique = shopUnique;
    }

    public String getSourceUnique() {
        return sourceUnique;
    }

    public void setSourceUnique(String sourceUnique) {
        this.sourceUnique = sourceUnique;
    }

    public String getStockRemarks() {
        return stockRemarks;
    }

    public void setStockRemarks(String stockRemarks) {
        this.stockRemarks = stockRemarks;
    }

    public List<OutStockGoodsParam> getGoodsList() {
        return goodsList;
    }

    public void setGoodsList(List<OutStockGoodsParam> goodsList) {
        this.goodsList = goodsList;
    }

    public List<OutStockGoodsBatchParam> getGoodsBatchList() {
        return goodsBatchList;
    }

    public void setGoodsBatchList(List<OutStockGoodsBatchParam> goodsBatchList) {
        this.goodsBatchList = goodsBatchList;
    }

    public Long getUserId() {
        return userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }

    public String getListUnique() {
        return listUnique;
    }

    public void setListUnique(String listUnique) {
        this.listUnique = listUnique;
    }

    public Integer getStockResource() {
        return stockResource;
    }

    public void setStockResource(Integer stockResource) {
        this.stockResource = stockResource;
    }
}
