package org.haier.shop.params.goods;

import java.math.BigDecimal;
import java.util.Date;

/**
 * @Description 入库商品详情
 * @ClassName OutStockGoodsParam
 * <AUTHOR>
 * @Date 2024/5/8 10:59
 **/
public class InStockGoodsParam {

    /**
     * 商品ID
     */
    private Long goodsId;
    /**
     * 商品名称
     */
    private String goodsName;
    /**
     * 商品条码
     */
    private String goodsBarcode;
    /**
     * 入库数量
     */
    private BigDecimal goodsCount;
    /**
     * 入库价格
     */
    private BigDecimal stockPrice;

    /**
     * 生产日期
     */
    private Date goodsProd;
    /**
     * 保质期
     */
    private Integer goodsLife;
    /**
     * 过期日期
     */
    private Date goodsExp;

    public Long getGoodsId() {
        return goodsId;
    }

    public void setGoodsId(Long goodsId) {
        this.goodsId = goodsId;
    }

    public String getGoodsName() {
        return goodsName;
    }

    public void setGoodsName(String goodsName) {
        this.goodsName = goodsName;
    }

    public String getGoodsBarcode() {
        return goodsBarcode;
    }

    public void setGoodsBarcode(String goodsBarcode) {
        this.goodsBarcode = goodsBarcode;
    }

    public BigDecimal getGoodsCount() {
        return goodsCount;
    }

    public void setGoodsCount(BigDecimal goodsCount) {
        this.goodsCount = goodsCount;
    }

    public BigDecimal getStockPrice() {
        return stockPrice;
    }

    public void setStockPrice(BigDecimal stockPrice) {
        this.stockPrice = stockPrice;
    }

    public Date getGoodsProd() {
        return goodsProd;
    }

    public void setGoodsProd(Date goodsProd) {
        this.goodsProd = goodsProd;
    }

    public Integer getGoodsLife() {
        return goodsLife;
    }

    public void setGoodsLife(Integer goodsLife) {
        this.goodsLife = goodsLife;
    }

    public Date getGoodsExp() {
        return goodsExp;
    }

    public void setGoodsExp(Date goodsExp) {
        this.goodsExp = goodsExp;
    }
}
