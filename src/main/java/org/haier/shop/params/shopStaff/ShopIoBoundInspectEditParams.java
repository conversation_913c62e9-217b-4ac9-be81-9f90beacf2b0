package org.haier.shop.params.shopStaff;

import org.hibernate.validator.constraints.NotBlank;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * @ClassName ShopIoBoundInspectEditParams
 * <AUTHOR>
 * @Date 2024/4/25 16:22
 */
public class ShopIoBoundInspectEditParams implements Serializable {
    @NotBlank(message = "请输入店铺编号")
    private String shopUnique;
    @NotNull(message = "请选择出入库审核")
    private Integer isIoBoundInspect;

    public String getShopUnique() {
        return shopUnique;
    }

    public void setShopUnique(String shopUnique) {
        this.shopUnique = shopUnique;
    }

    public Integer getIsIoBoundInspect() {
        return isIoBoundInspect;
    }

    public void setIsIoBoundInspect(Integer isIoBoundInspect) {
        this.isIoBoundInspect = isIoBoundInspect;
    }
}
