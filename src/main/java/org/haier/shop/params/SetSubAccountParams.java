package org.haier.shop.params;

import java.io.Serializable;

/**
 * @ClassName SetSubAccountParams
 * @Description 设置子账簿
 * <AUTHOR>
 * @Date 2025/3/20 17:34
 * @Version 1.0
 */

public class SetSubAccountParams implements Serializable {
    private static final long serialVersionUID = 1L;

    private String shopUnique;
    private String subAccount;


    public String getShopUnique() {
        return shopUnique;
    }

    public void setShopUnique(String shopUnique) {
        this.shopUnique = shopUnique;
    }

    public String getSubAccount() {
        return subAccount;
    }

    public void setSubAccount(String subAccount) {
        this.subAccount = subAccount;
    }
}