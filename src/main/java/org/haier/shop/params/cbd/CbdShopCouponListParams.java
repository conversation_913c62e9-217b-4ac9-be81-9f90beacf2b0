package org.haier.shop.params.cbd;

import com.fasterxml.jackson.annotation.JsonFormat;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.Date;

/**
 * @ClassName CbdShopCouponListParams
 * <AUTHOR>
 * @Date 2024/2/22 10:15
 */

public class CbdShopCouponListParams implements Serializable {
    /**
     * 店铺编号
     */
    @NotNull(message = "请输入店铺编号")
    private Long shopUnique;
    /**
     * 页码数
     */
    @NotNull(message = "请输入页码数")
    @Min(value = 1,message = "页码数最小为1")
    private Integer page;
    /**
     * 每页条数
     */
    @NotNull(message = "请输入每页条数")
    @Min(value = 1,message = "每页条数最小为1")
    private Integer limit;
    /**
     * 结算状态
     */
    private Integer settlementStatus;
    /**
     * 核销时间开始
     */
    private String useTimeStart;
    /**
     * 核销时间结束
     */
    private String useTimeEnd;

    public Long getShopUnique() {
        return shopUnique;
    }

    public void setShopUnique(Long shopUnique) {
        this.shopUnique = shopUnique;
    }

    public Integer getPage() {
        return page;
    }

    public void setPage(Integer page) {
        this.page = page;
    }

    public Integer getLimit() {
        return limit;
    }

    public void setLimit(Integer limit) {
        this.limit = limit;
    }

    public Integer getSettlementStatus() {
        return settlementStatus;
    }

    public void setSettlementStatus(Integer settlementStatus) {
        this.settlementStatus = settlementStatus;
    }

    public String getUseTimeStart() {
        return useTimeStart;
    }

    public void setUseTimeStart(String useTimeStart) {
        this.useTimeStart = useTimeStart;
    }

    public String getUseTimeEnd() {
        return useTimeEnd;
    }

    public void setUseTimeEnd(String useTimeEnd) {
        this.useTimeEnd = useTimeEnd;
    }
}
