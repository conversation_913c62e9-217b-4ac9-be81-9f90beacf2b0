package org.haier.shop.params.card;

import org.hibernate.validator.constraints.NotEmpty;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;

public class AddBusinessCardParams implements Serializable {
    //员工ID
    @NotNull(message = "员工ID不能为空")
    private Long userId;
    //员工类型(列表返回):1、店长；2、店员；3、团长；
    @NotNull(message = "员工类型不能为空")
    private Integer userType;
    //所属店铺ID
    @NotNull(message = "所属店铺ID不能为空")
    private Long shopUnique;
    //所属店铺
    @NotEmpty(message = "所属店铺不能为空")
    private String shopName;
    //员工手机号
    @NotEmpty(message = "员工手机号不能为空")
    private String mobile;
    //头像路径
    @NotEmpty(message = "头像路径不能为空")
    private String icon;
    //员工姓名
    @NotEmpty(message = "员工名称不能为空")
    private String userName;
    //邮箱
    @NotEmpty(message = "邮箱不能为空")
    private String email;
    //职位
    @NotEmpty(message = "职位不能为空")
    private String position;
    //微信号(同手机号)
    @NotEmpty(message = "微信号不能为空")
    private String weChat;
    //名片类型（1、企业用户；2、个人用户）
    @NotNull(message = "名片类型不能为空")
    private Integer cardType;
    //是否为默认名片（1、是；0、否）
    @NotNull(message = "是否为默认名片不能为空")
    private Integer defaultCard;


    //企业信息
    //企业名称
    private String companyName;
    //企业地址
    private String companyAddress;
    //企业简介
    private String companyIntroduce;
    //个人简介
    private String personalIntroduce;
    //个人图片介绍
    private List<String> personalPicFiles;
    //企业图片介绍
    private List<String> companyPicFiles;


    public Long getUserId() {
        return userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }

    public Integer getUserType() {
        return userType;
    }

    public void setUserType(Integer userType) {
        this.userType = userType;
    }

    public Long getShopUnique() {
        return shopUnique;
    }

    public void setShopUnique(Long shopUnique) {
        this.shopUnique = shopUnique;
    }

    public String getShopName() {
        return shopName;
    }

    public void setShopName(String shopName) {
        this.shopName = shopName;
    }

    public String getMobile() {
        return mobile;
    }

    public void setMobile(String mobile) {
        this.mobile = mobile;
    }

    public String getIcon() {
        return icon;
    }

    public void setIcon(String icon) {
        this.icon = icon;
    }

    public String getUserName() {
        return userName;
    }

    public void setUserName(String userName) {
        this.userName = userName;
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public String getPosition() {
        return position;
    }

    public void setPosition(String position) {
        this.position = position;
    }

    public String getWeChat() {
        return weChat;
    }

    public void setWeChat(String weChat) {
        this.weChat = weChat;
    }

    public Integer getCardType() {
        return cardType;
    }

    public void setCardType(Integer cardType) {
        this.cardType = cardType;
    }

    public Integer getDefaultCard() {
        return defaultCard;
    }

    public void setDefaultCard(Integer defaultCard) {
        this.defaultCard = defaultCard;
    }

    public String getCompanyName() {
        return companyName;
    }

    public void setCompanyName(String companyName) {
        this.companyName = companyName;
    }

    public String getCompanyAddress() {
        return companyAddress;
    }

    public void setCompanyAddress(String companyAddress) {
        this.companyAddress = companyAddress;
    }

    public String getCompanyIntroduce() {
        return companyIntroduce;
    }

    public void setCompanyIntroduce(String companyIntroduce) {
        this.companyIntroduce = companyIntroduce;
    }

    public String getPersonalIntroduce() {
        return personalIntroduce;
    }

    public void setPersonalIntroduce(String personalIntroduce) {
        this.personalIntroduce = personalIntroduce;
    }

    public List<String> getPersonalPicFiles() {
        return personalPicFiles;
    }

    public void setPersonalPicFiles(List<String> personalPicFiles) {
        this.personalPicFiles = personalPicFiles;
    }

    public List<String> getCompanyPicFiles() {
        return companyPicFiles;
    }

    public void setCompanyPicFiles(List<String> companyPicFiles) {
        this.companyPicFiles = companyPicFiles;
    }

    @Override
    public String toString() {
        return "AddBusinessCardParams{" +
                "userId=" + userId +
                ", userType=" + userType +
                ", shopUnique=" + shopUnique +
                ", shopName='" + shopName + '\'' +
                ", mobile='" + mobile + '\'' +
                ", icon='" + icon + '\'' +
                ", userName='" + userName + '\'' +
                ", email='" + email + '\'' +
                ", position='" + position + '\'' +
                ", weChat='" + weChat + '\'' +
                ", cardType=" + cardType +
                ", defaultCard=" + defaultCard +
                ", companyName='" + companyName + '\'' +
                ", companyAddress='" + companyAddress + '\'' +
                ", companyIntroduce='" + companyIntroduce + '\'' +
                ", personalIntroduce='" + personalIntroduce + '\'' +
                ", personalPicFiles=" + personalPicFiles +
                ", companyPicFiles=" + companyPicFiles +
                '}';
    }
}
