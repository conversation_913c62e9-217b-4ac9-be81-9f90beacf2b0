package org.haier.shop.params.card;

import java.io.Serializable;
import java.util.List;

public class UpdateBusinessCardParams implements Serializable {
    //id
    private Long id;
    //店铺编号
    private Long shopUnique;
    //头像路径
    private String icon;
    //员工姓名
    private String userName;
    //邮箱
    private String email;
    //职位
    private String position;
    //手机号
    private String mobile;
    //微信号(同手机号)
    private String weChat;
    //名片类型（1、企业用户；2、个人用户）
    private Integer cardType;

    //企业信息
    //企业名称
    private String companyName;
    //企业地址
    private String companyAddress;
    //企业简介
    private String companyIntroduce;
    //个人简介
    private String personalIntroduce;
    //个人图片介绍
    private List<String> personalPicFiles;
    //公司图片介绍
    private List<String> companyPicFiles;


    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getShopUnique() {
        return shopUnique;
    }

    public void setShopUnique(Long shopUnique) {
        this.shopUnique = shopUnique;
    }

    public String getIcon() {
        return icon;
    }

    public void setIcon(String icon) {
        this.icon = icon;
    }

    public String getUserName() {
        return userName;
    }

    public void setUserName(String userName) {
        this.userName = userName;
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public String getPosition() {
        return position;
    }

    public void setPosition(String position) {
        this.position = position;
    }

    public String getWeChat() {
        return weChat;
    }

    public void setWeChat(String weChat) {
        this.weChat = weChat;
    }

    public Integer getCardType() {
        return cardType;
    }

    public void setCardType(Integer cardType) {
        this.cardType = cardType;
    }

    public String getCompanyName() {
        return companyName;
    }

    public void setCompanyName(String companyName) {
        this.companyName = companyName;
    }

    public String getCompanyAddress() {
        return companyAddress;
    }

    public void setCompanyAddress(String companyAddress) {
        this.companyAddress = companyAddress;
    }

    public String getCompanyIntroduce() {
        return companyIntroduce;
    }

    public void setCompanyIntroduce(String companyIntroduce) {
        this.companyIntroduce = companyIntroduce;
    }

    public String getPersonalIntroduce() {
        return personalIntroduce;
    }

    public void setPersonalIntroduce(String personalIntroduce) {
        this.personalIntroduce = personalIntroduce;
    }

    public List<String> getPersonalPicFiles() {
        return personalPicFiles;
    }

    public void setPersonalPicFiles(List<String> personalPicFiles) {
        this.personalPicFiles = personalPicFiles;
    }

    public String getMobile() {
        return mobile;
    }

    public void setMobile(String mobile) {
        this.mobile = mobile;
    }

    public List<String> getCompanyPicFiles() {
        return companyPicFiles;
    }

    public void setCompanyPicFiles(List<String> companyPicFiles) {
        this.companyPicFiles = companyPicFiles;
    }
}
