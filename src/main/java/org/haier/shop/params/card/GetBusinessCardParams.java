package org.haier.shop.params.card;


import org.haier.shop.params.common.CommonPageQueryParams;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

public class GetBusinessCardParams extends CommonPageQueryParams implements Serializable {
    //开始时间
    private String startTime;
    //结束时间
    private String endTime;
    //姓名，电话，邮箱，职业
    private String userInfo;
    //店铺信息
    private String companyInfo;
    //店铺店铺编号
    @NotNull(message = "店铺编号不能为空")
    private Long shopUnique;
    //当前登录用户的ID
    private Long userId;
    //用户类型
    private Integer userType;

    public String getStartTime() {
        return startTime;
    }

    public void setStartTime(String startTime) {
        this.startTime = startTime;
    }

    public String getEndTime() {
        return endTime;
    }

    public void setEndTime(String endTime) {
        this.endTime = endTime;
    }

    public String getUserInfo() {
        return userInfo;
    }

    public void setUserInfo(String userInfo) {
        this.userInfo = userInfo;
    }

    public String getCompanyInfo() {
        return companyInfo;
    }

    public void setCompanyInfo(String companyInfo) {
        this.companyInfo = companyInfo;
    }

    public Long getShopUnique() {
        return shopUnique;
    }

    public void setShopUnique(Long shopUnique) {
        this.shopUnique = shopUnique;
    }

    public Long getUserId() {
        return userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }

    public Integer getUserType() {
        return userType;
    }

    public void setUserType(Integer userType) {
        this.userType = userType;
    }
}
