package org.haier.shop.params.card.jqtwo;


import java.io.Serializable;
import java.util.List;

public class AddCardParams implements Serializable {
    //人员id
    private Long userId;
    //店铺id
    private Long shopId;
    //店铺名称
    private String shopName;
    //授权手机
    private String mobile;
    //头像路径
    private String icon;
    //用户类型
    private Integer userType;
    //员工姓名
    private String userName;
    //邮箱
    private String email;
    //职位
    private String position;
    //微信号(同手机号)
    private String weChat;
    //名片类型（1、企业用户；2、个人用户）
    private Integer cardType;
    //是否为默认名片（1、是；0、否）
    private Integer defaultCard;


    //企业信息
    //企业名称
    private String companyName;
    //企业地址
    private String companyAddress;
    //企业简介
    private String companyIntroduce;
    //个人简介
    private String personalIntroduce;
    //个人图片介绍
    private List<String> personalPicFileList;
    //公司图片介绍
    private List<String> companyPicFileList;

    public Long getUserId() {
        return userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }

    public Long getShopId() {
        return shopId;
    }

    public void setShopId(Long shopId) {
        this.shopId = shopId;
    }

    public String getShopName() {
        return shopName;
    }

    public void setShopName(String shopName) {
        this.shopName = shopName;
    }

    public String getMobile() {
        return mobile;
    }

    public void setMobile(String mobile) {
        this.mobile = mobile;
    }

    public String getIcon() {
        return icon;
    }

    public void setIcon(String icon) {
        this.icon = icon;
    }

    public String getUserName() {
        return userName;
    }

    public void setUserName(String userName) {
        this.userName = userName;
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public String getPosition() {
        return position;
    }

    public void setPosition(String position) {
        this.position = position;
    }

    public String getWeChat() {
        return weChat;
    }

    public void setWeChat(String weChat) {
        this.weChat = weChat;
    }

    public Integer getCardType() {
        return cardType;
    }

    public void setCardType(Integer cardType) {
        this.cardType = cardType;
    }

    public Integer getDefaultCard() {
        return defaultCard;
    }

    public void setDefaultCard(Integer defaultCard) {
        this.defaultCard = defaultCard;
    }

    public String getCompanyName() {
        return companyName;
    }

    public void setCompanyName(String companyName) {
        this.companyName = companyName;
    }

    public String getCompanyAddress() {
        return companyAddress;
    }

    public void setCompanyAddress(String companyAddress) {
        this.companyAddress = companyAddress;
    }

    public String getCompanyIntroduce() {
        return companyIntroduce;
    }

    public void setCompanyIntroduce(String companyIntroduce) {
        this.companyIntroduce = companyIntroduce;
    }

    public String getPersonalIntroduce() {
        return personalIntroduce;
    }

    public void setPersonalIntroduce(String personalIntroduce) {
        this.personalIntroduce = personalIntroduce;
    }

    public List<String> getPersonalPicFileList() {
        return personalPicFileList;
    }

    public void setPersonalPicFileList(List<String> personalPicFileList) {
        this.personalPicFileList = personalPicFileList;
    }

    public List<String> getCompanyPicFileList() {
        return companyPicFileList;
    }

    public void setCompanyPicFileList(List<String> companyPicFileList) {
        this.companyPicFileList = companyPicFileList;
    }

    public Integer getUserType() {
        return userType;
    }

    public void setUserType(Integer userType) {
        this.userType = userType;
    }

    @Override
    public String toString() {
        return "AddCardParams{" +
                "userId=" + userId +
                ", shopId=" + shopId +
                ", shopName='" + shopName + '\'' +
                ", mobile='" + mobile + '\'' +
                ", icon='" + icon + '\'' +
                ", userType=" + userType +
                ", userName='" + userName + '\'' +
                ", email='" + email + '\'' +
                ", position='" + position + '\'' +
                ", weChat='" + weChat + '\'' +
                ", cardType=" + cardType +
                ", defaultCard=" + defaultCard +
                ", companyName='" + companyName + '\'' +
                ", companyAddress='" + companyAddress + '\'' +
                ", companyIntroduce='" + companyIntroduce + '\'' +
                ", personalIntroduce='" + personalIntroduce + '\'' +
                ", personalPicFileList=" + personalPicFileList +
                ", companyPicFileList=" + companyPicFileList +
                '}';
    }
}
