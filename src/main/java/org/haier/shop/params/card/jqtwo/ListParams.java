package org.haier.shop.params.card.jqtwo;

import java.io.Serializable;

public class ListParams extends PageParams implements Serializable {
    //店铺名称
    private String shopName;
    //员工名称
    private String userName;
    //开始时间
    private String startTime;
    //结束时间
    private String endTime;
    //店铺id，必传
    private Long shopId;
    //当前员工ID
    private Long userId;
    //用户类型
    private Integer userType;

    public String getShopName() {
        return shopName;
    }

    public void setShopName(String shopName) {
        this.shopName = shopName;
    }

    public String getUserName() {
        return userName;
    }

    public void setUserName(String userName) {
        this.userName = userName;
    }

    public String getStartTime() {
        return startTime;
    }

    public void setStartTime(String startTime) {
        this.startTime = startTime;
    }

    public String getEndTime() {
        return endTime;
    }

    public void setEndTime(String endTime) {
        this.endTime = endTime;
    }

    public Long getShopId() {
        return shopId;
    }

    public void setShopId(Long shopId) {
        this.shopId = shopId;
    }

    public Long getUserId() {
        return userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }

    public Integer getUserType() {
        return userType;
    }

    public void setUserType(Integer userType) {
        this.userType = userType;
    }
}
