package org.haier.shop.filter;

import java.io.IOException;
import java.net.URLEncoder;
import java.util.HashMap;
import java.util.Map;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.HttpSession;

import org.haier.shop.util.HandleMessyCode;
import org.haier.shop.util.HttpClientUtil;
import org.haier.shop.util.MUtil;
import org.haier.shop.util.ServletsUtil;
import org.haier.shop.util.wxPay.PayConfigUtil;
import org.springframework.util.StringUtils;
import org.springframework.web.servlet.HandlerInterceptor;
import org.springframework.web.servlet.ModelAndView;

import com.google.gson.Gson;

public class WeChatRequestAuthorizationFilter implements HandlerInterceptor{

	/**
	 * 微信商城过滤器
	 */
	@Override
	public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) throws Exception {
		Map<String, Object> params = HandleMessyCode.handle(ServletsUtil.getParameters(request));
		String requestUri = request.getRequestURI();
		String queryString = request.getQueryString();//获取参数
		
		HttpSession session = request.getSession();
		String openid = MUtil.strObject(session.getAttribute("openid"));
		if(openid != null && !openid.equals("")) {
			return true;
		}
		//获取授权登录后的code
		String weChatCode = (String) params.get("code");
				
		//若获取到授权登录后的code，则查询并保存微信用户信息，并通过请求
	    if(StringUtils.hasText(weChatCode)){
	    	// 通过code获取token
			Map<String, Object> accessTokenMap = get_access_token(PayConfigUtil.APP_ID,PayConfigUtil.AppSecret,weChatCode);
			openid = (String) accessTokenMap.get("openid");//用户唯一id
			session.setAttribute("openid", openid);
			return true;
	    }else{
	    	String url = PayConfigUtil.yuming+requestUri+"?"+queryString;
	    	System.out.println("微信授权回调url:"+url);
		    weChatLogin(request,response,url,PayConfigUtil.APP_ID);
		    return false;
		}
	}

	@Override
	public void postHandle(HttpServletRequest request,
						   HttpServletResponse response, Object handler,
						   ModelAndView modelAndView) throws Exception {
		System.out.println("posthandle");
		// TODO Auto-generated method stub
	}

	@Override
	public void afterCompletion(HttpServletRequest request,
								HttpServletResponse response, Object handler, Exception ex)
			throws Exception {
		System.out.println("afterCompletion");
		// TODO Auto-generated method stub

	}
	
	/**
	 * 微信自动登录，返回code
	 * @param request
	 * @param response
	 * @param url
	 * @throws IOException
	 */
	public void weChatLogin(HttpServletRequest request,
							HttpServletResponse response,String url,String appid) throws IOException{
        String authUrl = "https://open.weixin.qq.com/connect/oauth2/authorize?appid=APPID&redirect_uri=REDIRECT_URI&response_type=code&scope=snsapi_base&state=STATE#wechat_redirect";
		authUrl= authUrl.replace("APPID", appid);
		String REDIRECT_URI = url;
		REDIRECT_URI = URLEncoder.encode(REDIRECT_URI, "utf-8");
		REDIRECT_URI = REDIRECT_URI.replaceAll(":", "%3a").replaceAll("/", "%2f");//请求地址中，回调的redirect_uri中字符替换
		authUrl = authUrl.replace("REDIRECT_URI",REDIRECT_URI);
		//重定向到微信登录请求地址
		response.sendRedirect(authUrl);
	}
	
	/**
	 * 通过code换取网页授权access_token
	 * @param weChatAppid 公众号的唯一标识
	 * @param weChatSecret 公众号的appsecret
	 * @param weChatCode 填写第一步获取的code参数
	 * @return
	 */
	public static Map<String,Object> get_access_token(String weChatAppid,String weChatSecret,String weChatCode){
		StringBuffer uri = new StringBuffer("https://api.weixin.qq.com/sns/oauth2/access_token?appid="+weChatAppid+"&secret="+weChatSecret+"&code="+weChatCode+"&grant_type=authorization_code");
		String result = HttpClientUtil.requestByGetMethod(uri.toString(), false);
		// 解析Map数据
		Gson gson = new Gson();
		Map<String, Object> map = new HashMap<String, Object>();
		map = HandleMessyCode.handle(gson.fromJson(result, map.getClass()));
		return map;
	}
}
