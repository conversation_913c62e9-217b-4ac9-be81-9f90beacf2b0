package org.haier.shop.realm;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;

import org.apache.shiro.authc.AuthenticationException;
import org.apache.shiro.authc.AuthenticationInfo;
import org.apache.shiro.authc.AuthenticationToken;
import org.apache.shiro.authc.DisabledAccountException;
import org.apache.shiro.authc.SimpleAuthenticationInfo;
import org.apache.shiro.authc.UnknownAccountException;
import org.apache.shiro.authz.AuthorizationInfo;
import org.apache.shiro.authz.SimpleAuthorizationInfo;
import org.apache.shiro.cache.Cache;
import org.apache.shiro.realm.AuthorizingRealm;
import org.apache.shiro.subject.PrincipalCollection;
import org.haier.meituan.util.MUtil;
import org.haier.shop.entity.Staff;
import org.haier.shop.entity.SysAction;
import org.haier.shop.service.StaffService;
import org.haier.shop.service.SysRoleService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;

public class CustomRealm extends AuthorizingRealm{
    private static final Logger logger = LoggerFactory.getLogger(CustomRealm.class);
    
    @Autowired
    private StaffService staffService;
    
    @Autowired
    private SysRoleService roleService;
    
    /**
     * 用户授权认证֤
     */
    @Override
    protected AuthorizationInfo doGetAuthorizationInfo(PrincipalCollection pc) {
    	logger.info("======用户授权认证======");
    	SimpleAuthorizationInfo info = new SimpleAuthorizationInfo();
    	Cache<Object, AuthorizationInfo> cache = getAuthorizationCache();
    	Object key = getAuthorizationCacheKey(pc);
    	if (cache.get(key) != null) {
    		
    		info = (SimpleAuthorizationInfo) cache.get(key);
    	}else{
    		//1.得到用户登录账号
    		Staff staff= (Staff) pc.getPrimaryPrincipal();
    		Integer staff_id = staff.getStaff_id();
            //2.通过对象导航得到用户的角色列表 
    		String role_code = null;
    		Map<String ,Object> roleMap = roleService.getRoleByStaffId(staff_id);
    		if(roleMap != null){
    			role_code = MUtil.strObject(roleMap.get("role_code"));
    		}
    		Set<String> roles = new HashSet<String>();
        	roles.add(role_code);

        	List<String> actionList = new ArrayList<String>();
			List<SysAction> resultList = new ArrayList<SysAction>();
			Integer staff_position = staff.getStaff_position();
			if(staff_position == 3){//店主
				String version = "";
				Integer type = staff.getShop_type();//0普通商家；1：连锁；2加盟 3系统平台 4普通商家分店
				String terminal = "web";
				if(staff.getShop_class() == 3){
					version = "admin";
					type = null;
					terminal = null;
				}else if(staff.getShop_class() == 1){
					version = "chain";
				}else if(staff.getShop_class() == 2){
					version = "join";
				}else{
					version = "ordinary";
				}
				

				Map<String ,Object> params = new HashMap<String, Object>();
	   			params.put("version", version);
	   			params.put("type", type);
	   			params.put("terminal", terminal);
	   			resultList = roleService.getActionList(params);
			}else{
				//获取角色关联的操作权限列表
	        	Map<String ,Object> params = new HashMap<String, Object>();
	        	params.put("role_code", role_code);
	        	resultList = roleService.getActionListByRoleCode(params);
			}
			
            for(int i=0;i<resultList.size();i++){
            	SysAction action = resultList.get(i);
            	String action_param = action.getAction_param();
            	actionList.add(action_param);
            }
            info.addStringPermissions(actionList);
            info.setRoles(roles);
            key = getAuthorizationCacheKey(pc);
            cache.put(key, info);
    	}
        return info;      
    }
    /**
     * 用户登录认证֤
     */
    @Override
    protected AuthenticationInfo doGetAuthenticationInfo(AuthenticationToken authenticationToken) throws AuthenticationException {
    	  logger.info("======用户登录认证======");
          String staff_account = authenticationToken.getPrincipal().toString();
          Staff staff = staffService.getStaffByAccount(staff_account);
	      if (staff == null) {
		      System.out.println("认证：当前登录的用户不存在");
		      throw new UnknownAccountException();
	      }
	      if(staff.getExaminestatus() != 4){
		      throw new DisabledAccountException();
	      }
	      String pwd = staff.getStaff_pwd();
	      return new SimpleAuthenticationInfo(staff, pwd, getName());
    }

}