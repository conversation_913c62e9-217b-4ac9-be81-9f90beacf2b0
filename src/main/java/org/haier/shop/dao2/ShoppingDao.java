package org.haier.shop.dao2;

import java.util.List;
import java.util.Map;

import org.apache.ibatis.annotations.Param;
import org.haier.shop.entity.SystemMsgBean;
import org.haier.shop.entity.shop.ShopList;
import org.haier.shop.entity.wj.WJGoods;
import org.haier.shop.entity.wj.WJGoodsKindParent;

/**
 * 供货商城持久层
 * @author: yuliangliang
 * @date: 2019年10月24日
 */
public interface ShoppingDao {
	/**
	 * 定时任务，保持数据库连接状态
	 * @return
	 */
	public Integer keepLive();
	/**
	 * 根据本次预订的商品信息，判断是否达到成团标准，并更新预订商品数量
	 * @param list
	 * @return
	 */
	public Integer updateActivityMsg(List<Map<String,Object>> list);
	/**
	 * 查询订单预售商品，修改预售商品的预售数量
	 * @param map
	 * @return
	 */
	public List<Map<String,Object>> queryOrderDetailForBook(Map<String,Object> map);
	/**
	 * 为发送MQTT消息，查询订单列表
	 * @param main_order_no
	 * @return
	 */
	public List<Map<String,Object>> queryOrderListByOrderNo(Map<String ,Object> params);
	/**
	 * 查询支付文件列表
	 * @param map
	 * @return
	 */
	public List<Map<String,Object>> queryPayImageList(Map<String,Object> map);
	
	public Integer queryPayImageCount(Map<String,Object> map);
	
	public Integer addNewPayImage(String url);
	/**
	 * 支付成功后,修改主订单的支付信息,方便退款
	 * @param params
	 * @return
	 */
	public Integer updateMainOrder(Map<String,Object> params);
	
	public String queryCartGoodsIdById(Object id);
	
	public Double querySystemConfig();
	
	public Map<String,Object> queryShoppingCartById(Integer id);

	/**
	 * 查询订单支付状态
	 * @param map
	 * @return
	 */
	public Integer queryMainOrderPayStatus(Map<String,Object> map);
	
	public List<ShopList> getMyOrderListShopDetail(Map<String,Object> map);
	
	public Integer modifySupOrderList(Map<String,Object> map);
	public Integer modifySupOrderDetail(Map<String,Object> temp);
	/**
	 * 查询订单详情新
	 * @param map
	 * @return
	 */
	public List<Map<String,Object>> queryOrderDetailGoodListShop(Map<String,Object> map);
	
	public List<Map<String,Object>> querySupplierGoodListWJForSettle(Map<String,Object> map);
	/**
	 * 提交购物车前查询
	 * @param ids
	 * @return
	 */
	public List<Map<String,Object>> querySupplierByCartWJforSettle(String[] ids);
	
	public List<Map<String,Object>> querySupplierGoodListWJ(Map<String,Object> params);
	
	public List<Map<String,Object>> querySupplierByCartWJ(Map<String,Object> params);
	/**
	 * 五金店铺商品获取商品详情
	 * @param map
	 * @return
	 */
	public WJGoods getWJGoodSpecList(Map<String,Object> map);
	
	/**
	 * 获取五金店铺分类信息
	 * @param map
	 * @return
	 */
	public List<WJGoodsKindParent> getWJGoodskindList(Map<String,Object> map);
	
	public Integer getGoodListWJCount(Map<String,Object> map);
	public List<WJGoods> getGoodListWJ(Map<String,Object> map);
	 
	public List<Map<String,Object>> getGoodskindList(Map<String,Object> map);
	
	/**
	 * 批量修改订单的状态
	 * @param map
	 * @return
	 */
	public Integer modifyOrderMsg(Map<String,Object> map);
	/**
	 * 批量增加供货商余额信息
	 * @param list
	 * @return
	 */
	public Integer addCompanyBalance(List<Map<String,Object>> list);
	
	/**
	 * 查询月结定的子订单信息
	 * @param map
	 * @return
	 */
	public List<Map<String,Object>> queryOrderSettlementSubList(Map<String,Object> map);
	
	public Integer canOrderSettlement(Map<String,Object> map) ;
	/**
	 * 查询月结定的订单详情
	 * @param map
	 * @return
	 */
	public List<Map<String,Object>> queryOrderSettlementDetail(Map<String,Object> map);
	
	public Integer sureOrderSettlement(Map<String,Object> map);
	public Integer sureSupOrderLists(Map<String,Object> map);
	
	public Map<String,Object> queryOrderSettlementMsg(Map<String,Object> map);
	/**
	 * 添加结算主订单
	 * @param map
	 * @return
	 */
	public Integer addNewOrderSettlement(Map<String,Object> map);
	/**
	 * 添加结算子订单
	 * @param list
	 * @return
	 */
	public Integer addNewOrderSettlementDetail(List<Map<String,Object>> list);
	/**
	 * 获取指定店铺，指定供货商，指定日期内的未结算订单信息
	 * @param map
	 * @return
	 */
	public List<Map<String,Object>> querySupOrderListMsg(Map<String,Object> map);
	
	/**
	 * 获取所有的供货商订单信息
	 * @param map
	 * @return
	 */
	public List<Map<String,Object>> queryOrderSettlementList(Map<String,Object> map);
	public Integer queryOrderSettlementListCount(Map<String,Object> map);
	/**
	 * 获取指定商家的所有未支付供货商订单的供货商信息
	 */
	public List<Map<String,Object>> queryOrderSupplierMsg(Map<String,Object> params);
	
	/**
	 * 查询捆绑商品信息
	 * @param params
	 * @return
	 */
	public List<Map<String,Object>> getBindingGoodsList(Map<String ,Object> map);
	
	public int insertShoppingCart(Map<String ,Object> params);
	
	public List<Map<String,Object>> queryShoppingCart(Map<String ,Object> params);
		
	public List<Map<String ,Object>> getGoodList(Map<String ,Object> params);	
	
	public Integer getGoodListCount(Map<String ,Object> params);
	
	public List<Map<String ,Object>> getGoodSpecList(Map<String ,Object> params);	
	
	public int update_shopping_cart(Map<String ,Object> params);
	
	public int delete_shopping_cart(Map<String ,Object> params);
	
//	public int delete_bind_shopping_cart(Map<String ,Object> params);
	
	public int delete_shopping_cart_more(String[] ids);
	
	public int queryShoppingCartCount(Map<String ,Object> params);
	
	public String getGoodDetail(Map<String ,Object> params);
	
	public List<Map<String ,Object>> getGoodBindDetail(Map<String ,Object> params);
	
	public List<Map<String ,Object>> getCoupon(Map<String ,Object> params);
	
	public List<Map<String ,Object>> getFullgift(Map<String ,Object> params);
	
	public List<Map<String ,Object>> getGiftGoodsList(@Param("gift_id")String gift_id);
	
	public List<Map<String ,Object>> getGiftCouponList(@Param("gift_id")String gift_id);
	
	public List<Map<String ,Object>> querySupplierByCart(@Param("ids")String[] ids, @Param("shop_unique")String shop_unique);
	
	public List<Map<String ,Object>> querySupplierGoodList(@Param("company_code")String company_code,@Param("area_dict_num")String area_dict_num,@Param("ids")String[] ids,@Param("shop_unique") String shop_unique);
	
	public List<Map<String ,Object>> querySupplierGoodList2(@Param("company_code")String company_code,@Param("ids")String[] ids, @Param("shop_unique")String shop_unique);
	
	public int insertOrderMain(Map<String ,Object> params);
	
	public List<Map<String ,Object>> queryGoldUserList(Map<String ,Object> params);	
	
	public Integer queryGoldUserCount(Map<String ,Object> params);
	
	public List<Map<String ,Object>> getMyOrderList(Map<String ,Object> params);	
	
	public Integer getMyOrderListCount(Map<String ,Object> params);
	
	public Map<String ,Object> queryOrderByOrderNo(Map<String ,Object> params);	
	
	public int updateShoppingOrder(Map<String ,Object> params);
	
	public int updateShoppingOrderWJ(Map<String ,Object> params);
	
	public int updateShoppingOrderWJ2(Map<String ,Object> params);
	
	public List<Map<String ,Object>> queryOrderDetail(Map<String ,Object> params);
	
	public List<Map<String ,Object>> queryOrderDetailGoodListWJ(Map<String ,Object> params);
	public List<Map<String ,Object>> queryOrderDetailGoodList(Map<String ,Object> params);
	
	public Map<String ,Object> querySupOrderMain(Map<String ,Object> params);	
	
	public Map<String ,Object> getDeliveryPrice(Map<String ,Object> params);	
	
	public Map<String ,Object> getGoodDeliveryPrice(Map<String ,Object> params);
	
	public List<Map<String ,Object>> getOrderByMainOrderNo(Map<String ,Object> params);
	
	public Map<String ,Object> getGoodById(Map<String ,Object> params);
	
	public List<Map<String ,Object>> querySupplierByCart2(Map<String ,Object> params);
	
	public List<Map<String ,Object>> querySupplierGoodListNew(@Param("company_code")String company_code,@Param("area_dict_num")String area_dict_num,@Param("shop_unique")String shop_unique);

	public List<Map<String ,Object>> selectPromotionGoodsList(List<Map<String ,Object>> list);
	
	public List<Map<String ,Object>> getOrderDetail(String main_order_no);

	public List<Map<String, Object>> getGoodskindTwo(Map<String, Object> map);
	
	public List<Map<String, Object>> getGoodskind_new(Map<String, Object> map);
	
	public List<Map<String, Object>> getGoodskindTwo_new();
	
	public List<Map<String, Object>> getGoodskindTwo_new(Map<String, Object> map);
	
	public List<Map<String, Object>> getGoodskindTwo_food(Map<String, Object> map);

	public List<Map<String, Object>> querySupplierList2(Map<String, Object> params);

	public List<Map<String, Object>> queryBrandList(Map<String, Object> params);

	public int updateApplyRefund(Map<String, Object> params);

	public int cancelApplyRefund(Map<String, Object> params);

	public void addSysmsg(SystemMsgBean bean);

	public String[] getManagerList(Map<String, Object> params);

	public void addSysmsgUser(Map<String, Object> sysmsguser);
	
	public int getGoodsOrderCount(Map<String, Object> map);
	
	public List<Map<String ,Object>> querySupplierGoodBindingList(@Param("company_code")String company_code,@Param("area_dict_num")String area_dict_num,@Param("ids")String[] ids,@Param("shop_unique") String shop_unique);
	
	public List<Map<String ,Object>> querySupplierByBinding(@Param("ids")String[] ids, @Param("shop_unique")String shop_unique);
	
	public List<Map<String ,Object>> querySupplierGoodBindingList2(@Param("company_code")String company_code,@Param("ids")String[] ids,@Param("shop_unique") String shop_unique);
	//领取优惠券
	public int record(Map<String ,Object> params);
	//领取优惠券
	public int recordAll(List<Map<String,Object>> list);
	
	//查询优惠券
	//public List<Map<String ,Object>> getRecCoupon(Map<String ,Object> params);
	
	public void updateRecStatus(Map<String, Object> map);

	public int insertGiftOrder(List<Map<String, Object>> resource);

	public int insertGiftCoupon(List<Map<String, Object>> giftCoupon);

	public int queryShoppingCouponCount(Map<String, Object> map);

	public Map<String, Object> getOneCoupon(Map<String, Object> map);

	public int upCoupon(List<Map<String, Object>> list);

	public int addRecStatus(Map<String, Object> upMap);

	public List<Map<String, Object>> getUsedCoupon(Map<String, Object> reMap);
	
	public List<Map<String, Object>> queryShoppingOrder(Map<String, Object> params);

	public int delFullCoupon(List<Map<String, Object>> list);

	public int upCouponNumber(Map<String, Object> reCoupon);

	public List<Map<String, Object>> queryOrderCoupon(Map<String, Object> reCoupon);

	public int getRecCoupon(Map<String, Object> re);

	public List<Map<String, Object>> getAdminCoupon(Map<String, Object> params);

	public void updateGoodsCount(Map<String, Object> goods);

	public Map<String, Object> queryGoodsStock(Map<String, Object> goods);

	public void updateGoodsAddCount(Map<String, Object> goods);

	public List<Map<String, Object>> queryOrderGoodsCount(Map<String, Object> params);

	public Map<String, Object> queryMainOrderInfo(Map<String, Object> params);
	
}
