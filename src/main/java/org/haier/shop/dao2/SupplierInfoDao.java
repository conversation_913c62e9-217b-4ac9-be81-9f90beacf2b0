package org.haier.shop.dao2;

import java.util.List;
import java.util.Map;

import org.apache.ibatis.annotations.Param;

public interface SupplierInfoDao {
	
	//获取商品上次采购的供货商信息
	public Map<String ,Object> getSupplierInfo(Map<String ,Object> params);
	
	//获取商品所有供货商列表
	public List<Map<String ,Object>> getSupplierList(Map<String ,Object> params);
	
	//获取当前供货商的当前商品信息
	public List<Map<String ,Object>> getSupplierGoodsList(Map<String, Object> params);
	
	//获取当前商品的所有规格列表
	public List<Map<String ,Object>> getGoodsSpecList(Map<String, Object> params);
	
	//当前促销活动是否指定该商家
	public Integer getPromtionShopCount(Map<String, Object> params);
	
	//获取该商品规格的总库存
	public Integer getTotalCount(Map<String, Object> params);
	
	//插入订单主表
	public Integer insertOrder(Map<String, Object> params);
	
	//插入订单详情表
	public Integer insertOrderDetail(Map<String, Object> params);
	
	//插入订单规格详情表
	public Integer insertOrderGoodsSpec(Map<String, Object> params);
	
	//获取该商品规格所在仓库信息
	public List<Map<String ,Object>> getGoodsstockList(Map<String, Object> params);
	
	//修改商品规格所在仓库库存信息
	public void updateGoodsStockCount(Map<String, Object> params);
	
	//添加订单库存表
	public void insertOrderGoodsStock(Map<String, Object> params);
	
	//获取供货商商品信息
	public Map<String ,Object> getGoodsInfo(Map<String, Object> params);
	
	//获取供货商信息
	public Map<String ,Object> getCompanyInfo(String company_code);
	
	//分页获取总店所有商品列表
	public List<Map<String ,Object>> getCompanyGoodsList(Map<String, Object> params);
	
	//获取总店所有商品列表总条数
	public Integer getCompanyGoodsListCount(Map<String, Object> params);
	
	//分页获取关联供货商和总店商品列表
	public List<Map<String ,Object>> getOtherGoodsList(Map<String, Object> params);
	
	//获取关联供货商和总店商品列表总条数
	public Integer getOtherGoodsListCount(Map<String, Object> params);
	
	//获取百货豆赠送比例
	public Integer getPercentageNum();
	
	//添加云商订单消息提醒
	public Integer addSysmsg(Map<String ,Object> params);
	
	//获取供货商具有云商订单权限的所有用户id
	public List<String> getUserList(String company_code);
	
	//添加供货商消息用户表
	public void addSysmsgUser(List<Map<String ,Object>> list);
	
	//获取该赠送百货豆活动详情
	public Map<String ,Object> getBeansGoodsGiveInfo(String beans_goods_give_id);
	
	//修改商品百货豆规则已经补贴数量
	public void updateResidueCount(Map<String, Object> params);
	
	//修改商品百货豆促销活动使用数量
	public void updateUseCount(Map<String, Object> params);

	public List<Map<String, Object>> querySchoolContentList(Map<String, Object> map);

	public Map<String, Object> querySchoolContent(Map<String, Object> map);
}
