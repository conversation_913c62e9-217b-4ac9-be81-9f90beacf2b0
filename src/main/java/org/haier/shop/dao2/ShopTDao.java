package org.haier.shop.dao2;

import java.util.Map;

/**
 * 店铺新增修改等操作，用于店铺信息供货端与商家端的同步
 * <AUTHOR>
 *
 */
public interface ShopTDao {
	
	/**
	 * 更新店铺town_code
	 * @param map
	 * @return
	 */
	public Integer updateShopTownCode(Map<String,Object> map);
	/**
	 * 注册新店铺信息
	 * @param map
	 * @return
	 */
	public Integer registerNewShop(Map<String,Object> map);
	/**
	 * 更新店铺的审核状态
	 * @param map
	 * @return
	 */
	public Integer updateShopExamine(Map<String,Object> map);
	
	/**
	 * 更新店铺信息
	 * @param map
	 * @return
	 */
	public Integer updateShopDetail(Map<String,Object> map);
}
