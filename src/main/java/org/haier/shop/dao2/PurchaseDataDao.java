package org.haier.shop.dao2;

import java.util.List;
import java.util.Map;

public interface PurchaseDataDao {

	Map<String, Object> queryShopIsCenter(Map<String, Object> params);

	List<Map<String, Object>> queryChildSaleCount(Map<String, Object> params);

	Map<String, Object> queryOrderMsg(Map<String, Object> params);

	List<Map<String, Object>> queryHotSaleGoods(Map<String, Object> params);

	List<Map<String, Object>> querySaleTrend(Map<String, Object> map);

	List<Map<String, Object>> querySaleMoneyByPurchase(Map<String, Object> params);

	List<Map<String, Object>> queryOrderListPurchase(Map<String, Object> params);

	List<Map<String, Object>> querySaleCountByMonth(Map<String, Object> map);

	List<Map<String, Object>> kindSaleRatio();

	Double querySaleTBYearWeekMoney();

	Double querySaleTBNowWeekMoney();

	List<Map<String, Object>> queryPinPaiList();

	

}
