package org.haier.shop.dao2;

import java.util.List;
import java.util.Map;

public interface SupOrderDao {
	
	/**
	 * 查询进货订单列表
	 * @param shop_unique
	 * @param search_str 订单编号/供货商名称
	 * @param start_date 查询开始时间
	 * @param end_date 查询结束时间
	 * @param order_status 订单状态1: 待发货 2:待配送 3:配送中 4:已完成
	 * @param pay_status 支付状态：1、欠款；2、已结清
	 * @param order_type 订单类型 0：自动下单；1：客户下单
	 * @param startNum
	 * @param pageSize
	 * @return
	 */
	public List<Map<String ,Object>> getSupOrderList(Map<String ,Object> params);
	
	/**
	 * 查询进货订单列表总条数
	 * @param shop_unique
	 * @param search_str 订单编号/供货商名称
	 * @param start_date 查询开始时间
	 * @param end_date 查询结束时间
	 * @param order_status 订单状态1: 待发货 2:待配送 3:配送中 4:已完成
	 * @param pay_status 支付状态：1、欠款；2、已结清
	 * @param order_type 订单类型 0：自动下单；1：客户下单
	 * @return
	 */
	public Integer getSupOrderListCount(Map<String ,Object> params);
	
	/**
	 * 查询订单详情
	 * @param order_code 订单编号
	 * @return
	 */
	public Map<String ,Object> getSupOrder(String order_code);
	
	/**
	 * 查询订单子单列表
	 * @param order_code 订单编号
	 * @return
	 */
	public List<Map<String ,Object>> getSupOrderdetailList(String order_code);
	
	/**
	 * 查询订单支付列表
	 * @param order_code 订单编号
	 * @return
	 */
	public List<Map<String ,Object>> getSupOrderpayList(String order_code);
	
	/**
	 * 查询订单子单商品规格列表
	 * @param orderdetail_id 订单子单id
	 * @return
	 */
	public List<Map<String ,Object>> getSupOrderGoodsSpecsList(String orderdetail_id);
	
	/**
	 * 查询订单子单商品规格总数量
	 * @param sup_order_goodsSpecs_id 订单商品规格ID
	 * @return
	 */
	public Integer getSpecCount(String sup_order_goodsSpecs_id);
	
	/**
	 * 修改订单状态
	 * @param order_code 订单编号
	 * @param order_status 5:已取消
	 * @return
	 */
	public void updateOrderStatus(Map<String ,Object> params);
}
