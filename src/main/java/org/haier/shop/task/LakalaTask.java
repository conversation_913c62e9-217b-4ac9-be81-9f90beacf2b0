package org.haier.shop.task;

import java.io.IOException;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Random;
import java.util.SortedMap;
import java.util.TreeMap;

import javax.annotation.Resource;

import org.haier.shop.dao.TaskDao;
import org.haier.shop.util.JPushClientUtil;
import org.haier.shop.util.LakalaPayUtil;
import org.haier.shop.util.MUtil;
import org.haier.shop.util.SendMsgUtil;
import org.haier.shop.util.ShopsUtil;
import org.haier.shop.util.SmsMessageUtil;
import org.haier.shop.util.WeiXinUtil;

/**
 * 定时任务，检查未处理的订单
 * <AUTHOR>
 *
 */
public class LakalaTask {
	
	@Resource
	private TaskDao taskDao;
	
	
	public void handleUnpaidOrder() {
		List<Map<String,Object>> saleLists = taskDao.getListUnpaid();
		if(null != saleLists && !saleLists.isEmpty()) {
			for(int i = 0;i<saleLists.size();i++) {
				Map<String,Object> parmap = saleLists.get(i);
				SortedMap<String,String> map = new TreeMap<>();
				map.put("out_trade_no", parmap.get("out_trade_no").toString());
				map.put("mch_id", parmap.get("mch_id").toString());
				map.put("key", parmap.get("mch_key").toString());
				try {
					Map<String, String> resMap = LakalaPayUtil.getPayResult(map);
					if(null != resMap && !resMap.isEmpty()) {
						if(resMap.get("trade_state").toString().equals("SUCCESS")) {//若订单已成功
							//修改本地订单信息，修改
							Map<String, Object> listMap = new HashMap<String, Object>();//用来存储订单信息
							String out_trade_no = MUtil.strObject(map.get("out_trade_no"));//商户订单号
			                Double total_fee = Double.valueOf(MUtil.strObject(map.get("total_fee")));//总金额
			                String time_end = MUtil.strObject(map.get("time_end"));//支付时间
			                String transaction_id = MUtil.strObject(map.get("transaction_id"));//微信支付订单号	
			                String mch_id = MUtil.strObject(map.get("mch_id"));
							
							listMap.put("out_trade_no", out_trade_no);
							listMap.put("total_fee", total_fee/100);
							listMap.put("time_end", time_end);
							listMap.put("transaction_id", transaction_id);
							listMap.put("mch_id", mch_id);
							listMap.put("server_type", "2");//4：微信平台；2：拉卡拉平台
							Integer payType = 3;
							Map<String, Object> rmap= taskDao.getSaleList(listMap);//根据订单查询支付信息
							if(((Integer)rmap.get("sale_list_state"))!=3){//若订单未改为已付款状态
								Double sale_list_actually_received= ((BigDecimal)rmap.get("sale_list_actually_received")).doubleValue();
								Double card_deduction= ((BigDecimal)rmap.get("card_deduction")).doubleValue();
								Double beans_money= ((BigDecimal)rmap.get("beans_money")).doubleValue();
								Double endMoney=(Double) listMap.get("total_fee");
								
								if(card_deduction>0&&beans_money>0){
									//微信 和储值卡 百货豆混合支付
									rmap.put("sale_list_payment", 8);
									Map<String, Object> payMethod=new HashMap<String, Object>();
									payMethod.put("sale_list_unique", map.get("sale_list_unique"));
									payMethod.put("sale_list_payment", 5);
									payMethod.put("card_deduction", map.get("card_deduction"));

									taskDao.savePayMethod(payMethod);
									payMethod.put("sale_list_payment", 11);
									payMethod.put("card_deduction", beans_money);
									taskDao.savePayMethod(payMethod);
									payMethod.put("sale_list_payment", payType);
									payMethod.put("card_deduction", endMoney);
									payMethod.put("mch_id",listMap.get("mch_id"));
									payMethod.put("server_type", listMap.get("server_type"));
									taskDao.savePayMethod(payMethod);
								}else if(card_deduction>0){
									//微信 和储值卡混合支付
									rmap.put("sale_list_payment", 8);
									Map<String, Object> payMethod=new HashMap<String, Object>();
									payMethod.put("sale_list_unique", map.get("sale_list_unique"));
									payMethod.put("sale_list_payment", 5);
									payMethod.put("card_deduction", map.get("card_deduction"));
								
									taskDao.savePayMethod(payMethod);
									payMethod.put("sale_list_payment", payType);
									payMethod.put("card_deduction", endMoney);
									payMethod.put("mch_id",listMap.get("mch_id"));
									payMethod.put("server_type", listMap.get("server_type"));
									taskDao.savePayMethod(payMethod);
								}else if(beans_money>0){
									//微信 和百货豆混合支付
									rmap.put("sale_list_payment", 8);
									Map<String, Object> payMethod=new HashMap<String, Object>();
									payMethod.put("sale_list_unique", map.get("sale_list_unique"));
									payMethod.put("sale_list_payment", 11);
									payMethod.put("card_deduction", beans_money);
									taskDao.savePayMethod(payMethod);
									payMethod.put("sale_list_payment", payType);
									payMethod.put("card_deduction", endMoney);
									payMethod.put("mch_id",listMap.get("mch_id"));
									payMethod.put("server_type", listMap.get("server_type"));
									taskDao.savePayMethod(payMethod);
								}else{
									//微信支付
									rmap.put("sale_list_payment", 3);
									Map<String, Object> payMethod=new HashMap<String, Object>();
									payMethod.put("sale_list_unique", map.get("sale_list_unique"));
									payMethod.put("sale_list_payment", payType);
									payMethod.put("card_deduction", endMoney);
									payMethod.put("mch_id",listMap.get("mch_id"));
									payMethod.put("server_type", listMap.get("server_type"));
									taskDao.savePayMethod(payMethod);
								}
								
								//支付完成需要做的操作
								rmap.put("sale_list_state", 3);//已付款
								if((Integer)rmap.get("shipping_method")==2){
									rmap.put("sale_list_handlestate", 9);//待自提
								}else{
									rmap.put("sale_list_handlestate", 2);//待发货
								}
								//如果是平台的优惠券修改店铺的累计优惠券数量和优惠金额
						    	if(!"0".equals(rmap.get("shop_coupon_id").toString())){
						  	    	Map<String, Object> shopCouponMap =taskDao.queryShopCouponById(rmap);
						  	    	if(((Long)shopCouponMap.get("shop_unique")).toString().equals("8302016134121")){
						  	    		taskDao.updateShopPlatformCouponCount(shopCouponMap);
						  	    	}
						    	 }
						    	
								//查询商品列表
								List<Map<String, Object>> goodsList=taskDao.querySaleListDetailGoods(rmap);
								if(null != goodsList && !goodsList.isEmpty()) {
									payCompleteMake(rmap.get("cus_unique").toString() ,rmap.get("shop_unique").toString(),rmap.get("point_val").toString() , 
											rmap.get("beans_use").toString() ,rmap.get("card_deduction").toString() ,rmap ,(Long)rmap.get("sale_list_unique") ,
											goodsList);
								}
								rmap.put("trade_no", MUtil.strObject(listMap.get("transaction_id")));
								//修改订单状态等信息
								taskDao.updateSaleListBalancePay(rmap);
								
								//更新订单详情里的百货豆
								for (Map<String, Object> goods : goodsList) {
									taskDao.updateSaleListDetailBeans(goods);
								}
								if((Integer)rmap.get("sale_type")==7){
									//确认收货
									String sale_list_unique = rmap.get("sale_list_unique").toString();
									Map<String, Object> surmap = new HashMap<String, Object>();
									surmap.put("sale_list_unique", sale_list_unique);
									
									// 计算商家应该得到多少钱
									Double sum = 0.0;
									Map<String, Object> saleMap = taskDao.getSaleList(surmap);
									
									if ((Integer) saleMap.get("add_shop_balance_status") != 1) {
										// 优惠券金额
										Double coupon_amount = ((BigDecimal) saleMap.get("coupon_amount")).doubleValue();
										// 判断优惠券是平台优惠券还是商家优惠券
										Map<String, Object> shopUniqueMap = taskDao.queryShopCouponShopUnique(saleMap);
										if (shopUniqueMap != null) {
											if (((Long) shopUniqueMap.get("shop_unique")).toString().equals("8302016134121")) {
												// 平台
											} else {
												// 店铺
												coupon_amount = 0.0;
											}
										}
										// 判断是否计算配送费
										Double peisong = 0.0;
										if (saleMap.get("shipping_method") != null && (Integer) saleMap.get("shipping_method") == 2) {
											// 自提
										} else {
											// 查询配送费
											Map<String, Object> peiSongMap = taskDao.getPeiSongMoney(surmap);
											peisong = ((BigDecimal) peiSongMap.get("shop_delivery_price")).doubleValue();
										}
										// 积分抵扣金额
										Double point_deduction = ((BigDecimal) saleMap.get("point_deduction")).doubleValue();
										// 查询微信或支付宝的支付金额
										Double pay = 0.0;
										List<Map<String, Object>> payList = taskDao.queryPayMethodMoney(surmap);
										for (Map<String, Object> map2 : payList) {
											if ((Integer) map2.get("pay_method") == 3) {
												Double pay_money = ((BigDecimal) map2.get("pay_money")).doubleValue();
												pay += pay_money;
											} else if ((Integer) map2.get("pay_method") == 5) {
												Double pay_money = ((BigDecimal) map2.get("pay_money")).doubleValue();
												pay += pay_money;
											}
										}
										sum = coupon_amount - peisong - point_deduction + pay;
										// 修改店铺的剩余金额
										saleMap.put("shop_balance", sum);
										// System.out.println("修改店铺剩余金额"+sum);
										// 查询随机赠送的百货豆范围
										Map<String, Object> beansConfig = taskDao.queryBeansConfig();
										// 满足金额平台赠送会员百货豆
										if (beansConfig != null && pay >= (Integer) beansConfig.get("pt_mem_moeny")) {
											String num = getRandom((Integer) beansConfig.get("pt_mem_small"),
													(Integer) beansConfig.get("pt_mem_top"));
											surmap.put("platform_cus_beans", num);
											surmap.put("give_beans", num);
											// 增加会员百货豆
											taskDao.updateBeansJia(surmap);

										}
										// 平台赠送百货豆
										surmap.put("shop_unique", saleMap.get("shop_unique"));
										surmap.put("cus_unique", saleMap.get("cus_unique"));
										//查询订单中烟的商品金额
										double yan_money= taskDao.queryOrderYanMoney(surmap);
										System.out.println("返利金额"+(sum-yan_money));
										if(sum-yan_money>0){
											updateGiveCusBeansAndBalance(surmap, sum-yan_money);
										}
										// 查询店铺是否开通百货豆支付
										Map<String, Object> shopConfig = taskDao.queryOpenBeansPayStatus(saleMap);
										if (shopConfig != null && (Integer) shopConfig.get("beans_agreement") == 1) {
											// 非免密支付
											if (beansConfig != null && pay >= (Integer) beansConfig.get("fmm_moeny")) {
												Integer mm_probability = (Integer) beansConfig.get("fmm_probability");
												if (mm_probability >= 100) {
													String num = getRandom((Integer) beansConfig.get("fmm_small"),
															(Integer) beansConfig.get("fmm_top"));
													// 增加店铺豆
													map.put("platform_shop_beans", num);
													saleMap.put("beans_use",
															((BigDecimal) saleMap.get("beans_use")).doubleValue() + Double.parseDouble(num));
												} else {
													String cc = mm_probability.toString().substring(0, 1);
													String ss = getRandom(1, Integer.parseInt(cc));
													if (cc.equals(ss)) {
														String num = getRandom((Integer) beansConfig.get("fmm_small"),
																(Integer) beansConfig.get("fmm_top"));
														// 增加店铺豆
														map.put("platform_shop_beans", num);
														saleMap.put("beans_use",
																((BigDecimal) saleMap.get("beans_use")).doubleValue() + Double.parseDouble(num));
													}
												}
											}
										}

										// 给店铺加上百货豆,余额
										taskDao.updateShopBalance(saleMap);
										// 给代理商计算分润
										//updateSaleProfit(saleMap, sum);
										if (((Integer) saleMap.get("sale_type")) != 7) {
											// 给客户推送确认收货的消息
											if (saleMap.get("formId") != null && !"".equals(saleMap.get("formId"))
													&& !"null".equals(saleMap.get("formId"))) {
												// 推送小程序
												Map<String, Object> cus = taskDao.getOpenid(saleMap);
												WeiXinUtil.sendXiaoMsg((String) cus.get("openid"), (String) saleMap.get("formId"),
														sale_list_unique);
											} else {
												// 推送app
												String alert = "您购买的商品已经确认收货了！订单号:" + sale_list_unique;
												Map<String, Object> cus = taskDao.getOpenid(saleMap);
												String registration_id = (String) cus.get("registration_id");
												Integer cus_source = (Integer) cus.get("cus_source");
												if (registration_id != null && !"".equals(registration_id) && !"null".equals(registration_id)) {
													JPushClientUtil.notifyApp(registration_id, alert, cus_source, sale_list_unique);
												}
											}
										}

									}
								}
							}
							
						}
					}
				} catch (IOException e) {
					e.printStackTrace();
				}
			}
			
		}
	}
	
	public void payCompleteMake(String cus_unique, String shop_unique, String point_val, String beans_use,
			String card_deduction, Map<String, Object> map, Long sale_list_unique,
			List<Map<String, Object>> goodsList) {
		//查询是否会员
		Map<String, Object> cusMap= taskDao.validateCus(map);
		if(cusMap!=null){
			map.put("cus_id", cusMap.get("platform_cus_id"));
			//保存减掉日志记录
			List<Map<String, Object>> logList=new ArrayList<Map<String,Object>>();
			if(Double.parseDouble(card_deduction)>0){
				Map<String, Object> saleLog = new HashMap<String, Object>();
				saleLog.put("sale_list_unique", sale_list_unique);
				saleLog.put("shop_unique", shop_unique);
				saleLog.put("cus_unique", cus_unique);
				saleLog.put("money_type", 1);
		    	saleLog.put("sale_type", 2);
		    	saleLog.put("money", card_deduction);
		    	//减掉储值卡金额日志
		    	//dao.saveSaleLog(saleLog);
		    	logList.add(saleLog);
			}
			if(Double.parseDouble(beans_use)>0){
				Map<String, Object> saleLog = new HashMap<String, Object>();
				saleLog.put("sale_list_unique", sale_list_unique);
				saleLog.put("shop_unique", shop_unique);
				saleLog.put("cus_unique", cus_unique);
				saleLog.put("money_type", 2);
		    	saleLog.put("sale_type", 4);
		    	saleLog.put("money", beans_use);
		    	//减掉百货豆日志
		    	//dao.saveSaleLog(saleLog);
		    	logList.add(saleLog);
			}
			if(Double.parseDouble(point_val)>0){
				Map<String, Object> saleLog = new HashMap<String, Object>();
				saleLog.put("sale_list_unique", sale_list_unique);
				saleLog.put("shop_unique", shop_unique);
				saleLog.put("cus_unique", cus_unique);
		    	saleLog.put("money_type", 3);
		    	saleLog.put("sale_type", 5);
		    	saleLog.put("money", point_val);
		    	//减掉积分日志
		    	//dao.saveSaleLog(saleLog);
		    	logList.add(saleLog);
			}
			//累计积分
			double sumPoint=0;
			for (Map<String, Object> goods : goodsList) {
				//查询商品是否有配置方案
				String goods_barcode=(String) goods.get("goods_barcode");
				Map<String,Object> params=new HashMap<String,Object>();
				if(goods_barcode!=null&&!"".equals(goods_barcode)){
					params.put("goods_barcode", goods_barcode);
					params.put("shop_unique", shop_unique);
					Map<String,Object> resultMap=taskDao.findGoodsPointSet(params);
					if(resultMap!=null){
						Integer goods_points_type= (Integer) resultMap.get("goods_points_type");
						BigDecimal goods_points_val=(BigDecimal) resultMap.get("goods_points_val");
						Integer goods_kind_points_type= (Integer) resultMap.get("goods_kind_points_type");
						BigDecimal goods_kind_points_val=(BigDecimal) resultMap.get("goods_kind_points_val");
						Integer goods_kind_points_type_parent= (Integer) resultMap.get("goods_kind_points_type_parent");
						BigDecimal goods_kind_points_val_parent=(BigDecimal) resultMap.get("goods_kind_points_val_parent");
						if(goods_points_type!=null&&goods_points_val!=null){
							
						}else if(goods_kind_points_type!=null&&goods_kind_points_val!=null){
							//查询小分类是否有配置方案
							goods_points_type=goods_kind_points_type;
							goods_points_val=goods_kind_points_val;
						}else if(goods_kind_points_type_parent!=null&&goods_kind_points_val_parent!=null){
							//查询大分类是否有配置方案
							goods_points_type=goods_kind_points_type_parent;
							goods_points_val=goods_kind_points_val_parent;
						}
						if(goods_points_type!=null&&goods_points_val!=null){
							double point=0;
							if(goods_points_type==1){
								//金额比例
								//point=(Double.parseDouble((String)goods.get("sale_list_detail_price"))* (Integer)goods.get("sale_list_detail_count")/goods_points_val.doubleValue());
							}else if(goods_points_type==2){
								//固定积分
								//point=(Integer)goods.get("sale_list_detail_count")*goods_points_val.doubleValue();
							}else if(goods_points_type==3){
								//商品利润
								//point=((Double.parseDouble((String)goods.get("sale_list_detail_price"))-((BigDecimal)goods.get("goods_purprice")).doubleValue())*(Integer)goods.get("sale_list_detail_count")/goods_points_val.doubleValue());
							}
							sumPoint+=point;
						}
					}
				}
			}
			map.put("points_get",BigDecimal.valueOf(sumPoint).setScale(2,BigDecimal.ROUND_HALF_UP));
			if(sumPoint>0){
				//加上积分
				//判断店铺积分是否有记录没有则插入一条
				Map<String, Object> shopPoints=taskDao.queryShopPointsByCusUnique(map);
				if(shopPoints!=null&&!shopPoints.isEmpty()&&shopPoints.get("shop_unique")!=null){
					taskDao.updateShopPointsByCusJia(map);
				}else{
					taskDao.insertShopPointsByCusUnique(map);
				}
			}
			//累计百货豆
			Map<String, Object> benasMap= taskDao.queryBeansGetByMoney(map);
			//查询店铺是否开通百货豆支付
			Map<String, Object> shopConfig= taskDao.queryOpenBeansPayStatus(map);
			if(benasMap!=null&&(Integer)shopConfig.get("beans_agreement")==1){
				Double fmm_per=((Integer)benasMap.get("fmm_per")).doubleValue();
				Integer  beans= BigDecimal.valueOf(((fmm_per/100)*Double.parseDouble(card_deduction))*100).setScale(0,BigDecimal.ROUND_HALF_UP).intValue();
				map.put("beans_get", beans);
			}else{
				map.put("beans_get", 0);
			}
			//计算供货商和店铺赠送的百货豆
			saveCountGiveBeans(shopConfig,goodsList, shop_unique, cus_unique, map);
			//查询店铺的百货豆是否足够
			Map<String, Object> shopBeansMap= taskDao.queryShopBeansCan(map);
			if((Long)shopBeansMap.get("shop_beans")>=(Integer)map.get("beans_get")){
				//减掉店铺的百货豆
				taskDao.updateShopBeans(map);
			}else{
				//店铺百货豆不足给商家发送短信
				if((Integer)shopBeansMap.get("beans_send_msg_status")==0){
					String shop_phone=(String) shopBeansMap.get("shop_phone");
					if(shop_phone!=null&&!"".equals(shop_phone)&&shop_phone.length()==11){
						//SendMsgUtil.sendShopBeansBalanceMsg(shop_phone, ""); 老的
						SmsMessageUtil.send(shop_phone, "", "SMS_151178089");
						//修改发送状态下次不再发送
						taskDao.updateSendBeansMsgStataus(map);
					}
				}
				map.put("beans_get", 0);
			}
			
			//给用户加上百货豆和积分
			taskDao.updatePointsBeansByCusJia(map);
			//保存累计积分，百货豆日志
			if(sumPoint>0){
				Map<String, Object> saleLog = new HashMap<String, Object>();
				saleLog.put("sale_list_unique", sale_list_unique);
				saleLog.put("shop_unique", shop_unique);
				saleLog.put("cus_unique", cus_unique);
		    	saleLog.put("money_type", 3);
		    	saleLog.put("sale_type", 6);
		    	saleLog.put("money", sumPoint);
		    	//加上积分日志
		    	logList.add(saleLog);
			}
			if(((Integer)map.get("beans_get"))>0||((Integer)map.get("sup_give_beans"))>0||((Integer)map.get("shop_give_beans"))>0){
				Map<String, Object> saleLog = new HashMap<String, Object>();
				saleLog.put("sale_list_unique", sale_list_unique);
				saleLog.put("shop_unique", shop_unique);
				saleLog.put("cus_unique", cus_unique);
				saleLog.put("money_type", 2);
				saleLog.put("sale_type", 3);
				saleLog.put("money", (Integer)map.get("beans_get")+(Integer)map.get("sup_give_beans")+(Integer)map.get("shop_give_beans"));
				//加上百货豆日志
				logList.add(saleLog);
			}
			//保存日志
			if(logList.size()>0){
				taskDao.saveBatchSaleLog(logList);
			}
		}else{
			//非会员
			map.put("points_get", 0);
			map.put("beans_get", 0);
		}
		//添加出库记录
		List<Map<String, Object>> editGoodsCountList=new ArrayList<Map<String,Object>>();
		List<Map<String, Object>> stockList=new ArrayList<Map<String,Object>>();
		for (Map<String, Object> goods : goodsList) {
			Map<String, Object> maps = new HashMap<String, Object>();
			String goods_barcode=(String) goods.get("goods_barcode");
			Double sale_list_detail_count=Double.parseDouble(goods.get("sale_list_detail_count").toString());
			maps.put("goodsBarcode",goods_barcode);
			maps.put("shopUnique",shop_unique);
			//查询基本单位的商品信息
			Map<String, Object>  goodsInfo =taskDao.getSmallGoodsInfo2(maps);
			if(goodsInfo!=null){
				maps.put("goodsId", goodsInfo.get("goods_id"));
				maps.put("goodsBarcode", goodsInfo.get("goodsBarcode"));
				maps.put("goodsCount",sale_list_detail_count*(Long)goodsInfo.get("goods_contain"));
				maps.put("shoppingCartCount",sale_list_detail_count*(Long)goodsInfo.get("goods_contain"));
				System.out.println("-----"+maps);
				editGoodsCountList.add(maps);
			}
			
			//添加出库记录
			Map<String,Object> smap=new HashMap<String, Object>();
			if(goodsInfo!=null){
				smap.put("goodsBarcode", goodsInfo.get("goodsBarcode"));
				smap.put("goodsCount",sale_list_detail_count*(Long)goodsInfo.get("goods_contain"));
				smap.put("stock_count",((BigDecimal)goodsInfo.get("goods_count")).doubleValue()-sale_list_detail_count*(Long)goodsInfo.get("goods_contain"));
			}else{
				smap.put("goodsBarcode", goods_barcode);
				smap.put("goodsCount",sale_list_detail_count);
				smap.put("stock_count",0);
			}
			smap.put("stockType", 2);
			smap.put("shopUnique",shop_unique);
			smap.put("stockResource", 5);
			smap.put("listUnique",sale_list_unique);
			smap.put("stock_price",goods.get("sale_list_detail_price"));
			smap.put("saleListCashier",0);
			if(smap.get("goodsBarcode")!=null){
				stockList.add(smap);
			}
		}
		//出库记录
		taskDao.newStockRecords(stockList);	
		//修改商品库存
		if(editGoodsCountList!=null&&editGoodsCountList.size()>0){
			taskDao.batchStocks(editGoodsCountList);
		}
		//计算分销商佣金
		//saveCountDisProfit(cus_unique, shop_unique, sale_list_unique, goodsList);
	}
	
	//计算供货商和商家赠送百货豆
		public void saveCountGiveBeans(Map<String, Object> shopConfig, List<Map<String, Object>> goodsList,String shop_unique,String cus_unique,
				Map<String, Object> map){
			Integer sup_give_beans=0;
			Integer shop_give_beans=0;
			if((Integer)shopConfig.get("beans_agreement")==1){
				for (Map<String, Object> goods : goodsList) {
					Map<String, Object> maps = new HashMap<String, Object>();
					String goods_barcode=(String) goods.get("goods_barcode");
					Double sale_list_detail_count=Double.parseDouble(goods.get("sale_list_detail_count").toString());
					maps.put("goodsBarcode",goods_barcode);
					maps.put("shopUnique",shop_unique);
					maps.put("goodsCount", sale_list_detail_count);
					//查询基本单位的商品信息
					Map<String, Object>  goodsInfo =taskDao.getSmallGoodsInfo(maps);
					if(goodsInfo!=null){
						maps.put("goodsBarcode", goodsInfo.get("goodsBarcode"));
						maps.put("goodsCount",sale_list_detail_count*ShopsUtil.objToDouble(goodsInfo.get("goods_contain")));
					}
					//查询商品是否是供货商百货豆补贴商品
					Map<String, Object> result= taskDao.queryGoodsInSupBeansGoods(maps);
					System.out.println("供货商补贴百货豆"+result);
					if(result!=null){
						//判断每单是否超过赠送的上限
						int beanTop=((Long)result.get("beanTop")).intValue();
						int goods_beans=((Long)result.get("goods_beans")).intValue();
						int beanTimes=((Integer)result.get("beanTimes")).intValue();
						if(goods_beans>beanTop){
							goods_beans=beanTop;
						}
						//判断今天是否超过赠送单数
						maps.put("goodsBarcode2", goods_barcode);
						maps.put("cus_unique", cus_unique);
						Integer count =taskDao.queryBeansGiveSaleCount(maps);
						if(count>=beanTimes){
							goods_beans=0;
						}
						sup_give_beans+=goods_beans;
						//减掉供货商赠送商品百货豆
						maps.put("goods_beans", goods_beans);
						goods.put("goods_beans_count", goods_beans);
						taskDao.updateGoodsInSupBeansGoods(maps);
					}
					//查询商品是否是商家百货豆补贴商品
					Map<String, Object> shop_result= taskDao.queryGoodsInShopBeansGoods(maps);
					System.out.println("商家补贴百货豆"+shop_result);
					if(shop_result!=null){
						int goods_beans=((Long)shop_result.get("goods_beans")).intValue();
						shop_give_beans+=goods_beans;
						goods.put("shop_beans_count", goods_beans);
					}
				}
			}
			map.put("sup_give_beans", sup_give_beans);
			map.put("shop_give_beans", shop_give_beans);
			if(shop_give_beans>0){
				//减掉商家百货豆
				taskDao.updateJianShopGiveBeans(map);
				//增加用户百货豆
				taskDao.updateJiaCusGiveBeans(map);
			}

		}
		
		public String getRandom(int min, int max) {
			Random random = new Random();
			if (max <= 0) {
				max = 1;
			}
			int s = random.nextInt(max) % (max - min + 1) + min;
			return String.valueOf(s);

		}
		
		// 平台赠送百货豆或余额
		public void updateGiveCusBeansAndBalance(Map<String, Object> params, Double money) {
			// 查询店铺是否设置赠送
			Map<String, Object> shop = taskDao.queryShopIsGiveConfig(params);
			if (shop != null) {
				Integer reward = (Integer) shop.get("reward");
				if (reward > 0) {
					Double give = money * reward / 1000;
					if (give > 0) {
						// 判断是赠送百货豆还是余额
						if ((Integer) shop.get("reward_type") == 0) {
							// 百货豆
							Integer giveBeans = BigDecimal.valueOf(give * 100).setScale(0, BigDecimal.ROUND_HALF_UP)
									.intValue();
							if (giveBeans > 0) {
								// 更新用户百货豆
								if (params.get("platform_cus_beans") != null) {
									params.put("platform_cus_beans",
											Integer.parseInt((String) params.get("platform_cus_beans")) + giveBeans);
								} else {
									params.put("platform_cus_beans", giveBeans);
								}
								params.put("give_beans", giveBeans);
								// 增加会员百货豆
								taskDao.updateBeansJia(params);
								// 添加日志
								Map<String, Object> saleLog = new HashMap<String, Object>();
								saleLog.put("sale_list_unique", params.get("sale_list_unique"));
								saleLog.put("shop_unique", "8302016134121");
								saleLog.put("cus_unique", params.get("cus_unique"));
								saleLog.put("money_type", 2);
								saleLog.put("sale_type", 10);
								saleLog.put("money", giveBeans);
								// 加上百货豆日志
								List<Map<String, Object>> logList = new ArrayList<Map<String, Object>>();
								logList.add(saleLog);
								taskDao.saveBatchSaleLog(logList);
							}
						} else {
							// 余额
							// 更新用户余额
							params.put("add_balance", give);
							// 增加会员百货豆
							taskDao.updateBalanceJia(params);
							// 添加日志
							Map<String, Object> saleLog = new HashMap<String, Object>();
							saleLog.put("sale_list_unique", params.get("sale_list_unique"));
							saleLog.put("shop_unique", "8302016134121");
							saleLog.put("cus_unique", params.get("cus_unique"));
							saleLog.put("money_type", 1);
							saleLog.put("sale_type", 9);
							saleLog.put("money", give);
							// 加上百货豆日志
							List<Map<String, Object>> logList = new ArrayList<Map<String, Object>>();
							logList.add(saleLog);
							taskDao.saveBatchSaleLog(logList);
						}
					}
				}
			}

		}
}
