package org.haier.shop.task;

import javax.annotation.Resource;

import org.haier.shop.config.SysConfig;
import org.haier.shop.dao.TVDao;
import org.haier.shop.util.AddOrderTask;
import org.haier.shop.util.IPGet;
import org.haier.util.eshow.Downdefault;

/**
 * 电视的定时下载任务
 * <AUTHOR>
 *
 */
public class TVDownload {
	@Resource
	private TVDao tvDao;

	public void downLoadTvMsg() {
		try {
			if(SysConfig.REDISTEST.equals(SysConfig.FORMAL)) {
				System.out.println("定时任务开始执行");
				Downdefault data = tvDao.queryEshow();
				String user = data.getUser();
				String pass = data.getPass();
				String host = data.getFtp();
				Integer port = data.getPort();
				String sourcePath = data.getSourcePath();
				String savePath = data.getSavePath();
				
				FtpUtils ftpUtilS = new FtpUtils(user,pass,host,port,sourcePath,savePath);
				ftpUtilS.run();
			}
		}catch (Exception e) {
			e.printStackTrace();
		}
	}
}
