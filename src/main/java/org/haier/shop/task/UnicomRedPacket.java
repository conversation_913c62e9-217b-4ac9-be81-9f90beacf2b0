package org.haier.shop.task;

import java.util.logging.Logger;

import javax.annotation.Resource;

import org.haier.shop.service.BusinessService;

/**
 * 定时任务：会员发放红包
 */
public class UnicomRedPacket {
	
	private static final Logger logger = Logger.getGlobal();
	
	@Resource
	private BusinessService businessService;
	
	/**
	 * 定时任务：会员发放红包
	 */
	public void grantCusRedPacket(){
		logger.info("定时任务，会员发放红包开始");
		try {
			businessService.grantCusRedPacket();
			
		} catch (Exception e) {
			e.printStackTrace();
			logger.info("定时任务，会员发放红包异常"+e);
		}
		logger.info("定时任务，会员发放红包结束");
	}
	
	/**
	 * 定时任务：红包过期操作
	 */
	public void redPacketOverdue(){
		logger.info("定时任务，红包过期操作开始");
		try {
			businessService.redPacketOverdue();
			
		} catch (Exception e) {
			e.printStackTrace();
			logger.info("定时任务，红包过期操作异常"+e);
		}
		logger.info("定时任务，红包过期操作结束");
	}
}
