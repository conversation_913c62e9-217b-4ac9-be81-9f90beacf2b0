package org.haier.shop.task;

import java.util.logging.Logger;

import javax.annotation.Resource;

import org.haier.shop.config.SysConfig;
import org.haier.shop.service.GoodsService;
import org.haier.shop.util.AddOrderTask;
import org.haier.shop.util.IPGet;

/**
 * 定时任务：自动采购
 */
public class GoodsAutoPurchase {
	
	private static final Logger logger = Logger.getGlobal();
	
	@Resource
	private GoodsService goodsService;
	
	/**
	 * 定时任务：商品自动采购
	 */
	public void goodsAutoPurchase(){
		logger.info("定时任务，商品自动采购开始");
		try {
			if (SysConfig.FORMAL.equals(SysConfig.REDISTEST)) {
				goodsService.goodsAutoPurchase();
			}

		} catch (Exception e) {
			e.printStackTrace();
			logger.info("定时任务，商品自动采购异常"+e);
		}
		logger.info("定时任务，商品自动采购结束");
	}
	
}
