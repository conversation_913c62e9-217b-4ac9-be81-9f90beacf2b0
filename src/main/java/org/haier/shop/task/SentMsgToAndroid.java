package org.haier.shop.task;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;

import org.eclipse.paho.client.mqttv3.MqttClient;
import org.haier.shop.dao.NoticeDao;
import org.haier.shop.redis.RedisCache;
import org.haier.shop.util.mqtt.ClientMQTT;
import org.haier.shop.util.mqtt.MqttxUtil;

public class SentMsgToAndroid implements Runnable{
	private NoticeDao noticeDao;
	private RedisCache redis;
	
	/**
	 * 初始化赋值
	 * @param noticeDao
	 * @param redis
	 * @param shopList
	 */
	public SentMsgToAndroid(NoticeDao noticeDao,RedisCache redis) {
		this.noticeDao = noticeDao;
		this.redis = redis;
	}
	public SentMsgToAndroid() {
	}

	public void run() {
		/*
		 * 依次查询各个店铺的消息数量，并推送到各个收银设备上
		 */
		
		//从缓存中获取需要推送消息的店铺
		Set<String> shopList = new HashSet<String>();
		System.out.println(redis == null);
		
		if(null != redis.getListKet(ClientMQTT.MQTTPUBKEY)) {
			shopList = (Set<String>)redis.getListKet(ClientMQTT.MQTTPUBKEY);
			System.out.println(redis.getObject(ClientMQTT.MQTTPUBKEY));
			System.out.println(ClientMQTT.MQTTPUBKEY);
		}
		
		if(null == shopList || shopList.isEmpty()) {
			System.out.println("没有要发送信息的店铺");
			return;
		}
		
		Map<String,Object> map = new HashMap<String,Object>();
		for(String shopUnique : shopList) {
			//获取该店铺的未读消息数量
			Integer count = noticeDao.queryNoticeCount(shopUnique.replace(ClientMQTT.MQTTPUBKEY, ""));
			//
			List<String> macIds = (List<String>)redis.getObject(shopUnique);
			
			for(String mac : macIds) {
				//依次向收银设备发送信息
				map.put("ctrl", "msg_news_count");
				map.put("status", "200");
				map.put("errcode", 0);
				map.put("count", count);
				
				System.out.println("当前发送的店铺shopUnique==" + shopUnique + ";;;mac ====" + mac + "====" + map);
				
				MqttxUtil.sendMapMsg(map, mac);
			}
		}
	}
	
	

}
