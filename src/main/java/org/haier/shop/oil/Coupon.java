package org.haier.shop.oil;

public class Coupon {
	//优惠券编号
	private Integer shopCouponId;
	//优惠券类型：1、平台通用券；2、连锁店通用；3、指定店铺使用
	private Integer couponType;
	//优惠券类型说明
	private String couponTypeName;
	//优惠券类型为2、3时、指定的连锁店或店铺编号
	private String designatedShopUnique;
	//开始使用时间
	private String startTime;
	//截至使用时间
	private String endTime;
	//发卷店铺
	private String shopUnique;
	//创建时间
	private String createTime;
	//优惠券名称
	private String couponName;
	//发放优惠券数量
	private Integer couponCount;
	//优惠券使用条件
	private Double meetAmount;
	//优惠券优惠金额
	private Double couponAmount;
	//优惠券类型：0、普通优惠券；2、加油专用优惠券
	private Integer exclusiveType;
	public Integer getShopCouponId() {
		return shopCouponId;
	}
	public void setShopCouponId(Integer shopCouponId) {
		this.shopCouponId = shopCouponId;
	}
	public Integer getCouponType() {
		return couponType;
	}
	public void setCouponType(Integer couponType) {
		this.couponType = couponType;
	}
	public String getCouponTypeName() {
		return couponTypeName;
	}
	public void setCouponTypeName(String couponTypeName) {
		this.couponTypeName = couponTypeName;
	}
	public String getDesignatedShopUnique() {
		return designatedShopUnique;
	}
	public void setDesignatedShopUnique(String designatedShopUnique) {
		this.designatedShopUnique = designatedShopUnique;
	}
	public String getStartTime() {
		return startTime;
	}
	public void setStartTime(String startTime) {
		this.startTime = startTime;
	}
	public String getEndTime() {
		return endTime;
	}
	public void setEndTime(String endTime) {
		this.endTime = endTime;
	}
	public String getShopUnique() {
		return shopUnique;
	}
	public void setShopUnique(String shopUnique) {
		this.shopUnique = shopUnique;
	}
	public String getCreateTime() {
		return createTime;
	}
	public void setCreateTime(String createTime) {
		this.createTime = createTime;
	}
	public String getCouponName() {
		return couponName;
	}
	public void setCouponName(String couponName) {
		this.couponName = couponName;
	}
	public Integer getCouponCount() {
		return couponCount;
	}
	public void setCouponCount(Integer couponCount) {
		this.couponCount = couponCount;
	}
	public Double getMeetAmount() {
		return meetAmount;
	}
	public void setMeetAmount(Double meetAmount) {
		this.meetAmount = meetAmount;
	}
	public Double getCouponAmount() {
		return couponAmount;
	}
	public void setCouponAmount(Double couponAmount) {
		this.couponAmount = couponAmount;
	}
	public Integer getExclusiveType() {
		return exclusiveType;
	}
	public void setExclusiveType(Integer exclusiveType) {
		this.exclusiveType = exclusiveType;
	}
}
