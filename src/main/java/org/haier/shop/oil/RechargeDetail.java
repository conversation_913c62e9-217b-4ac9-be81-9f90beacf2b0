package org.haier.shop.oil;

import java.util.List;

public class RechargeDetail {
	private Integer rechargeId;
	private Integer cusId;
	private String saleListUnique;
	private String rechargeDatetime;
	private Double cusBalance;
	private String cusName;
	private String cusPhone;
	private Double rechargeMoney;
	private Integer rechargeConfigId;
	private Double money;
	private String createTime;
	private Integer deleteStatus;
	private String rechargeName;
	private String startTime;
	private String endTime;
	private Integer isCoupon;
	private List<Coupon> couponList;
	private Integer isPoint;
	private Double addPoint;
	private Integer isGoods;
	private List<Goods> goodsList;
	private Integer isBalance;
	private Double addBalance;
	private Integer isCusLevel;
	private Integer cusLevelId;
	private String cusLevelName;
	private String shopUnique;
	private Integer isBeans;
	private Integer beansCount;
	
	public String getCusLevelName() {
		return cusLevelName;
	}
	public void setCusLevelName(String cusLevelName) {
		this.cusLevelName = cusLevelName;
	}
	public String getCusName() {
		return cusName;
	}
	public void setCusName(String cusName) {
		this.cusName = cusName;
	}
	public String getCusPhone() {
		return cusPhone;
	}
	public void setCusPhone(String cusPhone) {
		this.cusPhone = cusPhone;
	}
	public Integer getRechargeId() {
		return rechargeId;
	}
	public void setRechargeId(Integer rechargeId) {
		this.rechargeId = rechargeId;
	}
	public Integer getCusId() {
		return cusId;
	}
	public void setCusId(Integer cusId) {
		this.cusId = cusId;
	}
	public String getSaleListUnique() {
		return saleListUnique;
	}
	public void setSaleListUnique(String saleListUnique) {
		this.saleListUnique = saleListUnique;
	}
	public String getRechargeDatetime() {
		return rechargeDatetime;
	}
	public void setRechargeDatetime(String rechargeDatetime) {
		this.rechargeDatetime = rechargeDatetime;
	}
	public Double getCusBalance() {
		return cusBalance;
	}
	public void setCusBalance(Double cusBalance) {
		this.cusBalance = cusBalance;
	}
	public Double getRechargeMoney() {
		return rechargeMoney;
	}
	public void setRechargeMoney(Double rechargeMoney) {
		this.rechargeMoney = rechargeMoney;
	}
	public Integer getRechargeConfigId() {
		return rechargeConfigId;
	}
	public void setRechargeConfigId(Integer rechargeConfigId) {
		this.rechargeConfigId = rechargeConfigId;
	}
	public Double getMoney() {
		return money;
	}
	public void setMoney(Double money) {
		this.money = money;
	}
	public String getCreateTime() {
		return createTime;
	}
	public void setCreateTime(String createTime) {
		this.createTime = createTime;
	}
	public Integer getDeleteStatus() {
		return deleteStatus;
	}
	public void setDeleteStatus(Integer deleteStatus) {
		this.deleteStatus = deleteStatus;
	}
	public String getRechargeName() {
		return rechargeName;
	}
	public void setRechargeName(String rechargeName) {
		this.rechargeName = rechargeName;
	}
	public String getStartTime() {
		return startTime;
	}
	public void setStartTime(String startTime) {
		this.startTime = startTime;
	}
	public String getEndTime() {
		return endTime;
	}
	public void setEndTime(String endTime) {
		this.endTime = endTime;
	}
	public Integer getIsCoupon() {
		return isCoupon;
	}
	public void setIsCoupon(Integer isCoupon) {
		this.isCoupon = isCoupon;
	}
	public List<Coupon> getCouponList() {
		return couponList;
	}
	public void setCouponList(List<Coupon> couponList) {
		this.couponList = couponList;
	}
	public Integer getIsPoint() {
		return isPoint;
	}
	public void setIsPoint(Integer isPoint) {
		this.isPoint = isPoint;
	}
	public Double getAddPoint() {
		return addPoint;
	}
	public void setAddPoint(Double addPoint) {
		this.addPoint = addPoint;
	}
	public Integer getIsGoods() {
		return isGoods;
	}
	public void setIsGoods(Integer isGoods) {
		this.isGoods = isGoods;
	}
	public List<Goods> getGoodsList() {
		return goodsList;
	}
	public void setGoodsList(List<Goods> goodsList) {
		this.goodsList = goodsList;
	}
	public Integer getIsBalance() {
		return isBalance;
	}
	public void setIsBalance(Integer isBalance) {
		this.isBalance = isBalance;
	}
	public Double getAddBalance() {
		return addBalance;
	}
	public void setAddBalance(Double addBalance) {
		this.addBalance = addBalance;
	}
	public Integer getIsCusLevel() {
		return isCusLevel;
	}
	public void setIsCusLevel(Integer isCusLevel) {
		this.isCusLevel = isCusLevel;
	}
	public Integer getCusLevelId() {
		return cusLevelId;
	}
	public void setCusLevelId(Integer cusLevelId) {
		this.cusLevelId = cusLevelId;
	}
	public String getShopUnique() {
		return shopUnique;
	}
	public void setShopUnique(String shopUnique) {
		this.shopUnique = shopUnique;
	}
	public Integer getIsBeans() {
		return isBeans;
	}
	public void setIsBeans(Integer isBeans) {
		this.isBeans = isBeans;
	}
	public Integer getBeansCount() {
		return beansCount;
	}
	public void setBeansCount(Integer beansCount) {
		this.beansCount = beansCount;
	}
}
