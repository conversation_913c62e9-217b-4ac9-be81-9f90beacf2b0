package org.haier.shop.service;

import java.util.List;
import java.util.Map;

import javax.servlet.http.HttpServletRequest;

import org.haier.shop.entity.ny.CustomerSummaryByGroup;
import org.haier.shop.entity.ny.NingyuLottery;
import org.haier.shop.params.customer.RechargeForNYCusDetailParams;
import org.haier.shop.util.PurResult;
import org.haier.shop.util.ShopsResult;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

public interface CustomerCheckOutService {


	/**
	 * 宁宇2025年积分兑换活动
	 * @param list
	 * @return
	 */
	public PurResult rechargeForNYCus(List<RechargeForNYCusDetailParams> list);
	

	/**
	 * 1、如果退款状态为驳回，将余额增加回账户；
	 * 2、如果退款状态为成功
	 * 2.1、现金退款，直接修改订单状态即可
	 * 2.2、使用小程序退款；
	 * 2.2.1、查询是否有小程序支付的列表，如果没有，返回提示要求使用现金退款；
	 * 2.2.2、查询是否有小程序支付的列表，如果有，比较充值的金额之和是否比当前的金额要大，如果大，提示用现金退款；
	 * 2.2.3、如果是小程序多笔退款，没退一笔，记录一次日志，防止部分退款成功；
	 * @param rechargeId 充值列表ID
	 * @param rechargeMethod 退款方式；
	 * @param rechargeStatus 退款状态；
	 * @param remarks 退款备注信息，驳回时记录
	 * @param shopUnique 店铺编号
	 * @return
	 */
	public PurResult submitRefundMsg(String rechargeId,Integer rechargeMethod,Integer rechargeStatus,String remarks,String shopUnique);
	/**
	 * 
	 * @param shopUnique 店铺编号
	 * @param cusUnique 会员编号
	 * @param startTime 开始查询时间
	 * @param endTime 结束查询时间
	 * @param page 页码
	 * @param limit 限制数量
	 * @return
	 */
	public PurResult queryCusRefundList(Map<String,Object> map);
	/**
	 * 修改会员是否可提现
	 * @param shopUnique 店铺编号
	 * @param cusUnique 会员编号
	 * @param sameType 1：不可提现；2：可提现；
	 * @return
	 */
	public PurResult modifyCusMsg(String shopUnique,String cusUnique,Integer sameType);
	/**
	 * 会员充值
	 * @param shopUnique 店铺编号
	 * @param cusUnique 会员编号
	 * @param rechargeType 充值类型：1、普通充值；2、换卡充值
	 * @param rechargeMoney 充值金额
	 * @return
	 */
	@Transactional
	public PurResult rechargeYJ(String shopUnique,String cusUnique,Integer rechargeType,Double rechargeMoney);
	/**
	 * 远见餐厅查询店铺内消费信息
	 * @param map
	 * @return
	 */
	public PurResult customerSummaryCYByGroup(Map<String,Object> map);
	
	/**
	 * 获取满足条件的会员信息
	 * @param shopUnique 店铺编号
	 * @param type 类型：1、预览；2、实际操作
	 * @param min_points 满足条件的最小积分
	 * @param min_money 满足条件的最小本金金额
	 * @param cus_msg 会员手机号或会员卡号或会员名称
	 */
	public List<Map<String,Object>> downLoadRedeenPointsDetail(String shopUnique,
			Integer type,Double min_points,Double min_money,String cus_msg,String filePath);
	/**
	 * 更新充值配置信息
	 * @param lottery
	 * @return
	 */
	public ShopsResult modifyLotterySet(NingyuLottery lottery);
	public Map<String,Object> getLotterySet();
	public ShopsResult queryNYLotteryMsg();
	public List<CustomerSummaryByGroup> downLoadCustomerSummaryList(Map<String,Object> map,Integer groupType);
	public PurResult customerSummaryByGroup(Map<String,Object> map,Integer groupType);
	/**
	 * 查询会员的充值记录
	 * @param map
	 * @return
	 */
	public PurResult queryCusRechargeList(Map<String,Object> map);
	
	/**
	 * 
	 * @param shopUnique 店铺编号
	 * @param cusMessage 会员信息，手机好，名称或会员编号
	 * @param startTime 开始查询时间
	 * @param endTime 截至查询时间
	 * @param cusStatus 会员状态：-1、全部；0、审核不通过；1、审核通过；2、待审核
	 * @param page 页码
	 * @param limit 每页查询数量
	 * @return
	 */
	public PurResult queryWJCusCheckOut(String shopUnique,String cusMessage,String startTime,String endTime,
			Integer cusStatus,Integer page,Integer limit);
	/**
	 * 查询会员的月消费额
	 * @param map
	 * @return
	 */
	public PurResult queryCusSaleMsgByMonth(Map<String,Object> map);
	
	public List<Map<String,Object>> queryCusSaleMsgByMonthExcel(Map<String,Object> map);
	
	public PurResult queryCusConsumptionList(Map<String,Object> map);
	/**
	 * 会员信息分页查询
	 * @param map
	 * @return
	 */
	public PurResult queryCusCheckOut(Map<String,Object> map,Integer shopType);
	
	public PurResult queryCusOnline(Map<String,Object> map);
	
	public PurResult queryCusOnlinePt(Map<String,Object> map);
	
	
	public PurResult getMemberLevel(Map<String,Object> map);
	
	/**
	 * 会员详情查询
	 * @param map
	 * @return
	 */
	public ShopsResult cusDetailMessageQuery(Map<String,Object> map);
	
	/**
	 * 会员详情更新
	 * @param map
	 * @return
	 */
	public ShopsResult saveCusMessage(Map<String,Object> map,HttpServletRequest request,String shopUnique,String cusType,String oldCusType);
	
	/**
	 * 会员充值记录查询
	 * @param map
	 * @return
	 */
	public PurResult queryRechargeRecord(Map<String,Object> map);
	
	public PurResult queryBuyRecord(Map<String,Object> map);
	
	public PurResult queryBackRecord(Map<String,Object> map);
	
	/**
	 * 会员消费记录查询
	 * @param map
	 * @return
	 */
	public PurResult queryComsumptionRecord(Map<String,Object> map);
	
	public PurResult queryComsumptionRecord2(Map<String,Object> map);

	/**
	 * 添加店铺会员信息
	 * @param map
	 * @return
	 */
	public ShopsResult addNewCus(Map<String,Object> map,HttpServletRequest request,String shopUnique);
	/**
	 * 会员充值
	 * @param map
	 * @return
	 */
	public ShopsResult cusRecharge(Map<String,Object> map);
	
	/**
	 * 取现
	 * @param map
	 * @return
	 */
	public ShopsResult sureTakeNow(Map<String,Object> map,Double takeNowMoney);
	/**
	 * 管理员旗下所有店铺员工信息查询
	 * @param map
	 * @return
	 */
	public ShopsResult queryManagerStaffs(Map<String,Object> map);
	
	/**
	 * 会员等级信息查询
	 * @param map
	 * @return
	 */
	public ShopsResult queryCusLevel(Map<String,Object> map);
	
	public List<Map<String,Object>> queryCusLevelList(Map<String,Object> map);
	//获取平台会员信息
	public Map<String ,Object> platformCustomerDetail(String cus_unique);

	//平台会员充值记录查询
	public PurResult queryRechargeRecordPlatform(Map<String, Object> map);

	public ShopsResult queryComsumptionRecordPlatformPages(Map<String, Object> map);

	public ShopsResult queryComsumptionRecordPlatform(Map<String, Object> map);
	
	/**
	 * 删除会员信息
	 * @param map
	 * @return
	 */
	public ShopsResult deleteCustomer(Map<String,Object> map);
	
	public ShopsResult updateMemberLevel(Map<String,Object> map);
	/**
	 * 查询会员信息，下载
	 * @param map
	 * @return
	 */
	public List<Map<String,Object>> downCusExcel(String shopUnique,Integer sameType);
	
	public Map<String,Object> getMemberLevelById(Map<String,Object> map);
	
	public Map<String,Object> queryCusLifeCycle(int id);
	
	public ShopsResult updateCusLifeCycle(Map<String,Object> map);

	public PurResult queryCusRenewList(Map<String, Object> params);
	
	public ShopsResult auditWJCusCheckOut(Map<String, Object> params);
	
	public ShopsResult updateWJCusCheckOut(Map<String, Object> params);
	public PurResult queryCusPointHistory(Map<String, Object> map);
	public ShopsResult updatePointClearConfig(Map<String, Object> map);
	public Map<String, Object> queryPointClear(String shop_unique);
	public void clearCusPoint();
	public List<Map<String, Object>> downloadNYRechargeLogExcel(Map<String, Object> map);
	public List<Map<String, Object>> downloadHistoryCusTotalExcel(Map<String, Object> map);

}
