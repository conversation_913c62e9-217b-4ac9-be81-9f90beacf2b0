package org.haier.shop.service;

import java.util.List;
import java.util.Map;
import java.util.UUID;

import org.haier.shop.dao.SysActionDao;
import org.haier.shop.entity.SysAction;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Service
public class SysActionServiceImpl implements SysActionService{
	
	@Autowired
    private SysActionDao dao;

	@Override
	public List<SysAction> quertList(Map<String, Object> params) {
		return dao.quertList(params);
	}

	@Override
	public int quertListCount(Map<String, Object> params) {
		return dao.quertListCount(params);
	}

	@Override
	@Transactional
	public void insert(SysAction action) {
		action.setCode(UUID.randomUUID().toString().replaceAll("-", ""));
		dao.insert(action);
	}

	@Override
	@Transactional
	public void update(SysAction action) {
		dao.update(action);
	}

	@Override
	public SysAction getAction(SysAction action) {
		return dao.getAction(action);
	}

	@Override
	@Transactional
	public void delete(String code) {
		dao.delete(code);
	}

}
