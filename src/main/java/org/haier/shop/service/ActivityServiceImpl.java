package org.haier.shop.service;

import java.io.File;
import java.io.InputStream;
import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.UUID;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;

import org.apache.shiro.SecurityUtils;
import org.apache.shiro.session.Session;
import org.apache.shiro.subject.Subject;
import org.haier.meituan.util.MUtil;
import org.haier.shop.dao.ActivityDao;
import org.haier.shop.dao.ShopDao;
import org.haier.shop.dao2.SupplierInfoDao;
import org.haier.shop.entity.Staff;
import org.haier.shop.entity.beans.PageData;
import org.haier.shop.util.FTPConfig;
import org.haier.shop.util.PicSaveUtil;
import org.haier.shop.util.PurResult;
import org.haier.shop.util.SFTPUtil;
import org.haier.shop.util.ShopsResult;
import org.haier.shop.util.SmsMessageUtil;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;


import net.sf.json.JSONArray;
import net.sf.json.JSONObject;

@Service
public class ActivityServiceImpl implements ActivityService{
	
	@Resource
	private ActivityDao activityDao;
	
	@Resource
	private SupplierInfoDao supplierInfoDao;
	
	@Resource
	private ShopDao shopDao;
	
	/**
	 * 查询与店铺相同管理员的店铺
	 * @param shopUnique
	 * @return
	 */
	public List<Map<String,Object>> selectShopsList(String shopUnique){
		Map<String,Object> map = new HashMap<String,Object>();
		map.put("shopUnique", shopUnique);
		List<Map<String,Object>> shopList = activityDao.selectShopsList(map);
		return shopList;
	}
	
	public PurResult deleteRechargeOffline(Long recharge_config_id) {
		PurResult pr = new PurResult(1,"停用成功!");
		activityDao.deleteRechargeOffline(recharge_config_id);
		return pr;
	}
	
	/**
	 * 根据ID查询充值详情
	 * @param recharge_config_id
	 * @return
	 */
	public PurResult queryOilRechargeConfigDetail(Long recharge_config_id) {
		PurResult pr = new  PurResult(1, "查询成功!");
		pr.setData(activityDao.queryOilRechargeConfigDetail(recharge_config_id));
		return pr;
	}
	/**
	 * 查询店铺的充值配置列表
	 * @param map
	 * @return
	 */
	public PurResult queryOilRechargeConfigList(Map<String,Object> map) {
		PurResult pr = new PurResult();
		
		List<Map<String,Object>> list = activityDao.queryOilRechargeConfigList(map);
		if(null == list || list.isEmpty()) {
			pr.setStatus(1);
			pr.setMsg("没有满足条件的信息");
			pr.setCord(0);
		}else {
			pr.setStatus(1);
			pr.setData(list);
			pr.setCount(activityDao.queryOilRechargeConfigListCount(map));
		}
		return pr;
	}

	public PurResult queryPromotionList(Map<String, Object> map) {
		PurResult pr = new PurResult();
		try {
			List<Map<String,Object>> data = activityDao.queryPromotionList(map);
			Integer count = activityDao.queryPromotionListCount(map);
			
			pr.setStatus(1);
			pr.setMsg("查询成功！");
			pr.setData(data);
			pr.setCount(count);
		} catch (Exception e) {
			e.printStackTrace();
			pr.setStatus(0);
			pr.setMsg("异常");
		}
		return pr;
	}

	public PurResult queryGoodsByPage(String shop_unique, String goodsMessage, String goods_kind_parunique,
			String goods_kind_unique,Integer pageNum, Integer pageSize) {
		PurResult pr=new PurResult();
		Map<String,Object> map=new HashMap<String, Object>();
		map.put("shop_unique", shop_unique);
		if(goodsMessage!=null&&!"".equals(goodsMessage)){
			map.put("goodsMessage", "%"+goodsMessage+"%");
		}
		if(!"-1".equals(goods_kind_parunique)&&!"null".equals(goods_kind_parunique)){
			map.put("goods_kind_parunique", goods_kind_parunique);
		}
		if(!"-1".equals(goods_kind_unique)){
			map.put("goods_kind_unique", goods_kind_unique);
		}
		System.out.println("信息查询！！1"+map);
		//获取商家信息
		Map<String,Object>  shopInfo = shopDao.queryShopMessage(map);
		Integer shop_class = Integer.parseInt(MUtil.strObject(shopInfo.get("shop_class")));//店铺分类：0普通商家；1：连锁；2加盟
		String company_code = MUtil.strObject(shopInfo.get("company_code"));//连锁加盟总店code
		String is_other_purchase = MUtil.strObject(shopInfo.get("is_other_purchase"));//是否允许像其他供货商采购：0不允许 1允许
		Map<String ,Object> companyInfo = new HashMap<String, Object>();
		if(shop_class != 0 && is_other_purchase.equals("0")){//连锁加盟并且不允许向其他供货商采购
			//获取供货商信息
			companyInfo = supplierInfoDao.getCompanyInfo(company_code);
		}
		
		//查询共多少条
		Map<String,Object> countMap=activityDao.queryGoodsCount(map);
		Integer count = Integer.parseInt(MUtil.strObject(countMap.get("count")));
		
		map.put("pageSize", pageSize);
		map.put("startNum", (pageNum-1)*pageSize);
		List<Map<String,Object>> list = activityDao.queryGoodsByPage(map);
		
		for(int i=0;i<list.size();i++){
			Map<String ,Object> supplierInfo = new HashMap<String, Object>();//供货商信息
			Integer is_button = 0;//是否显示选择供货商按钮：0显示  1不显示
			if(shop_class != 0 && is_other_purchase.equals("0")){//连锁加盟并且不允许向其他供货商采购
				supplierInfo = companyInfo;
				is_button = 1;
			}else{
				//获取该商品云商供货商信息
				Map<String,Object> params=new HashMap<String, Object>();
				params.put("shop_unique", shop_unique);
				params.put("goods_barcode", list.get(i).get("goods_barcode"));
				List<Map<String ,Object>> shopList = supplierInfoDao.getSupplierList(params);
				if(shopList.size() > 0){
					//获取该商品上次采购的供货商信息
					supplierInfo = supplierInfoDao.getSupplierInfo(params);
					if(supplierInfo == null){
						supplierInfo = shopList.get(0);
					}
					if(shopList.size() == 1){
						is_button = 1;
					}
				}else{
					is_button = 1;
				}
			}
			if(supplierInfo != null){
				list.get(i).putAll(supplierInfo);
			}
			list.get(i).put("is_button",is_button);
		}
		
		if(null==list||list.isEmpty()){
			map.put("company_code", company_code);
			if(shop_class != 0 && is_other_purchase.equals("0")){//连锁加盟并且不允许向其他供货商采购
				//分页获取总店所有商品列表
				list = supplierInfoDao.getCompanyGoodsList(map);
				count = supplierInfoDao.getCompanyGoodsListCount(map);
			}else{
				//分页获取关联供货商和总店商品列表
				list = supplierInfoDao.getOtherGoodsList(map);
				count = supplierInfoDao.getOtherGoodsListCount(map);
			}
		}
		
		pr.setCount(count);
		pr.setData(list);
		pr.setStatus(1);
		pr.setMsg("查询成功！");
		return pr;
	}
	
	public PurResult queryGoodsByPage1(String shop_unique, String goodsMessage, String goods_kind_parunique,
			String goods_kind_unique,Integer pageNum, Integer pageSize,Integer is_online,String supplierUnique) {
		PurResult pr = new PurResult();
		Map<String,Object> map=new HashMap<String, Object>();
		map.put("shop_unique", shop_unique);
		map.put("is_online", is_online);
		if(goodsMessage!=null&&!"".equals(goodsMessage)){
			map.put("goodsMessage", "%"+goodsMessage+"%");
		}
		if(!"-1".equals(goods_kind_parunique)&&!"null".equals(goods_kind_parunique)){
			map.put("goods_kind_parunique", goods_kind_parunique);
		}
		if(!"-1".equals(goods_kind_unique)){
			map.put("goods_kind_unique", goods_kind_unique);
		}
		map.put("supplierUnique", supplierUnique == null ? null: supplierUnique.equals("") ? null : supplierUnique );
		
		//查询共多少条
		Map<String,Object> countMap=activityDao.queryGoodsCount(map);
		Integer count = Integer.parseInt(MUtil.strObject(countMap.get("count")));
		
		map.put("pageSize", pageSize);
		map.put("startNum", (pageNum-1)*pageSize);
		List<Map<String,Object>> list = activityDao.queryGoodsByPage(map);
		
		pr.setData(list);
		pr.setCount(count);
		pr.setStatus(1);
		pr.setMsg("查询成功！");
		return pr;
	}


	@Transactional
	public PurResult submitSupplierStorageOrder(String shop_unique, String promotion_activity_name, String startDate,
			String endDate, String order_activity, String detailJson) {
			PurResult pr=new PurResult();
			try {
				JSONArray array= JSONArray.fromObject(detailJson);
				
				List<String> timeCoincide = new ArrayList<String>();
				for (int i = 0; i < array.size(); i++) {
					JSONObject temp = (JSONObject) array.get(i); 
					String goods_id= temp.getString("goods_id");
					String goods_name = temp.getString("goods_name");
					//判断当前商品是否有时间重合活动
					Map<String ,Object> params = new HashMap<String, Object>();
					params.put("shop_unique", shop_unique);
					params.put("goods_id", goods_id);
					params.put("startDate", startDate);
					params.put("endDate", endDate);
					Integer count = activityDao.getTimeCoincideGoodsMarkdown(params);
					if(count > 0){
						timeCoincide.add(goods_name);
					}
				}
				if(timeCoincide.size()>0){
					pr.setData(timeCoincide.toString());
					pr.setStatus(0);
					pr.setMsg("有时间重合商品");
					return pr;
				}
				
				Map<String,Object> map=new HashMap<String, Object>();
				map.put("shop_unique", shop_unique);
				map.put("promotion_activity_name", promotion_activity_name);
				map.put("startDate", startDate);
				map.put("endDate", endDate);
				map.put("order_activity", order_activity);
				map.put("type", 1);
				map.put("promotion_activity_id", 0);
				//添加订单
				int k=activityDao.submitSupplierStorageOrder(map);
				Integer promotion_activity_id= (Integer) map.get("promotion_activity_id");
				List<Map<String ,Object>> map3List = new ArrayList<Map<String ,Object>>();
				for (int i = 0; i < array.size(); i++) {
					JSONObject temp = (JSONObject) array.get(i); 
					String goods_id= temp.getString("goods_id");
					String meet_count1= temp.getString("meet_count1");
					String discount_percent1= temp.getString("discount_percent1");
					String meet_count2= temp.getString("meet_count2");
					String discount_percent2= temp.getString("discount_percent2");
					String meet_count3= temp.getString("meet_count3");
					String discount_percent3= temp.getString("discount_percent3");
					String limit_count= temp.getString("limit_count");
					Map<String,Object> map3=new HashMap<String, Object>();
					map3.put("goods_id", goods_id);
					map3.put("shop_unique", shop_unique);
					map3.put("meet_count1", meet_count1);
					map3.put("discount_percent1", discount_percent1);
					map3.put("meet_count2", meet_count2);
					map3.put("discount_percent2", discount_percent2);
					map3.put("meet_count3", meet_count3);
					map3.put("discount_percent3", discount_percent3);
					map3.put("limit_count", limit_count);
					map3.put("promotion_activity_id", promotion_activity_id);
					map3List.add(map3);
				}
				activityDao.addPromotionGoodsMarkdown(map3List);
			} catch (Exception e) {
				e.printStackTrace();
				pr.setStatus(2);
				pr.setMsg("异常");
				return pr;
			}
			pr.setStatus(1);
			pr.setMsg("提交成功");
			return pr;
	}

	@Transactional
	public PurResult submitGoodsGift(String shop_unique, String promotion_activity_name, String startDate,
			String endDate, String order_activity, String detailJson) {
			PurResult pr=new PurResult();
			JSONArray array= JSONArray.fromObject(detailJson);
			
			List<String> timeCoincide = new ArrayList<String>();
			for (int i = 0; i < array.size(); i++) {
				JSONObject temp = (JSONObject) array.get(i); 
				String goods_id= temp.getString("goods_id");
				String goods_name= temp.getString("goods_name");
				//判断当前商品是否有时间重合活动
				Map<String ,Object> params = new HashMap<String, Object>();
				params.put("shop_unique", shop_unique);
				params.put("goods_id", goods_id);
				params.put("startDate", startDate);
				params.put("endDate", endDate);
				Integer count = activityDao.getTimeCoincideGoodsGift(params);
				if(count > 0){
					timeCoincide.add(goods_name);
				}
			}
			
			if(timeCoincide.size()>0){
				pr.setData(timeCoincide.toString());
				pr.setStatus(0);
				pr.setMsg("有时间重合商品");
				return pr;
			}
			
			Map<String,Object> map=new HashMap<String, Object>();
			map.put("shop_unique", shop_unique);
			map.put("promotion_activity_name", promotion_activity_name);
			map.put("startDate", startDate);
			map.put("endDate", endDate);
			map.put("order_activity", order_activity);
			map.put("type", 2);
			map.put("promotion_activity_id", 0);
			//添加订单
			int k=activityDao.submitSupplierStorageOrder(map);
			Integer promotion_activity_id= (Integer) map.get("promotion_activity_id");
			List<Map<String ,Object>> map3List = new ArrayList<Map<String ,Object>>();
			for (int i = 0; i < array.size(); i++) {
				JSONObject temp = (JSONObject) array.get(i); 
				String goods_id= temp.getString("goods_id");
				String meet_count1= temp.getString("meet_count1");
				String gift_count1= temp.getString("gift_count1");
				String meet_count2= temp.getString("meet_count2");
				String gift_count2= temp.getString("gift_count2");
				String meet_count3= temp.getString("meet_count3");
				String gift_count3= temp.getString("gift_count3");
				String goods_id_gift= temp.getString("goods_id_gift");
				Map<String,Object> map3=new HashMap<String, Object>();
				map3.put("goods_id", goods_id);
				map3.put("shop_unique", shop_unique);
				map3.put("meet_count1", meet_count1);
				map3.put("gift_count1", gift_count1);
				map3.put("meet_count2", meet_count2);
				map3.put("gift_count2", gift_count2);
				map3.put("meet_count3", meet_count3);
				map3.put("gift_count3", gift_count3);
				map3.put("goods_id_gift", goods_id_gift);
				map3.put("promotion_activity_id", promotion_activity_id);
				map3.put("goods_id_gift1", temp.getString("goods_id_gift"));
				map3.put("goods_id_gift2", temp.getString("goods_id_gift"));
				map3.put("goods_id_gift3", temp.getString("goods_id_gift"));
				map3List.add(map3);
			}
			activityDao.addGoodsGift(map3List);
			pr.setStatus(1);
			pr.setMsg("提交成功");
			return pr;
	}

	@Transactional
	public PurResult addOrderMarkdown(String shop_unique, String promotion_activity_name, String startDate,
			String endDate, String meet_price1, String discount_price1, String meet_price2, String discount_price2,
			String meet_price3, String discount_price3,String goods_id1, String goods_id2, String goods_id3, String gift_count1, String gift_count2, String gift_count3) {
		PurResult pr=new PurResult();
		
		Map<String ,Object> params = new HashMap<String, Object>();
		params.put("shop_unique", shop_unique);
		params.put("startDate", startDate);
		params.put("endDate", endDate);
		if(meet_price1 != null && !meet_price1.equals("")){
			params.put("meet_price", meet_price1);
			Integer count = activityDao.getTimeCoincideOrderMarkdown(params);
			if(count > 0) {
				pr.setData(meet_price1);
				pr.setStatus(0);
				pr.setMsg("有时间重合");
				return pr;
			}
		}
		if(meet_price2 != null && !meet_price2.equals("")){
			params.put("meet_price", meet_price2);
			Integer count = activityDao.getTimeCoincideOrderMarkdown(params);
			if(count > 0) {
				pr.setData(meet_price2);
				pr.setStatus(0);
				pr.setMsg("有时间重合");
				return pr;
			}
		}
		if(meet_price3 != null && !meet_price3.equals("")){
			params.put("meet_price", meet_price3);
			Integer count = activityDao.getTimeCoincideOrderMarkdown(params);
			if(count > 0) {
				pr.setData(meet_price3);
				pr.setStatus(0);
				pr.setMsg("有时间重合");
				return pr;
			}
		}
		Map<String,Object> map=new HashMap<String, Object>();
		map.put("shop_unique", shop_unique);
		map.put("promotion_activity_name", promotion_activity_name);
		map.put("startDate", startDate);
		map.put("endDate", endDate);
		map.put("type", 3);
		map.put("promotion_activity_id", 0);
		//添加订单
		int k=activityDao.submitSupplierStorageOrder(map);
		Integer promotion_activity_id= (Integer) map.get("promotion_activity_id");
		Map<String,Object> map3=new HashMap<String, Object>();
		map3.put("shop_unique", shop_unique);
		if(!"".equals(meet_price1)){
			map3.put("meet_price1", meet_price1);
		}
		if(!"".equals(discount_price1)){
			map3.put("discount_price1", discount_price1);
		}
		if(!"".equals(meet_price2)){
			map3.put("meet_price2", meet_price2);
		}
		if(!"".equals(discount_price2)){
			map3.put("discount_price2", discount_price2);
		}
		if(!"".equals(meet_price3)){
			map3.put("meet_price3", meet_price3);
		}
		if(!"".equals(discount_price3)){
			map3.put("discount_price3", discount_price3);
		}
		if(!"".equals(goods_id1)){
			map3.put("goods_id1", goods_id1);
		}
		if(!"".equals(goods_id2)){
			map3.put("goods_id2", goods_id2);
		}
		if(!"".equals(goods_id3)){
			map3.put("goods_id3", goods_id3);
		}
		if(!"".equals(gift_count1)){
			map3.put("gift_count1", gift_count1);
		}
		if(!"".equals(gift_count2)){
			map3.put("gift_count2", gift_count2);
		}
		if(!"".equals(gift_count3)){
			map3.put("gift_count3", gift_count3);
		}
		map3.put("promotion_activity_id", promotion_activity_id);
		activityDao.addOrderMarkdown(map3);
		pr.setStatus(1);
		pr.setMsg("提交成功");
		return pr;
	}

	@Transactional
	public PurResult deleteActivity(Map<String, Object> map) {
		PurResult pr=new PurResult();
		activityDao.deleteActivity(map);
		activityDao.deleteGoodsMarkdown(map);
		activityDao.deleteGoodsGift(map);
		activityDao.deleteOrderMarkdown(map);
		activityDao.deleteSingleGoodsMarkdown(map);
		pr.setStatus(1);
		return pr;
	}

	@Transactional
	public PurResult updateActivityStatus(Map<String, Object> map) {
		PurResult pr = new PurResult();
		activityDao.updateActivityStatus(map);
		pr.setStatus(1);
		return pr;
	}

	public PurResult queryGoodsMarkdownDetail(Map<String, Object> map) {
		PurResult pr=new PurResult();
		List<Map<String, Object>> list=activityDao.queryGoodsMarkdownDetail(map);
		pr.setStatus(1);
		pr.setData(list);
		return pr;
	}


	public PurResult queryGoodsGiftDetail(Map<String, Object> map) {
		PurResult pr=new PurResult();
		List<Map<String, Object>> list=activityDao.queryGoodsGiftDetail(map);
		pr.setStatus(1);
		pr.setData(list);
		return pr;
	}


	public PurResult queryOrderMarkdownDetail(Map<String, Object> map) {
		PurResult pr=new PurResult();
		List<Map<String, Object>> list=activityDao.queryOrderMarkdownDetail(map);
		pr.setStatus(1);
		pr.setData(list);
		return pr;
	}

	@Transactional
	public PurResult submitGoodsKindPoint(String shop_unique, String goods_kind_parunique, String goods_kind_child,
			String goods_kind_points_type, String goods_kind_points_unit,String goods_kind_points_unit_val) {
		PurResult pr=new PurResult();
		Map<String,Object> map=new HashMap<String, Object>();
		map.put("shop_unique", shop_unique);
		if("-1".equals(goods_kind_child)){
			map.put("goods_kind_unique", goods_kind_parunique);
			map.put("goods_kind_parunique", 0);
		}else{
			map.put("goods_kind_unique", goods_kind_child);
			map.put("goods_kind_parunique", goods_kind_parunique);
		}
		
		
		Double goods_kind_points_val=0.0;
		if((goods_kind_points_unit!=null&&!"".equals(goods_kind_points_unit))){
			if("1".equals(goods_kind_points_type)||"3".equals(goods_kind_points_type)){
				//金额比例
				goods_kind_points_val=new BigDecimal(Double.parseDouble(goods_kind_points_unit)/Double.parseDouble(goods_kind_points_unit_val)).setScale(2, BigDecimal.ROUND_HALF_UP).doubleValue();
			}else{
				goods_kind_points_val=new BigDecimal(Double.parseDouble(goods_kind_points_unit_val)/Double.parseDouble(goods_kind_points_unit)).setScale(2, BigDecimal.ROUND_HALF_UP).doubleValue();
			}
		}else{
			goods_kind_points_unit=null;
			goods_kind_points_val=null;
			goods_kind_points_unit_val=null;
			goods_kind_points_type=null;
		}
		map.put("goods_kind_points_type", goods_kind_points_type);
		map.put("goods_kind_points_val", goods_kind_points_val);
		map.put("goods_kind_points_unit", goods_kind_points_unit);
		map.put("goods_kind_points_unit_val", goods_kind_points_unit_val);
		int k=activityDao.submitGoodsKindPoint(map);
		if(k>0){
			pr.setStatus(1);
			pr.setMsg("提交成功");
		}
		return pr;
	}

	@Transactional
	public PurResult submitGoodsPoint(String shop_unique, String detailJson) {
		PurResult pr=new PurResult();
		Map<String,Object> map=new HashMap<String, Object>();
		map.put("shop_unique", shop_unique);
		pr.setStatus(1);
		JSONArray array= JSONArray.fromObject(detailJson);
		for (int i = 0; i < array.size(); i++) {
			JSONObject temp = (JSONObject) array.get(i); 
			String goods_id= temp.getString("goods_id");
			String goods_points_type= temp.getString("goods_points_type");
			String goods_points_unit= temp.getString("goods_points_unit");
			String goods_points_unit_val= temp.getString("goods_points_unit_val");
			Double goods_points_val=0.0;
			if((goods_points_unit!=null&&!"".equals(goods_points_unit))){
				if("1".equals(goods_points_type)|| "3".equals(goods_points_type)){
					//金额比例
					goods_points_val=new BigDecimal(Double.parseDouble(goods_points_unit)/Double.parseDouble(goods_points_unit_val)).setScale(2, BigDecimal.ROUND_HALF_UP).doubleValue();
				}else{
					goods_points_val=new BigDecimal(Double.parseDouble(goods_points_unit_val)/Double.parseDouble(goods_points_unit)).setScale(2, BigDecimal.ROUND_HALF_UP).doubleValue();
				}
			}else{
				goods_points_val=null;
				goods_points_unit=null;
				goods_points_unit_val=null;
				goods_points_type=null;
			}
			
			Map<String,Object> map3=new HashMap<String, Object>();
			map3.put("goods_id", goods_id);
			map3.put("shop_unique", shop_unique);
			map3.put("goods_points_type", goods_points_type);
			map3.put("goods_points_val", goods_points_val);
			map3.put("goods_points_unit", goods_points_unit);
			map3.put("goods_points_unit_val", goods_points_unit_val);
			int k=activityDao.submitGoodsPoint(map3);
			if(k<=0){
				pr.setStatus(0);
			}
		}
		return pr;
	}

	@Transactional
	public PurResult submitGoodsKindCommission(String shop_unique, String goods_kind_parunique, String goods_kind_child,
			String goods_kind_commission_type, String goods_kind_commission_unit, String goods_kind_commission_unit_val) {
		PurResult pr=new PurResult();
		Map<String,Object> map=new HashMap<String, Object>();
		map.put("shop_unique", shop_unique);
		if("-1".equals(goods_kind_child)){
			map.put("goods_kind_unique", goods_kind_parunique);
			map.put("goods_kind_parunique", 0);
		}else{
			map.put("goods_kind_unique", goods_kind_child);
			map.put("goods_kind_parunique", goods_kind_parunique);
		}
		
		Double goods_kind_commission_val=0.0;
		if((goods_kind_commission_unit!=null&&!"".equals(goods_kind_commission_unit))){
			if("1".equals(goods_kind_commission_type)){
				//金额比例
				goods_kind_commission_val=new BigDecimal(Double.parseDouble(goods_kind_commission_unit)/Double.parseDouble(goods_kind_commission_unit_val)).setScale(2, BigDecimal.ROUND_HALF_UP).doubleValue();
			}else{
				goods_kind_commission_val=new BigDecimal(Double.parseDouble(goods_kind_commission_unit_val)/Double.parseDouble(goods_kind_commission_unit)).setScale(2, BigDecimal.ROUND_HALF_UP).doubleValue();
			}
		}else{
			goods_kind_commission_type=null;
			goods_kind_commission_val=null;
			goods_kind_commission_unit=null;
			goods_kind_commission_unit_val=null;
		}
		map.put("goods_kind_commission_type", goods_kind_commission_type);
		map.put("goods_kind_commission_val", goods_kind_commission_val);
		map.put("goods_kind_commission_unit", goods_kind_commission_unit);
		map.put("goods_kind_commission_unit_val", goods_kind_commission_unit_val);
		int k=activityDao.submitGoodsKindCommission(map);
		if(k>0){
			pr.setStatus(1);
			pr.setMsg("提交成功");
		}
		return pr;
	}


	public PurResult submitGoodsCommission(String shop_unique, String detailJson) {
		PurResult pr=new PurResult();
		Map<String,Object> map=new HashMap<String, Object>();
		map.put("shop_unique", shop_unique);
		pr.setStatus(1);
		JSONArray array= JSONArray.fromObject(detailJson);
		for (int i = 0; i < array.size(); i++) {
			JSONObject temp = (JSONObject) array.get(i); 
			String goods_id= temp.getString("goods_id");
			String goods_commission_type= temp.getString("goods_commission_type");
			String goods_commission_unit= temp.getString("goods_commission_unit");
			String goods_commission_unit_val= temp.getString("goods_commission_unit_val");
			
			Double goods_commission_val=0.0;
			if((goods_commission_unit!=null&&!"".equals(goods_commission_unit))){
				if("1".equals(goods_commission_type)){
					//金额比例
					goods_commission_val=new BigDecimal(Double.parseDouble(goods_commission_unit)/Double.parseDouble(goods_commission_unit_val)).setScale(2, BigDecimal.ROUND_HALF_UP).doubleValue();
				}else{
					goods_commission_val=new BigDecimal(Double.parseDouble(goods_commission_unit_val)/Double.parseDouble(goods_commission_unit)).setScale(2, BigDecimal.ROUND_HALF_UP).doubleValue();
				}
			}else{
				goods_commission_val=null;
				goods_commission_unit=null;
				goods_commission_unit_val=null;
				goods_commission_type=null;
			}
			
			Map<String,Object> map3=new HashMap<String, Object>();
			map3.put("goods_id", goods_id);
			map3.put("shop_unique", shop_unique);
			map3.put("goods_commission_type", goods_commission_type);
			map3.put("goods_commission_val", goods_commission_val);
			map3.put("goods_commission_unit", goods_commission_unit);
			map3.put("goods_commission_unit_val", goods_commission_unit_val);
			int k=activityDao.submitGoodsCommission(map3);
			if(k<=0){
				pr.setStatus(0);
			}
		}
		return pr;
	}


	public PurResult queryGoodsKindPointsAndCommissionByPage(String shop_unique, String goodsMessage,
			String goods_kind_parunique, String goods_kind_unique, Integer pageNum, Integer pageSize) {
		PurResult pr=new PurResult();
		Map<String,Object> map=new HashMap<String, Object>();
		map.put("shop_unique", shop_unique);
		if(goodsMessage!=null&&!"".equals(goodsMessage)){
			map.put("goodsMessage", "%"+goodsMessage+"%");
		}
		if(!"-1".equals(goods_kind_parunique)&&!"null".equals(goods_kind_parunique)){
			map.put("goods_kind_parunique", goods_kind_parunique);
		}
		if(!"-1".equals(goods_kind_unique)){
			map.put("goods_kind_unique", goods_kind_unique);
		}
		System.out.println("信息查询！！1"+map);
		//查询共多少条
		Map<String,Object> countMap=activityDao.queryGoodsKindPointsAndCommissionCount(map);
		Integer count = Integer.parseInt(MUtil.strObject(countMap.get("count")));
		
		map.put("pageSize", pageSize);
		map.put("startNum", (pageNum-1)*pageSize);
//		System.out.println("商品查询！！！"+map);
		List<Map<String,Object>> list=activityDao.queryGoodsKindPointsAndCommissionByPage(map);
		pr.setData(list);
		pr.setCount(count);
		pr.setStatus(1);
		pr.setMsg("查询成功！");
		return pr;
	}

	@Transactional
	public PurResult submitSingleGoodsPromotion(String shop_unique, String promotion_activity_name, String startDate,
			String endDate, String detailJson,String activity_range) {
		PurResult pr=new PurResult();
		JSONArray array= JSONArray.fromObject(detailJson);
	
		List<String> timeCoincide = new ArrayList<String>();
		for (int i = 0; i < array.size(); i++) {
			JSONObject temp = (JSONObject) array.get(i); 
			String goods_id= temp.getString("goods_id");
			String goods_name = temp.getString("goods_name");
			//判断当前商品是否有时间重合活动
			Map<String ,Object> params = new HashMap<String, Object>();
			params.put("shop_unique", shop_unique);
			params.put("goods_id", goods_id);
			params.put("startDate", startDate);
			params.put("endDate", endDate);
			Integer count = activityDao.getTimeGoodsPromotionOnline(params);
			if(count > 0){
				timeCoincide.add(goods_name);
			}
		}
		if(timeCoincide.size()>0){
			pr.setData(timeCoincide.toString());
			pr.setStatus(0);
			pr.setMsg("有时间重合商品");
			return pr;
		}
		
		Map<String,Object> map=new HashMap<String, Object>();
		map.put("shop_unique", shop_unique);
		map.put("promotion_activity_name", promotion_activity_name);
		map.put("startDate", startDate);
		map.put("endDate", endDate);
		map.put("type", 4);
		map.put("promotion_activity_id", 0);
		map.put("activity_range", activity_range);
		//添加订单
		int k=activityDao.submitSupplierStorageOrder(map);
		Integer promotion_activity_id= (Integer) map.get("promotion_activity_id");
		List<Map<String ,Object>> map3List = new ArrayList<Map<String ,Object>>();
		for (int i = 0; i < array.size(); i++) {
			JSONObject temp = (JSONObject) array.get(i); 
//			String goods_id= temp.getString("goods_id");
//			String promotion_price= temp.getString("promotion_price");
//			Map<String,Object> map3=new HashMap<String, Object>();
//			map3.put("goods_id", goods_id);
//			map3.put("promotion_price", promotion_price);
//			map3.put("shop_unique", shop_unique);
//			map3.put("promotion_activity_id", promotion_activity_id);
//			activityDao.submitSingleGoodsPromotion(map3);
			String goods_id= temp.getString("goods_id");
			String number1= temp.getString("number1");
			String discount_percent1= temp.getString("discount_percent1");
			String number2= temp.getString("number2");
			String discount_percent2= temp.getString("discount_percent2");
			String number3= temp.getString("number3");
			String discount_percent3= temp.getString("discount_percent3");
			Map<String,Object> map3=new HashMap<String, Object>();
			map3.put("shop_unique", shop_unique);
			map3.put("goods_id", goods_id);
			map3.put("promotion_activity_id", promotion_activity_id);
			map3.put("number1", number1);
			map3.put("discount_percent1", discount_percent1);
			map3.put("number2", number2);
			map3.put("discount_percent2", discount_percent2);
			map3.put("number3", number3);
			map3.put("discount_percent3", discount_percent3);
			map3List.add(map3);
		}
		activityDao.addPromotionGoodsSingle(map3List);
		pr.setStatus(1);
		pr.setMsg("提交成功");
		return pr;
	}
	
	@Transactional
	public PurResult submitFlashSale(String shop_unique, String promotion_activity_name, String startDate,
			String endDate, String detailJson,String activity_range) {
		PurResult pr=new PurResult();
		JSONArray array= JSONArray.fromObject(detailJson);
	
		List<String> timeCoincide = new ArrayList<String>();
		for (int i = 0; i < array.size(); i++) {
			JSONObject temp = (JSONObject) array.get(i); 
			String goods_id= temp.getString("goods_id");
			String goods_name = temp.getString("goods_name");
			//判断当前商品是否有时间重合活动
			Map<String ,Object> params = new HashMap<String, Object>();
			params.put("shop_unique", shop_unique);
			params.put("goods_id", goods_id);
			params.put("startDate", startDate);
			params.put("endDate", endDate);
			Integer count = activityDao.getTimeGoodsPromotionOnline(params);
			if(count > 0){
				timeCoincide.add(goods_name);
			}
		}
		if(timeCoincide.size()>0){
			pr.setData(timeCoincide.toString());
			pr.setStatus(0);
			pr.setMsg("有时间重合商品");
			return pr;
		}
		
		Map<String,Object> map=new HashMap<String, Object>();
		map.put("shop_unique", shop_unique);
		map.put("promotion_activity_name", promotion_activity_name);
		map.put("startDate", startDate);
		map.put("endDate", endDate);
		map.put("type", 4);
		map.put("promotion_activity_id", 0);
		map.put("activity_range", activity_range);
		//添加订单
		int k=activityDao.submitSupplierStorageOrder(map);
		Integer promotion_activity_id= (Integer) map.get("promotion_activity_id");
		List<Map<String ,Object>> map3List = new ArrayList<Map<String ,Object>>();
		for (int i = 0; i < array.size(); i++) {
			JSONObject temp = (JSONObject) array.get(i); 
			String goods_id= temp.getString("goods_id");
			String promotion_price= temp.getString("promotion_price");
			Map<String,Object> map3=new HashMap<String, Object>();
			map3.put("goods_id", goods_id);
			map3.put("promotion_price", promotion_price);
			map3.put("shop_unique", shop_unique);
			map3.put("promotion_activity_id", promotion_activity_id);
			map3.put("per_count", temp.get("per_count"));
			map3.put("total_count", temp.get("total_count"));
			activityDao.submitSingleGoodsPromotion(map3);
		}
		pr.setStatus(1);
		pr.setMsg("提交成功");
		return pr;
	}



	public PurResult querySingleGoodsPromotionDetail(Map<String, Object> map) {
		PurResult pr=new PurResult();
		List<Map<String, Object>> list=activityDao.querySingleGoodsPromotionDetail(map);
		pr.setStatus(1);
		pr.setData(list);
		return pr;
	}

	public PurResult queryFlashSaleDetail(Map<String, Object> map) {
		PurResult pr=new PurResult();
		List<Map<String, Object>> list=activityDao.queryFlashSaleDetail(map);
		pr.setStatus(1);
		pr.setData(list);
		return pr;
	}

	public PurResult queryShopCouponList(String shop_unique ,String user_status, String give_status, Integer pageNum, Integer pageSize) {
		PurResult result=new PurResult();
		try {
			Map<String,Object> map=new HashMap<String, Object>();
			map.put("shop_unique", shop_unique);
			map.put("user_status", Integer.parseInt(user_status));
			map.put("give_status", give_status);
			Map<String,Object> countMap=activityDao.queryShopCouponListCount(map);
			Integer count = Integer.parseInt(MUtil.strObject(countMap.get("count")));
			
			map.put("pageSize", pageSize);
			map.put("startNum", (pageNum-1)*pageSize);
			List<Map<String,Object>> orderList=activityDao.queryShopCouponList(map);
			for(int i=0;i<orderList.size();i++){
				List<Map<String,Object>> timeList = activityDao.getShopCouponTimes(orderList.get(i).get("shop_coupon_id").toString());
				orderList.get(i).put("timeList", timeList);
			}
			result.setStatus(1);
			result.setMsg("查询成功！");
			result.setData(orderList);
			result.setCount(count);
		} catch (Exception e) {
			e.printStackTrace();
			result.setStatus(0);
			result.setMsg("异常");
		}
		return result;
	}

	@Transactional
	public PurResult addShopCoupon(String shop_unique, String start_time, String end_time, String meet_amount, String coupon_amount, String type,
								   String is_time, String is_daily, String daily_num, String times, String is_auto_grant, String is_grant_num, String grant_num,
								   String exclusive_type, String days, Integer is_online, String coupon_name, String designated_shop_unique, Integer coupon_type, String rule_description) {
		PurResult pr = new PurResult();
		Subject subject = SecurityUtils.getSubject();
		Session session = subject.getSession();
		Staff staff = (Staff) session.getAttribute("staff");
		
		Integer shop_type = staff.getShop_type();
		
		try {
			Map<String,Object> params = new HashMap<String, Object>();
			
			params.put("shop_unique", shop_unique);
			params.put("startDate", start_time);
			params.put("endDate", end_time);
			params.put("meet_amount", meet_amount);
			params.put("coupon_amount", coupon_amount);
			params.put("type", type);
			params.put("give_status", 0);
			params.put("is_time", is_time);
			params.put("is_daily", is_daily);
			params.put("daily_num", daily_num);
			params.put("is_auto_grant", is_auto_grant);
			params.put("is_online", is_online);
			params.put("coupon_name", coupon_name);
			params.put("rule_description", rule_description);
			if(is_grant_num != null && is_grant_num.equals("-1")) {//不限制发放次数
				params.put("grant_num", "-1");
			}else {
				params.put("grant_num", grant_num);
				params.put("surplus_grant_num", grant_num);
			}
			params.put("exclusive_type", exclusive_type);
			if(null != coupon_type && coupon_type.equals(2)) {
				designated_shop_unique = shop_unique;
			}
			params.put("coupon_type", coupon_type);
			params.put("designated_shop_unique", designated_shop_unique);
			activityDao.addShopCoupon(params);
			
			if(is_time.equals("2") && times != null && !times.equals("")){//添加优惠券时段信息
				String[] timesStr = times.split(";");
				for(int i=0;i<timesStr.length;i++){
					String[] time = timesStr[i].split(" - ");
					Map<String ,Object> timeParams = new HashMap<String, Object>();
					timeParams.put("shop_coupon_id", params.get("shop_coupon_id"));
					timeParams.put("start_time", time[0]);
					timeParams.put("end_time", time[1]);
					
					activityDao.addShopCouponTime(timeParams);
				}
			}
			//
			if(null != days && !days.equals("")) {
				List<Map<String,Object>> dayList = new ArrayList<Map<String,Object>>();
				String[] daysArr = days.split(",");
				//将所有开始周期到结束周期内的满足条件的日期添加到数组中
				SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd");
				Date d1 = formatter.parse(start_time);
				Date d2 = formatter.parse(end_time);
				
				
				Calendar cs = Calendar.getInstance();
				Calendar ce = Calendar.getInstance();
				
				cs.setTime(d1);
				ce.setTime(d2);
				
				while (cs.compareTo(ce) <= 0) {
					Map<String,Object> map = new HashMap<String,Object>();
					Integer dayWeek = cs.get(Calendar.DAY_OF_WEEK);
					for(String s : daysArr) {
						if(Integer.parseInt(s) == dayWeek) {
							System.out.println(cs);
							System.out.println(cs.get(Calendar.MONTH));
							map.put("effective_day", cs.get(Calendar.YEAR) + "-" + (cs.get(Calendar.MONTH ) + 1) + "-" + cs.get(Calendar.DAY_OF_MONTH));
							map.put("shop_coupon_id", params.get("shop_coupon_id"));
							map.put("day_week", s);
							dayList.add(map);
							break;
						}
					}
					cs.add(Calendar.DATE, 1);
				}
				
				//将满足条件日期添加到对应优惠券信息中
				System.out.println(dayList);
				activityDao.addShopCouponEffective(dayList);
			}
			
			if(is_auto_grant.equals("2")) {//添加会员与优惠券关联
				
				if(shop_type != 11) {
					//获取所有平台会员列表
					List<Map<String ,Object>> cusList = activityDao.getCusList();
					for(int i=0;i<cusList.size();i++) {
						cusList.get(i).put("shop_coupon_id", params.get("shop_coupon_id"));
					}
					activityDao.addShopCouponCusList(cusList);
				}else {
					activityDao.addNewShopCouponCusOffline(params);
				}
			}
		} catch (Exception e) {
			e.printStackTrace();
			pr.setStatus(0);
			pr.setMsg("异常");
		}
		pr.setStatus(1);
		pr.setMsg("成功");
		return pr;
	}

	@Transactional
	public PurResult updateShopCoupon(String shop_coupon_id, String start_time, String end_time, String meet_amount, String coupon_amount, String type,
                                      String is_time, String is_daily, String daily_num, String times, String is_auto_grant, String old_is_auto_grant, String is_grant_num, String grant_num, String exclusive_type, String coupon_name, String days, String rule_description) {
		PurResult pr = new PurResult();
		try {
			Map<String,Object> params = new HashMap<String, Object>();
			if(is_daily.equals("")){
				is_daily = null;
			}
			if(daily_num.equals("")){
				daily_num = null;
			}
			params.put("coupon_name",coupon_name);
			params.put("shop_coupon_id", shop_coupon_id);
			params.put("start_time", start_time);
			params.put("end_time", end_time);
			params.put("meet_amount", meet_amount);
			params.put("coupon_amount", coupon_amount);
			params.put("type", type);
			params.put("give_status", 0);
			params.put("is_time", is_time);
			params.put("is_daily", is_daily);
			params.put("daily_num", daily_num);
			params.put("is_auto_grant", is_auto_grant);
			params.put("grant_num", grant_num);
			if(is_grant_num != null && is_grant_num.equals("-1")) {//不限制发放次数
				params.put("grant_num", "-1");
			}else {
				params.put("grant_num", grant_num);
				params.put("surplus_grant_num", grant_num);
			}
			if(exclusive_type != null && exclusive_type.equals("")) {
				exclusive_type = null;
			}
			params.put("exclusive_type", exclusive_type);
			params.put("rule_description", rule_description);
			activityDao.updateShopCoupon(params);
			
			//删除优惠券时段信息
			activityDao.deleteShopCouponTime(shop_coupon_id);
			if(is_time.equals("2") && times != null && !times.equals("")){//添加优惠券时段信息
				String[] timesStr = times.split(";");
				for(int i=0;i<timesStr.length;i++){
					String[] time = timesStr[i].split(" - ");
					Map<String ,Object> timeParams = new HashMap<String, Object>();
					timeParams.put("shop_coupon_id", params.get("shop_coupon_id"));
					timeParams.put("start_time", time[0]);
					timeParams.put("end_time", time[1]);
					
					activityDao.addShopCouponTime(timeParams);
				}
			}

			activityDao.deleteShopCouponEffective(shop_coupon_id);
			if(null != days && !days.equals("")) {
				List<Map<String,Object>> dayList = new ArrayList<Map<String,Object>>();
				String[] daysArr = days.split(",");
				//将所有开始周期到结束周期内的满足条件的日期添加到数组中
				SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd");
				Date d1 = formatter.parse(start_time);
				Date d2 = formatter.parse(end_time);

				Calendar cs = Calendar.getInstance();
				Calendar ce = Calendar.getInstance();

				cs.setTime(d1);
				ce.setTime(d2);

				while (cs.compareTo(ce) <= 0) {
					Map<String,Object> map = new HashMap<String,Object>();
					Integer dayWeek = cs.get(Calendar.DAY_OF_WEEK);
					for(String s : daysArr) {
						if(Integer.parseInt(s) == dayWeek) {
							System.out.println(cs);
							System.out.println(cs.get(Calendar.MONTH));
							map.put("effective_day", cs.get(Calendar.YEAR) + "-" + (cs.get(Calendar.MONTH ) + 1) + "-" + cs.get(Calendar.DAY_OF_MONTH));
							map.put("shop_coupon_id", params.get("shop_coupon_id"));
							map.put("day_week", s);
							dayList.add(map);
							break;
						}
					}
					cs.add(Calendar.DATE, 1);
				}

				//将满足条件日期添加到对应优惠券信息中
				System.out.println(dayList);
				activityDao.addShopCouponEffective(dayList);
			}

			if(!old_is_auto_grant.equals(is_auto_grant) && is_auto_grant.equals("2")) {//添加会员与优惠券关联
				//获取所有平台会员列表
				List<Map<String ,Object>> cusList = activityDao.getCusList();
				for(int i=0;i<cusList.size();i++) {
					cusList.get(i).put("shop_coupon_id", params.get("shop_coupon_id"));
				}
				activityDao.addShopCouponCusList(cusList);
			}
		} catch (Exception e) {
			e.printStackTrace();
			pr.setStatus(0);
			pr.setMsg("异常");
		}
		pr.setStatus(1);
		pr.setMsg("成功");
		return pr;
	}

	@Transactional
	public PurResult deleteShopCoupon(String shop_coupon_id) {
		PurResult pr=new PurResult();
		Map<String,Object> map=new HashMap<String, Object>();
		map.put("shop_coupon_id", shop_coupon_id);
		activityDao.deleteShopCoupon(map);
		pr.setStatus(1);
		return pr;
	}

	public Map<String ,Object> getShopCoupon(String shop_coupon_id){
		Map<String ,Object> shopCoupon = activityDao.getShopCoupon(shop_coupon_id);
		List<Map<String ,Object>> timeLIst = activityDao.getShopCouponTimes(shop_coupon_id);
		List<Map<String ,Object>> effectiveList = activityDao.getShopCouponEffective(shop_coupon_id);
		shopCoupon.put("timeLIst", timeLIst);
		shopCoupon.put("effectiveList", com.alibaba.fastjson.JSONArray.toJSON(effectiveList));
 		return shopCoupon;
	}

	public PurResult queryRechargeConfigList(Integer pageNum, Integer pageSize) {
		PurResult result=new PurResult();
		try {
			Map<String,Object> map=new HashMap<String, Object>();
			Map<String,Object> countMap=activityDao.queryRechargeConfigListCount(map);
			Integer count = Integer.parseInt(MUtil.strObject(countMap.get("count")));
			map.put("pageSize", pageSize);
			map.put("startNum", (pageNum-1)*pageSize);
			List<Map<String,Object>> orderList=activityDao.queryRechargeConfigList(map);
			result.setStatus(1);
			result.setMsg("查询成功！");
			result.setData(orderList);
			result.setCount(count);
		} catch (Exception e) {
			e.printStackTrace();
			result.setStatus(0);
			result.setMsg("异常");
		}
		return result;
	}


	@Transactional
	public PurResult addRechargeConfig(Map<String ,Object> params) {
		PurResult result=new PurResult();
		try {
			
			String give_type = MUtil.strObject(params.get("give_type"));
			String shop_coupon_id = null;
			String give_bean_num = null;
			
			if(give_type != null && give_type.equals("1")){//优惠券
				Map<String,Object> map=new HashMap<String, Object>();
				map.put("shop_unique", params.get("shop_unique"));
				map.put("startDate", params.get("startDate"));
				map.put("endDate", params.get("endDate"));
				map.put("meet_amount", params.get("meet_amount"));
				map.put("coupon_amount", params.get("coupon_amount"));
				map.put("type", params.get("type"));
				map.put("give_status", 1);
				activityDao.addShopCoupon(map);
				shop_coupon_id = MUtil.strObject(map.get("shop_coupon_id"));
			}else if(give_type != null && give_type.equals("2")){//百货豆
				give_bean_num = MUtil.strObject(params.get("give_bean_num"));
			}else if(give_type != null && give_type.equals("3")){//优惠券、百货豆同时赠送
				Map<String,Object> map=new HashMap<String, Object>();
				map.put("shop_unique", params.get("shop_unique"));
				map.put("startDate", params.get("startDate"));
				map.put("endDate", params.get("endDate"));
				map.put("meet_amount", params.get("meet_amount"));
				map.put("coupon_amount", params.get("coupon_amount"));
				map.put("type", params.get("type"));
				map.put("give_status", 1);
				activityDao.addShopCoupon(map);
				shop_coupon_id = MUtil.strObject(map.get("shop_coupon_id"));
				
				give_bean_num = MUtil.strObject(params.get("give_bean_num"));
			}else{
				give_type = null;
			}
			
			Map<String,Object> rechargeConfigParams = new HashMap<String, Object>();
			rechargeConfigParams.put("money", params.get("money"));
			rechargeConfigParams.put("give_type", give_type);
			rechargeConfigParams.put("shop_coupon_id", shop_coupon_id);
			rechargeConfigParams.put("give_bean_num", give_bean_num);
			activityDao.addRechargeConfig(rechargeConfigParams);
			
			result.setStatus(1);
			result.setMsg("成功");
		} catch (Exception e) {
			e.printStackTrace();
			result.setStatus(0);
			result.setMsg("异常");
		}
		return result;
	}

	public Map<String, Object> getRechargeConfig(String platform_recharge_config_id) {
		return activityDao.getRechargeConfig(platform_recharge_config_id);
	}
	
	@Transactional
	public PurResult updateRechargeConfig(Map<String ,Object> params) {
		PurResult result=new PurResult();
		try {
			String platform_recharge_config_id = MUtil.strObject(params.get("platform_recharge_config_id"));
			String give_type = MUtil.strObject(params.get("give_type"));
			String shop_coupon_id = MUtil.strObject(params.get("shop_coupon_id"));
			String give_bean_num = null;
			
			if(shop_coupon_id != null && !shop_coupon_id.equals("")){
				//删除优惠券信息
				Map<String ,Object> map = new HashMap<String, Object>();
				map.put("shop_coupon_id", shop_coupon_id);
				activityDao.deleteShopCoupon(map);
				shop_coupon_id = null;
			}else{
				shop_coupon_id = null;
			}
			
			if(give_type != null && give_type.equals("1")){//优惠券
				Map<String,Object> map=new HashMap<String, Object>();
				map.put("shop_unique", params.get("shop_unique"));
				map.put("startDate", params.get("startDate"));
				map.put("endDate", params.get("endDate"));
				map.put("meet_amount", params.get("meet_amount"));
				map.put("coupon_amount", params.get("coupon_amount"));
				map.put("type", params.get("type"));
				map.put("give_status", 1);
				activityDao.addShopCoupon(map);
				shop_coupon_id = MUtil.strObject(map.get("shop_coupon_id"));
			}else if(give_type != null && give_type.equals("2")){//百货豆
				give_bean_num = MUtil.strObject(params.get("give_bean_num"));
			}else if(give_type != null && give_type.equals("3")){//优惠券、百货豆同时赠送
				Map<String,Object> map=new HashMap<String, Object>();
				map.put("shop_unique", params.get("shop_unique"));
				map.put("startDate", params.get("startDate"));
				map.put("endDate", params.get("endDate"));
				map.put("meet_amount", params.get("meet_amount"));
				map.put("coupon_amount", params.get("coupon_amount"));
				map.put("type", params.get("type"));
				map.put("give_status", 1);
				activityDao.addShopCoupon(map);
				shop_coupon_id = MUtil.strObject(map.get("shop_coupon_id"));
				
				give_bean_num = MUtil.strObject(params.get("give_bean_num"));
			}else{
				give_type = null;
				shop_coupon_id = null;
			}
			
			Map<String,Object> rechargeConfigParams = new HashMap<String, Object>();
			rechargeConfigParams.put("money", params.get("money"));
			rechargeConfigParams.put("give_type", give_type);
			rechargeConfigParams.put("shop_coupon_id", shop_coupon_id);
			rechargeConfigParams.put("give_bean_num", give_bean_num);
			rechargeConfigParams.put("platform_recharge_config_id", platform_recharge_config_id);
			activityDao.updateRechargeConfig(rechargeConfigParams);
			
			result.setStatus(1);
			result.setMsg("成功");
		} catch (Exception e) {
			e.printStackTrace();
			result.setStatus(0);
			result.setMsg("异常");
		}
		return result;
	}
	
	@Transactional
	public PurResult deleteRechargeConfig(String platform_recharge_config_id,String shop_coupon_id) {
		PurResult pr=new PurResult();
		Map<String,Object> map=new HashMap<String, Object>();
		map.put("platform_recharge_config_id",platform_recharge_config_id);
		activityDao.deleteRechargeConfig(map);
//		if(shop_coupon_id != null && !shop_coupon_id.equals("")){
//			//删除优惠券信息
//			map.put("shop_coupon_id", shop_coupon_id);
//			activityDao.deleteShopCoupon(map);
//		}
		pr.setStatus(1);
		return pr;
	}


	@Override
	public PurResult setPointUse(String shop_unique, String points_val, String money, String use_top,Double use_money) {
		PurResult pr=new PurResult();
		Map<String,Object> map=new HashMap<String, Object>();
		map.put("shop_unique",shop_unique);
		map.put("points_val",points_val);
		map.put("money",money);
		map.put("use_top",use_top);
		map.put("use_money", use_money);
		if(activityDao.querySetPointUse(map)!=null){
			activityDao.updateSetPointUse(map);
		}else{
			activityDao.setPointUse(map);
		}
		pr.setStatus(1);
		return pr;
	}


	@Override
	public PurResult querySetPointUse(String shop_unique) {
		PurResult pr=new PurResult();
		Map<String,Object> map=new HashMap<String, Object>();
		map.put("shop_unique",shop_unique);
		Map<String,Object> data=activityDao.querySetPointUse(map);
		pr.setData(data);
		pr.setStatus(1);
		return pr;
	}


	@Override
	public PurResult queryClassThemeList(Integer pageNum, Integer pageSize) {
		PurResult result=new PurResult();
		try {
			Map<String,Object> map=new HashMap<String, Object>();
			Map<String,Object> countMap=activityDao.queryClassThemeListCount(map);
			Integer count = Integer.parseInt(MUtil.strObject(countMap.get("count")));
			map.put("pageSize", pageSize);
			map.put("startNum", (pageNum-1)*pageSize);
			List<Map<String,Object>> orderList=activityDao.queryClassThemeList(map);
			result.setStatus(1);
			result.setMsg("查询成功！");
			result.setData(orderList);
			result.setCount(count);
		} catch (Exception e) {
			e.printStackTrace();
			result.setStatus(0);
			result.setMsg("异常");
		}
		return result;
	}
	
	public PurResult queryBootImg() {
		PurResult result=new PurResult();
		try {
			List<Map<String,Object>> list=activityDao.queryBootImg();
			result.setStatus(1);
			result.setMsg("查询成功！");
			result.setData(list);
			result.setCount(list.size());
		} catch (Exception e) {
			e.printStackTrace();
			result.setStatus(0);
			result.setMsg("异常");
		}
		return result;
	}


	@Override
	public Map<String, Object> queryClassThemeById(String class_theme_id) {
		Map<String,Object> map=new HashMap<String, Object>();
		map.put("class_theme_id", class_theme_id);
		Map<String,Object> countMap=activityDao.queryClassThemeById(map);
		return countMap;
	}


	@Override
	@Transactional
	public PurResult editClassTheme(String class_theme_id, String class_theme_name, HttpServletRequest request) {
		PurResult shop=new PurResult();
		Map<String,Object> map=new HashMap<String, Object>();
		map.put("class_theme_id", class_theme_id);
		map.put("class_theme_name", class_theme_name);
		MultipartFile file=null;
		if(request instanceof MultipartHttpServletRequest){
			MultipartHttpServletRequest multipartHttpServletRequest =(MultipartHttpServletRequest)request;
			Map<String, MultipartFile> mp = multipartHttpServletRequest.getFileMap();
			file = mp.get("goods_kind_image");
			
		}
		if(file!=null){
			String orName=file.getOriginalFilename();
			String lastName=orName.substring(orName.lastIndexOf("."));
			String shop_dir = File.separator + "home" + File.separator + "apache-tomcat-7.0.42"
					+ File.separator + "webapps" + File.separator + "image" + File.separator + class_theme_id
					+ File.separator;
			File dir=new File(shop_dir);
			if(!dir.exists()){
				dir.mkdirs();
			}
			String shop_pictureName=UUID.randomUUID()+lastName;
			PicSaveUtil.handleFileUpId(file, request, shop_dir, shop_pictureName);//图片的保存
			String shop_picture_path="image" + File.separator + class_theme_id+ File.separator+shop_pictureName;
			if(shop_picture_path!=null&&!"".equals(shop_picture_path)){
				map.put("class_theme_image", shop_picture_path);
			}
		}
		
		int k=activityDao.editClassTheme(map);
//		System.out.println(k);
		if(0==k){
			shop.setStatus(0);
			shop.setMsg("更新失败！");
			return shop;
		}
		shop.setStatus(1);
		shop.setMsg("更新成功！");
		return shop;
	}
	
	@Transactional
	public PurResult updateBootImg(String id, String time, String url, HttpServletRequest request) {
		PurResult shop=new PurResult();
		Map<String,Object> map=new HashMap<String, Object>();
		map.put("id", id);
		map.put("time", time);
		map.put("url", url);
		int k=activityDao.updateBootImg(map);
		if(0==k){
			shop.setStatus(0);
			shop.setMsg("更新失败！");
			return shop;
		}
		shop.setStatus(1);
		shop.setMsg("更新成功！");
		return shop;
	}
	
	public PurResult uploadImg(HttpServletRequest request) {
		PurResult pr=new PurResult();
		MultipartFile file=null;
		Map<String,Object> map=new HashMap<String, Object>();
		if(request instanceof MultipartHttpServletRequest){
			MultipartHttpServletRequest multipartHttpServletRequest =(MultipartHttpServletRequest)request;
			Map<String, MultipartFile> mp = multipartHttpServletRequest.getFileMap();
			file = mp.get("boot_img");
			
		}
		if(file!=null){
			String orName=file.getOriginalFilename();
			String lastName=orName.substring(orName.lastIndexOf("."));
			String shop_dir = File.separator + "home" + File.separator + "apache-tomcat-7.0.42"
					+ File.separator + "webapps" + File.separator + "image" + File.separator + "bootImg"
					+ File.separator;
			File dir=new File(shop_dir);
			if(!dir.exists()){
				dir.mkdirs();
			}
			String shop_pictureName=UUID.randomUUID().toString().replaceAll("-", "")+lastName;
			PicSaveUtil.handleFileUpId(file, request, shop_dir, shop_pictureName);//图片的保存
			String shop_picture_path=File.separator +"image" + File.separator + "bootImg"+ File.separator+shop_pictureName;
			if(shop_picture_path!=null&&!"".equals(shop_picture_path)){
				map.put("url", shop_picture_path);
			}
		}
		pr.setData(map);
		pr.setStatus(1);
		pr.setMsg("上传成功！");
		return pr;
	}

	public PurResult uploadGoodsDictImg(HttpServletRequest request) {
		PurResult pr=new PurResult();
		MultipartFile file=null;
		Map<String,Object> map=new HashMap<String, Object>();
		if(request instanceof MultipartHttpServletRequest){
			MultipartHttpServletRequest multipartHttpServletRequest =(MultipartHttpServletRequest)request;
			Map<String, MultipartFile> mp = multipartHttpServletRequest.getFileMap();
			file = mp.get("good_img");
			
		}
		if(file!=null){
			String orName=file.getOriginalFilename();
			String lastName=orName.substring(orName.lastIndexOf("."));
			String shop_dir = File.separator + "home" + File.separator + "apache-tomcat-7.0.42"
					+ File.separator + "webapps" + File.separator + "image" + File.separator + "goodsDict"
					+ File.separator;
			File dir=new File(shop_dir);
			if(!dir.exists()){
				dir.mkdirs();
			}
			String shop_pictureName=UUID.randomUUID().toString().replaceAll("-", "")+lastName;
			PicSaveUtil.handleFileUpId(file, request, shop_dir, shop_pictureName);//图片的保存
			String shop_picture_path="image" + File.separator + "goodsDict"+ File.separator+shop_pictureName;
			if(shop_picture_path!=null&&!"".equals(shop_picture_path)){
				map.put("url", shop_picture_path);
			}
		}
		pr.setData(map);
		pr.setStatus(1);
		pr.setMsg("上传成功！");
		return pr;
	}
	

	@Override
	public PurResult queryClassThemeGoodsList(Map<String, Object> map) {
		PurResult pr=new PurResult();
		List<Map<String, Object>> list=activityDao.queryClassThemeGoodsList(map);
		pr.setStatus(1);
		pr.setData(list);
		return pr;
	}


	@Override
	@Transactional
	public PurResult deleteClassThemeGoodsByGoodsBarcode(Map<String, Object> map) {
		PurResult pr=new PurResult();
		activityDao.deleteClassThemeGoodsByGoodsBarcode(map);
		pr.setStatus(1);
		return pr;
	}


	@Override
	@Transactional
	public PurResult addClassThemeGoodsList(String class_theme_id,String goods_id, String shop_unique) {
		PurResult pr=new PurResult();
		String ids[]= goods_id.substring(0,goods_id.length()-1).split(",");
		for (String id : ids) {
			Map<String,Object> map=new HashMap<String, Object>();
			map.put("goods_id", id);
			map.put("shop_unique", shop_unique);
			map.put("class_theme_id", class_theme_id);
			activityDao.addClassThemeGoodsList(map);
		}
		pr.setStatus(1);
		return pr;
	
	}


	@Override
	public PurResult queryPlatformCusList(String cusMessage, Integer pageNum, Integer pageSize) {
		PurResult result=new PurResult();
		try {
			Map<String,Object> map=new HashMap<String, Object>();
			if(cusMessage!=null&&!"".equals(cusMessage)&&!"null".equals(cusMessage)){
				map.put("cusMessage","%"+cusMessage+"%");
			}
			Integer count = activityDao.queryPlatformCusListCount(map);
		
			map.put("pageSize", pageSize);
			map.put("startNum", (pageNum-1)*pageSize);
			List<Map<String,Object>> orderList=activityDao.queryPlatformCusList(map);
			
			result.setStatus(1);
			result.setMsg("成功！");
			result.setData(orderList);
			result.setCount(count);
		} catch (Exception e) {
			e.printStackTrace();
			result.setStatus(0);
			result.setMsg("异常");
		}
		return result;
	}
	

	@Override
	public PurResult queryRewardList(Map<String ,Object> params) {
		PurResult result=new PurResult();
		try {

			List<Map<String,Object>> orderList=activityDao.queryRewardList(params);
			int count=activityDao.queryRewardListCount(params);
			
			result.setStatus(1);
			result.setMsg("成功！");
			result.setData(orderList);
			result.setCount(count);
		} catch (Exception e) {
			e.printStackTrace();
			result.setStatus(0);
			result.setMsg("异常");
		}
		return result;
	}
	
	@Override
	public PurResult queryEshowTVList(Map<String ,Object> params) {
		PurResult result=new PurResult();
		try {

			List<Map<String,Object>> orderList=activityDao.queryEshowTVList(params);
			int count=activityDao.queryEshowTVListCount(params);
			
			result.setStatus(1);
			result.setMsg("成功！");
			result.setData(orderList);
			result.setCount(count);
		} catch (Exception e) {
			e.printStackTrace();
			result.setStatus(0);
			result.setMsg("异常");
		}
		return result;
	}

	@Override
	@Transactional
	public PurResult addReward(List<PageData> data2) {
		PurResult result=new PurResult();
		try {

			List<PageData> count=activityDao.queryRewardRepeat(data2);
			if(count==null||count.size()==0)
			{

				activityDao.addRewardRepeat(data2);
			}else
			{
				result.setStatus(0);
				result.setMsg("店铺不能重复添加");
				return result;
			}
			result.setStatus(1);
			result.setMsg("操作成功！");
		} catch (Exception e) {
			e.printStackTrace();
			result.setStatus(0);
			result.setMsg("异常");
		}
		return result;
	}
	@Override
	@Transactional
	public PurResult edtitReward(Map<String, Object>  data2) {
		PurResult result=new PurResult();
		try {
				int num=activityDao.editReward(data2);
				if(num>0)
				{
					result.setStatus(1);
					result.setMsg("操作成功！");
				}else
				{
					result.setStatus(0);
					result.setMsg("操作异常!");
				}

		} catch (Exception e) {
			e.printStackTrace();
			
		}
		return result;
	}
	@Override
	@Transactional
	public PurResult deleteReward(String id) {
		PurResult result=new PurResult();
		try {

			activityDao.deleteReward( id);
			result.setStatus(1);
			result.setMsg("操作成功！");
		} catch (Exception e) {
			e.printStackTrace();
			result.setStatus(0);
			result.setMsg("异常");
		}
		return result;
	}
	@Override
	public PurResult updateReward(String id,int valid) {
		PurResult result=new PurResult();
		try {
			Map<String, Object> map=new HashMap<String,Object>();
			map.put("id", id);
			map.put("valid", valid);
			activityDao.updateReward(map);
			result.setStatus(1);
			result.setMsg("操作成功！");
		} catch (Exception e) {
			e.printStackTrace();
			result.setStatus(0);
			result.setMsg("异常");
		}
		return result;
	}
	public PurResult queryCouponRecord(Map<String, Object> map) {
		PurResult pr = new PurResult();
		Subject subject = SecurityUtils.getSubject();
		Session session = subject.getSession();
		Staff staff = (Staff) session.getAttribute("staff");
		
		Integer shop_type = staff.getShop_type();
		Long shop_unqiue = staff.getShop_unique();
		
		try {
			if(shop_type != 11) {
				List<Map<String,Object>> data = activityDao.queryCouponRecordList(map);
				Integer count = activityDao.queryCouponRecordListCount(map);
				pr.setStatus(1);
				pr.setMsg("查询成功！");
				pr.setData(data);
				pr.setCount(count);
			}else {
				map.put("shop_unique", shop_unqiue);
				List<Map<String,Object>> data = activityDao.queryCouponOffRecordList(map);
				Integer count = activityDao.queryCouponOffRecordListCount(map);
				pr.setStatus(1);
				pr.setMsg("查询成功！");
				pr.setData(data);
				pr.setCount(count);
			}
		} catch (Exception e) {
			e.printStackTrace();
			pr.setStatus(0);
			pr.setMsg("异常");
		}
		return pr;
	}	
	
	/**
	 * 查询商品信息
	 * @param map
	 * @return
	 */
	public ShopsResult queryGoodsMsgList(Map<String,Object> map) {
		ShopsResult sr = new ShopsResult(1,"查询成功");
		try {
			List<Map<String,Object>> data = activityDao.queryGoodsMsgList(map);
			sr.setData(data);
		}catch (Exception e) {
			e.printStackTrace();
		}
		return sr;
	}

	@Override
	public PurResult queryGoodsKind(String shop_unique, String goodsMessage, int pageNum, int pageSize) {
		PurResult pr = new PurResult();
		Map<String,Object> map=new HashMap<String, Object>();
		map.put("shop_unique", shop_unique);
		if(goodsMessage!=null&&!"".equals(goodsMessage)){
			map.put("goodsMessage", "%"+goodsMessage+"%");
		}
		//查询共多少条
		Map<String,Object> countMap=activityDao.queryGoodsKindCount(map);
		Integer count = Integer.parseInt(MUtil.strObject(countMap.get("count")));
		
		map.put("pageSize", pageSize);
		map.put("startNum", (pageNum-1)*pageSize);
		List<Map<String,Object>> list = activityDao.queryGoodsKindByPage(map);
		
		pr.setData(list);
		pr.setCount(count);
		pr.setStatus(1);
		pr.setMsg("查询成功！");
		return pr;
	}

	@Override
	public PurResult queryCoudflashGoods(Map<String, Object> map) {
		PurResult pr = new PurResult();
			List<Map<String,Object>> list = activityDao.queryCoudflashGoods(map);
			Integer count = activityDao.queryCoudGoodsCount(map);
			pr.setData(list);
			pr.setCount(count);
			pr.setStatus(1);
			pr.setMsg("查询成功！");
			return pr;
	}
	public PurResult submitCoudflashGoods(String startDate,String endDate, String status, String detailJson) {
			PurResult pr=new PurResult();
			try {
				JSONArray array= JSONArray.fromObject(detailJson);
				//添加订单
				List<Map<String ,Object>> map3List = new ArrayList<Map<String ,Object>>();
				for (int i = 0; i < array.size(); i++) {
					JSONObject temp = (JSONObject) array.get(i); 
					String goods_barcode= temp.getString("goods_barcode");
					String promotionPrice= temp.getString("promotionPrice");
					Map<String,Object> map3=new HashMap<String, Object>();
					map3.put("goods_barcode", goods_barcode);
					map3.put("promotionPrice", promotionPrice);
					map3.put("startDate", startDate);
					map3.put("endDate", endDate);
					map3.put("status", status);
					map3List.add(map3);
				}
				activityDao.addCoudflashGoods(map3List);
			} catch (Exception e) {
				e.printStackTrace();
				pr.setStatus(2);
				pr.setMsg("异常");
				return pr;
			}
			pr.setStatus(1);
			pr.setMsg("提交成功");
			return pr;
	}
	public PurResult updateYSFStatus(String id,String status) {
		PurResult pr=new PurResult();
		try {
				Map<String,Object> map3=new HashMap<String, Object>();
				map3.put("id", id);
				map3.put("status", status);
			activityDao.updateYSFStatus(map3);
		} catch (Exception e) {
			e.printStackTrace();
			pr.setStatus(2);
			pr.setMsg("异常");
			return pr;
		}
		pr.setStatus(1);
		pr.setMsg("提交成功");
		return pr;
}

	@Override
	public PurResult queryFoodManagerList(Map<String, Object> map) {
		PurResult result = new PurResult();
		try {
			List<Map<String,Object>> list = activityDao.queryFoodManagerList(map);
	    	Integer count = activityDao.queryFoodManagerListCount(map);
	    	result.setStatus(1);
			result.setMsg("成功");
			result.setCount(count);
			result.setData(list);
		} catch (Exception e) {
			e.printStackTrace();
			result.setStatus(0);
			result.setMsg("异常");
		}
		return result;
	}

	@Override
	public ShopsResult addFoodManager(Map<String, Object> map, HttpServletRequest request) {
		ShopsResult sr=new ShopsResult();
		String shop_unique=map.get("shop_unique").toString();
	
		//实景图片
		MultipartFile file2=null;
		if(request instanceof MultipartHttpServletRequest){
			MultipartHttpServletRequest multipartHttpServletRequest =(MultipartHttpServletRequest)request;
			Map<String, MultipartFile> mp = multipartHttpServletRequest.getFileMap();
			file2 = mp.get("shop_picture2");
		}
		if(file2!=null){
			String orName=file2.getOriginalFilename();
			String lastName=orName.substring(orName.lastIndexOf("."));
			String shop_dir = "/mnt/myData/tomcat/tomcat1/webapps" + File.separator + "image" + File.separator + shop_unique
					+ File.separator;
			File dir=new File(shop_dir);
			if(!dir.exists()){
				dir.mkdirs();
			}
			String shop_pictureName=UUID.randomUUID()+lastName;
			PicSaveUtil.handleFileUpId(file2, request, shop_dir, shop_pictureName);//淇濆瓨鍥剧墖
			String shop_picture_path="image" + File.separator + shop_unique+ File.separator+shop_pictureName;
			map.put("image1", shop_picture_path);
		}
		MultipartFile file3=null;
		if(request instanceof MultipartHttpServletRequest){
			MultipartHttpServletRequest multipartHttpServletRequest =(MultipartHttpServletRequest)request;
			Map<String, MultipartFile> mp = multipartHttpServletRequest.getFileMap();
			file3 = mp.get("shop_picture3");
		}
		if(file3!=null){
			String orName=file3.getOriginalFilename();
			String lastName=orName.substring(orName.lastIndexOf("."));
			String shop_dir = "/mnt/myData/tomcat/tomcat1/webapps" + File.separator + "image" + File.separator + shop_unique
						+ File.separator;
			File dir=new File(shop_dir);
			if(!dir.exists()){
				dir.mkdirs();
			}
			String shop_pictureName=UUID.randomUUID()+lastName;
			PicSaveUtil.handleFileUpId(file3, request, shop_dir, shop_pictureName);//淇濆瓨鍥剧墖
			String shop_picture_path="image" + File.separator + shop_unique+ File.separator+shop_pictureName;
			map.put("image2", shop_picture_path);
		}
		
		MultipartFile file4=null;
		if(request instanceof MultipartHttpServletRequest){
			MultipartHttpServletRequest multipartHttpServletRequest =(MultipartHttpServletRequest)request;
			Map<String, MultipartFile> mp = multipartHttpServletRequest.getFileMap();
			file4 = mp.get("shop_picture4");
		}
		if(file4!=null){
			String orName=file4.getOriginalFilename();
			String lastName=orName.substring(orName.lastIndexOf("."));
			String shop_dir = "/mnt/myData/tomcat/tomcat1/webapps" + File.separator + "image" + File.separator + shop_unique
						+ File.separator;
			File dir=new File(shop_dir);
			if(!dir.exists()){
				dir.mkdirs();
			}
			String shop_pictureName=UUID.randomUUID()+lastName;
			PicSaveUtil.handleFileUpId(file4, request, shop_dir, shop_pictureName);//淇濆瓨鍥剧墖
			String shop_picture_path="image" + File.separator + shop_unique+ File.separator+shop_pictureName;
			map.put("image3", shop_picture_path);
		}
		
		MultipartFile file5=null;
		if(request instanceof MultipartHttpServletRequest){
			MultipartHttpServletRequest multipartHttpServletRequest =(MultipartHttpServletRequest)request;
			Map<String, MultipartFile> mp = multipartHttpServletRequest.getFileMap();
			file5 = mp.get("shop_picture5");
		}
		if(file5!=null){
			String orName=file5.getOriginalFilename();
			String lastName=orName.substring(orName.lastIndexOf("."));
			String shop_dir = "/mnt/myData/tomcat/tomcat1/webapps" + File.separator + "image" + File.separator + shop_unique
						+ File.separator;
			File dir=new File(shop_dir);
			if(!dir.exists()){
				dir.mkdirs();
			}
			String shop_pictureName=UUID.randomUUID()+lastName;
			PicSaveUtil.handleFileUpId(file5, request, shop_dir, shop_pictureName);//淇濆瓨鍥剧墖
			String shop_picture_path="image" + File.separator + shop_unique+ File.separator+shop_pictureName;
			map.put("image4", shop_picture_path);
		}
		
		MultipartFile file6=null;
		if(request instanceof MultipartHttpServletRequest){
			MultipartHttpServletRequest multipartHttpServletRequest =(MultipartHttpServletRequest)request;
			Map<String, MultipartFile> mp = multipartHttpServletRequest.getFileMap();
			file6 = mp.get("shop_picture6");
		}
		if(file6!=null){
			String orName=file6.getOriginalFilename();
			String lastName=orName.substring(orName.lastIndexOf("."));
			String shop_dir = "/mnt/myData/tomcat/tomcat1/webapps" + File.separator + "image" + File.separator + shop_unique
						+ File.separator;
			File dir=new File(shop_dir);
			if(!dir.exists()){
				dir.mkdirs();
			}
			String shop_pictureName=UUID.randomUUID()+lastName;
			PicSaveUtil.handleFileUpId(file6, request, shop_dir, shop_pictureName);//淇濆瓨鍥剧墖
			String shop_picture_path="image" + File.separator + shop_unique+ File.separator+shop_pictureName;
			map.put("image5", shop_picture_path);
		}
		if(map.get("id")!=null&&!"".equals(map.get("id").toString())){
			activityDao.editFoodManager(map);
		}else{
			activityDao.addFoodManager(map);
		}
		
		sr.setStatus(1);
		return sr;
	}

	@Override
	public Map<String, Object> queryFoodManager(Map<String, Object> map) {
		return activityDao.queryFoodManager(map);
	}
	@Override
	public Map<String, Object> queryNewsManager(Map<String, Object> map) {
		return activityDao.queryNewsManager(map);
	}

	@Override
	public ShopsResult deleteFoodManager(Map<String, Object> map) {
		ShopsResult sr=new ShopsResult();
		activityDao.deleteFoodManager(map);
		sr.setStatus(1);
		return sr;
	}
	@Override
	public ShopsResult deleteNewsManager(Map<String, Object> map) {
		ShopsResult sr=new ShopsResult();
		activityDao.deleteNewsManager(map);
		sr.setStatus(1);
		return sr;
	}
	
	@Override
	public PurResult queryNewsManagerList(Map<String, Object> map) {
		PurResult result = new PurResult();
		try {
			List<Map<String,Object>> list = activityDao.queryNewsManagerList(map);
	    	Integer count = activityDao.queryNewsManagerListCount(map);
	    	result.setStatus(1);
			result.setMsg("成功");
			result.setCount(count);
			result.setData(list);
		} catch (Exception e) {
			e.printStackTrace();
			result.setStatus(0);
			result.setMsg("异常");
		}
		return result;
	}
	
	@Override
	public ShopsResult addNewsManager(Map<String, Object> map, HttpServletRequest request) throws  Exception {
		ShopsResult sr=new ShopsResult();
		String shop_unique="8302016134121";
	
		//实景图片
		MultipartFile file2=null;
		if(request instanceof MultipartHttpServletRequest){
			MultipartHttpServletRequest multipartHttpServletRequest =(MultipartHttpServletRequest)request;
			Map<String, MultipartFile> mp = multipartHttpServletRequest.getFileMap();
			file2 = mp.get("shop_picture2");
		}
		if(file2!=null){
			InputStream is = file2.getInputStream();   
			String orName=file2.getOriginalFilename();
			String lastName=orName.substring(orName.lastIndexOf("."));
			String shop_dir = "/mnt/myData/tomcat/tomcat1/webapps" + File.separator + "image" + File.separator + shop_unique
					+ File.separator;
			File dir=new File(shop_dir);
			if(!dir.exists()){
				dir.mkdirs();
			}
			String shop_pictureName=UUID.randomUUID()+lastName;
			PicSaveUtil.handleFileUpId(file2, request, shop_dir, shop_pictureName);//淇濆瓨鍥剧墖
			SFTPUtil sftp = new SFTPUtil(FTPConfig.username, FTPConfig.password, FTPConfig.host, FTPConfig.port);   
	        sftp.login(); 
	        boolean flag=sftp.upload(FTPConfig.goods_path+"/"+shop_unique, shop_pictureName, is);   
			String shop_picture_path="image" + File.separator + shop_unique+ File.separator+shop_pictureName;
			map.put("image1", shop_picture_path);
			 sftp.logout(); 
		}
		if(map.get("id")!=null&&!"".equals(map.get("id").toString())){
			activityDao.editNewsManager(map);
		}else{
			activityDao.addNewsManager(map);
		}
		
		sr.setStatus(1);
		return sr;
	}

	@Override
	public PurResult queryReortConfigList(Map<String, Object> map) {
		PurResult result = new PurResult();
		try {
			List<Map<String,Object>> list = activityDao.queryReortConfigList(map);
	    	Integer count = activityDao.queryReortConfigListCount(map);
	    	result.setStatus(1);
			result.setMsg("成功");
			result.setCount(count);
			result.setData(list);
		} catch (Exception e) {
			e.printStackTrace();
			result.setStatus(0);
			result.setMsg("异常");
		}
		return result;
	}

	@Override
	public ShopsResult addReportConfig(Map<String, Object> map, HttpServletRequest request) {
		ShopsResult sr=new ShopsResult();
		if(map.get("id")!=null&&!"".equals(map.get("id").toString())){
			map.put("send_status", 2);
			activityDao.editReportConfig(map);
		}else{
			activityDao.addReportConfig(map);
		}
		
		sr.setStatus(1);
		return sr;
	}

	@Override
	public Map<String, Object> queryReportConfig(Map<String, Object> map) {
		return activityDao.queryReportConfig(map);
	}

	@Override
	public ShopsResult deleteReportConfig(Map<String, Object> map) {
		ShopsResult sr=new ShopsResult();
		activityDao.deleteReportConfig(map);
		sr.setStatus(1);
		return sr;
	}

	@Override
	public void serverEndReport() {
		List<Map<String,Object>> list = activityDao.queryServerEndReortList();
		for (Map<String, Object> map : list) {
			 JSONObject jo = new JSONObject();
			 jo.put("name", map.get("name")+":"+map.get("ip"));
			 jo.put("end_time", map.get("end_time"));
			 SmsMessageUtil.send(map.get("phone").toString(), jo.toString(),"SMS_224275470");
			 activityDao.updateReportStatus(map);
		}
		

		
	}
	@Transactional
	@Override
	public PurResult addSingleShopCoupon(String shop_unique, String start_time, String end_time, String coupon_name,
			String goods_barcode, String goods_name, String goods_in_price, String goods_price, String count,
			String exclusive_type) {
		PurResult pr = new PurResult();
		try {
			Map<String,Object> params = new HashMap<String, Object>();
			
			params.put("shop_unique", shop_unique);
			params.put("startDate", start_time);
			params.put("endDate", end_time);
			params.put("coupon_name", coupon_name);
			params.put("give_status", 0);
			params.put("exclusive_type", exclusive_type);
			params.put("meet_amount", goods_price);
			params.put("coupon_amount", goods_price);
			params.put("is_auto_grant", 1);
			params.put("grant_num",-1);
			params.put("is_single_good",1);
			activityDao.addShopCoupon(params);
		
			Map<String ,Object> goods = new HashMap<String, Object>();
			goods.put("shop_coupon_id", params.get("shop_coupon_id"));
			goods.put("goods_barcode", goods_barcode);
			goods.put("goods_name", goods_name);
			goods.put("goods_in_price", goods_in_price);
			goods.put("goods_price", goods_price);
			goods.put("count", count);
			activityDao.addShopCouponGoods(goods);
			
		} catch (Exception e) {
			e.printStackTrace();
			pr.setStatus(0);
			pr.setMsg("异常");
		}
		pr.setStatus(1);
		pr.setMsg("成功");
		return pr;
	}

	@Override
	@Transactional
	public PurResult updateSingleShopCoupon(String shop_unique, String start_time, String end_time, String coupon_name,
			String goods_barcode, String goods_name, String goods_in_price, String goods_price, String count,
			String exclusive_type, String shop_coupon_id, String id) {
		PurResult pr = new PurResult();
		try {
			Map<String,Object> params = new HashMap<String, Object>();
			
			params.put("shop_coupon_id", shop_coupon_id);
			params.put("shop_unique", shop_unique);
			params.put("startDate", start_time);
			params.put("endDate", end_time);
			params.put("coupon_name", coupon_name);
			params.put("meet_amount", goods_price);
			params.put("coupon_amount", goods_price);
			activityDao.updateSingleShopCoupon(params);
		
			Map<String ,Object> goods = new HashMap<String, Object>();
			goods.put("shop_coupon_id",shop_coupon_id);
			goods.put("id", id);
			goods.put("goods_barcode", goods_barcode);
			goods.put("goods_name", goods_name);
			goods.put("goods_in_price", goods_in_price);
			goods.put("goods_price", goods_price);
			goods.put("count", count);
			activityDao.updateShopCouponGoods(goods);
			
		} catch (Exception e) {
			e.printStackTrace();
			pr.setStatus(0);
			pr.setMsg("异常");
		}
		pr.setStatus(1);
		pr.setMsg("成功");
		return pr;
	}
	@Transactional
	public PurResult updateFlashSale(String promotion_activity_id,String shop_unique,  String promotion_activity_name, String startDate,
			String endDate, String detailJson,String activity_range) {
		PurResult pr=new PurResult();
		JSONArray array= JSONArray.fromObject(detailJson);
	
		List<String> timeCoincide = new ArrayList<String>();
		for (int i = 0; i < array.size(); i++) {
			JSONObject temp = (JSONObject) array.get(i); 
			String goods_id= temp.getString("goods_id");
			String goods_name = temp.getString("goods_name");
			//判断当前商品是否有时间重合活动
			Map<String ,Object> params = new HashMap<String, Object>();
			params.put("shop_unique", shop_unique);
			params.put("promotion_activity_id", promotion_activity_id);
			params.put("goods_id", goods_id);
			params.put("startDate", startDate);
			params.put("endDate", endDate);
			Integer count = activityDao.getTimeGoodsPromotionOnline(params);
			if(count > 0){
				timeCoincide.add(goods_name);
			}
		}
		if(timeCoincide.size()>0){
			pr.setData(timeCoincide.toString());
			pr.setStatus(0);
			pr.setMsg("有时间重合商品");
			return pr;
		}
		
		Map<String,Object> map=new HashMap<String, Object>();
		map.put("promotion_activity_id", promotion_activity_id);
		map.put("promotion_activity_name", promotion_activity_name);
		map.put("startDate", startDate);
		map.put("endDate", endDate);
	
		//编辑订单
		activityDao.updateSupplierStorageOrder(map);
		//修改商品
		for (int i = 0; i < array.size(); i++) {
			JSONObject temp = (JSONObject) array.get(i); 
			String goods_id= temp.getString("goods_id");
			Map<String,Object> map3=new HashMap<String, Object>();
			map3.put("goods_id", goods_id);
			map3.put("promotion_activity_id", promotion_activity_id);
			map3.put("per_count", temp.get("per_count"));
			map3.put("total_count", temp.get("total_count"));
			activityDao.updateSinglePromotionDetail(map3);
		}
		pr.setStatus(1);
		pr.setMsg("提交成功");
		return pr;
	}
}
