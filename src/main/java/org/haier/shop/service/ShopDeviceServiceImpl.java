package org.haier.shop.service;

import java.sql.Timestamp;
import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.haier.meituan.util.MUtil;
import org.haier.shop.dao.ShopDao;
import org.haier.shop.dao3.ShopDeviceDao;
import org.haier.shop.dao3.ShopSoftDao;
import org.haier.shop.util.PurResult;
import org.haier.shop.util.UUIDUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Service
public class ShopDeviceServiceImpl implements ShopDeviceService{
	
	@Autowired
    private ShopDeviceDao shopDeviceDao;
	
	@Autowired
    private ShopSoftDao shopSoftDao;
	
	@Autowired
    private ShopDao shopDao;
	
	@Autowired
	private ShopSoftService shopSoftService;
	
	@Override
	public List<Map<String, Object>> queryShopDeviceList(Map<String, Object> params) {
		return shopDeviceDao.queryShopDeviceList(params);
	}

	@Override
	public Integer queryShopDeviceListCount(Map<String, Object> params) {
		return shopDeviceDao.queryShopDeviceListCount(params);
	}

	@Override
	public Map<String, Object> queryShopDeviceSumInfo(String shop_unique) {
		List<Map<String ,Object>> shopDeviceList = shopDeviceDao.queryShopDeviceSum(shop_unique);
		Integer total_count = 0;
		Integer total_activated_num = 0;
		Integer total_need_activated_num = 0;
		for(int i=0;i<shopDeviceList.size();i++) {
			total_count = total_count + Integer.parseInt(MUtil.strObject(shopDeviceList.get(i).get("sum_count")));
			Integer device_type = Integer.parseInt(MUtil.strObject(shopDeviceList.get(i).get("device_type")));
			if(device_type == 1) {
				//已激活数量
				Map<String ,Object> params = new HashMap<String ,Object>();
				params.put("shop_unique", shop_unique);
				params.put("device_type_id", shopDeviceList.get(i).get("device_type_id"));
				Integer activated_num = shopDeviceDao.queryActivatedNum(params);
				shopDeviceList.get(i).put("activated_num", activated_num);
				total_activated_num = total_activated_num+activated_num;
				total_need_activated_num = total_need_activated_num + Integer.parseInt(MUtil.strObject(shopDeviceList.get(i).get("sum_count")));
			}else {
				shopDeviceList.get(i).put("activated_num", "/");
			}
		}
		Map<String ,Object> result = new HashMap<String ,Object>();
		result.put("shopDeviceList", shopDeviceList);
		result.put("total_count", total_count);
		result.put("total_activated_num", total_activated_num);
		result.put("total_need_activated_num", total_need_activated_num);
		return result;
	}

	@Override
	public Map<String, Object> queryShopDeviceDetail(String shop_device_id) {
		return shopDeviceDao.queryShopDeviceDetail(shop_device_id);
	}

	@Override
	public List<Map<String, Object>> shopDeviceApplyList(Map<String, Object> params) {
		return shopDeviceDao.queryShopDeviceApplyList(params);
	}

	@Override
	public Integer shopDeviceApplyListCount(Map<String, Object> params) {
		return shopDeviceDao.queryShopDeviceApplyListCount(params);
	}

	@Override
	public List<Map<String, Object>> queryDeviceTypeList() {
		return shopDeviceDao.queryDeviceTypeList();
	}

	@Override
	@Transactional
	public PurResult addDeviceApply(Map<String, Object> params) {
		PurResult result = new PurResult();
		try {
			String shop_unique = MUtil.strObject(params.get("shop_unique"));
			//获取店铺上级代理商code
			String agency_code = shopDeviceDao.queryShopAgencyCode(shop_unique);
			//添加申请主表
			Date date = new Date();
			String apply_id = UUIDUtil.getUUID32();
			params.put("id", apply_id);
			params.put("agency_code", agency_code);
			params.put("apply_no", "DA"+date.getTime());
			shopDeviceDao.addShopDeviceApply(params);
			
			//添加申请详情表
			List<Map<String, Object>> device_list = MUtil.strToList(MUtil.strObject(params.get("device_list")));
			List<Map<String, Object>> detailList = new ArrayList<Map<String, Object>>();
			for(int i=0;i<device_list.size();i++) {
				String device_type = MUtil.strObject(device_list.get(i).get("device_type"));
				if(device_type.equals("1")) {//收银机
					Integer device_count = Integer.parseInt(MUtil.strObject(device_list.get(i).get("device_count")));
					for(int j=0;j<device_count;j++) {
						Map<String, Object> detail = new HashMap<String ,Object>(); 
						detail.put("apply_id", apply_id);
						detail.put("device_type_id", device_list.get(i).get("device_type_id"));
						detail.put("device_count", 1);
						detail.put("device_deposit", device_list.get(i).get("device_deposit"));
						detail.put("sum_deposit", device_list.get(i).get("device_deposit"));
						detailList.add(detail);
					}
				}else {//普通设备
					Map<String, Object> detail = new HashMap<String ,Object>(); 
					detail.put("apply_id", apply_id);
					detail.put("device_type_id", device_list.get(i).get("device_type_id"));
					detail.put("device_count", device_list.get(i).get("device_count"));
					detail.put("device_deposit", device_list.get(i).get("device_deposit"));
					detail.put("sum_deposit", device_list.get(i).get("sum_deposit"));
					detailList.add(detail);
				}
			}
			shopDeviceDao.addShopDeviceApplyDeatil(detailList);
	    	result.setStatus(1);
			result.setMsg("成功");
		} catch (Exception e) {
			e.printStackTrace();
			result.setStatus(0);
			result.setMsg("异常");
		}
		return result;
	}

	@Override
	@Transactional
	public PurResult updateDeviceApply(Map<String, Object> params) {
		PurResult result = new PurResult();
		try {
			shopDeviceDao.updateDeviceApply(params);
	    	result.setStatus(1);
			result.setMsg("成功");
		} catch (Exception e) {
			e.printStackTrace();
			result.setStatus(0);
			result.setMsg("异常");
		}
		return result;
	}

	@Override
	public Map<String, Object> queryDeviceApplyDetail(String id) {
		//获取设备申请详情
		Map<String, Object> deviceApply = shopDeviceDao.queryDeviceApply(id);
		//获取设备申请详细信息列表
		List<Map<String, Object>> detailList = shopDeviceDao.queryDeviceApplyDetailList(id);
		deviceApply.put("detailList", detailList);
		return deviceApply;
	}

	@Override
	public List<Map<String, Object>> shopDeviceReturnList(Map<String, Object> params) {
		return shopDeviceDao.shopDeviceReturnList(params);
	}

	@Override
	public Integer shopDeviceReturnListCount(Map<String, Object> params) {
		return shopDeviceDao.shopDeviceReturnListCount(params);
	}
	
	@Override
	@Transactional
	public PurResult addDeviceReturn(Map<String, Object> params) {
		PurResult result = new PurResult();
		try {
			String shop_unique = MUtil.strObject(params.get("shop_unique"));
			//获取店铺上级代理商code
			String agency_code = shopDeviceDao.queryShopAgencyCode(shop_unique);
			
			//添加申请主表
			Date date = new Date();
			String shop_service_apply_no = "SS"+date.getTime();
			params.put("agency_code", agency_code);
			params.put("shop_service_apply_no", shop_service_apply_no);
			shopDeviceDao.addShopService(params);
			
			//添加申请详情表
			List<Map<String, Object>> device_list = MUtil.strToList(MUtil.strObject(params.get("device_list")));
			for(int i=0;i<device_list.size();i++) {
				device_list.get(i).put("shop_service_apply_no", shop_service_apply_no);
			}
			shopDeviceDao.addShopServiceDetail(device_list);
	    	result.setStatus(1);
			result.setMsg("成功");
		} catch (Exception e) {
			e.printStackTrace();
			result.setStatus(0);
			result.setMsg("异常");
		}
		return result;
	}
	
	@Override
	public Map<String, Object> queryDeviceReturnDetail(String id) {
		//获取设备退换详情
		Map<String, Object> deviceReturn = shopDeviceDao.queryDeviceReturn(id);
		//获取设备退换详细信息列表
		String shop_service_apply_no = MUtil.strObject(deviceReturn.get("shop_service_apply_no"));
		List<Map<String, Object>> detailList = shopDeviceDao.queryDeviceReturnDetailList(shop_service_apply_no);
		deviceReturn.put("detailList", detailList);
		return deviceReturn;
	}
	
	@Override
	@Transactional
	public PurResult updateShopService(Map<String, Object> params) {
		PurResult result = new PurResult();
		try {
			shopDeviceDao.updateShopService(params);
	    	result.setStatus(1);
			result.setMsg("成功");
		} catch (Exception e) {
			e.printStackTrace();
			result.setStatus(0);
			result.setMsg("异常");
		}
		return result;
	}
	
	@Override
	public List<Map<String, Object>> shopDeviceRepairList(Map<String, Object> params) {
		return shopDeviceDao.shopDeviceRepairList(params);
	}

	@Override
	public Integer shopDeviceRepairListCount(Map<String, Object> params) {
		return shopDeviceDao.shopDeviceRepairListCount(params);
	}
	
	@Override
	@Transactional
	public PurResult addDeviceRepair(Map<String, Object> params) {
		PurResult result = new PurResult();
		try {
			String shop_unique = MUtil.strObject(params.get("shop_unique"));
			//获取店铺上级代理商code
			String agency_code = shopDeviceDao.queryShopAgencyCode(shop_unique);
			
			//添加申请主表
			Date date = new Date();
			String shop_service_apply_no = "SS"+date.getTime();
			params.put("agency_code", agency_code);
			params.put("shop_service_apply_no", shop_service_apply_no);
			shopDeviceDao.addShopService(params);
			
			//添加申请详情表
			List<Map<String, Object>> detailList = new ArrayList<Map<String, Object>>();
			detailList.add(params);
			shopDeviceDao.addShopServiceDetail(detailList);
	    	result.setStatus(1);
			result.setMsg("成功");
		} catch (Exception e) {
			e.printStackTrace();
			result.setStatus(0);
			result.setMsg("异常");
		}
		return result;
	}

	@Override
	public PurResult getShopDeviceCdkeyInfo(Map<String, Object> params) {
		PurResult result = new PurResult();
		try {
			//获取店铺当前设备激活码信息
			Map<String ,Object> cdkeyInfo = shopDeviceDao.getShopDeviceCdkeyInfo(params);
			if(cdkeyInfo != null) {
				Timestamp due_time = (Timestamp) cdkeyInfo.get("due_time");
				Timestamp now_time = new Timestamp(new Date().getTime()); 
				if(due_time.before(now_time)) {//due_time时间比now_time早
					cdkeyInfo.put("status", 2);//已过期
				}else {
					cdkeyInfo.put("status", 1);//正常
				}
				result.setData(cdkeyInfo);
				result.setStatus(1);
				result.setMsg("成功");
			}else {
				//获取软件开始在线收费时间
				Timestamp soft_charge_start_time = (Timestamp) shopDao.queryShopSoftChargeTime().get("start_time");
				//获取店铺注册时间
				Map<String ,Object> shopInfo = shopDao.queryShopMessage(params);
				Timestamp registration_date = (Timestamp) shopInfo.get("registration_date");
				if(registration_date.before(soft_charge_start_time)) {//registration_date时间比soft_charge_start_time早
					//创建店铺软件信息
					params.put("cdkey_code", shopSoftService.getCdkeyCode(10));
					Map<String ,Object> shopSoft = new HashMap<String, Object>();
					shopSoft.put("soft_code", UUIDUtil.getUUID32());
					shopSoft.put("cdkey_code", params.get("cdkey_code"));
					shopSoft.put("shop_unique", params.get("shop_unique"));
					shopSoft.put("due_time", "9999-01-01 00:00:00");//永久
					shopSoft.put("device_no", params.get("device_no"));
					shopSoft.put("status", 2);//已激活
					DateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
					shopSoft.put("activation_time", sdf.format(registration_date));//激活时间
					shopSoft.put("edition", params.get("edition"));
					shopSoft.put("up_time", sdf.format(new Date()));
					shopSoftDao.addShopSoft(shopSoft);
					
					cdkeyInfo = new HashMap<String, Object>();
					cdkeyInfo.put("due_time", Timestamp.valueOf("9999-01-01 00:00:00"));
					cdkeyInfo.put("cdkey_code", params.get("cdkey_code"));
					cdkeyInfo.put("status", 1);
					
					result.setData(cdkeyInfo);
					result.setStatus(1);
					result.setMsg("成功");
				}else {
					result.setStatus(2);
					result.setMsg("没有激活码信息，需购买激活码");
				}
			}
		} catch (Exception e) {
			e.printStackTrace();
			result.setStatus(0);
			result.setMsg("异常");
		}
		return result;
	}
	
	@Override
	public PurResult cdkeyActivation(String shop_unique,String device_no,String cdkey_code) {
		PurResult result = new PurResult();
		try {
			//获取店铺当前激活码信息
			Map<String ,Object> params = new HashMap<String, Object>();
			params.put("shop_unique", shop_unique);
			params.put("cdkey_code", cdkey_code);
			Map<String ,Object> cdkeyInfo = shopDeviceDao.getShopDeviceCdkeyInfo(params);
			if(cdkeyInfo != null) {
				Timestamp due_time = (Timestamp) cdkeyInfo.get("due_time");
				Timestamp now_time = new Timestamp(new Date().getTime()); 
				if(due_time.before(now_time)) {//due_time时间比now_time早
					result.setStatus(3);
					result.setMsg("激活码已过期");
					
					return result;
				}
				//绑定激活码
				params.put("device_no", device_no);
				shopSoftDao.updateShopSoft(params);
				
				result.setStatus(1);
				result.setMsg("成功");
			}else {
				result.setStatus(2);
				result.setMsg("激活码错误，没有此激活码");
			}
		} catch (Exception e) {
			e.printStackTrace();
			result.setStatus(0);
			result.setMsg("异常");
		}
		return result;
	}
}
