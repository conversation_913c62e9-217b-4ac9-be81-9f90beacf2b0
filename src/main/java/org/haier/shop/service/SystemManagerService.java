package org.haier.shop.service;

import java.util.Map;

import javax.servlet.http.HttpServletRequest;

import org.haier.shop.util.ShopsResult;
import org.springframework.web.multipart.MultipartFile;

public interface SystemManagerService {

	/**
	 * 向收银设备发送mqtt指令
	 * @param shopUnique 店铺编号
	 * @param macId 收银机macId
	 * @param dayCount 上传日志天数
	 * @return
	 */
	public ShopsResult sendUpdloadCmd(String shopUnique,String macId, Integer dayCount,String startTime, String endTime);

	/**
	 * 查询店铺信息
	 * @param page 页码
	 * @param limit 单页查询数量
	 * @param shopMsg 搜索框信息
	 * @return
	 */
	public ShopsResult queryShopMacList(Integer page,Integer limit,String shopMsg);

	public ShopsResult uploadTest(HttpServletRequest request,MultipartFile file,String id,String value);
	/**
	 * 上传APP升级文件
	 * @param appId 1、APP商城；2、百货商家端；3、供货商（金圈云商）；4、
	 * @param appType
	 * @param update_version
	 * @param updateDes
	 * @param updateInstall
	 * @param appName
	 * @param updateLog
	 * @param request
	 * @return
	 */
	public ShopsResult uploadApp(Integer appId, Integer appType, String update_version,String updateDes,
			Integer updateInstall,String appName,String updateLog,HttpServletRequest request,MultipartFile file,Integer code);

	public ShopsResult uploadAppPacket(HttpServletRequest request,MultipartFile file);

	public ShopsResult queryAppUploadDetail(String id);


	public ShopsResult appPacketUnzip(Integer id, Integer appType, String url);

	public ShopsResult createMd5(Integer id,  String url);
	/**
	 * 分页查询APP升级记录
	 * @param page 页码
	 * @param pageSize 单页查询数量
	 * @param project_id 项目ID
	 * @param appId app类型：1、APP商城；2、APP商家端；3、供货商；4、物流端；5、PC客户端；6、一刻钟到家小程序；7、云商系统
	 * @param appType
	 * @param startTime
	 * @param endTime
	 * @return
	 */
	public ShopsResult queryAppVersionList(Integer page, Integer pageSize,Integer project_id,Integer appId, Integer appType, String startTime, String endTime);
	/**
	 * 店铺免密信息统计（全）
	 * @return
	 */
	public ShopsResult queryShopsUserMsg();
	
	/**
	 * 周期内免密使用情况
	 * @return
	 */
	public ShopsResult mianmiStatisQuery(Map<String,Object> map);
	
	/**
	 * 免密支付走势图数据列表查询
	 * @param map
	 * @return
	 */
	public ShopsResult mianmiStatisticsPic(Map<String,Object> map);
}
