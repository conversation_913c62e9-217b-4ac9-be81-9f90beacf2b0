package org.haier.shop.service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;

import org.haier.shop.dao.DepositDao;
import org.haier.shop.util.PurResult;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Service
public class DepositServiceImpl implements DepositService{
	
	@Resource
	private DepositDao depositDao;
	
	/**
	 * 盘点订单详情
	 * @param map
	 * @return
	 */
	public PurResult queryDeposit(Map<String,Object> params){
		PurResult sr=new PurResult();
		try {
			Map<String,Object> detail = depositDao.queryDeposit(params);
			Map<String ,Object> paramsList = new HashMap<String, Object>();
			paramsList.put("deposit_id", detail.get("deposit_id"));
			List<Map<String,Object>> list = depositDao.queryDepositRecordList(paramsList);
			sr.setData(list);
			sr.setCord(detail);
			sr.setStatus(1);
			sr.setMsg("成功");
		} catch (Exception e) {
			e.printStackTrace();
			sr.setStatus(0);
			sr.setMsg("异常");
		}
		return sr;
	}
}
