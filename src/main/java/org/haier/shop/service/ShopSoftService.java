package org.haier.shop.service;

import java.util.List;
import java.util.Map;

import org.haier.shop.util.PurResult;

import com.alipay.api.AlipayApiException;

public interface ShopSoftService {
	/**
	 * 查询店铺软件列表
	 * @param shop_unique 店铺编号
	 * @param message 激活码/设备编号
	 * @param status 状态：1未激活 2已激活 3禁用
	 * @param field 排序字段
	 * @param order 排序正序倒叙
	 * @param startNum
	 * @param pageSize
	 * @return
	 */
	public List<Map<String ,Object>> queryShopSoftList(Map<String,Object> params);
	
	/**
	 * 查询店铺软件列表总条数
	 * @param shop_unique 店铺编号
	 * @param message 激活码/设备编号
	 * @param status 状态：1未激活 2已激活 3禁用
	 * @param field 排序字段
	 * @param order 排序正序倒叙
	 * @param startNum
	 * @param pageSize
	 * @return
	 */
	public Integer queryShopSoftListCount(Map<String,Object> params);
	
	/**
	 * 查询店铺软件购买记录列表
	 * @param shop_unique 店铺编号
	 * @param message 激活码/交易流水
	 * @param bug_type 支付类型：1支付宝 2微信
	 * @param startNum
	 * @param pageSize
	 * @return
	 */
	public List<Map<String ,Object>> queryShopSoftProfitList(Map<String,Object> params);
	
	/**
	 * 查询店铺软件购买记录列表总条数
	 * @param shop_unique 店铺编号
	 * @param message 激活码/交易流水
	 * @param bug_type 支付类型：1支付宝 2微信
	 * @param startNum
	 * @param pageSize
	 * @return
	 */
	public Integer queryShopSoftProfitListCount(Map<String,Object> params);
	
	/**
	 * 删除软件购买记录
	 * @param id
	 * @return
	 */
	public void deleteShopSoftProfit(String id);
	
	/**
	 * 查询系统软件设置列表
	 * @return
	 */
	public List<Map<String ,Object>> querySysSoftSettingList();
	
	/**
	 * 续费-微信支付
	 * @param 
	 * @return
	 */
	public String weixinPay(Map<String,Object> params);
	
	/**
	 * 续费-支付宝支付
	 * @param 
	 * @return
	 */
	public String aliPay(Map<String,Object> params) throws AlipayApiException ;
	
	/**
	 * 续费-支付成功业务处理
	 * @param 
	 * @return
	 */
	public boolean paySuccess(Map<String,Object> params);
	
	/**
	 * 查询系统软件设置详情
	 * @return
	 */
	public Map<String ,Object> querySysSoftSetting(String code);
	
	/**
	 * 生成激活码,长度为n
	 * @return
	 */
	public String getCdkeyCode(int n);
	
	/**
	 * 验证是否支付成功
	 * @param profit_no 支付记录订单号：SP+时间戳
	 * @param shop_unique 店铺编号
	 */
	public PurResult isPaySuccess(Map<String,Object> params);
	
}
