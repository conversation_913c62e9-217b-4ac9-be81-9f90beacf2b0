package org.haier.shop.service;

import java.util.ArrayList;
import java.util.Calendar;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;

import org.haier.shop.dao.Test2Dao;
import org.haier.shop.util.ShopsResult;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Transactional
@Service
public class Test2ServiceImpl implements Test2Service{
	
	@Resource
	public Test2Dao test2Dao;
	public ShopsResult goodsFORpage(Map<String,Object> map){
		ShopsResult sr=new ShopsResult();
		List<Map<String,Object>> data=test2Dao.goodsFORpage(map);
		sr.setData(data);
		System.out.println(Calendar.getInstance().getTime());
		return sr;
	}
	
	
	public ShopsResult goodsFORpagea(Map<String,Object> map){
		ShopsResult sr=new ShopsResult();
		List<Map<String,Object>> data=test2Dao.goodsFORpagea(map);
		sr.setData(data);
		System.out.println(Calendar.getInstance().getTime());
		return sr;
	}
	
	public ShopsResult queryShopsList(){
		ShopsResult sr=new ShopsResult();
		List<String> list=test2Dao.queryShopsList();
		List<Map<String,Object>> data=new ArrayList<Map<String,Object>>();
		for(int i=0;i<list.size();i++){
			Map<String,Object> map=new HashMap<String, Object>();
			map.put("shopUnique", list.get(i));
			map.put("info", "新版通告");
			map.put("title", "新版！首页、统计更新，新增全网热销！");
			data.add(map);
		}
		int k = test2Dao.addNewTitle(data);
		System.out.println(k);
		sr.setStatus(1);
		sr.setMsg("");
		return sr;
	}
	
}
