package org.haier.shop.service;

import java.util.*;

import javax.annotation.Resource;

import org.haier.shop.dao.ProjectMsgDao;
import org.haier.shop.util.ShopsResult;
import org.haier.shop.util.ShopsUtil;
import org.springframework.stereotype.Service;

/**
 * @author: 作者:王恩龙
 * @version: 2023年6月14日 下午4:22:24
 *
 */
@Service
public class ProjectMsgServiceImpl implements ProjectMsgService {
	@Resource
	private ProjectMsgDao projectMsgDao;
	
	/**
	 * 工具，获取新HashMap<String,Object>；
	 * @return
	 */
	public static Map<String,Object> getMap(){
		Map<String,Object> map = new HashMap<String,Object>();
		return map;
	}
	/**
	 * 添加新的项目名称
	 * @param project_name
	 * @return
	 */
	public ShopsResult addNewProjectMsg(String project_name,String project_type,String remarks) {
		//查询项目名称和类型，防止重复
		Map<String, Object> params = getMap();
		params.put("projectMsg", project_name);
		params.put("project_type", project_type);
		params.put("startNum", 0);
		params.put("pageSize", 1);
		params.put("dict_type", "sys_project_key");
		
		List<Map<String,Object>> list = projectMsgDao.queryProjectMsgList(params);
		
		if(null != list && !list.isEmpty()) {
			return ShopsResult.fail("已有相同名称的项目存在");
		}
		
		params.put("project_name", project_name);
		params.put("remarks", remarks);
		
		//添加新的项目
		//创建项目编号
		String project_unique = UUID.randomUUID().toString();
		//项目密钥
		String project_key = ShopsUtil.md5(project_unique);
		
		params.put("project_unique", project_unique);
		params.put("project_key", project_key);
		
		projectMsgDao.addNewProjectMsg(params);
		
		return ShopsResult.ok();
	}

	/**
	 * 查询指定页码的项目信息
	 * 
	 * @param page
	 * @param limit
	 * @return
	 */
	public ShopsResult queryProjectMsgList(Integer page, Integer limit, Integer project_type) {
		Map<String, Object> params = new HashMap<String, Object>();
		params.put("startNum", (page-1)*limit);
		params.put("pageSize", limit);
		params.put("project_type", project_type);
		params.put("dict_type", "sys_project_key");
		List<Map<String, Object>> list = projectMsgDao.queryProjectMsgList(params);
		Integer count = projectMsgDao.queryProjectMsgCount(params);

		return ShopsResult.success(list, count);
	}

	@Override
	public ShopsResult queryShopsDeviceVeriosn(Integer page, Integer limit, String shopsMessage,Integer handleStatus) {
		Map<String, Object> params = new HashMap<String, Object>();
		params.put("startNum", (page-1)*limit);
		params.put("pageSize", limit);
		params.put("shopsMessage", shopsMessage);
		params.put("handleStatus", handleStatus);
		List<Map<String, Object>> list = projectMsgDao.queryShopsDeviceVeriosn(params);
		Integer count = projectMsgDao.queryShopsDeviceVeriosnCount(params);

		return ShopsResult.success(list, count);
	}

	@Override
	public ShopsResult updateShopsVersion(String ids, String version) {
		Map<String,Object> map=new HashMap<String, Object>();
		map.put("version", version);
		String[] idArr=ids.split(";");
		List<String> idList=new ArrayList<String>();
		for(String a:idArr){
			idList.add(a);
		}
		map.put("list", idList);
		projectMsgDao.updateShopsVersion(map);
		return  ShopsResult.success();
	}
}
