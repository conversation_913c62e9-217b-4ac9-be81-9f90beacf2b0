package org.haier.shop.service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;

import org.haier.meituan.util.MUtil;
import org.haier.shop.dao.SelfPurchaseDao;
import org.haier.shop.dao2.SupOrderDao;
import org.haier.shop.util.PurResult;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Service
@Transactional
public class SupOrderServiceImpl implements SupOrderService{
	
	@Resource
	private SupOrderDao supOrderDao;
	
	@Resource
	private SelfPurchaseDao selfPurchaseDao;

	/**
	 * 查询退款订单信息
	 * @param shopUnique 店铺编号
	 * @param page 页码
	 * @param pageSize 单页查询数量
	 * @param startTime 开始时间
	 * @param endTime 结束时间
	 * @param msg 输入框输入的信息，查询单号，供货商名称
	 * @return
	 */
	@Override
	public PurResult getSupRetOrderList(String shopUnique,Integer page,Integer pageSize,String startTime,String endTime,String msg ) {
		PurResult pr = new PurResult(1, "查询成功!");
		
		Map<String,Object> map = new HashMap<String,Object>();
		map.put("shopUnique", shopUnique);
		map.put("startTime", startTime);
		map.put("endTime", endTime);
		map.put("msg", msg);
		
		map.put("startNum", (page-1)*pageSize);
		map.put("pageSize", pageSize);
		
		try {
			List<Map<String,Object>> list = new ArrayList<Map<String,Object>>();
			Integer total = 0;
			
			list = selfPurchaseDao.getSupRetOrderList(map);
			total = selfPurchaseDao.getSupRetOrderListCount(map);
			
			pr.setData(list);
			pr.setTotal(total);
		}catch (Exception e) {
			e.printStackTrace();
			pr.setStatus(0);
			pr.setMsg("系统错误!");
		}
		
		return pr;
	}
	
	@Override
	public PurResult getSupOrderList(Map<String, Object> params,String order_source) {
		PurResult result = new PurResult();
		try {
			List<Map<String ,Object>> list = new ArrayList<Map<String,Object>>();
			Integer countNum = 0;
			if(order_source.equals("1")){
				list = supOrderDao.getSupOrderList(params);
				countNum = supOrderDao.getSupOrderListCount(params);
			}else if(order_source.equals("2")){
				list = selfPurchaseDao.getSelfPurchaseList(params);
				countNum = selfPurchaseDao.getSelfPurchaseListCount(params);
			}
					
			result.setCount(countNum);
			result.setData(list);
			result.setStatus(1);
			result.setMsg("成功");
		} catch (Exception e) {
			result.setStatus(0);
			result.setMsg("异常");
		}
		return result;
	}

	@Override
	public PurResult getSupOrderDetail(String order_code) {
		PurResult result = new PurResult();
		try {
			//订单基本信息
			Map<String ,Object> supOrder = supOrderDao.getSupOrder(order_code);
			//订单详情列表
			List<Map<String ,Object>> supOrderdetailList = supOrderDao.getSupOrderdetailList(order_code);
			for(int i=0;i<supOrderdetailList.size();i++){
				String orderdetail_id = MUtil.strObject(supOrderdetailList.get(i).get("orderdetail_id"));
				//查询订单子单商品规格列表
				List<Map<String ,Object>> supOrderGoodsSpecsList = supOrderDao.getSupOrderGoodsSpecsList(orderdetail_id);
				for(int j=0;j<supOrderGoodsSpecsList.size();j++){
					String sup_order_goodsSpecs_id = MUtil.strObject(supOrderGoodsSpecsList.get(j).get("sup_order_goodsSpecs_id"));
					//查询订单子单商品规格数量
					Integer spec_count = supOrderDao.getSpecCount(sup_order_goodsSpecs_id);
					supOrderGoodsSpecsList.get(j).put("spec_count", spec_count);
				}
				supOrderdetailList.get(i).put("supOrderGoodsSpecsList", supOrderGoodsSpecsList);
			}
			//订单支付列表
			List<Map<String ,Object>> supOrderpayList = supOrderDao.getSupOrderpayList(order_code);

			supOrder.put("supOrderdetailList", supOrderdetailList);
			supOrder.put("supOrderpayList", supOrderpayList);
			result.setData(supOrder);
			result.setStatus(1);
			result.setMsg("成功");
		} catch (Exception e) {
			e.printStackTrace();
			result.setStatus(0);
			result.setMsg("异常");
		}
		return result;
	}

	@Override
	public List<Map<String, Object>> getOrderList(Map<String, Object> params) {
		return supOrderDao.getSupOrderList(params);
	}
	
	@Override
	public List<Map<String, Object>> getSelfOrderList(Map<String, Object> params) {
		return selfPurchaseDao.getSelfPurchaseList(params);
	}

	@Override
	public PurResult updateOrderStatus(Map<String, Object> params) {
		PurResult result = new PurResult();
		try {
			//订单基本信息
			String order_code = MUtil.strObject(params.get("order_code"));
			Map<String ,Object> supOrder = supOrderDao.getSupOrder(order_code);
			Integer order_status =  Integer.parseInt(MUtil.strObject(supOrder.get("order_status")));
			if(order_status != 1){
				result.setStatus(2);
				if(order_status == 2){
					result.setMsg("订单待配送，不能取消");
				}
				if(order_status == 3){
					result.setMsg("订单配送中，不能取消");
				}
				if(order_status == 4){
					result.setMsg("订单已完成，不能取消");
				}
				if(order_status == 5){
					result.setMsg("订单已取消，不能取消");
				}
				return result;
			}
			//修改订单状态为已取消
			supOrderDao.updateOrderStatus(params);
			
			result.setStatus(1);
			result.setMsg("成功");
		} catch (Exception e) {
			e.printStackTrace();
			result.setStatus(0);
			result.setMsg("异常");
		}
		return result;
	}

}
