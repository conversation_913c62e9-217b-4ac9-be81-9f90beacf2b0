package org.haier.shop.service;

import java.util.Map;

import org.haier.shop.util.ShopsResult;

public interface EvaluateService {
	/**
	 * 查询店铺近期的商品评价
	 * @param map
	 * @return
	 */
	public ShopsResult evaluateListPagesQuery(Map<String,Object> map);
	
	
	/**
	 * @param map
	 * @return
	 */
	public ShopsResult evaluateListQuery(Map<String,Object> map);
	
	/**
	 * 查询评论详情
	 * @param map
	 * @return
	 * 
	 */
	public ShopsResult queryEvaluateDetail(Map<String,Object> map);
	
	/**
	 * 删除管理员评论内容
	 * @param map
	 * @return
	 */
	public ShopsResult deleteManagerEvaluate(Map<String,Object> map);
	
	/**
	 * 添加订单评论回复
	 * @param map
	 * @return
	 */
	public ShopsResult responeEvaluate(Map<String,Object> map);
}

