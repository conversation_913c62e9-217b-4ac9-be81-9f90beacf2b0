package org.haier.shop.service;

import org.haier.shop.entity.GoodsSaleBatch;
import org.haier.shop.entity.ShopStockDetail;
import org.haier.shop.entity.ShopsConfig;
import org.haier.shop.params.goods.*;
import org.haier.shop.params.goodsBatch.GoodsSaleBatchData;
import org.haier.shop.vo.GoodsBatchOutData;

import java.util.List;

/**
 * @Description 库存管理
 * @ClassName IGoodsBatchService
 * <AUTHOR>
 * @Date 2024/4/28 15:00
 **/
public interface InventoryManagerService {

    /**
     * 商品入库管理
     * @param params
     * @return
     */
    public boolean dealGoodsInventory(GoodsInventoryParam params);

    /**
     * 商品出库
     *
     * @param shopsConfig
     * @param outStockParam
     */
    List<GoodsSaleBatch> saleGoodsBatch(ShopsConfig shopsConfig, OutStockParam outStockParam);

    /**
     * 商品出库批次
     *
     * @param shopUnique
     * @param goodsParam
     * @param validateBatch         是否校验批次库存
     * @param newGoodsSaleBatchList
     * @param goodsBatchOutData
     * @return
     */
    List<GoodsSaleBatchData> createGoodsSaleBatch(OutStockParam shopUnique, OutStockGoodsParam goodsParam, boolean validateBatch, List<GoodsSaleBatchData> newGoodsSaleBatchList, GoodsBatchOutData goodsBatchOutData);

    /**
     * 出库审核通过，处理批次库存
     *
     * @param shopStockDetail
     * @param shopStockDataList
     */
    void subGoodsBatchCount(ShopStockDetail shopStockDetail, List<ShopStockData> shopStockDataList);

    GoodsBatchOutData dealOutSelectBatch(OutStockParam outStockParam);
}
