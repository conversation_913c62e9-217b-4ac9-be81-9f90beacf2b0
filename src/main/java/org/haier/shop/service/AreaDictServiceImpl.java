package org.haier.shop.service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;

import org.haier.shop.dao.AreaDao;
import org.haier.shop.util.ShopsResult;
import org.springframework.stereotype.Service;

@Service("areaDictService")
public class AreaDictServiceImpl implements AreaDictService{
	@Resource
	private AreaDao areaDao;
	/**
	 * 省市区县查询！
	 */
	public ShopsResult queryArea(String area_dict_parent_num) {
		ShopsResult shop=new ShopsResult();
		Map<String,Object> map=new HashMap<String, Object>();
		map.put("area_dict_parent_num", area_dict_parent_num);
		List<Map<String,Object>>  reList=areaDao.queryArea(map);
		shop.setStatus(0);
		shop.setMsg("查询成功！");
		shop.setData(reList);
		return shop;
	}
}
