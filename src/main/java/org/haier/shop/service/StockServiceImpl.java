package org.haier.shop.service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.stream.Collectors;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.thread.ThreadUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpUtil;
import org.apache.commons.lang3.StringUtils;
import org.haier.shop.dao.*;
import org.haier.shop.entity.*;
import org.haier.shop.entity.goodsRecord.RecordGoods;
import org.haier.shop.entity.goodsRecord.RecordGoodsOper;
import org.haier.shop.entity.goodsRecord.RecordGoodsOperParams;
import org.haier.shop.enums.*;
import org.haier.shop.enums.goodsEnum.DeviceSourceEnum;
import org.haier.shop.enums.goodsEnum.OperSourceEnum;
import org.haier.shop.enums.goodsEnum.OperTypeEnum;
import org.haier.shop.enums.goodsEnum.UserTypeEnum;
import org.haier.shop.mqtt.MqttForGoodsUpdate;
import org.haier.shop.params.goods.*;
import org.haier.shop.params.goodsBatch.GoodsSaleBatchData;
import org.haier.shop.params.stock.ShopOutStockAuditParam;
import org.haier.shop.params.stock.ShopStockDetailQueryParam;
import org.haier.shop.result.stock.ShopStockDetailVO;
import org.haier.shop.util.PurResult;
import org.haier.shop.util.ShopsResult;
import org.haier.shop.util.UtilForJAVA;
import org.haier.shop.vo.GoodsBatchOutData;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import net.sf.json.JSONArray;
import net.sf.json.JSONObject;
import org.springframework.util.ObjectUtils;

@Service
@Transactional
public class StockServiceImpl implements StockService {
    @Resource
    private StockDao stockDao;
    @Resource
    private GoodsDao goodsDao;
    @Resource
    private ShopsConfigDao shopsConfigDao;
    @Resource
    private InventoryManagerService inventoryManagerService;
    @Resource
    private ShopStockDetailMapper shopStockDetailMapper;
    @Resource
    private ShopStaffMapper shopStaffMapper;
    @Resource
    private ShopStockMapper shopStockMapper;
    @Resource
    private GoodsSaleBatchMapper goodsSaleBatchMapper;
    @Resource
    private GoodsBatchMapper goodsBatchMapper;

    @Resource
    private GoodsSaleBatchService goodsSaleBatchService;
    @Resource
    private MqttService mqttService;

    @Override
    public PurResult queryShopStockRecordList(Map<String, Object> map) {
        PurResult sr = new PurResult();
        List<Map<String, Object>> data = stockDao.queryShopStockRecordList(map);
        if (null == data || data.isEmpty()) {
            sr.setStatus(2);
            sr.setMsg("没有满足条件的出入库记录");
            return sr;
        }
        for (Map<String, Object> map2 : data) {
            //查询商品图片
            map2.put("shopUnique", map.get("shopUnique"));
            List<Map<String, Object>> goods_list = stockDao.queryGoodsImageByFailId(map2);
            map2.put("goods_list", goods_list);
        }

        sr.setStatus(1);
        sr.setMsg("查询成功！");
        sr.setData(data);
        return sr;
    }

    @Override
    public PurResult queryShopStockRecordDetail(Long shopUnique, String list_unique) {
        PurResult sr = new PurResult();
        Map<String, Object> params = new HashMap<String, Object>();
        params.put("shopUnique", shopUnique);
        params.put("list_unique", list_unique);
        //查询入库供货商详情
        Map<String, Object> data = stockDao.queryShopStockRecordDetail(params);
        List<Map<String, Object>> goods_list = stockDao.queryGoodsImageByFailId(params);
        data.put("goods_list", goods_list);
        sr.setStatus(1);
        sr.setMsg("查询成功！");
        sr.setData(data);
        return sr;
    }

    @Override
    public PurResult addIntoStock(InStockParam inStockParam) {
        //判断店铺是否需要审核
        ShopsConfig shopsConfig = shopsConfigDao.selectByShopUnique(String.valueOf(inStockParam.getShopUnique()));
        //查询单号是否存在
        PurResult purResult = new PurResult(PurResult.FAIL, "操作失败");
        ShopStockDetail shopStockDetail = shopStockDetailMapper.selectByListUnique(inStockParam.getListUnique());
        if (ObjectUtil.isNotNull(shopStockDetail)) {
            purResult.setMsg("单号已经存在");
            return purResult;
        }
        Date currentDate = DateUtil.date();
        shopStockDetail = new ShopStockDetail();
        shopStockDetail.setListUnique(inStockParam.getListUnique());
        shopStockDetail.setSourceUnique(inStockParam.getSourceUnique());
        shopStockDetail.setShopUnique(String.valueOf(inStockParam.getShopUnique()));
        shopStockDetail.setSupplierUnique(inStockParam.getSupplierUnique());
        shopStockDetail.setStockKind(StockKindEnum.INIT.getValue());
        shopStockDetail.setStockResource(inStockParam.getStockResource());
        if (ObjectUtil.isNull(shopStockDetail.getStockResource())) {
            shopStockDetail.setStockResource(StockReourceEnum.BY_HAND.getValue());
        }
        // 进货订单不需要审核
        if (ObjectUtil.equals(StockReourceEnum.BY_ORDER.getValue(), shopStockDetail.getStockResource()) || (ObjectUtil.isNotNull(shopsConfig) && ObjectUtil.equals(Integer.valueOf(0), shopsConfig.getIsIoboundInspect()))) {
            shopStockDetail.setAuditStatus(AduitStatusEnum.APPROVED.getValue());
            shopStockDetail.setAuditId(inStockParam.getUserId());
            shopStockDetail.setAuditTime(currentDate);
        } else {
            shopStockDetail.setAuditStatus(AduitStatusEnum.WAITING.getValue());
        }
        shopStockDetail.setStockRemarks(inStockParam.getStockRemarks());
        shopStockDetail.setStockType(StockTypeEnum.INSTORAGE.getValue());
        shopStockDetail.setStockOrigin(StockOriginEnum.WEB.getValue());
        shopStockDetail.setStaffId(inStockParam.getUserId());
        shopStockDetail.setStockTime(currentDate);
        shopStockDetail.setUpdateId(inStockParam.getUserId());
        shopStockDetail.setUpdateTime(currentDate);

        if (ObjectUtil.equals(AduitStatusEnum.APPROVED.getValue(), shopStockDetail.getAuditStatus())) {
            // 审核通过
            List<InStockGoodsParam> goodsList = inStockParam.getGoodsList();
            Map<String, GoodsEntity> goodsEntityMap = new HashMap<>();
            List<InStockGoodsData> goodsDataList = new ArrayList<>();
            Map<String, BigDecimal> sourceEntityMap = new HashMap<>();
            //处理最小规格商品进价、库存
            for (InStockGoodsParam shopStock : goodsList) {
                InStockGoodsData goodsData = new InStockGoodsData();
                BeanUtil.copyProperties(shopStock, goodsData);
                Map<String, Object> paramMap = new HashMap<>();
                paramMap.put("shopUnique", inStockParam.getShopUnique());
                paramMap.put("goodsBarcode", goodsData.getGoodsBarcode());
                GoodsEntity goodsEntity = goodsDao.queryOneByParam(paramMap);
                sourceEntityMap.put(goodsEntity.getGoodsBarcode(), goodsEntity.getGoodsCount());
                if (ObjectUtil.isNull(goodsEntity)) {
                    purResult.setMsg("入库商品条码：" + goodsData.getGoodsBarcode() + "不存在，无法入库");
                    return purResult;
                }
                BigDecimal goodsContain = goodsEntity.getGoodsContain();
                goodsData.setGoodsContain(goodsContain);
                goodsData.setForeignKey(String.valueOf(goodsEntity.getForeignKey()));
                BigDecimal smallTotalCount;
                GoodsEntity smallGoods = goodsEntityMap.get(goodsData.getForeignKey());
                if (ObjectUtil.isNull(smallGoods)) {
                    smallGoods = goodsDao.queryParantGoods(paramMap);
                    sourceEntityMap.put(smallGoods.getGoodsBarcode(), smallGoods.getGoodsCount());
                }
                if (!ObjectUtil.equals(goodsData.getGoodsBarcode(), goodsData.getForeignKey())) {
                    //有多规格
                    BigDecimal smallInCount = NumberUtil.mul(goodsData.getGoodsCount(), goodsContain);
                    if (ObjectUtil.isNotNull(smallGoods)) {
                        smallTotalCount = NumberUtil.add(smallGoods.getGoodsCount(), smallInCount);
                        if (ObjectUtil.isNotNull(shopsConfig) && ObjectUtil.equals(GoodsInPriceTypeEnums.AVG.getCode(), shopsConfig.getGoodsInPriceType())) {
                            // 移动加权平均价
                            BigDecimal totalPrice = NumberUtil.add(NumberUtil.mul(smallGoods.getGoodsInPrice(), smallGoods.getGoodsCount()), NumberUtil.mul(goodsData.getStockPrice(), goodsData.getGoodsCount()));
                            if (BigDecimal.ZERO.compareTo(smallGoods.getGoodsCount()) == 0) {
                                smallGoods.setGoodsInPrice(NumberUtil.div(goodsData.getStockPrice(), goodsContain).setScale(2, RoundingMode.HALF_UP));
                            } else {
                                smallGoods.setGoodsInPrice(NumberUtil.div(totalPrice, smallTotalCount, 2, RoundingMode.HALF_UP));
                            }
                        } else if (ObjectUtil.isNotNull(shopsConfig) && ObjectUtil.equals(GoodsInPriceTypeEnums.CUSTOM.getCode(), shopsConfig.getGoodsInPriceType())) {
                            // 新价格覆盖老价格
                            smallGoods.setGoodsInPrice(NumberUtil.div(goodsData.getStockPrice(), goodsContain, 2, RoundingMode.HALF_UP));
                        }
                        smallGoods.setGoodsCount(smallTotalCount);
                        goodsEntityMap.put(smallGoods.getGoodsBarcode(), smallGoods);
                    } else {
                        smallTotalCount = NumberUtil.add(goodsEntity.getGoodsCount(), smallInCount);
                        if (ObjectUtil.isNotNull(shopsConfig) && ObjectUtil.equals(GoodsInPriceTypeEnums.AVG.getCode(), shopsConfig.getGoodsInPriceType())) {
                            // 移动加权平均价
                            BigDecimal totalPrice = NumberUtil.add(NumberUtil.mul(goodsEntity.getGoodsInPrice(), goodsEntity.getGoodsCount()), NumberUtil.mul(goodsData.getStockPrice(), goodsData.getGoodsCount()));
                            if (BigDecimal.ZERO.compareTo(goodsEntity.getGoodsCount()) == 0) {
                                goodsEntity.setGoodsInPrice(goodsData.getStockPrice());
                            } else {
                                goodsEntity.setGoodsInPrice(NumberUtil.div(totalPrice, smallTotalCount, 2, RoundingMode.HALF_UP));
                            }
                        } else if (ObjectUtil.isNotNull(shopsConfig) && ObjectUtil.equals(GoodsInPriceTypeEnums.CUSTOM.getCode(), shopsConfig.getGoodsInPriceType())) {
                            goodsEntity.setGoodsInPrice(goodsData.getStockPrice());
                        }
                        goodsEntity.setGoodsCount(smallTotalCount);
                        goodsEntityMap.put(goodsEntity.getGoodsBarcode(), goodsEntity);
                    }
                } else {
                    smallTotalCount = goodsData.getGoodsCount();
                    if (ObjectUtil.isNotNull(smallGoods)) {
                        smallTotalCount = NumberUtil.add(smallTotalCount, smallGoods.getGoodsCount());
                    }
                    if (ObjectUtil.isNotNull(shopsConfig) && ObjectUtil.equals(GoodsInPriceTypeEnums.AVG.getCode(), shopsConfig.getGoodsInPriceType())) {
                        // 移动加权平均价
                        BigDecimal totalPrice = NumberUtil.add(NumberUtil.mul(smallGoods.getGoodsInPrice(), smallGoods.getGoodsCount()), NumberUtil.mul(goodsData.getStockPrice(), goodsData.getGoodsCount()));
                        if (BigDecimal.ZERO.compareTo(smallGoods.getGoodsCount()) == 0) {
                            smallGoods.setGoodsInPrice(goodsData.getStockPrice());
                        } else {
                            smallGoods.setGoodsInPrice(NumberUtil.div(totalPrice, smallTotalCount, 2, RoundingMode.HALF_UP));
                        }
                    } else if (ObjectUtil.isNotNull(shopsConfig) && ObjectUtil.equals(GoodsInPriceTypeEnums.CUSTOM.getCode(), shopsConfig.getGoodsInPriceType())) {
                        smallGoods.setGoodsInPrice(goodsData.getStockPrice());
                    }
                    smallGoods.setGoodsCount(smallTotalCount);
                    goodsEntityMap.put(smallGoods.getGoodsBarcode(), smallGoods);
                }
                goodsDataList.add(goodsData);
            }
            BigDecimal totalCount = BigDecimal.ZERO;
            BigDecimal totalAmount = BigDecimal.ZERO;
            List<ShopStock> shopStockList = new ArrayList<>();
            List<GoodsBatch> goodsBatches = new ArrayList<>();
            int index = 0;
            for (InStockGoodsData goodsData : goodsDataList) {
                index++;
                ShopStock shopStock = new ShopStock();
                shopStock.setGoodsBarcode(goodsData.getGoodsBarcode());
                shopStock.setGoodsCount(goodsData.getGoodsCount());
                shopStock.setStockCount(BigDecimal.ZERO);
                shopStock.setStockType(StockTypeEnum.INSTORAGE.getValue());
                shopStock.setStockTime(currentDate);
                shopStock.setShopUnique(inStockParam.getShopUnique());
                shopStock.setStockResource(shopStockDetail.getStockResource());
                shopStock.setListUnique(shopStockDetail.getListUnique());
                shopStock.setStockPrice(goodsData.getStockPrice());
                shopStock.setStockOrigin(shopStockDetail.getStockOrigin());
                shopStock.setStaffId(shopStockDetail.getStaffId());
                shopStock.setGoodsProd(goodsData.getGoodsProd());
                shopStock.setGoodsLife(goodsData.getGoodsLife());
                shopStock.setGoodsExp(goodsData.getGoodsExp());
                shopStock.setGoodsAvgOutPrice(BigDecimal.ZERO);
                totalCount = NumberUtil.add(totalCount, shopStock.getGoodsCount());
                totalAmount = NumberUtil.add(totalAmount, NumberUtil.mul(shopStock.getStockPrice(), shopStock.getGoodsCount()));
                BigDecimal smallCount = goodsEntityMap.get(goodsData.getForeignKey()).getGoodsCount();
                if (StrUtil.equals(goodsData.getGoodsBarcode(), goodsData.getForeignKey())) {
                    shopStock.setStockCount(smallCount);
                } else {
                    shopStock.setStockCount(NumberUtil.div(smallCount, goodsData.getGoodsContain(), 0, RoundingMode.DOWN));
                }

                shopStockList.add(shopStock);

                if (ObjectUtil.isNotNull(shopsConfig) && ObjectUtil.equals(GoodsInPriceTypeEnums.BATCH.getCode(), shopsConfig.getGoodsInPriceType())) {
                    // 批次方式
                    GoodsBatch goodsBatch = new GoodsBatch();
                    goodsBatch.setShopUnique(shopStock.getShopUnique());
                    goodsBatch.setBatchUnique(StrUtil.concat(true, String.valueOf(currentDate.getTime()), String.format("%03d", index)));
                    goodsBatch.setStockListUnique(shopStock.getListUnique());
                    goodsBatch.setGoodsBarcode(goodsData.getForeignKey());
                    goodsBatch.setGoodsInPrice(NumberUtil.div(goodsData.getStockPrice(), goodsData.getGoodsContain(), 2, RoundingMode.HALF_UP));
                    goodsBatch.setGoodsInCount(NumberUtil.mul(goodsData.getGoodsCount(), goodsData.getGoodsContain()));
                    goodsBatch.setGoodsCount(goodsBatch.getGoodsInCount());

                    // 冲销掉负库存
                    BigDecimal sourceCount = sourceEntityMap.get(goodsBatch.getGoodsBarcode());
                    if (ObjectUtil.isNotNull(sourceCount)) {
                        if (BigDecimal.ZERO.compareTo(sourceCount) > 0) {
                            BigDecimal newCount = NumberUtil.add(goodsBatch.getGoodsInCount(), sourceCount);
                            if (BigDecimal.ZERO.compareTo(newCount) > 0) {
                                goodsBatch.setGoodsCount(BigDecimal.ZERO);
                            } else {
                                goodsBatch.setGoodsCount(newCount);
                            }
                            sourceEntityMap.put(goodsBatch.getGoodsBarcode(), sourceCount);
                        }
                    }
                    goodsBatch.setGoodsProd(shopStock.getGoodsProd());
                    goodsBatch.setGoodsLife(shopStock.getGoodsLife());
                    goodsBatch.setGoodsExp(shopStock.getGoodsExp());
                    goodsBatch.setCreateTime(currentDate);
                    goodsBatch.setCreateId(inStockParam.getUserId());
                    goodsBatch.setUpdateId(goodsBatch.getCreateId());
                    goodsBatch.setUpdateTime(currentDate);
                    goodsBatch.setGoodsContain(goodsData.getGoodsContain());
                    goodsBatch.setSourceBarcode(goodsData.getGoodsBarcode());
                    goodsBatches.add(goodsBatch);
                }
            }
            shopStockDetail.setTotalAmount(totalAmount);
            shopStockDetail.setTotalCount(totalCount);
            int n = shopStockDetailMapper.insertShopStockDetail(shopStockDetail);
            if (n > 0) {
                shopStockMapper.insertBatch(shopStockList);
                if (CollectionUtil.isNotEmpty(goodsBatches)) {
                    goodsBatchMapper.insertBatch(goodsBatches);
                }
                List<GoodsEntity> goodsUpdateList = new ArrayList<>(goodsEntityMap.values());
                if (CollectionUtil.isNotEmpty(goodsUpdateList)) {
                    goodsDao.updateGoodsCount(goodsUpdateList);
                    // 更新大规格进价
                    if (ObjectUtil.notEqual(GoodsInPriceTypeEnums.BATCH.getCode(), shopsConfig.getGoodsInPriceType())) {
                        goodsDao.updateGoodsInPrice(goodsUpdateList);
                    }
                }
                purResult.setStatus(PurResult.SUCCESS);
                purResult.setMsg("操作成功");

                // 发送mqtt通知，商品更新
                List<MqttForGoodsUpdate> goodsUpdates = goodsUpdateList.stream().map(v -> {
                    MqttForGoodsUpdate goodsUpdate = new MqttForGoodsUpdate();
                    goodsUpdate.setGoods_barcode(v.getGoodsBarcode());
                    goodsUpdate.setShop_unique(v.getShopUnique());
                    goodsUpdate.setGoodsChengType(v.getGoodsChengType());
                    goodsUpdate.setGoods_contain(v.getGoodsContain());
                    goodsUpdate.setGoods_in_price(v.getGoodsInPrice());
                    goodsUpdate.setGoods_standard(v.getGoodsStandard());
                    goodsUpdate.setGoods_name(v.getGoodsName());
                    goodsUpdate.setGoods_alias(v.getGoodsAlias());
                    goodsUpdate.setGoods_brand(v.getGoodsBrand());
                    goodsUpdate.setPc_shelf_state(v.getPcShelfState());
                    goodsUpdate.setShelf_state(v.getShelfState());
                    goodsUpdate.setGoods_sale_price(v.getGoodsSalePrice());
                    goodsUpdate.setForeign_key(v.getForeignKey());
                    goodsUpdate.setGoods_cus_price(v.getGoodsCusPrice());
                    goodsUpdate.setGoods_kind_unique(v.getGoodsKindUnique());
                    goodsUpdate.setGoods_web_sale_price(v.getGoodsWebSalePrice());
                    if (null == v.getUpdateTime()) {
                        v.setUpdateTime(DateUtil.date());
                    }
                    goodsUpdate.setUpdate_time(DateUtil.formatDateTime(v.getUpdateTime()));
                    goodsUpdate.setGoods_count(v.getGoodsCount());
                    goodsUpdate.setGoods_unit(v.getGoodsUnit());
                    goodsUpdate.setSupplierUnique(v.getDefaultSupplierUnique());
                    return goodsUpdate;
                }).collect(Collectors.toList());
                ThreadUtil.execAsync(() -> {
                    mqttService.sendGoodsUpdate(goodsUpdates, String.valueOf(inStockParam.getShopUnique()));
                });
            }
        } else {
            List<InStockGoodsParam> goodsList = inStockParam.getGoodsList();
            BigDecimal totalCount = BigDecimal.ZERO;
            BigDecimal totalAmount = BigDecimal.ZERO;
            List<ShopStock> shopStockList = new ArrayList<>();
            for (InStockGoodsParam goodsParam : goodsList) {
                ShopStock shopStock = new ShopStock();
                shopStock.setGoodsBarcode(goodsParam.getGoodsBarcode());
                shopStock.setGoodsCount(goodsParam.getGoodsCount());
                shopStock.setStockCount(BigDecimal.ZERO);
                shopStock.setStockType(StockTypeEnum.INSTORAGE.getValue());
                shopStock.setStockTime(currentDate);
                shopStock.setShopUnique(inStockParam.getShopUnique());
                shopStock.setStockResource(shopStockDetail.getStockResource());
                shopStock.setListUnique(shopStockDetail.getListUnique());
                shopStock.setStockPrice(goodsParam.getStockPrice());
                shopStock.setStockOrigin(shopStockDetail.getStockOrigin());
                shopStock.setStaffId(shopStockDetail.getStaffId());
                shopStock.setGoodsProd(goodsParam.getGoodsProd());
                shopStock.setGoodsLife(goodsParam.getGoodsLife());
                shopStock.setGoodsExp(goodsParam.getGoodsExp());
                shopStock.setGoodsAvgOutPrice(BigDecimal.ZERO);
                totalCount = NumberUtil.add(totalCount, shopStock.getGoodsCount());
                totalAmount = NumberUtil.add(totalAmount, NumberUtil.mul(shopStock.getStockPrice(), shopStock.getGoodsCount()));
                shopStock.setStockCount(BigDecimal.ZERO);
                shopStockList.add(shopStock);
            }
            shopStockDetail.setTotalAmount(totalAmount);
            shopStockDetail.setTotalCount(totalCount);
            int n = shopStockDetailMapper.insertShopStockDetail(shopStockDetail);
            if (n > 0) {
                shopStockMapper.insertBatch(shopStockList);
                purResult.setStatus(PurResult.SUCCESS);
                purResult.setMsg("操作成功");
            }
        }
        return purResult;
    }

    @Override
    public PurResult listPage(ShopStockDetailQueryParam params) {
        PurResult result = new PurResult();
        result.setStatus(1);
        result.setMsg("查询成功");
        List<ShopStockDetailVO> list = shopStockDetailMapper.listPage(params);
        if (CollectionUtil.isNotEmpty(list)) {
            ShopStaff shopStaff = new ShopStaff();
            shopStaff.setShopUnique(Long.parseLong(params.getShopUnique()));
            List<ShopStaff> shopStaffs = shopStaffMapper.findList(shopStaff);
            if (CollectionUtil.isNotEmpty(shopStaffs)) {
                Map<Long, String> shopStaffMap = shopStaffs.stream().collect(Collectors.toMap(ShopStaff::getStaffId, ShopStaff::getStaffName));
                list.stream().forEach(v -> {
                    if (ObjectUtil.isNotNull(v.getStaffId())) {
                        v.setStaffName(shopStaffMap.getOrDefault(v.getStaffId(), StrUtil.EMPTY));
                    }
                    if (ObjectUtil.isNotNull(v.getUpdateId())) {
                        v.setUpdateName(shopStaffMap.getOrDefault(v.getUpdateId(), StrUtil.EMPTY));
                    }
                    if (ObjectUtil.isNotNull(v.getAuditId())) {
                        v.setAuditName(shopStaffMap.getOrDefault(v.getAuditId(), StrUtil.EMPTY));
                    }
                });
            }
        }
        result.setRows(shopStockDetailMapper.listPage(params));
        result.setCount(shopStockDetailMapper.listPageCount(params));
        return result;
    }

    @Override
    public ShopsResult deleteStock(Map<String, Object> map) {
        ShopsResult result = new ShopsResult();
        stockDao.deleteStock(map);
        stockDao.deleteStockDetail(map);
        goodsSaleBatchMapper.deleteByListUnique(Long.parseLong(String.valueOf(map.get("shop_unique"))), String.valueOf(map.get("list_unique")));
        result.setStatus(1);
        return result;
    }

    @Override
    public ShopsResult queryStockDetail(Map<String, Object> map) {
        ShopsResult result = new ShopsResult();
        List<Map<String, Object>> list = stockDao.queryStockDetail(map);
        if (ObjectUtil.equals(map.get("stock_type"), StockTypeEnum.OUTSTORAGE.getValue())) {
            if (ObjectUtil.equals(GoodsInPriceTypeEnums.BATCH.getCode(), (Integer) map.get("goodsInPriceType"))) {
                GoodsSaleBatch queryParams = new GoodsSaleBatch();
                queryParams.setShopUnique(Long.parseLong(String.valueOf(map.get("shop_unique"))));
                queryParams.setStockListUnique(String.valueOf(map.get("list_unique")));
                List<GoodsSaleBatch> goodsSaleBatches = goodsSaleBatchMapper.findList(queryParams);
                if (CollectionUtil.isNotEmpty(goodsSaleBatches)) {
                    Map<String, List<GoodsSaleBatch>> saleBatchMap = goodsSaleBatches.stream().collect(Collectors.groupingBy(GoodsSaleBatch::getGoodsBarcode));
                    list.stream().forEach(v -> {
                        String goodsBarcode = (String) v.get("goods_barcode");
                        List<GoodsSaleBatch> goodsSaleBatchList = saleBatchMap.get(goodsBarcode);
                        if (CollectionUtil.isNotEmpty(goodsSaleBatchList)) {
                            List<Map<String, Object>> mapList = goodsSaleBatchList.stream().map(m -> {
                                Map<String, Object> batchM = new HashMap<>();
                                batchM.put("batchUnique", m.getBatchUnique());
                                batchM.put("goodsCount", m.getGoodsOutCount());
                                batchM.put("goodsContain", v.getOrDefault("goods_contain", BigDecimal.ONE));
                                return batchM;
                            }).collect(Collectors.toList());
                            v.put("batchUniqueList", mapList);
                        } else {
                            v.put("batchUniqueList", Collections.EMPTY_LIST);
                        }
                    });
                }
            }
        }
        result.setStatus(1);
        result.setMsg("查询成功");
        result.setData(list);
        return result;
    }

    @Override
    public ShopsResult addAuditStock(String list_unique, String shop_unique, String audit_status,
                                     String stock_type_code, String staff_id, String audit_content, HttpServletRequest request) {
        ShopsResult result = new ShopsResult(PurResult.FAIL.intValue(), "操作失败");
        Map<String, Object> params = new HashMap<String, Object>();
        params.put("list_unique", list_unique);
        params.put("shop_unique", shop_unique);
        params.put("stock_type", stock_type_code);
        params.put("audit_status", audit_status);
        //查询是否已经审核过
        ShopStockDetail shopStockDetail = shopStockDetailMapper.selectByListUnique(list_unique);
        if (ObjectUtil.isNull(shopStockDetail)) {
            result.setMsg("入库记录不存在");
            return result;
        }
        if (ObjectUtil.notEqual(AduitStatusEnum.WAITING.getValue(), shopStockDetail.getAuditStatus())) {
            result.setMsg("已审核，无需重复审核");
            return result;
        }
        Date currentDate = DateUtil.date();
        Long usrId = Long.parseLong(staff_id);
        List<GoodsBatch> goodsBatches = new ArrayList<>();
        List<ShopStock> shopStockList = new ArrayList<>();
        Map<String, GoodsEntity> goodsEntityMap = new HashMap<>();
        ShopsConfig shopsConfig = shopsConfigDao.selectByShopUnique(shop_unique);
        if (ObjectUtil.equals(AduitStatusEnum.APPROVED.getValue(), Integer.parseInt(audit_status))) { //审核通过
            shopStockDetail.setAuditStatus(AduitStatusEnum.APPROVED.getValue());
            ShopStock queryParam = new ShopStock();
            queryParam.setShopUnique(Long.parseLong(shopStockDetail.getShopUnique()));
            queryParam.setListUnique(shopStockDetail.getListUnique());
            shopStockList = shopStockMapper.findList(queryParam);
            if (CollectionUtil.isEmpty(shopStockList)) {
                result.setMsg("入库商品为空，无法完成审核");
                return result;
            }
            Map<String, BigDecimal> sourceEntityMap = new HashMap<>();
            List<ShopStockData> shopStockDataList = new ArrayList<>();
            for (ShopStock shopStock : shopStockList) {
                ShopStockData shopStockData = new ShopStockData();
                BeanUtil.copyProperties(shopStock, shopStockData);
                GoodsEntity goodsEntity = goodsEntityMap.get(shopStockData.getGoodsBarcode());
                Map<String, Object> goodsQuery = new HashMap<>();
                goodsQuery.put("shopUnique", shopStock.getShopUnique());
                goodsQuery.put("goodsBarcode", shopStock.getGoodsBarcode());
                if (ObjectUtil.isNull(goodsEntity)) {
                    goodsEntity = goodsDao.queryOneByParam(goodsQuery);
                    sourceEntityMap.put(goodsEntity.getGoodsBarcode(), goodsEntity.getGoodsCount());
                }
                if (ObjectUtil.isNull(goodsEntity)) {
                    result.setMsg("入库商品条码: " + shopStock.getGoodsBarcode() + ", 在商品表中不存在，无法完成审核");
                    return result;
                }
                shopStockData.setForeignKey(String.valueOf(goodsEntity.getForeignKey()));
                shopStockData.setGoodsContain(goodsEntity.getGoodsContain());
                BigDecimal goodsContain = goodsEntity.getGoodsContain();
                BigDecimal smallTotalCount;
                if (!StrUtil.equals(shopStockData.getGoodsBarcode(), shopStockData.getForeignKey())) {
                    //有多规格
                    GoodsEntity smallGoods = goodsEntityMap.get(shopStockData.getForeignKey());
                    if (ObjectUtil.isNull(smallGoods)) {
                        smallGoods = goodsDao.queryParantGoods(goodsQuery);
                        sourceEntityMap.put(smallGoods.getGoodsBarcode(), smallGoods.getGoodsCount());
                    }
                    BigDecimal smallInCount = NumberUtil.mul(shopStockData.getGoodsCount(), goodsContain);
                    if (ObjectUtil.isNotNull(smallGoods)) {
                        smallTotalCount = NumberUtil.add(smallGoods.getGoodsCount(), smallInCount);
                        if (ObjectUtil.isNotNull(shopsConfig) && ObjectUtil.equals(GoodsInPriceTypeEnums.AVG.getCode(), shopsConfig.getGoodsInPriceType())) {
                            // 移动加权平均价
                            BigDecimal totalPrice = NumberUtil.add(NumberUtil.mul(smallGoods.getGoodsInPrice(), smallGoods.getGoodsCount()), NumberUtil.mul(shopStockData.getStockPrice(), shopStockData.getGoodsCount()));
                            if (BigDecimal.ZERO.compareTo(smallGoods.getGoodsCount()) == 0) {
                                smallGoods.setGoodsInPrice(NumberUtil.div(shopStockData.getStockPrice(), goodsContain).setScale(2, RoundingMode.HALF_UP));
                            } else {
                                smallGoods.setGoodsInPrice(NumberUtil.div(totalPrice, smallTotalCount, 2, RoundingMode.HALF_UP));
                            }
                        } else if (ObjectUtil.isNotNull(shopsConfig) && ObjectUtil.equals(GoodsInPriceTypeEnums.CUSTOM.getCode(), shopsConfig.getGoodsInPriceType())) {
                            // 新价格覆盖老价格
                            smallGoods.setGoodsInPrice(NumberUtil.div(shopStockData.getStockPrice(), goodsContain, 2, RoundingMode.HALF_UP));
                        }
                        smallGoods.setGoodsCount(smallTotalCount);
                        goodsEntityMap.put(smallGoods.getGoodsBarcode(), smallGoods);
                    } else {
                        smallTotalCount = NumberUtil.add(goodsEntity.getGoodsCount(), smallInCount);
                        if (ObjectUtil.isNotNull(shopsConfig) && ObjectUtil.equals(GoodsInPriceTypeEnums.AVG.getCode(), shopsConfig.getGoodsInPriceType())) {
                            // 移动加权平均价
                            BigDecimal totalPrice = NumberUtil.add(NumberUtil.mul(goodsEntity.getGoodsInPrice(), goodsEntity.getGoodsCount()), NumberUtil.mul(shopStockData.getStockPrice(), shopStockData.getGoodsCount()));
                            if (BigDecimal.ZERO.compareTo(goodsEntity.getGoodsCount()) == 0) {
                                goodsEntity.setGoodsInPrice(shopStockData.getStockPrice());
                            } else {
                                goodsEntity.setGoodsInPrice(NumberUtil.div(totalPrice, smallTotalCount, 2, RoundingMode.HALF_UP));
                            }
                        } else if (ObjectUtil.isNotNull(shopsConfig) && ObjectUtil.equals(GoodsInPriceTypeEnums.CUSTOM.getCode(), shopsConfig.getGoodsInPriceType())) {
                            goodsEntity.setGoodsInPrice(shopStockData.getStockPrice());
                        }
                        goodsEntity.setGoodsCount(smallTotalCount);
                        goodsEntityMap.put(goodsEntity.getGoodsBarcode(), goodsEntity);
                    }
                } else {
                    smallTotalCount = NumberUtil.add(goodsEntity.getGoodsCount(), shopStockData.getGoodsCount());
                    if (ObjectUtil.isNotNull(shopsConfig) && ObjectUtil.equals(GoodsInPriceTypeEnums.AVG.getCode(), shopsConfig.getGoodsInPriceType())) {
                        // 移动加权平均价
                        BigDecimal totalPrice = NumberUtil.add(NumberUtil.mul(goodsEntity.getGoodsInPrice(), goodsEntity.getGoodsCount()), NumberUtil.mul(shopStockData.getStockPrice(), shopStockData.getGoodsCount()));
                        if (BigDecimal.ZERO.compareTo(goodsEntity.getGoodsCount()) == 0) {
                            goodsEntity.setGoodsInPrice(shopStockData.getStockPrice());
                        } else {
                            goodsEntity.setGoodsInPrice(NumberUtil.div(totalPrice, smallTotalCount, 2, RoundingMode.HALF_UP));
                        }
                    } else if (ObjectUtil.isNotNull(shopsConfig) && ObjectUtil.equals(GoodsInPriceTypeEnums.CUSTOM.getCode(), shopsConfig.getGoodsInPriceType())) {
                        goodsEntity.setGoodsInPrice(shopStockData.getStockPrice());
                    }
                    goodsEntity.setGoodsCount(smallTotalCount);
                    goodsEntityMap.put(goodsEntity.getGoodsBarcode(), goodsEntity);
                }
                shopStockDataList.add(shopStockData);
            }
            shopStockList = new ArrayList<>();
            int index = 0;
            for (ShopStockData shopStockData : shopStockDataList) {
                index++;
                ShopStock shopStock = new ShopStock();
                BeanUtil.copyProperties(shopStockData, shopStock);
                BigDecimal smallCount = goodsEntityMap.get(shopStockData.getForeignKey()).getGoodsCount();
                if (StrUtil.equals(shopStockData.getGoodsBarcode(), shopStockData.getForeignKey())) {
                    shopStock.setStockCount(smallCount);
                } else {
                    shopStock.setStockCount(NumberUtil.div(smallCount, shopStockData.getGoodsContain(), 0, RoundingMode.DOWN));
                }
                shopStockList.add(shopStock);

                if (ObjectUtil.isNotNull(shopsConfig) && ObjectUtil.equals(GoodsInPriceTypeEnums.BATCH.getCode(), shopsConfig.getGoodsInPriceType())) {
                    // 批次方式
                    GoodsBatch goodsBatch = new GoodsBatch();
                    goodsBatch.setShopUnique(shopStock.getShopUnique());
                    goodsBatch.setBatchUnique(StrUtil.concat(true, String.valueOf(currentDate.getTime()), String.format("%03d", index)));
                    goodsBatch.setStockListUnique(shopStock.getListUnique());
                    goodsBatch.setGoodsBarcode(shopStockData.getForeignKey());
                    goodsBatch.setGoodsInPrice(NumberUtil.div(shopStockData.getStockPrice(), shopStockData.getGoodsContain(), 2, RoundingMode.HALF_UP));
                    goodsBatch.setGoodsInCount(NumberUtil.mul(shopStockData.getGoodsCount(), shopStockData.getGoodsContain()));
                    goodsBatch.setGoodsCount(goodsBatch.getGoodsInCount());
                    // 冲销掉负库存
                    BigDecimal sourceCount = sourceEntityMap.get(goodsBatch.getGoodsBarcode());
                    if (ObjectUtil.isNotNull(sourceCount)) {
                        if (BigDecimal.ZERO.compareTo(sourceCount) > 0) {
                            BigDecimal newCount = NumberUtil.add(goodsBatch.getGoodsInCount(), sourceCount);
                            if (BigDecimal.ZERO.compareTo(newCount) > 0) {
                                goodsBatch.setGoodsCount(BigDecimal.ZERO);
                            } else {
                                goodsBatch.setGoodsCount(newCount);
                            }
                            sourceEntityMap.put(goodsBatch.getGoodsBarcode(), newCount);
                        }
                    }
                    goodsBatch.setGoodsProd(shopStock.getGoodsProd());
                    goodsBatch.setGoodsLife(shopStock.getGoodsLife());
                    goodsBatch.setGoodsExp(shopStock.getGoodsExp());
                    goodsBatch.setCreateTime(currentDate);
                    goodsBatch.setCreateId(usrId);
                    goodsBatch.setUpdateId(goodsBatch.getCreateId());
                    goodsBatch.setUpdateTime(currentDate);
                    goodsBatch.setGoodsContain(shopStockData.getGoodsContain());
                    goodsBatch.setSourceBarcode(shopStockData.getGoodsBarcode());
                    goodsBatches.add(goodsBatch);
                }
            }

            // 发送mqtt通知，商品更新
            List<MqttForGoodsUpdate> goodsUpdates = goodsEntityMap.values().stream().map(v -> {
                MqttForGoodsUpdate goodsUpdate = new MqttForGoodsUpdate();
                goodsUpdate.setGoods_barcode(v.getGoodsBarcode());
                goodsUpdate.setShop_unique(v.getShopUnique());
                goodsUpdate.setGoodsChengType(v.getGoodsChengType());
                goodsUpdate.setGoods_contain(v.getGoodsContain());
                goodsUpdate.setGoods_in_price(v.getGoodsInPrice());
                goodsUpdate.setGoods_standard(v.getGoodsStandard());
                goodsUpdate.setGoods_name(v.getGoodsName());
                goodsUpdate.setGoods_alias(v.getGoodsAlias());
                goodsUpdate.setGoods_brand(v.getGoodsBrand());
                goodsUpdate.setPc_shelf_state(v.getPcShelfState());
                goodsUpdate.setShelf_state(v.getShelfState());
                goodsUpdate.setGoods_sale_price(v.getGoodsSalePrice());
                goodsUpdate.setForeign_key(v.getForeignKey());
                goodsUpdate.setGoods_cus_price(v.getGoodsCusPrice());
                goodsUpdate.setGoods_kind_unique(v.getGoodsKindUnique());
                goodsUpdate.setGoods_web_sale_price(v.getGoodsWebSalePrice());
                if (null == v.getUpdateTime()) {
                    v.setUpdateTime(DateUtil.date());
                }
                goodsUpdate.setUpdate_time(DateUtil.formatDateTime(v.getUpdateTime()));
                goodsUpdate.setGoods_count(v.getGoodsCount());
                goodsUpdate.setGoods_unit(v.getGoodsUnit());
                goodsUpdate.setSupplierUnique(v.getDefaultSupplierUnique());
                return goodsUpdate;
            }).collect(Collectors.toList());
            ThreadUtil.execAsync(() -> {
                mqttService.sendGoodsUpdate(goodsUpdates, shop_unique);
            });
        } else {
            shopStockDetail.setAuditStatus(AduitStatusEnum.REJECTED.getValue());
        }
        shopStockDetail.setAuditId(usrId);
        shopStockDetail.setAuditTime(currentDate);
        shopStockDetail.setAuditContent(audit_content);
        shopStockDetail.setUpdateId(usrId);
        shopStockDetail.setUpdateTime(currentDate);
        int n = shopStockDetailMapper.updateById(shopStockDetail);
        if (n > 0) {
            if (CollectionUtil.isNotEmpty(shopStockList)) {
                shopStockMapper.updateBatchGoodsCount(shopStockList);
            }
            if (CollectionUtil.isNotEmpty(goodsBatches)) {
                goodsBatchMapper.insertBatch(goodsBatches);
            }
            List<GoodsEntity> goodsUpdateList = new ArrayList<>(goodsEntityMap.values());
            if (CollectionUtil.isNotEmpty(goodsUpdateList)) {
                goodsDao.updateGoodsCount(goodsUpdateList);
                // 更新大规格进价
                if (ObjectUtil.notEqual(GoodsInPriceTypeEnums.BATCH.getCode(), shopsConfig.getGoodsInPriceType())) {
                    goodsDao.updateGoodsInPrice(goodsUpdateList);
                }
            }
            result.setStatus(PurResult.SUCCESS.intValue());
            result.setMsg("操作成功");
        }
        return result;
    }

    @Override
    public PurResult editIntoStock(InStockParam inStockParam) {
        PurResult result = new PurResult(PurResult.FAIL, "操作失败");
        ShopStockDetail shopStockDetail = shopStockDetailMapper.selectByListUnique(inStockParam.getListUnique());
        if (ObjectUtil.isNull(shopStockDetail)) {
            result.setMsg("入库单不存在");
        }
        shopStockMapper.deleteByListUnique(inStockParam.getListUnique());
        shopStockDetail.setSourceUnique(inStockParam.getSourceUnique());
        shopStockDetail.setStockRemarks(inStockParam.getStockRemarks());
        shopStockDetail.setUpdateId(inStockParam.getUserId());
        Date currentDate = DateUtil.date();
        shopStockDetail.setUpdateTime(currentDate);
        shopStockDetail.setSupplierUnique(inStockParam.getSupplierUnique());
        final BigDecimal[] totalPrice = {BigDecimal.ZERO};
        final BigDecimal[] totalCount = {BigDecimal.ZERO};
        List<ShopStock> shopStockList = inStockParam.getGoodsList().stream().map(v -> {
            ShopStock shopStock = new ShopStock();
            shopStock.setGoodsBarcode(v.getGoodsBarcode());
            shopStock.setGoodsCount(v.getGoodsCount());
            shopStock.setStockCount(BigDecimal.ZERO);
            shopStock.setStockType(shopStockDetail.getStockType());
            shopStock.setStockTime(currentDate);
            shopStock.setShopUnique(Long.parseLong(shopStockDetail.getShopUnique()));
            shopStock.setStockResource(shopStockDetail.getStockResource());
            shopStock.setListUnique(shopStockDetail.getListUnique());
            shopStock.setStockPrice(v.getStockPrice());
            shopStock.setStockOrigin(shopStockDetail.getStockOrigin());
            shopStock.setStaffId(inStockParam.getUserId());
            shopStock.setGoodsProd(v.getGoodsProd());
            shopStock.setGoodsLife(v.getGoodsLife());
            shopStock.setGoodsExp(v.getGoodsExp());
            totalPrice[0] = NumberUtil.add(totalPrice[0], NumberUtil.mul(shopStock.getStockPrice(), shopStock.getGoodsCount()));
            totalCount[0] = NumberUtil.add(totalCount[0], shopStock.getGoodsCount());
            return shopStock;
        }).collect(Collectors.toList());
        shopStockDetail.setTotalCount(totalCount[0]);
        shopStockDetail.setTotalAmount(totalPrice[0]);
        shopStockDetailMapper.updateById(shopStockDetail);
        shopStockMapper.insertBatch(shopStockList);
        result.setStatus(PurResult.SUCCESS);
        result.setMsg("操作成功");
        return result;
    }

    @Override
    @Transactional(rollbackFor = {Exception.class})
    public PurResult addOutStock(OutStockParam outStockParam) {
        PurResult purResult = new PurResult();
        //校验出库单号是否存在
        ShopStockDetail shopStockDetail = shopStockDetailMapper.selectByListUnique(outStockParam.getListUnique());
        if (ObjectUtil.isNotNull(shopStockDetail)) {
            purResult.setStatus(PurResult.FAIL.intValue());
            purResult.setMsg("出库单号已存在");
            return purResult;
        }
        Date currentDate = DateUtil.date();
        ShopsConfig shopsConfig = shopsConfigDao.selectByShopUnique(String.valueOf(outStockParam.getShopUnique()));
        shopStockDetail = new ShopStockDetail();
        shopStockDetail.setShopUnique(String.valueOf(outStockParam.getShopUnique()));
        shopStockDetail.setListUnique(outStockParam.getListUnique());
        shopStockDetail.setSourceUnique(outStockParam.getSourceUnique());
        shopStockDetail.setStockKind(StockKindEnum.INIT.getValue());
        shopStockDetail.setStockRemarks(outStockParam.getStockRemarks());
        shopStockDetail.setStockType(StockTypeEnum.OUTSTORAGE.getValue());
        shopStockDetail.setStockResource(outStockParam.getStockResource());
        if (ObjectUtil.isNull(shopStockDetail.getStockResource())) {
            shopStockDetail.setStockResource(StockReourceEnum.BY_HAND.getValue());
        }
        shopStockDetail.setStockOrigin(StockOriginEnum.WEB.getValue());
        shopStockDetail.setStaffId(outStockParam.getUserId());
        shopStockDetail.setStockTime(currentDate);
        shopStockDetail.setAuditStatus(AduitStatusEnum.WAITING.getValue());
        shopStockDetail.setUpdateTime(currentDate);
        shopStockDetail.setUpdateId(outStockParam.getUserId());
        if (ObjectUtil.equals(StockReourceEnum.BY_ORDER.getValue(), shopStockDetail.getStockResource()) || (ObjectUtil.isNotNull(shopsConfig) && ObjectUtil.equals(Integer.valueOf(0), shopsConfig.getIsIoboundInspect()))) {
            shopStockDetail.setAuditStatus(AduitStatusEnum.APPROVED.getValue());
            shopStockDetail.setAuditId(outStockParam.getUserId());
            shopStockDetail.setAuditTime(currentDate);
        }
        List<ShopStock> shopStockList = new ArrayList<>();
        BigDecimal totalCount = BigDecimal.ZERO, totalPrice = BigDecimal.ZERO;
        Map<String, GoodsEntity> goodsEntityMap = new HashMap<>();
        if (ObjectUtil.equals(AduitStatusEnum.APPROVED.getValue(), shopStockDetail.getAuditStatus())) {
            //审核通过
            // 处理商品库存
            outStockParam.getGoodsList().stream().forEach(goodsParam -> {
                Map<String, Object> goodsQuery = new HashMap<>();
                goodsQuery.put("shopUnique", outStockParam.getShopUnique());
                goodsQuery.put("goodsBarcode", goodsParam.getGoodsBarcode());
                GoodsEntity goodsEntity = goodsEntityMap.get(goodsParam.getGoodsBarcode());
                if (ObjectUtil.isNull(goodsEntity)) {
                    goodsEntity = goodsDao.queryOneByParam(goodsQuery);
                    if (ObjectUtil.isNull(goodsEntity)) {
                        throw new RuntimeException("商品条码: " + goodsParam.getGoodsBarcode() + " 对应商品不存在");
                    }
                }
                BigDecimal godsContain = goodsEntity.getGoodsContain();
                goodsParam.setGoodsContain(godsContain);
                goodsParam.setForeignKey(String.valueOf(goodsEntity.getForeignKey()));
                goodsParam.setGoodsAvgPrice(NumberUtil.mul(goodsEntity.getGoodsInPrice(), goodsEntity.getGoodsContain()));
                if (!StrUtil.equals(goodsParam.getGoodsBarcode(), goodsParam.getForeignKey())) {
                    // 多规格
                    GoodsEntity smallGoods = goodsEntityMap.get(goodsEntity.getForeignKey());
                    if (ObjectUtil.isNull(smallGoods)) {
                        smallGoods = goodsDao.queryParantGoods(goodsQuery);
                    }
                    if (ObjectUtil.isNotNull(smallGoods)) {
                        BigDecimal smallCount = NumberUtil.mul(goodsParam.getGoodsCount(), godsContain);
                        smallGoods.setGoodsCount(NumberUtil.sub(smallGoods.getGoodsCount(), smallCount));
                        goodsEntityMap.put(smallGoods.getGoodsBarcode(), smallGoods);
                    }
                } else {
                    goodsEntity.setGoodsCount(NumberUtil.sub(goodsEntity.getGoodsCount(), goodsParam.getGoodsCount()));
                    goodsEntityMap.put(goodsEntity.getGoodsBarcode(), goodsEntity);
                }
            });

            for (OutStockGoodsParam goodsParam : outStockParam.getGoodsList()) {
                ShopStock shopStock = new ShopStock();
                shopStock.setListUnique(shopStockDetail.getListUnique());
                shopStock.setGoodsBarcode(goodsParam.getGoodsBarcode());
                shopStock.setGoodsCount(goodsParam.getGoodsCount());
                totalCount = NumberUtil.add(totalCount, shopStock.getGoodsCount());
                shopStock.setStockPrice(goodsParam.getStockPrice());
                totalPrice = NumberUtil.add(totalPrice, NumberUtil.mul(shopStock.getGoodsCount(), shopStock.getStockPrice()));
                shopStock.setStockType(StockTypeEnum.OUTSTORAGE.getValue());
                shopStock.setStockTime(shopStockDetail.getStockTime());
                shopStock.setShopUnique(outStockParam.getShopUnique());
                shopStock.setStockResource(shopStockDetail.getStockResource());
                shopStock.setStockOrigin(shopStockDetail.getStockOrigin());
                shopStock.setStaffId(shopStockDetail.getStaffId());
                shopStock.setGoodsAvgOutPrice(BigDecimal.ZERO);
                shopStock.setStockCount(BigDecimal.ZERO);
                BigDecimal smallCount = goodsEntityMap.get(goodsParam.getForeignKey()).getGoodsCount();
                if (StrUtil.equals(goodsParam.getGoodsBarcode(), goodsParam.getForeignKey())) {
                    shopStock.setStockCount(smallCount);
                } else {
                    shopStock.setStockCount(NumberUtil.div(smallCount, goodsParam.getGoodsContain(), 0, RoundingMode.DOWN));
                }
                shopStockList.add(shopStock);
            }
            if (ObjectUtil.isNotNull(shopsConfig) && ObjectUtil.equals(GoodsInPriceTypeEnums.BATCH.getCode(), shopsConfig.getGoodsInPriceType())) {
                Map<String, BigDecimal> avgPriceMap = goodsSaleBatchService.addAndAuditBatch(shopStockDetail, outStockParam);
                shopStockList.stream().forEach(v -> {
                    BigDecimal avgPrice = avgPriceMap.get(v.getGoodsBarcode());
                    if (ObjectUtil.isNotNull(avgPrice)) {
                        v.setGoodsAvgOutPrice(avgPrice);
                    }
                });
            }

            // 发送mqtt通知，商品更新
            List<MqttForGoodsUpdate> goodsUpdates = goodsEntityMap.values().stream().map(v -> {
                MqttForGoodsUpdate goodsUpdate = new MqttForGoodsUpdate();
                goodsUpdate.setGoods_barcode(v.getGoodsBarcode());
                goodsUpdate.setShop_unique(v.getShopUnique());
                goodsUpdate.setGoodsChengType(v.getGoodsChengType());
                goodsUpdate.setGoods_contain(v.getGoodsContain());
                goodsUpdate.setGoods_in_price(v.getGoodsInPrice());
                goodsUpdate.setGoods_standard(v.getGoodsStandard());
                goodsUpdate.setGoods_name(v.getGoodsName());
                goodsUpdate.setGoods_alias(v.getGoodsAlias());
                goodsUpdate.setGoods_brand(v.getGoodsBrand());
                goodsUpdate.setPc_shelf_state(v.getPcShelfState());
                goodsUpdate.setShelf_state(v.getShelfState());
                goodsUpdate.setGoods_sale_price(v.getGoodsSalePrice());
                goodsUpdate.setForeign_key(v.getForeignKey());
                goodsUpdate.setGoods_cus_price(v.getGoodsCusPrice());
                goodsUpdate.setGoods_kind_unique(v.getGoodsKindUnique());
                goodsUpdate.setGoods_web_sale_price(v.getGoodsWebSalePrice());
                if (null == v.getUpdateTime()) {
                    v.setUpdateTime(DateUtil.date());
                }
                goodsUpdate.setUpdate_time(DateUtil.formatDateTime(v.getUpdateTime()));
                goodsUpdate.setGoods_count(v.getGoodsCount());
                goodsUpdate.setGoods_unit(v.getGoodsUnit());
                goodsUpdate.setSupplierUnique(v.getDefaultSupplierUnique());
                return goodsUpdate;
            }).collect(Collectors.toList());
            ThreadUtil.execAsync(() -> {
                mqttService.sendGoodsUpdate(goodsUpdates, String.valueOf(outStockParam.getShopUnique()));
            });
        } else {
            // 待审核
            outStockParam.getGoodsList().stream().forEach(goodsParam -> {
                Map<String, Object> goodsQuery = new HashMap<>();
                goodsQuery.put("shopUnique", outStockParam.getShopUnique());
                goodsQuery.put("goodsBarcode", goodsParam.getGoodsBarcode());
                GoodsEntity goodsEntity = goodsEntityMap.get(goodsParam.getGoodsBarcode());
                if (ObjectUtil.isNull(goodsEntity)) {
                    goodsEntity = goodsDao.queryOneByParam(goodsQuery);
                    if (ObjectUtil.isNull(goodsEntity)) {
                        throw new RuntimeException("商品条码: " + goodsParam.getGoodsBarcode() + " 对应商品不存在");
                    }
                }
                BigDecimal godsContain = goodsEntity.getGoodsContain();
                goodsParam.setGoodsContain(godsContain);
                goodsParam.setGoodsAvgPrice(goodsEntity.getGoodsInPrice());
                goodsParam.setForeignKey(String.valueOf(goodsEntity.getForeignKey()));
            });

            for (OutStockGoodsParam goodsParam : outStockParam.getGoodsList()) {
                ShopStock shopStock = new ShopStock();
                shopStock.setListUnique(shopStockDetail.getListUnique());
                shopStock.setGoodsBarcode(goodsParam.getGoodsBarcode());
                shopStock.setGoodsCount(goodsParam.getGoodsCount());
                totalCount = NumberUtil.add(totalCount, shopStock.getGoodsCount());
                shopStock.setStockPrice(goodsParam.getStockPrice());
                totalPrice = NumberUtil.add(totalPrice, NumberUtil.mul(shopStock.getGoodsCount(), shopStock.getStockPrice()));
                shopStock.setStockType(StockTypeEnum.OUTSTORAGE.getValue());
                shopStock.setStockTime(shopStockDetail.getStockTime());
                shopStock.setShopUnique(outStockParam.getShopUnique());
                shopStock.setStockResource(shopStockDetail.getStockResource());
                shopStock.setStockOrigin(shopStockDetail.getStockOrigin());
                shopStock.setStaffId(shopStockDetail.getStaffId());
                shopStock.setGoodsAvgOutPrice(BigDecimal.ZERO);
                shopStock.setStockCount(BigDecimal.ZERO);
                shopStockList.add(shopStock);
            }
        }

        if (ObjectUtil.isNotNull(shopsConfig) && ObjectUtil.equals(GoodsInPriceTypeEnums.BATCH.getCode(), shopsConfig.getGoodsInPriceType())) {
            goodsSaleBatchService.addAndWaitAuditBatch(shopStockDetail, outStockParam);
        }

        shopStockDetail.setTotalCount(totalCount);
        shopStockDetail.setTotalAmount(totalPrice);
        int n = shopStockDetailMapper.insertShopStockDetail(shopStockDetail);
        if (n > 0) {
            if (CollectionUtil.isNotEmpty(shopStockList)) {
                shopStockMapper.insertBatch(shopStockList);
            }
            List<GoodsEntity> goodsEntityList = new ArrayList<>(goodsEntityMap.values());
            if (CollectionUtil.isNotEmpty(goodsEntityList)) {
                goodsDao.updateGoodsCount(goodsEntityList);
            }
            purResult.setStatus(PurResult.SUCCESS.intValue());
            purResult.setMsg("操作成功");
        }
        return purResult;
    }

    @Override
    @Transactional(rollbackFor = {Exception.class})
    public PurResult editOutStock(OutStockParam outStockParam) {
        PurResult purResult = new PurResult();
        //校验出库单号是否存在
        ShopStockDetail shopStockDetail = shopStockDetailMapper.selectByListUnique(outStockParam.getListUnique());
        if (ObjectUtil.isNull(shopStockDetail)) {
            purResult.setStatus(PurResult.FAIL.intValue());
            purResult.setMsg("出库单号不存在");
            return purResult;
        }
        goodsSaleBatchMapper.deleteByListUnique(outStockParam.getShopUnique(), outStockParam.getListUnique());
        shopStockMapper.deleteByListUnique(outStockParam.getListUnique());
        Date currentDate = DateUtil.date();
        ShopsConfig shopsConfig = shopsConfigDao.selectByShopUnique(String.valueOf(outStockParam.getShopUnique()));
        shopStockDetail.setUpdateTime(currentDate);
        shopStockDetail.setUpdateId(outStockParam.getUserId());
        List<ShopStock> shopStockList = new ArrayList<>();
        BigDecimal totalCount = BigDecimal.ZERO, totalPrice = BigDecimal.ZERO;
        Map<String, GoodsEntity> goodsEntityMap = new HashMap<>();
        outStockParam.getGoodsList().stream().forEach(goodsParam -> {
            Map<String, Object> goodsQuery = new HashMap<>();
            goodsQuery.put("shopUnique", outStockParam.getShopUnique());
            goodsQuery.put("goodsBarcode", goodsParam.getGoodsBarcode());
            GoodsEntity goodsEntity = goodsEntityMap.get(goodsParam.getGoodsBarcode());
            if (ObjectUtil.isNull(goodsEntity)) {
                goodsEntity = goodsDao.queryOneByParam(goodsQuery);
                if (ObjectUtil.isNull(goodsEntity)) {
                    throw new RuntimeException("商品条码: " + goodsParam.getGoodsBarcode() + " 对应商品不存在");
                }
            }
            BigDecimal godsContain = goodsEntity.getGoodsContain();
            goodsParam.setGoodsContain(godsContain);
            goodsParam.setGoodsAvgPrice(goodsEntity.getGoodsInPrice());
            goodsParam.setForeignKey(String.valueOf(goodsEntity.getForeignKey()));
        });


        for (OutStockGoodsParam goodsParam : outStockParam.getGoodsList()) {
            ShopStock shopStock = new ShopStock();
            shopStock.setListUnique(shopStockDetail.getListUnique());
            shopStock.setGoodsBarcode(goodsParam.getGoodsBarcode());
            shopStock.setGoodsCount(goodsParam.getGoodsCount());
            totalCount = NumberUtil.add(totalCount, shopStock.getGoodsCount());
            shopStock.setStockPrice(goodsParam.getStockPrice());
            totalPrice = NumberUtil.add(totalPrice, NumberUtil.mul(shopStock.getGoodsCount(), shopStock.getStockPrice()));
            shopStock.setStockType(StockTypeEnum.OUTSTORAGE.getValue());
            shopStock.setStockTime(shopStockDetail.getStockTime());
            shopStock.setShopUnique(outStockParam.getShopUnique());
            shopStock.setStockResource(shopStockDetail.getStockResource());
            shopStock.setStockOrigin(shopStockDetail.getStockOrigin());
            shopStock.setStaffId(shopStockDetail.getStaffId());
            shopStock.setGoodsAvgOutPrice(BigDecimal.ZERO);
            shopStock.setStockCount(BigDecimal.ZERO);
            shopStockList.add(shopStock);
        }
        if (ObjectUtil.isNotNull(shopsConfig) && ObjectUtil.equals(GoodsInPriceTypeEnums.BATCH.getCode(), shopsConfig.getGoodsInPriceType())) {
            goodsSaleBatchService.addAndWaitAuditBatch(shopStockDetail, outStockParam);
        }
        shopStockDetail.setTotalCount(totalCount);
        shopStockDetail.setTotalAmount(totalPrice);
        int n = shopStockDetailMapper.updateById(shopStockDetail);
        if (n > 0) {
            if (CollectionUtil.isNotEmpty(shopStockList)) {
                shopStockMapper.insertBatch(shopStockList);
            }
            List<GoodsEntity> goodsEntityList = new ArrayList<>(goodsEntityMap.values());
            if (CollectionUtil.isNotEmpty(goodsEntityList)) {
                goodsDao.updateGoodsCount(goodsEntityList);
            }
            purResult.setStatus(PurResult.SUCCESS.intValue());
            purResult.setMsg("操作成功");
        }
        return purResult;
    }

    @Override
    public PurResult queryGoodsModifyPriceList(Map<String, Object> params) {
        PurResult result = new PurResult();
        List<Map<String, Object>> list = stockDao.queryGoodsModifyPriceList(params);
        result.setStatus(1);
        result.setMsg("查询成功");
        result.setCount(stockDao.queryGoodsModifyPriceListPageCount(params));
        result.setData(list);
        return result;
    }

    public List<Map<String, Object>> getGoodsModifyPriceList(Map<String, Object> params) {
        return stockDao.queryGoodsModifyPriceList(params);
    }

    @Override
    public PurResult addGoodsModify(String shop_list, String detailJson, String user_id, String shop_unique) {
        PurResult result = new PurResult();
        //生成订单号
        String list_unique = System.currentTimeMillis() + "" + (int) ((Math.random() * 9 + 1) * 10000);
        Map<String, Object> params = new HashMap<String, Object>();
        params.put("list_unique", list_unique);
        params.put("shop_unique", shop_unique);
        params.put("audit_status", 0);
        params.put("staff_id", user_id);
        params.put("shop_list", shop_list);
        stockDao.addGoodsModifyList(params);
        System.out.println("调价商品集合---" + detailJson);
        JSONArray array = JSONArray.fromObject(detailJson);
        for (int i = 0; i < array.size(); i++) {
            JSONObject temp = (JSONObject) array.get(i);
            String goods_barcode = temp.getString("goods_barcode");
            String goods_name = temp.getString("goods_name");
            double goods_sale_price = temp.getDouble("goods_sale_price");
            double goods_sale_price_modify = temp.getDouble("goods_sale_price_modify");
            double goods_cus_price = ObjectUtils.isEmpty(temp.getString("goods_cus_price")) ? 0d : temp.getDouble("goods_cus_price");
            double goods_cus_price_modify = temp.getDouble("goods_cus_price_modify");
            double goods_web_sale_price = ObjectUtils.isEmpty(temp.getString("goods_web_sale_price")) ? 0.0 : temp.getDouble("goods_web_sale_price");
            double goods_web_sale_price_modify = ObjectUtils.isEmpty(temp.getString("goods_web_sale_price_modify")) ? 0.0 : temp.getDouble("goods_web_sale_price_modify");
            Map<String, Object> map = new HashMap<String, Object>();
            map.put("list_unique", list_unique);
            map.put("goods_barcode", goods_barcode);
            map.put("goods_name", goods_name);
            map.put("goods_sale_price", goods_sale_price);
            map.put("goods_sale_price_modify", goods_sale_price_modify);
            map.put("goods_cus_price", goods_cus_price);
            map.put("goods_cus_price_modify", goods_cus_price_modify);
            map.put("goods_web_sale_price", goods_web_sale_price);
            map.put("goods_web_sale_price_modify", goods_web_sale_price_modify);
            //保存
            stockDao.addGoodsModify(map);

        }
        result.setStatus(1);
        return result;
    }

    @Override
    public ShopsResult deleteGoodsModify(Map<String, Object> map) {
        ShopsResult result = new ShopsResult();
        stockDao.deleteGoodsModify(map);
        stockDao.deleteGoodsModifyList(map);
        result.setStatus(1);
        return result;
    }

    @Override
    public ShopsResult queryGoodsModifyDetail(Map<String, Object> map) {
        ShopsResult result = new ShopsResult();
        List<Map<String, Object>> list = stockDao.queryGoodsModifyDetail(map);
        //查询审核状态
        Map<String, Object> data = stockDao.queryGoodsModifyList(map);
        result.setStatus(1);
        result.setMsg("查询成功");
        result.setData(list);
        String shop_list = (String) data.get("shop_list");
        shop_list = shop_list.substring(1, shop_list.length() - 1);
        String[] shops = shop_list.split(",");
        data.put("shops", shops);
        result.setCord(data);
        return result;
    }

    @Override
    public ShopsResult addAuditGoodsModify(String list_unique, String audit_status, String user_id, HttpServletRequest request) {
        ShopsResult result = new ShopsResult();
        Map<String, Object> params = new HashMap<String, Object>();
        params.put("list_unique", list_unique);
        params.put("audit_status", audit_status);
        params.put("user_id", user_id);
        stockDao.addAuditGoodsModify(params);
        if ("1".equals(audit_status)) {
            //查询需要更新的店铺
            Map<String, Object> data = stockDao.queryGoodsModifyList(params);
            String shop_list = (String) data.get("shop_list");
            shop_list = shop_list.substring(1, shop_list.length() - 1);
            String[] shops = shop_list.split(",");
            List<RecordGoodsOperParams> operParamsList = new ArrayList<>();
            for (String shop_unique : shops) {
                List<Map<String, Object>> list = stockDao.queryGoodsModifyDetail(params);
                //审核通过修改价格
                for (Map<String, Object> map : list) {
                    map.put("shop_unique", shop_unique);
                }
                //批量更新
                stockDao.updateBatchModifyPrice(list);

                for (Map<String, Object> goodsMap : list) {
                    RecordGoods sourceGoods = new RecordGoods();
                    RecordGoods resultGoods = new RecordGoods();
                    sourceGoods.setGoodsId(Integer.parseInt(goodsMap.get("goods_id").toString()));
                    sourceGoods.setShopUnique(Long.parseLong(shop_unique));
                    sourceGoods.setGoodsBarcode(goodsMap.get("goods_barcode").toString());
                    BeanUtil.copyProperties(sourceGoods, resultGoods);
                    if (ObjectUtil.isNotEmpty(goodsMap.get("goods_cus_price"))) {
                        sourceGoods.setGoodsCusPrice(goodsMap.get("goods_cus_price").toString());
                    }
                    if (ObjectUtil.isNotEmpty(goodsMap.get("goods_sale_price"))) {
                        sourceGoods.setGoodsSalePrice(new BigDecimal(goodsMap.get("goods_sale_price").toString()));
                    }
                    if (ObjectUtil.isNotEmpty(goodsMap.get("goods_in_price"))) {
                        sourceGoods.setGoodsInPrice(new BigDecimal(goodsMap.get("goods_in_price").toString()));
                    }
                    if (ObjectUtil.isNotEmpty(goodsMap.get("goods_web_sale_price"))) {
                        sourceGoods.setGoodsWebSalePrice(new BigDecimal(goodsMap.get("goods_web_sale_price").toString()));
                    }
                    if (ObjectUtil.isNotEmpty(goodsMap.get("goods_cus_price_modify"))) {
                        resultGoods.setGoodsCusPrice(goodsMap.get("goods_cus_price_modify").toString());
                    }
                    if (ObjectUtil.isNotEmpty(goodsMap.get("goods_sale_price_modify"))) {
                        resultGoods.setGoodsSalePrice(new BigDecimal(goodsMap.get("goods_sale_price_modify").toString()));
                    }
                    if (ObjectUtil.isNotEmpty(goodsMap.get("goods_in_price_modify"))) {
                        resultGoods.setGoodsInPrice(new BigDecimal(goodsMap.get("goods_in_price_modify").toString()));
                    }
                    if (ObjectUtil.isNotEmpty(goodsMap.get("goods_web_sale_price_modify"))) {
                        resultGoods.setGoodsWebSalePrice(new BigDecimal(goodsMap.get("goods_web_sale_price_modify").toString()));
                    }
                    //操作信息
                    RecordGoodsOper recordGoodsOper = new RecordGoodsOper();
                    recordGoodsOper.setGoodsId(Long.valueOf(resultGoods.getGoodsId()));
                    recordGoodsOper.setGoodsBarcode(resultGoods.getGoodsBarcode());
                    recordGoodsOper.setShopUnique(resultGoods.getShopUnique());
                    if (StrUtil.isNotBlank(user_id)) {
                        recordGoodsOper.setUserId(user_id);
                    }
                    recordGoodsOper.setOperType(OperTypeEnum.OPER_TYPE_UPDATE.getValue());
                    recordGoodsOper.setDeviceSource(DeviceSourceEnum.PC_WEB.getValue());
                    recordGoodsOper.setUserType(UserTypeEnum.SHOP_STAFF.getValue());
                    recordGoodsOper.setCreateTtime(DateUtil.formatDateTime(DateUtil.date()));
                    recordGoodsOper.setOperSource(OperSourceEnum.CHANGE_PRICE_ADJUST.getValue());
                    recordGoodsOper.setDeviceSourceMsg(DeviceSourceEnum.PC_WEB.getLabel());
                    RecordGoodsOperParams recordGoodsOperParams = new RecordGoodsOperParams();
                    recordGoodsOperParams.setSourceGoods(sourceGoods);
                    recordGoodsOperParams.setResultGoods(resultGoods);
                    recordGoodsOperParams.setRecordGoodsOper(recordGoodsOper);
                    operParamsList.add(recordGoodsOperParams);
                }
            }
            if (CollUtil.isNotEmpty(operParamsList)) {
                String host = UtilForJAVA.getHostByServerName(request.getServerName());
                String url = StringUtils.join(host,"/shopmanager/record/recordGoodsOperList.do");
                String json = com.alibaba.fastjson.JSONObject.toJSONString(operParamsList);
                System.out.println("-------------------同步商品操作记录，url:"+ url);
                System.out.println("-------------------同步商品操作记录，参数:"+ json);
                String resultMsg = HttpUtil.post(url, json);
                System.out.println("-------------------同步商品操作记录，返回值:"+ resultMsg);
            }
        }
        result.setStatus(1);
        result.setMsg("审核完成");
        return result;
    }

    @Override
    public PurResult updateGoodsModify(String shop_list, String detailJson, String user_id, String shop_unique,
                                       String list_unique) {
        PurResult result = new PurResult();
        Map<String, Object> params = new HashMap<String, Object>();
        params.put("list_unique", list_unique);
        params.put("shop_unique", shop_unique);
        params.put("audit_status", 0);
        params.put("staff_id", user_id);
        params.put("shop_list", shop_list);
        stockDao.updateGoodsModifyList(params);
        System.out.println("调价商品集合---" + detailJson);
        JSONArray array = JSONArray.fromObject(detailJson);
        for (int i = 0; i < array.size(); i++) {
            JSONObject temp = (JSONObject) array.get(i);
            String goods_barcode = temp.getString("goods_barcode");
            String goods_name = temp.getString("goods_name");
            double goods_sale_price = temp.getDouble("goods_sale_price");
            double goods_sale_price_modify = temp.getDouble("goods_sale_price_modify");
            double goods_cus_price = temp.getDouble("goods_cus_price");
            double goods_cus_price_modify = temp.getDouble("goods_cus_price_modify");
            Map<String, Object> map = new HashMap<String, Object>();
            map.put("list_unique", list_unique);
            map.put("goods_barcode", goods_barcode);
            map.put("goods_name", goods_name);
            map.put("goods_sale_price", goods_sale_price);
            map.put("goods_sale_price_modify", goods_sale_price_modify);
            map.put("goods_cus_price", goods_cus_price);
            map.put("goods_cus_price_modify", goods_cus_price_modify);
            //查询商品是否存在
            Map<String, Object> goods = stockDao.queryExistGoods(map);
            if (goods == null) {
                //保存
                stockDao.addGoodsModify(map);
            } else {
                stockDao.updateGoodsModify(map);
            }

        }
        result.setStatus(1);
        return result;
    }

    @Override
    public PurResult queryGoodsModifyInPriceList(Map<String, Object> params) {
        PurResult result = new PurResult();
        List<Map<String, Object>> list = stockDao.queryGoodsModifyInPriceList(params);
        result.setStatus(1);
        result.setMsg("查询成功");
        result.setCount(stockDao.queryGoodsModifyInPriceListPageCount(params));
        result.setData(list);
        return result;
    }

    @Override
    public PurResult addGoodsModifyInPrice(String shop_list, String detailJson, String user_id, String shop_unique) {
        PurResult result = new PurResult();
        //生成订单号
        String list_unique = System.currentTimeMillis() + "" + (int) ((Math.random() * 9 + 1) * 10000);
        Map<String, Object> params = new HashMap<String, Object>();
        params.put("list_unique", list_unique);
        params.put("shop_unique", shop_unique);
        params.put("audit_status", 0);
        params.put("staff_id", user_id);
        params.put("shop_list", shop_list);
        params.put("type", 2);
        stockDao.addGoodsModifyList(params);
        System.out.println("调价商品集合---" + detailJson);
        JSONArray array = JSONArray.fromObject(detailJson);
        for (int i = 0; i < array.size(); i++) {
            JSONObject temp = (JSONObject) array.get(i);
            String goods_barcode = temp.getString("goods_barcode");
            String goods_name = temp.getString("goods_name");
            double goods_sale_price = temp.getDouble("goods_sale_price");
            double goods_cus_price = ObjectUtils.isEmpty(temp.getString("goods_cus_price")) ? 0d : temp.getDouble("goods_cus_price");
            double goods_in_price = temp.getDouble("goods_in_price");
            double goods_in_price_modify = temp.getDouble("goods_in_price_modify");
            Map<String, Object> map = new HashMap<String, Object>();
            map.put("list_unique", list_unique);
            map.put("goods_barcode", goods_barcode);
            map.put("goods_name", goods_name);
            map.put("goods_sale_price", goods_sale_price);
            map.put("goods_cus_price", goods_cus_price);
            map.put("goods_in_price", goods_in_price);
            map.put("goods_in_price_modify", goods_in_price_modify);
            //保存
            stockDao.addGoodsModify(map);

        }
        result.setStatus(1);
        return result;
    }

    @Override
    public ShopsResult addAuditGoodsModifyInPrice(String list_unique, String audit_status, String user_id, HttpServletRequest request) {
        ShopsResult result = new ShopsResult();
        Map<String, Object> params = new HashMap<String, Object>();
        params.put("list_unique", list_unique);
        params.put("audit_status", audit_status);
        params.put("user_id", user_id);
        stockDao.addAuditGoodsModify(params);
        try {
            if ("1".equals(audit_status)) {
                //查询需要更新的店铺
                Map<String, Object> data = stockDao.queryGoodsModifyList(params);
                String shop_list = (String) data.get("shop_list");
                shop_list = shop_list.substring(1, shop_list.length() - 1);
                String[] shops = shop_list.split(",");
                List<RecordGoodsOperParams> operParamsList = new ArrayList<>();
                for (String shop_unique : shops) {
                    List<Map<String, Object>> list = stockDao.queryGoodsModifyDetail(params);
                    //审核通过修改价格
                    for (Map<String, Object> map : list) {
                        map.put("shop_unique", shop_unique);
                    }
                    //批量更新
                    stockDao.updateBatchModifyInPrice(list);
                    for (Map<String, Object> goodsMap : list) {
                        RecordGoods sourceGoods = new RecordGoods();
                        RecordGoods resultGoods = new RecordGoods();
                        sourceGoods.setGoodsId(Integer.parseInt(goodsMap.get("goods_id").toString()));
                        sourceGoods.setShopUnique(Long.parseLong(shop_unique));
                        sourceGoods.setGoodsBarcode(goodsMap.get("goods_barcode").toString());
                        BeanUtil.copyProperties(sourceGoods, resultGoods);
                        sourceGoods.setGoodsInPrice(new BigDecimal(goodsMap.get("goods_in_price").toString()));
                        resultGoods.setGoodsInPrice(new BigDecimal(goodsMap.get("goods_in_price_modify").toString()));
                        //操作信息
                        RecordGoodsOper recordGoodsOper = new RecordGoodsOper();
                        if (StrUtil.isNotBlank(user_id)) {
                            recordGoodsOper.setUserId(user_id);
                        }
                        recordGoodsOper.setGoodsId(Long.valueOf(resultGoods.getGoodsId()));
                        recordGoodsOper.setGoodsBarcode(resultGoods.getGoodsBarcode());
                        recordGoodsOper.setShopUnique(resultGoods.getShopUnique());
                        recordGoodsOper.setOperType(OperTypeEnum.OPER_TYPE_UPDATE.getValue());
                        recordGoodsOper.setDeviceSource(DeviceSourceEnum.PC_WEB.getValue());
                        recordGoodsOper.setUserType(UserTypeEnum.SHOP_STAFF.getValue());
                        recordGoodsOper.setCreateTtime(DateUtil.formatDateTime(DateUtil.date()));
                        recordGoodsOper.setOperSource(OperSourceEnum.CHANGE_PRICE_ADJUST.getValue());
                        recordGoodsOper.setDeviceSourceMsg(DeviceSourceEnum.PC_WEB.getLabel());
                        RecordGoodsOperParams recordGoodsOperParams = new RecordGoodsOperParams();
                        recordGoodsOperParams.setSourceGoods(sourceGoods);
                        recordGoodsOperParams.setResultGoods(resultGoods);
                        recordGoodsOperParams.setRecordGoodsOper(recordGoodsOper);
                        operParamsList.add(recordGoodsOperParams);
                    }
                }
                if (CollUtil.isNotEmpty(operParamsList)) {
                    String host = UtilForJAVA.getHostByServerName(request.getServerName());
                    String url = StringUtils.join(host,"/shopmanager/record/recordGoodsOperList.do");
                    String json = com.alibaba.fastjson.JSONObject.toJSONString(operParamsList);
                    System.out.println("-------------------同步商品操作记录，url:"+ url);
                    System.out.println("-------------------同步商品操作记录，参数:"+ json);
                    String resultMsg = HttpUtil.post(url, json);
                    System.out.println("-------------------同步商品操作记录，返回值:"+ resultMsg);
                }
            }
        } catch (NumberFormatException e) {
            e.printStackTrace();
        }
        result.setStatus(1);
        result.setMsg("审核完成");
        return result;
    }

    @Override
    public PurResult updateGoodsModifyInPrice(String shop_list, String detailJson, String user_id, String shop_unique,
                                              String list_unique) {
        PurResult result = new PurResult();
        Map<String, Object> params = new HashMap<String, Object>();
        params.put("list_unique", list_unique);
        params.put("shop_unique", shop_unique);
        params.put("audit_status", 0);
        params.put("staff_id", user_id);
        params.put("shop_list", shop_list);
        stockDao.updateGoodsModifyList(params);
        System.out.println("调价商品集合---" + detailJson);
        JSONArray array = JSONArray.fromObject(detailJson);
        for (int i = 0; i < array.size(); i++) {
            JSONObject temp = (JSONObject) array.get(i);
            String goods_barcode = temp.getString("goods_barcode");
            String goods_name = temp.getString("goods_name");
            double goods_sale_price = temp.getDouble("goods_sale_price");
            double goods_cus_price = temp.getDouble("goods_cus_price");
            double goods_in_price = temp.getDouble("goods_in_price");
            double goods_in_price_modify = temp.getDouble("goods_in_price_modify");
            Map<String, Object> map = new HashMap<String, Object>();
            map.put("list_unique", list_unique);
            map.put("goods_barcode", goods_barcode);
            map.put("goods_name", goods_name);
            map.put("goods_sale_price", goods_sale_price);
            map.put("goods_cus_price", goods_cus_price);
            map.put("goods_in_price", goods_in_price);
            map.put("goods_in_price_modify", goods_in_price_modify);
            //查询商品是否存在
            Map<String, Object> goods = stockDao.queryExistGoods(map);
            if (goods == null) {
                //保存
                stockDao.addGoodsModify(map);
            } else {
                stockDao.updateGoodsModifyInPrice(map);
            }

        }
        result.setStatus(1);
        return result;
    }

    @Override
    @Transactional(rollbackFor = {Exception.class})
    public PurResult outAuditStock(ShopOutStockAuditParam auditParam) {
        PurResult purResult = new PurResult(PurResult.FAIL, "操作失败");
        ShopStockDetail shopStockDetail = shopStockDetailMapper.selectById(auditParam.getShopStockDetailId());
        if (ObjectUtil.isNull(shopStockDetail)) {
            purResult.setMsg("出库单不存在");
            return purResult;
        }
        Date currentDate = DateUtil.date();
        Map<String, GoodsEntity> goodsEntityMap = new HashMap<>();
        List<ShopStock> shopStockList = new ArrayList<>();
        if (ObjectUtil.equals(AduitStatusEnum.APPROVED.getValue(), auditParam.getAuditStatus())) {
            if (ObjectUtil.notEqual(AduitStatusEnum.WAITING.getValue(), shopStockDetail.getAuditStatus())) {
                purResult.setMsg("出库单已审核，请勿重复审核");
                return purResult;
            }
            ShopStock queryParams = new ShopStock();
            queryParams.setShopUnique(Long.parseLong(shopStockDetail.getShopUnique()));
            queryParams.setListUnique(shopStockDetail.getListUnique());
            shopStockList = shopStockMapper.findList(queryParams);
            if (CollectionUtil.isEmpty(shopStockList)) {
                purResult.setMsg("出库商品为空，无法出库");
                return purResult;
            }
            List<ShopStockData> shopStockDataList = new ArrayList<>();
            for (ShopStock shopStock : shopStockList) {
                ShopStockData shopStockData = new ShopStockData();
                BeanUtil.copyProperties(shopStock, shopStockData);
                GoodsEntity goodsEntity = goodsEntityMap.get(shopStockData.getGoodsBarcode());
                Map<String, Object> goodsQuery = new HashMap<>();
                goodsQuery.put("shopUnique", shopStockData.getShopUnique());
                goodsQuery.put("goodsBarcode", shopStockData.getGoodsBarcode());
                if (ObjectUtil.isNull(goodsEntity)) {
                    goodsEntity = goodsDao.queryOneByParam(goodsQuery);
                    if (ObjectUtil.isNull(goodsEntity)) {
                        purResult.setMsg("入库商品条码: " + shopStock.getGoodsBarcode() + ", 在商品表中不存在，无法完成审核");
                        return purResult;
                    }
                }
                BigDecimal goodsContain = goodsEntity.getGoodsContain();
                shopStockData.setGoodsContain(goodsContain);
                shopStockData.setForeignKey(String.valueOf(goodsEntity.getForeignKey()));
                if (!StrUtil.equals(shopStockData.getGoodsBarcode(), shopStockData.getForeignKey())) {
                    //多规格
                    GoodsEntity smallGoods = goodsEntityMap.get(shopStockData.getForeignKey());
                    if (ObjectUtil.isNull(smallGoods)) {
                        smallGoods = goodsDao.queryParantGoods(goodsQuery);
                    }
                    if (ObjectUtil.isNotNull(smallGoods)) {
                        BigDecimal smallOutCount = NumberUtil.mul(goodsContain, shopStockData.getGoodsCount());
                        smallGoods.setGoodsCount(NumberUtil.sub(smallGoods.getGoodsCount(), smallOutCount));
                        goodsEntityMap.put(smallGoods.getGoodsBarcode(), smallGoods);
                    } else {
                        goodsEntity.setGoodsCount(NumberUtil.sub(goodsEntity.getGoodsCount(), shopStockData.getGoodsCount()));
                        goodsEntityMap.put(goodsEntity.getGoodsBarcode(), goodsEntity);
                    }
                } else {
                    goodsEntity.setGoodsCount(NumberUtil.sub(goodsEntity.getGoodsCount(), shopStockData.getGoodsCount()));
                    goodsEntityMap.put(goodsEntity.getGoodsBarcode(), goodsEntity);
                }
                shopStockDataList.add(shopStockData);
            }
            shopStockList = new ArrayList<>();
            ShopsConfig shopsConfig = shopsConfigDao.selectByShopUnique(shopStockDetail.getShopUnique());
            for (ShopStockData shopStockData : shopStockDataList) {
                ShopStock shopStock = new ShopStock();
                BeanUtil.copyProperties(shopStockData, shopStock);
                BigDecimal smallCount = goodsEntityMap.get(shopStockData.getForeignKey()).getGoodsCount();
                if (StrUtil.equals(shopStockData.getGoodsBarcode(), shopStockData.getForeignKey())) {
                    shopStock.setStockCount(smallCount);
                } else {
                    shopStock.setStockCount(NumberUtil.div(smallCount, shopStockData.getGoodsContain(), 0, RoundingMode.DOWN));
                }
                shopStockList.add(shopStock);
            }

            if (ObjectUtil.isNotNull(shopsConfig) && ObjectUtil.equals(GoodsInPriceTypeEnums.BATCH.getCode(), shopsConfig.getGoodsInPriceType())) {
                // 处理批次
                Map<String, BigDecimal> avgPriceMap = goodsSaleBatchService.subGoodsBatchCount(shopStockDetail, shopStockDataList);
                shopStockList.stream().forEach(v -> {
                    BigDecimal avgPrice = avgPriceMap.get(v.getGoodsBarcode());
                    if (ObjectUtil.isNotNull(avgPrice)) {
                        v.setGoodsAvgOutPrice(avgPrice);
                    }
                });
            }

            // 发送mqtt通知，商品更新
            List<MqttForGoodsUpdate> goodsUpdates = goodsEntityMap.values().stream().map(v -> {
                MqttForGoodsUpdate goodsUpdate = new MqttForGoodsUpdate();
                goodsUpdate.setGoods_barcode(v.getGoodsBarcode());
                goodsUpdate.setShop_unique(v.getShopUnique());
                goodsUpdate.setGoodsChengType(v.getGoodsChengType());
                goodsUpdate.setGoods_contain(v.getGoodsContain());
                goodsUpdate.setGoods_in_price(v.getGoodsInPrice());
                goodsUpdate.setGoods_standard(v.getGoodsStandard());
                goodsUpdate.setGoods_name(v.getGoodsName());
                goodsUpdate.setGoods_alias(v.getGoodsAlias());
                goodsUpdate.setGoods_brand(v.getGoodsBrand());
                goodsUpdate.setPc_shelf_state(v.getPcShelfState());
                goodsUpdate.setShelf_state(v.getShelfState());
                goodsUpdate.setGoods_sale_price(v.getGoodsSalePrice());
                goodsUpdate.setForeign_key(v.getForeignKey());
                goodsUpdate.setGoods_cus_price(v.getGoodsCusPrice());
                goodsUpdate.setGoods_kind_unique(v.getGoodsKindUnique());
                goodsUpdate.setGoods_web_sale_price(v.getGoodsWebSalePrice());
                if (null == v.getUpdateTime()) {
                    v.setUpdateTime(DateUtil.date());
                }
                goodsUpdate.setUpdate_time(DateUtil.formatDateTime(v.getUpdateTime()));
                goodsUpdate.setGoods_count(v.getGoodsCount());
                goodsUpdate.setGoods_unit(v.getGoodsUnit());
                goodsUpdate.setSupplierUnique(v.getDefaultSupplierUnique());
                return goodsUpdate;
            }).collect(Collectors.toList());
            ThreadUtil.execAsync(() -> {
                mqttService.sendGoodsUpdate(goodsUpdates, String.valueOf(shopStockDetail.getShopUnique()));
            });
        } else if (ObjectUtil.notEqual(AduitStatusEnum.REJECTED.getValue(), auditParam.getAuditStatus())) {
            purResult.setMsg("审核状态不正确");
            return purResult;
        }
        shopStockDetail.setAuditStatus(auditParam.getAuditStatus());
        shopStockDetail.setAuditContent(auditParam.getAuditContent());
        shopStockDetail.setAuditId(auditParam.getUserId());
        shopStockDetail.setUpdateTime(currentDate);
        shopStockDetail.setUpdateId(auditParam.getUserId());
        shopStockDetail.setAuditTime(currentDate);
        int n = shopStockDetailMapper.updateById(shopStockDetail);
        if (n > 0) {
            List<GoodsEntity> goodsEntityList = new ArrayList<>(goodsEntityMap.values());
            if (CollectionUtil.isNotEmpty(goodsEntityList)) {
                goodsDao.updateGoodsCount(goodsEntityList);
            }
            if (CollectionUtil.isNotEmpty(shopStockList)) {
                shopStockMapper.updateBatchGoodsCount(shopStockList);
            }
            purResult.setStatus(PurResult.SUCCESS);
            purResult.setMsg("操作成功");
        }
        return purResult;
    }
}
