package org.haier.shop.service;

import java.io.File;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.UUID;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;

import org.haier.shop.dao.ExpressDao;
import org.haier.shop.util.PicSaveUtil;
import org.haier.shop.util.PurResult;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;

@Service
@Transactional
public class ExpressServiceImpl implements ExpressService{
	
	@Resource
	private ExpressDao expressDao;
	
	@Override
	public PurResult getExpressList(Map<String, Object> params) {
		PurResult result = new PurResult();
		try {
			List<Map<String ,Object>> list = expressDao.getExpressList(params);
	    	Integer count = expressDao.getExpressListCount(params);
	    	result.setStatus(1);
			result.setMsg("成功");
			result.setCount(count);
			result.setData(list);
		} catch (Exception e) {
			e.printStackTrace();
			result.setStatus(0);
			result.setMsg("异常");
		}
		return result;
	}
	@Override
	public PurResult addExpress(Map<String, Object> params,HttpServletRequest request) {
		PurResult result = new PurResult();
		try {
			MultipartFile file=null;
			if(request instanceof MultipartHttpServletRequest){
				MultipartHttpServletRequest multipartHttpServletRequest =(MultipartHttpServletRequest)request;
				Map<String, MultipartFile> mp = multipartHttpServletRequest.getFileMap();
				file = mp.get("express_img");
				
			}
			if(file!=null){
				String orName=file.getOriginalFilename();
				String lastName=orName.substring(orName.lastIndexOf("."));
				String shop_dir = File.separator + "home" + File.separator + "apache-tomcat-7.0.42"
						+ File.separator + "webapps" + File.separator + "image" + File.separator + "expressImg"
						+ File.separator;
				File dir=new File(shop_dir);
				if(!dir.exists()){
					dir.mkdirs();
				}
				String shop_pictureName=UUID.randomUUID()+lastName;
				PicSaveUtil.handleFileUpId(file, request, shop_dir, shop_pictureName);//图片的保存
				String express_img_path="image" + File.separator + "expressImg"+ File.separator+shop_pictureName;
				if(express_img_path!=null&&!"".equals(express_img_path)){
					params.put("express_img", express_img_path);
				}
			}
			
			expressDao.addExpress(params);
	    	result.setStatus(1);
			result.setMsg("成功");
		} catch (Exception e) {
			e.printStackTrace();
			result.setStatus(0);
			result.setMsg("异常");
		}
		return result;
	}
	@Override
	public Map<String, Object> getExpress(String express_id) {
		Map<String ,Object> express = expressDao.getExpress(express_id);
		return express;
	}
	@Override
	public PurResult updateExpress(Map<String, Object> params,HttpServletRequest request) {
		PurResult result = new PurResult();
		try {
			MultipartFile file=null;
			if(request instanceof MultipartHttpServletRequest){
				MultipartHttpServletRequest multipartHttpServletRequest =(MultipartHttpServletRequest)request;
				Map<String, MultipartFile> mp = multipartHttpServletRequest.getFileMap();
				file = mp.get("express_img");
				
			}
			if(file!=null){
				String orName=file.getOriginalFilename();
				String lastName=orName.substring(orName.lastIndexOf("."));
				String shop_dir = File.separator + "home" + File.separator + "apache-tomcat-7.0.42"
						+ File.separator + "webapps" + File.separator + "image" + File.separator + "expressImg"
						+ File.separator;
				File dir=new File(shop_dir);
				if(!dir.exists()){
					dir.mkdirs();
				}
				String shop_pictureName=UUID.randomUUID()+lastName;
				PicSaveUtil.handleFileUpId(file, request, shop_dir, shop_pictureName);//图片的保存
				String express_img_path="image" + File.separator + "expressImg"+ File.separator+shop_pictureName;
				if(express_img_path!=null&&!"".equals(express_img_path)){
					params.put("express_img", express_img_path);
				}
			}
			
			expressDao.updateExpress(params);
	    	result.setStatus(1);
			result.setMsg("成功");
		} catch (Exception e) {
			e.printStackTrace();
			result.setStatus(0);
			result.setMsg("异常");
		}
		return result;
	}
	@Override
	public PurResult deleteExpress(String express_id) {
		PurResult result = new PurResult();
		try {
			expressDao.deleteExpress(express_id);
	    	result.setStatus(1);
			result.setMsg("成功");
		} catch (Exception e) {
			e.printStackTrace();
			result.setStatus(0);
			result.setMsg("异常");
		}
		return result;
	}
	@Override
	public List<Map<String, Object>> getExpressList() {
		return expressDao.getExpressList(new HashMap<String, Object>());
	}
	@Override
	public PurResult queryExpressPriceList(Map<String, Object> params) {
		PurResult result=new PurResult();			
		List<Map<String,Object>> list=expressDao.queryExpressPriceList(params);
		result.setStatus(1);
		result.setMsg("查询成功");
		result.setCount(expressDao.queryExpressPriceListCount(params));
		result.setData(list);
		return result;
	}
	@Override
	public PurResult queryParentAddress() {
		PurResult result=new PurResult();			
		List<Map<String,Object>> list=expressDao.queryParentAddress();
		result.setStatus(1);
		result.setMsg("查询成功");
		result.setData(list);
		return result;
	}
	@Override
	public PurResult addExpressPrice(String address_list, String first_price, String count_price) {
		PurResult result=new PurResult();
		Map<String, Object> params=new HashMap<>();
		params.put("first_price", first_price);
		params.put("count_price", count_price);
		params.put("express_price_id", 0l);
		expressDao.addExpressPrice(params);
		String address[]= address_list.split(",");
		for (String area_dict_num : address) {
			params.put("area_dict_num", area_dict_num.replaceAll("\"",""));
			expressDao.addExpressPriceAddress(params);
		}
		result.setStatus(1);
		result.setMsg("查询成功");
		return result;
	}
	@Override
	public PurResult queryExpressPriceDetail(String express_price_id) {
		PurResult result=new PurResult();
		Map<String, Object> params=new HashMap<>();
		params.put("express_price_id", express_price_id);
		Map<String, Object> data=expressDao.queryExpressPriceDetail(params);
		List<Map<String, Object>> list=expressDao.queryExpressPriceAddressList(params);
		data.put("list", list);
		result.setData(data);
		result.setStatus(1);
		result.setMsg("查询成功");
		return result;
	}
	@Override
	public PurResult editExpressPrice(String address_list, String first_price, String count_price,
			String express_price_id) {
		PurResult result=new PurResult();
		Map<String, Object> params=new HashMap<>();
		params.put("first_price", first_price);
		params.put("count_price", count_price);
		params.put("express_price_id", express_price_id);
		expressDao.editExpressPrice(params);
		//删除地区重新添加
		expressDao.deleteExpressPriceAddress(params);
		String address[]= address_list.split(",");
		for (String area_dict_num : address) {
			params.put("area_dict_num", area_dict_num.replaceAll("\"",""));
			expressDao.addExpressPriceAddress(params);
		}
		result.setStatus(1);
		result.setMsg("查询成功");
		return result;
	}
	@Override
	public PurResult deleteExpressPrice(String express_price_id) {
		PurResult result=new PurResult();
		Map<String, Object> params=new HashMap<>();
		params.put("express_price_id", express_price_id);
		expressDao.deleteExpressPrice(params);
		expressDao.deleteExpressPriceAddress(params);
		result.setStatus(1);
		result.setMsg("查询成功");
		return result;
	}
}
