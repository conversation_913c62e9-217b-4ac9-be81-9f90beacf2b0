package org.haier.shop.service;

import java.sql.Timestamp;
import java.util.List;
import java.util.Map;

import javax.servlet.http.HttpServletRequest;

import org.haier.shop.entity.PurListMain;
import org.haier.shop.util.PurResult;
import org.haier.shop.util.ShopsResult;
/**
 * <AUTHOR>
 */
public interface PurchaseListService {
	
	
	public ShopsResult modifyGoodsSupplier(String supplierUnique,String goodsIds);

	/**
	 * 订单详情查询
	 * @param shop_unique
	 * @param purchase_list_unique
	 * @return
	 */
	public ShopsResult queryOrderDetail(String shop_unique,String purchase_list_unique);
	
	/**
	 * 更新购物车商品
	 * @param shop_unique 店铺编号
	 * @param goods_barcode 商品条码
	 * @param count 变换数量
	 * @param supplier_unique 供应商编号
	 * @return
	 */
	public ShopsResult updateCartGoodsCount(String shop_unique,String goods_barcode,Integer count,String supplier_unique);
	
	/**
	 * 供货商购物车订单详情
	 * @param shop_unique
	 * @return
	 */
	public ShopsResult queryPurCartGoods(String shop_unique);
	/**
	 * 更新购物车商品数量
	 * @param shop_unique 店铺编号
	 * @param purchase_list_parunique 购物车编号
	 * @param goods_barcode 商品条码
	 * @param count 修改后的商品数量
	 * @return
	 */
	public ShopsResult modifyCartDetail(String shop_unique,String purchase_list_parunique,String goods_barcode,Double count);
	
	/**
	 * 移除购物车商品
	 * @param shop_unique 供货商编号
	 * @param purchase_list_parunique 购物车编号
	 * @param goods_barcode 商品条码
	 * @return
	 */
	public ShopsResult deleteFromCart(String shop_unique,String purchase_list_parunique,String goods_barcode);
	/**
	 * 购物城商品提交
	 * @param shop_unique
	 * @param goodsBarcodes
	 * @param purchase_list_parunique
	 * @return
	 */
	public ShopsResult toSubmitCart(String shop_unique,String[] goodsBarcodes,String purchase_list_parunique);
	/**
	 * 提交采购订单
	 * @param shop_unique
	 * @param goodsBarcodes
	 * @param uniques
	 * @return
	 */
	public ShopsResult submitGoods(String shop_unique, String[] goodsBarcodes, String[] uniques,String purchase_list_parunique);
	
	/**
	 * 快速采购库存不足的商品
	 * @param shop_unique
	 * @param stockType
	 * @return
	 */
	public ShopsResult easyPurchase(String shop_unique,Integer stockType);
	
	/**
	 * 下载进货订单excel表
	 * @param map
	 * @param shopUnique
	 * @return
	 */
	public List<PurListMain> purListExcel(Map<String,Object> map,String shopUnique,HttpServletRequest request);

	public ShopsResult printPurList(Map<String, Object> map, String shopUnique, HttpServletRequest request);

	public ShopsResult queryPurLists(String shop_unique, String orderMessage, Timestamp startTime, Timestamp endTime,
			String orderName, String orderType, Integer pageNum, Integer pageSize, Integer receipt_status,
			Integer paystatus);
	
	public PurResult queryAllorationList(Map<String,Object> map,HttpServletRequest request);
	
	public List<Map<String,Object>> getAllorationList(Map<String,Object> map);
	
	public PurResult queryGoodsByPage(String managerUnique,String goodsMessage,Long goods_kind_unique,Long goods_kind_parunique,Integer pageNum, Integer pageSize);
	
	public PurResult addAllorationStorage(String shop_unique, String detailJson, String purchase_list_remark,String storehouse_id_in,String user_id,String user_name);
	
	public PurResult queryAllorationDetail(String purchase_list_unique);
	
	public PurResult getGoodsTopList(Map<String,Object> map);
	
	public PurResult getGoodsNearbyList(Map<String,Object> map);
	
	public PurResult getGoodsListNoSale(Map<String,Object> map);
		
	public PurResult addNoNeedGoods(Map<String,Object> map);
	
	public PurResult insertGoods_top_order(Map<String,Object> map);
	
	public PurResult getGoodsTopOrderList(Map<String,Object> map);
}
