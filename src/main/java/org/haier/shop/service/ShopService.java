package org.haier.shop.service;

import java.sql.Timestamp;
import java.util.List;
import java.util.Map;

import javax.servlet.http.HttpServletRequest;

import org.haier.shop.entity.ShopVO;
import org.haier.shop.entity.ShopsEntity;
import org.haier.shop.params.shop.ShopGoodsInPriceTypeEditParams;
import org.haier.shop.result.shop.ShopConfigQueryResult;
import org.haier.shop.util.PurResult;
import org.haier.shop.util.ShopsResult;

public interface ShopService {
	
	/**
	 * 创建下载文件
	 * @param startTime
	 * @param endTime
	 * @return
	 */
	public List<Map<String,Object>> createAccountFile(String startTime,String endTime);
	/**
	 * @param shopUnique 店铺编号
	 * @param startTime 开始查询日期
	 * @param endTime 结束查询日期，在此基础上+1
	 * @param page 查询的页数
	 * @param limit 单页查询的数量
	 */
	public ShopsResult queryLoginRecord(String shopUnique,String startTime,String endTime,Integer page,Integer limit,Integer handover);
	/**
	 * 中心站查询各站点的往日营业状态
	 * @param shopUnique
	 * @param pageNum
	 * @param pageSize
	 * @param startDate
	 * @param endDate
	 * @return
	 */
	public ShopsResult queryShopByArea(String shopUnique,Integer pageNum,Integer pageSize,String startDate,String endDate,Integer searchType,String shopMsg);
	
	/**
	 * 根据会员帐号，查询会员信息
	 * @param staffPhone
	 * @return
	 */
	public ShopsResult checkShareMsg(String staffPhone,String shopUnique);
	/**
	 * 查询店铺详情
	 * 
	 * @param shop_unique
	 * @return
	 */
	public ShopsResult queryShopMessage(String shop_unique);
	/**
	 * 更新店铺信息
	 * @param request
	 * @param shop_unique
	 * @param shop_name
	 * @param shop_phone
	 * @param shop_address_detail
	 * @param manager_pwd
	 * @param shop_remark
	 * @param shop_announcement 
	 * @return
	 */
	public ShopsResult updateShopDetail(HttpServletRequest request,String shop_unique,String shop_name,String shop_phone,String startTime,String endTime,
			String shop_address_detail,String manager_pwd,String shop_remark,String shop_seller_email,String area_dict_num,String shopLongitude,String shopLatitude);
	
	/**
	 * 店铺功能查询
	 * @param shop_unique
	 * @return
	 */
	public ShopsResult queryShopFunction(String shop_unique);
	
	/**
	 * 更新商品功能
	 * @param shop_unique
	 * @param shop_flower
	 * @param shop_deliverwater
	 * @param shop_laundry
	 * @param shop_express
	 * @param shop_homemarking
	 * @param shop_cake
	 * @param shop_fruit
	 * @param shop_pur
	 * @return
	 */
	public ShopsResult updateShopFunction(String shop_unique,String shop_flower,String shop_deliverwater,
			String 	shop_laundry,String 	shop_express,String 	shop_homemarking,String 	shop_cake,String shop_fruit,String	shop_pur,Integer negative_sale,Integer below_cost,	Integer auto_pur,
			Integer auto_pur_days,
			Integer unsalable_days,
			Integer out_stock_warning_days,
			Integer out_stock_days,
			Integer out_stock_remind_type,Integer auto_pur_count_days);
	
	/**
	 * 店铺供货商对账查询
	 * @param shop_unique
	 * @param supMessage
	 * @param startTime
	 * @param endTime
	 * @return
	 */
	public ShopsResult queryTurnOver(String shop_unique,String supMessage,Timestamp startTime,Timestamp endTime);
	
	/**
	 * 经营统计
	 * @param shop_unique
	 * @param queryType
	 * @return
	 */
	public ShopsResult management(String shop_unique,Integer queryType,Integer pageNum);
	/**
     * 注册新用户
     *
     * @param shop_name
     * @param manager_account
     * @param manager_pwd
     * @param shop_address_detail
     * @param shop_phone
     * @param area_dict_num
     * @param town_code
     * @return
     */
	public ShopsResult register(String shop_name,
                                String manager_account,
                                String manager_pwd,
                                String shop_address_detail,
                                String shop_phone,
                                Integer examinestatus,
                                Integer shop_class,
                                String company_code,
                                Integer is_other_purchase,
                                String area_dict_num,
                                String shop_latitude,
                                String shop_longitude,
                                String province,
                                String city,
                                String district,
                                Integer shop_type,
                                String user_name,
                                String shop_image_path,
                                String license,
                                HttpServletRequest request, String town_code, String invitation_code);
	
	
	public ShopsResult registerApp(String shop_name,
								   String manager_account,
								   String manager_pwd,
								   String shop_address_detail,
								   String shop_phone,
								   Integer examinestatus,
								   Integer shop_class,
								   String company_code,
								   Integer is_other_purchase,
								   String area_dict_num,
								   String shop_latitude,
								   String shop_longitude,
								   String province,
								   String city,
								   String district,
								   Integer shop_type,
								   String code,
								   String agencyCode,
								   HttpServletRequest request, String town_code);
	
	
	/**
	 * 注册分店
	 * @param map
	 * @param district 
	 * @param city 
	 * @param province 
	 * @return
	 */
	public ShopsResult createNewShop(Map<String,Object> map,HttpServletRequest request, String province, String city, String district);
	
	/**
	 * 上帝视角
	 * @param map
	 * @return
	 */
	public PurResult countStroeTurnover(Map<String,Object> map,Integer macOnLine,Integer shopOnLine);
	
	/**
	 * 店铺
	 * @param map
	 * @return
	 */
	public ShopsResult countStroeTurnoverPages(Map<String,Object> map);
	
	/**
	 * 店铺数量页数查询
	 * @param map
	 * @return
	 */
	public ShopsResult queryShopsPages(Map<String,Object> map);
	
	
	/**
	 * 店铺销量总览界面-店铺列表查询
	 * @param map
	 * @return
	 */
	public ShopsResult queryShopsList(Map<String,Object> map);
	
	/**
	 * 查询某区域内的所有审核通过的非自营店铺信息
	 * @param areaDictNum
	 * @return
	 */
	public ShopsResult queryShopsListForGoods(String areaDictNum);
	public ShopsResult updateShop(Map<String, Object> map, HttpServletRequest request, String province, String city,
			String district,String shopHours);
	
	/**
	 * 查询商家结算列表
	 * @param shop_name 店铺名称
	 * @return
	 */
	public PurResult settlement(Map<String,Object> params);
	/**
	 * 宁宇
	 * @param params
	 * @return
	 */
	public PurResult settlement2(Map<String,Object> params);
	
	/**
	 * 查询商家结算列表
	 * @param shop_unique 店铺唯一编号
	 * @param cash_mode 提现方式：1、商家提现 2、系统结算
	 * @param handle_status 提现进度：1、待处理；2、已处理；3、驳回；4、其他
	 * @return
	 */
	public PurResult shopCouponCashList(Map<String,Object> params);
	
	/**
	 * 结算
	 * @param shop_unique 店铺唯一编号
	 * @param take_money 提现金额
	 * @param staff_id 申请者
	 * @param manager_id 管理人员ID
	 * @param card_id 提现第三方帐号：银行卡号，支付宝账号或微信号等
	 * @param card_type 提现的账户类型：1、微信；2、支付宝；3、银行卡；4、其他
	 * @param remarks 备注
	 * @param apply_type 提现类型：1、百货豆，2、现金
	 * @return
	 */
	public PurResult settlementConfirm(Map<String,Object> params,String phone,String payMoney,String shopName,String bankName,String bankAccount);
	
	//获取商家余额-百货豆余额
	public ShopVO getShopBalance(String shop_unique);
	//日收益
	public Double getShopBalanceDay(String shop_unique);
	
	public Double getShopBalanceDay2(String shop_unique);
	
	public Double getShopBalanceDay3(String shop_unique);
	//获取商家银行卡信息
	public Map<String ,Object> getShopCardInfo(String shop_unique);
	
	//获取结算费率，千分之
	public ShopVO getSysRate(String shop_unique);
	
	//修改店铺小程序开通状态
	public PurResult updateShowBuyStatus(Map<String,Object> params);
	
	//修改店铺是否开通分销状态
	public PurResult updateShopIsDis(Map<String,Object> params); 
	
	/**
	 *  查询小程序审核列表
	 * @return
	 */
	public PurResult queryWechatExamineList(Map<String,Object> params);
	
	/**
	 * 店铺管理列表
	 * @param shop_message 店铺信息
	 * @param examinestatus 店铺审核状态：1，未提交申请；2，已提交申请；3，审核未通过；4，审核通过；5：已撤回
	 * @param face_pay_status 人脸支付 0：未开通 1:已开通
	 * @param beans_agreement 0、未开通（未签订协议）；1、开通
	 * @param show_buy_status 微信小程序开通状态：0:不显示 1:显示 2:审核中 3:审核失败
	 * @return
	 */
	public PurResult shopList(Map<String,Object> params);
	
	//获取商家详情
	public Map<String ,Object> getShopDetail(String shop_unique);
	
	//获取商家详情
	public Map<String ,Object> getTogetherCodeInfo(String shop_unique);
	
	//获取店铺关联快递公司列表
	public List<Map<String ,Object>> getShopExpressRelationList(String shop_unique);
	
	/**
	 * 查询商家营业时间列表
	 * @return
	 */
	public List<Map<String ,Object>> getShopHoursList(String shop_unique);
	
	/**
	 * 查询商家分销商等级列表
	 * @return
	 */
	public PurResult queryShopDisLevelList(Map<String,Object> params);
	
	/**
	 * 查询商家分销商等级详情
	 * @return
	 */
	public Map<String ,Object> queryShopDisLevel(String dis_level_id);
	
	/**
	 * 修改商家分销商等级
	 * @return
	 */
	public PurResult updateDisLevel(Map<String,Object> params);
	
	/**
	 * 查询商家分销商等级列表
	 * @return
	 */
	public List<Map<String ,Object>> getDisLevelList(Map<String,Object> params);
	public int queryDifferentDisLevelCount(Map<String, Object> params);
	public Map<String, Object> querySetCommissionRatio(Map<String, Object> params);

	ShopsResult verifyUserInfoService(String manager_account, String manager_pwd);
	ShopsResult verifyShopInfoService(String shop_name);
	
	
	public ShopsResult togetherCode(Map<String,Object> params);
	
	
	public ShopsResult updateTogetherCode(String shop_unique,String aggregate_audit_status, String aggregation_code_id,String aggregate_refuse_reason);

	ShopsResult editGoodsInPriceType(ShopGoodsInPriceTypeEditParams params);

	ShopConfigQueryResult queryShopConfig(String shopUnique);

	List<ShopsEntity> createBatchShopPayCode(Long startShopId, Long endShopId);
}
