package org.haier.shop.service;

import java.math.BigDecimal;
import java.text.DecimalFormat;
import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.SortedMap;
import java.util.TreeMap;

import javax.annotation.Resource;

import org.haier.meituan.util.MUtil;
import org.haier.shop.dao.BeansDao;
import org.haier.shop.dao.ShopDao;
import org.haier.shop.entity.ShopBeansVO;
import org.haier.shop.entity.ShopVO;
import org.haier.shop.entity.TiCashVO;
import org.haier.shop.entity.beans.PageData;
import org.haier.shop.util.BeansRule;
import org.haier.shop.util.Page;
import org.haier.shop.util.PageQuery;
import org.haier.shop.util.PurResult;
import org.haier.shop.util.ShopsResult;
import org.haier.shop.util.wxPay.HttpUtil;
import org.haier.shop.util.wxPay.PayConfigUtil;
import org.haier.shop.util.wxPay.PayToolUtil;
import org.haier.shop.util.wxPay.XMLUtil4jdom;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.interceptor.TransactionAspectSupport;

@Service
@Transactional
public class BeanServiceImpl implements BeanService{
	@Resource
	private BeansDao beansDao;
	
	@Resource
	private ShopDao shopDao;
	
	public PageData getBeans(String shop_unique) throws Exception {
		return beansDao.getBeans(shop_unique);
	}


	public List<PageData> getTransactionList(Page page) throws Exception {
		// TODO Auto-generated method stub
		return beansDao.getTransactionList(page);
	}

	public List<PageData> getCusList(Page pd) throws Exception {
		// TODO Auto-generated method stub
		return beansDao.getCusList(pd);
	}

	public String weixinPay(String shop_unique, String productId,String spbill_create_ip,String beans_old_count,String payMoeny,String out_trade_no) throws Exception {
        //String out_trade_no = "WX" + System.currentTimeMillis(); //订单号 （调整为自己的生产逻辑）
       
        // 账号信息 
        String appid = PayConfigUtil.APP_ID;  // appid  
        String mch_id = PayConfigUtil.MCH_ID; // 商业号  
        String key = PayConfigUtil.API_KEY; // key  
        
        String currTime = PayToolUtil.getCurrTime();  
        String strTime = currTime.substring(8, currTime.length());  
        String strRandom = PayToolUtil.buildRandom(4) + "";  
        String nonce_str = strTime + strRandom;  
        // 回调接口   
        String notify_url = PayConfigUtil.NOTIFY_URL;
        String trade_type = "NATIVE";
         
        //添加订单
        PageData pd = new PageData();
        pd.put("shop_unique", shop_unique);
        pd.put("beans_old_count", beans_old_count);
        pd.put("pay_type", 1);
        pd.put("buyCount", payMoeny);
        pd.put("WIDout_trade_no", out_trade_no);
        
        beansDao.addBeanOrder(pd);
        
        SortedMap<Object,Object> packageParams = new TreeMap<Object,Object>();  
        packageParams.put("appid", appid);  
        packageParams.put("mch_id", mch_id);  
        packageParams.put("nonce_str", nonce_str);  
        packageParams.put("body", "百货豆");  //（调整为自己的名称）
        packageParams.put("out_trade_no", out_trade_no);  
        packageParams.put("total_fee", productId); //价格的单位为分  
        packageParams.put("spbill_create_ip", spbill_create_ip);  
        packageParams.put("notify_url", notify_url);  
        packageParams.put("trade_type", trade_type);  
  
        String sign = PayToolUtil.createSign("UTF-8", packageParams,key);  
        packageParams.put("sign", sign);
          
        String requestXML = PayToolUtil.getRequestXml(packageParams);  
        System.out.println(requestXML);  
   
        String resXml = HttpUtil.postData(PayConfigUtil.UFDODER_URL, requestXML);  
  
        @SuppressWarnings("rawtypes")
		Map map = XMLUtil4jdom.doXMLParse(resXml);  
        String urlCode = (String) map.get("code_url");  
        
        return urlCode;
	}

	public int getTransactionListCount(Page page) throws Exception {
		// TODO Auto-generated method stub
		return beansDao.getTransactionListCount(page);
	}

	public ShopsResult addBenasRule(PageData pd) throws Exception {
		ShopsResult sr=new ShopsResult(1,"成功！");
		try {
		beansDao.addBenasRule(pd);
		
		} catch (Exception e) {
			sr.setStatus(0);
			sr.setMsg("异常："+e.getMessage());
			throw new RuntimeException();//回滚
		}
		return sr;
	}

	public BeansRule queryBenasRule(PageData pd) throws Exception {
		return beansDao.queryBenasRule(pd);
	}

	public ShopsResult deleteBeansRule(PageData pd) throws Exception {
		// TODO Auto-generated method stub
		ShopsResult sr=new ShopsResult(1,"成功！");
		try {
			beansDao.deleteBeansRule(pd);
		} catch (Exception e) {
			sr.setStatus(0);
			sr.setMsg("异常："+e.getMessage());
		}
		return sr;
	}

	public ShopsResult updateBeansRule(PageData pd) throws Exception {
		ShopsResult sr=new ShopsResult(1,"成功！");
		try {
			beansDao.updateBeansRule(pd);
		} catch (Exception e) {
			sr.setStatus(0);
			sr.setMsg("异常："+e.getMessage());
		}
		return sr;
	}

	public PageData queryBeansDiKu(PageData pd) throws Exception {
		// TODO Auto-generated method stub
		return beansDao.queryBeansDiKu(pd);
	}


	public ShopsResult updateDikou(PageData pd) throws Exception {
		ShopsResult sr=new ShopsResult(1,"成功！");
		try {
			Map<String,Object> c= beansDao.queryBeansDiKuValidate(pd);
			if(c==null){
				sr.setStatus(0);
				sr.setMsg("抵扣比例不能小于最低比例");
				return sr;
			}
			int t=beansDao.queryeDikou(pd);
			if(t>0)
			{
				beansDao.updateDikou(pd);
			}else
			{
				beansDao.addDikou(pd);
			}
			
		} catch (Exception e) {
			sr.setStatus(0);
			sr.setMsg("异常："+e.getMessage());
		}
		return sr;
	}


	public ShopsResult addCash(PageData pd) throws Exception {
		ShopsResult sr=new ShopsResult(1,"成功！");
		try {
			int a=pd.getInt("beans_old_count");//提现前店铺百货豆数量；
			int b=pd.getInt("receive_count");//提现数量
			int c=a-b;
			if(c<0||b<1)
			{
				sr.setStatus(0);
				sr.setMsg("异常：请检查提现金额和账号余额！");
				
			}else
			{
				pd.put("shop_beans", c);
				beansDao.addCash(pd);
				beansDao.updateBeanCount(pd);
			}
			
			
		} catch (Exception e) {
			TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
			sr.setStatus(0);
			sr.setMsg("异常："+e.getMessage());
		}
		return sr;
	}


	public void addBeanOrder(PageData pd) throws Exception {
		beansDao.addBeanOrder(pd);
	}


	public PageData findOneByTradeCode(String pd) throws Exception {
		// TODO Auto-generated method stub
		return beansDao.findOneByTradeCode(pd);
	}


	public int payOrder(PageData pd) throws Exception {
		
		beansDao.updateShopBeans(pd);
		beansDao.updatePtBeans(pd);
		return beansDao.payOrder(pd);
		
	}


	public ShopsResult queryPayBeans(PageData pd) throws Exception {
		ShopsResult sr=new ShopsResult(1,"成功！");
		try {
				
			int k=beansDao.queryPayBeans(pd);
			
			sr.setData(k);
		} catch (Exception e) {
			sr.setStatus(0);
			sr.setMsg("异常："+e.getMessage());
		}
		return sr;
	}
	
	/**
	 * 分页查询店内百货豆获取信息
	 * @param map
	 * @return
	 */
	public ShopsResult queryTransactionList(PageQuery pageQuery){
		ShopsResult sr=new ShopsResult(1,"查询成功！");
		String message="%"+pageQuery.getGoodsMessage()+"%";
		pageQuery.setGoodsMessage(message);
		int a=beansDao.queryTransactionList(pageQuery);
		
		int b=0;
		if(a!=0)
		{
			b=(a-1)/pageQuery.getShowCount()+1;
		}
		
		sr.setCord(b);
		List<Map<String,Object>> list=beansDao.queryTransactionListPage(pageQuery);
		sr.setData(list);
		return sr;
	}
	/**
	 * 分页查询店内百货豆获取信息
	 * @param map
	 * @return
	 */
	public PurResult queryTransactionList2(PageQuery pageQuery){
		PurResult result = new PurResult();
		try {
			List<Map<String,Object>> list = beansDao.queryTransactionListPage2(pageQuery);
			Map<String,Object> mp=beansDao.queryTransactionList2(pageQuery);
	    	Integer count = Integer.parseInt(MUtil.strObject(mp.get("orderCount")));
	    	result.setStatus(1);
			result.setMsg("成功");
			result.setCount(count);
			result.setData(list);
			result.setCord(mp);
		} catch (Exception e) {
			e.printStackTrace();
			result.setStatus(0);
			result.setMsg("异常");
		}
		return result;
	}
	/**
	 * 分页查询商品促销记录表
	 * @param map
	 * @return
	 */
	public PurResult queryShopBeansPromation(PageQuery pageQuery){
		PurResult result = new PurResult();
		try {
			
			SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
	        Calendar c = Calendar.getInstance();
	         
	        //过去七天
	        c.setTime(new Date());
	        c.add(Calendar.DATE, - 7);
	        Date d = c.getTime();
	        String startTime = format.format(d);
	        pageQuery.setStartTime(startTime);
			List<Map<String,Object>> list = beansDao.queryShopBeansPromation(pageQuery);
			int count=beansDao.queryShopBeansPromationCount(pageQuery);
	    	result.setStatus(1);
			result.setMsg("成功");
			result.setCount(count);
			result.setData(list);
		} catch (Exception e) {
			e.printStackTrace();
			result.setStatus(0);
			result.setMsg("异常");
		}
		return result;
	}
	
	/**
	 * 查询店内所有用户消费所增加的百货豆订单数量
	 * @param pd
	 * @return
	 */
	public Integer queryCusCount(PageData pd) throws Exception{
		return beansDao.queryCusCount(pd);
	}
	
	/**
	 * 分页查询会员交易记录
	 * @param pageQuery
	 * @return
	 */
	public ShopsResult queryOrderListByPage(PageQuery pageQuery){
		ShopsResult sr=new ShopsResult(1, "查询成功！");
		try 
		{
			if(null!=pageQuery.getGoodsMessage()&&!pageQuery.getGoodsMessage().equals(""))
			{
				String message="%"+pageQuery.getGoodsMessage()+"%";
				pageQuery.setGoodsMessage(message);
			}

		Map<String,Object> mp= beansDao.queryOrderListByPageCount(pageQuery);
		List<Map<String,Object>> data=beansDao.queryOrderListByPage(pageQuery);
		int b=0;
		String orderCount=mp.get("orderCount").toString();
		int a=Integer.parseInt(orderCount);
		if(a!=0)
		{
			b=(a-1)/pageQuery.getShowCount()+1;
		}
		mp.put("pageCount", b);
		sr.setCord(mp);
		sr.setData(data);
		} catch (Exception e) {
			sr.setStatus(0);
			sr.setMsg("异常："+e.getMessage());
		}
		return sr;
	}
	
	/**
	 * 查询店铺的百货豆赠送情况
	 * @param pageQuery
	 * @return
	 */
	public PurResult queryShopsBeansListByPage(PageQuery pageQuery){
		PurResult result = new PurResult(1,"查询成功！");
		try {
			pageQuery.setExchangeType(1);
			List<Map<String,Object>> list=beansDao.queryShopsBeansListByPage(pageQuery);
			
			//获取百货豆赠送统计总和
			Map<String ,Object> cord = beansDao.getBeansTotal(pageQuery);
			result.setData(list);
			result.setCount(beansDao.queryShopsBeansListPages(pageQuery));//数量
			result.setCord(cord);
		} catch (Exception e) {
			e.printStackTrace();
			result.setStatus(0);
			result.setMsg("查询失败！");
		}
		return result;
	}
	/**
	 * 分页查询会员交易记录
	 * @param pageQuery
	 * @return
	 */
	public PurResult queryOrderListByPage2(PageQuery pageQuery){
		
		PurResult result = new PurResult();
		try {
			List<Map<String,Object>> list = beansDao.queryOrderListByPage2(pageQuery);
			Map<String ,Object> map = beansDao.queryOrderListByPageCount2(pageQuery);
	    	Integer count = Integer.parseInt(MUtil.strObject(map.get("orderCount")));
	    	result.setStatus(1);
			result.setMsg("成功");
			result.setCount(count);
			result.setData(list);
			result.setCord(map);
		} catch (Exception e) {
			e.printStackTrace();
			result.setStatus(0);
			result.setMsg("异常");
		}
		return result;
	}

	@Override
	public ShopsResult queryPtLi3(Map<String, Object> map) throws Exception {
		ShopsResult sr=new ShopsResult(1,"查询成功");
		try 
		{
		List<Map<String,Object>> data=beansDao.queryPtLi3(map);
		int a= beansDao.queryPtLi3Count(map);
		int b=a/10;
		if(b==0)
		{
			if( a <10&& a>0)
			{
				sr.setCord(1);
			}else
			{
				sr.setCord(b);
			}
			
		}else
		{
			sr.setCord(b+1);
		}
		sr.setData(data);
		
		} catch (Exception e) {
			sr.setStatus(0);
			sr.setMsg("异常："+e.getMessage());
		}
		return sr;
	}


	@Override
	public ShopsResult queryCard(String shopUnique) throws Exception {
		ShopsResult sr=new ShopsResult(1,"查询成功");
		try 
		{
		List<Map<String,Object>> data=beansDao.queryCard(shopUnique);
		sr.setData(data);
		} catch (Exception e) {
			sr.setStatus(0);
			sr.setMsg("异常："+e.getMessage());
		}
		return sr;
	}


	@Override
	public List<Map<String,Object>> queryBankName() throws Exception {
		return beansDao.queryBankName();
	}


	@SuppressWarnings("unlikely-arg-type")
	@Override
	public ShopsResult addbankCard(PageData pd) throws Exception {
		ShopsResult sr=new ShopsResult(1,"成功！");

		try 
		{
			String default_type=(String) pd.get("default_type");
			
			List<PageData> data=beansDao.queryCardMain(pd);
			
			
			if(data==null||data.equals("[]")||data.isEmpty())
			{
				if(default_type.equals("1"))
				{
					beansDao.addbankCard(pd);
				}else if(default_type.equals("2"))
				{
					sr.setStatus(0);
					sr.setMsg("请先添加主账户!");
					return sr;
				}
			}else
			{
				if(default_type.equals("1"))
				{
					for(PageData t:data)
					{
						t.put("default_type", 2);
						beansDao.updateBankCard(t);
					}
					beansDao.addbankCard(pd);
				}else if(default_type.equals("2"))
				{
					beansDao.addbankCard(pd);
				}
			}

			
			
		} catch (Exception e) {
			sr.setStatus(0);
			sr.setMsg("异常："+e.getMessage());
		}
		return sr;
	}


	@SuppressWarnings("unlikely-arg-type")
	@Override
	public ShopsResult updateBankCard(PageData pd) throws Exception {
		ShopsResult sr=new ShopsResult(1,"成功！");
		try 
		{
			String default_type=(String) pd.get("default_type");
			List<PageData> data=beansDao.queryCardMain(pd);
			
			
			if(data==null||data.equals("[]")||data.isEmpty())
			{
				if(default_type.equals("1"))
				{
					beansDao.updateBankCard(pd);
					
				}else if(default_type.equals("2"))
				{
					sr.setStatus(0);
					sr.setMsg("不能没有主账户!");
					return sr;
				}
			}else
			{
				int num=0;
				
				
				for(PageData t:data)
				{
					t.put("default_type", 2);
					String card_id=(String) pd.get("card_id");
					String id=t.get("card_id").toString();
					System.out.print(data.size());
					if(card_id.equals(id)&&data.size()<2)
					{
						num=num+1;
					}
				}
				if(num>0)
				{
					if(default_type.equals("2"))
					{
						sr.setStatus(0);
						sr.setMsg("不能没有主账户!");
						return sr;
						
					}else
					{
						beansDao.updateBankCard(pd);
					}
				}else
				{
					for(PageData t:data)
					{
						t.put("default_type", 2);
						beansDao.updateBankCard(t);
					}
					beansDao.updateBankCard(pd);
				}
				
			}
			
			
			
		} catch (Exception e) {
			sr.setStatus(0);
			sr.setMsg("异常："+e.getMessage());
		}

		return sr;
	}


	@Override
	public ShopsResult deleteBankCard(PageData pd) throws Exception {
		ShopsResult sr=new ShopsResult(1,"成功！");
		try 
		{
			beansDao.deleteBankCard(pd);
			
		} catch (Exception e) {
			sr.setStatus(0);
			sr.setMsg("异常："+e.getMessage());
		}

		return sr;
	}


	@Override
	public List<Map<String, Object>> queryBankCard(String shop_unique)
			throws Exception {
		// TODO Auto-generated method stub
		return beansDao.queryBankCard(shop_unique);
	}


	@Override
	public ShopBeansVO getShopBeans(String shop_unique) throws Exception {
		// TODO Auto-generated method stub
		return beansDao.getShopBeans(shop_unique);
	}


	@Override
	public ShopsResult addCardRecord(TiCashVO cashVO) throws Exception {
		ShopsResult sr=new ShopsResult(1,"成功！");
		try 
		{
			if(Integer.parseInt(cashVO.getReceive_count())>Integer.parseInt(cashVO.getBeans_old_count())){
				sr.setStatus(0);
				sr.setMsg("提现数量超过可提现数量");
				return sr;
			}
			
			beansDao.addCardRecord(cashVO);
			beansDao.shopBeansTixian(cashVO);
			
			//查询店铺剩余百货豆数量
			PageData beansDetail = beansDao.getBeans(cashVO.getShop_unique());	//店铺百货豆信息 
			sr.setData(beansDetail);
			
		} catch (Exception e) {
			e.printStackTrace();
			sr.setStatus(0);
			sr.setMsg("异常："+e.getMessage());
			throw new RuntimeException();//回滚
		}

		return sr;
	}


	@Override
	public PageData queryCashDetail(PageData pd) throws Exception {
		// TODO Auto-generated method stub
		return beansDao.queryCashDetail(pd);
	}
	
	@Override
	public List<PageData> queryCashList(PageData pd) throws Exception {
		// TODO Auto-generated method stub
		return beansDao.queryCashList(pd);
	}
	
	@Override
	public ShopsResult updateCashOrder(PageData pd) throws Exception {
		ShopsResult sr=new ShopsResult(1,"成功！");
		try 
		{
			
			beansDao.updateCashOrder(pd);
			beansDao.addPtBeans(pd);
			
		} catch (Exception e) {
			sr.setStatus(0);
			sr.setMsg("异常："+e.getMessage());
			throw new RuntimeException();
		}

		return sr;
	}


	@Override
	public PageData getPtBeans(String shop_unique) throws Exception {
		// TODO Auto-generated method stub
		return beansDao.getPtBeans(shop_unique);
	}


	@Override
	public ShopsResult updatePtRule(PageData pd) throws Exception {
		ShopsResult sr=new ShopsResult(1,"成功！");
		try 
		{
			
			beansDao.updatePtRule(pd);
			
			//修改会员注册百货豆个数
			Integer give_beans = Integer.parseInt(pd.getString("pt_give_beans"));
			beansDao.updatePtGiveBeans(give_beans);
		} catch (Exception e) {
			sr.setStatus(0);
			sr.setMsg("异常："+e.getMessage());
		}

		return sr;
	}


	@Override
	public ShopsResult updateBoHuiCash(PageData pd) throws Exception {
		ShopsResult sr=new ShopsResult(1,"成功！");
		try 
		{
			beansDao.updateShopBeans(pd);
			beansDao.updateBoHuiCash(pd);
			
		} catch (Exception e) {
			sr.setStatus(0);
			sr.setMsg("异常："+e.getMessage());
			 throw new RuntimeException();//回滚
		}

		return sr;
	}


	@Override
	public int queryTransactionCount(String shop_unique, int tx_dtae)
			throws Exception {
		int tixianCount=0;
		if(tx_dtae==1)
		{
			tixianCount=beansDao.queryTransactionCount2(shop_unique, tx_dtae);
		}else
		{
			tixianCount=beansDao.queryTransactionCount(shop_unique, tx_dtae);
		}
		 
		
		return tixianCount;
	}

	@Override
	public int queryTransactionCountPt(String shop_unique, int tx_dtae)
			throws Exception {
		// TODO Auto-generated method stub
		int tixianCount=0;
		if(tx_dtae==1)
		{
			tixianCount=beansDao.queryTransactionCountPt2(shop_unique, tx_dtae);
		}else
		{
			tixianCount=beansDao.queryTransactionCountPt(shop_unique, tx_dtae);
		}
		 
		
		return tixianCount;
	}
	
	@Override
	public PurResult queryDrawCashList(Map<String, Object> map)throws Exception {
		PurResult sr=new PurResult(1,"查询成功");
		try {
			List<Map<String,Object>> data=beansDao.queryDrawCashList(map);
			int count = beansDao.queryDrawCashListCount(map);
			sr.setCount(count);
			sr.setData(data);
		}catch (Exception e) {
			sr.setStatus(0);
			sr.setMsg("异常："+e.getMessage());
		}
		return sr;
	}


	@Override
	public PageData queryPtCashDetail(PageData pd) throws Exception {
		// TODO Auto-generated method stub
		 return beansDao.queryPtCashDetail(pd);
	}


	@Override
	public ShopsResult updateDrawCash(PageData pd) throws Exception {
		ShopsResult sr=new ShopsResult(1,"成功！"); 
		try 
		{ 
			
			String handle_status=pd.getString("handle_status");
			if(handle_status.equals("3"))
			{
				String  shop_balence=beansDao.queryShop_balance(pd);
				BigDecimal old= new BigDecimal(shop_balence);
				BigDecimal take_money= new BigDecimal(pd.get("take_money").toString());//已核算金额
				
				pd.put("take_money", old.add(take_money).doubleValue());
				beansDao.updateShopCashNew(pd);
			}
			
			beansDao.updateDrawCash(pd);
			
		} catch (Exception e) {
			sr.setStatus(0);
			sr.setMsg("异常："+e.getMessage());
			throw new RuntimeException();//回滚
		}

		return sr;
	}


	@Override
	public ShopsResult addDrawCash(PageData pd) throws Exception {
		ShopsResult sr=new ShopsResult(1,"成功！");
		try {
			String shop_unique = pd.getString("shop_unique");
			Double payMoney = Double.valueOf(pd.getString("payMoney"));
			
			//获取店铺余额
			Double shop_balance = beansDao.getShopBalance(shop_unique);
			if(shop_balance < payMoney){
				sr.setStatus(2);
				sr.setMsg("店铺余额不足！");
				return sr;
			}
			
			ShopBeansVO beanDetai = beansDao.getShopBeans(shop_unique);
			if(beanDetai != null){
				//最小提现金额
				Double s_moeny = Double.valueOf(beanDetai.getS_moeny());
				//最大提现金额
				Double m_moeny = Double.valueOf(beanDetai.getM_moeny());
				
				if(payMoney < s_moeny){
					sr.setStatus(2);
					sr.setMsg("提现金额小于最小提现金额");
					return sr;
				}
				
				if(payMoney > m_moeny){
					sr.setStatus(2);
					sr.setMsg("提现金额大于最大提现金额");
					return sr;
				}
				
				int tx_dtae = beanDetai.getTx_dtae();
				int tx_times = beanDetai.getTx_times();
				//查询最近tx_dtae日的交易次数
				int tixianCount = queryTransactionCountPt(shop_unique,tx_dtae);
				int tiXianCount = tx_times-tixianCount;
				if(tiXianCount <= 0){
					sr.setStatus(2);
					sr.setMsg("可提现次数不足");
					return sr;
				}
			}
			
			//订单号
			String order_id = "tx" + System.currentTimeMillis();
			pd.put("order_id", order_id);
			//获取结算费率,千分之
			ShopVO rateVO = shopDao.getSysRate(shop_unique);
			DecimalFormat df = new DecimalFormat("0.00");
			String num = df.format((float)rateVO.getRate()/1000);
			Double rates = Double.valueOf(num);
			pd.put("actual_money", payMoney*(1-rates));//实际到账金额
			//添加提现记录
			beansDao.addDrawCash(pd);
			//修改店铺余额
//			beansDao.reduceShopCash(pd);
			Map<String ,Object> balanceParams = new HashMap<String, Object>();
			balanceParams.put("shop_unique", shop_unique);
			balanceParams.put("shop_balance", payMoney);
			shopDao.updateShopBalance(balanceParams);
		} catch (Exception e) {
			sr.setStatus(0);
			sr.setMsg("异常："+e.getMessage());
			throw new RuntimeException();//回滚
		}

		return sr;
	}


	/* (non-Javadoc)
	 * @see org.haier.shop.service.BeanService#getAgreement(java.lang.String)
	 */
	@Override
	public int getAgreement(String shop_unique) throws Exception {
		// TODO Auto-generated method stub
		return beansDao.getAgreement(shop_unique);
	}


	/* (non-Javadoc)
	 * @see org.haier.shop.service.BeanService#updateProtocol(org.haier.shop.entity.beans.PageData)
	 */
	@Override
	public int updateProtocol(PageData pd) throws Exception {
		// TODO Auto-generated method stub
		ShopBeansVO beanDetai=beansDao.getShopBeans(pd.getString("shopUnique"));
		pd.put("diKou", beanDetai.getDiKou());
		int resultPd = beansDao.queryeDikou(pd);
		if(resultPd > 0){
			beansDao.updateDikou(pd);
		}else{
			beansDao.addDikou(pd);
		}
		
		return beansDao.updateProtocol(pd);
	}

	@Override
	public ShopsResult addShopBeanPromation(PageData pd) {
		ShopsResult sr=new ShopsResult(1,"成功！"); 
			
			int sum=beansDao.queryShopBeanPromationCount(pd);
			
			if(sum>0)
			{
				beansDao.updateShopBeanPromation(pd);
			}else
			{
				beansDao.addShopBeanPromation(pd);
			}
			return sr;
	}
	@Override
	public ShopsResult updateShopBeanPromationStatus(PageData pd) {
		ShopsResult sr=new ShopsResult(1,"成功！"); 
			
			int sum=beansDao.queryShopBeanPromationCount(pd);
			
			if(sum>0)
			{
				beansDao.updateShopBeanPromationStatus(pd);
			}else
			{
				beansDao.addShopBeanPromationStatus(pd);
			}
			return sr;
	}
}
