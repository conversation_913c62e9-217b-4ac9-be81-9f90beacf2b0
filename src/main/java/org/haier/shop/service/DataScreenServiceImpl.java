package org.haier.shop.service;

import java.io.BufferedReader;
import java.io.InputStreamReader;
import java.net.HttpURLConnection;
import java.net.URL;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Random;
import java.util.concurrent.TimeUnit;

import javax.annotation.Resource;

import org.haier.shop.dao.DataScreenDao;
import org.haier.shop.dao.DataSearchDao;
import org.haier.shop.util.ShopsResult;
import org.haier.shop.util.ShopsUtil;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;


import net.sf.json.JSONArray;
import net.sf.json.JSONObject;
@Service
public class DataScreenServiceImpl implements DataScreenService{
	
	@Resource
	private DataScreenDao dataDao;
	@Resource
	private DataSearchDao searchDao;
	@Resource
    private RedisTemplate<String, Object> redisTemplate; 
	
	public ShopsResult queryOnlineCount() {
		ShopsResult sr = new ShopsResult(1,"查询成功！");
		sr.setData(dataDao.queryOnlineCount());
		return sr;
	}
	/**
	 * 使用缓存-new
	 * @return
	 */
	public  ShopsResult useCache(String id){
		ShopsResult sr=new ShopsResult(1,"操作成功");
		System.out.println("缓存key======="+id);
		if(id.equals("orderTotalByHours")){
			
			Object d1=redisTemplate.opsForValue().get(id+1);
			Object d2=redisTemplate.opsForValue().get(id+1);
			if(null==d2||null==d1){
				return null;
			}
			sr.setData(d1);
			sr.setCord(d2);
		}else if(id.equals("")){
			
		}else{
			Object data=redisTemplate.opsForValue().get(id);
			if(data==null){
				return null;
			}else{
				sr.setData(data);
			}
		}
		return null;
	}
	
	/**
	 * 使用缓存，返回原始数据
	 * @param id
	 * @return
	 */
	public Object useCacheObject(String id) {
		Object data=redisTemplate.opsForValue().get(id);
		return data;
	}
	
	public  void SetCache(String id,Object value){
		redisTemplate.opsForValue().set(id, value.toString());
		redisTemplate.expire(id, 300, TimeUnit.SECONDS);
	}
	public  void SetCache2(String id,Object value,int num){
		redisTemplate.opsForValue().set(id, value.toString());
		redisTemplate.expire(id, num, TimeUnit.SECONDS);
	}
	/**
	 * 今日销量前五商品
	 */
	public ShopsResult top5GoodsMessage(){
		ShopsResult sr=useCache("top5GoodsMessage");
		if(sr!=null){
			return sr;
		}
		sr=new ShopsResult(1,"查询成功！");
		List<Map<String,Object>> data=dataDao.top5GoodsMessage();
		if(null==data||data.isEmpty()){
			sr.setStatus(2);
			sr.setMsg("今日尚未有订单");
			return sr;
		}
		SetCache("top5GoodsMessage",data);
		sr.setStatus(1);
		sr.setData(data);
		sr.setMsg("查询成功！");
		return sr;
	}
	
	/**
	 * 各种支付方式占比
	 * @return
	 */
	public ShopsResult payTypeMessage(){
//		ShopsResult sr=useCache("payTypeMessage");
//		if(sr!=null){
//			return sr;
//		}
//		sr=new ShopsResult(1,"查询成功！");
//		List<Map<String,Object>> data=dataDao.payTypeMessage();
//		
//		//
//		if(null==data||data.isEmpty()){
//			sr.setStatus(2);
//			sr.setMsg("查询不到支付方式！");
//			return sr;
//		}
//		SetCache("payTypeMessage",data);
//		sr.setStatus(1);
//		sr.setMsg("查询成功！");
//		sr.setData(data);
		ShopsResult sr=new ShopsResult(1, "查询成功！");
		List<Map<String,Object>> list=dataDao.payTypeMessage();
		Double r=0.0;
		Random ran=new Random();
		System.out.println(list.size());
//		for(int i=1;i<list.size();i++){
//			System.out.println(list.get(i).get("value"));
//			Double v=ran.nextFloat()>=0.5?ran.nextDouble()*0.2:-ran.nextDouble()*0.2;
//			list.get(i).put("value", Double.parseDouble(list.get(i).get("value").toString())+v);
//			r+=v;
//			System.out.println(list.get(i).get("value"));
//		}
//		list.get(0).put("value", Double.parseDouble(list.get(0).get("value").toString())+r);
		
		for(int i=0;i<list.size();i++){
			
			list.get(i).put("value", String.format("%.2f", list.get(i).get("value")));
		}
		sr.setData(list);
		return sr;
	}
	
	/**
	 * 订单达成率查询
	 * @return
	 */
	public ShopsResult proportionOfOrder(){
		ShopsResult sr=useCache("proportionOfOrder");
		if(sr!=null){
			return sr;
		}
		sr=new ShopsResult(1,"查询成功！");
		Map<String,Object> data=dataDao.proportionOfOrder();
		if(null==data||data.isEmpty()){
			sr.setStatus(2);
			sr.setMsg("没有满足条件的信息！");
			return sr;
		}
		//SetCache("proportionOfOrder",data);
		sr.setStatus(1);
		sr.setMsg("查询成功！");
		sr.setData(data);
		return sr;
	}
	
	/**
	 * 查询指定时间内每一秒的订单数量和订单总金额
	 * @return
	 */
	public ShopsResult getSaleListTotalMessageBySecond(){
		ShopsResult sr=useCache("getSaleListTotalMessageBySecond");
		if(sr!=null){
			return sr;
		}
		sr=new ShopsResult(1,"查询成功！");
		List<Map<String,Object>>  data=dataDao.getSaleListTotalMessageBySecond();
		if(null==data||data.isEmpty()){
			sr.setStatus(2);
			sr.setMsg("没有满足条件的订单信息！");
			return sr;
		}
		SetCache("getSaleListTotalMessageBySecond",data);
		sr.setStatus(1);
		sr.setMsg("查询成功！");
		sr.setData(data);
		return sr;
	}

	
	/**
	 * 相比昨日销售营业额对比
	 * @return
	 */
	public ShopsResult onLineSaleComparsionYesterday(){
		ShopsResult sr=useCache("onLineSaleComparsionYesterday");
		if(sr!=null){
			return sr;
		}
		sr=new ShopsResult(1,"查询成功！");
		Map<String,Object> data=dataDao.onLineSaleComparsionYesterday();
		if(null==data||data.isEmpty()){
			sr.setStatus(2);
			sr.setMsg("查询失败！");
			return sr;
		}
		//SetCache("onLineSaleComparsionYesterday",data);
		sr.setStatus(1);
		sr.setMsg("查询成功！");
		sr.setData(data);
		return sr;
	}
	/**
	 * 销量前五十商品名称查询
	 * @return
	 */
	public ShopsResult top50characterCloud(){
		ShopsResult sr=useCache("top50characterCloud");
		if(sr!=null){
			return sr;
		}
		sr=new ShopsResult(1,"查询成功！");
		List<Map<String,Object>> data=dataDao.top50characterCloud();
		if(null==data||data.isEmpty()){
			sr.setStatus(2);
			sr.setMsg("查询失败！");
			return sr;
		}
		SetCache("top50characterCloud",data);
		sr.setStatus(1);
		sr.setMsg("查询成功！");
		sr.setData(data);
		return sr;
	}
	
	/**
	 * 获取系统当前时间
	 */
	public ShopsResult selectNow(){
		ShopsResult sr=new ShopsResult();
		String data=dataDao.selectNow();
		sr.setStatus(1);
		sr.setMsg("查询成功！");
		sr.setData(data);
		return sr;
	}
	
	/**
	 * 活跃店铺信息查询益农版
	 * @return
	 */
	public ShopsResult getActiveShopsMessageYN() {
		ShopsResult sr = new ShopsResult(1, "查询成功！");
		StringBuilder shopsOnline=new StringBuilder();
		
		try {
			URL url=new URL("http://buyhoo.cc/shopmanager/pc/getShopsOnline.do");
			HttpURLConnection  httpConn=(HttpURLConnection)url.openConnection();
		    httpConn.setDoOutput(true); // 使用 URL 连接进行输出
            httpConn.setDoInput(true); // 使用 URL 连接进行输入
            httpConn.setUseCaches(false); // 忽略缓存
            httpConn.setRequestMethod("GET"); // 设置URL请求方法
            
            //读取链接返回的数据流
            BufferedReader responseReader1 = new BufferedReader(
                    new InputStreamReader(httpConn.getInputStream(), "UTF-8"));//获取返回参数
            String readLine1;
            while((readLine1=responseReader1.readLine())!=null){
            	shopsOnline.append(readLine1);
            }
		} catch (Exception e) {
			e.printStackTrace();
		}
		
		JSONObject shopJson=JSONObject.fromObject(shopsOnline.toString());
		JSONArray shopData=(JSONArray)shopJson.get("data");
		List<Map<String,Object>> shopResource=new ArrayList<Map<String,Object>>();
		for(int j=0;j<shopData.size();j++){
			@SuppressWarnings("unchecked")
			Map<String,Object> mp=(Map<String,Object>)shopData.get(j);//此处需要去重
			int m=0;
			for(int k=0;k<shopResource.size();k++){
				if(shopResource.get(k).get("shopUnique").toString().equals(mp.get("shopUnique").toString())){
					m=0;
					break;
				}
				m++;
			}
			if(m==shopResource.size()){
				shopResource.add(mp);
			}
		}
		
		sr.setData(dataDao.getAllShopYN());
//        Random random = new Random();
//		int s = random.nextInt(1430)%(1430-1400+1) + 1400;
		int s = shopResource.size();//shopTest
		sr.setCord(s>150?150:s);//
		return sr;
	}
	/**
	 * 活跃店铺信息查询
	 */
	public  ShopsResult getActiveShopsMessage(){
		ShopsResult sr=new ShopsResult(1,"查询成功！");
//		StringBuffer responseSb = new StringBuffer();
//		try {
//			URL url=new URL("http://buyhoo.cc/shopmanager/pc/getCount.do");
//			HttpURLConnection  httpConn=(HttpURLConnection)url.openConnection();
//		    httpConn.setDoOutput(true); // 使用 URL 连接进行输出
//            httpConn.setDoInput(true); // 使用 URL 连接进行输入
//            httpConn.setUseCaches(false); // 忽略缓存
//            httpConn.setRequestMethod("GET"); // 设置URL请求方法
//            BufferedReader responseReader = new BufferedReader(
//                    new InputStreamReader(httpConn.getInputStream(), "UTF-8"));//获取返回参数
//            String readLine;
//            while((readLine=responseReader.readLine())!=null){
//            	responseSb.append(readLine);
//            }
//		} catch (Exception e) {
//			e.printStackTrace();
//		}
//		sr.setStatus(1);
//		sr.setMsg("查询成功！");
//		JSONObject json=JSONObject.fromObject(responseSb.toString());
//		JSONArray jsonArr=null;
//		if(json.get("data")!=null&&!json.get("data").toString().equals("null")){
//			jsonArr=JSONArray.fromObject(json.get("data").toString());
//		}
//		if(jsonArr==null){
//			sr.setStatus(2);
//			sr.setMsg("没有信息！");
//			return sr;
//		}
//		sr.setStatus(1);
//		sr.setMsg("查询成功！");
//		sr.setData(jsonArr);
		StringBuilder shopsOnline=new StringBuilder();
		
		try {
			URL url=new URL("http://buyhoo.cc/shopmanager/pc/getShopsOnline.do");
			HttpURLConnection  httpConn=(HttpURLConnection)url.openConnection();
		    httpConn.setDoOutput(true); // 使用 URL 连接进行输出
            httpConn.setDoInput(true); // 使用 URL 连接进行输入
            httpConn.setUseCaches(false); // 忽略缓存
            httpConn.setRequestMethod("GET"); // 设置URL请求方法
            
            //读取链接返回的数据流
            BufferedReader responseReader1 = new BufferedReader(
                    new InputStreamReader(httpConn.getInputStream(), "UTF-8"));//获取返回参数
            String readLine1;
            while((readLine1=responseReader1.readLine())!=null){
            	shopsOnline.append(readLine1);
            }
		} catch (Exception e) {
			e.printStackTrace();
		}
		
		JSONObject shopJson=JSONObject.fromObject(shopsOnline.toString());
		JSONArray shopData=(JSONArray)shopJson.get("data");
		List<Map<String,Object>> shopResource=new ArrayList<Map<String,Object>>();
		for(int j=0;j<shopData.size();j++){
			@SuppressWarnings("unchecked")
			Map<String,Object> mp=(Map<String,Object>)shopData.get(j);//此处需要去重
			int m=0;
			for(int k=0;k<shopResource.size();k++){
				if(shopResource.get(k).get("shopUnique").toString().equals(mp.get("shopUnique").toString())){
					m=0;
					break;
				}
				m++;
			}
			if(m==shopResource.size()){
				shopResource.add(mp);
			}
		}
		
		sr.setData(dataDao.getAllShop());
//        Random random = new Random();
//		int s = random.nextInt(1430)%(1430-1400+1) + 1400;
		int s = shopResource.size()*11;//shopTest
		
		if(s < 20000) {
			s += 20000;
		}
		
		String id = "getActiveShopsMessage";
		Object o = useCacheObject("getActiveShopsMessage");
		
		System.out.println("getActiveShopsMessage" + o);
		if(null != o) {
			Integer count = Integer.parseInt(o.toString());
			if(s <= count) {
				s = count;
			}
		}
		
		sr.setCord(s);//
		SetCache2(id, s,7200);
		return sr;
	}
	
	/**
	 * 当日流水及订单量
	 * @return
	 */
	public ShopsResult getDailyTurnover(){
		ShopsResult sr=new ShopsResult();
		Map<String,Object> map=dataDao.getDailyTurnover();
		if(null==map){
			sr.setStatus(2);
			sr.setMsg("");
			return sr;
		}
		//SetCache("getDailyTurnover",map);
		sr.setStatus(1);
		sr.setMsg("查询成功！");
		sr.setData(map);
		return sr;
	}
	
	/**
	 * 今天 昨天交易额
	 */
	public ShopsResult getYestodayOrder(){
		ShopsResult sr=new ShopsResult(1,"查询成功！");
		Map<String,Object> today=dataDao.getDailyTurnover();
		Map<String,Object> yestoday=dataDao.getYestodayOrder();
		sr.setStatus(1);
		sr.setMsg("查询成功！");
		sr.setData(today);
		sr.setCord(yestoday);
		return sr;
	}
	
	public ShopsResult queryEvaluateList(){
		ShopsResult sr=new ShopsResult(1,"查询成功！");
		List<Map<String,Object>> list=dataDao.queryEvaluateList();
		sr.setStatus(1);
		sr.setMsg("查询成功！");
		sr.setData(list);
		return sr;
	}

	/**
	 * 本周新增店铺数量
	 */
	public ShopsResult newShopsCount() {
		ShopsResult sr=new ShopsResult();
		Map<String,Object> map=new HashMap<String, Object>();
//		Timestamp startTime=new Timestamp(ShopsUtil.weekFristDay().getTime());
//		map.put("startTime", startTime);
//		int data=dataDao.newShopsCount(map);
//		System.out.println(data);
		sr.setStatus(1);
//		sr.setMsg("查询成功！");
//		sr.setData(data);
		List<Integer> newShopCount = dataDao.queryExpressCount();
		sr.setData(newShopCount.get(2));
		return sr;
	}
	
	public ShopsResult newShopsCountYN() {
		ShopsResult sr=new ShopsResult(1,"查询成功！");
		Map<String,Object> map=new HashMap<String, Object>();
//		Timestamp startTime=new Timestamp(ShopsUtil.weekFristDay().getTime());
//		map.put("startTime", startTime);
		int data=dataDao.newShopsCountYN(map);
		sr.setData(data);
		return sr;
	}
	
	/**
	 * 按小时查询订单信息
	 */
	public ShopsResult orderTotalByHours(Double num){
		ShopsResult sr=useCache("orderTotalByHours");
		if(sr!=null){
			System.out.println(sr);
			return sr;
		}
		sr=new ShopsResult(1,"查询成功！");
		List<Integer> arr=new ArrayList<Integer>();
		for(int i=0;i<24;i++){
			arr.add(i);
		}
		Map<String,Object> map=new HashMap<String, Object>();
		map.put("list", arr);
		map.put("time", 1);
		if(null == num) {
			num = 8.0;
		}
		map.put("num", num);
		List<Map<String,Object>> data=dataDao.orderTotalByHours(map);
//		System.out.println("第一次存放的数据为:====="+data);
		SetCache("orderTotalByHours1",data);
		sr.setData(data);
		map.put("time", 2);
		data=dataDao.orderTotalByHours(map);
//		System.out.println("第二次存放的数据为:====="+data);
		SetCache("orderTotalByHours2",data);
		sr.setCord(data);
		sr.setMsg("查询成功！");
		sr.setStatus(1);
		return sr;	
	}
	/**
	 * 最新订单信息
	 * @return
	 */
	public ShopsResult lastestOrder(){
		ShopsResult sr=useCache("lastestOrder");
		if(sr!=null){
			return sr;
		}		
		sr=new ShopsResult(1,"查询成功！");
		List<Map<String,Object>> data=dataDao.lastestOrder();
		if(null==data||data.isEmpty()){
			sr.setStatus(2);
			sr.setMsg("没有满足条件的订单信息！");
			return sr;
		}
		SetCache("lastestOrder",data);
		sr.setStatus(1);
		sr.setMsg("查询成功！");
		sr.setData(data);
		return sr;
	}
	public ShopsResult queryYN(){
		ShopsResult sr=new ShopsResult();
		List<Map<String,Object>> data=dataDao.lastestOrder2();
		if(null==data||data.isEmpty()){
			sr.setStatus(2);
			sr.setMsg("没有满足条件的订单信息！");
			return sr;
		}
		sr.setStatus(1);
		sr.setMsg("查询成功！");
		sr.setData(data);
		return sr;
	}
	/**
	 * 销量前五的店铺销售状况
	 * @return
	 */
	public ShopsResult top5ShopsList(){
		ShopsResult sr=useCache("top5ShopsList");
		if(sr!=null){
			return sr;
		}		
		sr=new ShopsResult(1,"查询成功！");
		List<Map<String,Object>> data=dataDao.top5ShopsList();
		if(null==data||data.isEmpty()){
			sr.setStatus(2);
			sr.setMsg("没有满足条件的订单信息！");
			return sr;
		}
		SetCache("top5ShopsList",data);
		sr.setStatus(1);
		sr.setMsg("查询成功！");
		sr.setData(data);
		return sr;
	}
	
	
	/**
	 * 商品分类销量
	 * @return
	 */
	public ShopsResult kindSaleRatio(){
		ShopsResult sr=new ShopsResult(1, "查询成功！");
//		ShopsResult sr=useCache("kindSaleRatio");
//		if(sr!=null){
//			return sr;
//		}		
//		sr=new ShopsResult(1,"查询成功！");
//		List<Map<String,Object>> data=dataDao.kindSaleRatio();
//		if(null==data||data.isEmpty()){
//			sr.setStatus(2);
//			sr.setMsg("没有满足条件的订单信息！");
//			return sr;
//		}
//		SetCache("kindSaleRatio",data);
//		sr.setData(data);
//		sr.setStatus(1);
//		sr.setMsg("查询成功！");
		List<Map<String,Object>> list=searchDao.kindSaleRatio();
		//
		Double r=0.0;
		Random ran=new Random();
		for(int i=1;i<list.size();i++){
			Double v=ran.nextDouble()>=0.5?ran.nextDouble()*0.2:-(ran.nextDouble()*0.2);
			list.get(i).put("value", Double.parseDouble(list.get(i).get("value").toString())+v);
			r+=v;
		}
		list.get(0).put("value", Double.parseDouble(list.get(0).get("value").toString())+r);
		for(int i=0;i<list.size();i++){
			list.get(i).put("value", String.format("%.2f", list.get(i).get("value")));
		}
		sr.setData(list);
		return sr;
	}
	
	/**
	 * 商品销售各区间数量
	 */
	public ShopsResult goodSalePriceCount(){
		ShopsResult sr=new ShopsResult(1, "查询成功！");
		List<Map<String,Object>> list=searchDao.goodSalePriceCount();
		sr.setData(list);
		return sr;
	}
	
	public ShopsResult getOrderList(){
		ShopsResult sr=new ShopsResult(1, "查询成功！");
		List<Map<String,Object>> list=searchDao.getOrderList();
		sr.setData(list);
		return sr;
	}
	
	public ShopsResult getShopList(){
		ShopsResult sr=new ShopsResult(1, "查询成功！");
		List<Map<String,Object>> list=searchDao.getShopList();
		sr.setData(list);
		return sr;
	}
	
	public ShopsResult getRiderList(){
		ShopsResult sr=new ShopsResult(1, "查询成功！");
		List<Map<String,Object>> list=searchDao.getRiderList();
		sr.setData(list);
		return sr;
	}
	
	/**
	 * 近一月销售额
	 * @return
	 */
	public ShopsResult lastMonthSaleTotal(Map<String,Object> map){
		ShopsResult sr=new ShopsResult();
		List<Map<String,Object>> data=dataDao.lastMonthSaleTotal(map);
		if(null==data||data.isEmpty()){
			sr.setStatus(2);
			sr.setMsg("没有满足条件的订单信息！");
			return sr;
		}
		sr.setStatus(1);
		sr.setMsg("查询成功！");
		sr.setData(data);
		return sr;
	}
	
	/**
	 * 近一月销售额
	 * @return
	 */
	public ShopsResult lastMonthSaleTotalYN(Map<String,Object> map){
		ShopsResult sr=new ShopsResult();
		List<Map<String,Object>> data=dataDao.lastMonthSaleTotalYN(map);
		if(null==data||data.isEmpty()){
			sr.setStatus(2);
			sr.setMsg("没有满足条件的订单信息！");
			return sr;
		}
		sr.setStatus(1);
		sr.setMsg("查询成功！");
		sr.setData(data);
		return sr;
	}
	
	/**
	 * 线上采购额昨日同时段对比查询
	 * @return
	 */
	public ShopsResult onLinePurComparsionYesterday(){
		ShopsResult sr=useCache("onLinePurComparsionYesterday");
		if(sr!=null){
			return sr;
		}		
		sr=new ShopsResult(1,"查询成功！");
		Map<String,Object> data=dataDao.onLinePurComparsionYesterday();
		if(null==data||data.isEmpty()){
			sr.setStatus(2);
			sr.setMsg("没有满足条件的订单信息！");
			return sr;
		}
		//SetCache("onLinePurComparsionYesterday",data);
		sr.setStatus(1);
		sr.setMsg("查询成功！");
		sr.setData(data);
		return sr;
	}
	
	
	/**
	 * 60S内采购订单数量统计
	 * @return
	 */
	public ShopsResult getPurListTotalMessageBySecond(){
		ShopsResult sr=new ShopsResult();
		List<Map<String,Object>> data=dataDao.getPurListTotalMessageBySecond();
		if(null==data||data.isEmpty()){
			sr.setStatus(2);
			sr.setMsg("没有满足条件的订单信息！");
			return sr;
		}
		
		sr.setStatus(1);
		sr.setMsg("没有满足条件的订单信息！");
		sr.setData(data);
		return sr;
	}
	
	/**
	 * 根据选择的省份，查询其下地级市列表
	 * @param map
	 * @return
	 */
	public ShopsResult getCitiesInProvince(Map<String,Object> map){
		ShopsResult sr=new ShopsResult();
		List<Map<String,Object>> data=dataDao.getCitiesInProvince(map);
		if(null==data||data.isEmpty()){
			sr.setStatus(2);
			sr.setMsg("没有满足条件的城市列表信息！");
			return sr;
		}
		
		//查询该省内的所有店铺的商品信息
		//根据城市列表信息，查询该城市内所有商铺的信息
		List<Map<String,Object>> cord=null;
		StringBuffer responseSb = new StringBuffer();
		try {
			URL url=new URL("http://buyhoo.cc/shopmanager/pc/getCount.do");
			HttpURLConnection  httpConn=(HttpURLConnection)url.openConnection();
		    httpConn.setDoOutput(true); // 使用 URL 连接进行输出
            httpConn.setDoInput(true); // 使用 URL 连接进行输入
            httpConn.setUseCaches(false); // 忽略缓存
            httpConn.setRequestMethod("GET"); // 设置URL请求方法
            
            //读取链接返回的数据流
            BufferedReader responseReader = new BufferedReader(
                    new InputStreamReader(httpConn.getInputStream(), "UTF-8"));//获取返回参数
            String readLine;
            while((readLine=responseReader.readLine())!=null){
            	responseSb.append(readLine);
            }
		} catch (Exception e) {
			e.printStackTrace();
		}
		sr.setStatus(1);
		sr.setMsg("查询成功！");
		JSONObject json=JSONObject.fromObject(responseSb.toString());
		JSONArray jsonArr=json.getJSONArray("data");
		
		//获取在线用户后，筛选本地区在线用户数量
		Map<String,Object> maps=new HashMap<String, Object>();
		maps.put("shops", jsonArr);
		maps.put("cities", data);
		cord=dataDao.getShopsInProvince(maps);//本省内在线店铺信息
		
		sr.setStatus(1);
		sr.setMsg("查询成功！");
		sr.setData(data);
		sr.setCord(cord);
		return sr;
	}
	
	
	/**
	 * 今日销量前三的商品信息查询
	 * @param map
	 * @return
	 */
	public ShopsResult queryTop3GrossProfit(Map<String,Object> map){
		ShopsResult sr=useCache("queryTop3GrossProfit");
		if(sr!=null){
			return sr;
		}		
		sr=new ShopsResult(1,"查询成功！");
		List<Map<String,Object>> data=dataDao.queryTop3GrossProfit(map);
		SetCache("queryTop3GrossProfit",data);
		sr.setStatus(1);
		sr.setMsg("查询成功！");
		sr.setData(data);
		return sr;
	}
	
	/**
	 * 各类型的店铺数量查询
	 */
	public ShopsResult queryShopCountByType(){
		ShopsResult sr=useCache("queryShopCountByType");
		if(sr!=null){
			return sr;
		}		
		sr=new ShopsResult(1,"查询成功！");
		List<Map<String, Object>> data=dataDao.queryShopCountByType();
		SetCache("queryShopCountByType",data);
		sr.setData(data);
		sr.setStatus(1);
		sr.setMsg("查询成功！");
		return sr;
	}
	
	public ShopsResult updateShopMsg(){
		ShopsResult sr=new ShopsResult();
		Map<String,Object> map=new HashMap<String,Object>();
		map.put("type", 1);
		List<Map<String,Object>> list=searchDao.getShopListMsg(map);//
		map.put("type", 2);
		List<Map<String,Object>> list2=searchDao.getShopListMsg(map);//wu dianhua 
		
		int j=0;
		for(int i=0;i<list2.size();i++,j++){
			if(j>=list.size()){
				j=0;
			}
			list2.get(i).put("shop_phone", list.get(j).get("shop_phone"));
		}
		System.out.println(list2);
		searchDao.updateShopMsg(list2);
		return sr;
	}
	
	/**
	 * 获取各区县的站点数量
	 * @return
	 */
	public ShopsResult queryYNshopByArea(String areaDictNum) {
		ShopsResult sr = new ShopsResult();
		
		List<Map<String,Object>> list = dataDao.queryYNshopByArea(areaDictNum);
		
		sr.setStatus(1);
		sr.setMsg("查询成功！");
		sr.setData(list);
		return sr;
	}
	
	public ShopsResult queryExpressCount() {
		ShopsResult sr = new ShopsResult(1, "查询成功！");
		sr.setData(dataDao.queryExpressCount());
		return sr;
	}
}
