package org.haier.shop.service;

import java.math.BigDecimal;
import java.util.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpUtil;
import org.apache.commons.lang3.StringUtils;
import org.haier.shop.dao.GoodsDao;
import org.haier.shop.dao.GoodsShelfStateDao;
import org.haier.shop.entity.goodsRecord.RecordGoods;
import org.haier.shop.entity.goodsRecord.RecordGoodsOper;
import org.haier.shop.entity.goodsRecord.RecordGoodsOperParams;
import org.haier.shop.enums.goodsEnum.DeviceSourceEnum;
import org.haier.shop.enums.goodsEnum.OperSourceEnum;
import org.haier.shop.enums.goodsEnum.OperTypeEnum;
import org.haier.shop.enums.goodsEnum.UserTypeEnum;
import org.haier.shop.util.MyException;
import org.haier.shop.util.PurResult;
import org.haier.shop.util.ShopsResult;
import org.haier.shop.util.UtilForJAVA;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Service("goodsShelfStateService")
public class GoodsShelfStateServiceImpl implements GoodsShelfStateService{

	@Resource
	private GoodsShelfStateDao goodsShelfStateDao;
	@Resource
	private GoodsDao goodsDao;
	
	@Override
	public PurResult queryShelfStateGoodsMessage(Map<String, Object> params) {
		PurResult result=new PurResult();			
		List<Map<String,Object>> goodsList=goodsShelfStateDao.queryShelfStateGoodsMessage(params);
		result.setStatus(1);
		result.setMsg("查询成功");
		result.setCount(goodsShelfStateDao.queryShelfStateGoodsMessageCount(params));
		result.setData(goodsList);
		return result;
	}
	
	/**
	 * 下架所有搜索商品
	 * @param params
	 * @return
	 */
	@Transactional
	public ShopsResult downQueryShelfStateGoodsMessage(Map<String,Object> params) {
		ShopsResult sr = new ShopsResult(1, "操作成功！");
		try {
			goodsShelfStateDao.downQueryShelfStateGoodsMessage(params);
		}catch (Exception e) {
			e.printStackTrace();
			throw new MyException(0, "操作失败！");
		}
		
		return sr;
	}

	@Override
	@Transactional
	public ShopsResult updateAllShelfState(String shop_unique,String state,String type) {
		ShopsResult result = new ShopsResult();
		try {
			Map<String ,Object> params = new HashMap<String, Object>();
			params.put("shop_unique", shop_unique);
			params.put("state", state);
			params.put("type", type);
			if(state.equals("1")){//上架
				//获取店铺商品售价是否有负数
				List<Map<String ,Object>> fuPriceGoodsList = goodsShelfStateDao.getGoodsSalePriceFu(params);
				if(fuPriceGoodsList.size()>0){
					String cord = "";
					for(int i=0;i<fuPriceGoodsList.size();i++){
						cord = cord + "<br/>"+fuPriceGoodsList.get(i).get("goods_name")+"("+fuPriceGoodsList.get(i).get("goods_barcode")+")";
					}
					if(!cord.equals("")){
						if(type.equals("1")){
							cord = cord + "<br/><span style='color:#FF5722;'>网购价为负，请修改网购价！</span>";
						}else{
							cord = cord + "<br/><span style='color:#FF5722;'>售价为负，请修改售价！</span>";
						}
						
					}
					result.setStatus(2);
					result.setMsg("异常");
					result.setCord(cord);
					return result;
				}
			}
			//修改全部上下架状态
			Integer count = goodsShelfStateDao.updateAllShelfState(params);
			if(count >0 ){
				result.setStatus(0);
				result.setMsg("成功！");
			}else{
				result.setStatus(1);
				result.setMsg("异常");
			}
			
		} catch (Exception e) {
			e.printStackTrace();
			result.setStatus(1);
			result.setMsg("异常");
		}
		return result;
	}
	
	@Override
	@Transactional
	public ShopsResult updateShelfState(String goods_ids, String shelf_state, String staff_id, HttpServletRequest request) {
		ShopsResult result = new ShopsResult();
		try {
			List<String> goods_id_list = Arrays.asList(goods_ids.split(","));
			List<RecordGoods> sourceGoodsList = goodsDao.selectGoodsShelfState(goods_id_list);
			Map<String ,Object> params = new HashMap<String, Object>();
			params.put("goods_ids", goods_id_list);
			params.put("shelf_state", shelf_state);
			Integer count = goodsShelfStateDao.updateShelfState(params);
			if(count >0 ){
				result.setStatus(0);
				result.setMsg("成功！");
				List<RecordGoodsOperParams> operParamsList = new ArrayList<>();
				for (RecordGoods sourceGoods : sourceGoodsList) {
					RecordGoods resultGoods = new RecordGoods();
					BeanUtil.copyProperties(sourceGoods, resultGoods);
					resultGoods.setShelfState(Integer.parseInt(shelf_state));
					//操作信息
					RecordGoodsOper recordGoodsOper = new RecordGoodsOper();
					if (StrUtil.isNotBlank(staff_id)) {
						recordGoodsOper.setUserId(staff_id);
					}
					recordGoodsOper.setGoodsId(Long.valueOf(resultGoods.getGoodsId()));
					recordGoodsOper.setGoodsBarcode(resultGoods.getGoodsBarcode());
					recordGoodsOper.setShopUnique(resultGoods.getShopUnique());
					recordGoodsOper.setOperType(OperTypeEnum.OPER_TYPE_UPDATE.getValue());
					recordGoodsOper.setDeviceSource(DeviceSourceEnum.PC_WEB.getValue());
					recordGoodsOper.setUserType(UserTypeEnum.SHOP_STAFF.getValue());
					recordGoodsOper.setCreateTtime(DateUtil.formatDateTime(DateUtil.date()));
					recordGoodsOper.setOperSource(OperSourceEnum.CHANGE_SHELF_STATE.getValue());
					recordGoodsOper.setDeviceSourceMsg(DeviceSourceEnum.PC_WEB.getLabel());
					RecordGoodsOperParams recordGoodsOperParams = new RecordGoodsOperParams();
					recordGoodsOperParams.setSourceGoods(sourceGoods);
					recordGoodsOperParams.setResultGoods(resultGoods);
					recordGoodsOperParams.setRecordGoodsOper(recordGoodsOper);
					operParamsList.add(recordGoodsOperParams);
				}
				String host = UtilForJAVA.getHostByServerName(request.getServerName());
				String url = StringUtils.join(host,"/shopmanager/record/recordGoodsOperList.do");
				String json = com.alibaba.fastjson.JSONObject.toJSONString(operParamsList);
				System.out.println("-------------------同步商品操作记录，url:"+ url);
				System.out.println("-------------------同步商品操作记录，参数:"+ json);
				String resultMsg = HttpUtil.post(url, json);
				System.out.println("-------------------同步商品操作记录，返回值:"+ resultMsg);
			}else{
				result.setStatus(1);
				result.setMsg("异常");
			}
			
		} catch (Exception e) {
			e.printStackTrace();
			result.setStatus(1);
			result.setMsg("异常");
		}
		return result;
	}
	
	@Override
	@Transactional
	public ShopsResult updatePcShelfState(String goods_ids, String pc_shelf_state, String staff_id, HttpServletRequest request) {
		ShopsResult result = new ShopsResult();
		try {
			List<String> goods_id_list = Arrays.asList(goods_ids.split(","));
			if (CollUtil.isNotEmpty(goods_id_list)) {
				List<RecordGoods> sourceGoodsList = goodsDao.selectGoodsShelfState(goods_id_list);
				Map<String ,Object> params = new HashMap<String, Object>();
				params.put("goods_ids", goods_id_list);
				params.put("pc_shelf_state", pc_shelf_state);
				Integer count = goodsShelfStateDao.updateShelfState(params);
				if(count >0 ){
					result.setStatus(0);
					result.setMsg("成功！");
					List<RecordGoodsOperParams> operParamsList = new ArrayList<>();
					for (RecordGoods sourceGoods : sourceGoodsList) {
						RecordGoods resultGoods = new RecordGoods();
						BeanUtil.copyProperties(sourceGoods, resultGoods);
						resultGoods.setPcShelfState(Integer.parseInt(pc_shelf_state));
						//操作信息
						RecordGoodsOper recordGoodsOper = new RecordGoodsOper();
						if (StrUtil.isNotBlank(staff_id)) {
							recordGoodsOper.setUserId(staff_id);
						}
						recordGoodsOper.setGoodsId(Long.valueOf(resultGoods.getGoodsId()));
						recordGoodsOper.setGoodsBarcode(resultGoods.getGoodsBarcode());
						recordGoodsOper.setShopUnique(resultGoods.getShopUnique());
						recordGoodsOper.setOperType(OperTypeEnum.OPER_TYPE_UPDATE.getValue());
						recordGoodsOper.setDeviceSource(DeviceSourceEnum.PC_WEB.getValue());
						recordGoodsOper.setUserType(UserTypeEnum.SHOP_STAFF.getValue());
						recordGoodsOper.setCreateTtime(DateUtil.formatDateTime(DateUtil.date()));
						recordGoodsOper.setOperSource(OperSourceEnum.CHANGE_PC_SHELF_STATE.getValue());
						recordGoodsOper.setDeviceSourceMsg(DeviceSourceEnum.PC_WEB.getLabel());
						RecordGoodsOperParams recordGoodsOperParams = new RecordGoodsOperParams();
						recordGoodsOperParams.setSourceGoods(sourceGoods);
						recordGoodsOperParams.setResultGoods(resultGoods);
						recordGoodsOperParams.setRecordGoodsOper(recordGoodsOper);
						operParamsList.add(recordGoodsOperParams);
					}
					String host = UtilForJAVA.getHostByServerName(request.getServerName());
					String url = StringUtils.join(host,"/shopmanager/record/recordGoodsOperList.do");
					String json = com.alibaba.fastjson.JSONObject.toJSONString(operParamsList);
					System.out.println("-------------------同步商品操作记录，url:"+ url);
					System.out.println("-------------------同步商品操作记录，参数:"+ json);
					String resultMsg = HttpUtil.post(url, json);
					System.out.println("-------------------同步商品操作记录，返回值:"+ resultMsg);
				}else{
					result.setStatus(1);
					result.setMsg("异常");
				}
			}
		} catch (Exception e) {
			e.printStackTrace();
			result.setStatus(1);
			result.setMsg("异常");
		}
		return result;
	}
	
	@Override
	@Transactional
	public ShopsResult updateGoodsSalePrice(String goods_id, String goods_sale_price, String goods_web_sale_price, String staff_id, HttpServletRequest request) {
		ShopsResult result = new ShopsResult();
		try {
			Map<String ,Object> params = new HashMap<String, Object>();
			params.put("goods_id", goods_id);
			params.put("goods_sale_price", goods_sale_price);
			params.put("goods_web_sale_price", goods_web_sale_price);
			Map<String, Object> goodsParams = new HashMap<>();
			goodsParams.put("goods_id", goods_id);
			RecordGoods sourceGoods = goodsDao.selectSourceGoods(goodsParams);
			if (ObjectUtil.isNotEmpty(sourceGoods)) {
				Integer count = goodsShelfStateDao.updateGoodsSalePrice(params);
				if(count >0 ){
					result.setStatus(0);
					result.setMsg("成功！");
				}else{
					result.setStatus(1);
					result.setMsg("异常");
				}
				RecordGoods resultGoods = new RecordGoods();
				BeanUtil.copyProperties(sourceGoods, resultGoods);
				if (StrUtil.isNotBlank(goods_sale_price)) {
					resultGoods.setGoodsSalePrice(new BigDecimal(goods_sale_price));
				}
				if (StrUtil.isNotBlank(goods_web_sale_price)) {
					resultGoods.setGoodsWebSalePrice(new BigDecimal(goods_web_sale_price));
				}
				//修改前的商品信息
				//操作信息
				RecordGoodsOper recordGoodsOper = new RecordGoodsOper();
				recordGoodsOper.setGoodsId(Long.valueOf(sourceGoods.getGoodsId()));
				if (StrUtil.isNotBlank(staff_id)) {
					recordGoodsOper.setUserId(staff_id);
				}
				recordGoodsOper.setGoodsBarcode(sourceGoods.getGoodsBarcode());
				recordGoodsOper.setShopUnique(sourceGoods.getShopUnique());
				recordGoodsOper.setOperType(OperTypeEnum.OPER_TYPE_UPDATE.getValue());
				recordGoodsOper.setDeviceSource(DeviceSourceEnum.PC_WEB.getValue());
				recordGoodsOper.setUserType(UserTypeEnum.SHOP_STAFF.getValue());
				recordGoodsOper.setCreateTtime(DateUtil.formatDateTime(DateUtil.date()));
				recordGoodsOper.setOperSource(OperSourceEnum.GOODS_CHANGE.getValue());
				recordGoodsOper.setDeviceSourceMsg(DeviceSourceEnum.PC_WEB.getLabel());
				RecordGoodsOperParams recordGoodsOperParams = new RecordGoodsOperParams();
				recordGoodsOperParams.setSourceGoods(sourceGoods);
				recordGoodsOperParams.setRecordGoodsOper(recordGoodsOper);
				recordGoodsOperParams.setResultGoods(resultGoods);
				String host = UtilForJAVA.getHostByServerName(request.getServerName());
				String url = StringUtils.join(host,"/shopmanager/record/recordGoodsOper.do");
				String json = com.alibaba.fastjson.JSONObject.toJSONString(recordGoodsOperParams);
				System.out.println("-------------------同步商品操作记录，url:"+ url);
				System.out.println("-------------------同步商品操作记录，参数:"+ json);
				String resultMsg = HttpUtil.post(url, json);
				System.out.println("-------------------同步商品操作记录，返回值:"+ resultMsg);
			}
		} catch (Exception e) {
			e.printStackTrace();
			result.setStatus(1);
			result.setMsg("异常");
		}
		return result;
	}
}
