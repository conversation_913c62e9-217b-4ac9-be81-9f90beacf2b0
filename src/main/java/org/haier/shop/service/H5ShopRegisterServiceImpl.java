package org.haier.shop.service;

import java.io.File;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.GregorianCalendar;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.SortedMap;
import java.util.TreeMap;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;

import org.haier.meituan.util.MUtil;
import org.haier.shop.dao.BeansDao;
import org.haier.shop.dao.CusLevelDao;
import org.haier.shop.dao.FeedBackDao;
import org.haier.shop.dao.GoldDao;
import org.haier.shop.dao.Goods_kindDao;
import org.haier.shop.dao.PayTypeDao;
import org.haier.shop.dao.ShopDao;
import org.haier.shop.dao.ShopFunctionDao;
import org.haier.shop.dao.ShopStaffDao;
import org.haier.shop.dao.SupplierDao;
import org.haier.shop.dao.UnicomAbleDao;
import org.haier.shop.dao.UtilDao;
import org.haier.shop.dao2.ShopTDao;
import org.haier.shop.dao3.ShopDeviceDao;
import org.haier.shop.dao3.ShopSoftDao;
import org.haier.shop.entity.ShopBeansVO;
import org.haier.shop.entity.ShopPayMsg;
import org.haier.shop.entity.Staff;
import org.haier.shop.entity.beans.PageData;
import org.haier.shop.oss.OSSUtil;
import org.haier.shop.util.Base64DecodeMultipartFile;
import org.haier.shop.util.ChineseCharToEn;
import org.haier.shop.util.HandleMessyCode;
import org.haier.shop.util.ServletsUtil;
import org.haier.shop.util.ShopsResult;
import org.haier.shop.util.ShopsUtil;
import org.haier.shop.util.UUIDUtil;
import org.haier.shop.util.wxPay.HttpUtil;
import org.haier.shop.util.wxPay.PayConfigUtil;
import org.haier.shop.util.wxPay.PayToolUtil;
import org.haier.shop.util.wxPay.XMLUtil4jdom;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;

@Service("H5ShopRegisterService")
public class H5ShopRegisterServiceImpl implements H5ShopRegisterService{
	@Resource
	private ShopDao shopDao;
	@Resource
	private ShopFunctionDao funDao;
	@Resource
	private Goods_kindDao kindDao;
	@Resource
	private ShopStaffDao staffDao;
	@Resource
	private  CusLevelDao levelDao;
	@Resource
	private UtilDao utilDao;
	@Resource
	private ShopTDao shopTDao;
	
	@Resource
	ShopDeviceDao shopDeviceDao;
	@Resource
	ShopSoftDao shopSoftDao;
	
	@Resource
	SupplierDao supplierDao;
	
	@Resource
	PayTypeDao payTypeDao;
	
	@Resource
	UnicomAbleDao unicomAbleDao;
	
	@Resource
	FeedBackDao feedBackDao;
	
	@Resource
	BeansDao beansDao;
	
	@Resource
	GoldDao goldDao;
	
	@Override
	@Transactional
	public ShopsResult register(String shop_name, String manager_account,String is_esign,String esign_num) {
		ShopsResult shop = new ShopsResult();
		Map<String,Object> map=new HashMap<String, Object>();
		Map<String,Object> pmap=new HashMap<String,Object>();
		
		Map<String ,Object> esignInfo = null;
		if(is_esign != null && !is_esign.equals("") && is_esign.equals("2")) {//需要签单
			//获取签单信息
			esignInfo = unicomAbleDao.queryUnicomAbleEsign(esign_num);
			if(esignInfo == null) {
				shop.setStatus(1);
				shop.setMsg("请先电子签单！");
				return shop;
			}
		}
		
		map.put("manager_account", manager_account);
		Map<String, Object> re=shopDao.login(map);
		if(re!=null){
			String examinestatus = MUtil.strObject(re.get("examinestatus"));
			if(examinestatus != null && !examinestatus.equals("") && examinestatus.equals("1")) {//未提交申请
				shop.setData(re.get("shop_unique"));
				shop.setStatus(0);
				shop.setMsg("注册成功");
				
				return shop;
			}
			shop.setStatus(1);
			shop.setMsg("该账户已注册！");
			return shop;
		}
		
		String manager_pwd = manager_account.substring(5, manager_account.length());
		String staff_pwd = ShopsUtil.string2MD5(manager_pwd.trim()).trim();
		map.put("shop_phone", manager_account);
		map.put("manager_pwd", staff_pwd);
		map.put("shop_name", shop_name);
		map.put("shop_alias", ChineseCharToEn.getAllFirstLetter(shop_name).trim());
		map.put("examinestatus", 1);//未提交申请
		map.put("shop_class", "0");//普通商家
		map.put("login_name", shop_name);
		String shop_unique = new Date().getTime()+"";
		Staff staff = new Staff();
		staff.setShop_unique(Long.parseLong(shop_unique));
		staff.setStaff_account(manager_account);
		staff.setStaff_pwd(staff_pwd);
		staff.setStaff_phone(manager_account);
		staff.setPwd_ok(manager_pwd);
		staff.setStaff_position(3);
		staff.setManager_unique(shop_unique);
		staff.setStaff_name(shop_name);
		map.put("shop_unique", shop_unique);
		map.put("shopUnique", shop_unique);
		map.put("manager_unique", shop_unique);//添加主管理员
		map.put("kindType", 2);//默认自定义分类
		int k = shopDao.register(map);//商家端注册新店铺
		if(k==0){
			shop.setStatus(1);
			shop.setMsg("注册失败！");
			return shop;
		}else{
			k=shopTDao.registerNewShop(map);
		}
		shop.setData(shop_unique);
		
		shop.setStatus(0);
		shop.setMsg("注册成功！");
		map.put("shopUnique", shop_unique);
		k=funDao.addNewShopFunction(shop_unique);//添加店铺功能权限
		kindDao.addNewGoodsKinds(shop_unique);//添加店铺商品分类
		levelDao.addNewCusLevel(map);//添加会员等级分类信息
		funDao.addNewShopsConfig(map);//添加店铺配置信息，店铺的图片上传和免密开通配置信息等
		//添加员工
		k=staffDao.newStaff(staff);
		pmap.put("staff_id", staff.getStaff_id());
		pmap.put("powerRecharge", "1");
		//添加员工权限
		pmap.put("powerManager", "1");
		k=staffDao.newStaffPower(pmap);
		
		//添加店铺升级记录，用于更新
		//1:查询系统的最新版本号，
		//2：添加记录
		String version= utilDao.theLastVersion();
		map.put("versionNumber", version);
		shopDao.newShopUpDateRecord(map);
		
		//添加注册时的图标信息（APP功能）
		shopDao.addShopTitle(map);
		
		//添加店铺与平台代理商关系
		Map<String ,Object> params = new HashMap<>();
		params.put("shop_unique", shop_unique);
		params.put("agency_code", "110");
		params.put("shop_name", shop_name);
		shopDeviceDao.addShop(params);
		
		//修改电子签单记录
		if(esignInfo != null) {
			Map<String ,Object> eSignParams = new HashMap<>();
			eSignParams.put("shop_unique", shop_unique);
			eSignParams.put("esign_num", esign_num);
			unicomAbleDao.updateUnicomAbleEsign(eSignParams);
		}
		
		return shop;
	}


	@Override
	public ShopsResult queryDeviceList() {
		ShopsResult result = new ShopsResult();
		try {
			//查询设备列表
			List<Map<String ,Object>> deviceList = shopDeviceDao.queryDeviceTypeList();
			//查询软件设置列表
			List<Map<String ,Object>> softList = shopSoftDao.querySysSoftSettingList();
			result.setData(deviceList);
			result.setCord(softList);
			result.setStatus(0);
			result.setMsg("成功！");
		} catch (Exception e) {
			e.printStackTrace();
			result.setStatus(1);
			result.setMsg("系统异常");
		}
		return result;
	}


	@Override
	public ShopsResult weChatPay(Map<String ,Object> params) {
		ShopsResult result = new ShopsResult();
		try {
			params.put("pay_type", "2");//微信
			Date date = new Date();
			String out_trade_no = String.valueOf(date.getTime());
			//设备业务-------------开始
			Integer device_fee = shopDevice(params, out_trade_no);
			
			//软件业务------------开始
			Integer soft_fee = shopSoft(params, out_trade_no);
			
			Integer total_fee = device_fee+soft_fee;
			
	        String appid = PayConfigUtil.APP_ID;  // appid  
	        String mch_id = PayConfigUtil.MCH_ID; // 商户号
	        String key = PayConfigUtil.API_KEY; // key  
	        
	        String currTime = PayToolUtil.getCurrTime();  
	        String strTime = currTime.substring(8, currTime.length());  
	        String strRandom = PayToolUtil.buildRandom(4) + "";  
	        String nonce_str = strTime + strRandom; 
	        // 回调接口   
	        String notify_url = PayConfigUtil.NOTIFY_URL_DEVICESOFT;
	        String trade_type = "JSAPI";
	        
	        SortedMap<Object,Object> packageParams = new TreeMap<Object,Object>();  
	        packageParams.put("appid", appid);  
	        packageParams.put("mch_id", mch_id);  
	        packageParams.put("openid", params.get("openid"));
	        packageParams.put("nonce_str", nonce_str);  
	        packageParams.put("body", "百货收银软件");  //（调整为自己的名称）
	        packageParams.put("out_trade_no", out_trade_no);  
	        packageParams.put("total_fee", total_fee.toString()); //价格的单位为分  
	        packageParams.put("spbill_create_ip", MUtil.strObject(params.get("spbill_create_ip")));  
	        packageParams.put("notify_url", notify_url);  
	        packageParams.put("trade_type", trade_type);
	        packageParams.put("scene_info", "{\"h5_info\": {\"type\":\"Wap\",\"wap_url\": \"http://buyhoo.cc\",\"wap_name\": \"金圈购买设备\"}}");
	  
	        String sign = PayToolUtil.createSign("UTF-8", packageParams,key);  
	        packageParams.put("sign", sign);
	          
	        String requestXML = PayToolUtil.getRequestXml(packageParams);  
	        System.out.println(requestXML);  
	   
	        String resXml = HttpUtil.postData(PayConfigUtil.UFDODER_URL, requestXML);  
	        
			Map map = XMLUtil4jdom.doXMLParse(resXml);
			String result_code = MUtil.strObject(map.get("result_code"));
			if(result_code.equals("SUCCESS")) {
				Map<String ,Object> rParams = new HashMap<>();
				rParams.put("out_trade_no", out_trade_no);
				rParams.put("appId", appid);
				rParams.put("timeStamp", MUtil.genTimeStamp());
				rParams.put("nonceStr", MUtil.getRandomString(32));
				rParams.put("prepay_id", MUtil.strObject(map.get("prepay_id")));
				rParams.put("key", key);
				result.setData(rParams);
				result.setStatus(0);
				result.setMsg("成功！");
			}else {
				result.setStatus(1);
				result.setMsg(MUtil.strObject(map.get("return_msg")));
			}
		} catch (Exception e) {
			e.printStackTrace();
			result.setStatus(1);
			result.setMsg("系统异常");
		}
		return result;
	}
	
	//软件申请业务
	public Integer shopDevice(Map<String ,Object> params,String out_trade_no) {
		List<Map<String, Object>> device_list = MUtil.strToList(MUtil.strObject(params.get("device_list")));
		if(device_list.size()>0) {
			String shop_unique = MUtil.strObject(params.get("shop_unique"));
			String apply_no = "DA"+out_trade_no;
			Map<String ,Object> deviceParams = new HashMap<>();
			//店铺上级代理商code
			String agency_code = "110";//默认系统平台
			String apply_id = UUIDUtil.getUUID32();
			//添加设备申请支付记录
			Map<String ,Object> devicePayParams = new HashMap<>();
			devicePayParams.put("pay_record_no", apply_no);
			devicePayParams.put("device_apply_id", apply_id);
			devicePayParams.put("pay_money", params.get("sum_deposit"));
			devicePayParams.put("pay_type", params.get("pay_type"));//支付类型：1支付宝 2微信
			shopDeviceDao.addShopDevicePay(devicePayParams);
			//添加申请主表
			deviceParams.put("id", apply_id);
			deviceParams.put("shop_unique", shop_unique);
			deviceParams.put("agency_code", agency_code);
			deviceParams.put("apply_no", apply_no);
			deviceParams.put("sum_deposit", params.get("sum_deposit"));
			deviceParams.put("sum_count", params.get("sum_count"));
			deviceParams.put("oper_name", params.get("shop_name"));
			deviceParams.put("apply_remarks", "");
			deviceParams.put("shop_name", params.get("shop_name"));
			deviceParams.put("pay_record_id", devicePayParams.get("pay_record_id"));
			shopDeviceDao.addShopDeviceApply(deviceParams);
			
			//添加申请详情表
			List<Map<String, Object>> detailList = new ArrayList<Map<String, Object>>();
			for(int i=0;i<device_list.size();i++) {
				String device_type = MUtil.strObject(device_list.get(i).get("device_type"));
				if(device_type.equals("1")) {//收银机
					Integer device_count = Integer.parseInt(MUtil.strObject(device_list.get(i).get("device_count")));
					for(int j=0;j<device_count;j++) {
						Map<String, Object> detail = new HashMap<String ,Object>(); 
						detail.put("apply_id", apply_id);
						detail.put("device_type_id", device_list.get(i).get("device_type_id"));
						detail.put("device_count", 1);
						detail.put("device_deposit", device_list.get(i).get("device_deposit"));
						detail.put("sum_deposit", device_list.get(i).get("device_deposit"));
						detailList.add(detail);
					}
				}else {//普通设备
					Map<String, Object> detail = new HashMap<String ,Object>(); 
					detail.put("apply_id", apply_id);
					detail.put("device_type_id", device_list.get(i).get("device_type_id"));
					detail.put("device_count", device_list.get(i).get("device_count"));
					detail.put("device_deposit", device_list.get(i).get("device_deposit"));
					detail.put("sum_deposit", device_list.get(i).get("sum_deposit"));
					detailList.add(detail);
				}
			}
			shopDeviceDao.addShopDeviceApplyDeatil(detailList);
			
	        Double sum_deposit = Double.valueOf(MUtil.strObject(params.get("sum_deposit")));
	        Integer total_fee = (int) (sum_deposit*100);
			return total_fee;
		}else {
			return 0;
		}
	}
	
	//软件购买业务
	public Integer shopSoft(Map<String ,Object> params,String out_trade_no) {
		String profit_no = "SP"+out_trade_no;
		String cdkey_code = MUtil.strObject(params.get("cdkey_code"));
		String shop_unique = MUtil.strObject(params.get("shop_unique"));
		String pay_type = MUtil.strObject(params.get("pay_type"));
		
		String soft_code = UUIDUtil.getUUID32();
		
		//获取软件配置信息
        String setting_code = MUtil.strObject(params.get("setting_code"));
        Map<String ,Object> setting = shopSoftDao.querySysSoftSetting(setting_code);
        Double price = Double.valueOf(MUtil.strObject(setting.get("price")));
        Double discount = Double.valueOf(MUtil.strObject(setting.get("discount")));
        Integer total_fee = (int) (price*discount*100);
		
		//添加商家购买软件记录表
		Map<String ,Object> shopSoftProfitParams = new HashMap<String ,Object>();
		shopSoftProfitParams.put("profit_no", profit_no);
		shopSoftProfitParams.put("soft_code", soft_code);
		shopSoftProfitParams.put("shop_unique", shop_unique);
		shopSoftProfitParams.put("pay_money", price*discount);
		shopSoftProfitParams.put("soft_time", setting.get("time"));
		shopSoftProfitParams.put("bug_type", "1");//购买
		shopSoftProfitParams.put("pay_type", pay_type);
		shopSoftProfitParams.put("setting_code", setting_code);
		shopSoftProfitParams.put("cdkey_code", cdkey_code);
		shopSoftProfitParams.put("device_no", "");
		shopSoftDao.addShopSoftProfit(shopSoftProfitParams);
		
		return total_fee;
	}
	
	
	@Override
	@Transactional
	public boolean deviceSoftPaySuccess(Map<String, Object> params) {
		boolean flag = false;
		try {
			String out_trade_no = MUtil.strObject(params.get("out_trade_no"));
			String trans_num = MUtil.strObject(params.get("trans_num"));
			String pay_money = MUtil.strObject(params.get("pay_money"));
			System.out.println("out_trade_no:"+out_trade_no);
			//硬件业务-----------------开始
			String apply_no = "DA"+out_trade_no;
			//修改硬件支付信息
			Map<String ,Object> deviceParams = new HashMap<>();
			deviceParams.put("apply_no", apply_no);
			deviceParams.put("is_pay", "2");//已支付
			shopDeviceDao.updateDeviceApply(deviceParams);
			deviceParams.put("trans_num", trans_num);
			deviceParams.put("pay_record_no", apply_no);
			deviceParams.put("pay_status", "2");//支付成功
			shopDeviceDao.updateDevicePay(deviceParams);
			
			//软件业务-----------------开始
			String profit_no = "SP"+out_trade_no;
			//获取软件购买记录信息
			Map<String ,Object> shopSoftProfitParams = new HashMap<String ,Object>();
			shopSoftProfitParams.put("profit_no", profit_no);
			Map<String ,Object> shopSoftProfit = shopSoftDao.getShopSoftProfit(shopSoftProfitParams);
			String soft_code = MUtil.strObject(shopSoftProfit.get("soft_code"));
			String setting_code = MUtil.strObject(shopSoftProfit.get("setting_code"));
			String cdkey_code = MUtil.strObject(shopSoftProfit.get("cdkey_code"));
			String shop_unique = MUtil.strObject(shopSoftProfit.get("shop_unique"));
			String shop_soft_profit_id = MUtil.strObject(shopSoftProfit.get("id"));
			String device_no = MUtil.strObject(shopSoftProfit.get("device_no"));
			
			Map<String ,Object> shopSoft = shopSoftDao.queryShopSoft(cdkey_code);
        	if(shopSoft != null) {//说明已经回调成功
        		return true;
        	}
			
			//获取软件配置信息
	        Map<String ,Object> setting = shopSoftDao.querySysSoftSetting(setting_code);
			
	        String due_time = "";
			Integer time = Integer.parseInt(MUtil.strObject(setting.get("time")));
			if(time != null && time == 0) {//永久
				due_time = "9999-01-01 00:00:00";
			}else {
				Calendar calendar = new GregorianCalendar();
				Date now = new Date();
				calendar.setTime(now); 
				calendar.add(calendar.YEAR, time);
				Date date = calendar.getTime();
	            SimpleDateFormat sdf1 = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
	            due_time = sdf1.format(date);
			}
	        
			//添加店铺软件信息
			Map<String ,Object> shopSoftParams = new HashMap<String ,Object>();
			shopSoftParams.put("soft_code", soft_code);
			shopSoftParams.put("cdkey_code", cdkey_code);
			shopSoftParams.put("shop_unique", shop_unique);
			shopSoftParams.put("due_time", due_time);
			shopSoftParams.put("device_no", device_no);
			shopSoftParams.put("status", 2);
			shopSoftDao.addShopSoft(shopSoftParams);
			
			//修改软件购买记录成功
			shopSoftProfitParams.put("pay_status", 2);//支付成功
			shopSoftProfitParams.put("trans_num", trans_num);
			shopSoftDao.updateShopSoftProfit(shopSoftProfitParams);
			
			Double pt_profit = Double.valueOf(pay_money);
			
			//修改总平台余额
			Map<String ,Object> ptAgencyAmountParams = new HashMap<String ,Object>(); 
			ptAgencyAmountParams.put("agency_code", "110");
			ptAgencyAmountParams.put("shop_profit", pt_profit);
			Map<String ,Object> account = shopSoftDao.queryAgencyAccount(ptAgencyAmountParams);
			if(account != null) {
				shopSoftDao.updateAgencyAccount(ptAgencyAmountParams);
			}else {
				shopSoftDao.addAgencyAccount(ptAgencyAmountParams);
			}
			
			//添加商家购买软件平台分润表
			Map<String ,Object> ptProfitParams = new HashMap<String ,Object>();
			ptProfitParams.put("shop_soft_profit_id", shop_soft_profit_id);
			ptProfitParams.put("agency_code", "110");
			ptProfitParams.put("agency_profit", shopSoftProfit.get("pay_money"));
			shopSoftDao.addShopSoftProfitDetail(ptProfitParams);
			
			//修改店铺状态为审核通过
			
			Map<String,Object> map = new HashMap<String, Object>();
			map.put("shop_unique", shop_unique);
			map.put("examinestatus", "4");
			map.put("examinestatus_reason", "注册时购买设备或软件");
				
			//审核通过(1.百货豆开通。2.分销功能开通3.返利功能开通，默认2%。4.金圈币默认开通。5.小程序默认开通)
			map.put("is_dis", "2");
			map.put("show_buy_status", "1");
			map.put("beans_agreement", "1");
			
			//添加百货豆默认抵扣比例信息
			PageData pd = new PageData();
			ShopBeansVO beanDetai = beansDao.getShopBeans(shop_unique);
			pd.put("diKou", beanDetai.getDiKou());
			pd.put("shopUnique", shop_unique);
			beansDao.addDikou(pd);
			//添加会员返利
			Map<String,Object> memParams = new HashMap<String, Object>();
			memParams.put("shop_unique", shop_unique);
			memParams.put("reward", "20");
			memParams.put("reward_type", "0");
			memParams.put("valid", "1");
			feedBackDao.addMemReward(memParams);
			//开通小程序
			Map<String, Object> goldRule = goldDao.queryGoldRule();
			goldRule.put("shop_unique", shop_unique);
			goldRule.put("device_num", "0");
			goldDao.addGlodShop(goldRule);
			goldRule.put("giveOut_type", "0");
			goldRule.put("start_date", new Date());
			goldDao.addGlodOrder(goldRule);
			//开通分销
			Map<String ,Object> disParams = new HashMap<String, Object>(); 
			disParams.put("shop_unique", shop_unique);
			List<Map<String ,Object>> disLevelList = shopDao.getDisLevelList(disParams);
			if(disLevelList.size() == 0) {
				disParams.put("shop_unique", "0");
				List<Map<String ,Object>> disLevelListMoren = shopDao.getDisLevelList(disParams);
				List<Map<String ,Object>> list = new ArrayList<Map<String ,Object>>();
				for(int i=0;i<disLevelListMoren.size();i++) {
					Map<String ,Object> maps = new HashMap<String, Object>();  
					maps.put("shop_unique", shop_unique);
					maps.put("dis_level_name", disLevelListMoren.get(i).get("dis_level_name"));
					maps.put("level", disLevelListMoren.get(i).get("level"));
					maps.put("one_commission_ratio", disLevelListMoren.get(i).get("one_commission_ratio"));
					maps.put("two_commission_ratio", disLevelListMoren.get(i).get("two_commission_ratio"));
					list.add(maps);
				}
				shopDao.addDisLevel(list);
			}
			feedBackDao.updateShopExamine(map);
				
			shopTDao.updateShopExamine(map);
			
			flag = true;
		} catch (Exception e) {
			e.printStackTrace();
		}
		return flag;
	}


	@Override
	public ShopsResult queryPayInfo(String out_trade_no) {
		ShopsResult result = new ShopsResult();
		try {
			Map<String ,Object> payInfo = shopSoftDao.queryPayInfo("SP"+out_trade_no);
			result.setData(payInfo);
			result.setStatus(0);
			result.setMsg("成功！");
		} catch (Exception e) {
			e.printStackTrace();
			result.setStatus(1);
			result.setMsg("系统异常");
		}
		return result;
	}


	@Override
	@Transactional
	public ShopsResult updateShopInfo(HttpServletRequest request) {
		ShopsResult result = new ShopsResult();
		try {
			Map<String, Object> params = HandleMessyCode.handle(ServletsUtil.getParameters(request));
			String shop_unique = MUtil.strObject(params.get("shop_unique"));
			String area_dict_num = MUtil.strObject(params.get("area_dict_num"));
			if(area_dict_num == null || area_dict_num.equals("")){
				Map<String ,Object> dictParams = new HashMap<String, Object>();
				dictParams.put("province", params.get("province"));
				dictParams.put("city", params.get("city"));
				dictParams.put("district", params.get("district"));
				//查询区的编号
				Map<String,Object> quCode = supplierDao.queryDistrict(dictParams);
				area_dict_num = (String) quCode.get("area_dict_num");
				params.put("area_dict_num", area_dict_num);
			}
			String pay_status = MUtil.strObject(params.get("pay_status"));
			if(pay_status.equals("2")) {//已支付
				
			}else {//未支付
				params.put("examinestatus", "2");//待审核
			}
			//修改店铺信息
			shopDao.updateShopDetail(params);
			
			Map<String, MultipartFile> mp= null;
			if(request instanceof MultipartHttpServletRequest){
				MultipartHttpServletRequest multipartHttpServletRequest =(MultipartHttpServletRequest)request;
				mp = multipartHttpServletRequest.getFileMap();
			}
			if(null != mp) {
				String manager_name = MUtil.strObject(params.get("manager_name"));
				String shop_address_detail = MUtil.strObject(params.get("shop_address_detail"));
				String manager_account = MUtil.strObject(params.get("manager_account"));
				//给出图片保存路径和图片文件信息，变量名信息，将图片保存并返回保存结果
				ShopPayMsg shopPayMsg = new ShopPayMsg();
				shopPayMsg.setShopUnique(shop_unique);
				shopPayMsg.setLegalPerson(manager_name);
				shopPayMsg.setUserPhone(manager_account);
				shopPayMsg.setShopAddress(shop_address_detail);
				shopPayMsg.setEmail(null);
				shopPayMsg.setSubBranch(null);
				
				String abpath = "/mnt/tomcat/imageBackups";//图片本地备份路径
				String path = File.separator+"images"+File.separator+"shopPayMsg"+File.separator+shop_unique+File.separator;
				String bucketName = "download-buyhoo";
				String p = "images/shopPayMsg/"+shop_unique+"/";
				shopPayMsg.setLicense(OSSUtil.savePic(bucketName, mp.get("license"), p));
				shopPayMsg.setDoorPhoto(OSSUtil.savePic(bucketName, mp.get("doorPhoto"), p));
				shopPayMsg.setIdentityBlack(OSSUtil.savePic(bucketName, mp.get("identityBlack"), p));
				shopPayMsg.setIdentityFront(OSSUtil.savePic(bucketName, mp.get("identityFront"), p));
				shopPayMsg.setBankCardBlack(OSSUtil.savePic(bucketName, mp.get("bankCardBlack"), p));
				shopPayMsg.setBankCardFront(OSSUtil.savePic(bucketName, mp.get("bankCardFront"), p));
				
				PayTypeServiceImpl.savePic(abpath,path,"license",mp);
				PayTypeServiceImpl.savePic(abpath,path,"doorPhoto",mp);
				PayTypeServiceImpl.savePic(abpath,path,"identityBlack",mp);
				PayTypeServiceImpl.savePic(abpath,path,"identityFront",mp);
				PayTypeServiceImpl.savePic(abpath,path,"bankCardBlack",mp);
				PayTypeServiceImpl.savePic(abpath,path,"bankCardFront",mp);
				
				payTypeDao.addNewShopPayMsg(shopPayMsg);
			}
			result.setData(shop_unique);
			result.setStatus(0);
			result.setMsg("成功！");
		} catch (Exception e) {
			e.printStackTrace();
			result.setStatus(1);
			result.setMsg("系统异常");
		}
		return result;
	}


	@Override
	public ShopsResult queryShopInfo(String shop_unique) {
		ShopsResult result = new ShopsResult();
		try {
			Map<String ,Object> payInfo = shopDao.getShopDetail(shop_unique);
			result.setData(payInfo);
			result.setStatus(0);
			result.setMsg("成功！");
		} catch (Exception e) {
			e.printStackTrace();
			result.setStatus(1);
			result.setMsg("系统异常");
		}
		return result;
	}


	@Override
	public ShopsResult queryESignInfo(String able_num) {
		ShopsResult result = new ShopsResult();
		try {
			//获取能人信息
			Map<String ,Object> params = new HashMap<String ,Object>();
			params.put("able_num", able_num);
			Map<String ,Object> unicomAble = unicomAbleDao.queryUnicomAbleDetail(params);
			if(unicomAble != null) {
				//获取电子合同信息
				Map<String ,Object> contractMap = unicomAbleDao.queryUnicomAbleContract();
				contractMap.put("unicom_able_id", unicomAble.get("unicom_able_id"));
				result.setData(contractMap);
				result.setStatus(0);
				result.setMsg("成功！");
			}else {
				result.setStatus(1);
				result.setMsg("能人不存在");
			}
			
		} catch (Exception e) {
			e.printStackTrace();
			result.setStatus(1);
			result.setMsg("系统异常");
		}
		return result;
	}


	@Override
	@Transactional
	public ShopsResult saveEsign(HttpServletRequest request) {
		ShopsResult result = new ShopsResult();
		try {
			Map<String, Object> params = HandleMessyCode.handle(ServletsUtil.getParameters(request));
			String unicom_able_id = MUtil.strObject(params.get("unicom_able_id"));
			String esign_base64 = MUtil.strObject(params.get("esign_base64"));
			if(null != esign_base64) {
				MultipartFile detailFile = Base64DecodeMultipartFile.base64Convert(esign_base64);
				//给出图片保存路径和图片文件信息，变量名信息，将图片保存并返回保存结果
				String abpath = "/mnt/tomcat/imageBackups";//图片本地备份路径
				String path = File.separator+"images"+File.separator+"unicomAbleEsign"+File.separator+unicom_able_id+File.separator;
				String bucketName = "download-buyhoo";
				String p = "images/unicomAbleEsign/"+unicom_able_id+"/";
				//上传OSS
				String esign_url = OSSUtil.savePic(bucketName, detailFile, p);
				//保存本地
				Map<String,MultipartFile> picMap = new HashMap<String, MultipartFile>();
				picMap.put("detailFile", detailFile);
				PayTypeServiceImpl.savePic(abpath,path,"detailFile",picMap);
				
				Date date = new Date();
				String esign_num = String.valueOf(date.getTime());
				params.put("esign_num", esign_num);
				params.put("esign_url", esign_url);
				unicomAbleDao.addUnicomAbleEsign(params);
				
				result.setData(esign_num);
				result.setStatus(0);
				result.setMsg("成功！");
			}
		} catch (Exception e) {
			e.printStackTrace();
			result.setStatus(1);
			result.setMsg("系统异常");
		}
		return result;
	}
	
}
