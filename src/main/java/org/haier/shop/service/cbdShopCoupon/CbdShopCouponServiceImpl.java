package org.haier.shop.service.cbdShopCoupon;

import cn.hutool.core.util.ObjectUtil;
import org.haier.shop.dao.cbd.CbdShopCouponDao;
import org.haier.shop.params.cbd.CbdShopCouponListParams;
import org.haier.shop.result.cbd.CbdShopCouponCountDtoResult;
import org.haier.shop.result.cbd.CbdShopCouponCountResult;
import org.haier.shop.result.cbd.CbdShopCouponDtoResult;
import org.haier.shop.util.ShopsResult;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.List;

/**
 * @ClassName CbdShopCouponServiceImpl
 * <AUTHOR>
 * @Date 2024/2/22 10:52
 */
@Service
@Transactional
public class CbdShopCouponServiceImpl implements CbdShopCouponService {
    @Autowired
    private CbdShopCouponDao cbdShopCouponDao;

    @Override
    public ShopsResult queryCouponRecordListWithPage(CbdShopCouponListParams params) {
        ShopsResult sr = new ShopsResult();
        if (params.getPage() != null && params.getPage() != null) {
            params.setPage((params.getPage() - 1) * params.getLimit());
        }
        List<CbdShopCouponDtoResult> list = cbdShopCouponDao.queryCouponRecordList(params);
        Integer count = cbdShopCouponDao.queryCouponRecordListCount(params);
        /*for (CbdShopCouponDtoResult cbdShopCouponDtoResult : list) {
            if ("已结算".equals(cbdShopCouponDtoResult.getSettlementStatusDesc())) {
                CbdShopInfoParams cbdShopInfoParams = new CbdShopInfoParams();
                cbdShopInfoParams.setShopUnique(params.getShopUnique());
                cbdShopInfoParams.setSettlementTime(DateUtil.format(cbdShopCouponDtoResult.getSettlementTime(), "yyyy-MM-dd HH:mm"));
                CbdShopInfoResult cbdShopInfoResult = couponPaymentSettlementDao.queryCbdShopInfo(cbdShopInfoParams);
                cbdShopCouponDtoResult.setLegalPerson(cbdShopInfoResult.getLegalPerson());
                cbdShopCouponDtoResult.setBankNo(cbdShopInfoResult.getBankNo());
            }
        }*/
        CbdShopCouponCountResult result = new CbdShopCouponCountResult(0L, BigDecimal.ZERO, 0L, BigDecimal.ZERO, 0L, BigDecimal.ZERO);
        List<CbdShopCouponCountDtoResult> cbdShopCouponCountResultList = cbdShopCouponDao.queryCouponRecordCount(params);
        if (ObjectUtil.isNotEmpty(cbdShopCouponCountResultList)) {
            for (CbdShopCouponCountDtoResult cbdShopCouponCountDtoResult : cbdShopCouponCountResultList) {
                result.setUseCount(result.getUseCount() + 1);
                result.setUseAmount(result.getUseAmount().add(cbdShopCouponCountDtoResult.getCouponActualityMoney()));
                if (cbdShopCouponCountDtoResult.getSettlementStatus() == 0) {
                    result.setUnSettlementCount(result.getUnSettlementCount() + 1);
                    result.setUnSettlementAmount(result.getUnSettlementAmount().add(cbdShopCouponCountDtoResult.getCouponActualityMoney()));
                } else if (cbdShopCouponCountDtoResult.getSettlementStatus() == 1) {
                    result.setSettlementCount(result.getSettlementCount() + 1);
                    result.setSettlementAmount(result.getSettlementAmount().add(cbdShopCouponCountDtoResult.getCouponActualityMoney()));
                }
            }
        }
        sr.setStatus(1);
        sr.setMsg("查询成功！");
        sr.setData(list);
        sr.setCount(count);
        sr.setCord(result);
        return sr;
    }
}
