package org.haier.shop.service;

import java.math.BigDecimal;
import java.util.Calendar;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Random;

import javax.annotation.Resource;

import org.haier.shop.dao.LogisticsDao;
import org.haier.shop.util.ShopsResult;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Service
@Transactional
public class LogisticsService{
	@Resource
	private LogisticsDao logisticsDao;
	
	public ShopsResult getSexList(){
		ShopsResult sr=new ShopsResult(1, "操作成功");
		List<Map<String,Object>> sexList=logisticsDao.getSexList();
		List<Map<String,Object>> ageList=logisticsDao.getAgeList();
		
		sr.setData(sexList);
		sr.setCord(ageList);
		return sr;
	}
	
	public ShopsResult getPeiSongList(){
		ShopsResult sr=new ShopsResult(1, "操作成功");
		List<Map<String,Object>> list=logisticsDao.getPeiSongList();
				
		sr.setData(list);
		return sr;
	}
	
	public ShopsResult getOneMinute(){
		ShopsResult sr=new ShopsResult(1, "操作成功");
		Map<String,Object> map=logisticsDao.getOneMinute();
		double d = Math.random()*0.8;
		Random random = new Random();
		int p = random.nextInt(5);//人
		map.put("time_peisong", new BigDecimal(d).add(new BigDecimal(map.get("time_peisong").toString())).setScale(2, BigDecimal.ROUND_HALF_UP).doubleValue());
		map.put("on_line_peisong", Integer.valueOf(map.get("on_line_peisong").toString())+p);
		sr.setData(map);
		return sr;
	}
	
	public ShopsResult getPeiSongTypeList(){
		ShopsResult sr=new ShopsResult(1, "操作成功");
		List<Map<String,Object>> list=logisticsDao.getPeiSongTypeList();
		
		sr.setData(list);
		return sr;
	}
	
	public ShopsResult getMonthActivity(){
		ShopsResult sr=new ShopsResult(1, "操作成功");
		Map<String,Object> map=logisticsDao.getMonthActivity();
		
		sr.setData(map);
		return sr;
	}
	
	public ShopsResult getOrderList(){
		ShopsResult sr=new ShopsResult(1, "操作成功");
		String[] dates=logisticsDao.getDateList();
		sr.setCord(dates);

		Map<String,Object> params=new HashMap<String,Object>();
		params.put("name", "步行配送");
		String[] data1=logisticsDao.getOrderList(params);		
		params.put("name", "自行车");
		String[] data2=logisticsDao.getOrderList(params);
		params.put("name", "电车");
		String[] data3=logisticsDao.getOrderList(params);
		params.put("name", "摩托车");
		String[] data4=logisticsDao.getOrderList(params);
		params.put("data1", data1);
		params.put("data2", data2);
		params.put("data3", data3);
		params.put("data4", data4);
		
		BigDecimal nowCount=new BigDecimal(logisticsDao.getNowCount());//今天订单量
		BigDecimal yesCount=new BigDecimal(logisticsDao.getYesterdayCount());//昨天订单
		BigDecimal hour=new BigDecimal("16");//定义一天16个小时 
		BigDecimal seven=new BigDecimal("7");//从7点开始 
		BigDecimal nowMinute=new BigDecimal(getMinute(new Date())).divide(new BigDecimal("60"),10,BigDecimal.ROUND_HALF_DOWN);
		BigDecimal nowHour=new BigDecimal(getHour(new Date())).add(nowMinute).subtract(seven);//当前小时数
		
		
		BigDecimal count=nowHour.multiply(yesCount).divide(hour,0,BigDecimal.ROUND_HALF_DOWN);//昨日此时的订单量
		//增长速度
		BigDecimal percent=(nowCount.subtract(count)).multiply(new BigDecimal("100")).divide(nowCount,2,BigDecimal.ROUND_HALF_DOWN);
		params.put("percent", percent);
		params.put("count", nowCount);
		sr.setData(params);
		return sr;
	}
		
	public int getHour(Date date) {
		Calendar calendar = null;
		calendar = Calendar.getInstance();
		calendar.setTime(date);
		return calendar.get(Calendar.HOUR_OF_DAY);
	}
	
	public int getMinute(Date date) {
		Calendar calendar = null;
		calendar = Calendar.getInstance();
		calendar.setTime(date);
		return calendar.get(Calendar.MINUTE);
	}
}
