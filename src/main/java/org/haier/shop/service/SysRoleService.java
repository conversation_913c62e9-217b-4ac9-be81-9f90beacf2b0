package org.haier.shop.service;

import java.util.List;
import java.util.Map;

import org.haier.shop.entity.SysAction;
import org.haier.shop.entity.SysPermission;
import org.haier.shop.entity.SysRole;

public interface SysRoleService {
	public List<SysRole> quertRoleList(Map<String ,Object> params);//获取角色列表
	
	public int quertRoleListCount(Map<String ,Object> params);//获取角色列表总条数
	
	public void insert(SysRole role);//添加角色
	
	public void update(SysRole role);//修改角色
	
	public SysRole getRole(SysRole role);//获取角色详情
	
	public void delete(String code);//删除角色
	
	public List<SysPermission> getMenuListByRoleCode(Map<String ,Object> params);//获取角色菜单列表
	
	public List<SysAction> getActionListByRoleCode(Map<String ,Object> params);//获取角色权限操作列表
	
	public void sitesRoleAuth(Map<String ,Object> params);//保存角色授权 
	
	public void sitesRoleAuth1(Map<String ,Object> params);//保存角色授权 
	
	public int getStaffByRoleCodeCount(Map<String ,Object> params);//获取该角色下员工数量
	
	public Map<String ,Object> getRoleByStaffId(Integer staff_id);//获取该员工角色
	
	public List<SysAction> getActionList(Map<String ,Object> params);//获取该权限归属所有操作列表
	
}
