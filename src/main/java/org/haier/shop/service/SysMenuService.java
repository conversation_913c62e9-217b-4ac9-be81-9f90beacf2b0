package org.haier.shop.service;

import java.util.List;
import java.util.Map;

import org.haier.shop.entity.SysPermission;

public interface SysMenuService {
	public List<SysPermission> quertMenuList(Map<String ,Object> params);//获取菜单列表
	public List<SysPermission> quertMenuList2(Map<String ,Object> params);//获取菜单列表
	public int quertMenuListCount(Map<String ,Object> params);//获取菜单列表总条数
	public void insert(SysPermission permission);//添加菜单
	public void update(SysPermission permission);//修改菜单
	public SysPermission getMenu(SysPermission permission);//获取菜单详情
	public void delete(String code);//删除菜单
	public int getRoleCountByMenuCode(String code);//获取该菜单授权角色个数
}
