package org.haier.shop.service;

import java.util.List;
import java.util.Map;

import javax.servlet.http.HttpServletRequest;

import org.haier.shop.util.PurResult;
import org.haier.shop.util.ShopsResult;

public interface LTService {

	
	public PurResult queryCusList(Map<String, Object> map);

	public PurResult updateCusStatus(Map<String, Object> map);

	public PurResult queryTpyList(Map<String, Object> map);

	public PurResult updateTpyInfo(Map<String, Object> map);

	public PurResult queryTeamList(Map<String, Object> map);

	public Map<String, Object> queryUserYJAllAndBalance(String id);

	public PurResult queryYJDetailList(Map<String, Object> map);

	public PurResult saveRecharge(Map<String, Object> map);

	public Map<String, Object> queryCusDetail(String id);

	public Map<String, Object> queryStaffAreaCode(String staff_id);

	public PurResult queryYjMoneyList(Map<String, Object> map);

	public PurResult queryFinanceList(Map<String, Object> map);

	public PurResult updateReturnMoneyConfig(String detailJson, HttpServletRequest request);

	public PurResult queryCashList(Map<String, Object> map);

	public PurResult updateCashStatus(Map<String, Object> map);

	public List<Map<String, Object>> queryCashListExcel(Map<String, Object> params);

	public List<Map<String, Object>> queryTpyListExcel(Map<String, Object> params);

	public void ltReturnMonthMoney();

	public List<Map<String, Object>> queryTpyImageList(Map<String, Object> params);

	public PurResult queryReturnMoneyConfigList();

	public PurResult deleteReturnMoneyConfigList(String id);

	public void ltReturnParentMonthMoney();

	public void ltReturnMonthCoupon();
	
}
