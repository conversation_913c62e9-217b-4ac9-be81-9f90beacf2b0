package org.haier.shop.service;

import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;

import org.haier.shop.dao.CustomerRechargeConfigDao;
import org.haier.shop.util.PurResult;
import org.haier.shop.util.ShopsResult;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Service
public class CustomerRechargeConfigServiceImpl implements CustomerRechargeConfigService{
	@Resource
	private CustomerRechargeConfigDao dao;
	
	
	@Transactional
	public ShopsResult addCusOffRechargeConfig(String rechargeName,Double money,String startTime,String endTime,String shopUnique,
			Integer isCoupon,String couponList,Integer isPoint,Double addPoint,Integer isGoods,String goodsList,Integer isBalance,Double addBalance,
			String isCusLevel,Integer cusLevelId
			) {
		ShopsResult sr = new ShopsResult(1,"新增成功!");
		
		//添加充值信息
		Map<String,Object> map = new HashMap<String,Object>();
		map.put("money", money);
		map.put("deleteStatus", "0");
		map.put("rechargeName", rechargeName);
		map.put("startTime", startTime);
		map.put("endTime", endTime);
		map.put("isCoupon", isCoupon);
		map.put("isPoint", isPoint);
		map.put("addPoint", addPoint);
		map.put("isGoods", isGoods);
		map.put("isBalance", isBalance);
		map.put("addBalance", addBalance);
		map.put("isCusLevel", isCusLevel);
		map.put("cusLevelId", cusLevelId);
		map.put("shopUnique", shopUnique);
		
		dao.addCusOffRechargeConfig(map);
		
		//查看是否有赠送优惠券
		if(null != couponList && !couponList.isEmpty()) {
			String[] cou = couponList.split(";");
			List<Map<String,Object>> list = new ArrayList<Map<String,Object>>();
			for(String c : cou) {
				Map<String,Object> cm = new HashMap<String,Object>();
				cm.put("recharge_config_id", map.get("recharge_config_id"));
				cm.put("shop_coupon_id", c.split(":")[0]);
				cm.put("coupon_count", c.split(":")[1]);
				list.add(cm);
			}
			
			if(null != list && !list.isEmpty()) {
				dao.addCusOffRechargeCoupon(list);
			}
		}
		
		if(null != goodsList && !goodsList.equals("")) {
			String[] goods = goodsList.split(";");
			List<Map<String,Object>> list = new ArrayList<Map<String,Object>>();
			for(String g : goods) {
				Map<String,Object> gm = new HashMap<String,Object>();
				gm.put("recharge_config_id", map.get("recharge_config_id"));
				gm.put("goods_id", g.split(":")[0]);
				gm.put("goods_count", g.split(":")[1]);
				list.add(gm);
			}
			if(null != list && !list.isEmpty()) {
				dao.addCusOffRechargeGoods(list);
			}
		}
		
		//查看是否有赠送商品
		
		
		return sr;
	}

	@Override
	public PurResult queryRechargeConfigList(Map<String, Object> params) {
		PurResult result=new PurResult();			
		List<Map<String,Object>> configList=dao.queryRechargeConfig(params);
		Integer count = dao.queryRechargeConfigPages(params);
		result.setStatus(1);
		result.setMsg("查询成功");
		result.setCount(count);
		result.setData(configList);
		return result;
	}

	@Override
	@Transactional
	public PurResult InsertRechargeConfig(Map<String, Object> params) {
		PurResult result=new PurResult();	
		params.put("create_time", new Date());
		params.put("flag", "1");
		dao.insertCustomer_recharge_config(params);
		result.setStatus(1);
		result.setMsg("保存成功!");
		return result;
	}
	
	@Override
	@Transactional
	public PurResult updateRechargeConfig(Map<String, Object> params) {
		PurResult result=new PurResult();	
		params.put("create_time", new Date());
		dao.updateCustomer_recharge_config(params);
		result.setStatus(1);
		result.setMsg("保存成功!");
		return result;
	}


	
	
	
}
