package org.haier.shop.service;

import java.util.List;
import java.util.Map;

import org.haier.shop.oil.RechargeDetail;
import org.haier.shop.util.PurResult;
import org.haier.shop.util.ShopsResult;


public interface GasOilService {

	public PurResult queryManagerOilgun(Map<String, Object> map);

	public PurResult queryManagerOils(Map<String, Object> map);

	public PurResult queryOilSaleList(Map<String, Object> map);

	public PurResult queryGasOrderDetail(String sale_list_unique, String shop_unique);

	public List<Map<String, Object>> saleListExcel(Map<String, Object> params);

	public PurResult queryGasRechargeList(Map<String, Object> params);

	public PurResult queryGasRechargeDetail(String sale_list_unique);

	public PurResult queryCouponName(Map<String, Object> params);

	public PurResult queryCouponCount(Map<String, Object> params);

	public PurResult queryGoodsName(Map<String, Object> params);

	public PurResult queryGoodsCount(Map<String, Object> params);

	public PurResult queryAddBalance(Map<String, Object> params);

	public PurResult queryCusLevelName(Map<String, Object> params);

	public List<Map<String,Object>> rechargeListExcel(Map<String, Object> params);

}
