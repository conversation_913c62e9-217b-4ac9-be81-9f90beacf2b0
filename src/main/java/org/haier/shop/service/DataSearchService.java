package org.haier.shop.service;

import org.haier.shop.util.ShopsResult;

public interface DataSearchService {
	/**
	 * 大屏左上角，到店人数数量统计
	 * @return
	 */
	public ShopsResult peopleArrivingSearch();
	
	/**
	 * 在线点数数量统计
	 * @return
	 */
	public ShopsResult shopOnLineStatis();

	/**
	 * 明星店铺统计
	 * @return
	 */
	public ShopsResult theBestSallerShop();
	
	/**
	 * 各阶段支付金额统计
	 * @return
	 */
	public ShopsResult getListIncomeStage();
	

	/**
	 * 近一月商品价格走势图
	 * @return
	 */
	public ShopsResult goodsPriceTrend();
	
	
	/**
	 * 查询某个商品三个月内的售价浮动情况
	 * @return
	 */
	public ShopsResult getPriceFloat();
	/**
	 * 查询某商品近一月的售价浮动情况
	 * @return
	 */
	public ShopsResult getPriceDiff(Integer goodsType);
	
	/**
	 * 今日订单量及客单值查询
	 * @return
	 */
	public ShopsResult queryTotalStatistics();
	
	/**
	 * 小程序实时在线用户量
	 * @return
	 */
	public ShopsResult queryOnlineUserCount();
	
	public ShopsResult queryOnlineUserCountNew();
	
	/**
	 * 线上线下订单量对比
	 * @return
	 */
	public ShopsResult queryLineCount();
	
	/**
	 * 更新店铺名单
	 * @return
	 */
	public ShopsResult updateShopName();
}
