package org.haier.shop.service;

import java.io.InputStream;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.UUID;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;

import org.haier.shop.dao.LoanDao;
import org.haier.shop.dao.LoanMoneyDao;
import org.haier.shop.util.FTPConfig;
import org.haier.shop.util.JPushClientUtil;
import org.haier.shop.util.PurResult;
import org.haier.shop.util.SFTPUtil;
import org.haier.shop.util.ShopsResult;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;

import com.google.gson.JsonObject;

@Service
public class LoanMoneyServiceImpl implements LoanMoneyService{
	
	@Resource
	private LoanMoneyDao loanMoneyDao;
	
	@Resource
	private LoanDao loanDao;
	
	
	@Override
	public Map<String, Object> queryIsOpenLoan(String shop_unique) {
		Map<String,Object> params=new HashMap<>();
		params.put("shop_unique", shop_unique);
		return loanMoneyDao.queryIsOpenLoan(params);
	}


	@Override
	public ShopsResult addOpenLoan(Map<String, Object> map, HttpServletRequest request) {
		ShopsResult sr=new ShopsResult();
		String shop_unique=map.get("shop_unique").toString();
		SFTPUtil sftp = new SFTPUtil(FTPConfig.username, FTPConfig.password, FTPConfig.host, FTPConfig.port);   
        sftp.login(); 
		MultipartFile file2=null;
		if(request instanceof MultipartHttpServletRequest){
			MultipartHttpServletRequest multipartHttpServletRequest =(MultipartHttpServletRequest)request;
			Map<String, MultipartFile> mp = multipartHttpServletRequest.getFileMap();
			file2 = mp.get("shop_picture2");
		}
		if(file2!=null){
			String orName=file2.getOriginalFilename();
			String lastName=orName.substring(orName.lastIndexOf("."));
			String newName=UUID.randomUUID()+lastName;
			String filePathDetail="/"+"image"+"/"+shop_unique;
			  
	        InputStream is;
			try {
				is = file2.getInputStream();
				boolean flag=sftp.upload(FTPConfig.goods_path+"/"+shop_unique, newName, is);   
			} catch (Exception e) {
				e.printStackTrace();
			}   
	      
	        String goods_picturepath="http://file.buyhoo.cc"+filePathDetail+"/"+newName;
			map.put("business_image", goods_picturepath);
		}
		MultipartFile file3=null;
		if(request instanceof MultipartHttpServletRequest){
			MultipartHttpServletRequest multipartHttpServletRequest =(MultipartHttpServletRequest)request;
			Map<String, MultipartFile> mp = multipartHttpServletRequest.getFileMap();
			file3 = mp.get("shop_picture3");
		}
		if(file3!=null){
			String orName=file3.getOriginalFilename();
			String lastName=orName.substring(orName.lastIndexOf("."));
			String newName=UUID.randomUUID()+lastName;
			String filePathDetail="/"+"image"+"/"+shop_unique;
			
			InputStream is;
			try {
				is = file3.getInputStream();
				boolean flag=sftp.upload(FTPConfig.goods_path+"/"+shop_unique, newName, is);   
			} catch (Exception e) {
				e.printStackTrace();
			}   
			
			String goods_picturepath="http://file.buyhoo.cc"+filePathDetail+"/"+newName;
			map.put("bank_image", goods_picturepath);
		}
		MultipartFile file4=null;
		if(request instanceof MultipartHttpServletRequest){
			MultipartHttpServletRequest multipartHttpServletRequest =(MultipartHttpServletRequest)request;
			Map<String, MultipartFile> mp = multipartHttpServletRequest.getFileMap();
			file4 = mp.get("shop_picture4");
		}
		if(file4!=null){
			String orName=file4.getOriginalFilename();
			String lastName=orName.substring(orName.lastIndexOf("."));
			String newName=UUID.randomUUID()+lastName;
			String filePathDetail="/"+"image"+"/"+shop_unique;
			
			InputStream is;
			try {
				is = file4.getInputStream();
				boolean flag=sftp.upload(FTPConfig.goods_path+"/"+shop_unique, newName, is);   
			} catch (Exception e) {
				e.printStackTrace();
			}   
			
			String goods_picturepath="http://file.buyhoo.cc"+filePathDetail+"/"+newName;
			map.put("ID_front_image", goods_picturepath);
		}
		MultipartFile file5=null;
		if(request instanceof MultipartHttpServletRequest){
			MultipartHttpServletRequest multipartHttpServletRequest =(MultipartHttpServletRequest)request;
			Map<String, MultipartFile> mp = multipartHttpServletRequest.getFileMap();
			file5 = mp.get("shop_picture5");
		}
		if(file5!=null){
			String orName=file5.getOriginalFilename();
			String lastName=orName.substring(orName.lastIndexOf("."));
			String newName=UUID.randomUUID()+lastName;
			String filePathDetail="/"+"image"+"/"+shop_unique;
			
			InputStream is;
			try {
				is = file5.getInputStream();
				boolean flag=sftp.upload(FTPConfig.goods_path+"/"+shop_unique, newName, is);   
			} catch (Exception e) {
				e.printStackTrace();
			}   
			
			String goods_picturepath="http://file.buyhoo.cc"+filePathDetail+"/"+newName;
			map.put("ID_back_image", goods_picturepath);
		}
		
		
		sftp.logout();
		if(map.get("id")!=null&&!"".equals(map.get("id").toString())){
			loanMoneyDao.editOpenLoan(map);
		}else{
			loanMoneyDao.addOpenLoan(map);
		}
		
		sr.setStatus(1);
		return sr;
	}


	@Override
	public void saveReturnMoney(Map<String, Object> params) {
		loanMoneyDao.saveReturnMoney(params);
	}


	@Override
	@Transactional
	public void updateReturnMoney(String out_trade_no) {
		System.out.println("当前回调成功的订单号为" + out_trade_no);
		//查询订单状态
		Map<String,Object> params=new HashMap<>();
		params.put("sale_list_unique", out_trade_no);
		Map<String,Object> order= loanMoneyDao.queryReturnMoneyOrder(params);
		if("1".equals(order.get("pay_status").toString())){
			//修改订单状态
			loanMoneyDao.updateReturnMoneyOrderStatus(params);
			//用于更新分期列表
			List<Map<String,Object>> updateFenqiList = new ArrayList<Map<String,Object>>();
			
			/*
			 * 1更新待还款情况
			 * 2由于提前还款的金额会比正常还款多，所以需要计算出提前还款后，应减少的提前还款金额
			 * 3本金可采用 principal_money
			 * 4还款金额由sx_fenqi_log分笔累计，不超过分笔的和
			 */
			
			Map<String,Object> map = new HashMap<String,Object>();
			BigDecimal notBackMoney = new BigDecimal(0);
			
			
			/*
			 * 1、查询当前订单的还款本金和总额信息
			 * 2、查询未还款分日还款信息，按日期和订单赠序排序
			 * 3、依次修改未还款的信息
			 */
			Map<String,Object> retMap = loanMoneyDao.queryReturnMoneyOrder(params);
			if(null != retMap && !retMap.isEmpty()) {
				BigDecimal principal_money = new BigDecimal(retMap.get("principal_money").toString());
				map.put("balance_loan_money", principal_money);
				BigDecimal return_money = new BigDecimal(retMap.get("return_money").toString());
				List<Map<String,Object>> fenQiList = loanMoneyDao.queryUnDoFenQiLog(retMap);

				if(null != fenQiList && !fenQiList.isEmpty()) {
					for(Map<String,Object> tempMap : fenQiList) {
						BigDecimal needBackMoney = new BigDecimal(tempMap.get("needBackMoney").toString());
						BigDecimal needBackPrincipal = new BigDecimal(tempMap.get("needBackPrincipal").toString());
						
						if(needBackMoney.compareTo(new BigDecimal(0)) <= 0 || needBackPrincipal.compareTo(new BigDecimal(0)) == 0) {
							tempMap.put("status", "2");
							updateFenqiList.add(tempMap);
							continue;
						}
						
						if(principal_money.compareTo(needBackPrincipal) > 0) {
							tempMap.put("status", 2);
							//可用分账余额
							principal_money = principal_money.subtract(needBackPrincipal).setScale(2,BigDecimal.ROUND_HALF_UP);
							
							if(return_money.subtract(needBackMoney).compareTo(principal_money) >= 0) {
								return_money = return_money.subtract(needBackMoney).setScale(2,BigDecimal.ROUND_HALF_UP);
							}else {
								tempMap.put("needBackMoney", return_money.subtract(principal_money));
								return_money = principal_money;
							}
							updateFenqiList.add(tempMap);
							notBackMoney = notBackMoney.add(needBackMoney);
							
						}else if(principal_money.compareTo(needBackPrincipal) == 0){
							tempMap.put("status", 2);
							tempMap.put("needBackMoney", return_money);
							updateFenqiList.add(tempMap);
							notBackMoney = notBackMoney.add(needBackMoney);
						}else {
							//如果未还清，需要计算already_money应还的金额，防止already_money已超过fenqi_money,但是already_principal_money已等于principal_money
							BigDecimal newReturnMoney = principal_money.multiply(needBackMoney).divide(needBackPrincipal,2,BigDecimal.ROUND_HALF_UP);
							tempMap.put("needBackPrincipal", principal_money);
							tempMap.put("needBackMoney", newReturnMoney);
							updateFenqiList.add(tempMap);
							notBackMoney = notBackMoney.add(newReturnMoney);
						}
						
					}
				}
				
				if(null != updateFenqiList && !updateFenqiList.isEmpty()) {
					//更新分期日志
					loanMoneyDao.refreshFenqiLog(updateFenqiList);
				}
				
				map.put("shop_unique", order.get("shop_unique"));
				map.put("not_return_money", notBackMoney);
				
				loanMoneyDao.rebackShopLoanMsg(map);
			}
			//推送安卓收银机
			Map<String,Object> shop= loanMoneyDao.queryShopJGId(order);
			JsonObject js=new JsonObject();
			js.addProperty("type", 2);
			js.addProperty("out_trade_no", out_trade_no);
			JPushClientUtil.notifyAndoidPos(shop.get("pc_registration_id").toString(), "消息通知", 2, js.toString());
			System.out.println("推送成功");
			
		}
		
	}


	@Override
	public ShopsResult queryOrderStatus(Map<String, Object> map) {
		ShopsResult rs=new ShopsResult();
		Map<String,Object> order= loanMoneyDao.queryReturnMoneyOrder(map);
		rs.setData(order);
		return rs;
	}


	@Override
	public PurResult queryLoanReturnList(Map<String, Object> map) {
		PurResult result = new PurResult();
		try {
			List<Map<String,Object>> list = loanMoneyDao.queryLoanReturnList(map);
	    	Integer count = loanMoneyDao.queryLoanReturnListCount(map);
	    	//统计总量
	    	Map<String,Object> statisMap = loanMoneyDao.queryLoanReturnStatistics(map);
	    	result.setStatus(1);
			result.setMsg("成功");
			result.setCount(count);
			result.setData(list);
			result.setCord(statisMap);
		} catch (Exception e) {
			e.printStackTrace();
			result.setStatus(0);
			result.setMsg("异常");
		}
		return result;
	}


	@Override
	public PurResult queryLoanList(Map<String, Object> map) {
		PurResult result = new PurResult();
		try {
			List<Map<String,Object>> list = loanMoneyDao.queryLoanList(map);
	    	Integer count = loanMoneyDao.queryLoanListCount(map);
	    	result.setStatus(1);
			result.setMsg("成功");
			result.setCount(count);
			result.setData(list);
		} catch (Exception e) {
			e.printStackTrace();
			result.setStatus(0);
			result.setMsg("异常");
		}
		return result;
	}


	@Override
	public Map<String, Object> queryAdvanceMoney(String shop_unique) {
		Map<String,Object> params=new HashMap<>();
		params.put("shop_unique", shop_unique);
		Map<String,Object> order= loanMoneyDao.queryAdvanceMoney(params);
		//查询提前还款赊销规则
		
		Map<String,Object> map = loanDao.queryLoanPolicy();
		if(null != map && order != null) {
			BigDecimal rate = new BigDecimal(map.get("breach_rate").toString());
			BigDecimal advance_money = new BigDecimal(order.get("advance_money").toString());
			order.put("principal_money", advance_money);
			BigDecimal wyj = advance_money.multiply(rate).setScale(2, BigDecimal.ROUND_HALF_UP);
			order.put("wyj", wyj);
			advance_money = advance_money.add(advance_money.multiply(rate).setScale(2, BigDecimal.ROUND_HALF_UP)).setScale(2,BigDecimal.ROUND_UP);
			order.put("advance_money", advance_money);
		}else {
			map.put("advance_money", "0.0");
		}
		
		return order;
	}
}
