package org.haier.shop.service;

import java.util.List;
import java.util.Map;

import javax.servlet.http.HttpServletRequest;

import org.haier.shop.util.PurResult;

public interface BusinessService {
	
	/**
	 * 查询列表
	 * @return
	 */
	public PurResult queryBusinessList(Map<String,Object> params);
	
	/**
	 * 添加
	 * @return
	 */
	public PurResult addBusiness(HttpServletRequest request);
	
	/**
	 * 获取详情
	 * @return
	 */
	public Map<String ,Object> queryBusinessDetail(String id);
	
	public List<Map<String ,Object>> queryRedOrderStatistics(Map<String,Object> params);
	
	/**
	 * 修改
	 * @return
	 */
	public PurResult updateBusiness(HttpServletRequest request);
	
	/**
	 * 删除
	 * @return
	 */
	public PurResult deleteBusiness(String id);
	
	/**
	 * 联通新卡用户红包导入
	 * @return
	 */
	public PurResult unicomUserImport(List<Map<String,Object>> list);
	
	/**
	 * 查询能人列表
	 * @return
	 */
	public PurResult queryRedPersonList(Map<String,Object> params);
	
	public PurResult queryRedOrderList(Map<String,Object> params);
	
	public PurResult queryPlatformMsg(Map<String,Object> params);
	
	public PurResult getStatistics(Map<String,Object> params);
	
	/**
	 * 查询能人佣金列表
	 * @return
	 */
	public PurResult queryRedPersonCommissionList(Map<String,Object> params);
	
	/**
	 * 查询能人提现列表
	 * @return
	 */
	public PurResult queryRedPersonWidthList(Map<String,Object> params);
	
	/**
	 * 定时任务：发放会员红包
	 * @return
	 */
	public void grantCusRedPacket();
	
	/**
	 * 定时任务：红包过期操作
	 * @return
	 */
	public void redPacketOverdue();
	
	/**
	 * 能人确认提现
	 * @return
	 */
	public PurResult weixinTransfers(String withd_id ,String spbill_create_ip);
	
	/**
	 * 联通能人佣金导入
	 * @return
	 */
	public PurResult unicomPersonCommissionImport(List<Map<String,Object>> list);
	
	/**
	 * 查询红包发放记录列表
	 * @return
	 */
	public PurResult queryRedPacketList(Map<String,Object> params);
}
