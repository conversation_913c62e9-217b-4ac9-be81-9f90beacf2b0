package org.haier.shop.service;

import java.util.List;
import java.util.Map;

import org.haier.shop.entity.SysAction;

public interface SysActionService {
	public List<SysAction> quertList(Map<String ,Object> params);//获取操作列表
	public int quertListCount(Map<String ,Object> params);//获取操作列表总条数
	public void insert(SysAction action);//添加操作
	public void update(SysAction action);//修改操作
	public SysAction getAction(SysAction action);//获取操作详情
	public void delete(String code);//删除操作
}
