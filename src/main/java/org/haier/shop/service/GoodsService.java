package org.haier.shop.service;

import java.util.List;
import java.util.Map;

import javax.servlet.http.HttpServletRequest;

import org.haier.shop.entity.GoodsInfo;
import org.haier.shop.util.PurResult;
import org.haier.shop.util.ShopsResult;
import org.springframework.web.multipart.MultipartFile;

public interface GoodsService {
	
	public ShopsResult importGoodsKindMsg(String goodsMsg,String shopUnique);
	 public ShopsResult queryGoodsKindMsg(String goodsMsg,String shopUnique);
	 /**
     * 保存商品修改信息
     * @param shopUnique 店铺编码
     * @param goodsMsg 商品信息，格式：商品条码：新增库存，新的商品进价，新的商品售价
     * @return
     */
	public ShopsResult importGoodsBaseMsg(String shopUnique,String goodsMsg,HttpServletRequest request);
	/**
     * 查询商品的基础信息
     * @param goodsMsg 商品基本信息
     * @param shopUnique
     * @return
     */
    public ShopsResult queryGoodsBaseMsg(String goodsMsg,String shopUnique);
	/**
	 * 添加云端商品
	 * @param goods_barcode 商品条码
	 * @param goods_name 商品名称
	 * @param goods_in_price 商品进价
	 * @param goods_sale_price 商品售价
	 * @param goods_cus_price 商品会员价
	 * @param goods_count 库存数量
	 * @param goods_standard 商品规格
	 * @param goods_unit 销售单位
	 * @param goods_picturepath 图片
	 * @param addIndex 前端生产的添加序号，防止重复提交
	 * @param request 
	 * @return
	 */
	public ShopsResult addNewGoodsByCloud(String goods_barcode,String goods_name,Double goods_in_price,Double goods_sale_price,Double goods_cus_price,
			Double goods_count,String goods_standard,String goods_unit,String goods_picturepath,HttpServletRequest request,Integer addIndex);
	/**
	 * 从大库中查询商品
	 * @param page
	 * @param pageSize
	 * @param goodsMsg
	 * @param goodsList
	 * @return
	 */
	public ShopsResult queryBaseGoods(Integer page, Integer pageSize, String goodsMsg, String goodsList,String shopUnique);
	/**
	 * 删除首页商品信息
	 * @param shopUnique
	 * @param goods_barcode
	 * @return
	 */
	public ShopsResult deleteGoodsIndex(String shopUnique, String goods_barcode);
	/**
	 * 修改商品列表
	 * @param shopUnique
	 * @param goodsBarcodes
	 * @return
	 */
	public ShopsResult addNewGoodsIndex(String shopUnique,String goodsBarcodes);
	/**
	 * 查询小程序首页显示的商品信息
	 * @param shopUnique
	 * @return
	 */
	public ShopsResult queryGoodsIndexSort(String shopUnique);
	/**
	 * 1、查询加油站品牌信息
	 * @param goodsBarcode
	 * @param shopUnique
	 * @return
	 */
	public Map<String,Object> getOilGoodsDetail(String goodsBarcode,String shopUnique);
	/**
	 * 删除油品信息
	 * @param goodsBarcode 商品编号
	 * @param shopUnique 店铺编号
	 * @param staffId 操作员工
	 * @param equipmentType 设备类型：1、；2、；3、网页
 	 * @param macId 设备编号
 	 * @param gunNum 油枪号
 	 * @param oilNum 油号
	 * @return
	 */
	public ShopsResult deleteOilGoods(String goodsBarcode,String shopUnique,Integer staffId,Integer equipmentType,String macId,String gunNum,String oilNum);
	/**
	 * 新增或添加商品信息
	 * @param shopUnique 店铺编号
	 * @param gunNum 油枪编号
	 * @param oilNum 油号
	 * @param goodsSalePrice 商品售价
	 * @param goodsInPrice 商品进价
	 * @param goodsCount 商品库存
	 * @param goodsId 商品ID
	 * @return
	 */
	public ShopsResult addNewOilMsg(String shopUnique,Integer gunNum,String gunName,Integer oilNum,String oilName,
			Double goodsSalePrice,Double goodsInPrice,Double goodsCount,Integer goodsId);
	/**
	 * 查询油品，油枪列表（油枪列表不包含当前店铺已有编号）
	 * @return
	 */
	public List<Map<String,Object>> queryUseAbleOilList(Integer parType,String shopUnique,Integer allMsg);
	/**
	 * 查询油品信息列表
	 * @param shopUnique
	 * @return
	 */
	public ShopsResult queryOilGoodsList(String shopUnique);
	/**
	 * 查询摄像头列表
	 * @param shopUnique 摄像头所属的店铺编号
	 * @return
	 */
	public List<Map<String,Object>> queryMonitorList(String shopUnique,String goodsId);
	
	public PurResult uploadImg(MultipartFile[] file,String shop_unique,HttpServletRequest request);
	/**
	 * 查询店铺的自营供货商信息
	 * @param shopUnique
	 * @return
	 */
	public PurResult getGoodsSupplierMsg(String shopUnique) ;
	/**
	 * 商品信息查询
	 * @param shop_unique 店铺编号
	 * @param goodsMessage 输入的商品信息
	 * @param goods_kind_unique 商品分类编号
	 * @param goods_kind_parunique 商品大类编号
	 * @return
	 */
	public PurResult getGoodList(Map<String ,Object> params);
	/**
	 * 价签打印
	 * @param shop_unique 店铺编号
	 * @param goodsMessage 输入的商品信息
	 * @param goods_kind_unique 商品分类编号
	 * @param goods_kind_parunique 商品大类编号
	 * @return
	 */
	public PurResult getGoodPrintList(Map<String ,Object> params);
	/**
	 * 商品详情查询
	 * @param shop_unique 店铺编号
	 * @param goods_barcode 商品条码
	 * @return
	 */
	public ShopsResult queryGoodsDetail(String shop_unique,String goods_barcode);
	/**
     * 更新商品信息
     *
     * @param goods_barcode商品条码
     * @param goods_name商品名称
     * @param goods_kind_unique商品分类编号
     * @param goods_brand商品品牌
     * @param goods_in_price商品进价
     * @param goods_sale_price商品售价
     * @param goods_life商品保质期
     * @param goods_points会员积分
     * @param goods_count商品库存量
     * @param goods_sold销售数量
     * @param goods_standard商品规格
     * @param default_supplier_unique默认供货商编号
     * @param shop_unique                    商铺编号
     * @param staff_id
     * @return
     */
	public ShopsResult updateGoodsMessage(String shop_unique, String goods_barcode, String goods_name, String goods_kind_unique, String goods_brand, Double goods_in_price, Double  goods_sale_price, String goods_unit,
                                          Integer goods_life, Integer goods_points, Double goods_count, Double goods_sold, String goods_standard, String default_supplier_unique,
                                          Double goods_cus_price, Double goods_web_sale_price, String staff_id, HttpServletRequest request);

	/**
	 * 商品供应商查询
	 * 此方法只能查询店铺所在区县的供应商中提供该商品的供应商
	 * @param shop_unique 店铺编号
	 * @param goods_barcode 商品条码
	 * @return
	 */
	public ShopsResult queryGoodsSupplier(String shopUnique,String goodsBarcode);
	
	/**
	 * 更新图片信息
	 * @param shop_unique 供货商编号
	 * @param request 图片请求
	 * @param goods_barcode 商品条码
	 * @return
	 */
	
	public ShopsResult updateGoodsImage(String shop_unique,HttpServletRequest request,String goods_barcode,String goods_name,String goods_kind_unique,String goods_brand,Double goods_in_price,Double  goods_sale_price,
			Integer goods_life,Integer goods_points,Integer goods_count,Integer goods_sold,String goods_standard,String default_supplier_unique,String goods_address,String goods_remarks,String goods_unit,String sup_goods_barcode);
	/**
	 * 添加新商品
	 * @param shop_unique 商铺编号
	 * @param goods_barcode商品条码
	 * @param goods_name商品名称
	 * @param goods_kind_unique商品分类编号
	 * @param goods_brand商品品牌
	 * @param goods_in_price商品进价
	 * @param goods_sale_price商品售价
	 * @param goods_life商品保质期
	 * @param goods_points会员积分
	 * @param goods_count商品库存量
	 * @param goods_sold销售数量
	 * @param goods_standard商品规格
	 * @param default_supplier_unique默认供货商编号
	 * @return
	 */
	public ShopsResult addNewGoods(String shop_unique,String goods_barcode,String goods_name,String goods_kind_unique,String goods_brand,Double goods_in_price,Double  goods_sale_price,
			Integer goods_life,Integer goods_points,Integer goods_count,Integer goods_sold,String goods_standard,String default_supplier_unique,String goods_address,String goods_remarks);
	
	
	public ShopsResult goodsTest();
	public ShopsResult selectName(Integer pageNum);
	public ShopsResult selectGoods(Integer pageNum);
	
	/**
	 * 商品信息查询，用于饼状图制作
	 * @param shop_unique
	 * @param days
	 * @return
	 */
	public ShopsResult goodsCount(String shop_unique,Integer days);
	
	/**
	 * 商品信息查询，用于饼状图金额制作
	 * @param shop_unique
	 * @param days
	 * @return
	 */
	public ShopsResult goodsAmount(String shop_unique,Integer days);

	/**
	 * 商品信息查询页数查询
	 * @param shop_unique
	 * @param goodsMessage
	 * @param goods_kind_unique
	 * @param goods_kind_parunique
	 * @param stockType
	 * @return
	 */
	public ShopsResult queryGoodsPages(String shop_unique,String goodsMessage,String goods_kind_unique,String goods_kind_parunique,Integer stockType,Integer pageSize);
	
	
	/**
	 * 商品查询（捆绑商品添加）
	 * @param map
	 * @return
	 */
	public ShopsResult bindGoodsAddSearch(Map<String,Object> map);
	

	/**
	 * 促销商品页数查询
	 * @param map
	 * @return
	 */
	public ShopsResult promotionGoodsPages(Map<String,Object> map);
	

	/**
	 * 商品信息查询
	 * @param map
	 * @return
	 */
	public ShopsResult promotionGoodsSearchByPage(Map<String,Object> map);
	/**
	 * 取消促销活动（将促销数量改为0，并将折扣改为1）
	 * @param map
	 * @return
	 */
	public ShopsResult cancelPromotion(Map<String,Object> map);
	
	/**
	 * 商品详情查询
	 * @param map
	 * @return
	 */
	public ShopsResult goodsDetail(Map<String,Object> map);
	
	
	/**
	 * 查询商品供货商供应商品信息
	 * @param map
	 * @return
	 */
	public ShopsResult querySupplierGoods(Map<String,Object> map);
	
	/**
	 * 更新商品信息
	 * @param map
	 * @return
	 */
	public ShopsResult updateListGoodsMessage(Map<String,Object> map,String goodsMessage,HttpServletRequest request,Double goodsCount,String goodsBarcode,Long shopUnique,Long foreignKey);
	
	/**
	 * @return
	 */
	public ShopsResult  aaa();
	
	/**
	 * 查询商品今日销售统计总页数
	 * @param map
	 * @return
	 */
	public ShopsResult queryPagesForRanking(Map<String,Object> map);
	
	/**
	 * 商品日销量：
	 * @param map
	 * @return
	 */
	public ShopsResult queryGoodsRanking(Map<String,Object> map);
	
	/**
	 * 商品日销量界面：商品销售详情
	 * @param map
	 * @return
	 */
	public ShopsResult queryRankingDetail(Map<String,Object> map);
	
	/**
	 * 出入库记录：页数查询
	 * @param map
	 * @return
	 */
	public ShopsResult queryStockRecordPages(Map<String,Object> map);
	
	/**
	 * 商品出入库记录查询
	 * @param map
	 * @return
	 */
	public PurResult queryGoodsRecordByPage(Map<String,Object> map);
	
	
	public List<GoodsInfo> ExcelGoodsRecord(Map<String,Object> map);
	
	public GoodsInfo ExcelGoodsRecordCount(Map<String,Object> map);
	
	
	public List<GoodsInfo> ExcelGoodsAlloration(Map<String,Object> map); 
	
	public List<GoodsInfo> ExcelGoodsInfo(Map<String,Object> map);
	/**
	 * 店铺商品进货量查询
	 * @param map
	 * @return
	 */
	public ShopsResult goodsCostQuery(Map<String,Object> map);
	
	/**
	 * 删除店铺的重复商品并保留最大ID的商品信息
	 * @param map
	 * @return
	 */
	public ShopsResult deleteSameGoods(String shopUnique);
	
	/**
	 * 商品销售页数查询
	 * @param map
	 * @return
	 */
	public ShopsResult queryGoodsSaleMessagePages(Map<String,Object> map);
	
	/**
	 * 商品销量排行界面；分页查询
	 * @param map
	 * @return
	 */
	public ShopsResult queryGoodsByPage(Map<String,Object> map);
	
	/**
	 * 商品横向比较页面：页数查询
	 * @param map
	 * @return
	 */
	public ShopsResult queryGoodsLateralSalePages(Map<String,Object> map);
	
	/**
	 * 分页查询店铺某商品销售信息
	 * @param map
	 * @return
	 */
	public ShopsResult queryGoodsLateralSaleByPage(Map<String,Object> map);
	
	public ShopsResult  queryGoodsProtraitPages(Map<String,Object> map);
	
	public ShopsResult queryGoodsProtraitByPage(Map<String,Object> map);
	
	/**
	 * 输入条码后，获取云库商品或本店商品
	 * @param map
	 * @return
	 */
	public PurResult getCloudMessage(Map<String,Object> map);
	
	/**
	 * 将商品信息保存，并更新云库商品
	 * 此处为保证所有商品的foreignKey一致，将所有同条码的商品更新为相同
	 * 此处先更新本店铺的商品信息，若更新不成功，则将新商品添加，否则仅更新
	 * @param manager_unique 
	 * @param goods_count 
	 * @param map
	 * @return
	 */
	public PurResult saveGoodsMessage(String operateType, String shop_unique,String goodsBrand,String kindUnique,String goodsMessage,String goodsRemarks,String foreignKey,
			Integer goodsGround,HttpServletRequest request,String ip,String userAgent,String goods_barcode,String outStockCount,String autoGoodsJson,
			String goodsDelete,String staff_id,Integer sameType,Integer goodsChengType,String supplierUnique, String manager_unique);
	
	public PurResult saveGoodsMessage_wj(String shop_unique,String goodsBrand,String kindUnique,String goodsMessage,String goodsRemarks,String foreignKey,
			Integer goodsGround,HttpServletRequest request,String ip,String userAgent,String goods_barcode,String outStockCount,String autoGoodsJson,
			String goodsDelete,String staff_id,Integer sameType,Integer goodsChengType,String supplierUnique, String manager_unique);
	public PurResult getGoodsBaseMessage(Map<String,Object> map);
	
	public ShopsResult printAllorationList(Map<String, Object> map);
	
	/**
	 * 商品销售统计界面查询
	 * @param map
	 * @return
	 */
	public PurResult queryGoodsSaleStatistics(Map<String,Object> map);
	
	/**
	 * 商品销售明细
	 * @param goods_barcode 商品编码
	 * @param shop_unique 店铺编码
	 * @param startTime 开始时间
	 * @param endTime 结束时间
	 * @return
	 */
	public PurResult queryGoodsSaleDetail(Map<String,Object> params);
	
	/**
	 * 查询全部销售数据
	 * @param map
	 * @return
	 */
	public List<Map<String, Object>> downloadSaleStatisticsExcel(Map<String,Object> map);
	/**
	 * 查询商品详情
	 * @return
	 */
	public PurResult queryGoodsStatisticsDetail(Map<String,Object> map);
	
	public List<Map<String,Object>> downloadSaleStatisticsDetailExcel(Map<String,Object> map);
	
	public Map<String,Object> downloadSaleStatisticsCount(Map<String,Object> map);
	/**
	 * 商品出入库明细界面商品信息查询
	 * @param map
	 * @return
	 */
	public ShopsResult queryGoodsMessageForStatisticsDetail(Map<String,Object> map);
	
	/**
	 * 统计销售数据并下载
	 * @param map
	 * @return
	 */
	public List<Map<String,Object>> downloadGoodsSaleStatisticsExcel(Map<String,Object> map);
	
	/**
	 * 统计销售数据并下载_宁宇— 全部店铺
	 * @param map
	 * @return
	 */
	public List<Map<String,Object>> downloadGoodsSaleStatisticsExcel_NYALL(Map<String,Object> map);
	/**
	 * 获取商家是否开启自动补货设置
	 * @param shop_unique 商家唯一标示
	 * @return
	 */
	public PurResult getAutoPurchase(String shop_unique);
	
	/**
	 * 开启、关闭自动补货
	 * @param shop_unique 商家唯一标示
	 * @param auto_purchase 0 关闭自动补货 1开启自动补货
	 * @return
	 */
	public PurResult updateAutoPurchase(Map<String, Object> params);
	
	/**
	 * 商品自动采购
	 * @return
	 */
	public void goodsAutoPurchase();
	/**
	 * 删除商品信息界面，查询分页数量
	 * @param map
	 * @return
	 */
	public ShopsResult toQueryGoodsPage(Map<String,Object> map);
	
	/**
	 * 删除商品信息，并修改同步信息至删除
	 * @param map
	 * @return
	 */
	public ShopsResult deleteGoodsList(Map<String,Object> map,String[] barcodes);
	
	/**
	 * 删除所有查询到的商品信息
	 * @param map
	 * @return
	 */
	public ShopsResult deleteGoodsSearch(Map<String,Object> map);
	
	  /**
     * 烟草导入
     * @param map
     * @return
     */
    public ShopsResult yancaoInsert(Map<String,Object> map);
    
    /**
     * 获取所有审核通过店铺列表
     * @return
     */
    public List<Map<String ,Object>> getAllShopList();

    /**
     * 统计某商店录码数量
     * @param map
     * @return
     */
    public ShopsResult queryGoodsNewCountForShop(Map<String,Object> map);
    
    /**
     * 创建新的条码
     * @param goodsBarcode
     * @param barcodes
     * @return
     */
    public ShopsResult queryGoodsBarcodeSameForeignkey(String goodsBarcode,String barcodes,String shopUnique);
    
    /**
     * 获取商品详情
     * @param map
     * @return
     */
    public ShopsResult getGoodsDetail(Map<String,Object> map);
    
    /**
     * 更新商品图片
     * @param shop_unique 店铺编码
     * @param goods_barcode 商品编码
     * @param file 商品图片文件
     * @return
     */
    public PurResult updateGoodsImg(String shop_unique,String goods_barcode,MultipartFile file,HttpServletRequest request);
    
    
    /**
     * 更新大库商品信息
     * @param barcode
     * @param file
     * @param request
     * @return
     */
    public ShopsResult updateGoodsDictImg(String barcode,MultipartFile file,HttpServletRequest request);
    /**
     * 获取商品详情信息
     * @param goods_id 商品id
     * @return
     */
    public List<Map<String ,Object>> getGoodsDetails(Map<String ,Object> params);
    
    /**
     * 更新商品详细信息
     * @param goods_id 商品id
     * @return
     */
    public PurResult updateGoodsDetails(MultipartFile file1,MultipartFile file2,MultipartFile file3,MultipartFile file4,HttpServletRequest request,String monitor_info_id);
    
    /**
     * 批量更新商品库存
     * @return
     */
    public PurResult batchUpdateCount(HttpServletRequest request);
    
    public List<Map<String ,Object>> queryGoodsSaleStatisticsExcel(Map<String ,Object> params);
    
    public Map<String ,Object> queryGoodsSaleStatisticsCountExcel(Map<String ,Object> params);

	Map<String,Object> goodsSaleStatistics(Map<String ,Object> params);
	public List<Map<String, Object>> downloadSingleGoodsExcel(Map<String, Object> map);
	
	
	public PurResult querySperGoods(String goods_id,String goods_name,String goods_barcode) ;
	
	public void updatePhoto(Map<String,Object> map);

	ShopsResult getGoodBatchList(Map<String ,Object> params);
}
