package org.haier.shop.service;

import java.util.List;
import java.util.Map;

import javax.annotation.Resource;

import org.haier.shop.dao.BeanRechargeConfigDao;
import org.haier.shop.util.Load;
import org.haier.shop.util.PurResult;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Service
@Transactional
public class BeanRechargeConfigServiceImpl implements BeanRechargeConfigService{
	@Resource
	private BeanRechargeConfigDao beanRechargeConfigDao;
	
	@Override
	public PurResult getList(Map<String, Object> params) {
		PurResult result = new PurResult();
		try {
			params.put("url", Load.IMGDOMAINNAME);
			List<Map<String ,Object>> list = beanRechargeConfigDao.getList(params);
	    	Integer count = beanRechargeConfigDao.getListCount(params);
	    	result.setStatus(1);
			result.setMsg("成功");
			result.setCount(count);
			result.setData(list);
		} catch (Exception e) {
			e.printStackTrace();
			result.setStatus(0);
			result.setMsg("异常");
		}
		return result;
	}
	@Override
	public PurResult add(Map<String, Object> params) {
		PurResult result = new PurResult();
		try {
			beanRechargeConfigDao.add(params);
	    	result.setStatus(1);
			result.setMsg("成功");
		} catch (Exception e) {
			e.printStackTrace();
			result.setStatus(0);
			result.setMsg("异常");
		}
		return result;
	}
	@Override
	public Map<String, Object> getInfo(String beans_recharge_config_id) {
		return beanRechargeConfigDao.getInfo(beans_recharge_config_id);
	}
	@Override
	public PurResult update(Map<String, Object> params) {
		PurResult result = new PurResult();
		try {
			beanRechargeConfigDao.update(params);
	    	result.setStatus(1);
			result.setMsg("成功");
		} catch (Exception e) {
			e.printStackTrace();
			result.setStatus(0);
			result.setMsg("异常");
		}
		return result;
	}
	@Override
	public PurResult delete(String beans_recharge_config_id) {
		PurResult result = new PurResult();
		try {
			beanRechargeConfigDao.delete(beans_recharge_config_id);
	    	result.setStatus(1);
			result.setMsg("成功");
		} catch (Exception e) {
			e.printStackTrace();
			result.setStatus(0);
			result.setMsg("异常");
		}
		return result;
	}
}
