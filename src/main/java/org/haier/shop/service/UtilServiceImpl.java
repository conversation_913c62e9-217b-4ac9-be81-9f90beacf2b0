package org.haier.shop.service;


import java.io.BufferedReader;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileReader;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.UUID;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;

import org.haier.shop.dao.UpdateDao;
import org.haier.shop.dao.UtilDao;
import org.haier.shop.dao2.ShoppingDao;
import org.haier.shop.entity.ShopsYN;
import org.haier.shop.redis.RedisCache;
import org.haier.shop.thread.PcUploadThread;
import org.haier.shop.util.ChineseFristLetter;
import org.haier.shop.util.GoodsImport;
import org.haier.shop.util.Load;
import org.haier.shop.util.PCUpdateUtil;
import org.haier.shop.util.PicSaveUtil;
import org.haier.shop.util.PurResult;
import org.haier.shop.util.ShopsResult;
import org.haier.shop.util.ShopsUtil;
import org.haier.shop.util.UtilForJAVA;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;

@Service
@Transactional
public class UtilServiceImpl implements UtilService{
	
	@Resource
	public UtilDao utilDao;
	@Resource
	private UpdateDao updateDao;
	@Resource
	private RedisCache redis;
	@Resource
	private ShoppingDao shoppingDao;
	
	/**
	 * 查询支付文件列表
	 * @param startTime
	 * @param endTime
	 * @param page
	 * @param limit
	 * @return
	 */
	public ShopsResult queryPayimageList(String startTime , String endTime , Integer page, Integer limit) {
		ShopsResult sr = new ShopsResult(1,"查询成功!");
		
		Map<String,Object> map = new HashMap<String,Object>();
		map.put("startTime", startTime);
		map.put("endTime", endTime);
		map.put("startNum", (page - 1) * limit);
		map.put("pageSize",  limit);
		List<Map<String,Object>> list = shoppingDao.queryPayImageList(map);
		
		Integer count = shoppingDao.queryPayImageCount(map);
		
		sr.setData(list);
		sr.setCount(count);
		
		return sr;
	}
	/**
	 * 上传交易凭证，返回文件的保存绝对路径（包含http头）
	 */
	public ShopsResult uploadImage(HttpServletRequest request) {
		ShopsResult sr = new ShopsResult(1,"上传成功!");
		try {
			MultipartFile file=null;
			if(request instanceof MultipartHttpServletRequest){
				MultipartHttpServletRequest multipartHttpServletRequest =(MultipartHttpServletRequest)request;
				Map<String, MultipartFile> mp = multipartHttpServletRequest.getFileMap();
				file = mp.get("image");
			}
			
			if(file!=null){
				String orName=file.getOriginalFilename();
				String lastName=orName.substring(orName.lastIndexOf("."));
				
				String absPath = this.getClass().getClassLoader().getResource("../../").getPath();
				String filePathDetail = File.separator + "image" + File.separator + "pingzheng" + File.separator ;
				String filePath = absPath.substring(0, absPath.length() - request.getContextPath().length()) + filePathDetail;
				File dir=new File(filePath);
				if(!dir.exists()){
					dir.mkdirs();
				}
				String shop_pictureName=UUID.randomUUID()+lastName;
				PicSaveUtil.handleFileUpId(file, request, filePath, shop_pictureName);//图片的保存
				
				//将文件上传到文件服务器
				File ftpFile = new File(filePath + shop_pictureName);
				FileInputStream fis = new FileInputStream(ftpFile);
				
				ShopsUtil.ftpUpload("/pingzheng", shop_pictureName, fis);
				
				//将数据保存到服务器
				shoppingDao.addNewPayImage("https://file.buyhoo.cc" + filePathDetail + shop_pictureName);
			}
		}catch (Exception e) {
			e.printStackTrace();
		}
		
		return sr;
	}
	
	public ShopsResult addNewShopYN(){
		ShopsResult sr = new ShopsResult();
		List<ShopsYN> list = GoodsImport.shopsImport("D:\\\\logs\\\\ynx.xls", "shops");
		if(null != list && !list.isEmpty()) {
			utilDao.addNewShopYN(list);
		}
		return sr;
	}

	public ShopsResult updateKindUnqualified(){
		ShopsResult sr=new ShopsResult();
		List<String> list=utilDao.queryKindUnqualified();
		if(null==list||list.isEmpty()){
			sr.setStatus(2);
			sr.setMsg("没有需要更新的商品信息");
			return sr;
		}
		System.out.println(list);
		int k=utilDao.updateKindUnqualified(list);
		System.out.println("更新的商品数量为："+k);
		sr.setStatus(1);
		sr.setMsg("更新成功！");
		sr.setData(k);
		return sr;
	}
	
	public ShopsResult addGoodsGoods(String shopUnique){
		ShopsResult sr=new ShopsResult();
		utilDao.addGoodsGoods(shopUnique);
//		utilDao.updateForerign(shopUnique);
		sr.setStatus(1);
		sr.setMsg("失败");
		return sr;
	}
	
	public ShopsResult updateForeign(String shopUnique){
		ShopsResult sr=new ShopsResult();
//		utilDao.addGoodsGoods(shopUnique);
		utilDao.updateForerign(shopUnique);
		sr.setStatus(1);
		sr.setMsg("失败");
		return sr;
	}
	
	
	public ShopsResult queryNoUseGoods(String shopUnique){
		Map<String,Object> map=new HashMap<String,Object>();
		ShopsResult sr=new ShopsResult();
		map.put("shopUnique", shopUnique);
		List<Integer> list=utilDao.queryNoUseGoods(map);
		map.put("list", list);
		
		int k=utilDao.deleteNoUse(map);
		System.out.println("删除的商品数量"+k);
		sr.setStatus(1);
		sr.setMsg("chengg!");
		sr.setData(list);
		return sr;
	}
	
	public ShopsResult copyGoodsToNewShop(Map<String,Object> map){
		ShopsResult sr=new ShopsResult();
		Integer data=utilDao.copyGoodsToNewShop(map);
		sr.setStatus(1);
		sr.setData(data);
		return sr;
	}
	
	/**
	 * 更新用于更新PC收银端软件的新消息
	 * @return
	 */
	public ShopsResult updatePcFile(){
		ShopsResult ns=new ShopsResult();
		ns.setStatus(1);
		ns.setMsg("更新成功！！！");
		//删除已有文件信息
		int k=0;
		try{
			//打开升级日志，记录升级文件信息
			File file=new File(Load.FILEFORPCUPDATEPATH+File.separator+"updateLog.txt");
			StringBuffer strs=new StringBuffer();
			if(file.exists()) {
				String str=null;
				BufferedReader br=new BufferedReader(new FileReader(file));
				while((str=br.readLine())!=null){
					strs.append(str);
				}
				br.close();
			}
			//读取文件的版本信息，
			String version_number=PCUpdateUtil.getVersion(new File(Load.FILEFORPCUPDATEPATH+File.separator+"BUYHOO.exe"));
			Map<String,Object> map=new HashMap<String, Object>();
			map.put("versionNumber", version_number);
			map.put("nversionNumber", version_number);
			map.put("remarks", strs.toString());
			//插入一条升级记录
			//插入前做一次查询，防止版本重复插入
			List<Map<String,Object>> maps=utilDao.queryHistoricalVersion(map);
			//新版本提交成功后，创建新版本的待更新信息
			if(null==maps||maps.isEmpty()){
				utilDao.addNewCashUpdateRecord(map);
				k=utilDao.deleteUpdateFilesMessage();
				List<Map<String,Object>> data=PCUpdateUtil.getMd5FileForUpdate(Load.FILEFORPCUPDATEPATH,1);
//				data.addAll(PCUpdateUtil.getMd5FileForUpdate(Load.FILEFORPCUPDATEPATHYN, 2));
				if(null==data||data.isEmpty()){
					ns.setStatus(2);
					ns.setMsg("没有需要更新的文件");
					return ns;
				}
				k=utilDao.addUpdateFilesMessage(data);
				System.out.println(k);
			}else{
				ns.setStatus(2);
				ns.setMsg("该版本已提交");
			}
			utilDao.setVersionNumberOverdue();
			List<Map<String,Object>> mas=utilDao.queryLastVersionNumber(map);
			if(null!=mas&&!mas.isEmpty()){
				map.put("list", mas);
				//创建新的店铺升级记录
				k = utilDao.addNewVersionForUpdate(map);
				//设置升级数量，打开五个常用店铺的升级
//				List<String> list=updateDao.queryShopsNeedUpdate(5);
//				System.out.println("需要升级的店铺信息"+list);
//				//将店铺信息设置为更新中状态
//				if(list!=null &&!list.isEmpty()){
//					map.put("list", list);
//					map.put("handleStatusNow", UpdateUtil.OWHANDLESTATUS);
//					map.put("handleStatus", UpdateUtil.OHANDLESTATUS);
//					updateDao.overUpdate(map);
//				}
			}
			
			//开启线程，向收银机推送升级消息
			PcUploadThread thread = new PcUploadThread(utilDao, redis);
			Thread t = new Thread(thread);
			t.start();
		}catch(Exception e){
			e.printStackTrace();
			ns.setStatus(1);
			ns.setMsg("升级失败！");
		}
		return ns;
	}
	/**
	 * 历史版本信息查询
	 * @return
	 */
	public ShopsResult queryHistoricalVersion(){
		ShopsResult sr=new ShopsResult();
		List<Map<String,Object>> data=utilDao.queryHistoricalVersion(null);
		sr.setStatus(1);
		sr.setMsg("查询成功！");
		sr.setData(data);
		return sr;
	}
	
	/**
	 * 查询店铺总数量
	 */
	public ShopsResult queryShopsCount(Map<String,Object> map){
		ShopsResult sr=new ShopsResult();
		int num = utilDao.queryShopsCount(map);
		System.out.println(num);
		sr.setStatus(1);
		sr.setMsg("操作成功！");
		int pageSize = (Integer)map.get("pageSize");
		int ys = num%pageSize;
		if(ys==0){
			sr.setData(num/pageSize);
		}else{
			int count = num/pageSize+1;
			sr.setData(count);
		}
		sr.setCord(num);
		return sr;
	}
	
	/**
	 * 查询每页店铺信息
	 */
	public PurResult queryShopsMessage(Map<String,Object> params){
		PurResult result=new PurResult();	
		params.put("versionNumber", utilDao.theLastVersion());
		List<Map<String,Object>> list=utilDao.queryShopsMessage(params);
		result.setStatus(1);
		result.setMsg("查询成功");
		result.setCount(utilDao.queryShopsCount(params));//shopTest
//		result.setCord(utilDao.queryShopsCount(params));
		result.setData(list);
		return result;
	}
	
	/**
	 * 批量设置更新状态
	 * @param map
	 * @return
	 */
	public ShopsResult modifyShopsVersion(Map<String,Object> map){
		ShopsResult sr=new ShopsResult();
		int k = utilDao.modifyShopsVersion(map);
		sr.setStatus(1);
		sr.setMsg("更新成功！");
		sr.setData(k);
		return sr;
	}
	
	/**
	 * 测试接口，在不同的服务器，获取文件MD5值不同
	 * @return
	 */
	public ShopsResult updatePcFileTest(){
		ShopsResult sr=new ShopsResult(1,"测试成功！");
		//删除已有文件信息
				int k=0;
				try{
					List<Map<String,Object>> data=PCUpdateUtil.getMd5FileForUpdate(Load.FILEFORPCUPDATEPATH,1);
					sr.setData(data);
				}catch(Exception e){
					System.out.println(k);
					e.printStackTrace();
				}
		return sr;
	}
	
	public ShopsResult test(){
		ShopsResult sr=new ShopsResult(1,"测试成功！");
		List<String> list=utilDao.queryList();
		Integer k=utilDao.addShopTitle(list);
		sr.setData(k);
		return sr;
	}
	
	public ShopsResult modifyGoodsPic(String shopUnique){
		ShopsResult sr=new ShopsResult(1,"操作成功！");
		List<String> list=UtilForJAVA.getFileListName("D:\\项目文件\\商品图片整理\\120X120\\2018-12-24");
		List<Map<String,Object>> glist=new ArrayList<>();
		if(null==list||list.isEmpty()){
		}else{
			int k=0;
			k=utilDao.addGoods(list);
			for(int i=0;i<list.size();i++){
				Map<String,Object> m=new HashMap<String,Object>();
				m.put("goodsBarcode", list.get(i));
				m.put("goodsPic", "image/goods/120*120/"+list.get(i)+".png");
				m.put("shopUnique", shopUnique);
				glist.add(m);
			}
			k=utilDao.modifyGoodsPic(glist);
			sr.setData(k);
		}
		return sr;
	}
	
	public ShopsResult addNewGoodsAll(){
		ShopsResult sr=new ShopsResult(1,"操作成功！");
		List<String> list=utilDao.getShopList();
		for(int i=0;i<list.size();i++){
			System.out.println(list.get(i));
			utilDao.addNewGoodsAll(list.get(i));
		}
		return sr;
	}
	
	public ShopsResult modifyNullList(){
		ShopsResult sr=new ShopsResult(1, "");
		List<Map<String,Object>> list=utilDao.getNullList();
		List<Map<String,Object>> l=new ArrayList<>();
		for(int i=0;i<list.size();i++){
			if(i%1000==0){
				if(!l.isEmpty()){
					utilDao.modifyNullList(l);
				}
				l=new ArrayList<>();
			}
			l.add(list.get(i));
		}
		return sr;
		
	}
	
	public ShopsResult deleteListDetail(){
		ShopsResult sr=new ShopsResult(1,"shanchu ");
		Integer k=utilDao.deleteListDetail();
		System.out.println(k==null?0:k);
		sr.setData(k);
		return sr;
	}
	
	/**
	 * 添加自定义分类98001
	 * @return
	 */
	public ShopsResult addSelfKind(){
		ShopsResult sr=new ShopsResult(1, "查询成功！");
		List<String> list=utilDao.queryShopNeedSelfKind();
		System.out.println(list);
		if(null!=list&&!list.isEmpty()){
			System.out.println(list.size());
			int k=utilDao.addSelfKind(list);
			sr.setData(k);
		}
		return sr;
	}
	
	/**
	 * 批量修改会员别名信息
	 * @return
	 */
	public ShopsResult updateCusAlias(Map<String,Object> map){
		ShopsResult sr = new ShopsResult();
		List<Map<String,Object>> list = utilDao.getAllCusMsg(map);
		
		for(int i =0 ;i <list.size() ; i++){
			list.get(i).put("cusAlias", ChineseFristLetter.converterToFirstSpell(list.get(i).get("cusName").toString()));
		}
		for(int i =0 ;i <list.size()&&i<10;i++){
			System.out.println(list.get(i).get("cusAlias"));
		}
		Integer k = utilDao.updateAllCusAlias(list);
		sr.setData(k);
		return sr;
	}
}
