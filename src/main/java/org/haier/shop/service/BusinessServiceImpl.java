package org.haier.shop.service;

import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.SortedMap;
import java.util.TreeMap;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;

import org.haier.meituan.util.MUtil;
import org.haier.shop.dao.BusinessDao;
import org.haier.shop.util.HandleMessyCode;
import org.haier.shop.util.PurResult;
import org.haier.shop.util.ServletsUtil;
import org.haier.shop.util.wxPay.ClientCustomSSL;
import org.haier.shop.util.wxPay.PayConfigUtil;
import org.haier.shop.util.wxPay.PayToolUtil;
import org.haier.shop.util.wxPay.XMLUtil4jdom;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.interceptor.TransactionAspectSupport;

import cn.hutool.core.util.NumberUtil;

@Service("businessService")
public class BusinessServiceImpl implements BusinessService{
	
	@Resource
	private BusinessDao businessDao;

	@Override
	public PurResult queryBusinessList(Map<String, Object> params) {
		PurResult result = new PurResult();
		try {
			List<Map<String ,Object>> list = businessDao.queryBusinessList(params);
			Integer count = businessDao.queryBusinessListCount(params);
			result.setData(list);
			result.setCount(count);
			
			result.setStatus(1);
			result.setMsg("成功");
		} catch (Exception e) {
			e.printStackTrace();
			result.setStatus(0);
			result.setMsg("异常");
		}
		
		return result;
	}

	@Override
	@Transactional
	public PurResult addBusiness(HttpServletRequest request) {
		PurResult result = new PurResult();
		try {
			Map<String ,Object> params = HandleMessyCode.handle(ServletsUtil.getParameters(request));
			businessDao.addBusiness(params);
			result.setStatus(1);
			result.setMsg("成功");
		} catch (Exception e) {
			e.printStackTrace();
			result.setStatus(0);
			result.setMsg("异常");
		}
		
		return result;
	}

	@Override
	public Map<String, Object> queryBusinessDetail(String id) {
		Map<String ,Object> business = businessDao.queryBusinessDetail(id);
		return business;
	}
	
	public List<Map<String ,Object>> queryRedOrderStatistics(Map<String,Object> params){
		return businessDao.queryRedOrderStatistics(params);
	}
	

	@Override
	@Transactional
	public PurResult deleteBusiness(String id) {
		PurResult result = new PurResult();
		try {
			Map<String ,Object> params = new HashMap<>();
			params.put("id", id);
			params.put("valid_type", "0");
			businessDao.updateBusiness(params);
			result.setStatus(1);
			result.setMsg("成功");
		} catch (Exception e) {
			e.printStackTrace();
			result.setStatus(0);
			result.setMsg("异常");
		}
		
		return result;
	}

	@Override
	@Transactional
	public PurResult updateBusiness(HttpServletRequest request) {
		PurResult result = new PurResult();
		try {
			Map<String ,Object> params = HandleMessyCode.handle(ServletsUtil.getParameters(request));
			businessDao.updateBusiness(params);
			result.setStatus(1);
			result.setMsg("成功");
		} catch (Exception e) {
			e.printStackTrace();
			result.setStatus(0);
			result.setMsg("异常");
		}
		
		return result;
	}

	@Override
	@Transactional
	public PurResult unicomUserImport(List<Map<String, Object>> list) {
		PurResult result = new PurResult();
		try {
			List<Map<String ,Object>> redProvideList = new ArrayList<>();
			List<Map<String ,Object>> redPacketList = new ArrayList<>();
			List<Map<String ,Object>> redCommissionList = new ArrayList<>();
			SimpleDateFormat df = new SimpleDateFormat("yyyy-MM-dd");//设置日期格式
			Calendar c = Calendar.getInstance();
			Date date = new Date();
			
			c.setTime(date);
		    c.add(Calendar.MONTH, 3);
		    Date m3 = c.getTime();
			
			for(int i=0;i<list.size();i++) {
				Map<String ,Object> params = list.get(i);
				//根据手机号获取用户信息
				Map<String ,Object> cus_info = businessDao.queryCusInfoByPhone(params);
				Map<String ,Object> business = businessDao.queryBusinessByTypeAndMoney(params);
				if(cus_info == null) {
					
					cus_info=new HashMap<String,Object>();
					cus_info.put("cus_unique", "");
					
				}
					//根据套餐金额和业务类型获取红包期数和红包金额
					
					if(business == null) {
						result.setStatus(0);
						result.setMsg("套餐金额为"+params.get("business_money")+"未加入专享红包业务");
						
						TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();//事务回滚
						return result;
					}
					//查询会员业务红包信息
					params.put("cus_unique", cus_info.get("cus_unique"));
					params.put("business_id", business.get("business_id"));
					Map<String ,Object> cus_red_provide = businessDao.queryRedProvide(params);
					if(cus_red_provide != null) {//已有套餐会员红包，对比新套餐金额，如果新金额小于已有金额，红包停止发放；否则不变
						Double old_business_money = Double.valueOf(MUtil.strObject(cus_red_provide.get("business_money")));//已有套餐金额
						Double new_business_money = Double.valueOf(MUtil.strObject(params.get("business_money")));//新套餐金额
						if(new_business_money < old_business_money) {
							//修改会员业务红包信息为无效
							cus_red_provide.put("valid_type", "0");//无效
							businessDao.updateRedProvide(cus_red_provide);
						}
					}else {
						
						Map<String ,Object> redProvide = new HashMap<String ,Object>();
						redProvide.put("business_id", business.get("business_id"));
						redProvide.put("cus_unique", cus_info.get("cus_unique"));
						redProvide.put("provide_count", business.get("donation_count"));
						redProvide.put("surplus_count", (Integer.parseInt(MUtil.strObject(business.get("donation_count"))))-1);
						redProvide.put("referees_num", params.get("referees_num"));
						redProvide.put("net_time", params.get("net_time"));
						redProvide.put("cus_phone", params.get("cus_phone"));
						redProvideList.add(redProvide);
						Map<String ,Object> redPacket = new HashMap<String ,Object>();
						redPacket.put("cus_unique", cus_info.get("cus_unique"));
						redPacket.put("provide_time", df.format(date));
						redPacket.put("overdue_time", df.format(m3));
						redPacket.put("business_id", business.get("business_id"));
						if(cus_info.get("cus_unique").equals(""))
						{
							redPacket.put("red_status", 3);
						}else
						{
							redPacket.put("red_status", 1);
						}
						redPacket.put("total_money", business.get("donation_money"));
						redPacket.put("cus_phone", params.get("cus_phone"));
						redPacketList.add(redPacket);
						//获取能人信息
						Map<String ,Object> redPerson = businessDao.queryRedPerson(params);
						if(redPerson != null ) {//修改能人佣金余额
							redPerson.put("balance_money", business.get("commission_money"));
							redPerson.put("staff_num", params.get("referees_num"));
							businessDao.updateRedPerson(redPerson);
						}else {//添加能人佣金信息
							Map<String ,Object> redPersonParams = new HashMap<>();
							redPersonParams.put("balance_money", business.get("commission_money"));
							redPersonParams.put("staff_num", params.get("referees_num"));
							redPersonParams.put("person_name", params.get("person_name"));
							redPersonParams.put("person_phone", params.get("person_phone"));
							businessDao.addRedPerson(redPersonParams);
						} 
						//添加能人佣金记录
						Map<String ,Object> redCommission = new HashMap<String ,Object>();
						redCommission.put("staff_num", params.get("referees_num"));
						redCommission.put("cus_unique", cus_info.get("cus_unique"));
						redCommission.put("business_id", business.get("business_id"));
						redCommission.put("commission_money", business.get("commission_money"));
						redCommission.put("commission_type", "1");//推广佣金
						redCommission.put("consume_money", null);
						redCommission.put("cus_phone", params.get("cus_phone"));
						redCommissionList.add(redCommission);
					}
//				}else
//				{
//					//2021-05-22 新增非会员 策略
//					params.put("cus_unique", "");
//					params.put("business_id", business.get("business_id"));
//					params.put("total_money", business.get("donation_money"));
//					params.put("provide_time", df.format(date));
//					params.put("overdue_time", df.format(m3));
//					params.put("red_status", 3);
//					redPacketList.add(params);
//					
//				}
			}
			//添加会员业务红包总信息
			if(redProvideList.size()>0) {
				businessDao.addRedProvide(redProvideList);
			}
			//添加会员红包记录
			if(redPacketList.size()>0) {
				businessDao.addRedPacket(redPacketList);
			}
			//添加能人佣金记录
			if(redCommissionList.size()>0) {
				businessDao.addRedCommission(redCommissionList);
			}
			
			result.setStatus(1);
			result.setMsg("成功");
		} catch (Exception e) {
			e.printStackTrace();
			result.setStatus(0);
			result.setMsg("异常");
		}
		
		return result;
	}
	
	@Override
	public PurResult queryRedPersonList(Map<String, Object> params) {
		PurResult result = new PurResult();
		try {
			List<Map<String ,Object>> list = businessDao.queryRedPersonList(params);
			Integer count = businessDao.queryRedPersonListCount(params);
			result.setData(list);
			result.setCount(count);
			
			result.setStatus(1);
			result.setMsg("成功");
		} catch (Exception e) {
			e.printStackTrace();
			result.setStatus(0);
			result.setMsg("异常");
		}
		
		return result;
	}
	
	@Override
	public PurResult queryRedOrderList(Map<String, Object> params) {
		PurResult result = new PurResult();
		try {
			List<Map<String ,Object>> list = businessDao.queryRedOrderList(params);
			Integer count = businessDao.queryRedOrderListCount(params);
			result.setData(list);
			result.setCount(count);
			
			result.setStatus(1);
			result.setMsg("成功");
		} catch (Exception e) {
			e.printStackTrace();
			result.setStatus(0);
			result.setMsg("异常");
		}
		
		return result;
	}
	
	
	@Override
	public PurResult queryPlatformMsg(Map<String, Object> params) {
		PurResult result = new PurResult();
		try {
			List<Map<String ,Object>> list = businessDao.queryPlatformMsg(params);
			Integer count = businessDao.queryPlatformMsgCount(params);
			result.setData(list);
			result.setCount(count);
			
			result.setStatus(1);
			result.setMsg("成功");
		} catch (Exception e) {
			e.printStackTrace();
			result.setStatus(0);
			result.setMsg("异常");
		}
		
		return result;
	}
	
	@Override
	public PurResult getStatistics(Map<String, Object> params) {
		PurResult result = new PurResult();
		try {
			List<Map<String ,Object>> list = businessDao.queryRedOrderStatistics(params);
			Integer count = list.size();
			result.setData(list);
			result.setCount(count);
			
			result.setStatus(1);
			result.setMsg("成功");
		} catch (Exception e) {
			e.printStackTrace();
			result.setStatus(0);
			result.setMsg("异常");
		}
		
		return result;
	}
	
	@Override
	public PurResult queryRedPersonCommissionList(Map<String, Object> params) {
		PurResult result = new PurResult();
		try {
			List<Map<String ,Object>> list = businessDao.queryRedPersonCommissionList(params);
			Integer count = businessDao.queryRedPersonCommissionListCount(params);
			result.setData(list);
			result.setCount(count);
			
			result.setStatus(1);
			result.setMsg("成功");
		} catch (Exception e) {
			e.printStackTrace();
			result.setStatus(0);
			result.setMsg("异常");
		}
		
		return result;
	}
	
	@Override
	public PurResult queryRedPersonWidthList(Map<String, Object> params) {
		PurResult result = new PurResult();
		try {
			List<Map<String ,Object>> list = businessDao.queryRedPersonWidthList(params);
			Integer count = businessDao.queryRedPersonWidthListCount(params);
			result.setData(list);
			result.setCount(count);
			
			result.setStatus(1);
			result.setMsg("成功");
		} catch (Exception e) {
			e.printStackTrace();
			result.setStatus(0);
			result.setMsg("异常");
		}
		
		return result;
	}

	@Override
	@Transactional
	public void grantCusRedPacket() {
		try {
			//获取可发放红包会员列表
			List<Map<String ,Object>> cusList = businessDao.queryRedProvideList();
			SimpleDateFormat df = new SimpleDateFormat("yyyy-MM-dd");//设置日期格式
			Calendar c = Calendar.getInstance();
			Date date = new Date();
			
			c.setTime(date);
		    c.add(Calendar.MONTH, 3);
		    Date m3 = c.getTime();
			for(int i=0;i<cusList.size();i++) {
				cusList.get(i).put("provide_time", df.format(date));
				cusList.get(i).put("overdue_time", df.format(m3));
			}
			if(cusList.size()>0) {
				//发放红包
				businessDao.addRedPacket(cusList);
				//修改剩余发放次数
				businessDao.updateSurplusCountById(cusList);
			}
		} catch (Exception e) {
			e.printStackTrace();
		}
	}
	
	@Override
	@Transactional
	public void redPacketOverdue() {
		try {
			//修改过期红包
			businessDao.updateRedStatus();
		} catch (Exception e) {
			e.printStackTrace();
		}
	}
	
	@Override
	@Transactional
	public PurResult weixinTransfers(String withd_id,String spbill_create_ip) {
		PurResult result = new PurResult();
		try {
			//获取提现信息
			Map<String ,Object> widthInfo = businessDao.queryWidthInfo(withd_id);
			
			BigDecimal bigDecimal_total_fee = NumberUtil.mul(MUtil.strObject(widthInfo.get("withd_money")), "100");//微信请求金额单位为分
			Integer amount = bigDecimal_total_fee.intValue();
			SortedMap<Object,Object> parametersMap = new TreeMap<Object,Object>();
			parametersMap.put("mch_appid", PayConfigUtil.JINQUAN_APP_ID);//应用ID
			parametersMap.put("mchid", PayConfigUtil.JINQUAN_MCH_ID);//商户号
			parametersMap.put("nonce_str", MUtil.getRandomString(32));//32位随机字符串
			parametersMap.put("partner_trade_no", MUtil.strObject(widthInfo.get("width_num")));//商户订单号，需保持唯一性
			parametersMap.put("openid", MUtil.strObject(widthInfo.get("openid")));//用户标识
			parametersMap.put("check_name", "NO_CHECK");//不强校验真实姓名
//			parametersMap.put("re_user_name", MUtil.strObject(params.get("re_user_name")));//强校验真实姓名
			parametersMap.put("amount", amount.toString());//付款金额
			parametersMap.put("desc", "能人提现");//用户提现
			parametersMap.put("spbill_create_ip",spbill_create_ip);//Ip地址；该IP同在商户平台设置的IP白名单中的IP没有关联，该IP可传用户端或者服务端的IP。
			String sign = PayToolUtil.createSign("utf-8",parametersMap,PayConfigUtil.JINQUAN_API_KEY);//生成签名，签名算法
			parametersMap.put("sign", sign);
			String xml = PayToolUtil.getRequestXml(parametersMap);//拼接xml请求参数
			System.out.println("------微信企业付款参数："+xml);
			String weixinPost = ClientCustomSSL.doRefund(PayConfigUtil.TRANSFERS, xml,PayConfigUtil.JINQUAN_MCH_ID);
			System.out.println("======微信企业付款返回参数："+weixinPost);
			Map<String ,Object> map = XMLUtil4jdom.doXMLParse(weixinPost);
			String result_code = MUtil.strObject(map.get("result_code"));
			if(result_code.equals("SUCCESS")) {
				//修改提现状态为成功
				widthInfo.put("withd_status", "2");
				widthInfo.put("wechat_payment_no", map.get("payment_no"));
				businessDao.updateWidthInfo(widthInfo);
				
				result.setStatus(1);
				result.setMsg("成功");
			}else {
				//修改提现状态为失败
				widthInfo.put("withd_status", "3");
				businessDao.updateWidthInfo(widthInfo);
				//修改能人佣金余额
				widthInfo.put("balance_money", widthInfo.get("old_withd_money"));
				businessDao.updateRedPerson(widthInfo);
				
				result.setStatus(2);
				result.setMsg(MUtil.strObject(map.get("err_code_des")));
			}
		} catch (Exception e) {
			e.printStackTrace();
			result.setStatus(0);
			result.setMsg("异常");
		}
		
		return result;
	}
	
	@Override
	@Transactional
	public PurResult unicomPersonCommissionImport(List<Map<String, Object>> list) {
		PurResult result = new PurResult();
		try {
			List<Map<String ,Object>> redCommissionList = new ArrayList<>();
			for(int i=0;i<list.size();i++) {
				Map<String ,Object> params = list.get(i);
				
				//根据手机号获取用户信息
				Map<String ,Object> cus_info = businessDao.queryCusInfoByPhone(params);
				if(cus_info != null) {
					//根据套餐金额和业务类型获取红包期数和红包金额
					Map<String ,Object> business = businessDao.queryBusinessByTypeAndMoney(params);
					if(business == null) {
						result.setStatus(0);
						result.setMsg("套餐金额为"+params.get("business_money")+"未加入专享红包业务");
						
						TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();//事务回滚
						return result;
					}
					Double commission_money = Double.valueOf(params.get("out_account").toString())*Double.valueOf(business.get("out_account_rate").toString())/100;
					//添加能人佣金记录
					Map<String ,Object> redCommission = new HashMap<String ,Object>();
					redCommission.put("staff_num", params.get("referees_num"));
					redCommission.put("cus_unique", cus_info.get("cus_unique"));
					redCommission.put("business_id", business.get("business_id"));
					redCommission.put("commission_money", commission_money);
					redCommission.put("commission_type", "2");//长期用户消费佣金
					redCommission.put("consume_money", params.get("out_account"));//消费金额
					redCommissionList.add(redCommission);
					
					//获取能人信息
					Map<String ,Object> redPerson = businessDao.queryRedPerson(params);
					if(redPerson != null ) {//修改能人佣金余额
						redPerson.put("balance_money", commission_money);
						redPerson.put("staff_num", params.get("referees_num"));
						businessDao.updateRedPerson(redPerson);
					}else {//添加能人佣金信息
						Map<String ,Object> redPersonParams = new HashMap<>();
						redPersonParams.put("balance_money", commission_money);
						redPersonParams.put("staff_num", params.get("referees_num"));
						redPersonParams.put("person_name", params.get("person_name"));
						redPersonParams.put("person_phone", params.get("person_phone"));
						businessDao.addRedPerson(redPersonParams);
					} 
				}
			}
			//添加能人佣金记录
			if(redCommissionList.size()>0) {
				businessDao.addRedCommission(redCommissionList);
			}
			
			result.setStatus(1);
			result.setMsg("成功");
		} catch (Exception e) {
			e.printStackTrace();
			result.setStatus(0);
			result.setMsg("异常");
		}
		
		return result;
	}
	
	@Override
	public PurResult queryRedPacketList(Map<String, Object> params) {
		PurResult result = new PurResult();
		try {
			List<Map<String ,Object>> list = businessDao.queryRedPacketList(params);
			Integer count = businessDao.queryRedPacketListCount(params);
			result.setData(list);
			result.setCount(count);
			
			result.setStatus(1);
			result.setMsg("成功");
		} catch (Exception e) {
			e.printStackTrace();
			result.setStatus(0);
			result.setMsg("异常");
		}
		
		return result;
	}
}
