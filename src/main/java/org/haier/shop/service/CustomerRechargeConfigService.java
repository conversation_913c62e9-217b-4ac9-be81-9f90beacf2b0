package org.haier.shop.service;

import java.util.Map;

import org.haier.shop.util.PurResult;
import org.haier.shop.util.ShopsResult;

public interface CustomerRechargeConfigService {
	public ShopsResult addCusOffRechargeConfig(String rechargeName,Double money,String startTime,String endTime,String shopUnique,
			Integer isCoupon,String couponList,Integer isPoint,Double addPoint,Integer isGoods,String goodsList,Integer isBalance,Double addBalance,
			String isCusLevel,Integer cusLevelId
			);

	public PurResult queryRechargeConfigList(Map<String ,Object> params);
	
	public PurResult InsertRechargeConfig(Map<String ,Object> params);
	
	public PurResult updateRechargeConfig(Map<String ,Object> params);
}
