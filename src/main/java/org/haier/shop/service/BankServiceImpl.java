package org.haier.shop.service;

import java.io.File;
import java.io.FileInputStream;
import java.io.InputStream;
import java.util.List;
import java.util.Map;
import java.util.UUID;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;

import org.haier.shop.dao.BankDao;
import org.haier.shop.util.*;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;

@Service
public class BankServiceImpl implements BankService {
    @Resource
    private BankDao bankDao;

    @Override
    public PurResult getBankList(Map<String, Object> params) {
        PurResult result = new PurResult();
        try {
            params.put("url", Load.IMGDOMAINNAME);
            List<Map<String, Object>> list = bankDao.getBankList(params);
            Integer count = bankDao.getBankListCount(params);
            result.setStatus(1);
            result.setMsg("成功");
            result.setCount(count);
            result.setData(list);
        } catch (Exception e) {
            e.printStackTrace();
            result.setStatus(0);
            result.setMsg("异常");
        }
        return result;
    }

    @Override
    @Transactional
    public PurResult addBank(Map<String, Object> params, HttpServletRequest request) {
        PurResult result = new PurResult();
        try {
            MultipartFile file = null;
            if (request instanceof MultipartHttpServletRequest) {
                MultipartHttpServletRequest multipartHttpServletRequest = (MultipartHttpServletRequest) request;
                Map<String, MultipartFile> mp = multipartHttpServletRequest.getFileMap();
                file = mp.get("bank_img");

            }
            if (file != null) {
                String orName = file.getOriginalFilename();
                String lastName = orName.substring(orName.lastIndexOf("."));
                String shop_dir = File.separator + "home" + File.separator + "apache-tomcat-7.0.42"
                        + File.separator + "webapps" + File.separator + "image" + File.separator + "bankImg"
                        + File.separator;
                File dir = new File(shop_dir);
                if (!dir.exists()) {
                    dir.mkdirs();
                }
                String shop_pictureName = UUID.randomUUID() + lastName;
                PicSaveUtil.handleFileUpId(file, request, shop_dir, shop_pictureName);//图片的保存
                String shop_picture_path = "image" + File.separator + "bankImg" + File.separator + shop_pictureName;
                if (shop_picture_path != null && !"".equals(shop_picture_path)) {
                    params.put("bank_img", shop_picture_path);
                }
            }

            MultipartFile file1 = null;
            if (request instanceof MultipartHttpServletRequest) {
                MultipartHttpServletRequest multipartHttpServletRequest = (MultipartHttpServletRequest) request;
                Map<String, MultipartFile> mp = multipartHttpServletRequest.getFileMap();
                file1 = mp.get("bank_logo");

            }
            if (file1 != null) {
                String orName = file1.getOriginalFilename();
                String lastName = orName.substring(orName.lastIndexOf("."));
                String shop_dir = File.separator + "home" + File.separator + "apache-tomcat-7.0.42"
                        + File.separator + "webapps" + File.separator + "image" + File.separator + "bankImg"
                        + File.separator;
                File dir = new File(shop_dir);
                if (!dir.exists()) {
                    dir.mkdirs();
                }
                String shop_pictureName = UUID.randomUUID() + lastName;
                PicSaveUtil.handleFileUpId(file1, request, shop_dir, shop_pictureName);//图片的保存
                String shop_picture_path = "image" + File.separator + "bankImg" + File.separator + shop_pictureName;
                if (shop_picture_path != null && !"".equals(shop_picture_path)) {
                    params.put("bank_logo", shop_picture_path);
                }
            }
            bankDao.addBank(params);
            result.setStatus(1);
            result.setMsg("成功");
        } catch (Exception e) {
            e.printStackTrace();
            result.setStatus(0);
            result.setMsg("异常");
        }
        return result;
    }

    @Override
    public Map<String, Object> getBank(String bank_id) {
        Map<String, Object> bank = bankDao.getBank(bank_id);
        bank.put("img_url", Load.IMGDOMAINNAME);
        return bank;
    }

    @Override
    @Transactional
    public PurResult updateBank(Map<String, Object> params, HttpServletRequest request) {
        PurResult result = new PurResult();
        SFTPUtil sftp = new SFTPUtil(FTPConfig.username, FTPConfig.password, FTPConfig.host, FTPConfig.port);
        sftp.login();
        try {
            // background
            MultipartFile file = null;
            if (request instanceof MultipartHttpServletRequest) {
                MultipartHttpServletRequest multipartHttpServletRequest = (MultipartHttpServletRequest) request;
                Map<String, MultipartFile> mp = multipartHttpServletRequest.getFileMap();
                file = mp.get("bank_img");

            }
            if (file != null) {
                String orName = file.getOriginalFilename();
                String lastName = orName.substring(orName.lastIndexOf("."));
                String image_dir = File.separator + "bankImg" + File.separator;

                String newName = UUID.randomUUID() + lastName;
                byte[] bytes = ImageZipUtils.file2byte(ImageZipUtils.MultipartFileToFile(file));
                try {
                    sftp.upload(FTPConfig.goods_path + image_dir, newName, bytes);
                    String bank_img_path = "/image" + image_dir + newName;
                    params.put("bank_img", bank_img_path);
                } catch (Exception e) {
                    System.out.println("银行背景图文件上传失败");
                    e.printStackTrace();
                }
            }
            // logo
            MultipartFile file1 = null;
            if (request instanceof MultipartHttpServletRequest) {
                MultipartHttpServletRequest multipartHttpServletRequest = (MultipartHttpServletRequest) request;
                Map<String, MultipartFile> mp = multipartHttpServletRequest.getFileMap();
                file1 = mp.get("bank_logo");

            }
            if (file1 != null) {
                String orName = file1.getOriginalFilename();
                String lastName = orName.substring(orName.lastIndexOf("."));
                String image_dir = File.separator + "bankImg" + File.separator;

                String newName = UUID.randomUUID() + lastName;
                byte[] bytes = ImageZipUtils.file2byte(ImageZipUtils.MultipartFileToFile(file1));
                try {
                    sftp.upload(FTPConfig.goods_path + image_dir, newName, bytes);
                    String bank_logo_path = "/image" + image_dir + newName;
                    params.put("bank_logo", bank_logo_path);
                } catch (Exception e) {
                    System.out.println("银行logo文件上传失败");
                    e.printStackTrace();
                }
            }
            bankDao.updateBank(params);
            result.setStatus(1);
            result.setMsg("成功");
        } catch (Exception e) {
            e.printStackTrace();
            result.setStatus(0);
            result.setMsg("异常");
        } finally {
            sftp.logout();
        }
        return result;
    }

    @Override
    @Transactional
    public PurResult deleteBank(String bank_id) {
        PurResult result = new PurResult();
        try {
            bankDao.deleteBank(bank_id);
            result.setStatus(1);
            result.setMsg("成功");
        } catch (Exception e) {
            e.printStackTrace();
            result.setStatus(0);
            result.setMsg("异常");
        }
        return result;
    }
}
