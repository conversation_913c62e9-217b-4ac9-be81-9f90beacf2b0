package org.haier.shop.service;

import java.util.Map;

import org.haier.shop.util.PurResult;
import org.haier.shop.util.ShopsResult;

public interface CountMsgService {

	/**
	 * 
	 * @param shopUnique 店铺编号
	 * @param startTime 开始时间
	 * @param endTime 结束时间
	 * @param goods_big_class1
	 * @param goods_small_class1
	 * @param supplierUnique1
	 * @param goodsMessage1
	 * @param goods_big_class2
	 * @param goods_small_class2
	 * @param supplierUnique2
	 * @param goodsMessage2
	 * @return
	 */
	public PurResult queryCountStatisticsSameGoods(String shopUnique,String startTime,String endTime
			,Integer goods_big_class1,Integer goods_small_class1,String supplierUnique1,String goodsMessage1
			,Integer goods_big_class2,Integer goods_small_class2,String supplierUnique2,String goodsMessage2
			);
	/**
	 * 
	 * @param shopUnique
	 * @param startTime1
	 * @param endTime1
	 * @param startTime2
	 * @param endTime2
	 * @param goods_big_class
	 * @param good_small_class
	 * @param supplierUnique
	 * @param goodsMessage
	 * @return
	 */
	public PurResult queryCountStatisticsSameTime(String shopUnique,String startTime1,String endTime1,String startTime2,String endTime2
			,Integer goods_big_class,Integer good_small_class,String supplierUnique,String goodsMessage
			) ;
	PurResult queryTurnover(String shop_unique, String type);

	PurResult queryOrderMsg(String shop_unique, String type);

	PurResult queryKindSaleProportion(String shop_unique, String type);

	PurResult querySaleTrend(String shop_unique, String type, String startDate, String endDate);

	PurResult queryShopPurchaseTop5(String shop_unique, String type);

	PurResult queryGoodsSaleTop5(String shop_unique, String type);

	PurResult querySalesmanTop5(String shop_unique, String type);

	PurResult queryPayProportion(String shop_unique, String type);

	ShopsResult orderTotalByHours(String shop_unique);
	
	/**
	 * 百货豆统计
	 * @param map
	 * @return
	 */
	public ShopsResult queryShopStatisticsMsgPage(Map<String,Object> map);
	/**
	 * 积分统计
	 * @param map
	 * @return
	 */
	public ShopsResult queryShopStatisticsMsgPagePoints(Map<String,Object> map);
	/**
	 * 分时段百货豆交易统计
	 * @param map
	 * @return
	 */
	public ShopsResult queryBeansAccountByDays(Map<String,Object> map);
	/**
	 * 分时段积分交易统计
	 * @param map
	 * @return
	 */
	public ShopsResult pointsStatisticsByDay(Map<String,Object> map);

	/**
	 * 公共优惠券店内销售统计
	 * @param map
	 * @return
	 */
	public ShopsResult queryCouponPublicStatistics(Map<String,Object> map);
	
	/**
	 * 周期内平台优惠券店内使用情况统计
	 * @param map
	 * @return
	 */
	public ShopsResult queryPubCouponMsg(Map<String,Object> map);
	/**
	 * 本店优惠券发放及使用情况
	 * @param map
	 * @return
	 */
	public ShopsResult ourShopCouponMsg(Map<String,Object> map);
	/**
	 * 本店优惠券使用情况
	 * @param map
	 * @return
	 */
	public ShopsResult ourCouponUseMsgByDay(Map<String,Object> map);
	/**
	 * 店铺内经营金额统计
	 * @param map
	 * @return
	 */
	public ShopsResult accountStatistics(Map<String,Object> map);
	

	/**
	 * 体现记录
	 * @param map
	 * @return
	 */
	public ShopsResult queryTakeCashList(Map<String,Object> map);

	/**
	 * 订单收入详情界面及分页
	 * @param map
	 * @return
	 */
	public ShopsResult queryOrderListNetWork(Map<String,Object> map);

}
