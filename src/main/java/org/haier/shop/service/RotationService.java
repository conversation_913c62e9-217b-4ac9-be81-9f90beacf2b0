package org.haier.shop.service;

import java.util.List;
import java.util.Map;

import org.haier.shop.util.ShopsResult;

public interface RotationService {
	
	/**
	 * 更新或添加或删除轮播图
	 * @param img_url 图片保存地址
	 * @param brand_id 品牌ID
	 * @param img_id 原图片ID，更新和删除时用到
	 * @param img_type 图片类型
	 * @param remarks 备注信息
	 * @param valid_status 状态：1、正常；0、已删除；
	 * @return
	 */
	public ShopsResult addNewRotationImg(String img_url,Integer brand_id,Integer img_id,Integer img_type,String remarks,Integer valid_status);
	/**
	 * 查询加油站品牌列表
	 * @return
	 */
	public List<Map<String,Object>> queryBrandList();
	/**
	 * 查询轮播图信息
	 * @param brandId 品牌ID
	 * @param imgType 图片类型
	 * @param page 页码
	 * @param limit 单页查询数量
	 * @return
	 */
	public ShopsResult queryRotationList(Integer brandId,Integer imgType,Integer page,Integer limit);
}
