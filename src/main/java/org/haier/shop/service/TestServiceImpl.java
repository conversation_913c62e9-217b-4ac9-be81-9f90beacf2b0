package org.haier.shop.service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;

import org.haier.shop.dao.ShopDao;
import org.haier.shop.dao.TestDao;
import org.haier.shop.dao2.ShopTDao;
import org.haier.shop.util.HttpGetUtil;
import org.haier.shop.util.HttpsUtil;
import org.haier.shop.util.ShopsResult;
import org.springframework.stereotype.Service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;

import io.goeasy.GoEasy;

@Service("testService")
public class TestServiceImpl implements TestService{
	@Resource
	private TestDao testDao;
	
	@Resource
	private ShopTDao shopTDao;
	
	public ShopsResult updateShopTownCode(Integer maxIndex) {
		ShopsResult sr = new ShopsResult(1, "");
		
		List<Map<String,Object>> shopList = testDao.queryShopsList();
		if(null != shopList && !shopList.isEmpty()) {
			for(Integer i = 0; i < shopList.size() && i <= maxIndex; i++) {
				//
				shopTDao.updateShopTownCode(shopList.get(i));
			}
		}
		
		return sr;
	}
	
	public ShopsResult updateShopMsg(Integer maxSize ) {
		ShopsResult sr = new ShopsResult(1, "");
		
		List<Map<String,Object>>  shopsList = testDao.queryShopsList();
		
		for(Integer i = 0; i < shopsList.size() && i < (maxSize == null ? 1000000 : maxSize); i++) {
			//获取数据
			String url = "https://restapi.amap.com/v3/geocode/regeo";
			String param = "key=0518502b5e3e90fad440e3dd35c26b68&location=";
			param += shopsList.get(i).get("shop_longitude") + "," + shopsList.get(i).get("shop_latitude");
			String res = HttpGetUtil.sendGet(url, param);
			net.sf.json.JSONObject jo = net.sf.json.JSONObject.fromObject(res);
			String townCode = jo.getJSONObject("regeocode").getJSONObject("addressComponent").getString("towncode");
			
			//将数据更新到服务器
			shopsList.get(i).put("town_code", townCode);
			
			testDao.updateShopMsg(shopsList.get(i));
		}
		
		return sr;
	}
	
	public ShopsResult updateDict(){
		ShopsResult shopsResult=new ShopsResult();
		List<Map<String,Object>> list=testDao.queryGoods();
		int l=0;
		for(int i=0;i<list.size();i++){
//			System.out.println(list.get(i));
			int k=testDao.updateDict(list.get(i));
//			System.out.println(k);
			l+=k;
//			System.out.println(l);
		}
		shopsResult.setData(l);
		return shopsResult;
	}
	
	/**
	 * 测试批量更新
	 * @return
	 */
	public ShopsResult piliang(){
		ShopsResult sr=new ShopsResult();
		List<Integer> list=new ArrayList<Integer>();
		list.add(1);
		list.add(2);
		list.add(3);
		list.add(4);
		int k=testDao.piling(list);
		sr.setData(k);
		return sr;
	}
	
	public ShopsResult huifu(String shop_unique){
		ShopsResult sr=new ShopsResult();
		List<Map<String,Object>> result=testDao.queryIdPath(shop_unique);
		List<Map<String,String>> newGoods=new ArrayList<Map<String,String>>();
		for(int k=0;k<result.size();k++){
			String[] a=result.get(k).get("goods_picturepath").toString().split("/");
			if(a.length>0&&!a[a.length-1].equals("")){
//				System.out.println(result.get(k));
//				System.out.println(a[a.length-1]);
//				System.out.println(a[a.length-1].substring(0, a[a.length-1].length()-4));
				String barcode=a[a.length-1].substring(0, a[a.length-1].length()-4);
				if(!barcode.equals("no_goodsB")){
					Map<String,String> map=new HashMap<String, String>();
					map.put("goods_id", result.get(k).get("goods_id").toString());
					map.put("goods_barcode", barcode);
					newGoods.add(map);
				}
			}
		}
//		System.out.println(newGoods);
		int k=testDao.huifuBarcode(newGoods);
		System.out.println(k);
		sr.setStatus(0);
		sr.setData(newGoods);
		return sr;
	}
	
	public ShopsResult deleteSame(){
		ShopsResult sr=new ShopsResult();
		
		List<String> list=testDao.querySameGoods();
//		System.out.println(list);
		for(int k=0;k<list.size();k++){
			testDao.deleteSameGoods(list.get(k));
//			System.out.println(k);
		}
		return sr;
	}
	
	public ShopsResult testGoeasy(){
		ShopsResult sr=new ShopsResult();
		GoEasy goeasy=new GoEasy("BC-91e94f22780e4ed697c2f0837f1de9c5",null);
		goeasy.publish("测试频道", "测试");
		return sr;
	}
	
	public ShopsResult queryGoodsSupplierMsg(){
		ShopsResult sr=new ShopsResult();
		List<Map<String,Object>> list=testDao.queryGoodsSupplierMsg();
		for(int i=0;i<list.size();i++){
			System.out.println(list.get(i));
		}
		Integer k=testDao.updateGoodsMsg(list);
		
		sr.setData(list);
		return sr;
	}
	
}
