package org.haier.shop.service;

import java.math.BigDecimal;
import java.sql.Timestamp;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Random;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;

import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSON;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.haier.meituan.util.MUtil;
import org.haier.shop.dao.GoodsDao;
import org.haier.shop.dao.Purchase_listDao;
import org.haier.shop.dao.SelfPurchaseDao;
import org.haier.shop.dao.SupplierDao;
import org.haier.shop.entity.*;
import org.haier.shop.util.HttpClientUtil;
import org.haier.shop.util.Load;
import org.haier.shop.util.PurResult;
import org.haier.shop.util.ShopsResult;
import org.haier.util.eshow.UUIDUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import net.sf.json.JSONArray;
import net.sf.json.JSONObject;
import org.springframework.util.ObjectUtils;

@Service("purchaseListService")
@Transactional
public class PurchaseListServiceImpl implements PurchaseListService{
	@Resource
	private Purchase_listDao purDao;
	@Resource
	private GoodsDao goodsDao;
	@Resource
	private SelfPurchaseDao selfPurchaseDao;
	@Resource
	private SupplierDao supplierDao;
	
	
	
	public ShopsResult modifyGoodsSupplier(String supplierUnique,String goodsIds) {
		ShopsResult sr = new ShopsResult(1,"操作成功！");
		if(null == goodsIds || goodsIds.equals("")) {
			sr.setStatus(0);
			sr.setMsg("未选择商品");
			return sr;
		}
		if(null == supplierUnique || supplierUnique.equals("")) {
			sr.setStatus(0);
			sr.setMsg("未选择供货商");
			return sr;
		}
		
//		List<String> list = new ArrayList<String>();
		String[] ids = goodsIds.split(";");
		Map<String,Object> map = new HashMap<String,Object>();
		map.put("supplierUnique", supplierUnique);
		map.put("list", ids);
		
		goodsDao.modifyGoodsSupplier(map);
		
		
		return sr;
	}
	
	/**
	 * 进货订单查询
	 * 	@param shop_unique
	 * @param orderMessage
	 * @param startTime
	 * @param endTime
	 * @param order
	 * @param orderType
	 * @param pageNum
	 * @param pageSize
	 * @param receipt_status
	 * @param paystatus
	 * @return
	 */
	public ShopsResult queryPurLists(String shop_unique, String orderMessage, Timestamp startTime, Timestamp endTime,
			String orderName, String orderType, Integer pageNum, Integer pageSize, Integer receipt_status,
			Integer paystatus) {
		ShopsResult shop=new ShopsResult();
		Map<String,Object> map=new HashMap<String,Object>();
		map.put("shop_unique", shop_unique);
		if(null==orderMessage||"".equals(orderMessage.trim())){
			orderMessage=null;
		}else{
			orderMessage="%"+orderMessage+"%";
		}
		map.put("orderMessage", orderMessage);
		map.put("startTime", startTime);
		map.put("endTime", endTime);
		
		if(null!=receipt_status&&receipt_status<0){
			receipt_status=null;
		}
		if(null!=paystatus&&(paystatus<0)){
			paystatus=null;
		}
		
		map.put("receipt_status", receipt_status);
		if(paystatus!=null&&paystatus!=0){
			map.put("paystatus", paystatus);
		}
		map.put("purchase_list_parunique", 0);
		List<Map<String,Object>> data=purDao.queryPurLists(map);
		Integer k=data.size();
		if(k==0){
			shop.setStatus(1);
			shop.setMsg("没有想关订单！");
			return shop;
		}
		Map<String,Object> mp=purDao.queryPurTotal(map);
		
		if(k%pageSize==0){
			mp.put("pageCount",k/pageSize);
		}else{
			mp.put("pageCount",k/pageSize+1);
		}
		map.put("pageSize", pageSize);
		map.put("startNum", (pageNum-1)*pageSize);
		map.put("OrderBy"," "+orderName +" "+orderType);
		List<Map<String,Object>> orderList=purDao.queryPurLists(map);
		shop.setStatus(0);
		shop.setMsg("查询成功！");
		shop.setCord(mp);
		shop.setData(orderList);
		return shop;
	}
	/**
	 * 订单详情查询
	 * @param shop_unique
	 * @param purchase_list_unique
	 * @return
	 */
	public ShopsResult queryOrderDetail(String shop_unique,String purchase_list_unique){
		ShopsResult shop=new ShopsResult();
		Map<String,Object> map=new HashMap<String,Object>();
		if(null==shop_unique){
			shop.setStatus(2);
			shop.setMsg("登录超时！请重新登录！");
			return shop;
		}
		if(null==purchase_list_unique){
			shop.setStatus(1);
			shop.setMsg("订单编号不能为空！");
			return shop;
		}
		map.put("shop_unique", shop_unique);
		map.put("purchase_list_unique", purchase_list_unique);
//		System.out.println("订单详情查询："+map);
		List<Map<String,Object>> dList=purDao.queryOrderDetail(map);
		if(dList.size()==0){
			shop.setStatus(1);
			shop.setMsg("没有想关的订单详情，请查证！");
			return shop;
		}
		shop.setData(dList);
		shop.setStatus(0);
		shop.setMsg("查询成功！");
		return shop;
	}
	/**
	 * 更新购物车商品
	 * @param shop_unique 店铺编号
	 * @param goods_barcode 商品条码
	 * @param count 变换数量
	 * @return
	 */
	public ShopsResult updateCartGoodsCount(String shop_unique,String goods_barcode,Integer count,String supplier_unique){
		ShopsResult shop=new ShopsResult();
		Map<String,Object> map=new HashMap<String,Object>();
		if(null==goods_barcode){
			shop.setStatus(1);
			shop.setMsg("商品条码不能为空！");
			return shop;
		}
		map.put("shop_unique", shop_unique);
		map.put("supplier_unique", supplier_unique);//供货商编号
		map.put("purchase_list_status", "2");//购物车状态
//		验证商品是否为该店铺所供应
//		if(null==goodsDao.queryGoodsDetail(map)){
//			shop.setStatus(1);
//			shop.setMsg("该店铺不供应此产品");
//			return shop;
//		}
		
		map.put("group", "purchase_list_parunique");
//		System.out.println(map);
		List<Map<String,Object>> pur=purDao.queryCartGoodsUnique(map);
//		System.out.println("订单查询"+pur);
//		System.out.println(pur.size());//购物车数量
		if(0==pur.size()){//购物车不存在
			Long purchase_list_unique=new Date().getTime();
			map.put("purchase_list_unique", purchase_list_unique+"");
			int k=purDao.createPurCart(map);
			if(k==0){
				shop.setStatus(1);
				shop.setMsg("添加失败！");
				return shop;
			}
			map.put("goods_barcode", goods_barcode);
			map.put("purchase_list_parunique", purchase_list_unique);
			map.put("purchase_list_detail_count", count);
			purDao.addCartGoods(map);
			shop.setStatus(0);
			shop.setMsg("添加成功！");
			return shop;
		}
		map.put("goods_barcode", goods_barcode);
		if(pur.get(0).get("purchase_list_parunique")!=null){
			
		}
		String purchase_list_parunique=pur.get(0).get("purchase_list_unique").toString();
		pur.clear();
		pur=purDao.queryCartGoodsUnique(map);
		map.put("purchase_list_parunique", purchase_list_parunique);
		if(0!=pur.size()){//购物车存在该商品
			if(pur.get(0).get("purchase_list_detail_count")!=null){
				Double purchase_list_detail_count=Double.parseDouble(pur.get(0).get("purchase_list_detail_count").toString())+count;
				if(purchase_list_detail_count<=0){//新旧数量相加后数量不大于零则删除
					purDao.deleteCartGoods(map);
//					System.out.println("删除购物车商品！");
				}else{//大于零则更新
					map.put("purchase_list_detail_count", purchase_list_detail_count);
					purDao.updateCartDetail(map);
				}
			}
		}else{//不存在则添加
			map.put("purchase_list_detail_count", count);
			if(count<=0){
				shop.setStatus(1);
				shop.setMsg("添加数量需大于0!");
				return shop;
			}
			purDao.addCartGoods(map);
		}
		shop.setStatus(0);
		shop.setMsg("更新成功！");
		return shop;
	}
	/**
	 * 供货商购物车订单详情
	 * @param shop_unique
	 * @return
	 */
	public ShopsResult queryPurCartGoods(String shop_unique){
		ShopsResult shop=new ShopsResult();
		Map<String,Object> map=new HashMap<String,Object>();
		if(null==shop_unique){
			shop.setStatus(2);
			shop.setMsg("登录超时！请重新登录！");
			return shop;
		}
		map.put("purchase_list_status", 2);
		map.put("shop_unique", shop_unique);
		
		List<PurCart> reList=purDao.queryPurCartGoods(map);
		if(reList.size()==0){
			Long purchase_list_unique=new Date().getTime();
			map.put("purchase_list_unique", purchase_list_unique+"");
			map.put("receipt_status", Load.CARTRECEIPTSTATUS);//购物车状态
//			purDao.createPurCart(map);
			shop.setStatus(1);
			shop.setMsg("购物车为空！");
			return shop;
		}
		
		shop.setStatus(0);
		shop.setMsg("查询成功！");
		shop.setData(reList);
		return shop;
	}
	
	/**
	 * 更新购物车商品数量
	 * @param shop_unique 店铺编号
	 * @param purchase_list_parunique 购物车编号
	 * @param goods_barcode 商品条码
	 * @param count 修改后的商品数量
	 * @return
	 */
	public ShopsResult modifyCartDetail(String shop_unique,String purchase_list_parunique,String goods_barcode,Double count){
		ShopsResult shop=new ShopsResult();
		Map<String,Object> map=new HashMap<String,Object>();
		if(null==shop_unique){
			shop.setStatus(2);
			shop.setMsg("登录超时！请重新登录！");
			return shop;
		}
		if(null==purchase_list_parunique){
			shop.setStatus(1);
			shop.setMsg("订单编号不能为空");
			return shop;
		}
		if(null==goods_barcode){
			shop.setStatus(1);
			shop.setMsg("商品条码不能为空！");
			return shop;
		}
		if(count<=0){
			shop.setStatus(1);
			shop.setMsg("商品数量不能少于零");
			return shop;
		}
		
		map.put("purchase_list_parunique", purchase_list_parunique);
		map.put("goods_barcode", goods_barcode);
		map.put("purchase_list_detail_count", count);
		int k=purDao.modifyCartDetail(map);
		if(k==0){
			shop.setStatus(1);
			shop.setMsg("更新失败！");
			return shop;
		}
		shop.setStatus(0);
		shop.setMsg("更新成功！");
		return shop;
	}
	
	/**
	 * 移除购物车商品
	 * @param shop_unique 供货商编号
	 * @param purchase_list_parunique 购物车编号
	 * @param goods_barcode 商品条码
	 * @return
	 */
	public ShopsResult deleteFromCart(String shop_unique,String purchase_list_parunique,String goods_barcode){
		ShopsResult shop=new ShopsResult();
		Map<String,Object> map=new HashMap<String,Object>();
		if(null==shop_unique){
			shop.setStatus(2);
			shop.setMsg("登录超时！请重新登录！");
			return shop;
		}
		if(null==goods_barcode){
			shop.setStatus(1);
			shop.setMsg("商品条码不能为空！");
			return shop;
		}
		if(null==purchase_list_parunique){
			shop.setStatus(1);
			shop.setMsg("订单编号不能为空");
			return shop;
		}
		
		map.put("goods_barcode", goods_barcode);
		map.put("purchase_list_parunique", purchase_list_parunique);
		int k=purDao.deleteCartGoods(map);
//		System.out.println("商品删除："+map);
		if(k!=1){
			shop.setStatus(1);
			shop.setMsg("商品删除失败！");
			return shop;
		}
		shop.setStatus(0);
		shop.setMsg("商品移除成功！");
		return shop;
	}
	
	/**
	 * 提交订单
	 * @param shop_unique
	 * @param goodsBarcodes
	 * @param purchase_list_parunique
	 * @return
	 */
	public ShopsResult toSubmitCart(String shop_unique,String[] goodsBarcodes,String purchase_list_parunique){
//		System.out.println("提交订单");
		ShopsResult shop=new ShopsResult();
		Map<String,Object> map=new HashMap<String,Object>();
		if(null==shop_unique){
			shop.setStatus(2);
			shop.setMsg("登录超时！请重新登录！");
			return shop;
		}
		if(null==purchase_list_parunique){
			shop.setStatus(1);
			shop.setMsg("订单编号不能为空");
			return shop;
		}
		map.put("shop_unqiue", shop_unique);
		List<String> barcodes=new ArrayList<String>();
		for(int i=0;i<goodsBarcodes.length;i++){
			barcodes.add(goodsBarcodes[i]);
		}
		map.put("goodsBarcodes", barcodes);
		map.put("purchase_list_parunique", purchase_list_parunique);
		List<PurCart> list=purDao.queryPurCartGoods(map);
		
		shop.setStatus(0);
		shop.setMsg("提交成功！");
		shop.setData(list);
		return shop;
	}
	/**
	 * 确认采购订单
	 * 
	 */
	public ShopsResult submitGoods(String shop_unique, String[] barcodes, String[] remarks,String purchase_list_parunique) {
//		System.out.println(remarks[0]);
		ShopsResult shop=new ShopsResult();
		Map<String,Object> cmap=new HashMap<String, Object>();//主订单更新map
		cmap.put("purchase_list_unique", purchase_list_parunique);
		cmap.put("purchase_list_status", 1);
		
		List<Map<String,Object>> purlists=new ArrayList<Map<String,Object>>();//子订单信息集合
		Random random=new Random();//随机函数，用户创建订单编号
		
		//商品信息查询（包含商品进价，商品售价，商品名称）
		List<String> goodsBarcodes=new ArrayList<String>();//商品条码集合，用于商品信息查询
		for(String bar:barcodes){
			goodsBarcodes.add(bar);
		}
		
		Map<String,Object> gmap=new HashMap<String, Object>();//商品条码map用于商品信息查询
		gmap.put("goodsBarcodes", goodsBarcodes);
		gmap.put("shop_unique", shop_unique);
		List<Map<String,Object>> goodsList=purDao.queryGoodsMessage(gmap);//购物车中所有商品的信息
//		System.out.println("购物车中商品为："+goodsList);
		List<Map<String,Object>> ngoodsList=new ArrayList<Map<String,Object>>();//用于更新商品详情
		
		Double totalTotal=0.00;//订单商品总金额
		Double totalSum=0.0;//订单商品总数量
		
		for(int i=0;i<remarks.length;i++){
			Map<String,Object> mp=new HashMap<String, Object>();
//			JSONObject object=JSONObject.fromObject(jsonArray.get(i));
//			mp.put("supplier_unique", object.get("supplier_unique"));//供货商编号
			mp.put("supplier_unique", remarks[i].split(":")[0]);
//			mp.put("purchase_list_remark", object.get("remark"));//订单备注
			if(!remarks[i].split(":")[1].equals("]|[|]|")){
				try {
				mp.put("purchase_list_remark", remarks[i].split(":")[1]);
				} catch (Exception e) {
					e.printStackTrace();
				}
			}else{
				mp.put("purchase_list_remark", "");
			}
			Integer num=random.nextInt(89)+10;
			String purchase_list_unique=new Date().getTime()+""+num+i;
			mp.put("purchase_list_unique", purchase_list_unique);//子订单编码
			mp.put("shop_unique", shop_unique);//店铺编号
			mp.put("purchase_list_parunique", purchase_list_parunique);//父类编号
			Timestamp purchase_list_date=new Timestamp(new Date().getTime());
			mp.put("purchase_list_date", purchase_list_date);//订单生成时间
			cmap.put("purchase_list_date", purchase_list_date);//主订生成时间
			Double purchase_list_total=0.00;//每个子订单的总金额
			Double purchase_list_saleTotal=0.00;//每个子订单的销售金额
			Double purchase_list_sum=0.0;//每个子订单的数量
//			System.out.println("新订单的供货商编号为："+object.get("supplier_unique"));
			for(int j=0;j<goodsList.size();j++){
//				System.out.println(goodsList.get(j).get("supplier_unique"));
				if(remarks[i].split(":")[0].equals(goodsList.get(j).get("supplier_unique"))){
//				if(object.get("supplier_unique").toString().equals(goodsList.get(j).get("supplier_unique"))){
					
//					purchase_list_total+=Double.parseDouble(goodsList.get(j).get("goods_price").toString());
					purchase_list_total+=Double.parseDouble(goodsList.get(j).get("goods_price").toString())*Double.parseDouble(goodsList.get(j).get("purchase_list_detail_count").toString());
					purchase_list_saleTotal+=Double.parseDouble(goodsList.get(j).get("goods_sale_price").toString());
					purchase_list_sum+=Double.parseDouble(goodsList.get(j).get("purchase_list_detail_count").toString());
//					System.out.println("该此循环的金额为："+purchase_list_sum);
					goodsList.get(j).put("purchase_list_unique", purchase_list_unique);
					goodsList.get(j).put("purchase_list_parunique", purchase_list_parunique);
					ngoodsList.add(goodsList.get(j));//用于更新子订单详情
//					break;//如果找到相同的差评就结束本次内循环
				}
			}
//			System.out.println("zing订单金额："+purchase_list_sum);
			mp.put("purchase_list_sum", purchase_list_sum);
			totalSum+=purchase_list_sum;
			totalTotal+=purchase_list_total;
			mp.put("purchase_list_saleTotal", purchase_list_saleTotal);
			mp.put("purchase_list_total", purchase_list_total);
			mp.put("purchase_list_status", 1);
			purlists.add(mp);
		}
		cmap.put("totalSum", totalSum);
//		System.out.println("总商品数量："+totalSum);
		cmap.put("totalTotal", totalTotal);
//		System.out.println("总商品金额“"+totalTotal);
		
//		System.out.println(cmap);
		int k=purDao.updateMainPurList(cmap);//更新主订单信息
		if(k==0){
		}
//		System.out.println(k);
		purDao.addSubPurLists(purlists);//创建新子订单
//		System.out.println("购物车详情更新："+ngoodsList);
		purDao.updateCartDetail1(ngoodsList);//更新订单详情
		k=goodsDao.updateGoodsMessageList(ngoodsList);
//		purDao.newSubOrders(purlists);
		String purchase_list_newparunique=new Date().getTime()+"";
		Map<String,Object> nmap=new HashMap<String, Object>();
		nmap.put("purchase_list_parunique", purchase_list_parunique);
		nmap.put("purchase_list_newparunique", purchase_list_newparunique);
		nmap.put("purchase_list_unique", purchase_list_newparunique);
		nmap.put("shop_unique", shop_unique);
		nmap.put("purchase_list_status", 2);
		purDao.createPurCart(nmap);//创建新购物车
		purDao.updateToCartDetail(nmap);//将未结算的购物车商品添加到新购物车中
		shop.setStatus(0);
		shop.setMsg("提交成功！");
		return shop;
	}
	
	
	/**
	 * 快速采购库存不足的商品
	 * @param shop_unique
	 * @param stockType
	 * @return
	 */
	public ShopsResult easyPurchase(String shop_unique,Integer stockType){
//		System.out.println("库存不足查询！");
		ShopsResult shop=new ShopsResult();
		Map<String,Object> map=new HashMap<String, Object>();
		map.put("shop_unique", shop_unique);
		map.put("stockType", stockType);
		map.put("purchase_list_status", 2);
		map.put("purType", "aaa");
		map.put("days", 30);
		List<Map<String,Object>> list=goodsDao.getGoodList(map);//查询商品信息，确定需要采购的商品列表
//		System.out.println("查询商品信息："+list);
		List<Map<String,Object>> cartList=purDao.queryCartGoodsUnique(map);//查询购物车商品
		List<Map<String,Object>> upList=new ArrayList<Map<String,Object>>();//更新列表
		List<Map<String,Object>> newList=new ArrayList<Map<String,Object>>();//新加列表
//		System.out.println("购物车内容查询："+cartList);
		String purchase_list_parunique=new Date().getTime()+"";
		if(null==cartList||cartList.isEmpty()){
			Map<String,Object> nmap=new HashMap<String, Object>();
			nmap.put("purchase_list_unique", purchase_list_parunique);
			nmap.put("purchase_list_status", 2);
			nmap.put("shop_unique", shop_unique);
			purDao.createPurCart(nmap);
		}else{
			purchase_list_parunique=cartList.get(0).get("purchase_list_unique").toString();
		}
		for(int i=0;i<list.size();i++){
			Integer count=(int) (15*Double.parseDouble(list.get(i).get("aver").toString())-Integer.parseInt(list.get(i).get("goods_count").toString()));
//			System.out.println(count);
			String goods_barcode=list.get(i).get("goods_barcode").toString();
			boolean flag=true;
			for(int j=0;j<cartList.size();j++){
				if(cartList.get(j).get("goods_barcode")==null){
					break;
				}
				if(goods_barcode.equals(cartList.get(j).get("goods_barcode").toString())){
					Map<String,Object>  t=cartList.get(j);
					t.put("purchase_list_detail_count", count);//设置需要更新的数量
					t.put("purchase_list_parunique", purchase_list_parunique);
					upList.add(t);
					flag=false;
					break;
				}
			}
			if(flag){
				Map<String ,Object> temp=list.get(i);
				temp.put("purchase_list_parunique", purchase_list_parunique);
				temp.put("purchase_list_detail_count",count);
				newList.add(list.get(i));
			}
		}
		
		//将数据更新到数据库
//		System.out.println(upList);
//		System.out.println(newList);
		if(upList.size()>0){
			purDao.updateCartDetails(upList);//更新子订单信息
		}
		if(newList.size()>0){
			
			purDao.addCartDetail(newList);
		}
		shop.setStatus(0);
		shop.setMsg("添加成功！");
		return shop;
	}
	
	/**
	 * 下载进货订单excel表
	 * @param map
	 * @param shopUnique
	 * @return
	 */
	
	public List<PurListMain> purListExcel(Map<String,Object> map,String shopUnique,HttpServletRequest request){
		return purDao.purListExcel(map);
	}
	
	/**
	 * 商品采购界面：商品信息查询
	 * @param map
	 * @return
	 */
	public ShopsResult querySupGoodsForCart(Map<String,Object> map){
		ShopsResult sr=new ShopsResult();
		
		sr.setStatus(1);
		sr.setMsg("查询成功！");
		return sr;
	}
	public ShopsResult printPurList(Map<String, Object> map, String shopUnique, HttpServletRequest request) {
		ShopsResult sr=new ShopsResult();
		List<PurListMain> data=purDao.purListExcel(map);
		if(null==data||data.isEmpty()){
				sr.setStatus(2);
				sr.setMsg("没有满足条件的订单信息！");
				return sr;
		}
		sr.setStatus(1);
		sr.setMsg("查询成功！");
		sr.setData(data);
		return sr;
	}
	public PurResult queryAllorationList(Map<String,Object> params,HttpServletRequest request) {
		PurResult result=new PurResult();
		String serverName = request.getServerName();
		String host = "http://test170.buyhoo.cc";
		if (!serverName.contains("test170") && !serverName.contains("localhost") && !serverName.contains("127.0.0.1")){
			host = "http://buyhoo.cc";
		}
		String url = host + "/shopUpdate/goods/allocate/queryList.do";

		//shopUpdate查询
		Map<String,String> queryParam = new HashMap<>();
		String allocationStatus = MapUtils.getString(params, "allocationStatus");
		if (StringUtils.isNotBlank(allocationStatus)){
			queryParam.put("allocationStatus",allocationStatus);
		}
		String startTime = MapUtils.getString(params, "startTime");
		if (StringUtils.isNotBlank(startTime)){
			queryParam.put("startTime",startTime);
		}
		String endTime = MapUtils.getString(params, "endTime");
		if (StringUtils.isNotBlank(endTime)){
			queryParam.put("endTime",endTime);
		}
		queryParam.put("pullStoreOfId",MapUtils.getString(params,"shop_unique"));
		String goodsMessage = MapUtils.getString(params, "goodsMessage");
		if (StringUtils.isNotBlank(goodsMessage)){
			queryParam.put("searchKey",goodsMessage);
		}
		queryParam.put("pageIndex",MapUtils.getString(params,"page"));
		queryParam.put("pageSize",MapUtils.getString(params,"limit"));

		System.out.println(StringUtils.join("请求shopUpdate调拨:url:",url,",请求参数:",JSON.toJSONString(queryParam)));
		String resp = HttpClientUtil.doGet(url, queryParam);
//		System.out.println(StringUtils.join("请求shopUpdate调拨响应:",resp));

		//返回参数构建
		com.alibaba.fastjson.JSONObject respObj = JSON.parseObject(resp);
		Integer status = respObj.getInteger("status");
		if (status == 0){
			result.setStatus(0);
			result.setMsg(respObj.getString("msg"));
			return result;
		}

		//		List<Map<String,Object>> goodsList=purDao.queryPurListsPages(params);
		result.setStatus(1);
		result.setMsg("查询成功");
		result.setCount(ObjectUtils.isEmpty(respObj.getInteger("count")) ? 0 : respObj.getInteger("count"));
		result.setData(handleGoodsList(respObj.getJSONObject("data")));
		return result;
	}
	
	public List<Map<String,Object>> getAllorationList(Map<String,Object> map){
		return purDao.queryPurListsPages(map);
	}
	
	public PurResult queryGoodsByPage(String managerUnique, String goodsMessage,Long goods_kind_unique, Long goods_kind_parunique,
			 Integer pageNum, Integer pageSize) {
		PurResult pr=new PurResult();
		Map<String,Object> map=new HashMap<String,Object>();
		map.put("managerUnique",managerUnique);
		map.put("pageSize", pageSize);
		map.put("startNum", (pageNum-1)*pageSize);
		
		if(-1==goods_kind_unique||goods_kind_unique==null ){
		}else{
			map.put("goods_kind_unique",goods_kind_unique);
		}
		if(-1==goods_kind_parunique||goods_kind_parunique==null ){
		}else{
			map.put("goods_kind_parunique",goods_kind_parunique);
		}
		if(goodsMessage==null ||"".equals(goodsMessage)){
		}else{
			map.put("goodsMessage",goodsMessage);
		}
		//查询共多少条
		Map<String,Object> countMap=purDao.queryGoodsByPages(map);
		
		List<Map<String,Object>> itemMap=purDao.queryGoodsByPage(map);
		
		Long count=(Long) countMap.get("count");
		pr.setCountNum(count.intValue());
		pr.setCount((int)Math.ceil(1.0*count/pageSize));
		pr.setStatus(1);
		pr.setMsg("查询成功！");
		pr.setData(itemMap);
		return pr;
	}
	public PurResult addAllorationStorage(String shop_unique, String detailJson, String purchase_list_remark,
			String storehouse_id_in,String user_id,String user_name) {
		PurResult pr=new PurResult();
		JSONArray array= JSONArray.fromObject(detailJson);

		if (array.size() == 0){
			pr.setStatus(0);
			pr.setMsg("请选择调拨的商品");
			return pr;
		}

		//获取所有的barcode
		List<String> barcodeList = new ArrayList<>();
		for (int i = 0; i < array.size(); i++) {
			JSONObject temp = (JSONObject) array.get(i);
			barcodeList.add(temp.getString("goods_barcode"));
		}
		Map<String,Object> queryMap = new HashMap<>();
		queryMap.put("shopUnique",shop_unique);
		queryMap.put("goodsBarcodeList",barcodeList);
		List<GoodsInfo> goodsInfos = goodsDao.queryGoodsCountByBarcodeList(queryMap);
		for (int i = 0; i < array.size(); i++) {
			JSONObject temp = (JSONObject) array.get(i);
			double count = temp.getDouble("purchase_list_detail_count");
			GoodsInfo goodsInfo = goodsInfos.stream().filter(g -> g.getGoodsBarcode().equals(temp.getString("goods_barcode"))).findFirst().orElse(null);
			if(ObjectUtils.isEmpty(goodsInfo)){
				pr.setStatus(0);
				pr.setMsg("未获取到商品信息");
				return pr;
			}
			if (new BigDecimal(goodsInfo.getGoodsCount()).compareTo(new BigDecimal(String.valueOf(count))) == -1){
				pr.setStatus(0);
				pr.setMsg(StringUtils.join("\"",goodsInfo.getGoodsName(),"\"库存不足"));
				return pr;
			}
		}


		Map<String,Object> map=new HashMap<String, Object>();
		map.put("purchase_list_remark", purchase_list_remark);
		Long purchase_list_unique = UUIDUtil.createID();
		map.put("purchase_list_unique", purchase_list_unique);//订单编号
		map.put("purchase_list_date", new Date());
		map.put("storehouse_in_id", storehouse_id_in);
		map.put("storehouse_out_id", shop_unique);
		map.put("user_id", user_id);
		map.put("allocation_status",2);
		map.put("user_name",user_name);
		List<Map<String,Object>> goodss=new ArrayList<Map<String,Object>>();
		//添加订单
		purDao.submitAllorationStorage(map);
		for (int i = 0; i < array.size(); i++) {
			JSONObject temp = (JSONObject) array.get(i); 
			String goods_name= temp.getString("goods_name");
			String goods_barcode= temp.getString("goods_barcode");
			String purchase_list_detail_count= temp.getString("purchase_list_detail_count");
			String purchase_list_detail_price= temp.getString("purchase_list_detail_price");
			Map<String,Object> map2=new HashMap<String, Object>();
			Map<String,Object> map3=new HashMap<String, Object>();
			map3.put("goodsBarcode", goods_barcode);
			map3.put("goodsCostPrice", purchase_list_detail_price);
			goodss.add(map3);
			map2.put("goods_barcode", goods_barcode);
			map2.put("purchase_list_detail_count", purchase_list_detail_count);
			//增加调入仓库商品库存
			//查询仓库中是否有这个商品
			Map<String,Object> storehouseMap=new HashMap<String, Object>();
			storehouseMap.put("goods_barcode", goods_barcode);
			storehouseMap.put("storehouse_id_out", shop_unique);
			storehouseMap.put("storehouse_id", storehouse_id_in);
			Map<String,Object> storehouseGodos=purDao.queryStorehouseGoodsExist(storehouseMap);
			//只针对同一调出仓库名称
			if(storehouseGodos!=null&&storehouseGodos.containsKey("goods_in_price")){
				//存在修改数量和进价
				Double price=((BigDecimal)storehouseGodos.get("goods_in_price")).doubleValue();
				Double goods_count=((BigDecimal)storehouseGodos.get("goods_count")).doubleValue();
				Double sumPrice=price*goods_count;
				Double zonghe=Double.parseDouble(purchase_list_detail_price)*Long.parseLong(purchase_list_detail_count);
				Double countSum=goods_count+Double.parseDouble(purchase_list_detail_count);
				Double goods_in_price = 0.0;
				if( countSum == 0) {
					goods_in_price = price;
				}else {
					goods_in_price = BigDecimal.valueOf((sumPrice+zonghe)/countSum).setScale(2,   BigDecimal.ROUND_DOWN).doubleValue();
				}
				storehouseMap.put("goods_count", countSum);
				storehouseMap.put("goods_in_price", goods_in_price);
				purDao.updateStorehouseGoods(storehouseMap);
			}else{
				//不存在新增一条 
				Map<String,Object> newGoods =purDao.queryGoodsDetail(storehouseMap);
				newGoods.put("goods_count", purchase_list_detail_count);
				newGoods.put("shop_unique", storehouse_id_in);
				newGoods.put("goods_in_price", purchase_list_detail_price);
				purDao.addNewGoods(newGoods);
			}
			//减去调出仓库的库存
			Map<String,Object> storehouseOutMap=new HashMap<String, Object>();
			storehouseOutMap.put("goods_barcode", goods_barcode);
			storehouseOutMap.put("storehouse_id", shop_unique);
			Map<String,Object> storehouseOutGoods=purDao.queryStorehouseGoodsExist(storehouseOutMap);
			Double price=((BigDecimal)storehouseOutGoods.get("goods_in_price")).doubleValue();
			Double goods_count=((BigDecimal)storehouseOutGoods.get("goods_count")).doubleValue();
			Double sumPrice=price*goods_count;
			Double zonghe=Double.parseDouble(purchase_list_detail_price)*Long.parseLong(purchase_list_detail_count);
			Double countSum=goods_count-Long.parseLong(purchase_list_detail_count);
//			if(countSum>0){
//				Double goods_in_price= BigDecimal.valueOf((sumPrice-zonghe)/countSum).setScale(2,   BigDecimal.ROUND_DOWN).doubleValue();
//				storehouseOutMap.put("goods_in_price", goods_in_price);
//			}else{
//				storehouseOutMap.put("goods_in_price", 0);
//			}
			storehouseOutMap.put("goods_count", countSum);
			purDao.updateStorehouseGoods(storehouseOutMap);
			//添加订单详情
			map2.put("goods_name", goods_name);
			map2.put("purchase_list_detail_price", purchase_list_detail_price);
			map2.put("purchase_list_unique", purchase_list_unique);
			purDao.addAllorationListDetail(map2);
		}
		
		
		pr.setStatus(1);
		pr.setMsg("提交成功");
		return pr;
	}
	public PurResult queryAllorationDetail(String purchase_list_unique) {
		PurResult pr=new PurResult();
		Map<String,Object> map=new HashMap<String, Object>();
		map.put("purchase_list_unique", purchase_list_unique);
		List<Map<String,Object>> list=purDao.queryAllorationDetail(map);
		pr.setStatus(1);
		pr.setMsg("查询成功！");
		pr.setData(list);
		return pr;
	}
	
	/**
	 * ##################################   商品结构优化    ############################################
	 */
	
	public PurResult getGoodsTopList(Map<String,Object> map) {
		PurResult pr=new PurResult();
		List<Map<String,Object>> list=purDao.getGoodsTopList(map);
		pr.setStatus(1);
		pr.setMsg("查询成功！");
		pr.setData(list);
		return pr;
	}
	
	public PurResult getGoodsNearbyList(Map<String,Object> map) {
		PurResult pr=new PurResult();
		Map<String,Object> shop=purDao.getShopById(map);
		double latitude=new Double(shop.get("shop_latitude").toString());
		double longitude=new Double(shop.get("shop_longitude").toString());
		double distance=new Double(map.get("distance")!=null?map.get("distance").toString():"3");//不传默认方圆三公里范围
		Map<String,Object> point =getRangePosition(longitude,latitude,distance);
		map.putAll(point);
		List<Map<String,Object>> nearbyList=purDao.getGoodsNearbyList(map);
		pr.setStatus(1);
		pr.setMsg("查询成功！");
		pr.setData(nearbyList);
		return pr;
	}
	
	/**
	 * 根据坐标点获取周围四个坐标点（正方形）
	 */
	public Map<String,Object> getRangePosition(double longitude,double latitude,double distance){  
        //先计算查询点的经纬度范围  
        double r = 6371;//地球半径千米  
        
        double dlng =  2*Math.asin(Math.sin(distance/(2*r))/Math.cos(latitude*Math.PI/180));  
        dlng = dlng*180/Math.PI;//角度转为弧度  
        double dlat = distance/r;  
        dlat = dlat*180/Math.PI;          
        double minlat =latitude-dlat;  
        double maxlat = latitude+dlat;  
        double minlng = longitude -dlng;  
        double maxlng = longitude + dlng;  
        Map<String,Object> params=new HashMap<String,Object>();
        params.put("minlat", minlat);
        params.put("maxlat", maxlat);
        params.put("minlng", minlng);
        params.put("maxlng", maxlng);
        return params;
    }
	
	public PurResult getGoodsListNoSale(Map<String,Object> map) {
		PurResult pr=new PurResult();
		List<Map<String,Object>> list=purDao.getGoodsListNoSale(map);
		Integer count = purDao.getGoodsListNoSaleCount(map);
		pr.setStatus(1);
		pr.setMsg("查询成功！");
		pr.setCount(count);
		pr.setData(list);
		return pr;
	}
	
	public PurResult getGoodsTopOrderList(Map<String,Object> map) {
		PurResult pr=new PurResult();
		List<Map<String,Object>> list=purDao.getGoodsTopOrderList(map);
		Integer count = purDao.getGoodsTopOrderListCount(map);
		pr.setStatus(1);
		pr.setMsg("查询成功！");
		pr.setCount(count);
		pr.setData(list);
		return pr;
	}
	
	public PurResult addNoNeedGoods(Map<String,Object> map) {
		PurResult pr=new PurResult();
		int count=purDao.insertNoNeedGoods(map);
		pr.setStatus(1);
		pr.setMsg("操作成功！");
		pr.setCount(count);
		return pr;
	}
	
	public PurResult insertGoods_top_order(Map<String,Object> map) {
		PurResult pr=new PurResult();
		//添加自采购订单
		String self_purchase_unique = createOrderNum();//自采购进货单唯一标示
		//添加进货单信息
		map.put("self_purchase_unique", self_purchase_unique);
		map.put("supplier_unique", "1476781086919");//平台供货商
		int total_count=Integer.valueOf(map.get("goods_count").toString());//商品数量
		Double goods_in_price=Double.valueOf(map.get("goods_in_price").toString());//商品数量
		map.put("total_count", total_count);
		map.put("total_price", goods_in_price*total_count);
		map.put("arrears_price", "0");
		map.put("pay_status", "0");
		map.put("purchase_status", "1");
		map.put("create_time", new Date());
		map.put("update_time", new Date());
		selfPurchaseDao.insertSelfPurchase(map);
		//添加商品详情
		map.put("gift_type", "1");
		List<Map<String ,Object>> detailList=new ArrayList<Map<String ,Object>>();
		detailList.add(map);
		selfPurchaseDao.insertSelfPurchaseDetail(detailList);
		//添加商品
		map.put("goods_unit", map.get("unit_name"));
		map.put("foreign_key", map.get("goods_barcode"));
		purDao.insertGoods(map);
		//添加入库记录
		Map<String,Object> smap=new HashMap<String, Object>();
		smap.put("goods_barcode", map.get("goods_barcode"));
		smap.put("goods_count",map.get("goods_count"));
		smap.put("stock_count",map.get("goods_count"));
		smap.put("stock_type",1);
		smap.put("shop_unique",map.get("shop_unique"));
		smap.put("stock_resource", 3);
		smap.put("stock_price", Double.valueOf(MUtil.strObject(map.get("goods_in_price"))));
		smap.put("stock_origin", 3);
		smap.put("staff_id", map.get("create_staff_id"));
		smap.put("list_unique", self_purchase_unique);
		supplierDao.addShopStock(smap);
		
		//添加支付记录
		Double pay_money = goods_in_price*total_count;
		Map<String ,Object> payParams = new HashMap<String, Object>();
		payParams.put("self_purchase_unique", self_purchase_unique);
		payParams.put("need_topay", map.get("arrears_price"));//剩余未付金额
		payParams.put("pay_money", pay_money);//本次支付金额
		payParams.put("staff_id", map.get("create_staff_id"));//操作员工编号
		payParams.put("source_type", 1);//操作终端：1商家后台 2Android 3ios
		payParams.put("network_ip", map.get("network_ip"));//操作网络ip
		selfPurchaseDao.insertSelfPurchasePay(payParams);
		
		pr.setStatus(1);
		pr.setMsg("操作成功！");
		return pr;
	}
	
	//创建编号
    private static String createOrderNum(){
    	SimpleDateFormat df = new SimpleDateFormat("yyyyMMddHHmmss");//设置日期格式
    	String datestr = df.format(new Date());
    	String sj_num = ""+(int)(1+Math.random()*9)+(int)(1+Math.random()*9)+(int)(1+Math.random()*9)+(int)(1+Math.random()*9);
    	String order_num = "SP" + datestr + sj_num;
    	return order_num;
    }

	/**
	 * 列表返回数据
	 * @param dataObj
	 * @return
	 */
	private List<AllocationShopInfo> handleGoodsList(com.alibaba.fastjson.JSONObject dataObj){
		if (ObjectUtils.isEmpty(dataObj)){
			return new ArrayList<>();
		}

		com.alibaba.fastjson.JSONArray rows = dataObj.getJSONArray("rows");
		if (ObjectUtils.isEmpty(rows)){
			return new ArrayList<>();
		}

		List<AllocationShopInfo> allocationShopInfos = JSON.parseArray(rows.toJSONString(), AllocationShopInfo.class);
		for (AllocationShopInfo s : allocationShopInfos){
			s.setCreateTime(DateUtil.format(s.getPurchaseListDate(),"yyyy-MM-dd HH:mm:ss"));
		}
		return allocationShopInfos;
	}
}
