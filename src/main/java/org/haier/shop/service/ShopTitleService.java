package org.haier.shop.service;

import org.haier.shop.util.ShopsResult;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;

public interface ShopTitleService {
    /**
     * 查询店铺的标题信息
     *
     * @return
     */
    public ShopsResult queryShopTitleList(Integer page, Integer limit, Long shopUnique, Integer validType,String titleName);

    ShopsResult addShopTitle(String titleName, MultipartFile file, Integer modularType, HttpServletRequest request);

    ShopsResult modifyShopTitle(Long id, String titleName, MultipartFile file, Integer modularType, HttpServletRequest request);

    ShopsResult queryShopTitleInfo(Long id, String shopMessage, Integer pageIndex, Integer pageSize);

    ShopsResult authorizeShopTitle(Long id, String shopTitleStr);

    ShopsResult deleteShopTitle(Long id);
}
