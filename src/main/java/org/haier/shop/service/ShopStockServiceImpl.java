package org.haier.shop.service;

import org.haier.shop.dao.ShopStockDetailMapper;
import org.haier.shop.entity.ShopStockDetail;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * @Description 出入库管理
 * @ClassName ShopStockServiceImpl
 * <AUTHOR>
 * @Date 2024/5/1 11:57
 **/
@Service
public class ShopStockServiceImpl implements ShopStockService{

    @Resource
    private ShopStockDetailMapper shopStockDetailMapper;

    @Override
    public ShopStockDetail selectById(Long shopStockDetailId) {
        ShopStockDetail shopStockDetail = shopStockDetailMapper.selectById(shopStockDetailId);
        return shopStockDetail;
    }
}
