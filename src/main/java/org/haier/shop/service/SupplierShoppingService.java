package org.haier.shop.service;

import java.io.IOException;
import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.SortedMap;
import java.util.TreeMap;
import java.util.concurrent.TimeUnit;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;

import org.apache.commons.collections4.Put;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.util.EntityUtils;
import org.apache.shiro.SecurityUtils;
import org.apache.shiro.session.Session;
import org.apache.shiro.subject.Subject;
import org.haier.meituan.util.MUtil;
import org.haier.shop.controller.config.SwiftpassConfig;
import org.haier.shop.dao.GlobalThemeDao;
import org.haier.shop.dao.Goods_kindDao;
import org.haier.shop.dao.LoanDao;
import org.haier.shop.dao.ShopDao;
import org.haier.shop.dao.SupplierShoppingDao;
import org.haier.shop.dao2.ShoppingDao;
import org.haier.shop.dao2.SupplierInfoDao;
import org.haier.shop.entity.SettlementVo;
import org.haier.shop.entity.Staff;
import org.haier.shop.entity.SystemMsgBean;
import org.haier.shop.entity.shop.ShopList;
import org.haier.shop.entity.wj.GoodsSpec;
import org.haier.shop.entity.wj.WJGoods;
import org.haier.shop.entity.wj.WJGoodsKindParent;
import org.haier.shop.redis.RedisCache;
import org.haier.shop.util.HttpsUtil;
import org.haier.shop.util.MD5;
import org.haier.shop.util.MyException;
import org.haier.shop.util.PurResult;
import org.haier.shop.util.PushThread;
import org.haier.shop.util.ShopsResult;
import org.haier.shop.util.SignUtils;
import org.haier.shop.util.TimeDateUtil;
import org.haier.shop.util.UniqueOrder;
import org.haier.shop.util.UtilForJAVA;
import org.haier.shop.util.UtilForRequest;
import org.haier.shop.util.XMLUtils;
import org.haier.shop.util.mqtt.MqttxUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.interceptor.TransactionAspectSupport;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.sun.org.apache.bcel.internal.generic.NEW;


@Service
public class SupplierShoppingService {


    @Resource
    private Goods_kindDao goodsKindDao;
    @Resource
    private ShoppingDao shoppingDao;
    @Resource
    private SupplierShoppingDao supShoppingDao;
    @Resource
    private SupplierInfoDao supplierInfoDao;
    @Resource
    private ShopDao shopDao;
    @Resource
    private GlobalThemeDao globalThemeDao;
    @Resource
    private RedisTemplate<String, Object> redisTemplate;
    @Resource
    private LoanDao loanDao;
    @Autowired
    private RedisCache rc;

    public boolean startOrderLoanMsg(String orderNo) {
        if (null == orderNo || orderNo.equals("")) {
            return false;
        }

        Map<String, Object> map = loanDao.queryOrderLoanMsg(orderNo);
        if (null == map || map.isEmpty()) {
            return false;
        }

        Integer valid_status = Integer.parseInt(map.get("valid_status").toString());
        if (valid_status == 1) {
            return true;
        } else {
            map.put("valid_status", 1);
            Integer time_limit = Integer.parseInt(map.get("time_limit").toString());
            BigDecimal totalMoney = new BigDecimal(map.get("loan_money").toString());
            BigDecimal loan_rate = new BigDecimal(map.get("loan_rate").toString());
            BigDecimal fenqi_money = totalMoney.divide(new BigDecimal(time_limit)).setScale(2, BigDecimal.ROUND_HALF_UP);
            loan_rate = loan_rate.multiply(totalMoney).setScale(2, BigDecimal.ROUND_HALF_UP);

            fenqi_money = fenqi_money.add(loan_rate);
            List<Map<String, Object>> list = new ArrayList<Map<String, Object>>();
            for (Integer i = 1; i <= time_limit; i++) {

                Map<String, Object> tempMap = new HashMap<String, Object>();
                tempMap.put("order_no", orderNo);
                tempMap.put("status", 1);
                tempMap.put("index", i);
                tempMap.put("fenqi_money", fenqi_money);
                tempMap.put("already_money", 0);

                list.add(tempMap);
            }
            loanDao.addNewFenQiLog(list);
            return true;
        }
    }

    /**
     * 根据订单查询订单支付状态
     *
     * @param orderNo
     * @return
     */
    public PurResult queryMainOrderPayStatus(String orderNo) {
        PurResult sr = new PurResult();
        Map<String, Object> map = new HashMap<String, Object>();
        map.put("orderNo", orderNo);
        Integer status = shoppingDao.queryMainOrderPayStatus(map);
        if (status == 2) {
            sr.setStatus(1);
        } else {
            sr.setStatus(0);
        }
        return sr;
    }

    public List<ShopList> downLoadSupOrderExcel(Map<String, Object> map) {
        List<ShopList> list = shoppingDao.getMyOrderListShopDetail(map);
        return list;
    }

    @Transactional
    public ShopsResult modifySupOrderList(Map<String, Object> map, List<Map<String, Object>> list) {
        ShopsResult sr = new ShopsResult(1, "修改成功！");
        Integer c = shoppingDao.modifySupOrderList(map);
        if (c == 1) {
            for (Map<String, Object> temp : list) {
                shoppingDao.modifySupOrderDetail(temp);
            }
        } else {
            throw new MyException(0, "更新主订单错误");
        }
        return sr;
    }

    /**
     * 取消待支付的订单
     *
     * @param shopUnique
     * @param orderCode
     * @return
     */
    public ShopsResult canOrderSettlement(String shopUnique, String orderCode) {
        ShopsResult sr = new ShopsResult(1, "取消成功！");
        Map<String, Object> map = new HashMap<String, Object>();
        map.put("orderCode", orderCode);
        map.put("payStatus", 3);
        map.put("surePayStatus", 2);
        Integer count = shoppingDao.canOrderSettlement(map);
        if (count == 0) {
            sr.setStatus(0);
            sr.setMsg("订单不存在或已支付");
        }
        return sr;
    }

    /**
     * 查询月结定的子订单详情
     *
     * @param order_code
     * @return
     */
    public ShopsResult queryOrderSettlementDetail(String order_code) {
        ShopsResult sr = new ShopsResult(1, "查询成功！");
        Map<String, Object> map = new HashMap<String, Object>();
        map.put("order_code", order_code);
        sr.setData(shoppingDao.queryOrderSettlementDetail(map));
        return sr;
    }

    /**
     * 支付回调，完成主定的支付状态及各个子订单的支付状态
     */
    @Transactional
    public void orderSettlementCallBack(Map<String, Object> params) {
        //更新主订单
        Integer c = shoppingDao.sureOrderSettlement(params);
        System.out.println("更新主订单数量0" + c);
        //查询并更新所有子订单
        if (c > 0) {
            /*
             * 获取所有定的状态，如果为已完成，则增加余额；
             * 如果为待付款，则修改为已付款（待发货）；
             * 否则只修改支付状态
             */
            //查询所有子订单信息
            List<Map<String, Object>> lists = shoppingDao.queryOrderSettlementSubList(params);
            System.out.println("子订单信息");
            System.out.println(lists);
            List<Map<String, Object>> comLists = new ArrayList<Map<String, Object>>();
            List<Map<String, Object>> unpayList = new ArrayList<Map<String, Object>>();
            List<Map<String, Object>> uncomLists = new ArrayList<Map<String, Object>>();
            for (Map<String, Object> tempMap : lists) {
                if (!tempMap.get("pay_status").toString().equals("2")) {
                    String orderStatus = tempMap.get("order_status").toString();
                    tempMap.put("pay_type", 3);
                    if (orderStatus.equals("4")) {
                        comLists.add(tempMap);
                    } else if (orderStatus.equals("0")) {
                        unpayList.add(tempMap);
                    } else {
                        uncomLists.add(tempMap);
                    }
                }
            }
            Map<String, Object> map = new HashMap<String, Object>();
            if (comLists != null && !comLists.isEmpty()) {
                //增加供货商余额，修改订单状态
                map.clear();
                map.put("pay_status", "2");
                map.put("list", comLists);
                map.put("pay_type", params.get("pay_type"));
                System.out.println("已完成订单信息更新====" + map);
                //修改订单状态
                Integer co = shoppingDao.modifyOrderMsg(map);
                System.out.println("更新的数量为" + co);
                //增加店铺余额
                shoppingDao.addCompanyBalance(comLists);

            }
            if (unpayList != null && !unpayList.isEmpty()) {
                //修改订单状态信息和修改订单支付状态
                map.clear();
                map.put("pay_status", "2");
                map.put("order_status", 1);
                map.put("list", unpayList);
                map.put("pay_type", params.get("pay_type"));
                System.out.println("待支付订单信息更新====" + map);
                Integer co = shoppingDao.modifyOrderMsg(map);
                System.out.println("更新的数量为" + co);
            }
            if (uncomLists != null && !uncomLists.isEmpty()) {
                //修改订单支付状态
                map.clear();
                map.put("pay_status", 2);
                map.put("list", uncomLists);
                map.put("pay_type", params.get("pay_type"));
                System.out.println("已发货订单信息更新====" + map);
                Integer co = shoppingDao.modifyOrderMsg(map);
                System.out.println("更新的数量为" + co);
            }

//			shoppingDao.sureSupOrderLists(params);
        }

    }

    /**
     * 创建主订单和子订单信息
     *
     * @param shopUnique
     * @param orderList
     * @param totalMoney
     * @return
     */
    @Transactional
    public ShopsResult addNewOrderSettlement(String shopUnique, String orderList, Double totalMoney) {
        ShopsResult sr = new ShopsResult(1, "添加成功");
        Map<String, Object> map = new HashMap<String, Object>();
        map.put("customer_code", shopUnique);
        map.put("pay_status", 1);
        map.put("order_total", totalMoney);
        String order_code = "OS_" + System.nanoTime();
        map.put("order_code", order_code);
        //存储主订单

        shoppingDao.addNewOrderSettlement(map);
        //存储子订单
        List<Map<String, Object>> list = new ArrayList<Map<String, Object>>();
        String[] orders = orderList.split(",");
        for (String order : orders) {
            Map<String, Object> tempMap = new HashMap<String, Object>();
            tempMap.put("main_order_code", order_code);
            tempMap.put("order_code", order);

            list.add(tempMap);
        }
        shoppingDao.addNewOrderSettlementDetail(list);

        sr.setData(map);
        return sr;
    }

    /**
     * 查询指定店铺，指定供货商，指定时间内的未结算订单信息
     *
     * @param shopUnique
     * @param company_code
     * @param startTime
     * @param endTime
     * @return
     */
    public ShopsResult querySupOrderListMsg(String shopUnique, String company_code, String startTime, String endTime) {
        ShopsResult sr = new ShopsResult(1, "查询成功！");
        Map<String, Object> map = new HashMap<String, Object>();
        map.put("shopUnique", shopUnique);
        map.put("company_code", company_code);
        map.put("startTime", startTime);
        map.put("endTime", endTime);

        sr.setData(shoppingDao.querySupOrderListMsg(map));

        return sr;
    }

    /**
     * 查询商家的结算记录
     *
     * @param shopUnique   商家编号
     * @param company_code 供货商编号：-1为全部
     * @param pay_status   订单状态：-1全部；1:待支付；2、已支付；3、已取消；
     * @param limit        当前查询的页码
     * @param pageSize     单页查询数量
     * @return
     */
    public ShopsResult queryOrderSettlementList(String shopUnique, String company_code, Integer pay_status, Integer page, Integer pageSize, String startTime, String endTime) {
        ShopsResult sr = new ShopsResult(1, "查询成功！");
        Map<String, Object> map = new HashMap<String, Object>();
        map.put("shopUnique", shopUnique);
        map.put("company_code", company_code);
        map.put("pay_status", pay_status);
        map.put("startNum", (page - 1) * pageSize);
        map.put("pageSize", pageSize);
        map.put("startTime", startTime);
        map.put("endTime", endTime);
        sr.setData(shoppingDao.queryOrderSettlementList(map));
        sr.setCount(shoppingDao.queryOrderSettlementListCount(map));
        return sr;
    }

    /**
     * 查询指定商家的所有为结算供货订单的
     *
     * @param shopUnique
     * @return
     */
    public ShopsResult queryOrderSupplierMsg(String shopUnique) {
        ShopsResult sr = new ShopsResult(1, "查询成功！");
        Map<String, Object> params = new HashMap<String, Object>();
        params.put("customer_code", shopUnique);
        sr.setData(shoppingDao.queryOrderSupplierMsg(params));
        return sr;
    }

    @Transactional
    public ShopsResult modifyShopGold(String shopUnique, String goldGrantId) {
        ShopsResult sr = new ShopsResult();
        Map<String, Object> map = new HashMap<String, Object>();
        map.put("shopUnique", shopUnique);
        map.put("goldGrantId", goldGrantId);

        //查询领取状态，防止重复领取
        Map<String, Object> jqbMap = shopDao.queryGoldGrant(map);
        if (null == jqbMap || !jqbMap.get("receive_status").toString().equals("1")) {
            sr.setStatus(0);
            sr.setMsg("该优惠已领取或已过期");
            return sr;
        }

        //修改为已领取状态
        map.put("receiveStatus", 2);
        shopDao.grantGoldById(map);
        //需要增加的金圈币数量
        map.put("jqbCount", jqbMap.get("jqb_count"));

        //查询店铺金圈币信息，如果没有则新增，如果有则更新
        Integer c = shopDao.modifyShopGold(map);
        if (c == 0) {
            shopDao.addNewShopGold(map);
        }
        sr.setStatus(1);
        sr.setMsg("领取成功");
        sr.setData(shopDao.queryShopJQB(map));
        return sr;
    }

    public ShopsResult queryShopJQB(String shopUnique) {
        ShopsResult sr = new ShopsResult();
        Map<String, Object> map = new HashMap<>();
        map.put("shopUnique", shopUnique);
        //获取店铺的金圈币信息
        Double jqbCount = shopDao.queryShopJQB(map);
        if (null == jqbCount) {
            jqbCount = 0.0;
        }
        //获取未领取的金圈币信息
        Map<String, Object> jqbMap = shopDao.queryGoldGrant(map);
        sr.setData(jqbCount);
        sr.setCord(jqbMap);
        sr.setStatus(1);
        sr.setMsg("查询成功！");
        return sr;
    }

    /**
     * 获取五金店铺的分类信息
     *
     * @param area_dict_num
     * @return
     */
    public List<WJGoodsKindParent> getWJGoodskindList(String area_dict_num) {
        Map<String, Object> map = new HashMap<String, Object>();
        map.put("area_dict_num", area_dict_num);
        List<WJGoodsKindParent> list = shoppingDao.getWJGoodskindList(map);
        return list;
    }

    public List<Map<String, Object>> getGoodskindList(String area_dict_num, String shopUnique) {
//		Jedis jedis = RedisUtil.getJedis();
        List<Map<String, Object>> goodkindList = new ArrayList<Map<String, Object>>();
/*		if(redisTemplate.hasKey("goodkindList")){
			String jsonstr=(String) redisTemplate.opsForValue().get("goodkindList");
			goodkindList=JSON.parseObject(jsonstr, new TypeReference<List<Map<String, Object>>>() {});
		}else {
			goodkindList=goodsKindDao.getGoodskindList();
			for(Map<String ,Object> map:goodkindList) {
				//List<Map<String ,Object>> goodkindTwo=goodsKindDao.getGoodskindTwo(map);
				List<Map<String ,Object>> goodkindTwo=shoppingDao.getGoodskindTwo(map);
				map.put("goodkindTwo",goodkindTwo);
			}
//			jedis.set("goodkindList", JSON.toJSONString(goodkindList));
			redisTemplate.opsForValue().set("goodkindList", JSON.toJSONString(goodkindList));
		}*/
        Map<String, Object> params = new HashMap<String, Object>();
        params.put("shopUnique", shopUnique == null ? "0" : shopUnique);
        goodkindList = goodsKindDao.getGoodskindList(params);
//		goodkindList=shoppingDao.getGoodskind_new(params);//0421改为供货商城分类

        List<Map<String, Object>> goodkindTwo = shoppingDao.getGoodskindTwo_new();
        for (Map<String, Object> map : goodkindList) {
            map.put("area_dict_num", area_dict_num);
            map.put("shopUnique", shopUnique == null ? "0" : shopUnique);
            //List<Map<String ,Object>> goodkindTwo=goodsKindDao.getGoodskindTwo(map);
            //List<Map<String ,Object>> goodkindTwo=shoppingDao.getGoodskindTwo(map);
            List<Map<String, Object>> goodkindTwo2 = new ArrayList<Map<String, Object>>();
            for (Map<String, Object> map2 : goodkindTwo) {

                if (map2.get("goodsclass_parentid").toString().equals(map.get("goods_kind_unique").toString())) {
                    goodkindTwo2.add(map2);
                }
            }
            map.put("goodkindTwo", goodkindTwo2);
        }
        return goodkindList;
    }

    public List<Map<String, Object>> getGoodskindList_food(String area_dict_num, String shopUnique) {
//		Jedis jedis = RedisUtil.getJedis();
        List<Map<String, Object>> goodkindList = new ArrayList<Map<String, Object>>();
/*		if(redisTemplate.hasKey("goodkindList")){
			String jsonstr=(String) redisTemplate.opsForValue().get("goodkindList");
			goodkindList=JSON.parseObject(jsonstr, new TypeReference<List<Map<String, Object>>>() {});
		}else {
			goodkindList=goodsKindDao.getGoodskindList();
			for(Map<String ,Object> map:goodkindList) {
				//List<Map<String ,Object>> goodkindTwo=goodsKindDao.getGoodskindTwo(map);
				List<Map<String ,Object>> goodkindTwo=shoppingDao.getGoodskindTwo(map);
				map.put("goodkindTwo",goodkindTwo);
			}
//			jedis.set("goodkindList", JSON.toJSONString(goodkindList));
			redisTemplate.opsForValue().set("goodkindList", JSON.toJSONString(goodkindList));
		}*/
        Map<String, Object> params = new HashMap<String, Object>();
        params.put("shopUnique", shopUnique == null ? "0" : shopUnique);
        goodkindList = goodsKindDao.getGoodskindList_food(params);
        for (Map<String, Object> map : goodkindList) {
            map.put("area_dict_num", area_dict_num);
            map.put("shopUnique", shopUnique == null ? "0" : shopUnique);
            //List<Map<String ,Object>> goodkindTwo=goodsKindDao.getGoodskindTwo(map);
            List<Map<String, Object>> goodkindTwo = shoppingDao.getGoodskindTwo_food(map);
            map.put("goodkindTwo", goodkindTwo);
        }
        return goodkindList;
    }

    public WJGoods getWJGoodsSpecList(Map<String, Object> params) {
        WJGoods g = shoppingDao.getWJGoodSpecList(params);
        if (g.getGoods_img1().indexOf("../") == -1 && g.getGoods_img1().indexOf("http") == -1) {
            g.setGoods_img1("http://file.buyhoo.cc/" + g.getGoods_img1());
        }
        if (g.getGoods_img2().indexOf("../") == -1 && g.getGoods_img2().indexOf("http") == -1) {
            g.setGoods_img2("http://file.buyhoo.cc/" + g.getGoods_img2());
        }
        if (g.getGoods_img3().indexOf("../") == -1 && g.getGoods_img3().indexOf("http") == -1) {
            g.setGoods_img3("http://file.buyhoo.cc/" + g.getGoods_img3());
        }
        if (g.getGoods_img4().indexOf("../") == -1 && g.getGoods_img4().indexOf("http") == -1) {
            g.setGoods_img4("http://file.buyhoo.cc/" + g.getGoods_img4());
        }
        List<GoodsSpec> list = g.getList();

        for (GoodsSpec gs : list) {
            if (null == gs.getSpec_img() || gs.getSpec_img().equals("")) {
                gs.setSpec_img("../static/img/no_goods.jpg");
            }
            if (gs.getSpec_img().indexOf("../") == -1 && gs.getSpec_img().indexOf("http") == -1) {
                gs.setSpec_img("http://file.buyhoo.cc" + gs.getSpec_img());
            }
        }
        return g;
    }

    public List<Map<String, Object>> getGoodSpecList(Map<String, Object> params) {

        return shoppingDao.getGoodSpecList(params);
    }

    public String getGoodDetail(Map<String, Object> params) {

        return shoppingDao.getGoodDetail(params);
    }

    public List<Map<String, Object>> getGoodBindDetail(Map<String, Object> params) {
        List<Map<String, Object>> list = shoppingDao.getGoodBindDetail(params);
        for (Map<String, Object> goodMap : list) {
            if ("".equals(goodMap.get("goods_img"))) {
                goodMap.put("goods_img", "../static/img/no_goods.jpg");
            } else if (goodMap.get("goods_img").toString().indexOf("http") == -1) {
                goodMap.put("goods_img", "http://file.buyhoo.cc/" + goodMap.get("goods_img").toString());
            }
        }
        return list;
    }

    public List<Map<String, Object>> getCoupon(Map<String, Object> params) {
        List<Map<String, Object>> list = shoppingDao.getCoupon(params);
        //System.out.println(list);
        List<Map<String, Object>> noList = new ArrayList<Map<String, Object>>();
        List<Map<String, Object>> yList = new ArrayList<Map<String, Object>>();
        for (Map<String, Object> map : list) {
            if (map.containsKey("record_id")) {
                int recordId = Integer.parseInt(map.get("record_id").toString());
                if (recordId == -1) {
                    noList.add(map);
                } else {
                    yList.add(map);
                }
            }
        }
        int yRecord = Integer.parseInt(params.get("yRecord").toString());
        if (params.get("notre").toString().equals("0")) {
            if (yRecord == -1) {//未领取
                return noList;
            } else if (yRecord == 1) {//已领取
                if (params.containsKey("allCou")) {
                    if (params.get("allCou").toString().equals("1")) {
                        //平台优惠券
                        List<Map<String, Object>> adminList = shoppingDao.getAdminCoupon(params);
                        for (Map<String, Object> adMap : adminList) {
                            //筛选出未用过的平台优惠券
                            Map<String, Object> reMap = new HashMap<String, Object>();
                            reMap.put("shop_unique", params.get("shop_unique").toString());
                            reMap.put("coupon_id", adMap.get("coupon_id").toString());
                            reMap.put("company_code", "GS371300001");
                            List<Map<String, Object>> recCoupon = shoppingDao.getUsedCoupon(reMap);
                            if (recCoupon.size() == 0) {
                                yList.add(adMap);
                            }
                        }
                    }
                }

                return yList;
            } else if (yRecord == 0) {//全部
                if (params.get("company_code").toString().equals("GS371300001")) {
                    //平台优惠券
                    List<Map<String, Object>> adminList = shoppingDao.getAdminCoupon(params);
                    for (Map<String, Object> adMap : adminList) {
                        //筛选出未用过的平台优惠券
                        Map<String, Object> reMap = new HashMap<String, Object>();
                        reMap.put("shop_unique", params.get("shop_unique").toString());
                        reMap.put("coupon_id", adMap.get("coupon_id").toString());
                        reMap.put("company_code", "GS371300001");
                        List<Map<String, Object>> recCoupon = shoppingDao.getUsedCoupon(reMap);
                        if (recCoupon.size() == 0) {
                            yList.add(adMap);
                        }
                    }
                }
                return yList;
            } else {
                return list;
            }
        } else {
            return list;
        }

    }

    public List<Map<String, Object>> getFullgift(Map<String, Object> params) {
        List<Map<String, Object>> list = shoppingDao.getFullgift(params);
        System.out.println(list);
        for (Map<String, Object> goodMap : list) {

            List<Map<String, Object>> giftGoodsList = shoppingDao.getGiftGoodsList(goodMap.get("gift_id").toString());

            for (Map<String, Object> giftGoodMap : giftGoodsList) {
                if ("".equals(giftGoodMap.get("goods_img"))) {
                    giftGoodMap.put("goods_img", "../static/img/no_goods.jpg");
                } else if (giftGoodMap.get("goods_img").toString().indexOf("http") == -1) {
                    giftGoodMap.put("goods_img", "http://file.buyhoo.cc/" + giftGoodMap.get("goods_img").toString());
                }
            }

            goodMap.put("giftGoods", giftGoodsList);

            List<Map<String, Object>> giftCouponList = shoppingDao.getGiftCouponList(goodMap.get("gift_id").toString());
            for (Map<String, Object> giftCouponMap : giftCouponList) {
                giftCouponMap.put("coupon_img", "../static/img/coupon.jpg");
            }
            goodMap.put("giftCoupon", giftCouponList);

        }
        //System.out.println(list);
        return list;
    }

    public int queryShoppingCartCount(Map<String, Object> params) {
        return shoppingDao.queryShoppingCartCount(params);
    }

    public PurResult getGoodListWJ(Map<String, Object> map) {
        PurResult pr = new PurResult(1, "查询成功！");
        //获取当前时间为截止时间，转换为long型
        long startTime = fromDateStringToLong(new SimpleDateFormat("yyyy-MM-dd hh:mm:ss:SSS").format(new Date()));
        List<WJGoods> list = shoppingDao.getGoodListWJ(map);
        //获取当前时间为开始时间，转换为long型
        long stopTime = fromDateStringToLong(new SimpleDateFormat("yyyy-MM-dd hh:mm:ss:SSS").format(new Date()));
        //计算时间差,单位毫秒
        long timeSpan = stopTime - startTime;
        System.out.println("商城商品查询执行时间（毫秒）：" + timeSpan);
        for (WJGoods goods : list) {
            if ("".equals(goods.getGoods_img())) {
                goods.setGoods_img("../static/img/no_goods.jpg");
            } else if (goods.getGoods_img().indexOf("http") == -1) {
                goods.setGoods_img("http://file.buyhoo.cc/" + goods.getGoods_img());
            }
            if ("".equals(goods.getGoods_img1())) {
                goods.setGoods_img1("../static/img/no_goods.jpg");
            } else if (goods.getGoods_img1().indexOf("http") == -1) {
                goods.setGoods_img1("http://file.buyhoo.cc/" + goods.getGoods_img1());
            }
            if ("".equals(goods.getGoods_img2())) {
                goods.setGoods_img2("../static/img/no_goods.jpg");
            } else if (goods.getGoods_img2().indexOf("http") == -1) {
                goods.setGoods_img2("http://file.buyhoo.cc/" + goods.getGoods_img2());
            }
            if ("".equals(goods.getGoods_img3())) {
                goods.setGoods_img3("../static/img/no_goods.jpg");
            } else if (goods.getGoods_img3().indexOf("http") == -1) {
                goods.setGoods_img3("http://file.buyhoo.cc/" + goods.getGoods_img3());
            }
            if ("".equals(goods.getGoods_img4())) {
                goods.setGoods_img4("../static/img/no_goods.jpg");
            } else if (goods.getGoods_img4().indexOf("http") == -1) {
                goods.setGoods_img4("http://file.buyhoo.cc/" + goods.getGoods_img4());
            }
        }
        pr.setCount(shoppingDao.getGoodListWJCount(map));
        pr.setData(list);
        int cartCount = shoppingDao.queryShoppingCartCount(map);
        pr.setCord(cartCount);

        return pr;
    }

    public static long fromDateStringToLong(String inVal) {
        Date date = null;
        SimpleDateFormat inputFormat = new SimpleDateFormat("yyyy-MM-dd hh:mm:ss:SSS");
        try {
            date = inputFormat.parse(inVal);

        } catch (Exception e) {
            e.printStackTrace();
        }
        return date.getTime();
    }

    public PurResult getGoodList(Map<String, Object> map) {
        PurResult result = new PurResult();
        try {
            List<Map<String, Object>> list = shoppingDao.getGoodList(map);
            for (Map<String, Object> goodMap : list) {
                if ("".equals(goodMap.get("goods_img"))) {
                    goodMap.put("goods_img", "../static/img/no_goods.jpg");
                } else if (goodMap.get("goods_img").toString().indexOf("http") == -1) {
                    goodMap.put("goods_img", "http://file.buyhoo.cc/" + goodMap.get("goods_img").toString());
                }
            }
            Integer count = shoppingDao.getGoodListCount(map);
            int cartCount = shoppingDao.queryShoppingCartCount(map);
            map.put("usage_status", '0');
            map.put("notre", '1');
            map.put("yRecord", "1");
            int couponCount = shoppingDao.queryShoppingCouponCount(map);//供应商优惠券
            //平台优惠券数量
            List<Map<String, Object>> countList = new ArrayList<>();
            List<Map<String, Object>> adminList = shoppingDao.getAdminCoupon(map);
            if (adminList.size() != 0) {
                for (Map<String, Object> adMap : adminList) {
                    //筛选出未用过的平台优惠券
                    Map<String, Object> reMap = new HashMap<String, Object>();
                    reMap.put("shop_unique", map.get("shop_unique").toString());
                    reMap.put("coupon_id", adMap.get("coupon_id").toString());
                    reMap.put("company_code", "GS371300001");
                    List<Map<String, Object>> recCoupon = shoppingDao.getUsedCoupon(reMap);
                    if (recCoupon.size() == 0) {
                        countList.add(adMap);
                    }
                }
            }
            int adminCouNum = countList.size();
            couponCount = couponCount + adminCouNum;
            result.setStatus(1);
            result.setMsg("成功");
            result.setCount(count);
            result.setData(list);
            result.setCord(cartCount);
            result.setCountNum(couponCount);
        } catch (Exception e) {
            e.printStackTrace();
            result.setStatus(0);
            result.setMsg("异常");
        }
        return result;
    }

    public PurResult queryGoldUserList(Map<String, Object> map) {
        PurResult result = new PurResult();
        try {
            List<Map<String, Object>> list = shoppingDao.queryGoldUserList(map);
            Integer count = shoppingDao.queryGoldUserCount(map);
            result.setStatus(1);
            result.setMsg("成功");
            result.setCount(count);
            result.setData(list);
        } catch (Exception e) {
            e.printStackTrace();
            result.setStatus(0);
            result.setMsg("异常");
        }
        return result;
    }

    public PurResult getMyOrderList(Map<String, Object> map) {
        PurResult result = new PurResult();
        try {
            List<Map<String, Object>> list = shoppingDao.getMyOrderList(map);
            Integer count = shoppingDao.getMyOrderListCount(map);
            result.setStatus(1);
            result.setMsg("成功");
            result.setCount(count);
            result.setData(list);
        } catch (Exception e) {
            e.printStackTrace();
            result.setStatus(0);
            result.setMsg("异常");
        }
        return result;
    }

    public PurResult queryGoldRewardList(Map<String, Object> map) {
        PurResult result = new PurResult();
        try {
            List<Map<String, Object>> list = supShoppingDao.queryGoldRewardDetail(map);
            Integer count = supShoppingDao.queryGoldRewardDetailCount(map);
            result.setStatus(1);
            result.setMsg("成功");
            result.setCount(count);
            result.setData(list);
        } catch (Exception e) {
            e.printStackTrace();
            result.setStatus(0);
            result.setMsg("异常");
        }
        return result;
    }

    @Transactional
    public PurResult insertShoppingCart(Map<String, Object> map) {
        PurResult result = new PurResult();
        try {
            //判断是不是学校
            Subject subject = SecurityUtils.getSubject();
            Session session = subject.getSession();
            Staff staff = (Staff) session.getAttribute("staff");
            Integer shop_type = staff.getShop_type();
            if (shop_type == 9) {
                map.put("create_time", new Date());
                shoppingDao.insertShoppingCart(map);
                result.setStatus(1);
                result.setMsg("成功");
            } else {
                //查询捆绑商品并添加到购物车
                String good_id = map.get("good_id").toString();
                String goods_name = map.get("goods_name").toString();
                String shop_unique = map.get("shop_unique").toString();
                String spec_id = map.get("spec_id").toString();
                String spec_name = map.get("spec_name").toString();
                String good_count = map.get("good_count").toString();
                String company_code = map.get("company_code").toString();
                String cycle = map.get("cycle").toString();
                String frequency = map.get("frequency").toString();
                String quotaNumber = "";

                Map<String, Object> mp = new HashMap<String, Object>();
                mp.put("good_id", good_id);
                List<Map<String, Object>> spol = shoppingDao.getBindingGoodsList(mp);

                //存在捆绑商品则插入数据库，否则执行原操作插入
                if (spol.isEmpty()) {
                    //判断限购周期与限购次数
//						if(!cycle.equals("") && !frequency.equals("")) {
//							Map<String,Object> oc = new HashMap<String,Object>();
//							oc.put("good_id", good_id);
//					    	oc.put("shop_unique", shop_unique);
//					    	oc.put("cycle", cycle);
//					    	oc.put("frequency", frequency);
//							PurResult result1 = cycleFrequency(oc);
//							if(result1.getStatus() == 0) {
//								result.setStatus(result1.getStatus());
//								result.setMsg("商品("+goods_name+")"+result1.getMsg());
//								return result;
//							}
//						}
                    map.put("create_time", new Date());

                    //判断商品库存是否足够
                    map.put("goods_id", map.get("good_id"));
                    List<Map<String, Object>> spList = getGoodSpecList(map);


                    for (Map<String, Object> m : spList) {
                        Integer stock_count = Integer.parseInt(m.get("available_stock_count").toString());
                        Integer auto_fxiaoshou = Integer.parseInt(m.get("auto_fxiaoshou").toString());
                        Integer car_count = Integer.parseInt(m.get("goods_count").toString());
                        Integer cart_good_count = Integer.parseInt(m.get("cart_good_count").toString());
                        if (auto_fxiaoshou != 1) {
                            if (Double.parseDouble(good_count) + cart_good_count > car_count) {
                                result.setStatus(0);
                                result.setMsg(goods_name + "购买数量不能超过库存数量！");
                                return result;
                            }
                        }
                    }


                    int in = shoppingDao.insertShoppingCart(map);
                    System.out.println(in);
                } else {
                    for (Map<String, Object> map1 : spol) {
                        String gId = map1.get("good_id").toString();
                        goods_name = map1.get("goods_name").toString();
                        cycle = map1.get("cycle").toString();
                        frequency = map1.get("frequency").toString();
                        quotaNumber = map1.get("quotaNumber").toString();
                        Integer good_count1 = Integer.valueOf(map1.get("good_count").toString());
                        Integer auto_fxiaoshou = Integer.valueOf(map1.get("auto_fxiaoshou").toString());

                        //规格库存
                        List<Map<String, Object>> spList = getGoodSpecList(map1);
                        for (Map<String, Object> m : spList) {
                            Integer stock_count = (Integer) m.get("available_stock_count");
                            if (auto_fxiaoshou != 1) {
                                if (good_count1 > stock_count) {
                                    result.setStatus(0);
                                    result.setMsg(goods_name + "购买数量不能超过库存数量！");
                                    return result;
                                }
                            }
                        }
                        //判断限购数量
//				    	if(!quotaNumber.equals("")) {
//				    		if(good_count1 > (Integer.valueOf(quotaNumber))) {
//					    		result.setStatus(0);
//								result.setMsg(goods_name+"购买数量不能超过限购数量！");
//								return result;
//					    	}
//				    	}
                        //判断限购周期与限购次数
//						if(!cycle.equals("") && !frequency.equals("")) {
//							Map<String,Object> oc = new HashMap<String,Object>();
//							oc.put("good_id", gId);
//					    	oc.put("shop_unique", shop_unique);
//					    	oc.put("cycle", cycle);
//					    	oc.put("frequency", frequency);
//							PurResult result1 = cycleFrequency(oc);
//							if(result1.getStatus() == 0) {
//								result.setStatus(result1.getStatus());
//								result.setMsg("捆绑商品("+goods_name+")"+result1.getMsg());
//								return result;
//							}
//						}
                        if (gId.equals(good_id)) {
                            map.put("create_time", new Date());
                            shoppingDao.insertShoppingCart(map);
                        } else {
                            map1.put("shop_unique", shop_unique);
                            map1.put("create_time", new Date());
                            shoppingDao.insertShoppingCart(map1);
                        }
                    }
                }
                result.setStatus(1);
                result.setMsg("成功");
            }

        } catch (Exception e) {
            e.printStackTrace();
            result.setStatus(0);
            result.setMsg("异常");
        }
        return result;
    }

    public int updateShoppingOrder(Map<String, Object> params) {
        //检查是否有待生效的赊销信息，如果有，修改为生效状态
//		String main_order_no = params.get("main_order_no").toString();
//		Map<String,Object> loanMap = loanDao.querySXOrderMsg(main_order_no);
//		if(null != loanMap && !loanMap.isEmpty()) {
//			loanDao.makeEffectiveSXOrder(main_order_no);
//			loanDao.addSXNotReturnMoney(loanMap);
//
//			//是否添加详情，如果下单添加详情，容易造成垃圾数据，如果此处添加详情，数据不好获取，担心下单时的规则发生变化。
//		}
        //添加业务员关联信息
        Map<String, Object> main = shoppingDao.queryMainOrderInfo(params);
        if (main != null) {
            UtilForRequest.sendMsgToAgency(main.get("customer_code").toString(), main.get("main_order_no").toString(), main.get("order_amt").toString(),
                    main.get("collect_name").toString(), main.get("user_id") == null ? null : main.get("user_id").toString());
        }
        //依次向所有的供货商发送消息
        try {
            List<Map<String, Object>> list = shoppingDao.queryOrderListByOrderNo(params);
            for (Integer i = 0; i < list.size(); i++) {
                Map<String, Object> mqttMap = list.get(i);
                mqttMap.put("ctrl", "msg_yun_print");
                MqttxUtil.sendMapMsg(mqttMap, mqttMap.get("ID").toString());

                //判断是否有预售商品，
                Integer specialType = Integer.parseInt(list.get(i).get("specialType").toString());
                if (specialType == 1) {
                    //预售订单，查询订单的商品详情，并增加对应的商品预订了
                    Map<String, Object> map = new HashMap<String, Object>();
                    map.put("orderCode", list.get(i).get("order_code"));
                    List<Map<String, Object>> goodsList = shoppingDao.queryOrderDetailForBook(map);
                    if (null != goodsList && !goodsList.isEmpty()) {
                        shoppingDao.updateActivityMsg(goodsList);
                    }
                }
            }

        } catch (Exception e) {
            e.printStackTrace();
        }

        //更新主订单的支付信息，方便退款
        shoppingDao.updateMainOrder(params);
        return shoppingDao.updateShoppingOrder(params);
    }

    public void updateShoppingOrderWJ(Map<String, Object> params) {
        shoppingDao.updateShoppingOrderWJ(params);
        shoppingDao.updateShoppingOrderWJ2(params);
    }

    public PurResult updateShoppingLoanCart(Integer id, Integer loan_count, Integer type) {
        PurResult result = new PurResult(1, "更新成功！");
        Map<String, Object> map = new HashMap<String, Object>();
        map.put("id", id);
        map.put("loan_count", loan_count);


        if (loan_count > 0 && type == 1) {
            map.put("good_count_add", loan_count);
        }
        if (loan_count < 0 && type == 1) {
            //查询购物车商品数量，防止负数
            Map<String, Object> nowCountMap = shoppingDao.queryShoppingCartById(id);
            Double a = loan_count + 0.0;
            System.out.println(Math.abs(a));
            Double b = new Double(nowCountMap.get("loan_count").toString());
            if (b.compareTo(Math.abs(a)) < 0) {
                result.setStatus(0);
                result.setMsg("数量不可为负数，请刷新页面重试");
                return result;
            }
            map.put("good_count_sub", -loan_count);
        }
        try {
            shoppingDao.update_shopping_cart(map);
        } catch (Exception e) {
            result.setStatus(0);
            result.setMsg("失败！");
        }

        return result;
    }

    public PurResult updateShoppingCart(Map<String, Object> map) {
        PurResult result = new PurResult();
        try {
            if (map.get("good_count_sub") != null) {

            }
            //如果是增加数量，验证购物车商品库存信息

            //判断商品库存是否足够
            map.put("goods_id", shoppingDao.queryCartGoodsIdById(map.get("id")));
            List<Map<String, Object>> spList = getGoodSpecList(map);

            if (null != map.get("good_count_add") && !map.get("good_count_add").toString().trim().equals("")) {

                String good_count = map.get("good_count_add").toString();

                for (Map<String, Object> m : spList) {
                    Integer stock_count = Integer.parseInt(m.get("available_stock_count").toString());
                    Integer auto_fxiaoshou = Integer.parseInt(m.get("auto_fxiaoshou").toString());
                    if (auto_fxiaoshou != 1) {
                        if (Double.parseDouble(good_count) > stock_count) {
                            result.setStatus(0);
                            result.setMsg("购买数量不能超过库存数量！");
                            return result;
                        }
                    }
                }
            }

            shoppingDao.update_shopping_cart(map);
            result.setStatus(1);
            result.setMsg("成功");
        } catch (Exception e) {
            e.printStackTrace();
            result.setStatus(0);
            result.setMsg("异常");
        }
        return result;
    }

    public PurResult deleteShoppingCart(Map<String, Object> map) {
        PurResult result = new PurResult();
        try {
            shoppingDao.delete_shopping_cart(map);
            result.setStatus(1);
            result.setMsg("成功");
        } catch (Exception e) {
            e.printStackTrace();
            result.setStatus(0);
            result.setMsg("异常");
        }
        return result;
    }

    /**
     * 捆绑删除
     *
     * @param map
     * @return
     */
//	public PurResult deleteBindShoppingCart(Map<String,Object> map){
//		PurResult result = new PurResult();
//		try {
//			shoppingDao.delete_bind_shopping_cart(map);
//	    	result.setStatus(1);
//			result.setMsg("成功");
//		}catch (Exception e) {
//			e.printStackTrace();
//			result.setStatus(0);
//			result.setMsg("异常");
//		}
//		return result;
//	}
    public PurResult deleteShoppingCartMore(String[] ids) {
        PurResult result = new PurResult();
        try {
            shoppingDao.delete_shopping_cart_more(ids);
            result.setStatus(1);
            result.setMsg("成功");
        } catch (Exception e) {
            e.printStackTrace();
            result.setStatus(0);
            result.setMsg("异常");
        }
        return result;
    }

    public List<Map<String, Object>> queryShoppingCart(Map<String, Object> params) {
        List<Map<String, Object>> list = shoppingDao.queryShoppingCart(params);
        for (Map<String, Object> goodMap : list) {
            if ("".equals(goodMap.get("goods_img"))) {
                goodMap.put("goods_img", "../static/img/no_goods.jpg");
            } else if (goodMap.get("goods_img").toString().indexOf("http") == -1) {
                goodMap.put("goods_img", "http://file.buyhoo.cc/" + goodMap.get("goods_img").toString());
            }
        }
        return list;
    }

    public Map<String, Object> queryGoldConfig(String deduction) {
        return supShoppingDao.queryGoldConfig("deduction");
    }

    /**
     * 五金店铺，购物车结算
     *
     * @param ids         提交结算的购物车详情ID
     * @param shop_unique 结算的店铺编号
     */
    public SettlementVo querySettlementListWJ(String[] ids, String shop_unique) {
        SettlementVo settle = new SettlementVo();
        BigDecimal sum_amt_all = new BigDecimal("0");
        BigDecimal deduct_amt_all = new BigDecimal("0");
        BigDecimal sum_delivery_price = new BigDecimal("0");
        Integer sum_count = 0;

        //查询所有购物车商品信息
        List<Map<String, Object>> list = shoppingDao.querySupplierByCartWJforSettle(ids);
        for (Map<String, Object> map : list) {
            map.put("ids", ids);
            List<Map<String, Object>> good_list = shoppingDao.querySupplierGoodListWJForSettle(map);
            //商品总金额
            for (Map<String, Object> goodsMap : good_list) {
                if ("".equals(goodsMap.get("spec_img").toString())) {
                    goodsMap.put("spec_img", "../static/img/no_goods.jpg");
                } else if (!goodsMap.get("spec_img").toString().startsWith("../") && !goodsMap.get("spec_img").toString().startsWith("http")) {
                    goodsMap.put("spec_img", "http://file.buyhoo.cc/" + goodsMap.get("spec_img").toString());
                }
            }
            map.put("goods_list", good_list);
            sum_amt_all = sum_amt_all.add(new BigDecimal(map.get("sum_amt").toString()));
            sum_count += Integer.valueOf(map.get("sum_good_count").toString());
        }
        settle.setDeduct_amt_all(deduct_amt_all);
        settle.setJqb_count(new BigDecimal("0"));
        settle.setJqb_max_count(new BigDecimal("0"));
        settle.setSettlementList(list);
        settle.setShould_amt_all(sum_amt_all);
        settle.setSum_amt_all(sum_amt_all);
        settle.setSum_count(sum_count);
        settle.setSum_delivery_price(sum_delivery_price);
        return settle;
    }

    public SettlementVo querySettlementList(String[] ids, String shop_unique, String area_dict_num) {
        SettlementVo settle = new SettlementVo();
        Map<String, Object> paramsShop = new HashMap<String, Object>();
        paramsShop.put("shop_unique", shop_unique);
        Map<String, Object> goldMap = supShoppingDao.queryGoldByShop(paramsShop);//店铺金圈币可用数量
        if (goldMap != null) {
            settle.setJqb_count(new BigDecimal(goldMap.get("jqb_count").toString()));
        }
        BigDecimal sum_amt_all = new BigDecimal("0");
        BigDecimal should_amt_all = new BigDecimal("0");
        BigDecimal deduct_amt_all = new BigDecimal("0");
        BigDecimal sum_delivery_price = new BigDecimal("0");
        BigDecimal loan_amt_all = new BigDecimal("0");
        Integer sum_count = 0;
        BigDecimal temp = settle.getJqb_count();//可用金圈币数量
        List<Map<String, Object>> list = shoppingDao.querySupplierByCart(ids, shop_unique);
        for (Map<String, Object> map : list) {
            map.put("area_dict_num", area_dict_num);
            //配送费
            Map<String, Object> deliveryPrice = shoppingDao.getDeliveryPrice(map);
            map.put("free_delivery_price", deliveryPrice.get("free_delivery_price").toString());//订单免配送费价格
            BigDecimal delivery_price = new BigDecimal("0");
            if ("2".equals(deliveryPrice.get("delivery_price_type").toString())) {//按订单配送
                delivery_price = new BigDecimal(deliveryPrice.get("delivery_price").toString());
            }
            List<Map<String, Object>> good_list = shoppingDao.querySupplierGoodList(map.get("company_code").toString(), area_dict_num, ids, shop_unique);
            for (Map<String, Object> goodMap : good_list) {
                if ("".equals(goodMap.get("goods_img"))) {
                    goodMap.put("goods_img", "../static/img/no_goods.jpg");
                } else if (goodMap.get("goods_img").toString().indexOf("http") == -1) {
                    goodMap.put("goods_img", "http://file.buyhoo.cc/" + goodMap.get("goods_img").toString());
                }
                if ("1".equals(deliveryPrice.get("delivery_price_type").toString())) {//按件数
                    delivery_price = delivery_price.add(new BigDecimal(goodMap.get("delivery_price").toString()).multiply(new BigDecimal(goodMap.get("good_count").toString())));
                }

            }
            map.put("good_list", good_list);
            map.put("delivery_price", delivery_price);//总配送费
            //商品总金额
            BigDecimal sum_amt = new BigDecimal(map.get("sum_amt").toString());
            //贷款总金额
            BigDecimal loan_amt = new BigDecimal(map.get("loan_amt").toString());
            //抵扣金额
            BigDecimal deduct_amt = new BigDecimal(map.get("sum_deduct_amt").toString());
            if (temp.compareTo(deduct_amt) > 0) {
                temp = temp.subtract(deduct_amt);
            } else {
                deduct_amt = temp;
                temp = new BigDecimal("0");
            }
            //应付金额
            BigDecimal should_amt = sum_amt.subtract(deduct_amt);
//			if(should_amt.compareTo(new BigDecimal(0)) <= 0) {
//				sum_amt = new BigDecimal(0);
//			}else {
//				sum_amt = should_amt;
//			}
//			map.put("sum_amt", sum_amt);
            BigDecimal actual_delivery_price = should_amt.compareTo(new BigDecimal(deliveryPrice.get("free_delivery_price").toString())) >= 0 ? new BigDecimal("0") : delivery_price;//实际配送费
            should_amt = should_amt.add(actual_delivery_price);

            //如果优惠金额大于实际需要支付的金额，减少赊销金额
            if (should_amt.compareTo(new BigDecimal(0)) < 0) {
                loan_amt = loan_amt.add(should_amt);
                should_amt = should_amt.subtract(should_amt);
                map.put("loan_amt", loan_amt);
            }

            map.put("deduct_amt", deduct_amt);
            map.put("should_amt", should_amt);
            sum_amt_all = sum_amt_all.add(sum_amt);
            should_amt_all = should_amt_all.add(should_amt);
            deduct_amt_all = deduct_amt_all.add(deduct_amt);
            sum_count += Integer.valueOf(map.get("sum_good_count").toString());

            map.put("actual_delivery_price", actual_delivery_price);
            sum_delivery_price = sum_delivery_price.add(actual_delivery_price);
            loan_amt_all = loan_amt_all.add(loan_amt);


            //供应商优惠券
            Map<String, Object> reMap = new HashMap<String, Object>();
            reMap.put("company_code", map.get("company_code").toString());
            reMap.put("shop_unique", shop_unique);
            reMap.put("usage_status", "0");
            reMap.put("overdue_status", "1");
            reMap.put("yRecord", "1");
            reMap.put("notre", "0");
            List<Map<String, Object>> recCoupon = getCoupon(reMap);
            if (recCoupon.size() == 0) {
                map.put("coupon_amount", new BigDecimal(0));//优惠金额
            } else {
                List<Map<String, Object>> Maxcoupon = new ArrayList<Map<String, Object>>();
                for (Map<String, Object> co : recCoupon) {
                    BigDecimal meetAmount = new BigDecimal(co.get("meet_amount").toString());
                    if (sum_amt.compareTo(meetAmount) >= 0) {
                        Maxcoupon.add(co);
                    }
                }
                //遍历获取符合优惠券需求的最大一条记录
                if (Maxcoupon.size() != 0) {
                    Map<String, Object> maxcou = Maxcoupon.get(0);
                    BigDecimal MaxcouAmount = new BigDecimal(Maxcoupon.get(0).get("coupon_amount").toString());
                    for (int i = 0; i < Maxcoupon.size(); i++) {
                        BigDecimal couAmount = new BigDecimal(Maxcoupon.get(i).get("coupon_amount").toString());
                        if (couAmount.compareTo(MaxcouAmount) >= 0) {
                            MaxcouAmount = couAmount;
                            maxcou = Maxcoupon.get(i);
                        }
                    }
                    map.put("coupon_amount", new BigDecimal(maxcou.get("coupon_amount").toString()));//优惠金额
                    map.put("coupon_id", maxcou.get("coupon_id").toString());//优惠id
                    map.put("record_id", maxcou.get("record_id").toString());//领取id
                } else {
                    map.put("coupon_amount", new BigDecimal(0));//优惠金额
                }
            }

            //满赠
            Map<String, Object> fullMap = new HashMap<String, Object>();
            fullMap.put("company_code", map.get("company_code").toString());
            List<Map<String, Object>> fullgift = getFullgift(fullMap);
            List<Map<String, Object>> allFullgift = new ArrayList<Map<String, Object>>();
            //获取所有符合的满赠
            for (Map<String, Object> gift : fullgift) {
                String meet_amount = gift.get("meet_amount").toString();
                BigDecimal meetAmount = new BigDecimal(meet_amount);
                if (sum_amt.compareTo(meetAmount) >= 0) {
                    allFullgift.add(gift);
                }
            }
            //遍历获取符合满赠需求的最大一条记录
            Map<String, Object> maxGift = new HashMap<String, Object>();
            if (allFullgift.size() != 0) {
                BigDecimal MaxAmount = new BigDecimal(allFullgift.get(0).get("meet_amount").toString());
                for (int i = 0; i < allFullgift.size(); i++) {
                    BigDecimal meetAmount = new BigDecimal(allFullgift.get(i).get("meet_amount").toString());
                    if (meetAmount.compareTo(MaxAmount) >= 0) {
                        MaxAmount = meetAmount;
                        maxGift = allFullgift.get(i);
                    }
                }
            }
            List<Map<String, Object>> maxFullgift = new ArrayList<Map<String, Object>>();
            maxFullgift.add(maxGift);
            map.put("fullgiftList", maxFullgift);//满赠
        }

        //跨店优惠券
        Map<String, Object> adMap = new HashMap<String, Object>();
        adMap.put("shop_unique", shop_unique);
        adMap.put("company_code", "GS371300001");
        adMap.put("usage_status", "0");
        adMap.put("overdue_status", "1");
        adMap.put("yRecord", "0");
        adMap.put("notre", "0");
        List<Map<String, Object>> adCoupon = getCoupon(adMap);
        if (adCoupon.size() == 0) {
            settle.setAdmin_coupon(new BigDecimal(0));//优惠金额
            settle.setAdminCouponId("");//优惠id
            //settle.setAdminRecordId("");//领取id
        } else {
            List<Map<String, Object>> MaxcouponAll = new ArrayList<Map<String, Object>>();
            for (Map<String, Object> co : adCoupon) {
                //筛选出未用过的平台优惠券
                Map<String, Object> reMap = new HashMap<String, Object>();
                reMap.put("shop_unique", shop_unique);
                reMap.put("coupon_id", co.get("coupon_id").toString());
                reMap.put("company_code", "GS371300001");
                List<Map<String, Object>> recCoupon = shoppingDao.getUsedCoupon(reMap);
                if (recCoupon.size() == 0) {
                    //未使用过
                    BigDecimal meetAmount = new BigDecimal(co.get("meet_amount").toString());
                    int limit_quantity_type = Integer.parseInt(co.get("limit_quantity_type").toString());
                    int total_surplus = Integer.parseInt(co.get("total_surplus").toString());
                    if (sum_amt_all.compareTo(meetAmount) >= 0) {
                        //判断限制数量的优惠券
                        if (limit_quantity_type == 2) {
                            //剩余数量不为零的
                            if (total_surplus > 0) {
                                MaxcouponAll.add(co);
                            }
                        } else {
                            //不限制数量的
                            MaxcouponAll.add(co);
                        }
                    }
                }

            }
            //遍历获取符合优惠券需求的最大一条记录
            if (MaxcouponAll.size() != 0) {
                Map<String, Object> maxcouAll = MaxcouponAll.get(0);
                BigDecimal MaxcouAmountAll = new BigDecimal(MaxcouponAll.get(0).get("coupon_amount").toString());
                for (int i = 0; i < MaxcouponAll.size(); i++) {
                    BigDecimal couAmount = new BigDecimal(MaxcouponAll.get(i).get("coupon_amount").toString());
                    if (couAmount.compareTo(MaxcouAmountAll) >= 0) {
                        MaxcouAmountAll = couAmount;
                        maxcouAll = MaxcouponAll.get(i);
                    }
                }
                settle.setAdmin_coupon(new BigDecimal(maxcouAll.get("coupon_amount").toString()));//优惠金额
                settle.setAdminCouponId(maxcouAll.get("coupon_id").toString());//优惠id
//				System.out.println("AdminCoupon:"+maxcouAll.get("coupon_id").toString());
                //settle.setAdminRecordId(maxcouAll.get("record_id").toString());//领取id
            } else {
                settle.setAdmin_coupon(new BigDecimal(0));//优惠金额
                settle.setAdminCouponId("");//优惠id
                //settle.setAdminRecordId("");//领取id
            }
        }

        settle.setDeduct_amt_all(deduct_amt_all);
        settle.setLoan_amt_all(loan_amt_all);
        settle.setSum_amt_all(sum_amt_all);
        settle.setSum_count(sum_count);
        settle.setSum_delivery_price(sum_delivery_price);
        settle.setSettlementList(list);
        settle.setJqb_max_count(settle.getJqb_count().compareTo(deduct_amt_all) >= 0 ? deduct_amt_all : settle.getJqb_count());//本次可抵扣金圈币个数
        settle.setShould_amt_all(should_amt_all);
        return settle;
    }

    @Transactional
    /**
     *
     * @param ids 提交的商品详情在购物车的ID
     * @param shop_unique 店铺编号
     * @param oper_id 供货商名称
     * @param gold_amt 金圈币使用总额
     * @param order_amt 订单总金额
     * @param actual_amt 实际支付总金额
     * @param delivery_price 总的配送费
     * @param order_remark 订单备注
     * @param delivery_fee 订单配送费列表
     * @return
     */
    public PurResult saveOrderWJ(String[] ids, String shop_unique, String oper_id, String gold_amt, String order_amt,
                                 String actual_amt, String delivery_price, String[] order_remark, String[] delivery_fee) {
        PurResult result = new PurResult();
        //获取店铺信息
        Map<String, Object> paramsShop = new HashMap<String, Object>();
        paramsShop.put("shop_unique", shop_unique);
        Map<String, Object> shopInfo = shopDao.queryShopMessage(paramsShop);
        String orderid = "T" + new UniqueOrder(0, 0).nextId();
        List<Map<String, Object>> list = shoppingDao.querySupplierByCartWJforSettle(ids);

        int i = 0;
        int j = 0;
        for (Map<String, Object> map : list) {
            Map<String, Object> tempMap = new HashMap<String, Object>();
            tempMap.put("company_code", map.get("company_code").toString());
            tempMap.put("ids", ids);
            List<Map<String, Object>> good_list = shoppingDao.querySupplierGoodListWJForSettle(tempMap);
            BigDecimal sum_amt = new BigDecimal(map.get("sum_amt").toString());//子订单售价
            BigDecimal purchase_cut = new BigDecimal(map.get("purchase_cut").toString());//五金分润
            BigDecimal cost_amt = new BigDecimal(map.get("cost_amt").toString());//子订单成本价
            Map<String, Object> paramsOrder = new HashMap<String, Object>();
            String order_code = SupplierInfoServiceImpl.createOrderNum();
            paramsOrder.put("order_code", order_code);
            paramsOrder.put("company_code", map.get("company_code"));
            if (order_remark.length > i) {
                paramsOrder.put("order_remarks", order_remark[i]);
                i++;
            } else {
                paramsOrder.put("order_remarks", "");
            }
            //paramsOrder.put("pay_mode", 1);//支付方式：1、货到付款；2、支付宝；3、微信;4、其他
            paramsOrder.put("pay_status", 1);//支付状态：1、欠款；2、已结清
            paramsOrder.put("order_status", "0");//订单状态  0待付款
            if (new BigDecimal(actual_amt).compareTo(new BigDecimal("0")) == 0) {
                paramsOrder.put("pay_status", 2);
                paramsOrder.put("order_status", "1");
                sendMsg(order_code);
            }
            paramsOrder.put("customer_code", shop_unique);//客户编号
            paramsOrder.put("collect_name", MUtil.strObject(shopInfo.get("shop_name")));//收货人姓名
            paramsOrder.put("collect_phone", MUtil.strObject(shopInfo.get("shop_phone")));//联系电话
            paramsOrder.put("collect_address", MUtil.strObject(shopInfo.get("shop_address_detail")));//收货地址
            paramsOrder.put("order_type", 1);//订单类型 0：自动下单；1：客户下单
            paramsOrder.put("order_money", sum_amt);//订单总金额
            paramsOrder.put("shop_latitude", MUtil.strObject(shopInfo.get("shop_latitude")));//店铺纬度
            paramsOrder.put("shop_longitude", MUtil.strObject(shopInfo.get("shop_longitude")));//店铺经度
            paramsOrder.put("present_beans", "0");//满足规则赠送百货豆
            paramsOrder.put("platform_beans", "0");//实际赠送的百货豆
            paramsOrder.put("main_order_no", orderid);
            paramsOrder.put("delivery_fee", delivery_fee[j]);//配送费
            paramsOrder.put("cost_fee", cost_amt);//成本价
            paramsOrder.put("purchase_cut", purchase_cut);//20201112 新增五金总分润
            paramsOrder.put("pay_money", new BigDecimal(delivery_fee[j]).add(cost_amt));//成本价+配送费
            //如果计算每个订单的实际金圈币抵扣金额
            paramsOrder.put("sum_deduct_amt", map.get("sum_deduct_amt"));
            //新增订单
            supplierInfoDao.insertOrder(paramsOrder);
            j++;
            //添加订单详情表
            Map<String, Object> paramsOrderdetail = new HashMap<String, Object>();
            paramsOrderdetail.put("order_code", order_code);

            //添加商品详情(购物车表当做商品详情表)
            for (Map<String, Object> cart : good_list) {
                cart.put("is_order", 1);
                cart.put("order_no", orderid);
                shoppingDao.update_shopping_cart(cart);
                paramsOrderdetail.put("goods_code", cart.get("goods_barcode"));
                paramsOrderdetail.put("goods_cost", cart.get("supply_price"));
                paramsOrderdetail.put("goods_price", cart.get("online_price"));
                paramsOrderdetail.put("real_price", cart.get("online_price"));
                paramsOrderdetail.put("goods_name", cart.get("goods_name"));
                paramsOrderdetail.put("goods_unit", cart.get("goodsunit_name"));
                paramsOrderdetail.put("goods_id", cart.get("good_id"));
                paramsOrderdetail.put("fact_price", cart.get("sum_amt"));
                paramsOrderdetail.put("goods_count", cart.get("good_count"));
                paramsOrderdetail.put("auto_fxiaoshou", cart.get("auto_fxiaoshou"));
                supplierInfoDao.insertOrderDetail(paramsOrderdetail);
                Integer orderdetail_id = Integer.parseInt(MUtil.strObject(paramsOrderdetail.get("orderdetail_id")));

                //添加订单详情规则表
                Map<String, Object> paramsOrderSpec = new HashMap<String, Object>();
                paramsOrderSpec.put("spec_name", cart.get("spec_name"));
                paramsOrderSpec.put("orderdetail_id", orderdetail_id);
                paramsOrderSpec.put("compose_specs_id", cart.get("spec_id"));
                paramsOrderSpec.put("goods_count", cart.get("good_count"));
                paramsOrderSpec.put("price", cart.get("online_price"));
                supplierInfoDao.insertOrderGoodsSpec(paramsOrderSpec);
            }

        }
        //添加主订单
        paramsShop.put("main_order_no", orderid);
        paramsShop.put("gold_amt", gold_amt);
        paramsShop.put("order_amt", order_amt);
        paramsShop.put("actual_amt", actual_amt);
        paramsShop.put("delivery_price", delivery_price);
        paramsShop.put("oper_id", oper_id);
        paramsShop.put("shop_unique", shop_unique);
        shoppingDao.insertOrderMain(paramsShop);
        paramsShop.put("total_fee", new BigDecimal(actual_amt).multiply(new BigDecimal("100")));
        result.setStatus(1);
        result.setData(paramsShop);
        result.setMsg("提交订单成功！");
        return result;
    }

    /**
     * @param ids            提交的商品列表
     * @param shop_unique    店铺编号
     * @param oper_id        操作员信息
     * @param gold_amt       金圈比总额
     * @param order_amt      订单总额
     * @param actual_amt     实际支付总额
     * @param delivery_price 总运费
     * @param order_remark   订单备注
     * @param delivery_fee   运费明细
     * @param loan_all_amt   赊销总额（本金，不含手续费）
     * @param sxRuleId       赊销规则ID
     * @return
     */
    @Transactional
    public PurResult saveOrder(String[] ids, String shop_unique, String oper_id, String gold_amt, String order_amt,
                               String actual_amt, String delivery_price, String[] order_remark, String[] delivery_fee, String loan_all_amt,
                               Integer sxRuleId, String[] recordId, String[] giftId, String[] giftCouponId, String AdminCouponId) {
        PurResult result = new PurResult();

        Object o = rc.getObject("initial_delivery_fee");
        Double fee = 0.0;
        if (null == o) {
            fee = shoppingDao.querySystemConfig();
        } else {
            fee = Double.parseDouble(o.toString());
        }

        if (fee.compareTo(Double.parseDouble(loan_all_amt) + Double.parseDouble(actual_amt)) > 0) {
            result.setStatus(0);
            result.setMsg("订单最小起订金额不能小于" + fee + "元");
            return result;
        }

        //获取店铺信息
        Map<String, Object> paramsShop = new HashMap<String, Object>();
        paramsShop.put("shop_unique", shop_unique);
        Map<String, Object> shopInfo = shopDao.queryShopMessage(paramsShop);
        Double loanMoney = Double.parseDouble(shopInfo.get("loanMoney").toString());
        if (loan_all_amt != null && !loan_all_amt.equals("") && loanMoney.compareTo(0.0) > 0 && loanMoney < Double.parseDouble(loan_all_amt)) {
            result.setStatus(0);
            result.setMsg("可用借款额度不足，请刷新页面获取借款额度");
            return result;
        }
        String orderid = "T" + new UniqueOrder(0, 0).nextId();
        List<Map<String, Object>> list = shoppingDao.querySupplierByCart(ids, shop_unique);
        Map<String, Object> goldMap = supShoppingDao.queryGoldByShop(paramsShop);//店铺金圈币可用数量
        BigDecimal temp = new BigDecimal("0");
        if (goldMap != null) {
            temp = new BigDecimal(goldMap.get("jqb_count").toString());//可用金圈币数量
        }
        BigDecimal deduct_amt_all = new BigDecimal("0");
        for (Map<String, Object> map : list) {
            //金圈币抵扣金额
            BigDecimal deduct_amt = new BigDecimal(map.get("sum_deduct_amt").toString());
            if (temp.compareTo(deduct_amt) > 0) {
                temp = temp.subtract(deduct_amt);
            } else {
                deduct_amt = temp;
                temp = new BigDecimal("0");
            }
            deduct_amt_all = deduct_amt_all.add(deduct_amt);
        }
        int i = 0;
        int j = 0;
        for (Map<String, Object> map : list) {

            List<Map<String, Object>> good_list = shoppingDao.querySupplierGoodList2(map.get("company_code").toString(), ids, shop_unique);
            Integer is_order = 0;
            for (Map<String, Object> tempMap : good_list) {
                is_order = Integer.parseInt(tempMap.get("is_order").toString());
                if (is_order == 1) {
                    result.setStatus(0);
                    result.setMsg("商品" + tempMap.get("goods_name").toString() + "已提交订单，请重新下单");
                    return result;
                }
            }
        }


        List<String> orderCodeList = new ArrayList<String>();

        for (Map<String, Object> map : list) {

            List<Map<String, Object>> good_list = shoppingDao.querySupplierGoodList2(map.get("company_code").toString(), ids, shop_unique);

            for (Map<String, Object> gMap : good_list) {
                //验证商品库存是否发生变化，防止商品已被抢购

                //判断商品库存是否足够
                gMap.put("shop_unique", shop_unique);

                String good_count = gMap.get("good_count").toString();
                String goods_name = gMap.get("goods_name").toString();
                String stock_count = gMap.get("stock_count").toString();
                Integer auto_fxiaoshou = Integer.parseInt(gMap.get("auto_fxiaoshou").toString());

                if (auto_fxiaoshou == 2 && (Double.parseDouble(good_count) - Double.parseDouble(stock_count) > 0)) {
                    throw new RuntimeException(goods_name + "采购量大于现有库存，可采购数量" + stock_count);
                }
            }
            //减掉库存
            for (Map<String, Object> goods : good_list) {
                Integer auto_fxiaoshou = Integer.parseInt(goods.get("auto_fxiaoshou").toString());
                if (auto_fxiaoshou == 2) {
                    shoppingDao.updateGoodsCount(goods);
                }
            }


            BigDecimal sum_amt = new BigDecimal(map.get("sum_amt").toString());//子订单售价
//			BigDecimal loan_amt = new BigDecimal(map.get("loan_amt").toString());//子订单赊销金额
            BigDecimal loan_amt = new BigDecimal(map.get("sum_amt").toString());//子订单赊销金额为子订单售价
            BigDecimal cost_amt = new BigDecimal(map.get("cost_amt").toString());//子订单成本价
            Map<String, Object> paramsOrder = new HashMap<String, Object>();
            String order_code = SupplierInfoServiceImpl.createOrderNum();

            orderCodeList.add(order_code);

            paramsOrder.put("order_code", order_code);
            paramsOrder.put("company_code", map.get("company_code"));
            if (order_remark.length > i) {
                paramsOrder.put("order_remarks", order_remark[i]);
                i++;
            } else {
                paramsOrder.put("order_remarks", "");
            }
            //paramsOrder.put("pay_mode", 1);//支付方式：1、货到付款；2、支付宝；3、微信;4、其他
            paramsOrder.put("pay_status", 1);//支付状态：1、欠款；2、已结清
            paramsOrder.put("order_status", "0");//订单状态  0待付款
            if (new BigDecimal(actual_amt).compareTo(new BigDecimal("0")) == 0) {
                paramsOrder.put("pay_status", 2);
                paramsOrder.put("order_status", "1");
                sendMsg(order_code);
            }
            paramsOrder.put("customer_code", shop_unique);//客户编号
            paramsOrder.put("collect_name", MUtil.strObject(shopInfo.get("shop_name")));//收货人姓名
            paramsOrder.put("collect_phone", MUtil.strObject(shopInfo.get("shop_phone")));//联系电话
            paramsOrder.put("collect_address", MUtil.strObject(shopInfo.get("shop_address_detail")));//收货地址
            paramsOrder.put("order_type", 1);//订单类型 0：自动下单；1：客户下单
            paramsOrder.put("order_money", sum_amt);//订单总金额


            paramsOrder.put("loan_money", loan_amt);
            paramsOrder.put("shop_latitude", MUtil.strObject(shopInfo.get("shop_latitude")));//店铺纬度
            paramsOrder.put("shop_longitude", MUtil.strObject(shopInfo.get("shop_longitude")));//店铺经度
            paramsOrder.put("present_beans", "0");//满足规则赠送百货豆
            paramsOrder.put("platform_beans", "0");//实际赠送的百货豆
            paramsOrder.put("main_order_no", orderid);
            paramsOrder.put("delivery_fee", delivery_fee[j]);//配送费
            paramsOrder.put("cost_fee", cost_amt);//成本价
            paramsOrder.put("pay_money", new BigDecimal(delivery_fee[j]).add(cost_amt));//成本价+配送费
            //如果计算每个订单的实际金圈币抵扣金额
            if (Double.parseDouble(gold_amt) < deduct_amt_all.doubleValue()) {
                BigDecimal deduct_amt = new BigDecimal(map.get("sum_deduct_amt").toString());
                double shiji_deduct_amt = Double.parseDouble(gold_amt) * (deduct_amt.doubleValue() / deduct_amt_all.doubleValue());
                paramsOrder.put("sum_deduct_amt", BigDecimal.valueOf(shiji_deduct_amt).setScale(2, BigDecimal.ROUND_HALF_UP));
            } else {
                if (Double.parseDouble(gold_amt) <= 0) {
                    paramsOrder.put("sum_deduct_amt", 0);
                } else {
                    paramsOrder.put("sum_deduct_amt", map.get("sum_deduct_amt"));
                }
            }

            //查询优惠券金额
            BigDecimal couponRe = new BigDecimal(0);
            if (recordId != null) {
                for (int r = 0; r < recordId.length; r++) {
                    Map<String, Object> mapReid = new HashMap<String, Object>();
                    mapReid.put("recordId", recordId[r]);
                    Map<String, Object> reID = shoppingDao.getOneCoupon(mapReid);
                    if (shop_unique.equals(reID.get("staffer_login").toString()) && map.get("company_code").toString().equals(reID.get("compay_code").toString())) {
                        couponRe = new BigDecimal(reID.get("coupon_amount").toString());
                    } else {
                        couponRe = new BigDecimal(0);
                    }

                }
            }


            //商品借款额度需要查看优惠信息，如果金圈币抵扣金额大于运费和非借款额度总和，则减少借款金额，优先使用金圈币
            BigDecimal sum_deduct_amt = new BigDecimal(paramsOrder.get("sum_deduct_amt").toString());
            BigDecimal realPayMoney = sum_amt.add(new BigDecimal(delivery_fee[j]).subtract(sum_deduct_amt));
            if (realPayMoney.compareTo(new BigDecimal(0)) < 0) {
                //实际支付的金额加上运费，扣除优惠金额小于0，则应减少赊销金额
                paramsOrder.put("loan_money", loan_amt.add(sum_amt).subtract(sum_deduct_amt).add(new BigDecimal(delivery_fee[j])).setScale(2, BigDecimal.ROUND_HALF_UP));
                //计算实际支付金额
                paramsOrder.put("real_pay_money", 0);
            } else {
                paramsOrder.put("real_pay_money", realPayMoney);
            }

            supplierInfoDao.insertOrder(paramsOrder);
            j++;
            //添加订单详情表
            Map<String, Object> paramsOrderdetail = new HashMap<String, Object>();
            paramsOrderdetail.put("order_code", order_code);


            //把赠送商品添加到订单详情中
            if (giftId != null) {
                for (int k = 0; k < giftId.length; k++) {
                    Map<String, Object> resmap = new HashMap<String, Object>();
                    resmap.put("giftId", giftId[k]);
                    List<Map<String, Object>> fullgiftList = shoppingDao.getFullgift(resmap);
                    for (Map<String, Object> full : fullgiftList) {
                        String companyCode = full.get("compay_code").toString();
                        if (companyCode.equals(map.get("company_code").toString())) {
                            String gift_id = full.get("gift_id").toString();
                            List<Map<String, Object>> fullGoods = shoppingDao.getGiftGoodsList(gift_id);
                            for (Map<String, Object> go : fullGoods) {
                                go.put("goods_barcode", go.get("goods_barcode").toString());
                                go.put("supply_price", '0');
                                go.put("online_price", '0');
                                go.put("goods_name", go.get("goods_name").toString());
                                go.put("goodsunit_name", go.get("goodsunit_name").toString());
                                go.put("good_id", go.get("goods_id").toString());
                                go.put("sum_amt", '0');
                                go.put("good_count", go.get("gfree_quantity").toString());
                                go.put("auto_fxiaoshou", go.get("auto_fxiaoshou").toString());
                                go.put("goods_label", '4');
                                go.put("promotion_id", "-1");
                                go.put("loan_count", null);
                                go.put("loan_cut", null);
                                go.put("spec_id", go.get("spec_id").toString());
                                go.put("spec_name", go.get("spec_name").toString());
                                go.put("company_code", map.get("company_code").toString());
                                go.put("shop_unique", shop_unique);//用户
                                shoppingDao.insertShoppingCart(go);//插入到购物车表
                                Integer caid = Integer.parseInt(MUtil.strObject(go.get("id")));
                                go.put("id", caid);
                                good_list.add(go);
                            }
                        }
                    }
                }

            }


            //添加商品详情(购物车表当做商品详情表)
            for (Map<String, Object> cart : good_list) {
                Map<String, Object> tempMap = new HashMap<String, Object>();

                tempMap.put("is_order", 1);
                tempMap.put("order_no", orderid);
                tempMap.put("id", cart.get("id"));
                shoppingDao.update_shopping_cart(tempMap);

                paramsOrderdetail.put("goods_code", cart.get("goods_barcode"));
                paramsOrderdetail.put("goods_cost", cart.get("supply_price"));
                paramsOrderdetail.put("goods_price", cart.get("online_price"));
                paramsOrderdetail.put("real_price", cart.get("online_price"));
                paramsOrderdetail.put("goods_name", cart.get("goods_name"));
                paramsOrderdetail.put("goods_unit", cart.get("goodsunit_name"));
                paramsOrderdetail.put("goods_id", cart.get("good_id"));
                paramsOrderdetail.put("fact_price", cart.get("sum_amt"));
                paramsOrderdetail.put("goods_count", cart.get("good_count"));
                paramsOrderdetail.put("auto_fxiaoshou", cart.get("auto_fxiaoshou"));
                paramsOrderdetail.put("promotion_id", cart.get("promotion_id"));
                paramsOrderdetail.put("loan_count", cart.get("loan_count") == null ? 0 : cart.get("loan_count"));
                paramsOrderdetail.put("loan_cut", cart.get("loan_cut") == null ? 0 : cart.get("loan_cut"));
                if (cart.containsKey("goods_label")) {
                    paramsOrderdetail.put("goods_label", cart.get("goods_label"));
                }
                supplierInfoDao.insertOrderDetail(paramsOrderdetail);
                Integer orderdetail_id = Integer.parseInt(MUtil.strObject(paramsOrderdetail.get("orderdetail_id")));

                //添加订单详情规则表
                Map<String, Object> paramsOrderSpec = new HashMap<String, Object>();
                paramsOrderSpec.put("spec_name", cart.get("spec_name"));
                paramsOrderSpec.put("orderdetail_id", orderdetail_id);
                paramsOrderSpec.put("compose_specs_id", cart.get("spec_id"));
                supplierInfoDao.insertOrderGoodsSpec(paramsOrderSpec);
            }
        }
        //添加主订单
        paramsShop.put("main_order_no", orderid);
        paramsShop.put("gold_amt", gold_amt);
        paramsShop.put("order_amt", order_amt);
        paramsShop.put("loan_amt", loan_all_amt);
        paramsShop.put("actual_amt", actual_amt);
        paramsShop.put("delivery_price", delivery_price);
        paramsShop.put("oper_id", oper_id);
        paramsShop.put("shop_unique", shop_unique);
        shoppingDao.insertOrderMain(paramsShop);
        paramsShop.put("total_fee", new BigDecimal(actual_amt).multiply(new BigDecimal("100")));


        //优惠券使用
        if (recordId != null) {
            Map<String, Object> upMap = new HashMap<String, Object>();
            upMap.put("recordId", recordId);
            upMap.put("orderid", orderid);
            shoppingDao.updateRecStatus(upMap);
        }

        //跨店优惠券使用
        if (!AdminCouponId.equals("") && !AdminCouponId.equals(null)) {
            Map<String, Object> upMap = new HashMap<String, Object>();
            upMap.put("shopUnique", shop_unique);
            upMap.put("couponId", AdminCouponId);
            upMap.put("usageStatus", '1');
            upMap.put("orderid", orderid);
            upMap.put("remark", "自动领取使用");
            //限制数量
            Map<String, Object> cou = shoppingDao.getOneCoupon(upMap);
            Integer limit_quantity_type = Integer.valueOf(cou.get("limit_quantity_type").toString());
            Integer total_distribution = Integer.valueOf(cou.get("total_distribution").toString());
            String couponId = cou.get("coupon_id").toString();
            Map<String, Object> re = new HashMap<String, Object>();
            re.put("coupon_id", couponId);
            re.put("couponId", couponId);
            if (limit_quantity_type == 2) {
                int reCount = shoppingDao.queryShoppingCouponCount(re);
                if (reCount >= total_distribution) {
                    result.setStatus(0);
                    result.setMsg("优惠券已领完！");
                } else {
                    int ad = shoppingDao.addRecStatus(upMap);
                    if (ad > 0) {
                        List<Map<String, Object>> uplist = new ArrayList<Map<String, Object>>();
                        uplist.add(re);
                        shoppingDao.upCoupon(uplist);
                    }
                }
            } else {
                shoppingDao.addRecStatus(upMap);
            }
        }


        //满赠
        //插入中间表信息
//		System.out.println("giftId.length:");
//
//		System.out.println(giftId);
        if (giftId != null) {
            List<Map<String, Object>> resource = new ArrayList<Map<String, Object>>();
            for (int k = 0; k < giftId.length; k++) {
                Map<String, Object> resmap = new HashMap<String, Object>();
                resmap.put("orderCode", orderid);//总订单
                resmap.put("giftId", giftId[k]);
                resource.add(resmap);
            }
            shoppingDao.insertGiftOrder(resource);
        }

        //插入赠送优惠券
        if (giftCouponId != null) {
            if (giftId != null) {
                for (int g = 0; g < giftId.length; g++) {
                    String gift_id = giftId[g];
                    List<Map<String, Object>> gc = shoppingDao.getGiftCouponList(gift_id);
                    for (Map<String, Object> fgc : gc) {
                        for (int k = 0; k < giftCouponId.length; k++) {
                            String couponid = fgc.get("coupon_id").toString();
                            if (couponid.equals(giftCouponId[k])) {
                                int cfree_quantity = Integer.parseInt(fgc.get("cfree_quantity").toString());
                                for (int t = 1; t <= cfree_quantity; t++) {
                                    List<Map<String, Object>> GiftCoupon = new ArrayList<Map<String, Object>>();
                                    Map<String, Object> coumap = new HashMap<String, Object>();
                                    coumap.put("shop_unique", shop_unique);//用户
                                    coumap.put("couponId", giftCouponId[k]);
                                    coumap.put("remark", "商家赠送");
                                    coumap.put("gift_order", orderid);
                                    GiftCoupon.add(coumap);
                                    //限制数量
                                    Map<String, Object> cou = shoppingDao.getOneCoupon(coumap);
                                    Integer limit_quantity_type = Integer.valueOf(cou.get("limit_quantity_type").toString());
                                    Integer total_distribution = Integer.valueOf(cou.get("total_distribution").toString());
                                    String couponId = cou.get("coupon_id").toString();
                                    Map<String, Object> re = new HashMap<String, Object>();
                                    re.put("coupon_id", couponId);
                                    re.put("couponId", couponId);
                                    if (limit_quantity_type == 2) {
                                        int reCount = shoppingDao.queryShoppingCouponCount(re);
                                        if (reCount >= total_distribution) {
                                            result.setStatus(0);
                                            result.setMsg("优惠券已领完！");
                                        } else {
                                            shoppingDao.insertGiftCoupon(GiftCoupon);
                                            shoppingDao.upCoupon(GiftCoupon);
                                        }
                                    } else {
                                        shoppingDao.insertGiftCoupon(GiftCoupon);
                                        shoppingDao.upCoupon(GiftCoupon);
                                    }

                                }
                            }
                        }
                    }
                }
            }
        }


        //店铺金圈币余额减少
        if (!"0".equals(gold_amt.replace("\n", "").replace("\t", "").replace(" ", ""))) {
            int num = supShoppingDao.updateShopGold(paramsShop);
            if (num == 0) {
                TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
                throw new RuntimeException("金圈币不足，请重试");
            }
        }

        //判断借款额度，修改对应的借款额度信息，添加对应的借款列表信息
        BigDecimal loanTotal = new BigDecimal(0);
        if (null != loan_all_amt && !loan_all_amt.trim().equals("") && Double.parseDouble(loan_all_amt.trim()) != 0.0) {
            //根据借款规则ID，获取借款规则详情
            List<Map<String, Object>> ruleList = loanDao.querySxRuleMsg(sxRuleId);
            if (null == ruleList || ruleList.isEmpty()) {
                throw new MyException(0, "贷款规则不存在");
            }
            Map<String, Object> ruleMap = ruleList.get(0);

            //添加借款记录
            Map<String, Object> loanMap = new HashMap<String, Object>();
            loanMap.put("order_no", orderid);
            loanMap.put("loan_money", loan_all_amt);
            loanMap.put("loan_rate", ruleMap.get("rate"));
            loanMap.put("time_limit", ruleMap.get("time_limit"));
            loanMap.put("shop_unique", shop_unique);

            //如果没有待支付的余额，则赊销直接生效，否额等支付完成后，赊销生效
            //读取缓存信息，如果未设置下单赊销生效，则等待确认收货生效：1 or null、等待确认收货时赊销生效；
            Object sxWaitSure = redisTemplate.opsForValue().get("sxWaitSure");
            //添加借款详情记录
            List<Map<String, Object>> listLoan = null;
            Integer timeLimit = Integer.parseInt(ruleMap.get("time_limit").toString());
            BigDecimal rate = new BigDecimal(loan_all_amt).multiply(new BigDecimal(ruleMap.get("rate").toString()));
            if (null == sxWaitSure || sxWaitSure.toString().equals("1")) {
                loanMap.put("valid_type", "2");
            } else {
                if (new BigDecimal(actual_amt).compareTo(new BigDecimal("0")) == 0) {
                    listLoan = calculateLoanDetail(timeLimit, rate, loan_all_amt, orderid);

                    if (null != listLoan && !listLoan.isEmpty()) {
                        Map<String, Object> tempMap = listLoan.get(listLoan.size() - 1);
                        listLoan.remove(listLoan.size() - 1);
                        loanTotal = new BigDecimal(tempMap.get("loanTotal").toString());
                        loanMap.put("repayment_money", loanTotal.doubleValue());
                        loanDao.addNewLoanDetail(listLoan);
                    }
                    loanMap.put("valid_type", "1");
                } else {
                    loanMap.put("valid_type", "2");
                }
            }

//			//计算每一期需要偿还的金额
//			BigDecimal fenqiMoney = new BigDecimal(loan_all_amt).divide(new BigDecimal(ruleMap.get("time_limit").toString()),10,BigDecimal.ROUND_UP);
//			BigDecimal principal_money = new BigDecimal(loan_all_amt).divide(new BigDecimal(ruleMap.get("time_limit").toString()),BigDecimal.ROUND_DOWN).setScale(2,BigDecimal.ROUND_DOWN);
//			BigDecimal rate = new BigDecimal(loan_all_amt).multiply(new BigDecimal(ruleMap.get("rate").toString()));
//			fenqiMoney = fenqiMoney.add(rate).setScale(2,BigDecimal.ROUND_UP);
//
//			BigDecimal fenqiTotalMoney = new BigDecimal(loan_all_amt);
//			fenqiTotalMoney = fenqiTotalMoney.subtract(principal_money.multiply(new BigDecimal(timeLimit))).setScale(2,BigDecimal.ROUND_HALF_UP);
//
//			for(Integer tl = 0; tl< timeLimit ; tl++) {
//				Map<String,Object> tempMap = new HashMap<String,Object>();
//				tempMap.put("order_no", orderid);
//				tempMap.put("fenqi_money", fenqiMoney.setScale(2,BigDecimal.ROUND_UP));
//				tempMap.put("index", tl+1);
//				if(fenqiTotalMoney.compareTo(new BigDecimal(0)) >0) {
//					BigDecimal tempBig = new BigDecimal(0.01);
//					tempMap.put("principal_money", principal_money.add(tempBig).setScale(2,BigDecimal.ROUND_HALF_UP));
//					fenqiTotalMoney = fenqiTotalMoney.subtract(tempBig).setScale(2,BigDecimal.ROUND_HALF_UP);
//				}else {
//					tempMap.put("principal_money", principal_money);
//				}
//				tempMap.put("already_principal_money", 0.00);
//				//计算赊销需要还款总额
//				loanTotal = loanTotal.add(fenqiMoney).setScale(2,BigDecimal.ROUND_HALF_UP);
//
//				listLoan.add(tempMap);
//			}
            //添加赊销记录和赊销详情

            loanDao.addNewLoanOrder(loanMap);

            /*
             * 修改店铺的借款信息，如果订单未已支付状态，则增加未还款金额，否则，需要等待支付结果
             * 不管订单是否已支付，都应扣掉可用金额
             */
            Map<String, Object> shopLoanMap = new HashMap<String, Object>();
            shopLoanMap.put("shop_unique", shop_unique);
            if (new BigDecimal(actual_amt).compareTo(new BigDecimal("0")) == 0) {
                shopLoanMap.put("loanTotal", loanTotal);
            }
            shopLoanMap.put("loanMoney", loan_all_amt);
            loanDao.updateShopLoanMsg(shopLoanMap);
        }


        result.setStatus(1);
        result.setData(paramsShop);
        result.setMsg("提交订单成功！");
        return result;
    }

    /**
     * 1计算赊销信息详情
     *
     * @return
     */
    public static List<Map<String, Object>> calculateLoanDetail(Integer timeLimit, BigDecimal rate, String loan_all_amt, String orderid) {
        List<Map<String, Object>> listLoan = new ArrayList<Map<String, Object>>();
        if (null == loan_all_amt || null == rate || null == timeLimit) {

        } else {
            //计算每一期需要偿还的金额
            BigDecimal fenqiMoney = new BigDecimal(loan_all_amt).divide(new BigDecimal(timeLimit), 10, BigDecimal.ROUND_UP);
            BigDecimal principal_money = new BigDecimal(loan_all_amt).divide(new BigDecimal(timeLimit), BigDecimal.ROUND_DOWN).setScale(2, BigDecimal.ROUND_DOWN);
            fenqiMoney = fenqiMoney.add(rate).setScale(2, BigDecimal.ROUND_UP);
            BigDecimal loanTotal = new BigDecimal(0);

            BigDecimal fenqiTotalMoney = new BigDecimal(loan_all_amt);
            fenqiTotalMoney = fenqiTotalMoney.subtract(principal_money.multiply(new BigDecimal(timeLimit))).setScale(2, BigDecimal.ROUND_HALF_UP);

            for (Integer tl = 0; tl < timeLimit; tl++) {
                Map<String, Object> tempMap = new HashMap<String, Object>();
                tempMap.put("order_no", orderid);
                tempMap.put("fenqi_money", fenqiMoney.setScale(2, BigDecimal.ROUND_UP));
                tempMap.put("index", tl + 1);
                if (fenqiTotalMoney.compareTo(new BigDecimal(0)) > 0) {
                    BigDecimal tempBig = new BigDecimal(0.01);
                    tempMap.put("principal_money", principal_money.add(tempBig).setScale(2, BigDecimal.ROUND_HALF_UP));
                    fenqiTotalMoney = fenqiTotalMoney.subtract(tempBig).setScale(2, BigDecimal.ROUND_HALF_UP);
                } else {
                    tempMap.put("principal_money", principal_money);
                }
                tempMap.put("already_principal_money", 0.00);
                //计算赊销需要还款总额
                loanTotal = loanTotal.add(fenqiMoney).setScale(2, BigDecimal.ROUND_HALF_UP);

                listLoan.add(tempMap);
            }

            Map<String, Object> totalMap = new HashMap<String, Object>();
            totalMap.put("loanTotal", loanTotal.doubleValue());
            listLoan.add(totalMap);
        }
        return listLoan;
    }

    @Transactional
    public PurResult cancelOrder(String main_order_no) {
        PurResult result = new PurResult();

        //取消订单  修改订单状态
        Map<String, Object> params = new HashMap<String, Object>();
        params.put("main_order_no", main_order_no);
        params.put("order_status", "5");
        //增加库存
        List<Map<String, Object>> goods_list = shoppingDao.queryOrderGoodsCount(params);
        for (Map<String, Object> goods : goods_list) {
            if ("0".equals(goods.get("order_status").toString())) {
                shoppingDao.updateGoodsAddCount(goods);
            }
        }

        shoppingDao.updateShoppingOrder(params);
        //店铺金圈币返还
        Map<String, Object> orderMain = shoppingDao.querySupOrderMain(params);//主订单表
        params.put("shop_unique", orderMain.get("shop_unique"));
        params.put("gold_amt", orderMain.get("gold_amt"));
        supShoppingDao.addShopGold(params);

        //检查是否有对应的赊销信息，如果有，退还赊销额度
        Map<String, Object> sxMap = loanDao.queryOrderLoanMsg(main_order_no);
        if (null != sxMap && !sxMap.isEmpty()) {
            Integer validType = Integer.parseInt(sxMap.get("valid_type").toString());
            //如果订单处于待支付状态，撤销订单，并返回额度
            if (validType == 2) {
                sxMap.put("valid_type", "3");
                Integer c = loanDao.cancelOrderLoan(sxMap);
                if (c == 1) {
                    sxMap.put("shopUnique", sxMap.get("shop_unique"));
                    sxMap.put("loan_money", -Double.parseDouble(sxMap.get("loan_money").toString()));
                    sxMap.remove("repayment_money");
                    loanDao.addSXNotReturnMoney(sxMap);
                }
            }
        }


        //删除商家赠送的优惠券并增加优惠券剩余数量
        List<Map<String, Object>> giftIds = shoppingDao.queryShoppingOrder(params);//查询赠送优惠券
        if (giftIds != null && giftIds.size() > 0) {
            for (Map<String, Object> reCoupon : giftIds) {
                reCoupon.put("shop_unique", orderMain.get("shop_unique").toString());
                reCoupon.put("main_order_no", main_order_no);
                reCoupon.put("remark", "商家赠送");
                List<Map<String, Object>> couponIds = shoppingDao.queryOrderCoupon(reCoupon);//查询赠送明细
                if (couponIds != null && couponIds.size() > 0) {
                    shoppingDao.delFullCoupon(couponIds);//删除赠送记录
                    shoppingDao.upCouponNumber(reCoupon);//修改优惠券剩余数量
                }
            }
        }


        result.setStatus(1);
        result.setMsg("该笔订单已经取消！");
        return result;
    }

    @Transactional
    public PurResult buyNowOrder(String good_id, String shop_unique, String oper_id, String gold_amt, String order_amt, String actual_amt, String actual_delivery_price,
                                 String order_remark, String company_code, String spec_id, String spec_name, String good_count, String loan_amt, String loan_count, String recordIds, String giftIds, String giftCouponIds, String AdminCouponId) {
        PurResult result = new PurResult();
        //获取店铺信息
        Map<String, Object> paramsShop = new HashMap<String, Object>();
        paramsShop.put("shop_unique", shop_unique);
        paramsShop.put("good_id", good_id);
        paramsShop.put("good_count", good_count);
        Map<String, Object> shopInfo = shopDao.queryShopMessage(paramsShop);
        System.out.println("xxxxxx=" + paramsShop.toString());
        Map<String, Object> goods = shoppingDao.getGoodById(paramsShop);
        System.out.println(goods.toString());
        goods.put("spec_id", spec_id);
        goods.put("spec_name", spec_name);
        goods.put("good_count", good_count);
        goods.put("good_id", good_id);
        Map<String, Object> goods2 = shoppingDao.queryGoodsStock(goods);
        if (goods2 == null) {
            result.setStatus(0);
            result.setMsg("库存量不足");
            return result;
        }

        //添加订单信息
        String orderid = "T" + new UniqueOrder(0, 0).nextId();
        Map<String, Object> paramsOrder = new HashMap<String, Object>();
        String order_code = SupplierInfoServiceImpl.createOrderNum();
        paramsOrder.put("order_code", order_code);
        paramsOrder.put("company_code", company_code);
        paramsOrder.put("order_remarks", order_remark);
        //paramsOrder.put("pay_mode", 1);//支付方式：1、货到付款；2、支付宝；3、微信;4、其他
        paramsOrder.put("pay_status", 1);//支付状态：1、欠款；2、已结清
        paramsOrder.put("order_status", "0");//订单状态  0待付款
        if (new BigDecimal(actual_amt).compareTo(new BigDecimal("0")) == 0) {
            paramsOrder.put("pay_status", 2);
            paramsOrder.put("order_status", "1");
            sendMsg(order_code);
        }
        paramsOrder.put("customer_code", shop_unique);//客户编号
        paramsOrder.put("collect_name", MUtil.strObject(shopInfo.get("shop_name")));//收货人姓名
        paramsOrder.put("collect_phone", MUtil.strObject(shopInfo.get("shop_phone")));//联系电话
        paramsOrder.put("collect_address", MUtil.strObject(shopInfo.get("shop_address_detail")));//收货地址
        paramsOrder.put("order_type", 1);//订单类型 0：自动下单；1：客户下单
        paramsOrder.put("order_money", order_amt);//订单总金额
        paramsOrder.put("shop_latitude", MUtil.strObject(shopInfo.get("shop_latitude")));//店铺纬度
        paramsOrder.put("shop_longitude", MUtil.strObject(shopInfo.get("shop_longitude")));//店铺经度
        paramsOrder.put("present_beans", "0");//满足规则赠送百货豆
        paramsOrder.put("platform_beans", "0");//实际赠送的百货豆
        paramsOrder.put("main_order_no", orderid);
        paramsOrder.put("delivery_fee", actual_delivery_price);//配送费

        //查询优惠券金额
        BigDecimal couponRe = new BigDecimal(0);
        if (recordIds != null && !recordIds.equals("")) {
            String[] recordId = recordIds.split(",");
            Map<String, Object> mapReid = new HashMap<String, Object>();
            mapReid.put("recordId", recordId[0]);
            Map<String, Object> reID = shoppingDao.getOneCoupon(mapReid);
            couponRe = new BigDecimal(reID.get("coupon_amount").toString());
        }

//		BigDecimal cost_amt=new BigDecimal(good_count).multiply(new BigDecimal(goods.get("supply_price").toString()).subtract(couponRe));
        BigDecimal cost_amt = new BigDecimal(good_count).multiply(new BigDecimal(goods.get("supply_price").toString()));
        paramsOrder.put("cost_fee", cost_amt);//成本价
        paramsOrder.put("pay_money", new BigDecimal(actual_delivery_price).add(cost_amt));//成本价+配送费
        paramsOrder.put("real_pay_money", cost_amt);
        paramsOrder.put("sum_deduct_amt", gold_amt);//金圈币抵扣
        paramsOrder.put("loan_money", loan_amt == null ? 0 : loan_amt);
        supplierInfoDao.insertOrder(paramsOrder);


        //把赠送商品添加到订单详情中
        List<Map<String, Object>> good_list = new ArrayList<Map<String, Object>>();
        List<Map<String, Object>> cart_list = new ArrayList<Map<String, Object>>();

        if (giftIds != null && !giftIds.equals("")) {
            String[] giftId = giftIds.split(",");
            for (int k = 0; k < giftId.length; k++) {
                Map<String, Object> resmap = new HashMap<String, Object>();
                resmap.put("giftId", giftId[k]);
                List<Map<String, Object>> fullgiftList = shoppingDao.getFullgift(resmap);
                for (Map<String, Object> full : fullgiftList) {
                    String companyCode = full.get("compay_code").toString();
                    if (companyCode.equals(company_code)) {
                        String gift_id = full.get("gift_id").toString();
                        List<Map<String, Object>> fullGoods = shoppingDao.getGiftGoodsList(gift_id);
                        for (Map<String, Object> go : fullGoods) {
                            go.put("good_id", go.get("goods_id").toString());
                            go.put("good_count", go.get("gfree_quantity").toString());
                            go.put("supply_price", '0');
                            go.put("online_price", '0');
                            go.put("sum_amt", '0');
                            go.put("goods_label", '4');
                            go.put("promotion_id", "-1");
                            go.put("promotion_count", null);
                            go.put("loan_count", null);
                            go.put("loan_cut", null);
                            go.put("company_code", company_code);
                            go.put("shop_unique", shop_unique);
                            go.put("create_time", new Date());
                            go.put("company_code", company_code);
                            go.put("is_order", 1);
                            go.put("order_no", orderid);
                            good_list.add(go);
                            cart_list.add(go);
                        }
                    }
                }
            }

        }

        //添加购物车(购物车表当做商品详情表)
        Map<String, Object> cart = new HashMap<String, Object>();
        cart.put("shop_unique", shop_unique);
        cart.put("good_id", good_id);
        cart.put("spec_id", spec_id);
        cart.put("spec_name", spec_name);
        cart.put("good_count", good_count);
        cart.put("create_time", new Date());
        cart.put("company_code", company_code);
        cart.put("is_order", 1);
        cart.put("order_no", orderid);
        cart_list.add(cart);
        for (Map<String, Object> gCart : cart_list) {
            shoppingDao.insertShoppingCart(gCart);
        }

        good_list.add(goods);

        for (Map<String, Object> goodsCart : good_list) {
            //添加订单详情表
            Map<String, Object> paramsOrderdetail = new HashMap<String, Object>();
            paramsOrderdetail.put("order_code", order_code);
            paramsOrderdetail.put("goods_code", goodsCart.get("goods_barcode"));
            paramsOrderdetail.put("goods_cost", goodsCart.get("supply_price"));
            if (goodsCart.get("promotion_count") != null && Double.parseDouble(goodsCart.get("promotion_count").toString()) >= Integer.parseInt(good_count)) {
                paramsOrderdetail.put("goods_price", goodsCart.get("promotion_price"));
                paramsOrderdetail.put("real_price", goodsCart.get("promotion_price"));
            } else {
                paramsOrderdetail.put("goods_price", goodsCart.get("online_price"));
                paramsOrderdetail.put("real_price", goodsCart.get("online_price"));
            }
            paramsOrderdetail.put("goods_name", goodsCart.get("goods_name"));
            paramsOrderdetail.put("goods_unit", goodsCart.get("goodsunit_name"));
            paramsOrderdetail.put("goods_id", goodsCart.get("good_id"));
            paramsOrderdetail.put("fact_price", new BigDecimal(good_count).multiply(new BigDecimal(goodsCart.get("online_price").toString())));
            paramsOrderdetail.put("goods_count", goodsCart.get("good_count"));
            paramsOrderdetail.put("auto_fxiaoshou", goodsCart.get("auto_fxiaoshou"));
            if (goodsCart.get("promotion_count") != null && Double.parseDouble(goodsCart.get("promotion_count").toString()) >= Integer.parseInt(good_count)) {
                paramsOrderdetail.put("promotion_id", goodsCart.get("promotion_id"));
            }

            if (goodsCart.containsKey("goods_label")) {
                paramsOrderdetail.put("goods_label", goodsCart.get("goods_label"));
            }

            supplierInfoDao.insertOrderDetail(paramsOrderdetail);
            Integer orderdetail_id = Integer.parseInt(MUtil.strObject(paramsOrderdetail.get("orderdetail_id")));

            //添加订单详情规则表
            Map<String, Object> paramsOrderSpec = new HashMap<String, Object>();
            paramsOrderSpec.put("spec_name", goodsCart.get("spec_name").toString());
            paramsOrderSpec.put("orderdetail_id", orderdetail_id);
            paramsOrderSpec.put("compose_specs_id", goodsCart.get("spec_id").toString());
            supplierInfoDao.insertOrderGoodsSpec(paramsOrderSpec);
        }

        //添加主订单
        paramsShop.put("main_order_no", orderid);
        paramsShop.put("gold_amt", gold_amt);
        paramsShop.put("order_amt", order_amt);
        paramsShop.put("actual_amt", actual_amt);
        paramsShop.put("oper_id", oper_id);
        paramsShop.put("shop_unique", shop_unique);
        paramsShop.put("loan_amt", loan_amt == null ? 0 : loan_amt);
        paramsShop.put("delivery_price", actual_delivery_price);
        shoppingDao.insertOrderMain(paramsShop);
        paramsShop.put("total_fee", new BigDecimal(actual_amt).multiply(new BigDecimal("100")));

        //优惠券使用
        if (recordIds != null && !recordIds.equals("")) {
            String[] recordId = recordIds.split(",");
            Map<String, Object> upMap = new HashMap<String, Object>();
            upMap.put("recordId", recordId);
            upMap.put("orderid", orderid);
            shoppingDao.updateRecStatus(upMap);
        }

        //跨店优惠券使用
        if (AdminCouponId != null && !AdminCouponId.equals("")) {
            Map<String, Object> upMap = new HashMap<String, Object>();
            upMap.put("shopUnique", shop_unique);
            upMap.put("couponId", AdminCouponId);
            upMap.put("usageStatus", '1');
            upMap.put("orderid", orderid);
            upMap.put("remark", "自动领取使用");

            //限制数量
            Map<String, Object> adMap = new HashMap<String, Object>();
            adMap.put("couponId", AdminCouponId);
            adMap.put("coupon_id", AdminCouponId);
            Map<String, Object> cou = shoppingDao.getOneCoupon(adMap);
            Integer limit_quantity_type = Integer.valueOf(cou.get("limit_quantity_type").toString());
            Integer total_distribution = Integer.valueOf(cou.get("total_distribution").toString());
            //判断限制数量的优惠券是否数量足够
            if (limit_quantity_type == 2) {
                int reCount = shoppingDao.queryShoppingCouponCount(adMap);
                if (reCount >= total_distribution) {
                    result.setStatus(0);
                    result.setMsg("优惠券已领完！");
                } else {
                    int ad = shoppingDao.addRecStatus(upMap);
                    if (ad > 0) {
                        List<Map<String, Object>> list = new ArrayList<Map<String, Object>>();
                        list.add(adMap);
                        shoppingDao.upCoupon(list);
                    }
                }
            } else {
                shoppingDao.addRecStatus(upMap);
            }

        }

        //满赠
        //插入中间表信息
        if (giftIds != null && !giftIds.equals("")) {
            String[] giftId = giftIds.split(",");
            List<Map<String, Object>> resource = new ArrayList<Map<String, Object>>();
            for (int k = 0; k < giftId.length; k++) {
                Map<String, Object> resmap = new HashMap<String, Object>();
                resmap.put("orderCode", orderid);//总订单
                resmap.put("giftId", giftId[k]);
                resource.add(resmap);
            }
            shoppingDao.insertGiftOrder(resource);
        }

//        //插入赠送优惠券
//		if(!giftCouponIds.equals("") && giftCouponIds.equals(null)) {
//			String []giftCouponId = giftCouponIds.split(",");
//			List<Map<String,Object>> GiftCoupon=new ArrayList<Map<String,Object>>();
//			for(int k=0;k<giftCouponId.length;k++){
//				Map<String,Object> coumap=new HashMap<String, Object>();
//				coumap.put("shop_unique", shop_unique);//用户
//				coumap.put("couponId", giftCouponId[k]);
//				coumap.put("remark", "商家赠送");
//				GiftCoupon.add(coumap);
//			}
//	        shoppingDao.insertGiftCoupon(GiftCoupon);
//		}

        //插入赠送优惠券
        if (giftCouponIds != null && !giftCouponIds.equals("")) {
            String[] giftCouponId = giftCouponIds.split(",");
            if (!giftIds.equals(null) && !giftIds.equals("")) {
                String[] giftId = giftIds.split(",");
                for (int g = 0; g < giftId.length; g++) {
                    String gift_id = giftId[g];
                    List<Map<String, Object>> gc = shoppingDao.getGiftCouponList(gift_id);
                    for (Map<String, Object> fgc : gc) {
                        for (int k = 0; k < giftCouponId.length; k++) {
                            String couponid = fgc.get("coupon_id").toString();
                            if (couponid.equals(giftCouponId[k])) {
                                int cfree_quantity = Integer.parseInt(fgc.get("cfree_quantity").toString());
                                for (int t = 1; t <= cfree_quantity; t++) {
                                    List<Map<String, Object>> GiftCoupon = new ArrayList<Map<String, Object>>();
                                    Map<String, Object> coumap = new HashMap<String, Object>();
                                    coumap.put("shop_unique", shop_unique);//用户
                                    coumap.put("couponId", giftCouponId[k]);
                                    coumap.put("remark", "商家赠送");
                                    coumap.put("gift_order", orderid);
                                    GiftCoupon.add(coumap);
                                    //限制数量
                                    Map<String, Object> cou = shoppingDao.getOneCoupon(coumap);
                                    Integer limit_quantity_type = Integer.valueOf(cou.get("limit_quantity_type").toString());
                                    Integer total_distribution = Integer.valueOf(cou.get("total_distribution").toString());
                                    String couponId = cou.get("coupon_id").toString();
                                    Map<String, Object> re = new HashMap<String, Object>();
                                    re.put("coupon_id", couponId);
                                    re.put("couponId", couponId);
                                    if (limit_quantity_type == 2) {
                                        int reCount = shoppingDao.queryShoppingCouponCount(re);
                                        if (reCount >= total_distribution) {
                                            result.setStatus(0);
                                            result.setMsg("优惠券已领完！");
                                        } else {
                                            int ad = shoppingDao.insertGiftCoupon(GiftCoupon);
                                            if (ad > 0) {
                                                shoppingDao.upCoupon(GiftCoupon);
                                            }
                                        }
                                    } else {
                                        int ad = shoppingDao.insertGiftCoupon(GiftCoupon);
                                        if (ad > 0) {
                                            shoppingDao.upCoupon(GiftCoupon);
                                        }
                                    }

                                }
                            }
                        }
                    }
                }
            }
        }

        //减掉库存
        shoppingDao.updateGoodsCount(goods);

        //店铺金圈币余额减少
        if (new BigDecimal(gold_amt).compareTo(new BigDecimal("0")) > 0) {
            int num = supShoppingDao.updateShopGold(paramsShop);
            if (num == 0) {
                TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
                result.setStatus(0);
                result.setMsg("金圈币不足");
                return result;
            }
        }

        result.setStatus(1);
        result.setData(paramsShop);
        result.setMsg("提交订单成功！");
        return result;
    }

    public Map<String, Object> queryOrderByOrderNo(Map<String, Object> params) {
        return shoppingDao.queryOrderByOrderNo(params);
    }

    public ShopsResult orderSettlementCode(HttpServletRequest request, String out_trade_no, String shop_unique, int total_fee) throws IOException {
        ShopsResult sr = new ShopsResult(1, "获取信息成功!");
        //检查该订单编号是否付款
        Map<String, Object> params = new HashMap<String, Object>();
        params.put("main_order_no", out_trade_no);
        Map<String, Object> orderMap = shoppingDao.queryOrderSettlementMsg(params);
        if (null == orderMap || orderMap.isEmpty()) {

        } else if (orderMap.get("pay_status").toString().equals("2")) {
            sr.setMsg("该订单已支付");
            sr.setStatus(1);
            return sr;
        } else if (orderMap.get("pay_status").toString().equals("3")) {
            sr.setMsg("该订单已取消");
            sr.setStatus(1);
            return sr;
        }
        String mch_id = SwiftpassConfig.mch_id;
        String mch_key = SwiftpassConfig.key;

        //易通扫码支付
        SortedMap<String, String> sMap = new TreeMap<String, String>();
        sMap.put("service", "unified.trade.native");
        sMap.put("sign_type", "MD5");
        sMap.put("mch_id", mch_id);
        sMap.put("out_trade_no", out_trade_no);
        sMap.put("total_fee", String.valueOf(total_fee));
//		sMap.put("total_fee", "1");
        sMap.put("notify_url", SwiftpassConfig.ORDERSETTLEMENT);
        sMap.put("nonce_str", String.valueOf(new Date().getTime()));
        sMap.put("mch_create_ip", XMLUtils.getIpAddr(request));
        sMap.put("body", "供货商城-易通生成二维码");
        sMap.put("device_info", shop_unique);

        Map<String, String> tem = SignUtils.paraFilter(sMap);
        StringBuilder buf = new StringBuilder((tem.size() + 1) * 10);
        SignUtils.buildPayParams(buf, tem, false);
        String preStr = buf.toString();
        String sign = MD5.sign(preStr, "&key=" + mch_key, "utf-8");
        sMap.put("sign", sign);
        String reqUrl = SwiftpassConfig.req_url;
        System.out.println("供货商城-生成二维码，请求参数:" + XMLUtils.parseXML(sMap));
        CloseableHttpResponse response = null;
        CloseableHttpClient client = null;
        String res = null;
        Map<String, String> resultMap = null;

        try {
            HttpPost httpPost = new HttpPost(reqUrl);
            StringEntity entityParams = new StringEntity(XMLUtils.parseXML(sMap), "utf-8");
            httpPost.setEntity(entityParams);
            httpPost.setHeader("Content-Type", "text/xml;utf-8");
            client = HttpClients.createDefault();
            response = client.execute(httpPost);
            if (response != null && response.getEntity() != null) {
                resultMap = SignUtils.toMap(EntityUtils.toByteArray(response.getEntity()), "utf-8");
                res = XMLUtils.toXml(resultMap);
                System.out.println("请求结果：" + res);
                if (!SignUtils.checkParam(resultMap, mch_key)) {
                    sr.setStatus(0);//支付失败
                    sr.setMsg("支付失败，验证签名不通过！");
                    return sr;
                } else {
                    if ("0".equals(resultMap.get("status")) && "0".equals(resultMap.get("result_code"))) {
                        sr.setStatus(1);
                        sr.setMsg("生成二维码成功！");
                        resultMap.put("total_fee", total_fee + "");
                        resultMap.put("main_order_no", out_trade_no);
                        sr.setData(resultMap);
                    } else {
                        sr.setStatus(0);//支付失败
                        sr.setMsg("支付失败，请联系客服人员！");
                        return sr;
                    }
                }
            } else {
                sr.setStatus(0);//支付失败
                sr.setMsg("支付失败，请重试！");
                return sr;
            }
        } catch (Exception e) {
            sr.setStatus(0);//支付失败
            sr.setMsg("支付失败，请重试！");
            return sr;
        } finally {
            if (response != null) {
                response.close();
            }
            if (client != null) {
                client.close();
            }
        }

        return sr;
    }

    public PurResult generateCode(HttpServletRequest request, String out_trade_no, String shop_unique, int total_fee, Integer shopType) throws Exception {
        PurResult result = new PurResult();

        //检查该订单编号是否付款
        Map<String, Object> params = new HashMap<String, Object>();
        params.put("main_order_no", out_trade_no);
        Map<String, Object> order = shoppingDao.queryOrderByOrderNo(params);
        if ("5".equals(order.get("order_status").toString())) {
            result.setStatus(0);
            result.setMsg("该笔订单已取消！");
            return result;
        }
        if (!"0".equals(order.get("order_status").toString())) {
            result.setStatus(0);
            result.setMsg("该笔订单已付款！");
            return result;
        }

        String mch_id = SwiftpassConfig.mch_id;
        String mch_key = SwiftpassConfig.key;

        //易通扫码支付
        SortedMap<String, String> sMap = new TreeMap<String, String>();
        sMap.put("service", "unified.trade.native");
        sMap.put("sign_type", "MD5");
        sMap.put("mch_id", mch_id);
        sMap.put("out_trade_no", out_trade_no);
        sMap.put("total_fee", String.valueOf(total_fee));
//		sMap.put("total_fee", "1");
        if (null == shopType || shopType != 8) {
            sMap.put("notify_url", SwiftpassConfig.notify_url);
        } else {
            sMap.put("notify_url", SwiftpassConfig.notify_url_WJ);
        }
        sMap.put("nonce_str", String.valueOf(new Date().getTime()));
        sMap.put("mch_create_ip", XMLUtils.getIpAddr(request));
        sMap.put("body", "供货商城-易通生成二维码");
        sMap.put("device_info", shop_unique);

        Map<String, String> tem = SignUtils.paraFilter(sMap);
        StringBuilder buf = new StringBuilder((tem.size() + 1) * 10);
        SignUtils.buildPayParams(buf, tem, false);
        String preStr = buf.toString();
        String sign = MD5.sign(preStr, "&key=" + mch_key, "utf-8");
        sMap.put("sign", sign);
        String reqUrl = SwiftpassConfig.req_url;
        System.out.println("供货商城-生成二维码，请求参数:" + XMLUtils.parseXML(sMap));
        CloseableHttpResponse response = null;
        CloseableHttpClient client = null;
        String res = null;
        Map<String, String> resultMap = null;
        try {
            HttpPost httpPost = new HttpPost(reqUrl);
            StringEntity entityParams = new StringEntity(XMLUtils.parseXML(sMap), "utf-8");
            httpPost.setEntity(entityParams);
            httpPost.setHeader("Content-Type", "text/xml;utf-8");
            client = HttpClients.createDefault();
            response = client.execute(httpPost);
            if (response != null && response.getEntity() != null) {
                resultMap = SignUtils.toMap(EntityUtils.toByteArray(response.getEntity()), "utf-8");
                res = XMLUtils.toXml(resultMap);
                System.out.println("请求结果：" + res);
                if (!SignUtils.checkParam(resultMap, mch_key)) {
                    result.setStatus(0);//支付失败
                    result.setMsg("支付失败，验证签名不通过！");
                    return result;
                } else {
                    if ("0".equals(resultMap.get("status")) && "0".equals(resultMap.get("result_code"))) {
                        result.setStatus(1);
                        result.setMsg("生成二维码成功！");
                        result.setData(resultMap);
                    } else {
                        result.setStatus(0);//支付失败
                        result.setMsg("支付失败，请联系客服人员！");
                        return result;
                    }
                }
            } else {
                result.setStatus(0);//支付失败
                result.setMsg("支付失败，请重试！");
                return result;
            }
        } catch (Exception e) {
            result.setStatus(0);//支付失败
            result.setMsg("支付失败，请重试！");
            return result;
        } finally {
            if (response != null) {
                response.close();
            }
            if (client != null) {
                client.close();
            }
        }
        return result;
    }

    public ShopsResult queryShopOrderDetail(String orderCode, String main_order_no, String company_code) {
        ShopsResult sr = new ShopsResult(1, "查询成功");
        Map<String, Object> map = new HashMap<String, Object>();
        map.put("main_order_no", main_order_no);
        map.put("company_code", company_code);
        List<Map<String, Object>> good_list = shoppingDao.queryOrderDetailGoodListShop(map);
        BigDecimal sum = new BigDecimal("0");
        BigDecimal realSum = new BigDecimal("0");
        for (Map<String, Object> goodMap : good_list) {
            if ("".equals(goodMap.get("goods_img"))) {
                goodMap.put("goods_img", "../static/img/no_goods.jpg");
            } else if (goodMap.get("goods_img").toString().indexOf("http") == -1) {
                goodMap.put("goods_img", "http://file.buyhoo.cc/" + goodMap.get("goods_img").toString());
            }
            sum = sum.add(new BigDecimal(goodMap.get("sum_amt").toString()));
            realSum = realSum.add(new BigDecimal(goodMap.get("real_amt").toString()));
        }
        sr.setData(good_list);
        map.put("sum", sum);
        map.put("realSum", realSum);
        sr.setCord(map);
        return sr;
    }

    public SettlementVo queryOrderDetail(String main_order_no, Integer shopType, String shop_unique) {
        SettlementVo settle = new SettlementVo();
        Map<String, Object> params = new HashMap<String, Object>();
        params.put("main_order_no", main_order_no);
        params.put("shop_unique", shop_unique);
        List<Map<String, Object>> list = shoppingDao.queryOrderDetail(params);

        Integer sum_count = 0;
        BigDecimal couponTotal = new BigDecimal(0);//优惠券总费用
        String pay_type = "";
        for (Map<String, Object> map : list) {
            map.put("main_order_no", main_order_no);
            map.put("shop_unique", shop_unique);

            pay_type = map.get("pay_type").toString();

            List<Map<String, Object>> good_list;
            if (null == shopType || shopType != 8) {
                good_list = shoppingDao.queryOrderDetailGoodList(map);
                //System.out.println("111"+good_list.toString());
            } else {
                good_list = shoppingDao.queryOrderDetailGoodListWJ(map);
            }
            BigDecimal sum = new BigDecimal("0");
            for (Map<String, Object> goodMap : good_list) {
                //System.out.println("2222"+goodMap.toString());
                if ("".equals(goodMap.get("goods_img"))) {
                    goodMap.put("goods_img", "../static/img/no_goods.jpg");
                } else if (goodMap.get("goods_img").toString().indexOf("http") == -1) {
                    goodMap.put("goods_img", "http://file.buyhoo.cc/" + goodMap.get("goods_img").toString());
                }
                sum = sum.add(new BigDecimal(goodMap.get("total_amt").toString()));
//				if(goodMap.get("goods_label").toString().equals("4")) {
//					good_list.remove(goodMap);
//				}
            }
            sum_count += Integer.valueOf(map.get("sum_good_count").toString());
            map.put("sum_amt", sum.setScale(2, BigDecimal.ROUND_HALF_UP).doubleValue());
            map.put("good_list", good_list);


            BigDecimal sumAmt = new BigDecimal(sum.setScale(2, BigDecimal.ROUND_HALF_UP).doubleValue()); //商品金额
            //供应商优惠券
            Map<String, Object> reMap = new HashMap<String, Object>();
            reMap.put("orderCode", main_order_no);
            reMap.put("shop_unique", shop_unique);
            reMap.put("usage_status", "1");
            reMap.put("company_code", map.get("company_code").toString());
            List<Map<String, Object>> recCoupon = shoppingDao.getUsedCoupon(reMap);
            if (recCoupon.size() == 0) {
                map.put("coupon_amount", '0');//优惠金额
            } else {
                for (Map<String, Object> co : recCoupon) {
                    BigDecimal coupon_amount = new BigDecimal(co.get("coupon_amount").toString());
                    map.put("coupon_amount", coupon_amount);//优惠金额
                    couponTotal = couponTotal.add(coupon_amount).setScale(2, BigDecimal.ROUND_HALF_UP);
                }

            }

            //满赠
            Map<String, Object> fullMap = new HashMap<String, Object>();
            fullMap.put("orderCode", main_order_no);
            fullMap.put("company_code", map.get("company_code").toString());
            //BigDecimal max1 = new BigDecimal(0);
            System.out.println(fullMap);
            List<Map<String, Object>> fullgift = getFullgift(fullMap);
            System.out.println(fullgift);
            map.put("fullgiftList", fullgift);//赠品

        }

        Map<String, Object> orderMain = shoppingDao.querySupOrderMain(params);
        settle.setDeduct_amt_all(orderMain.get("gold_amt") != null ? new BigDecimal(orderMain.get("gold_amt").toString()) : new BigDecimal("0"));
        settle.setSum_amt_all(orderMain.get("order_amt") != null ? new BigDecimal(orderMain.get("order_amt").toString()) : new BigDecimal("0"));
        settle.setShould_amt_all(orderMain.get("actual_amt") != null ? new BigDecimal(orderMain.get("actual_amt").toString()) : new BigDecimal("0"));
        settle.setSettlementList(list);
        settle.setSum_delivery_price(orderMain.get("delivery_price") != null ? new BigDecimal(orderMain.get("delivery_price").toString()) : new BigDecimal("0"));
        settle.setSum_count(sum_count);


        BigDecimal shouldAmtAll = orderMain.get("actual_amt") != null ? new BigDecimal(orderMain.get("actual_amt").toString()) : new BigDecimal("0");
        //跨店优惠券
        Map<String, Object> adMap = new HashMap<String, Object>();
        adMap.put("orderCode", main_order_no);
        adMap.put("shop_unique", shop_unique);
        adMap.put("company_code", "GS371300001");
        List<Map<String, Object>> adCoupon = shoppingDao.getUsedCoupon(adMap);
        if (adCoupon.size() == 0) {
            settle.setAdmin_coupon(new BigDecimal(0));//优惠金额
        } else {
            for (Map<String, Object> ad : adCoupon) {
                BigDecimal coupon_amount = new BigDecimal(ad.get("coupon_amount").toString());
                settle.setAdmin_coupon(coupon_amount);//优惠金额
            }
        }

        //订单总金额：商品总额+配送费-优惠券-商品满减-金圈币
        BigDecimal total_money = new BigDecimal(0);
        BigDecimal sum_amt_all = settle.getSum_amt_all();//商品总金额
        BigDecimal sum_delivery_price = settle.getSum_delivery_price();//配送费总额

        BigDecimal admin_coupon = settle.getAdmin_coupon();//跨店满减
        BigDecimal deduct_amt_all = settle.getDeduct_amt_all();//金圈币

        total_money = sum_amt_all.add(sum_delivery_price).subtract(couponTotal).subtract(admin_coupon).subtract(deduct_amt_all).setScale(2, BigDecimal.ROUND_HALF_UP);

        settle.setTotal_money(total_money);


        BigDecimal loan_amt_all = new BigDecimal(orderMain.get("loan_amt").toString());//赊销总金额
        BigDecimal actual_amt = new BigDecimal(orderMain.get("actual_amt").toString());//订单总金额


        //赊销金额>0,查询应还款金额
        if (loan_amt_all.compareTo(new BigDecimal(0)) > 0) {
            pay_type = "赊销";
            Map<String, Object> loanMap = shopDao.queryNeedReturnMoneyByMainOrderNo(main_order_no);

            //为了防止未计算赊销应还款金额，本地计算
            Integer time_limit = Integer.parseInt(loanMap.get("time_limit").toString());
            BigDecimal loan_rate = new BigDecimal(loanMap.get("loan_rate").toString());

            //计算应还款金额
            //添加借款详情记录

            List<Map<String, Object>> listLoan = calculateLoanDetail(time_limit, loan_rate, loan_amt_all + "", main_order_no);
            if (null != listLoan && !listLoan.isEmpty()) {
                Map<String, Object> tempMap = listLoan.get(listLoan.size() - 1);
                loan_amt_all = new BigDecimal(tempMap.get("loanTotal").toString());
            }

        }
        BigDecimal pay_money = loan_amt_all.add(actual_amt).setScale(2, BigDecimal.ROUND_HALF_UP);

        //支付方式
        settle.setPay_type(pay_type);
        //实际支付金额
        settle.setPay_money(pay_money);

        return settle;
    }

    public Map<String, Object> queryGoldByShop(Map<String, Object> params) {
        return supShoppingDao.queryGoldByShop2(params);
    }

    public Map<String, Object> queryGoldByShop1(Map<String, Object> params) {
        return supShoppingDao.queryGoldByShop(params);
    }

    //添加云商订单消息提醒
    public void sendMsg(String main_order_no) {
        try {
            Map<String, Object> params = new HashMap<String, Object>();
            params.put("main_order_no", main_order_no);
            List<Map<String, Object>> list = shoppingDao.getOrderByMainOrderNo(params);
            for (Map<String, Object> map : list) {
                Map<String, Object> sysmsgParams = new HashMap<String, Object>();
                sysmsgParams.put("sysmsg_content", "您有一条新的云商订单，请查看！");
                sysmsgParams.put("source_code", map.get("order_code"));
                sysmsgParams.put("company_code", map.get("company_code"));
                sysmsgParams.put("sysmsg_type", "1");
                supplierInfoDao.addSysmsg(sysmsgParams);
                Integer sysmsg_id = Integer.parseInt(MUtil.strObject(sysmsgParams.get("sysmsg_id")));

                //获取业务员id
                List<String> userList = supplierInfoDao.getUserList(map.get("company_code").toString());
                List<Map<String, Object>> sysmsgUserList = new ArrayList<Map<String, Object>>();
                for (int k = 0; k < userList.size(); k++) {
                    Map<String, Object> sysmsgUser = new HashMap<String, Object>();
                    sysmsgUser.put("sysmsg_id", sysmsg_id);
                    sysmsgUser.put("staffer_id", userList.get(k));
                    sysmsgUserList.add(sysmsgUser);
                }
                //添加供货商消息用户表
                supplierInfoDao.addSysmsgUser(sysmsgUserList);
                //推送消息
                Map<String, String> extra = new HashMap<String, String>();
                extra.put("msgType", "1");
                extra.put("source_id", map.get("order_code").toString());
                System.out.println("推送的列表" + extra + userList);
                PushThread pushThread = new PushThread(userList, "智慧云商", "2", "您有一条新的云商订单，请查看！", extra);
                Thread t = new Thread(pushThread);
                t.start();
            }
        } catch (Exception e) {
            e.printStackTrace();
            System.out.println("推送消息失败！！！");
        }
    }

    public Map<String, Object> queryShopMessage(Map<String, Object> map) {
        return shopDao.queryShopMessage(map);
    }

    public void updateShopGold(Map<String, Object> params) {
        supShoppingDao.updateShopGold(params);
    }

    public void addShopGold(Map<String, Object> params) {
        supShoppingDao.addShopGold(params);
    }

    public List<Map<String, Object>> queryShoppingCart2(String shop_unique, String area_dict_num, Integer shopType) {
        Map<String, Object> params = new HashMap<String, Object>();
        params.put("shop_unique", shop_unique);
        List<Map<String, Object>> list;
        if (null == shopType || shopType != 8) {
            list = shoppingDao.querySupplierByCart2(params);
            for (Map<String, Object> map : list) {
                map.put("area_dict_num", area_dict_num);
                //配送费
                Map<String, Object> deliveryPrice = shoppingDao.getDeliveryPrice(map);

                map.put("free_delivery_price", deliveryPrice.get("free_delivery_price").toString());//订单免配送费价格
                BigDecimal delivery_price = new BigDecimal("0");
                if ("2".equals(deliveryPrice.get("delivery_price_type").toString())) {//按订单配送
                    delivery_price = new BigDecimal(deliveryPrice.get("delivery_price").toString());
                }

                List<Map<String, Object>> good_list = shoppingDao.querySupplierGoodListNew(map.get("company_code").toString(), area_dict_num, shop_unique);

                for (Map<String, Object> goodMap : good_list) {
                    if ("".equals(goodMap.get("goods_img"))) {
                        goodMap.put("goods_img", "../static/img/no_goods.jpg");
                    } else if (goodMap.get("goods_img").toString().indexOf("http") == -1) {
                        goodMap.put("goods_img", "http://file.buyhoo.cc/" + goodMap.get("goods_img").toString());
                    }
                    if ("1".equals(deliveryPrice.get("delivery_price_type").toString())) {//按件数
                        delivery_price = delivery_price.add(new BigDecimal(goodMap.get("delivery_price").toString()).multiply(new BigDecimal(goodMap.get("good_count").toString())));
                    }

                }
                map.put("delivery_price_type", deliveryPrice.get("delivery_price_type").toString());//配送费类型
                map.put("send_price", deliveryPrice.get("send_price").toString());//订单起送价格
                map.put("good_list", good_list);
                //商品总金额
                BigDecimal sum_amt = new BigDecimal(map.get("sum_amt").toString());
                map.put("sum_amt", sum_amt);

                //满赠
                Map<String, Object> par = new HashMap<String, Object>();
                par.put("company_code", map.get("company_code").toString());
                List<Map<String, Object>> fg = getFullgift(par);
                map.put("fullgiftList", fg);
            }
        } else {
            list = shoppingDao.querySupplierByCartWJ(params);
            for (Map<String, Object> map : list) {
                map.put("area_dict_num", area_dict_num);
                map.put("free_delivery_price", "0");
                map.put("delivery_price_type", 1);
                map.put("send_price", 0);//订单起送价格
                params.clear();
                map.put("shop_unique", shop_unique);
                List<Map<String, Object>> good_list = shoppingDao.querySupplierGoodListWJ(map);
                for (Map<String, Object> tempMap : good_list) {
                    System.out.println(tempMap.get("spec_img"));
                    String imgPath = tempMap.get("spec_img").toString();
                    if (imgPath.indexOf("../") == -1 && imgPath.indexOf("http") == -1) {
                        tempMap.put("spec_img", "http://file.buyhoo.cc/" + imgPath);
                    }
                }
                map.put("good_list", good_list);
            }
        }
        return list;
    }

    public void addPromotionUnionpay(String main_order_no) {
        List<Map<String, Object>> goodsList = shoppingDao.getOrderDetail(main_order_no);
        List<Map<String, Object>> cuGoodList = shoppingDao.selectPromotionGoodsList(goodsList);

        List<Map<String, Object>> newCuGoodList = new ArrayList<Map<String, Object>>();

        if (null != cuGoodList && cuGoodList.size() > 0) {
            for (Map<String, Object> cu : cuGoodList) {
                for (Map<String, Object> g : goodsList) {
                    if (cu.get("goods_barcode").equals(g.get("goods_barcode"))) {
                        cu.put("shop_unique", g.get("shop_unique"));
                        cu.put("promotion_count", g.get("goods_count"));
                        newCuGoodList.add(cu);
                    }
                }
            }
        }

        if (null != newCuGoodList && !newCuGoodList.isEmpty()) {
            supShoppingDao.inserPromotionUnionpays(newCuGoodList);
        }

        //添加银联促销商品
//		if(cuGoodList!=null&&cuGoodList.size()>0) {
//			for(Map<String ,Object> cu:cuGoodList) {
//				for(Map<String ,Object> map:goodsList) {
//					if(cu.get("goods_barcode").equals(map.get("goods_barcode"))) {
//						cu.put("shop_unique", map.get("shop_unique"));
//						cu.put("promotion_count", map.get("goods_count"));
//						supShoppingDao.insertPromotionUnionpay(cu);
//					}
//				}
//			}
//		}
    }

    /**
     * 使用缓存-new
     *
     * @return
     */
    public PurResult useCache(String id) {
        PurResult sr = new PurResult(1, "操作成功");
        System.out.println("缓存key=======" + id);
        Object d1 = redisTemplate.opsForValue().get(id);
        if (null == d1 || d1.toString().equals("")) {
            return null;
        }
        //
        System.out.println("缓存信息" + d1.toString());
        if (d1.toString().startsWith("[{")) {
            sr.setData(JSONArray.parseArray(d1.toString()));
        } else {
            JSONObject o = JSONObject.parseObject(d1.toString());
            sr.setData(o);
        }
        return null;
    }

    public void SetCache(String id, Object value) {
        redisTemplate.opsForValue().set(id, value.toString());
        redisTemplate.expire(id, 300, TimeUnit.SECONDS);
    }

    public PurResult querySupplierList2(String area_dict_num, String shop_type) {
        Map<String, Object> params = new HashMap<>();
        params.put("area_dict_num", area_dict_num);
        params.put("shop_type", shop_type);
        List<Map<String, Object>> supplier_list = shoppingDao.querySupplierList2(params);
        List<Map<String, Object>> brand_list = shoppingDao.queryBrandList(params);
        String supid = "querySupplierList2" + area_dict_num + shop_type + "supplier_list";
        String brandid = "querySupplierList2" + area_dict_num + shop_type + "brand_list";
        SetCache(supid, JSON.toJSONString(supplier_list));
        SetCache(brandid, JSON.toJSONString(brand_list));
        PurResult result = new PurResult();
        result.setData(supplier_list);
        result.setCord(brand_list);
        return result;
    }

    public PurResult updateApplyRefund(String order_code, String company_code) {
        PurResult result = new PurResult();
        Map<String, Object> params = new HashMap<>();
        params.put("order_code", order_code);
        params.put("company_code", company_code);
        int i = shoppingDao.updateApplyRefund(params);
        if (i > 0) {
            result.setStatus(1);
            SystemMsgBean bean = new SystemMsgBean();
            bean.setCompany_code(company_code);
            bean.setSysmsg_content("您有一条申请退款单");
            bean.setSource_code(order_code);
            bean.setSysmsg_type("9");
            shoppingDao.addSysmsg(bean);
            String[] staff_list = shoppingDao.getManagerList(params);
            for (String staff_id : staff_list) {
                Map<String, Object> sysmsguser = new HashMap<String, Object>();
                sysmsguser.put("sysmsg_id", bean.getSysmsg_id());
                sysmsguser.put("staffer_id", staff_id);
                shoppingDao.addSysmsgUser(sysmsguser);
            }
        } else {
            result.setStatus(0);
        }
        return result;
    }

    public PurResult cancelApplyRefund(String order_code) {
        PurResult result = new PurResult();
        Map<String, Object> params = new HashMap<>();
        params.put("order_code", order_code);
        int i = shoppingDao.cancelApplyRefund(params);
        if (i > 0) {
            result.setStatus(1);
        } else {
            result.setStatus(0);
        }
        return result;
    }

    public List<Map<String, Object>> queryGoodskindListByYN(String area_dict_num, Object object) {

        List<Map<String, Object>> goodkindList = new ArrayList<Map<String, Object>>();
        Map<String, Object> params = new HashMap<String, Object>();
        goodkindList = goodsKindDao.getGoodskindListByYN(params);
        for (Map<String, Object> map : goodkindList) {
            List<Map<String, Object>> goodkindTwo = goodsKindDao.getGoodskindTwoByYN(map);
            map.put("goodkindTwo", goodkindTwo);
        }
        return goodkindList;
    }

    public PurResult getGoodListYN(Map<String, Object> params) {
        PurResult result = new PurResult();
        try {
            List<Map<String, Object>> list = globalThemeDao.getGoodListYN(params);
            for (Map<String, Object> goodMap : list) {
                if ("".equals(goodMap.get("goods_img")) || goodMap.get("goods_img") == null) {
                    goodMap.put("goods_img", "../static/img/no_goods.jpg");
                } else {
                    goodMap.put("goods_img", "http://file.buyhoo.cc/" + goodMap.get("goods_img"));

                }
            }
            Integer count = globalThemeDao.getGoodListYNCount(params);
            int cartCount = globalThemeDao.queryShoppingCartYNCount(params);
            result.setStatus(1);
            result.setMsg("成功");
            result.setCount(count);
            result.setData(list);
            result.setCord(cartCount);
        } catch (Exception e) {
            e.printStackTrace();
            result.setStatus(0);
            result.setMsg("异常");
        }
        return result;
    }

    public PurResult querySupplierListYN(String area_dict_num, String shop_type) {
        Map<String, Object> params = new HashMap<>();
        params.put("area_dict_num", area_dict_num);
        params.put("shop_type", shop_type);
        List<Map<String, Object>> supplier_list = globalThemeDao.querySupplierListYN(params);
        PurResult result = new PurResult();
        result.setData(supplier_list);
        return result;
    }

    public PurResult insertShoppingCartYN(Map<String, Object> params) {
        PurResult result = new PurResult();
        try {
            params.put("create_time", new Date());
            long cartid = 0l;
            params.put("cartid", cartid);

            globalThemeDao.insertShoppingCartYN(params);
            result.setData(params);
            result.setStatus(1);
            result.setMsg("成功");
        } catch (Exception e) {
            e.printStackTrace();
            result.setStatus(0);
            result.setMsg("异常");
        }
        return result;
    }

    public Object queryShoppingCartYN(String shop_unique, String area_dict_num, Integer shopType) {
        Map<String, Object> params = new HashMap<String, Object>();
        params.put("shop_unique", shop_unique);
        List<Map<String, Object>> list;
        list = globalThemeDao.querySupplierByCartYN(params);
        for (Map<String, Object> map : list) {
            map.put("area_dict_num", area_dict_num);
            List<Map<String, Object>> good_list = globalThemeDao.querySupplierGoodListNewYN(map.get("company_code").toString(), area_dict_num, shop_unique);
            for (Map<String, Object> goodMap : good_list) {
                if ("".equals(goodMap.get("goods_img")) || goodMap.get("goods_img") == null) {
                    goodMap.put("goods_img", "../static/img/no_goods.jpg");
                } else if (goodMap.get("goods_img").toString().indexOf("http") == -1) {
                    goodMap.put("goods_img", "http://file.buyhoo.cc/" + goodMap.get("goods_img").toString());
                }

            }
            map.put("good_list", good_list);
            //商品总金额
            BigDecimal sum_amt = new BigDecimal(map.get("sum_amt").toString());
            map.put("sum_amt", sum_amt);
        }
        return list;
    }

    public PurResult deleteShoppingCartYN(Map<String, Object> params) {
        PurResult result = new PurResult();
        try {
            System.out.println(params.toString() + ">>>>>>>>>");
            globalThemeDao.delete_shopping_cart(params);
            result.setStatus(1);
            result.setMsg("成功");
        } catch (Exception e) {
            e.printStackTrace();
            result.setStatus(0);
            result.setMsg("异常");
        }
        return result;
    }

    public PurResult deleteShoppingCartMoreYN(String[] id) {
        PurResult result = new PurResult();
        try {
            globalThemeDao.delete_shopping_cart_more(id);
            result.setStatus(1);
            result.setMsg("成功");
        } catch (Exception e) {
            e.printStackTrace();
            result.setStatus(0);
            result.setMsg("异常");
        }
        return result;
    }

    public PurResult updateShoppingCartYN(Map<String, Object> params) {
        PurResult result = new PurResult();
        try {
            globalThemeDao.update_shopping_cart(params);
            result.setStatus(1);
            result.setMsg("成功");
        } catch (Exception e) {
            e.printStackTrace();
            result.setStatus(0);
            result.setMsg("异常");
        }
        return result;
    }

    public SettlementVo querySettlementListYN(String[] ids, String shop_unique, String area_dict_num) {
        SettlementVo settle = new SettlementVo();
        Map<String, Object> paramsShop = new HashMap<String, Object>();
        paramsShop.put("shop_unique", shop_unique);
        Map<String, Object> goldMap = supShoppingDao.queryGoldByShop(paramsShop);//店铺金圈币可用数量
        if (goldMap != null) {
            settle.setJqb_count(new BigDecimal(goldMap.get("jqb_count").toString()));
        }
        BigDecimal sum_amt_all = new BigDecimal("0");
        BigDecimal should_amt_all = new BigDecimal("0");
        BigDecimal deduct_amt_all = new BigDecimal("0");
        BigDecimal sum_delivery_price = new BigDecimal("0");
        Integer sum_count = 0;
        BigDecimal temp = settle.getJqb_count();//可用金圈币数量
        List<Map<String, Object>> list = globalThemeDao.querySupplierByCartByIdsYN(ids, shop_unique);
        for (Map<String, Object> map : list) {
            map.put("area_dict_num", area_dict_num);
            //配送费
            BigDecimal delivery_price = new BigDecimal("0");
            List<Map<String, Object>> good_list = globalThemeDao.querySupplierGoodListNewYNByIds(map.get("company_code").toString(), area_dict_num, ids, shop_unique);
            for (Map<String, Object> goodMap : good_list) {
                if ("".equals(goodMap.get("goods_img")) || goodMap.get("goods_img") == null) {
                    goodMap.put("goods_img", "../static/img/no_goods.jpg");
                } else if (goodMap.get("goods_img").toString().indexOf("http") == -1) {
                    goodMap.put("goods_img", "http://file.buyhoo.cc/" + goodMap.get("goods_img").toString());
                }
                delivery_price = delivery_price.add(new BigDecimal(goodMap.get("delivery_price").toString()));

            }
            map.put("good_list", good_list);
            map.put("delivery_price", delivery_price);//总配送费
            //商品总金额
            BigDecimal sum_amt = new BigDecimal(map.get("sum_amt").toString());
            //抵扣金额
            BigDecimal deduct_amt = new BigDecimal(map.get("sum_deduct_amt").toString());
            if (temp.compareTo(deduct_amt) > 0) {
                temp = temp.subtract(deduct_amt);
            } else {
                deduct_amt = temp;
                temp = new BigDecimal("0");
            }
            //应付金额
            BigDecimal should_amt = sum_amt.subtract(deduct_amt);
            BigDecimal actual_delivery_price = delivery_price;//实际配送费
            should_amt = should_amt.add(actual_delivery_price);
            map.put("deduct_amt", deduct_amt);
            map.put("should_amt", should_amt);
            sum_amt_all = sum_amt_all.add(sum_amt);
            should_amt_all = should_amt_all.add(should_amt);
            deduct_amt_all = deduct_amt_all.add(deduct_amt);
            sum_count += Integer.valueOf(map.get("sum_good_count").toString());

            map.put("actual_delivery_price", actual_delivery_price);
            sum_delivery_price = sum_delivery_price.add(actual_delivery_price);
        }
        settle.setDeduct_amt_all(deduct_amt_all);
        settle.setSum_amt_all(sum_amt_all);
        settle.setSum_count(sum_count);
        settle.setSum_delivery_price(sum_delivery_price);
        settle.setSettlementList(list);
        settle.setJqb_max_count(settle.getJqb_count().compareTo(deduct_amt_all) >= 0 ? deduct_amt_all : settle.getJqb_count());//本次可抵扣金圈币个数
        settle.setShould_amt_all(settle.getSum_amt_all().subtract(settle.getJqb_max_count()).add(sum_delivery_price));
        System.out.println(list.toString());
        return settle;
    }

    /**
     * 提交订单
     */
    @Transactional
    public PurResult saveOrderYN(String[] ids, String shop_unique, String oper_id, String gold_amt, String order_amt, String actual_amt, String delivery_price, String[] order_remark, String[] delivery_fee) {
        PurResult result = new PurResult();
        //获取店铺信息
        Map<String, Object> paramsShop = new HashMap<String, Object>();
        paramsShop.put("shop_unique", shop_unique);
        Map<String, Object> shopInfo = shopDao.queryShopMessage(paramsShop);

        String orderid = "T" + new UniqueOrder(0, 0).nextId();
        List<Map<String, Object>> list = globalThemeDao.querySupplierByCartByIdsYN(ids, shop_unique);
        Map<String, Object> goldMap = supShoppingDao.queryGoldByShop(paramsShop);//店铺金圈币可用数量
        BigDecimal temp = new BigDecimal("0");
        if (goldMap != null) {
            temp = new BigDecimal(goldMap.get("jqb_count").toString());//可用金圈币数量
        }
        BigDecimal deduct_amt_all = new BigDecimal("0");
        for (Map<String, Object> map : list) {
            //金圈币抵扣金额
            BigDecimal deduct_amt = new BigDecimal(map.get("sum_deduct_amt").toString());
            if (temp.compareTo(deduct_amt) > 0) {
                temp = temp.subtract(deduct_amt);
            } else {
                deduct_amt = temp;
                temp = new BigDecimal("0");
            }
            deduct_amt_all = deduct_amt_all.add(deduct_amt);
        }
        int i = 0;
        int j = 0;
        List<String> orderCodeList = new ArrayList<String>();

        for (Map<String, Object> map : list) {
            List<Map<String, Object>> good_list = globalThemeDao.querySupplierGoodListNewYNByIds(map.get("company_code").toString(), "", ids, shop_unique);
            BigDecimal sum_amt = new BigDecimal(map.get("sum_amt").toString());//子订单售价
            BigDecimal cost_amt = new BigDecimal(map.get("cost_amt").toString());//子订单成本价
            Map<String, Object> paramsOrder = new HashMap<String, Object>();
            Map<String, Object> tempMap = new HashMap<String,Object>();
            String order_code = SupplierInfoServiceImpl.createOrderNum();

            orderCodeList.add(order_code);

            paramsOrder.put("order_code", order_code);
            paramsOrder.put("company_code", map.get("company_code"));
            
            tempMap.put("sale_list_unique", order_code.substring(2));
            tempMap.put("shop_unique", map.get("company_code"));
            tempMap.put("sale_list_total", sum_amt);
            tempMap.put("sale_list_pur", cost_amt);
            tempMap.put("sale_list_count", map.get("sum_good_count"));
            tempMap.put("cus_unique", "");
            tempMap.put("sale_list_name", shopInfo.get("shop_name"));
            tempMap.put("sale_list_phone", shopInfo.get("shop_phone"));
            tempMap.put("sale_list_address", shopInfo.get("shop_address_detail"));
            tempMap.put("sale_list_state", "2");
            tempMap.put("sale_list_handlestate", "8");
            tempMap.put("sale_list_remark", "益农项目订单");
            tempMap.put("sale_list_actually_received", sum_amt);
            tempMap.put("card_deduction", "0");
            tempMap.put("coupon_amount", "0");
            tempMap.put("beans_use", 0);
            tempMap.put("shop_coupon_id", null);
            tempMap.put("beans_money", "0");
            tempMap.put("delivery_type", "1");
            tempMap.put("addr_latitude", shopInfo.get("shop_latitude"));
            tempMap.put("addr_longitude", shopInfo.get("shop_longitude"));
            
            if (order_remark.length > i) {
                paramsOrder.put("order_remarks", order_remark[i]);
                i++;
            } else {
                paramsOrder.put("order_remarks", "");
            }
            //paramsOrder.put("pay_mode", 1);//支付方式：1、货到付款；2、支付宝；3、微信;4、其他
            paramsOrder.put("pay_status", 1);//支付状态：1、欠款；2、已结清
            paramsOrder.put("order_status", "0");//订单状态  0待付款
            if (new BigDecimal(actual_amt).compareTo(new BigDecimal("0")) == 0) {
                paramsOrder.put("pay_status", 2);
                paramsOrder.put("order_status", "1");
                tempMap.put("sale_list_state", "3");
                tempMap.put("sale_list_handlestate", "2");
                sendMsg(order_code);
            }
            paramsOrder.put("customer_code", shop_unique);//客户编号
            paramsOrder.put("collect_name", MUtil.strObject(shopInfo.get("shop_name")));//收货人姓名
            paramsOrder.put("collect_phone", MUtil.strObject(shopInfo.get("shop_phone")));//联系电话
            paramsOrder.put("collect_address", MUtil.strObject(shopInfo.get("shop_address_detail")));//收货地址
            paramsOrder.put("order_type", 1);//订单类型 0：自动下单；1：客户下单
            paramsOrder.put("order_money", sum_amt);//订单总金额
            paramsOrder.put("real_pay_money", sum_amt);
            paramsOrder.put("shop_latitude", MUtil.strObject(shopInfo.get("shop_latitude")));//店铺纬度
            paramsOrder.put("shop_longitude", MUtil.strObject(shopInfo.get("shop_longitude")));//店铺经度
            paramsOrder.put("present_beans", "0");//满足规则赠送百货豆
            paramsOrder.put("platform_beans", "0");//实际赠送的百货豆
            paramsOrder.put("main_order_no", orderid);
            paramsOrder.put("delivery_fee", delivery_fee[j]);//配送费
            paramsOrder.put("cost_fee", cost_amt);//成本价
            paramsOrder.put("pay_money", new BigDecimal(delivery_fee[j]).add(cost_amt));//成本价+配送费
            //如果计算每个订单的实际金圈币抵扣金额
            if (Double.parseDouble(gold_amt) < deduct_amt_all.doubleValue()) {
                BigDecimal deduct_amt = new BigDecimal(map.get("sum_deduct_amt").toString());
                double shiji_deduct_amt = Double.parseDouble(gold_amt) * (deduct_amt.doubleValue() / deduct_amt_all.doubleValue());
                paramsOrder.put("sum_deduct_amt", BigDecimal.valueOf(shiji_deduct_amt).setScale(2, BigDecimal.ROUND_HALF_UP));
            } else {
                if (Double.parseDouble(gold_amt) <= 0) {
                    paramsOrder.put("sum_deduct_amt", 0);
                } else {
                    paramsOrder.put("sum_deduct_amt", map.get("sum_deduct_amt"));
                }
            }
            globalThemeDao.insertOrder(paramsOrder);
            
            Object yanshiStatus = rc.getObject("yanshiStatus");
            
            j++;
            //添加订单详情表
            Map<String, Object> paramsOrderdetail = new HashMap<String, Object>();
            paramsOrderdetail.put("order_code", order_code);

            //添加商品详情(购物车表当做商品详情表)
            List<Map<String,Object>> goodslist = new ArrayList<Map<String,Object>>();
            for (Map<String, Object> cart : good_list) {
                cart.put("is_order", 1);
                cart.put("order_no", orderid);
                globalThemeDao.update_shopping_cart(cart);
                paramsOrderdetail.put("goods_code", cart.get("goods_barcode"));
                paramsOrderdetail.put("goods_cost", cart.get("supply_price"));
                paramsOrderdetail.put("goods_price", cart.get("online_price"));
                paramsOrderdetail.put("real_price", cart.get("online_price"));
                paramsOrderdetail.put("goods_name", cart.get("goods_name"));
                paramsOrderdetail.put("goods_unit", cart.get("goodsunit_name"));
                paramsOrderdetail.put("goods_id", cart.get("good_id"));
                paramsOrderdetail.put("fact_price", cart.get("sum_amt"));
                paramsOrderdetail.put("goods_count", cart.get("good_count"));
                paramsOrderdetail.put("auto_fxiaoshou", cart.get("auto_fxiaoshou"));
                paramsOrderdetail.put("promotion_id", cart.get("promotion_id"));
                paramsOrderdetail.put("spec_name", cart.get("spec_name"));
                globalThemeDao.insertOrderDetail(paramsOrderdetail);
                
                Map<String,Object> goodsMap = new HashMap<String,Object>();
                goodsMap.put("sale_list_unique", order_code.substring(2));
                goodsMap.put("goods_barcode", cart.get("goods_barcode"));
                goodsMap.put("goods_name", cart.get("goods_name"));
                goodsMap.put("detail_count", cart.get("good_count"));
                goodsMap.put("sale_list_detail_price", cart.get("online_price"));
                goodsMap.put("goods_purprice", cart.get("supply_price"));
                goodsMap.put("goods_picturepath", cart.get("goods_img"));
                
                goodslist.add(goodsMap);
            }
            
            System.out.println("当前是否演示状态" + yanshiStatus);
            if(null != yanshiStatus && yanshiStatus.toString().equals("1")) {
            	globalThemeDao.addNewSaleList(tempMap);
            	globalThemeDao.addNewSaleListDetail(goodslist);
            }
        }
        //添加主订单
        paramsShop.put("main_order_no", orderid);
        paramsShop.put("gold_amt", gold_amt);
        paramsShop.put("order_amt", order_amt);
        paramsShop.put("actual_amt", actual_amt);
        paramsShop.put("delivery_price", delivery_price);
        paramsShop.put("oper_id", MUtil.strObject(shopInfo.get("shop_name")));
        paramsShop.put("shop_unique", shop_unique);
        globalThemeDao.insertOrderMain(paramsShop);
        paramsShop.put("total_fee", actual_amt);

        //店铺金圈币余额减少
        if (!"0".equals(gold_amt.replace("\n", "").replace("\t", "").replace(" ", ""))) {
            int num = supShoppingDao.updateShopGold(paramsShop);
            if (num == 0) {
                TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
                result.setStatus(0);
                result.setMsg("金圈币不足,请重试!");
                return result;
            }
        }


        result.setStatus(1);
        result.setData(paramsShop);
        result.setMsg("提交订单成功！");
        return result;
    }

    public void updateShoppingOrderYN(Map<String, Object> params) {
        globalThemeDao.updateShoppingOrderYN(params);

    }

    public PurResult queryYNOrderStatus(Map<String, Object> params) {
        PurResult result = new PurResult();
        Map<String, Object> data = globalThemeDao.queryYNOrderStatus(params);
        result.setData(data);
        return result;
    }

    public PurResult getMyOrderListYN(Map<String, Object> params) {
        PurResult result = new PurResult();
        try {
            System.out.println(params.toString());
            List<Map<String, Object>> list = globalThemeDao.getMyOrderList(params);
            System.out.println(list.toString());
            Integer count = globalThemeDao.getMyOrderListCount(params);
            result.setStatus(1);
            result.setMsg("成功");
            result.setCount(count);
            result.setData(list);
        } catch (Exception e) {
            e.printStackTrace();
            result.setStatus(0);
            result.setMsg("异常");
        }
        return result;
    }

    public PurResult sendYNOrder(Map<String, Object> params) {
        PurResult result = new PurResult();
        globalThemeDao.sendYNOrder(params);
        result.setStatus(1);
        return result;
    }

    @Transactional
    public PurResult confirmYNOrder(Map<String, Object> params) {
        PurResult result = new PurResult();
        globalThemeDao.confirmYNOrder(params);
        //查询供货商应该得到的金额
        Map<String, Object> order = globalThemeDao.querySupAddMoney(params);
        globalThemeDao.addShopBalance(order);
        result.setStatus(1);
        return result;
    }

    public SettlementVo queryOrderDetailYN(String main_order_no, Integer shopType, String shop_unique, String company_code) {
        SettlementVo settle = new SettlementVo();
        Map<String, Object> params = new HashMap<String, Object>();
        params.put("main_order_no", main_order_no);
        params.put("shop_unique", shop_unique);
        if (company_code != null && !"".equals(company_code) && !"null".equals(company_code)) {
            params.put("company_code", company_code);
        }
        List<Map<String, Object>> list = globalThemeDao.queryOrderDetail(params);
        Integer sum_count = 0;
        for (Map<String, Object> map : list) {
            map.put("main_order_no", main_order_no);
            map.put("shop_unique", shop_unique);
            List<Map<String, Object>> good_list;
            good_list = globalThemeDao.queryOrderDetailGoodList(map);
            BigDecimal sum = new BigDecimal("0");
            for (Map<String, Object> goodMap : good_list) {
                if ("".equals(goodMap.get("goods_img"))) {
                    goodMap.put("goods_img", "../static/img/no_goods.jpg");
                } else if (goodMap.get("goods_img").toString().indexOf("http") == -1) {
                    goodMap.put("goods_img", "http://file.buyhoo.cc/" + goodMap.get("goods_img").toString());
                }
                sum = sum.add(new BigDecimal(goodMap.get("sum_amt").toString()));
            }
            sum_count += Integer.valueOf(map.get("sum_good_count").toString());
            map.put("sum_amt", sum.setScale(2, BigDecimal.ROUND_HALF_UP).doubleValue());
            map.put("good_list", good_list);
        }
        Map<String, Object> orderMain = globalThemeDao.querySupOrderMain(params);
        settle.setDeduct_amt_all(orderMain.get("gold_amt") != null ? new BigDecimal(orderMain.get("gold_amt").toString()) : new BigDecimal("0"));
        settle.setSum_amt_all(orderMain.get("order_amt") != null ? new BigDecimal(orderMain.get("order_amt").toString()) : new BigDecimal("0"));
        settle.setShould_amt_all(orderMain.get("actual_amt") != null ? new BigDecimal(orderMain.get("actual_amt").toString()) : new BigDecimal("0"));
        settle.setSettlementList(list);
        settle.setSum_delivery_price(orderMain.get("delivery_price") != null ? new BigDecimal(orderMain.get("delivery_price").toString()) : new BigDecimal("0"));
        settle.setSum_count(sum_count);
        return settle;
    }

    @Transactional
    public PurResult cancelOrderYN(String main_order_no) {
        PurResult result = new PurResult();
        //取消订单  修改订单状态
        Map<String, Object> params = new HashMap<String, Object>();
        params.put("main_order_no", main_order_no);
        params.put("order_status", "5");
        globalThemeDao.updateShoppingOrderYN(params);
        //店铺金圈币返还
        Map<String, Object> orderMain = globalThemeDao.querySupOrderMain(params);//主订单表
        params.put("shop_unique", orderMain.get("shop_unique"));
        params.put("gold_amt", orderMain.get("gold_amt"));
        supShoppingDao.addShopGold(params);
        result.setStatus(1);
        result.setMsg("该笔订单已经取消！");
        return result;
    }

    public Object queryOrderDetailYNSup(String main_order_no, Integer shopType, String shop_unique, String company_code) {
        SettlementVo settle = new SettlementVo();
        Map<String, Object> params = new HashMap<String, Object>();
        params.put("main_order_no", main_order_no);
        params.put("shop_unique", shop_unique);
        if (company_code != null && !"".equals(company_code) && !"null".equals(company_code)) {
            params.put("company_code", company_code);
        }
        List<Map<String, Object>> list = globalThemeDao.queryOrderDetail(params);
        Integer sum_count = 0;
        for (Map<String, Object> map : list) {
            map.put("main_order_no", main_order_no);
            map.put("shop_unique", shop_unique);
            List<Map<String, Object>> good_list;
            good_list = globalThemeDao.queryOrderDetailGoodList(map);
            BigDecimal sum = new BigDecimal("0");
            for (Map<String, Object> goodMap : good_list) {
                if ("".equals(goodMap.get("goods_img"))) {
                    goodMap.put("goods_img", "../static/img/no_goods.jpg");
                } else if (goodMap.get("goods_img").toString().indexOf("http") == -1) {
                    goodMap.put("goods_img", "http://file.buyhoo.cc/" + goodMap.get("goods_img").toString());
                }
                sum = sum.add(new BigDecimal(goodMap.get("sum_amt2").toString()));
            }
            sum_count += Integer.valueOf(map.get("sum_good_count").toString());
            map.put("sum_amt", sum.setScale(2, BigDecimal.ROUND_HALF_UP).doubleValue());
            map.put("good_list", good_list);
        }
        Map<String, Object> orderMain = globalThemeDao.queryOrderInfo(params);
        settle.setDeduct_amt_all(orderMain.get("gold_amt") != null ? new BigDecimal(orderMain.get("gold_amt").toString()) : new BigDecimal("0"));
        settle.setSum_amt_all(orderMain.get("order_amt") != null ? new BigDecimal(orderMain.get("order_amt").toString()) : new BigDecimal("0"));
        settle.setShould_amt_all(orderMain.get("pay_money") != null ? new BigDecimal(orderMain.get("pay_money").toString()) : new BigDecimal("0"));
        settle.setSettlementList(list);
        settle.setSum_delivery_price(orderMain.get("delivery_fee") != null ? new BigDecimal(orderMain.get("delivery_fee").toString()) : new BigDecimal("0"));
        settle.setSum_count(sum_count);
        return settle;
    }


    public PurResult cycleFrequency(Map<String, Object> oc) {
        PurResult result = new PurResult();
        try {
            String cycle = oc.get("cycle").toString();//限购周期
            String frequency = oc.get("frequency").toString();//限购次数
            //判断限购周期与与限购次数
            String start_date = "";
            String end_date = "";
            if (cycle.equals("每天")) {
                start_date = TimeDateUtil.getCurrentDayStartTime();
                end_date = TimeDateUtil.getCurrentDayEndTime();
                oc.put("start_date", start_date);
                oc.put("end_date", end_date);
            } else if (cycle.equals("每周")) {
                start_date = TimeDateUtil.getCurrentWeekDayStartTime();
                end_date = TimeDateUtil.getCurrentWeekDayEndTime();
                oc.put("start_date", start_date);
                oc.put("end_date", end_date);
            } else if (cycle.equals("每月")) {
                start_date = TimeDateUtil.getCurrentMonthStartTime();
                end_date = TimeDateUtil.getCurrentMonthEndTime();
                oc.put("start_date", start_date);
                oc.put("end_date", end_date);
            } else if (cycle.equals("每季度")) {
                start_date = TimeDateUtil.getCurrentQuarterStartTime();
                end_date = TimeDateUtil.getCurrentQuarterEndTime();
                oc.put("start_date", start_date);
                oc.put("end_date", end_date);
            } else if (cycle.equals("每年")) {
                start_date = TimeDateUtil.getCurrentYearStartTime();
                end_date = TimeDateUtil.getCurrentYearEndTime();
                oc.put("start_date", start_date);
                oc.put("end_date", end_date);
            }
            int fre = shoppingDao.getGoodsOrderCount(oc);
            int frequency1 = Integer.parseInt(frequency);
            if (frequency1 <= fre) {
                result.setStatus(0);
                result.setMsg("购买失败，该产品" + cycle + "限购" + frequency1 + "次！");
                return result;
            }
        } catch (Exception e) {
            e.printStackTrace();
            result.setStatus(0);
            result.setMsg("异常");
        }
        result.setStatus(1);
        return result;

    }

    /**
     * 捆绑产品立即购买
     *
     * @param ids
     * @param shop_unique
     * @param area_dict_num
     * @return
     */
    public SettlementVo querySettlementBindingList(String[] ids, String shop_unique, String area_dict_num) {
        SettlementVo settle = new SettlementVo();
        Map<String, Object> paramsShop = new HashMap<String, Object>();
        paramsShop.put("shop_unique", shop_unique);
        Map<String, Object> goldMap = supShoppingDao.queryGoldByShop(paramsShop);//店铺金圈币可用数量
        if (goldMap != null) {
            settle.setJqb_count(new BigDecimal(goldMap.get("jqb_count").toString()));
        }
        BigDecimal sum_amt_all = new BigDecimal("0");
        BigDecimal should_amt_all = new BigDecimal("0");
        BigDecimal deduct_amt_all = new BigDecimal("0");
        BigDecimal sum_delivery_price = new BigDecimal("0");
        //BigDecimal loan_amt_all = new BigDecimal("0");
        Integer sum_count = 0;
        BigDecimal temp = settle.getJqb_count();//可用金圈币数量
        List<Map<String, Object>> list = shoppingDao.querySupplierByBinding(ids, shop_unique);
        for (Map<String, Object> map : list) {
            map.put("area_dict_num", area_dict_num);
            //配送费
            Map<String, Object> deliveryPrice = shoppingDao.getDeliveryPrice(map);
            map.put("free_delivery_price", deliveryPrice.get("free_delivery_price").toString());//订单免配送费价格
            BigDecimal delivery_price = new BigDecimal("0");
            if ("2".equals(deliveryPrice.get("delivery_price_type").toString())) {//按订单配送
                delivery_price = new BigDecimal(deliveryPrice.get("delivery_price").toString());
            }
            List<Map<String, Object>> good_list = shoppingDao.querySupplierGoodBindingList(map.get("company_code").toString(), area_dict_num, ids, shop_unique);
            for (Map<String, Object> goodMap : good_list) {
                if ("".equals(goodMap.get("goods_img"))) {
                    goodMap.put("goods_img", "../static/img/no_goods.jpg");
                } else if (goodMap.get("goods_img").toString().indexOf("http") == -1) {
                    goodMap.put("goods_img", "http://file.buyhoo.cc/" + goodMap.get("goods_img").toString());
                }
                if ("1".equals(deliveryPrice.get("delivery_price_type").toString())) {//按件数
                    delivery_price = delivery_price.add(new BigDecimal(goodMap.get("deliveryPrice").toString()).multiply(new BigDecimal(goodMap.get("start_order").toString())));
                }

            }
            map.put("good_list", good_list);
            map.put("delivery_price", delivery_price);//总配送费
            //商品总金额
            BigDecimal sum_amt = new BigDecimal(map.get("sum_amt").toString());
            //贷款总金额
            //BigDecimal loan_amt =new BigDecimal( map.get("loan_amt").toString());
            //抵扣金额
            BigDecimal deduct_amt = new BigDecimal(map.get("sum_deduct_amt").toString());
            if (temp.compareTo(deduct_amt) > 0) {
                temp = temp.subtract(deduct_amt);
            } else {
                deduct_amt = temp;
                temp = new BigDecimal("0");
            }
            //应付金额
            BigDecimal should_amt = sum_amt.subtract(deduct_amt);
            BigDecimal actual_delivery_price = should_amt.compareTo(new BigDecimal(deliveryPrice.get("free_delivery_price").toString())) >= 0 ? new BigDecimal("0") : delivery_price;//实际配送费
            should_amt = should_amt.add(actual_delivery_price);

            //如果优惠金额大于实际需要支付的金额，减少赊销金额
			/*if(should_amt.compareTo(new BigDecimal(0)) < 0) {
				loan_amt = loan_amt.add(should_amt);
				should_amt = should_amt.subtract(should_amt);
				map.put("loan_amt", loan_amt);
			}*/

            map.put("deduct_amt", deduct_amt);
            map.put("should_amt", should_amt);
            sum_amt_all = sum_amt_all.add(sum_amt);
            should_amt_all = should_amt_all.add(should_amt);
            deduct_amt_all = deduct_amt_all.add(deduct_amt);
            sum_count += Integer.valueOf(map.get("sum_good_count").toString());

            map.put("actual_delivery_price", actual_delivery_price);
            sum_delivery_price = sum_delivery_price.add(actual_delivery_price);
            //loan_amt_all = loan_amt_all.add(loan_amt);
        }
        settle.setDeduct_amt_all(deduct_amt_all);
        //settle.setLoan_amt_all(loan_amt_all);
        settle.setSum_amt_all(sum_amt_all);
        settle.setSum_count(sum_count);
        settle.setSum_delivery_price(sum_delivery_price);
        settle.setSettlementList(list);
        settle.setJqb_max_count(settle.getJqb_count().compareTo(deduct_amt_all) >= 0 ? deduct_amt_all : settle.getJqb_count());//本次可抵扣金圈币个数
        settle.setShould_amt_all(should_amt_all);
        return settle;
    }

    /**
     * 提交捆绑订单
     */
    @Transactional
    public PurResult saveOrderBinding(String[] ids, String shop_unique, String oper_id, String gold_amt, String order_amt, String actual_amt, String good_id, String spec_id, String spec_name, String good_count, String delivery_price, String[] order_remark, String[] delivery_fee, Integer sxRuleId) {
        PurResult result = new PurResult();
        //获取店铺信息
        Map<String, Object> paramsShop = new HashMap<String, Object>();
        paramsShop.put("shop_unique", shop_unique);
        Map<String, Object> shopInfo = shopDao.queryShopMessage(paramsShop);

        String orderid = "T" + new UniqueOrder(0, 0).nextId();
        List<Map<String, Object>> list = shoppingDao.querySupplierByBinding(ids, shop_unique);
        Map<String, Object> goldMap = supShoppingDao.queryGoldByShop(paramsShop);//店铺金圈币可用数量
        BigDecimal temp = new BigDecimal("0");
        if (goldMap != null) {
            temp = new BigDecimal(goldMap.get("jqb_count").toString());//可用金圈币数量
        }
        BigDecimal deduct_amt_all = new BigDecimal("0");
        for (Map<String, Object> map : list) {
            //金圈币抵扣金额
            BigDecimal deduct_amt = new BigDecimal(map.get("sum_deduct_amt").toString());
            if (temp.compareTo(deduct_amt) > 0) {
                temp = temp.subtract(deduct_amt);
            } else {
                deduct_amt = temp;
                temp = new BigDecimal("0");
            }
            deduct_amt_all = deduct_amt_all.add(deduct_amt);
        }
        int i = 0;
        int j = 0;
        for (Map<String, Object> map : list) {
            List<Map<String, Object>> good_list = shoppingDao.querySupplierGoodBindingList2(map.get("company_code").toString(), ids, shop_unique);
            BigDecimal sum_amt = new BigDecimal(map.get("sum_amt").toString());//子订单售价
            BigDecimal cost_amt = new BigDecimal(map.get("cost_amt").toString());//子订单成本价
            Map<String, Object> paramsOrder = new HashMap<String, Object>();
            String order_code = SupplierInfoServiceImpl.createOrderNum();
            paramsOrder.put("order_code", order_code);
            paramsOrder.put("company_code", map.get("company_code"));
            if (order_remark.length > i) {
                paramsOrder.put("order_remarks", order_remark[i]);
                i++;
            } else {
                paramsOrder.put("order_remarks", "");
            }
            //paramsOrder.put("pay_mode", 1);//支付方式：1、货到付款；2、支付宝；3、微信;4、其他
            paramsOrder.put("pay_status", 1);//支付状态：1、欠款；2、已结清
            paramsOrder.put("order_status", "0");//订单状态  0待付款
            if (new BigDecimal(actual_amt).compareTo(new BigDecimal("0")) == 0) {
                paramsOrder.put("pay_status", 2);
                paramsOrder.put("order_status", "1");
                sendMsg(order_code);
            }
            paramsOrder.put("customer_code", shop_unique);//客户编号
            paramsOrder.put("collect_name", MUtil.strObject(shopInfo.get("shop_name")));//收货人姓名
            paramsOrder.put("collect_phone", MUtil.strObject(shopInfo.get("shop_phone")));//联系电话
            paramsOrder.put("collect_address", MUtil.strObject(shopInfo.get("shop_address_detail")));//收货地址
            paramsOrder.put("order_type", 1);//订单类型 0：自动下单；1：客户下单
            paramsOrder.put("order_money", sum_amt);//订单总金额


            paramsOrder.put("shop_latitude", MUtil.strObject(shopInfo.get("shop_latitude")));//店铺纬度
            paramsOrder.put("shop_longitude", MUtil.strObject(shopInfo.get("shop_longitude")));//店铺经度
            paramsOrder.put("present_beans", "0");//满足规则赠送百货豆
            paramsOrder.put("platform_beans", "0");//实际赠送的百货豆
            paramsOrder.put("main_order_no", orderid);
            paramsOrder.put("delivery_fee", delivery_fee[j]);//配送费
            paramsOrder.put("cost_fee", cost_amt);//成本价
            paramsOrder.put("pay_money", new BigDecimal(delivery_fee[j]).add(cost_amt));//成本价+配送费
            //如果计算每个订单的实际金圈币抵扣金额
            if (Double.parseDouble(gold_amt) < deduct_amt_all.doubleValue()) {
                BigDecimal deduct_amt = new BigDecimal(map.get("sum_deduct_amt").toString());
                double shiji_deduct_amt = Double.parseDouble(gold_amt) * (deduct_amt.doubleValue() / deduct_amt_all.doubleValue());
                paramsOrder.put("sum_deduct_amt", BigDecimal.valueOf(shiji_deduct_amt).setScale(2, BigDecimal.ROUND_HALF_UP));
            } else {
                if (Double.parseDouble(gold_amt) <= 0) {
                    paramsOrder.put("sum_deduct_amt", 0);
                } else {
                    paramsOrder.put("sum_deduct_amt", map.get("sum_deduct_amt"));
                }
            }

            //商品借款额度需要查看优惠信息，如果金圈币抵扣金额大于运费和非借款额度总和，则减少借款金额，优先使用金圈币
            BigDecimal sum_deduct_amt = new BigDecimal(paramsOrder.get("sum_deduct_amt").toString());
            BigDecimal realPayMoney = sum_amt.add(new BigDecimal(delivery_fee[j]).subtract(sum_deduct_amt));
            if (realPayMoney.compareTo(new BigDecimal(0)) < 0) {
                //实际支付的金额加上运费，扣除优惠金额小于0，则应减少赊销金额
                //paramsOrder.put("loan_money", loan_amt.add(sum_amt).subtract(sum_deduct_amt).add(new BigDecimal(delivery_fee[j])).setScale(2,BigDecimal.ROUND_HALF_UP));
                //计算实际支付金额
                paramsOrder.put("real_pay_money", 0);
            } else {
                paramsOrder.put("real_pay_money", realPayMoney);
            }

            supplierInfoDao.insertOrder(paramsOrder);
            j++;
            //添加订单详情表
            Map<String, Object> paramsOrderdetail = new HashMap<String, Object>();
            paramsOrderdetail.put("order_code", order_code);

            //添加商品详情(购物车表当做商品详情表)
            for (Map<String, Object> detail : good_list) {
                //添加购物车(购物车表当做商品详情表)
                Map<String, Object> cart = new HashMap<String, Object>();
                //添加订单详情规则表
                Map<String, Object> paramsOrderSpec = new HashMap<String, Object>();
                //判断是否是捆绑主商品
                if ((detail.get("goods_id").toString()).equals(good_id)) {
                    cart.put("good_id", good_id);
                    cart.put("spec_id", spec_id);
                    cart.put("spec_name", spec_name);
                    cart.put("good_count", good_count);
                    paramsOrderdetail.put("goods_count", good_count);
                    paramsOrderSpec.put("spec_name", spec_name);
                    paramsOrderSpec.put("compose_specs_id", spec_id);
                } else {
                    cart.put("good_id", detail.get("goods_id"));
                    cart.put("spec_id", detail.get("spec_id"));
                    cart.put("spec_name", detail.get("spec_name"));
                    cart.put("good_count", detail.get("start_order"));
                    paramsOrderdetail.put("goods_count", detail.get("start_order"));
                    paramsOrderSpec.put("spec_name", detail.get("spec_name"));
                    paramsOrderSpec.put("compose_specs_id", detail.get("spec_id"));
                }
                cart.put("shop_unique", shop_unique);
                cart.put("create_time", new Date());
                cart.put("company_code", map.get("company_code"));
                cart.put("is_order", 1);
                cart.put("order_no", orderid);

                shoppingDao.insertShoppingCart(cart);


                paramsOrderdetail.put("goods_code", detail.get("goods_barcode"));
                paramsOrderdetail.put("goods_cost", detail.get("supply_price"));
                paramsOrderdetail.put("goods_price", detail.get("onlinePrice"));
                paramsOrderdetail.put("real_price", detail.get("onlinePrice"));
                paramsOrderdetail.put("goods_name", detail.get("goods_name"));
                paramsOrderdetail.put("goods_unit", detail.get("goodsunit_name"));
                paramsOrderdetail.put("goods_id", detail.get("goods_id"));
                paramsOrderdetail.put("fact_price", detail.get("sum_amt"));
                paramsOrderdetail.put("auto_fxiaoshou", detail.get("auto_fxiaoshou"));
                paramsOrderdetail.put("promotion_id", detail.get("promotion_id"));
                //订单明细
                supplierInfoDao.insertOrderDetail(paramsOrderdetail);
                Integer orderdetail_id = Integer.parseInt(MUtil.strObject(paramsOrderdetail.get("orderdetail_id")));

                //添加订单详情规则表
                paramsOrderSpec.put("orderdetail_id", orderdetail_id);
                supplierInfoDao.insertOrderGoodsSpec(paramsOrderSpec);
            }
        }
        //添加主订单
        paramsShop.put("main_order_no", orderid);
        paramsShop.put("gold_amt", gold_amt);
        paramsShop.put("order_amt", order_amt);
        paramsShop.put("actual_amt", actual_amt);
        paramsShop.put("delivery_price", delivery_price);
        paramsShop.put("oper_id", oper_id);
        paramsShop.put("shop_unique", shop_unique);
        shoppingDao.insertOrderMain(paramsShop);
        paramsShop.put("total_fee", new BigDecimal(actual_amt).multiply(new BigDecimal("100")));

        //店铺金圈币余额减少
        if (!"0".equals(gold_amt.replace("\n", "").replace("\t", "").replace(" ", ""))) {
            int num = supShoppingDao.updateShopGold(paramsShop);
            if (num == 0) {
                TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
                result.setStatus(0);
                result.setMsg("金圈币不足,请重试!");
                return result;
            }
        }

        result.setStatus(1);
        result.setData(paramsShop);
        result.setMsg("提交订单成功！");
        return result;
    }

    //领取优惠券
    public PurResult record(Map<String, Object> map) {
        PurResult result = new PurResult();
        try {
            //限制数量
            Map<String, Object> cou = shoppingDao.getOneCoupon(map);
            Integer limit_quantity_type = Integer.valueOf(cou.get("limit_quantity_type").toString());
            Integer total_distribution = Integer.valueOf(cou.get("total_distribution").toString());
            String couponId = cou.get("coupon_id").toString();
            Map<String, Object> re = new HashMap<String, Object>();
            re.put("coupon_id", couponId);
            re.put("couponId", couponId);
            //优惠券是否已领取
            re.put("staffer_login", map.get("shopUnique").toString());
            int recc = shoppingDao.getRecCoupon(re);
            if (recc > 0) {
                result.setStatus(0);
                result.setMsg("优惠券已领取,请刷新！");
            } else {
                if (limit_quantity_type == 2) {
                    int reCount = shoppingDao.queryShoppingCouponCount(re);
                    if (reCount >= total_distribution) {
                        result.setStatus(0);
                        result.setMsg("优惠券已领完！");
                    } else {
                        int r = shoppingDao.record(map);
                        if (r > 0) {
                            List<Map<String, Object>> list = new ArrayList<Map<String, Object>>();
                            list.add(re);
                            int uc = shoppingDao.upCoupon(list);
                            if (uc > 0) {
                                result.setStatus(1);
                                result.setMsg("成功");
                            } else {
                                result.setStatus(0);
                                result.setMsg("数量修改错误！");
                            }
                            result.setStatus(1);
                            result.setMsg("优惠券领取成功");
                        } else {
                            result.setStatus(0);
                            result.setMsg("优惠券领取失败！");
                        }

                    }
                } else {
                    int rec = shoppingDao.record(map);
                    if (rec > 0) {
                        List<Map<String, Object>> list = new ArrayList<Map<String, Object>>();
                        list.add(re);
                        int uc = shoppingDao.upCoupon(list);
                        if (uc > 0) {
                            result.setStatus(1);
                            result.setMsg("成功");
                        } else {
                            result.setStatus(0);
                            result.setMsg("数量修改错误！");
                        }
                        result.setStatus(1);
                        result.setMsg("优惠券领取成功");
                    } else {
                        result.setStatus(0);
                        result.setMsg("优惠券领取失败！");
                    }

                }
            }

        } catch (Exception e) {
            e.printStackTrace();
            result.setStatus(0);
            result.setMsg("异常");
        }
        return result;
    }

    //领取优惠券
    public PurResult recordAll(Map<String, Object> map) {
        PurResult result = new PurResult();
        try {
            //System.out.println(map);
            String[] id = map.get("ids").toString().split(",");
            List<Map<String, Object>> list = new ArrayList<Map<String, Object>>();
            List<Map<String, Object>> uplist = new ArrayList<Map<String, Object>>();
            for (int i = 0; i < id.length; i++) {
                //限制数量
                Map<String, Object> re = new HashMap<String, Object>();
                re.put("couponId", id[i]);
                re.put("coupon_id", id[i]);
                //优惠券是否已领取
                re.put("staffer_login", map.get("shopUnique").toString());
                int recc = shoppingDao.getRecCoupon(re);

                if (recc > 0) {
                    result.setStatus(0);
                    result.setMsg("优惠券已领取,请刷新！");
                } else {
                    Map<String, Object> cou = shoppingDao.getOneCoupon(re);
                    Integer limit_quantity_type = Integer.valueOf(cou.get("limit_quantity_type").toString());
                    Integer total_distribution = Integer.valueOf(cou.get("total_distribution").toString());
                    if (limit_quantity_type == 2) {
                        int reCount = shoppingDao.queryShoppingCouponCount(re);
                        if (reCount < total_distribution) {
                            Map<String, Object> recordMap = new HashMap<String, Object>();
                            recordMap.put("couponId", id[i]);
                            recordMap.put("shopUnique", map.get("shopUnique").toString());
                            recordMap.put("remark", map.get("remark").toString());
                            list.add(recordMap);
                            uplist.add(recordMap);
                        }
                    } else {
                        Map<String, Object> recordMap = new HashMap<String, Object>();
                        recordMap.put("couponId", id[i]);
                        recordMap.put("shopUnique", map.get("shopUnique").toString());
                        recordMap.put("remark", map.get("remark").toString());
                        list.add(recordMap);
                    }
                }
            }
            if (list.size() > 0) {
                int ra = shoppingDao.recordAll(list);
                if (ra > 0) {
                    if (uplist != null && !uplist.isEmpty()) {
                        int uc = shoppingDao.upCoupon(uplist);
                        if (uc > 0) {
                            result.setStatus(1);
                            result.setMsg("领取成功！");
                        } else {
                            result.setStatus(0);
                            result.setMsg("优惠券领取成功，剩余数量修改失败！");
                        }
                    } else {
                        result.setStatus(1);
                        result.setMsg("领取成功！");
                    }

                } else {
                    result.setStatus(0);
                    result.setMsg("领取失败！");
                }
            } else {
                result.setStatus(0);
                result.setMsg("领取失败！");
            }


        } catch (Exception e) {
            e.printStackTrace();
            result.setStatus(0);
            result.setMsg("异常");
        }
        return result;
    }

    //优惠券
//	public List<Map<String, Object>> getRecCoupon(Map<String, Object> map) {
//		List<Map<String,Object>> list = shoppingDao.getRecCoupon(map);
//		//System.out.println(list);
//		return 	list;
//	}

}
