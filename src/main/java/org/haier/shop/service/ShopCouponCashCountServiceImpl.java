package org.haier.shop.service;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;

import org.haier.shop.dao.ShopCouponCashCountDao;
import org.haier.shop.util.DateUtils;
import org.haier.shop.util.PurResult;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;


@Service("shopCouponCashCountService")
public class ShopCouponCashCountServiceImpl implements ShopCouponCashCountService{
	
	@Resource
	private ShopCouponCashCountDao shopCouponCashCountDao;
	

	@Override
	public PurResult queryShopCashBalanceInfo(String shop_unique) {
		PurResult pr=new PurResult();
		Map<String,Object> map=new HashMap<String, Object>();
		if("8302016134121".equals(shop_unique)){
			//平台
		}else{
			//店铺
			map.put("shop_unique", shop_unique);
		}
		//查询所有店铺的余额
		Map<String,Object> allBalanceMap= shopCouponCashCountDao.queryAllShopBalanceCount(map);
		//查询已提现金额
		Map<String,Object> alreadyCashMap= shopCouponCashCountDao.queryAllShopAlreadyCashCount(map);
		allBalanceMap.put("take_money", alreadyCashMap.get("take_money"));
		//查询待处理金额
		Map<String,Object> waitCashMap= shopCouponCashCountDao.queryAllShopWaitCashCount(map);
		allBalanceMap.put("wait_money", waitCashMap.get("wait_money"));
		pr.setData(allBalanceMap);
		pr.setStatus(1);
		pr.setMsg("查询成功！");
		return pr;
	}


	@Override
	public PurResult queryShopOrderCountInfo(String shop_unique, String type) {
		SimpleDateFormat df = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");//设置日期格式
		System.out.println(df.format(new Date()));// new Date()为获取当前系统时间
		PurResult pr=new PurResult();
		Map<String,Object> map=new HashMap<String, Object>();
		map.put("type", type);
		if("8302016134121".equals(shop_unique)){
			//平台
		}else{
			//店铺
			map.put("shop_unique", shop_unique);
		}
		//查询订单的统计信息
		Map<String,Object> orderMap=shopCouponCashCountDao.queryShopOrderInfo(map);
		System.out.println(df.format(new Date()));// new Date()为获取当前系统时间
		
		//查询平台优惠券金额
		Map<String,Object> platformCouponMap=shopCouponCashCountDao.queryShopOrderPlatformCouponInfo(map);
		System.out.println(df.format(new Date()));// new Date()为获取当前系统时间
		orderMap.put("coupon_amount",platformCouponMap.get("coupon_amount"));
		//查询店铺优惠券金额
		Map<String,Object> shopCouponMap=shopCouponCashCountDao.queryShopOrderShopCouponInfo(map);
		System.out.println(df.format(new Date()));// new Date()为获取当前系统时间
		orderMap.put("shop_coupon_amount",shopCouponMap.get("shop_coupon_amount"));
		//查询线上订单数量
		Map<String,Object> orderCountMap=shopCouponCashCountDao.queryShopOrderCountInfo(map);
		System.out.println(df.format(new Date()));// new Date()为获取当前系统时间
		orderMap.put("orderCount",orderCountMap.get("orderCount"));
		//查询百货豆抵扣订单数量
		Map<String,Object> beansCountMap=shopCouponCashCountDao.queryShopBeansCountInfo(map);
		System.out.println(df.format(new Date()));// new Date()为获取当前系统时间
		orderMap.put("beansCount",beansCountMap.get("beansCount"));
		//查询店铺积分订单数量
		Map<String,Object> pointCountMap=shopCouponCashCountDao.queryShopPointCountInfo(map);
		System.out.println(df.format(new Date()));// new Date()为获取当前系统时间
		orderMap.put("pointCount",pointCountMap.get("pointCount"));
		//查询订单外送总金额
		Map<String,Object> queryShopOutSend = shopCouponCashCountDao.queryShopOutSend(map);
		orderMap.put("money_amount", queryShopOutSend == null ? 0 : queryShopOutSend.get("money_amount"));
		orderMap.put("money_all_amount", queryShopOutSend == null ? 0 : queryShopOutSend.get("money_all_amount"));
		orderMap.put("sendOrderCount", queryShopOutSend == null ? 0 : queryShopOutSend.get("sendOrderCount"));
		//查询配送支出订单数量
		Map<String,Object> deliveryCountMap=shopCouponCashCountDao.queryShopDeliveryCountInfo(map);
		System.out.println(df.format(new Date()));// new Date()为获取当前系统时间
		orderMap.put("deliveryCount",deliveryCountMap.get("deliveryCount"));
		orderMap.put("shop_delivery_price",deliveryCountMap.get("shop_delivery_price"));
		//查询平台优惠券订单数量
		Map<String,Object> platformCouponOrderCountMap=shopCouponCashCountDao.queryPlatformCouponOrderCountInfo(map);
		System.out.println(df.format(new Date()));// new Date()为获取当前系统时间
		orderMap.put("platformCouponCount",platformCouponOrderCountMap.get("platformCouponCount"));
		//查询店铺优惠券订单数量
		Map<String,Object> shopCouponOrderCountMap=shopCouponCashCountDao.queryShopCouponOrderCountInfo(map);
		System.out.println(df.format(new Date()));// new Date()为获取当前系统时间
		orderMap.put("shopCouponCount",shopCouponOrderCountMap.get("shopCouponCount"));
		pr.setData(orderMap);
		pr.setStatus(1);
		pr.setMsg("查询成功！");
		System.out.println(df.format(new Date()));// new Date()为获取当前系统时间
		return pr;
	}


	@Override
	public PurResult queryShopCashLog(String shop_unique, String startDate, String endDate, String cash_mode,String handle_status,Integer pageNum,
			Integer pageSize) {
		PurResult result=new PurResult();
		Map<String,Object> map=new HashMap<String, Object>();
		if("8302016134121".equals(shop_unique)){
			//平台
		}else{
			//店铺
			map.put("shop_unique", shop_unique);
		}
		if(startDate!=null&&!"".equals(startDate)){
			map.put("startDate", DateUtils.parse(startDate));
		}
		if(endDate!=null&&!"".equals(endDate)){
			map.put("endDate", DateUtils.parse(endDate));
		}
		map.put("cash_mode", cash_mode);
		map.put("handle_status", handle_status);
		Map<String,Object> countMap=shopCouponCashCountDao.queryShopCashLogCount(map);
		Long count= (Long) countMap.get("count");
		map.put("pageSize", pageSize);
		map.put("startNum", (pageNum-1)*pageSize);
		List<Map<String,Object>> orderList=shopCouponCashCountDao.queryShopCashLog(map);
		result.setStatus(0);
		result.setMsg("查询成功！");
		result.setData(orderList);
		result.setCount(count.intValue());
		return result;
	}


	@Override
	public PurResult queryOnlineOrderList(String shopUnique, String startTime, String endTime, Integer page,
			Integer limit,Integer saleListHandleState) {
		System.out.println("开始时间"+startTime);
		PurResult result=new PurResult();
		Map<String,Object> map=new HashMap<String, Object>();
		map.put("saleListHandleState", saleListHandleState);
		if("8302016134121".equals(shopUnique)){
			//平台
		}else{
			//店铺
			map.put("shopUnique", shopUnique);
		}
		if(startTime!=null&&!"".equals(startTime)){
			map.put("startTime", DateUtils.parse(startTime));
		}
		if(endTime!=null&&!"".equals(endTime)){
			map.put("endTime", DateUtils.parse(endTime));
		}
		Map<String,Object> countMap=shopCouponCashCountDao.queryOnlineOrderListCount(map);
		Long count= (Long) countMap.get("count");
		map.put("limit", limit);
		map.put("startNum", (page-1)*limit);
		List<Map<String,Object>> orderList=shopCouponCashCountDao.queryOnlineOrderList(map);
		result.setStatus(0);
		result.setMsg("查询成功！");
		result.setData(orderList);
		result.setCount(count.intValue());
		return result;
	}
	
}


