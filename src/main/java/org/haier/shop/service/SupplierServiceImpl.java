package org.haier.shop.service;

import java.math.BigDecimal;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Random;

import javax.annotation.Resource;

import org.haier.meituan.util.MUtil;
import org.haier.shop.dao.SupplierDao;
import org.haier.shop.util.PurResult;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import net.sf.json.JSONArray;
import net.sf.json.JSONObject;
import org.springframework.util.ObjectUtils;

@Service
public class SupplierServiceImpl implements SupplierService{
	@Resource
	private SupplierDao supplierDao;

	/**
	 * 删除指定供货商的全部商品
	 */
	public PurResult deleteAllGoodsSupplier(String shopUnique,String supplierUnique) {
		PurResult pr = new PurResult(1, "操作成功！");
		Map<String,Object> map = new HashMap<String,Object>();
		map.put("shopUnique", shopUnique);
		map.put("supplierUnique", supplierUnique);
		supplierDao.deleteAllGoodsSupplier(map);
		return pr;
	}
	
	public PurResult querySupplierList(String shop_unique, Integer pageNum, Integer pageSize,String supMsg) {
		PurResult result = new PurResult();
		try {
			Map<String,Object> map=new HashMap<String, Object>();
			map.put("shop_unique", shop_unique);
			if(null!=supMsg&&!supMsg.trim().equals("")){
				map.put("supMsg", "%"+supMsg+"%");
			}
			map.put("pageSize", pageSize);
			map.put("startNum", (pageNum-1)*pageSize);			
			List<Map<String,Object>> list = supplierDao.querySupplierList(map);
			Map<String,Object> countMap = supplierDao.querySupplierListCount(map);
			Integer count = Integer.parseInt(MUtil.strObject(countMap.get("count")));
	    	result.setStatus(1);
			result.setMsg("成功");
			result.setCount(count);
			result.setData(list);
		} catch (Exception e) {
			e.printStackTrace();
			result.setStatus(0);
			result.setMsg("异常");
		}
		return result;
	}

	
	public PurResult getMapCenter(String shop_unique) {
		PurResult pr=new PurResult();
		Map<String,Object> map=new HashMap<String, Object>();
		map.put("shop_unique", shop_unique);
	
		Map<String,Object> countMap=supplierDao.getMapCenter(map);
		pr.setData(countMap);
		pr.setStatus(1);
		pr.setMsg("查询成功！");
		return pr;
	}

	@Transactional
	public PurResult saveSupplier(String shop_unique, String shop_name, String shop_address_detail, String shop_phone,
			String manager_name, BigDecimal shop_latitude, BigDecimal shop_longitude, String province, String city,
			String district,String supplier_kind_id,String settlementBank,
			String settlementName,
			String settlementCard,String supplierRemark,String supplierUnique) {
		PurResult pr=new PurResult();
		Map<String,Object> map=new HashMap<String,Object>();
		map.put("shop_unique", shop_unique);
		map.put("shop_name", shop_name);
		map.put("shop_address_detail", shop_address_detail);
		map.put("shop_phone", shop_phone);
		map.put("manager_name", manager_name);
		map.put("shop_latitude", shop_latitude);
		map.put("shop_longitude", shop_longitude);
		map.put("province", province);
		map.put("city", city);
		map.put("district", district);
		map.put("supplier_kind_id", supplier_kind_id);
		map.put("settlementBank", settlementBank);
		map.put("settlementName", settlementName);
		map.put("settlementCard", settlementCard);
		map.put("supplierRemark", supplierRemark);
		//查询区的编号
		Map<String,Object> quCode=supplierDao.queryDistrict(map);
		String area_dict_num=(String) quCode.get("area_dict_num");
		map.put("area_dict_num", area_dict_num);
		Random ran=new Random();
		int m=ran.nextInt(89999)+10000;
		String supplier_unique=new String(new Date().getTime()+"").substring(5)+""+m;	
		map.put("supplier_unique", supplierUnique == null ? supplier_unique : supplierUnique);
		int  i =supplierDao.saveSupplier(map);
		//添加供货商和超市关联表
		//shopsDao.saveShopsAndSupplierRelation(map);
		pr.setStatus(1);
		return pr;
	}

	public Map<String, Object> getSupplierById(Integer supplier_id) {
		Map<String,Object> map=new HashMap<String, Object>();
		map.put("supplier_id", supplier_id);
		Map<String,Object> result=supplierDao.getSupplierById(map);
		//查询市 省 
		String area_dict_num=(String) result.get("area_dict_num");
		map.put("area_dict_num", area_dict_num);
		Map<String,Object> resultDistrict=supplierDao.getDistrict(map);
		
		Map<String,Object> resultCity=supplierDao.getCity(map);
		if(resultCity!=null){
		String area_dict_num_city= (String) resultCity.get("area_dict_num");
		map.put("area_dict_num", area_dict_num_city);
		String area_dict_content_city= (String) resultCity.get("area_dict_content");
		Map<String,Object> resultProvince=supplierDao.getCity(map);
		String area_dict_content_province= (String) resultProvince.get("area_dict_content");
		result.put("area_dict_content_city", area_dict_content_city);
		result.put("area_dict_content_province", area_dict_content_province);
		result.put("area_dict_content_district", resultDistrict.get("area_dict_content"));
		}
		return result;
	}

	@Transactional
	public PurResult editSupplier(String supplier_unique, String shop_name, String shop_address_detail,
			String shop_phone, String manager_name, BigDecimal shop_latitude, BigDecimal shop_longitude,
			String province, String city, String district, String shop_unique,String supplier_kind_id,String settlementBank,
			String settlementName,
			String settlementCard,
			String supplierRemark) {
		PurResult pr=new PurResult();
		Map<String,Object> map=new HashMap<String,Object>();
		map.put("supplier_unique", supplier_unique);
		map.put("shop_name", shop_name);
		map.put("shop_address_detail", shop_address_detail);
		map.put("shop_phone", shop_phone);
		map.put("manager_name", manager_name);
		map.put("shop_latitude", shop_latitude);
		map.put("shop_longitude", shop_longitude);
		map.put("province", province);
		map.put("city", city);
		map.put("district", district);
		map.put("supplier_kind_id", supplier_kind_id);
		map.put("settlementBank", settlementBank);
		map.put("settlementName", settlementName);
		map.put("settlementCard", settlementCard);
		map.put("supplierRemark", supplierRemark);
		
		//查询区的编号
		Map<String,Object> quCode=supplierDao.queryDistrict(map);
		if (!ObjectUtils.isEmpty(quCode)){
			String area_dict_num=(String) quCode.get("area_dict_num");
			map.put("area_dict_num", area_dict_num);
			map.put("shop_unique", shop_unique);
			supplierDao.editSupplier(map);
		}
		pr.setStatus(1);
		return pr;
	}

	@Transactional
	public PurResult deleteP(Integer supplier_id) {
		PurResult pr=new PurResult();
		Map<String,Object> map=new HashMap<String,Object>();
		map.put("supplier_id", supplier_id);
		supplierDao.deleteP(map);
		pr.setStatus(1);
		return pr;
	}

	public PurResult querySupplierByContries(String area_dict_num) {
		PurResult pr=new PurResult();
		Map<String,Object> map=new HashMap<String,Object>();
		map.put("area_dict_num", area_dict_num);
		List<Map<String,Object>> list =supplierDao.querySupplierByContries(map);
		pr.setStatus(1);
		pr.setData(list);
		return pr;
	}

	public PurResult querySupplierByName(String searchShopName) {
		PurResult pr=new PurResult();
		Map<String,Object> map=new HashMap<String,Object>();
		map.put("searchShopName", "%"+searchShopName+"%");
		List<Map<String,Object>> list =supplierDao.querySupplierByName(map);
		pr.setStatus(1);
		pr.setData(list);
		return pr;
	}
	
	@Transactional
	public PurResult submitOrder(String supplier_unique, String shop_unique, Integer purchase_list_sum,
			BigDecimal purchase_list_total, String detailJson,String login_shop_unique) {
		PurResult pr=new PurResult();
		Map<String,Object> map=new HashMap<String, Object>();
		map.put("supplier_unique", supplier_unique);
		map.put("shop_unique", shop_unique);
		map.put("purchase_list_sum", purchase_list_sum);
		map.put("purchase_list_total", purchase_list_total);
		//判断供货商线上还是线下自己的添加的
		Map<String,Object> supplierMap= supplierDao.querySupplierBySupplierUnique(map);
		if(!"0".equals((String)supplierMap.get("shop_unique"))){
			//线下
			map.put("receipt_status", 6);
		}else{
			map.put("receipt_status", 1);
		}
		Random ran=new Random();
		int m=ran.nextInt(89999)+10000;
		String purchase_list_unique=new String(new Date().getTime()+"").substring(2)+""+m;
		map.put("purchase_list_unique", purchase_list_unique);//订单编号
		map.put("purchase_list_date", new Date());
		map.put("purchase_list_redate", new Date());
		//添加订单
		int k=supplierDao.submitOrder(map);
		JSONArray array= JSONArray.fromObject(detailJson);
		for (int i = 0; i < array.size(); i++) {
			JSONObject temp = (JSONObject) array.get(i); 
			String goods_name= temp.getString("goods_name");
			String goods_barcode= temp.getString("goods_barcode");
			String purchase_list_detail_count= temp.getString("shop_list_detail_count");
			String purchase_list_detail_price= temp.getString("shop_list_detail_price");
			Object gift=temp.get("gift");
			Map<String,Object> map2=new HashMap<String, Object>();
			map2.put("goods_barcode", goods_barcode);
			map2.put("purchase_list_detail_count", purchase_list_detail_count);
			map2.put("supplier_unique", supplier_unique);
			//添加订单详情
			map2.put("goods_name", goods_name);
			map2.put("purchase_list_detail_price", purchase_list_detail_price);
			map2.put("purchase_list_unique", purchase_list_unique);
			map2.put("gift", gift);
			
			supplierDao.addPurchaseListDetail(map2);
			//判断是否有此商品
			HashMap<String, Object> params=new HashMap<String, Object>();
			params.put("shop_unique", shop_unique);
			params.put("goods_barcode", goods_barcode);
			Map<String, Object> goodsInfo=supplierDao.getGoodsInfo(params);
			Double stock_count=Double.parseDouble(purchase_list_detail_count);
			if(goodsInfo!=null){
				//更新商品数量和采购价
				Double goodsCount=((BigDecimal)goodsInfo.get("goods_count")).doubleValue();
				stock_count=goodsCount+Double.parseDouble(purchase_list_detail_count);
				goodsInfo.put("goods_count", stock_count);
				goodsInfo.put("goods_in_price", purchase_list_detail_price);
				supplierDao.updateGoodsCount(goodsInfo);
			}else{
				//查询售价
				params.put("shop_unique", login_shop_unique);
				Map<String, Object> goodsInfoSource=supplierDao.getGoodsInfo(params);
				goodsInfoSource.put("goods_count", purchase_list_detail_count);
				goodsInfoSource.put("goods_in_price", purchase_list_detail_price);
				goodsInfoSource.put("shop_unique", shop_unique);
				//添加商品
				supplierDao.addNewGoods(goodsInfoSource);
			}
			//添加入库记录
			Map<String,Object> smap=new HashMap<String, Object>();
			smap.put("goods_barcode", goods_barcode);
			smap.put("goods_count",purchase_list_detail_count);
			smap.put("stock_count",stock_count);
			smap.put("stock_type",1);
			smap.put("shop_unique",shop_unique);
			smap.put("stock_resource", 1);
			smap.put("stock_price", purchase_list_detail_price);
			supplierDao.addShopStock(smap);
			
		}
		if(k==1){
			pr.setStatus(1);  
			pr.setMsg("提交成功！");
			return pr;
		}else{
			pr.setStatus(0);
			pr.setMsg("提交失败！");
			return pr;
		}
	}
	
	/**
	 * 供货商供应商品界面-分页数量查询
	 */
	public PurResult querySupGoodsPages(Map<String,Object> map){
		PurResult pr=new PurResult(1,"查询成功！");
		Integer pageCount=supplierDao.querySupGoodsPages(map);
		pr.setData(pageCount);
		return pr;
	}
	
	/**
	 * 供应商供应商品界面-分页查询
	 * @param map
	 * @return
	 */
	public PurResult querySupGoodsByPage(Map<String,Object> map){
		PurResult result = new PurResult();
		try {
			List<Map<String,Object>> list = supplierDao.querySupGoodsByPage(map);
			Integer count = supplierDao.querySupGoodsPages(map);
	    	result.setStatus(1);
			result.setMsg("成功");
			result.setCount(count);
			result.setData(list);
		} catch (Exception e) {
			e.printStackTrace();
			result.setStatus(0);
			result.setMsg("异常");
		}
		return result;
	}
	
	/**
	 * 清除店内商品的供货商信息
	 * @param map
	 * @return
	 */
	public PurResult clearSupGoodsMsgPage(Map<String,Object> map){
		PurResult pr=new PurResult(1, "查询成功！");
		System.out.println(map);
		Integer k=supplierDao.clearSupGoodsMsgPage(map);
		pr.setData(k);
		return pr;
	}
	
	/**
	 * 商品进价信息保存
	 * @param map
	 * @return
	 */
	@Transactional
	public PurResult saveGoodsInPrice(Map<String,Object> map){
		PurResult pr=new PurResult(1,"商品进价保存成功！");
		Integer data=supplierDao.saveGoodsInPrice(map);
		if(data==0){
			pr.setStatus(2);
			pr.setMsg("保存失败！");
		}
		return pr;
	}


	@Override
	public PurResult querySupplierKindList(String shop_unique, int pageNum, int pageSize, String supMsg) {
		PurResult result = new PurResult();
		try {
			Map<String,Object> map=new HashMap<String, Object>();
			map.put("shop_unique", shop_unique);
			if(null!=supMsg&&!supMsg.trim().equals("")){
				map.put("supMsg", "%"+supMsg+"%");
			}
			map.put("pageSize", pageSize);
			map.put("startNum", (pageNum-1)*pageSize);			
			List<Map<String,Object>> list = supplierDao.querySupplierKindList(map);
			Map<String,Object> countMap = supplierDao.querySupplierKindListCount(map);
			Integer count = Integer.parseInt(MUtil.strObject(countMap.get("count")));
	    	result.setStatus(1);
			result.setMsg("成功");
			result.setCount(count);
			result.setData(list);
		} catch (Exception e) {
			e.printStackTrace();
			result.setStatus(0);
			result.setMsg("异常");
		}
		return result;
	}


	@Override
	@Transactional
	public PurResult saveSupplierKind(String shop_unique, String supplier_kind_name) {
		PurResult pr=new PurResult();
		Map<String,Object> map=new HashMap<String,Object>();
		map.put("shop_unique", shop_unique);
		map.put("supplier_kind_name", supplier_kind_name);
		int  i =supplierDao.saveSupplierKind(map);
		pr.setStatus(1);
		return pr;
	}


	@Override
	@Transactional
	public PurResult deleteSupplierKind(Integer id) {
		PurResult pr=new PurResult();
		Map<String,Object> map=new HashMap<String,Object>();
		map.put("id", id);
		supplierDao.deleteSupplierKind(map);
		pr.setStatus(1);
		return pr;
	}


	@Override
	public Map<String, Object> getSupplierKindById(Integer id) {
		Map<String,Object> map=new HashMap<String,Object>();
		map.put("id", id);
		Map<String,Object> result=supplierDao.getSupplierKindById(map);
		return result;
	}


	@Override
	@Transactional
	public PurResult editSupplierKind(String id, String supplier_kind_name) {
		PurResult pr=new PurResult();
		Map<String,Object> map=new HashMap<String,Object>();
		map.put("id", id);
		map.put("supplier_kind_name", supplier_kind_name);
		supplierDao.editSupplierKind(map);
		pr.setStatus(1);
		return pr;
	}


	@Override
	public PurResult querySupplierKindByShopUnique(String shop_unique) {
		PurResult pr=new PurResult();
		Map<String,Object> map=new HashMap<String,Object>();
		map.put("shop_unique", shop_unique);
		List<Map<String, Object>> list= supplierDao.querySupplierKindByShopUnique(map);
		pr.setData(list);
		pr.setStatus(1);
		return pr;
	}
}