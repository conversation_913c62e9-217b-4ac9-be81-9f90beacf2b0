package org.haier.shop.service;

import java.util.List;
import java.util.Map;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;

import org.haier.shop.dao.SysAgreementDao;
import org.haier.shop.util.PurResult;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Service
@Transactional
public class SysAgreementServiceImpl implements SysAgreementService{
	
	@Resource
	private SysAgreementDao sysAgreementDao;
	
	@Override
	public Map<String, Object> querySysAgreement(String code) {
		return sysAgreementDao.querySysAgreement(code);
	}

	@Override
	public void updateSysAgreement(Map<String, Object> params) {
		sysAgreementDao.updateSysAgreement(params);
	}
	@Override
	public PurResult addFaultHanding(Map<String, Object> params){
		PurResult result = new PurResult();
		sysAgreementDao.addFaultHanding(params);
	    result.setStatus(1);
		result.setMsg("成功");
		return result;
	}

	@Override
	public PurResult queryfaultHanding(HttpServletRequest request) {
		PurResult result = new PurResult();
		List<Map<String,Object>> list = sysAgreementDao.queryfaultHanding();
		result.setData(list);
	    result.setStatus(1);
		result.setMsg("成功");
		return result;
	}
}
