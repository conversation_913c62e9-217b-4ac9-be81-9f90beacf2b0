package org.haier.shop.service;

import java.sql.Timestamp;

import org.haier.shop.util.ShopsResult;

public interface CountService {
	/**
	 * 各时间段内用户注册量
	 * @param startTime
	 * @param endTime
	 * @return
	 */
	public ShopsResult cusCountByTime(Timestamp startTime,Timestamp endTime);
	
	/**
	 * 各时间段内用户注册方式（ANDRIOD,IOS,微信）
	 * @param startTime
	 * @param endTime
	 * @return
	 */
	public ShopsResult cusCountByPhone(Timestamp startTime,Timestamp endTime);
	
	/**
	 * 各时间段内活跃用户数量
	 * @param startTime
	 * @param endTime
	 * @return
	 */
	public ShopsResult cusCountByActive(Timestamp startTime,Timestamp endTime);
	/**
	 *  各时间段内活跃用户数量(按区域)
	 * @param startTime
	 * @param endTime
	 * @return
	 */
	public ShopsResult cusCountByActArea(Timestamp startTime,Timestamp endTime);
	/**
	 * 阶段内客户购买力(分区县,分时间)
	 * @param startTime
	 * @param endTime
	 * @return
	 */
	public ShopsResult cusPurPowerByArea(Timestamp startTime,Timestamp endTime);
	/**
	 * 所有地区客户购买能力
	 * @param startTime
	 * @param endTime
	 * @return
	 */
	public ShopsResult cusPurPower(Timestamp startTime,Timestamp endTime);
	/**
	 *  各地区商户注册量
	 * @param startTime
	 * @param endTime
	 * @return
	 */
	public ShopsResult shopCountByArea(Timestamp startTime,Timestamp endTime);
	/**
	 * 各地区的订单量，订单金额统计，商品销售数量统计
	 * @param startTime
	 * @param endTime
	 * @return
	 */
	public ShopsResult statisByArea(Timestamp startTime,Timestamp endTime);
	
	
}
