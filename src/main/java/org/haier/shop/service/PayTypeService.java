package org.haier.shop.service;

import java.util.Map;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.haier.shop.entity.publicEntity.PageSearch;
import org.haier.shop.params.SetSubAccountParams;
import org.haier.shop.util.ShopsResult;

public interface PayTypeService {
	
	/**
	 * 查询需要下载的数据信息
	 * @param shopMsg
	 * @param examineStatus
	 * @param startTime
	 * @param endTime
	 * @return
	 */
	public void downloadYiTongShopMsgData(String shopMsg,Integer examineStatus,String startTime,String endTime,HttpServletRequest request,HttpServletResponse response);
	
	/**
	 * 修改支付状态
	 * @param id
	 * @param status
	 * @param examineStatus
	 * @return
	 */
	public ShopsResult modifyShopPayMsg(Integer id, Integer examineStatus , String examineRemarks);
	/**
	 * 获取店铺支付信息提交审核的所有审核状态
	 * @return
	 */
	public ShopsResult queryShopPayMsgExamineStatus();
	/**
	 * 分页查询已有的审核资料信息
	 * @param shopUnique
	 * @return
	 */
	public ShopsResult queryShopPayMsgByPage(Map<String,Object> map ,PageSearch pageSearch);
	
	/**
	 * 更新店铺的支付信息
	 * @param id
	 * @param mchId
	 * @param mchKey
	 * @param defaultType
	 * @param shopUnique
	 * @param payType
	 * @return
	 */
	public ShopsResult saveShopPayType(String id,String mchId,String mchKey,String defaultType,String shopUnique,String payType);
	/**
	 * 查询当前易通支付信息
	 * @return
	 */
	public ShopsResult queryShopYiTongPayMsg(String shopUnique) ;
	
	/**
	 * 获取当前店铺的有效支付信息
	 * @param shopUnique
	 * @return
	 */
	public ShopsResult getShopPayMsg(String shopUnique) ;
	
	public ShopsResult addNewShopPayMsg(HttpServletRequest request,
			String shopUnique,
			String legalPerson,
			String userPhone,
			Integer id,
			String shopAddress,
			String email,
			String subBranch
			
			);
	
	/**
	 * 查询店铺内所有的支付方式
	 * @param map
	 * @return
	 */
	public ShopsResult getPayType(Map<String,Object> map);
	
	/**
	 * 更新店铺的支付信息
	 * @param map
	 * @return
	 */
	public ShopsResult modiyfPayType(Map<String,Object> map) ;
	

	/**
	 * 查询平台所有支付方式
	 * @param map
	 * @return
	 */
	public ShopsResult queryPlatPayType(Map<String,Object> map);
	
	/**
	 * 修改平台支付方式信息
	 * @param map
	 * @return
	 */
	public ShopsResult modifyPlatPayType(Map<String,Object> map);
	
	/**
	 * 查询该支付方式所有店铺列表
	 * @param pay_type 支付方式
	 * @param shop_message 店铺名称/店铺编号
	 * @return
	 */
	public ShopsResult queryPayTypeShopList(Map<String,Object> map);
	
	/**
	 * 添加店铺支付方式
	 * @param map
	 * @return
	 */
	public ShopsResult addShopPayType(Map<String,Object> map) ;
	
	/**
	 * 获取店铺支付方式详情
	 * @param shop_pay_type_id
	 * @return
	 */
	public Map<String ,Object> queryShopPayType(String shop_pay_type_id);
	/**
	 * 设置子账户
	 * @param params
	 */
	void setSubAccount(SetSubAccountParams params);
}
