package org.haier.shop.service;

import org.haier.shop.dao.ShopStaffDao;
import org.haier.shop.result.card.Personnel;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Service
public class BusinessCardServiceImpl implements BusinessCardService{

    @Resource
    private ShopStaffDao shopStaffDao;

    /**
     * 查询商家名片人员列表
     * @param shopUnique
     * @return
     */
    public List<Personnel> queryShopStaffList(String shopUnique,Long staffId){
        Map<String,Object> map=new HashMap<>();
        map.put("shopUnique",shopUnique);
        map.put("staffId",staffId);
        return shopStaffDao.queryStaffList(map);
    }
}
