package org.haier.shop.service;

import java.util.Map;

import org.haier.shop.util.ShopsResult;

/**
 * 加油相关功能处理
 * <AUTHOR>
 *
 */
public interface OilService {
	
	
	/**
	 * 
	 * @param id 修改时上传ID
	 * @param shopUnique 店铺编号
	 * @param disName 名称
	 * @param startTime 活动开始时间
	 * @param endTime 活动结束时间
	 * @param payAmount 需要支付的金额
	 * @param discountMoney 给顾客优惠的金额
	 * @param activeState 1、正常；2、停用；3、删除
	 * @return
	 */
	public ShopsResult addNewShopDis(Integer id,String shopUnique,String disName,String startTime,
			String endTime,Double payAmount,Double discountMoney,Integer activeState);
	/**
	 * 查询店铺的优惠配置信息
	 * @param shopUnique
	 * @param page
	 * @param pageSize
	 * @return
	 */
	public ShopsResult queryShopDisList(String shopUnique,Integer page,Integer pageSize,String startTime,String endTime);
	
	/**
	 * 根据ID获取对应的油品、油枪信息
	 * @param id
	 * @return
	 */
	public Map<String,Object> queryOrderMsgById(Integer id,Integer parType);
	/**
	 * 添加新的油枪油品信息
	 * @param parName 名称
	 * @param parValue 值
	 * @param stationPrice 油站价格
	 * @param interPrice 国标价
	 * @param parType 类型：1、油枪；2、油品（油品必须标注价格）
	 * @param id 当前ID信息，如果不为空，则为更新
	 * @param delFlag 1、删除；否则不操作
	 * @return
	 */
	public ShopsResult addNewMsg(String parName,String parValue,Double stationPrice,Double interPrice,Integer parType,Integer id,Integer delFlag);
	/**
	 * 查询油号油枪列表信息
	 * @param parType
	 * @param delFlag
	 * @return
	 */
	public ShopsResult queryOilMsgList(Integer parType,Integer delFlag,Integer id);
}
