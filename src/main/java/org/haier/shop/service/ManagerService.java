package org.haier.shop.service;

import java.sql.Timestamp;
import java.util.Map;

import org.haier.customer.entity.ShopResult;
import org.haier.shop.util.PurResult;
import org.haier.shop.util.ShopsResult;

public interface ManagerService {
	
	
	/**
	 * 查询指定日期的平台充值信息
	 * @param datetime
	 * @return
	 */
	public PurResult queryOnlinePlatRechargeListDetail(String datetime);
	/**
	 * 查询平台为线上会员充值的记录
	 * @param startTime
	 * @param endTime
	 * @return
	 */
	public PurResult queryOnlineCusRechargeList(String startTime,String endTime) ;
	/**
	 * 给会员充值
	 * @param cusAccounts
	 * @return
	 */
	public PurResult rechargeForCus(String cusAccounts);
	/**
	 * 根据会员手机号，查询会员信息
	 * @param cusAccounts
	 * @return
	 */
	public PurResult queryOnlineCusMsgByCusAccount(String cusAccounts);
	
	public PurResult queryCusRechargeStaticByShop(String startTime,String endTime,Integer page,Integer limit,String shopUnique);
	
	public PurResult queryCusRechargeStatic(String shopUnique,String startTime,String endTime,String shopMsg);
	/**
	 * 登录检查
	 * @param manager_account
	 * @param manager_pwd
	 * @param manager_phone
	 * @return
	 */
	public ShopsResult login(String manager_account,String manager_pwd);
	
	/**
	 * 注册新帐户
	 * @param manager_account
	 * @param manager_pwd
	 * @param manager_phone
	 * @param manager_name
	 * @return
	 */
	public ShopsResult addNewManager(String manager_account,String manager_pwd,String manager_phone,String manager_name);
	
	/**
	 * 查询管理元旗下所有POS
	 * @param manager_unique
	 * @return
	 */
	public ShopsResult queryMachineNums(String manager_unique);
	
	/**
	 * 商品接口
	 * @param shopID
	 * @param pageIndex
	 * @param pageSize
	 * @param startTime
	 * @param endTime
	 * @return
	 */
	public ShopResult queryShopsGoods(String shopID,Integer pageIndex,Integer pageSize,Timestamp startTime, Timestamp endTime);
	
	/**
	 * 查询管理员管理的所有店铺
	 */
	public ShopResult queryShops(Timestamp startTime,Timestamp endTime);
	
	/**
	 * 3.销售接口（根据销售时间段返回所有销售记录）
	 * @param shopID
	 * @param startTime
	 * @param endTime
	 * @param pageIndex
	 * @param pageSize
	 * @return
	 */
	public ShopResult queryOrderLists(String shopID,Timestamp startTime,Timestamp endTime,Integer pageIndex,Integer pageSize);
	
	/**
	 * 商品分页查询
	 * @param shopID
	 * @param pageIndex
	 * @param pageSize
	 * @param startTime
	 * @param endTime
	 * @return
	 */
	public ShopResult queryGoodsByPage(String shopID,Integer pageIndex,Integer pageSize,Timestamp startTime,Timestamp endTime);
	
	/**
	 * 会员信息详情查询
	 * @param map
	 * @return
	 */
	public ShopsResult queryCusDetail(Map<String,Object> map);
	

}
