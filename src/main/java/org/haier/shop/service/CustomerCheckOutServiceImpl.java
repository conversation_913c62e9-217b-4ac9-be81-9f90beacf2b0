package org.haier.shop.service;

import java.io.File;
import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.UUID;
import java.util.stream.Collectors;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import org.apache.shiro.SecurityUtils;
import org.apache.shiro.session.Session;
import org.apache.shiro.subject.Subject;
import org.haier.shop.dao.CustomerReverseRechargeDao;
import org.haier.shop.config.WxPushConfig;
import org.haier.shop.dao.ImportDao;
import org.haier.shop.dao.ManagerDao;
import org.haier.shop.entity.CustomerReverseRechargeEntity;
import org.haier.shop.entity.Staff;
import org.haier.shop.entity.CY.CustomerSummaryCYByGroup;
import org.haier.shop.entity.ny.CustomerSummaryByGroup;
import org.haier.shop.entity.ny.NingyuLottery;
import org.haier.shop.params.customer.QueryCustomerReverseRechargeListParams;
import org.haier.shop.params.customer.RechargeForNYCusDetailParams;
import org.haier.shop.redis.RedisCache;
import org.haier.shop.thread.NYCustomerRechargeThread;
import org.haier.shop.util.PurResult;
import org.haier.shop.util.ShopsResult;
import org.haier.shop.util.ShopsUtil;
import org.haier.shop.util.UtilForJAVA;
import org.haier.util.eshow.UUIDUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;
import org.yxl.heLiBaoPay.util.HeLiBaoPay;
import org.yxl.heLiBaoPay.vo.AppPayRefundOrderResponseVo;
import org.yxl.heLiBaoPay.vo.AppPayRefundOrderVo;
import org.yxl.heLiBaoPay.vo.AppPayRefundQueryResponseVo;
import org.yxl.heLiBaoPay.vo.AppPayRefundQueryVo;

@Service
public class CustomerCheckOutServiceImpl implements CustomerCheckOutService{
	@Resource
	private ManagerDao manDao;
	@Resource
	private RedisCache rc;
	@Resource
	private CustomerReverseRechargeDao customerReverseRechargeDao;
	@Resource
	private WxPushConfig wxPushConfig;
	@Resource
	private ImportDao importDao;

	/**
	 * 宁宇2025年积分兑换活动
	 * @param list
	 * @return
	 */
	public PurResult rechargeForNYCus(List<RechargeForNYCusDetailParams> list) {
		boolean flag = WxPushConfig.isRecharge.compareAndSet(false,true);
		if (flag) {
			//本次更新成功，说明之前的状态为false，并设置成了true
			//由于数量较多，需要开启线程处理
			NYCustomerRechargeThread nyCustomerRechargeThread = new NYCustomerRechargeThread(rc,manDao,importDao, wxPushConfig,list);
			nyCustomerRechargeThread.start();
			return PurResult.ok();
		} else {
			//本次更新失败，说明当前的值不是false
			return PurResult.fail("已有充值列表在进行，请勿重复提交");
		}
	}

	/**
	 * 1、如果退款状态为驳回，将余额增加回账户；
	 * 2、如果退款状态为成功
	 * 2.1、现金退款，直接修改订单状态即可
	 * 2.2、使用小程序退款；
	 * 2.2.1、查询是否有小程序支付的列表，如果没有，返回提示要求使用现金退款；
	 * 2.2.2、查询是否有小程序支付的列表，如果有，比较充值的金额之和是否比当前的金额要大，如果大，提示用现金退款；
	 * 2.2.3、如果是小程序多笔退款，没退一笔，记录一次日志，防止部分退款成功；
	 * @param rechargeId 充值列表ID
	 * @param rechargeMethod 退款方式；
	 * @param rechargeStatus 退款状态；
	 * @return
	 */
	@Override
	@Transactional
	public PurResult submitRefundMsg(String rechargeId,Integer rechargeMethod,Integer rechargeStatus,String remarks,String shopUnique) {
		PurResult pr = new PurResult(1,"操作成功！");
		Map<String,Object> map = new HashMap<String,Object>();
		map.put("rechargeId", rechargeId);
		map.put("rechargeMethod", rechargeMethod);
		map.put("rechargeStatus", rechargeStatus);
		map.put("remarks", remarks);
		map.put("shopUnique", shopUnique);
		
		String redisId = "refundId" + rechargeId;
		
		if(rc.getObject(redisId) != null) {
			pr.setStatus(0);
			pr.setMsg("已申请退款，请勿重复提交");
			return pr;
		}
		
		Double totalrefundMoney = manDao.queryCusRechargeDetail(map);
		
		rc.putObject(redisId, rechargeId);
		//查询当前订单信息，防止退款信息不存在
		Map<String,Object> refundMap = manDao.queryRefundDetail(rechargeId);
		if(null == refundMap || refundMap.isEmpty()) {
			pr.setStatus(0);
			pr.setMsg("退款的信息不存在");
			return pr;
		}
		
		//需要防止重复退款
		Integer nowResStatus = Integer.parseInt(refundMap.get("recharge_status").toString());
		if(nowResStatus == 1) {
			pr.setStatus(1);
			pr.setMsg("退款申请成功!");
			return pr;
		}
		
		if(nowResStatus == 3) {
			pr.setStatus(1);
			pr.setMsg("驳回成功!");
			return pr;
		}
		
		
		if(rechargeStatus == 3) {
			//驳回，修改申请状态，加回余额
			map.remove("rechargeMethod");
			manDao.modifyRefundMsg(map);
			
			//修改当前会员余额
			map.put("refundMoney", refundMap.get("recharge_money"));
			manDao.returnWithdrawal(map);
		}else if(rechargeMethod == 7) {
			//由小程序退款
//			//1、验证是否有对应的小程序充值信息
//			map.put("refundMoney", refundMoney);
//			Map<String,Object> rechargeMap = manDao.querySmallRechargeMsg(map);
//			if(null == rechargeMap || rechargeMap.isEmpty()) {
//				pr.setStatus(0);
//				pr.setMsg("未在平台充值过，请选择现金退款");
//				return pr;
//			}
//			//2、比对充值金额是否大于退款申请金额；
//
//			Double rechargeMoney = Double.parseDouble(rechargeMap.get("recharge_money").toString());
//
//			if(rechargeMoney.compareTo(refundMoney) < 0) {
//				pr.setStatus(0);
//				pr.setMsg("本次退款不支持小程序退款，请选择现金退款！");
//				return pr;
//			}
//
//			//3、可以通过小程序退款，获取店铺的充值配置信息
//
//			Map<String,Object> payMap = manDao.queryShopPayMsg(map);
//
//			if(null == payMap || payMap.isEmpty()) {
//				pr.setStatus(0);
//				pr.setMsg("支付信息不存在，请选择现金退款!");
//				return pr;
//			}

			/**
			 *	1、查询近一年的充值列表
			 *  2、在查询近一年的充值列表对应的退款列表信息
			 *  3、统计可退款金额，如果可退款金额之和大于退款金额，则进行退款；
			 *  4、如果可退款金额不大于或者等于可退款金额，返回提示；
			 *  5、依次退款，并记录对应的退款信息
			 *
			 */
			List<Map<String,Object>> rechargeList = manDao.queryYJCusRechargeList(map);

			if (null == rechargeList || rechargeList.isEmpty()) {
				pr.setStatus(0);
				pr.setMsg("没有符合推荐的充值记录，请联系客户线下退款");
			}

			//获取当前退款记录的退款金额
			List<String> uniques = new ArrayList<String>();
			uniques = rechargeList.stream().map(item -> item.get("sale_list_unique").toString()).collect(Collectors.toList());

			//根据订单编号，查询其已退款的金额
			QueryCustomerReverseRechargeListParams queryCustomerReverseRechargeListParams = new QueryCustomerReverseRechargeListParams();
			queryCustomerReverseRechargeListParams.setUniques(uniques);
			List<CustomerReverseRechargeEntity> reveseReverseRechargeList = customerReverseRechargeDao.queryCustomerReverseRechargeList(queryCustomerReverseRechargeListParams);

			//总的需要退款的金额
			BigDecimal totalRefundedMoney = new BigDecimal(totalrefundMoney).setScale(2,BigDecimal.ROUND_HALF_UP);
			//计算各个充值订单可退款的金额，并统计可退款的总额
			List<Map<String,Object>> waitForRefundList = new ArrayList<Map<String,Object>>();
			for(Integer i = 0 ;i < rechargeList.size(); i ++) {
				Map<String,Object> rechargeMap = rechargeList.get(i);

				BigDecimal availableMoney = new BigDecimal(rechargeMap.get("recharge_money").toString());

				//筛选该订单对应的退款订单
				List<CustomerReverseRechargeEntity> filterList =reveseReverseRechargeList.stream().filter(item -> item.getSaleListUnique().equals(rechargeMap.get("sale_list_unique").toString())).collect(Collectors.toList());
				for (CustomerReverseRechargeEntity item : filterList) {
					if (item.getApplyStatus() == 3) {
						//退款失败
						continue;
					}
					//退款中和退款成功的都要算在已退款中，防止退款中的数据退款成功，造成可退款金额不足
					BigDecimal refundedMoney = item.getApplyMoney();
					availableMoney = availableMoney.subtract(refundedMoney).setScale(2,BigDecimal.ROUND_HALF_UP);
				}

				System.out.println("当前订单" + rechargeMap.get("sale_list_unique") + "可用退款余额" + availableMoney + "待退款余额" + totalRefundedMoney);
				if(availableMoney.compareTo(BigDecimal.ZERO) > 0) {
					System.out.println("当前订单有可用退款余额");
					//比较当前可退款金额和赢退款金额，如果可退款金额大于或者等于退款金额，则本笔订单只退退款金额，如果小于退款金额，继续下一笔订单校验
					if (availableMoney.compareTo(totalRefundedMoney) >= 0) {
						System.out.println("当前订单可退款余额大于等于本次退款金额");
						rechargeMap.put("recharge_money", totalRefundedMoney);
						totalRefundedMoney = BigDecimal.ZERO;
						//该笔订单有可退的余额，加到退款列表中
						waitForRefundList.add(rechargeMap);
						//待退款金额已满足，跳出循环
						break;
					} else {
						System.out.println("当前订单可退款余额小于本次退款金额");
						rechargeMap.put("recharge_money", availableMoney);
						totalRefundedMoney = totalRefundedMoney.subtract(availableMoney).setScale(2,BigDecimal.ROUND_HALF_UP);
						//该笔订单有可退的余额，加到退款列表中
						waitForRefundList.add(rechargeMap);
					}
				}
			}

			System.out.println("需要退款的订单列表" + waitForRefundList);

			//查看待退款金额是否为0，如果不是0，则失败
			if (totalRefundedMoney.compareTo(BigDecimal.ZERO) != 0) {
				pr.setStatus(0);
				pr.setMsg("本次退款不支持小程序退款，请选择现金退款！");
				return pr;
			}

			//待退款的金额为0，依次对各个订单退款，如果退款异常，需要通知到后台
			Map<String,Object> payMap = manDao.queryShopPayMsg(map);

			//依次退款，并记录对应的退款信息
			for (Map<String,Object> rechargeMap : waitForRefundList) {
				if(payMap.get("pay_type").toString().equals("6")) {
					String retListUnique = UUIDUtil.getCargoRecord();
					AppPayRefundOrderVo orderVo = new AppPayRefundOrderVo();
					orderVo.setP2_orderId(rechargeMap.get("sale_list_unique").toString());
					orderVo.setP3_customerNumber(payMap.get("mch_id").toString());
					orderVo.setP4_refundOrderId(retListUnique);
					orderVo.setP5_amount(rechargeMap.get("recharge_money").toString());
					orderVo.setP7_desc("餐厅充值退卡");
					HeLiBaoPay heLiBaoPay = new HeLiBaoPay();

					System.out.println("退款信息===" + orderVo);
					String keys = payMap.get("mch_key").toString();
					AppPayRefundOrderResponseVo orderResponseVo =  heLiBaoPay.appPayRefund(orderVo,keys);

					System.out.println(orderResponseVo.getRt2_retCode() + orderResponseVo.getRt8_amount());
					//根据退款信息判断是否需要提示客户

					//根据请求结果，判断是否需要查询退款结果
					if( orderResponseVo.getRt2_retCode().equals("0000") || orderResponseVo.getRt2_retCode().equals("0001")) {
						//如果退款申请成功，请求退款结果
						AppPayRefundQueryVo queryVo = new AppPayRefundQueryVo();
						queryVo.setP2_refundOrderId(orderVo.getP4_refundOrderId());
						queryVo.setP3_customerNumber(orderVo.getP3_customerNumber());

						AppPayRefundQueryResponseVo responseVo =  heLiBaoPay.toRefundQuery(queryVo,keys);
						if(responseVo.getRt2_retCode().equals("0000") && (responseVo.getRt8_orderStatus().equals("SUCCESS") ||
								responseVo.getRt8_orderStatus().equals("INIT") || responseVo.getRt8_orderStatus().equals("RECEIVE")
								|| responseVo.getRt8_orderStatus().equals("DOING")
								)) {
							/*
							 * 1、修改退款申请状态及方法；
							 * 2、修改会员待提现金额；
							 */
							CustomerReverseRechargeEntity entity = new CustomerReverseRechargeEntity();
							entity.setSaleListUnique(rechargeMap.get("sale_list_unique").toString());
							entity.setApplyStatus(2);
							entity.setApplyMoney(new BigDecimal(rechargeMap.get("recharge_money").toString()));
							entity.setCreateDatetime(DateUtil.now());
							entity.setSuccessDatetime(DateUtil.now());
							entity.setRetListUnique(retListUnique);
							customerReverseRechargeDao.addCustomerReverseRecharge(entity);
						}else if(responseVo.getRt2_retCode().equals("") ){
							//这里有个坑，如果收单机构的可用余额不足，没做处理。
							CustomerReverseRechargeEntity entity = new CustomerReverseRechargeEntity();
							entity.setSaleListUnique(rechargeMap.get("sale_list_unique").toString());
							entity.setApplyStatus(3);
							entity.setApplyMoney(new BigDecimal(rechargeMap.get("recharge_money").toString()));
							entity.setCreateDatetime(DateUtil.now());
							entity.setSuccessDatetime(DateUtil.now());
							entity.setRetListUnique(retListUnique);
							entity.setRemarks(orderResponseVo.getRt3_retMsg());
							customerReverseRechargeDao.addCustomerReverseRecharge(entity);
						}

					}else {
						pr.setStatus(0);
						pr.setMsg(orderResponseVo.getRt3_retMsg());
					}
				}
			}
			//默认为退款成功，修改退款申请订单的状态为退款成功
			manDao.modifyRefundMsg(map);
		} else {
			//使用现金退款，直接将退款订单改为已退款状态
			manDao.modifyRefundMsg(map);
		}
		
		return pr;
	}
	
	/**
	 * 
	 * @param shopUnique 店铺编号
	 * @param cusUnique 会员编号
	 * @param startTime 开始查询时间
	 * @param endTime 结束查询时间
	 * @param page 页码
	 * @param limit 限制数量
	 * @return
	 */
	public PurResult queryCusRefundList(Map<String,Object> map) {
		
		PurResult result = new PurResult();
		List<Map<String,Object>> list = manDao.queryCusRefundList(map);
		result.setStatus(1);
		result.setMsg("查询成功！");
		result.setData(list);
		result.setCount(manDao.queryCusRefundListCount(map));
		return result;
	}
	
	/**
	 * 修改会员是否可提现
	 * @param shopUnique 店铺编号
	 * @param cusUnique 会员编号
	 * @param sameType 1：不可提现；2：可提现；
	 * @return
	 */
	public PurResult modifyCusMsg(String shopUnique,String cusUnique,Integer sameType) {
		PurResult pr = new PurResult(1,"修改成功!");
		
		Map<String,Object> map = new HashMap<String,Object>();
		map.put("shopUnique", shopUnique);
		map.put("cusUnique", cusUnique);
		map.put("sameType", sameType);
		
		manDao.modifyCusMsg(map);
		
		
		return pr;
	}
	
	/**
	 * 会员充值
	 * @param shopUnique 店铺编号
	 * @param cusUnique 会员编号
	 * @param rechargeType 充值类型：1、普通充值；2、换卡充值
	 * @param rechargeMoney 充值金额
	 * @return
	 */
	@Transactional
	public PurResult rechargeYJ(String shopUnique,String cusUnique,Integer rechargeType,Double rechargeMoney) {
		PurResult pr = new PurResult(1, "操作成功!");
		
		Map<String,Object> map = new HashMap<String,Object>();
		map.put("shopUnique", shopUnique);
		map.put("cusUnique", cusUnique);
		map.put("cusType", "储");
		//查询会员当前余额信息
		Map<String,Object> cusMap = manDao.cusDetailMessageQuery(map);
		if(null == cusMap || cusMap.isEmpty()) {
			pr.setStatus(0);
			pr.setMsg("没有满足条件的会员信息!");
			return pr;
		}
		
		Subject subject = SecurityUtils.getSubject();
		Session session = subject.getSession();
		Staff staff = (Staff) session.getAttribute("staff");
		
		//比较余额，防止退卡过程中，余额发生变化
		if(rechargeType == 4 || rechargeType == 5) {
			//需要退款，判断余额是否足够
			Double cusBalance = Double.parseDouble(cusMap.get("cusBalance").toString());
			if(cusBalance.compareTo(rechargeMoney) < 0) {
				pr.setStatus(0);
				pr.setMsg("卡内余额不足，请刷新页面重新获取会员信息！");
				return pr;
			}
		}
		
		
		//充值记录
		map.put("cus_id", cusMap.get("cus_id"));
		map.put("recharge_money", rechargeMoney);
		BigDecimal temRecharge = new BigDecimal(rechargeMoney);
		BigDecimal temBalance = new BigDecimal(cusMap.get("cusBalance").toString());
		BigDecimal temCusAmount = new BigDecimal(cusMap.get("cusAmount").toString());
		map.put("cus_balance", temBalance.add(temRecharge).setScale(2,BigDecimal.ROUND_HALF_UP));
		map.put("cus_amount", temCusAmount.add(temRecharge).setScale(2,BigDecimal.ROUND_HALF_UP));
		map.put("cus_type", 1);
		map.put("sale_list_unique", System.currentTimeMillis());
		map.put("saleListCashier", staff.getStaff_id());
		map.put("shop_unique", staff.getShop_unique());
		//现金充值
		map.put("rechargeMoney", rechargeMoney);
		if(rechargeType == 1) {
			map.put("recharge_method", "1");
			map.put("remarks", "线下充值");
			map.put("data_type", "1");
		}else if(rechargeType == 2) {
			map.put("recharge_method", "10");
			map.put("remarks", "会员换卡充值!");
			map.put("data_type", "2");
		}else if(rechargeType == 3) {
			map.put("recharge_method", "9");
			map.put("remarks", "线下充值");
			map.put("data_type", "1");
		}else if(rechargeType == 4) {
			map.put("cus_type", 1);
			map.put("recharge_method", "1");
			map.put("remarks", "因错退款");
			map.put("data_type", "1");
			map.put("rechargeMoney", -rechargeMoney);
		}else if(rechargeType == 5) {
			map.put("cus_type", 2);
			map.put("recharge_method", "5");
			map.put("remarks", "退款取现");
			map.put("data_type", "1");
			map.put("rechargeMoney", -rechargeMoney); 
		}else if(rechargeType == 6) {
			map.put("recharge_method", "3");
			map.put("remarks", "线下充值");
			map.put("data_type", "1");
		}else if(rechargeType == 7) {
			map.put("recharge_method", "2");
			map.put("remarks", "线下充值");
			map.put("data_type", "1");
			
		}
		map.put("shop_unique", shopUnique);
		map.put("recharge_status", 1);
		map.put("give_money", 0);
		
		Integer i = manDao.addNewRechargeRecord(map);
		
		//修改会员信息
		if(i == 1) {
			map.put("cusType","储");
			manDao.modifyCusBalance(map);
		}
		
		return pr;
	}
	
	/**
	 * 远见餐厅查询店铺内消费信息
	 * @param map
	 * @return
	 */
	public PurResult customerSummaryCYByGroup(Map<String,Object> map) {
		PurResult pr = new PurResult(1,"查询成功！");
		
		//查询各个员工的消费金额
		List<CustomerSummaryCYByGroup> list = manDao.customerSummaryCYByGroup(map);
		
		pr.setData(list);
		
		pr.setTotal(manDao.customerSummaryCYByGroupCount(map));
		
		return pr;
	}
	
	/**
	 * 获取满足条件的会员信息
	 * @param shopUnique 店铺编号
	 * @param type 类型：1、预览；2、实际操作
	 * @param min_points 满足条件的最小积分
	 * @param min_money 满足条件的最小本金金额
	 * @param cus_msg 会员手机号或会员卡号或会员名称
	 */
	public List<Map<String,Object>> downLoadRedeenPointsDetail(String shopUnique,
			Integer type,Double min_points,Double min_money,String cus_msg,String filePath) {
		Map<String,Object> map = new HashMap<String,Object>();
		map.put("shopUnique", shopUnique);
		map.put("min_points", min_points);
		map.put("min_money", min_money);
		map.put("cus_msg", cus_msg);
		
		//获取对应的会员信息列表
		List<Map<String,Object>> list = manDao.downLoadRedeenPointsDetail(map);
		if(type == 2) {
			Date date = new Date();
			String dateStr = date.getYear() + "-" + (date.getMonth() + 1) + "-" + date.getDay();
			//将会员积分依次转换为余额
			Map<String,Object> resmap = new HashMap<String,Object>();
			resmap.put("total", list.size());
			resmap.put("filePath", filePath);
			resmap.put("now", 0);
			rc.putObject("downLoadSpeedOfProgress", resmap, 24*3600);
			Integer count = 0;
			for(Map<String,Object> tm : list) {
				count++;
				resmap.put("now", count);
				rc.putObject("downLoadSpeedOfProgress", resmap, 24*3600);
				//添加会员充值记录
				tm.put("data_type", "1");
				tm.put("recharge_money", "0");
				tm.put("cus_type", "1");
				tm.put("recharge_method", 1);
				tm.put("remarks", "积分兑换：兑换比例100:1，兑换日期：" + dateStr);
				tm.put("data_type", "1");
				tm.put("shopUnique", shopUnique);
				tm.put("shop_unique", shopUnique);
				tm.put("recharge_status", 1);
				manDao.addCusRechargeMsg(tm);
				//添加会员充值使用记录
				tm.put("recharge_balance", 0);
				manDao.addCusRechargeUseMsg(tm);
				
				//增加会员余额，减少积分，增加充值记录累计金额，增加积分使用金额
				manDao.clearCusPoints(tm);
				
			}
			
		}
		
		return list;
	}
	
	/**
	 * 更新充值配置信息
	 * @param lottery
	 * @return
	 */
	public ShopsResult modifyLotterySet(NingyuLottery lottery) {
		ShopsResult sr = new ShopsResult(1,"更新成功！");
		manDao.modifyLotterySet(lottery);
		manDao.modifyLotteryMoney(lottery.getList());
		
		return sr;
	}
	
	public Map<String,Object> getLotterySet(){
		return manDao.getLotterySet();
	}
	
	public ShopsResult queryNYLotteryMsg() {
		ShopsResult sr = new ShopsResult();
		
		List<Map<String,Object>> list = manDao.queryNYLotteryMsg();
		BigDecimal t = new BigDecimal(0);
		if(null != list && !list.isEmpty()) {
			for(Map<String,Object> map : list) {
				t = t.add(new BigDecimal(map.get("ratio").toString()));
			}
			
			for(Map<String,Object> map: list) {
				BigDecimal change = new BigDecimal(map.get("ratio").toString());
				change = change.multiply(new BigDecimal(100)).divide(t,2,BigDecimal.ROUND_HALF_UP);
				map.put("change", change);
			}
		}
		sr.setData(list);
		
		return sr;
	}
	
	public List<CustomerSummaryByGroup> downLoadCustomerSummaryList(Map<String,Object> map,Integer groupType){
		List<CustomerSummaryByGroup> list;
		if(groupType == 1) {
			list = manDao.customerSummaryByGroupForShop(map);
		}else {
			list = manDao.customerSummaryByGroupForDate(map);
		}
		
		return list;
	}
	
	public PurResult customerSummaryByGroup(Map<String,Object> map,Integer groupType) {
		PurResult pr = new PurResult(1, "查询成功！");
		
		List<CustomerSummaryByGroup> list;
		if(groupType == 1) {
			list = manDao.customerSummaryByGroupForShop(map);
		}else {
			list = manDao.customerSummaryByGroupForDate(map);
		}
		
		pr.setCount(manDao.customerSummaryByGroupCount(map));
		pr.setData(list);
		return pr;
	}
	
	public PurResult queryCusRechargeList(Map<String,Object> map) {
		PurResult result = new PurResult();
		Subject subject = SecurityUtils.getSubject();
		Session session = subject.getSession();
		Staff staff = (Staff) session.getAttribute("staff");
		List<Map<String,Object>> list = manDao.queryCusRechargeList(map);
		List<Map<String,Object>> shopList ;
		if(staff.getShop_type() == 12) {
			shopList = manDao.queryCusRechargeGroupYJ(map);
		}else if(staff.getShop_type() == 6){
			
			shopList = manDao.queryCusRechargeGroup(map);
		}else {
			shopList = new ArrayList<>();
		}
		result.setStatus(1);
		result.setMsg("查询成功！");
		result.setData(list);
		Map<String,Object> m = manDao.queryCusRechargeListCount(map);
		result.setCount(Integer.parseInt(m.get("count")==null? "0": m.get("count").toString()));
		List<Map<String,Object>> detailList = manDao.queryCusRechargeStatisByMethod(map);
		result.setCord(detailList);
		result.setRows(shopList);
		return result;
	}
	
	/**
	 * 
	 * @param shopUnique 店铺编号
	 * @param cusMessage 会员信息，手机好，名称或会员编号
	 * @param startTime 开始查询时间
	 * @param endTime 截至查询时间
	 * @param cusStatus 会员状态：-1、全部；0、审核不通过；1、审核通过；2、待审核
	 * @param page 页码
	 * @param limit 每页查询数量
	 * @return
	 */
	public PurResult queryWJCusCheckOut(String shopUnique,String cusMessage,String startTime,String endTime,
			Integer cusStatus,Integer page,Integer limit) {
		PurResult pr = new PurResult(1, "查询成功！");
		Map<String,Object> map = new HashMap<String,Object>();
		map.put("shopUnique", shopUnique);
		map.put("startTime", startTime);
		map.put("endTime", endTime);
		if(null != cusMessage && !cusMessage.equals("")) {
			map.put("cusMessage", "%" + cusMessage + "%");
		}
		map.put("startNum", (page-1) * limit);
		map.put("pageSize", limit);
		map.put("cusStatus", cusStatus);
		
		pr.setData(manDao.queryWJCusCheckOut(map));
		pr.setCord(manDao.queryWJCusCheckOutCount(map));
		
		return pr;
	}
	
	
	public List<Map<String,Object>> queryCusSaleMsgByMonthExcel(Map<String,Object> map){
		List<Map<String,Object>> list = manDao.queryCusSaleMsgByMonth(map);
		return list;
	}
	
	/**
	 * 查询会员的月消费额
	 * @param map
	 * @return
	 */
	public PurResult queryCusSaleMsgByMonth(Map<String,Object> map) {
		PurResult result = new PurResult(1, "查询成功！");
		List<Map<String,Object>> list = manDao.queryCusSaleMsgByMonth(map);
		result.setData(list);
		Integer count = manDao.queryCusSaleMsgByMonthCount(map);
		result.setCount(count);
		return result;
	}
	/**
	 * 会员信息分页查询
	 * @param map
	 * @return
	 */
	public PurResult queryCusCheckOut(Map<String,Object> map,Integer shopType){
		PurResult result = new PurResult();
		try {
			map.put("shopType", shopType);
			List<Map<String,Object>> list = manDao.queryCusCheckOut(map);
	    	int count = manDao.cusCheckOutPages(map);
	    	result.setStatus(1);
			result.setMsg("成功");
			result.setCount(count);
			result.setData(list);
		} catch (Exception e) {
			e.printStackTrace();
			result.setStatus(0);
			result.setMsg("异常");
		}
		return result;
	}
	/**
	 * 线上会员信息分页查询
	 * @param map
	 * @return
	 */
	public PurResult queryCusOnlinePt(Map<String,Object> map){
		PurResult result = new PurResult();
		try {
			Map<String,Object> data=manDao.queryCusConfig();
			map.put("data1", data.get("data1"));
			map.put("data2", data.get("data2"));
			map.put("data3", data.get("data3"));
			map.put("data4", data.get("data4"));
			map.put("data4", data.get("data4"));
			map.put("data5", data.get("data5"));
			map.put("data6", data.get("data6"));
			map.put("data7", data.get("data7"));
			map.put("data8", data.get("data8"));
			map.put("data9", data.get("data9"));
			map.put("data99", data.get("data99"));
			map.put("data10", data.get("data10"));
			map.put("data11", data.get("data11"));
			map.put("data12", data.get("data12"));
			map.put("data13", data.get("data13"));
			map.put("data14", data.get("data14"));
			map.put("data144", data.get("data144"));
			map.put("data15", data.get("data15"));
			map.put("data16", data.get("data16"));
			map.put("data17", data.get("data17"));
			map.put("data18", data.get("data18"));
			map.put("data19", data.get("data19"));
			map.put("data20", data.get("data20"));
			map.put("data21", data.get("data21"));
			List<Map<String,Object>> list = manDao.queryCusOnlinePt(map);
	    	int count = manDao.queryCusOnlinePagesPt(map);
	    	result.setStatus(1);
			result.setMsg("成功");
			result.setCount(count);
			result.setData(list);
		} catch (Exception e) {
			e.printStackTrace();
			result.setStatus(0);
			result.setMsg("异常");
		}
		return result;
	}	
	/**
	 * 线上会员信息分页查询
	 * @param map
	 * @return
	 */
	public PurResult queryCusOnline(Map<String,Object> map){
		PurResult result = new PurResult();
		try {

			List<Map<String,Object>> list = manDao.queryCusOnline(map);
	    	int count = manDao.queryCusOnlinePages(map);
	    	result.setStatus(1);
			result.setMsg("成功");
			result.setCount(count);
			result.setData(list);
		} catch (Exception e) {
			e.printStackTrace();
			result.setStatus(0);
			result.setMsg("异常");
		}
		return result;
	}
	public PurResult getMemberLevel(Map<String,Object> map){
		PurResult result = new PurResult();
		try {
			List<Map<String,Object>> list = manDao.getMemberLevel(map);
	    	result.setStatus(1);
			result.setMsg("成功");
			result.setData(list);
		} catch (Exception e) {
			e.printStackTrace();
			result.setStatus(0);
			result.setMsg("异常");
		}
		return result;
	}
	
	/**
	 * 会员详情查询
	 * @param map
	 * @return
	 */
	public ShopsResult cusDetailMessageQuery(Map<String,Object> map){
			ShopsResult sr=new ShopsResult();
			Map<String,Object> data=manDao.cusDetailMessageQuery(map);
			if(null==data||data.isEmpty()){
				sr.setStatus(2);
				sr.setMsg("没有满足条件的详情！");
				return sr;
			}
			sr.setStatus(1);
			sr.setMsg("查询成功！");
			sr.setData(data);
			return sr;
	}
	
	
	/**
	 * 会员详情更新
	 * @param map
	 * @return
	 */
	@Transactional
	public ShopsResult saveCusMessage(Map<String,Object> map,HttpServletRequest request,String shopUnique,String cusType,String oldCusType){
		
		ShopsResult sr=new ShopsResult();
		Integer oldCusId = Integer.parseInt(map.get("cusId").toString());
		map.containsKey("powerCredit");
		System.out.println(map.get("powerCredit"));
		Integer powerCredit = map.containsKey("powerCredit") ? Integer.parseInt(map.get("powerCredit").toString()) : 0;
		Double creditLimit = map.containsKey("creditLimit") ? Double.parseDouble(map.get("creditLimit").toString()) : 0.0;
		MultipartFile file=ShopsUtil.testMulRequest(request, "cus_head_path");
		int kk=0;
		if(null!=file){
			String orName=file.getOriginalFilename();
			String savePath = request.getServletContext().getRealPath("");
			savePath = savePath.substring(0,savePath.lastIndexOf(request.getServletContext().getContextPath().substring(1)));
			
			/*String filePath=File.separator + "home" + File.separator + "apache-tomcat-7.0.42"
					+ File.separator + "webapps" + File.separator + "image"+File.separator+shopUnique;*/
			orName = UUID.randomUUID().toString();
			String filePath=savePath + "image"+File.separator+shopUnique;
			boolean flag=ShopsUtil.savePicture(file, filePath, orName,"2");
			if(flag){//保存成功！
				map.put("cusHeadPath", "image"+File.separator+shopUnique+File.separator+orName);
			}
		}


		if(cusType.equals(oldCusType)){

		}else if(oldCusType.equals("共")){
			//需要删除其中一种会员状态
			if(cusType.equals("会")){
				map.put("cusType","储");
			}else{
				map.put("cusType","会");
			}
			//保存需要删除的信息，用作备份；
			kk=manDao.copyDeleteCusMessage(map);
			//删除不需要的信息
			if(kk!=0){//备份不成功则不删除
				kk=manDao.deleteShopCus(map);
			}
			//删除后，更新剩余的信息
			map.put("cusType",cusType);
		}else if(cusType.equals("共")){
			//需要增加一种状态
			//需要删除其中一种会员状态
			if(oldCusType.equals("会")){
				map.put("cusType","储");
			}else{
				map.put("cusType","会");
			}
			kk=manDao.addNewCus(map);
			//添加后更新已有的信息
			map.put("cusType",oldCusType);
		}else{
			map.put("cusType", cusType);
		}
		map.remove("cusId");
		int k= manDao.saveCusMessage(map);

		//获取当前会员的储值会员ID，
		Integer cusId = manDao.queryCusChuId(oldCusId);
		if(null != cusId){
			//查询是否有配置信息，如果有，更新，如果没有，创建
			Map<String,Object> cusSet = manDao.queryCusSet(cusId);
			if(ObjectUtil.isNotEmpty(cusSet)){
				cusSet.put("powerCredit", powerCredit);
				cusSet.put("creditLimit", creditLimit);

				//更新配置
				manDao.updateCusSet(cusSet);
			}

			if(ObjectUtil.isEmpty(cusSet)){
				cusSet = new HashMap<>();
				cusSet.put("cusId",cusId);
				cusSet.put("powerCredit", powerCredit);
				cusSet.put("creditLimit", creditLimit);

				manDao.addNewCusSet(cusSet);
			}
		}

		if(k==0){
			sr.setStatus(2);
			sr.setMsg("更新失败！");
			return sr;
		}
		sr.setStatus(1);
		sr.setMsg("更新成功");
		return sr;
	}
	
	/**
	 * 会员充值记录查询
	 * @param map
	 * @return
	 */
	public PurResult queryRechargeRecord(Map<String,Object> map){
		PurResult result = new PurResult();
		try {
			List<Map<String,Object>> list = manDao.queryRechargeRecord(map);
	    	Integer count = manDao.rechargeRecordPages(map);
	    	result.setStatus(1);
			result.setMsg("成功");
			result.setCount(count);
			result.setData(list);
		} catch (Exception e) {
			e.printStackTrace();
			result.setStatus(0);
			result.setMsg("异常");
		}
		return result;
	}
	
	public PurResult queryBuyRecord(Map<String,Object> map){
		PurResult result = new PurResult();
		try {
			List<Map<String,Object>> list = manDao.queryBuyRecord(map);
	    	Integer count = manDao.queryBuyRecordCount(map);
	    	result.setStatus(1);
			result.setMsg("成功");
			result.setCount(count);
			result.setData(list);
		} catch (Exception e) {
			e.printStackTrace();
			result.setStatus(0);
			result.setMsg("异常");
		}
		return result;
	}
	
	public PurResult queryBackRecord(Map<String,Object> map){
		PurResult result = new PurResult();
		try {
			List<Map<String,Object>> list = manDao.queryBackRecord(map);
	    	Integer count = manDao.queryBackRecordCount(map);
	    	result.setStatus(1);
			result.setMsg("成功");
			result.setCount(count);
			result.setData(list);
		} catch (Exception e) {
			e.printStackTrace();
			result.setStatus(0);
			result.setMsg("异常");
		}
		return result;
	}
	
	/**
	 * 会员消费记录查询
	 * @param map
	 * @return
	 */
	public PurResult queryComsumptionRecord(Map<String,Object> map){
		PurResult result = new PurResult();
		try {
			List<Map<String,Object>> list = manDao.queryComsumptionRecord(map);
	    	Integer count = manDao.queryComsumptionRecordPages(map);
	    	result.setStatus(1);
			result.setMsg("成功");
			result.setCount(count);
			result.setData(list);
		} catch (Exception e) {
			e.printStackTrace();
			result.setStatus(0);
			result.setMsg("异常");
		}
		return result;
	}
	
	/**
	 * 添加店铺会员信息
	 * @param map
	 * @return
	 */
	@Transactional
	public ShopsResult addNewCus(Map<String,Object> map,HttpServletRequest request,String shopUnique){
		ShopsResult sr=new ShopsResult();
		Map<String,Object> L=manDao.cusDetailMessageQuery(map);
		if(null!=L){
			sr.setStatus(2);
			sr.setMsg("该会员号已存在!");
			return sr;
		}
		MultipartFile file=ShopsUtil.testMulRequest(request, "cus_head_path");
		if(null!=file){
			String orName=file.getOriginalFilename();
			String savePath = request.getServletContext().getRealPath("");
			savePath = savePath.substring(0,savePath.lastIndexOf(request.getServletContext().getContextPath().substring(1)));
			
			/*String filePath=File.separator + "home" + File.separator + "apache-tomcat-7.0.42"
					+ File.separator + "webapps" + File.separator + "image"+File.separator+shopUnique;*/
			orName = UUID.randomUUID().toString();
			String filePath=savePath + "image"+File.separator+shopUnique;
			boolean flag=ShopsUtil.savePicture(file, filePath, orName,"2");
			if(flag){//保存成功！
				map.put("cusHeadPath", "image"+File.separator+shopUnique+File.separator+orName);
			}
		}
		
		
		int k=0;
		if(map.get("cusType").equals("共")){
			map.put("cusType","会");
			k+=manDao.addNewCus(map);
			map.put("cusType","储");
			k+=manDao.addNewCus(map);
		}else{
			k+=manDao.addNewCus(map);
		}
		if(k==0){
			sr.setStatus(2);
			sr.setMsg("会员添加成功！");
			return sr;
		}
		sr.setStatus(1);
		sr.setMsg("添加成功！");
		return sr;
	}
	
	/**
	 * 会员充值
	 * @param map
	 * @return
	 */
	@Transactional
	public ShopsResult cusRecharge(Map<String,Object> map){
		ShopsResult sr=new ShopsResult();
		int k=0;
		k=manDao.cusRecharge(map);
		if(k==0){
			sr.setStatus(2);
			sr.setMsg("充值失败，该ID不存在");
			return sr;
		}
		k=manDao.newRechareRecord(map);
		if(k==0){
			sr.setStatus(1);
			sr.setMsg("充值成功！添加充值记录失败！");
			return sr;
		}
		sr.setStatus(1);
		sr.setMsg("冲值成功！");
		return sr;
	}
	/**
	 * 取现
	 * @param map
	 * @return
	 */
	@Transactional
	public ShopsResult sureTakeNow(Map<String,Object> map,Double takeNowMoney){
		ShopsResult sr=new ShopsResult();
		Double money=manDao.getCusMoney(map);
		if(takeNowMoney>money){
			sr.setStatus(2);
			sr.setMsg("您的帐户余额不足！");
			return sr;
		}
		int k=0;
		//取现，将会员余额减少
		k=manDao.sureTakeNow(map);
		if(k==0){
			sr.setStatus(2);
			sr.setMsg("充值失败，该ID不存在");
			return sr;
		}
		//添加新的取现记录
		k=manDao.newRechareRecord(map);
		if(k==0){
			sr.setStatus(1);
			sr.setMsg("充值成功！添加充值记录失败！");
			return sr;
		}
		sr.setStatus(1);
		sr.setMsg("取现成功！");
		return sr;
	}
	
	/**
	 * 管理员旗下所有管理员查询
	 * @param map
	 * @return
	 */
	public ShopsResult queryManagerStaffs(Map<String,Object> map){
		ShopsResult sr=new ShopsResult();
		List<Map<String,Object>> data=manDao.queryManagerStaffs(map);
		if(null==data||data.isEmpty()){
			sr.setStatus(1);
			sr.setMsg("没有相关信息！");
			return sr;
		}
		sr.setStatus(1);
		sr.setMsg("查询");
		sr.setData(data);
		return sr;
	}
	
	/**
	 * 会员等级信息查询
	 * @param map
	 * @return
	 */
	public ShopsResult queryCusLevel(Map<String,Object> map){
		ShopsResult sr=new ShopsResult();
		List<Map<String,Object>> data=manDao.queryCusLevel(map);
		if(null==data||data.isEmpty()){
			sr.setStatus(2);
			sr.setMsg("没有相关信息！");
			return sr;
		}
		sr.setStatus(1);
		sr.setMsg("查询成功！");
		sr.setData(data);
		return sr;
	}
	
	public List<Map<String,Object>> queryCusLevelList(Map<String,Object> map){
		List<Map<String,Object>> data=manDao.queryCusLevel(map);
		return data;
	}

	@Override
	public Map<String ,Object> platformCustomerDetail(String cus_unique) {
		return manDao.platformCustomerDetail(cus_unique);
	}
	
	@Override
	public PurResult queryRechargeRecordPlatform(Map<String, Object> map) {
		PurResult result = new PurResult();
		try {
			List<Map<String,Object>> list = manDao.queryRechargeRecordPlatform(map);
			Integer count = manDao.rechargeRecordPlatformPages(map);
			result.setStatus(1);
			result.setMsg("成功");
			result.setCount(count);
			result.setData(list);
		} catch (Exception e) {
			e.printStackTrace();
			result.setStatus(0);
			result.setMsg("异常");
		}
		return result;
	}

	@Override
	public ShopsResult queryComsumptionRecordPlatformPages(Map<String, Object> map) {
		ShopsResult sr=new ShopsResult();
		int data=manDao.queryComsumptionRecordPlatformPages(map);
		sr.setStatus(1);
		sr.setMsg("查询成功！");
		sr.setData(data);
		return sr;
	}

	@Override
	public ShopsResult queryComsumptionRecordPlatform(Map<String, Object> map) {
		ShopsResult sr=new ShopsResult();
		List<Map<String,Object>> data=manDao.queryComsumptionRecordPlatform(map);
		if(null==data||data.isEmpty()){
			sr.setStatus(2);
			sr.setMsg("没有满足条件的会员消费信息！");
			return sr;
		}
		sr.setStatus(1);
		sr.setMsg("查询成功！");
		sr.setData(data);
		return sr;
	}
	
	/**
	 * 删除会员信息
	 * @param map
	 * @return
	 */
	@Transactional
	public ShopsResult deleteCustomer(Map<String,Object> map){
		ShopsResult sr=new ShopsResult(1,"删除成功");
		Integer c=manDao.deleteCustomer(map);
		if(c==0){
			sr.setStatus(2);
			sr.setMsg("删除的用户信息不存在");
		}
		return sr;
	}
	
	@Transactional
	public ShopsResult updateMemberLevel(Map<String,Object> map){
		ShopsResult sr=new ShopsResult(1,"修改成功!");
		Integer c=manDao.updateMemberLevel(map);
		if(c==0){
			sr.setStatus(2);
			sr.setMsg("修改失败!");
		}
		return sr;
	}
	
	public List<Map<String,Object>> downCusExcel(String shopUnique,Integer sameType){
		Map<String,Object> map = new HashMap<String,Object>();
		map.put("shopUnique", shopUnique);
		map.put("sameType", sameType);
		return manDao.getCustomerMsgForDown(map);
	}

	@Override
	public Map<String, Object> getMemberLevelById(Map<String, Object> map) {
		return manDao.getMemberLevelById(map);
	}
	
	@Override
	public Map<String, Object> queryCusLifeCycle(int id) {
		return manDao.queryCusLifeCycle(id);
	}
	/**
	 * 平台会员消费记录查询
	 * @param map
	 * @return
	 */
	public PurResult queryComsumptionRecord2(Map<String,Object> map){
		PurResult result = new PurResult();
		try {
			List<Map<String,Object>> list = manDao.queryComsumptionRecord2(map);
	    	Integer count = manDao.queryComsumptionRecordPages2(map);
	    	result.setStatus(1);
			result.setMsg("成功");
			result.setCount(count);
			result.setData(list);
		} catch (Exception e) {
			e.printStackTrace();
			result.setStatus(0);
			result.setMsg("异常");
		}
		return result;
	}
	
	/**
	 * 修改生命周期配置
	 * @param map
	 * @return
	 */
	@Transactional
	public ShopsResult updateCusLifeCycle(Map<String,Object> map){
		ShopsResult result = new ShopsResult();
		String type=map.get("id").toString();
		int num=0;
		if(type.equals("2"))
		{
			//
			num=manDao.updateCusLifeCycle(map);
		}else
		{
			num=manDao.updateCusActivity(map);
		}
		
	    if(num>0)
	    {
	    	result.setStatus(1);
			result.setMsg("成功");
	    }else
	    {
	    	result.setStatus(0);
	    	result.setMsg("操作失败！");
	    }
		

		return result;
	}
	@Override
	public PurResult queryCusRenewList(Map<String, Object> params) {
		PurResult result=new PurResult();			
		List<Map<String,Object>> list=manDao.queryCusRenewList(params);
		System.out.println(list);
		result.setStatus(1);
		result.setMsg("查询成功");
		result.setCount(manDao.queryCusRenewListPageCount(params));
		result.setData(list);
		return result;
	}
	
	public PurResult queryCusConsumptionList(Map<String,Object> map) {
		PurResult result = new PurResult();
		List<Map<String,Object>> list = manDao.queryCusConsumptionList(map);
		List<String> uniqueList = new ArrayList<String>();
		List<Map<String,Object>> retList =null; 
		if(null != list && !list.isEmpty()) {
			for(Map<String,Object> tempMap : list) {
				uniqueList.add(tempMap.get("saleListUnique").toString());
			}
			
			retList = manDao.queryCusConsumptionListret(uniqueList);
			
			for(Map<String,Object> tempMap : list) {
				String unique = tempMap.get("saleListUnique").toString();
				for(Map<String, Object> retTemp : retList) {
					if(retTemp.get("sale_list_unique").toString().equals(unique)) {
						Double rechargeMoney = UtilForJAVA.subDouble(tempMap.get("recharge_money"), retTemp.get("recharge_money"));
						tempMap.put("recharge_money", rechargeMoney);
						Double giveMoney = UtilForJAVA.subDouble(tempMap.get("give_money"), retTemp.get("give_money"));
						tempMap.put("give_money", giveMoney);
						tempMap.put("payMoney", UtilForJAVA.subDouble(tempMap.get("payMoney"), UtilForJAVA.addDouble(retTemp.get("recharge_money"), retTemp.get("give_money"),2)));
						break;
					}
				}
			}
		}
		result.setStatus(1);
		result.setMsg("查询成功！");
		result.setData(list);
		Map<String,Object> m = manDao.queryCusConsumptionListCount(map);
		Map<String,Object> ma = manDao.queryCusConsumptionListCountret(map);
		Double retMoney = 0.00;
		Double retGiveMoney = 0.00;
		Double sum = 0.00;
		if(null != ma) {
			retMoney = ma.get("retMoney") == null ? 0.00:Double.parseDouble(ma.get("retMoney").toString());
			retGiveMoney = ma.get("retGiveMoney") == null ? 0.00:Double.parseDouble(ma.get("retGiveMoney").toString());
			sum = UtilForJAVA.addDouble(retGiveMoney, retMoney);
		}
		m.put("rechargeMoney",UtilForJAVA.subDouble(m.get("rechargeMoney"), retMoney));
		m.put("giveMoney", UtilForJAVA.subDouble(m.get("giveMoney"),retGiveMoney));
		m.put("sum", UtilForJAVA.subDouble(m.get("sum"), sum));
		result.setCount(Integer.parseInt(m.get("count").toString()));
		result.setCord(m);
		return result;
	}
	
	public PurResult queryCusConsumptionListTotal(Map<String,Object> map) {
		PurResult result = new PurResult();
		
		return result;
	}
	public ShopsResult auditWJCusCheckOut(Map<String,Object> map){
		ShopsResult sr=new ShopsResult(1,"修改成功!");
		Integer c=manDao.auditWJCusCheckOut(map);
		if(c==0){
			sr.setStatus(2);
			sr.setMsg("修改失败!");
		}
		return sr;
	}
	public ShopsResult updateWJCusCheckOut(Map<String,Object> map){
		ShopsResult sr=new ShopsResult(1,"修改成功!");
		Integer c=manDao.updateWJCusCheckOut(map);
		if(c==0){
			sr.setStatus(2);
			sr.setMsg("修改失败!");
		}
		return sr;
	}

	@Override
	public PurResult queryCusPointHistory(Map<String, Object> map) {
		PurResult result = new PurResult();
		try {
			List<Map<String,Object>> list = manDao.queryCusPointHistory(map);
	    	Integer count = manDao.queryCusPointHistoryCount(map);
	    	result.setStatus(1);
			result.setMsg("成功");
			result.setCount(count);
			result.setData(list);
		} catch (Exception e) {
			e.printStackTrace();
			result.setStatus(0);
			result.setMsg("异常");
		}
		return result;
	}

	@Override
	public ShopsResult updatePointClearConfig(Map<String, Object> map) {
		ShopsResult sr=new ShopsResult(1,"成功!");
		if(map.get("id")!=null&&!"".equals(map.get("id"))){
			manDao.updatePointClearConfig(map);
		}else{
			manDao.savePointClearConfig(map);
		}
		return sr;
	}

	@Override
	public Map<String, Object> queryPointClear(String shop_unique) {
		Map<String,Object> map=new HashMap<String, Object>();
		map.put("shop_unique", shop_unique);
		return manDao.queryPointClear(map);
	}

	@Transactional
	@Override
	public void clearCusPoint() {
		//查询开启定时的店铺
		List<Map<String,Object>> shop_list=  manDao.queryClearCusPointShop();
		SimpleDateFormat date=new SimpleDateFormat("yyyy-MM-dd HH");
		Date now=new Date();
		String now2 = date.format(now);  //当前日期
		System.out.println(now2);
		for (Map<String, Object> map : shop_list) {
			int month=Integer.parseInt(map.get("month").toString());
			int day=Integer.parseInt(map.get("day").toString());
			int hour=Integer.parseInt(map.get("hour").toString());
			Calendar c = Calendar.getInstance();
			if(month!=-1){
				  c.set(Calendar.MONTH, month-1);
			}
			if(day!=-1){
				 c.set(Calendar.DATE, day);
			 }else{
				 c.set(Calendar.MONTH, c.get(Calendar.MONTH)+1);
				 c.set(Calendar.DAY_OF_MONTH,0);
			 }
			 if(hour!=-1){
				 c.set(Calendar.HOUR_OF_DAY, hour);
			 } 
			 String currentDate = date.format(c.getTime());  //当前日期
			 if(now2.equals(currentDate)){
				 System.out.println("当前时间"+now2+"清零日期 = " +currentDate);
				 System.out.println("清零"+map.toString());
				 //查询店铺会员
				 List<Map<String,Object>> cus_list= manDao.queryCusList(map);
				 //保存清零日志
				 for (Map<String, Object> cus : cus_list) {
					 cus.put("cus_type", 4);
					 manDao.savePointClearLog(cus);
				}
				 //清零
				 manDao.updateClearPoint(map);
			 }
			 
		}
		
	}

	@Override
	public List<Map<String, Object>> downloadNYRechargeLogExcel(Map<String, Object> map) {
		List<Map<String,Object>> list = manDao.queryCusRechargeList(map);
		return list;
	}

	@Override
	public List<Map<String, Object>> downloadHistoryCusTotalExcel(Map<String, Object> map) {
		List<Map<String,Object>> statisList = manDao.queryCusRechargeStaticByShop(map);
		return statisList;
	}

 }
