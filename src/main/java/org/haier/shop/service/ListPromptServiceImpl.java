package org.haier.shop.service;

import java.util.List;

import javax.annotation.Resource;

import org.haier.shop.dao.ListPromptDao;
import org.haier.shop.entity.ListPrompt;
import org.haier.shop.util.ShopsResult;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;


@Service
public class ListPromptServiceImpl implements ListPromptService {
	@Resource
	private ListPromptDao lpDao;
	
	/**
	 * 查询有效的订单数量提示信息
	 * @return
	 */
	public ShopsResult getValidPromptRule(){
		ShopsResult sr=new ShopsResult(1, "查询成功！");
		ListPrompt listPrompt=new ListPrompt();
		List<ListPrompt> list=lpDao.getValidPromptRule(listPrompt);
		if(null==list||list.isEmpty()){
			sr.setStatus(2);
			sr.setMsg("没有有效的提示信息");
		}
		sr.setData(list);
		return sr;
	}
	
	/**
	 * 将已有规则设置为无效状态
	 * @return
	 */
	@Transactional
	public ShopsResult deleteListPrompt(ListPrompt listPrompt){
		ShopsResult sr=new ShopsResult(1,"删除成功");
		listPrompt.setValidType(0);//设置为失效状态
		Integer count=lpDao.updateListPrompt(listPrompt);
		if(count==0){
			sr.setMsg("该规则已删除");
			sr.setStatus(2);
		}
		return sr;
	}
	
	/**
	 * 添加或更新规则信息
	 * @param listPrompt
	 * @return
	 */
	@Transactional
	public ShopsResult modifyListPrompt(ListPrompt listPrompt){
		ShopsResult sr=new ShopsResult(1, "操作成功！");
		Integer count=0;
		//需做一个防重查询
		List<ListPrompt> list=lpDao.getValidPromptRule(listPrompt);
		//必填信息帅选
		if(listPrompt.getListCount()==null||listPrompt.getListCount()<=0){
			sr.setStatus(2);
			sr.setMsg("订单数量为空或订单数量不大于");
			return sr;
		}
		if(listPrompt.getPromptInfo()==null||listPrompt.getPromptInfo().equals("")){
			sr.setStatus(2);
			sr.setMsg("提示信息不能为空");
			return sr;
		}
		if(list!=null&&!list.isEmpty()){
			sr.setStatus(2);
			sr.setMsg("该订单数量的规则信息已存在");
			return sr;
		}
		if(listPrompt.getId()!=null){
			//更新提示信息
			count=lpDao.updateListPrompt(listPrompt);
			if(count==0){
				sr.setStatus(2);
				sr.setMsg("未更改");
			}
		}else{
			//添加提示信息
			count=lpDao.addListPrompt(listPrompt);
			if(count==0){
				sr.setStatus(2);
				sr.setMsg("添加失败");
			}
		}
		sr.setData(count);
		return sr;
	}
	
	public ShopsResult clearListPrompt(){
		ShopsResult sr=new ShopsResult(1,"操作成功");
		if(lpDao.clearListPrompt()==0){
			sr.setStatus(2);
			sr.setMsg("清空失败");
		}
		return sr;
	}
}
