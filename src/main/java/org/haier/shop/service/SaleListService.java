package org.haier.shop.service;

import org.haier.customer.entity.ShopResult;
import org.haier.shop.entity.SaleListMain;
import org.haier.shop.util.PurResult;
import org.haier.shop.util.ShopsResult;

import javax.servlet.http.HttpServletRequest;
import java.util.List;
import java.util.Map;

public interface SaleListService {
	
	/**
	 * 1、查询各分店的营业信息
	 * @param manager_unique 店铺管理员帐户
	 * @param page 页码
	 * @param limit 单页查询数量
	 * @param startTime 开始查询时间
	 * @param endTime 结束查询时间
	 * @param shopList 查询的店铺列表
	 * @return
	 */
	public ShopsResult statisticsForShopByPage(String manager_unique,Integer page,Integer limit,String startTime,
			String endTime,String shopList,String field,String order);
	
	public ShopsResult queryXXsale(Map<String,Object> map);

	public ShopsResult queryTimeSale(Map<String,Object> map);
	
	
	public ShopsResult updateGoodsList();
	/**
	 * 查询退款订单详情
	 * @param retListUnique
	 * @return
	 */
	public ShopsResult queryReturnDetail(String retListUnique);
	/**
	 * 修改退款订单状态
	 * @param retListUnique 退款单号
	 * @param retListHandlestate 退款审核状态
	 * @param retListRemarks 如果拒绝退款，需要填写拒绝原因
	 * @param staffId 操作员工ID
	 * @param macId 操作设备的macId或浏览器型号
	 * @return
	 */
	public ShopsResult modifyReturnMsg(String retListUnique, Integer retListHandlestate, String retListRemarks, String staffId, String macId) ;
	/**
	 * 查询退款订单的申请信息
	 * @param shopUnique 店铺编号
	 * @param page 页码
	 * @param limit 单页查询数量
	 * @param startTime 开始查询时间
	 * @param endTime 结束查询时间
	 * @return
	 */
	public ShopsResult queryRetLists(String shopUnique, Integer page, Integer limit, String startTime, String endTime, Integer retListHandlestate);
	
	public List<SaleListMain> saleListExcelDetailClass(Map<String,Object> map);
	/**
	 * 网单查询，去掉了充值统计和会员续费统计功能
	 * @param shop_unique
	 * @param orderMessage
	 * @param sale_list_handlestate
	 * @param startTime
	 * @param endTime
	 * @param sale_list_state
	 * @param orderName
	 * @param orderType
	 * @param pageNum
	 * @param pageSize
	 * @param staffId
	 * @param goodsMessage
	 * @param goods_kind_parunique
	 * @param goods_kind_unique
	 * @param cusType
	 * @param paymentMethod
	 * @return
	 */
	public PurResult querySaleListsNet(String shop_unique, String orderMessage, Integer sale_list_handlestate,
			String startTime, String endTime, Integer sale_list_state, String orderName, String orderType,
			Integer pageNum, Integer pageSize,String staffId,
			 String goodsMessage, String goods_kind_parunique, String goods_kind_unique,Integer cusType,Integer paymentMethod);
	/**
	 * 销售订单查询
	 * @param shop_unique
	 * @param orderMessage
	 * @param sale_list_handlestate
	 * @param startTime
	 * @param endTime
	 * @param sale_list_state
	 * @param goods_kind_unique 
	 * @param goods_kind_parunique 
	 * @param goodsMessage 
	 * @return
	 */
	public PurResult querySaleLists(String shop_unique,String orderMessage,Integer sale_list_handlestate,String startTime,
			String endTime,Integer sale_list_state,String orderName,String orderType,Integer pageNum,Integer pageSize,String staffId, 
			String goodsMessage, String goods_kind_parunique, String goods_kind_unique,Integer cusType,Integer paymentMethod);
	
	public PurResult querySaleNYLists(String shop_unique,String orderMessage,Integer sale_list_handlestate,String startTime,
			String endTime,Integer sale_list_state,String orderName,String orderType,Integer pageNum,Integer pageSize,String staffId, 
			String goodsMessage, String goods_kind_parunique, String goods_kind_unique,Integer cusType,Integer paymentMethod, Integer sourceType);
	
	public List<Map<String, Object>> querySaleLists2(String shop_unique,String orderMessage,Integer sale_list_handlestate,String startTime,
			String endTime,Integer sale_list_state,String orderName,String orderType,Integer staffId, String goodsMessage, String goods_kind_parunique, String goods_kind_unique);
	
	public PurResult queryShopOrderList(String manager_unique,String startTime,String endTime,Integer pageNum,Integer pageSize);
	
	/**
	 * 查询订单详情
	 * @param shop_unique
	 * @param sale_list_unique
	 * @return
	 */
	public ShopsResult queryOrderDetail(String shop_unique,String sale_list_unique,Integer loanStatus);
	
	/**
	 * 查询订单详情
	 * @param shop_unique
	 * @param sale_list_unique
	 * @return
	 */
	public ShopsResult queryOrderDetailJY(String shop_unique,String sale_list_unique,Integer loanStatus);
	
	/**
	 * 查询订单详情
	 * @param shop_unique
	 * @param sale_list_unique
	 * @return
	 */
	public ShopsResult queryFarmOrderDetail(String secretary_id,String sup_order_unique);
	
	public PurResult querySaleListsNY(Map<String,Object> map);
	
	
	/**
	 * 更新订单处理状态
	 * @param shop_unique
	 * @param sale_list_unique
	 * @param sale_list_handlestate
	 * @param sale_list_state
	 * @return
	 */
	public ShopsResult updateSaleList(String shop_unique,String sale_list_unique,Integer sale_list_handlestate,Integer sale_list_state);
	
	/**
	 * 主界面查询基本信息
	 * @param shop_unique
	 * @return
	 */
	public ShopsResult baseMessage(String shop_unique);
	
	/**
	 * 店铺营运信息查询
	 * @param map
	 * @return
	 */
	public ShopsResult salesTurnoverStatistics(Map<String,Object> map);
	
	/**
	 * 查询各月内，各分类销售情况汇总
	 * @param map
	 * @return
	 */
	public ShopResult typeSaleByTime(Map<String,Object> map);
	/**
	 * 各分数的订单数量统计
	 * @param map
	 * @return
	 */
	public ShopsResult saleListEvaluateQuery(Map<String,Object> map);
	
	/**
	 * 用户活跃度查询
	 * @param map
	 * @return
	 */
	public ShopsResult cusActivityQuery(Map<String,Object> map);
	
	/**
	 * 订单信息数量查询
	 * @param map
	 * @return
	 */
	public ShopsResult newOrdersCount(Map<String,Object> map);
	
	/**
	 * 订单详情EXCEL表生成并返回下载地址
	 * @param map
	 * @return
	 */
	public List<Map<String, Object>> saleListExcel(Map<String,Object> map,HttpServletRequest request,Long shopUnique);
	
	/**
	 * 订单详情EXCEL表生成并返回下载地址-农产品
	 * @param map
	 * @return
	 */
	public List<Map<String, Object>> farmListExcel(Map<String,Object> map,HttpServletRequest request);
	
	
	/**
	 * 订单详情EXCEL表生成并返回下载地址-宁宇
	 * @param map
	 * @return
	 */
	public List<Map<String, Object>> saleListNYExcel(Map<String,Object> map);
	
	public List<Map<String, Object>> saleListDetailYNExcel(Map<String,Object> map);
	/**
	 * 订票订单总览界面：页数查询
	 * @param map
	 * @return
	 */
	public ShopsResult queryListPages(Map<String,Object> map);
	
	/**
	 * 店铺订单总览界面：分页查询订单
	 * @param map
	 * @return
	 */
	public ShopsResult queryListByPage(Map<String,Object> map);
	
	/**
	 * 订单详情查询
	 * @param map
	 * @return
	 */
	public ShopsResult queryListDetail(Map<String,Object> map);
	public ShopsResult printOrderList(Map<String, Object> map, HttpServletRequest request, Long shopUnique);
	public Map<String, Object> queryOrderTotal(Map<String, Object> map);
	
	public List<Map<String,Object>> saleListExcelDetail(Map<String,Object> map);
	
	public PurResult getEvaluateList(Map<String,Object> map);
	
	public ShopsResult updateEvaluate(Map<String,Object> map);
	
	public List<Map<String,Object>> getEvaluateImage(Map<String,Object> map);
	
	/**
	 * 平台农产品销售订单查询
	 * @param shop_unique
	 * @param orderMessage
	 * @param sale_list_handlestate
	 * @param startTime
	 * @param endTime
	 * @param sale_list_state
	 * @param goods_kind_unique 
	 * @param goods_kind_parunique 
	 * @param goodsMessage 
	 * @return
	 */
	public PurResult queryFarmListsAll(String orderMessage,Integer handle_status,String startTime,
			String endTime,Integer secretary_id,Integer pay_status,String orderName,String orderType,Integer pageNum,Integer pageSize,Integer staffId, 
			String goodsMessage, String goods_kind_parunique, String goods_kind_unique,Integer cusType,Integer paymentMethod);
	public ShopsResult printFarmOrderList(Map<String, Object> map, HttpServletRequest request);
	public Map<String, Object> queryFarmOrderDetailSum(Map<String, Object> map);
	public PurResult querySelectPeiSong(String shop_unique);
	public ShopsResult updateSaleListStatus(String sale_list_unique);

    List<Map<String, Object>> selectGoodsDetailInfo(Map<String, Object> goodsParams);
}
