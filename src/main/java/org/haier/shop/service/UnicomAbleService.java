package org.haier.shop.service;

import java.util.Map;

import org.haier.shop.util.PurResult;

public interface UnicomAbleService {
	
	/**
	 * 查询能人列表
	 * @return
	 */
	public PurResult queryUnicomAbleList(Map<String,Object> params);
	
	/**
	 * 添加能人
	 * @return
	 */
	public PurResult addUnicomAble(Map<String,Object> params);
	
	/**
	 * 获取能人详情
	 * @return
	 */
	public Map<String ,Object> queryUnicomAbleDetail(Map<String,Object> params);
	
	/**
	 * 修改能人
	 * @return
	 */
	public PurResult updateUnicomAble(Map<String,Object> params);
	
	/**
	 * 删除能人
	 * @return
	 */
	public PurResult deleteUnicomAble(String unicom_able_id);
	
	/**
	 * 查询能人签单列表
	 * @return
	 */
	public PurResult queryUnicomAbleEsignList(Map<String,Object> params);
}
