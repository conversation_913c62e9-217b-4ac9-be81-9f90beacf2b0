package org.haier.shop.service;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.haier.shop.dao.FarmProductDao;
import org.haier.shop.entity.FarmProductShelf;
import org.haier.shop.entity.beans.PageData;
import org.haier.shop.entity.globalSelect.FarmKind;
import org.haier.shop.entity.globalSelect.GlobalDetailVO;
import org.haier.shop.oss.OSSUtil;
import org.haier.shop.redis.RedisCache;
import org.haier.shop.util.PurResult;
import org.haier.shop.util.ShopsResult;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;

import net.sf.json.JSONArray;
import net.sf.json.JsonConfig;

@Service
public class FarmProductService{
	
	@Resource
	private FarmProductDao farmProductDao;
	
	@Autowired
	private RedisCache redis;
	
	public PurResult addNewFarmKind(FarmKind farmKind,HttpServletRequest request,HttpServletResponse response) {
		PurResult result = new PurResult(1, "操作成功！");
		Integer count = 0;
		//图片信息保存
		Map<String, MultipartFile> mp= null;
		if( request instanceof MultipartHttpServletRequest){
			MultipartHttpServletRequest multipartHttpServletRequest =(MultipartHttpServletRequest)request;
			mp = multipartHttpServletRequest.getFileMap();
		}
		
		if(null != mp && !(mp.get("kindImage") == null)) {
				//将信息存储到OSS
				String bucketName = "download-buyhoo";
				String p = "images/farmKind/";
				farmKind.setKindImage(OSSUtil.savePic(bucketName, mp.get("kindImage"), p));
		}
		if(farmKind.getId() == null || farmKind.getId() == -1) {
			count = farmProductDao.addNewFarmKind(farmKind);
		}else {
			count = farmProductDao.modifyFarmMsg(farmKind);
		}
		
		if(count == 0) {
			result.setStatus(0);
			result.setMsg("操作失败！");
		}
		return result;
	}
	
	public PurResult queryFarmKind(Map<String,Object> map) {
		PurResult result = new PurResult(1, "查询成功！");
		try {
			List<FarmKind> list = farmProductDao.queryFarmKind(map);
			result.setCount(farmProductDao.queryFarmKindCount(map));
			result.setData(list);
		}catch (Exception e) {
			e.printStackTrace();
			result.setStatus(0);
			result.setMsg("查询成功！");
		}
		return result;
	}

	public PurResult queryFarmProductAuditList(Map<String,Object> map){
		PurResult result = new PurResult();
		try {
			List<Map<String,Object>> list = farmProductDao.queryFarmProductAuditList(map);
	    	Integer count = farmProductDao.queryFarmProductAuditCount(map);
	    	result.setStatus(1);
			result.setMsg("成功");
			result.setCount(count);
			result.setData(list);
		} catch (Exception e) {
			e.printStackTrace();
			result.setStatus(0);
			result.setMsg("异常");
		}
		return result;
	}
	
	public PurResult queryFarmOrderList(Map<String,Object> map){
		PurResult result = new PurResult();
		List<Map<String,Object>> list = farmProductDao.queryFarmOrderList(map);
		BigDecimal sum_total=new BigDecimal("0");
		for(Map<String,Object> m:list) {
			sum_total=sum_total.add(new BigDecimal(m.get("sub_list_total").toString()));
		}
    	Integer count = farmProductDao.queryFarmOrderListCount(map);
    	result.setStatus(1);
		result.setMsg("成功");
		result.setCount(count);
		result.setData(list);
		result.setCord(sum_total);
		return result;
	}
	
	public List<Map<String ,Object>> queryFarmOrderExcel(Map<String,Object> params){
		return farmProductDao.queryFarmOrderExcel(params);
	}
	
	public PurResult queryFarmProductShelfList(Map<String,Object> map){
		PurResult result = new PurResult();
		try {
			List<Map<String,Object>> list = farmProductDao.queryFarmProductShelfList(map);
	    	Integer count = farmProductDao.queryFarmProductShelfCount(map);
	    	result.setStatus(1);
			result.setMsg("成功");
			result.setCount(count);
			result.setData(list);
		} catch (Exception e) {
			e.printStackTrace();
			result.setStatus(0);
			result.setMsg("异常");
		}
		return result;
	}
	
	public List<Map<String ,Object>> getFarmProductSpecList(Map<String,Object> map)
	{
		return farmProductDao.getFarmProductSpecList(map);
	}
	
	public List<Map<String ,Object>> getFarmProductShelfDetail(Map<String,Object> map)
	{
		return farmProductDao.getFarmProductShelfDetail(map);
	}
	
	//获取有商家的省份列表
	public List<Map<String ,Object>> getProvinceList(List<Map<String ,Object>> list)
	{
		return farmProductDao.getProvinceList(list);
	}
	//获取有商家的城市列表
	public List<Map<String ,Object>> getCityList(List<Map<String ,Object>> list){
		return farmProductDao.getCityList(list);
	}
	//获取有商家的区域列表
	public List<Map<String ,Object>> getAreaList(Map<String,Object> map){
		return farmProductDao.getAreaList(map);
	}
	public PurResult deleteFarmProductShelf(Map<String,Object> params){
		PurResult result = new PurResult();
		FarmProductShelf shelf=new FarmProductShelf();
		shelf.setCreate_time(new Date());
		shelf.setFlag(0);
		shelf.setGood_id(params.get("good_id").toString());
		shelf.setShelf_status(2);
		shelf.setShop_unique(params.get("shop_unique").toString());
		farmProductDao.insertFarmProductShelf(shelf);
    	result.setStatus(1);
		result.setMsg("成功");
		return result;
	}
	
	@Transactional
	public PurResult auditFarmProduct(Map<String,Object> map){
		PurResult result = new PurResult();
		map.put("create_time", new Date());
		map.put("id", map.get("id"));
		farmProductDao.updateFarmProductStatus(map);
		JSONArray goodJson=JSONArray.fromObject(map.get("detailJson"));
		JSONArray goodJson_sup=JSONArray.fromObject(map.get("detailJson_sup"));
		List<PageData> goodsList = JSONArray.toList(goodJson, new PageData(), new JsonConfig());//规格
		List<PageData> goodsList_sup = JSONArray.toList(goodJson_sup, new PageData(), new JsonConfig());//规格
		for(PageData t:goodsList) {
			Map<String,Object> params=new HashMap<>();
			params.put("id", t.get("id"));
			params.put("sale_price", t.get("sale_price"));
			farmProductDao.updateFarmSalePrice(params);

		}
		for(PageData t:goodsList_sup) {
			Map<String,Object> params=new HashMap<>();
			params.put("id", t.get("id"));
			params.put("sale_price", t.get("sale_price"));
			params.put("gold_deduct", t.get("gold_deduct"));
			farmProductDao.updateFarmSalePriceSup(params);
		}
		//上架店铺全球精选
		//按区域查询所有店铺
		farmProductDao.delete_farm_product_shelf(map);
		farmProductDao.delete_shopping_cart(map);
		for(PageData t : goodsList){
			 //将订单信息存储到店铺订单列表中，用于演示
	        Object yanshiStatus = redis.getObject("yanshiStatus");
	        List<Map<String,Object>> shops = null;
	        if(null != yanshiStatus && yanshiStatus.toString().equals("1")) {
	        	shops = new ArrayList<Map<String,Object>>();
	        	//获取演示的店铺列表
	        	Object yanshiShops = redis.getObject("yanshiShops");
	        	if(null != yanshiShops) {
	        		String[] shopsList = yanshiShops.toString().split(",");
	        		for(Integer i = 0; i < shopsList.length; i++) {
	        			Map<String,Object> tmp = new HashMap<String,Object>();
	        			tmp.put("shop_unique", shopsList[i]);
	        			shops.add(tmp);
	        		}
	        	}else {
	        		Map<String,Object> tmp = new HashMap<String,Object>();
        			tmp.put("shop_unique", "1536215939565");
        			shops.add(tmp);
	        	}
	        }else {
	        	shops = farmProductDao.queryAreaShop(map);
	        }
	        
			for (Map<String, Object> shop : shops) {
				FarmProductShelf shelf=new FarmProductShelf();
				shelf.setCreate_time(new Date());
				shelf.setFlag(1);
				shelf.setGood_id(map.get("id").toString());
				shelf.setShelf_status(1);
				shelf.setShop_unique(shop.get("shop_unique").toString());
				farmProductDao.insertFarmProductShelf(shelf);
				//上架  添加售价
				map.put("shelf_id", shelf.getId());
				Map<String, Object> params =new HashMap<String, Object>();
				params.put("shelf_id", shelf.getId());
				params.put("good_id", map.get("id").toString());
				params.put("spec_name",t.get("spec_name") );
				params.put("price",t.get("price"));
				params.put("sale_price", t.get("sale_price"));
				params.put("convert_unit", t.get("convert_unit"));
				farmProductDao.insertShelfPrice(params);
			}
		}
    	result.setStatus(1);
		result.setMsg("成功");
		return result;
	}
	
	@Transactional
	public PurResult auditFarmProductNo(Map<String,Object> map){
		PurResult result = new PurResult();
		map.put("create_time", new Date());
		map.put("id", map.get("id"));
		farmProductDao.updateFarmProductStatus(map);
		farmProductDao.insertFarmProductAudit(map);
    	result.setStatus(1);
		result.setMsg("成功");
		return result;
	}
	
	@Transactional
	public PurResult upperShelfProduct(Map<String,Object> map){
		PurResult result = new PurResult();
		FarmProductShelf shelf=new FarmProductShelf();
		shelf.setCreate_time(new Date());
		shelf.setFlag(1);
		shelf.setGood_id(map.get("good_id").toString());
		shelf.setShelf_status(1);
		shelf.setShop_unique(map.get("shop_unique").toString());
		farmProductDao.insertFarmProductShelf(shelf);
//		//上架  添加售价
//		map.put("shelf_id", shelf.getId());
//		farmProductDao.delete_shopping_cart(map);
//		for(int i=0;i<good_ids.length;i++) {
//			Map<String, Object> params =new HashMap<String, Object>();
//			params.put("shelf_id", shelf.getId());
//			params.put("good_id", good_ids[i]);
//			params.put("spec_name", spec_names[i]);
//			params.put("price", prices[i]);
//			params.put("sale_price", sale_prices[i]);
//			params.put("convert_unit", convert_units[i]);
//			farmProductDao.insertShelfPrice(params);
//		}
    	result.setStatus(1);
		result.setMsg("成功");
		return result;
	}
	@Transactional
	public PurResult upperShelfGlobal2(Map<String,Object> map){
		PurResult result = new PurResult();
		try {
			map.put("create_time", new Date());
			
			if(map.containsKey("id"))
			{
				farmProductDao.updateGoodShelfStatus(map);
				
			}
			
	    	result.setStatus(1);
			result.setMsg("成功");
		}catch (Exception e) {
			e.printStackTrace();
			result.setStatus(0);
			result.setMsg("异常");
		}
		return result;
	}
	public PurResult updateGoodShelfStatus(Map<String,Object> map){
		PurResult result = new PurResult();
		try {
			farmProductDao.updateGoodShelfStatus(map);
	    	result.setStatus(1);
			result.setMsg("成功");
		}catch (Exception e) {
			e.printStackTrace();
			result.setStatus(0);
			result.setMsg("异常");
		}
		return result;
	}
	
	

	
	public GlobalDetailVO queryGlobalThemeDetail(Map<String,Object> map){

		return farmProductDao.queryGlobalThemeDetail(map);
	}
	
	@Transactional
	public PurResult editExpressInfo(Map<String,Object> map){
		PurResult result = new PurResult();
		try {
			farmProductDao.updateFarmExpress(map);
			map.put("handle_status", "2");
			map.put("send_datetime", new Date());
			farmProductDao.updateFarmSubOrder(map);
	    	result.setStatus(1);
			result.setMsg("成功");
		}catch (Exception e) {
			e.printStackTrace();
			result.setStatus(0);
			result.setMsg("异常");
		}
		return result;
	}
	
	public PurResult getOrderDetail(Map<String,Object> map){
		PurResult result = new PurResult();
		try {
			List<Map<String,Object>> list = farmProductDao.getOrderDetail(map);
	    	Integer count = list.size();
	    	result.setStatus(1);
			result.setMsg("成功");
			result.setCount(count);
			result.setData(list);
		} catch (Exception e) {
			e.printStackTrace();
			result.setStatus(0);
			result.setMsg("异常");
		}
		return result;
	}
	public PurResult queryPTGGShelfList(Map<String,Object> map){
		PurResult result = new PurResult();
			List<Map<String,Object>> list = farmProductDao.queryPTGGShelfList(map);
			Integer count = farmProductDao.queryPTGGShelfCount(map);
	    	result.setStatus(1);
			result.setMsg("成功");
			result.setCount(count);
			result.setData(list);
			return result;
	}
	@Transactional
	public PurResult updateGoodShelf(Map<String,Object> map){
		PurResult result = new PurResult();
		
		String type=(String) map.get("type");
		
		if(type.equals("A"))//全部上架
		{
			map.put("shelf_status", 1);
			farmProductDao.updateGoodShelf(map);
			//从未上架的店铺
			List<Map<String,Object>> list2 = farmProductDao.queryPTGGShelf2List(map);
			farmProductDao.addGoodShelfList(list2);
			
			
		}else if(type.equals("B"))//全部下架
		{
			map.put("shelf_status", 2);
			farmProductDao.updateGoodShelf(map);
		}else if(type.equals("C"))//强制上架
		{
			map.put("shelf_status", 1);
			farmProductDao.updateGoodShelf(map);
			//从未上架的店铺
			List<Map<String,Object>> list2 = farmProductDao.queryPTGGShelf2List(map);
			if(list2!=null&&list2.size()>0)
			{
				farmProductDao.addGoodShelfList(list2);
			}
			
			//强制上架
			map.put("compulsory", 1);
			farmProductDao.updateGoodShelfCompulsory(map);
		}else if(type.equals("D"))//全部下架
		{
			map.put("compulsory", 0);
			farmProductDao.updateGoodShelfCompulsory(map);
		}
		
	    result.setStatus(1);
		result.setMsg("成功");
		return result;
	}
	
	public PurResult queryGlobalSecretaryList(Map<String,Object> map){
		PurResult result = new PurResult();
			List<Map<String,Object>> list = farmProductDao.queryGlobalSecretaryList(map);
			Integer count = farmProductDao.queryGlobalSecretaryCount(map);
	    	result.setStatus(1);
			result.setMsg("成功");
			result.setCount(count);
			result.setData(list);
			return result;
	}
	public PurResult addSecretary(Map<String,Object> map){
		PurResult result = new PurResult();
		farmProductDao.addSecretary(map);
	    result.setStatus(1);
		result.setMsg("成功");
		return result;
	}
	public PurResult deleteGlobalSecretary(Map<String,Object> map){
		PurResult result = new PurResult();
		farmProductDao.deleteGlobalSecretary(map);
	    result.setStatus(1);
		result.setMsg("成功");
		return result;
	}
	public Map<String,Object> querySecretary(String map){

		return farmProductDao.querySecretary(map);
	}
	public PurResult updateSecretary(Map<String,Object> map){

		PurResult result = new PurResult();
		farmProductDao.updateSecretary(map);
	    result.setStatus(1);
		result.setMsg("成功");
		return result;
	}
	
	public ShopsResult queryGoodsKinds(String shop_unique, String area_dict_parent_num) {
		ShopsResult shop=new ShopsResult();
		Map<String,Object> map=new HashMap<String,Object>();
		if(null==area_dict_parent_num){
			area_dict_parent_num="0";
		}
		map.put("area_dict_parent_num", area_dict_parent_num);
		//查询当前店铺使用的分类类型，1：默认分类；2：自定义分类
		List<Map<String,Object>> kindList= farmProductDao.queryArea(map);
		
		shop.setStatus(0);
		shop.setMsg("商品分类查询成功！");
		shop.setData(kindList);
		return shop;
	}
	
	public int batchUpdateFarmLogistics(List<String[]> list) {
		List<Map<String, Object>> orderList=new ArrayList<Map<String, Object>>();	
		String []ids=new String[list.size()-1];
		for(int i=1;i<list.size();i++){
			String []tem=list.get(i);
			Map<String,Object> map=new HashMap<String,Object>();
			map.put("global_sub_id", tem[0]);
			map.put("compay_name", tem[11]);
			map.put("logistics_unique", tem[10]);
			ids[i-1]=tem[0];
			orderList.add(map);					
		}
		Map<String,Object> params=new HashMap<String,Object>();
		params.put("handle_status", "2");
		params.put("send_datetime", new Date());
		params.put("ids", ids);
		farmProductDao.updateFarmSubOrderBattch(params);
		
		return farmProductDao.batchUpdateFarmLogistics(orderList);		
	}
	
	public PurResult queryFarmInformation(HttpServletRequest request){
		PurResult result = new PurResult();
		List<Map<String,Object>> list = farmProductDao.queryFarmInformation();
		result.setData(list);
	    result.setStatus(1);
		result.setMsg("成功");
		return result;
	}
	public PurResult addFarmInformation(Map<String, Object> params){
		PurResult result = new PurResult();
		farmProductDao.addFarmInformation(params);
	    result.setStatus(1);
		result.setMsg("成功");
		return result;
	}
	
}
