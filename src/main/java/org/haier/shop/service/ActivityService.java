package org.haier.shop.service;

import java.util.List;
import java.util.Map;

import javax.servlet.http.HttpServletRequest;

import org.haier.shop.entity.beans.PageData;
import org.haier.shop.util.PurResult;
import org.haier.shop.util.ShopsResult;

public interface ActivityService {
	/**
	 * 查询与店铺相同管理员的店铺
	 * @param shopUnique
	 * @return
	 */
	public List<Map<String,Object>> selectShopsList(String shopUnique);
	/**
	 * s删除充值配置信息
	 * @param deleteRechargeOffline
	 * @return
	 */
	public PurResult deleteRechargeOffline(Long recharge_config_id);
	/**
	 * 根据ID查询充值详情
	 * @param recharge_config_id
	 * @return
	 */
	public PurResult queryOilRechargeConfigDetail(Long recharge_config_id);
	/**
	 * 查询店铺的充值配置列表
	 * @param map
	 * @return
	 */
	public PurResult queryOilRechargeConfigList(Map<String,Object> map);
	
	public PurResult queryPromotionList(Map<String, Object> map);

	public PurResult queryGoodsByPage(String shop_unique, String goodsMessage, String goods_kind_parunique,
			String goods_kind_unique, Integer pageNum, Integer pageSize);
	
	public PurResult queryGoodsByPage1(String shop_unique, String goodsMessage, String goods_kind_parunique,
			String goods_kind_unique, Integer pageNum, Integer pageSize,Integer is_online,String supplierUnique);

	public PurResult submitSupplierStorageOrder(String shop_unique, String promotion_activity_name, String startDate,
			String endDate, String order_activity, String detailJson);

	public PurResult submitGoodsGift(String shop_unique, String promotion_activity_name, String startDate,
			String endDate, String order_activity, String detailJson);

	public PurResult addOrderMarkdown(String shop_unique, String promotion_activity_name, String startDate,
			String endDate, String meet_price1, String discount_price1, String meet_price2, String discount_price2,
			String meet_price3, String discount_price3, String goods_id1, String goods_id2, String goods_id3, String gift_count1, String gift_count2, String gift_count3);

	public PurResult deleteActivity(Map<String, Object> map);
	
	public PurResult updateActivityStatus(Map<String, Object> map);

	public PurResult queryGoodsMarkdownDetail(Map<String, Object> map);

	public PurResult queryGoodsGiftDetail(Map<String, Object> map);

	public PurResult queryOrderMarkdownDetail(Map<String, Object> map);

	public PurResult submitGoodsKindPoint(String shop_unique, String goods_kind_parunique, String goods_kind_child,
			String goods_kind_points_type, String goods_kind_points_unit, String goods_kind_points_unit_val);

	public PurResult submitGoodsPoint(String shop_unique, String detailJson);

	public PurResult submitGoodsKindCommission(String shop_unique, String goods_kind_parunique, String goods_kind_child,
			String goods_kind_commission_type, String goods_kind_commission_unit, String goods_kind_commission_unit_val);

	public PurResult submitGoodsCommission(String shop_unique, String detailJson);

	public PurResult queryGoodsKindPointsAndCommissionByPage(String shop_unique, String goodsMessage,
			String goods_kind_parunique, String goods_kind_unique, Integer pageNum, Integer pageSize);

	public PurResult submitSingleGoodsPromotion(String shop_unique, String promotion_activity_name, String startDate,
			String endDate, String detailJson,String activity_range);
	
	public PurResult submitFlashSale(String shop_unique, String promotion_activity_name, String startDate,
			String endDate, String detailJson,String activity_range);
	
	public PurResult updateFlashSale(String promotion_activity_id,String shop_unique, String promotion_activity_name, String startDate,
			String endDate, String detailJson,String activity_range);

	public PurResult querySingleGoodsPromotionDetail(Map<String, Object> map);
	
	public PurResult queryFlashSaleDetail(Map<String, Object> map);

	public PurResult queryShopCouponList(String shop_unique, String user_status, String give_status ,Integer pageNum, Integer pageSize);

	public PurResult addShopCoupon(String shop_unique, String start_time, String end_time, String meet_amount, String coupon_amount, String type,
								   String is_time, String is_daily, String daily_num, String times, String is_auto_grant, String is_grant_num, String grant_num,
								   String exclusive_type, String days, Integer is_online, String coupon_name, String designated_shop_unique, Integer coupon_type, String rule_description);

	public PurResult updateShopCoupon(String shop_coupon_id, String start_time, String end_time, String meet_amount, String coupon_amount, String type,
                                      String is_time, String is_daily, String daily_num, String times, String is_auto_grant, String old_is_auto_grant, String is_grant_num, String grant_num, String exclusive_type, String coupon_name, String days, String rule_description);
	
	public PurResult deleteShopCoupon(String shop_coupon_id);
	
	public Map<String ,Object> getShopCoupon(String shop_coupon_id);

	public PurResult queryRechargeConfigList(Integer pageNum, Integer pageSize);

	public PurResult addRechargeConfig(Map<String ,Object> params);
	
	public Map<String ,Object> getRechargeConfig(String platform_recharge_config_id);
	
	public PurResult updateRechargeConfig(Map<String ,Object> params);

	public PurResult deleteRechargeConfig(String platform_recharge_config_id,String shop_coupon_id);

	public PurResult setPointUse(String shop_unique, String points_val, String money, String use_top,Double use_money);

	public PurResult querySetPointUse(String shop_unique);

	public PurResult queryClassThemeList(Integer pageNum, Integer pageSize);
	
	public PurResult queryBootImg();

	public Map<String, Object> queryClassThemeById(String class_theme_id);

	public PurResult editClassTheme(String class_theme_id, String class_theme_name, HttpServletRequest request);
	
	public PurResult updateBootImg(String id, String time,String url, HttpServletRequest request);
	
	public PurResult uploadImg(HttpServletRequest request);
	
	public PurResult uploadGoodsDictImg(HttpServletRequest request);

	public PurResult queryClassThemeGoodsList(Map<String, Object> map);

	public PurResult deleteClassThemeGoodsByGoodsBarcode(Map<String, Object> map);

	public PurResult addClassThemeGoodsList( String class_theme_id, String goods_id, String shop_unique);

	public PurResult queryPlatformCusList(String cusMessage, Integer pageNum, Integer pageSize);
	
	public PurResult queryRewardList(Map<String ,Object> params);
	
	public PurResult queryEshowTVList(Map<String ,Object> params);
	
	public PurResult addReward(List<PageData> data2);
	
	public PurResult deleteReward(String id);
	
	public PurResult updateReward(String id,int valid);
	
	public PurResult edtitReward(Map<String ,Object> params);
	
	public PurResult queryCouponRecord(Map<String ,Object> params);
	
	/**
	 * 查询商品信息
	 * @param map
	 * @return
	 */
	public ShopsResult queryGoodsMsgList(Map<String,Object> map) ;

	public PurResult queryGoodsKind(String shop_unique, String goodsMessage, int page, int pageSize);
	
	public PurResult queryCoudflashGoods(Map<String,Object> map) ;

	public PurResult submitCoudflashGoods(String startDate,String endDate, String status, String detailJson);
	
	public PurResult updateYSFStatus(String id, String status);

	public PurResult queryFoodManagerList(Map<String, Object> map);

	public ShopsResult addFoodManager(Map<String, Object> map, HttpServletRequest request);

	public Map<String, Object> queryFoodManager(Map<String, Object> map);

	public ShopsResult deleteFoodManager(Map<String, Object> map);

	public PurResult queryNewsManagerList(Map<String, Object> map);

	public ShopsResult addNewsManager(Map<String, Object> map, HttpServletRequest request) throws Exception;

	public Map<String, Object> queryNewsManager(Map<String, Object> map);

	public ShopsResult deleteNewsManager(Map<String, Object> map);

	public PurResult queryReortConfigList(Map<String, Object> map);

	public ShopsResult addReportConfig(Map<String, Object> map, HttpServletRequest request);

	public Map<String, Object> queryReportConfig(Map<String, Object> map);

	public ShopsResult deleteReportConfig(Map<String, Object> map);

	public void serverEndReport();

	public PurResult addSingleShopCoupon(String shop_unique, String start_time, String end_time, String coupon_name,
			String goods_barcode, String goods_name, String goods_in_price, String goods_price, String count,
			String exclusive_type);

	public PurResult updateSingleShopCoupon(String shop_unique, String start_time, String end_time, String coupon_name,
			String goods_barcode, String goods_name, String goods_in_price, String goods_price, String count,
			String exclusive_type, String shop_coupon_id, String id);
	
}
