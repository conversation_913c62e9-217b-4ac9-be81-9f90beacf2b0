package org.haier.shop.service;

import java.util.List;
import java.util.Map;

import org.haier.shop.entity.MianMiMain;
import org.haier.shop.util.PurResult;
import org.haier.shop.util.ShopsResult;

public interface ShopStatisticsService {
	/**
	 * 根据日期查询各种支付方式的支付金额
	 * @param map
	 * @return
	 */
	public PurResult queryPayMethodByDay(Map<String,Object> map);
	

	/**
	 * 查询免密支付分页数量及总订单数量
	 * @param map
	 * @return
	 */
	public ShopsResult payMethodDetailPages(Map<String,Object> map);
	/**
	 * 查询免密支付分页数量及总订单数量
	 * @param map
	 * @return
	 */
	public ShopsResult payMethodDetail(Map<String,Object> map);
	
	/**
	 * 查询已开通免密支付的店铺数量
	 * @param map
	 * @return
	 */
	public ShopsResult mianmiDetailShopsListPages(Map<String,Object> map);
	
	/**
	 * 查询符合条件的店铺信息
	 * @param map
	 * @return
	 */
	public ShopsResult mianmiDetailShopsList(Map<String,Object> map);
	/**
	 * 店铺销售订单列表页面
	 * 周期内查询店铺订单信息列表统计
	 * @param map
	 * @return
	 */
	public ShopsResult getShopListPageMsg(Map<String,Object> map);
	/**
	 * 店铺销售列表查询
	 */
	public ShopsResult getShopListByPage(Map<String,Object> map);
	
	/**
	 * 获取订单详情
	 * @param map
	 * @return
	 */
	public ShopsResult showListDetail(Map<String,Object> map);
	
	/**
	 * 更新店铺免密支付状态
	 * @param map
	 * @return
	 */
	public ShopsResult updateShopMianmiStatus(Map<String,Object> map);
	

	/**
	 * 免密支付EXCEL下载
	 * @param map
	 * @return
	 */
	public List<MianMiMain> downExcel(Map<String,Object> map);
}
