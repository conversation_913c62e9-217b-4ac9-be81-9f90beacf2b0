package org.haier.shop.service;

import java.util.Map;

import javax.annotation.Resource;

import org.haier.shop.dao.ShopFunctionDao;
import org.haier.shop.util.ShopsResult;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Service
public class ShopFunctionServiceImpl implements ShopFunctionService{
	@Resource
	private ShopFunctionDao funDao;
	
	public ShopsResult queryShopFunction(Map<String,Object> map){
		ShopsResult sr=new ShopsResult();
		Map<String,Object> data = funDao.queryShopFunction(map);
		if(null==data||data.isEmpty()){
			sr.setStatus(2);
			sr.setMsg("没有满足条件的信息！");
			return sr;
		}
		sr.setStatus(1);
		sr.setMsg("查询成功！");
		sr.setData(data);
		return sr;
	}
}
