package org.haier.shop.service;

import java.util.List;
import java.util.Map;

import javax.annotation.Resource;

import org.haier.shop.dao.BranchStoreDao;
import org.haier.shop.util.PurResult;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Service
public class BranchStoreServiceImpl implements BranchStoreService{
	
	@Resource
	private BranchStoreDao branchStoreDao;

	@Override
	public PurResult getBranchStoreList(Map<String, Object> params) {
		PurResult result = new PurResult();
		try {
			List<Map<String ,Object>> list = branchStoreDao.getBranchStoreList(params);
			Integer total = branchStoreDao.getBranchStoreListCount(params);
			result.setTotal(total);
			result.setRows(list);
			result.setStatus(1);
			result.setMsg("成功");
		} catch (Exception e) {
			result.setStatus(0);
			result.setMsg("异常");
		}
		return result;
	}

	@Override
	public PurResult getBranchStore(String shop_unique) {
		PurResult result = new PurResult();
		try {
			Map<String ,Object> data = branchStoreDao.getBranchStore(shop_unique);
			result.setData(data);
			result.setStatus(1);
			result.setMsg("成功");
		} catch (Exception e) {
			result.setStatus(0);
			result.setMsg("异常");
		}
		return result;
	}

	@Override
	@Transactional
	public PurResult deleteBranchStore(String shop_unique) {
		PurResult result = new PurResult();
		try {
			branchStoreDao.deleteBranchStore(shop_unique);
			result.setStatus(1);
			result.setMsg("成功");
		} catch (Exception e) {
			e.printStackTrace();
			result.setStatus(0);
			result.setMsg("异常");
		}
		return result;
	}

}
