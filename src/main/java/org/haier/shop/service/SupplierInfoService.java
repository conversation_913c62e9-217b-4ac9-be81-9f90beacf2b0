package org.haier.shop.service;

import java.util.Map;

import org.haier.shop.util.PurResult;

public interface SupplierInfoService {
	
	//获取商品所有供货商列表
	public PurResult getSupplierList(Map<String, Object> params);
	
	//获取当前供货商的当前商品信息
	public PurResult getSupplierGoodsList(Map<String, Object> params);
	
	//保存订单信息
	public PurResult saveOrder(String shop_unique,String order_remarks ,String goods);

	public PurResult querySchoolContentList();

	public Map<String, Object> querySchoolContent(String id);
}
