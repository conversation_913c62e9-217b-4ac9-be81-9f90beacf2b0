package org.haier.shop.service;

import java.util.Map;

import javax.servlet.http.HttpServletRequest;

import org.haier.shop.util.PurResult;
import org.haier.shop.util.ShopsResult;

public interface LoanMoneyService {

	public Map<String, Object> queryIsOpenLoan(String shop_unique);

	public ShopsResult addOpenLoan(Map<String, Object> map, HttpServletRequest request);

	public void saveReturnMoney(Map<String, Object> params);

	public void updateReturnMoney(String out_trade_no);

	public ShopsResult queryOrderStatus(Map<String, Object> map);

	public PurResult queryLoanReturnList(Map<String, Object> map);

	public PurResult queryLoanList(Map<String, Object> map);

	public Map<String, Object> queryAdvanceMoney(String shop_unique);
	
}
