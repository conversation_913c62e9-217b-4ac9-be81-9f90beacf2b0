package org.haier.shop.service;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import cn.hutool.poi.excel.ExcelUtil;
import org.haier.shop.params.shop.ShopBatchRegisterParams;
import org.haier.shop.util.ShopsResult;
import org.haier.shop.util.excel.inpl.ExcelReader;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.io.IOException;
import java.util.Iterator;
import java.util.List;
import java.util.Map;

/**
 * @Description
 * @ClassName ShopBatchUtilServiceImpl
 * <AUTHOR>
 * @Date 2024/6/12 16:27
 **/
@Service
public class ShopBatchUtilServiceImpl implements ShopBatchUtilService{

    private static final Logger logger = LoggerFactory.getLogger(ShopBatchUtilServiceImpl.class);

    @Resource
    private ShopService shopService;
    @Override
    public ShopsResult shopBatchRegister(HttpServletRequest request, ShopBatchRegisterParams params) {

        if (!(request instanceof MultipartHttpServletRequest)) {
            return null;
        }
        try {
            MultipartHttpServletRequest multipartHttpServletRequest = (MultipartHttpServletRequest) request;
            Map<String, MultipartFile> allFiles = multipartHttpServletRequest.getFileMap();
            Iterator<Map.Entry<String, MultipartFile>> iter = allFiles.entrySet().iterator();
            while (iter.hasNext()) {
                Map.Entry<String, MultipartFile> entry = iter.next();
                MultipartFile mfile = entry.getValue();
                org.haier.shop.util.excel.inpl.ExcelReader reader = new ExcelReader(mfile.getInputStream());
                List<String[]> read=reader.getAllData(0);
                for (String[] objects : read) {
                    String phone = objects[0];
                    if (StrUtil.isNotBlank(phone)) {
                        phone = StrUtil.replace(phone, " ", "");
                        String shopName = objects[1];
                        String userName = objects[2];
                        shopName = StrUtil.replace(shopName, " ", "");
                        String pwd = StrUtil.subWithLength(phone, 5, 6);
                        ShopsResult sr = shopService.register(shopName, phone, pwd, params.getShopAddressDetail(), phone, 2, 0, null, null,
                                params.getAreaDictNum(), params.getShopLatitude(), params.getShopLongitude(), params.getProvince(), params.getCity(), params.getDistrict(), 1, userName, null, null, request, params.getTownCode(), params.getInvitationCode());
                        if (ObjectUtil.equals(Integer.valueOf(1), sr.getStatus())) {
                            logger.info("--店铺名称：{}-----手机号：{}------注册结果:{}--------------", shopName, phone, JSONUtil.toJsonStr(sr));
                        } else {
                            logger.error("--店铺名称：{}-----手机号：{}------注册结果:{}--------------", shopName, phone, JSONUtil.toJsonStr(sr));
                        }
                    }
                }
            }
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
        return ShopsResult.ok();
    }
}
