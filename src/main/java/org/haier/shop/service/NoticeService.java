package org.haier.shop.service;

import java.util.Map;

import javax.servlet.http.HttpServletRequest;

import org.haier.shop.util.PurResult;

public interface NoticeService {
	
	/**
	 * 查询列表
	 * @return
	 */
	public PurResult queryNoticeList(Map<String,Object> params);
	
	/**
	 * 添加
	 * @return
	 */
	public PurResult addNotice(HttpServletRequest request);
	
	/**
	 * 获取详情
	 * @return
	 */
	public Map<String ,Object> queryNoticeDetail(String notice_id);
	
	/**
	 * 修改
	 * @return
	 */
	public PurResult updateNotice(HttpServletRequest request);
	
	/**
	 * 删除
	 * @return
	 */
	public PurResult deleteNotice(String notice_id);
	
	/**
	 * 发布
	 * @return
	 */
	public PurResult releaseNotice(String notice_id,String notice_type);
	
	/**
	 * 撤回
	 * @return
	 */
	public PurResult withdrawNotice(String notice_id);
}
