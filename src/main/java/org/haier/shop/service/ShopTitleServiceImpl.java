package org.haier.shop.service;

import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import org.haier.shop.dao.ShopTitleDao;
import org.haier.shop.entity.shopTitle.ShopTitle;
import org.haier.shop.util.ShopsResult;
import org.haier.shop.util.ShopsUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import java.io.File;
import java.io.FileInputStream;
import java.util.*;

@Service
@Transactional
public class ShopTitleServiceImpl implements ShopTitleService {

    @Autowired
    private ShopTitleDao shopTitleDao;

    private static List<Map<String, Object>> getList(List<Map<String, Object>> shopList, List<ShopTitle> shopTitleList) {
        List<Map<String, Object>> list = new ArrayList<>();
        for (Map<String, Object> shopMap :
                shopList) {
            Map<String, Object> map = new HashMap<>();
            map.put("shopUnique", shopMap.get("shopUnique"));
            map.put("shopName", shopMap.get("shopName"));
            for (int i = 0; shopTitleList != null && i < shopTitleList.size(); i++) {
                ShopTitle shopTitle = shopTitleList.get(i);
                if (String.valueOf(shopMap.get("shopUnique")).equals(String.valueOf(shopTitle.getShopUnique()))) {
                    map.put("checked", shopTitle.getValidType() == 1);
                    map.put("id", shopTitle.getId());
                    break;
                }
            }
            map.putIfAbsent("checked", false);
            list.add(map);
        }
        return list;
    }

    /**
     * 查询店铺的标题信息
     *
     * @return
     */
    public ShopsResult queryShopTitleList(Integer page, Integer limit, Long shopUnique, Integer validType,String titleName) {

        Map<String, Object> shopTitle = new HashMap<String, Object>();
        shopTitle.put("shopUnique", shopUnique);
        shopTitle.put("validType", validType);

        if (null != page && null != limit) {
            shopTitle.put("startNum", (page - 1) * limit);
            shopTitle.put("pageSize", limit);
        }
        if(titleName != null && !titleName.isEmpty()){
            shopTitle.put("titleName","%"+titleName+"%");
        }
        List<ShopTitle> titleList = shopTitleDao.queryShopTitleList(shopTitle);
        Integer titleCount = shopTitleDao.queryShopTitleCount(shopTitle);

        return ShopsResult.success(titleList, titleCount);
    }

    @Override
    public ShopsResult addShopTitle(String titleName, MultipartFile file, Integer modularType, HttpServletRequest request) {
        ShopsResult result = new ShopsResult();
        if (titleName == null || titleName.isEmpty()) {
            result.setMsg("标题名称不能为空");
            result.setStatus(0);
            return result;
        }
        if (file == null || file.isEmpty()) {
            result.setMsg("标题头像不能为空");
            result.setStatus(0);
            return result;
        }
        if (modularType == null) {
            result.setMsg("标题分类不能为空");
            result.setStatus(0);
            return result;
        }
        String titleImgPath = uploadImg(file, request);

        Map<String, Object> map = new HashMap<>();
        map.put("shopUnique", 0);
        Integer maxId = shopTitleDao.queryMaxId(map);
        ShopTitle shopTitle0 = new ShopTitle();
        shopTitle0.setId(maxId + 1L);
        shopTitle0.setShopUnique(0L);
        shopTitle0.setTitleName(titleName);
        shopTitle0.setModularNum(maxId + 1);
        shopTitle0.setModularType(modularType);
        shopTitle0.setTitleSort(maxId + 1);
        shopTitle0.setValidType(1);
        shopTitle0.setTitleImg(titleImgPath);
        List<Map<String, Object>> shopList = shopTitleDao.queryShopList(new HashMap<>());


        //由于店铺数量太多，此处需要分批处理,每批次不超过1000
        Integer batchSize = 1000;
        Integer batchCount = (shopList.size() + batchSize) / batchSize;
        for (Integer i = 0; i < batchCount; i++) {
            List<ShopTitle> shopTitleList = new ArrayList<>();
            for (Integer j = i * batchSize; j < (i + 1) * batchSize && j < shopList.size(); j++) {
                Map<String, Object> shopMap = shopList.get(j);
                ShopTitle shopTitle = new ShopTitle();
                shopTitle.setShopUnique(Long.valueOf(String.valueOf(shopMap.get("shopUnique"))));
                shopTitle.setTitleName(titleName);
                shopTitle.setModularNum(maxId + 1);
                shopTitle.setTitleSort(maxId + 1);
                shopTitle.setModularType(modularType);
                shopTitle.setTitleImg(titleImgPath);
                shopTitle.setValidType(2);
                shopTitleList.add(shopTitle);
            }
            shopTitleDao.batchAddShopTitle(shopTitleList);
        }

        shopTitleDao.addShopTitle(shopTitle0);
        result.setStatus(1);
        result.setMsg("添加成功");
        return result;
    }

    @Override
    public ShopsResult modifyShopTitle(Long id, String titleName, MultipartFile file, Integer modularType, HttpServletRequest request) {
        ShopsResult result = new ShopsResult();
        if (id == null) {
            result.setMsg("ID不能为空");
            result.setStatus(0);
            return result;
        }
        if (titleName == null || titleName.isEmpty()) {
            result.setMsg("标题名称不能为空");
            result.setStatus(0);
            return result;
        }
        if (modularType == null) {
            result.setMsg("标题分类不能为空");
            result.setStatus(0);
            return result;
        }
        String titleImgPath = "";
        if (file != null) {
            titleImgPath = uploadImg(file, request);
        }
        ShopTitle yShopTitle = shopTitleDao.queryShopTitleInfoById(id);
        ShopTitle shopTitle0 = new ShopTitle();
        shopTitle0.setTitleName(titleName);
        shopTitle0.setModularNum(yShopTitle.getModularNum());
        shopTitle0.setModularType(modularType);
        if (!Objects.equals(titleImgPath, "")) {
            shopTitle0.setTitleImg(titleImgPath);
        }
        shopTitleDao.modifyShopTitle(shopTitle0);
        result.setStatus(1);
        result.setMsg("修改成功");
        return result;
    }

    @Override
    public ShopsResult queryShopTitleInfo(Long id, String shopMessage, Integer pageIndex, Integer pageSize) {
        Map<String, Object> queryShopTitleInfoMap = new HashMap<>();
        queryShopTitleInfoMap.put("id", id);
        if (pageIndex != null && pageSize != null) {
            queryShopTitleInfoMap.put("pageIndex", (pageIndex - 1) * pageSize);
            queryShopTitleInfoMap.put("pageSize", pageSize);
        }
        if (shopMessage != null && !shopMessage.isEmpty()) {
            queryShopTitleInfoMap.put("shopMessage", "%" + shopMessage + "%");
        }
        List<Map<String, Object>> shopList = shopTitleDao.queryShopList(queryShopTitleInfoMap);
        List<ShopTitle> shopTitleList = shopTitleDao.queryShopTitleInfo(queryShopTitleInfoMap);
        List<Map<String, Object>> list = getList(shopList, shopTitleList);
        Integer titleCount = shopTitleDao.queryShopTitleInfoCount(queryShopTitleInfoMap);
        return ShopsResult.success(list, titleCount);
    }

    @Override
    public ShopsResult authorizeShopTitle(Long id, String shopTitleStr) {
        ShopsResult result = new ShopsResult();
        JSONArray ja = JSONUtil.parseArray(shopTitleStr);
        ShopTitle yShopTitle = shopTitleDao.queryShopTitleInfoById(id);
        List<ShopTitle> shopTitleList = new ArrayList<>();
        List<Map<String, Object>> updateList = new ArrayList<>();
        for (int i = 0; i < ja.size(); i++) {
            JSONObject jo = ja.getJSONObject(i);
            if (jo.get("id") != null && jo.get("id") != "") {
                Map<String, Object> map = new HashMap<>();
                map.put("id", jo.get("id"));
                map.put("shopUnique", jo.get("shopUnique"));
                map.put("validType", "true".equals(String.valueOf(jo.get("checked"))) ? 1 : 2);
                updateList.add(map);
            } else {
                ShopTitle shopTitle = new ShopTitle();
                shopTitle.setShopUnique(Long.valueOf(String.valueOf(jo.get("shopUnique"))));
                shopTitle.setTitleName(yShopTitle.getTitleName());
                shopTitle.setModularNum(yShopTitle.getModularNum());
                shopTitle.setTitleSort(yShopTitle.getTitleSort());
                shopTitle.setModularType(yShopTitle.getModularType());
                shopTitle.setTitleImg(yShopTitle.getTitleImg());
                shopTitle.setValidType("true".equals(String.valueOf(jo.get("checked"))) ? 1 : 2);
                shopTitleList.add(shopTitle);

            }
        }
        if (!shopTitleList.isEmpty()) {
            shopTitleDao.batchAddShopTitle(shopTitleList);
        }
        if (!updateList.isEmpty()) {
            shopTitleDao.modifyShopTitleValidTypeBatch(updateList);
        }
        result.setStatus(1);
        result.setMsg("授权成功");
        return result;
    }

    @Override
    public ShopsResult deleteShopTitle(Long id) {
        ShopsResult result = new ShopsResult();
        ShopTitle shopTitle = shopTitleDao.queryShopTitleInfoById(id);
        shopTitleDao.deleteShopTitle(shopTitle.getModularNum());
        result.setStatus(1);
        result.setMsg("删除成功");
        return result;
    }

    private String uploadImg(MultipartFile file, HttpServletRequest request) {
        String iconPath = "";
        try {
            //获取服务器所在路径
            String absPath = this.getClass().getClassLoader().getResource("../../").getPath();
            String filePathDetail = "image/shopTitle/";
            String filePath = absPath.substring(0, absPath.length() - request.getContextPath().length()) + filePathDetail + "/";

            //将文件存储到本地，
            File floder = new File(filePath);
            if (!floder.exists()) {
                floder.mkdirs();
            }
            String goodsName = file.getOriginalFilename();
            String lastName = goodsName.substring(goodsName.lastIndexOf("."));

            UUID uuid = UUID.randomUUID();
            String newName = goodsName;

            //将文件保存到文件
            ShopsUtil.savePicture(file, filePath, goodsName, "1");
            //如果图片尺寸过大，压缩
            if (file.getSize() > 1024 * 1024) {
                newName = uuid + lastName;
                ShopsUtil.targetZoomOut(filePath + "/" + goodsName, filePath + "/" + newName, filePath);
            }

            //获取本地文件的文件流
            File tempFile = new File(filePath + newName);
            FileInputStream fis = new FileInputStream(tempFile);
            //将图片上传到文件
            ShopsUtil.ftpUpload("/" + "shopTitle", newName, fis);//存储到文件服务器

            iconPath = filePathDetail + newName;
            return iconPath;

        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }
    }

}
