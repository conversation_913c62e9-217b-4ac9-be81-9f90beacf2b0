package org.haier.shop.service;

import java.io.File;
import java.io.FileInputStream;
import java.io.InputStream;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;

import org.haier.shop.config.SysConfig;
import org.haier.shop.dao.LTDao;
import org.haier.shop.util.FTPConfig;
import org.haier.shop.dao.ManagerDao;
import org.haier.shop.redis.RedisCache;
import org.haier.shop.util.PurResult;
import org.haier.shop.util.SFTPUtil;
import org.haier.shop.util.ShopsUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import net.sf.json.JSONArray;
import net.sf.json.JSONObject;

@Service
public class LTServiceImpl implements LTService{
	
	@Resource
	private LTDao ltDao;
	
	@Resource
	private ManagerDao manDao;
	
	@Autowired
	private RedisCache rc;

	@Override
	public PurResult queryCusList(Map<String, Object> map) {
		PurResult result = new PurResult();
		try {
			List<Map<String,Object>> list = ltDao.queryCusList(map);
	    	Integer count = ltDao.queryCusListCount(map);
	    	result.setStatus(1);
			result.setMsg("成功");
			result.setCount(count);
			result.setData(list);
		} catch (Exception e) {
			e.printStackTrace();
			result.setStatus(0);
			result.setMsg("异常");
		}
		return result;
	}





	@Override
	@Transactional
	public PurResult updateCusStatus(Map<String, Object> map) {
		PurResult result=new PurResult(1, "");
		//查询奖励配置
		Map<String,Object> returnMoney= ltDao.queryReturnMoneyConfig(map);
		map.putAll(returnMoney);
		//更新会员状态
		ltDao.updateCusStatus(map);
		Map<String,Object> data=ltDao.queryCommissionerById(map);
		//保存业务办理日志
		/*data.put("balance_withdrawal", data.get("balance_consumption"));
		data.put("balance_type", 2);
		data.put("operate_type", 1);
		data.put("operate_detail_type",6 );
		data.put("operate_status", 1);
		data.put("money", returnMoney.get("first_month_money"));
		ltDao.saveRecharge(data);*/

		//保存上级奖励日志
		Map<String,Object> parent_params=new HashMap<>();
		parent_params.put("commissioner_id", data.get("recommender_frist"));
		Map<String,Object> parent_data=ltDao.queryCommissionerById(parent_params);
		if(parent_data!=null){
		parent_data.put("balance_withdrawal", parent_data.get("balance_withdrawal"));
		parent_data.put("balance_type", 1);
		parent_data.put("operate_type", 1);
		parent_data.put("operate_detail_type",1 );
		parent_data.put("operate_status", 1);
		parent_data.put("money", returnMoney.get("first_money"));
		parent_data.put("source_id", data.get("id"));
		ltDao.saveRecharge(parent_data);
		if(returnMoney.get("first_next_money")!=null&& Double.parseDouble(returnMoney.get("first_next_money").toString())>0){
			parent_params.put("parent_money", returnMoney.get("first_next_money"));
			parent_params.put("return_type", 1);
			parent_params.put("source_id", data.get("id"));
			ltDao.saveReturnParentMoney(parent_params);
		}
		
		}
		
		//保存上上级奖励日志
		Map<String,Object> parent2_params=new HashMap<>();
		parent2_params.put("commissioner_id", data.get("recommender_second"));
		Map<String,Object> parent2_data=ltDao.queryCommissionerById(parent2_params);
		if(parent2_data!=null){
		parent2_data.put("balance_withdrawal", parent2_data.get("balance_withdrawal"));
		parent2_data.put("balance_type", 1);
		parent2_data.put("operate_type", 1);
		parent2_data.put("operate_detail_type",2 );
		parent2_data.put("operate_status", 1);
		parent2_data.put("money", returnMoney.get("two_money"));
		parent2_data.put("source_id", data.get("id"));
		ltDao.saveRecharge(parent2_data);
		if(returnMoney.get("two_next_money")!=null&& Double.parseDouble(returnMoney.get("two_next_money").toString())>0){
			parent2_params.put("parent_money", returnMoney.get("two_next_money"));
			parent2_params.put("return_type", 2);
			parent2_params.put("source_id", data.get("id"));
			ltDao.saveReturnParentMoney(parent2_params);
		}
		}
		//增加余额
		//查询是一刻钟到家小程序是否有相同手机号，如果有，将奖金直接发放到一刻钟到家小程序
		
		Map<String, Object> rmap = ltDao.querySamePhoneCus(data);
		if(null == rmap || rmap.isEmpty()) {
			//增加余额
			map.put("need_change", map.get("return_month_money"));
			rc.putObject("need_change", "need_change");
		}else {
			map.put("need_change", 0);
			//将余额充值到一刻钟小程序，并增加充值记录
			List<Map<String,Object>> list = new ArrayList<Map<String,Object>>();
			rmap.put("balance", map.get("return_month_money"));
			list.add(rmap);
			
			rmap.put("shopUnique", SysConfig.PUBLICSHOP);
			rmap.put("moneyType", "1");
			rmap.put("saleType", "1");
			rmap.put("saleListUnique", data.get("id"));
			rmap.put("rechargeMethod", "3");
			rmap.put("payStatus", "1");
			rmap.put("serverType", "5");
			rmap.put("orderUnicompay", data.get("id"));
			
			manDao.cusRechargept(list);
			manDao.addCusRechargeRecord(list);
		}
		ltDao.updateCommissioner2(map);
		//增加上级奖励
		data.putAll(returnMoney);
		if(parent_data!=null){
			//一级
			ltDao.updateCommYongJin(data);
		}
		if(parent2_data!=null){
			//二级
			ltDao.updateCommYongJinTwo(data);
		}
		
		
		return result;
	}





	@Override
	public PurResult queryTpyList(Map<String, Object> map) {
		PurResult result = new PurResult();
		try {
			//查询登录人的区域
			Map<String,Object> staff= ltDao.queryStaffAreaCode(map);
			if(staff!=null&&staff.get("county")!=null&&!"".equals(staff.get("county"))){
				map.put("county", staff.get("county"));
			}
			List<Map<String,Object>> list = ltDao.queryTpyList(map);
	    	Integer count = ltDao.queryTpyListCount(map);
	    	result.setStatus(1);
			result.setMsg("成功");
			result.setCount(count);
			result.setData(list);
		} catch (Exception e) {
			e.printStackTrace();
			result.setStatus(0);
			result.setMsg("异常");
		}
		return result;
	}





	@Override
	public PurResult updateTpyInfo(Map<String, Object> map) {
		PurResult result=new PurResult(1, "");
		ltDao.updateTpyInfo(map);
		return result;
	}


	@Override
	public PurResult queryTeamList(Map<String, Object> map) {
		PurResult result = new PurResult();
		try {
			List<Map<String,Object>> list = ltDao.queryTeamList(map);
	    	Integer count = ltDao.queryTeamListCount(map);
	    	result.setStatus(1);
			result.setMsg("成功");
			result.setCount(count);
			result.setData(list);
		} catch (Exception e) {
			e.printStackTrace();
			result.setStatus(0);
			result.setMsg("异常");
		}
		return result;
	}





	@Override
	public Map<String, Object> queryUserYJAllAndBalance(String id) {
		Map<String,Object> params=new HashMap<String, Object>();
		params.put("id", id);
		Map<String,Object> data =ltDao.queryUserYJAllAndBalance(params);
		return data;
	}





	@Override
	public PurResult queryYJDetailList(Map<String, Object> map) {
		PurResult result = new PurResult();
		try {
			List<Map<String,Object>> list = ltDao.queryYJDetailList(map);
	    	Integer count = ltDao.queryYJDetailListCount(map);
	    	result.setStatus(1);
			result.setMsg("成功");
			result.setCount(count);
			result.setData(list);
		} catch (Exception e) {
			e.printStackTrace();
			result.setStatus(0);
			result.setMsg("异常");
		}
		return result;
	}





	@Override
	@Transactional
	public PurResult saveRecharge(Map<String, Object> params) {
		PurResult result=new PurResult(1, "");
		Map<String,Object> data =ltDao.queryUserYJAllAndBalance(params);
		params.put("balance_withdrawal", data.get("balance_withdrawal"));
		//更改余额
		ltDao.updateCommissBalanceWithdrawal(params);
		//添加记录
		params.put("balance_type", 1);
		params.put("operate_detail_type",7 );
		params.put("operate_status", 1);
		ltDao.saveRecharge(params);
		return result;
	}





	@Override
	public Map<String, Object> queryCusDetail(String id) {
		Map<String,Object> params=new HashMap<String, Object>();
		params.put("id", id);
		Map<String,Object> data=ltDao.queryCusDetail(params);
		//查询签名图片
		List<Map<String,Object>> file=ltDao.queryCusFile(params);
		data.put("file_list", file);
		return data;
	}





	@Override
	public Map<String, Object> queryStaffAreaCode(String staff_id) {
		Map<String,Object> params=new HashMap<String, Object>();
		params.put("staff_id", staff_id);
		Map<String,Object> staff= ltDao.queryStaffAreaCode(params);
		return staff;
	}





	@Override
	public PurResult queryYjMoneyList(Map<String, Object> map) {
		PurResult result = new PurResult();
		try {
			List<Map<String,Object>> list = ltDao.queryYjMoneyList(map);
			Map<String, Object>  count = ltDao.queryYjMoneyListCount(map);
	    	result.setStatus(1);
			result.setMsg("成功");
			result.setCount(Integer.parseInt(count.get("count").toString()));
			result.setCord(count);
			result.setData(list);
		} catch (Exception e) {
			e.printStackTrace();
			result.setStatus(0);
			result.setMsg("异常");
		}
		return result;
	}





	@Override
	public PurResult queryFinanceList(Map<String, Object> map) {
		PurResult result = new PurResult();
		try {
			List<Map<String,Object>> list = ltDao.queryFinanceList(map);
	    	Integer count = ltDao.queryFinanceListCount(map);
	    	result.setStatus(1);
			result.setMsg("成功");
			result.setCount(count);
			result.setData(list);
		} catch (Exception e) {
			e.printStackTrace();
			result.setStatus(0);
			result.setMsg("异常");
		}
		return result;
	}






	@Override
	@Transactional
	public PurResult updateReturnMoneyConfig(String detailJson,HttpServletRequest request) {
		PurResult result=new PurResult(1, "");
		SFTPUtil sftp = new SFTPUtil(FTPConfig.username, FTPConfig.password, FTPConfig.host, FTPConfig.port);   
	    sftp.login(); 
	    String shop_unique= "8302016134121";
		System.out.println("---"+detailJson);
		JSONArray array= JSONArray.fromObject(detailJson);
		for (int i = 0; i < array.size(); i++) {
			JSONObject obj = (JSONObject) array.get(i); 
			Map<String, Object> data =new HashMap<>();
	        Map < String,Object>mapBean = (Map) JSONObject.toBean(obj, Map.class, data);
			MultipartFile file=null;
	        file=ShopsUtil.testMulRequest(request, "goodsPicturePath"+(i+1));
			if(file!=null){
				String orName=file.getOriginalFilename();//获取文件原名称
				String lastName=orName.substring(orName.lastIndexOf("."));
				String newName=Math.round(Math.random()*100)+lastName;
				String filePathDetail=File.separator+"image"+File.separator+shop_unique;
				String filePath = this.getClass().getClassLoader().getResource("../../").getPath();
				filePath=filePath.substring(0, filePath.length()-request.getContextPath().length())+filePathDetail;
				//存到文件服务器-start
				InputStream is;
				boolean flag=false;
				try {
					flag=ShopsUtil.savePicture(file, filePath, newName,"2");//图片保存本地
					ShopsUtil.targetZoomOut(filePath+File.separator+newName, filePath+File.separator+newName, filePath, 500, 500);
					is=new FileInputStream(new File(filePath+File.separator+newName));
					sftp.upload(FTPConfig.goods_path+"/"+shop_unique, newName, is);   
					
				} catch (Exception e) {
					e.printStackTrace();
				}
				if(flag){
					mapBean.put("file_img", filePathDetail+File.separator+newName);
				}
				
			}
			System.out.println(mapBean.toString());
			if(mapBean.get("id")!=null&&!"".equals(mapBean.get("id").toString())){
				System.out.println(1111);
				//更新
				ltDao.updateReturnMoneyConfig(mapBean);
			}else{
				System.out.println(2222);
				//新增
				ltDao.addReturnMoneyConfig(mapBean);
			}
		}
		sftp.logout();
		return result;
	}





	@Override
	public PurResult queryCashList(Map<String, Object> map) {
		PurResult result = new PurResult();
		try {
			List<Map<String,Object>> list = ltDao.queryCashList(map);
	    	Integer count = ltDao.queryCashListCount(map);
	    	result.setStatus(1);
			result.setMsg("成功");
			result.setCount(count);
			result.setData(list);
		} catch (Exception e) {
			e.printStackTrace();
			result.setStatus(0);
			result.setMsg("异常");
		}
		return result;
	}





	@Override
	@Transactional
	public PurResult updateCashStatus(Map<String, Object> map) {
		PurResult result=new PurResult(1, "");
		ltDao.updateCashStatus(map);
		if(map.get("cash_status").toString().equals("3")){
			//拒绝打款
			Map<String, Object> data= ltDao.queryCashInfo(map);
			ltDao.updateCommissCashReteturn(data);
		}else{
			map.put("operate_status", 1);
			ltDao.updateListStatus(map);
		}
		return result;
	}





	@Override
	public List<Map<String, Object>> queryCashListExcel(Map<String, Object> params) {
		List<Map<String,Object>> list = ltDao.queryCashList(params);

		return list;
	}





	@Override
	public List<Map<String, Object>> queryTpyListExcel(Map<String, Object> params) {
		//查询登录人的区域
		Map<String,Object> staff= ltDao.queryStaffAreaCode(params);
		if(staff!=null&&staff.get("county")!=null&&!"".equals(staff.get("county"))){
			System.out.println(staff.toString());
			params.put("county", staff.get("county"));
		}
		System.out.println(params.toString());
		List<Map<String,Object>> list = ltDao.queryTpyList(params);
		return list;
	}





	@Override
	@Transactional
	public void ltReturnMonthMoney() {
		//查询到期一个月未返奖励的金额
		List<Map<String,Object>> list= ltDao.queryLTReturnMonthMoneyList();
		for (Map<String, Object> map : list) {
			System.out.println("联通返利"+map.toString());
			//保存日志
			//保存业务办理日志
			map.put("balance_withdrawal", map.get("balance_consumption"));
			map.put("balance_type", 2);
			map.put("operate_type", 1);
			map.put("operate_detail_type",6 );
			map.put("operate_status", 1);
			if(Double.parseDouble(map.get("return_month_money").toString())<=Double.parseDouble(map.get("balance_return_money").toString())){
				map.put("money", map.get("return_month_money"));
			}else{
				map.put("money", map.get("balance_return_money"));
			}
			ltDao.saveRecharge(map);
			//更改余额
			map.put("return_month_money", map.get("money"));
			ltDao.updateCommissioner(map);
			
			ltDao.updateBusinessReturnMoney(map);
		
		}
	}





	@Override
	public List<Map<String, Object>> queryTpyImageList(Map<String, Object> params) {
		List<Map<String,Object>> file=ltDao.queryCusFile(params);
		return file;
	}





	@Override
	public PurResult queryReturnMoneyConfigList() {
		PurResult result=new PurResult(1, "");
		List<Map<String,Object>> list=ltDao.queryReturnMoneyConfigList();
		result.setData(list);
		return result;
	}





	@Override
	public PurResult deleteReturnMoneyConfigList(String id) {
		PurResult result=new PurResult(1, "");
		ltDao.deleteReturnMoneyConfigList(id);
		return result;
	}





	@Override
	@Transactional
	public void ltReturnParentMonthMoney() {
		//查询到期一个月未返奖励的金额
		List<Map<String,Object>> list= ltDao.queryLTReturnParentMonthMoneyList();
		for (Map<String, Object> map : list) {
			System.out.println("联通父级返利"+map.toString());
			//保存日志
			//保存业务办理日志
			map.put("balance_withdrawal", map.get("balance_consumption"));
			map.put("balance_type", 1);
			map.put("operate_type", 1);
			map.put("operate_detail_type",map.get("return_type"));
			map.put("operate_status", 1);
			ltDao.saveRecharge(map);
			//更改佣金
			map.put("return_month_money", map.get("money"));
			ltDao.updateCommissionerYongJin(map);
			//更改状态
			ltDao.updateLTReturnParentLogStatus(map);
		
		}
		
	}





	@Override
	public void ltReturnMonthCoupon() {
		//查询到期一个月未返奖励的金额
		List<Map<String,Object>> list= ltDao.queryLTReturnCouponList();
		for (Map<String, Object> map : list) {
			System.out.println("联通返优惠券"+map.toString());
			if("69".equals(map.get("taocan_id").toString())){
				//69套餐
				//增加优惠券
				Map<String,Object> coupon=new HashMap<>();
				coupon.put("shop_unique", "8302016134121");
				coupon.put("meet_amount", 10);
				coupon.put("coupon_amount", 5);
				coupon.put("type", 1);
				coupon.put("give_status", 0);
				coupon.put("is_time", 1);
				coupon.put("is_daily", 1);
				coupon.put("is_auto_grant", 1);
				coupon.put("grant_num", 1);
				coupon.put("surplus_grant_num", 0);
				ltDao.addShopCoupon(coupon);
				//添加领取记录
				coupon.put("cus_unique", map.get("cus_unique"));
				ltDao.addShopCouponCus(coupon);
				
				coupon.put("meet_amount", 10);
				coupon.put("coupon_amount", 5);
				ltDao.addShopCoupon(coupon);
				ltDao.addShopCouponCus(coupon);
				
				coupon.put("meet_amount", 19);
				coupon.put("coupon_amount", 10);
				ltDao.addShopCoupon(coupon);
				ltDao.addShopCouponCus(coupon);
				
			}else if("129".equals(map.get("taocan_id").toString())){
				//129套餐
				Map<String,Object> coupon=new HashMap<>();
				coupon.put("shop_unique", "8302016134121");
				coupon.put("meet_amount", 10);
				coupon.put("coupon_amount", 10);
				coupon.put("type", 1);
				coupon.put("give_status", 0);
				coupon.put("is_time", 1);
				coupon.put("is_daily", 1);
				coupon.put("is_auto_grant", 1);
				coupon.put("grant_num", 1);
				coupon.put("surplus_grant_num", 0);
				ltDao.addShopCoupon(coupon);
				//添加领取记录
				coupon.put("cus_unique", map.get("cus_unique"));
				ltDao.addShopCouponCus(coupon);
				
				coupon.put("meet_amount", 19);
				coupon.put("coupon_amount", 10);
				ltDao.addShopCoupon(coupon);
				ltDao.addShopCouponCus(coupon);
				
				coupon.put("meet_amount", 19);
				coupon.put("coupon_amount", 10);
				ltDao.addShopCoupon(coupon);
				ltDao.addShopCouponCus(coupon);
				
				coupon.put("meet_amount", 19);
				coupon.put("coupon_amount", 10);
				ltDao.addShopCoupon(coupon);
				ltDao.addShopCouponCus(coupon);
			}
			//更改月数和时间
			ltDao.updateBusinessReturnMoney2(map);
			
		}
		
	}


	
}
