package org.haier.shop.service;

import java.util.List;
import java.util.Map;

import javax.servlet.http.HttpServletRequest;

import org.haier.shop.util.PurResult;

public interface ExpressService {
	
	/**
	 * 查询快递公司列表
	 * @return
	 */
	public PurResult getExpressList(Map<String ,Object> params);
	
	/**
	 * 添加快递公司信息
	 * @return
	 */
	public PurResult addExpress(Map<String ,Object> params,HttpServletRequest request);
	
	/**
	 * 获取快递公司信息
	 * @return
	 */
	public Map<String ,Object> getExpress(String express_id);
	
	/**
	 * 修改快递公司信息
	 * @return
	 */
	public PurResult updateExpress(Map<String ,Object> params,HttpServletRequest request);
	
	/**
	 * 删除快递公司信息
	 * @return
	 */
	public PurResult deleteExpress(String express_id);
	
	/**
	 * 查询快递公司列表--不分页
	 * @return
	 */
	public List<Map<String ,Object>> getExpressList();

	public PurResult queryExpressPriceList(Map<String, Object> params);

	public PurResult queryParentAddress();

	public PurResult addExpressPrice(String address_list, String first_price, String count_price);

	public PurResult queryExpressPriceDetail(String express_price_id);

	public PurResult editExpressPrice(String address_list, String first_price, String count_price,
			String express_price_id);

	public PurResult deleteExpressPrice(String express_price_id);
}
