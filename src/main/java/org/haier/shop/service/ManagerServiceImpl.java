package org.haier.shop.service;

import java.math.BigDecimal;
import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;

import org.apache.shiro.SecurityUtils;
import org.apache.shiro.session.Session;
import org.apache.shiro.subject.Subject;
import org.haier.customer.entity.Order;
import org.haier.customer.entity.ShopResult;
import org.haier.customer.entity.Shops;
import org.haier.shop.dao.ManagerDao;
import org.haier.shop.dao.ShopDao;
import org.haier.shop.entity.Staff;
import org.haier.shop.util.PurResult;
import org.haier.shop.util.ShopsResult;
import org.haier.shop.util.ShopsUtil;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Service("managerService")
public class ManagerServiceImpl implements ManagerService {
	@Resource
	private ManagerDao manDao;
	@Resource
	private ShopDao shopDao;
	
	
	
	/**
	 * 查询指定日期的平台充值信息
	 * @param datetime
	 * @return
	 */
	public PurResult queryOnlinePlatRechargeListDetail(String datetime) {
		PurResult pr = new PurResult();
		Map<String,Object> map = new HashMap<String,Object>();
		map.put("datetime", datetime);
		
		List<Map<String,Object>> list = manDao.queryOnlinePlatRechargeListDetail(map);
		
		pr.setStatus(1);
		pr.setMsg("查询成功！");
		pr.setData(list);
		return pr;
	}
	/**
	 * 查询平台为线上会员充值的记录
	 * @param startTime
	 * @param endTime
	 * @return
	 */
	public PurResult queryOnlineCusRechargeList(String startTime,String endTime) {
		PurResult pr = new PurResult();
		Map<String,Object> map = new HashMap<String,Object>();
		map.put("startTime", startTime);
		map.put("endTime", endTime);
		List<Map<String,Object>> rechargeList = manDao.queryOnlineCusRechargeList(map);
		pr.setStatus(1);
		pr.setMsg("查询成功！");
		pr.setData(rechargeList);
		return pr;
	}
	/**
	 * 需要将不存在的会员信息找出，并提示
	 */
	public PurResult queryOnlineCusMsgByCusAccount(String cusAccounts) {
		PurResult pr = new PurResult();
		if(null == cusAccounts || cusAccounts.equals("")) {
			pr.setStatus(0);
			pr.setMsg("没有满足条件的会员信息");
		}else {
			String[] cusAccountListDetail = cusAccounts.split(";");
			List<String> cusAccountList = new ArrayList<String>();
			List<String> cusMsg = new ArrayList<String>();
			for(String s : cusAccountListDetail) {
				cusAccountList.add(s.split(":")[0]);
				cusMsg.add(s);
			}
			List<Map<String,Object>> cusList = manDao.queryOnlineCusMsgByCusAccount(cusAccountList);
			//将充值后的余额信息添加
			for(Map<String,Object> map : cusList) {
				for(String cus : cusMsg) {
					if(map.get("cus_account").toString().equals(cus.split(":")[0])) {
						cusMsg.remove(cus);
						Double balance = Double.parseDouble(map.get("pc_balance").toString());
						
						Double nbalance = Double.parseDouble(cus.split(":")[1]);
						map.put("rechargeMoney", nbalance);
						map.put("nbalance", balance + nbalance);
						break;
					}
				}
			}
			
			//检验不存在的数据，返回界面
			for(String s : cusAccountList) {
				
				boolean flag = true;
				for(Map<String,Object> map : cusList) {
					if(s.equals(map.get("cus_account").toString())) {
						flag = false;
						break;
					}
				}
				
				if(flag) {
					//这个用户没查询到，提示信息反馈到前端
					Map<String,Object> map = new HashMap<String,Object>();
					map.put("pc_nick_name", "用户信息不存在，请核实账号");
					map.put("pc_balance", -100);
					map.put("cus_unique", "");
					map.put("cus_protrait", "");
					map.put("cus_account", s);
					cusList.add(0,map);
				}
			}
			
			pr.setStatus(1);
			pr.setMsg("查询成功");
			pr.setData(cusList);
		}
		
		return pr;
	}
	
	@Transactional
	public PurResult rechargeForCus(String cusAccounts) {
		PurResult pr = new PurResult();
		
		//查询本月是否已有充值，如果有，不允许再次充值
		List<Map<String,Object>> rlist = manDao.queryPlatcusRechargeMsg(null);
		
//		if(null != rlist && !rlist.isEmpty()) {
//			pr.setStatus(0);
//			pr.setMsg("本月已充值，请勿重复充值！");
//			return pr;
//		}
		
		if(null == cusAccounts || cusAccounts.trim().equals("")) {
			pr.setStatus(0);
			pr.setMsg("没有会员需要充值");
			return pr;
		}
		
		String[] cusAccountListDetail = cusAccounts.split(";");
		List<Map<String,Object>> cusAccountList = new ArrayList<Map<String,Object>>();
		String orderUnicompay = "pt" + System.currentTimeMillis();
		Integer i = 0;
		BigDecimal total = new BigDecimal(0);
		for(String s : cusAccountListDetail) {
			Map<String,Object> temp = new HashMap<String,Object>();
			temp.put("cusUnique", s.split(":")[0]);
			temp.put("balance", s.split(":")[1]);
			temp.put("shopUnique", "*************");
			temp.put("moneyType", "1");
			temp.put("saleType", "1");
			temp.put("saleListUnique", "");
			temp.put("rechargeMethod", "4");
			temp.put("payStatus", "1");
			temp.put("serverType", "5");
			temp.put("orderUnicompay", orderUnicompay + (i++));
			cusAccountList.add(temp);
			
			total = total.add(new BigDecimal(s.split(":")[1]));
		}
		
		total.setScale(2,BigDecimal.ROUND_HALF_UP);
		//创建订单号
		//添加充值记录
		//修改会员余额
		manDao.addCusRechargeRecord(cusAccountList);
		manDao.cusRechargept(cusAccountList);
		
		Map<String,Object> rmap = new HashMap<String,Object>();
		rmap.put("cusCount", cusAccountList.size());
		rmap.put("cusAmount", total);
		
		manDao.addNewPlatCurRecharge(rmap);
		pr.setStatus(1);
		pr.setMsg("充值成功，点击查询，查看最新余额信息！");
		return pr;
	}
	
	public PurResult queryCusRechargeStaticByShop(String startTime,String endTime,Integer page,Integer limit,String shopUnique) {
		PurResult pr = new PurResult(1, "查询成功!");
		Map<String,Object> map = new HashMap<String,Object>();
		map.put("startTime", startTime);
		map.put("endTime", endTime);
		map.put("startNum", (page-1)*limit);
		map.put("pageSize", limit);
		map.put("shopUnique", shopUnique);
		List<Map<String,Object>> statisList = manDao.queryCusRechargeStaticByShop(map);
		pr.setCount(manDao.queryCusRechargeStaticByShopCount(map));
		pr.setData(statisList);
		return pr;
	}
	
	public PurResult queryCusRechargeStatic(String shopUnique, String startTime, String endTime, String shopMsg) {
		PurResult pr = new PurResult(1, "查询成功");
		Map<String,Object> map = new HashMap<String,Object>();
		map.put("shopUnique", shopUnique);
		map.put("startTime", startTime);
		map.put("endTime", endTime);
		map.put("shopMsg", shopMsg);
		map.put("cusType", "1");
		map.put("rechargeStatus", "1");
		map.put("rechargeMethod", -1);
		
		Subject subject = SecurityUtils.getSubject();
		Session session = subject.getSession();
		Staff staff = (Staff) session.getAttribute("staff");
		
		//充值金额
		Map<String,Object> resMap = manDao.queryCusRechargeStatic(map); 
		if(null == resMap) {
			resMap = new HashMap<String,Object>();
		}
		if(resMap.isEmpty()) {
			resMap.put("rechargeMoney", 0.00);
			resMap.put("giveMoney", 0.00);
		}
		
		//退款金额
		map.put("rechargeMethod", 5);
		Map<String,Object> retMap = manDao.queryCusRechargeStatic(map);
		if(null == retMap || retMap.isEmpty()) {
			resMap.put("refundsMoney", 0.00);
			resMap.put("refundsGiveMoney", 0.00);
		}else {
			resMap.put("refundsMoney", retMap.get("rechargeMoney"));
			resMap.put("refundsGiveMoney", retMap.get("giveMoney"));
		}
		
		//赠送金额
		map.put("rechargeMethod", 8);
		Map<String,Object> actMap = manDao.queryCusRechargeStatic(map);
		if(null == actMap || actMap.isEmpty()) {
			resMap.put("actMoney", 0.00);
			resMap.put("actGiveMoney", 0.00);
		}else {
			resMap.put("actMoney", actMap.get("rechargeMoney"));
			resMap.put("actGiveMoney", actMap.get("giveMoney"));
		}
		
		//消费金额
		map.put("cusType", 3);
		map.remove("rechargeMethod");
		Map<String,Object> tempMap = manDao.queryCusRechargeStatic(map);
		if(null == tempMap || tempMap.isEmpty()) {
			resMap.put("consumeMoney", 0.00);
			resMap.put("consumeGiveMoney", 0.00);
		}else {
			resMap.put("consumeMoney", tempMap.get("rechargeMoney"));
			resMap.put("consumeGiveMoney", tempMap.get("giveMoney"));
		}
		tempMap.clear();
		
		//退卡金额
		if(staff.getShop_type() == 6) {
			
			tempMap = manDao.queryCusRefundsStatic(map);
			if(null == tempMap || tempMap.isEmpty()) {
				resMap.put("refundsCard", 0.00);
				resMap.put("refundsCardMoney", 0.00);
			}else {
				resMap.put("refundsCard", tempMap.get("refundsMoney"));
				resMap.put("refundsCardMoney", tempMap.get("refundsGiveMoney"));
			}
		}else if(staff.getShop_type() == 12){
			map.put("rechargeStatus", -1);
			tempMap = manDao.queryCusYJRefundsStatis(map);
			if(null == tempMap || tempMap.isEmpty()) {
				resMap.put("refundsCard", 0.00);
				resMap.put("refundsCardMoney", 0.00);
			}else {
				resMap.put("refundsCard", tempMap.get("refundsMoney"));
				resMap.put("refundsCardMoney", tempMap.get("refundsGiveMoney"));
			}
		}
		
		//当前店铺的会员余额和赠额余额
		Map<String,Object> balanceMap = manDao.queryNowCusbalance(map);
		resMap.putAll(balanceMap == null ? new HashMap<String,Object>() : balanceMap);
		Map<String,Object> lastBalanceMap = manDao.queryLastCusbalance(map);
		resMap.putAll(lastBalanceMap == null ? new HashMap<String,Object>() : lastBalanceMap);
		pr.setData(resMap);
		return pr;
	}
	/**
	 * 管理员登录
	 */
	public ShopsResult login(String manager_account, String manager_pwd) {
		ShopsResult sr=new ShopsResult();
		Map<String,Object> map=new HashMap<String,Object>();
		if(null==manager_account||null==manager_pwd){
			sr.setStatus(1);
			sr.setMsg("账号和密码不能为空！");
			return sr;
		}
//		ZhiFuBaoUtil.test();
		map.put("manager_account", manager_account.trim());
		map.put("manager_pwd", ShopsUtil.string2MD5(manager_pwd.trim()).trim());
//		List<Map<String,Object>> resultMap=manDao.login(map);
		Map<String,Object>  resultMap=shopDao.login(map);
//		System.out.println("登录信息："+map);
		if(null==resultMap||resultMap.size()==0){
			sr.setStatus(1);
			sr.setMsg("账号或密码错误！");
			return sr;
		}
		sr.setStatus(0);
		sr.setMsg("登录成功！");
		sr.setData(resultMap);
		return sr;
	}
	
	/**
	 * 注册新帐户
	 * @param manager_account
	 * @param manager_pwd
	 * @param manager_phone
	 * @param manager_name
	 * @return
	 */
	@Transactional
	public ShopsResult addNewManager(String manager_account,String manager_pwd,String manager_phone,String manager_name){
		ShopsResult sr=new ShopsResult();
		Map<String,Object> map=new HashMap<String,Object>();
		map.put("manager_account", manager_account);
		int k=manDao.login(map).size();
//		System.out.println(k+"条信息已存在");
		if(k>=1){
			sr.setStatus(1);
			sr.setMsg("该账户已存在，请选择新的账户");
//			System.out.println("添加状态为："+sr);
			return sr;
		}
		map.clear();
		map.put("manager_phone", manager_phone);
		k=manDao.login(map).size();
		if(k>=1){
			sr.setStatus(1);
			sr.setMsg("该手机号已注册！");
//			System.out.println("该手机已被注册"+sr);
			return sr;
		}
		map.put("manager_account", manager_account);
		map.put("manager_pwd", ShopsUtil.string2MD5(manager_pwd));
		map.put("manager_name", manager_name);
		map.put("manager_unique", new Date().getTime());
		k=manDao.addNewManager(map);
		if(k==0){
			sr.setStatus(1);
			sr.setMsg("注册失败，请稍后重试！");
//			System.out.println("信息添加失败！"+sr);
			return sr;
		}
		
		sr.setStatus(0);
		sr.setMsg("添加成功！");
//		System.out.println("注册成功！");
		return sr;
	}
	
	
	/**
	 * 查询管理元旗下所有POS
	 * @param manager_unique
	 * @return
	 */
	public ShopsResult queryMachineNums(String manager_unique){
		ShopsResult sr=new ShopsResult();
		Map<String,Object> map=new HashMap<String, Object>();
		map.put("manager_unique", manager_unique);
		List<Shops> result= manDao.queryMachineNums(map);
		if(null==result||result.isEmpty()){
			sr.setStatus(1);
			sr.setMsg("您没有相关的店铺和POS");
			return sr;
		}
		sr.setStatus(0);
		sr.setMsg("查询成功！");
		sr.setData(result);
		return sr;
	}
	
	/**
	 * 商品接口
	 * @param shopID
	 * @param pageIndex
	 * @param pageSize
	 * @param startTime
	 * @param endTime
	 * @return
	 */
	public ShopResult queryShopsGoods(String shopID,Integer pageIndex,Integer pageSize,Timestamp startTime, Timestamp endTime){
		ShopResult sr=new ShopResult();
		Integer pageCount=0;
		List<Map<String,Object>> result=null;
		Map<String,Object> map=new HashMap<String, Object>();
		if(pageSize!=0){
			map.put("startNum", (pageIndex-1)*pageSize);
			map.put("pageSize", pageSize);
		}
		map.put("shop_unique", shopID);
		map.put("startTime", startTime);
		map.put("endTime", endTime);
		if(null==shopID){
			pageCount=manDao.queryManagerPages(map);
			result=manDao.queryManagerGoods(map);
		}else{
			pageCount=manDao.queryGoodsPages(map);
			result=manDao.queryShopsGoods(map);
		}
		
//		System.out.println("商品查询"+map);
		if(null==result||result.isEmpty()){
			sr.setStatus(1);
			sr.setMessage("没有相关产品信息！");
			return sr;
		}
//		System.out.println(pageCount);
//		System.out.println(pageSize);
//		System.out.println(pageCount/pageSize);
		if(pageCount%pageSize==0){
			sr.setPageCount(pageCount/pageSize);
		}else{
			sr.setPageCount(pageCount/pageSize+1);
		}
		sr.setStatus(0);
		sr.setMessage("查询成功！");
		sr.setData(result);
		sr.setPageIndex(pageIndex);
		sr.setPageSize(result.size());
		return sr;
	}
	
	/**
	 * 查询管理员管理的所有店铺
	 */
	public ShopResult queryShops(Timestamp startTime,Timestamp endTime){
		ShopResult sr=new ShopResult();
		Map<String,Object> map=new HashMap<String, Object>();
		map.put("startTime", startTime);
		map.put("endTime", endTime);
		List<Map<String,Object>> data=manDao.queryShops(map);
		if(null==data||data.isEmpty()){
			sr.setStatus(1);
			sr.setMessage("没有满足条件的店铺信息！");
			return sr;
		}
		sr.setStatus(0);
		sr.setMessage("查询成功！");
		sr.setData(data);
		return sr;
	}
	
	/**
	 * 3.销售接口（根据销售时间段返回所有销售记录）
	 * @param shopID
	 * @param startTime
	 * @param endTime
	 * @param pageIndex
	 * @param pageSize
	 * @return
	 */
	public ShopResult queryOrderLists(String shopID,Timestamp startTime,Timestamp endTime,Integer pageIndex,Integer pageSize){
		ShopResult sr=new ShopResult();
		int k=0;
		List<Order> result=null;
		Map<String,Object> map=new HashMap<String, Object>();
		map.put("startTime", startTime);
		map.put("shop_unique", shopID);
		map.put("endTime", endTime);
		if(pageIndex<=0){
			pageIndex=1;
		}
		
		map.put("startNum", (pageIndex-1)*pageSize);
		map.put("pageSize", pageSize);
		k=manDao.queryOrderCount(map);//
		
//		System.out.println(k);
//		System.out.println("订单数量查询字符集"+map);
		result=manDao.queryOrderLists(map);
		if(null==result||result.isEmpty()){
			sr.setMessage("没有满足条件的订单信息！");
			sr.setStatus(1);
			return sr;
		}
		if(k%pageSize==0){
			sr.setPageCount(k/pageSize);
		}else{
			sr.setPageCount(k/pageSize+1);
		}
		sr.setStatus(0);
		sr.setMessage("查询成功！");
		sr.setPageIndex(pageIndex);
		sr.setPageSize(result.size());
		sr.setData(result);
		return sr;
	}
	
	/**
	 * 商品分页查询
	 * @param shopID
	 * @param pageIndex
	 * @param pageSize
	 * @param startTime
	 * @param endTime
	 * @return
	 * 
	 */
	public ShopResult queryGoodsByPage(String shopID,Integer pageIndex,Integer pageSize,Timestamp startTime,Timestamp endTime){
		ShopResult sr=new ShopResult();
		Map<String,Object> map=new HashMap<String, Object>();
		map.put("shop_unique", shopID);
		map.put("startTime", startTime);
		map.put("endTime", endTime);
		map.put("startNum", (pageIndex-1)*pageSize);
		map.put("pageSize", pageSize);
//		System.out.println("商品分页查询！"+map);
		int pageCount=manDao.queryGoodsPageCounts(map);
		List<Map<String,Object>> result=manDao.queryGoodsByPage(map);
		if(null==result||result.isEmpty()){
			sr.setStatus(1);
			sr.setMessage("没有满足条件的商品信息！");
			return sr;
		}
		
		if(pageCount%pageSize==0){
			sr.setPageCount(pageCount/pageSize);
		}else{
			sr.setPageCount(pageCount/pageSize+1);
		}
		sr.setStatus(0);
		sr.setMessage("查询成功！");
		sr.setPageIndex(pageIndex);
		sr.setPageSize(result.size());
		sr.setData(result);
		return sr;
	}
	
	/**
	 * 会员信息详情查询
	 * @param map
	 * @return
	 */
	public ShopsResult queryCusDetail(Map<String,Object> map){
		ShopsResult sr=new ShopsResult();
		Map<String,Object> data=manDao.queryCusDetail(map);
		sr.setData(data);
		sr.setStatus(1);
		sr.setMsg("查询成功！");
		return sr;
	}
}
