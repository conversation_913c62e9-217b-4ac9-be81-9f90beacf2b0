package org.haier.shop.service;

import org.haier.shop.mqtt.MqttForGoodsUpdate;
import org.haier.shop.redis.RedisCache;
import org.haier.shop.util.mqtt.MqttxUtil;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @Description
 * @ClassName MqttServiceImpl
 * <AUTHOR>
 * @Date 2024/9/24 17:51
 **/
@Service("mqttServiceImpl")
public class MqttServiceImpl implements MqttService {

    @Resource
    private RedisCache redis;

    @Override
    public void sendGoodsUpdate(List<MqttForGoodsUpdate> goodsUpdates, String shopUnique) {
        try {
            //获取需要通知的店铺列表
            Object mac = redis.getObject("topic_" + shopUnique);
            if(null != mac) {
                List<String> macIdList = (List<String>) mac;
                // 2 MQTT 发送消息
                for (String macid : macIdList) {
                    //依次发送MQTT消息
                    Map<String, Object> data = new HashMap<String, Object>();
                    data.put("ctrl", "msg_goods_update");
                    data.put("ID", macid);
                    data.put("status", 200);
                    data.put("data", goodsUpdates);
                    data.put("count", goodsUpdates.size());
                    MqttxUtil.sendMapMsg(data, macid);
                }
            }
        }catch (Exception e) {
            e.printStackTrace();
        }
    }
}
