package org.haier.shop.service;

import cn.hutool.core.date.DateUtil;
import cn.hutool.json.JSONUtil;
import org.haier.ele.util.Qutil;
import org.haier.meituan.util.MUtil;
import org.haier.shop.dao.PayTypeDao;
import org.haier.shop.dao.ShopDao;
import org.haier.shop.entity.SaleList;
import org.haier.shop.entity.SaleListDetail;
import org.haier.shop.entity.ShopPayMsg;
import org.haier.shop.params.SubAccountAddOrderParam;
import org.haier.shop.util.ShopsResult;
import org.haier.shop.util.common.DingTalkUtils;
import org.haier.shop.util.common.SendDingDingTalkUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.bind.annotation.RequestBody;

import javax.annotation.Resource;
import java.util.*;

/**
* @author: 作者:qwl
* @version: 2025年03月29日 下午4:22:47
*
*/
@Service
public class SubAccountServiceImpl implements SubAccountService{

	private static final Logger log = LoggerFactory.getLogger(SubAccountServiceImpl.class);
	@Resource
 	private ShopDao shopDao;
	@Resource
	private PayTypeDao payTypeDao;
	@Resource
	private SendDingDingTalkUtils SendDingTalkMessage;
	@Override
	public String addOrder(SubAccountAddOrderParam param) {
		Map<String,Object> result =payTypeDao.getShopPayMsgBySubAccount(param.getAccNo());
		if(result==null){
			SendDingTalkMessage.sendDingDingTalkMsgText(DateUtil.now(),"子账号不存在,入参："+ JSONUtil.toJsonStr(param),null);
			log.error("该子账号不存在,入参：{}", JSONUtil.toJsonStr(param));
			return "error";
		}
		try {
			//订单新增
			addSaleList(param,result);
			//订单详情
			addSaleListDetail(param);
			return "SUCCESS";
		}catch (Exception e){
			SendDingTalkMessage.sendDingDingTalkMsgText(DateUtil.now(),"子账号新增订单异常,入参："+ JSONUtil.toJsonStr(param)+"异常信息:"+e.getMessage(),null);
			log.error("子账号新增订单异常,入参：{}", JSONUtil.toJsonStr(param));
			return "error";
		}

	}

	private void addSaleListDetail(SubAccountAddOrderParam param) {
		List<SaleListDetail> listDet = new ArrayList<>();
		SaleListDetail saleListDetail = new SaleListDetail();
		saleListDetail.setSaleListUnique(param.getSaleListUnique());
		listDet.add(saleListDetail);
		shopDao.batchAddSaleDetail(listDet);
	}

	private void addSaleList(SubAccountAddOrderParam param,Map<String,Object> result) {
		List<SaleList> salelists = new ArrayList<SaleList>();
		SaleList saleList = new SaleList();
		Random ran = new Random();
		int m = ran.nextInt(89999) + 10000;
		Random ran2 = new Random();
		int m2 = ran2.nextInt(89999) + 10000;
		String saleListUnique = new String(new Date().getTime() + "").substring(7) + "" + m + "" + m2;


		String shopUnique = result.get("shop_unique").toString();
		saleList.setSaleListUnique(Long.parseLong(saleListUnique));// 商铺唯一性标识
		saleList.setShopUnique(Long.parseLong(shopUnique));// 商铺唯一性标识
		saleList.setSaleListDatetime(Qutil.getTime());//订单生成时间

		saleList.setSaleListTotal(Double.valueOf(MUtil.strObject(param.getTransAmount())));//销售单金额（商品）
		saleList.setCusUnique("");//用户的唯一性标识
		saleList.setSaleType(0);//订单类型（5-饿了么外卖订单）
		saleList.setSaleListDiscount(1.0);// 折扣率
		saleList.setSaleListState(3);// 已付款
		saleList.setSaleListPur(0.0);
		saleList.setSaleListTotalCount(0);
		saleList.setSaleListHandlestate(1);// 发货状态；1-无效订单 2-未发货 3-已发货 4-已收货
		saleList.setSaleListPayment(16);//子账簿转账
		saleList.setCusUnique("");//用户的唯一性标识
		saleList.setSaleListName("");//订单收货人姓名
		saleList.setSaleListDelfee(0.0);
		saleList.setSaleListSameType(1);
		saleList.setMachineNum(0);
		saleList.setSaleListRemarks("11");
		saleList.setSaleListCashier(1);
		saleList.setSaleListActuallyReceived(Double.valueOf(MUtil.strObject(param.getTransAmount())));//订单实际收到金额
		param.setSaleListUnique(Long.parseLong(saleListUnique));
		salelists.add(saleList);
		shopDao.addrSaleList(salelists);
	}
}
