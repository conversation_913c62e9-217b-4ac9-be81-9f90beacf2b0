package org.haier.shop.service;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;

import org.haier.meituan.util.MUtil;
import org.haier.shop.dao.GoodsDao;
import org.haier.shop.dao.ShopDao;
import org.haier.shop.dao2.SupplierInfoDao;
import org.haier.shop.util.PurResult;
import org.haier.shop.util.PushThread;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Service
@Transactional
public class SupplierInfoServiceImpl implements SupplierInfoService{
	
	@Resource
	private SupplierInfoDao supplierInfoDao;
	
	@Resource
	private ShopDao shopDao;
	
	@Resource
	private GoodsDao goodsDao;

	@Override
	public PurResult getSupplierList(Map<String, Object> params) {
		PurResult result = new PurResult();
		try {
			List<Map<String ,Object>> list = supplierInfoDao.getSupplierList(params);
			Integer percentage_num = supplierInfoDao.getPercentageNum();
			for(int i=0;i<list.size();i++){
				list.get(i).put("percentage_num", percentage_num);
			}
			result.setCord(percentage_num);
			result.setData(list);
			result.setStatus(1);
			result.setMsg("成功");
		} catch (Exception e) {
			result.setStatus(0);
			result.setMsg("异常");
		}
		
		return result;
	}

	@Override
	public PurResult getSupplierGoodsList(Map<String, Object> params) {
		PurResult result = new PurResult();
		try {
			List<Map<String ,Object>> list = supplierInfoDao.getSupplierGoodsList(params);
			Integer percentage_num = supplierInfoDao.getPercentageNum();
			for(int i=0;i<list.size();i++){
				//获取当前商品的所有规格列表
				List<Map<String ,Object>> specList = supplierInfoDao.getGoodsSpecList(list.get(i));
				list.get(i).put("specList", specList);
				//促销编号
				String promtion_code = MUtil.strObject(list.get(i).get("promtion_code"));
				//是否指定商家:0-默认全部，1-指定商家
		  		String shop_type = MUtil.strObject(list.get(i).get("shop_type"));
		  		if(shop_type != null && shop_type.equals("1")){
		  			//当前促销活动是否指定该商家
		  			params.put("promtion_code", promtion_code);
		  			Integer count = supplierInfoDao.getPromtionShopCount(params);
		  			if(count == 0){
		  				list.get(i).put("goods_beans_count", 0);
		  				list.get(i).put("beans_goods_give_id", 0);
		  				list.get(i).put("surplus_count", 0);
		  			}
		  		}
		  		Integer goods_beans_count = Integer.parseInt(MUtil.strObject(list.get(i).get("goods_beans_count")));
				list.get(i).put("present_beans", goods_beans_count);
				goods_beans_count = (goods_beans_count*percentage_num)/100;
				list.get(i).put("goods_beans_count", goods_beans_count);//实际赠送百货豆
			}
			result.setCord(percentage_num);
			result.setData(list);
			result.setStatus(1);
			result.setMsg("成功");
		} catch (Exception e) {
			e.printStackTrace();
			result.setStatus(0);
			result.setMsg("异常");
		}
		
		return result;
	}

	
	/**
	 * 保存订单信息
	 * @param shop_unique 店铺唯一标示
	 * @param order_remarks 订单备注
	 * @param goods [{
	 * 	company_code 公司编号
	 *  goodsList [{
	 *  	goods_barcode 商品条形码
	 *  	compose_specs_id 规格组合id
	 *  	goods_count 商品数量
	 *  	goods_name 商品名称
	 *  	goodsunit_name 商品单位名称
	 *  	goods_spec_name 商品规则名称
	 *  	wholesale_price 销售价
	 *  	goods_id 商品id
	 *  	goods_price 成本价
	 *  	base_barcode 基本单位条形编码
	 *  	proportion_num 换算比例
	 *  	goods_beans_count 每单位实际赠送百货豆
	 *  	present_beans 每单位满足规则赠送百货豆
	 *  	beans_goods_give_id 优惠活动id
	 *  }]
	 * }]
	 * @return
	 */
	@Override
	public PurResult saveOrder(String shop_unique,String order_remarks ,String goods) {
		PurResult result = new PurResult();
		try {
			//获取店铺信息
			Map<String ,Object> paramsShop = new HashMap<String, Object>();
			paramsShop.put("shop_unique", shop_unique);
			Map<String ,Object> shopInfo = shopDao.queryShopMessage(paramsShop);
			
			//库存不足供货商信息
			List<Map<String ,Object>> companyNoList = new ArrayList<Map<String,Object>>();
			//库存不足商品信息
			List<Map<String ,Object>> stockNoList = new ArrayList<Map<String,Object>>();
			
			String msg = "";
			List<Map<String ,Object>> goodsList = MUtil.strToList(goods);
			
			for(int i=0;i<goodsList.size();i++){
				String company_code = MUtil.strObject(goodsList.get(i).get("company_code"));
				String company_name = MUtil.strObject(goodsList.get(i).get("company_name"));
				Map<String ,Object> company = new HashMap<String, Object>();
				company.put("company_code", company_code);
				company.put("company_name", company_name);
				companyNoList.add(company);
				
				List<Map<String ,Object>> goods_infos1 = (List<Map<String, Object>>) goodsList.get(i).get("goodsList");
				for(int j=0;j<goods_infos1.size();j++){
					String compose_specs_id = MUtil.strObject(goods_infos1.get(j).get("compose_specs_id"));//规格组合id
					Integer goods_count = Integer.parseInt(MUtil.strObject(goods_infos1.get(j).get("goods_count")));//商品数量
					String goods_name = MUtil.strObject(goods_infos1.get(j).get("goods_name"));//商品名称
					String goodsunit_name = MUtil.strObject(goods_infos1.get(j).get("goodsunit_name"));//商品单位名称
					String goods_spec_name = MUtil.strObject(goods_infos1.get(j).get("goods_spec_name"));//商品规则名称
					
					//获取该商品规格的总库存
					Map<String ,Object> params1 = new HashMap<String, Object>();
					params1.put("goods_code", MUtil.strObject(goods_infos1.get(j).get("goods_id")));
					params1.put("company_code", company_code);
					if(!compose_specs_id.equals("")){
						params1.put("compose_specs_id", compose_specs_id);
					}
					Integer totalCount = supplierInfoDao.getTotalCount(params1);
					if(totalCount == null){
						totalCount = 0;
					}
					
					if(goods_count > totalCount){
						//当前商品库存不足
						msg = goods_name+goods_spec_name+"("+goodsunit_name+")"+"库存不足！"+ "<br/>"+msg;
						//返回库存不足信息到页面
						stockNoList.add(goods_infos1.get(j));
					}
				}
			}
			if(stockNoList.size()>0){
				Map<String ,Object> cord = new HashMap<String, Object>();
				cord.put("companyList", companyNoList);
				cord.put("list", stockNoList);
				
				result.setCord(cord);
				result.setStatus(2);
				result.setMsg(msg);
				return result;
			}
			
			
			for(int i=0;i<goodsList.size();i++){
				String company_code = MUtil.strObject(goodsList.get(i).get("company_code"));
				
				List<Map<String ,Object>> goods_infos = (List<Map<String, Object>>) goodsList.get(i).get("goodsList");
				Double order_money = 0.00;
				for(int j=0;j<goods_infos.size();j++){
					Integer goods_count = Integer.parseInt(MUtil.strObject(goods_infos.get(j).get("goods_count")));//商品数量
					Double wholesale_price = Double.valueOf(MUtil.strObject(goods_infos.get(j).get("wholesale_price")));//销售价
					//计算总金额
					order_money = order_money + wholesale_price*goods_count;
				}
				if(goods_infos.size()>0){
					//添加订单主表
					Map<String ,Object> paramsOrder = new HashMap<String, Object>();
					String order_code = createOrderNum();
					paramsOrder.put("order_code", order_code);
					paramsOrder.put("company_code", company_code);
					paramsOrder.put("order_remarks", order_remarks);
					paramsOrder.put("pay_mode", 1);//支付方式：1、货到付款；2、支付宝；3、微信;4、其他
					paramsOrder.put("pay_status", 1);//支付状态：1、欠款；2、已结清
					paramsOrder.put("customer_code", shop_unique);//客户编号
					paramsOrder.put("collect_name", MUtil.strObject(shopInfo.get("shop_name")));//收货人姓名
					paramsOrder.put("collect_phone", MUtil.strObject(shopInfo.get("shop_phone")));//联系电话
					paramsOrder.put("collect_address", MUtil.strObject(shopInfo.get("shop_address_detail")));//收货地址
					paramsOrder.put("order_type", 1);//订单类型 0：自动下单；1：客户下单
					paramsOrder.put("order_money", order_money);//订单总金额
					paramsOrder.put("shop_latitude", MUtil.strObject(shopInfo.get("shop_latitude")));//店铺纬度
					paramsOrder.put("shop_longitude", MUtil.strObject(shopInfo.get("shop_longitude")));//店铺经度
					
					//添加云商订单消息提醒
					Map<String ,Object> sysmsgParams = new HashMap<String, Object>();
					sysmsgParams.put("sysmsg_content", "您有一条新的云商订单，请查看！");
					sysmsgParams.put("source_code", order_code);
					sysmsgParams.put("company_code", company_code);
					sysmsgParams.put("sysmsg_type", "1");
					supplierInfoDao.addSysmsg(sysmsgParams);
					Integer sysmsg_id = Integer.parseInt(MUtil.strObject(sysmsgParams.get("sysmsg_id")));
					
					//获取业务员id
					List<String> userList = supplierInfoDao.getUserList(company_code);
					List<Map<String ,Object>> sysmsgUserList = new ArrayList<Map<String,Object>>();
					for(int k=0;k<userList.size();k++){
						Map<String ,Object> sysmsgUser = new HashMap<String, Object>();
						sysmsgUser.put("sysmsg_id", sysmsg_id);
						sysmsgUser.put("staffer_id", userList.get(k));
						sysmsgUserList.add(sysmsgUser);
					}
					//添加供货商消息用户表
					supplierInfoDao.addSysmsgUser(sysmsgUserList);
					//推送消息
					Map<String, String> extra=new HashMap<String, String>();
					extra.put("msgType", "1");
					extra.put("source_id",order_code);
					PushThread pushThread=new PushThread(userList, "智慧云商", "2", "您有一条新的云商订单，请查看！",extra);
					Thread t=new Thread(pushThread);
					t.start();
					
					Integer present_beans = 0;
					Integer platform_beans = 0;
					
					//去重
					List<Map<String ,Object>> new_goods_infos = removedup(goods_infos);
					for(int m=0;m<new_goods_infos.size();m++){
						String goods_barcode = MUtil.strObject(new_goods_infos.get(m).get("goods_barcode"));//商品条形码
						String goods_name = MUtil.strObject(new_goods_infos.get(m).get("goods_name"));//商品名称
						String goodsunit_name = MUtil.strObject(new_goods_infos.get(m).get("goodsunit_name"));//商品单位名称
						Double wholesale_price = Double.valueOf(MUtil.strObject(new_goods_infos.get(m).get("wholesale_price")));//销售价
						String goods_id = MUtil.strObject(new_goods_infos.get(m).get("goods_id"));
						Double goods_price = Double.valueOf(MUtil.strObject(new_goods_infos.get(m).get("goods_price")));//成本价
						String base_barcode = MUtil.strObject(new_goods_infos.get(m).get("base_barcode"));//基本单位条形编码
						Double proportion_num = Double.parseDouble(MUtil.strObject(new_goods_infos.get(m).get("proportion_num"))); //换算比例
						Integer goods_beans_count = Integer.parseInt(MUtil.strObject(new_goods_infos.get(m).get("goods_beans_count")));//每单位实际赠送百货豆
						Integer present_beans_count = Integer.parseInt(MUtil.strObject(new_goods_infos.get(m).get("present_beans")));//每单位满足规则赠送百货豆
						String beans_goods_give_id = MUtil.strObject(new_goods_infos.get(m).get("beans_goods_give_id"));//赠送百货豆优惠规则id
						
						//添加订单详情表
						Map<String ,Object> paramsOrderdetail = new HashMap<String, Object>();
						paramsOrderdetail.put("order_code", order_code);
						paramsOrderdetail.put("goods_code", goods_barcode);
						paramsOrderdetail.put("goods_cost", goods_price);
						paramsOrderdetail.put("goods_price", wholesale_price);
						paramsOrderdetail.put("goods_name", goods_name);
						paramsOrderdetail.put("goods_unit", goodsunit_name);
						paramsOrderdetail.put("goods_id", goods_id);
						Double fact_price = 0.00;
						Integer total_goods_count = 0;
						for(int j=0;j<goods_infos.size();j++){
							Integer goods_count1 = Integer.parseInt(MUtil.strObject(goods_infos.get(j).get("goods_count")));//商品数量
							Double wholesale_price1 = Double.valueOf(MUtil.strObject(goods_infos.get(j).get("wholesale_price")));//销售价
							if(MUtil.strObject(goods_infos.get(j).get("goods_id")).equals(goods_id)){
								total_goods_count = total_goods_count + goods_count1;
								fact_price = fact_price + (wholesale_price1*goods_count1);
							}
						}
						String promtion_code = null;
						Integer count_beans = null;
						Integer total_bean = null;
						if(!beans_goods_give_id.equals("0")){
							Map<String ,Object> beansGoodsGiveInfo = supplierInfoDao.getBeansGoodsGiveInfo(beans_goods_give_id);
							if(beansGoodsGiveInfo == null){
								beans_goods_give_id = null;
							}else{
								Integer counts = Integer.parseInt(MUtil.strObject(beansGoodsGiveInfo.get("counts")));
								Integer residue_count = 0;
								if(counts >= total_goods_count){
									total_bean = goods_beans_count*total_goods_count;
									
									//计算满足规则赠送百货豆
									present_beans = present_beans + present_beans_count*total_goods_count;
									//实际赠送的百货豆
									platform_beans = platform_beans + goods_beans_count*total_goods_count;
									
									residue_count = total_goods_count;
								}else{
									total_bean = goods_beans_count*(counts);
									
									//计算满足规则赠送百货豆
									present_beans = present_beans + present_beans_count*(counts);
									//实际赠送的百货豆
									platform_beans = platform_beans + goods_beans_count*(counts);
									
									residue_count = counts;
								}
								count_beans = goods_beans_count;
								promtion_code = MUtil.strObject(beansGoodsGiveInfo.get("promtion_code"));
								
								//修改商品百货豆规则已经补贴数量
								Map<String ,Object> residueCountParams = new HashMap<String, Object>();
								residueCountParams.put("residue_count", residue_count);
								residueCountParams.put("id", beans_goods_give_id);
								supplierInfoDao.updateResidueCount(residueCountParams);
								//修改商品百货豆促销活动使用数量
								Map<String ,Object> useCountParams = new HashMap<String, Object>();
								useCountParams.put("use_count", residue_count*present_beans_count);
								useCountParams.put("promtion_code", promtion_code);
								supplierInfoDao.updateUseCount(useCountParams);
							}
						}
						paramsOrderdetail.put("goods_count", total_goods_count);
						paramsOrderdetail.put("fact_price", fact_price);
						paramsOrderdetail.put("goods_beans_count", total_bean);
						paramsOrderdetail.put("count_beans", count_beans);
						paramsOrderdetail.put("promtion_code", promtion_code);
						paramsOrderdetail.put("beans_goods_give_id", beans_goods_give_id);
						supplierInfoDao.insertOrderDetail(paramsOrderdetail);
						Integer orderdetail_id = Integer.parseInt(MUtil.strObject(paramsOrderdetail.get("orderdetail_id")));
						for(int j=0;j<goods_infos.size();j++){
							if(MUtil.strObject(goods_infos.get(j).get("goods_id")).equals(goods_id)){
								String goods_spec_name = MUtil.strObject(goods_infos.get(j).get("goods_spec_name"));//商品规则名称
								String compose_specs_id = MUtil.strObject(goods_infos.get(j).get("compose_specs_id"));//规格组合id
								Integer goods_count1 = Integer.parseInt(MUtil.strObject(goods_infos.get(j).get("goods_count")));//商品数量
								//添加订单详情规则表
								Map<String ,Object> paramsOrderSpec = new HashMap<String, Object>();
								paramsOrderSpec.put("spec_name", goods_spec_name);
								paramsOrderSpec.put("orderdetail_id", orderdetail_id);
								if(!compose_specs_id.equals("")){
									paramsOrderSpec.put("compose_specs_id", compose_specs_id);
								}
								supplierInfoDao.insertOrderGoodsSpec(paramsOrderSpec);
								Integer sup_order_goodsSpecs_id = Integer.parseInt(MUtil.strObject(paramsOrderSpec.get("sup_order_goodsSpecs_id")));
								//获取该商品规格所在仓库信息
								Map<String ,Object> paramsStock = new HashMap<String, Object>();
								paramsStock.put("goods_code", MUtil.strObject(goods_infos.get(j).get("goods_id")));
								paramsStock.put("company_code", company_code);
								if(!compose_specs_id.equals("")){
									paramsStock.put("compose_specs_id", compose_specs_id);
								}
								List<Map<String ,Object>> goodsstockList = supplierInfoDao.getGoodsstockList(paramsStock);
								for(int n=0;n<goodsstockList.size();n++){
									Integer count = Integer.parseInt(MUtil.strObject(goodsstockList.get(n).get("goods_count")));
									String goodsstock_id = MUtil.strObject(goodsstockList.get(n).get("goodsstock_id"));
									String depot_name = MUtil.strObject(goodsstockList.get(n).get("depot_name"));
									String shelves_name = MUtil.strObject(goodsstockList.get(n).get("shelves_name"));
									if(count >= goods_count1){//库存足
										//修改商品规格所在仓库库存信息
										Map<String ,Object> paramsStockCount = new HashMap<String, Object>();
										paramsStockCount.put("goods_count", goods_count1);
										paramsStockCount.put("goodsstock_id", goodsstock_id);
										supplierInfoDao.updateGoodsStockCount(paramsStockCount);
										//添加订单库存表
										Map<String ,Object> paramsOrderStock = new HashMap<String, Object>();
										paramsOrderStock.put("sup_order_goodsSpecs_id", sup_order_goodsSpecs_id);
										paramsOrderStock.put("goodsstock_id", goodsstock_id);
										paramsOrderStock.put("goods_count", goods_count1);
										paramsOrderStock.put("depot_name", depot_name);
										paramsOrderStock.put("shelves_name", shelves_name);
										supplierInfoDao.insertOrderGoodsStock(paramsOrderStock);
										//跳出循环
										break;
									}else{//库存不足
										//修改商品规格所在仓库库存信息
										Map<String ,Object> paramsStockCount = new HashMap<String, Object>();
										paramsStockCount.put("goods_count", count);
										paramsStockCount.put("goodsstock_id", goodsstock_id);
										supplierInfoDao.updateGoodsStockCount(paramsStockCount);
										//添加订单库存表
										Map<String ,Object> paramsOrderStock = new HashMap<String, Object>();
										paramsOrderStock.put("sup_order_goodsSpecs_id", sup_order_goodsSpecs_id);
										paramsOrderStock.put("goodsstock_id", goodsstock_id);
										paramsOrderStock.put("goods_count", count);
										paramsOrderStock.put("depot_name", depot_name);
										paramsOrderStock.put("shelves_name", shelves_name);
										supplierInfoDao.insertOrderGoodsStock(paramsOrderStock);
										
										goods_count1 = goods_count1-count;
									}
								}
							}
						}
						//获取店铺该商品信息
						Map<String ,Object> paramsGoods = new HashMap<String, Object>();
						paramsGoods.put("shop_unique", shop_unique);
						paramsGoods.put("goods_barcode", goods_barcode);
						Map<String ,Object> goodsInfo = goodsDao.queryGoodsDetail(paramsGoods);
						if(goodsInfo != null){
							//修改商品已采购待入库数量
							paramsGoods.put("goods_count", total_goods_count);
							goodsDao.updateStayStockCount(paramsGoods);
						}else{
							//添加商品信息
							paramsGoods.put("goods_name", goods_name);
							paramsGoods.put("goods_sale_price", -1.00);//负数未定价
							paramsGoods.put("goods_web_sale_price", -1.00);//负数未定价
							paramsGoods.put("goods_in_price", wholesale_price);//商家进价
							paramsGoods.put("shelf_state", "2");//线上上架状态：2、未上架；1、未上架
							paramsGoods.put("pc_shelf_state", "2");//线下上架状态:2、未上架；1、已上架
							if(base_barcode.length() < 24){
								paramsGoods.put("foreignKey", base_barcode);
							}else{
								paramsGoods.put("foreignKey", goods_barcode);
							}
							paramsGoods.put("goods_contain", proportion_num);
							paramsGoods.put("goods_kind_unique", goodsDao.getGoodsKindUnique(shop_unique));
							paramsGoods.put("stay_stock_count", total_goods_count);
							goodsDao.addNewGoods(paramsGoods);
						}
					}
					
					paramsOrder.put("present_beans", present_beans);//满足规则赠送百货豆
					paramsOrder.put("platform_beans", platform_beans);//实际赠送的百货豆
					supplierInfoDao.insertOrder(paramsOrder);
				}
			}
			result.setStatus(1);
			result.setMsg("成功");
		} catch (Exception e) {
			e.printStackTrace();
			result.setStatus(0);
			result.setMsg("异常");
		}
		
		return result;
	}
    
    //创建订单编号
    public static String createOrderNum(){
    	SimpleDateFormat df = new SimpleDateFormat("yyyyMMddHHmmss");//设置日期格式
    	String datestr = df.format(new Date());
    	String sj_num = ""+(int)(1+Math.random()*9)+(int)(1+Math.random()*9)+(int)(1+Math.random()*9)+(int)(1+Math.random()*9);
    	String order_num = "DD" + datestr + sj_num;
    	return order_num;
    }
    
    //根据goods_id去重
    public  static List<Map<String ,Object>> removedup(List<Map<String ,Object>> list){
        Map<String,Map<String ,Object>> hash = new HashMap<String,Map<String,Object>>();
        // 通过hashMap的key不能重复，达到去重的目的
        for(Map<String ,Object> aa :list){
            hash.put(MUtil.strObject(aa.get("goods_id")),aa);
        }
        List<Map<String ,Object>>  newList = new ArrayList<Map<String ,Object>>()  ;
        for(Map.Entry<String,Map<String ,Object>> set : hash.entrySet()){
            newList.add(set.getValue());
        }
        return  newList;
    }

	@Override
	public PurResult querySchoolContentList() {
		PurResult rs=new PurResult(1, "");
		Map<String ,Object> map=new HashMap<>();
		map.put("type", 1);
		List<Map<String ,Object>>  newList =supplierInfoDao.querySchoolContentList(map);
		map.put("type", 2);
		List<Map<String ,Object>>  newList2 =supplierInfoDao.querySchoolContentList(map);
		rs.setData(newList);
		rs.setRows(newList2);
		return rs;
	}

	@Override
	public Map<String, Object> querySchoolContent(String id) {
		Map<String ,Object> map=new HashMap<>();
		map.put("id", id);
		Map<String ,Object>  newList =supplierInfoDao.querySchoolContent(map);
		return newList;
	}
}
