package org.haier.shop.service;

import java.io.File;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSON;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSONObject;
import org.haier.shop.dao.PayTypeDao;
import org.haier.shop.entity.ExcelTitle;
import org.haier.shop.entity.ShopPayMsg;
import org.haier.shop.entity.publicEntity.PageSearch;
import org.haier.shop.oss.OSSUtil;
import org.haier.shop.params.SetSubAccountParams;
import org.haier.shop.util.ExcelUtil;
import org.haier.shop.util.ShopsResult;
import org.haier.shop.util.UtilForJAVA;
import org.haier.shop.util.wxPay.HttpUtil;
import org.haier.shop.util.wxPay.PayConfigUtil;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;

import cn.hutool.core.lang.UUID;


@Service
@Transactional
public class PayTypeServiceImpl implements PayTypeService{
	@Resource
	private PayTypeDao payTypeDao;

	/**
	 * 查询需要下载的数据信息
	 * @param shopMsg
	 * @param examineStatus
	 * @param startTime
	 * @param endTime
	 * @return
	 */
	public void downloadYiTongShopMsgData(String shopMsg,Integer examineStatus,String startTime,String endTime,HttpServletRequest request,HttpServletResponse response){
		Map<String,Object> map = new HashMap<String,Object>();
		if(null != shopMsg && !shopMsg.trim().equals("") && !shopMsg.equals("undefined")) {
			map.put("shopMsg", "%"+shopMsg+"%");
		}
		map.put("examineStatus", examineStatus);
		map.put("startTime", startTime);
		map.put("endTime", endTime);
		PageSearch pageSearch = new PageSearch();
		List<Map<String,Object>> list = payTypeDao.queryShopPayMsgByPage(map, pageSearch);
		List<String> downPaths = new ArrayList<>();
		
		//将信息保存到excel表格中
		List<ExcelTitle> tls = new ArrayList<ExcelTitle>();
		ExcelTitle t1 = new ExcelTitle();
		ExcelTitle t2 = new ExcelTitle();
		ExcelTitle t3 = new ExcelTitle();
		ExcelTitle t4 = new ExcelTitle();
		ExcelTitle t5 = new ExcelTitle();
		ExcelTitle t6 = new ExcelTitle();
		ExcelTitle t7 = new ExcelTitle();
		t1.setKey("shopUnique");t1.setValue("店铺编号");
		t2.setKey("shopName");t2.setValue("店铺名称");
		t3.setKey("shopAddress");t3.setValue("店铺地址");
		t4.setKey("legalPerson");t4.setValue("法人");
		t5.setKey("userPhone");t5.setValue("手机号");
		t6.setKey("email");t6.setValue("邮箱");
		t7.setKey("subBranch");t7.setValue("支行信息");
		tls.add(t1);
		tls.add(t2);
		tls.add(t3);
		tls.add(t4);
		tls.add(t5);
		tls.add(t6);
		tls.add(t7);
		String abpath = request.getSession().getServletContext().getRealPath(File.separator);
		String excelPath = File.separator+"downLoad";
		Calendar c = Calendar.getInstance();
		String fileName = c.get(Calendar.YEAR)+"-"+(c.get(Calendar.MONTH)+1)+"-"+c.get(Calendar.DAY_OF_MONTH)+".xls";
		ExcelUtil.buildExcel(tls, abpath + excelPath, fileName, list);
		downPaths.add(abpath+excelPath+File.separator+fileName);
		//下载图片信息
		String downloadUrl = "http://download.buyhoo.cc";
		String downloadSavepath = abpath+excelPath;
		for(Integer i = 0;i<list.size() ;i ++) {
			String fname = "license";
			UtilForJAVA.downloadFormUrl(downloadSavepath, 
					list.get(i).get("shopName").toString()+fname+list.get(i).get(fname).toString().substring(list.get(i).get(fname).toString().lastIndexOf(".")),
					downloadUrl+list.get(i).get(fname).toString());
			downPaths.add(downloadSavepath+File.separator+list.get(i).get("shopName").toString()+fname+list.get(i).get(fname).toString().substring(list.get(i).get(fname).toString().lastIndexOf(".")));
			fname = "doorPhoto";
			UtilForJAVA.downloadFormUrl(downloadSavepath, 
					list.get(i).get("shopName").toString()+fname+list.get(i).get(fname).toString().substring(list.get(i).get(fname).toString().lastIndexOf(".")),
					downloadUrl+list.get(i).get(fname).toString());
			downPaths.add(downloadSavepath+File.separator+list.get(i).get("shopName").toString()+fname+list.get(i).get(fname).toString().substring(list.get(i).get(fname).toString().lastIndexOf(".")));
			fname = "identityFront";
			UtilForJAVA.downloadFormUrl(downloadSavepath, 
					list.get(i).get("shopName").toString()+fname+list.get(i).get(fname).toString().substring(list.get(i).get(fname).toString().lastIndexOf(".")),
					downloadUrl+list.get(i).get(fname).toString());
			downPaths.add(downloadSavepath+File.separator+list.get(i).get("shopName").toString()+fname+list.get(i).get(fname).toString().substring(list.get(i).get(fname).toString().lastIndexOf(".")));
			fname = "identityBlack";
			UtilForJAVA.downloadFormUrl(downloadSavepath, 
					list.get(i).get("shopName").toString()+fname+list.get(i).get(fname).toString().substring(list.get(i).get(fname).toString().lastIndexOf(".")),
					downloadUrl+list.get(i).get(fname).toString());
			downPaths.add(downloadSavepath+File.separator+list.get(i).get("shopName").toString()+fname+list.get(i).get(fname).toString().substring(list.get(i).get(fname).toString().lastIndexOf(".")));
			fname = "bankCardFront";
			UtilForJAVA.downloadFormUrl(downloadSavepath, 
					list.get(i).get("shopName").toString()+fname+list.get(i).get(fname).toString().substring(list.get(i).get(fname).toString().lastIndexOf(".")),
					downloadUrl+list.get(i).get(fname).toString());
			downPaths.add(downloadSavepath+File.separator+list.get(i).get("shopName").toString()+fname+list.get(i).get(fname).toString().substring(list.get(i).get(fname).toString().lastIndexOf(".")));
			fname = "bankCardBlack";
			UtilForJAVA.downloadFormUrl(downloadSavepath, 
					list.get(i).get("shopName").toString()+fname+list.get(i).get(fname).toString().substring(list.get(i).get(fname).toString().lastIndexOf(".")),
					downloadUrl+list.get(i).get("license").toString());
			downPaths.add(downloadSavepath+File.separator+list.get(i).get("shopName").toString()+fname+list.get(i).get(fname).toString().substring(list.get(i).get(fname).toString().lastIndexOf(".")));
		}
		
		//添加excel
		
		//打包
		try {
			String zipFile = downloadSavepath+File.separator+"shopPayMsg.zip";
			File ff = new File(zipFile);
			if(!ff.exists()) {
				ff.createNewFile();
			}
			UtilForJAVA.zip(zipFile, downPaths);
			
			//下载
			
			UtilForJAVA.download(new File(zipFile),request,response);
		} catch (Exception e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		}
	}
	
	/**
	 * 修改支付状态
	 * @param id
	 * @param status
	 * @param examineStatus
	 * @return
	 */
	public ShopsResult modifyShopPayMsg(Integer id, Integer examineStatus , String examineRemarks) {
		ShopsResult sr = new ShopsResult(1, "保存成功！");
		ShopPayMsg shopPayMsg = new ShopPayMsg();
		shopPayMsg.setId(id);
		shopPayMsg.setExamineStatus(examineStatus+"");
		shopPayMsg.setExamineRemarks(examineRemarks);
		if(payTypeDao.modifyShopPayMsg(shopPayMsg) == 0) {
			sr.setStatus(0);
			sr.setMsg("修改失败！");
		}
		return sr;
	}
	
	/**
	 * 获取店铺支付信息提交审核的所有审核状态
	 * @return
	 */
	public ShopsResult queryShopPayMsgExamineStatus() {
		ShopsResult sr = new ShopsResult(1, "查询成功！");
		sr.setData(payTypeDao.queryShopPayMsgExamineStatus());
		return sr;
	}
	/**
	 * 分页查询已有的审核资料信息
	 * @param shopUnique
	 * @return
	 */
	public ShopsResult queryShopPayMsgByPage(Map<String,Object> map ,PageSearch pageSearch) {
		ShopsResult sr = new ShopsResult(1,"查询成功！");
		List<Map<String,Object>> list = payTypeDao.queryShopPayMsgByPage(map, pageSearch);
		sr.setData(list);
		sr.setCount(payTypeDao.queryShopPayMsgCount(map));
		return sr;
	}
	/**
	 * 查询当前易通支付信息
	 * @return
	 */
	public ShopsResult queryShopYiTongPayMsg(String shopUnique) {
		ShopsResult sr = new ShopsResult(1, "查询成功！");
		sr.setData(payTypeDao.queryShopYiTongPayMsg(shopUnique));
		return sr;
	}
	/**
	 * 获取当前店铺的有效支付信息
	 * @param shopUnique
	 * @return
	 */
	public ShopsResult getShopPayMsg(String shopUnique) {
		ShopsResult sr = new ShopsResult(1,"查询成功！");
		ShopPayMsg payMsg = new ShopPayMsg();
		payMsg.setShopUnique(shopUnique);
		payMsg.setValidType("1");
		ShopPayMsg shopPayMsg = payTypeDao.getShopPayMsg(payMsg);
		sr.setData(shopPayMsg);
		return sr;
	}
	
	public ShopsResult addNewShopPayMsg(HttpServletRequest request,
			String shopUnique,
			String legalPerson,
			String userPhone,
			Integer id,
			String shopAddress,
			String email,
			String subBranch) {
		ShopsResult sr = new ShopsResult(1,"保存成功");
		Map<String, MultipartFile> mp= null;
		if( request instanceof MultipartHttpServletRequest){
			MultipartHttpServletRequest multipartHttpServletRequest =(MultipartHttpServletRequest)request;
			mp = multipartHttpServletRequest.getFileMap();
		}
		
		//给出图片保存路径和图片文件信息，变量名信息，将图片保存并返回保存结果
		ShopPayMsg shopPayMsg = new ShopPayMsg();
		shopPayMsg.setShopUnique(shopUnique);
		shopPayMsg.setLegalPerson(legalPerson);
		shopPayMsg.setUserPhone(userPhone);
		shopPayMsg.setShopAddress(shopAddress);
		shopPayMsg.setEmail(email);
		shopPayMsg.setSubBranch(subBranch);
//		String abpath = request.getSession().getServletContext().getRealPath(File.separator);
//		abpath = new File(abpath).getParent();
		String abpath = "/mnt/tomcat/imageBackups";//图片本地备份路径
		String path=File.separator+"images"+File.separator+"shopPayMsg"+File.separator+shopUnique+File.separator;
		String bucketName = "download-buyhoo";
		String p = "images/shopPayMsg/"+shopUnique+"/";
		if(null != mp) {
			shopPayMsg.setLicense(OSSUtil.savePic(bucketName, mp.get("license"), p));
			shopPayMsg.setDoorPhoto(OSSUtil.savePic(bucketName, mp.get("doorPhoto"), p));
			shopPayMsg.setIdentityBlack(OSSUtil.savePic(bucketName, mp.get("identityBlack"), p));
			shopPayMsg.setIdentityFront(OSSUtil.savePic(bucketName, mp.get("identityFront"), p));
			shopPayMsg.setBankCardBlack(OSSUtil.savePic(bucketName, mp.get("bankCardBlack"), p));
			shopPayMsg.setBankCardFront(OSSUtil.savePic(bucketName, mp.get("bankCardFront"), p));
			
			savePic(abpath,path,"license",mp);
			savePic(abpath,path,"doorPhoto",mp);
			savePic(abpath,path,"identityBlack",mp);
			savePic(abpath,path,"identityFront",mp);
			savePic(abpath,path,"bankCardBlack",mp);
			savePic(abpath,path,"bankCardFront",mp);
			
			
//			shopPayMsg.setLicense(savePic(abpath, path, "license", mp));
//			shopPayMsg.setDoorPhoto(savePic(abpath, path, "doorPhoto", mp));
//			shopPayMsg.setIdentityBlack(savePic(abpath, path, "identityBlack", mp));
//			shopPayMsg.setIdentityFront(savePic(abpath, path, "identityFront", mp));
//			shopPayMsg.setBankCardBlack(savePic(abpath, path, "bankCardBlack", mp));
//			shopPayMsg.setBankCardFront(savePic(abpath, path, "bankCardFront", mp));
		}
		
		if(shopPayMsg.getLicense() == null || shopPayMsg.getDoorPhoto() == null || shopPayMsg.getIdentityBlack() == null 
				|| shopPayMsg.getIdentityFront() == null || shopPayMsg.getBankCardBlack() == null || shopPayMsg.getBankCardFront() == null) {
			ShopPayMsg payMsg = new ShopPayMsg();
			payMsg.setShopUnique(shopUnique);
			payMsg.setValidType("1");
			ShopPayMsg shopPayMsgold = payTypeDao.getShopPayMsg(payMsg);
			if(null != shopPayMsgold) {
				if(shopPayMsg.getLicense() == null) {
					shopPayMsg.setLicense(shopPayMsgold.getLicense());
				}
				
				if(shopPayMsg.getDoorPhoto() == null) {
					shopPayMsg.setDoorPhoto(shopPayMsgold.getDoorPhoto());
				}
				
				if(shopPayMsg.getIdentityBlack() == null) {
					shopPayMsg.setIdentityBlack(shopPayMsgold.getIdentityBlack());
				}
				
				if(shopPayMsg.getIdentityFront() == null) {
					shopPayMsg.setIdentityFront(shopPayMsgold.getBankCardFront());
				}
				
				if(shopPayMsg.getBankCardBlack() == null) {
					shopPayMsg.setBankCardBlack(shopPayMsgold.getBankCardBlack());
				}
				
				if(shopPayMsg.getBankCardFront() == null) {
					shopPayMsg.setBankCardFront(shopPayMsgold.getBankCardFront());
				}
			}
		}
		if(id != null) {
			shopPayMsg.setExamineStatus("3");
		}
		payTypeDao.addNewShopPayMsg(shopPayMsg);
		sr.setData(shopPayMsg);
		return sr;
	}
	
	
	/**
	 * 
	 * @param request
	 * @param path
	 * @param picMap
	 * @return
	 */
	public static String savePic(String abpath,String path,String parName,Map<String,MultipartFile> picMap) {
		File f = new File(abpath + path);
		if(!f.exists()) {//若文件路径信息不存在，创建文件保存文件夹
			f.mkdirs();
		}
		MultipartFile file = picMap.get(parName);
		
		if(null == file) {
			return null;
		}
		Set<String> keySet = picMap.keySet();
		for(String s :keySet) {
			System.out.println(s);
		}
		String fileName = UUID.randomUUID().toString()+file.getOriginalFilename().substring(file.getOriginalFilename().lastIndexOf("."));
		File saveFile = new File(abpath+path+fileName);
		try {
			if(!saveFile.exists()) {
				saveFile.createNewFile();
			}
			file.transferTo(saveFile);
		}catch (Exception e) {
			e.printStackTrace();
			return null;
		}
		return path+fileName;
	}
	
	/**
	 * 查询店铺内所有的支付方式
	 * @param map
	 * @return
	 */
	public ShopsResult getPayType(Map<String,Object> map) {
		ShopsResult result = new  ShopsResult(1,"查询成功！");
		
		if(map.get("shopUnique")==null) {
			result.setStatus(0);
			result.setMsg("店铺编号未上传");
			return result;
		}
		
		List<Map<String,Object>> list = payTypeDao.queryPayType(map);
		result.setData(list);
		
		return result;
	}
	
	/**
	 * 更新店铺的支付信息
	 * @param map
	 * @return
	 */
	public ShopsResult modiyfPayType(Map<String,Object> map) {
		ShopsResult result = new  ShopsResult(1,"更新成功！");
		if((null != map.get("defaultType") && map.get("defaultType").toString().equals("1")) ||
				( null != map.get("appDefaultType") &&  map.get("appDefaultType").toString().equals("1"))) {//如果修改默认支付方式，则需要将其他支付方式设置为非默认
			Map<String,Object> oldMap = new HashMap<String, Object>();
			
			oldMap.put("noId", map.get("id"));
			oldMap.put("shopUnique", map.get("shopUnique"));
			if(map.get("defaultType") != null && map.get("defaultType").toString().equals("1")) {
				oldMap.put("defaultType", "2");
				//将所有的线下支付该为非默认
				payTypeDao.modiyfPayType(oldMap);
			}
			if(map.get("appDefaultType") != null && map.get("appDefaultType").toString().equals("1")) {
				oldMap.put("appDefaultType", "2");
				//将所有的线下支付该为非默认
				payTypeDao.modiyfPayType(oldMap);
			}
		}
		
		payTypeDao.modiyfPayType(map);
		return result;
	}
	
	/**
	 * 查询平台所有支付方式
	 * @param map
	 * @return
	 */
	public ShopsResult queryPlatPayType(Map<String,Object> map) {
		ShopsResult result = new  ShopsResult(1,"查询成功！");
		List<Map<String,Object>> list = payTypeDao.queryPlatPayType(map);
		result.setData(list);
		return result;
	}
	
	/**
	 * 修改平台支付方式信息
	 * @param map
	 * @return
	 */
	public ShopsResult modifyPlatPayType(Map<String,Object> map) {
		ShopsResult shopsResult = new ShopsResult(1,"修改成功");
		Integer c = payTypeDao.modifyPlatPayType(map);
		if(c == 0) {
			shopsResult.setStatus(0);
			shopsResult.setMsg("更新失败");
		}
		return shopsResult;
	}
	
	/**
	 * 查询该支付方式所有店铺列表
	 * @param pay_type 支付方式
	 * @param shop_message 店铺名称/店铺编号
	 * @return
	 */
	@Override
	public ShopsResult queryPayTypeShopList(Map<String, Object> map) {
		ShopsResult result = new  ShopsResult(1,"查询成功！");
		List<Map<String,Object>> list = payTypeDao.queryPayTypeShopList(map);
		Integer count = payTypeDao.queryPayTypeShopListCount(map);
		result.setData(list);
		result.setCount(count);
		return result;
	}

	/**
	 * 添加店铺支付方式
	 * @return
	 */
	@Override
	public ShopsResult addShopPayType(Map<String, Object> map) {
		ShopsResult shopsResult = new ShopsResult(1,"成功");
		Map<String,Object> oldMap = new HashMap<String,Object>();
		oldMap.put("shopUnique", map.get("shopUnique"));
		//此处需要防止重复添加
		oldMap.put("payType", map.get("payType"));
		
		Integer haveCount = payTypeDao.queryPayTypeHave(oldMap);
		if(haveCount>0) {
			shopsResult.setStatus(0);
			shopsResult.setMsg("该支付类型已添加，请勿重复添加");
			return shopsResult;
		}
		if((null != map.get("defaultType") && map.get("defaultType").toString().equals("1")) ||( null != map.get("appDefaultType") &&  map.get("appDefaultType").toString().equals("1"))) {//如果修改默认支付方式，则需要将其他支付方式设置为非默认
			if(map.get("defaultType") != null && map.get("defaultType").toString().equals("1")) {
				oldMap.put("defaultType", "2");
				//将所有的线下支付该为非默认
				payTypeDao.modiyfPayType(oldMap);
			}
			if(map.get("appDefaultType") != null && map.get("appDefaultType").toString().equals("1")) {
				oldMap.put("appDefaultType", "2");
				//将所有的线下支付该为非默认
				payTypeDao.modiyfPayType(oldMap);
			}
		}
		Integer c = payTypeDao.addShopPayType(map);
		if(c == 0) {
			shopsResult.setStatus(0);
			shopsResult.setMsg("失败");
		}
		return shopsResult;
	}

	/**
	 * 获取店铺支付方式详情
	 * @param shop_pay_type_id
	 * @return
	 */
	@Override
	public Map<String ,Object> queryShopPayType(String shop_pay_type_id) {
        return new HashMap<>(payTypeDao.queryShopPayType(shop_pay_type_id));
	}

	/**
	 * 更新店铺的支付信息
	 * @param id
	 * @param mchId
	 * @param mchKey
	 * @param defaultType
	 * @param shopUnique
	 * @param payType
	 * @return
	 */
	public ShopsResult saveShopPayType(String id,String mchId,String mchKey,String defaultType,String shopUnique,String payType) {
		ShopsResult sr = new ShopsResult(1, "保存成功！");
		Map<String,Object> oldMap = new HashMap<String,Object>();
		oldMap.put("shopUnique", shopUnique);
		//此处需要防止重复添加
		oldMap.put("payTay", payType);
		
		if(id == null) {//如果是新增，先验证是否已有，防止重复添加
			Integer haveCount = payTypeDao.queryPayTypeHave(oldMap);
			if(haveCount>0) {
				sr.setStatus(0);
				sr.setMsg("该支付类型已添加，请勿重复添加");
				return sr;
			}
		}
		
		if(defaultType.equals("1")) {//如果是默认，先将其他支付信息设置为非默认
			oldMap.put("defaultType", "2");
			oldMap.put("noId", id);
			//将所有的线下支付该为非默认
			payTypeDao.modiyfPayType(oldMap);
		}
		Map<String,Object> map = new HashMap<String,Object>();
		map.put("shopUnique", shopUnique);
		map.put("id", id);
		map.put("mchId", mchId);
		map.put("mchKey", mchKey);
		map.put("defaultType", defaultType);
		map.put("payType", payType);
		if(id == null || id == "") {
			//添加支付方式信息
			map.put("otherSet", "wx3e36062de3f5d729");
			map.put("rate", "38");
			map.put("appValidType", "2");
			map.put("appDefaultType", "2");
			map.put("appRate", "300");
			payTypeDao.addShopPayType(map);
		}else {
			//更新原有支付方式
			payTypeDao.modiyfPayType(map);
		}
		return sr;
	}

	@Override
	public void setSubAccount(SetSubAccountParams params) {
		payTypeDao.setSubAccountByShopUnique(params);
	}

}
