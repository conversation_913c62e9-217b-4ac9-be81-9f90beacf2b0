package org.haier.shop.service;


import org.haier.shop.entity.ListPrompt;
import org.haier.shop.util.ShopsResult;

public interface ListPromptService {
	/**
	 * 查询有效的订单数量提示信息
	 * @return
	 */
	public ShopsResult getValidPromptRule();
	
	/**
	 * 将已有规则设置为无效状态
	 * @return
	 */
	public ShopsResult deleteListPrompt(ListPrompt listPrompt);
	
	/**
	 * 添加或更新规则信息
	 * @param listPrompt
	 * @return
	 */
	public ShopsResult modifyListPrompt(ListPrompt listPrompt);
	
	public ShopsResult clearListPrompt();
}
