package org.haier.shop.service;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import org.haier.shop.dao.ShopsConfigDao;
import org.haier.shop.entity.ShopsConfig;
import org.haier.shop.result.customer.SetMemberDaySetParams;
import org.haier.shop.util.PurResult;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * @Description 店铺设置
 * @ClassName ShopsConfigService
 * <AUTHOR>
 * @Date 2024/5/9 10:29
 **/
@Service
public class ShopsConfigServiceImpl implements ShopsConfigService {

    @Resource
    private ShopsConfigDao shopsConfigDao;

    public ShopsConfig selectByShopUnique(String shopUnique) {
        ShopsConfig shopsConfig = shopsConfigDao.selectByShopUnique(shopUnique);
        return shopsConfig;
    }

    /**
     * 设置会员日信息
     * @param params
     * @return
     */
    public PurResult setMemberDaySet(SetMemberDaySetParams params) {
        //先确定店铺有没有配置表
        ShopsConfig shopsConfig = shopsConfigDao.selectByShopUnique(params.getShopUnique());
        ShopsConfig shopsConfigNew = new ShopsConfig();
        BeanUtil.copyProperties(params, shopsConfigNew);
        shopsConfigNew.setShopUnique(params.getShopUnique());
        if (ObjectUtil.isNull(shopsConfig)) {
            //没有，新建
            shopsConfigDao.addNewShopsConfig(shopsConfigNew);
        } else {
            //有，更新
            shopsConfigDao.updateShopsConfig(shopsConfigNew);
        }

        return PurResult.ok();
    }
}
