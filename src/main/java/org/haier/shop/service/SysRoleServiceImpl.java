package org.haier.shop.service;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.UUID;

import org.haier.shop.dao.SysMenuDao;
import org.haier.shop.dao.SysRoleDao;
import org.haier.shop.entity.SysAction;
import org.haier.shop.entity.SysActionRole;
import org.haier.shop.entity.SysPermission;
import org.haier.shop.entity.SysRole;
import org.haier.shop.entity.SysRolePermission;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Service
public class SysRoleServiceImpl implements SysRoleService{
	
	@Autowired
    private SysRoleDao roleDao;
	
	@Autowired
    private SysMenuDao menuDao;
	
	@Override
	public List<SysRole> quertRoleList(Map<String ,Object> params) {
		return roleDao.quertRoleList(params);
	}
	
	@Override
	public int quertRoleListCount(Map<String ,Object> params) {
		return roleDao.quertRoleListCount(params);
	}
	
	@Override
	@Transactional
	public void insert(SysRole role) {
		role.setCode(UUID.randomUUID().toString().replaceAll("-", ""));
		role.setCreate_code("");
		role.setUpdate_code("");
		role.setDel_flag(0);
		roleDao.insert(role);
	}

	@Override
	@Transactional
	public void update(SysRole role) {
		role.setUpdate_code("");
		roleDao.update(role);
	}

	@Override
	public SysRole getRole(SysRole role) {
		return roleDao.getRole(role);
	}

	@Override
	@Transactional
	public void delete(String code) {
		//逻辑删除
		SysRole role = new SysRole();
		role.setCode(code);
		role.setDel_flag(1);
		roleDao.update(role);
	}

	@Override
	public List<SysPermission> getMenuListByRoleCode(Map<String, Object> params) {
		return roleDao.getMenuListByRoleCode(params);
	}

	@Override
	public List<SysAction> getActionListByRoleCode(Map<String, Object> params) {
		return roleDao.getActionListByRoleCode(params);
	}
	
	/**       
	* 保存角色授权 
	* @param params
	* @param rowBounds
	*/
	@Override
	@Transactional
	public void sitesRoleAuth(Map<String ,Object> params) {
		String codes = (String) params.get("actionCodes");//权限code
		String shop_unique = (String) params.get("shop_unique");//权限code
		String[] actionCodes = {};
		if(codes != null && !codes.equals("")){
			actionCodes = codes.split(";");
		}
		String roleCode = (String) params.get("roleCode");//角色code
		List<SysRolePermission> rolePermissionlist = new ArrayList<SysRolePermission>();//菜单列表
		List<SysActionRole> actionRolelist = new ArrayList<SysActionRole>();//操作权限列表
		for(int i=0;i<actionCodes.length;i++) {
			String[] actions = actionCodes[i].split(",");
			if(actions[0].equals("menu")){
				SysRolePermission rolePermission = new SysRolePermission();
				rolePermission.setRole_code(roleCode);
				rolePermission.setPermission_code(actions[1]);
				rolePermission.setShop_unique(shop_unique);
				rolePermissionlist.add(rolePermission);
			}else{
				SysActionRole actionRole = new SysActionRole();
				actionRole.setAction_code(actions[1]);
				actionRole.setRole_code(roleCode);
				actionRole.setShop_unique(shop_unique);
				actionRolelist.add(actionRole);
			}
		}
		//保存前先删除所有
		roleDao.deleteRolePermission(params);
		roleDao.deleteActionRole(params);
		if(rolePermissionlist.size()>0){
			roleDao.addRolePermission(rolePermissionlist);
		}
		if(actionRolelist.size()>0){
			roleDao.addActionRole(actionRolelist);
		}
	}

	@Override
	@Transactional
	public void sitesRoleAuth1(Map<String ,Object> params) {
		String codes = (String) params.get("actionCodes");//权限code
		String shop_unique = (String) params.get("shop_unique");//权限code
		String[] actionCodes = {};
		if(codes != null && !codes.equals("")){
			actionCodes = codes.split(";");
		}
		String roleCode = (String) params.get("roleCode");//角色code
		List<SysRolePermission> rolePermissionlist = new ArrayList<SysRolePermission>();//菜单列表
		List<SysActionRole> actionRolelist = new ArrayList<SysActionRole>();//操作权限列表
		for(int i=0;i<actionCodes.length;i++) {
			String[] actions = actionCodes[i].split(",");
			if(actions[0].equals("menu")){
				SysRolePermission rolePermission = new SysRolePermission();
				rolePermission.setRole_code(roleCode);
				rolePermission.setPermission_code(actions[1]);
				rolePermission.setShop_unique(shop_unique+"yll");
				rolePermissionlist.add(rolePermission);
			}else{
				SysActionRole actionRole = new SysActionRole();
				actionRole.setAction_code(actions[1]);
				actionRole.setShop_unique(shop_unique+"yll");
				actionRole.setRole_code(roleCode);
				actionRolelist.add(actionRole);
			}
		}
		//保存前先删除所有
		params.put("shop_unique", shop_unique+"yll");
		roleDao.deleteRolePermission(params);
		roleDao.deleteActionRole(params);
		if(rolePermissionlist.size()>0){
			roleDao.addRolePermission(rolePermissionlist);
		}
		if(actionRolelist.size()>0){
			roleDao.addActionRole(actionRolelist);
		}
	}
	
	@Override
	public int getStaffByRoleCodeCount(Map<String, Object> params) {
		return roleDao.getStaffByRoleCodeCount(params);
	}

	@Override
	public Map<String, Object> getRoleByStaffId(Integer staff_id) {
		return roleDao.getRoleByStaffId(staff_id);
	}

	@Override
	public List<SysAction> getActionList(Map<String, Object> params) {
		return roleDao.getActionList(params);
	}
}
