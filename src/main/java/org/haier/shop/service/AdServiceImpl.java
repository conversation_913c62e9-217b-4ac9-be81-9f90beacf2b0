package org.haier.shop.service;

import java.io.File;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;

import org.haier.meituan.util.MUtil;
import org.haier.shop.dao.AdDao;
import org.haier.shop.util.DeleteFileUtil;
import org.haier.shop.util.HandleMessyCode;
import org.haier.shop.util.PurResult;
import org.haier.shop.util.ServletsUtil;
import org.haier.shop.util.ShopsUtil;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

@Service("adService")
@Transactional
public class AdServiceImpl implements AdService{
	
	@Resource
	private AdDao adDao;

	@Override
	public PurResult queryAdList(Map<String, Object> params) {
		PurResult result = new PurResult();
		try {
			List<Map<String ,Object>> list = adDao.queryAdList(params);
			Integer count = adDao.queryAdListCount(params);
			result.setData(list);
			result.setCount(count);
			result.setStatus(1);
			result.setMsg("成功");
		} catch (Exception e) {
			e.printStackTrace();
			result.setStatus(0);
			result.setMsg("异常");
		}
		
		return result;
	}

	@Override
	public PurResult addAd(HttpServletRequest request) {
		PurResult result = new PurResult();
		try {
			Map<String ,Object> params = HandleMessyCode.handle(ServletsUtil.getParameters(request));
			//封面图
			MultipartFile titleFile = ShopsUtil.testMulRequest(request, "title_File");
			if(null != titleFile){//图片信息处理
				String orName = titleFile.getOriginalFilename();//获取文件原名称
				String suffix = orName.substring(orName.lastIndexOf("."));
				String fileName = Math.round(Math.random()*100)+suffix;
				String filePathDetail = File.separator+"image"+File.separator + "sys_advertising";
				String filePath = request.getSession().getServletContext().getRealPath(File.separator);
				filePath = filePath.substring(0, filePath.length()-request.getContextPath().length()) + filePathDetail;
				boolean flag = ShopsUtil.saveFile(titleFile, filePath, fileName);
				if(flag){
					params.put("title_image", filePathDetail+File.separator + fileName);
				}
			}
			adDao.addAd(params);
			String sys_ad_id = MUtil.strObject(params.get("sys_ad_id"));
			//添加广告投放区域
			String ad_areas = MUtil.strObject(params.get("ad_areas"));
			List<Map<String ,Object>> ad_area_list = MUtil.strToList(ad_areas);
			if(ad_area_list.size() > 0) {
				for(int i=0;i<ad_area_list.size();i++) {
					ad_area_list.get(i).put("sys_ad_id", sys_ad_id);
				}
				adDao.addAdAreaList(ad_area_list);
			}
			//添加广告内容文件
			List<Map<String ,Object>> adContentFileList = new ArrayList<Map<String ,Object>>();
			String filePathDetail = File.separator+"image"+File.separator + "sys_advertising"+sys_ad_id;
			String filePath = request.getSession().getServletContext().getRealPath(File.separator);
			filePath = filePath.substring(0, filePath.length()-request.getContextPath().length()) + filePathDetail;
			Integer task_type = Integer.parseInt(MUtil.strObject(params.get("task_type")));
			if(task_type == 1) {//图文
				List<MultipartFile> imgFileList = new ArrayList<MultipartFile>();
				MultipartFile img_file1 = ShopsUtil.testMulRequest(request, "img_file1");
				if(img_file1 != null) {
					imgFileList.add(img_file1);
				}
				MultipartFile img_file2 = ShopsUtil.testMulRequest(request, "img_file2");
				if(img_file2 != null) {
					imgFileList.add(img_file2);
				}
				MultipartFile img_file3 = ShopsUtil.testMulRequest(request, "img_file3");
				if(img_file3 != null) {
					imgFileList.add(img_file3);
				}
				MultipartFile img_file4 = ShopsUtil.testMulRequest(request, "img_file4");
				if(img_file4 != null) {
					imgFileList.add(img_file4);
				}
				MultipartFile img_file5 = ShopsUtil.testMulRequest(request, "img_file5");
				if(img_file5 != null) {
					imgFileList.add(img_file5);
				}
				MultipartFile img_file6 = ShopsUtil.testMulRequest(request, "img_file6");
				if(img_file6 != null) {
					imgFileList.add(img_file6);
				}
				MultipartFile img_file7 = ShopsUtil.testMulRequest(request, "img_file7");
				if(img_file7 != null) {
					imgFileList.add(img_file7);
				}
				MultipartFile img_file8 = ShopsUtil.testMulRequest(request, "img_file8");
				if(img_file8 != null) {
					imgFileList.add(img_file8);
				}
				MultipartFile img_file9 = ShopsUtil.testMulRequest(request, "img_file9");
				if(img_file9 != null) {
					imgFileList.add(img_file9);
				}
				if(imgFileList.size()>0){//图片信息处理
					for(int i=0;i<imgFileList.size();i++) {
						MultipartFile imgFile = imgFileList.get(i);
						String orName = imgFile.getOriginalFilename();//获取文件原名称
						String suffix = orName.substring(orName.lastIndexOf("."));
						String fileName = Math.round(Math.random()*100)+suffix;
						boolean flag = ShopsUtil.saveFile(imgFile, filePath, fileName);
						if(flag){
							Map<String ,Object> adContentFile = new HashMap<String ,Object>();
							adContentFile.put("content_file", filePathDetail+File.separator + fileName);
							adContentFile.put("sys_ad_id", sys_ad_id);
							adContentFileList.add(adContentFile);
						}
					}
				}
			}else {//视频
				MultipartFile videoFile = ShopsUtil.testMulRequest(request, "video_file");
				if(null != videoFile){//图片信息处理
					String orName = videoFile.getOriginalFilename();//获取文件原名称
					String suffix = orName.substring(orName.lastIndexOf("."));
					String fileName = Math.round(Math.random()*100)+suffix;
					boolean flag = ShopsUtil.saveFile(videoFile, filePath, fileName);
					if(flag){
						Map<String ,Object> adContentFile = new HashMap<String ,Object>();
						adContentFile.put("content_file", filePathDetail+File.separator + fileName);
						adContentFile.put("sys_ad_id", sys_ad_id);
						adContentFileList.add(adContentFile);
					}
				}
			}
			if(adContentFileList.size()>0) {
				adDao.addAdContentFileList(adContentFileList);
			}
			
			//添加广告优惠券
			String ad_coupons = MUtil.strObject(params.get("ad_coupons"));
			if(ad_coupons != null && !ad_coupons.equals("")) {
				List<Map<String ,Object>> ad_coupon_list = MUtil.strToList(ad_coupons);
				if(ad_coupon_list.size() > 0) {
					for(int i=0;i<ad_coupon_list.size();i++) {
						ad_coupon_list.get(i).put("sys_ad_id", sys_ad_id);
					}
					adDao.addAdCouponList(ad_coupon_list);
				}
			}
			
			result.setStatus(1);
			result.setMsg("成功");
		} catch (Exception e) {
			e.printStackTrace();
			result.setStatus(0);
			result.setMsg("异常");
		}
		
		return result;
	}

	@Override
	public Map<String, Object> queryAdDetail(String sys_ad_id) {
		Map<String ,Object> adDetail = adDao.queryAdDetail(sys_ad_id);
		// 获取广告投放区域列表
		List<Map<String ,Object>> adAreaList = adDao.queryAdAreaList(sys_ad_id);
		//获取广告投放内容列表
		List<Map<String ,Object>> adContentFileList = adDao.queryAdContentFileList(sys_ad_id);
		// 获取广告优惠券列表
		List<Map<String ,Object>> adCouponList = adDao.queryAdCouponList(sys_ad_id);
		adDetail.put("adAreaList", adAreaList);
		adDetail.put("adContentFileList", adContentFileList);
		adDetail.put("adCouponList", adCouponList);
		return adDetail;
	}

	@Override
	public PurResult updateAd(HttpServletRequest request) {
		PurResult result = new PurResult();
		try {
			Map<String ,Object> params = HandleMessyCode.handle(ServletsUtil.getParameters(request));
			//封面图
			MultipartFile titleFile = ShopsUtil.testMulRequest(request, "title_file");
			if(null != titleFile){//图片信息处理
				//删除原文件
				String filePath = request.getSession().getServletContext().getRealPath(File.separator);
				filePath = filePath.substring(0, filePath.length()-request.getContextPath().length());
				DeleteFileUtil.delete(filePath + params.get("title_image"));
				
				String orName = titleFile.getOriginalFilename();//获取文件原名称
				String suffix = orName.substring(orName.lastIndexOf("."));
				String fileName = Math.round(Math.random()*100)+suffix;
				String filePathDetail = File.separator+"image"+File.separator + "sys_advertising";
				filePath = filePath.substring(0, filePath.length()-request.getContextPath().length()) + filePathDetail;
				boolean flag = ShopsUtil.saveFile(titleFile, filePath, fileName);
				if(flag){
					params.put("title_image", filePathDetail+File.separator + fileName);
				}
			}
			//修改广告基本信息
			if(params.get("is_receive_coupon").equals("on")) {
				params.put("is_receive_coupon", "1");
			}
			if(params.get("is_receive_beans").equals("on")) {
				params.put("is_receive_beans", "1");
			}
			adDao.updateAd(params);
			String sys_ad_id = MUtil.strObject(params.get("sys_ad_id"));
			
			//删除投放区域
			adDao.deleteAdArea(sys_ad_id);
			//添加广告投放区域
			String ad_areas = MUtil.strObject(params.get("ad_areas"));
			List<Map<String ,Object>> ad_area_list = MUtil.strToList(ad_areas);
			if(ad_area_list.size() > 0) {
				for(int i=0;i<ad_area_list.size();i++) {
					ad_area_list.get(i).put("sys_ad_id", sys_ad_id);
				}
				adDao.addAdAreaList(ad_area_list);
			}
			
			//删除需要删除的文件
			String delete_files = MUtil.strObject(params.get("delete_files"));
			if(delete_files != null && !delete_files.equals("")) {
				List<Map<String ,Object>> delete_file_list = MUtil.strToList(delete_files);
				String deleteFilePath = request.getSession().getServletContext().getRealPath(File.separator);
				deleteFilePath = deleteFilePath.substring(0, deleteFilePath.length()-request.getContextPath().length());
				for(int i=0;i<delete_file_list.size();i++) {
					DeleteFileUtil.delete(deleteFilePath+delete_file_list.get(i).get("content_file"));
					adDao.deleteAdContentFile(MUtil.strObject(delete_file_list.get(i).get("sys_ad_content_file_id")));
				} 
			}
			
			//添加广告内容文件
			List<Map<String ,Object>> adContentFileList = new ArrayList<Map<String ,Object>>();
			String filePathDetail = File.separator+"image"+File.separator + "sys_advertising"+sys_ad_id;
			String filePath = request.getSession().getServletContext().getRealPath(File.separator);
			filePath = filePath.substring(0, filePath.length()-request.getContextPath().length()) + filePathDetail;
			Integer task_type = Integer.parseInt(MUtil.strObject(params.get("task_type")));
			if(task_type == 1) {//图文
				List<MultipartFile> imgFileList = new ArrayList<MultipartFile>();
				MultipartFile img_file1 = ShopsUtil.testMulRequest(request, "img_file1");
				if(img_file1 != null) {
					imgFileList.add(img_file1);
				}
				MultipartFile img_file2 = ShopsUtil.testMulRequest(request, "img_file2");
				if(img_file2 != null) {
					imgFileList.add(img_file2);
				}
				MultipartFile img_file3 = ShopsUtil.testMulRequest(request, "img_file3");
				if(img_file3 != null) {
					imgFileList.add(img_file3);
				}
				MultipartFile img_file4 = ShopsUtil.testMulRequest(request, "img_file4");
				if(img_file4 != null) {
					imgFileList.add(img_file4);
				}
				MultipartFile img_file5 = ShopsUtil.testMulRequest(request, "img_file5");
				if(img_file5 != null) {
					imgFileList.add(img_file5);
				}
				MultipartFile img_file6 = ShopsUtil.testMulRequest(request, "img_file6");
				if(img_file6 != null) {
					imgFileList.add(img_file6);
				}
				MultipartFile img_file7 = ShopsUtil.testMulRequest(request, "img_file7");
				if(img_file7 != null) {
					imgFileList.add(img_file7);
				}
				MultipartFile img_file8 = ShopsUtil.testMulRequest(request, "img_file8");
				if(img_file8 != null) {
					imgFileList.add(img_file8);
				}
				MultipartFile img_file9 = ShopsUtil.testMulRequest(request, "img_file9");
				if(img_file9 != null) {
					imgFileList.add(img_file9);
				}
				if(imgFileList.size()>0){//图片信息处理
					for(int i=0;i<imgFileList.size();i++) {
						MultipartFile imgFile = imgFileList.get(i);
						String orName = imgFile.getOriginalFilename();//获取文件原名称
						String suffix = orName.substring(orName.lastIndexOf("."));
						String fileName = Math.round(Math.random()*100)+suffix;
						boolean flag = ShopsUtil.saveFile(imgFile, filePath, fileName);
						if(flag){
							Map<String ,Object> adContentFile = new HashMap<String ,Object>();
							adContentFile.put("content_file", filePathDetail+File.separator + fileName);
							adContentFile.put("sys_ad_id", sys_ad_id);
							adContentFileList.add(adContentFile);
						}
					}
				}
			}else {//视频
				MultipartFile videoFile = ShopsUtil.testMulRequest(request, "video_file");
				if(null != videoFile){//图片信息处理
					String orName = videoFile.getOriginalFilename();//获取文件原名称
					String suffix = orName.substring(orName.lastIndexOf("."));
					String fileName = Math.round(Math.random()*100)+suffix;
					boolean flag = ShopsUtil.saveFile(videoFile, filePath, fileName);
					if(flag){
						Map<String ,Object> adContentFile = new HashMap<String ,Object>();
						adContentFile.put("content_file", filePathDetail+File.separator + fileName);
						adContentFile.put("sys_ad_id", sys_ad_id);
						adContentFileList.add(adContentFile);
					}
				}
			}
			if(adContentFileList.size()>0) {
				adDao.addAdContentFileList(adContentFileList);
			}
			
			//删除优惠券
			adDao.deleteAdCoupon(sys_ad_id);
			//添加广告优惠券
			String ad_coupons = MUtil.strObject(params.get("ad_coupons"));
			if(ad_coupons != null && !ad_coupons.equals("")) {
				List<Map<String ,Object>> ad_coupon_list = MUtil.strToList(ad_coupons);
				if(ad_coupon_list.size() > 0) {
					for(int i=0;i<ad_coupon_list.size();i++) {
						ad_coupon_list.get(i).put("sys_ad_id", sys_ad_id);
					}
					adDao.addAdCouponList(ad_coupon_list);
				}
			}
			result.setStatus(1);
			result.setMsg("成功");
		} catch (Exception e) {
			e.printStackTrace();
			result.setStatus(0);
			result.setMsg("异常");
		}
		
		return result;
	}

	@Override
	public List<Map<String, Object>> queryAdLogList(String sys_ad_id) {
		return adDao.queryAdLogList(sys_ad_id);
	}

	@Override
	public PurResult updateAdStatus(Map<String, Object> params) {
		PurResult result = new PurResult();
		try {
			adDao.updateAd(params);
			result.setStatus(1);
			result.setMsg("成功");
		} catch (Exception e) {
			e.printStackTrace();
			result.setStatus(0);
			result.setMsg("异常");
		}
		
		return result;
	}

	@Override
	public PurResult deleteAd(String sys_ad_id,HttpServletRequest request) {
		PurResult result = new PurResult();
		try {
			adDao.deleteAd(sys_ad_id);
			//获取广告投放内容列表
			List<Map<String ,Object>> adContentFileList = adDao.queryAdContentFileList(sys_ad_id);
			//删除文件
			String filePath = request.getSession().getServletContext().getRealPath(File.separator);
			filePath = filePath.substring(0, filePath.length()-request.getContextPath().length());
			for(int i=0;i<adContentFileList.size();i++) {
				DeleteFileUtil.delete(filePath+adContentFileList.get(i).get("content_file"));
				adDao.deleteAdContentFile(MUtil.strObject(adContentFileList.get(i).get("sys_ad_content_file_id")));
			}
			adDao.deleteAdArea(sys_ad_id);//删除区域
			adDao.deleteAdCoupon(sys_ad_id);//删除优惠券
			result.setStatus(1);
			result.setMsg("成功");
		} catch (Exception e) {
			e.printStackTrace();
			result.setStatus(0);
			result.setMsg("异常");
		}
		
		return result;
	}

	@Override
	public List<Map<String, Object>> queryAreaDictList(String area_dict_parent_num) {
		Map<String ,Object> params = new HashMap<String ,Object>();
		params.put("area_dict_parent_num", area_dict_parent_num);
		return adDao.queryAreaDictList(params);
	}
	
}
