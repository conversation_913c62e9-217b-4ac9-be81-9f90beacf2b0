package org.haier.shop.service;

import java.io.File;
import java.sql.Timestamp;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import org.apache.commons.collections.MapUtils;
import org.haier.shop.dao.Goods_kindDao;
import org.haier.shop.entity.*;
import org.haier.shop.dao.ImportDao;
import org.haier.shop.entity.importMsg.ImportSupplierGoods;
import org.haier.shop.redis.RedisCache;
import org.haier.shop.util.GoodsImport;
import org.haier.shop.util.Load;
import org.haier.shop.util.MyException;
import org.haier.shop.util.ShopsResult;
import org.haier.shop.util.mqtt.MqttxUtil;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.ObjectUtils;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;
import org.springframework.web.multipart.commons.CommonsMultipartResolver;

@Service
@Transactional
public class ImportServiceImpl implements ImportService{
	@Resource
	private ImportDao importDao;

	@Resource
	private Goods_kindDao kindDao;

	public ShopsResult queryGoodsMsg(List<String> list, String shopUnique, Integer count){
		ShopsResult sr = new ShopsResult(1, "查询成功!");

		Map<String, Object> map = new HashMap<>();
		map.put("shopUnique", shopUnique);
		map.put("list", list);

		List<Map<String, Object>> resList = importDao.queryGoodsMsg(map);

		sr.setData(resList);
		sr.setCount(count);

		return sr;
	}
	@Transactional
	public ShopsResult updateGoodsSupplier(ImportSupplierGoods importSupplierGoods){
		ShopsResult shopsResult = new ShopsResult();

		importDao.updateGoodsSupplier(importSupplierGoods);

		return shopsResult;
	}
	
	@SuppressWarnings("unchecked")
	public ShopsResult importGoodsKind (HttpServletRequest request,HttpServletResponse response,Map<String ,Object> datas) {
		ShopsResult sr=new ShopsResult(1, "导入成功！");
		String fileName=null;
		String absPath=null;
		//获取上传的文件内容
		CommonsMultipartResolver multipartResolver=new CommonsMultipartResolver(request.getSession().getServletContext());
		try {
			if(multipartResolver.isMultipart(request)){
				MultipartHttpServletRequest multiRequest=(MultipartHttpServletRequest)request;
				MultipartFile file=multiRequest.getFile("file");
				 
//				 if(null==file){
//					 file=multiRequest.getFile("file.xlsx");
//				 }
				 if(file==null){
					 sr.setStatus(2);
					 sr.setMsg("没有符合条件的文件上传");
					 return sr;
				 }
				 //文件保存路径
				 String originName=file.getOriginalFilename();
				 String contextPath=request.getServletContext().getContextPath();
				 File filePath=new File(contextPath).getParentFile();
				 contextPath=filePath.getAbsolutePath()+File.separator+"excelForImport";
				 Calendar c=Calendar.getInstance();
				 fileName=datas.get("shopUnique").toString()+"goodsForImport"+c.getTimeInMillis()+"."+originName;
				 File path=new File(contextPath);//创建文件保存目录
				 if(!path.exists()){
					 path.mkdirs();
				 }
				 //创建文件
				 absPath=contextPath+File.separator+fileName;
				 File goodsFile=new File(absPath);
				 if(!goodsFile.exists()){
					 goodsFile.createNewFile();
				 }
				 file.transferTo(goodsFile);//将表格信息
			}else{
				sr.setStatus(8);
				sr.setMsg("上传文件失败");
				return sr;
			}

			//获取文件信息
			sr = GoodsImport.goodsKindImport(absPath,"goodsKind", datas);

			//失败直接返回
			if (!sr.getStatus().equals(1)) return sr;

			List<KindForImport> l = (List<KindForImport>)sr.getData();

			//先校验所填的父类编号是否存在，防止乱输入
			Set<Long> paruninqueSet = l.stream().filter(k -> !"0".equals(k.getGoodsKindParunique())).map(k -> Long.parseLong(k.getGoodsKindParunique())).collect(Collectors.toSet());
			if (!ObjectUtils.isEmpty(paruninqueSet)){
				List<GoodsGroups> gg = kindDao.getKindByShopuniqueAndKindUnique(l.get(0).getShopUnique(), new ArrayList<>(paruninqueSet));
				if (paruninqueSet.size() != gg.size()){
					sr.setStatus(6);
					sr.setMsg("请输入正确的父类编号");
					return sr;
				}
			}

			//判断导入的数据是否有重复
			boolean repeat = false;
			loop:
			for (int i = 0; i < l.size(); i++) {
				KindForImport kfi = l.get(i);
				for (int j = 0; j < l.size(); j++) {
					if (i == j) continue;
					KindForImport kfij = l.get(j);
					if (kfi.getGoodsKindName().equals(kfij.getGoodsKindName()) && kfi.getGoodsKindParunique().equals(kfij.getGoodsKindParunique())){
						repeat = true;
						break loop;
					}
				}
			}
			if (repeat){
				sr.setStatus(6);
				sr.setMsg("同一分类下分类名称不能重复");
				return sr;
			}

			//判断导入的数据与库里的有没有重复
			Map<String,Object> queryAllMap = new HashMap<>();
			queryAllMap.put("shop_unique", l.get(0).getShopUnique());
			queryAllMap.put("kindType",2);
			List<Map<String, Object>> allKind = kindDao.getAllGoodsKind(queryAllMap);
			for (KindForImport k : l){
				Map<String, Object> filterMap = allKind.stream().filter(m -> k.getGoodsKindName().equals(MapUtils.getString(m, "goods_kind_name")) && k.getGoodsKindParunique().equals(MapUtils.getString(m, "goods_kind_parunique")))
						.findAny().orElse(null);
				if (filterMap != null){
					sr.setStatus(6);
					sr.setMsg("分类名称：\"" + k.getGoodsKindName() + "\"已存在");
					return sr;
				}
			}

			for (int i = 0; i < l.size(); i++) {
				KindForImport kfi = l.get(i);
				//编写分类编号
				Map<String,Object> map = new HashMap<>();
				map.put("shopUnique",kfi.getShopUnique());
				map.put("groupUnique",kfi.getGoodsKindParunique());
				Long kindUnique=kindDao.queryNowMaxUnique(map);
				if(null==kindUnique&&map.get("groupUnique").equals("0")){
					kindUnique=100000l + i + 1;
				}else if(null==kindUnique){
					kindUnique=Long.parseLong(map.get("groupUnique").toString())+i+1;
				}else if(null !=kindUnique && map.get("groupUnique").equals("0")){
					kindUnique=(long) Math.ceil(kindUnique*Math.pow(1.1,i+1));
				}else{
					kindUnique+=i + 1;
				}
				kfi.setGoodsKindUnique(String.valueOf(kindUnique));
			}

			Integer cord = importDao.importNewGoodsKind(l);
			sr.setCord(cord);

			//20220909 新增MQTT -通知收银机有商品分类更新----start
	 		RedisCache rc = new RedisCache("");
	 		Object mac = rc.getObject("topic_"+datas.get("shopUnique"));
		    if (mac != null){
				List<String> macIdList = (List<String>)mac;
				//2 MQTT 发送消息
				for(String macid : macIdList)
				{
					Map<String,Object> data=new HashMap<String,Object>();
					data.put("ctrl", "msg_goods_kind");//支付成功
					data.put("ID", macid);
					data.put("status", 200);
					data.put("data",null);
					data.put("count",1 );
					MqttxUtil.sendMapMsg(data, macid);
				}
			}

		}catch (Exception e) {
			e.printStackTrace();
			sr.setStatus(0);
			sr.setMsg("导入失败");
		}
		return sr;
	}
	/**
	 * 商品信息导入
	 * @param request
	 * @param response
	 * @param datas
	 * @return
	 */
	public ShopsResult goodsImport(HttpServletRequest request,HttpServletResponse response,Map<String ,Object> datas){
		ShopsResult sr=new ShopsResult(1, "导入成功！");
		String fileName=null;
		String absPath=null;
		//获取上传的文件内容
		CommonsMultipartResolver multipartResolver=new CommonsMultipartResolver(request.getSession().getServletContext());
		try{
			if(multipartResolver.isMultipart(request)){
				MultipartHttpServletRequest multiRequest=(MultipartHttpServletRequest)request;
				MultipartFile file=multiRequest.getFile("file");
				 
//				 if(null==file){
//					 file=multiRequest.getFile("file.xlsx");
//				 }
				 if(file==null){
					 sr.setStatus(2);
					 sr.setMsg("没有符合条件的文件上传");
					 return sr;
				 }
				 //文件保存路径
				 String originName=file.getOriginalFilename();
				 String contextPath=request.getServletContext().getContextPath();
				 File filePath=new File(contextPath).getParentFile();
				 contextPath=filePath.getAbsolutePath()+File.separator+"excelForImport";
				 Calendar c=Calendar.getInstance();
				 fileName=datas.get("shopUnique").toString()+"goodsForImport"+c.getTimeInMillis()+"."+originName;
				 File path=new File(contextPath);//创建文件保存目录
				 if(!path.exists()){
					 path.mkdirs();
				 }
				 //创建文件
				 absPath=contextPath+File.separator+fileName;
				 File goodsFile=new File(contextPath+File.separator+fileName);
				 if(!goodsFile.exists()){
					 goodsFile.createNewFile();
				 }
				 file.transferTo(goodsFile);//将表格信息
			}else{
				sr.setStatus(8);
				sr.setMsg("上传文件失败");
				return sr;
			}
			
			//根据文件的保存路径，获取文件信息
			//必须上传的字段
			List<String> needList=new ArrayList<String>();
			needList.add("条码");
			needList.add("名称");
			needList.add("售价");
			//元素设置
			Map<String,ImportFieldRemarks> map=new HashMap<String, ImportFieldRemarks>();
			ImportFieldRemarks m1=new ImportFieldRemarks("条码", true, 1);
			ImportFieldRemarks m2=new ImportFieldRemarks("名称", true, 2);
			ImportFieldRemarks m3=new ImportFieldRemarks("售价", true, 3);
			map.put("条码", m1);
			map.put("名称", m2);
			map.put("售价", m3);
			
			//获取当前店铺默认分类，防止导入时无商品分类信息
			Integer type=importDao.queryShopKindType(datas.get("shopUnique").toString());
			if(null==type){
				sr.setStatus(0);
				sr.setMsg("登录失效，请重新登录");
				return sr;
			}
			if(type==1){
				datas.put("kindUnique", Load.DEFAULTSYSKINDUNIQUE);
			}else if(type==2){
				datas.put("kindUnique", Load.DEFAULTSELFKINDUNIQUE);
			}
			sr=GoodsImport.goodsImport(absPath, datas.get("sheetName").toString(), needList, map, 1, datas);
			if(sr.getStatus()!=1){
				return sr;
			}
			
			
			@SuppressWarnings("unchecked")
			List<GoodsForImport> list = (List<GoodsForImport>) sr.getData();
//			System.out.println(list);
			datas.put("list", list);
			//删除已有商品信息
			Integer k=importDao.screenGoods(datas);
			//添加新商品
//			System.out.println("商品导入中！");
			k=importDao.importGoods(list);
			
			//mqtt 同步商品
			try {
				
				RedisCache rc = new RedisCache("");
		 		Object mac = rc.getObject("topic_"+datas.get("shopUnique").toString());
			     if(mac!=null)
			     {
			    	 @SuppressWarnings("unchecked")
					List<String> macIdList = (List<String>)mac;
			    	 
			    	 List<Map<String,Object>> returnList=new ArrayList<>();
			    	 for(GoodsForImport goods:list)
			    	 {
			    		  Map<String,Object> map2=new HashMap<String, Object>();
			    		  map2.put("shop_unique", datas.get("shopUnique").toString());
			    		  map2.put("goods_barcode", goods.getGoodsBarcode());
			    		  map2.put("goods_name", goods.getGoodsName());
			    		  map2.put("goods_kind_unique", goods.getGoodsKindsUnique());
			    		  map2.put("goods_in_price", goods.getGoodsInPrice());
			    		  map2.put("goods_sale_price", goods.getGoodsSalePrice());
			    		  map2.put("goods_unit", goods.getGoodsUnit());
			    		  map2.put("goods_count", goods.getGoodsCount());
			    		  map2.put("goods_standard", goods.getGoodsStandard());
			    		  String update_time=LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
			    		  map2.put("update_time", update_time);
			    		  map2.put("goodsCusPrice", goods.getGoodsCusPrice());
			    		  map2.put("goods_web_sale_price", goods.getGoodsWebSalePrice()); 
				    		returnList.add(map2);
			    	 }
			    		
			       	//2 MQTT 发送消息
			    	 for(String macid:macIdList)
			    	 {
			    		 
			 	        Map<String,Object> data=new HashMap<String,Object>();
				        data.put("ctrl", "msg_goods_update");
				        data.put("ID", macid);
				        data.put("status", 200);
				        data.put("data",returnList);
				        data.put("count",returnList.size() );
				        MqttxUtil.sendMapMsg(data, macid); 
			    	 }

			      }
			     //清除缓存 
			     String rcId = "pcGoods"+datas.get("shopUnique").toString()+1+3+null;
			     Object res = rc.getObject(rcId);
			     if(null != res) {
			    	 rc.removeObject(rcId);
			     }
			     String rcId2 = "pcGoods"+datas.get("shopUnique").toString()+0+3+null;
			     Object res2 = rc.getObject(rcId2);
			     if(null != res2) {
			    	 rc.removeObject(rcId2);
			     }
			    
			     
			}catch(Exception e)
			{
				e.printStackTrace();
			}
			
			sr.setCord(k);
			
			//导入商品成功后，需将不存在于大库中的额商品信息添加到大库中
			importDao.addNewCloudGoods(datas);
			//查询店内商品在大库中的规格信息
			List<Map<String,Object>> goodsList=importDao.queryGoodsNewForeignKey(datas);
			List<Map<String,Object>> sGoods=new ArrayList<Map<String,Object>>();
			List<Map<String,Object>> hGoods=new ArrayList<Map<String,Object>>();
			//将所有中规格的商品信息从数据中分离出来，存放到sGoods中
			if(null!=goodsList&&goodsList.size()>0){
				for(int i=0;i<goodsList.size();i++){
					if(!goodsList.get(i).get("goodsContain").toString().equals("1")){
						sGoods.add(goodsList.get(i));
						goodsList.remove(i);
						i--;
					}
				}
			}
			
			//检查sGoods，将其中包含中规格，但包含小规格的商品转移到hGoods中，并将仅包含中规格的商品的goodsContain设为1
			if(sGoods!=null && sGoods.size()>0){
				for(int i=0;i<sGoods.size();i++){
					for(int j=0;j<goodsList.size();j++){
						if(sGoods.get(i).get("foreignKey").toString().equals(goodsList.get(j).get("foreignKey").toString())){
							hGoods.add(sGoods.get(i));
//						System.out.println(sGoods.get(i));
//						goodsList.add(sGoods.get(i));
							sGoods.remove(i);
							i--;
							break;
						}
					}
				}
				for(int i=0;i<sGoods.size();i++){
					sGoods.get(i).put("goodsContain", 1);//不修改foreignKey值，仅修改其goodsContain值
//				System.err.println("只包含一个规格的商品为："+sGoods.get(i));
				}
				sGoods.addAll(hGoods);
				
				datas.put("list", sGoods);
				Integer count=importDao.updateNewShopsGoodsMessage(datas);
				System.out.println(count);
			}
			//更新店内商品规格信息
		}catch(Exception e){
			e.printStackTrace();
			sr.setStatus(2);
			sr.setMsg("导入失败！请联系开发者导入");
		}
		return sr;
	}
	
	/**
	 * 会员信息导入
	 * @param request
	 * @param response
	 * @param datas
	 * @return
	 * 步骤一：获取上传的文件信息，将文件信息保存，
	 * 步骤二：读取文件信息，并将文件信息读取为List列表；
	 * 步骤三：删除已有的会员信息，防止重复插入；
	 * 步骤四：查询会员相关补充信息，例如：会员等级信息等
	 * 步骤五：将补充后的会员信息导入，并返回提示信息
	 */
	@SuppressWarnings("unchecked")
	public ShopsResult customerImport(HttpServletRequest request,HttpServletResponse response,Map<String ,Object> datas,String saleListCashier){
		ShopsResult sr=new ShopsResult(1,"导入成功");
		String fileName=null;
		String absPath=null;
//		System.out.println("会员等级BUGl::"+importDao.queryCusLevelIdInShops(datas));
		datas.putAll(importDao.queryCusLevelIdInShops(datas));
		//获取上传的文件内容
		CommonsMultipartResolver multipartResolver=new CommonsMultipartResolver(request.getSession().getServletContext());
		try{
			if(multipartResolver.isMultipart(request)){
				MultipartHttpServletRequest multiRequest=(MultipartHttpServletRequest)request;
				 MultipartFile file=multiRequest.getFile("customer");
				 if(null==file){
					 file=multiRequest.getFile("customer.xlsx");
				 }
				 if(file==null){
					 sr.setStatus(2);
					 sr.setMsg("没有符合条件的文件上传");
					 return sr;
				 }
				 //文件保存路径
				 String originName=file.getOriginalFilename();
				 String contextPath=request.getServletContext().getContextPath();
				 File filePath=new File(contextPath).getParentFile();
				 contextPath=filePath.getAbsolutePath()+File.separator+"excelForImport";
				 Calendar c=Calendar.getInstance();
				 fileName=datas.get("shopUnique").toString()+"customerForImport"+c.getTimeInMillis()+"."+originName;
				 File path=new File(contextPath);//创建文件保存目录
				 if(!path.exists()){
					 path.mkdirs();
				 }
				 //创建文件
				 absPath=contextPath+File.separator+fileName;
				 File goodsFile=new File(contextPath+File.separator+fileName);
				 if(!goodsFile.exists()){
					 goodsFile.createNewFile();
				 }
				 file.transferTo(goodsFile);//将表格信息转化为文件信息
				 System.out.println("文件保存成功！");
			}else{
				sr.setStatus(8);
				sr.setMsg("上传文件失败");
				return sr;
			}
			List<String> needList=new ArrayList<>();
			needList.add("会员编号");
			sr=GoodsImport.goodsImport(absPath,datas.get("sheetName").toString(), needList, null, 2, datas);
			if(sr.getStatus()!=1){
				return sr;
			}
			List<CustomerForImport> cusList=(List<CustomerForImport>)sr.getData();
			//获取到最新的会员信息，将已存在于数据库的相同会员信息删除；
			datas.put("list", cusList);
			importDao.screenCustomer(datas);
			for(int i=0;i<cusList.size();i++){
				CustomerForImport customerForImport = cusList.get(i);
				//将需要重新添加的会员信息导入
				importDao.importCustomers(customerForImport);
				//累计充值
//				Double cusAmount = customerForImport.getCusAmount();
//				if(cusAmount > 0){
//					//添加初始化数据充值记录
//					Map<String ,Object> params1 = new HashMap<String, Object>();
//					params1.put("cus_id", customerForImport.getCus_id());
//					params1.put("recharge_money", cusAmount);
//					params1.put("cus_balance", cusAmount);
//					params1.put("cus_amount", cusAmount);
//					params1.put("cus_type", 1);//充值
//					params1.put("saleListCashier", saleListCashier);
//					params1.put("remarks", "会员信息表格导入初始化充值记录");
//					importDao.insertCustomerRecharge(params1);
//				}
				
				//如果有余额，新增充值信息
				Double cusBalance = customerForImport.getCusBalance();
				if(cusBalance > 0) {//需要添加充值记录
					Map<String,Object> rechargeParams = new HashMap<String,Object>();
					rechargeParams.put("cus_id", customerForImport.getCus_id());
					rechargeParams.put("recharge_money", cusBalance);
					rechargeParams.put("cus_balance", cusBalance);		
					rechargeParams.put("cus_amount", cusBalance);
					rechargeParams.put("cus_type", 1);
					rechargeParams.put("recharge_method", 1);
					rechargeParams.put("remarks", "会员导入初始化数据");
					rechargeParams.put("data_type", "2");
					rechargeParams.put("give_money", customerForImport.getGiveBalance());
					rechargeParams.put("shop_unique", datas.get("shopUnique"));
					rechargeParams.put("recharge_status", "1");
					
					importDao.addNewCustomerRecharge(rechargeParams);
					Map<String,Object> useMap = new HashMap<String,Object>();
					useMap.put("recharge_id", rechargeParams.get("recharge_id"));
					useMap.put("recharge_balance", cusBalance);
					useMap.put("recharge_give_balance", customerForImport.getGiveBalance());
					
					importDao.addNewCustomerRechargeUse(useMap);
				}
				
				
				//累计消费
				Double cusTotal = customerForImport.getCusTotal();
				if(cusTotal > 0){
					//添加初始化数据消费记录
					Map<String ,Object> params1 = new HashMap<String, Object>();
					params1.put("cus_id", customerForImport.getCus_id());
					params1.put("recharge_money", cusTotal);
					params1.put("cus_balance", customerForImport.getCusBalance());
					params1.put("cus_amount", cusTotal);
					params1.put("cus_type", 3);//消费
					params1.put("saleListCashier", saleListCashier);
					params1.put("remarks", "会员信息表格导入初始化消费记录");
					importDao.insertCustomerRecharge(params1);
				}
			}
		}catch (Exception e){
			e.printStackTrace();
			sr.setStatus(2);
			sr.setMsg("导入失败");
		}
		return sr;
	}
	
	/**
	 * 订单信息导入
	 * @param request
	 * @param response
	 * @param datas
	 * @param saleListCashier
	 * @return
	 */
	public ShopsResult importSaleList(HttpServletRequest request,HttpServletResponse response,Map<String ,Object> datas){
		ShopsResult sr = new ShopsResult(1,"导入成功！");
		try {
			/**
			 * 订单信息导入流程
			 * 1:获取上传的文件信息,将文件保存到本地备用;
			 * 2:读取文件的sheetName表格，将获取文件信息；
			 * 3：验证相关的字段是否存在，若不存在，导入失败
			 * 4：先统计订单信息；
			 * 5：根据订单信息，统计订单详情信息；
			 * 6：将信息导入数据库
			 */
			
			String fileName=null;//文件名
			String absPath=null;//文件存储的绝对路径
			
//			CommonsMultipartResolver multipartResolver=new CommonsMultipartResolver(request.getSession().getServletContext());
			
			MultipartHttpServletRequest multiRequest=(MultipartHttpServletRequest)request;
//			Iterator<String> l= multiRequest.getFileNames();
//			while(l.hasNext()){
//				System.out.println(l.next());
//			}
			MultipartFile file=multiRequest.getFile("file");
			if(file==null){
				sr.setStatus(2);
				sr.setMsg("没有符合条件的文件上传");
				return sr;
			}
			//文件保存路径
			String originName=file.getOriginalFilename();//文件原名
			String contextPath=request.getServletContext().getContextPath();//获取当前项目所在的绝对路径
			File filePath=new File(contextPath).getParentFile();
			contextPath=filePath.getAbsolutePath()+File.separator+"excelForImport";
			Calendar c=Calendar.getInstance();
			fileName=datas.get("shopUnique").toString()+"SaleListForImport"+c.getTimeInMillis()+"."+originName;
			System.out.println("文件的保存路径为：：："+contextPath);
			File path=new File(contextPath);//创建文件保存目录
			if(!path.exists()){
			path.mkdirs();
			}
			//创建文件
			absPath=contextPath+File.separator+fileName;
			File saleFile=new File(contextPath+File.separator+fileName);
			if(!saleFile.exists()){
				saleFile.createNewFile();
			}
			file.transferTo(saleFile);//将表格信息转化为文件信息
		 
			List<String> needList=new ArrayList<>();
			needList.add("订单编号");
			needList.add("下单时间");
			needList.add("商品条码");
			needList.add("商品名称");
			needList.add("数量");
			
			//从excel表格获取数据，并将数据返回
			sr=GoodsImport.SaleListImport(absPath, datas.get("sheetName").toString(), needList, null, datas);
			
			if(sr.getStatus()!=1){
				return sr;
			}
			@SuppressWarnings("unchecked")
			List<SaleListMain> saleList = (List<SaleListMain>)sr.getData();
			
			//将获取的数据导入订单列表
			if(saleList.isEmpty()){
				sr.setStatus(0);
				sr.setMsg("数据表无信息");
			}
			
			importDao.addNewSaleList(saleList);
			
			List<SaleListDetail> dls = new ArrayList<>();
			for(int i = 0;i<saleList.size();i++){
				dls.addAll(saleList.get(i).getListDetail());
			}
			importDao.addNewSaleListDetail(dls);
			sr.setData(saleList.size());
		} catch (Exception e) {
			e.printStackTrace();
			sr.setStatus(0);
			sr.setMsg("系统错误！");
			throw new MyException(0,"系统错误！");
		}
		return sr;
	}

	@Override
	public ShopsResult importGoodsModifyPrice(HttpServletRequest request, HttpServletResponse response,
			Map<String, Object> datas) {
		ShopsResult sr=new ShopsResult(1, "导入成功！");
		String fileName=null;
		String absPath=null;
		//获取上传的文件内容
		CommonsMultipartResolver multipartResolver=new CommonsMultipartResolver(request.getSession().getServletContext());
		try{
			if(multipartResolver.isMultipart(request)){
				MultipartHttpServletRequest multiRequest=(MultipartHttpServletRequest)request;
				 MultipartFile file=multiRequest.getFile("file");
				 
//				 if(null==file){
//					 file=multiRequest.getFile("file.xlsx");
//				 }
				 if(file==null){
					 sr.setStatus(2);
					 sr.setMsg("没有符合条件的文件上传");
					 return sr;
				 }
				 //文件保存路径
				 String originName=file.getOriginalFilename();
				 String contextPath=request.getServletContext().getContextPath();
				 File filePath=new File(contextPath).getParentFile();
				 contextPath=filePath.getAbsolutePath()+File.separator+"excelForImport";
				 Calendar c=Calendar.getInstance();
				 fileName=datas.get("shopUnique").toString()+"goodsForImport"+c.getTimeInMillis()+"."+originName;
				 File path=new File(contextPath);//创建文件保存目录
				 if(!path.exists()){
					 path.mkdirs();
				 }
				 //创建文件
				 absPath=contextPath+File.separator+fileName;
				 File goodsFile=new File(contextPath+File.separator+fileName);
				 if(!goodsFile.exists()){
					 goodsFile.createNewFile();
				 }
				 file.transferTo(goodsFile);//将表格信息
			}else{
				sr.setStatus(8);
				sr.setMsg("上传文件失败");
				return sr;
			}
			
			//根据文件的保存路径，获取文件信息
			//必须上传的字段
			List<String> needList=new ArrayList<String>();
			needList.add("条码");
			needList.add("名称");
			needList.add("售价");
			needList.add("调后售价");
			needList.add("会员价");
			needList.add("调后会员价");
			//元素设置
			Map<String,ImportFieldRemarks> map=new HashMap<String, ImportFieldRemarks>();
			ImportFieldRemarks m1=new ImportFieldRemarks("条码", true, 1);
			ImportFieldRemarks m2=new ImportFieldRemarks("名称", true, 2);
			ImportFieldRemarks m3=new ImportFieldRemarks("售价", true, 3);
			ImportFieldRemarks m4=new ImportFieldRemarks("调后售价", true, 4);
			ImportFieldRemarks m5=new ImportFieldRemarks("会员价", true, 5);
			ImportFieldRemarks m6=new ImportFieldRemarks("调后会员价", true, 6);
			map.put("条码", m1);
			map.put("名称", m2);
			map.put("售价", m3);
			map.put("调后售价", m4);
			map.put("会员价", m5);
			map.put("调后会员价", m6);
			
			//获取当前店铺默认分类，防止导入时无商品分类信息
			Integer type=importDao.queryShopKindType(datas.get("shopUnique").toString());
			if(null==type){
				sr.setStatus(0);
				sr.setMsg("登录失效，请重新登录");
				return sr;
			}
			if(type==1){
				datas.put("kindUnique", Load.DEFAULTSYSKINDUNIQUE);
			}else if(type==2){
				datas.put("kindUnique", Load.DEFAULTSELFKINDUNIQUE);
			}
			sr=GoodsImport.goodsImport(absPath, datas.get("sheetName").toString(), needList, map, 1, datas);
			if(sr.getStatus()!=1){
				return sr;
			}
			
			
			@SuppressWarnings("unchecked")
			List<GoodsForImport> list = (List<GoodsForImport>) sr.getData();
			System.out.println(list);
			sr.setData(list);
		}catch(Exception e){
			e.printStackTrace();
			sr.setStatus(2);
			sr.setMsg("导入失败！请联系开发者导入");
		}
		return sr;
	}
}
