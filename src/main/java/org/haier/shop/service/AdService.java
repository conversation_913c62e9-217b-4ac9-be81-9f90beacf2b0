package org.haier.shop.service;

import java.util.List;
import java.util.Map;

import javax.servlet.http.HttpServletRequest;

import org.haier.shop.util.PurResult;

public interface AdService {
	
	/**
	 * 查询广告列表
	 * @return
	 */
	public PurResult queryAdList(Map<String,Object> params);
	
	/**
	 * 添加广告
	 * @return
	 */
	public PurResult addAd(HttpServletRequest request);
	
	/**
	 * 获取广告详情
	 * @return
	 */
	public Map<String ,Object> queryAdDetail(String sys_ad_id);
	
	/**
	 * 修改广告
	 * @return
	 */
	public PurResult updateAd(HttpServletRequest request);
	
	/**
	 * 获取广告投放详情列表
	 * @return
	 */
	public List<Map<String ,Object>> queryAdLogList(String sys_ad_id);
	
	/**
	 * 修改广告状态
	 * @return
	 */
	public PurResult updateAdStatus(Map<String,Object> params);
	
	/**
	 * 删除广告
	 * @return
	 */
	public PurResult deleteAd(String sys_ad_id,HttpServletRequest request);
	
	/**
	 * 获取省市区列表
	 * @return
	 */
	public List<Map<String ,Object>> queryAreaDictList(String area_dict_parent_num);
}
