package org.haier.shop.service;

import java.util.List;
import java.util.Map;

import org.haier.shop.util.PurResult;
import org.haier.shop.util.ShopsResult;

public interface InventoryService {
	/**
	 * 商品盘库订单信息查询
	 * @param map
	 * @return
	 */
	
	public PurResult queryInventoryRecord(Map<String,Object> map);
	/**
	 * 盘点订单详情
	 * @param map
	 * @return
	 */
	public PurResult queryInventoryDetail(Map<String,Object> map);
	
	public List<Map<String,Object>> queryInventoryList(Map<String,Object> map);
	
}
