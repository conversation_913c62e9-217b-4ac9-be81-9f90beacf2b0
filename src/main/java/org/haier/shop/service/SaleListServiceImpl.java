package org.haier.shop.service;

import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import org.haier.customer.entity.ShopResult;
import org.haier.shop.dao.*;
import org.haier.shop.entity.SaleListMain;
import org.haier.shop.entity.ShopVO;
import org.haier.shop.entity.ShopsConfig;
import org.haier.shop.entity.ret.ReturnMain;
import org.haier.shop.entity.ret.ReturnPayDetail;
import org.haier.shop.enums.GoodsInPriceTypeEnums;
import org.haier.shop.params.goodsBatch.GoodsSaleBatchData;
import org.haier.shop.redis.RedisCache;
import org.haier.shop.util.*;
import org.haier.shop.util.wxPay.HttpUtil;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.yxl.heLiBaoPay.util.HeLiBaoPay;
import org.yxl.heLiBaoPay.vo.AppPayRefundOrderResponseVo;
import org.yxl.heLiBaoPay.vo.AppPayRefundOrderVo;
import org.yxl.heLiBaoPay.vo.AppPayRefundQueryResponseVo;
import org.yxl.heLiBaoPay.vo.AppPayRefundQueryVo;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.math.BigDecimal;
import java.sql.Timestamp;
import java.text.ParseException;
import java.util.*;
import org.haier.shop.enums.SourceTypeEnums;

@Service("saleListService")
public class SaleListServiceImpl implements SaleListService{
	@Resource
	private Sale_listDao saleDao;
	
	@Resource
	private DisDao disDao;
	
	@Resource
	private FarmProductDao farmProductDao;
	
	@Resource
	private RedisCache redis;

	@Resource
	private ShopDao shopDao;

	@Resource
	private ShopsConfigDao shopsConfigDao;
	@Resource
	private GoodsSaleBatchMapper goodsSaleBatchMapper;
	
	/**
	 * 分店统计-柱状图
	 */
	public ShopsResult queryXXsale(Map<String,Object> map){
		ShopsResult sr=new ShopsResult(1,"查询成功！");
		try {
			List<Map<String,Object>> data=new  ArrayList<>();
			Map<String,Object> cord=new  HashMap<String,Object>();
			if(map.containsKey("query_type")&& map.get("query_type").equals("shop"))
			{
				 data=saleDao.queryXXsale(map);
				 cord=saleDao.queryXXsaleCount(map);
			}else
			{
	//			 data=saleDao.queryNYsaleArea(map);
	//			 cord=saleDao.queryNYsaleAreaCount(map);
			}
	
			sr.setData(data);
			sr.setCord(cord);
		} catch (MyException e) {
			return new ShopsResult(e.status, e.msg);
		}
		return sr;
	}
	/**
	 * 1、查询各分店的营业信息
	 * @param manager_unique
	 * @param page
	 * @param limit
	 * @param startTime
	 * @param endTime
	 * @param shopList
	 * @return
	 */
	public ShopsResult statisticsForShopByPage(String manager_unique,Integer page,Integer limit,String startTime,
			String endTime,String shopList,String field,String order) {
		ShopsResult sr = new ShopsResult(1,"查询成功!");
		try {
		if(null == startTime || null == endTime || startTime.equals("") || endTime.equals("")) {
			sr.setStatus(0);
			sr.setMsg("请上传查询时间");
			return sr;
		}
		
		Map<String,Object> map = new HashMap<String,Object>();
		map.put("startTime", startTime);
		map.put("endTime", endTime);
		map.put("manager_unique", manager_unique);
		map.put("startNum", (page - 1) * limit);
		map.put("limit", limit);
		map.put("shopList", shopList);
		map.put("field", field);
		map.put("order", order);
		List<Map<String,Object>> list = saleDao.statisticsForShopByPage(map);
		Integer count = saleDao.statisticsForShopCount(map);
		
		sr.setCount(count);
		sr.setData(list);
		} catch (MyException e) {
			return new ShopsResult(e.status, e.msg);
		}
		return sr;
	}
	
	public ShopsResult updateGoodsList() {
		ShopsResult sr = new ShopsResult();
		List<Map<String,Object>> list = saleDao.queryGoodsCount();
		if(null != list && !list.isEmpty()) {
			saleDao.updateGoodsList(list);
		}
		sr.setStatus(1);
		sr.setMsg("成功！");
		return sr;
	}
	/**
	 * 查询退款订单详情
	 * @param retListUnique
	 * @return
	 */
	public ShopsResult queryReturnDetail(String retListUnique) {
		ShopsResult sr = new ShopsResult(1,"查询成功!");
		
		Map<String,Object> map = new HashMap<String,Object>();
		map.put("retListUnique", retListUnique);
		ReturnMain returnMain = saleDao.queryReturnDetail(map);
		sr.setData(returnMain);
		
		return sr;
	}
	/**
	 * 修改退款订单状态
	 * @param retListUnique 退款单号
	 * @param retListHandlestate 退款审核状态
	 * @param retListRemarks 如果拒绝退款，需要填写拒绝原因
 	 * @param staffId 操作员工ID
	 * @param macId 操作设备的macId或浏览器型号
	 * @return
	 */
	@Transactional
	public ShopsResult modifyReturnMsg(String retListUnique, Integer retListHandlestate, String retListRemarks, String staffId, String macId) {
		ShopsResult sr = new ShopsResult(1,"操作成功!");
		
		//需要防止重复操作
		String retBackId = "retBackId" + retListUnique + retListHandlestate;
		if(null != redis.getObject(retBackId)) {
			
			
			sr.setStatus(0);
			sr.setMsg("已经提交操作申请,请刷新或等待结果");
//			return sr;
		}else {
			redis.putObject(retBackId, retBackId);
		}
		
		
		Map<String,Object> map = new HashMap<String,Object>();
		map.put("retListUnique", retListUnique);
		map.put("retListHandlestate", retListHandlestate);
		map.put("retListState", 2);
		map.put("retListRemarks", retListRemarks);
		map.put("staffId", staffId);
		map.put("macId", macId);
		//分为拒绝和退款两种，如果拒绝，需要提交拒绝原因；
		if(retListHandlestate == 4) {
			if(null == retListRemarks || retListRemarks.trim().equals("")) {
				sr.setStatus(0);
				sr.setMsg("请输入拒绝原因");
				return sr;
			}
			
			//修改退款申请单
			Integer retCount = saleDao.modifyReturnMsg(map);
			if(retCount == 1) {
				return sr;
			}else {
				sr.setStatus(0);
				sr.setMsg("改订单已完成退款，请勿重复退款");
				return sr;
			}
		}
	
		/*
		 * 同意退款
		 * 1、查询该订单的退款信息,该订单的退款详情信息
		 * 2、如果需要三方机构退款，向三方机构发起退款申请
		 * 2.1、获取三方机构退款申请结果，如果退款成功，继续下面的流程
		 * 2.2、如果三方机构退款失败，返回退款失败原因，退款结束
		 * 3、增加用户的余额，百货豆等信息，扣除订单赠送给用户的百货豆
		 * 3.1、分别增加用户的余额，百货豆变更信息
		 * 4、如果有优惠券，扣除订单优惠券金额；
		 * 4、如果已经确认收货，减少店铺的百货豆、余额信息；
		 * 5、增加商品库存信息；
		 * 6、增加商品出入口信息；
		 */
		//1、获取订单的退款信息，退款详情信息,
		ReturnMain retMain = saleDao.queryReturnDetail(map);
		if(null == retMain) {
			sr.setStatus(0);
			sr.setMsg("退款信息不存在");
			return sr;
		}
		
		List<ReturnPayDetail> payList = retMain.getPayDetailList();
		//需要扣除店铺的余额
		BigDecimal shopBalance = new BigDecimal(0.0);
		String cusUnique = retMain.getCusUnique();
		String shopUnique = retMain.getShopUnique();
		//需要给客户增加的余额
		BigDecimal cusBalance = new BigDecimal(0.0);
		//需要给客户增加的百货豆及需要扣除的百货豆
		Integer cusBeans = 0, giveBean = 0;
		for(Integer i = 0; i < payList.size(); i++) {
			ReturnPayDetail payDetail = payList.get(i);
			if(payDetail.getServiceType() == 6) {
				Map<String, Object> payConfigParams = new HashMap<>();
				payConfigParams.put("shopUnique", retMain.getShopUnique());
				payConfigParams.put("saleListUnique", retMain.getSaleListUnique());
				Map<String, String> payConfig = saleDao.selectPayConfig(payConfigParams);
				//申请线上退款
				ShopsResult retRes;
				if (payConfig != null) {
					retRes = HelibaoReturn(retMain.getSaleListUnique() + "", retListUnique, payConfig.get("mchId"), payConfig.get("mchKey"), payDetail.getPayMoney() + "");
				} else {
					retRes = HelibaoReturn(retMain.getSaleListUnique() + "", retListUnique, "E1802781288", "sSO3EWRyaQtETPpBmcAu2Zttj1ph8HFM", payDetail.getPayMoney() + "");
				}
				if(retRes.getStatus() == 1) {
					//退款成功，继续操作
					shopBalance = shopBalance.add(new BigDecimal(payDetail.getPayMoney()));
				}else {
					sr.setStatus(0);
					sr.setMsg(retRes.getMsg());
					return sr;
				}
			}
			//储值卡支付
			if(payDetail.getServiceType() == 1 && payDetail.getPayType() == 5) {
				shopBalance = shopBalance.add(new BigDecimal(payDetail.getPayMoney()));
				cusBalance = cusBalance.add(new BigDecimal(payDetail.getPayMoney()));
			}
			//百货豆
			if(payDetail.getServiceType() == 1 && payDetail.getPayType() == 8) {
				cusBeans = cusBeans + (new BigDecimal(payDetail.getPayMoney()).multiply(new BigDecimal(100.0))).setScale(0,BigDecimal.ROUND_HALF_UP).intValue();
			}
			
			//优惠券退款
			if(payDetail.getServiceType() == 1 && payDetail.getPayType() == 7) {
				shopBalance = shopBalance.add(new BigDecimal(payDetail.getPayMoney()));
			}
		}
		//将订单修改为已退款状态，需要先确认线上退款已完成
		map.put("isBack", 1);
		saleDao.modifyReturnMsg(map);

		
		//增加客户余额，百货豆信息
		
		//如果余额有变动，扣除对应百货豆数量
		//如果需要退款，意味着需要扣除百货赠送的百货豆，计算好，更新退款订单并增加退款记录
//		Integer rate = saleDao.queryRewardList(shopUnique);
		//此处需要确认是否完全退款，如果完全退款，后期不再有确认订单增加送豆的流程，因此
//		giveBean = (int) Math.round(new Double(cusBalance.setScale(2,BigDecimal.ROUND_HALF_UP).doubleValue()*rate/10));
		
		Map<String,Object> cusMap = new HashMap<String,Object>();
		cusMap.put("cusUnique", cusUnique);
		cusMap.put("cusBeans", cusBeans - giveBean);
		cusMap.put("cusBalance", cusBalance.setScale(2, BigDecimal.ROUND_HALF_UP).doubleValue());
		
		//分别增加会员的余额变动记录，百货豆变动记录
		if((cusBeans - giveBean) != 0 || cusBalance.compareTo(new BigDecimal(0.0))!= 0) {
			saleDao.modifyPlatcusMsg(cusMap);
		}
		
		//判断是否需要增加会员余额，百货豆变动记录
		List<Map<String,Object>> cusList = new ArrayList<>();
		Map<String,Object> pubMap = new HashMap<>();
		pubMap.put("shopUnique", shopUnique);
		pubMap.put("cusUnique", cusUnique);
		pubMap.put("saleListUnique", retListUnique);
		pubMap.put("payStatus", "1");
		pubMap.put("serverType", "1");
		if(cusBalance.compareTo(new BigDecimal(0.0)) != 0) {
			//会员余额有变动，添加变动记录
			Map<String,Object> tm = new HashMap<String,Object>();
			tm.putAll(pubMap);
			tm.put("moneyType", 1);
			tm.put("saleType", "1");
			tm.put("money", cusBalance.setScale(2,BigDecimal.ROUND_HALF_UP).doubleValue());
			tm.put("rechargeMethod", "3");
			tm.put("serverType", 6);
			tm.put("give_beans", 0);
			cusList.add(tm);
		}
		
		if(cusBeans.compareTo(0) != 0) {
			//会员百货豆有退款
			Map<String,Object> tm = new HashMap<String,Object>();
			tm.putAll(pubMap);
			tm.put("moneyType", 2);
			tm.put("saleType", "8");
			tm.put("money", 0);
			tm.put("rechargeMethod", "1");
			tm.put("give_beans", cusBeans);
			
			cusList.add(tm);
		}
		
		if(giveBean.compareTo(0) != 0) {
			//会员百货豆有退款
			Map<String,Object> tm = new HashMap<String,Object>();
			tm.putAll(pubMap);
			tm.put("moneyType", 2);
			tm.put("saleType", "15");
			tm.put("money", giveBean);
			tm.put("rechargeMethod", "1");
			tm.put("give_beans", 0);
			
			cusList.add(tm);
		}
		//添加会员余额，百货豆变动记录
		if(null != cusList && !cusList.isEmpty()) {
			saleDao.addCusChangRecord(cusList);
		}
		
		/*
		 * 修改店铺余额信息
		 * 1、确认订单是否已经确认收货了，
		 * 1.1、如果已确认收货，修改店铺的余额信息，并添加百货豆记录
		 * 1.2、如果未确认收货，确认收货时，根据退货信息，修改收款到帐情况
		 * 1.3、统计订单时，根据订单退款情况，去掉退款对应的信息
		 */
		
		Integer saleListHandlestate = retMain.getSaleListHandlestate();
		if(saleListHandlestate == 4 || saleListHandlestate == 6) {
			//已确认收货，需要店铺对应的退款余额和百货豆
			Map<String,Object> shopMap = new HashMap<String,Object>();
			shopMap.put("shopBeans", -cusBeans);
			shopMap.put("shopBalance", -(shopBalance.setScale(2,BigDecimal.ROUND_HALF_UP).doubleValue()));
			shopMap.put("shopUnique", shopUnique);
			
			if(cusBeans != null && cusBeans != 0 && shopBalance != null && shopBalance.compareTo(new BigDecimal(0.0)) != 0) {
				//有需要修改的店铺余额、百货豆信息
				saleDao.modifyShopsMsg(shopMap);
			}
		}else {
			//订单未确认收货，如果订单完全退款，需要将订单设置为已完成配送状态。减少后期订单确认收货去修改订单的信息
			//怎么判断是否全部退款，退款对于不同配送方式的订单配送费的退款规则不一致，暂时没有对接第三方，按照全额退款计算
			//由于全部退款时，不再赠送百货豆，所以不需要额外扣除用户余额
			BigDecimal saleListTotal = new BigDecimal(retMain.getSaleListTotal()).setScale(2, BigDecimal.ROUND_HALF_UP);
			BigDecimal saleListDelfee = new BigDecimal(retMain.getSaleListDelfee()).setScale(2,BigDecimal.ROUND_HALF_UP);
			BigDecimal retListTotal = new BigDecimal(retMain.getRetListTotal()).setScale(2,BigDecimal.ROUND_HALF_UP);
			
			//如果退款总金额 = 订单商品总金额 + 订单配送费，说明订单为全额退款,防止精度失准，改用bigDecimal计算
			if(retListTotal.compareTo(saleListTotal.add(saleListDelfee)) == 0) {
				//需要将订单修改为已确认收货状态
				Map<String,Object> listMap = new HashMap<>();
				listMap.put("sale_list_unique", retMain.getSaleListUnique());
				listMap.put("sale_list_handlestate", "4");
				
				saleDao.updateSaleList(listMap);
			}
			
		}
		
		
		/*
		 * 
		 * 修改商品库存信息
		 * 1、计算商品对应的小规格商品
		 * 2、修改对应小规格商品的库存信息
		 * 3、添加商品出入库记录，添加商品出入口记录主记录
		 */
		map.put("shopUnique", shopUnique);
		map.put("retListUnique", retListUnique);
		List<Map<String,Object>> goodsList = saleDao.querySmallGoodsMsg(map);
		
		if(null != goodsList && !goodsList.isEmpty()) {
			//修改商品库存信息
			saleDao.modifyGoodsMsg(goodsList);
			
			//添加商品出入口记录主记录
			map.put("stockKind", "2");
			map.put("auditStatus", 1);
			map.put("stockRemarks", "小程序退款");
			saleDao.addShopStockDetail(map);
			
			//添加商品出入库记录
			map.put("stockType", 1);
			map.put("stockResource", 9);
			map.put("stockOrigin", "3");
			map.put("staffId", staffId);
			map.put("list", goodsList);
			saleDao.addShopStockList(map);
		}
		
		
		return sr;
	}
	
	public ShopsResult HelibaoReturn(String saleListUnique, String retListUnique, String mchId, String keys, String retAmount) {
		ShopsResult ns = new ShopsResult();
		AppPayRefundOrderVo orderVo = new AppPayRefundOrderVo();
		orderVo.setP2_orderId(saleListUnique);
		orderVo.setP3_customerNumber(mchId);
		orderVo.setP4_refundOrderId(saleListUnique);
		orderVo.setP5_amount(retAmount);
		orderVo.setP7_desc("商家退款");
		
		HeLiBaoPay heLiBaoPay = new HeLiBaoPay();
		AppPayRefundOrderResponseVo orderResponseVo =  heLiBaoPay.appPayRefund(orderVo,keys);
		
		//根据请求结果，判断是否需要查询退款结果
		if( orderResponseVo.getRt2_retCode().equals("0000") || orderResponseVo.getRt2_retCode().equals("0001")) {
			//如果退款申请成功，请求退款结果
			AppPayRefundQueryVo queryVo = new AppPayRefundQueryVo();
			queryVo.setP2_refundOrderId(orderVo.getP4_refundOrderId());
			queryVo.setP3_customerNumber(orderVo.getP3_customerNumber());
			try {
				Thread.sleep(1000);
			} catch (InterruptedException e) {
				e.printStackTrace();
			}
			AppPayRefundQueryResponseVo responseVo =  heLiBaoPay.toRefundQuery(queryVo,keys);
			//接收成功，其他状态，继续查询，直到成功!或者失败
			if(responseVo.getRt2_retCode().equals("0000") && (responseVo.getRt8_orderStatus().equals("SUCCESS") ||responseVo.getRt8_orderStatus().equals("DOING") )) {
				
				
				ns.setStatus(1);
				ns.setMsg("退款成功!");
				return ns;
			}else if(responseVo.getRt2_retCode().equals("0000") ){
				return queryHeLiBaoReturnResult(orderVo.getP4_refundOrderId(), orderVo.getP3_customerNumber(), keys, 5);
			}
			
		}else {
			ns.setStatus(0);
			ns.setMsg(orderResponseVo.getRt3_retMsg());
		}
		return ns;
	}
	
	public ShopsResult queryHeLiBaoReturnResult(String retListUnique,String mchId,String keys,Integer count) {
		ShopsResult ns = new ShopsResult(0, "退款失败");
		if(count == 0) {
			return ns;
		}
		AppPayRefundQueryVo queryVo = new AppPayRefundQueryVo();
		queryVo.setP2_refundOrderId(retListUnique);
		queryVo.setP3_customerNumber(mchId);
		HeLiBaoPay heLiBaoPay = new HeLiBaoPay();
		try {
			Thread.sleep(1000);
			AppPayRefundQueryResponseVo responseVo =  heLiBaoPay.toRefundQuery(queryVo,keys);
			if(responseVo.getRt2_retCode().equals("0000") && (responseVo.getRt8_orderStatus().equals("SUCCESS") ||responseVo.getRt8_orderStatus().equals("DOING") )) {
				ns.setStatus(1);
				ns.setMsg("退款成功!");
				return ns;
			}else if(responseVo.getRt2_retCode().equals("0000")) {
				//继续查询
				return queryHeLiBaoReturnResult(retListUnique,mchId,keys,--count);
			}
		}catch (Exception e) {
			e.printStackTrace();
		}
		
		return ns;
	}
	/**
	 * 查询退款订单的申请信息
	 * @param shopUnique 店铺编号
	 * @param page 页码
	 * @param limit 单页查询数量
	 * @param startTime 开始查询时间
	 * @param endTime 结束查询时间
	 * @return
	 */
	public ShopsResult queryRetLists(String shopUnique, Integer page, Integer limit, String startTime, String endTime, Integer retListHandlestate) {
		ShopsResult sr = new ShopsResult(1,"查询成功!");
		
		Map<String,Object> map = new HashMap<String,Object>();
		map.put("startNum", (page - 1) * limit);
		map.put("pageSize", limit);
		map.put("shopUnique", shopUnique);
		map.put("startTime", startTime);
		map.put("endTime",  endTime);
		map.put("retListHandlestate", retListHandlestate);
		
		List<Map<String,Object>> list = saleDao.queryRetLists(map);
		Integer count = saleDao.queryRetListsCount(map);
		sr.setData(list);
		sr.setCount(count);
		
		Map<String,Object> retMap = saleDao.queryRetStatistics(map);
		if(null == retMap) {
			retMap = new HashMap<String,Object>();
			retMap.put("orderCount", 0);
			retMap.put("retTotal", 0.0);
		}
		
		List<Map<String,Object>> payList = saleDao.queryRetStatisticsDetail(map);
		retMap.put("payList", payList);
		sr.setCord(retMap);
		
		return sr;
	}
	
	public List<SaleListMain> saleListExcelDetailClass(Map<String,Object> map){
		return saleDao.saleListExcelDetailClass(map);
	}
	/**
	 * 查询订单详情
	 * @param shop_unique
	 * @param sale_list_unique
	 * @return
	 */
	public ShopsResult queryOrderDetailJY(String shop_unique,String sale_list_unique,Integer loanStatus) {
		ShopsResult shop=new ShopsResult();
		Map<String,Object> map=new HashMap<String,Object>();
		map.put("sale_list_unique", sale_list_unique);
		map.put("shop_unique", shop_unique);
		map.put("loanStatus", loanStatus);
		Map<String, Object> detail=saleDao.queryOrderDetailJY(map);
		if(detail==null){
			shop.setStatus(1);
			shop.setMsg("订单编号错误，请确认！");
			return shop;
		}
		
		shop.setStatus(1);
		shop.setMsg("订单详情查询成功！");
		shop.setData(detail);
		return shop;
	}
	/**
	 * 分店销售统计
	 */
	public PurResult queryShopOrderList(String manager_unique,String startTime,String endTime,Integer pageNum,Integer pageSize) {
		PurResult result = new PurResult();
		
		Map<String,Object> map = new HashMap<String,Object>();
		map.put("manager_unique", manager_unique);
		map.put("startTime", startTime);
		map.put("endTime", endTime);
//		map.put("startNum", (pageNum-1)*pageSize);
//		map.put("pageSize", pageSize);
		
		List<Map<String,Object>> orderList = saleDao.queryShopOrderList(map);
//		int count = saleDao.queryShopOrderListPage(map);
		int count =orderList.size();
		result.setStatus(1);
		result.setMsg("成功！");
		result.setData(orderList);
		result.setCount(count);
		return result;
	}
	
	/**
	 * 网单查询，去掉了充值统计和会员续费统计功能
	 * @param shop_unique
	 * @param orderMessage
	 * @param sale_list_handlestate
	 * @param startTime
	 * @param endTime
	 * @param sale_list_state
	 * @param orderName
	 * @param orderType
	 * @param pageNum
	 * @param pageSize
	 * @param staffId
	 * @param goodsMessage
	 * @param goods_kind_parunique
	 * @param goods_kind_unique
	 * @param cusType
	 * @param paymentMethod
	 * @return
	 */
	public PurResult querySaleListsNet(String shop_unique, String orderMessage, Integer sale_list_handlestate,
			String startTime, String endTime, Integer sale_list_state, String orderName, String orderType,
			Integer pageNum, Integer pageSize,String staffId,
			 String goodsMessage, String goods_kind_parunique, String goods_kind_unique,Integer cusType,Integer paymentMethod) {
		PurResult result = new PurResult();
		try {
		Map<String,Object> map = new HashMap<String,Object>();
		map.put("shop_unique", shop_unique);
		if(null != cusType && cusType != -1) {
			map.put("cusType", cusType);
		}
		if(null != paymentMethod && paymentMethod != -1) {
			map.put("paymentMethod", paymentMethod);
		}
		
		if(null!=orderMessage&&!"".equals(orderMessage)){
			map.put("orderMessage", "%"+orderMessage+"%");
		}
		if(null!=goodsMessage&&!"".equals(goodsMessage)){
			map.put("goodsMessage", "%"+goodsMessage+"%");
		}
		if(null!=goods_kind_parunique && !"-1".equals(goods_kind_parunique)){
			map.put("goods_kind_parunique", goods_kind_parunique);
		}
		if(null!=goods_kind_unique&&!"-1".equals(goods_kind_unique)){
			map.put("goods_kind_unique", goods_kind_unique);
		}
		map.put("startTime", startTime);
		map.put("endTime", endTime);
		map.put("sale_list_handlestate", sale_list_handlestate);
		map.put("sale_list_state", sale_list_state);
		map.put("staffId", staffId);
		
		Map<String,Object> mp = saleDao.queryOrderTotal(map);
		mp = mp==null?new HashMap<>():mp;
		if(mp!=null && !mp.containsKey("commission_sum")){
			mp.put("commission_sum", 0.00);
		}
		
		map.put("startNum", (pageNum-1)*pageSize);
		map.put("pageSize", pageSize);
		map.put("OrderBy"," "+orderName +" "+orderType);
		List<Map<String,Object>> orderList = saleDao.querySaleLists(map);
		Integer count = saleDao.querySaleListsCount(map);
		
		result.setStatus(1);
		result.setMsg("成功！");
		result.setData(orderList);
		result.setCount(count);
//		result.setCord(mp);
		
		map.clear();
		
		map.put("cashier_id", staffId);
		map.put("staff_id", staffId);
		map.put("shopUnique", shop_unique);
		map.put("startTime", startTime);
		map.put("endTime", endTime);
		map.put("sale_list_handlestate", sale_list_handlestate);
		map.put("sale_list_state", sale_list_state);
		
		if(null != cusType && cusType != -1) {
			map.put("cusType", cusType);
		}
		if(null != paymentMethod && paymentMethod != -1) {
			map.put("paymentMethod", paymentMethod);
		}
		if(null!=orderMessage&&!"".equals(orderMessage)){
			map.put("orderMessage", "%"+orderMessage+"%");
		}
		
		System.out.println(map);
		Map<String,Object> onlineList = saleDao.payTypeStatisticsOnline(map);
		//退款统计
		List<Map<String,Object>> retPayList = saleDao.getReturnListMsg(map);
		retPayList = retPayList==null?new ArrayList<>():retPayList;
		System.out.println(mp == null);
		mp.put("retPayList", retPayList==null?new ArrayList<>():retPayList);
		Double retMoney = 0.0;
		if(null != retPayList && !retPayList.isEmpty()) {
			for(int i =0 ;i<retPayList.size() ;i ++) {
				retMoney = UtilForJAVA.addDouble(retMoney, new Double(retPayList.get(i).get("payment_total").toString()));
			}
		}
		mp.put("retMoney", retMoney);
		result.setCord(mp);
	} catch (MyException e) {
		return new PurResult(e.status, e.msg);
	}
		return result;
	}
	

	/**
	 * 查询满足条件的商品订单
	 */
	public PurResult querySaleLists(String shop_unique, String orderMessage, Integer sale_list_handlestate,
			String startTime, String endTime, Integer sale_list_state, String orderName, String orderType,
			Integer pageNum, Integer pageSize,String staffId,
			 String goodsMessage, String goods_kind_parunique, String goods_kind_unique,Integer cusType,Integer paymentMethod) {
		
		PurResult result = new PurResult();
		Map<String,Object> map = new HashMap<String,Object>();
		map.put("shop_unique", shop_unique);
		if(null != cusType && cusType != -1) {
			map.put("cusType", cusType);
		}
		if(null != paymentMethod && paymentMethod != -1) {
			map.put("paymentMethod", paymentMethod);
		}
		
		if(null!=orderMessage&&!"".equals(orderMessage)){
			map.put("orderMessage", "%"+orderMessage+"%");
		}
		if(null!=goodsMessage&&!"".equals(goodsMessage)){
			map.put("goodsMessage", "%"+goodsMessage+"%");
		}
		if(null!=goods_kind_parunique && !"-1".equals(goods_kind_parunique)){
			map.put("goods_kind_parunique", goods_kind_parunique);
		}
		if(null!=goods_kind_unique&&!"-1".equals(goods_kind_unique)){
			map.put("goods_kind_unique", goods_kind_unique);
		}
		map.put("startTime", startTime);
		map.put("endTime", endTime);
		map.put("sale_list_handlestate", sale_list_handlestate);
		map.put("sale_list_state", sale_list_state);
		map.put("staffId", staffId);
		
		Map<String,Object> mp = saleDao.queryOrderTotal(map);
		mp = mp==null?new HashMap<>():mp;
		if(mp!=null && !mp.containsKey("commission_sum")){
			mp.put("commission_sum", 0.00);
		}
		
		map.put("startNum", (pageNum-1)*pageSize);
		map.put("pageSize", pageSize);
		map.put("OrderBy"," "+orderName +" "+orderType);
		List<Map<String,Object>> orderList = saleDao.querySaleLists(map);
		Integer count = saleDao.querySaleListsCount(map);
		
		result.setStatus(1);
		result.setMsg("成功！");
		result.setData(orderList);
		result.setCount(count);
//		result.setCord(mp);
		
		map.clear();
		
		map.put("cashier_id", staffId);
		map.put("staff_id", staffId);
		map.put("shopUnique", shop_unique);
		map.put("startTime", startTime);
		map.put("endTime", endTime);
		map.put("sale_list_handlestate", sale_list_handlestate);
		map.put("sale_list_state", sale_list_state);
		
		if(null != cusType && cusType != -1) {
			map.put("cusType", cusType);
		}
		if(null != paymentMethod && paymentMethod != -1) {
			map.put("paymentMethod", paymentMethod);
		}
		if(null!=orderMessage&&!"".equals(orderMessage)){
			map.put("orderMessage", "%"+orderMessage+"%");
		}
		
		System.out.println(map);
		/**
		 * 线下收入统计
		 */
		List<Map<String,Object>> plist = saleDao.payTypeStatistics(map);
		if(null == plist ) {
			plist = new ArrayList<>();
		}
		Map<String,Object> onlineList = saleDao.payTypeStatisticsOnline(map);
		List<Map<String,Object>> payList = saleDao.payTypeStatistics(map);//各支付状态的收银状况集合
		if(null == payList) {
			payList = new ArrayList<>();
		}
		//退款统计
		List<Map<String,Object>> retPayList = saleDao.getReturnListMsg(map);
//		payList.addAll(retPayList);
		retPayList = retPayList==null?new ArrayList<>():retPayList;
		System.out.println(mp == null);
		mp.put("retPayList", retPayList==null?new ArrayList<>():retPayList);
		Double retMoney = 0.0;
		BigDecimal onlineMoney = BigDecimal.ZERO;
		if(null != retPayList && !retPayList.isEmpty()) {
			for(int i =0 ;i<retPayList.size() ;i ++) {
				retMoney = UtilForJAVA.addDouble(retMoney, new Double(retPayList.get(i).get("payment_total").toString()));
			}
		}
		mp.put("retMoney", retMoney);
		if(null == onlineList || null == onlineList.get("beansGet")) {
			Map<String,Object> m = new HashMap<String,Object>();
//			m.put("staffId", Integer.parseInt(map.get("staff_id").toString()));
			m.put("payment_total", 0);
			m.put("pay_ment", "百货豆赠送");
			payList.add(m);
		}else {
			Map<String,Object> m = new HashMap<String,Object>();
//			m.put("staffId", Integer.parseInt(map.get("staff_id").toString()));
			m.put("payment_total", onlineList.get("beansGet"));
			m.put("pay_ment", "百货豆赠送");
			payList.add(m);
		}
		if(null == onlineList || null == onlineList.get("onLineReceived")) {
			Map<String,Object> m = new HashMap<String,Object>();
//			m.put("staffId", Integer.parseInt(map.get("staff_id").toString()));
			m.put("payment_total", 0);
			m.put("pay_ment", "线上收入");
			payList.add(m);
		}else {
			Map<String,Object> m = new HashMap<String,Object>();
			Double payment_total = UtilForJAVA.addDouble(new Double(onlineList.get("onLineReceived").toString()),new Double(onlineList.get("couponAmount").toString()));
			m.put("payment_total", payment_total);
			m.put("pay_ment", "线上收入");
			payList.add(m);
			onlineMoney = BigDecimal.valueOf(payment_total);
		}
		if(null == onlineList || null == onlineList.get("beansUse")) {
			Map<String,Object> m = new HashMap<String,Object>();
//			m.put("staffId", Integer.parseInt(map.get("staff_id").toString()));
			m.put("payment_total", 0);
			m.put("pay_ment", "百货豆抵扣");
			payList.add(m);
		}else {
			Map<String,Object> m = new HashMap<String,Object>();
//			m.put("staffId", Integer.parseInt(map.get("staff_id").toString()));
			m.put("payment_total", onlineList.get("beansUse"));
			m.put("pay_ment", "百货豆抵扣");
			payList.add(m);
		}
		
	
		mp.put("payList", payList);
		
		
		//会员充值记录
		List<Map<String,Object>> list=saleDao.queryCustmerRechargeStatistics(map);
		
		if(null==list||list.isEmpty()||list.size()<4){
			List<String> cpl = new ArrayList<>();
			cpl.add("现金");
			cpl.add("微信");
			cpl.add("支付宝");
			cpl.add("存零");
			
			//返回的数组不包含数据
			if(null==list){
				list = new ArrayList<>();
			}
			for(String key:cpl){
				boolean flag = false;
				for(int i=0;i<list.size();i++){
					if(list.get(i).get("rechargeMethod") != null && list.get(i).get("rechargeMethod").toString().equals(key)){
						flag=true;
						break;
					}
				}
				if(!flag){
					Map<String,Object> m = new HashMap<String,Object>();
					m.put("rechargeCode", 0);
					m.put("rechargeMethod", key);
					m.put("rechargeMoney", 0);
					list.add(m);
				}
			}
		}
		mp.put("rechargeList", list);
		
		//查询会员续费统计
		List<Map<String, Object>> cusRenewList= saleDao.queryCusRenewCount(map);
		mp.put("cusRenewList", cusRenewList);
		// 查询店铺手续费
		ShopVO shopVO = shopDao.getSysRate(shop_unique);
		BigDecimal totalPaymentFee = BigDecimal.ZERO;
		if (ObjectUtil.isNotNull(shopVO)) {
			// 1、金圈平台
			for (Map<String, Object> m : payList) {
				String payType = (String) m.get("pay_ment");
				BigDecimal payment_total = new BigDecimal(m.getOrDefault("payment_total", BigDecimal.ZERO).toString());
				if (StrUtil.equals("金圈平台", payType)) {
					BigDecimal tpf = NumberUtil.mul(shopVO.getRate1(), payment_total);
					if (tpf.compareTo(BigDecimal.ZERO) > 0) {
						totalPaymentFee = NumberUtil.add(totalPaymentFee, tpf.divide(BigDecimal.valueOf(1000), 2, BigDecimal.ROUND_HALF_UP));
					}

				}
			}
			// 2、线上小程序
			BigDecimal fee = NumberUtil.mul(shopVO.getRate(), onlineMoney);
			if (fee.compareTo(BigDecimal.ZERO) > 0) {
				totalPaymentFee = NumberUtil.add(totalPaymentFee, fee.divide(BigDecimal.valueOf(1000), 2, BigDecimal.ROUND_HALF_UP));
			}
		}
		mp.put("totalPaymentFee", totalPaymentFee);
		result.setCord(mp);
		return result;
	}
	
	public List<Map<String,Object>> querySaleLists2(String shop_unique, String orderMessage, Integer sale_list_handlestate,
			String startTime, String endTime, Integer sale_list_state, String orderName, String orderType,
			Integer staffId,String goodsMessage, String goods_kind_parunique, String goods_kind_unique) {
		
		
		Map<String,Object> map = new HashMap<String,Object>();
		map.put("shop_unique", shop_unique);
		if(null!=orderMessage&&!"".equals(orderMessage)){
			map.put("orderMessage", "%"+orderMessage+"%");
		}
		if(null!=goodsMessage&&!"".equals(goodsMessage)){
			map.put("goodsMessage", "%"+goodsMessage+"%");
		}
		if(null!=goods_kind_parunique && !"-1".equals(goods_kind_parunique)){
			map.put("goods_kind_parunique", goods_kind_parunique);
		}
		if(null!=goods_kind_unique&&!"-1".equals(goods_kind_unique)){
			map.put("goods_kind_unique", goods_kind_unique);
		}
		map.put("startTime", startTime);
		map.put("endTime", endTime);
		map.put("sale_list_handlestate", sale_list_handlestate);
		map.put("sale_list_state", sale_list_state);
		map.put("staffId", staffId);
		
		Map<String,Object> mp = saleDao.queryOrderTotal(map);
		mp = mp==null?new HashMap<>():mp;
		if(mp!=null && !mp.containsKey("commission_sum")){
			mp.put("commission_sum", 0.00);
		}
		
		map.put("OrderBy"," "+orderName +" "+orderType);
		List<Map<String,Object>> orderList = saleDao.querySaleLists(map);
		return orderList;
	}
	
	/**
	 * 查询订单详情
	 * @param shop_unique
	 * @param sale_list_unique
	 * @return
	 */
	public ShopsResult queryOrderDetail(String shop_unique,String sale_list_unique,Integer loanStatus){
		ShopsResult shop=new ShopsResult();
		Map<String,Object> map=new HashMap<String,Object>();
		map.put("sale_list_unique", sale_list_unique);
		map.put("shop_unique", shop_unique);
		map.put("loanStatus", loanStatus);
		SaleListMain detail=saleDao.queryOrderDetail(map);
		if(detail==null){
			shop.setStatus(1);
			shop.setMsg("订单编号错误，请确认！");
			return shop;
		}
		detail.setSourceType(SourceTypeEnums.getSourceTypeName(detail.getSource_type()));
		//查询订单核实商品信息
		List<Map<String ,Object>> saleListVerifyList = saleDao.getSaleListVerifyList(map);
		detail.setSaleListVerifyList(saleListVerifyList);
		
		//查询该订单分销佣金
		Double commission = disDao.queryCommissionBySaleListUnique(sale_list_unique);
		detail.setCommission(commission);

		ShopsConfig shopsConfig = shopsConfigDao.selectByShopUnique(shop_unique);
		if (ObjectUtil.isNotNull(shopsConfig) && ObjectUtil.equals(GoodsInPriceTypeEnums.BATCH.getCode(), shopsConfig.getGoodsInPriceType())) {
			List<GoodsSaleBatchData> goodsSaleBatchDataList = goodsSaleBatchMapper.selectSaleBatchList(Long.parseLong(shop_unique), sale_list_unique);
			detail.setGoodsSaleBatchList(goodsSaleBatchDataList);
		}
		shop.setStatus(0);
		shop.setMsg("订单详情查询成功！");
		shop.setData(detail);
		return shop;
	}
	
	
	/**
	 * 查询平台益农订单详情
	 * @param shop_unique
	 * @param sale_list_unique
	 * @return
	 */
	public ShopsResult queryFarmOrderDetail(String secretary_id,String sup_order_unique){
		ShopsResult shop=new ShopsResult();
		Map<String,Object> map=new HashMap<String,Object>();
		map.put("sup_order_unique", sup_order_unique);
		map.put("secretary_id", secretary_id);
		System.out.println(sup_order_unique);
		Map<String, Object> detail=farmProductDao.queryFarmOrderDetail(map);
		if(detail==null){
			shop.setStatus(1);
			shop.setMsg("订单编号错误，请确认！");
			return shop;
		}
		//查询订单核实商品信息
//		List<Map<String ,Object>> saleListVerifyList = saleDao.getSaleListVerifyList(map);
//		detail.setSaleListVerifyList(saleListVerifyList);
		
		shop.setStatus(0);
		shop.setMsg("订单详情查询成功！");
		shop.setData(detail);
		return shop;
	}
	
	/**
	 * 更新订单处理状态
	 * @param shop_unique
	 * @param sale_list_unique
	 * @param sale_list_handlestate
	 * @param sale_list_state
	 * @return
	 */
	@Transactional
	public ShopsResult updateSaleList(String shop_unique,String sale_list_unique,Integer sale_list_handlestate,Integer sale_list_state){
		ShopsResult shop=new ShopsResult();
		Map<String,Object> map=new HashMap<String,Object>();
		if(null==shop_unique){
			shop.setStatus(2);
			shop.setMsg("登录超时！请重新登录！");
			return shop;
		}
		if(null==sale_list_unique){
			shop.setStatus(1);
			shop.setMsg("订单编号不能为空！");
			return shop;
		}
		map.put("sale_list_unique", sale_list_unique);
		map.put("shop_unique", shop_unique);
		map.put("sale_list_handlestate", sale_list_handlestate);
		map.put("sale_list_state", sale_list_state);
		int k=saleDao.updateSaleList(map);
		if(k==0){
			shop.setStatus(1);
			shop.setMsg("更新失败！请检查订单号");
			return shop;
		}
		shop.setStatus(0);
		shop.setMsg("更新成功！");
		return shop;
	}
	/**
	 * 主界面查询基本信息
	 * @param shop_unique
	 * @return
	 */
	
	public ShopsResult baseMessage(String shop_unique){
		ShopsResult shop=new ShopsResult();
		Map<String,Object> map=new HashMap<String, Object>();
		Map<String,Object> reMap=new HashMap<String, Object>();
		map.put("shop_unique", shop_unique);
		Calendar calendar=Calendar.getInstance();
		calendar.set(Calendar.HOUR_OF_DAY, 23);
		calendar.set(Calendar.MINUTE, 59);
		calendar.set(Calendar.SECOND, 59);
		Timestamp endTime=new Timestamp(calendar.getTimeInMillis());//截至时间
		long timeEnd=calendar.getTimeInMillis();
		calendar.add(Calendar.DATE, -1);
		Timestamp startTime=new Timestamp(calendar.getTimeInMillis());//起始时间
		map.put("startTime", startTime);
		map.put("endTime", endTime);
//		System.out.println(saleDao.testT(map));
		
//		Map<String,Object> test=saleDao.querySaleMessage(map);
//		System.out.println(test);
		reMap=saleDao.querySaleMessage(map);
		map.put("sale_list_handlestate", 2);
		reMap.put("nListCount", saleDao.querySaleMessage(map).get("listCount"));
		
		calendar.add(Calendar.MONTH, -1);
		calendar.add(Calendar.DATE, 1);
		long timeStart=calendar.getTimeInMillis();
		long days=(timeEnd-timeStart)/3600/1000/24;
		map.put("days", days);
		List<Map<String,Object>> result=saleDao.warningCount(map);
		for(int i=0;i<result.size();i++){
			reMap.put(result.get(i).get("averDays").toString(), result.get(i).get("count"));
		}
		shop.setStatus(0);
		shop.setMsg("查询成功！");
		shop.setData(reMap);
		return shop;
	}
	
	/**
	 * 店铺营运信息查询
	 * @param map
	 * @return
	 */
	public ShopsResult salesTurnoverStatistics(Map<String,Object> map){
		ShopsResult sr=new ShopsResult();
		List<Map<String,Object>> data=saleDao.salesTurnoverStatistics(map); 
		if(null==data||data.isEmpty()){
			sr.setStatus(2);
			sr.setMsg("没有满足条件的信息");
			return sr;
		}
		sr.setData(data);
		sr.setStatus(1);
		sr.setMsg("查询成功！");
		return sr;
	}
	
	/**
	 * 查询各月内，各分类销售情况汇总
	 * @param map
	 * @return
	 */
	public ShopResult typeSaleByTime(Map<String,Object> map){
		ShopResult sr=new ShopResult();
		List<Map<String,Object>> data=saleDao.typeSaleByTime(map);
		if(null==data||data.isEmpty()){
			sr.setStatus(2);
			sr.setMessage("没有满足条件的销售信息！");
			return sr;
		}
		sr.setData(data);
		sr.setStatus(1);
		sr.setMessage("查询成功!");
		return sr;
	}
	
	/**
	 * 各分数的订单数量统计
	 * @param map
	 * @return
	 */
	public ShopsResult saleListEvaluateQuery(Map<String,Object> map){
		ShopsResult sr=new ShopsResult();
		List<List<Map<String,Object>>> datas=new ArrayList<List<Map<String,Object>>>();
		Integer[] days={7,30,180,10000};
		for(int i=0;i<days.length;i++){
			map.put("days", days[i]);
			List<Map<String,Object>> data=saleDao.saleListEvaluateQuery(map);
			datas.add(data);
		}
		sr.setData(datas);
		sr.setStatus(1);
		sr.setMsg("查询成功！");
		return sr;
	}
	/**
	 * 用户活跃度查询
	 * @param map
	 * @return
	 */
	public ShopsResult cusActivityQuery(Map<String,Object> map){
		ShopsResult sr=new ShopsResult();
		List<Map<String,Object>> data=saleDao.cusActivityQuery(map);
		if(null==data||data.isEmpty()){
			sr.setStatus(2);
			sr.setMsg("没有满足条件的信息！");
			return sr;
		}
		sr.setStatus(1);
		sr.setMsg("查询成功！");
		sr.setData(data);
		return sr;
	}
	/**
	 * 订单信息数量查询
	 * @param map
	 * @return
	 */
	public ShopsResult newOrdersCount(Map<String,Object> map){
		ShopsResult sr=new ShopsResult();
		int k=saleDao.newOrdersCount(map);
		System.out.println("新订单数量：：：："+k);
		sr.setData(k);
		sr.setStatus(1);
		sr.setMsg("信息查询成功！");
		return sr;
	}

	/**
	 * 订单详情EXCEL表生成并返回下载地址
	 * @param map
	 * @return
	 */
	public List<Map<String, Object>> saleListExcel(Map<String,Object> map,HttpServletRequest request,Long shopUnique){
		map.put("OrderBy", " sale_list_datetime desc ");
		List<Map<String, Object>> data=saleDao.querySaleListsForExcel(map);
		for(int i=0;i<data.size();i++){
			Object sourceType = data.get(i).get("source_type");
			Integer sourceTypeInt = 0;
			String sourceTypeName = null;
			if (ObjectUtil.isNotNull(sourceType)) {
				sourceTypeInt = Integer.parseInt(sourceType.toString());
			}
			sourceTypeName = SourceTypeEnums.getSourceTypeName(sourceTypeInt);
			data.get(i).put("source_type", sourceTypeName);
		}
		
		return data;
	}
	
	
	/**
	 * 订单详情EXCEL表生成并返回下载地址-农产品
	 * @param map
	 * @return
	 */
	public List<Map<String, Object>> farmListExcel(Map<String,Object> map,HttpServletRequest request){
		map.put("OrderBy", " fo.sale_list_datetime desc ");
		List<Map<String, Object>> data=farmProductDao.queryFarmOrderDetailD(map);
		
		return data;
	}
	
	
	/**
	 * 订单详情EXCEL表生成并返回下载地址newOrdersCount
	 * @param map
	 * @return
	 */
	public List<Map<String, Object>> saleListNYExcel(Map<String,Object> map){
		
		List<Map<String, Object>> data=saleDao.querySaleListsNYForExcel(map);
		
		return data;
	}
	/**
	 * 订单详情EXCEL表生成并返回下载地址newOrdersCount
	 * @param map
	 * @return
	 */
	public List<Map<String, Object>> saleListDetailYNExcel(Map<String,Object> map){
		
		List<Map<String, Object>> data=saleDao.saleListDetailYNExcel(map);
		
		return data;
	}	
	/**
	 * 订票订单总览界面：页数查询
	 * @param map
	 * @return
	 */
	public ShopsResult queryListPages(Map<String,Object> map){
		ShopsResult sr=new ShopsResult();
		int k=saleDao.queryListPages(map);
		sr.setStatus(1);
		sr.setMsg("查询成功！");
		sr.setData(k);
		return sr;
	}
	
	/**
	 * 店铺订单总览界面：分页查询订单
	 * @param map
	 * @return
	 */
	public ShopsResult queryListByPage(Map<String,Object> map){
		ShopsResult sr=new ShopsResult();
		List<Map<String,Object>> data=saleDao.queryListByPage(map);
		if(null==data||data.isEmpty()){
			sr.setStatus(2);
			sr.setMsg("没有满足条件的信息！");
			return sr;
		}
		sr.setStatus(1);
		sr.setMsg("查询成功！");
		sr.setData(data);
		return sr;
	}
	
	/**
	 * 订单详情查询
	 * @param map
	 * @return
	 */
	public ShopsResult queryListDetail(Map<String,Object> map){
		ShopsResult sr=new ShopsResult();
		List<Map<String,Object>> data=saleDao.queryListDetail(map);
		if(null==data||data.isEmpty()){
			sr.setStatus(2);
			sr.setMsg("订单编号错误");
			return sr;
		}
		sr.setStatus(1);
		sr.setMsg("查询成功！");
		sr.setData(data);
		return sr;
	}


	public ShopsResult printOrderList(Map<String, Object> map, HttpServletRequest request, Long shopUnique) {
		ShopsResult sr=new ShopsResult();
		List<Map<String, Object>> data=saleDao.querySaleLists(map);
		if(null==data||data.isEmpty()){
				sr.setStatus(2);
				sr.setMsg("没有满足条件的订单信息！");
				return sr;
		}
		sr.setStatus(1);
		sr.setMsg("查询成功！");
		sr.setData(data);//返回文件保存路径？？？
		return sr;
	}


	public Map<String, Object> queryOrderTotal(Map<String, Object> map) {
		Map<String,Object> mp=saleDao.queryOrderTotal(map);
		if(mp!=null&&!mp.containsKey("commission_sum")){
			mp.put("commission_sum", 0.00);
		}else if(mp==null){
			mp=new HashMap<String, Object>();
		}
		return mp;
	}
	/**
	 * 查询所有满足条件的订单信息及详情
	 */
	public List<Map<String,Object>> saleListExcelDetail(Map<String,Object> map){
//		List<SaleListMain> list=saleDao.saleListExcel(map);
		List<Map<String,Object>> list=saleDao.saleListExcelDetail(map);
		for(int i=0;i<list.size();i++){
			list.get(i).put("sale_list_unique", list.get(i).get("sale_list_unique").toString());
		}
		return list;
	}
	
	public PurResult getEvaluateList(Map<String, Object> params) {
		PurResult result=new PurResult();			
		List<Map<String,Object>> evaluateList=saleDao.getEvaluateList(params);
		Integer count = saleDao.getEvaluateListPage(params);
		result.setStatus(1);
		result.setMsg("查询成功");
		result.setCount(count);
		result.setData(evaluateList);
		return result;
	}

	@Transactional
	public ShopsResult updateEvaluate(Map<String, Object> map) {
		ShopsResult shop=new ShopsResult();
		map.put("replay_time", new Date());
		int k=saleDao.updateEvaluate(map);
		if(k==0){
			shop.setStatus(1);
			shop.setMsg("更新失败！请检查订单号");
			return shop;
		}
		shop.setStatus(0);
		shop.setMsg("更新成功！");
		return shop;
	}
	
	public List<Map<String,Object>> getEvaluateImage(Map<String,Object> map){
		return saleDao.getEvaluateImage(map);
	}

	public PurResult querySaleListsNY(Map<String, Object> map) {
		PurResult result=new PurResult();			
		List<Map<String,Object>> evaluateList=saleDao.querySaleListsNY(map);
		Integer count = saleDao.querySaleListsNYCount(map);
		result.setStatus(1);
		result.setMsg("查询成功");
		result.setCount(count);
		result.setData(evaluateList);
		return result;
	}
	
	/**
	 * 宁宇销售订单
	 */
	public PurResult querySaleNYLists(String shop_unique, String orderMessage, Integer sale_list_handlestate,
			String startTime, String endTime, Integer sale_list_state, String orderName, String orderType,
			Integer pageNum, Integer pageSize,String staffId,
			 String goodsMessage, String goods_kind_parunique, String goods_kind_unique,Integer cusType,Integer paymentMethod, Integer sourceType) {
		
		PurResult result = new PurResult();
		Map<String,Object> map = new HashMap<String,Object>();
		map.put("shop_unique", shop_unique);
		if(null != cusType && cusType != -1) {
			map.put("cusType", cusType);
		}
		if(null != paymentMethod && paymentMethod != -1) {
			map.put("paymentMethod", paymentMethod);
		}

		if (ObjectUtil.isNotNull(sourceType) && sourceType != -1) {
			map.put("sourceType", sourceType);
		}

		if(null!=orderMessage&&!"".equals(orderMessage)){
			map.put("orderMessage", "%"+orderMessage+"%");
		}
		if(null!=goodsMessage&&!"".equals(goodsMessage)){
			map.put("goodsMessage", "%"+goodsMessage+"%");
		}
		if(null!=goods_kind_parunique && !"-1".equals(goods_kind_parunique)){
			map.put("goods_kind_parunique", goods_kind_parunique);
		}
		if(null!=goods_kind_unique&&!"-1".equals(goods_kind_unique)){
			map.put("goods_kind_unique", goods_kind_unique);
		}
		map.put("startTime", startTime);
		map.put("endTime", endTime);
		map.put("sale_list_handlestate", sale_list_handlestate);
		map.put("sale_list_state", sale_list_state);
		map.put("staffId", staffId);
		
		Map<String,Object> mp = saleDao.queryOrderTotal(map);
//		Map<String,Object> mm = saleDao.queryOrderTotalNew(map);
//		System.out.println(mm);
		mp = mp==null?new HashMap<>():mp;
		if(mp!=null && !mp.containsKey("commission_sum")){
			mp.put("commission_sum", 0.00);
		}
		
		map.put("startNum", (pageNum-1)*pageSize);
		map.put("pageSize", pageSize);
		map.put("OrderBy"," "+orderName +" "+orderType);
		System.out.println(mp);
		List<Map<String,Object>> orderList = saleDao.querySaleNYLists(map);
		
		result.setStatus(1);
		result.setMsg("成功！");
		result.setData(orderList);
		result.setCount(Integer.valueOf(String.valueOf(mp.get("orderCount") == null ? 0 : mp.get("orderCount")) ));
		result.setCord(mp);
		return result;
	}
	
	
	/**
	 * 查询满足条件的商品订单
	 */
	public PurResult queryFarmListsAll(String orderMessage,Integer handle_status,String startTime,
			String endTime,Integer secretary_id,Integer pay_status,String orderName,String orderType,Integer pageNum,Integer pageSize,Integer staffId, 
			String goodsMessage, String goods_kind_parunique, String goods_kind_unique,Integer cusType,Integer paymentMethod) {
		
		PurResult result = new PurResult();
		Map<String,Object> map = new HashMap<String,Object>();
//		map.put("shop_unique", shop_unique);
		if(null != cusType && cusType != -1) {
			map.put("cusType", cusType);
		}
		if(null != paymentMethod && paymentMethod != -1) {
			map.put("paymentMethod", paymentMethod);
		}
		
		if(null!=orderMessage&&!"".equals(orderMessage)){
			map.put("orderMessage", "%"+orderMessage+"%");
		}
		if(null!=goodsMessage&&!"".equals(goodsMessage)){
			map.put("goodsMessage", "%"+goodsMessage+"%");
		}
		if(null!=goods_kind_parunique && !"-1".equals(goods_kind_parunique)){
			map.put("goods_kind_parunique", goods_kind_parunique);
		}
		if(null!=goods_kind_unique&&!"-1".equals(goods_kind_unique)){
			map.put("goods_kind_unique", goods_kind_unique);
		}
		map.put("startTime", startTime);
		map.put("endTime", endTime);
		map.put("handle_status", handle_status);
		map.put("secretary_id", secretary_id);
		map.put("pay_status", pay_status);
		map.put("staffId", staffId);
		
//		Map<String,Object> mp = saleDao.queryOrderTotal(map);
//		mp = mp==null?new HashMap<>():mp;
//		if(mp!=null && !mp.containsKey("commission_sum")){
//			mp.put("commission_sum", 0.00);
//		}
		
		map.put("startNum", (pageNum-1)*pageSize);
		map.put("pageSize", pageSize);
		map.put("OrderBy"," "+orderName +" "+orderType);
		List<Map<String,Object>> orderList = farmProductDao.queryFarmOrderListAll(map);
		Integer count = farmProductDao.queryFarmOrderListAllCount(map);
		
		result.setStatus(1);
		result.setMsg("成功！");
		result.setData(orderList);
		result.setCount(count);
//		result.setCord(mp);
		
//		map.clear();
//		
//		map.put("cashier_id", staffId);
//		map.put("staff_id", staffId);
////		map.put("shopUnique", shop_unique);
//		map.put("startTime", startTime);
//		map.put("endTime", endTime);
//		map.put("handle_status", handle_status);
//		map.put("secretary_id", secretary_id);
//		map.put("pay_status", pay_status);
//		
//		if(null != cusType && cusType != -1) {
//			map.put("cusType", cusType);
//		}
//		if(null != paymentMethod && paymentMethod != -1) {
//			map.put("paymentMethod", paymentMethod);
//		}
//		if(null!=orderMessage&&!"".equals(orderMessage)){
//			map.put("orderMessage", "%"+orderMessage+"%");
//		}
//		
//		System.out.println(map);
//		/**
//		 * 线下收入统计
//		 */
//		List<Map<String,Object>> plist = saleDao.payTypeStatistics(map);
//		if(null == plist ) {
//			plist = new ArrayList<>();
//		}
//		Map<String,Object> onlineList = saleDao.payTypeStatisticsOnline(map);
//		List<Map<String,Object>> payList = saleDao.payTypeStatistics(map);//各支付状态的收银状况集合
//		if(null == payList) {
//			payList = new ArrayList<>();
//		}
//		//退款统计
//		List<Map<String,Object>> retPayList = saleDao.getReturnListMsg(map);
////		payList.addAll(retPayList);
//		retPayList = retPayList==null?new ArrayList<>():retPayList;
//		System.out.println(mp == null);
//		mp.put("retPayList", retPayList==null?new ArrayList<>():retPayList);
//		Double retMoney = 0.0;
//		if(null != retPayList && !retPayList.isEmpty()) {
//			for(int i =0 ;i<retPayList.size() ;i ++) {
//				retMoney = UtilForJAVA.addDouble(retMoney, new Double(retPayList.get(i).get("payment_total").toString()));
//			}
//		}
//		mp.put("retMoney", retMoney);
//		if(null == onlineList || null == onlineList.get("beansGet")) {
//			Map<String,Object> m = new HashMap<String,Object>();
////			m.put("staffId", Integer.parseInt(map.get("staff_id").toString()));
//			m.put("payment_total", 0);
//			m.put("pay_ment", "百货豆赠送");
//			payList.add(m);
//		}else {
//			Map<String,Object> m = new HashMap<String,Object>();
////			m.put("staffId", Integer.parseInt(map.get("staff_id").toString()));
//			m.put("payment_total", onlineList.get("beansGet"));
//			m.put("pay_ment", "百货豆赠送");
//			payList.add(m);
//		}
//		if(null == onlineList || null == onlineList.get("onLineReceived")) {
//			Map<String,Object> m = new HashMap<String,Object>();
////			m.put("staffId", Integer.parseInt(map.get("staff_id").toString()));
//			m.put("payment_total", 0);
//			m.put("pay_ment", "线上收入");
//			payList.add(m);
//		}else {
//			Map<String,Object> m = new HashMap<String,Object>();
//			
//			m.put("payment_total", UtilForJAVA.addDouble(new Double(onlineList.get("onLineReceived").toString()),new Double(onlineList.get("couponAmount").toString())));
//			m.put("pay_ment", "线上收入");
//			payList.add(m);
//		}
//		if(null == onlineList || null == onlineList.get("beansUse")) {
//			Map<String,Object> m = new HashMap<String,Object>();
////			m.put("staffId", Integer.parseInt(map.get("staff_id").toString()));
//			m.put("payment_total", 0);
//			m.put("pay_ment", "百货豆抵扣");
//			payList.add(m);
//		}else {
//			Map<String,Object> m = new HashMap<String,Object>();
////			m.put("staffId", Integer.parseInt(map.get("staff_id").toString()));
//			m.put("payment_total", onlineList.get("beansUse"));
//			m.put("pay_ment", "百货豆抵扣");
//			payList.add(m);
//		}
//		/*if(null == onlineList || null == onlineList.get("red_use")) {
//			Map<String,Object> m = new HashMap<String,Object>();
//			m.put("payment_total", 0);
//			m.put("pay_ment", "红包抵扣");
//			payList.add(m);
//		}else {
//			Map<String,Object> m = new HashMap<String,Object>();
//			m.put("payment_total", onlineList.get("red_use"));
//			m.put("pay_ment", "红包抵扣");
//			payList.add(m);
//		}*/
//		//退款信息统计
//		
//	
//		mp.put("payList", payList);
//		
//		
//		//会员充值记录
//		List<Map<String,Object>> list=saleDao.queryCustmerRechargeStatistics(map);
//		
//		if(null==list||list.isEmpty()||list.size()<4){
//			List<String> cpl = new ArrayList<>();
//			cpl.add("现金");
//			cpl.add("微信");
//			cpl.add("支付宝");
//			cpl.add("存零");
//			
//			//返回的数组不包含数据
//			if(null==list){
//				list = new ArrayList<>();
//			}
//			for(String key:cpl){
//				boolean flag = false;
//				for(int i=0;i<list.size();i++){
//					if(list.get(i).get("rechargeMethod") != null && list.get(i).get("rechargeMethod").toString().equals(key)){
//						flag=true;
//						break;
//					}
//				}
//				if(!flag){
//					Map<String,Object> m = new HashMap<String,Object>();
//					m.put("rechargeCode", 0);
//					m.put("rechargeMethod", key);
//					m.put("rechargeMoney", 0);
//					list.add(m);
//				}
//			}
//		}
//		mp.put("rechargeList", list);
//		
//		//查询会员续费统计
//		List<Map<String, Object>> cusRenewList= saleDao.queryCusRenewCount(map);
//		mp.put("cusRenewList", cusRenewList);
//		result.setCord(mp);
		return result;
	}
	
	
	public ShopsResult printFarmOrderList(Map<String, Object> map, HttpServletRequest request) {
		ShopsResult sr=new ShopsResult();
		List<Map<String, Object>> data=farmProductDao.queryFarmOrderDetailD(map);
		if(null==data||data.isEmpty()){
				sr.setStatus(2);
				sr.setMsg("没有满足条件的订单信息！");
				return sr;
		}
		sr.setStatus(1);
		sr.setMsg("查询成功！");
		sr.setData(data);//返回文件保存路径？？？
		return sr;
	}
	
	
	public Map<String, Object> queryFarmOrderDetailSum(Map<String, Object> map) {
		Map<String,Object> mp = farmProductDao.queryFarmOrderDetailSum(map);
		return mp;
	}

	@Override
	public PurResult querySelectPeiSong(String shop_unique) {
		PurResult sr=new PurResult();
		Map<String,Object> m = new HashMap<String,Object>();
		m.put("shop_unique", shop_unique);
		List<Map<String, Object>> data=saleDao.querySelectPeiSong(m);
		sr.setStatus(1);
		sr.setMsg("查询成功！");
		sr.setData(data);//返回文件保存路径？？？
		return sr;
	}

	@Override
	public ShopsResult updateSaleListStatus(String sale_list_unique) {
		ShopsResult result=new ShopsResult();
		String url = "http://buyhoo.cc/goBuy/my/confirmReceipt.do";
		if (IPGet.ExtranetIP.equals(AddOrderTask.IP18)) {
			url = "http://buyhoo.cc/goBuy/my/confirmReceipt.do";
		}else if(IPGet.ExtranetIP.equals(AddOrderTask.IP12) || IPGet.ExtranetIP.equals(AddOrderTask.IPLOCAL)) {
			url = "http://test170.buyhoo.cc/goBuy/my/confirmReceipt.do";
		}
		String msg= HttpUtil.postData(url, "sale_list_unique="+sale_list_unique);
		System.out.println(msg);
		return result;
	}

	@Override
	public List<Map<String, Object>> selectGoodsDetailInfo(Map<String, Object> goodsParams) {
		return saleDao.queryGoodsDetailInfo(goodsParams);
	}

	@Override
	public ShopsResult queryTimeSale(Map<String, Object> map) {
	ShopsResult sr = new ShopsResult(1,"查询成功!");
	try {
	List<Map<String,Object>> list =new ArrayList<Map<String,Object>>();
	

		
			if(DateUtils.matchSame(map.get("start_time").toString(),map.get("end_time").toString(),2))
			{
				List<Integer> arr=new ArrayList<Integer>();
				for(int i=0;i<24;i++){
					arr.add(i);
				}
				
				
				map.put("list", arr);
				map.put("end_time", map.get("end_time")+" 23:59:59");
				 list = saleDao.statisticsForShopByTime(map);
			}else
			{
				map.put("end_time", map.get("end_time")+" 23:59:59");
				list = saleDao.statisticsForShopByDay(map);
			}
		
		
		
		sr.setData(list);
	} catch (ParseException e) {
		// TODO Auto-generated catch block
		e.printStackTrace();
	}
		return sr;
	}
	

}
