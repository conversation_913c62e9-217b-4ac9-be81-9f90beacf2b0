package org.haier.shop.service;

import java.util.Map;

import org.haier.shop.util.PurResult;

public interface BeanRechargeConfigService {
	
	/**
	 * 查询列表
	 * @return
	 */
	public PurResult getList(Map<String ,Object> params);
	
	/**
	 * 添加
	 * @return
	 */
	public PurResult add(Map<String ,Object> params);
	
	/**
	 * 获取详情
	 * @return
	 */
	public Map<String ,Object> getInfo(String beans_recharge_config_id);
	
	/**
	 * 修改
	 * @return
	 */
	public PurResult update(Map<String ,Object> params);
	
	/**
	 * 删除
	 * @return
	 */
	public PurResult delete(String beans_recharge_config_id);
}
