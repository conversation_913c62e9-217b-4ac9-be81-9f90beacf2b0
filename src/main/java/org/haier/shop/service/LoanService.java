package org.haier.shop.service;

import java.util.List;
import java.util.Map;

import org.haier.shop.util.ShopsResult;

public interface LoanService {
	
	/**
	 * 查询审核通过店铺列表
	 * @return
	 */
	public List<Map<String, Object>> queryShopLoanList2();
	
	public ShopsResult queryShopLoanList(String searchMsg , Integer page,Integer pageSize,Integer audit_status);
	
	public ShopsResult queryWindControl(String searchMsg , Integer page,Integer pageSize);
	
	public ShopsResult queryWindControlDetail(String shop_unique , Integer page,Integer pageSize);
	
	public ShopsResult queryWindControlDetail2(String shop_unique , Integer page,Integer pageSize);
	
	public ShopsResult queryWindControlDetail3(String order_no , Integer page,Integer pageSize);
	
	/**
	 * 查询店铺
	 * @param shop_unique
	 * @return
	 */
	public ShopsResult queryLoanShopDetail(String shop_unique);
	
	/**
	 * 查询借款规则
	 * @param sxRuleId
	 * @return
	 */
	public List<Map<String, Object>> querySxRuleMsg(Integer sxRuleId);
	
	
	
	/**
	 * 查询店铺的贷款额度
	 * @param shop_unique
	 * @param shop_loan_id
	 * @return
	 */
	public String queryShopLoanMoney(String shop_unique,Integer shop_loan_id);
	/**
	 * 更新店铺审核信息
	 * @param map
	 * @return
	 */
	public ShopsResult updateShopLoanMsg(Integer shop_loan_id,Integer audit_status,String loan_money,String remarks,String refuse_reason,Integer new_loan_money);
	/**
	 * 查询未审核的贷款申请列表
	 * @param page
	 * @param pageSize
	 * @return
	 */
	public ShopsResult queryLoanShopList(Integer page,Integer pageSize,Integer audit_status) ;
	
	public ShopsResult queryRules() ;
	
	public ShopsResult updateRules(Map<String,Object> map);
	
	public ShopsResult queryLoanOrderList(String searchMsg , Integer page,Integer pageSize,Integer audit_status,String num);
}
