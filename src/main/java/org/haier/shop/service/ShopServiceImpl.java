package org.haier.shop.service;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSONObject;
import com.jcraft.jsch.SftpException;
import org.haier.meituan.util.MUtil;
import org.haier.shop.controller.SmsThread;
import org.haier.shop.dao.*;
import org.haier.shop.dao2.ShopTDao;
import org.haier.shop.entity.*;
import org.haier.shop.entity.beans.PageData;
import org.haier.shop.params.shop.ShopGoodsInPriceTypeEditParams;
import org.haier.shop.pojo.QueryShopInfoPo;
import org.haier.shop.redis.RedisCache;
import org.haier.shop.result.shop.ShopConfigQueryResult;
import org.haier.shop.util.*;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpSession;
import java.io.*;
import java.math.BigDecimal;
import java.net.URLDecoder;
import java.sql.Timestamp;
import java.util.*;
import java.util.regex.Pattern;

@Service("shopService")
public class ShopServiceImpl implements ShopService {
    @Resource
    private ShopDao shopDao;
    @Resource
    private Purchase_listDao purDao;
    @Resource
    private ShopFunctionDao funDao;
    @Resource
    private Goods_kindDao kindDao;
    @Resource
    private ShopStaffDao staffDao;
    @Resource
    private CusLevelDao levelDao;
    @Resource
    private SupplierDao supplierDao;
    @Resource
    private UtilDao utilDao;
    @Resource
    private ActivityDao activityDao;
    @Resource
    private PayTypeDao payDao;
    @Resource
    private FeedBackDao feedBackDao;
    @Resource
    private ShopTDao shopTDao;

    @Resource
    private RedisCache redis;

    @Resource
    private BeansDao beansDao;
    /**
     * 创建下载文件
     *
     * @param startTime
     * @param endTime
     * @return
     */
    public List<Map<String, Object>> createAccountFile(String startTime, String endTime) {

        //获取需要下载的文件信息
        Map<String, Object> map = new HashMap<String, Object>();
        map.put("startTime", startTime);
        map.put("endTime", endTime);
        List<Map<String, Object>> unlineList = shopDao.queryUnlineAccount(map);
        List<Map<String, Object>> onlineList = shopDao.queryOnlineAccount(map);
        List<Map<String, Object>> retList = shopDao.queryReturnListByShop(map);

        if (null == unlineList) {
            unlineList = new ArrayList<Map<String, Object>>();
        }

        if (null != onlineList) {
            //比较线上线下的数据差，将相同的相加，不相同的增加
            for (Integer i = 0; i < onlineList.size(); i++) {
                boolean flag = true;
                Map<String, Object> onMap = onlineList.get(i);
                for (Integer j = 0; j < unlineList.size(); j++) {
                    Map<String, Object> unMap = unlineList.get(j);

                    if (onMap.get("shop_unique").toString().equals(unMap.get("shop_unique").toString())) {
                        flag = false;
                        unMap.putAll(onMap);
                        break;
                    }
                }

                if (flag) {
                    //没有相同的店铺信息，需要添加
                    unlineList.add(onMap);
                }
            }
        }

        //将退款的依次扣除，问题：如果今天没有收款，只有扣款，该怎么处理
        //特别注意，如果没有金额，不需要退款，防止
        for(Integer j = 0; j < unlineList.size(); j++) {
        	Map<String, Object> unMap = unlineList.get(j);
        	//分别计算线上先线下退款
        	String onlineStr = unMap.get("onlineMoney") == null ? "0" : unMap.get("onlineMoney").toString();
        	String unlineStr = unMap.get("unlineMoney") == null ? "0" : unMap.get("unlineMoney").toString();
        	String onlineRetStr = "0";
        	String unlineRetStr = "0";
        	String onlineRateStr = unlineList.get(j).get("online_rate") == null ? "0" : unMap.get("online_rate").toString();
        	String unlineRateStr = unlineList.get(j).get("unline_rate") == null ? "0" : unMap.get("unline_rate").toString();
        	if(null != retList && !retList.isEmpty()) {
        		for(Integer i = 0; i < retList.size(); i++) {
        			Map<String, Object> retMap = retList.get(i);
					if (retMap.get("shop_unique").toString().equals(unMap.get("shop_unique").toString())) {
						onlineRetStr = retMap.get("onlineRet") == null ? "0" : retMap.get("onlineRet").toString();
						unlineRetStr = retMap.get("unlineRet") == null ? "0" : retMap.get("unlineRet").toString();
						if(onlineRateStr.equals("0")) {
							onlineRateStr = retMap.get("online_rate") == null ? "0" : retMap.get("online_rate").toString();
						}
						if(unlineRateStr.equals("0")) {
							unlineRateStr = retMap.get("unline_rate") == null ? "0" : retMap.get("unline_rate").toString();
						}
					}
        		}
        	}
        	
        	BigDecimal onlineMoney = new BigDecimal(onlineStr);
        	BigDecimal unlineMoney = new BigDecimal(unlineStr);
        	BigDecimal onlineRet = new BigDecimal(onlineRetStr);
        	BigDecimal unlineRet = new BigDecimal(unlineRetStr);
        	BigDecimal onlineRate = new BigDecimal(onlineRateStr);
        	BigDecimal unlineRate = new BigDecimal(unlineRateStr);
        	
        	
        	//计算最终应的金额
        	BigDecimal payMoney = new BigDecimal("0");
        	//线下需要判断有没有金额，如果没有，不需要退款，防止非公户退款造成的差额
        	if(unlineMoney.compareTo(new BigDecimal(0.0)) == 0) {
        		unlineRet = new BigDecimal(0.0);
        	}
        	
        	payMoney = payMoney.add((onlineMoney.subtract(onlineRet)).multiply(onlineRate).setScale(2,BigDecimal.ROUND_HALF_UP));
        	payMoney = payMoney.add((unlineMoney.subtract(unlineRet)).multiply(unlineRate).setScale(2,BigDecimal.ROUND_HALF_UP));
        	
        	unlineList.get(j).put("payMoney", payMoney);
        }
        
        //添加远见餐厅超市的
        Map<String, Object> canMap = shopDao.queryCanTingAccount(map);
        if (null != canMap && !canMap.isEmpty()) {
            unlineList.add(canMap);
        }
        //排序,依次从数组中取出最大值，并插入到新的数组中
        Collections.sort(unlineList, new Comparator<Map<String, Object>>() {
            public int compare(Map<String, Object> m1, Map<String, Object> m2) {
                BigDecimal m11 = new BigDecimal(m1.get("payMoney").toString());
                BigDecimal m22 = new BigDecimal(m2.get("payMoney").toString());

                return m22.compareTo(m11);
            }
        });

        for (Integer i = 0; i < unlineList.size(); i++) {
            unlineList.get(i).put("payMoney", new BigDecimal(unlineList.get(i).get("payMoney").toString()).setScale(2, BigDecimal.ROUND_HALF_UP));
        }

        return unlineList;
    }

    /**
     * @param shopUnique 店铺编号
     * @param startTime  开始查询日期
     * @param endTime    结束查询日期，在此基础上+1
     * @param page       查询的页数
     * @param limit      单页查询的数量
     */
    public ShopsResult queryLoginRecord(String shopUnique, String startTime, String endTime, Integer page, Integer limit, Integer handover) {
        ShopsResult sr = new ShopsResult(1, "查询成功！");
        /*
         * 1、查询各个店在指定时间内的交接班记录
         * 2、循环查询各个交接班内的
         * 2.1：营业额和订单数量；
         * 2.2：充值金额
         */

        Map<String, Object> map = new HashMap<String, Object>();
        map.put("startTime", startTime);
        map.put("endTime", endTime);
        if (page != null) {
            map.put("startNum", (page - 1) * limit);
            map.put("pageSize", limit);
        }

        map.put("shopUnique", shopUnique);
        map.put("handover", handover);

        //获取接交班记录
        List<Map<String, Object>> loginList = shopDao.queryLoginRecord(map);
        if (loginList != null && !loginList.isEmpty()) {
            for (Integer i = 0; i < loginList.size(); i++) {
                //
                Map<String, Object> businessMap = shopDao.queryLoginBusinessMsg(loginList.get(i));
                if (null != loginList && !loginList.isEmpty()) {
                    loginList.get(i).putAll(businessMap);
                } else {
                    loginList.get(i).put("saleListTotal", 0);
                    loginList.get(i).put("listCount", 0);
                }

                Map<String, Object> payDetail = shopDao.queryLoginBusinessDetail(loginList.get(i));
                //是否需要查询会员支付
                boolean flag = false;
                //
                if (null == payDetail || payDetail.isEmpty()) {
                    loginList.get(i).put("cashMoney1", 0);
                    loginList.get(i).put("cashMoney2", 0);
                    loginList.get(i).put("cashMoney3", 0);
                    loginList.get(i).put("cashMoney5", 0);
                    loginList.get(i).put("cashMoney6", 0);
                    loginList.get(i).put("cashMoney13", 0);
                } else {
                    loginList.get(i).putAll(payDetail);
                    if (!payDetail.get("cashMoney5").toString().equals("0.00")) {
                        flag = true;
                    }
                }

                if (flag) {
                    List<Map<String, Object>> rechargeList = shopDao.queryLoginCusRechargeMsg(loginList.get(i));
                    if (null != rechargeList && !rechargeList.isEmpty()) {
                        loginList.get(i).put("cashMoney5", rechargeList.get(0).get("rechargeMoney"));
                        loginList.get(i).put("cashMoney6", rechargeList.get(0).get("giveMoney"));
                    } else {
                        loginList.get(i).put("cashMoney5", 0);
                        loginList.get(i).put("cashMoney6", 0);
                    }
                }

//				/**
//				 * 查询店内储值卡和退款金额
//				 */
//				List<Map<String,Object>> rechargeList = shopDao.queryLoginCusRechargeMsg(loginList.get(i));
//				String tempRecharge = "0";
//				String tempConsumption = "0";
//				String tempCashout = "0";
//				String tempRechargeGive = "0";
//				String tempConsumptionGive = "0";
//				String tempCashoutGive = "0";
//				if(null != rechargeList && !rechargeList.isEmpty()) {
//					for(Integer j = 0; j< rechargeList.size() ; j++) {
//						if(rechargeList.get(j).get("cusType").toString().equals("1")) {
//							tempRecharge = rechargeList.get(j).get("rechargeMoney").toString();
//							tempRechargeGive = rechargeList.get(j).get("giveMoney").toString();
//						}
//						if(rechargeList.get(j).get("cusType").toString().equals("3")) {
//							tempConsumption = rechargeList.get(j).get("rechargeMoney").toString();
//							tempConsumptionGive = rechargeList.get(j).get("giveMoney").toString();
//						}
//					}
//				}
//
//				/**
//				 * 查询店内退款信息
//				 */
//				Map<String,Object> cashMap = shopDao.queryLoginCusRefundMsg(loginList.get(i));
//				if(null == cashMap || cashMap.isEmpty()) {
//					loginList.get(i).put("tempCashout", tempCashout);
//					loginList.get(i).put("tempCashoutGive", tempCashoutGive);
//				}else {
//					loginList.get(i).putAll(cashMap);
//				}
//
//				loginList.get(i).put("tempRecharge", tempRecharge);
//				loginList.get(i).put("tempRechargeGive", tempRechargeGive);
//				loginList.get(i).put("tempConsumption", tempConsumption);
//				loginList.get(i).put("tempConsumptionGive", tempConsumptionGive);
            }
        }
        sr.setData(loginList);
        sr.setCount(shopDao.queryLoginBusinessMsgCount(map));
        return sr;
    }

    /**
     * 中心站查询各站点的往日营业状态
     *
     * @param shopUnique
     * @param pageNum
     * @param pageSize
     * @param startDate
     * @param endDate
     * @return
     */
    public ShopsResult queryShopByArea(String shopUnique, Integer pageNum, Integer pageSize, String startDate, String endDate, Integer searchType, String shopMsg) {
        ShopsResult sr = new ShopsResult(1, "查询成功!");
        Map<String, Object> map = new HashMap<String, Object>();
        map.put("shopUnique", shopUnique);
        map.put("startDate", startDate);
        map.put("endDate", endDate);
        map.put("startNum", (pageNum - 1) * pageSize);
        map.put("pageSize", pageSize);
        map.put("searchType", searchType);
        map.put("shopMsg", shopMsg);
        /*
         * 查询所有记录条数
         */
        Integer count = shopDao.queryShopByAreaCount(map);
        if (null == count || count == 0) {
            return sr;
        }
        sr.setCount(count);
        List<Map<String, Object>> resList = shopDao.queryShopByArea(map);
        sr.setData(resList);
        return sr;
    }

    /**
     * 根据会员编号，查询会员信息
     */
    public ShopsResult checkShareMsg(String staffPhone, String shopUnique) {
        ShopsResult sr = new ShopsResult(1, "查询成功！");
        if (null == staffPhone || staffPhone.equals("")) {
            sr.setStatus(0);
            sr.setMsg("请输入正确的手机号");
            return sr;
        }
        Map<String, Object> resMap = shopDao.queryCusOnLineMsg(staffPhone);
        sr.setData(resMap);
        if (null != sr.getData()) {
            //更新shops_config
            Map<String, Object> map = new HashMap<String, Object>();
            map.put("shopUnique", shopUnique);
            map.put("cusUnique", resMap.get("cusUnique"));
            shopDao.modifyShopsConfig(map);
        }
        return sr;
    }

    /**
     * 查询店铺基本信息
     *
     * @param shop_unique
     * @return
     */
    public ShopsResult queryShopMessage(String shop_unique) {
        ShopsResult shop = new ShopsResult();
        Map<String, Object> map = new HashMap<String, Object>();
        map.put("shop_unique", shop_unique);
        if (null == shop_unique || shop_unique.equals("")) {
            shop.setStatus(0);
            shop.setMsg("请上传正确的店铺信息");
            return shop;
        }
        Map<String, Object> reMap = shopDao.queryShopMessage(map);
        if (null == reMap) {
            shop.setStatus(1);
            shop.setMsg("没有店铺相关的信息");
            return shop;
        }
        shop.setStatus(0);
        shop.setMsg("查询成功！!");
        shop.setData(reMap);
        return shop;
    }


    /**
     * 更新店铺基本信息
     *
     * @param request
     * @param shop_unique
     * @param shop_name
     * @param shop_phone
     * @param shop_address_detail
     * @param manager_pwd
     * @param shop_remark
     * @return
     */
    @Transactional
    public ShopsResult updateShopDetail(HttpServletRequest request, String shop_unique, String shop_name, String shop_phone, String startTime, String endTime,
                                        String shop_address_detail, String manager_pwd, String shop_remark, String shop_seller_email, String area_dict_num, String shopLongitude, String shopLatitude
    ) {
        ShopsResult shop = new ShopsResult();
        Map<String, Object> map = new HashMap<String, Object>();
        map.put("shop_unique", shop_unique);
        map.put("shop_name", shop_name);
        map.put("shop_phone", shop_phone);
        map.put("shop_address_detail", shop_address_detail);
        map.put("shop_remark", shop_remark);
        map.put("shop_seller_email", shop_seller_email);
        map.put("area_dict_num", area_dict_num);
        map.put("shopLongitude", shopLongitude);
        map.put("shopLatitude", shopLatitude);
        if (startTime != null && endTime != null) {
            map.put("shop_hours", startTime + "-" + endTime);
        }
        if (manager_pwd != null) {
            map.put("manager_pwd", ShopsUtil.string2MD5(manager_pwd.trim()).trim());
        }
        MultipartFile file = null;
//		System.out.println("商品图片是否上传成功！！！"+(request instanceof MultipartHttpServletRequest));
        if (request instanceof MultipartHttpServletRequest) {
            MultipartHttpServletRequest multipartHttpServletRequest = (MultipartHttpServletRequest) request;
            Map<String, MultipartFile> mp = multipartHttpServletRequest.getFileMap();
            file = mp.get("shop_picture");

        }
        if (file != null) {
//			System.out.println("已上传商品图片");
            String orName = file.getOriginalFilename();
            String lastName = orName.substring(orName.lastIndexOf("."));
            String shop_dir = File.separator + "home" + File.separator + "apache-tomcat-7.0.42"
                    + File.separator + "webapps" + File.separator + "image" + File.separator + shop_unique
                    + File.separator;
            File dir = new File(shop_dir);
            if (!dir.exists()) {
                dir.mkdirs();
            }
            String shop_pictureName = shop_unique + lastName;
            PicSaveUtil.handleFileUpId(file, request, shop_dir, shop_pictureName);//图片的保存
            String shop_picture_path = "image" + File.separator + shop_unique + File.separator + shop_pictureName;
            map.put("shop_image_path", shop_picture_path);
        }


//		System.out.println("商家端信息更新MAP::::："+map);
        int k = shopDao.updateShopDetail(map);
//		System.out.println(k);
        if (0 == k) {
            shop.setStatus(1);
            shop.setMsg("更新失败！");
            return shop;
        }
        shop.setStatus(0);
        shop.setMsg("更新成功！");
        return shop;
    }

    /**
     * 店铺功能查询
     *
     * @param shop_unique
     * @return
     */
    public ShopsResult queryShopFunction(String shop_unique) {
        ShopsResult shop = new ShopsResult();
        Map<String, Object> map = new HashMap<String, Object>();
        map.put("shop_unique", shop_unique);
        Map<String, Object> result = shopDao.queryShopFunction(map);
        if (null == result) {
            shop.setStatus(1);
            shop.setMsg("没有满足条件的信息！");
            return shop;
        }
        shop.setStatus(0);
        shop.setMsg("查询成功！");
        shop.setData(result);
        return shop;
    }


    /**
     * 更新商品功能
     *
     * @param shop_unique
     * @param shop_flower
     * @param shop_deliverwater
     * @param shop_laundry
     * @param shop_express
     * @param shop_homemarking
     * @param shop_cake
     * @param shop_fruit
     * @param shop_pur
     * @return
     */
    @Transactional
    public ShopsResult updateShopFunction(String shop_unique, String shop_flower, String shop_deliverwater, String shop_laundry, String shop_express,
                                          String shop_homemarking, String shop_cake, String shop_fruit, String shop_pur, Integer negative_sale, Integer below_cost, Integer auto_pur,
                                          Integer auto_pur_days,
                                          Integer unsalable_days,
                                          Integer out_stock_warning_days,
                                          Integer out_stock_days,
                                          Integer out_stock_remind_type,
                                          Integer auto_pur_count_days) {
        ShopsResult shop = new ShopsResult();
        Map<String, Object> map = new HashMap<String, Object>();
        map.put("shop_unique", shop_unique);
        map.put("shop_flower", shop_flower);
        map.put("shop_deliverwater", shop_deliverwater);
        map.put("shop_laundry", shop_laundry);
        map.put("shop_express", shop_express);
        map.put("shop_homemarking", shop_homemarking);
        map.put("shop_cake", shop_cake);
        map.put("shop_fruit", shop_fruit);
        map.put("shop_pur", shop_pur);
        map.put("negative_sale", negative_sale);
        map.put("below_cost", below_cost);
        map.put("auto_pur", auto_pur);
        map.put("auto_pur_days", auto_pur_days);
        map.put("auto_pur_count_days", auto_pur_count_days);
        map.put("unsalable_days", unsalable_days);
        map.put("out_stock_warning_days", out_stock_warning_days);
        map.put("out_stock_days", out_stock_days);
        map.put("out_stock_remind_type", out_stock_remind_type);
//		System.out.println("店铺功能设置"+map);
        int k = shopDao.updateShopFunction(map);
        if (k == 0) {
            shop.setStatus(1);
            shop.setMsg("店铺功能未更新！");
            return shop;
        }
        shop.setStatus(0);
        shop.setMsg("更新成功！");
        return shop;
    }


    /**
     * 店铺供货商对账查询
     *
     * @param shop_unique
     * @param supMessage
     * @param startTime
     * @param endTime
     * @return
     */
    public ShopsResult queryTurnOver(String shop_unique, String supMessage, Timestamp startTime, Timestamp endTime) {
        ShopsResult shop = new ShopsResult();
        Map<String, Object> map = new HashMap<String, Object>();
        map.put("shop_unique", shop_unique);
        if (null != supMessage && !"".equals(supMessage)) {
            map.put("supMessage", "%" + supMessage + "%");
        }
        map.put("startTime", startTime);
        map.put("endTime", endTime);
//		System.out.println("对账查询！"+map);
        List<TurnOverMain> result = purDao.queryTurnOver(map);
//		System.out.println(purDao.test(map));
        shop.setStatus(0);
        shop.setMsg("查询成功！");
        shop.setData(result);
        return shop;
    }

    /**
     * 经营统计
     *
     * @param shop_unique
     * @param queryType
     * @return
     */
    public ShopsResult management(String shop_unique, Integer queryType, Integer pageNum) {
        if (pageNum == null || pageNum <= 0) {
            pageNum = 1;
        }
        ShopsResult ns = new ShopsResult();
        Map<String, Object> map = new HashMap<String, Object>();
        map.put("turnType", "%Y-%m-%d");
        if (null == shop_unique) {
            ns.setStatus(1);
            ns.setMsg("店铺编号不能为空！");
            return ns;

        }
        map.put("shop_unique", shop_unique);
        int k = 0;
        if (queryType == 1 || queryType == 2) {
            k = shopDao.turnCountDays(map);
        } else if (queryType == 3) {
            k = shopDao.visitCountDays(map);
        } else if (queryType == 4) {
            k = shopDao.stockDays(map);
        }
        if (k == 0) {
            ns.setStatus(1);
            ns.setMsg("没有想关的信息！");
            return ns;
        }
        if (k % 30 == 0) {
            ns.setCord(k / 30);
        } else {
            ns.setCord(k / 30 + 1);
        }
//		System.out.println(k+"天数");
        Calendar calendar = Calendar.getInstance();
        Calendar sCalendar = Calendar.getInstance();
        calendar.set(Calendar.HOUR_OF_DAY, 23);
        calendar.set(Calendar.MINUTE, 59);
        calendar.set(Calendar.SECOND, 59);
        sCalendar.set(Calendar.HOUR_OF_DAY, 0);
        sCalendar.set(Calendar.MINUTE, 0);
        sCalendar.set(Calendar.SECOND, 1);
        List<String> times = new ArrayList<String>();//用来存放时间节点
        if (null == queryType) {
            queryType = 1;
        }
        long betweenDays = 30;
        if (k < 30 * pageNum) {
            betweenDays = k - (pageNum * 30) + 30;
        }
        if (betweenDays <= 0) {
            ns.setStatus(1);
            ns.setMsg("页码范围错误！");
            return ns;
        }
        calendar.add(Calendar.DATE, -pageNum * 30 + 30);
        sCalendar.add(Calendar.DATE, -pageNum * 30 + 30);
        Timestamp endTime = new Timestamp(calendar.getTimeInMillis());
        Timestamp startTime = null;
        for (int i = 0; i < betweenDays; i++) {
            times.add(sCalendar.get(Calendar.MONTH) + 1 + "." + sCalendar.get(Calendar.DAY_OF_MONTH));//将日期中的天数添加到数据库
            sCalendar.add(Calendar.DATE, -1);
        }
        startTime = new Timestamp(sCalendar.getTimeInMillis());
        map.put("startTime", startTime);
        map.put("endTime", endTime);
        if (queryType == 1) {
            ns = saleTotal(map, times, queryType, shop_unique, ns, k);
        } else if (queryType == 2) {
            ns = orderCount(map, times, queryType, shop_unique, ns, k);
        } else if (queryType == 3) {
            ns = viewCount(map, times, queryType, shop_unique, ns, k);
        } else if (queryType == 4) {
            ns = stockCount(map, times, queryType, shop_unique, ns, k);
        }
        ns.setStatus(0);
        ns.setMsg("查询成功");
        return ns;
    }


    /**
     * 销售额统计制图
     *
     * @param map
     * @param times
     * @param queryType
     * @param shop_unique
     * @return
     */
    public ShopsResult saleTotal(Map<String, Object> map, List<String> times, Integer queryType, String shop_unique, ShopsResult shop, int sum) {
//		System.out.println("销售额统计"+map);
        List<Map<String, Object>> result = shopDao.turnCount(map);
        String ran = "saleTotal" + Math.round(Math.random() * 100) + ".jpg";
        String filePath = File.separator + "home" + File.separator + "apache-tomcat-7.0.42" + File.separator
                + "webapps" + File.separator + "image" + File.separator + shop_unique + File.separator + ran;
        String path = "image" + File.separator + shop_unique + File.separator + ran;
        shop.setData(result);
        Map<String, Object> mp = new HashMap<String, Object>();
        if (sum % 30 == 0) {
            mp.put("pageCount", sum / 30);
        } else {
            mp.put("pageCount", sum / 30 + 1);
        }
        mp.put("imagePath", path);
        shop.setCord(mp);
        CreateLine.createLines(filePath, result, times, "销售额统计");
        return shop;
    }

    /**
     * 订单量查询制图
     *
     * @param map   限制信息
     * @param times 制图时间列表
     * @return
     */
    public ShopsResult orderCount(Map<String, Object> map, List<String> times, Integer queryType, String shop_unique, ShopsResult shop, int sum) {
        List<Map<String, Object>> result = shopDao.saleListCount(map);
        String ran = "saleCount" + Math.round(Math.random() * 100) + ".jpg";
        String filePath = File.separator + "home" + File.separator + "apache-tomcat-7.0.42" + File.separator
                + "webapps" + File.separator + "image" + File.separator + shop_unique + File.separator + ran;
        String path = "image" + File.separator + shop_unique + File.separator + ran;
        SaleCountLine.createLines(filePath, result, times, "销售订单量统计");
        shop.setData(result);
        Map<String, Object> mp = new HashMap<String, Object>();
        if (sum % 30 == 0) {
            mp.put("pageCount", sum / 30);
        } else {
            mp.put("pageCount", sum / 30 + 1);
        }
        mp.put("imagePath", path);
        shop.setCord(mp);
        return shop;
    }

    /**
     * 浏览量查询制图
     *
     * @param map
     * @param times
     * @param queryType
     * @param shop_unique
     * @return
     */
    public ShopsResult viewCount(Map<String, Object> map, List<String> times, Integer queryType, String shop_unique, ShopsResult shop, int sum) {
//		System.out.println("访问量查询："+map);
        List<Map<String, Object>> result = shopDao.visitCount(map);
//		System.out.println("访问量查询"+result.size());
        String ran = "viewCount" + Math.round(Math.random() * 100) + ".jpg";
        String filePath = File.separator + "home" + File.separator + "apache-tomcat-7.0.42" + File.separator
                + "webapps" + File.separator + "image" + File.separator + shop_unique + File.separator + ran;
        String path = "image" + File.separator + shop_unique + File.separator + ran;
        SaleCountLine.createLines(filePath, result, times, "浏览量查询");
        shop.setData(result);
        Map<String, Object> mp = new HashMap<String, Object>();
        if (sum % 30 == 0) {
            mp.put("pageCount", sum / 30);
        } else {
            mp.put("pageCount", sum / 30 + 1);
        }
        mp.put("imagePath", path);
        shop.setCord(mp);
        return shop;
    }

    /**
     * 库存查询
     *
     * @param map
     * @param times
     * @param queryType
     * @param shop_unique
     * @return
     */
    public ShopsResult stockCount(Map<String, Object> map, List<String> times, Integer queryType, String shop_unique, ShopsResult shop, int sum) {
        List<Map<String, Object>> result = shopDao.queryStockValue(map);
        String ran = "stockCount" + Math.round(Math.random() * 100) + ".jpg";
        String filePath = File.separator + "home" + File.separator + "apache-tomcat-7.0.42" + File.separator
                + "webapps" + File.separator + "image" + File.separator + shop_unique + File.separator + ran;
        String path = "image" + File.separator + shop_unique + File.separator + ran;
        CreateLine.createLines(filePath, result, times, "库存额查询");
        shop.setData(result);
        Map<String, Object> mp = new HashMap<String, Object>();
        if (sum % 30 == 0) {
            mp.put("pageCount", sum / 30);
        } else {
            mp.put("pageCount", sum / 30 + 1);
        }
        mp.put("imagePath", path);
        shop.setCord(mp);
        return shop;
    }

    /**
     * 注册新用户
     *
     * @param shop_name
     * @param manager_account
     * @param manager_pwd
     * @param shop_address_detail
     * @param shop_phone
     * @param area_dict_num
     * @param town_code
     * @return
     */
    @Transactional
    public ShopsResult register(String shop_name,
                                String manager_account,
                                String manager_pwd,
                                String shop_address_detail,
                                String shop_phone,
                                Integer examinestatus,
                                Integer shop_class,
                                String company_code,
                                Integer is_other_purchase,
                                String area_dict_num,
                                String shop_latitude,
                                String shop_longitude,
                                String province,
                                String city,
                                String district,
                                Integer shop_type,
                                String user_name,
                                String shop_image_path,
                                String license,
                                HttpServletRequest request, String town_code, String invitation_code) {
        Map<String, Object> map = new HashMap<String, Object>();
        Map<String, Object> pmap = new HashMap<String, Object>();
        try {
            shop_name = URLDecoder.decode(shop_name, "UTF-8");
            if (null != shop_address_detail && !"" .equals(shop_address_detail)) {
                shop_address_detail = URLDecoder.decode(shop_address_detail, "UTF-8");
            }
            shop_phone = URLDecoder.decode(shop_phone, "UTF-8");
            province = URLDecoder.decode(province, "UTF-8");
            city = URLDecoder.decode(city, "UTF-8");
            district = URLDecoder.decode(district, "UTF-8");
            user_name = URLDecoder.decode(user_name == null ? shop_name : user_name, "UTF-8");
            invitation_code = invitation_code == null ? "" : URLDecoder.decode(invitation_code, "UTF-8");
        } catch (UnsupportedEncodingException e) {
            e.printStackTrace();
        }
        if ((area_dict_num == null && city != null && !city.equals("")) || (area_dict_num != null && area_dict_num.equals("") && city != null && !city.equals(""))) {
            Map<String, Object> dictParams = new HashMap<String, Object>();
            dictParams.put("province", province);
            dictParams.put("city", city);
            dictParams.put("district", district);
            //查询区的编号
            Map<String, Object> quCode = supplierDao.queryDistrict(dictParams);
            area_dict_num = (String) quCode.get("area_dict_num");
        }
        map.put("manager_account", manager_account);
        Map<String, Object> re = shopDao.login(map);
        if (re != null) {
            if (re.containsKey("examinestatus") && re.get("examinestatus").equals(2)) {
                return ShopsResult.fail("该账户已注册，正在审核中！");
            } else if (re.containsKey("examinestatus") && re.get("examinestatus").equals(3)) {
                //修改店铺审核状态
                HttpSession session = request.getSession();
                session.setAttribute("shop_unique", re.get("shop_unique"));
                session.setAttribute("shop_phone", shop_phone);
                Map<String, Object> map2 = new HashMap<String, Object>();
                map2.put("shop_unique", re.get("shop_unique"));
                map2.put("shop_address_detail", shop_address_detail);
                map2.put("area_dict_num", area_dict_num);
                map2.put("shop_name", shop_name);
                map2.put("shop_latitude", shop_latitude);
                map2.put("shop_longitude", shop_longitude);
                map2.put("examinestatus", 2);
                shopDao.updateShopDetail(map2);
                //修改密码
                re.put("staffId", re.get("staff_id"));
                re.put("staff_pwd", ShopsUtil.string2MD5(manager_pwd.trim()).trim());
                re.put("staffPwdOk", manager_pwd);
                staffDao.updateStaffBaseMessage(re);

                return ShopsResult.fail("该账户审核已驳回，请联系管理员！");
            } else if (re.containsKey("examinestatus") && re.get("examinestatus").equals(4)) {
                return ShopsResult.fail("该账户已存在！");
            } else if (re.containsKey("examinestatus") && re.get("examinestatus").equals(5)) {
                return ShopsResult.fail("该账户已注销！");
            }
            return ShopsResult.fail("用户已注册");
        }
        Staff staff = new Staff();
        String staff_pwd = ShopsUtil.string2MD5(manager_pwd.trim()).trim();
        map.put("shop_phone", shop_phone);
        map.put("manager_pwd", staff_pwd);
        map.put("shop_address_detail", shop_address_detail);
        map.put("area_dict_num", area_dict_num);
        map.put("town_code", town_code);
        map.put("shop_name", shop_name);
        map.put("shop_alias", ChineseCharToEn.getAllFirstLetter(shop_name).trim());
        map.put("examinestatus", examinestatus);
        map.put("shop_class", shop_class);
        map.put("company_code", company_code);
        map.put("is_other_purchase", is_other_purchase);
        map.put("login_name", shop_name);
        map.put("shop_latitude", shop_latitude);
        map.put("shop_longitude", shop_longitude);
        map.put("shop_image_path", shop_image_path);
        map.put("shop_image_path", shop_image_path);
        map.put("invitation_code", invitation_code);
//		map.put("shop_alias", shop_name);
        String shop_unique = new Date().getTime() + "";
        staff.setShop_unique(Long.parseLong(shop_unique));
        staff.setStaff_account(manager_account);
        staff.setStaff_pwd(staff_pwd);
        staff.setStaff_phone(shop_phone);
        staff.setPwd_ok(manager_pwd);
        staff.setStaff_position(3);
        staff.setManager_unique(shop_unique);
        staff.setStaff_name(user_name == null ? shop_name : user_name);
        map.put("shop_unique", shop_unique);
        map.put("shopUnique", shop_unique);
        map.put("manager_unique", shop_unique);//添加主管理员
        map.put("kindType", 2);//默认自定义分类

        //如果客户上传了营业执照，保存信息
        if (null != license && !license.equals("")) {
            ShopPayMsg shopPayMsg = new ShopPayMsg();
            shopPayMsg.setShopUnique(shop_unique);
            shopPayMsg.setLicense(license);
            payDao.addNewShopPayMsg(shopPayMsg);
        }

        //2020-09-19新增五金店 修改分类 并且 默认使用系统分类
        if (shop_type == 8) {
            map.put("kindType", 1);//默认系统分类
        }
        map.put("shop_type", shop_type);
        int k = shopDao.register(map);//商家端注册新店铺


        if (k == 0) {
            return ShopsResult.fail("注册失败！");
        } else {
            k = shopTDao.registerNewShop(map);
        }

        //验证是否存在gold_shop数据，如果不存在，新增
//		map.put("jqb_count", "0");
//		shopDao.addNewShopGold(map);

        //添加金圈币
        shopDao.addNewGrantDetail(shop_unique);

        HttpSession session = request.getSession();
        session.setAttribute("shop_unique", shop_unique);
        session.setAttribute("shop_phone", shop_phone);
        if (shop_type == 4 || shop_type == 5) {
            session.setAttribute("url", "loginYn");
        } else {
            session.setAttribute("url", "login");
        }

        map.put("shopUnique", shop_unique);
        k = funDao.addNewShopFunction(shop_unique);//添加店铺功能权限
        if (shop_type == 8) {
            kindDao.addNewGoodsKindsWJ(shop_unique);

            //新增百货豆抵扣比率0
            PageData pd = new PageData();
            pd.put("shopUnique", shop_unique);
            pd.put("diKou", 0);
            beansDao.addDikou(pd);
        } else {
            kindDao.addNewGoodsKinds(shop_unique);
        }

        //添加店铺商品分类
        levelDao.addNewCusLevel(map);//添加会员等级分类信息
        funDao.addNewShopsConfig(map);//添加店铺配置信息，店铺的图片上传和免密开通配置信息等
        //添加员工
        k = staffDao.newStaff(staff);
        pmap.put("staff_id", staff.getStaff_id());
        pmap.put("powerRecharge", "1");
        //添加员工权限
        pmap.put("powerManager", "1");
        k = staffDao.newStaffPower(pmap);

        //添加店铺升级记录，用于更新
        //1:查询系统的最新版本号，
        //2：添加记录
        String version = utilDao.theLastVersion();
        map.put("versionNumber", version);
        shopDao.newShopUpDateRecord(map);

        //添加注册时的图标信息（APP功能）
        shopDao.addShopTitle(map);
        List<Map<String, Object>> hourList = new ArrayList<Map<String, Object>>();
        Map<String, Object> hmap = new HashMap<String, Object>();
        hmap.put("shop_unique", shop_unique);
        hmap.put("start_hours", "07:00:00");
        hmap.put("end_hours", "20:00:00");
        hourList.add(hmap);
        //添加营业时间
        shopDao.addShopHours(hourList);
        return ShopsResult.ok("注册成功！",shop_unique);
    }


    @Transactional
    public ShopsResult registerApp(String shop_name,
                                   String manager_account,
                                   String manager_pwd,
                                   String shop_address_detail,
                                   String shop_phone,
                                   Integer examinestatus,
                                   Integer shop_class,
                                   String company_code,
                                   Integer is_other_purchase,
                                   String area_dict_num,
                                   String shop_latitude,
                                   String shop_longitude,
                                   String province,
                                   String city,
                                   String district,
                                   Integer shop_type,
                                   String code,
                                   String agencyCode,
                                   HttpServletRequest request, String town_code) {
        ShopsResult shop = new ShopsResult();
        Map<String, Object> map = new HashMap<String, Object>();
        Map<String, Object> pmap = new HashMap<String, Object>();
        try {
            shop_name = URLDecoder.decode(shop_name, "UTF-8");
            shop_address_detail = URLDecoder.decode(shop_address_detail, "UTF-8");
            shop_phone = URLDecoder.decode(shop_phone, "UTF-8");
            province = URLDecoder.decode(province, "UTF-8");
            city = URLDecoder.decode(city, "UTF-8");
            district = URLDecoder.decode(district, "UTF-8");
        } catch (UnsupportedEncodingException e) {
            e.printStackTrace();
        }
        if (area_dict_num == null && city != null || area_dict_num.equals("") && city != null) {
            Map<String, Object> dictParams = new HashMap<String, Object>();
            dictParams.put("province", province);
            dictParams.put("city", city);
            dictParams.put("district", district);
            //查询区的编号
            Map<String, Object> quCode = supplierDao.queryDistrict(dictParams);
            area_dict_num = (String) quCode.get("area_dict_num");
        }
        map.put("manager_account", manager_account);
        Map<String, Object> re = shopDao.login(map);
        if (re != null) {
            shop.setStatus(1);
            shop.setMsg("该账户已注册！");
            return shop;
        }
        Staff staff = new Staff();
        String staff_pwd = ShopsUtil.string2MD5(manager_pwd.trim()).trim();
        map.put("shop_phone", shop_phone);
        map.put("manager_pwd", staff_pwd);
        map.put("shop_address_detail", shop_address_detail);
        map.put("area_dict_num", area_dict_num);
        map.put("town_code", town_code);
        map.put("shop_name", shop_name);
        map.put("shop_alias", ChineseCharToEn.getAllFirstLetter(shop_name).trim());
        map.put("examinestatus", examinestatus);
        map.put("shop_class", shop_class);
        map.put("company_code", company_code);
        map.put("is_other_purchase", is_other_purchase);
        map.put("login_name", shop_name);
        map.put("shop_latitude", shop_latitude);
        map.put("shop_longitude", shop_longitude);
//		map.put("shop_alias", shop_name);
        String shop_unique = new Date().getTime() + "";
        staff.setShop_unique(Long.parseLong(shop_unique));
        staff.setStaff_account(manager_account);
        staff.setStaff_pwd(staff_pwd);
        staff.setStaff_phone(shop_phone);
        staff.setPwd_ok(manager_pwd);
        staff.setStaff_position(3);
        staff.setManager_unique(shop_unique);
        staff.setStaff_name(shop_name);
        map.put("shop_unique", shop_unique);
        map.put("shopUnique", shop_unique);
        map.put("manager_unique", shop_unique);//添加主管理员
        map.put("kindType", 2);//默认自定义分类
        //2020-09-19新增五金店 修改分类 并且 默认使用系统分类
        if (shop_type == 8) {
            map.put("kindType", 1);//默认系统分类
        }
        map.put("shop_type", shop_type);
        int k = shopDao.register(map);//商家端注册新店铺


        if (k == 0) {
            shop.setStatus(1);
            shop.setMsg("注册失败！");
            return shop;
        } else {
            k = shopTDao.registerNewShop(map);
        }
        shop.setData(shop_unique);

        //验证是否存在gold_shop数据，如果不存在，新增

        //添加金圈币
        shopDao.addNewGrantDetail(shop_unique);

        HttpSession session = request.getSession();
        session.setAttribute("shop_unique", shop_unique);
        session.setAttribute("shop_phone", shop_phone);
        if (shop_type == 4 || shop_type == 5) {
            session.setAttribute("url", "loginYn");
        } else {
            session.setAttribute("url", "login");
        }

        shop.setStatus(0);
        shop.setMsg("注册成功！");
        map.put("shopUnique", shop_unique);
        k = funDao.addNewShopFunction(shop_unique);//添加店铺功能权限
        if (shop_type == 8) {
            kindDao.addNewGoodsKindsWJ(shop_unique);

            //新增百货豆抵扣比率0
            PageData pd = new PageData();
            pd.put("shopUnique", shop_unique);
            pd.put("diKou", 0);
            beansDao.addDikou(pd);
        } else {
            kindDao.addNewGoodsKinds(shop_unique);
        }

        //添加店铺商品分类
        levelDao.addNewCusLevel(map);//添加会员等级分类信息
        funDao.addNewShopsConfig(map);//添加店铺配置信息，店铺的图片上传和免密开通配置信息等
        //添加员工
        k = staffDao.newStaff(staff);
        pmap.put("staff_id", staff.getStaff_id());
        pmap.put("powerRecharge", "1");
        //添加员工权限
        pmap.put("powerManager", "1");
        k = staffDao.newStaffPower(pmap);

        //添加店铺升级记录，用于更新
        //1:查询系统的最新版本号，
        //2：添加记录
        String version = utilDao.theLastVersion();
        map.put("versionNumber", version);
        shopDao.newShopUpDateRecord(map);

        //添加注册时的图标信息（APP功能）
        shopDao.addShopTitle(map);
        List<Map<String, Object>> hourList = new ArrayList<Map<String, Object>>();
        Map<String, Object> hmap = new HashMap<String, Object>();
        hmap.put("shop_unique", shop_unique);
        hmap.put("start_hours", "07:00:00");
        hmap.put("end_hours", "20:00:00");
        hourList.add(hmap);
        //添加营业时间
        shopDao.addShopHours(hourList);
        System.out.println(code);
        //添加店铺至代理商系统私海
		/*
		if(code != null && !code.equals("")) {
//			/bindNewShopExamine.do code shop_unique agencyCode
			Map<String, Object> bindMap = new HashMap<>();
			bindMap.put("code", code);
			bindMap.put("shopUnique", shop_unique);
			bindMap.put("agencyCode", agencyCode);
			System.out.println("请求的参数为：：：" + bindMap);
			System.out.println("请求的路径" + HelibaoPayConfig.BINDNEWSHOPEXAMINE);
			String resMsg = UtilForRequest.doPost(HelibaoPayConfig.BINDNEWSHOPEXAMINE, bindMap);
			if(null != resMsg && resMsg.startsWith("{")) {
				JSONObject o = JSONObject.parseObject(resMsg);
				System.out.println("代理商项目返回的结果为：：；" + resMsg);
				if(o.getString("status").equals("1")) {
					if(o.containsKey("msg")) {
						JSONObject data = o.getJSONObject("msg");
						System.out.println(data);
					}
				}
			}
		}*/

        return shop;
    }


    /**
     * 注册分店
     *
     * @param map
     * @return
     */
    @Transactional
    public ShopsResult createNewShop(Map<String, Object> map, HttpServletRequest request, String province, String city, String district) {
        ShopsResult sr = new ShopsResult();
        SFTPUtil sftp = new SFTPUtil(FTPConfig.username, FTPConfig.password, FTPConfig.host, FTPConfig.port);
        sftp.login();
        if (map.get("area_dict_num") == null && city != null) {
            map.put("province", province);
            map.put("city", city);
            map.put("district", district);
            //查询区的编号
            Map<String, Object> quCode = supplierDao.queryDistrict(map);
            String area_dict_num = (String) quCode.get("area_dict_num");
            map.put("area_dict_num", area_dict_num);
        }
        String shop_unique = new Date().getTime() + "";
        map.put("shopUnique", shop_unique);
        map.put("shop_unique", shop_unique);
        map.put("jqb_count", 0);

        /**
         * 店铺头像处理
         */
        MultipartFile file = null;
        if (request instanceof MultipartHttpServletRequest) {
            MultipartHttpServletRequest multipartHttpServletRequest = (MultipartHttpServletRequest) request;
            Map<String, MultipartFile> mp = multipartHttpServletRequest.getFileMap();
            file = mp.get("shop_picture");
        }
        if (file != null) {
            String orName = file.getOriginalFilename();
            String lastName = orName.substring(orName.lastIndexOf("."));
            String filePath = request.getServletContext().getRealPath("");// 获取项目所在的绝对路径
            filePath = File.separator + new File(filePath).getParent();
            String shop_dir = filePath + File.separator + "image" + File.separator + shop_unique
                    + File.separator;
            File dir = new File(shop_dir);
            if (!dir.exists()) {
                dir.mkdirs();
            }
            String shop_pictureName = shop_unique + lastName;
            PicSaveUtil.handleFileUpId(file, request, shop_dir, shop_pictureName);//淇濆瓨鍥剧墖
            String shop_picture_path = "image" + File.separator + shop_unique + File.separator + shop_pictureName;
            map.put("shopImagePath", shop_picture_path);

            //保存成功，上传文件服务器
            try {
                InputStream is;
                is = new FileInputStream(new File(shop_dir + shop_pictureName));
                sftp.upload(FTPConfig.shop_head_path + "/" + shop_unique, shop_pictureName, is);
            } catch (FileNotFoundException e) {
                e.printStackTrace();
            } catch (SftpException e) {
                e.printStackTrace();
            }

        }

        //查询店铺实景图片
        List<Map<String, Object>> imageList = staffDao.queryShopImageList(map);
        //实景图片
        MultipartFile file2 = null;
        if (request instanceof MultipartHttpServletRequest) {
            MultipartHttpServletRequest multipartHttpServletRequest = (MultipartHttpServletRequest) request;
            Map<String, MultipartFile> mp = multipartHttpServletRequest.getFileMap();
            file2 = mp.get("shop_picture2");
        }
        if (file2 != null) {
            String orName = file2.getOriginalFilename();
            String lastName = orName.substring(orName.lastIndexOf("."));
            String filePath = request.getServletContext().getRealPath("");// 获取项目所在的绝对路径
            filePath = File.separator + new File(filePath).getParent();
            String shop_dir = filePath + File.separator + "image" + File.separator + shop_unique
                    + File.separator;
            File dir = new File(shop_dir);
            if (!dir.exists()) {
                dir.mkdirs();
            }
            String shop_pictureName = UUID.randomUUID() + lastName;
            PicSaveUtil.handleFileUpId(file2, request, shop_dir, shop_pictureName);//淇濆瓨鍥剧墖
            String shop_picture_path = "image" + File.separator + shop_unique + File.separator + shop_pictureName;
            Map<String, Object> imageMap = new HashMap<String, Object>();
            imageMap.put("shop_image_path", shop_picture_path);
            imageMap.put("shop_unique", shop_unique);

            //保存成功，上传文件服务器
            try {
                InputStream is;
                is = new FileInputStream(new File(shop_dir + shop_pictureName));
                sftp.upload(FTPConfig.shop_head_path + "/" + shop_unique, shop_pictureName, is);
            } catch (FileNotFoundException e) {
                e.printStackTrace();
            } catch (SftpException e) {
                e.printStackTrace();
            }

            if (imageList != null && imageList.size() > 0) {
                imageList.get(0).put("shop_image_path", shop_picture_path);
                staffDao.updateShopImage(imageList.get(0));
            } else {
                staffDao.saveShopImage(imageMap);
            }
        }
        MultipartFile file3 = null;
        if (request instanceof MultipartHttpServletRequest) {
            MultipartHttpServletRequest multipartHttpServletRequest = (MultipartHttpServletRequest) request;
            Map<String, MultipartFile> mp = multipartHttpServletRequest.getFileMap();
            file3 = mp.get("shop_picture3");
        }
        if (file3 != null) {
//							System.out.println("已上传商品图片");
            String orName = file3.getOriginalFilename();
            String lastName = orName.substring(orName.lastIndexOf("."));
            String filePath = request.getServletContext().getRealPath("");// 获取项目所在的绝对路径
            filePath = File.separator + new File(filePath).getParent();
            String shop_dir = filePath + File.separator + "image" + File.separator + shop_unique
                    + File.separator;
            File dir = new File(shop_dir);
            if (!dir.exists()) {
                dir.mkdirs();
            }
            String shop_pictureName = UUID.randomUUID() + lastName;
            PicSaveUtil.handleFileUpId(file3, request, shop_dir, shop_pictureName);//淇濆瓨鍥剧墖
            String shop_picture_path = "image" + File.separator + shop_unique + File.separator + shop_pictureName;
            Map<String, Object> imageMap = new HashMap<String, Object>();
            imageMap.put("shop_image_path", shop_picture_path);
            imageMap.put("shop_unique", shop_unique);

            //保存成功，上传文件服务器
            try {
                InputStream is;
                is = new FileInputStream(new File(shop_dir + shop_pictureName));
                sftp.upload(FTPConfig.shop_head_path + "/" + shop_unique, shop_pictureName, is);
            } catch (FileNotFoundException e) {
                e.printStackTrace();
            } catch (SftpException e) {
                e.printStackTrace();
            }

            if (imageList != null && imageList.size() > 1) {
                imageList.get(1).put("shop_image_path", shop_picture_path);
                staffDao.updateShopImage(imageList.get(1));
            } else {
                staffDao.saveShopImage(imageMap);
            }
        }
        MultipartFile file4 = null;
        if (request instanceof MultipartHttpServletRequest) {
            MultipartHttpServletRequest multipartHttpServletRequest = (MultipartHttpServletRequest) request;
            Map<String, MultipartFile> mp = multipartHttpServletRequest.getFileMap();
            file4 = mp.get("shop_picture4");
        }
        if (file4 != null) {
//							System.out.println("已上传商品图片");
            String orName = file4.getOriginalFilename();
            String lastName = orName.substring(orName.lastIndexOf("."));
            String filePath = request.getServletContext().getRealPath("");// 获取项目所在的绝对路径
            filePath = File.separator + new File(filePath).getParent();
            String shop_dir = filePath + File.separator + "image" + File.separator + shop_unique
                    + File.separator;
            File dir = new File(shop_dir);
            if (!dir.exists()) {
                dir.mkdirs();
            }
            String shop_pictureName = UUID.randomUUID() + lastName;
            PicSaveUtil.handleFileUpId(file4, request, shop_dir, shop_pictureName);//淇濆瓨鍥剧墖
            String shop_picture_path = "image" + File.separator + shop_unique + File.separator + shop_pictureName;
            Map<String, Object> imageMap = new HashMap<String, Object>();
            imageMap.put("shop_image_path", shop_picture_path);
            imageMap.put("shop_unique", shop_unique);

            //保存成功，上传文件服务器
            try {
                InputStream is;
                is = new FileInputStream(new File(shop_dir + shop_pictureName));
                sftp.upload(FTPConfig.shop_head_path + "/" + shop_unique, shop_pictureName, is);
            } catch (FileNotFoundException e) {
                e.printStackTrace();
            } catch (SftpException e) {
                e.printStackTrace();
            }

            if (imageList != null && imageList.size() > 2) {
                imageList.get(2).put("shop_image_path", shop_picture_path);
                staffDao.updateShopImage(imageList.get(2));
            } else {
                staffDao.saveShopImage(imageMap);
            }
        }
        map.put("shop_class", 4);
        int k = staffDao.createNewShop(map);
        //添加供货商店铺信息
        k = shopTDao.registerNewShop(map);
        //创建分店的同时需要注册的信息；
        //店铺分类信息
        funDao.addNewShopFunction(shop_unique);//添加店铺部分权限功能（老款）
        kindDao.addNewGoodsKinds(shop_unique);//添加店铺商品分类
        levelDao.addNewCusLevel(map);//添加会员等级分类信息
        funDao.addNewShopsConfig(map);//添加店铺配置信息，店铺的图片上传和免密开通配置信息等
        shopDao.addShopTitle(map);//添加注册时的图标信息（APP功能）
        shopDao.addNewShopGold(map);//添加店铺金圈币信息

//		String version= utilDao.theLastVersion();
//		map.put("versionNumber", version);
//		shopDao.newShopUpDateRecord(map);//添加版本信息

        if (k == 1) {
            sr.setStatus(1);
            sr.setMsg("创建成功！");
            return sr;
        }
        sr.setStatus(0);
        sr.setMsg("失败！");
        return sr;
    }

    /**
     * 上帝视角
     *
     * @param map
     * @return
     */
    public PurResult countStroeTurnover(Map<String, Object> map, Integer macOnLine, Integer shopOnLine) {
        PurResult sr = new PurResult();
        List<Map<String, Object>> data = shopDao.countStroeTurnover(map);
//		List<Map<String,Object>> list=shopDao.countStoreTurnoverShop(map);
        try {
//			Random r=new Random();
//			map.put("tableName", "loginData"+r.nextInt(100));
//			shopDao.createTemTable(map);
//			shopDao.addTemData(map);
//			List<Map<String,Object>> data=shopDao.countStroeTurnover(map);
//			List<Map<String,Object>> datas=shopDao.queryTemData(map);
            sr.setData(data);
        } catch (Exception e) {
            e.printStackTrace();
            sr.setStatus(0);
            sr.setMsg("异常");
            return sr;
        }
//		Integer count = shopDao.countStroeTurnoverPages(map)*8;//shopTest
        Integer count = shopDao.countStroeTurnoverPages(map);
        sr.setStatus(1);
        sr.setMsg("查询成功！");
        sr.setCount(count);
        Map<String, Object> cord = new HashMap<String, Object>();
        cord.put("macOnLine", macOnLine);
        cord.put("shopOnLine", shopOnLine);
        sr.setCord(cord);
        return sr;
    }

    /**
     * 店铺
     *
     * @param map
     * @return
     */
    public ShopsResult countStroeTurnoverPages(Map<String, Object> map) {
        ShopsResult sr = new ShopsResult();
        Integer count = shopDao.countStroeTurnoverPages(map);
        sr.setStatus(1);
        sr.setMsg("查询成功！");
        sr.setData(count);
        return sr;
    }

    /**
     * 店铺数量页数查询
     *
     * @param map
     * @return
     */
    public ShopsResult queryShopsPages(Map<String, Object> map) {
        ShopsResult sr = new ShopsResult();
        Integer count = shopDao.queryShopsPages(map);

        sr.setStatus(1);
        sr.setMsg("查询成功！");
        sr.setData(count);
        return sr;
    }

    /**
     * 店铺销量总览界面-店铺列表查询
     *
     * @param map
     * @return
     */
    public ShopsResult queryShopsList(Map<String, Object> map) {
        ShopsResult sr = new ShopsResult();
        List<Map<String, Object>> data = shopDao.queryShopsList(map);
        Double big = shopDao.biggestSaleTotal(map);
        if (null == data || data.isEmpty()) {
            sr.setStatus(2);
            sr.setMsg("没有满足条件的店铺信息！");
            return sr;
        }
        sr.setCord(big);
        sr.setStatus(1);
        sr.setMsg("查询成功！");
        sr.setData(data);
        return sr;
    }

    /**
     * 查询某区域内的所有审核通过的非自营店铺信息
     *
     * @param areaDictNum
     * @return
     */
    public ShopsResult queryShopsListForGoods(String areaDictNum) {
        ShopsResult sr = new ShopsResult();
        List<Map<String, Object>> data = shopDao.queryShopsListForGoods(areaDictNum);
        if (null == data || data.isEmpty()) {
            sr.setStatus(2);
            sr.setMsg("没有满足条件的店铺信息！");
            return sr;
        }
        sr.setStatus(1);
        sr.setMsg("查询成功！");
        sr.setData(data);
        return sr;
    }

    @Transactional
    public ShopsResult updateShop(Map<String, Object> map, HttpServletRequest request, String province, String city,
                                  String district, String shopHours) {
        ShopsResult sr = new ShopsResult();
        SFTPUtil sftp = new SFTPUtil(FTPConfig.username, FTPConfig.password, FTPConfig.host, FTPConfig.port);
        sftp.login();
        if (map.get("area_dict_num") == null && city != null || map.get("area_dict_num").equals("") && city != null) {
            map.put("province", province);
            map.put("city", city);
            map.put("district", district);
            //查询区的编号
            Map<String, Object> quCode = supplierDao.queryDistrict(map);
            String area_dict_num = (String) quCode.get("area_dict_num");
            map.put("area_dict_num", area_dict_num);
        }
        String shop_unique = ((Long) map.get("shop_unique")).toString();
        map.put("shopUnique", shop_unique);
        /**
         * 店铺头像处理
         */
        MultipartFile file = null;
        if (request instanceof MultipartHttpServletRequest) {
            MultipartHttpServletRequest multipartHttpServletRequest = (MultipartHttpServletRequest) request;
            Map<String, MultipartFile> mp = multipartHttpServletRequest.getFileMap();
            file = mp.get("shop_picture");
        }
        if (file != null) {
            String orName = file.getOriginalFilename();
            String lastName = orName.substring(orName.lastIndexOf("."));
            String filePath = request.getServletContext().getRealPath("");// 获取项目所在的绝对路径
            filePath = File.separator + new File(filePath).getParent();
            String shop_dir = filePath + File.separator + "image" + File.separator + shop_unique
                    + File.separator;
            File dir = new File(shop_dir);
            if (!dir.exists()) {
                dir.mkdirs();
            }
            String shop_pictureName = shop_unique + lastName;
            PicSaveUtil.handleFileUpId(file, request, shop_dir, shop_pictureName);//淇濆瓨鍥剧墖
            String shop_picture_path = File.separator + "image" + File.separator + shop_unique + File.separator + shop_pictureName;
            map.put("shop_image_path", FTPConfig.FILEURL + shop_picture_path);

            //保存成功，上传文件服务器
            try {
                InputStream is;
                is = new FileInputStream(new File(shop_dir + shop_pictureName));
                sftp.upload(FTPConfig.shop_head_path + "/" + shop_unique, shop_pictureName, is);
            } catch (FileNotFoundException e) {
                e.printStackTrace();
            } catch (SftpException e) {
                e.printStackTrace();
            }
        }
        //查询店铺实景图片
        List<Map<String, Object>> imageList = staffDao.queryShopImageList(map);

        //实景图片
        MultipartFile file2 = null;
        if (request instanceof MultipartHttpServletRequest) {
            MultipartHttpServletRequest multipartHttpServletRequest = (MultipartHttpServletRequest) request;
            Map<String, MultipartFile> mp = multipartHttpServletRequest.getFileMap();
            file2 = mp.get("shop_picture2");
        }
        if (file2 != null) {
//							System.out.println("已上传商品图片");
            String orName = file2.getOriginalFilename();
            String lastName = orName.substring(orName.lastIndexOf("."));
            String filePath = request.getServletContext().getRealPath("");// 获取项目所在的绝对路径
            filePath = File.separator + new File(filePath).getParent();
            String shop_dir = filePath + File.separator + "image" + File.separator + shop_unique
                    + File.separator;
            File dir = new File(shop_dir);
            if (!dir.exists()) {
                dir.mkdirs();
            }
            String shop_pictureName = UUID.randomUUID() + lastName;
            PicSaveUtil.handleFileUpId(file2, request, shop_dir, shop_pictureName);//淇濆瓨鍥剧墖
            String shop_picture_path = File.separator + "image" + File.separator + shop_unique + File.separator + shop_pictureName;
            Map<String, Object> imageMap = new HashMap<String, Object>();
            imageMap.put("shop_image_path", FTPConfig.FILEURL + shop_picture_path);
            imageMap.put("shop_unique", shop_unique);

            //保存成功，上传文件服务器
            try {
                InputStream is;
                is = new FileInputStream(new File(shop_dir + shop_pictureName));
                sftp.upload(FTPConfig.shop_head_path + "/" + shop_unique, shop_pictureName, is);
            } catch (FileNotFoundException e) {
                e.printStackTrace();
            } catch (SftpException e) {
                e.printStackTrace();
            }

            if (imageList != null && imageList.size() > 0) {
                imageList.get(0).put("shop_image_path", FTPConfig.FILEURL + shop_picture_path);
                staffDao.updateShopImage(imageList.get(0));
            } else {
                staffDao.saveShopImage(imageMap);
            }
        }
        MultipartFile file3 = null;
        if (request instanceof MultipartHttpServletRequest) {
            MultipartHttpServletRequest multipartHttpServletRequest = (MultipartHttpServletRequest) request;
            Map<String, MultipartFile> mp = multipartHttpServletRequest.getFileMap();
            file3 = mp.get("shop_picture3");
        }
        if (file3 != null) {
//							System.out.println("已上传商品图片");
            String orName = file3.getOriginalFilename();
            String lastName = orName.substring(orName.lastIndexOf("."));
            String filePath = request.getServletContext().getRealPath("");// 获取项目所在的绝对路径
            filePath = File.separator + new File(filePath).getParent();
            String shop_dir = filePath + File.separator + "image" + File.separator + shop_unique
                    + File.separator;
            File dir = new File(shop_dir);
            if (!dir.exists()) {
                dir.mkdirs();
            }
            String shop_pictureName = UUID.randomUUID() + lastName;
            PicSaveUtil.handleFileUpId(file3, request, shop_dir, shop_pictureName);//淇濆瓨鍥剧墖
            String shop_picture_path = File.separator + "image" + File.separator + shop_unique + File.separator + shop_pictureName;
            Map<String, Object> imageMap = new HashMap<String, Object>();
            imageMap.put("shop_image_path", FTPConfig.FILEURL + shop_picture_path);
            imageMap.put("shop_unique", shop_unique);

            //保存成功，上传文件服务器
            try {
                InputStream is;
                is = new FileInputStream(new File(shop_dir + shop_pictureName));
                sftp.upload(FTPConfig.shop_head_path + "/" + shop_unique, shop_pictureName, is);
            } catch (FileNotFoundException e) {
                e.printStackTrace();
            } catch (SftpException e) {
                e.printStackTrace();
            }

            if (imageList != null && imageList.size() > 1) {
                imageList.get(1).put("shop_image_path", FTPConfig.FILEURL + shop_picture_path);
                staffDao.updateShopImage(imageList.get(1));
            } else {
                staffDao.saveShopImage(imageMap);
            }
        }
        MultipartFile file4 = null;
        if (request instanceof MultipartHttpServletRequest) {
            MultipartHttpServletRequest multipartHttpServletRequest = (MultipartHttpServletRequest) request;
            Map<String, MultipartFile> mp = multipartHttpServletRequest.getFileMap();
            file4 = mp.get("shop_picture4");
        }
        if (file4 != null) {
//							System.out.println("已上传商品图片");
            String orName = file4.getOriginalFilename();
            String lastName = orName.substring(orName.lastIndexOf("."));
            String filePath = request.getServletContext().getRealPath("");// 获取项目所在的绝对路径
            filePath = File.separator + new File(filePath).getParent();
            String shop_dir = filePath + File.separator + "image" + File.separator + shop_unique
                    + File.separator;
            File dir = new File(shop_dir);
            if (!dir.exists()) {
                dir.mkdirs();
            }
            String shop_pictureName = UUID.randomUUID() + lastName;
            PicSaveUtil.handleFileUpId(file4, request, shop_dir, shop_pictureName);//淇濆瓨鍥剧墖
            String shop_picture_path = File.separator + "image" + File.separator + shop_unique + File.separator + shop_pictureName;
            Map<String, Object> imageMap = new HashMap<String, Object>();
            imageMap.put("shop_image_path", FTPConfig.FILEURL + shop_picture_path);
            imageMap.put("shop_unique", shop_unique);

            //保存成功，上传文件服务器
            try {
                InputStream is;
                is = new FileInputStream(new File(shop_dir + shop_pictureName));
                sftp.upload(FTPConfig.shop_head_path + "/" + shop_unique, shop_pictureName, is);
            } catch (FileNotFoundException e) {
                e.printStackTrace();
            } catch (SftpException e) {
                e.printStackTrace();
            }

            if (imageList != null && imageList.size() > 2) {
                imageList.get(2).put("shop_image_path", FTPConfig.FILEURL + shop_picture_path);
                staffDao.updateShopImage(imageList.get(2));
            } else {
                staffDao.saveShopImage(imageMap);
            }
        }
        String is_collecting_express_str = MUtil.strObject(map.get("is_collecting_express"));
        if (is_collecting_express_str != null && !is_collecting_express_str.equals("")) {
            Integer is_collecting_express = Integer.parseInt(is_collecting_express_str);//开启代收快递：1不开启 2开启
            Map<String, Object> params = new HashMap<String, Object>();
            params.put("shop_unique", shop_unique);
            params.put("express_type", 1);
            //删除店铺关联快递公司信息
            shopDao.deleteShopExpressRelation(shop_unique);
            if (is_collecting_express == 1) {//不开启
                //删除店铺快递设置信息
                shopDao.deleteShopExpress(params);
            } else {//开启
                params.put("staff_id", map.get("staff_id"));
                params.put("express_price", map.get("collecting_express_price"));
                //获取店铺快递设置信息
                Map<String, Object> express = shopDao.getShopExpress(params);
                if (express != null) {
                    //修改店铺快递设置信息
                    shopDao.updateShopExpress(params);
                } else {
                    Map<String, Object> moParams = new HashMap<String, Object>();
                    moParams.put("shop_unique", "0");
                    moParams.put("express_type", 1);
                    Map<String, Object> moMap = shopDao.getShopExpress(moParams);
                    //添加店铺快递设置信息
                    params.put("express_barcode", moMap.get("express_barcode"));
                    params.put("express_name", moMap.get("express_name"));
                    shopDao.addShopExpress(params);
                }
                //添加店铺关联快递公司信息
                String expressRelation = MUtil.strObject(map.get("expressRelation"));
                String[] expressRelationArr = expressRelation.split(",");
                List<Map<String, Object>> relationList = new ArrayList<Map<String, Object>>();
                for (int i = 0; i < expressRelationArr.length; i++) {
                    Map<String, Object> relation = new HashMap<String, Object>();
                    relation.put("shop_unique", shop_unique);
                    relation.put("express_id", expressRelationArr[i]);
                    relationList.add(relation);
                }
                shopDao.addShopExpressRelation(relationList);
            }
        }

        String is_send_express_str = MUtil.strObject(map.get("is_send_express"));
        if (is_send_express_str != null && !is_send_express_str.equals("")) {
            Integer is_send_express = Integer.parseInt(is_send_express_str);//开启代送快递：1不开启 2开启
            Map<String, Object> params = new HashMap<String, Object>();
            params.put("shop_unique", shop_unique);
            params.put("express_type", 2);
            if (is_send_express == 1) {//不开启
                //删除店铺快递设置信息
                shopDao.deleteShopExpress(params);
            } else {//开启
                params.put("staff_id", map.get("staff_id"));
                params.put("express_price", map.get("send_express_price"));
                //获取店铺快递设置信息
                Map<String, Object> express = shopDao.getShopExpress(params);
                if (express != null) {
                    //修改店铺快递设置信息
                    shopDao.updateShopExpress(params);
                } else {
                    Map<String, Object> moParams = new HashMap<String, Object>();
                    moParams.put("shop_unique", "0");
                    moParams.put("express_type", 2);
                    Map<String, Object> moMap = shopDao.getShopExpress(moParams);
                    //添加店铺快递设置信息
                    params.put("express_barcode", moMap.get("express_barcode"));
                    params.put("express_name", moMap.get("express_name"));
                    shopDao.addShopExpress(params);
                }
            }
        }
        if (shopHours != null && !shopHours.equals("[]")) {
            //删除店铺营业时间
            shopDao.deleteShopHours(shop_unique);
            //添加店铺营业时间
            List<Map<String, Object>> shopHoursList = MUtil.strToList(shopHours);
            shopDao.addShopHours(shopHoursList);
        }
        System.out.println(map);
        int k = shopDao.updateShopDetail(map);
        //更新供货端的店铺信息
        shopTDao.updateShopDetail(map);
        if (k == 1) {
            sr.setStatus(1);
            sr.setMsg("修改成功！");
            return sr;
        }
        sr.setStatus(0);
        sr.setMsg("失败！");
        return sr;
    }


    @Override
    public PurResult settlement(Map<String, Object> params) {
        PurResult result = new PurResult();
        try {
            List<Map<String, Object>> list = shopDao.settlementList(params);
//			Integer count = shopDao.settlementListCount(params)*5;//shopTest
            Integer count = shopDao.settlementListCount(params);
            result.setData(list);
            result.setCount(count);
            result.setStatus(1);
            result.setMsg("成功");
        } catch (Exception e) {
            e.printStackTrace();
            result.setStatus(0);
            result.setMsg("异常");
        }

        return result;
    }

    @Override
    public PurResult settlement2(Map<String, Object> params) {
        PurResult result = new PurResult();
        try {
            List<Map<String, Object>> list = shopDao.settlementListNY(params);
            Integer count = shopDao.settlementNYListCount(params);
            result.setData(list);
            result.setCount(count);
            result.setStatus(1);
            result.setMsg("成功");
        } catch (Exception e) {
            e.printStackTrace();
            result.setStatus(0);
            result.setMsg("异常");
        }

        return result;
    }

    @Override
    public PurResult shopCouponCashList(Map<String, Object> params) {
        PurResult result = new PurResult();
        try {
            List<Map<String, Object>> list = shopDao.shopCouponCashList(params);
            Integer count = shopDao.shopCouponCashListCount(params);
            result.setData(list);
            result.setCount(count);
            result.setStatus(1);
            result.setMsg("成功");
        } catch (Exception e) {
            e.printStackTrace();
            result.setStatus(0);
            result.setMsg("异常");
        }

        return result;
    }


    @Override
    @Transactional
    public PurResult settlementConfirm(Map<String, Object> params, String phone, String payMoney, String shopName, String bankName, String bankAccount) {
        PurResult result = new PurResult();
        try {
            String shop_unique = MUtil.strObject(params.get("shop_unique"));
            Double take_money = Double.valueOf(MUtil.strObject(params.get("take_money")));
            Double take_money2 = Double.valueOf(MUtil.strObject(params.get("take_money2")));
            Double take_money3 = Double.valueOf(MUtil.strObject(params.get("take_money3")));
            Double bean = Double.valueOf(MUtil.strObject(params.get("bean")));

            //获取商家余额
            ShopVO shopVO = shopDao.getShopBalance(shop_unique);
            if (shopVO.getBalance() < take_money) {
                result.setStatus(2);
                result.setMsg("余额不足");

                return result;
            }
            //添加结算记录
            String order_id = "js" + System.currentTimeMillis();
            params.put("order_id", order_id);
            shopDao.addShopCouponCash(params);
            //修改商家余额
            Map<String, Object> balanceParams = new HashMap<String, Object>();
            balanceParams.put("shop_unique", shop_unique);
            balanceParams.put("shop_balance", take_money2);
            balanceParams.put("lkl_balance", take_money3);

            if (bean < 0) {
                balanceParams.put("bean", bean);
            } else {
                balanceParams.put("bean", null);
            }

            shopDao.updateShopBalance(balanceParams);

            //发送短信
            if (bankAccount.length() > 4) {
                bankAccount = bankAccount.substring(bankAccount.length() - 4, bankAccount.length());
            }
            JSONObject jo = new JSONObject();
            BigDecimal b1 = new BigDecimal(Double.toString(take_money));
            BigDecimal payMoney2 = new BigDecimal(payMoney.trim());
            String serviceFee = b1.subtract(payMoney2).setScale(2, BigDecimal.ROUND_DOWN).toString();
            jo.put("revenue", Double.toString(take_money));
            jo.put("serviceFee", serviceFee);
            jo.put("income", payMoney);

            //短信发送，搞成本地不发送
            if (IPGet.ExtranetIP.equals(AddOrderTask.IP18)) {
                SmsThread smsThread = new SmsThread(phone, jo.toString(), "SMS_173406054");
                Thread t = new Thread(smsThread);
                t.start();
            }
            result.setStatus(1);
            result.setMsg("成功");
        } catch (Exception e) {
            e.printStackTrace();
            result.setStatus(0);
            result.setMsg("异常");
        }

        return result;
    }

    @Override
    public ShopVO getShopBalance(String shop_unique) {
        return shopDao.getShopBalance(shop_unique);
    }

    @Override
    public Double getShopBalanceDay(String shop_unique) {
        return shopDao.getShopBalanceDay(shop_unique);
    }

    @Override
    public Double getShopBalanceDay2(String shop_unique) {
        return shopDao.getShopBalanceDay2(shop_unique);
    }

    @Override
    public Double getShopBalanceDay3(String shop_unique) {
        return shopDao.getShopBalanceDay3(shop_unique);
    }

    @Override
    public Map<String, Object> getShopCardInfo(String shop_unique) {
        return shopDao.getShopCardInfo(shop_unique);
    }

    @Override
    public ShopVO getSysRate(String shop_unique) {
        return shopDao.getSysRate(shop_unique);
    }

    @Override
    @Transactional
    public PurResult updateShowBuyStatus(Map<String, Object> params) {
        PurResult result = new PurResult();
        try {
            shopDao.updateShowBuyStatus(params);
            String shop_unique = MUtil.strObject(params.get("shop_unique"));
            //添加百货豆默认抵扣比例信息
            PageData pd = new PageData();
            ShopBeansVO beanDetai = beansDao.getShopBeans(shop_unique);
            pd.put("diKou", beanDetai.getDiKou());
            pd.put("shopUnique", shop_unique);
            int resultPd = beansDao.queryeDikou(pd);
            if (resultPd > 0) {
                beansDao.updateDikou(pd);
            } else {
                beansDao.addDikou(pd);
            }

            //添加百货豆抵扣信息，查看是否已添加
            List<PageData> list = new ArrayList<>();
            PageData pageData = new PageData();
            pageData.put("shop_unique", params.get("shop_unique"));
            pageData.put("reward", "20");
            pageData.put("rewart_type", "0");
            pageData.put("valid", "1");
            list.add(pageData);
            List<PageData> haveList = activityDao.queryRewardRepeat(list);
            if (null == haveList || haveList.isEmpty()) {//没有已知的数据，添加
                activityDao.addRewardRepeat(list);
            }

            //同步一刻钟商家信息
            Map<String, Object> shopInfo = shopDao.getShopMsgDetail(params);
            String shopUnique = MUtil.strObject(shopInfo.get("shopUnique"));
            String shopName = MUtil.strObject(shopInfo.get("shopName"));
            String shopAddressDetail = MUtil.strObject(shopInfo.get("shopAddress"));
            String domainPath = MUtil.strObject(shopInfo.get("domainPath"));
            String shopImagePath = MUtil.strObject(shopInfo.get("shopImagePath"));
            String shopPhone = MUtil.strObject(shopInfo.get("shopPhone"));
            Integer examinestatus = Integer.parseInt(MUtil.strObject(shopInfo.get("examinestatus")));
            Double longitude = Double.valueOf(MUtil.strObject(shopInfo.get("longitude")));
            Double latitude = Double.valueOf(MUtil.strObject(shopInfo.get("latitude")));
            Integer showBuyStatus = Integer.parseInt(MUtil.strObject(shopInfo.get("showBuyStatus")));
            String shopInfos = "shopUnique=" + shopUnique
                    + "&shopName=" + shopName
                    + "&shopAddressDetail=" + shopAddressDetail
                    + "&domainPath=" + domainPath
                    + "&shopImagePath=" + shopImagePath
                    + "&shopPhone=" + shopPhone
                    + "&examinestatus=" + examinestatus
                    + "&longitude=" + longitude
                    + "&latitude=" + latitude
                    + "&showBuyStatus=" + showBuyStatus;
            HttpsUtil.sendPost("http://delivery.buyhoo.cc/outside/updateShopInfo", shopInfos);
            result.setStatus(1);
            result.setMsg("成功");
        } catch (Exception e) {
            e.printStackTrace();
            result.setStatus(0);
            result.setMsg("异常");
        }

        return result;
    }

    @Override
    @Transactional
    public PurResult updateShopIsDis(Map<String, Object> params) {
        PurResult result = new PurResult();
        try {
            shopDao.updateShowBuyStatus(params);
            String shop_unique = MUtil.strObject(params.get("shop_unique"));
            String is_dis = MUtil.strObject(params.get("is_dis"));
            if (is_dis != null && is_dis.equals("2")) {//开启
                Map<String, Object> disParams = new HashMap<String, Object>();
                disParams.put("shop_unique", shop_unique);
                List<Map<String, Object>> disLevelList = shopDao.getDisLevelList(disParams);
                if (disLevelList.size() == 0) {
                    disParams.put("shop_unique", "0");
                    List<Map<String, Object>> disLevelListMoren = shopDao.getDisLevelList(disParams);
                    List<Map<String, Object>> list = new ArrayList<Map<String, Object>>();
                    for (int i = 0; i < disLevelListMoren.size(); i++) {
                        Map<String, Object> map = new HashMap<String, Object>();
                        map.put("shop_unique", shop_unique);
                        map.put("dis_level_name", disLevelListMoren.get(i).get("dis_level_name"));
                        map.put("level", disLevelListMoren.get(i).get("level"));
                        map.put("one_commission_ratio", disLevelListMoren.get(i).get("one_commission_ratio"));
                        map.put("two_commission_ratio", disLevelListMoren.get(i).get("two_commission_ratio"));
                        list.add(map);
                    }
                    shopDao.addDisLevel(list);
                }
            }

            result.setStatus(1);
            result.setMsg("成功");
        } catch (Exception e) {
            e.printStackTrace();
            result.setStatus(0);
            result.setMsg("异常");
        }

        return result;
    }

    @Override
    public PurResult queryWechatExamineList(Map<String, Object> params) {
        PurResult result = new PurResult();
        try {
            List<Map<String, Object>> list = shopDao.queryWechatExamineList(params);
//			Integer count = shopDao.queryWechatExamineListCount(params)*5;//shopTest
            Integer count = shopDao.queryWechatExamineListCount(params);
            result.setData(list);
            result.setCount(count);
            result.setStatus(1);
            result.setMsg("成功");
        } catch (Exception e) {
            e.printStackTrace();
            result.setStatus(0);
            result.setMsg("异常");
        }

        return result;
    }

    @Override
    public PurResult shopList(Map<String, Object> params) {
        PurResult result = new PurResult();
        try {
            List<Map<String, Object>> list = shopDao.getshopList(params);
//			Integer count = shopDao.getshopListCount(params)*5;//shopTest
            Integer count = shopDao.getshopListCount(params);
            result.setData(list);
            result.setCount(count);
            result.setStatus(1);
            result.setMsg("成功");
        } catch (Exception e) {
            e.printStackTrace();
            result.setStatus(0);
            result.setMsg("异常");
        }

        return result;
    }

    @Override
    public Map<String, Object> getShopDetail(String shop_unique) {
        return shopDao.getShopDetail(shop_unique);
    }


    @Override
    public List<Map<String, Object>> getShopExpressRelationList(String shop_unique) {
        return shopDao.getShopExpressRelationList(shop_unique);
    }

    @Override
    public List<Map<String, Object>> getShopHoursList(String shop_unique) {
        return shopDao.getShopHoursList(shop_unique);
    }

    @Override
    @Transactional
    public PurResult queryShopDisLevelList(Map<String, Object> params) {
        PurResult result = new PurResult();
        try {
            List<Map<String, Object>> disLevelList = shopDao.getDisLevelList(params);
            result.setData(disLevelList);
            result.setStatus(1);
            result.setMsg("成功");
        } catch (Exception e) {
            e.printStackTrace();
            result.setStatus(0);
            result.setMsg("异常");
        }

        return result;
    }

    @Override
    public Map<String, Object> queryShopDisLevel(String dis_level_id) {
        Map<String, Object> result = shopDao.queryShopDisLevel(dis_level_id);
        Map<String, Object> params = new HashMap<String, Object>();
        params.put("shop_unique", result.get("shop_unique"));
        params.put("dis_level_id", dis_level_id);
        List<Map<String, Object>> conditionList = shopDao.queryDisLevelConditionList(params);
        result.put("conditionList", conditionList);
        result.put("conditionCount", conditionList.size());
        List<Map<String, Object>> goodsList = shopDao.queryDisGoodsList(params);
        result.put("goodsList", goodsList);
        List<Map<String, Object>> goodsKindList = shopDao.queryDisGoodsKindList(params);
        result.put("goodsKindList", goodsKindList);
        return result;
    }

    @Override
    @Transactional
    public PurResult updateDisLevel(Map<String, Object> params) {
        PurResult result = new PurResult();
        try {
            String dis_level_id = MUtil.strObject(params.get("dis_level_id"));
            shopDao.updateDisLevel(params);
            //删除升级条件
            shopDao.deleteDisLevelCondition(dis_level_id);
            //添加升级条件
            List<Map<String, Object>> conditionList = MUtil.strToList(MUtil.strObject(params.get("condition_list")));
            if (conditionList.size() > 0) {
                List<Map<String, Object>> addList = new ArrayList<Map<String, Object>>();
                for (int i = 0; i < conditionList.size(); i++) {
                    Map<String, Object> condition = conditionList.get(i);
                    String up_commission = MUtil.strObject(condition.get("up_commission"));
                    Integer up_commission_relation = null;
                    if (up_commission != null && !up_commission.equals("")) {
                        up_commission_relation = Integer.parseInt(MUtil.strObject(condition.get("up_commission_relation")));
                    } else {
                        up_commission = null;
                    }

                    String up_order_total = MUtil.strObject(condition.get("up_order_total"));
                    Integer up_order_total_relation = null;
                    if (up_order_total != null && !up_order_total.equals("")) {
                        up_order_total_relation = Integer.parseInt(MUtil.strObject(condition.get("up_order_total_relation")));
                    } else {
                        up_order_total = null;
                    }

                    String up_dis_num = MUtil.strObject(condition.get("up_dis_num"));
                    Integer up_dis_num_relation = null;
                    if (up_dis_num != null && !up_dis_num.equals("")) {
                        up_dis_num_relation = Integer.parseInt(MUtil.strObject(condition.get("up_dis_num_relation")));
                    } else {
                        up_dis_num = null;
                    }

                    String up_cus_num = MUtil.strObject(condition.get("up_cus_num"));
                    Integer up_cus_num_relation = null;
                    if (up_cus_num != null && !up_cus_num.equals("")) {
                        up_cus_num_relation = Integer.parseInt(MUtil.strObject(condition.get("up_cus_num_relation")));
                    } else {
                        up_cus_num = null;
                    }

                    String up_lower_order_total = MUtil.strObject(condition.get("up_lower_order_total"));
                    Integer up_lower_order_total_relation = null;
                    if (up_lower_order_total != null && !up_lower_order_total.equals("")) {
                        up_lower_order_total_relation = Integer.parseInt(MUtil.strObject(condition.get("up_lower_order_total_relation")));
                    } else {
                        up_lower_order_total = null;
                    }

                    String up_goods_barcode = MUtil.strObject(condition.get("up_goods_barcode"));
                    Integer up_goods_barcode_relation = null;
                    if (up_goods_barcode != null && !up_goods_barcode.equals("")) {
                        up_goods_barcode_relation = Integer.parseInt(MUtil.strObject(condition.get("up_goods_barcode_relation")));
                    } else {
                        up_goods_barcode = null;
                    }

                    Map<String, Object> conditionParams = new HashMap<String, Object>();
                    conditionParams.put("dis_level_id", dis_level_id);
                    conditionParams.put("up_commission", up_commission);
                    conditionParams.put("up_commission_relation", up_commission_relation);
                    conditionParams.put("up_order_total", up_order_total);
                    conditionParams.put("up_order_total_relation", up_order_total_relation);
                    conditionParams.put("up_dis_num", up_dis_num);
                    conditionParams.put("up_dis_num_relation", up_dis_num_relation);
                    conditionParams.put("up_cus_num", up_cus_num);
                    conditionParams.put("up_cus_num_relation", up_cus_num_relation);
                    conditionParams.put("up_lower_order_total", up_lower_order_total);
                    conditionParams.put("up_lower_order_total_relation", up_lower_order_total_relation);
                    conditionParams.put("up_goods_barcode", up_goods_barcode);
                    conditionParams.put("up_goods_barcode_relation", up_goods_barcode_relation);
                    addList.add(conditionParams);
                }
                shopDao.addDisLevelCondition(addList);
            }
            //分销商品类型
            int dis_goods_type = Integer.parseInt(MUtil.strObject(params.get("dis_goods_type")));
            if (dis_goods_type == 3) {
                List<Map<String, Object>> goods_list = MUtil.strToList(MUtil.strObject(params.get("goods_list")));
                if (goods_list.size() > 0) {
                    for (int i = 0; i < goods_list.size(); i++) {
                        Map<String, Object> goods = goods_list.get(i);
                        goods.put("shop_unique", params.get("shop_unique"));
                        goods.put("dis_level_id", dis_level_id);
                    }
                    shopDao.deleteDisGoods(params);
                    shopDao.addDisGoods(goods_list);
                }

            } else if (dis_goods_type == 2) {
                List<Map<String, Object>> goods_kind_list = MUtil.strToList(MUtil.strObject(params.get("goods_kind_list")));
                if (goods_kind_list.size() > 0) {
                    for (int i = 0; i < goods_kind_list.size(); i++) {
                        Map<String, Object> goodsKind = goods_kind_list.get(i);
                        goodsKind.put("dis_level_id", dis_level_id);
                    }
                    shopDao.deleteDisGoodsKind(params);
                    shopDao.addDisGoodsKind(goods_kind_list);
                }

            }
            result.setStatus(1);
            result.setMsg("成功");
        } catch (Exception e) {
            e.printStackTrace();
            result.setStatus(0);
            result.setMsg("异常");
        }

        return result;
    }


    @Override
    public List<Map<String, Object>> getDisLevelList(Map<String, Object> params) {
        return shopDao.getDisLevelList(params);
    }

    @Override
    public int queryDifferentDisLevelCount(Map<String, Object> params) {
        return shopDao.queryDifferentDisLevelCount(params);

    }

    @Override
    public Map<String, Object> querySetCommissionRatio(Map<String, Object> params) {
        return shopDao.querySetCommissionRatio(params);
    }

    @Override
    public ShopsResult verifyUserInfoService(String manager_account, String manager_pwd) {
//		ShopsResult shopsResult = new ShopsResult();
        if (StringUtil.isBlank(manager_account)) {
            return ShopsResult.fail("手机号为空");
        }
        String mobile = "^1(3\\d|4[5-9]|5[0-35-9]|6[567]|7[0-8]|8\\d|9[0-35-9])\\d{8}$";
        if (!Pattern.compile(mobile).matcher(manager_account).matches()) {
            return ShopsResult.fail("请输入正确手机号");
        }

        if (manager_pwd.length() < 8) {
            return ShopsResult.fail("密码最少为8位");
        }
        String password = "^(?=.*[A-Za-z])(?=.*\\d)[A-Za-z\\d]+$";
        if (!Pattern.compile(password).matcher(manager_pwd).matches()) {
            return ShopsResult.fail("密码应包含数字和字母");
        }

        QueryShopInfoPo po = new QueryShopInfoPo();
        po.setManagerAccount(manager_account);
        Map<String, Object> shopInfo = staffDao.queryStaffByParams(po);
        if (null == shopInfo) {
            return ShopsResult.ok("验证成功手机号密码可用");
        }
        return ShopsResult.fail("手机号已存在");
    }

    @Override
    public ShopsResult verifyShopInfoService(String shop_name) {
        if (StringUtil.isBlank(shop_name)) {
            return ShopsResult.fail("店铺名为空");
        }
        QueryShopInfoPo po = new QueryShopInfoPo();
        po.setShopName(shop_name);
        Map<String, Object> shopInfo = shopDao.queryShopInfoByParams(po);
        if (null == shopInfo) {
            return ShopsResult.ok("店铺名可用");
        }
        return ShopsResult.fail("店铺名已存在，请修改");
    }

	@Override
	public ShopsResult togetherCode(Map<String, Object> params) {
		
        List<Map<String, Object>> list = shopDao.getTogetherCode(params);
        Integer count = shopDao.getTogetherCodeCount(params);

        return ShopsResult.pageOK( list,count);
	}

	@Override
	public ShopsResult updateTogetherCode(String shop_unique, String aggregate_audit_status,
			 String aggregation_code_id,String aggregate_refuse_reason) {

		  	Map<String, Object> params = new HashMap<String, Object>();
	        params.put("shop_unique", shop_unique);
	        params.put("aggregate_audit_status", aggregate_audit_status);
	        params.put("aggregate_refuse_reason", aggregate_refuse_reason);
	        params.put("aggregation_code_id", aggregation_code_id);
	        int result = shopDao.updateTogetherCode(params);
	        
	        if(aggregate_audit_status.equals("3"))
	        {
	        	shopDao.updateTogetherCodeDetail(params);
	        }else
	        {
	        	ShopQualificationInfoEntity info=shopDao.queryTogetherCodeInfo(params);
	        	
	        	//审核通过，保存资质
	        	shopDao.insertQualificationInfo(info);
	        	
	        }
	        if (result == 1) {
	            return ShopsResult.ok("操作成功！");
	        }
	        return ShopsResult.fail("操作失败！");
	}

    @Override
    public ShopsResult editGoodsInPriceType(ShopGoodsInPriceTypeEditParams params) {
        ShopsResult result = new ShopsResult(1, "保存成功");
        ShopConfigQueryResult shopConfigQueryResult = shopDao.queryShopConfig(params.getShopUnique());
        if (ObjectUtil.isNotEmpty(shopConfigQueryResult)) {
            shopConfigQueryResult.setGoodsInPriceType(params.getGoodsInPriceType());
            shopDao.updateShopConfig(shopConfigQueryResult);
        } else {
            shopConfigQueryResult = new ShopConfigQueryResult();
            shopConfigQueryResult.setShopUnique(Long.valueOf(params.getShopUnique()));
            shopConfigQueryResult.setGoodsInPriceType(params.getGoodsInPriceType());
            shopDao.insertShopConfig(shopConfigQueryResult);
        }
        return result;
    }

    @Override
    public ShopConfigQueryResult queryShopConfig(String shopUnique) {
        return shopDao.queryShopConfig(shopUnique);
    }

    @Override
    public List<ShopsEntity> createBatchShopPayCode(Long startShopId, Long endShopId) {
        Map<String, Object> params = new HashMap<>();
        params.put("startShopId", startShopId);
        params.put("endShopId", endShopId);
        return shopDao.createBatchShopPayCode(params);
    }

    @Override
	public Map<String, Object> getTogetherCodeInfo(String shop_unique) {
		// TODO Auto-generated method stub
		return shopDao.getTogetherCodeInfo(shop_unique);
	}
}
