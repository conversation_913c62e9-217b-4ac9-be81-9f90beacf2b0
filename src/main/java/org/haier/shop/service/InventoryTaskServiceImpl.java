package org.haier.shop.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.haier.shop.enums.DeviceTypeEnum;
import org.haier.shop.util.*;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.OutputStream;
import java.net.URLEncoder;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

@Service
public class InventoryTaskServiceImpl implements InventoryTaskService{


    /**
     * 任务列表
     * @param map
     * @return
     */
    @Override
    public PurResult taskList(Map<String, Object> map, HttpServletRequest request) {
        String host = UtilForJAVA.getHostByServerName(request.getServerName());
        String url = StringUtils.join(host,"/shopUpdate/inventoryTask/taskList.do");

        map.put("pageIndex", MapUtils.getString(map,"page"));
        map.put("pageSize",MapUtils.getString(map,"limit"));
        map.put("from", DeviceTypeEnum.WEB.getName());
        map.remove("page");
        map.remove("limit");

        //请求shopUpdate
        String resp = HttpClientUtil.httpPost(url, JSON.toJSONString(map), false);
        JSONObject obj = JSON.parseObject(resp);
        Integer status = obj.getInteger("status");
        if (status != 1) return PurResult.fail(obj.getString("msg"));

        return PurResult.ok(obj.getJSONObject("data").getJSONArray("taskList"),obj.getInteger("count"));
    }

    /**
     * 盘库单预览
     * @param request
     * @return
     */
    @Override
    public PurResult taskPreview(HttpServletRequest request) {
        Map<String, Object> map = HandleMessyCode.handle(ServletsUtil.getParameters(request));

        String host = UtilForJAVA.getHostByServerName(request.getServerName());
        String url = StringUtils.join(host,"/shopUpdate/inventoryTask/taskPreview.do");

        map.put("pageIndex", MapUtils.getString(map,"page"));
        map.put("pageSize",MapUtils.getString(map,"limit"));
        map.put("from", DeviceTypeEnum.WEB.getName());
        map.remove("page");
        map.remove("limit");
        //请求shopUpdate
        String resp = HttpClientUtil.httpPost(url, JSON.toJSONString(map), false);
        JSONObject obj = JSON.parseObject(resp);
        Integer status = obj.getInteger("status");
        if (status != 1) return PurResult.fail(obj.getString("msg"));

        JSONObject data = obj.getJSONObject("data");
        data.put("count", obj.getInteger("count"));

        return PurResult.ok(data);
    }

    /**
     * 单个商品明细-商品盘点
     * @param request
     * @return
     */
    @Override
    public PurResult taskGoodsDetail(HttpServletRequest request) {
        Map<String, Object> map = HandleMessyCode.handle(ServletsUtil.getParameters(request));

        String host = UtilForJAVA.getHostByServerName(request.getServerName());
        String url = StringUtils.join(host,"/shopUpdate/inventoryTask/taskGoodsDetail.do");

        //请求shopUpdate
        String resp = HttpClientUtil.httpPost(url, JSON.toJSONString(map), false);
        JSONObject obj = JSON.parseObject(resp);
        Integer status = obj.getInteger("status");
        if (status != 1) return PurResult.fail(obj.getString("msg"));

        return PurResult.ok(obj.getJSONObject("data"));
    }

    /**
     * 盘库单下载
     * @param request
     * @param response
     */
    @Override
    public void exportInventoryExcel(HttpServletRequest request, HttpServletResponse response) {
        Map<String, Object> map = HandleMessyCode.handle(ServletsUtil.getParameters(request));

        String host = UtilForJAVA.getHostByServerName(request.getServerName());
        String url = StringUtils.join(host,"/shopUpdate/inventoryTask/taskPreviewDownload.do");
        //请求shopUpdate
        String resp = HttpClientUtil.httpPost(url, JSON.toJSONString(map), false);
        JSONObject obj = JSON.parseObject(resp);
        Integer status = obj.getInteger("status");
        if (status != 1) return ;

        JSONObject data = obj.getJSONObject("data");
        JSONArray taskDetailArray = data.getJSONArray("taskDetailList");
        String[] titles=new String[] {
                "商品名称","商品条码","盘点前库存","盘点数","进货价","盈亏数","盈亏金额"
        };
        loadXLS(taskDetailArray,StringUtils.join("盘点单-",data.getString("taskName")),request,response,titles);

    }

    private void loadXLS(JSONArray taskDetailArray, final String fileName, HttpServletRequest request, HttpServletResponse response, final String[] titles){
        try {
            List<Map<String,Object>> listmap = new ArrayList<>();
            for (int i = 0; i < taskDetailArray.size(); i++) {
                JSONObject jsonObject = taskDetailArray.getJSONObject(i);
                listmap.add(jsonObject);
            }

            response.setContentType(
                    "application/x-msdownload; charset=UTF-8");// 告知浏览器文件类型
            String agent = request.getHeader("user-agent");
            if (agent.contains("Firefox")) {
                response.setHeader(
                        "Content-Disposition",
                        "attachment;filename="
                                + new String((fileName + ".xls")
                                .getBytes("GB2312"), "ISO-8859-1"));// 设置文件头
            } else {
                response.setHeader(
                        "Content-Disposition",
                        "attachment;filename="
                                + URLEncoder.encode(fileName + ".xls", "UTF-8"));// 设置文件头
            }
            OutputStream os = response.getOutputStream();
            LoadOutObjectXLSUtil<Map<String, Object>> objectXLS = new LoadOutObjectXLSUtil<Map<String, Object>>();
            objectXLS.generateXLS(os,listmap , new XLSCallBack<Map<String, Object>>() {
                public Object[] getValues(Map<String, Object> tt) {
                    String str = "";

                    String goodsName = tt.get("goodsName")+"";
                    if (StringUtil.blank(goodsName)) {
                        goodsName = str;
                    }
                    String goodsBarcode =  tt.get("goodsBarcode")+"";
                    if (StringUtil.blank(goodsBarcode)) {
                        goodsBarcode = str;
                    }
                    String preStock =tt.get("preStock")+"";
                    if (StringUtil.blank(preStock)) {
                        preStock = str;
                    }
                    String inventoryCount = tt.get("inventoryCount")+"";
                    if (StringUtil.blank(inventoryCount)) {
                        inventoryCount = str;
                    }
                    String goodsInPrice = tt.get("goodsInPrice")+"";
                    if (StringUtil.blank(goodsInPrice)) {
                        goodsInPrice = str;
                    }
                    String diffCount = tt.get("diffCount")+"";
                    if (StringUtil.blank(diffCount)) {
                        diffCount = str;
                    }
                    String diffMoney = tt.get("diffMoney")+"";
                    if (StringUtil.blank(diffMoney)) {
                        diffMoney = str;
                    }

                    return new String[] {goodsName,goodsBarcode,preStock,
                            inventoryCount,goodsInPrice,diffCount,diffMoney
                    };
                }

                public String getTitle() {
                    return fileName;
                }

                public int[] getColumnsWidth() {
                    return new int[] {
                            80 * 80, 80 * 80,80 * 80,80 * 80,
                            80 * 80, 80 * 80,80 * 80,80 * 80,
                            80 * 80,80 * 80,80 * 80};
                }

                public String[] getColumnsName() {
                    return titles;
                }

                public int[][] MergedRegion() {
                    // TODO Auto-generated method stub
                    return null;
                }

                public List<Object[]> getheaderValue() {
                    // TODO Auto-generated method stub
                    return null;
                }

            });
            os.flush();
        } catch (IOException e) {
            e.printStackTrace();
        }
    }
}
