package org.haier.shop.service;

import org.haier.shop.util.ShopsResult;

/**
* @author: 作者:王恩龙
* @version: 2023年6月14日 下午4:22:47
*
*/
public interface ProjectMsgService {
	
	/**
	 * 添加新的项目名称
	 * @param project_name
	 * @return
	 */
	public ShopsResult addNewProjectMsg(String project_name,String project_type,String remarks);
	/**
	 * 查询指定页码的项目信息
	 * 
	 * @param page
	 * @param limit
	 * @return
	 */
	public ShopsResult queryProjectMsgList(Integer page, Integer limit, Integer project_type);

	public ShopsResult queryShopsDeviceVeriosn(Integer page, Integer limit, String shopName,Integer handleStatus);


	public ShopsResult updateShopsVersion(String ids, String version);

}
