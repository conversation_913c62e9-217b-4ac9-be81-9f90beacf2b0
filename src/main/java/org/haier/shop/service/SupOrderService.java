package org.haier.shop.service;

import java.util.List;
import java.util.Map;

import org.haier.shop.util.PurResult;

public interface SupOrderService {
	
	/**
	 * 查询退款订单信息
	 * @param shopUnique 店铺编号
	 * @param page 页码
	 * @param pageSize 单页查询数量
	 * @param startTime 开始时间
	 * @param endTime 结束时间
	 * @param msg 输入框输入的信息，查询单号，供货商名称
	 * @return
	 */
	public PurResult getSupRetOrderList(String shopUnique,Integer page,Integer pageSize,String startTime,String endTime,String msg ) ;
	/**
	 * 查询进货订单列表
	 * @param shop_unique
	 * @param search_str 订单编号/供货商名称
	 * @param start_date 查询开始时间
	 * @param end_date 查询结束时间
	 * @param order_status 订单状态1: 待发货 2:待配送 3:配送中 4:已完成
	 * @param pay_status 支付状态：1、欠款；2、已结清
	 * @param order_type 订单类型 0：自动下单；1：客户下单
	 * @param startNum
	 * @param pageSize
	 * @param order_source 订单来源：1云商/总店 2自采购
	 * @return
	 */
	public PurResult getSupOrderList(Map<String ,Object> params,String order_source);
	
	/**
	 * 查询进货订单列表
	 * @param shop_unique
	 * @param search_str 订单编号/供货商名称
	 * @param start_date 查询开始时间
	 * @param end_date 查询结束时间
	 * @param order_status 订单状态1: 待发货 2:待配送 3:配送中 4:已完成
	 * @param pay_status 支付状态：1、欠款；2、已结清
	 * @param order_type 订单类型 0：自动下单；1：客户下单
	 * @return
	 */
	public List<Map<String ,Object>> getOrderList(Map<String ,Object> params);
	
	/**
	 * 查询自采购进货订单列表
	 * @return
	 */
	public List<Map<String ,Object>> getSelfOrderList(Map<String ,Object> params);
	
	/**
	 * 获取云商订单详情
	 * @param order_code 订单编号
	 * @return
	 */
	public PurResult getSupOrderDetail(String order_code);
	
	/**
	 * 取消云商进货单
	 * @param order_code 订单编号
	 * @param order_status 5:已取消
	 * @return
	 */	
	public PurResult updateOrderStatus(Map<String ,Object> params);
}
