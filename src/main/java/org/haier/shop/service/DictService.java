package org.haier.shop.service;

import java.util.Map;

import org.haier.shop.util.PurResult;



public interface DictService {
	public PurResult queryDictList(Map<String, Object> params);

	public PurResult queryParentDictList(Map<String, Object> params);

	public PurResult addDict(String area_dict_parent_num, String area_dict_content, String sort);

	public PurResult deleteDict(String area_dict_id);

	public PurResult queryDictById(String area_dict_id);

	public PurResult editDict(String area_dict_parent_num, String area_dict_content, String sort, String area_dict_id);
}
