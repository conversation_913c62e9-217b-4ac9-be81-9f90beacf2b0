package org.haier.shop.service;

import java.util.Map;

import org.haier.shop.entity.ShopCourier;
import org.haier.shop.util.PurResult;
import org.haier.shop.util.ShopsResult;

public interface DeliveryService {

	//获取店铺配送信息
	public Map<String ,Object> queryShopDelivery(String shop_unique);

	//修改店铺配送信息
	public ShopsResult updateShopDelivery(Map<String ,Object> params);
	
	//获取店铺骑手列表
	public PurResult getShopCourierList(ShopCourier shopCourier);
		
	//添加店铺骑手信息
	public ShopsResult addShopCourier(ShopCourier shopCourier);
		
	//获取店铺骑手详情
	public ShopCourier getShopCourier(String courier_id);
		
	//修改店铺骑手信息
	public ShopsResult updateShopCourier(ShopCourier shopCourier);
		
	//删除店铺骑手
	public ShopsResult deleteShopCourier(String courier_id);
	
	//获取店铺特殊商品列表
	public PurResult getShopSpecialList(Map<String ,Object> params);
	
	//删除店铺特殊商品
	public ShopsResult deleteShopSpecial(String goods_id);
	
	//获取店铺未加入特殊商品列表
	public PurResult getShopGoodsList(Map<String ,Object> params);
	
	//添加店铺特殊商品
	public ShopsResult addShopSpecial(String goods_infos);
}
