package org.haier.shop.service;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Random;

import javax.annotation.Resource;

import org.haier.shop.dao.CountMsgYiNongDao;
import org.haier.shop.dao2.PurchaseDataDao;
import org.haier.shop.util.DateUtils;
import org.haier.shop.util.PurResult;
import org.haier.shop.util.ShopsResult;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;


@Service("countMsgYiNongService")
@Transactional
public class CountMsgYiNongServiceImpl implements CountMsgYiNongService{
	
	@Resource
	private CountMsgYiNongDao countMsgYiNongDao;
	
	@Resource
	private PurchaseDataDao purchaseDataDao;
	
	
	@Override
	public PurResult queryShopIsCenter(String shop_unique) {
		PurResult sr=new PurResult(1, "查询成功！");
		Map<String, Object> params=new HashMap<String,Object>();
		params.put("shop_unique", shop_unique);
		Map<String,Object> data=countMsgYiNongDao.queryShopIsCenter(params);
		sr.setData(data);
		return sr;
	}


	@Override
	public PurResult queryChildSaleCount(String shop_unique, String type) {
		PurResult sr=new PurResult(1, "查询成功！");
		Map<String, Object> params=new HashMap<String,Object>();
		params.put("shop_unique", shop_unique);
		params.put("type", type);
		List<Map<String,Object>> data=countMsgYiNongDao.queryChildSaleCount(params);
		sr.setData(data);
		return sr;
	}


	@Override
	public PurResult queryOrderMsg(String shop_unique, String type) {
		PurResult sr=new PurResult(1, "查询成功！");
		Map<String, Object> params=new HashMap<String,Object>();
		params.put("shop_unique", shop_unique);
		params.put("type", type);
		Map<String,Object> data=countMsgYiNongDao.queryOrderMsg(params);
		sr.setData(data);
		//查询热销商品
		List<Map<String,Object>> goods_list=countMsgYiNongDao.queryHotSaleGoods(params);
		sr.setCord(goods_list);
		return sr;
	}


	@Override
	public PurResult querySaleCount(String shop_unique, String type) {
		PurResult pr=new PurResult();
		Map<String,Object> map=new HashMap<String, Object>();
		map.put("shop_unique", shop_unique);
		map.put("type", type);
		String[] dates=null;
		int length=0;
		if("3".equals(type)){
			length=7;
		}else if ("4".equals(type)){
			length=30;
		}
		if("3".equals(type)||"4".equals(type)){
			dates=new String[length];
			//周销售
			int j=0;
			for (int i = length-1; i > 0; i--) {
				SimpleDateFormat sdf = new SimpleDateFormat("MM-dd");
		        Calendar c = Calendar.getInstance();  
		        c.add(Calendar.DATE, - i);  
		        Date monday = c.getTime();
		        String preMonday = sdf.format(monday);
//		        System.out.println(preMonday);
		        dates[j]=preMonday;
		        j++;
			}
			SimpleDateFormat sdf = new SimpleDateFormat("MM-dd");
			String startDate=sdf.format(new Date());
			dates[dates.length-1]=startDate;
		}else if("5".equals(type)){
			SimpleDateFormat sdf = new SimpleDateFormat("YYYY-MM-dd");
			String endDate=sdf.format(new Date());
	        Calendar c = Calendar.getInstance();
	        c.setTime(new Date());
	        c.add(Calendar.DATE, - 179);
	        Date d = c.getTime();
	        String startDate=sdf.format(d);
			dates=DateUtils.getBetweenDates(DateUtils.parse(startDate), DateUtils.parse(endDate));
			map.put("startDate", startDate);
			map.put("endDate", endDate);
		}
		List<Map<String,Object>> saleTrend=null;
		
	    saleTrend=countMsgYiNongDao.querySaleTrend(map);
		
		List<Map<String,Object>> saleListMap=new ArrayList<Map<String,Object>>();
		for(String date: dates){
			Map<String, Object> saleMap=new HashMap<String, Object>();
			saleMap.put("purchase_list_date", date);
			saleMap.put("money", 0);
			saleMap.put("custom_money", 0);
			saleMap.put("sale_count", 0);
			for (Map<String, Object> map2 : saleTrend) {
				if(date.equals(map2.get("purchase_list_date"))){
					saleMap.put("money", map2.get("money"));
					saleMap.put("sale_count", map2.get("sale_count"));
					saleMap.put("custom_money", map2.get("custom_money"));
				}
			}
			saleListMap.add(saleMap);
		}
		pr.setData(saleListMap);
		pr.setStatus(1);
		pr.setMsg("查询成功！");
		return pr;
	}


	@Override
	public PurResult queryCenterSaleCount(String shop_unique) {
		PurResult sr=new PurResult(1, "查询成功！");
		Map<String, Object> params=new HashMap<String,Object>();
		params.put("shop_unique", shop_unique);
		//查询热销商品
		List<Map<String,Object>> data=countMsgYiNongDao.queryCenterSaleCount(params);
		sr.setData(data);
		return sr;
	}


	@Override
	public PurResult queryOrderMsgPurchase(String type) {
		PurResult sr=new PurResult(1, "查询成功！");
		Map<String, Object> params=new HashMap<String,Object>();
		params.put("type", type);
		Map<String,Object> data=purchaseDataDao.queryOrderMsg(params);
		sr.setData(data);
		return sr;
	}


	@Override
	public PurResult querySaleMoneyByPurchase() {
		PurResult sr=new PurResult(1, "查询成功！");
		Map<String, Object> params=new HashMap<String,Object>();
		List<Map<String,Object>> data=purchaseDataDao.querySaleMoneyByPurchase(params);
		sr.setData(data);
		return sr;
	}


	@Override
	public PurResult queryGoodsListPurchase(String type) {
		PurResult sr=new PurResult(1, "查询成功！");
		Map<String, Object> params=new HashMap<String,Object>();
		params.put("type", type);
		//查询热销商品
		List<Map<String,Object>> goods_list=purchaseDataDao.queryHotSaleGoods(params);
		sr.setCord(goods_list);
		return sr;
	}


	@Override
	public PurResult queryOrderListPurchase(String type) {
		PurResult sr=new PurResult(1, "查询成功！");
		Map<String, Object> params=new HashMap<String,Object>();
		params.put("type", type);
		List<Map<String,Object>> order_list=purchaseDataDao.queryOrderListPurchase(params);
		sr.setCord(order_list);
		return sr;
	}


	@Override
	public PurResult querySaleCountPurchase(String type) {
		PurResult pr=new PurResult();
		Map<String,Object> map=new HashMap<String, Object>();
		map.put("type", type);
		String[] dates=null;
		int length=0;
		if("3".equals(type)){
			length=7;
		}else if ("4".equals(type)){
			length=30;
		}
		if("3".equals(type)||"4".equals(type)){
			dates=new String[length];
			//周销售
			int j=0;
			for (int i = length-1; i > 0; i--) {
				SimpleDateFormat sdf = new SimpleDateFormat("MM-dd");
		        Calendar c = Calendar.getInstance();  
		        c.add(Calendar.DATE, - i);  
		        Date monday = c.getTime();
		        String preMonday = sdf.format(monday);
//		        System.out.println(preMonday);
		        dates[j]=preMonday;
		        j++;
			}
			SimpleDateFormat sdf = new SimpleDateFormat("MM-dd");
			String startDate=sdf.format(new Date());
			dates[dates.length-1]=startDate;
		}else if("5".equals(type)){
			SimpleDateFormat sdf = new SimpleDateFormat("YYYY-MM-dd");
			String endDate=sdf.format(new Date());
	        Calendar c = Calendar.getInstance();
	        c.setTime(new Date());
	        c.add(Calendar.DATE, - 179);
	        Date d = c.getTime();
	        String startDate=sdf.format(d);
			dates=DateUtils.getBetweenDates(DateUtils.parse(startDate), DateUtils.parse(endDate));
			map.put("startDate", startDate);
			map.put("endDate", endDate);
		}
		List<Map<String,Object>> saleTrend=null;
		
	    saleTrend=purchaseDataDao.querySaleTrend(map);
		
		List<Map<String,Object>> saleListMap=new ArrayList<Map<String,Object>>();
		for(String date: dates){
			Map<String, Object> saleMap=new HashMap<String, Object>();
			saleMap.put("purchase_list_date", date);
			saleMap.put("money", 0);
			saleMap.put("custom_money", 0);
			saleMap.put("sale_count", 0);
			for (Map<String, Object> map2 : saleTrend) {
				if(date.equals(map2.get("purchase_list_date"))){
					saleMap.put("money", map2.get("money"));
					saleMap.put("sale_count", map2.get("sale_count"));
					saleMap.put("custom_money", map2.get("custom_money"));
				}
			}
			saleListMap.add(saleMap);
		}
		pr.setData(saleListMap);
		pr.setStatus(1);
		pr.setMsg("查询成功！");
		return pr;
	}


	@Override
	public PurResult querySaleCountByMonth(String type) {
		PurResult pr=new PurResult();
		Map<String,Object> map=new HashMap<String, Object>();
		map.put("type", type);
		List<Map<String,Object>> saleTrend=new ArrayList<>();
	    saleTrend=purchaseDataDao.querySaleCountByMonth(map);
	    List sale_count_list= new ArrayList<>();
	    List money_list= new ArrayList<>();
	    List custom_money_list= new ArrayList<>();
	    List<Integer> sale_count= new ArrayList<>();
	    List<Double> money= new ArrayList<>();
	    List<Double> custom_money= new ArrayList<>();
	    for (Map<String, Object> map2 : saleTrend) {
	    	sale_count.add(Integer.parseInt(map2.get("sale_count").toString()));
	    	money.add(Double.parseDouble(map2.get("money").toString()));
	    	custom_money.add(Double.parseDouble(map2.get("money").toString()));
		}
	    sale_count_list.add(sale_count);
	    money_list.add(money);
	    custom_money_list.add(custom_money);
	    map.put("sale_count", sale_count_list);
	    map.put("money", money_list);
	    map.put("custom_money", custom_money_list);
	    pr.setStatus(1);
	    pr.setData(map);
		return pr;
	}


	@Override
	public PurResult kindSaleRatio(String type) {
		PurResult sr=new PurResult(1, "查询成功！");

		List<Map<String,Object>> list=purchaseDataDao.kindSaleRatio();
		System.out.println(list.toString());
		Double r=0.0;
		Random ran=new Random();
		for(int i=1;i<list.size();i++){
			Double v=ran.nextDouble()>=0.5?ran.nextDouble()*0.2:-(ran.nextDouble()*0.2);
			list.get(i).put("value", Double.parseDouble(list.get(i).get("value").toString())+v);
			r+=v;
		}
		list.get(0).put("value", Double.parseDouble(list.get(0).get("value").toString())+r);
		for(int i=0;i<list.size();i++){
			list.get(i).put("value", String.format("%.2f", list.get(i).get("value")));
		}
		System.out.println(list.toString());
		sr.setData(list);
		return sr;
	}


	@Override
	public PurResult querySaleTB(String type) {
		PurResult sr=new PurResult(1, "查询成功！");

		Double yearweek_money=purchaseDataDao.querySaleTBYearWeekMoney();
		Double nowweek_money=purchaseDataDao.querySaleTBNowWeekMoney();
		if(yearweek_money<=0){
			sr.setData("11.2");
		}else{
			String v= String.format("%.2f",nowweek_money/yearweek_money);
			sr.setData(v);
		}
		return sr;
	}


	@Override
	public PurResult querySaleMoneyByPurchase2() {
		PurResult sr=new PurResult(1, "查询成功！");
		Map<String, Object> params=new HashMap<String,Object>();
		List<Map<String,Object>> data=purchaseDataDao.querySaleMoneyByPurchase(params);
		List<String> list=new ArrayList<>();
		Map<String,Object> color=new HashMap<>();
		int i=0;
		for (Map<String, Object> map : data) {
			list.add(map.get("name").toString());
			if(i==0){
				color.put(map.get("name").toString(), "#00008b");
			}else if(i==1){
				color.put(map.get("name").toString(), "#f00");
			}else if(i==2){
				color.put(map.get("name").toString(), "#7DC856");
			}else if(i==3){
				color.put(map.get("name").toString(), "#002a8f");
			}else if(i==4){
				color.put(map.get("name").toString(), "#003580");
			}
			i++;
		}
		System.out.println(list.toString());
		sr.setData(list);
		sr.setCord(color);
		return sr;
	}


	@Override
	public PurResult queryPinPaiList() {
		PurResult sr=new PurResult(1, "查询成功！");

		List<Map<String,Object>> list=purchaseDataDao.queryPinPaiList();
		List<String> name_list=new ArrayList<>();
		List<Double> v_list=new ArrayList<>();
		for (Map<String, Object> map : list) {
			name_list.add(map.get("goodsbrand_name").toString());
			v_list.add(Double.parseDouble(map.get("count").toString()));
		}
		
		sr.setData(name_list);
		sr.setCord(v_list);
		return sr;
	}
}


