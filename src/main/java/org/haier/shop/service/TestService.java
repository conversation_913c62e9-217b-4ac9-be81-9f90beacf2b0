package org.haier.shop.service;

import org.haier.shop.util.ShopsResult;

public interface TestService {
	
	public ShopsResult updateShopTownCode(Integer maxIndex);
	
	public ShopsResult updateShopMsg(Integer maxSize);
	public ShopsResult updateDict();
	
	/**
	 * 测试批量更新
	 * @return
	 */
	public ShopsResult piliang();
	
	public ShopsResult huifu(String shop_unique);
	
	public ShopsResult deleteSame();
	
	public ShopsResult queryGoodsSupplierMsg();
}
