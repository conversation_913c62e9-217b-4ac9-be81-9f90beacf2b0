package org.haier.shop.service;

import java.util.List;
import java.util.Map;

import org.haier.shop.params.purchase.PurchaseGoodsAddParams;
import org.haier.shop.params.purchase.PurchaseGoodsRetParam;
import org.haier.shop.util.PurResult;

import javax.servlet.http.HttpServletRequest;

/**
	 * 商家自采购进货单service
	 */
	public interface SelfPurchaseService {


	/**
	 * 添加进货单信息
	 * @param retParam
	 * @return
	 */
	PurResult insertSelfRetPurchase(PurchaseGoodsRetParam retParam);
	/**
	 * 查询退货订单详情
	 * @param selfPurchaseRetUnique
	 * @return
	 */
	public PurResult getSelfRetPurchase(String selfPurchaseRetUnique);
		
		
	public PurResult addSelfPurPay(String order_code,Double payMoney,
			Double surplusMoney,String staff_id,String shop_unique
			);
	
	/**
	 * 添加退货订单
	 * @param shopUnique 店铺编号
	 * @param selfPurchaseUnique 进货订单号
	 * @param goodsDetail 商品详情，JSONArray格式
	 * @param supplierUnique 供货商编号
	 * @param staffId 操作员工编号
	 * @return
	 */
	public PurResult addNewReturnPurList(
			String shopUnique,String selfPurchaseUnique,String goodsDetail,String supplierUnique,String staffId,String remark
		);
	
	/**
	 * 查询订单详情
	 * @param self_purchase_unique
	 * @return
	 */
	public PurResult getSelfPurchaseReturnDetailList(String self_purchase_unique,String shop_unique);
	
	public List<Map<String,Object>> querySaleList();
	/**
	 * 查询进货订单列表
	 * @param shop_unique
	 * @param search_str 订单编号/供货商名称
	 * @param start_date 查询开始时间
	 * @param end_date 查询结束时间
	 * @param pay_status 支付状态：0全部支付 1欠款
	 * @param purchase_status 采购单状态：0待收货 1已完成 2已取消
	 * @param startNum
	 * @param pageSize
	 * @return
	 */	
	public PurResult getSelfPurchaseList(Map<String, Object> params);
	
	
	/**
	 * 查询订单详情
	 * @param shop_unique 店铺唯一标示
	 * @param self_purchase_unique 订单编号
	 * @return
	 */	
	public PurResult getSelfPurchase(Map<String, Object> params);

	/**
	 * 添加进货单信息
	 * @param addParams
	 * @return
	 */
	PurResult insertSelfPurchase(PurchaseGoodsAddParams addParams);
	
	/**
	 * 添加进货单支付记录信息
	 * @param self_purchase_unique 自采购进货单唯一标示
	 * @param shop_unique 店铺唯一标示
	 * @param pay_money 本次支付金额
	 * @param staff_id 操作员工编号
	 * @param source_type 操作终端：1商家后台 2Android 3ios
	 * @param network_ip 操作网络ip
	 * @return
	 */	
	public PurResult insertSelfPurchasePay(Map<String ,Object> params);
	
	/**
	 * 修改进货单信息
	 * @param self_purchase_unique 自采购进货单唯一标示
	 * @param shop_unique 店铺唯一标示
	 * @param purchase_status 采购单状态：0待收货 1已完成 2已取消
	 * @param update_staff_id 修改员工编号
	 * @return
	 */	
	public PurResult updateSelfPurchase(Map<String ,Object> params);
	
	/**
	 * 删除进货单信息
	 * @param self_purchase_unique 自采购进货单唯一标示
	 * @param shop_unique 店铺唯一标示
	 * @param del_flag 删除标示:0正常 1删除
	 * @return
	 */	
	public PurResult deleteSelfPurchase(Map<String ,Object> params);

}
