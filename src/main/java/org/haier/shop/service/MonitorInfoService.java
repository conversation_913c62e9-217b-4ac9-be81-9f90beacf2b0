package org.haier.shop.service;


import org.haier.shop.util.PurResult;

import java.util.List;
import java.util.Map;

/**
 * 监控设备信息Service接口
 *
 * <AUTHOR>
 * @date 2022-06-07
 */
public interface MonitorInfoService
{
	
	public List<Map<String,Object>> showSixMonitor();
	
    PurResult queryUnicomMonitorList(Map<String, Object> params);

    PurResult addMonitorInfo(Map<String, Object> params);

    public Map<String ,Object> selectMonitorInfoByMonitorInfoId(Long monitorInfoId);

    PurResult updateMonitorInfo(Map<String, Object> params);
    /* *//**
     * 查询监控设备信息
     *
     * @param monitorInfoId 监控设备信息主键
     * @return 监控设备信息
     *//*
    public MonitorInfo selectMonitorInfoByMonitorInfoId(Long monitorInfoId);

    *//**
     * 查询监控设备信息列表
     *
     * @param monitorInfo 监控设备信息
     * @return 监控设备信息集合
     *//*
    public List<MonitorInfo> selectMonitorInfoList(MonitorInfo monitorInfo);

    *//**
     * 新增监控设备信息
     *
     * @param monitorInfo 监控设备信息
     * @return 结果
     *//*
    public int insertMonitorInfo(MonitorInfo monitorInfo);

    *//**
     * 修改监控设备信息
     *
     * @param monitorInfo 监控设备信息
     * @return 结果
     *//*
    public int updateMonitorInfo(MonitorInfo monitorInfo);

    *//**
     * 批量删除监控设备信息
     *
     * @param monitorInfoIds 需要删除的监控设备信息主键集合
     * @return 结果
     *//*
    public int deleteMonitorInfoByMonitorInfoIds(Long[] monitorInfoIds);

    *//**
     * 删除监控设备信息信息
     *
     * @param monitorInfoId 监控设备信息主键
     * @return 结果
     *//*
    public int deleteMonitorInfoByMonitorInfoId(Long monitorInfoId);*/
}
