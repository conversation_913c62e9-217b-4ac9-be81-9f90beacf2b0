package org.haier.shop.service;

import java.io.IOException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;

import org.apache.http.client.ClientProtocolException;
import org.haier.shop.dao.FarmProductDao;
import org.haier.shop.dao.GlobalThemeDao;
import org.haier.shop.dao.WeishiDao;
import org.haier.shop.entity.FarmProductShelf;
import org.haier.shop.entity.beans.PageData;
import org.haier.shop.entity.globalSelect.FarmDetailVO;
import org.haier.shop.util.MUtil;
import org.haier.shop.util.PurResult;
import org.haier.shop.util.ServletsUtil;
import org.haier.shop.util.ShopsResult;
import org.haier.shop.util.ShopsUtil;
import org.haier.shop.util.WeiXinUtil;
import org.haier.shop.util.weishi.AuditInfo;
import org.haier.shop.util.weishi.Delivery;
import org.haier.shop.util.weishi.DeliveryInfo;
import org.haier.shop.util.weishi.OrderDetail;
import org.haier.shop.util.weishi.ProductDesc;
import org.haier.shop.util.weishi.ProductInfo;
import org.haier.shop.util.weishi.ProductInfo2;
import org.haier.shop.util.weishi.ProductVO;
import org.haier.shop.util.weishi.Sku;
import org.haier.shop.util.wxPay.PayConfigUtil;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import com.alibaba.fastjson.JSON;

import net.sf.json.JSONArray;
import net.sf.json.JSONObject;
import net.sf.json.JsonConfig;

@Service
public class WeishiProductService{
	
	@Resource
	private FarmProductDao farmProductDao;
	@Resource
	private GlobalThemeDao globalThemeDao;
	@Resource
	private WeishiDao weishiDao;
	
	@Transactional
	@SuppressWarnings( "unchecked" )
	public PurResult addProduct(Map<String,Object> map,HttpServletRequest request,List<PageData> peopleList){
		PurResult result = new PurResult();
		JSONArray areaJson=JSONArray.fromObject(map.get("areaJson"));
		JSONArray goodJson=JSONArray.fromObject(map.get("detailJson"));
		List<PageData> areaList = JSONArray.toList(areaJson, new PageData(), new JsonConfig());//区域
		List<PageData> goodsList = JSONArray.toList(goodJson, new PageData(), new JsonConfig());//规格

		//新增全球精选
		
		Object[] shelf_status_str=(Object[]) JSONArray.fromObject(map.get("shelf_status_str")).toArray();
		map.put("shelf_status", 0);
		map.put("sup_shelf_status", 0);
		for (Object value : shelf_status_str) {
			if("quan".equals(value.toString())){
				map.put("shelf_status", 1);
			}else if("sup".equals(value.toString())){
				map.put("sup_shelf_status", 1);
			}
		}
		map.put("vedio_shelf_status", 2);
		map.put("is_wechat", 2);
		//新增农产品
		globalThemeDao.addProduct(map);
		//增加上架的店铺列表
		map.put("list", areaList);
		globalThemeDao.addFarmProductShelfList(map);
		//增加上架的规格列表
		for(PageData pageData : goodsList) {
			map.putAll(pageData);
			globalThemeDao.addFarmProductShelfPriceList(map);
		}
		//新增全球精选规格
		if(goodsList.size()>0){
		for(PageData t:goodsList)
		{
			t.put("farm_products_id", map.get("id"));
			
		}
		if("1".equals(map.get("shelf_status").toString())){
			globalThemeDao.addProductSpec(goodsList);
		}
		}
		//新增农户
		for(PageData p:peopleList)
		{
			p.put("farm_products_id", map.get("id"));
		}
		globalThemeDao.addProductPeople(peopleList);
		//新增区域
		for(PageData a:areaList)
		{
			a.put("farm_products_id", map.get("id"));
		}
		globalThemeDao.addProductArea(areaList);
			
	    result.setStatus(1);
		result.setMsg("成功");
		
		return result;
	}
	
	@Transactional
	@SuppressWarnings( "unchecked" )
	public PurResult updateWeishiProduct(Map<String,Object> map,HttpServletRequest request) {
		PurResult result = new PurResult();
		JSONArray areaJson=JSONArray.fromObject(map.get("areaJson"));
		JSONArray goodJson=JSONArray.fromObject(map.get("detailJson"));
		JSONArray peopleJson=JSONArray.fromObject(map.get("peopleJson"));
		JSONArray goodJson_sup=JSONArray.fromObject(map.get("detailJson_sup"));
		Object[] shelf_status_str=(Object[]) JSONArray.fromObject(map.get("shelf_status_str")).toArray();
		map.put("audit_status", 0);
		map.put("shelf_status", 0);
		map.put("sup_shelf_status", 0);
		for (Object value : shelf_status_str) {
			if("quan".equals(value)){
				map.put("shelf_status", 1);
			}else if("sup".equals(value)){
				map.put("sup_shelf_status", 1);
			}
		}
		List<PageData> areaList = JSONArray.toList(areaJson, new PageData(), new JsonConfig());//区域
		List<PageData> goodsList = JSONArray.toList(goodJson, new PageData(), new JsonConfig());//规格
		List<PageData> goodsList_sup = JSONArray.toList(goodJson_sup, new PageData(), new JsonConfig());//规格
		List<PageData> peopleList = JSONArray.toList(peopleJson, new PageData(), new JsonConfig());//农户
		List<PageData> OldPeopleList = globalThemeDao.quertPeolpleList(map);
		//
		
		//新增农产品
		globalThemeDao.updateProduct(map);
		globalThemeDao.delProductSpec(map);
		
		globalThemeDao.delProductArea(map);
		globalThemeDao.delProductPeople(map);
		//新增规格
		for(PageData t:goodsList)
		{
			t.put("farm_products_id", map.get("id"));
		}
		if("1".equals(map.get("shelf_status").toString())){
			globalThemeDao.delProductSpec(map);
			globalThemeDao.addProductSpec(goodsList);
		}else{
			globalThemeDao.delProductSpec(map);
			
		}
		
		//新增规格
		for(PageData t:goodsList_sup)
		{
			t.put("farm_products_id", map.get("id"));

		}
//		if("1".equals(map.get("sup_shelf_status").toString())){
//			globalThemeDao.delProductSpec_sup(map);
//			globalThemeDao.addProductSpec_sup(goodsList_sup);
//		}else{
//			globalThemeDao.delProductSpec_sup(map);
//			
//		}
		
		//新增农户
		for(PageData p:peopleList)
		{
			p.put("farm_products_id", map.get("id"));
			MultipartFile file1=null;
			file1=ShopsUtil.testMulRequest(request, p.get("aptitudes_imgages").toString());
			if(file1==null){
				
				p.put("aptitudes_imgages", null);
				for(PageData t :OldPeopleList)
				{
					String peopleId=String.valueOf(t.get("peopleId"));
					String id=String.valueOf(p.get("id"));
					if(peopleId.equals(id))
					{
						 if(t.containsKey("aptitudes_imgages"))
						{
							p.put("aptitudes_imgages", t.get("aptitudes_imgages"));
						}
					}
				}
			}
			MultipartFile file2=null;
			file2=ShopsUtil.testMulRequest(request, p.get("aptitudes_imgages2").toString());
			if(file2==null){
				
				p.put("aptitudes_imgages2", null);
				for(PageData t :OldPeopleList)
					{
						String peopleId=String.valueOf(t.get("peopleId"));
						String id=String.valueOf(p.get("id"));
						if(peopleId.equals(id))
						{
							 if(t.containsKey("aptitudes_imgages2"))
							{
								p.put("aptitudes_imgages2", t.get("aptitudes_imgages2"));
							}
						}
					}
			}
			
			MultipartFile file3=null;
			file3=ShopsUtil.testMulRequest(request,p.get("aptitudes_imgages3").toString());
			if(file3==null) {
				p.put("aptitudes_imgages3", null);
				for(PageData t :OldPeopleList)
				{
					String peopleId=String.valueOf(t.get("peopleId"));
					String id=String.valueOf(p.get("id"));
					if(peopleId.equals(id))
					{
						 if(t.containsKey("aptitudes_imgages3"))
						{
							p.put("aptitudes_imgages3", t.get("aptitudes_imgages3"));
						}
					}
				}
			}
			
		}
		globalThemeDao.addProductPeople(peopleList);
		//新增区域
		for(PageData a:areaList)
		{
			a.put("farm_products_id", map.get("id"));
			
		}
		globalThemeDao.addProductArea(areaList);
		
	    result.setStatus(1);
		result.setMsg("成功");
		
		return result;
	}
	
	@Transactional
	public PurResult auditProduct(Map<String,Object> map,String access_token){
		PurResult result = new PurResult(1,"操作成功！");
		//查询商品详情
		Map<String,Object> wData=weishiDao.queryWeishiProduct(map);
		String shop_unique=wData.get("shop_unique").toString();
		
		
		map.put("create_time", new Date());
		map.put("id", map.get("id"));
		
		JSONArray goodJson=JSONArray.fromObject(map.get("detailJson"));
		@SuppressWarnings("unchecked")
		List<PageData> goodsList = JSONArray.toList(goodJson, new PageData(), new JsonConfig());//规格
		for(PageData t:goodsList)
		{
			Map<String,Object> params=new HashMap<>();
			params.put("id", t.get("id"));
			params.put("sale_price", t.get("sale_price"));
			farmProductDao.updateFarmSalePrice(params);

		}
		//上架店铺全球精选
		//按区域查询所有店铺
		farmProductDao.delete_farm_product_shelf(map);
		farmProductDao.delete_shopping_cart(map);
		
		String path="";
		List<Sku> skus=new ArrayList<>();
		for(PageData t:goodsList){
//		List<Map<String,Object>> shops= farmProductDao.queryAreaShop(map);
//		for (Map<String, Object> shop : shops) {
			FarmProductShelf shelf=new FarmProductShelf();
			shelf.setCreate_time(new Date());
			shelf.setFlag(1);
			shelf.setGood_id(map.get("id").toString());
			shelf.setShelf_status(1);
			shelf.setShop_unique(shop_unique);
			farmProductDao.insertFarmProductShelf(shelf);
			//上架  添加售价
			map.put("shelf_id", shelf.getId());
			Map<String, Object> params =new HashMap<String, Object>();
			params.put("shelf_id", shelf.getId());
			params.put("good_id", map.get("id").toString());
			params.put("spec_name",t.get("spec_name") );
			params.put("price",t.get("price"));
			params.put("sale_price", t.get("sale_price"));
			params.put("convert_unit", t.get("convert_unit"));
			farmProductDao.insertShelfPrice(params);
			
					    Sku sku=new Sku();
					 	sku.setBarcode(wData.get("barcode").toString());
					 	String out_product_id=t.getString("id");//商家自定义商品ID
					 	sku.setOut_product_id(out_product_id);//farm_products_spec 表  id
					 	String out_sku_id=String.valueOf(shelf.getId());//商家自定义skuID
					 	sku.setOut_sku_id(out_sku_id);//farm_products_shelf_price 表 shelf_id
					 	sku.setThumb_img(wData.get("goods_image").toString());
					 	double price=Double.valueOf(t.get("sale_price").toString())*100;
					 	sku.setSale_price(price);//售价（供价）
					 	
//					 	double market_price=Double.valueOf(wData.get("sale_price").toString());
					 	sku.setMarket_price(price);//市场价
					 	Double stock_num=Double.valueOf(wData.get("inventory").toString());
					 	sku.setStock_num((int) Math.round(stock_num));//库存
					 	skus.add(sku);
//			path="pages/productDetails/productDetails?shopId="+shop_unique+"&goodsBarcode=QQ"+shelf.getId()+"&gtsId="+params.get("id")+"&scene1=1&source=2";
			path="pages/productDetails/productDetails?shopId="+shop_unique+"&goodsBarcode=QQ"+map.get("id").toString()+"&gtsId="+shelf.getId()+"&scene1=1&source=2";
			System.out.println(path);
//			}
		}
		
		try {
  
			JSONObject resultData;
			//查询微视该商品是否存在
			Map<String,Object> map2=new HashMap<String, Object>();
			map2.put("out_product_id", wData.get("barcode").toString());
			map2.put("need_edit_spu", 0);
			resultData = WeiXinUtil.doPostStr(PayConfigUtil.WXP_SUP_GET+"?access_token="+access_token,JSON.toJSONString(map2));
			
			System.out.println(access_token);
			ProductVO weishiVO=new ProductVO();
			//1 	商家自定义商品ID
			weishiVO.setOut_product_id(wData.get("barcode").toString());
			//2 	标题
			weishiVO.setTitle(wData.get("goods_name").toString());
			//3
			weishiVO.setPath(path);
			//4		主图，多张，列表，图片类型，最多不超过9张
			List<String> head_img=new ArrayList<>();
			
				if(wData.containsKey("goods_image"))
				{
					head_img.add(wData.get("goods_image").toString());
				} 
				if(wData.containsKey("detail_image"))
				{
					head_img.add(wData.get("detail_image").toString());
				} 
				if(wData.containsKey("goods_video"))
				{
					head_img.add(wData.get("goods_video").toString());
				}
			
			weishiVO.setHead_img(head_img);
			//5 	第三级类目ID
				int kind_id=Integer.valueOf(wData.get("cat_id").toString()); 
			weishiVO.setThird_cat_id(kind_id);
			//6		品牌id
				int brand_id=Integer.valueOf(wData.get("brand_id").toString()); 
			weishiVO.setBrand_id(brand_id);
			//7		商品使用场景,1:视频号，3:订单中心
//			 	int[] scene_group_list= {1,3};
			int[] scene_group_list= {1};
			weishiVO.setScene_group_list(scene_group_list);
			//8		sku数组
			weishiVO.setSkus(skus);
			//9		商品资质
			List<String> qualification_pics=new ArrayList<>();
			weishiVO.setQualification_pics(qualification_pics);
			//10	商品详情
			 ProductDesc desc_info=new ProductDesc();
			 if(wData.containsKey("village_remarks"))
				{
				 desc_info.setDesc(wData.get("village_remarks").toString());
				}
			 	weishiVO.setDesc_info(desc_info);
			if(resultData.get("errcode").equals(0))
			{
				//存在-更新微视频商品
				resultData = WeiXinUtil.doPostStr(PayConfigUtil.WXP_SUP_UPDATE+"?access_token="+access_token,JSON.toJSONString(weishiVO));
				if(!resultData.get("errcode").equals(0))
				{
					result.setStatus(2);
					result.setMsg(resultData.get("errmsg").toString());
					map.put("audit_status", 2);
					map.put("audit_reason", resultData.get("errmsg").toString());
					//修改审核状态
					farmProductDao.updateFarmProductStatus(map);
					map.put("farm_product_id", map.get("id"));
					farmProductDao.insertFarmProductAudit(map);		
				}else
				{
					map.put("audit_status", 3);
					//修改审核状态
					farmProductDao.updateFarmProductStatus(map);
				}
			}else
			{
				//不存在-新增微视频商品
				resultData = WeiXinUtil.doPostStr(PayConfigUtil.WXP_SUP_ADD+"?access_token="+access_token,JSON.toJSONString(weishiVO));
						
				if(!resultData.get("errcode").equals(0))
				{
					result.setStatus(2);
					result.setMsg(resultData.get("errmsg").toString());
					map.put("audit_status", 0);
					//修改审核状态
					farmProductDao.updateFarmProductStatus(map);
					
				}else
				{
					map.put("audit_status", 3);
					//修改审核状态
					farmProductDao.updateFarmProductStatus(map);
				}
			}
		
				} catch (ClientProtocolException e) {
					// TODO Auto-generated catch block
					e.printStackTrace();
				} catch (IOException e) {
					// TODO Auto-generated catch block
					e.printStackTrace();
				}	 
    	
		return result;
	}
	public PurResult queryProductsList(Map<String,Object> map){
		PurResult result = new PurResult();
		try {
			List<Map<String,Object>> list = weishiDao.queryProductsList(map);
	    	Integer count = weishiDao.queryProductsCount(map);
	    	result.setStatus(1);
			result.setMsg("成功");
			result.setCount(count);
			result.setData(list);
		} catch (Exception e) {
			e.printStackTrace();
			result.setStatus(0);
			result.setMsg("异常");
		}
		return result;
	}
	/**
	 * @param data
	 * @param params
	 * @param access_token
	 */
	public void queryAuditProduct(FarmDetailVO data, Map<String, Object> params, String access_token) {
		try {
	    	//如果平台审核通过 ，并且 橱窗待审核，查询审核状态
	    	if(data.getAudit_status()==3)
	    	{
	    		Map<String,Object> sendMap=new HashMap<String,Object>();
	    		sendMap.put("need_edit_spu", 0);
	    		sendMap.put("out_product_id", data.getBarcode());
	    		
			    	JSONObject resultData=WeiXinUtil.doPostStr(PayConfigUtil.WXP_SUP_GET+"?access_token="+access_token,JSON.toJSONString(sendMap));
			    		
			    	if(resultData.get("errcode").equals(0))
			    	{
//			    		枚举-edit_status
//			    		枚举值	描述
//			    		1	未审核
//			    		2	审核中
//			    		3	审核失败
//			    		4	审核成功
//			    		枚举-status
//			    		枚举值	描述
//			    		0	初始值
//			    		5	上架
//			    		11	自主下架
//			    		13	违规下架/风控系统下架
			    		 Map<String, Object> sup=MUtil.jsonToMap(resultData.get("spu").toString());
			    		 if(sup.get("edit_status").equals(3))
			    		 {
			    			 JSONObject data2 = JSONObject.fromObject(sup.get("audit_info"));
			    			 AuditInfo audit_info =(AuditInfo) JSONObject.toBean(data2,AuditInfo.class);

			    			 //修改审核状态
			    			 params.put("audit_status", 2);
			    			 params.put("vedio_shelf_status", 0);
			    			 params.put("audit_reason", audit_info.getReject_reason());
			    			 params.put("create_time", audit_info.getAudit_time());
			    			 farmProductDao.updateFarmProductStatus(params);
			    			 farmProductDao.insertFarmProductAudit(params);
			    			 
			    		 }else if(sup.get("edit_status").equals(4))
			    		 {
			    			 params.put("audit_status", 1);
			    			 params.put("vedio_shelf_status", 1);
			    			 farmProductDao.updateFarmProductStatus(params);
			    		 }
			    	}
			    	
	    	}
			} catch (Exception e) {
				e.printStackTrace();
			}
		
	}
	/**
	 * 上下架
	 * @param id
	 * @param shelf_status
	 * @param good_status
	 * @return
	 */
	@Transactional
	public PurResult updateStatus(int id,int shelf_status,int good_status,String barcode,String access_token){
		PurResult result = new PurResult(1,"操作成功!");
		  try {
		Map<String,Object> params=new HashMap<String,Object>();
		
		params.put("id", id);
		params.put("shelf_status", shelf_status);
		params.put("good_status", good_status);
		if(good_status==2)
		{
			params.put("flag", 0);
		}else
		{
			params.put("flag", 1);
			params.put("good_status", 1);
		}
		//下架
		if(shelf_status==0)
		{
			//全部店铺下架
			params.put("audit_status", 0);
			globalThemeDao.updateFarmShopStatus(params);
			//微视频下架商品
    		Map<String,Object> sendMap=new HashMap<String,Object>();
    		sendMap.put("out_product_id", barcode);
		  
			JSONObject resultData=WeiXinUtil.doPostStr(PayConfigUtil.WXP_SUP_DELISTING+"?access_token="+access_token,JSON.toJSONString(sendMap));
			if(resultData.get("errcode").equals(0))
			{
				System.out.println("下架成功!");
			}
		}else
		{
			//微视频下架商品
    		Map<String,Object> sendMap=new HashMap<String,Object>();
    		sendMap.put("out_product_id", barcode);
		  
			JSONObject resultData=WeiXinUtil.doPostStr(PayConfigUtil.WXP_SUP_LISTING+"?access_token="+access_token,JSON.toJSONString(sendMap));
			if(resultData.get("errcode").equals(0))
			{
				System.out.println("上架成功!");
			}else
			{
				result.setStatus(0);
				result.setMsg(resultData.get("errmsg").toString());
				return result;
			}
			params.put("audit_status", null);
		}
		globalThemeDao.updateFarmStatus(params);
		
		} catch (ClientProtocolException e) {
			e.printStackTrace();
			 result.setStatus(0);
				result.setMsg("异常");
		} catch (IOException e) {
			e.printStackTrace();
			 result.setStatus(0);
				result.setMsg("异常");
		}
		
	   
		
		return result;
	}
	/**
	 * 查询平台益农订单详情
	 * @param shop_unique
	 * @param sale_list_unique
	 * @return
	 */
	public ShopsResult queryOrderDetail(String out_order_id){
		ShopsResult shop=new ShopsResult();
		Map<String,Object> map=new HashMap<String,Object>();
		map.put("sale_list_unique", out_order_id);
		Map<String, Object> detail=farmProductDao.queryFarmOrderDetail(map);
		if(detail==null){
			shop.setStatus(1);
			shop.setMsg("订单编号错误，请确认！");
			return shop;
		}

		shop.setStatus(0);
		shop.setMsg("订单详情查询成功！");
		shop.setData(detail);
		return shop;
	}
	
	
	/**
	  * 发货
	 * @param map
	 * @param access_token
	 * @return
	 */
	@Transactional
	public PurResult addOrderDelivery(String out_order_id,Long order_id,String delivery_id,String waybill_id,String openid,String access_token,List<ProductInfo> product_infos,HttpServletRequest request){
		PurResult result = new PurResult(1,"操作成功！");
		try {
			
			Map<String, Object> params = ServletsUtil.getParameters(request);
			Delivery delivery=new Delivery();
//			delivery.setOrder_id(order_id);
			delivery.setOut_order_id(out_order_id);
			delivery.setFinish_all_delivery(1);
			delivery.setOpenid(openid);
				List<DeliveryInfo> delivery_list=new ArrayList<DeliveryInfo>();
				DeliveryInfo send=new DeliveryInfo();
				send.setDelivery_id(delivery_id);
				send.setWaybill_id(waybill_id);
				List<ProductInfo2> info =new ArrayList<ProductInfo2>();
				for(ProductInfo item:product_infos)
				{
					ProductInfo2 data=new ProductInfo2();
					data.setOut_product_id(item.getOut_product_id());
					data.setOut_sku_id(item.getOut_sku_id());
					info.add(data);
				}
				send.setProduct_info_list(info);
				delivery_list.add(send);
			delivery.setDelivery_list(delivery_list);
			Date day=new Date();    
			SimpleDateFormat df = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss"); 
			System.out.println(product_infos);  
			delivery.setShip_done_time(df.format(day));
			JSONObject resultData = WeiXinUtil.doPostStr(PayConfigUtil.WXP_DELIVEY_SEND+"?access_token="+access_token,JSON.toJSONString(delivery));
			if(resultData.get("errcode").equals(0))
			{
				//新增微视物流信息
				weishiDao.addOrderDelivery(params);
				//修改订单状态
				params.put("handle_status", 2);
				weishiDao.updateOrder(params);
				weishiDao.updateSubOrder(params);
				
			}
		
		
			} catch (ClientProtocolException e) {
					e.printStackTrace();
			} catch (IOException e) {
					e.printStackTrace();
			}	 
    	
		return result;
	}
	/**
	  * 收货
	 * @param map
	 * @param access_token
	 * @return
	 */
	@Transactional
	public PurResult deliveryRecieve(Map<String,Object> map,String access_token){
		PurResult result = new PurResult(1,"操作成功！");
		try {
			

			JSONObject resultData = WeiXinUtil.doPostStr(PayConfigUtil.WXP_DELIVEY_RECIEVE+"?access_token="+access_token,JSON.toJSONString(map));
			if(resultData.get("errcode").equals(0))
			{
				map.put("handle_status", 3);
				//修改订单状态
				weishiDao.updateOrder(map);
				weishiDao.updateSubOrder(map);
				
			}
		
		
			} catch (ClientProtocolException e) {
					e.printStackTrace();
			} catch (IOException e) {
					e.printStackTrace();
			}	 
    	
		return result;
	}
}
