package org.haier.shop.service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.UUID;

import org.haier.shop.dao.SysMenuDao;
import org.haier.shop.entity.SysPermission;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Service
public class SysMenuServiceImpl implements SysMenuService{
	
	@Autowired
    private SysMenuDao menuDao;

	@Override
	@Transactional
	public void insert(SysPermission permission) {
		String level = permission.getLevel();
		if(level != null && level.equals("1")){
			permission.setParent_code("-1");
		}
		permission.setCode(UUID.randomUUID().toString().replaceAll("-", ""));
		permission.setDel_flag(0);
		menuDao.insert(permission);
	}

	@Override
	@Transactional
	public void delete(String code) {
//		menuDao.delete(code);
//		menuDao.deleteByParentId(code);
		//逻辑删除
		SysPermission permission = new SysPermission();
		permission.setDel_flag(1);
		permission.setCode(code);
		menuDao.update(permission);
	}

	@Override
	public List<SysPermission> quertMenuList(Map<String, Object> params) {
		List<SysPermission> list = new ArrayList<SysPermission>();
		System.out.println(params);
//		if(params.get("level") != null && params.get("level").toString().equals("2")) {
//			list = menuDao.quertMenuList(params);
//		}else {
//			list = menuDao.quertMenuListDetail(params);
//		}
		List<SysPermission> resultList = menuDao.quertMenuList(params);
		
		if(params.get("level") != null && params.get("level").toString().equals("2")) {
			list.addAll(resultList);
		}else {
			for(int i=0;i<resultList.size();i++){
				SysPermission p = resultList.get(i);
				Map<String, Object> params1 = new HashMap<String, Object>();
				params1.put("parent_code", p.getCode());
				List<SysPermission> children = menuDao.quertMenuList(params1);
				p.setChildren(children);
				list.add(p);
			}
		}
		return list;
	}
	@Override
	public List<SysPermission> quertMenuList2(Map<String, Object> params) {

		List<SysPermission> resultList = menuDao.quertMenuList2(params);

		return resultList;
	}

	@Override
	public int quertMenuListCount(Map<String, Object> params) {
		return menuDao.quertMenuListCount(params);
	}

	@Override
	@Transactional
	public void update(SysPermission permission) {
		String level = permission.getLevel();
		if(level != null && level.equals("1")){
			permission.setParent_code("-1");
		}
		menuDao.update(permission);
	}

	@Override
	public SysPermission getMenu(SysPermission permission) {
		return menuDao.getMenu(permission);
	}

	@Override
	public int getRoleCountByMenuCode(String code) {
		return menuDao.getRoleCountByMenuCode(code);
	}

}
