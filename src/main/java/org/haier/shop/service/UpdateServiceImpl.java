package org.haier.shop.service;

import java.io.BufferedReader;
import java.io.File;
import java.io.FileReader;
import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;

import org.haier.shop.dao.CusLevelDao;
import org.haier.shop.dao.Goods_kindDao;
import org.haier.shop.dao.ShopDao;
import org.haier.shop.dao.ShopFunctionDao;
import org.haier.shop.dao.ShopStaffDao;
import org.haier.shop.dao.SupplierDao;
import org.haier.shop.dao.UpdateDao;
import org.haier.shop.dao.UtilDao;
import org.haier.shop.dao2.ShopTDao;
import org.haier.shop.entity.SqlPc;
import org.haier.shop.entity.Staff;
import org.haier.shop.util.ChineseCharToEn;
import org.haier.shop.util.Load;
import org.haier.shop.util.PCUpdateUtil;
import org.haier.shop.util.ShopsResult;
import org.haier.shop.util.ShopsUtil;
import org.haier.shop.util.UpdateUtil;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;


@Service
@Transactional
public class UpdateServiceImpl implements UpdateService{
	@Resource
	private UpdateDao updateDao;
	@Resource
	public UtilDao utilDao;
	@Resource
	private ShopDao shopDao;
	@Resource
	private SupplierDao supplierDao;
	@Resource
	private ShopTDao shopTDao;
	@Resource
	private ShopFunctionDao funDao;
	@Resource
	private Goods_kindDao kindDao;
	@Resource
	private ShopStaffDao staffDao;
	@Resource
	private  CusLevelDao levelDao;
	
	/**
	 * 批量注册
	 * @return
	 */
	public ShopsResult register(String shop_name,
			String manager_account,
			String manager_pwd,
			String shop_address_detail,
			String shop_phone,
			Integer examinestatus,
			Integer shop_class,
			String company_code,
			Integer is_other_purchase,
			String area_dict_num,
			String shop_latitude,
			String shop_longitude,
			String province,
			String city,
			String district,
			Integer shop_type
			){
		ShopsResult shop=new ShopsResult();
		Map<String,Object> map=new HashMap<String, Object>();
		Map<String,Object> pmap=new HashMap<String,Object>();
		if(area_dict_num == null &&city!=null|| area_dict_num.equals("")&&city!=null){
			Map<String ,Object> dictParams = new HashMap<String, Object>();
			dictParams.put("province", province);
			dictParams.put("city", city);
			dictParams.put("district", district);
			//查询区的编号
			Map<String,Object> quCode=supplierDao.queryDistrict(dictParams);
			area_dict_num=(String) quCode.get("area_dict_num");
		}
		map.put("manager_account", manager_account);
		Map<String, Object> re=shopDao.login(map);
		if(re!=null){
			shop.setStatus(1);
			shop.setMsg("该账户已注册！");
			return shop;
		}
		Staff  staff=new Staff();
		String staff_pwd=ShopsUtil.string2MD5(manager_pwd.trim()).trim();
		map.put("shop_phone", shop_phone);
		map.put("manager_pwd", staff_pwd);
		map.put("shop_address_detail", shop_address_detail);
		map.put("area_dict_num", area_dict_num);
		map.put("shop_name", shop_name);
		map.put("shop_alias", ChineseCharToEn.getAllFirstLetter(shop_name).trim());
		map.put("examinestatus", examinestatus);
		map.put("shop_class", shop_class);
		map.put("company_code", company_code);
		map.put("is_other_purchase", is_other_purchase);
		map.put("login_name", shop_name);
		map.put("shop_latitude", shop_latitude);
		map.put("shop_longitude", shop_longitude);
//		map.put("shop_alias", shop_name);
		String shop_unique=new Date().getTime()+"";
		staff.setShop_unique(Long.parseLong(shop_unique));
		staff.setStaff_account(manager_account);
		staff.setStaff_pwd(staff_pwd);
		staff.setStaff_phone(shop_phone);
		staff.setPwd_ok(manager_pwd);
		staff.setStaff_position(3);
		staff.setManager_unique(shop_unique);
		staff.setStaff_name(shop_name);
		map.put("shop_unique", shop_unique);
		map.put("shopUnique", shop_unique);
		map.put("manager_unique", shop_unique);//添加主管理员
		map.put("kindType", 2);//默认自定义分类
		map.put("shop_type", shop_type);
		int k=shopDao.register(map);//商家端注册新店铺
		
		
		if(k==0){
			shop.setStatus(1);
			shop.setMsg("注册失败！");
			return shop;
		}else{
			k=shopTDao.registerNewShop(map);
		}
		shop.setData(shop_unique);
		
		shop.setStatus(0);
		shop.setMsg("注册成功！");
		map.put("shopUnique", shop_unique);
		k=funDao.addNewShopFunction(shop_unique);//添加店铺功能权限
		kindDao.addNewGoodsKinds(shop_unique);//添加店铺商品分类
		levelDao.addNewCusLevel(map);//添加会员等级分类信息
		funDao.addNewShopsConfig(map);//添加店铺配置信息，店铺的图片上传和免密开通配置信息等
		//添加员工
		k=staffDao.newStaff(staff);
		pmap.put("staff_id", staff.getStaff_id());
		pmap.put("powerRecharge", "1");
		//添加员工权限
		pmap.put("powerManager", "1");
		k=staffDao.newStaffPower(pmap);
		
		//添加店铺升级记录，用于更新
		//1:查询系统的最新版本号，
		//2：添加记录
		String version= utilDao.theLastVersion();
		map.put("versionNumber", version);
		shopDao.newShopUpDateRecord(map);
		
		//添加注册时的图标信息（APP功能）
		shopDao.addShopTitle(map);
		List<Map<String,Object>> hourList = new ArrayList<Map<String,Object>>();
		Map<String,Object> hmap = new HashMap<String,Object>();
		hmap.put("shop_unique", shop_unique);
		hmap.put("start_hours", "07:00:00");
		hmap.put("end_hours", "20:00:00");
		hourList.add(hmap);
		//添加营业时间
		shopDao.addShopHours(hourList);
		
		return shop;
	}
	public ShopsResult queryUpdatePageCount(Map<String,Object> map){
		ShopsResult sr=new ShopsResult(1,"查询成功");
		if(map.get("handleStatus")!=null){
			map.putAll(updateDao.theLaseVersionNumber());
		}
		System.out.println("搜索条件"+map);
		Integer pageCount=updateDao.queryUpdatePageCount(map);	
		System.out.println("页数"+pageCount);
		sr.setData(pageCount);
		return sr;
	}
	
	/**
	 * 查询满足条件的店铺机器信息，升级用
	 * @param map
	 * @return
	 */
	public ShopsResult queryUpdateMessageByPage(Map<String,Object> map){
		ShopsResult sr=new ShopsResult(1, "查询成功！");
		map.putAll(updateDao.theLaseVersionNumber());
		List<Map<String,Object>> maps=updateDao.queryUpdateMessageByPage(map);
		sr.setData(maps);
		return sr;
	}
	
	/**
	 * 添加新的店铺操作指令
	 * @return
	 */
	public ShopsResult operatingMachine(Map<String,Object> map,Integer operateType){
		ShopsResult sr=new ShopsResult(1, "保存成功！");
		//删除已有的机器操作指令
		updateDao.cancelOperateUnexecuted(map);
		if(null!=operateType){
			updateDao.addNewOperate(map);
		}
		return sr;
	}
	
	/**
	 * 设置新版本
	 * @return
	 */
	public ShopsResult setNewEdition(){
		ShopsResult sr=new ShopsResult(1,"设置成功！");
		//删除已有文件信息
		int k=0;
		try{
			//打开升级日志，记录升级文件信息
			File file=new File(Load.FILEFORPCUPDATEPATH+File.separator+"updateLog.txt");
			StringBuffer strs=new StringBuffer();
			String str=null;
			BufferedReader br=new BufferedReader(new FileReader(file));
			while((str=br.readLine())!=null){
				strs.append(str);
			}
			br.close();
			//读取文件的版本信息，
			String version_number=PCUpdateUtil.getVersion(new File(Load.FILEFORPCUPDATEPATH+File.separator+"BUYHOO.exe"));
			Map<String,Object> map=new HashMap<String, Object>();
			map.put("versionNumber", version_number);
			map.put("nversionNumber", version_number);
			map.put("remarks", strs.toString());
			//插入一条升级记录
			//插入前做一次查询，防止版本重复插入
			List<Map<String,Object>> maps=utilDao.queryHistoricalVersion(map);
			//新版本提交成功后，创建新版本的待更新信息
			if(null==maps||maps.isEmpty()){
				utilDao.addNewCashUpdateRecord(map);
				k=utilDao.deleteUpdateFilesMessage();
				List<Map<String,Object>> data=PCUpdateUtil.getMd5FileForUpdate(Load.FILEFORPCUPDATEPATH,1);
				if(null==data||data.isEmpty()){
					sr.setStatus(2);
					sr.setMsg("没有需要更新的文件");
					return sr;
				}
				k=utilDao.addUpdateFilesMessage(data);
			}else{
				sr.setStatus(2);
				sr.setMsg("该版本已提交");
			}
			
			//旧版操作：将未更新的店铺设置为已过期状态；
			utilDao.setVersionNumberOverdue();
			List<Map<String,Object>> mas=utilDao.queryLastVersionNumber(map);//
			map.put("list", mas);
			k = utilDao.addNewVersionForUpdate(map);
		
			//新版操作；将未更新的店铺设置为已过期状态；
			List<String> statusList=new ArrayList<String>();
			statusList.add(UpdateUtil.COMPLEHANDLESTATUS);
			statusList.add(UpdateUtil.CANCELHANDLESTATUS);
			map.put("cancelHandleStatus",UpdateUtil.CANCELHANDLESTATUS);
			map.put("list", statusList);
			k=updateDao.setUpdateStatusOverDue(map);
			
			//新版操作：查询需要升级的机器列表
			map.put("onHandleStatus", UpdateUtil.ONHANDLESTATUS);
			map.put("examinestatus", UpdateUtil.EXAMINESTATUS);
			map.put("useType", UpdateUtil.OPERATEUSETYPE);
			List<Map<String,Object>> macs=updateDao.queryMacNeedUpdate(map);
			
			map.put("handleStatus", UpdateUtil.HANDLESTATUS);
			map.put("downloadStatus", UpdateUtil.DOWNLOADSTATUS);
			if(macs.size()>UpdateUtil.MAXNUMALLOWDOWNLOAD){
				List<Map<String, Object>> list=macs.subList(0, UpdateUtil.MAXNUMALLOWDOWNLOAD);
				map.put("list", list);
				updateDao.addNewEdition(map);
				macs.subList(0, UpdateUtil.MAXNUMALLOWDOWNLOAD).clear();
			}
			map.put("list", macs);
			
			updateDao.addNewEdition(map);
		}catch(Exception e){
			System.out.println(k);
			e.printStackTrace();
			sr.setStatus(2);
			sr.setMsg("更新失败");
		}
		return sr;
	}
	
	/**
	 * 所有店铺的版本信息
	 * @return
	 */
	public ShopsResult queryVersionNumberList(){
		ShopsResult sr=new ShopsResult(1, "查询成功！");
		List<String> list=updateDao.queryVersionNumberList();
		
		sr.setData(list);
		return sr;
	}
	
	/**
	 * 保存SQL信息
	 * @param sqlPc
	 * @return
	 */
	public ShopsResult saveSqlCmd(SqlPc sqlPc){
		ShopsResult sr=new ShopsResult(1,"保存成功！");
		System.out.println(sqlPc);
		Integer k=updateDao.saveSqlCmd(sqlPc);
		if(k==0){
			sr.setStatus(2);
			sr.setMsg("保存失败！");
		}
		return sr;
	}
}
