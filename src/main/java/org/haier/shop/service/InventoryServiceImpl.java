package org.haier.shop.service;

import java.util.List;
import java.util.Map;

import javax.annotation.Resource;

import org.haier.shop.dao.InventoryDao;
import org.haier.shop.util.PurResult;
import org.haier.shop.util.ShopsResult;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Service
public class InventoryServiceImpl implements InventoryService{
	
	@Resource
	private InventoryDao invDao;
	/**
	 * 商品盘库订单信息查询
	 * @param map
	 * @return
	 */
	public PurResult queryInventoryRecord(Map<String,Object> map){
		PurResult result=new PurResult();
		List<Map<String,Object>> list=invDao.queryInventoryRecord(map);
		result.setStatus(1);
		result.setMsg("查询成功");
		result.setCount(invDao.queryInventoryRecordPages(map));
		result.setData(list);
		return result;
	}
	
	public List<Map<String,Object>> queryInventoryList(Map<String,Object> map){
		return invDao.queryInventoryRecord(map);
	
	}
	
	/**
	 * 盘点订单详情
	 * @param map
	 * @return
	 */
	public PurResult queryInventoryDetail(Map<String,Object> map){
		PurResult sr=new PurResult();
		Map<String,Object> detail=invDao.queryInventoryDetail(map);
		List<Map<String,Object>> list=invDao.queryInventoryGoodList(map);
		sr.setData(list);
		sr.setCord(detail);
		sr.setStatus(1);
		if(list.size()>0) {
			sr.setMsg("查询成功");
		}else {
			sr.setMsg("无数据");
		}
		
		sr.setCount(list.size());
		return sr;
	}
}
