package org.haier.shop.service.supplier;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import org.haier.shop.dao.supplier.ShopSupBillDao;
import org.haier.shop.dao.supplier.ShopSupplierDao;
import org.haier.shop.dao.supplier.ShopsRestockPlanDao;
import org.haier.shop.entity.supplier.*;
import org.haier.shop.params.shopSupBill.AddSupRelParams;
import org.haier.shop.params.shopSupBill.UpdateBillStatusParams;
import org.haier.shop.params.shopSupBill.externalCall.GoodsDetailInfoParams;
import org.haier.shop.params.shopSupBill.externalCall.GoodsInfoParams;
import org.haier.shop.params.shopSupBill.externalCall.ShopGoodsInfoParams;
import org.haier.shop.params.shopSupplier.*;
import org.haier.shop.params.shopSupplier.paramsDao.QuerySupKindInfoByKuParams;
import org.haier.shop.params.shopSupplier.paramsDao.SupKindSortParams;
import org.haier.shop.params.shopSupplier.paramsDao.SupKindUniqueParams;
import org.haier.shop.result.shopSupplier.*;
import org.haier.shop.util.OrderNoUtils;
import org.haier.shop.util.ShopsResult;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.*;

@Service
@Transactional
public class ShopSupplierServiceImpl implements ShopSupplierService {
    @Autowired
    private ShopSupBillService shopSupBillService;
    @Autowired
    private ShopSupplierDao shopSupplierDao;
    @Autowired
    private ShopSupBillDao shopSupBillDao;
    @Autowired
    private ShopsRestockPlanDao shopsRestockPlanDao;

    @Override
    public ShopsResult addSupKind(SupKindAddParams params) {
        ShopsResult result = new ShopsResult();
        ShopSupSupplierKindEntity entity = new ShopSupSupplierKindEntity();
        entity.setShopUnique(params.getShopUnique());
        entity.setSupplierKindName(params.getSupKindName());
        if (params.getSupKindParunique() != null && !"0".equals(params.getSupKindParunique())) {
            ShopSupSupplierKindEntity shopSupSupplierKindEntity = shopSupplierDao.querySupKindInfoByKindName(params);
            if (shopSupSupplierKindEntity != null) {
                result.setStatus(0);
                result.setMsg("该分类已存在");
                return result;
            }
            QuerySupKindInfoByKuParams querySupKindInfoByKuParams = new QuerySupKindInfoByKuParams();
            querySupKindInfoByKuParams.setShopUnique(params.getShopUnique());
            querySupKindInfoByKuParams.setSupKindUnique((params.getSupKindParunique()));
            ShopSupSupplierKindEntity kindParuniqueEntity = shopSupplierDao.querySupKindInfo(querySupKindInfoByKuParams);
            if (kindParuniqueEntity == null) {
                result.setStatus(0);
                result.setMsg("父级分类编号不存在");
                return result;
            }
            if (kindParuniqueEntity.getSupplierKindLevel() == 2) {
                result.setStatus(0);
                result.setMsg("供货商分类目前只有两级，请在上级分类下添加");
                return result;
            }
            //查询父类下分类排序最大值
            entity.setSupplierKindParunique(params.getSupKindParunique());
            entity.setSupplierKindLevel(2);
            Map<String, Object> orderSort = shopSupplierDao.querySupKindSort(querySupKindInfoByKuParams);
            if (orderSort == null) {
                entity.setOrderSort(0);
            } else {
                entity.setOrderSort(Integer.parseInt(String.valueOf(orderSort.get("orderSort"))) + 1);
            }
        } else {
            params.setSupKindParunique("0");
            ShopSupSupplierKindEntity shopSupSupplierKindEntity = shopSupplierDao.querySupKindInfoByKindName(params);
            if (shopSupSupplierKindEntity != null) {
                result.setStatus(0);
                result.setMsg("该分类已存在");
                return result;
            }

            entity.setSupplierKindParunique("0");
            entity.setSupplierKindLevel(1);
            QuerySupKindInfoByKuParams querySupKindInfoByKuParams = new QuerySupKindInfoByKuParams();
            querySupKindInfoByKuParams.setShopUnique(params.getShopUnique());
            querySupKindInfoByKuParams.setSupKindUnique(("0"));
            Map<String, Object> orderSort = shopSupplierDao.querySupKindSort(querySupKindInfoByKuParams);
            if (orderSort == null) {
                entity.setOrderSort(0);
            } else {
                entity.setOrderSort(Integer.parseInt(String.valueOf(orderSort.get("orderSort"))) + 1);
            }
        }
        String kindUnique = OrderNoUtils.createOrderNo("SSK");

        entity.setSupplierKindUnique(kindUnique);
        entity.setCreateId(params.getCreateId());
        entity.setCreateBy(params.getCreateBy());
        int i = shopSupplierDao.addShopSupSupplierKindEntity(entity);
        result.setStatus(1);
        result.setMsg("添加成功");
        result.setData(kindUnique);
        return result;
    }

    @Override
    public ShopsResult modifySupKind(SupKindModifyParams params) {
        ShopsResult result = new ShopsResult();
        if (params.getModifyType() == 1) {//修改
            ShopSupSupplierKindEntity entity = new ShopSupSupplierKindEntity();
            entity.setShopUnique(params.getShopUnique());
            entity.setSupplierKindUnique(params.getSupKindUnique());
            entity.setModifyBy(params.getCreateBy());
            entity.setModifyId(params.getCreateId());
            entity.setSupplierKindName(params.getSupKindName());
            int i = shopSupplierDao.updateShopSupSupplierKindEntity(entity);
            if (i == 1) {
                result.setStatus(1);
                result.setMsg("修改成功");
            } else {
                result.setStatus(0);
                result.setMsg("修改失败");
            }
        } else if (params.getModifyType() == 2) {//删除
            int m = 1;
            SupKindUniqueParams supKindUniqueParams = new SupKindUniqueParams();
            BeanUtil.copyProperties(params, supKindUniqueParams);
            List<ShopSupSupplierKindEntity> shopSupSupplierKindEntity = shopSupplierDao.querySupKindInfoFromSupplier(supKindUniqueParams);
            if (shopSupSupplierKindEntity != null && !shopSupSupplierKindEntity.isEmpty()) {
                result.setStatus(0);
                result.setMsg("有供货商在此分类中不允许删除");
                return result;
            }
            int n = shopSupplierDao.deleteSupKindBySupKindUnique(supKindUniqueParams);
            List<ShopSupSupplierKindEntity> subSupKindEntities = shopSupplierDao.querySupKindListByKindPreUnique(supKindUniqueParams);
            if (null != subSupKindEntities && !subSupKindEntities.isEmpty()) {
                m += subSupKindEntities.size();
            }
            for (int i = 0; null != subSupKindEntities && !subSupKindEntities.isEmpty() && i < subSupKindEntities.size(); i++) {
                SupKindUniqueParams subSupKindUniqueParams = new SupKindUniqueParams();
                ShopSupSupplierKindEntity subSupKindEntity = subSupKindEntities.get(i);
                subSupKindUniqueParams.setShopUnique(subSupKindEntity.getShopUnique());
                subSupKindUniqueParams.setSupKindUnique(subSupKindEntity.getSupplierKindUnique());
                n += shopSupplierDao.deleteSupKindBySupKindUnique(subSupKindUniqueParams);
            }
            if (m == n) {
                result.setStatus(1);
                result.setMsg("删除成功");
            } else {
                result.setStatus(0);
                result.setMsg("删除失败");
            }

        }
        return result;
    }

    @Override
    public ShopsResult updateSupKindSort(SupKindSortUpdateParams params) {
        ShopsResult result = new ShopsResult();
        String supKindSortList = params.getSupKindSortList();
        if (supKindSortList != null && !supKindSortList.isEmpty()) {
            List<SupKindSortParams> list = new ArrayList<>();
            JSONArray supKindSortArray = JSONUtil.parseArray(supKindSortList);
            int j = supKindSortArray.size();
            for (int i = 0; !supKindSortArray.isEmpty() && i < supKindSortArray.size(); i++) {
                JSONObject supKindSortObject = supKindSortArray.getJSONObject(i);
                SupKindSortParams supKindSortParams = new SupKindSortParams();
                supKindSortParams.setSupplierKindUnique(supKindSortObject.getStr("supplierKindUnique"));
                supKindSortParams.setOrderSort(j - i - 1);
                list.add(supKindSortParams);
            }
            shopSupplierDao.updateSupKindSortList(list);
            result.setStatus(1);
            result.setMsg("更新成功");
        }
        return result;
    }

    @Override
    public ShopsResult querySupKindList(ShopUniqueParams params) {
        ShopsResult result = new ShopsResult();
        List<ShopSupplierKindResult> shopSupplierListResult = shopSupplierDao.querySupKindList(params);
        result.setStatus(1);
        result.setMsg("查询成功");
        result.setData(shopSupplierListResult);
        return result;
    }

    @Override
    public ShopsResult addShopSupRel(AddSupRelParams params) {
        ShopsResult result = new ShopsResult();
        Map<String, Object> map = new HashMap<>();
        map.put("shopUnique", params.getShopUnique());
        map.put("supplierUnique", params.getSupplierUnique());
        ShopSupSupplierExamineEntity shopSupSupplierExamineEntity = shopSupplierDao.queryShopSupplierByUnique(map);
        if (shopSupSupplierExamineEntity != null) {
            ShopSupSupplierExamineEntity entity = new ShopSupSupplierExamineEntity();
            BeanUtil.copyProperties(params, entity);
            entity.setId(shopSupSupplierExamineEntity.getId());
            int i = shopSupplierDao.updateShopSupSupplierExamineEntity(entity);
            if (shopSupSupplierExamineEntity.getBindFlag() == 1) {
                ShopSupSupplierEntity supplierEntity = new ShopSupSupplierEntity();
                BeanUtil.copyProperties(params, supplierEntity);
                /*supplierEntity.setShopUnique(params.getShopUnique());
                supplierEntity.setSupplierUnique(params.getSupplierUnique());
                supplierEntity.setSupplierName(params.getSupplierName());
                supplierEntity.setContactMobile(params.getContactMobile());
                supplierEntity.setAddress(params.getAddress());
                supplierEntity.setContacts(params.getContacts());*/
                int j = shopSupplierDao.updateShopSupSupplierEntity(supplierEntity);
            }
            result.setStatus(1);
            result.setMsg("修改成功");
        } else {
            ShopSupSupplierEntity entity = new ShopSupSupplierEntity();
            BeanUtil.copyProperties(params, entity);
            int id = shopSupplierDao.addShopSupRel(entity);
            result.setStatus(1);
            result.setMsg("新增成功");
        }
        return result;
    }

    @Override
    public ShopsResult addSupInfo(SupInfoAddParams params) {
        ShopsResult result = new ShopsResult();
        Map<String, Object> map = new HashMap<>();
        map.put("shopUnique", params.getShopUnique());
        map.put("contactMobile", params.getContactMobile());
        ShopSupSupplierEntity shopSupSupplierEntity = shopSupplierDao.queryShopSupplierByMobile(map);
        if (shopSupSupplierEntity != null) {
            result.setStatus(0);
            result.setMsg("该供货商已添加");
            return result;
        }
        ShopSupSupplierEntity entity = new ShopSupSupplierEntity();
        BeanUtil.copyProperties(params, entity);
        entity.setPurchaseType(2);
        shopSupplierDao.addShopSupSupplierEntity(entity);
        entity.setSupplierUnique(String.valueOf(entity.getId()));
        shopSupplierDao.updateShopSupSupplierEntity(entity);
        result.setStatus(1);
        result.setMsg("添加成功");
        result.setData(entity.getId());
        return result;
    }

    @Override
    public ShopsResult updateSupInfo(SupInfoUpdateParams params) {
        ShopsResult result = new ShopsResult();
        if (2 == params.getEnableStatus()) {
            Map<String, Object> map = new HashMap<>();
            map.put("shopUnique", params.getShopUnique());
            map.put("id", params.getId());
            ShopSupSupplierEntity shopSupSupplierEntity = shopSupplierDao.querySupplierById(map);
            if (shopSupSupplierEntity != null) {
                String supplierUnique = shopSupSupplierEntity.getSupplierUnique();
                SupplierUniqueParams supplierUniqueParams = new SupplierUniqueParams();
                supplierUniqueParams.setShopUnique(params.getShopUnique());
                supplierUniqueParams.setSupplierUnique(supplierUnique);
                List<ShopSupBillEntity> shopSupBillEntity = shopSupBillDao.queryShopSupBillEntityBySupplierUnique(supplierUniqueParams);
                if (shopSupBillEntity != null && !shopSupBillEntity.isEmpty()) {
                    result.setStatus(0);
                    result.setMsg("该供应商商品存在购销单中，无法停用");
                    return result;
                }

                List<RestockPlanEntity> restockPlanEntity = shopsRestockPlanDao.queryRestockPlanBySupplierUnique(supplierUniqueParams);
                if (restockPlanEntity != null && !restockPlanEntity.isEmpty()) {
                    result.setStatus(0);
                    result.setMsg("该供应商商品存在已生成补货任务中，无法停用");
                    return result;
                }
            }
        }
        ShopSupSupplierEntity entity = new ShopSupSupplierEntity();
        BeanUtil.copyProperties(params, entity);
        int i = shopSupplierDao.updateShopSupSupplierEntity(entity);
        if (i == 1) {
            result.setStatus(1);
            result.setMsg("修改成功");
        } else {
            result.setStatus(0);
            result.setMsg("修改失败");
        }
        result.setData(entity.getId());
        return result;
    }

    @Override
    public ShopsResult deleteSupInfo(SupplierIdParams params) {
        ShopsResult result = new ShopsResult();

        Map<String, Object> map = new HashMap<>();
        map.put("shopUnique", params.getShopUnique());
        map.put("id", params.getId());
        ShopSupSupplierEntity shopSupSupplierEntity = shopSupplierDao.querySupplierById(map);
        if (shopSupSupplierEntity != null) {
            String supplierUnique = shopSupSupplierEntity.getSupplierUnique();
            SupplierUniqueParams supplierUniqueParams = new SupplierUniqueParams();
            supplierUniqueParams.setShopUnique(params.getShopUnique());
            supplierUniqueParams.setSupplierUnique(supplierUnique);
            List<ShopSupBillEntity> shopSupBillEntity = shopSupBillDao.queryShopSupBillEntityBySupplierUnique(supplierUniqueParams);
            if (shopSupBillEntity != null && !shopSupBillEntity.isEmpty()) {
                result.setStatus(0);
                result.setMsg("该供应商商品存在购销单中，无法删除");
                return result;
            }
            List<RestockPlanEntity> restockPlanEntity = shopsRestockPlanDao.queryRestockPlanBySupplierUnique(supplierUniqueParams);
            if (restockPlanEntity != null && !restockPlanEntity.isEmpty()) {
                result.setStatus(0);
                result.setMsg("该供应商商品存在已生成补货任务中，无法删除");
                return result;
            }
            List<ShopSupplierBillResult> shopSupplierBillResults = shopSupplierDao.queryUnpaidBillList(supplierUniqueParams);
            if (shopSupplierBillResults != null && !shopSupplierBillResults.isEmpty()) {
                result.setStatus(0);
                result.setMsg("您存在未付款金额，无法删除");
                return result;
            }
        }
        int i = shopSupplierDao.deleteShopSupSupplierEntityLogic(params);
        if (i == 1) {
            result.setStatus(1);
            result.setMsg("删除成功");
        } else {
            result.setStatus(0);
            result.setMsg("删除失败");
        }
        return result;
    }

    @Override
    public ShopsResult querySupList(QuerySupListParams params) {
        ShopsResult result = new ShopsResult();
        params.setPageIndex((params.getPageIndex() - 1) * params.getPageSize());
        if (params.getSupplierName() != null) {
            params.setSupplierName("%" + params.getSupplierName() + "%");
        }
        if (params.getContacts() != null) {
            params.setContacts("%" + params.getContacts() + "%");
        }
        if (params.getContactMobile() != null) {
            params.setContactMobile("%" + params.getContactMobile() + "%");
        }
        List<ShopSupplierListResult> list = shopSupplierDao.querySupList(params);
        result.setStatus(1);
        result.setMsg("查询成功");
        result.setData(list);
        return result;
    }

    @Override
    public ShopsResult querySupBusinessInfo(SupplierUniqueParams params) {
        ShopsResult result = new ShopsResult();
        ShopSupplierBussResult bussResult = shopSupplierDao.querySupBusinessInfo(params);
        if (ObjectUtil.isNotEmpty(bussResult)) {
            Long billCount = shopSupBillDao.selectBillCount(params);
            bussResult.setBillCount(billCount);
        }
        result.setStatus(1);
        result.setMsg("查询成功");
        result.setData(bussResult);
        return result;
    }

    @Override
    public ShopsResult querySupBillInfo(QueryBillListParams params) {
        ShopsResult result = new ShopsResult();
        List<ShopSupplierBillResult> billResult = shopSupplierDao.querySupBillInfo(params);
        result.setStatus(1);
        result.setMsg("查询成功");
        result.setData(billResult);
        return result;
    }

    @Override
    public ShopsResult querySupPaymentInfo(SupplierUniqueParams params) {
        ShopsResult result = new ShopsResult();
        List<ShopSupplierPayResult> payResult = shopSupplierDao.querySupPaymentInfo(params);
        result.setStatus(1);
        result.setMsg("查询成功");
        result.setData(payResult);
        return result;
    }

    @Override
    public ShopsResult addGoods(GoodsInfoParams params) {
        ShopsResult result = new ShopsResult();
        List<GoodsDetailInfoParams> goodsList = params.getGoodsList();
        for (int i = 0; goodsList != null && !goodsList.isEmpty() && i < goodsList.size(); i++) {
            GoodsDetailInfoParams detailInfoParams = goodsList.get(i);
            Map<String, Object> map = new HashMap<>();
            map.put("shopUnique", params.getCustomerUnique());
            map.put("supplierUnique", params.getSupplierUnique());
            map.put("goodsBarcode", detailInfoParams.getGoodsBarcode());
            ShopSupGoodsEntity shopSupGoodsEntityNow = shopSupplierDao.queryShopSupGoodsByGoodsBarcode(map);
            if (shopSupGoodsEntityNow != null) {
                result.setStatus(0);
                result.setMsg("商品条码为【" + detailInfoParams.getGoodsBarcode() + "】的商品已添加，请勿重复添加");
                return result;
            }
            ShopSupGoodsEntity shopSupGoodsEntity = new ShopSupGoodsEntity();
            BeanUtil.copyProperties(detailInfoParams, shopSupGoodsEntity);
            shopSupGoodsEntity.setShopUnique(Long.valueOf(params.getCustomerUnique()));
            shopSupGoodsEntity.setSupplierUnique(params.getSupplierUnique());
            shopSupplierDao.addShopSupGoodsEntity(shopSupGoodsEntity);

        }
        result.setStatus(1);
        result.setMsg("添加未建档商品成功");
        return result;
    }

    @Override
    public ShopsResult addShopsGood(ShopGoodsInfoParams params) {
        ShopsResult result = new ShopsResult();
        List<String> shopList = params.getShopList();
        if (shopList == null || shopList.isEmpty()) {
            result.setStatus(0);
            result.setMsg("店铺编号不存在");
            return result;
        }
        List<GoodsDetailInfoParams> goodsList = params.getGoodsList();
        if (goodsList == null || goodsList.isEmpty()) {
            result.setStatus(0);
            result.setMsg("商品信息不存在");
            return result;
        }
        for (String shopUnique : shopList) {
            for (GoodsDetailInfoParams detailInfoParams : goodsList) {
                Map<String, Object> map = new HashMap<>();
                map.put("shopUnique", shopUnique);
                map.put("supplierUnique", params.getSupplierUnique());
                map.put("goodsBarcode", detailInfoParams.getGoodsBarcode());
                ShopSupGoodsEntity shopSupGoodsEntityNow = shopSupplierDao.queryShopSupGoodsByGoodsBarcode(map);
                if (shopSupGoodsEntityNow != null) {
                    result.setStatus(0);
                    result.setMsg("商品条码为【" + detailInfoParams.getGoodsBarcode() + "】的商品已添加，请勿重复添加");
                    return result;
                }
                ShopSupGoodsEntity shopSupGoodsEntity = new ShopSupGoodsEntity();
                BeanUtil.copyProperties(detailInfoParams, shopSupGoodsEntity);
                shopSupGoodsEntity.setShopUnique(Long.valueOf(shopUnique));
                shopSupGoodsEntity.setSupplierUnique(params.getSupplierUnique());
                shopSupplierDao.addShopSupGoodsEntity(shopSupGoodsEntity);
            }
        }
        result.setStatus(1);
        result.setMsg("添加未建档商品成功");
        return result;
    }

    @Override
    public ShopsResult querySupRecordGoodList(QueryRecordGoodsListParams params) {
        ShopsResult result = new ShopsResult();
        List<ShopSupplierRecordGoodsResult> goodsList = shopSupplierDao.querySupRecordGoodList(params);
        for (int i = 0; goodsList != null && !goodsList.isEmpty() && i < goodsList.size(); i++) {
            ShopSupplierRecordGoodsResult recordGoods = goodsList.get(i);
            String goodsBarcode = recordGoods.getGoodsBarcode();
            if (goodsBarcode != null) {
                Map<String, Object> map = new HashMap<>();
                map.put("goodsBarcode", goodsBarcode);
                map.put("shopUnique", params.getShopUnique());
                BigDecimal monthlySales = shopSupplierDao.queryMonthlySales(map);
                recordGoods.setMonthlySales(monthlySales);
            }
        }
        result.setStatus(1);
        result.setMsg("查询成功");
        result.setData(goodsList);
        return result;
    }

    @Override
    public ShopsResult queryQepaymentInfo(QueryRepayHisInfoParams params) {
        ShopsResult result = new ShopsResult();
        ShopSupplierPayDetailResult payDetailResult = shopSupplierDao.queryQepaymentInfo(params);
        result.setStatus(1);
        result.setMsg("查询成功");
        result.setData(payDetailResult);
        return result;
    }

    @Override
    public ShopsResult queryUnpaidBillList(SupplierUniqueParams params) {
        ShopsResult result = new ShopsResult();
        Map<String, Object> map = new HashMap<>();
        List<ShopSupplierBillResult> billResult = shopSupplierDao.queryUnpaidBillList(params);
        if (billResult != null) {
            map.put("outstandingCount", billResult.size());
            BigDecimal outstandingMoney = BigDecimal.ZERO;
            for (ShopSupplierBillResult shopSupplierBillResult : billResult) {
                outstandingMoney = outstandingMoney.add(shopSupplierBillResult.getTotalPrice());
            }
            map.put("purchaseAmounts", outstandingMoney);
            map.put("billList", billResult);
        }
        result.setStatus(1);
        result.setMsg("查询成功");
        result.setData(map);
        return result;
    }

    @Override
    public ShopsResult repaymentBills(RepaymentBillsParams params) {
        ShopsResult result = new ShopsResult();
        if (params.getBillIdList().isEmpty()) {
            result.setStatus(0);
            result.setMsg("请选择购销单");
            return result;
        }
        BillIdParams billIdParams = new BillIdParams();
        BeanUtil.copyProperties(params, billIdParams);
        billIdParams.setBillId(params.getBillIdList().get(0));
        ShopSupSupplierExamineEntity shopSupSupplierExamineEntity = shopSupplierDao.queryShopSupplierExamineByUnique(billIdParams);
        if (shopSupSupplierExamineEntity != null) {
            if (params.getVoucherPicturepath() == null || params.getVoucherPicturepath().isEmpty()) {
                result.setStatus(0);
                result.setMsg("请上传付款凭证");
                return result;
            }
        }
        //将购销单ID列表转变为逗号分隔的字符串
        StringBuilder strBuilder = new StringBuilder();
        for (String billId : params.getBillIdList()) {
            if (strBuilder.length() > 0) {
                strBuilder.append(",").append(billId);
            } else {
                strBuilder.append(billId);
            }
        }
        String billIds = strBuilder.toString();
        ShopSupPaymentOrderEntity paymentOrderEntity = new ShopSupPaymentOrderEntity();
        paymentOrderEntity.setShopUnique(params.getShopUnique());
        paymentOrderEntity.setSupplierUnique(params.getSupplierUnique());
        paymentOrderEntity.setBillId(billIds);
        paymentOrderEntity.setPaymentMoney(params.getPaymentMoney());
        paymentOrderEntity.setRemark(params.getRemark());
        paymentOrderEntity.setCreateId(params.getCreateId());
        paymentOrderEntity.setCreateBy(params.getCreateBy());
        //保存付款信息
        int i = shopSupBillDao.addShopSupPaymentOrderEntity(paymentOrderEntity);
        List<String> voucherPicturepathList = params.getVoucherPicturepath();
        for (String s : voucherPicturepathList) {
            ShopSupPaymentEvidenceEntity paymentEvidenceEntity = new ShopSupPaymentEvidenceEntity();
            paymentEvidenceEntity.setPaymentId(paymentOrderEntity.getPaymentId());
            paymentEvidenceEntity.setVoucherPicturepath(s);
            paymentEvidenceEntity.setCreateId(params.getCreateId());
            paymentEvidenceEntity.setCreateBy(params.getCreateBy());
            //保存付款凭证信息
            int k = shopSupBillDao.addShopSupPaymentEvidenceEntity(paymentEvidenceEntity);
        }
        for (String billId : params.getBillIdList()) {
            UpdateBillStatusParams updateBillStatusParams = new UpdateBillStatusParams();
            updateBillStatusParams.setId(Long.valueOf(billId));
            updateBillStatusParams.setShopUnique(params.getShopUnique());
            updateBillStatusParams.setCreateId(params.getCreateId());
            updateBillStatusParams.setCreateBy(params.getCreateBy());
            if (shopSupSupplierExamineEntity != null) {
                updateBillStatusParams.setStatus(3);
            } else {
                updateBillStatusParams.setStatus(4);
            }
            shopSupBillService.updateBillStatus(updateBillStatusParams);
        }
        result.setStatus(1);
        result.setMsg("保存成功");
        return result;
    }

    @Override
    public ShopsResult updateSupRecordGood(SupRecordGoodsUpdateParams params) {
        ShopsResult result = new ShopsResult();
        ShopSupGoodsEntity entity = new ShopSupGoodsEntity();
        BeanUtil.copyProperties(params, entity);
        entity.setRecordFlag(1);
        int i = shopSupplierDao.updateShopSupGoodsEntity(entity);
        result.setStatus(1);
        result.setMsg("更新成功");
        return result;
    }

    @Override
    public ShopsResult bindSupplier(BindSupParams params) {
        ShopsResult result = new ShopsResult();
        ShopSupSupplierExamineEntity shopSupSupplierExamineEntity = shopSupplierDao.queryShopSupplierExamineById(params);
        if (shopSupSupplierExamineEntity != null) {
            shopSupSupplierExamineEntity.setBindFlag(1);
            shopSupplierDao.updateShopSupSupplierExamineEntity(shopSupSupplierExamineEntity);
            ShopSupSupplierEntity shopSupSupplierEntity = new ShopSupSupplierEntity();
            BeanUtil.copyProperties(shopSupSupplierExamineEntity, shopSupSupplierEntity);
            shopSupSupplierEntity.setEnableStatus(1);
            shopSupSupplierEntity.setPurchaseType(1);
            shopSupplierDao.addShopSupSupplierEntity(shopSupSupplierEntity);
        } else {
            result.setStatus(0);
            result.setMsg("未查询到供货商申请信息");
        }
        return result;
    }
    @Override
    public ShopsResult querySupExamineList(QuerySupListParams params) {
        ShopsResult result = new ShopsResult();
        params.setPageIndex((params.getPageIndex() - 1) * params.getPageSize());
        if (params.getSupplierName() != null) {
            params.setSupplierName("%" + params.getSupplierName() + "%");
        }
        if (params.getContacts() != null) {
            params.setContacts("%" + params.getContacts() + "%");
        }
        if (params.getContactMobile() != null) {
            params.setContactMobile("%" + params.getContactMobile() + "%");
        }
        List<ShopSupSupplierExamineEntity> list = shopSupplierDao.queryShopSupplierExamineList(params);
        result.setStatus(1);
        result.setData(list);
        result.setMsg("查询成功");
        return result;
    }
    @Override
    public ShopsResult deleteShopSupGoodsEntity(ShopSupGoodsDeleteParams params) {
        ShopsResult result = new ShopsResult();
        int i = shopSupplierDao.deleteShopSupGoodsEntity(params);
        if (i == 1) {
            result.setStatus(1);
            result.setMsg("删除成功");
        } else {
            result.setStatus(0);
            result.setMsg("删除失败");
        }
        return result;
    }
}
