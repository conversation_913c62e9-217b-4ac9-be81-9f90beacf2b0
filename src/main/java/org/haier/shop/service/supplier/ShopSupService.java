package org.haier.shop.service.supplier;

import org.haier.shop.util.PurResult;

import java.util.List;
import java.util.Map;

public interface ShopSupService {
    /**
     * 根据店铺编码查询供货商列表信息
     * @param params (shopUnique：店铺编号)
     * @return
     */
    PurResult querySupplierList(Map<String,Object> params);

    /**
     * 商品编辑页面添加供货商
     * @param params（shopUnique：店铺编号；goodsBarcode：商品条码；supplierUnique：供货商编号）
     * @return
     */
    PurResult addSupGood(Map<String,Object> params, List<String> bindGoodList);

    /**
     * 商品列表页面多选商品批量更换供货商
     * @param params（shopUnique：店铺列表；goodsList：商品条码；supplierUnique：供货商编号）
     * @return
     */
    PurResult batchAddSupGoods(Map<String,Object> params);

}
