package org.haier.shop.service.supplier;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.http.HttpUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import org.haier.shop.dao.GoodsDao;
import org.haier.shop.dao.supplier.ShopSupDao;
import org.haier.shop.entity.GoodsEntity;
import org.haier.shop.entity.supplier.ShopSupGoodsEntity;
import org.haier.shop.params.CustomerGoodsBingParams;
import org.haier.shop.params.CustomerGoodsDeleteParams;
import org.haier.shop.util.PurResult;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;

@Service
@Transactional
public class ShopSupServiceImpl implements ShopSupService {
    @Resource
    private ShopSupDao shopSupDao;
    @Resource
    private GoodsDao goodsDao;
    private final static String URL = "http://suppliert.allscm.top/cpfr";

    @Override
    public PurResult querySupplierList(Map<String, Object> params) {
        PurResult result = new PurResult();
        try {
            if (params.get("shopUnique") == null || params.get("shopUnique") == "") {
                result.setStatus(0);
                result.setMsg("店铺编号不能为空");
                return result;
            }
            List<Map<String, Object>> list = shopSupDao.querySupplierList(params);
            result.setData(list);
            result.setStatus(1);
            result.setMsg("成功");
        } catch (Exception e) {
            result.setStatus(0);
            result.setMsg("异常");
        }
        return result;
    }

    @Override
    public PurResult addSupGood(Map<String, Object> params, List<String> bindGoodList) {
        PurResult result = new PurResult();
        result.setStatus(1);
        try {
            String checkMsg = checkAddSupGoodsParams(params);
            if (checkMsg != null) {
                result.setStatus(0);
                result.setMsg(checkMsg);
                return result;
            }
            //查询建档商品信息
            Map<String, Object> supGoodResult = shopSupDao.querySupGoodByBarcode(params);
            bindGoodList.add(String.valueOf(params.get("goodsBarcode")));
            if (supGoodResult != null) {
                if (supGoodResult.get("supplier_unique") == null || "".equals(supGoodResult.get("supplier_unique"))) {
                    result.setStatus(0);
                    result.setMsg("商品信息异常(无原供货商信息)");
                    return result;
                } else if (!Objects.equals(String.valueOf(supGoodResult.get("record_flag")), "1")) {
                    result.setStatus(0);
                    result.setMsg("商品未建档");
                    return result;
                } else if (!String.valueOf(supGoodResult.get("supplier_unique")).equals(String.valueOf(params.get("supplierUnique")))) {
                    int i = shopSupDao.updateSupplier(params);//更换供货商
                    Map<String, Object> shopSupSupplierExamineEntityResult = shopSupDao.queryShopSupSupplierExamineEntity(params);
                    if (shopSupSupplierExamineEntityResult != null) {
                        CustomerGoodsBingParams customerGoodsBingParams = new CustomerGoodsBingParams();
                        customerGoodsBingParams.setSupplierUnique(String.valueOf(params.get("supplierUnique")));
                        if (supGoodResult.get("supplier_unique") != null) {
                            Map<String, Object> oldMap = new HashMap<>();
                            oldMap.put("shopUnique", params.get("shopUnique"));
                            oldMap.put("supplierUnique", supGoodResult.get("supplier_unique"));
                            Map<String, Object> oldShopSupSupplierExamineEntityResult = shopSupDao.queryShopSupSupplierExamineEntity(oldMap);
                            if (oldShopSupSupplierExamineEntityResult != null) {
                                customerGoodsBingParams.setOldSupplierUnique(String.valueOf(supGoodResult.get("supplier_unique")));
                            }
                        }
                        customerGoodsBingParams.setCustomerUnique(String.valueOf(params.get("shopUnique")));
                        customerGoodsBingParams.setGoodsBarcode(String.valueOf(supGoodResult.get("goods_barcode")));
                        customerGoodsBingParams.setGoodsName(String.valueOf(supGoodResult.get("goods_name")));
                        customerGoodsBingParams.setGoodsUnit(String.valueOf(supGoodResult.get("goods_unit")));
                        customerGoodsBingParams.setExpirationDate(Integer.valueOf(String.valueOf(supGoodResult.get("expiration_date"))));
                        customerGoodsBingParams.setGoodsImageUrl(String.valueOf(supGoodResult.get("goods_image_url")));
                        customerGoodsBingParams.setGoodsSalePrice(new BigDecimal(String.valueOf(supGoodResult.get("goods_sale_price"))));
                        String data = JSONUtil.toJsonStr(customerGoodsBingParams);
                        PurResult result1 = httpPost(URL + "/external/goods/bingGoods", data);
                        if (result1.getStatus() != 1) {
                            result.setStatus(0);
                            result.setMsg(result1.getMsg());
                            throw new RuntimeException(result1.getMsg());
                        }
                    } else {
                        CustomerGoodsDeleteParams customerGoodsDeleteParams = new CustomerGoodsDeleteParams();
                        customerGoodsDeleteParams.setSupplierUnique(String.valueOf(supGoodResult.get("supplier_unique")));
                        customerGoodsDeleteParams.setCustomerUnique(String.valueOf(params.get("shopUnique")));
                        customerGoodsDeleteParams.setGoodsBarcode(String.valueOf(supGoodResult.get("goods_barcode")));
                        String data = JSONUtil.toJsonStr(Collections.singletonList(customerGoodsDeleteParams));
                        PurResult result1 = httpPost(URL + "/external/goods/deleteBingGoods", data);
                        if (result1.getStatus() != 1) {
                            result.setStatus(0);
                            result.setMsg(result1.getMsg());
                            throw new RuntimeException(result1.getMsg());
                        }
                    }
                    if (i == 1) {
                        result.setStatus(1);
                        result.setMsg("绑定成功");
                    } else {
                        result.setStatus(0);
                        result.setMsg("绑定失败");
                    }
                }
            } else {
                Map<String, Object> goodResult = shopSupDao.queryGoodByBarcode(params);
                if (goodResult != null && !goodResult.isEmpty()) {
                    Map<String, Object> supGoodParams = new HashMap<>();
                    BeanUtil.copyProperties(goodResult, supGoodParams);
                    supGoodParams.put("supplier_unique", params.get("supplierUnique"));
                    supGoodParams.put("expiration_date", goodResult.get("goods_life"));
                    supGoodParams.put("goods_image_url", goodResult.get("goods_picturepath"));
                    supGoodParams.put("create_id", params.get("createId"));
                    supGoodParams.put("create_by", params.get("createBy"));
                    int i = shopSupDao.addSupGood(supGoodParams);
                    Map<String, Object> shopSupSupplierExamineEntityResult = shopSupDao.queryShopSupSupplierExamineEntity(params);
                    if (shopSupSupplierExamineEntityResult != null) {
                        CustomerGoodsBingParams customerGoodsBingParams = new CustomerGoodsBingParams();
                        customerGoodsBingParams.setSupplierUnique(String.valueOf(params.get("supplierUnique")));
                        customerGoodsBingParams.setCustomerUnique(String.valueOf(params.get("shopUnique")));
                        customerGoodsBingParams.setGoodsBarcode(String.valueOf(goodResult.get("goods_barcode")));
                        customerGoodsBingParams.setGoodsName(String.valueOf(goodResult.get("goods_name")));
                        customerGoodsBingParams.setGoodsUnit(String.valueOf(goodResult.get("goods_unit")));
                        customerGoodsBingParams.setExpirationDate(Integer.valueOf(String.valueOf(goodResult.get("goods_life"))));
                        customerGoodsBingParams.setGoodsImageUrl(String.valueOf(goodResult.get("goods_picturepath")));
                        customerGoodsBingParams.setGoodsSalePrice(new BigDecimal(String.valueOf(goodResult.get("goods_sale_price"))));
                        String data = JSONUtil.toJsonStr(customerGoodsBingParams);
                        PurResult result1 = httpPost(URL + "/external/goods/bingGoods", data);
                        if (result1.getStatus() != 1) {
                            result.setStatus(0);
                            result.setMsg(result1.getMsg());
                            throw new RuntimeException(result1.getMsg());
                        }
                    }
                    if (i == 1) {
                        result.setStatus(1);
                        result.setMsg("绑定成功");
                    } else {
                        result.setStatus(0);
                        result.setMsg("绑定失败");
                    }
                } else {
                    result.setStatus(0);
                    result.setMsg("该商品信息不存在");
                }
            }

            //更换相同商品不同规格对应供货商
            GoodsEntity goodsEntity = goodsDao.queryOneByParam(params);
            Map<String, Object> foreignMap = new HashMap<>();
            foreignMap.put("shopUnique", params.get("shopUnique"));
            Set<Long> foreignKeySet = new HashSet<>(1024);
            foreignKeySet.add(goodsEntity.getForeignKey());
            foreignMap.put("foreignKeyList", foreignKeySet);
            List<GoodsEntity> goodsEntities = goodsDao.queryGoodsByParam(foreignMap);
            if (goodsEntities != null) {
                for (GoodsEntity goods1 :
                        goodsEntities) {
                    if (!Objects.equals(goods1.getGoodsBarcode(), params.get("goodsBarcode"))) {
                        bindGoodList.add(String.valueOf(goods1.getGoodsBarcode()));
                        Map<String, Object> goodsForeignMap = new HashMap<>();
                        goodsForeignMap.put("shopUnique", params.get("shopUnique"));
                        goodsForeignMap.put("goodsBarcode", goods1.getGoodsBarcode());
                        ShopSupGoodsEntity goodsForeignEntity = shopSupDao.queryShopSupGoodsByGoodsBarcode(goodsForeignMap);
                        if (goodsForeignEntity != null) {
                            if (goodsForeignEntity.getRecordFlag() != 1) {
                                result.setStatus(0);
                                result.setMsg("不同规格商品【" + goods1.getGoodsName() + "】未建档，不能更换供货商");
                                throw new RuntimeException(result.getMsg());
                            }
                            if (!Objects.equals(String.valueOf(goodsForeignEntity.getSupplierUnique()), String.valueOf(params.get("supplierUnique")))) {
                                Map<String, Object> updateSupplierMap = new HashMap<>();
                                updateSupplierMap.put("supplierUnique", params.get("supplierUnique"));
                                updateSupplierMap.put("shopUnique", params.get("shopUnique"));
                                updateSupplierMap.put("goodsBarcode", goods1.getGoodsBarcode());
                                shopSupDao.updateSupplier(updateSupplierMap);
                                Map<String, Object> shopSupSupplierExamineEntityResult = shopSupDao.queryShopSupSupplierExamineEntity(params);
                                if (shopSupSupplierExamineEntityResult != null) {
                                    CustomerGoodsBingParams customerGoodsBingParams = new CustomerGoodsBingParams();
                                    customerGoodsBingParams.setSupplierUnique(String.valueOf(params.get("supplierUnique")));
                                    customerGoodsBingParams.setCustomerUnique(String.valueOf(params.get("shopUnique")));
                                    if (goodsForeignEntity.getSupplierUnique() != null) {
                                        Map<String, Object> oldMap = new HashMap<>();
                                        oldMap.put("shopUnique", params.get("shopUnique"));
                                        oldMap.put("supplierUnique", goodsForeignEntity.getSupplierUnique());
                                        Map<String, Object> oldShopSupSupplierExamineEntityResult = shopSupDao.queryShopSupSupplierExamineEntity(oldMap);
                                        if (oldShopSupSupplierExamineEntityResult != null) {
                                            customerGoodsBingParams.setOldSupplierUnique(goodsForeignEntity.getSupplierUnique());
                                        }
                                    }
                                    customerGoodsBingParams.setGoodsBarcode(goodsForeignEntity.getGoodsBarcode());
                                    customerGoodsBingParams.setGoodsName(goodsForeignEntity.getGoodsName());
                                    customerGoodsBingParams.setGoodsUnit(goodsForeignEntity.getGoodsUnit());
                                    customerGoodsBingParams.setExpirationDate(goodsForeignEntity.getExpirationDate());
                                    customerGoodsBingParams.setGoodsImageUrl(goodsForeignEntity.getGoodsImageUrl());
                                    customerGoodsBingParams.setGoodsSalePrice(goodsForeignEntity.getGoodsSalePrice());
                                    String data = JSONUtil.toJsonStr(customerGoodsBingParams);
                                    PurResult result1 = httpPost(URL + "/external/goods/bingGoods", data);
                                    if (result1.getStatus() != 1) {
                                        result.setStatus(0);
                                        result.setMsg(result1.getMsg());
                                        throw new RuntimeException(result1.getMsg());
                                    }
                                } else {
                                    if (goodsForeignEntity.getSupplierUnique() != null && !goodsForeignEntity.getSupplierUnique().isEmpty()) {
                                        CustomerGoodsDeleteParams customerGoodsDeleteParams = new CustomerGoodsDeleteParams();
                                        customerGoodsDeleteParams.setSupplierUnique(goodsForeignEntity.getSupplierUnique());
                                        customerGoodsDeleteParams.setCustomerUnique(String.valueOf(params.get("shopUnique")));
                                        customerGoodsDeleteParams.setGoodsBarcode(goodsForeignEntity.getGoodsBarcode());
                                        String data = JSONUtil.toJsonStr(Collections.singletonList(customerGoodsDeleteParams));
                                        PurResult result1 = httpPost(URL + "/external/goods/deleteBingGoods", data);
                                        if (result1.getStatus() != 1) {
                                            result.setStatus(0);
                                            result.setMsg(result1.getMsg());
                                            throw new RuntimeException(result1.getMsg());
                                        }
                                    }
                                }
                                result.setStatus(1);
                                result.setMsg("更改供应商成功！");
                            }
                        } else {
                            Map<String, Object> foreignParams = new HashMap<>();
                            foreignParams.put("supplierUnique", params.get("supplierUnique"));
                            foreignParams.put("shopUnique", params.get("shopUnique"));
                            foreignParams.put("goodsBarcode", goods1.getGoodsBarcode());
                            foreignParams.put("create_id", params.get("createId"));
                            foreignParams.put("create_by", params.get("createBy"));
                            Map<String, Object> goodResult = shopSupDao.queryGoodByBarcode(foreignParams);
                            if (goodResult != null && !goodResult.isEmpty()) {
                                Map<String, Object> supGoodParams = new HashMap<>();
                                BeanUtil.copyProperties(goodResult, supGoodParams);
                                supGoodParams.put("supplier_unique", foreignParams.get("supplierUnique"));
                                supGoodParams.put("expiration_date", goodResult.get("goods_life"));
                                supGoodParams.put("goods_image_url", goodResult.get("goods_picturepath"));
                                supGoodParams.put("create_id", foreignParams.get("createId"));
                                supGoodParams.put("create_by", foreignParams.get("createBy"));
                                int i = shopSupDao.addSupGood(supGoodParams);
                                Map<String, Object> shopSupSupplierExamineEntityResult = shopSupDao.queryShopSupSupplierExamineEntity(foreignParams);
                                if (shopSupSupplierExamineEntityResult != null) {
                                    CustomerGoodsBingParams customerGoodsBingParams = new CustomerGoodsBingParams();
                                    customerGoodsBingParams.setSupplierUnique(String.valueOf(foreignParams.get("supplierUnique")));
                                    customerGoodsBingParams.setCustomerUnique(String.valueOf(foreignParams.get("shopUnique")));
                                    customerGoodsBingParams.setGoodsBarcode(String.valueOf(goodResult.get("goods_barcode")));
                                    customerGoodsBingParams.setGoodsName(String.valueOf(goodResult.get("goods_name")));
                                    customerGoodsBingParams.setGoodsUnit(String.valueOf(goodResult.get("goods_unit")));
                                    customerGoodsBingParams.setExpirationDate(Integer.valueOf(String.valueOf(goodResult.get("goods_life"))));
                                    customerGoodsBingParams.setGoodsImageUrl(String.valueOf(goodResult.get("goods_picturepath")));
                                    customerGoodsBingParams.setGoodsSalePrice(new BigDecimal(String.valueOf(goodResult.get("goods_sale_price"))));
                                    String data = JSONUtil.toJsonStr(customerGoodsBingParams);
                                    PurResult result1 = httpPost(URL + "/external/goods/bingGoods", data);
                                    if (result1.getStatus() != 1) {
                                        result.setStatus(0);
                                        result.setMsg(result1.getMsg());
                                        throw new RuntimeException(result1.getMsg());
                                    }
                                }
                                if (i == 1) {
                                    result.setStatus(1);
                                    result.setMsg("绑定成功");
                                } else {
                                    result.setStatus(0);
                                    result.setMsg("绑定失败");
                                }
                            }
                        }
                    }
                }
            }
        } catch (Exception e) {
            result.setStatus(0);
            result.setMsg("异常");
            throw new RuntimeException("绑定失败");
        }
        return result;
    }

    @Override
    public PurResult batchAddSupGoods(Map<String, Object> params) {
        PurResult result = new PurResult();
        try {
            String checkMsg = checkBatchAddSupGoodsParams(params);
            if (checkMsg != null) {
                result.setStatus(0);
                result.setMsg(checkMsg);
                return result;
            }
            String[] goodsList = (String[]) params.get("goodsList");
            List<String> bindGoodList = new ArrayList<>();
            Map<String, Object> addSupGoodParams = new HashMap<>();
            BeanUtil.copyProperties(params, addSupGoodParams);
            addSupGoodParams.remove("goodsList");
            for (String goodsBarcode : goodsList) {
                if (!bindGoodList.contains(goodsBarcode)) {
                    addSupGoodParams.put("goodsBarcode", goodsBarcode);
                    PurResult result1 = addSupGood(addSupGoodParams, bindGoodList);
                    if (result1.getStatus() == 0) {
                        result.setStatus(0);
                        result.setMsg("绑定失败");
                        throw new RuntimeException("绑定失败");
                    }
                }
            }
            result.setStatus(1);
            result.setMsg("成功");
        } catch (Exception e) {
            result.setStatus(0);
            result.setMsg("绑定失败");
            throw new RuntimeException("绑定失败");
        }
        return result;
    }

    public PurResult httpPost(String url, String data) {
        PurResult result = new PurResult();
        System.out.println("-------------------------同步, 地址: {" + url + "},参数: {" + data + "}---------------");
        String result1 = HttpUtil.post(url, data);
        System.out.println("-------------------------同步结果: {" + result1 + "}---------------");
        JSONObject jo1 = JSONUtil.parseObj(result1);
        if (jo1.get("status") == null) {
            result.setStatus(0);
            result.setMsg("访问同步信息接口超时");
        } else if ("1".equals(String.valueOf(jo1.get("status")))) {
            result.setStatus(1);
            result.setMsg(String.valueOf(jo1.get("message")));
        } else {
            result.setStatus(0);
            result.setMsg(String.valueOf(jo1.get("message")));
        }
        return result;
    }

    private String checkAddSupGoodsParams(Map<String, Object> params) {
        StringBuilder str = new StringBuilder();
        if (params.get("shopUnique") == null || params.get("shopUnique") == "") {
            str.append("店铺编号不能为空；");
        }
        if (params.get("goodsBarcode") == null || params.get("goodsBarcode") == "") {
            str.append("商品条码不能为空；");
        }
        if (params.get("supplierUnique") == null || params.get("supplierUnique") == "") {
            str.append("供货商编号不能为空；");
        }
        if (params.get("createId") == null || params.get("createId") == "") {
            str.append("操作人ID不能为空；");
        }
        if (params.get("createBy") == null || params.get("createBy") == "") {
            str.append("操作人姓名不能为空；");
        }
        if (str.length() > 0) {
            return str.toString();
        } else {
            return null;
        }
    }

    private String checkBatchAddSupGoodsParams(Map<String, Object> params) {
        StringBuilder str = new StringBuilder();
        if (params.get("shopUnique") == null || params.get("shopUnique") == "") {
            str.append("店铺编号不能为空；");
        }
        if (params.get("goodsList") == null || params.get("goodsList") == "") {
            str.append("商品条码不能为空；");
        } else {
            String[] goodsList = (String[]) params.get("goodsList");
            if (goodsList.length < 1) {
                str.append("商品条码不能为空；");
            }
        }
        if (params.get("supplierUnique") == null || params.get("supplierUnique") == "") {
            str.append("供货商编号不能为空；");
        }
        if (params.get("createId") == null || params.get("createId") == "") {
            str.append("操作人ID不能为空；");
        }
        if (params.get("createBy") == null || params.get("createBy") == "") {
            str.append("操作人姓名不能为空；");
        }
        if (str.length() > 0) {
            return str.toString();
        } else {
            return null;
        }
    }

}
