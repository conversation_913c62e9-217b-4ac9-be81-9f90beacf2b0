package org.haier.shop.service.supplier;

import org.haier.shop.params.shopSupBill.*;
import org.haier.shop.params.shopSupBill.externalCall.*;
import org.haier.shop.params.shopSupBill.supExternalCall.StorageAllGoodsParams;
import org.haier.shop.util.ShopsResult;

public interface ShopSupBillService {

	ShopsResult querySupBillList(QueryBillListParams params);
	ShopsResult querySupBillGoodsList(QueryBillGoodsListParams params);
	ShopsResult storageAllGoods(StorageAllGoodsParams params);
	ShopsResult storageGoods(StorageGoodsParams params);
	ShopsResult addPaymentOrder(AddPaymentOrderParams params);
	ShopsResult modifyPaymentOrder(ModifyPaymentOrderParams params);
	ShopsResult cancelStorageGoods(CancelStorageGoodsParams params);
	ShopsResult addShopSupBill(BillInfoParams params);
	ShopsResult updateBillStatus(UpdateBillStatusParams params);
	ShopsResult updateSupBillStatus(UpdateSupBillStatusParams params);
	ShopsResult queryPayment(QueryBillGoodsListParams params);
	ShopsResult checkGoods(CheckGoodsParams params);
	ShopsResult cancelSupBill(CancelSupBillParams params);
	ShopsResult cancelCheckGoods(CancelCheckGoodsParams params);

}
