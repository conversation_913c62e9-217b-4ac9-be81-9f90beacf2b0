package org.haier.shop.service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;

import org.haier.shop.dao.RotationDao;
import org.haier.shop.util.ShopsResult;
import org.springframework.stereotype.Service;

@Service
public class RotationServiceImpl implements RotationService{
	
	@Resource
	private RotationDao rotationDao;
	
	/**
	 * 更新或添加或删除轮播图
	 * @param img_url 图片保存地址
	 * @param brand_id 品牌ID
	 * @param img_id 原图片ID，更新和删除时用到
	 * @param img_type 图片类型
	 * @param remarks 备注信息
	 * @param valid_status 状态：1、正常；0、已删除；
	 * @return
	 */
	public ShopsResult addNewRotationImg(String img_url,Integer brand_id,Integer img_id,Integer img_type,String remarks,Integer valid_status) {
		ShopsResult sr = new ShopsResult(1, "添加成功!");
		
		
		Map<String,Object> map = new HashMap<String,Object>();
		map.put("img_id", img_id);
		map.put("valid_status", valid_status);
		
		if(valid_status != null && valid_status == 0) {
			rotationDao.deleteRotationImg(map);
			sr.setStatus(1);
			sr.setMsg("删除成功!");
			return sr;
		}
		
		map.put("img_url", img_url);
		map.put("img_type", img_type);
		map.put("brand_id", brand_id);
		map.put("remarks", remarks);
		
		if(img_id != null) {
			rotationDao.updateRotationImg(map);
		}else {
			rotationDao.addNewRotationImg(map);
		}
		
		return sr;
	}
	
	
	/**
	 * 查询加油站品牌列表
	 * @return
	 */
	public List<Map<String,Object>> queryBrandList(){
		return rotationDao.queryBrandList();
	}
	/**
	 * 查询轮播图信息
	 * @param brandId 品牌ID
	 * @param imgType 图片类型
	 * @param page 页码
	 * @param limit 单页查询数量
	 * @return
	 */
	@Override
	public ShopsResult queryRotationList(Integer brandId,Integer imgType,Integer page,Integer limit) {
		ShopsResult sr = new ShopsResult(1, "查询成功!");
		
		Map<String,Object> map = new HashMap<String,Object>();
		map.put("startNum", (page-1)*limit);
		map.put("pageSize", limit);
		
		map.put("imgType", imgType);
		map.put("brandId", brandId);
		
		List<Map<String,Object>> list = rotationDao.queryRotationList(map);
		
		sr.setData(list);
		sr.setCount(rotationDao.queryRotationListCount(map));
		
		return sr;
	}
}
