package org.haier.shop.service;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.bouncycastle.jcajce.provider.asymmetric.dsa.DSASigner.detDSA;
import org.haier.shop.dao.GasOilDao;
import org.haier.shop.oil.Coupon;
import org.haier.shop.oil.Goods;
import org.haier.shop.oil.RechargeDetail;
import org.haier.shop.util.PurResult;
import org.haier.shop.util.ShopsResult;
import org.haier.shop.util.UtilForJAVA;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.sun.org.apache.bcel.internal.generic.NEW;

//import jdk.nashorn.internal.runtime.GlobalConstants;

@Service
public class GasOilServiceImpl implements GasOilService{

	@Autowired
	private GasOilDao gasOilDao;
	
	/**
	 * 查询站点
	 */
	@Override
	public PurResult queryManagerOils(Map<String, Object> map) {
		PurResult result = new PurResult();
		try {
			List<Map<String, Object>> oilsList = gasOilDao.queryManagerOils(map);
			
			result.setData(oilsList);
			result.setStatus(1);
			result.setMsg("查询成功！");
		} catch (Exception e) {
			// TODO: handle exception
			e.printStackTrace();
		}
		return result;
	}
	
	/**
	 * 查询油枪油品商品列表
	 */
	@Override
	public PurResult queryManagerOilgun(Map<String, Object> map) {
		PurResult result = new PurResult();
		
		try {
//			List<Map<String, Object>> oilGunList = gasOilDao.queryManagerOilgun(map);
			List<Map<String, Object>> oilList = gasOilDao.queryShopsOilList(map);
			result.setData(oilList);
			result.setStatus(1);
			result.setMsg("查询成功！");
		} catch (Exception e) {
			// TODO: handle exception
			e.printStackTrace();
		}
		return result;
	}
	
	
	/**
	 * 查询加油站订单列表
	 */
	@Override
	public PurResult queryOilSaleList(Map<String, Object> map) {
		PurResult result = new PurResult();
		try {
			
			System.out.println(map);
			//订单列表
			map.put("page",(Integer.valueOf(map.get("page").toString())-1)*Integer.parseInt(map.get("limit").toString()));
			List<Map<String, Object>> gasOilSaleList = gasOilDao.queryOilSaleList(map);
			
			//订单数量
			int count = gasOilDao.queryOilSaleListCount(map);

			result.setData(gasOilSaleList);
			result.setCount(count);
			result.setStatus(1);
			result.setMsg("查询成功！");
			Map<String, Object> map2 = new HashMap<>();
			map2.put("orderCount", count);//订单总数量
			String tot = gasOilDao.queryOilsaleTotal(map);
			if(tot == null) {
				tot = "0.00";
			}
			Double saleTotal = Double.valueOf(tot);
			System.out.println(saleTotal);
			map2.put("saleTotal", saleTotal);//订单总金额
			Double cus = saleTotal/count;
			if(cus > 0) {
			}else {
				cus = 0.00;
			}
			map2.put("customerPrice",cus);//客单价
			
			List<String> customerFlow = gasOilDao.queryOilcustomerFlow(map);;
			map2.put("customerFlow", customerFlow.size());//客流量
			
			result.setCord(map2);
			
		} catch (Exception e) {
			// TODO: handle exception
			e.printStackTrace();
			result.setData(null);
			result.setCount(0);
			result.setStatus(0);
			result.setMsg("查询失败！");
		}
		return result;
	}
	
	
	/**
	 * 查询订单详情
	 * @param shop_unique
	 * @param sale_list_unique
	 * @return
	 */
	public PurResult queryGasOrderDetail(String sale_list_unique,String shop_unique){
		PurResult shop=new PurResult();
		Map<String,Object> map=new HashMap<String,Object>();
		map.put("shop_unique", shop_unique);
		map.put("sale_list_unique", sale_list_unique);
		System.out.println(shop_unique);
		Map<String, Object> detail=gasOilDao.queryGasOrderDetail(map);
		if(detail==null){
			shop.setStatus(1);
			shop.setMsg("订单编号错误，请确认！");
			return shop;
		}
		shop.setStatus(0);
		shop.setMsg("订单详情查询成功！");
		shop.setData(detail);
		return shop;
	}
	
	/**
	 * 查询加油站订单列表
	 */
	@Override
	public List<Map<String, Object>> saleListExcel(Map<String, Object> map) {
		System.out.println(map);
		//订单列表
		List<Map<String, Object>> gasOilSaleList = gasOilDao.queryOilSaleList(map);

		return gasOilSaleList;
	}
	
	
	/**
	 * 查询加油站充值列表
	 */
	@Override
	public PurResult queryGasRechargeList(Map<String, Object> map) {
		PurResult result = new PurResult();
		try {
			
			System.out.println(map);
			//订单列表
			map.put("page",Integer.valueOf(map.get("page").toString())-1);
			List<RechargeDetail> gasOilSaleList = gasOilDao.queryGasRechargeList(map);
			
			//订单数量
			int count = gasOilDao.queryGasRechargeListCount(map);

			result.setData(gasOilSaleList);
			result.setCount(count);
			result.setStatus(1);
			result.setMsg("查询成功！");
			
		} catch (Exception e) {
			// TODO: handle exception
			e.printStackTrace();
			result.setData(null);
			result.setCount(0);
			result.setStatus(0);
			result.setMsg("查询失败！");
		}
		return result;
	}
	
	
	/**
	 * 查询充值详情
	 * @param shop_unique
	 * @param sale_list_unique
	 * @return
	 */
	public PurResult queryGasRechargeDetail(String sale_list_unique){
		PurResult shop=new PurResult(1,"查询成功!");
		if(null == sale_list_unique || sale_list_unique.equals("")) {
			shop.setStatus(0);
			shop.setMsg("请输入订单编号!");
			return shop;
		}
		Map<String,Object> map=new HashMap<String,Object>();
		map.put("sale_list_unique", sale_list_unique);
		RechargeDetail detail=gasOilDao.queryGasRechargeDetail(map);
		if(detail==null){
			shop.setStatus(0);
			shop.setMsg("订单编号错误，请确认！");
			return shop;
		}
		shop.setStatus(1);
		shop.setMsg("订单详情查询成功！");
		shop.setData(detail);
		return shop;
	}

	@Override
	public PurResult queryCouponName(Map<String, Object> params) {
		PurResult result = new PurResult();
		try {
			System.out.println(params);
			//订单列表
			List<Map<String, Object>> couponNameList = gasOilDao.queryCouponName(params);
			result.setData(couponNameList);
			result.setStatus(1);
			result.setMsg("查询成功！");
		} catch (Exception e) {
			// TODO: handle exception
			e.printStackTrace();
			result.setData(null);
			result.setCount(0);
			result.setStatus(0);
			result.setMsg("查询失败！");
		}
		return result;
	}

	@Override
	public PurResult queryCouponCount(Map<String, Object> params) {
		PurResult result = new PurResult();
		try {
			System.out.println(params);
			//订单列表
			List<Map<String, Object>> couponCountList = gasOilDao.queryCouponCount(params);
			result.setData(couponCountList);
			result.setStatus(1);
			result.setMsg("查询成功！");
		} catch (Exception e) {
			// TODO: handle exception
			e.printStackTrace();
			result.setData(null);
			result.setCount(0);
			result.setStatus(0);
			result.setMsg("查询失败！");
		}
		return result;
	}

	@Override
	public PurResult queryGoodsName(Map<String, Object> params) {
		PurResult result = new PurResult();
		try {
			System.out.println(params);
			//订单列表
			List<Map<String, Object>> goodsNameList = gasOilDao.queryGoodsName(params);
			result.setData(goodsNameList);
			result.setStatus(1);
			result.setMsg("查询成功！");
		} catch (Exception e) {
			// TODO: handle exception
			e.printStackTrace();
			result.setData(null);
			result.setCount(0);
			result.setStatus(0);
			result.setMsg("查询失败！");
		}
		return result;
	}

	@Override
	public PurResult queryGoodsCount(Map<String, Object> params) {
		PurResult result = new PurResult();
		try {
			System.out.println(params);
			//订单列表
			List<Map<String, Object>> goodsCountList = gasOilDao.queryGoodsCount(params);
			result.setData(goodsCountList);
			result.setStatus(1);
			result.setMsg("查询成功！");
		} catch (Exception e) {
			// TODO: handle exception
			e.printStackTrace();
			result.setData(null);
			result.setCount(0);
			result.setStatus(0);
			result.setMsg("查询失败！");
		}
		return result;
	}

	@Override
	public PurResult queryAddBalance(Map<String, Object> params) {
		PurResult result = new PurResult();
		try {
			System.out.println(params);
			//订单列表
			List<Map<String, Object>> addBalanceList = gasOilDao.queryAddBalance(params);
			result.setData(addBalanceList);
			result.setStatus(1);
			result.setMsg("查询成功！");
		} catch (Exception e) {
			// TODO: handle exception
			e.printStackTrace();
			result.setData(null);
			result.setCount(0);
			result.setStatus(0);
			result.setMsg("查询失败！");
		}
		return result;
	}

	@Override
	public PurResult queryCusLevelName(Map<String, Object> params) {
		PurResult result = new PurResult();
		try {
			System.out.println(params);
			//订单列表
			List<Map<String, Object>> cusLevelNameList = gasOilDao.queryCusLevelName(params);
			result.setData(cusLevelNameList);
			result.setStatus(1);
			result.setMsg("查询成功！");
		} catch (Exception e) {
			// TODO: handle exception
			e.printStackTrace();
			result.setData(null);
			result.setCount(0);
			result.setStatus(0);
			result.setMsg("查询失败！");
		}
		return result;
	}

	@Override
	public List<Map<String, Object>> rechargeListExcel(Map<String, Object> params) {
		List<RechargeDetail> gasOilSaleList = gasOilDao.queryGasRechargeList(params);
		List<Map<String,Object>> list = new ArrayList<Map<String,Object>>();
		
		if(null != gasOilSaleList && !gasOilSaleList.isEmpty()) {
			for(Integer i = 0; i < gasOilSaleList.size(); i++) {
				Map<String,Object> tm = new HashMap<String,Object>();
				RechargeDetail rd = gasOilSaleList.get(i);
				tm.put("recharge_id", rd.getRechargeId());
				tm.put("cus_id", rd.getCusId());
				tm.put("sale_list_unique", rd.getSaleListUnique());
				tm.put("recharge_datetime", rd.getRechargeDatetime());
				tm.put("recharge_money", rd.getRechargeMoney());
				tm.put("recharge_name", rd.getRechargeName());
				tm.put("add_point", rd.getAddPoint());
				tm.put("add_balance", rd.getAddBalance());
				tm.put("cus_level_name", rd.getCusLevelName());
				tm.put("goods_name", "");
				tm.put("goods_count", 0);
				tm.put("coupon_name", "");
				tm.put("coupon_count", 0);
				
				if(null != rd.getGoodsList()) {
					List<Goods> goodsList = rd.getGoodsList();
					String goodsName = "";
					String goodsCount = "";
					for(Integer j = 0; j < goodsList.size(); j++) {
						if(j == 0) {
							goodsName += goodsList.get(j).getGoodsName();
							goodsCount += goodsList.get(j).getGoodsCount();
						}else {
							goodsName += "/" + goodsList.get(j).getGoodsName();
							goodsCount += "/" + goodsList.get(j).getGoodsCount();
						}
					}
					tm.put("goods_name", goodsName);
					tm.put("goods_count", goodsCount);
				}
				
				if(null != rd.getCouponList()) {
					List<Coupon> couponList = rd.getCouponList();
					String couponName = "";
					String couponCount = "";
					for(Integer j = 0; j < couponList.size(); j++) {
						if(j == 0) {
							couponName += couponList.get(j).getCouponName();
							couponCount += couponList.get(j).getCouponCount();
						}else {
							couponName += "/" + couponList.get(j).getCouponName();
							couponCount +="/" + couponList.get(j).getCouponCount();
						}
					}
					tm.put("coupon_name", couponName);
					tm.put("coupon_count", couponCount);
				}
				list.add(tm);
			}
		}
		
		return list;
	}
}
