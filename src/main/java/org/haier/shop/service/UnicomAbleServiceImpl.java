package org.haier.shop.service;

import java.util.List;
import java.util.Map;

import javax.annotation.Resource;
import org.haier.shop.dao.UnicomAbleDao;
import org.haier.shop.util.MUtil;
import org.haier.shop.util.PurResult;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Service("UnicomAbleService")
public class UnicomAbleServiceImpl implements UnicomAbleService{

	@Resource
	UnicomAbleDao unicomAbleDao;

	@Override
	public PurResult queryUnicomAbleList(Map<String, Object> params) {
		PurResult result = new PurResult();
		try {
			List<Map<String ,Object>> list = unicomAbleDao.queryUnicomAbleList(params);
			Integer count = unicomAbleDao.queryUnicomAbleListCount(params);
			result.setData(list);
			result.setCount(count);
			result.setStatus(1);
			result.setMsg("成功！");
		} catch (Exception e) {
			e.printStackTrace();
			result.setStatus(0);
			result.setMsg("系统异常");
		}
		return result;
	}


	@Override
	@Transactional
	public PurResult addUnicomAble(Map<String, Object> params) {
		PurResult result = new PurResult();
		try {
			params.put("able_num", MUtil.genTimeStamp());
			unicomAbleDao.addUnicomAble(params);
			result.setStatus(1);
			result.setMsg("成功！");
		} catch (Exception e) {
			e.printStackTrace();
			result.setStatus(0);
			result.setMsg("系统异常");
		}
		return result;
	}


	@Override
	public Map<String, Object> queryUnicomAbleDetail(Map<String, Object> params) {
		return unicomAbleDao.queryUnicomAbleDetail(params);
	}


	@Override
	@Transactional
	public PurResult updateUnicomAble(Map<String, Object> params) {
		PurResult result = new PurResult();
		try {
			unicomAbleDao.updateUnicomAble(params);
			result.setStatus(1);
			result.setMsg("成功！");
		} catch (Exception e) {
			e.printStackTrace();
			result.setStatus(0);
			result.setMsg("系统异常");
		}
		return result;
	}


	@Override
	@Transactional
	public PurResult deleteUnicomAble(String unicom_able_id) {
		PurResult result = new PurResult();
		try {
			unicomAbleDao.deleteUnicomAble(unicom_able_id);
			result.setStatus(1);
			result.setMsg("成功！");
		} catch (Exception e) {
			e.printStackTrace();
			result.setStatus(0);
			result.setMsg("系统异常");
		}
		return result;
	}


	@Override
	public PurResult queryUnicomAbleEsignList(Map<String, Object> params) {
		PurResult result = new PurResult();
		try {
			List<Map<String ,Object>> list = unicomAbleDao.queryUnicomAbleEsignList(params);
			Integer count = unicomAbleDao.queryUnicomAbleEsignListCount(params);
			result.setData(list);
			result.setCount(count);
			result.setStatus(1);
			result.setMsg("成功！");
		} catch (Exception e) {
			e.printStackTrace();
			result.setStatus(0);
			result.setMsg("系统异常");
		}
		return result;
	}
}
