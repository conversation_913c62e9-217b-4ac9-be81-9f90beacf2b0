package org.haier.shop.service;

import org.apache.commons.collections.MapUtils;
import org.haier.meituan.util.MUtil;
import org.haier.shop.dao.GoodsKindIconDao;
import org.haier.shop.dao.Goods_kindDao;
import org.haier.shop.dao.ImportDao;
import org.haier.shop.entity.AllKindsInShop;
import org.haier.shop.entity.GoodsGroups;
import org.haier.shop.redis.RedisCache;
import org.haier.shop.util.*;
import org.haier.shop.util.mqtt.MqttxUtil;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.ObjectUtils;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.io.BufferedReader;
import java.io.File;
import java.io.FileInputStream;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;

@Service("goodsKindIconService")
public class GoodsKindIconServiceImpl implements GoodsKindIconService {
    @Resource
    private GoodsKindIconDao goodsKindIconDao;
    @Resource
    private ImportDao importDao;

    @Override
    public ShopsResult addGoodsKindIcon(MultipartFile file, Integer shop_type, Integer icon_type, String shop_unique, String create_user, HttpServletRequest request) throws MyException {
        ShopsResult sr = new ShopsResult(1, "添加成功!");
        StringBuffer str = new StringBuffer();
        if (null == shop_type) {
            str.append("行业类型不能为空；");
        }
        if (null == icon_type || !new ArrayList<Integer>(Arrays.asList(1, 2)).contains(icon_type)) {
            str.append("图标类型不能为空；");
        } else if (icon_type == 2 && (null == shop_unique || "".equals(shop_unique))) {
            str.append("店铺信息不能为空；");
        }
        if (null == create_user || "".equals(create_user)) {
            str.append("创建人不能为空；");
        }
        if (str.length() > 0) {
            return new ShopsResult(0, str.toString());
        }
        try {
            String filePath = "";
            if (icon_type == 1) { //系统图标
                filePath = "system";
            } else if (icon_type == 2) { //本地图标
                filePath = shop_unique;
            }
            String iconPath = uploadImg(file, filePath, request);
            if (iconPath != null && !"".equals(iconPath)) {
                Map<String, Object> map = new HashMap<String, Object>();
                map.put("shop_type", shop_type);
                map.put("icon_type", icon_type);
                map.put("use_type", "0"); //默认未使用
                if(icon_type == 2){
                    map.put("shop_unique", shop_unique);
                }
                map.put("goods_kind_icon_picture", iconPath);
                map.put("create_user", create_user);
                String update_time = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
                map.put("create_date", update_time);
                goodsKindIconDao.addNewGoodsKindIcon(map);
            } else {
                return new ShopsResult(0, "上传图标失败!");
            }

        } catch (Exception e) {
            throw new MyException(0, "系统异常，请联系客服");
        }

        return sr;
    }

    @Override
    public ShopsResult queryListByIconType(Integer icon_type, String shop_unique) {
        ShopsResult sr = new ShopsResult(1, "查询成功！");
        if (icon_type != 2 && icon_type != 1) {
            return new ShopsResult(0, "图标类型不正确");
        }
        if (icon_type == 2 && (shop_unique == null || "".equals(shop_unique))) {
            return new ShopsResult(0, "店铺信息不能为空");
        }
        List<Map<String, Object>> list;
        try {
            Map<String, Object> map = new HashMap<String, Object>();
            map.put("url", Load.IMGDOMAINNAME);
            if (icon_type == 2) {
                map.put("shopUnique", shop_unique);
                map.put("iconType", icon_type);
                list = goodsKindIconDao.queryListByIconTypeAndShopUnique(map);
            } else {
                map.put("iconType", icon_type);
                list = goodsKindIconDao.queryListByIconType(map);
            }
            for (Map<String, Object> m : list) {
                String pic = (String) m.get("goods_kind_icon_picture");
                m.put("goods_kind_icon_picture",ObjectUtils.isEmpty(pic) ? "" : pic.replaceAll("\\\\","/"));
            }
            sr.setData(list);
        } catch (Exception e) {
            throw new MyException(0, "系统异常，请联系客服");
        }
        return sr;
    }

    public String uploadImg(MultipartFile file, String iconFilePath, HttpServletRequest request) {
        String iconPath = "";
        try {
            //获取服务器所在路径
            String absPath = this.getClass().getClassLoader().getResource("../../").getPath();
            String filePathDetail = File.separator + "image" + File.separator + "goods_kind" + File.separator + iconFilePath + File.separator;
            String filePath = absPath.substring(0, absPath.length() - request.getContextPath().length()) + filePathDetail;

            //将文件存储到本地，
            File floder = new File(filePath);
            if (!floder.exists()) {
                floder.mkdirs();
            }
            String goodsName = file.getOriginalFilename();
            String lastName = goodsName.substring(goodsName.lastIndexOf("."));

            UUID uuid = UUID.randomUUID();
            String newName = goodsName;

            //将文件保存到文件
            ShopsUtil.savePicture(file, filePath, goodsName, "1");
            //如果图片尺寸过大，压缩
            if (file.getSize() > 1024 * 1024) {
                newName = uuid + lastName;
                ShopsUtil.targetZoomOut(filePath + File.separator + goodsName, filePath + File.separator + newName, filePath);
            }

            //获取本地文件的文件流
            File tempFile = new File(filePath + newName);
            FileInputStream fis = new FileInputStream(tempFile);
            //将图片上传到文件
            ShopsUtil.ftpUpload("/" + "goods_kind" +"/" + iconFilePath, newName, fis);//存储到文件服务器
//            ShopsUtil.ftpUpload(File.separator + "goods_kind" + File.separator + iconFilePath, newName, fis);//存储到文件服务器

            iconPath = filePathDetail + newName;
            return iconPath;
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }

    }
}
