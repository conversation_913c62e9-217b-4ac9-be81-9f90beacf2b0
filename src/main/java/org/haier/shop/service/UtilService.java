package org.haier.shop.service;

import java.util.Map;

import javax.servlet.http.HttpServletRequest;

import org.haier.shop.util.PurResult;
import org.haier.shop.util.ShopsResult;

public interface UtilService {
	/**
	 * 查询支付文件列表
	 * @param startTime
	 * @param endTime
	 * @param page
	 * @param limit
	 * @return
	 */
	public ShopsResult queryPayimageList(String startTime , String endTime , Integer page, Integer limit);
	/**
	 * 上传交易凭证
	 * @param request
	 * @return
	 */
	public ShopsResult uploadImage(HttpServletRequest request);
	
	public ShopsResult addNewShopYN();
	
	public ShopsResult updateKindUnqualified();
	
	public ShopsResult addGoodsGoods(String shopUnique);
	
	public ShopsResult updateForeign(String shopUnique);
	
	public ShopsResult queryNoUseGoods(String shopUnique);
	
	public ShopsResult copyGoodsToNewShop(Map<String,Object> map);
	
	/**
	 * 更新用于更新PC收银端软件的新消息
	 * @return
	 */
	public ShopsResult updatePcFile();
	
	/**
	 * 历史版本信息查询
	 * @return
	 */
	public ShopsResult queryHistoricalVersion();
	/**
	 * 满足条件的商铺总数量查询
	 * @param map
	 * @return
	 */
	public ShopsResult queryShopsCount(Map<String,Object> map);
	
	/**
	 * 分页查询满足条件的信息
	 * @param map
	 * @return
	 */
	public PurResult queryShopsMessage(Map<String,Object> map);
	/**
	 * 批量设置更新状态
	 * @param map
	 * @return
	 */
	public ShopsResult modifyShopsVersion(Map<String,Object> map);
	
	/**
	 * 测试接口，在不同的服务器，获取文件MD5值不同
	 * @return
	 */
	public ShopsResult updatePcFileTest();
	
	public ShopsResult test();
	
	public ShopsResult modifyGoodsPic(String shopUnique);
	
	public ShopsResult addNewGoodsAll();
	
	public ShopsResult modifyNullList();
	
	public ShopsResult deleteListDetail();
	
	/**
	 * 添加自定义分类98001
	 * @return
	 */
	public ShopsResult addSelfKind();
	
	/**
	 * 修改会员拼音码信息
	 * @param map
	 * @return
	 */
	public ShopsResult updateCusAlias(Map<String,Object> map);
}
