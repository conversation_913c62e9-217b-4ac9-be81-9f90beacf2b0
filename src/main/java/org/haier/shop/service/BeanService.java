package org.haier.shop.service;

import java.util.List;
import java.util.Map;

import org.haier.shop.entity.ShopBeansVO;
import org.haier.shop.entity.TiCashVO;
import org.haier.shop.entity.beans.PageData;
import org.haier.shop.util.BeansRule;
import org.haier.shop.util.Page;
import org.haier.shop.util.PageQuery;
import org.haier.shop.util.PurResult;
import org.haier.shop.util.ShopsResult;

public interface BeanService {
	
	/**
	 * 获取百货豆信息
	 * @param pd
	 * @return
	 * @throws Exception
	 */
	public PageData getBeans(String shop_unique)throws Exception;
	
	public PageData getPtBeans(String shop_unique)throws Exception;
	
	public int getAgreement(String shop_unique)throws Exception;
	
	public int updateProtocol(PageData pd)throws Exception;
	
	/**
	 * 获取商家百货豆交易记录
	 * @param pd
	 * @return
	 * @throws Exception
	 */  
	
	public int getTransactionListCount(Page page)throws Exception;
	public List<PageData> getTransactionList(Page page)throws Exception;
	/**
	 * 获取会员百货豆交易记录
	 * @param pd
	 * @return
	 * @throws Exception
	 */
	public List<PageData> getCusList(Page page)throws Exception;
	
	public String weixinPay(String shop_unique, String productId,String spbill_create_ip,String beans_old_count,String payMoeny,String out_trade_no) throws Exception;

	public ShopsResult addBenasRule(PageData pd)throws Exception;
	
	public BeansRule queryBenasRule(PageData pd)throws Exception;
	
	public ShopsResult deleteBeansRule(PageData pd)throws Exception;
	
	public ShopsResult updateBeansRule(PageData pd)throws Exception;
	
	public  PageData queryBeansDiKu(PageData pd)throws Exception;
	
	public ShopsResult updateDikou(PageData pd)throws Exception;
	
	public ShopsResult addCash(PageData pd)throws Exception;
	
	public void addBeanOrder (PageData pd)throws Exception;
	
	public PageData findOneByTradeCode(String pd)throws Exception;
	
	public int payOrder(PageData pd)throws Exception;
	
	public ShopsResult queryTransactionList(PageQuery page);

	public PurResult queryTransactionList2(PageQuery page);
	
	public PurResult queryShopBeansPromation(PageQuery page);
	/**
	 * 查询店内所有用户消费所增加的百货豆订单数量
	 * @param pd
	 * @return
	 * @throws Exception 
	 */
	public Integer queryCusCount(PageData pd) throws Exception;
	/**
	 * 分页查询会员交易记录
	 * @param pageQuery
	 * @return
	 */
	public ShopsResult queryOrderListByPage(PageQuery pageQuery);
	
	public PurResult queryOrderListByPage2(PageQuery pageQuery);

	public ShopsResult queryPayBeans(PageData pd)throws Exception;

	public ShopsResult queryPtLi3(Map<String,Object> map)throws Exception;
	
	public ShopsResult queryCard(String shopUnique)throws Exception;
	
	public List<Map<String,Object>> queryBankName()throws Exception;
	
	public ShopsResult addbankCard(PageData pd)throws Exception;
	
	public ShopsResult updateBankCard(PageData pd)throws Exception;
	
	public ShopsResult deleteBankCard(PageData pd)throws Exception;
	
	public List<Map<String,Object>> queryBankCard(String shop_unique )throws Exception;
	
	public ShopBeansVO getShopBeans(String shop_unique )throws Exception;
	
	public ShopsResult addCardRecord(TiCashVO cashVO)throws Exception;
	
	public  PageData queryCashDetail(PageData pd)throws Exception;
	
	public List<PageData> queryCashList(PageData pd)throws Exception;
	
	public  ShopsResult updateCashOrder(PageData pd)throws Exception;

	public  ShopsResult updatePtRule(PageData pd)throws Exception;
	
	public  ShopsResult updateBoHuiCash(PageData pd)throws Exception;
	
	public  int queryTransactionCount( String shop_unique, int tx_dtae)throws Exception;
	
	public PurResult queryDrawCashList(Map<String,Object> map)throws Exception;
	
	public  PageData queryPtCashDetail(PageData pd)throws Exception;
	
	public  ShopsResult updateDrawCash(PageData pd)throws Exception;
	
	public  ShopsResult addDrawCash(PageData pd)throws Exception;
	
	public  int queryTransactionCountPt( String shop_unique, int tx_dtae)throws Exception;
	
	/**
	 * 查询店铺的百货豆赠送情况
	 * @param pageQuery
	 * @return
	 */
	public PurResult queryShopsBeansListByPage(PageQuery pageQuery);
	
	public  ShopsResult addShopBeanPromation(PageData pd);
	
	public  ShopsResult updateShopBeanPromationStatus(PageData pd);
}
