package org.haier.shop.service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;

import org.haier.shop.dao.SysDictDao;
import org.haier.shop.util.PurResult;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Service
@Transactional
public class DictServiceImpl implements DictService{
	@Resource
	private SysDictDao sysDictDao;
	

	@Override
	public PurResult queryDictList(Map<String, Object> params) {
		PurResult result=new PurResult();			
		List<Map<String,Object>> list=sysDictDao.queryDictList(params);
		result.setStatus(1);
		result.setMsg("查询成功");
		result.setCount(sysDictDao.queryDictListPageCount(params));
		result.setData(list);
		return result;
	}


	@Override
	public PurResult queryParentDictList(Map<String, Object> params) {
		PurResult result=new PurResult();			
		List<Map<String,Object>> list=sysDictDao.queryParentDictList(params);
		result.setStatus(1);
		result.setMsg("查询成功");
		result.setData(list);
		return result;
	}


	@Override
	public PurResult addDict(String area_dict_parent_num, String area_dict_content, String sort) {
		PurResult result=new PurResult();	
		Map<String, Object> params=new HashMap<String, Object>();
		params.put("area_dict_content", area_dict_content);
		params.put("area_dict_parent_num", area_dict_parent_num);
		params.put("area_dict_num", System.currentTimeMillis());
		if(sort!=null&&!"".equals(sort)){
			params.put("sort", sort);
		}
		sysDictDao.addDict(params);
		result.setStatus(1);
		return result;
	}


	@Override
	public PurResult deleteDict(String area_dict_id) {
		PurResult result=new PurResult();	
		Map<String, Object> params=new HashMap<String, Object>();
		params.put("area_dict_id", area_dict_id);
		sysDictDao.deleteDict(params);
		result.setStatus(1);
		return result;
	}


	@Override
	public PurResult queryDictById(String area_dict_id) {
		PurResult result=new PurResult();	
		Map<String, Object> params=new HashMap<String, Object>();
		params.put("area_dict_id", area_dict_id);
		Map<String, Object> data=sysDictDao.queryDictById(params);
		result.setStatus(1);
		result.setData(data);
		return result;
	}


	@Override
	public PurResult editDict(String area_dict_parent_num, String area_dict_content, String sort, String area_dict_id) {
		PurResult result=new PurResult();	
		Map<String, Object> params=new HashMap<String, Object>();
		params.put("area_dict_content", area_dict_content);
		params.put("area_dict_parent_num", area_dict_parent_num);
		params.put("area_dict_id", area_dict_id);
		if(sort!=null&&!"".equals(sort)){
			params.put("sort", sort);
		}
		sysDictDao.updateDict(params);
		result.setStatus(1);
		return result;
	}
	
	
}
