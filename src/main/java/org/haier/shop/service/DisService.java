package org.haier.shop.service;

import java.util.Map;

import org.haier.shop.util.PurResult;

public interface DisService {
	
	/**
	 * 查询店铺分销商列表
	 * @return
	 */
	public PurResult shopDisList(Map<String,Object> params);
	
	/**
	 * 查询会员列表
	 * @return
	 */
	public PurResult queryCustomerList(Map<String,Object> params);
	
	/**
	 * 新增分销商
	 * @return
	 */
	public PurResult addDisRelation(String cus_uniques,String shop_unique,String dis_level_id);
	
	/**
	 * 获取分销商详情
	 * @return
	 */
	public Map<String ,Object> queryDisRelation(String dis_relation_id);
	
	/**
	 * 分销商详情页面统计
	 * @return
	 */
	public PurResult shopDisDetailStatistics(Map<String ,Object> params);
	
	/**
	 * 编辑分销商
	 * @return
	 */
	public PurResult updateDisRelation(Map<String ,Object> params);
	
	/**
	 * 查询店铺分销商下级会员列表
	 * @return
	 */
	public PurResult shopDisLowerList(Map<String,Object> params);
	
	/**
	 * 查询店铺分销商账单明细列表
	 * @return
	 */
	public PurResult disTradingList(Map<String,Object> params);
	
	/**
	 * 查询店铺佣金明细列表
	 * @return
	 */
	public PurResult commissionList(Map<String,Object> params);
	
	/**
	 * 获取店铺佣金累计信息
	 * @return
	 */
	public Map<String ,Object> queryCommissionInfo(String shop_unique);
	
	/**
	 * 查询店铺佣金统计
	 * @return
	 */
	public PurResult commissionStatistics(Map<String,Object> params);
	
	/**
	 * 查询店铺分销订单列表
	 * @return
	 */
	public PurResult disOrderList(Map<String,Object> params);
	
	/**
	 * 获取分销订单详情
	 * @return
	 */
	public Map<String ,Object> queryDisOrderDetail(String sale_list_unique);
	
	/**
	 * 分销自动降级
	 * @return
	 */
	public void disLevelAutoDown();
	
	/**
	 * 查询分销商提现列表
	 * @return
	 */
	public PurResult disWithdList(Map<String,Object> params);
	
	/**
	 * 分销商确认提现
	 * @return
	 */
	public PurResult weixinTransfers(Map<String,Object> params);

	public PurResult queryShopDisChildCusList(Map<String, Object> map);

	public PurResult queryShopDisTradingAmountList(Map<String, Object> map);

	public PurResult queryDisGoodsList(Map<String, Object> map);

	public PurResult queryCusRelationList(Map<String, Object> map);

	public PurResult addSetCommissionRatio(Double commission_ratio, String shop_unique);

	public PurResult queryShopDisCashAmountList(Map<String, Object> map);
}
