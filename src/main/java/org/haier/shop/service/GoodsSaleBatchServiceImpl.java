package org.haier.shop.service;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import org.haier.shop.dao.GoodsBatchMapper;
import org.haier.shop.dao.GoodsSaleBatchMapper;
import org.haier.shop.entity.GoodsBatch;
import org.haier.shop.entity.GoodsSaleBatch;
import org.haier.shop.entity.ShopStockDetail;
import org.haier.shop.params.goods.*;
import org.haier.shop.params.goodsBatch.GoodsSaleBatchData;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @Description
 * @ClassName GoodsSaleBatchService
 * <AUTHOR>
 * @Date 2024/5/24 15:36
 **/
@Service
public class GoodsSaleBatchServiceImpl implements GoodsSaleBatchService {

    @Resource
    private GoodsBatchMapper goodsBatchMapper;
    @Resource
    private GoodsSaleBatchMapper goodsSaleBatchMapper;

    /**
     * 新增并审核通过处理批次信息
     * @param shopStockDetail
     * @param outStockParam
     * @return
     */
    @Override
    public Map<String, BigDecimal> addAndAuditBatch(ShopStockDetail shopStockDetail, OutStockParam outStockParam) {
        Map<String, BigDecimal> map = new HashMap<>();
        List<OutStockGoodsParam> goodsList = outStockParam.getGoodsList();
        List<OutStockGoodsBatchParam> selectGoodsBatchList = outStockParam.getGoodsBatchList();
        Map<Long, List<OutStockBatchParam>> selectGoodsBatchMap = selectGoodsBatchList.stream().collect(Collectors.toMap(OutStockGoodsBatchParam::getGoodsId, OutStockGoodsBatchParam::getBatchList));
        Map<String, GoodsBatch> goodsBatchMap = new HashMap<>();
        Map<String, GoodsSaleBatch> goodsSaleBatchMap = new HashMap<>();
        for (OutStockGoodsParam outStockGoodsParam : goodsList) {
            BigDecimal totalCount = BigDecimal.ZERO;
            BigDecimal totalMoney = BigDecimal.ZERO;
            List<GoodsBatch> hasGoodsBatchList = goodsBatchMapper.selectAvailableList(Long.parseLong(shopStockDetail.getShopUnique()), outStockGoodsParam.getForeignKey());
            List<OutStockBatchParam> goodsOutBachList = selectGoodsBatchMap.get(outStockGoodsParam.getGoodsId());
            BigDecimal waitOutCount = outStockGoodsParam.getGoodsCount();
            BigDecimal batchOutCount = BigDecimal.ZERO;
            if (CollectionUtil.isNotEmpty(goodsOutBachList)) {
                // 已选出库批次
                for (OutStockBatchParam stockBatchParam : goodsOutBachList) {
                    GoodsBatch goodsBatch = goodsBatchMap.get(stockBatchParam.getBatchUnique());
                    if (ObjectUtil.isNull(goodsBatch)) {
                        Optional<GoodsBatch> optional = hasGoodsBatchList.stream().filter(v -> ObjectUtil.equals(v.getBatchUnique(), stockBatchParam.getBatchUnique())).findFirst();
                        if (!optional.isPresent()) {
                            throw new RuntimeException("商品名称：" + outStockGoodsParam.getGoodsName() + "(" + outStockGoodsParam.getGoodsBarcode() + ") 对应出库批次：" + stockBatchParam.getBatchUnique() + " 库存不足");
                        }
                        goodsBatch = optional.get();
                    }
                    BigDecimal smallCount = NumberUtil.mul(outStockGoodsParam.getGoodsContain(), stockBatchParam.getGoodsCount());
                    if (goodsBatch.getGoodsCount().compareTo(smallCount) < 0) {
                        throw new RuntimeException("商品名称：" + outStockGoodsParam.getGoodsName() + "(" + outStockGoodsParam.getGoodsBarcode() + ") 对应出库批次：" + stockBatchParam.getBatchUnique() + " 库存不足");
                    }
                    goodsBatch.setGoodsCount(NumberUtil.sub(goodsBatch.getGoodsCount(), smallCount));
                    goodsBatch.setUpdateId(shopStockDetail.getUpdateId());
                    goodsBatch.setUpdateTime(shopStockDetail.getUpdateTime());
                    goodsBatchMap.put(goodsBatch.getGoodsBarcode(), goodsBatch);
                    batchOutCount = NumberUtil.add(batchOutCount, stockBatchParam.getGoodsCount());

                    GoodsSaleBatch goodsSaleBatch = goodsSaleBatchMap.get(outStockGoodsParam.getGoodsBarcode() + stockBatchParam.getBatchUnique());
                    if (ObjectUtil.isNull(goodsSaleBatch)) {
                        goodsSaleBatch = new GoodsSaleBatchData();
                        goodsSaleBatch.setShopUnique(goodsBatch.getShopUnique());
                        goodsSaleBatch.setStockListUnique(shopStockDetail.getListUnique());
                        goodsSaleBatch.setGoodsBarcode(outStockGoodsParam.getGoodsBarcode());
                        goodsSaleBatch.setGoodsInPrice(NumberUtil.mul(goodsBatch.getGoodsInPrice(), stockBatchParam.getGoodsCount()).setScale(2, RoundingMode.HALF_UP));
                        goodsSaleBatch.setGoodsOutPrice(outStockGoodsParam.getStockPrice());
                        goodsSaleBatch.setCreateId(shopStockDetail.getUpdateId());
                        goodsSaleBatch.setCreateTime(shopStockDetail.getUpdateTime());
                        goodsSaleBatch.setUpdateId(shopStockDetail.getUpdateId());
                        goodsSaleBatch.setBatchUnique(goodsBatch.getBatchUnique());
                        goodsSaleBatch.setUpdateTime(shopStockDetail.getUpdateTime());
                        goodsSaleBatch.setGoodsOutCount(stockBatchParam.getGoodsCount());
                    } else {
                        goodsSaleBatch.setGoodsOutCount(NumberUtil.add(goodsSaleBatch.getGoodsOutCount(), stockBatchParam.getGoodsCount()));
                    }
                    goodsSaleBatchMap.put(goodsSaleBatch.getGoodsBarcode() + goodsSaleBatch.getBatchUnique(), goodsSaleBatch);

                    totalCount = NumberUtil.add(totalCount, smallCount);
                    totalMoney = NumberUtil.add(totalMoney, NumberUtil.mul(smallCount, goodsBatch.getGoodsInPrice()));
                }
                if (waitOutCount.compareTo(batchOutCount) < 0) {
                    throw new RuntimeException("商品名称：" + outStockGoodsParam.getGoodsName() + "(" + outStockGoodsParam.getGoodsBarcode() + ") 实际出库数量不能小于批次出库总数量");
                }
                waitOutCount = NumberUtil.sub(waitOutCount, batchOutCount);
            }
            if (BigDecimal.ZERO.compareTo(waitOutCount) < 0) {
                for (GoodsBatch goodsBatch : hasGoodsBatchList) {
                    GoodsBatch hasGoodsBatch = goodsBatchMap.get(goodsBatch.getBatchUnique());
                    if (ObjectUtil.isNotNull(hasGoodsBatch)) {
                        goodsBatch = hasGoodsBatch;
                    }
                    BigDecimal bigCount = goodsBatch.getGoodsCount();
                    if (!StrUtil.equals(outStockGoodsParam.getGoodsBarcode(), outStockGoodsParam.getForeignKey())) {
                        bigCount = NumberUtil.div(goodsBatch.getGoodsCount(), outStockGoodsParam.getGoodsContain(), 0, RoundingMode.DOWN);
                    }
                    if (waitOutCount.compareTo(BigDecimal.ZERO) > 0) {
                        GoodsSaleBatch goodsSaleBatch = goodsSaleBatchMap.get(outStockGoodsParam.getGoodsBarcode()+goodsBatch.getBatchUnique());
                        if (ObjectUtil.isNull(goodsSaleBatch)) {
                            goodsSaleBatch = new GoodsSaleBatchData();
                            goodsSaleBatch.setShopUnique(goodsBatch.getShopUnique());
                            goodsSaleBatch.setStockListUnique(shopStockDetail.getListUnique());
                            goodsSaleBatch.setGoodsBarcode(outStockGoodsParam.getGoodsBarcode());
                            goodsSaleBatch.setGoodsInPrice(NumberUtil.mul(goodsBatch.getGoodsInPrice(), outStockGoodsParam.getGoodsContain()).setScale(2, RoundingMode.HALF_UP));
                            goodsSaleBatch.setGoodsOutPrice(outStockGoodsParam.getStockPrice());
                            goodsSaleBatch.setCreateId(shopStockDetail.getUpdateId());
                            goodsSaleBatch.setCreateTime(shopStockDetail.getUpdateTime());
                            goodsSaleBatch.setUpdateId(shopStockDetail.getUpdateId());
                            goodsSaleBatch.setBatchUnique(goodsBatch.getBatchUnique());
                            goodsSaleBatch.setUpdateTime(shopStockDetail.getUpdateTime());
                            goodsSaleBatch.setGoodsOutCount(BigDecimal.ZERO);
                        }
                        if (waitOutCount.compareTo(bigCount) > 0) {
                            goodsSaleBatch.setGoodsOutCount(NumberUtil.add(goodsSaleBatch.getGoodsOutCount(), bigCount));
                            waitOutCount = NumberUtil.sub(waitOutCount, bigCount);
                            goodsBatch.setGoodsCount(NumberUtil.sub(goodsBatch.getGoodsCount(), NumberUtil.mul(bigCount, outStockGoodsParam.getGoodsContain())));
                            goodsBatch.setUpdateTime(shopStockDetail.getUpdateTime());
                            goodsBatch.setUpdateId(shopStockDetail.getUpdateId());
                        } else {
                            goodsBatch.setGoodsCount(NumberUtil.sub(goodsBatch.getGoodsCount(), NumberUtil.mul(waitOutCount, outStockGoodsParam.getGoodsContain())));
                            goodsSaleBatch.setGoodsOutCount(NumberUtil.add(goodsSaleBatch.getGoodsOutCount(), waitOutCount));
                            waitOutCount = BigDecimal.ZERO;
                            goodsBatch.setUpdateTime(shopStockDetail.getUpdateTime());
                            goodsBatch.setUpdateId(shopStockDetail.getUpdateId());
                        }
                        goodsSaleBatchMap.put(goodsSaleBatch.getGoodsBarcode()+goodsSaleBatch.getBatchUnique(), goodsSaleBatch);
                        goodsBatchMap.put(goodsBatch.getBatchUnique(), goodsBatch);

                        totalCount = NumberUtil.add(totalCount, NumberUtil.mul(bigCount, outStockGoodsParam.getGoodsContain()));
                        totalMoney = NumberUtil.add(totalMoney, NumberUtil.mul(NumberUtil.mul(bigCount, outStockGoodsParam.getGoodsContain()), goodsBatch.getGoodsInPrice()));
                    } else {
                        break;
                    }
                }
            }
            if (BigDecimal.ZERO.compareTo(totalMoney) != 0) {
                map.put(outStockGoodsParam.getGoodsBarcode(), NumberUtil.mul(NumberUtil.div(totalMoney, totalCount), outStockGoodsParam.getGoodsContain()).setScale(2, RoundingMode.HALF_UP));
            }
        }
        if (CollectionUtil.isNotEmpty(goodsBatchMap.values())) {
            List<GoodsBatch> goodsBatchList = new ArrayList<>(goodsBatchMap.values());
            goodsBatchMapper.updateBatchCount(goodsBatchList);
        }
        if (CollectionUtil.isNotEmpty(goodsSaleBatchMap.values())) {
            List<GoodsSaleBatch> goodsSaleBatchList = new ArrayList<>(goodsSaleBatchMap.values());
            goodsSaleBatchMapper.insertBatch(goodsSaleBatchList);
        }
        return map;
    }

    @Override
    public void addAndWaitAuditBatch(ShopStockDetail shopStockDetail, OutStockParam outStockParam) {
        List<OutStockGoodsParam> goodsList = outStockParam.getGoodsList();
        List<OutStockGoodsBatchParam> selectGoodsBatchList = outStockParam.getGoodsBatchList();
        Map<Long, List<OutStockBatchParam>> selectGoodsBatchMap = selectGoodsBatchList.stream().collect(Collectors.toMap(OutStockGoodsBatchParam::getGoodsId, OutStockGoodsBatchParam::getBatchList));
        Map<String, GoodsBatch> goodsBatchMap = new HashMap<>();
        Map<String, GoodsSaleBatch> goodsSaleBatchMap = new HashMap<>();
        for (OutStockGoodsParam outStockGoodsParam : goodsList) {
            List<GoodsBatch> hasGoodsBatchList = goodsBatchMapper.selectAvailableList(Long.parseLong(shopStockDetail.getShopUnique()), outStockGoodsParam.getForeignKey());
            List<OutStockBatchParam> goodsOutBachList = selectGoodsBatchMap.get(outStockGoodsParam.getGoodsId());
            BigDecimal waitOutCount = outStockGoodsParam.getGoodsCount();
            BigDecimal batchOutCount = BigDecimal.ZERO;
            if (CollectionUtil.isNotEmpty(goodsOutBachList)) {
                // 已选出库批次
                for (OutStockBatchParam stockBatchParam : goodsOutBachList) {
                    GoodsBatch goodsBatch = goodsBatchMap.get(stockBatchParam.getBatchUnique());
                    if (ObjectUtil.isNull(goodsBatch)) {
                        Optional<GoodsBatch> optional = hasGoodsBatchList.stream().filter(v -> ObjectUtil.equals(v.getBatchUnique(), stockBatchParam.getBatchUnique())).findFirst();
                        if (!optional.isPresent()) {
                            throw new RuntimeException("商品名称：" + outStockGoodsParam.getGoodsName() + "(" + outStockGoodsParam.getGoodsBarcode() + ") 对应出库批次：" + stockBatchParam.getBatchUnique() + " 库存不足");
                        }
                        goodsBatch = optional.get();
                    }
                    BigDecimal smallCount = NumberUtil.mul(outStockGoodsParam.getGoodsContain(), stockBatchParam.getGoodsCount());
                    if (goodsBatch.getGoodsCount().compareTo(smallCount) < 0) {
                        throw new RuntimeException("商品名称：" + outStockGoodsParam.getGoodsName() + "(" + outStockGoodsParam.getGoodsBarcode() + ") 对应出库批次：" + stockBatchParam.getBatchUnique() + " 库存不足");
                    }
                    goodsBatch.setGoodsCount(NumberUtil.sub(goodsBatch.getGoodsCount(), smallCount));
                    goodsBatch.setUpdateId(shopStockDetail.getUpdateId());
                    goodsBatch.setUpdateTime(shopStockDetail.getUpdateTime());
                    goodsBatchMap.put(goodsBatch.getBatchUnique(), goodsBatch);
                    batchOutCount = NumberUtil.add(batchOutCount, stockBatchParam.getGoodsCount());

                    GoodsSaleBatch goodsSaleBatch = goodsSaleBatchMap.get(outStockGoodsParam.getGoodsBarcode()+stockBatchParam.getBatchUnique());
                    if (ObjectUtil.isNull(goodsSaleBatch)) {
                        goodsSaleBatch = new GoodsSaleBatchData();
                        goodsSaleBatch.setShopUnique(goodsBatch.getShopUnique());
                        goodsSaleBatch.setStockListUnique(shopStockDetail.getListUnique());
                        goodsSaleBatch.setGoodsBarcode(outStockGoodsParam.getGoodsBarcode());
                        goodsSaleBatch.setGoodsInPrice(NumberUtil.mul(goodsBatch.getGoodsInPrice(), outStockGoodsParam.getGoodsContain()).setScale(2, RoundingMode.HALF_UP));
                        goodsSaleBatch.setGoodsOutPrice(outStockGoodsParam.getStockPrice());
                        goodsSaleBatch.setCreateId(shopStockDetail.getUpdateId());
                        goodsSaleBatch.setCreateTime(shopStockDetail.getUpdateTime());
                        goodsSaleBatch.setUpdateId(shopStockDetail.getUpdateId());
                        goodsSaleBatch.setBatchUnique(goodsBatch.getBatchUnique());
                        goodsSaleBatch.setUpdateTime(shopStockDetail.getUpdateTime());
                        goodsSaleBatch.setGoodsOutCount(stockBatchParam.getGoodsCount());
                    } else {
                        goodsSaleBatch.setGoodsOutCount(NumberUtil.add(goodsSaleBatch.getGoodsOutCount(), stockBatchParam.getGoodsCount()));
                    }
                    goodsSaleBatchMap.put(goodsSaleBatch.getGoodsBarcode() + goodsSaleBatch.getBatchUnique(), goodsSaleBatch);
                }
                if (waitOutCount.compareTo(batchOutCount) < 0) {
                    throw new RuntimeException("商品名称：" + outStockGoodsParam.getGoodsName() + "(" + outStockGoodsParam.getGoodsBarcode() + ") 实际出库数量不能小于批次出库总数量");
                }
            }
        }
        if (CollectionUtil.isNotEmpty(goodsSaleBatchMap.values())) {
            List<GoodsSaleBatch> goodsSaleBatchList = new ArrayList<>(goodsSaleBatchMap.values());
            goodsSaleBatchMapper.insertBatch(goodsSaleBatchList);
        }
    }

    @Override
    public Map<String, BigDecimal> subGoodsBatchCount(ShopStockDetail shopStockDetail, List<ShopStockData> shopStockDataList) {
        Map<String, BigDecimal> map = new HashMap<>();
        GoodsSaleBatch querySaleBatch = new GoodsSaleBatch();
        querySaleBatch.setShopUnique(Long.parseLong(shopStockDetail.getShopUnique()));
        querySaleBatch.setStockListUnique(shopStockDetail.getListUnique());
        List<GoodsSaleBatch> goodsSaleBatchList = goodsSaleBatchMapper.findList(querySaleBatch);
        Map<String, GoodsBatch> goodsBatchUpdateMap = new HashMap<>();
        Map<String, GoodsSaleBatch> goodsSaleBatchMap = new HashMap<>();
        Date currentDate = DateUtil.date();
        for (ShopStockData shopStockData : shopStockDataList) {
            BigDecimal totalCount = BigDecimal.ZERO;
            BigDecimal totalMoney = BigDecimal.ZERO;
            List<GoodsBatch> hasGoodsBatchList = goodsBatchMapper.selectAvailableList(Long.parseLong(shopStockDetail.getShopUnique()), shopStockData.getForeignKey());
            List<GoodsSaleBatch> goodsSaleBatches = goodsSaleBatchList.stream().filter(f -> StrUtil.equals(f.getGoodsBarcode(), shopStockData.getGoodsBarcode())).collect(Collectors.toList());
            BigDecimal waitOutCount = shopStockData.getGoodsCount();
            BigDecimal saleBatchTotalCount = BigDecimal.ZERO;
            if (CollectionUtil.isNotEmpty(goodsSaleBatches)) {
                for (GoodsSaleBatch goodsSaleBatch : goodsSaleBatches) {
                    BigDecimal smallCount = NumberUtil.mul(shopStockData.getGoodsContain(), goodsSaleBatch.getGoodsOutCount());
                    GoodsBatch goodsBatch = goodsBatchUpdateMap.get(goodsSaleBatch.getBatchUnique());
                    if (ObjectUtil.isNull(goodsBatch)) {
                        Optional<GoodsBatch> optional = hasGoodsBatchList.stream().filter(v -> StrUtil.equals(v.getBatchUnique(), goodsSaleBatch.getBatchUnique())).findFirst();
                        if (optional.isPresent()) {
                            goodsBatch = optional.get();
                        } else {
                            throw new RuntimeException("商品条码：" + shopStockData.getGoodsBarcode() + " 对应出库批次：" + goodsBatch.getBatchUnique() + " 库存不足");
                        }
                    }
                    if (goodsBatch.getGoodsCount().compareTo(smallCount) < 0) {
                        throw new RuntimeException("商品条码：" + shopStockData.getGoodsBarcode() + " 对应出库批次：" + goodsBatch.getBatchUnique() + " 库存不足");
                    }
                    goodsBatch.setGoodsCount(NumberUtil.sub(goodsBatch.getGoodsCount(), smallCount));
                    goodsBatch.setUpdateTime(currentDate);
                    goodsBatch.setUpdateId(shopStockDetail.getUpdateId());
                    goodsBatchUpdateMap.put(goodsBatch.getBatchUnique(), goodsBatch);
                    goodsSaleBatchMap.put(goodsSaleBatch.getGoodsBarcode()+goodsSaleBatch.getBatchUnique(), goodsSaleBatch);
                    saleBatchTotalCount = NumberUtil.add(saleBatchTotalCount, goodsSaleBatch.getGoodsOutCount());

                    totalCount = NumberUtil.add(totalCount, smallCount);
                    totalMoney = NumberUtil.add(totalMoney, NumberUtil.mul(smallCount, goodsBatch.getGoodsInPrice()));
                }
            }
            if (waitOutCount.compareTo(saleBatchTotalCount) < 0) {
                throw new RuntimeException("商品条码：" + shopStockData.getGoodsBarcode() + " 应出库数量不能小于对应出库批次总数量");
            }
            waitOutCount = NumberUtil.sub(waitOutCount, saleBatchTotalCount);
            if (waitOutCount.compareTo(BigDecimal.ZERO) > 0) {
                //先进先出
                for (GoodsBatch goodsBatch : hasGoodsBatchList) {
                    GoodsBatch hasGoodsBatch = goodsBatchUpdateMap.get(goodsBatch.getBatchUnique());
                    if (ObjectUtil.isNotNull(hasGoodsBatch)) {
                        goodsBatch = hasGoodsBatch;
                    }
                    BigDecimal bigCount = goodsBatch.getGoodsCount();
                    if (!StrUtil.equals(shopStockData.getGoodsBarcode(), shopStockData.getForeignKey())) {
                        bigCount = NumberUtil.div(goodsBatch.getGoodsCount(), shopStockData.getGoodsContain(), 0, RoundingMode.DOWN);
                    }
                    if (waitOutCount.compareTo(BigDecimal.ZERO) > 0) {
                        GoodsSaleBatch goodsSaleBatch = goodsSaleBatchMap.get(shopStockData.getGoodsBarcode()+goodsBatch.getBatchUnique());
                        if (ObjectUtil.isNull(goodsSaleBatch)) {
                            goodsSaleBatch = new GoodsSaleBatchData();
                            goodsSaleBatch.setShopUnique(goodsBatch.getShopUnique());
                            goodsSaleBatch.setStockListUnique(shopStockData.getListUnique());
                            goodsSaleBatch.setGoodsBarcode(shopStockData.getGoodsBarcode());
                            goodsSaleBatch.setGoodsInPrice(NumberUtil.mul(goodsBatch.getGoodsInPrice(), shopStockData.getGoodsContain()).setScale(2, RoundingMode.HALF_UP));
                            goodsSaleBatch.setGoodsOutPrice(shopStockData.getStockPrice());
                            goodsSaleBatch.setCreateId(shopStockDetail.getUpdateId());
                            goodsSaleBatch.setCreateTime(currentDate);
                            goodsSaleBatch.setUpdateId(shopStockDetail.getUpdateId());
                            goodsSaleBatch.setBatchUnique(goodsBatch.getBatchUnique());
                            goodsSaleBatch.setUpdateTime(currentDate);
                            goodsSaleBatch.setGoodsOutCount(BigDecimal.ZERO);
                        }
                        if (waitOutCount.compareTo(bigCount) > 0) {
                            goodsSaleBatch.setGoodsOutCount(NumberUtil.add(goodsSaleBatch.getGoodsOutCount(), bigCount));
                            waitOutCount = NumberUtil.sub(waitOutCount, bigCount);
                            goodsBatch.setGoodsCount(NumberUtil.sub(goodsBatch.getGoodsCount(), NumberUtil.mul(bigCount, shopStockData.getGoodsContain())));
                            goodsBatch.setUpdateTime(currentDate);
                            goodsBatch.setUpdateId(shopStockDetail.getUpdateId());
                        } else {
                            goodsBatch.setGoodsCount(NumberUtil.sub(goodsBatch.getGoodsCount(), NumberUtil.mul(waitOutCount, shopStockData.getGoodsContain())));
                            goodsSaleBatch.setGoodsOutCount(NumberUtil.add(goodsSaleBatch.getGoodsOutCount(), waitOutCount));
                            waitOutCount = BigDecimal.ZERO;
                            goodsBatch.setUpdateTime(currentDate);
                            goodsBatch.setUpdateId(shopStockDetail.getUpdateId());
                        }
                        goodsSaleBatchMap.put(goodsSaleBatch.getGoodsBarcode()+goodsSaleBatch.getBatchUnique(), goodsSaleBatch);
                        goodsBatchUpdateMap.put(goodsBatch.getBatchUnique(), goodsBatch);

                        totalCount = NumberUtil.add(totalCount, NumberUtil.mul(bigCount, shopStockData.getGoodsContain()));
                        totalMoney = NumberUtil.add(totalMoney, NumberUtil.mul(NumberUtil.mul(bigCount, shopStockData.getGoodsContain()), goodsBatch.getGoodsInPrice()));
                    } else {
                        break;
                    }
                }
            } else {
                break;
            }
            if (BigDecimal.ZERO.compareTo(totalMoney) != 0) {
                map.put(shopStockData.getGoodsBarcode(), NumberUtil.mul(NumberUtil.div(totalMoney, totalCount), shopStockData.getGoodsContain()).setScale(2, RoundingMode.HALF_UP));
            }
        }
        if (CollectionUtil.isNotEmpty(goodsBatchUpdateMap.values())) {
            List<GoodsBatch> goodsBatchList = new ArrayList<>(goodsBatchUpdateMap.values());
            goodsBatchMapper.updateBatchCount(goodsBatchList);
        }
        if (CollectionUtil.isNotEmpty(goodsSaleBatchMap.values())) {
            List<GoodsSaleBatch> goodsSaleBatches = new ArrayList<>(goodsSaleBatchMap.values()).stream().filter(v -> BigDecimal.ZERO.compareTo(v.getGoodsOutCount()) == -1).collect(Collectors.toList());
            List<GoodsSaleBatch> updateList = goodsSaleBatches.stream().filter(v -> ObjectUtil.isNotNull(v.getGoodsSaleBatchId())).collect(Collectors.toList());
            if (CollectionUtil.isNotEmpty(updateList)) {
                goodsSaleBatchMapper.deleteByListUnique(Long.parseLong(shopStockDetail.getShopUnique()), shopStockDetail.getListUnique());
            }
            goodsSaleBatchMapper.insertBatch(goodsSaleBatches);
        }
        return map;
    }
}
