package org.haier.shop.service;

import org.haier.shop.dao.CountMsgNingyuDao;
import org.haier.shop.util.PurResult;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;


@Service("countMsgNingyuService")
@Transactional
public class CountMsgNingyuServiceImpl implements CountMsgNingyuService{
	
	@Resource
	private CountMsgNingyuDao countMsgNingyuDao;
	
	
	
	public PurResult queryNYsale(Map<String,Object> map){
		PurResult sr=new PurResult(1,"查询成功！");
		List<Map<String,Object>> data=new  ArrayList<>();
		Map<String,Object> cord=new  HashMap<String,Object>();
		if(map.containsKey("query_type")&& map.get("query_type").equals("shop"))
		{
			 data=countMsgNingyuDao.queryNYsale(map);
			 cord=countMsgNingyuDao.queryNYsaleCount(map);
		}else
		{
			 data=countMsgNingyuDao.queryNYsaleArea(map);
			 cord=countMsgNingyuDao.queryNYsaleAreaCount(map);
		}

		sr.setData(data);
		sr.setCord(cord);
		return sr;
	}


	@Override
	public PurResult queryNYBar(Map<String, Object> map) {
		PurResult sr=new PurResult(1,"查询成功！");
		List<Map<String,Object>> data=new  ArrayList<>();// 商品 -分组
		List<Map<String,Object>> data2=new  ArrayList<>();// 分类- 分组
		if(map.containsKey("query_type")&& map.get("query_type").equals("shop"))
		{
		 data=countMsgNingyuDao.queryNYBar(map);
		 data2=countMsgNingyuDao.querykindBar(map);
		 
		}else
		{
			 data=countMsgNingyuDao.queryNYBarArea(map);
		}
		
		sr.setData(data);
		sr.setRows(data2);
		return sr;
	}


	


}


