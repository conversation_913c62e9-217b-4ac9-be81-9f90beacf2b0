package org.haier.shop.service;

import java.util.Map;

import javax.servlet.http.HttpServletRequest;

import org.haier.shop.entity.Staff;
import org.haier.shop.params.shopStaff.ShopIoBoundInspectEditParams;
import org.haier.shop.result.shop.ShopConfigQueryResult;
import org.haier.shop.util.PurResult;
import org.haier.shop.util.ShopsResult;

public interface StaffService {
	/**
	 * 管理员登录
	 * @param map
	 * @return
	 */
	public ShopsResult staffLoginByAccountPwd(HttpServletRequest request,Map<String,Object> map);
	
	/**
	 * 根据管理员信息，查询管理员管理的店铺信息
	 * @param map
	 * @return
	 */
	public ShopsResult queryShopsByManager(Map<String,Object> map);
	
	/**
	 * 查询管理员旗下所有店铺及其管理员权限信息
	 * @param map
	 * @return
	 */
	public ShopsResult queryShopManager(Map<String,Object> map);
	
	
	/**
	 * 修改员工权限
	 * @param map
	 * @return
	 */
	public ShopsResult modifyStaffPower(Map<String,Object> map);
	
	
	/**
	 * 根据店铺名获取相关管理员信息
	 * @param map
	 * @return
	 */
	public ShopsResult queryShopManagers(Map<String,Object> map);
	
	/**
	 * 根据员工编号，修改相应权限
	 * @param map
	 * @return
	 */
	public ShopsResult queryManagerPower(Map<String,Object> map);
	/**
	 * 会员信息页数查询
	 * @param map
	 * @return
	 */
	public ShopsResult queryStaffsPages(Map<String,Object> map);
	
	/**
	 * 员工信息分页查询
	 * @param map
	 * @return
	 */
	public PurResult queryStaffByPage(Map<String,Object> map);
	
	/**
	 * 查询员工信息详情
	 * @param map
	 * @return
	 */
	public ShopsResult queryStaffDetailMessage(Map<String,Object> map);
	
	/**
	 * 更新会员信息
	 * @param map
	 * @return
	 */
	public ShopsResult updateStaffBaseMessage(Map<String,Object> map,String role_code,String staffAccount_old);
	
	/**
	 * 添加新的员工信息
	 * @return
	 */
	public ShopsResult addNewStaff(
			HttpServletRequest request,
			Long shopUnique,
			String staffAccount,
			String staffPwd,
			String staffName,
			String staffPhone,
			Integer  staffPosition,
//			Integer powerPrice,
//			Integer powerCount,
//			Integer powerSupplier,
//			Integer powerKind,
//			Integer powerInPrice,
//			Integer powerName,
//			Integer powerDelete,
//			Integer powerPur,
//			Integer powerAdd,
//			Integer powerRecharge
			String role_code,
			String county,
			String managerUnique
			);
	/**
	 * 管理员旗下店铺数量页数查询
	 * @param map
	 * @return
	 */
	public ShopsResult queryAllShopsPages(Map<String,Object> map);
	
	/**
	 * 查询同管理员的店铺信息列表
	 * @param map
	 * @return
	 */
	public PurResult queryAllShopsByPage(Map<String,Object> map);
	
	/**
	 * 销售详情
	 * @param map
	 * @return
	 */
	public PurResult queryGoodsSaleByStaffPage(Map<String,Object> map);

	public Map<String, Object> getStaffById(Integer staffId);

	public Map<String, Object> queryShopInfoByShopUnique(Long shopUnique);
	
	/**
	 * 根据登录账户获取登录用户信息
	 * @param staff_account 登录账号
	 * @return
	 */
	public Staff getStaffByAccount(String staff_account);
	
	/**
	 * 删除员工
	 * @param staffId 员工id
	 * @return
	 */
	public PurResult deleteStaff(String staffId);

	ShopsResult editIoBoundInspect(ShopIoBoundInspectEditParams params);

	ShopConfigQueryResult queryShopConfig(String shopUnique);
}
