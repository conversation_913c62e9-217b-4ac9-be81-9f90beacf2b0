package org.haier.shop.service;

import java.math.BigDecimal;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;

import org.haier.shop.dao.ShopStatisticsDao;
import org.haier.shop.entity.MianMiMain;
import org.haier.shop.util.PurResult;
import org.haier.shop.util.ShopsResult;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Service
public class ShopStatisticsServiceImpl implements ShopStatisticsService{
	@Resource
	private ShopStatisticsDao dao;
	
	/**
	 * 根据日期查询各种支付方式的支付金额
	 * @param map
	 * @return
	 */
	public PurResult queryPayMethodByDay(Map<String,Object> params){
		PurResult result=new PurResult();			
		List<Map<String,Object>> list=dao.queryPayMethodByDay(params);
		BigDecimal total=new BigDecimal("0");
		for(Map<String,Object> map:list) {			
			total=total.add(new BigDecimal(map.get("listTotal").toString()));			
		}
		for(Map<String,Object> map:list) {
			if(total.compareTo(new BigDecimal("0"))==0) {
				map.put("percent", "100%");
			}else {
				BigDecimal percent=new BigDecimal(map.get("listTotal").toString()).multiply(new BigDecimal("100")).divide(total, 2);			
				map.put("percent", percent.toString()+"%");
			}			
		}
		result.setStatus(1);
		result.setMsg("查询成功");
		result.setCount(list.size());
		result.setData(list);
		return result;
	}
	
	/**
	 * 查询免密支付分页数量及总订单数量
	 * @param map
	 * @return
	 */
	public ShopsResult payMethodDetailPages(Map<String,Object> map){
		ShopsResult sr=new ShopsResult(1, "查询成功！");
		List<Map<String,Object>> datas=dao.payMethodDetailPages(map);
		if(null==datas){
			sr.setStatus(2);
			sr.setMsg("没有免密支付的数据信息！");
			return sr;
		}
		Long totalCount=0l;
		BigDecimal totalSum=new BigDecimal(0);
		Map<String,Object> result=new HashMap<String,Object>();
		for(int i=0;i<datas.size();i++){
			totalCount+=(Long)datas.get(i).get("count");
			BigDecimal payMoney=(BigDecimal)(datas.get(i).get("payMoney"));
			totalSum=totalSum.add(payMoney);
		}
		List<Map<String,Object>> cord=dao.queryMianmiStatus();
		result.put("shopsCount", 0l+datas.size());
		result.put("listCount", totalCount);
		result.put("totalSum", totalSum.doubleValue());
		sr.setData(result);
		sr.setCord(cord);
		return sr;
	}
	
	/**
	 * 查询免密支付分页数量及总订单数量
	 * @param map
	 * @return
	 */
	public ShopsResult payMethodDetail(Map<String,Object> map){
		ShopsResult sr=new ShopsResult(1, "查询成功！");
		List<Map<String,Object>> data=dao.payMethodDetail(map);
		sr.setData(data);
		return sr;
	}
	
	/**
	 * 查询符合条件的店铺信息
	 * @param map
	 * @return
	 */
	public ShopsResult mianmiDetailShopsListPages(Map<String,Object> map){
		ShopsResult sr=new ShopsResult(1, "查询成功！");
		Integer data=dao.mianmiDetailShopsListPages(map);
		sr.setData(data);
		return sr;
	}
	
	/**
	 * 查询符合条件的店铺信息
	 * @param map
	 * @return
	 */
	public ShopsResult mianmiDetailShopsList(Map<String,Object> map){
		ShopsResult sr=new ShopsResult(1, "查询成功!");
		List<Map<String,Object>> data=dao.mianmiDetailShopsList(map);
		sr.setData(data);
		return sr;
	}
	
	/**
	 * 店铺销售订单列表页面
	 * 周期内查询店铺订单信息列表统计
	 * @param map
	 * @return
	 */
	public ShopsResult getShopListPageMsg(Map<String,Object> map){
		ShopsResult sr=new ShopsResult(1,"查询成功");
		Map<String,Object> data=dao.getShopListPageMsg(map);
		sr.setData(data);
		return sr;
	}
	
	/**
	 * 店铺销售列表查询
	 */
	public ShopsResult getShopListByPage(Map<String,Object> map){
		ShopsResult sr=new ShopsResult(1, "查询成功！");
		List<Map<String,Object>> data=dao.getShopListByPage(map);
		sr.setData(data);
		return sr;
	}

	/**
	 * 获取订单详情
	 * @param map
	 * @return
	 */
	public ShopsResult showListDetail(Map<String,Object> map){
		ShopsResult sr=new ShopsResult(1, "查询成功！");
		List<Map<String,Object>> data=dao.showListDetail(map);
		sr.setData(data);
		return sr;
	}
	
	/**
	 * 更新店铺免密支付状态
	 * @param map
	 * @return
	 */
	@Transactional
	public ShopsResult updateShopMianmiStatus(Map<String,Object> map){
		ShopsResult sr=new ShopsResult(1,"更新成功");
		Integer k=dao.updateShopMianmiStatus(map);
		if(k==0){
			sr.setStatus(2);
			sr.setMsg("更新失败");
		}
		return sr;
	}
	
	
	/**
	 * 免密支付EXCEL下载
	 * @param map
	 * @return
	 */
	public List<MianMiMain> downExcel(Map<String,Object> map){
		System.out.println("开始请求接口：：："+new Date());
		ShopsResult sr=new ShopsResult(1,"更新成功！");
//		Map<String,Object> resMap=dao.queryListStatistics(map);
//		resMap.put("allListCount", resMap.get("listCount"));
//		resMap.put("allListSum", resMap.get("listSum"));
		map.put("saleListPayment", 9);
//		resMap.putAll(dao.queryListStatistics(map));
//		sr.setCord(resMap);//总数据
		List<MianMiMain> list=null;
		if(map.get("type").toString().equals("1")){
			list=dao.queryShopsMianmiStatistics(map);
		}else{
			list=dao.queryShopsAllStatistics(map);
		}
		
		System.out.println("已获得数据：：："+new Date());
		sr.setData(list);
		System.out.println(list.size());
		System.out.println(new Date());
		return list;
	}
	
}
