package org.haier.shop.service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;

import org.haier.shop.dao.OilDao;
import org.haier.shop.util.ShopsResult;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;


@Service
public class OilServiceImpl implements OilService{

	@Resource
	private OilDao oilDao;
	
	
	/**
	 * 
	 * @param id 修改时上传ID
	 * @param shopUnique 店铺编号
	 * @param disName 名称
	 * @param startTime 活动开始时间
	 * @param endTime 活动结束时间
	 * @param payAmount 需要支付的金额
	 * @param discountMoney 给顾客优惠的金额
	 * @param activeState 1、正常；2、停用；3、删除
	 * @return
	 */
	public ShopsResult addNewShopDis(Integer id,String shopUnique,String disName,String startTime,
			String endTime,Double payAmount,Double discountMoney,Integer activeState) {
		ShopsResult sr = new ShopsResult(1, "操作成功!");
		
		Map<String,Object> map = new HashMap<String,Object>();
		map.put("id", id);
		map.put("activeState", activeState == null ? 1 : activeState);
		map.put("shopUnique", shopUnique);
		map.put("disName", disName);
		map.put("startTime", startTime);
		map.put("endTime", endTime);
		map.put("payAmount", payAmount);
		map.put("discountMoney", discountMoney);
		
		if(null != id && id != 0) {
			oilDao.updateShopDis(map);
		}else {
			oilDao.addNewShopDis(map);
		}
		
		return sr;
	}
	/**
	 * 查询店铺的优惠配置信息
	 * @param shopUnique
	 * @param page
	 * @param pageSize
	 * @return
	 */
	public ShopsResult queryShopDisList(String shopUnique,Integer page,Integer pageSize,String startTime,String endTime) {
		ShopsResult sr = new ShopsResult(1,"查询成功!");
		
		Map<String,Object> map = new HashMap<String,Object>();
		map.put("shopUnique", shopUnique);
		map.put("startNum", (page - 1) * pageSize);
		map.put("pageSize", pageSize);
		map.put("startTime", startTime);
		map.put("endTime", endTime);
		
		List<Map<String,Object>> list = oilDao.queryShopDisList(map);
		Integer count = oilDao.queryShopDisCount(map);
		
		sr.setData(list);
		sr.setCount(count);
		
		return sr;
	}
	/**
	 * 根据ID获取对应的油品、油枪信息
	 * @param id
	 * @return
	 */
	@Override
	public Map<String,Object> queryOrderMsgById(Integer id,Integer parType) {
		Map<String,Object> map = new HashMap<String,Object>();
		
		map.put("id", id);
		map.put("parType", parType);
		List<Map<String,Object>> list = oilDao.queryOilMsgList(map);
		if(null == list || list.isEmpty()) {
			return null;
		}else {
			return list.get(0);
		}
	}
	
	/**
	 * 添加新的油枪油品信息
	 * @param parName 名称
	 * @param parValue 值
	 * @param stationPrice 油站价格
	 * @param interPrice 国标价
	 * @param parType 类型：1、油枪；2、油品（油品必须标注价格）
	 * @param id 当前ID信息，如果不为空，则为更新
	 * @param delFlag 1、删除；否则不操作
	 * @return
	 */
	@Override
	@Transactional
	public ShopsResult addNewMsg(String parName,String parValue,Double stationPrice,Double interPrice,Integer parType,Integer id,Integer delFlag) {
		ShopsResult sr = new ShopsResult(1,"操作成功!");
		
		if(null == parType || (parType != 1 && parType != 2)) {
			sr.setStatus(0);
			sr.setMsg("请输入正确的添加类型");
			return sr;
		}
		//添加油枪，不需要输入价格
		if(parType == 1) {
			
		}else {
			if((null == stationPrice || null == interPrice) && delFlag == null) {
				sr.setStatus(0);
				sr.setMsg("国标价和油站价格不能为空");
				return sr;
			}
		}
		
		Map<String,Object> map = new HashMap<String,Object>();
		map.put("parName", parName);
		map.put("parValue", parValue);
		//根据ID是否存在，判断是新增还是更新
		if(id != null) {
			//更有油品信息
			map.put("delFlag", delFlag);
			map.put("id", id);
			oilDao.updateOilMsg(map);
			
			//更新价格信息或删除价格信息
			map.put("oilNum", parValue);
			if(null != delFlag) {
				oilDao.deleteOilInterPrice(map);
			}else {
				map.put("stationPrice", stationPrice);
				map.put("interPrice", interPrice);
				oilDao.updateOilInterPrice(map);
			}
			
			return sr;
		}
		
		
		map.put("parType", parType);
		map.put("delFlag", "1");
		//防止重复插入，先查重
		List<Map<String,Object>> list = oilDao.queryOilMsgList(map);
		if(null != list && !list.isEmpty()) {
			sr.setStatus(0);
			sr.setMsg("该类型的油品信息已存在，");
			return sr;
		}
		
		oilDao.addNewMsg(map);
	
		if(parType == 2) {
			map.put("oilNum", parValue);
			map.put("stationPrice", stationPrice);
			map.put("interPrice", interPrice);
			oilDao.addNewInterPrice(map);
		}
		
		return sr;
	}
	
	/**
	 * 查询油号油枪列表信息
	 * @param parType
	 * @param delFlag
	 * @return
	 */
	public ShopsResult queryOilMsgList(Integer parType,Integer delFlag,Integer id) {
		ShopsResult sr = new ShopsResult(1,"查询成功！");
		
		Map<String,Object> map = new HashMap<String,Object>();
		map.put("parType", parType);
		map.put("delFlag", delFlag);
		map.put("id", id);
		
		List<Map<String,Object>> list = oilDao.queryOilMsgList(map);
		
		sr.setData(list);
		
		return sr;
	}
}
