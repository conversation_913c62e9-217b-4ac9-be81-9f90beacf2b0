package org.haier.shop.service;

import java.util.List;
import java.util.Map;

import org.haier.shop.util.ShopsResult;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import com.alibaba.fastjson.JSONArray;

/**
* @author: 作者:王恩龙
* @version: 2023年3月29日 下午5:32:24
*
*/
public interface GoodsKindInventedService {
	/**
	 * 批量更新虚拟分类排序
	 * @param list<Map<String,Object>> goodsKindInventedId：虚拟分类ID;kindSort:虚拟分类排序
	 * @return
	 */
	public ShopsResult modifyGoodsKindInventedSort(JSONArray list);
	/**
	 * 删除指定虚拟分类下的商品或删除指定ID的虚拟分类下商品
	 * @param goods_kind_invented_id
	 * @param goods_kind_invented_goods_id
	 * @return
	 */
	public ShopsResult deleteGoodsKindInventedGoods(Integer goods_kind_invented_id , Integer goods_kind_invented_goods_id);
	/**
	 * 查询店铺商品信息，排除指定虚拟分类
	 * @param shop_unique 店铺编号
	 * @param goods_kind_invented_id 虚拟分类ID
	 * @param page 页码
	 * @param limit 单页查询数量
	 * @param search_name 输入框搜索内容
	 * @return
	 */
	public ShopsResult queryGoodsList(String shop_unique, Integer goods_kind_invented_id, Integer page , Integer limit, String search_name,String barcodeList,String parUnique,String kindUnique);
	/**
	 * 添加或更新虚拟分类信息
	 * @param goods_kind_invented_id
	 * @param goods_kind_unique
	 * @param goods_kind_name
	 * @param goods_list
	 * @param shop_unique
	 * @param valid_type 1\有效;0\无效
	 * @return
	 */
	public ShopsResult addGoodsKindInvented(Integer goods_kind_invented_id,Integer goods_kind_unique,String goods_kind_name,String goods_list,String shop_unique,Integer valid_type);
	
	/**
	 * 查询虚拟分类下的商品列表信息
	 * @param goods_kind_invented_id 虚拟分类ID
	 * @param goods_kind_unique 虚拟分类编号
	 * @return
	 */
	public ShopsResult queryGoodsKindInventedGoodsList(Integer goods_kind_invented_id,String goods_kind_unique,Integer page,Integer limit);
	
	/**
	 * 查询店铺内虚拟分类信息
	 * @param shop_unique 店铺编号
	 * @param page 页码
	 * @param pageSize 单页查询数量
	 * @return
	 */
	public ShopsResult queryGoodsKindInventedList(String shop_unique ,String search_name, Integer page, Integer pageSize);
}
