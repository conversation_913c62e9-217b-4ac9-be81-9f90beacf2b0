package org.haier.shop.service;

import java.util.ArrayList;
import java.util.Calendar;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;

import org.haier.customer.entity.Shops;
import org.haier.shop.dao.DataSearchDao;
import org.haier.shop.util.ShopsResult;
import org.haier.shop.util.UtilForJAVA;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Service
@Transactional
public class DataSearchServiceImpl implements DataSearchService{
	@Resource
	private DataSearchDao dataDao;
	
	/**
	 * 大屏左上角，到店人数数量统计
	 * @return
	 */
	public ShopsResult peopleArrivingSearch(){
		ShopsResult sr=new ShopsResult(1,"查询成功");
		String startTime="";
		String endTime="";
		List<Map<String,Object>> list=new ArrayList<>();
		Calendar c=Calendar.getInstance();
		//今天到店人数和昨日比对情况
		startTime=c.get(Calendar.YEAR)+"-"+(c.get(Calendar.MONTH)+1)+"-"+c.get(Calendar.DAY_OF_MONTH);
		Map<String,Object> map=new HashMap<String,Object>();
		map.put("startTime", startTime);
		map.put("endTime", startTime);
		map.put("dayType", "DAY");
		list.add(dataDao.peopleArrivingSearch(map));
		
		
		map.put("startTime", UtilForJAVA.getMondayOfThisWeek());	
		map.put("endTime", UtilForJAVA.getSundayOfThisWeek());
		map.put("dayType", "WEEK");
		list.add(dataDao.peopleArrivingSearch(map));
		
		
		c=Calendar.getInstance();
		c.set(Calendar.DAY_OF_MONTH,0);
		c.add(Calendar.DAY_OF_MONTH, 1);
		//本周到店人数和昨日比对情况
		startTime=c.get(Calendar.YEAR)+"-"+(c.get(Calendar.MONTH)+1)+"-"+c.get(Calendar.DAY_OF_MONTH);
		c.add(Calendar.MONTH, 1);
		map.put("dayType", "MONTH");
		endTime=c.get(Calendar.YEAR)+"-"+(c.get(Calendar.MONTH)+1)+"-"+(c.get(Calendar.DAY_OF_MONTH));
		map.put("startTime", startTime);
		map.put("endTime", endTime);
		list.add(dataDao.peopleArrivingSearch(map));
		
		sr.setData(list);
		return sr;
	}
	
	/**
	 * 在线点数数量统计
	 * @return
	 */
	public ShopsResult shopOnLineStatis(){
		ShopsResult sr=new ShopsResult();
		List<Map<String,Object>> list=dataDao.shopOnLineStatis();
		sr.setData(list);
		return sr;
	}
	
	/**
	 * 明星店铺统计
	 * @return
	 */
	public ShopsResult theBestSallerShop(){
		ShopsResult sr=new ShopsResult(1,"查询成功！");
		List<Map<String,Object>> list=dataDao.theBestSallerShop();
		if(null==list||list.isEmpty()){
			sr.setStatus(2);
			sr.setMsg("没有相应的店铺信息");
		}
		sr.setData(list);
		return sr;
	}
	
	/**
	 * 各阶段支付金额统计
	 * @return
	 */
	public ShopsResult getListIncomeStage(){
		ShopsResult sr=new ShopsResult(1, "查询成功！");
		List<Map<String,Object>> list=dataDao.getListIncomeStage();
		sr.setData(list);
		return sr;
	}
	
	/**
	 * 近一月商品价格走势图
	 * @return
	 */
	public ShopsResult goodsPriceTrend(){
		ShopsResult sr=new ShopsResult(1, "查询成功！");
		Map<String,Object> map=new HashMap<String,Object>();
		List<Map<String,Object>> list=dataDao.goodsPriceTrend(map);
		sr.setData(list);
		return sr;
	}
	
	/**
	 * 查询某个商品三个月内的售价浮动情况
	 * @return
	 */
	public ShopsResult getPriceFloat(){
		ShopsResult sr=new ShopsResult(1,"查询成功");
		Double averprice=dataDao.getGoodsAverPrice();
		Map<String,Object> map=new HashMap<String,Object>();
		map.put("averprice", averprice);
		
		Calendar c=Calendar.getInstance();
		c=Calendar.getInstance();
		c.set(Calendar.DAY_OF_MONTH,0);
		c.add(Calendar.MONTH, -2);
		c.add(Calendar.DAY_OF_MONTH, 1);
		map.put("startTime", c.get(Calendar.YEAR)+"-"+(c.get(Calendar.MONTH)+1)+"-"+c.get(Calendar.DAY_OF_MONTH));
		
		List<Map<String,Double>> list=dataDao.getPriceFloat(map); 
		sr.setData(list);
		return sr;
	}
	
	/**
	 * 查询某商品近一月的售价浮动情况
	 * @return
	 */
	public ShopsResult getPriceDiff(Integer goodsType){
		ShopsResult sr=new ShopsResult(1,"查询成功");
		Double averprice=dataDao.getGoodsAverPrice();
		Map<String,Object> map=new HashMap<String,Object>();
		map.put("goodsName", goodsType==1?"猪肉":goodsType==2?"大米":"鸡蛋");
		map.put("averprice", averprice);
		List<Map<String,Object>> list=dataDao.getPriceDiff(map);
		sr.setData(list);
		return sr;
	}
	
	public ShopsResult queryTotalStatistics(){
		ShopsResult sr=new ShopsResult(1,"查询成功");
		Map<String,Object> map=dataDao.queryTotalStatistics();
		if(null==map||map.isEmpty()){
			map=new HashMap<String,Object>();
			map.put("count", "0");
			map.put("sum","0");
			map.put("aver","0");
		}
		sr.setData(map);
		return sr;
	}
	
	/**
	 * 小程序实时在线用户量
	 * @return
	 */
	public ShopsResult queryOnlineUserCount(){
		ShopsResult sr=new ShopsResult(1, "查询成功！");
		List<Map<String,Object>> list=dataDao.queryOnlineUserCount();
		if(null==list||list.isEmpty()){
			sr.setStatus(2);
			sr.setMsg("没有在线用户");
		}else{
			sr.setData(list);
		}
		return sr;
	}
	
	public ShopsResult queryOnlineUserCountNew(){
		ShopsResult sr=new ShopsResult(1, "查询成功");
		List<Map<String,Object>> list=dataDao.queryOnlineUserCountNew();
		
		if(null==list||list.isEmpty()){
			sr.setStatus(2);
			sr.setMsg("没有在线用户");
		}else{
			sr.setData(list);
		}
		return sr;
	}
	
	/**
	 * 线上线下订单量对比
	 * @return
	 */
	public ShopsResult queryLineCount(){
		ShopsResult sr=new ShopsResult(1,"查询成功！");
		List<Map<String,Object>> list=dataDao.queryLineCount();
		if(list==null||list.isEmpty()){
			
		}
		sr.setData(list);
		return sr;
	}
	
	public ShopsResult updateShopName(){
		ShopsResult sr=new ShopsResult();
		String[] names=new String[]{
				"王女士"
				,"李女士"
				,"二十一"
				,"张女士"
				,"刘女士"
				,"未签名"
				,"王先生"
				,"孙女士"
				,"王丽丽"
				,"张先生"
				,"刘海霞"
				,"李先生"
				,"刘先生"
				,"王艳艳"
				,"张丽丽"
				,"杨女士"
				,"陈女士"
				,"杜光瑞"
				,"李弈慧"
				,"张译文"
				,"赵女士"
				,"刘子涵"
				,"张丽华"
				,"张夏夏"
				,"王丽娜"
				,"黄秀娟"
				,"李春燕"
				,"徐女士"
				,"王成娟"
				,"刘丽丽"
				,"张晓丽"
				,"高女士"
				,"陈先生"
				,"张永娟"
				,"王丽萍"
				,"王玉霞"
				,"孙晓丽"
				,"李金凤"
				,"王晓璐"
				,"徐先生"
				,"赵先生"
				,"刘梦迪"
				,"刘美辰"
				,"颜晓萌"
				,"常晓华"
				,"于贤丞"
				,"李玉娟"
				,"王玉兰"
				,"郑艳艳"
				,"王一然"
				,"周女士"
				,"王晓丽"
				,"刘婷婷"
				,"徐倩倩"
				,"孙睿杰"
				,"刘长艳"
				,"王一鸣"
				,"王海燕"
				,"董自美"
				,"李雨轩"
				,"三等奖"
				,"王金凤"
				,"张苗苗"
				,"李晓丽"
				,"王子墨"
				,"李甜甜"
				,"王玉娟"
				,"王秀丽"
				,"杜女士"
				,"张琳琳"
				,"李学娟"
				,"刘海龙"
				,"于金金"
				,"姚京涛"
				,"王永胜"
				,"刘春霞"
				,"文永真"
				,"张腾月"
				,"王艳红"
				,"王美女"
				,"陈丽丽"
				,"付会玲"
				,"刘海燕"
				,"王世慧"
				,"王一博"
				,"梁者荣"
				,"徐海霞"
				,"王金霞"
				,"张婷婷"
				,"王甜甜"
				,"李慧芳"
				,"张秀霞"
				,"吴镇兆"
				,"刘晓丽"
				,"王丽娟"
				,"陆倩倩"
				,"孙玲玲"
				,"王晓艳"
				,"陈俞霖"
				,"朱秀娟"
				,"付艳花"
				,"王子涵"
				,"孙婷婷"
				,"徐子淇"
				,"安立华"
				,"张丽萍"
				,"刘一诺"
				,"杨先生"
				,"马俊杰"
				,"赵子旭"
				,"高丽丽"
				,"纪彦羽"
				,"王乐乐"
				,"刘玉双"
				,"王晓燕"
				,"公艺晓"
				,"王文文"
				,"葛瑞雪"
				,"孙晓凡"
				,"赵广君"
				,"虞培敏"
				,"谭付荣"
				,"李佳树"
				,"朱先生"
				,"杨子墨"
				,"李福艳"
				,"刘一然"
				,"王伟娜"
				,"李雨彤"
				,"张莹莹"
				,"解晓露"
				,"李丹丹"
				,"王艳玲"
				,"马红霞"
				,"王玲玲"
				,"王美玲"
				,"赵一诺"
				,"张梓墨"
				,"孙红艳"
				,"张文娜"
				,"孙玉梅"
				,"刘家豪"
				,"张晓燕"
				,"王艳霞"
				,"王秀梅"
				,"马丽娜"
				,"王淑云"
				,"王建英"
				,"郑丽丽"
				,"王婷婷"
				,"李红梅"
				,"刘建芳"
				,"王文静"
				,"王建霞"
				,"孙丽丽"
				,"孙晓云"
				,"刘莉莉"
				,"王洪英"
				,"李雪梅"
				,"徐丽丽"
				,"刘奕彤"
				,"张秀梅"
				,"王倩倩"
				,"王丽梅"
				,"李红霞"
				,"王志远"
				,"王晓华"
				,"李春娟"
				,"王彩云"
				,"王庆霞"
				,"王洪云"
				,"王玉美"
				,"丁先生"
				,"盖海妮"
				,"曹掂霞"
				,"季女士"
				,"李晓霞"
				,"王圆圆"
				,"张瑞花"
				,"王一诺"
				,"黄女士"
				,"张秀娟"
				,"李海燕"
				,"李兆云"
				,"王艳丽"
				,"战鸿儒"
				,"迟熙原"
				,"王洪敏"
				,"张兰英"
				,"张洪臣"
				,"冯女士"
				,"胡浩然"
				,"张美女"
				,"刘晶晶"
				,"赵文浩"
				,"张媛媛"
				,"王佳慧"
				,"石运华"
				,"马梓晨"
				,"薛安倩"
				,"李传彩"
				,"李秀玲"
				,"杨燕如"
				,"朱义芳"
				,"许馨元"
				,"徐莉莉"
				,"孟凡娟"
				,"马洪娟"
				,"王文英"
				,"张家豪"
				,"刘祥慧"
				,"姜浩泽"
				,"刘晴晴"
				,"彭德霞"
				,"宋昌昊"
				,"王梦琳"
				,"王秀云"
				,"王雪晴"
				,"周胜南"
				,"葛瑞英"
				,"毛洪举"
				,"葛祥菊"
				,"马西栋"
				,"邰士恒"
				,"周玉平"
				,"王媛媛"
				,"朱孔芝"
				,"杨金淑"
				,"姜兆萍"
				,"刘美婷"
				,"刘萌萌"
				,"付惠歆"
				,"赵瑞娟"
				,"王萍萍"
				,"徐浩天"
				,"刘美霞"
				,"王瑞娜"
				,"焦子滈"
				,"刑明娟"
				,"马静静"
				,"张浩楠"
				,"李淑慧"
				,"林祥娟"
				,"王子瑞"
				,"蒋丽萍"
				,"李昊泽"
				,"韩春秀"
				,"马彩云"
				,"王晓玲"
				,"徐静静"
				,"王娜娜"
				,"李若琳"
				,"郭姝含"
				,"刘传礼"
				,"王俊杰"
				,"王永全"
				,"孙恩秀"
				,"刘永丽"
				,"张馨月"
				,"燕静媛"
				,"曲喜燕"
				,"李淑艳"
				,"马丽萍"
				,"薛超隆"
				,"李金霞"
				,"张译心"
				,"秦女士"
				,"李秀珍"
				,"史雪丽"
				,"王耀雪"
				,"朱秀芝"
				,"郑清予"
				,"王晏欣"
				,"冯秀玲"
				,"王凯越"
				,"刘开贵"
				,"钱女士"
				,"刘子豪"
				,"王浩然"
				,"庄舒雅"
				,"无姓名"
				,"张秀丽"
				,"杨婧琪"
				,"朱文丽"
				,"朱萧雅"
				,"肖明洋"
				,"杨艳红"
				,"赵雨晴"
				,"张艳霞"
				,"庙育萁"
				,"王晨玮"
				,"杨青青"
				,"王德芳"
				,"潘春霞"
				,"陈佳妮"
				,"马丽丽"
				,"窦金美"
				,"郭朝光"
				,"马女士"
				,"田女士"
		};
		//获取全部店铺信息
		Map<String,Object> map=new HashMap<String,Object>();
		map.put("type", 3);
		List<Map<String,Object>> list=dataDao.getShopListMsg(map);
		int j=0;
		for(int i=0;i<list.size();i++,j++){
			if(j>=names.length){
				j=0;
			}
			list.get(i).put("manager_name", names[j]);
		}
		dataDao.updateShopName(list);
		return sr;
		
	}
}
