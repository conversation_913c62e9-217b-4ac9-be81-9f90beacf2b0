package org.haier.shop.service;

import org.haier.shop.util.PurResult;

public interface ShopCouponCashCountService {

	

	PurResult queryShopCashBalanceInfo(String shop_unique);

	PurResult queryShopOrderCountInfo(String shop_unique, String type);

	PurResult queryShopCashLog(String shop_unique, String startDate, String endDate,String cash_mode,String handle_status, Integer pageNum, Integer pageSize);

	PurResult queryOnlineOrderList(String shop_unique, String startDate, String endDate, Integer pageNum,
			Integer pageSize,Integer saleListHandleStatus);
}
