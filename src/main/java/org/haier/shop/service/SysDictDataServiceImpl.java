package org.haier.shop.service;

import java.util.List;

import javax.annotation.Resource;

import org.haier.shop.dao.SysDictDataDao;
import org.haier.shop.entity.publicEntity.SysDictData;
import org.haier.shop.util.ShopsResult;
import org.springframework.stereotype.Service;

/**
* @author: 作者:王恩龙
* @version: 2023年6月14日 下午3:28:25
*
*/
@Service
public class SysDictDataServiceImpl implements SysDictDataService{
	@Resource
	private SysDictDataDao sysDictDataDao;
	
	/**
	 * 查询数据字典的键值对
	 * @param dict_type
	 * @return
	 */
	public ShopsResult querySysDictDataList(String dict_type) {
		
		List<SysDictData> list = sysDictDataDao.querySysDictDataList(dict_type);
		
		return ShopsResult.success(list);
	}
}
