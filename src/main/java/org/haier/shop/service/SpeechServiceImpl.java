package org.haier.shop.service;

import org.haier.shop.dao.SpeechCmdDao;
import org.haier.shop.dao.SpeechListDao;
import org.haier.shop.entity.speech.SpeechCmdEntity;
import org.haier.shop.entity.speech.SpeechListEntity;
import org.haier.shop.params.speech.AddNewSpeechCmdParams;
import org.haier.shop.params.speech.QuerySpeechCmdListParams;
import org.haier.shop.params.speech.QuerySpeechListParams;
import org.haier.shop.result.speech.QuerySpeechListByParamResult;
import org.haier.shop.util.ShopsResult;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

@Service
public class SpeechServiceImpl implements SpeechService{
    @Resource
    private SpeechListDao speechListDao;
    @Resource
    private SpeechCmdDao speechCmdDao;


    /**
     * 新增语音指令
     * @return
     */
    public ShopsResult addNewSpeechCmd(AddNewSpeechCmdParams params) {
        //防重复校验
        QuerySpeechListParams querySpeechListParams = new QuerySpeechListParams();
        querySpeechListParams.setCmdSys(params.getCmdSys());

        //将指令存储
        SpeechCmdEntity speechCmdEntity = new SpeechCmdEntity();
        speechCmdEntity.setCmdSys(params.getCmdSys());
        if(speechCmdDao.querySpeechCmdEntity(speechCmdEntity) != null){
            return ShopsResult.fail("该指令已存在");
        }
        speechCmdEntity.setAppType(params.getAppType());
        speechCmdEntity.setCmdType(params.getCmdType());
        speechCmdEntity.setPageIndex(params.getPageIndex());
        speechCmdEntity.setCmdDescribe(params.getCmdDescribe());

        speechCmdEntity.setRemarks(params.getRemarks());

        speechCmdDao.addSpeechCmd(speechCmdEntity);
        return ShopsResult.success();
    }

    /**
     * 查询满足条件的语音列表
     * @param querySpeechListParams
     * @return
     */
    public ShopsResult querySpeechListByParam(QuerySpeechListParams querySpeechListParams) {
        List<QuerySpeechListByParamResult> speechList = speechListDao.querySpeechListByParam(querySpeechListParams);
        Integer count = speechListDao.querySpeechListCount(querySpeechListParams);
        return ShopsResult.pageOK(speechList,count);
    }

    /**
     * 查询语音指令列表
     * @param params
     * @return
     */
    public ShopsResult querySpeechCmdList(QuerySpeechCmdListParams params) {
        List<SpeechCmdEntity> speechCmdList = speechCmdDao.querySpeechCmdList(params);
        Integer count = speechCmdDao.querySpeechCmdListCount(params);
        return ShopsResult.pageOK(speechCmdList,count);
    }
}