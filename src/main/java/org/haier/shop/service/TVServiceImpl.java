package org.haier.shop.service;

import java.io.BufferedReader;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.math.BigInteger;
import java.net.HttpURLConnection;
import java.net.URL;
import java.net.URLConnection;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.text.DecimalFormat;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;

import org.apache.commons.codec.digest.DigestUtils;
import org.apache.http.Header;
import org.apache.http.HttpEntity;
import org.apache.http.HttpResponse;
import org.apache.http.NameValuePair;
import org.apache.http.client.ClientProtocolException;
import org.apache.http.client.HttpClient;
import org.apache.http.client.entity.UrlEncodedFormEntity;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.impl.client.DefaultHttpClient;
import org.apache.http.message.BasicNameValuePair;
import org.dom4j.Attribute;
import org.dom4j.Document;
import org.dom4j.DocumentException;
import org.dom4j.Element;
import org.dom4j.io.SAXReader;
import org.haier.shop.dao.TVDao;
import org.haier.shop.util.AreaCodeUtil;
import org.haier.shop.util.FTPConfig;
import org.haier.shop.util.JPushClientUtil;
import org.haier.shop.util.PurResult;
import org.haier.shop.util.SFTPUtil;
import org.haier.shop.util.ShopsUtil;
import org.haier.util.eshow.AreaVO;
import org.haier.util.eshow.Downdefault;
import org.haier.util.eshow.EshowListDetailVO;
import org.haier.util.eshow.EshowPlay;
import org.haier.util.eshow.Media;
import org.haier.util.eshow.PlayListDetail2VO;
import org.haier.util.eshow.PlayListDetailVO;
import org.haier.util.eshow.SubtitleVO;
import org.haier.util.eshow.TvModelVO;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.ui.Model;
import org.springframework.web.multipart.MultipartFile;

import com.alipay.api.domain.AreaCode;
import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import com.jcraft.jsch.SftpException;

import cn.hutool.core.lang.UUID;
import cn.hutool.crypto.symmetric.RC4;
import cn.jpush.api.push.PushResult;
import net.sf.json.JSONArray;
import net.sf.json.JSONObject;
import net.sf.json.JsonConfig;

@Service
public class TVServiceImpl implements TVService{
	@Resource
	private TVDao tvDao;
	@Resource
    private RedisTemplate<String, Object> redisTemplate;
	
	public PurResult queryAreaPalyList(String province_code,String city_code,String county_code,String start_date,String end_date) {
		PurResult result = new PurResult(1,"查询成功！");
		Map<String,Object> map = new HashMap<String, Object>();
		map.put("start_date", start_date);
		map.put("end_date", end_date);
		map.put("province_code", province_code);
		map.put("city_code", city_code);
		map.put("county_code", county_code);
		
		List<Map<String,Object>> list = tvDao.queryAreaPalyList(map);
		result.setData(list);
		return result;
	}
	
	public PurResult downYinong(String url,String title,String db_id,String eshow_id,
			String size,String md5,String type) {
		PurResult purResult = new PurResult(1,"开始下载文件");
		Downdefault data=tvDao.queryEshow();
		
		System.out.println("FTP信息："+data);
		if(null == data || data.getFtp() == null) {
			purResult.setStatus(0);
			purResult.setMsg("下载的源文件不存在");
			return purResult;
		}
		
		//根据当前的信息开启下载
		try {
			FtpUtils ftpUtilS = new FtpUtils(data.getFtp(), data.getUser(), data.getPass(), url, data.getSavePath(), title,
					db_id,eshow_id,title,size,url,md5,type,data.getPort()
					);
			ftpUtilS.run();
			
		}catch (Exception e) {
			e.printStackTrace();
			purResult.setData(0);
			purResult.setMsg("开启下载失败");
		}
		return purResult;
	}
	
	@Override
	public PurResult queryPeopleList(Map<String, Object> params) {
		PurResult result = new PurResult();
		try {
			List<Map<String ,Object>> list = tvDao.queryPeopleList(params);
	    	Integer count = tvDao.queryPeopleListCount(params);
	    	result.setStatus(1);
			result.setMsg("成功");
			result.setCount(count);
			result.setData(list);
		} catch (Exception e) {
			e.printStackTrace();
			result.setStatus(0);
			result.setMsg("异常");
		}
		return result;
	}
	
	@Override
	public PurResult queryTVList(Map<String, Object> params) {
		PurResult result = new PurResult();
		try {
			
			/*Map<String, String> extra=new HashMap<String, String>();
			extra.put("type", "6");
			JiguangPushTV.pushAll("在线查询","TV",extra);*/
			
			List<Map<String ,Object>> list = tvDao.queryTVList(params);
	    	Integer count = tvDao.queryTVListCount(params);
	    	result.setStatus(1);
			result.setMsg("成功");
			result.setCount(count);
			result.setData(list);
		} catch (Exception e) {
			e.printStackTrace();
			result.setStatus(0);
			result.setMsg("异常");
		}
		return result;
	}
	/**
	 * 控制电视 1. 关机 2 重启 3截图  4
	 */
	@Override
	public PushResult pushTVMessage(Map<String, String> map) throws Exception {
		PushResult result = new PushResult();
		try {
		List<String> list=new ArrayList<String>();
		list.add(map.get("mac").toString());
		
		// result=JiguangPushTV.push(list,"TV","电视",map);
		 result=JPushClientUtil.notifyTV(map.get("mac").toString(),"TV",map);
		
		} catch (Exception e) {
			e.printStackTrace();
		}
		return result;
	}
	/**
	 * 控制电视 6
	 */
	@Override
	public PushResult pushTVMessageALL(Map<String, String> map) throws Exception {
		PushResult result = new PushResult();
		try {
			Integer count = tvDao.updateTVstatus();
		// result=JiguangPushTV.push(list,"TV","电视",map);
		// result=JiguangPushTV.pushAll("设备在线查询!","TV",map);
		
		} catch (Exception e) {
			e.printStackTrace();
		}
		return result;
	}

	@Override
	public PurResult updateRegistrationId(Map<String, Object> map) throws Exception {
		// TODO Auto-generated method stub
		PurResult result = new PurResult();
		int k=tvDao.updateRegistrationId(map);
		if(k==0){
			result.setStatus(0);
			result.setMsg("更新失败！");
			return result;
		}
		result.setStatus(1);
		result.setMsg("保存成功！");
		return result;
	}

	@Override
	public PurResult queryAllShops() {
		PurResult result = new PurResult();
		try {
			List<Map<String ,Object>> list = tvDao.queryAllShops();
	    	result.setStatus(1);
			result.setMsg("成功");
			result.setData(list);
		} catch (Exception e) {
			e.printStackTrace();
			result.setStatus(0);
			result.setMsg("异常");
		}
		return result;
	}

	@Override
	public PurResult addAdPeople(Map<String, Object> params) {
		PurResult result = new PurResult();
		try {
			tvDao.addAdPeople(params);
	    	result.setStatus(1);
			result.setMsg("成功");
		} catch (Exception e) {
			e.printStackTrace();
			result.setStatus(0);
			result.setMsg("异常");
		}
		return result;
	}

	@Override
	public PurResult queryAdPeopleDetail(Map<String, Object> params) {
		PurResult result = new PurResult();
		try {
			Map<String ,Object> data = tvDao.queryAdPeopleDetail(params);
	    	result.setStatus(1);
			result.setMsg("成功");
			result.setData(data);
		} catch (Exception e) {
			e.printStackTrace();
			result.setStatus(0);
			result.setMsg("异常");
		}
		return result;
	}

	@Override
	public PurResult updateAdPeople(Map<String, Object> params) {
		PurResult result = new PurResult();
		try {
			tvDao.updateAdPeople(params);
	    	result.setStatus(1);
			result.setMsg("成功");
		} catch (Exception e) {
			e.printStackTrace();
			result.setStatus(0);
			result.setMsg("异常");
		}
		return result;
	}

	@Override
	public PurResult deleteAdPeople(Map<String, Object> params) {
		PurResult result = new PurResult();
		try {
			tvDao.updateAdPeopleInfo(params);
	    	result.setStatus(1);
			result.setMsg("成功");
		} catch (Exception e) {
			e.printStackTrace();
			result.setStatus(0);
			result.setMsg("异常");
		}
		return result;
	}

	@Override
	public PurResult deleteTV(String id) throws Exception {
		// TODO Auto-generated method stub
		PurResult result = new PurResult();
		int k=tvDao.deleteTV(id);
		if(k==0){
			result.setStatus(0);
			result.setMsg("操作失败！");
			return result;
		}
		result.setStatus(1);
		result.setMsg("操作成功！");
		return result;
	}

	@Override
	public PurResult updateTV(Map<String, Object> map) throws Exception {
		// TODO Auto-generated method stub
		PurResult result = new PurResult();
		int k=tvDao.updateTV(map);
		if(k==0){
			result.setStatus(0);
			result.setMsg("操作失败！");
			return result;
		}
		result.setStatus(1);
		result.setMsg("操作成功！");
		return result;
	}

	@Override
	public Map<String, Object> getTV(String id) throws Exception {
		// TODO Auto-generated method stub
		return tvDao.getTV(id);
	}

	@Override
	public PurResult queryShopAreaList(Map<String, Object> params) {
		PurResult result = new PurResult();
		try {
			List<Map<String ,Object>> list = tvDao.queryShopAreaList(params);
	    	result.setStatus(1);
			result.setMsg("成功");
			result.setData(list);
		} catch (Exception e) {
			e.printStackTrace();
			result.setStatus(0);
			result.setMsg("异常");
		}
		return result;
	}

	@Override
	public PurResult updateLockStatus(Map<String, Object> params) {
		PurResult result = new PurResult();
		int k=tvDao.updateLockStatus(params);
		if(k==0){
			result.setStatus(0);
			result.setMsg("操作失败！");
			return result;
		}
		result.setStatus(1);
		result.setMsg("操作成功！");
		return result;
	}

	@Override
	public PurResult quetyMedia(String countyCody) {
		PurResult result = new PurResult();
		// TODO Auto-generated method stub
		Downdefault data=tvDao.queryEshow();
		List<Media> dataList=new ArrayList<>();
		try {
			String tvDowload=tvDao.getUploadRoute("download");
			if(null == countyCody) {countyCody = "370523";};
//			String tvDowload = FtpUtilS.preEshowUrl+countyCody + FtpUtilS.afterEshowUrl;
			String downdefault = FtpUtils.preEshowUrl+countyCody + FtpUtils.afterEshowDownDefaultUrl;
			String download = FtpUtils.preEshowUrl + countyCody + FtpUtils.afterEshowUrl;
			String downpriority = FtpUtils.preEshowUrl + countyCody + FtpUtils.afterEshowDownpriorityUrl;
			List<Media> dataList1=quetyMedia(data,downdefault,1,"http://"+tvDowload+"/"+"upload/yinong/");
			dataList.addAll(dataList1);
			List<Media> dataList2=quetyMedia(data,download,2,"http://"+tvDowload+"/"+"upload/yinong/");
			dataList.addAll(dataList2);
			List<Media> dataList3=quetyMedia(data,downpriority,3,"http://"+tvDowload+"/"+"upload/yinong/");
			dataList.addAll(dataList3);
			
			result.setStatus(1);
			result.setMsg("成功");
			result.setData(dataList);
		} catch (IOException e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		} catch (DocumentException e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		} catch (SftpException e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		}
		
		return result;
	}
	
	
	public List<Media> quetyMedia(Downdefault data,String path,int num,String tvDowload) throws IOException, DocumentException, SftpException
	{
		URL url = null;
		url = new URL(path);
		StringBuilder sb=new StringBuilder();
        BufferedReader reader2 = new BufferedReader(new InputStreamReader(url.openStream()));
        String s;
        while ((s = reader2.readLine()) != null) {
        	System.out.println(s.toString());
        	sb.append(s);
        }
        reader2.close();
        InputStream is = new ByteArrayInputStream(sb.toString().getBytes());
        SAXReader sb2 = new SAXReader();
	    Document document = (Document) sb2.read(is);
        Element rootElement =document.getRootElement();
//        List<Element> eleList = rootElement.elements();
//        for (int i = 0; i < eleList.size(); i++) {
//        	System.out.println(eleList.get(i).attributes());
//        	List<Attribute> ll = eleList.get(i).attributes();
//        	for(Attribute a : ll) {
//        		System.out.println(a.getName());
//        		System.out.println(a.getValue());
//        	}
//		}
//        List<Attribute> attributes0 = rootElement.attributes();
        Downdefault play=new Downdefault();
        List<Media> mediaList=new ArrayList<>();
        //System.out.println("======获取属性值======");
    /*    for (Attribute attribute : attributes0) {
        	System.out.print(attribute.getName()+":"+attribute.getValue());
        }*/
        Iterator iterator = rootElement.elementIterator();
        while (iterator.hasNext()){
        	Element stu = (Element) iterator.next();
        	List<Attribute> attributes = stu.attributes();
        	for (Attribute attribute : attributes) {
        		
             	if(attribute.getName().endsWith("ftp"))
             	{
             		 String fileName = attribute.getValue().substring(attribute.getValue().lastIndexOf("/"));
             		play.setFtp(fileName);
             	}else if(attribute.getName().endsWith("port"))
             	{
             		play.setPort(Integer.parseInt(attribute.getValue()));
             	}else if(attribute.getName().endsWith("user"))
             	{
             		play.setUser(attribute.getValue());
             	}else if(attribute.getName().endsWith("pass"))
             	{
             		play.setPass(attribute.getValue());
             	}
        	 }
        	 
             Iterator iterator22 = stu.elementIterator();
             while (iterator22.hasNext()){
            	 Media media=new Media();
             	Element stuchild = (Element) iterator22.next();
             	 List<Attribute> attributesChild = stuchild.attributes();
             	 for (Attribute attribute : attributesChild) {
             		 if(attribute.getName().endsWith("db_id"))
                 	{
             			media.setDb_id(Integer.parseInt(attribute.getValue()));
                 	}else if(attribute.getName().endsWith("id"))
                 	{
                 		media.setId(Integer.parseInt(attribute.getValue()));
                 	}else if(attribute.getName().endsWith("title"))
                 	{
                 		String fileName = attribute.getValue().substring(attribute.getValue().lastIndexOf(".")+1);
                 		if(fileName.equals("xml"))
                 		{
                 			media.setTitle(attribute.getValue()+".source");
                 			media.setLocal_address(tvDowload+attribute.getValue()+".source");
                 		}else
                 		{
                 			media.setTitle(attribute.getValue());
                 			media.setLocal_address(tvDowload+attribute.getValue());
                 		}
                 		
                 	}else if(attribute.getName().endsWith("size"))
                 	{
                 		media.setSize(attribute.getValue());
                 	}else if(attribute.getName().endsWith("url"))
                 	{
                 		//String fileName = attribute.getValue().substring(attribute.getValue().lastIndexOf("."));
                 		String pass = attribute.getValue().substring(0,attribute.getValue().lastIndexOf("/"));
                 		media.setUrl(pass);
                 	}else if(attribute.getName().endsWith("md5"))
                 	{
                 		media.setMd5(attribute.getValue());
                 	}
             		media.setType(num);
             	 }
             	mediaList.add(media);
             }
        }
        play.setMediaList(mediaList);
        for(Media media:mediaList)
        {
        	String twx = media.getUrl().substring(media.getUrl().lastIndexOf(".")+1);
        	
        	if(media.getMd5()!=null&&!media.getMd5().equals(""))
        	{
        		//判断文件是否存在
        		int count=tvDao.queryEshowList(media.getMd5());
        		//判断数据库是否存在
	        	if(count==0)
	        	{
	        		//判断文件是否存在
	        		File file = new File(data.getSavePath()+"/"+media.getTitle());
	        		boolean flag=false;
	        		if (file.exists()) {
	        			if(Long.parseLong(media.getSize()) > file.length()) {
	        				flag=false;
	        				media.setRateOfProgress(file.length()*100.0/Long.parseLong(media.getSize()));
	        				media.setStatus(3);//文件下载中
	        			}else {
	        				System.out.println("默认广告:"+media+"==已经存在1111");
	        				media.setStatus(1);
	        				media.setRateOfProgress(100.0);
	        			}
	        		} else
	        		{
	        			//flag=FtpUtil2.downloadFile(data.getFtp(),data.getPort(), data.getUser(),data.getPass(),media.getUrl(),media.getTitle(),data.getSavePath());
	        			//System.out.println("下载默认广告:"+media+"===="+flag);
	        			media.setStatus(2);
	        			media.setRateOfProgress(0.0);
	        		}
	        		
	        	}else
	        	{
	        		media.setStatus(1);
	        		media.setRateOfProgress(100.0);
	        	}
	        	
        	}
        }
		return mediaList;

	}

	@Override
	public PurResult querySourceList(Map<String, Object> params) {
		PurResult result = new PurResult();
			
			List<Map<String ,Object>> list = tvDao.querySourceList(params);
	    	Integer count = tvDao.querySourceListCount(params);
	    	result.setStatus(1);
			result.setMsg("成功");
			result.setCount(count);
			result.setData(list);
			return result;
	}


	@Override
	public List<AreaVO> queryAreaList(Map<String, Object> params) {
		// TODO Auto-generated method stub
		return tvDao.queryAreaList(params);
	}

	@Override
	public List<TvModelVO> queryModelList(Map<String, Object> params) {
		// TODO Auto-generated method stub
		return tvDao.queryModelList(params);
	}

	@Override
	public PurResult addArea(AreaVO params) {
		PurResult result = new PurResult();
		//查询全国区域是否存在
		String area_dict_num=tvDao.queryAreaDict(params);
		if(area_dict_num=="" ||area_dict_num==null || area_dict_num.equals(""))
		{
			result.setStatus(0);
			result.setMsg("区域不存在，请联系管理员");
		}else
		{
			params.setArea_dict_num(area_dict_num);
			int count=tvDao.queryArea(params);
			
			if(count>0)
			{
				result.setStatus(0);
				result.setMsg("区域已存在");
			}else
			{
				
				if(params.getLevel().equals("1"))
				{
					params.setArea_dict_parent_num("-1");
				}
				tvDao.addArea(params);
				result.setStatus(1);
			}
			
		}
		return result;
	}

	@Override
	public PurResult addTVSource(String title, String url, String source_type, String lai_yuan, String content,
			HttpServletRequest request) throws Exception {
		Map<String, Object> params=new HashMap<>();
		if("1".equals(source_type)||"3".equals(source_type)){
		MultipartFile file=null;
		file=ShopsUtil.testMulRequest(request, "imgFile");
		if(file!=null){
			String orName=file.getOriginalFilename();//获取文件原名称
			String lastName=orName.substring(orName.lastIndexOf("."));
			String newName=UUID.randomUUID()+lastName;
			String filePathDetail="/upload/yinong";
	        InputStream is = file.getInputStream();
	    	
			MultipartFile file2=ShopsUtil.testMulRequest(request, "imgFile");
			InputStream is2 = file2.getInputStream();
			String md5=getMD5Three(is2);
			params.put("md5", md5);
	        SFTPUtil sftp = new SFTPUtil(FTPConfig.tv_username, FTPConfig.tv_password, FTPConfig.tv_host, FTPConfig.tv_port);   
	        sftp.login();
	        boolean flag=sftp.upload(FTPConfig.tv_file_path, newName, is); 
	        sftp.logout();
			if(flag){
				params.put("url", "http://ad.buyhoo.cc"+filePathDetail+"/"+newName);
			}
			params.put("size", file.getSize());
		


		} 	
		}else if("4".equals(source_type)){
			params.put("url", url);
		}else if("2".equals(source_type)){
			params.put("content", content);
		}
		params.put("title", title);
		params.put("source_type", source_type);
		params.put("lai_yuan", lai_yuan);
		System.out.println(params.toString());
		tvDao.addTVSource(params);
		PurResult result=new PurResult();
		result.setStatus(1);
		return result;
	}
	public static String getMD5Three(InputStream fis) {
        BigInteger bi = null;
        try {
            byte[] buffer = new byte[8192];
            int len = 0;
            MessageDigest md = MessageDigest.getInstance("MD5");
           // File f = new File(path);
           // FileInputStream fis = new FileInputStream(f);
            while ((len = fis.read(buffer)) != -1) {
                md.update(buffer, 0, len);
            }
            fis.close();
            byte[] b = md.digest();
            bi = new BigInteger(1, b);
        } catch (NoSuchAlgorithmException e) {
            e.printStackTrace();
        } catch (IOException e) {
            e.printStackTrace();
        }
        return bi.toString(16);
    }
	
	
	public static String GetFileSize(long fileS){
	    String size = ""; 
	    DecimalFormat df = new DecimalFormat("#.00"); 
	    if (fileS < 1024) {
               size = df.format((double) fileS) + "BT";
        } else if (fileS < 1048576) {
               size = df.format((double) fileS / 1024) + "KB";
        } else if (fileS < 1073741824) {
               size = df.format((double) fileS / 1048576) + "MB";
        } else {
    		   size = df.format((double) fileS / 1073741824) +"GB";
        }
	    return size;
	   }

	@Override
	public PurResult deleteTVSource(String id) {
		PurResult result = new PurResult();
		int k=tvDao.deleteTVSource(id);
		if(k==0){
			result.setStatus(0);
			result.setMsg("操作失败！");
			return result;
		}
		result.setStatus(1);
		result.setMsg("操作成功！");
		return result;
	}

	@Override
	public PurResult updateLockStatusSource(Map<String, Object> params) {
		PurResult result = new PurResult();
		int k=tvDao.updateLockStatusSource(params);
		if(k==0){
			result.setStatus(0);
			result.setMsg("操作失败！");
			return result;
		}
		result.setStatus(1);
		result.setMsg("操作成功！");
		return result;
	}

	@Override
	public PurResult queryPalyList(Map<String, Object> map) {
		PurResult result = new PurResult();
		try {
			List<Map<String ,Object>> list = tvDao.queryPalyList(map);
	    	Integer count = tvDao.queryPalyCount(map);
	    	result.setStatus(1);
			result.setMsg("成功");
			result.setCount(count);
			result.setData(list);
		} catch (Exception e) {
			e.printStackTrace();
			result.setStatus(0);
			result.setMsg("异常");
		}
		return result;
	}

	@Override
	public PurResult queryAllSource() {
		PurResult result = new PurResult();
		try {
			List<Map<String ,Object>> list = tvDao.queryAllSource();
	    	result.setStatus(1);
			result.setMsg("成功");
			result.setData(list);
		} catch (Exception e) {
			e.printStackTrace();
			result.setStatus(0);
			result.setMsg("异常");
		}
		return result;
	}

	@Override
	public List<Map<String, Object>> queryAllAreaList() {
		return 	tvDao.queryAllAreaList();
	}

	@Override
	public List<Map<String, Object>> queryAllCityList() {
		return 	tvDao.queryAllCityList();
	}

	@Override
	public PurResult addAreaPlay(String title, String play_type, String source_id, String areaJson,String type) {
		PurResult result=new PurResult();
		result.setStatus(1);
		Long play_id=0l;
		Map<String, Object> params=new HashMap<>();
		params.put("title", title);
		params.put("play_type", play_type);
		params.put("type", type);
		params.put("model", 1);
		params.put("play_id", play_id);
		tvDao.addAreaPlay(params);
		String [] source_ids= source_id.replaceAll("\"", "").split(",");
		for (String v : source_ids) {
			Map<String, Object> play_params=new HashMap<>();
			play_params.put("play_id", params.get("play_id"));
			play_params.put("source_id", v);
			tvDao.addAreaPlaySource(play_params);
		}
		
		JSONArray array= JSONArray.fromObject(areaJson);
		
		for (int i = 0; i < array.size(); i++) {
			JSONObject temp = (JSONObject) array.get(i); 
			String area_dict_num= temp.getString("county");
			Map<String ,Object> area_params = new HashMap<String, Object>();
			area_params.put("area_dict_num", area_dict_num);
			area_params.put("play_id", params.get("play_id"));
			tvDao.addAreaPlaySourceQuYu(area_params);

		}
		return result;
	}

	@Override
	public PurResult updatePlayStatus(Map<String, Object> params) {
		PurResult result = new PurResult();
		int k=tvDao.updatePlayStatus(params);
		if(k==0){
			result.setStatus(0);
			result.setMsg("操作失败！");
			return result;
		}
		result.setStatus(1);
		result.setMsg("操作成功！");
		return result;
	}
	
	@Override
	public PurResult updateTVTiming(Map<String, Object> params) {
		PurResult result = new PurResult();
		int k=tvDao.updateTVTiming(params);
		if(k==0){
			result.setStatus(0);
			result.setMsg("操作失败！");
			return result;
		}
		result.setStatus(1);
		result.setMsg("操作成功！");
		return result;
	}

	@Override
	public PurResult deleteAreaPlay(String id) {
		PurResult result = new PurResult();
		int k=tvDao.deleteAreaPlay(id);
		tvDao.deletePlaySuCai(id);
		tvDao.deletePlayQuYu(id);
		if(k==0){
			result.setStatus(0);
			result.setMsg("操作失败！");
			return result;
		}
		result.setStatus(1);
		result.setMsg("操作成功！");
		return result;
	}

	@Override
	public void queryPlayInfo(String id, Model model) {
		Map<String, Object> params=new HashMap<>();
		params.put("id", id);
		Map<String, Object> play= tvDao.queryPlayInfo(params);
		model.addAttribute("play", play);
		String [] source_list= tvDao.queryPlaySourceList(params);
		String str=String.join(",",source_list);// (Java8) 使用join方法链接字符串
		model.addAttribute("source_list", str);
		String[] quyu_list= tvDao.queryPlayQuYuList(params);
		String str2=String.join(",",quyu_list);// (Java8) 使用join方法链接字符串
		model.addAttribute("quyu_list", str2);

		
	}

	@Override
	public PurResult editAreaPlay(String title, String play_type, String source_id, String areaJson, String id,String type) {
		PurResult result=new PurResult();
		result.setStatus(1);
		Map<String, Object> params=new HashMap<>();
		params.put("title", title);
		params.put("play_type", play_type);
		params.put("type", type);
		params.put("model", 1);
		params.put("play_id", id);
		tvDao.updateAreaPlay(params);
		tvDao.deletePlaySuCai(id);
		tvDao.deletePlayQuYu(id);
		String [] source_ids= source_id.replaceAll("\"", "").split(",");
		for (String v : source_ids) {
			Map<String, Object> play_params=new HashMap<>();
			play_params.put("play_id", params.get("play_id"));
			play_params.put("source_id", v);
			tvDao.addAreaPlaySource(play_params);
		}
		
		JSONArray array= JSONArray.fromObject(areaJson);
		
		for (int i = 0; i < array.size(); i++) {
			JSONObject temp = (JSONObject) array.get(i); 
			String area_dict_num= temp.getString("county");
			Map<String ,Object> area_params = new HashMap<String, Object>();
			area_params.put("area_dict_num", area_dict_num);
			area_params.put("play_id", params.get("play_id"));
			tvDao.addAreaPlaySourceQuYu(area_params);

		}
		return result;
	}

	@Override
	@Transactional
	public PurResult addAreaCode(String city_code) {
	/*	String area_dict_num=city_code;
		List<Map<String,Object>> list=new ArrayList<>();
		for (int i = 0; i < code_count; i++) {
			Map<String, Object> params=new HashMap<>();
			params.put("area_dict_num", area_dict_num);
			params.put("code_count", code_count);
			String code =AreaCodeUtil.generateNewCode(Integer.parseInt(area_dict_num),10);
			params.put("area_code", code);
			list.add(params);
		}
		tvDao.addAreaCode(list);
		Map<String, Object> params=new HashMap<>();
		params.put("area_dict_num", Integer.parseInt(area_dict_num));
		params.put("code_count", code_count);
		tvDao.updateAreaCount(params);*/
		//查询未生成激活码的手机号
		List<Map<String,Object>> list =tvDao.queryNotAreaCodePhone();
		for (Map<String, Object> params : list) {
			String code =AreaCodeUtil.generateNewCode(Integer.parseInt(city_code),10);
			params.put("area_code", code);
			tvDao.updateAreadCodeById(params);
		}
		PurResult result=new PurResult(1,"");
		return result;
	}

	@Override
	public PurResult queryEshowPlayList(String id) {
		PurResult result=new PurResult(1,"");
		//1.模拟登陆获取PHPSESSION--POST
		String phpSession=null;
		String county=id;
		id="Eshow"+id;
		try {
			phpSession=(String) redisTemplate.opsForValue().get(id);
			if(phpSession==null||phpSession.equals("")){
				phpSession = getPHPSESSION(county,"123456");
				phpSession=phpSession.substring(0, phpSession.indexOf(";"));	
				System.out.println("PHPSESSION:"+phpSession);
				redisTemplate.opsForValue().set(id, phpSession);
				redisTemplate.expire(id, 300, TimeUnit.SECONDS);
			}else
			{
		    System.out.print("缓存PHPsession...............");
			}
			//phpSession=phpSession.substring(phpSession.indexOf("=")+1, phpSession.indexOf(";"));		
			//2.获取播放模板列表GET
			String data=postData("http://tcldsfb.ny360.cn:8088/index.php?r=group/playtask&_dc=16027553619538&node=root","",phpSession);
			com.alibaba.fastjson.JSONObject jsonObject = com.alibaba.fastjson.JSONObject.parseObject(data);
			String children=jsonObject.getString("children");
			com.alibaba.fastjson.JSONObject children2 = com.alibaba.fastjson.JSONObject.parseObject(children);
			System.out.println("result:"+children2.get("children"));
			result.setData(children2.get("children"));
			result.setCount(100);
				
		} catch (ClientProtocolException e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		} catch (IOException e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		}
		return result;
	}
	
	//1.模拟登陆获取PHPSESSION
	public static  String getPHPSESSION(String username,String password) throws ClientProtocolException, IOException
	{
		
	HttpClient httpclient = new DefaultHttpClient();
    //设置登录参数  
    List<NameValuePair> formparams = new ArrayList<NameValuePair>();  
    formparams.add(new BasicNameValuePair("LoginForm[username]", username));  //201400814122
    formparams.add(new BasicNameValuePair("LoginForm[password]", password));  //xiaolong74074
    UrlEncodedFormEntity entity1 = new UrlEncodedFormEntity(formparams);  
    //新建Http  post请求  
    HttpPost httppost = new HttpPost("http://tcldsfb.ny360.cn:8088/index.php?r=site/login");  
    httppost.setEntity(entity1);  	

    //处理请求，得到响应  
    HttpResponse response = httpclient.execute(httppost);  
    Header[] map =  response.getAllHeaders();
   for (Header entry : map)
   {
       System.out.println("Key : " + entry.getName() + " ,Value : " + entry.getValue());
       if(entry.getValue().startsWith("PHPSESSID"))
       {
    	  // System.out.println(entry.getValue());  
    	   return entry.getValue();
       }
   }
  
	return null;
	}
	
    public static String postData(String urlStr, String data, String cookie){  
        BufferedReader reader = null;  
        try {  
            URL url = new URL(urlStr);  
            URLConnection conn = url.openConnection();  
            conn.setRequestProperty("cookie", cookie);
            conn.setDoOutput(true);  
            conn.connect();      
            reader = new BufferedReader(new InputStreamReader(conn.getInputStream(),"utf-8"));  
            StringBuilder sb = new StringBuilder(); 
            
            String line = null;  
            while ((line = reader.readLine()) != null) {  
                sb.append(line);  
                sb.append("\r\n");  
            }  
            return sb.toString();  
        } catch (IOException e) {  
//            logger.error("Error connecting to " + urlStr + ": " + e.getMessage());  
            System.out.println(e.getMessage());
        } finally {  
            try {  
                if (reader != null)  
                    reader.close();  
            } catch (IOException e) {  
            }  
        }  
        return null;  
    }

	@Override
	public String toEshowPlayDetail(String id, String city_code) {
		String result2=null;
		String phpSession=null;
		String id2="Eshow"+city_code;
		try {
		phpSession=(String) redisTemplate.opsForValue().get(id2);
		if(phpSession==null||phpSession.equals("")){
			
				phpSession = getPHPSESSION(city_code,"123456");
				phpSession=phpSession.substring(0, phpSession.indexOf(";"));	
				System.out.println("PHPSESSION:"+phpSession);
				redisTemplate.opsForValue().set(id, phpSession);
				redisTemplate.expire(id, 300, TimeUnit.SECONDS);
		}else
		{
			System.out.println("使用缓存PHPsession:"+phpSession);
		}
		 result2=EshowPlay.postData("http://tcldsfb.ny360.cn:8088/index.php?r=playTask/contentList&_dc=1602769686068&page=1&start=0&limit=25&id="+id,"",phpSession);
		} catch (ClientProtocolException e) {
			e.printStackTrace();
		} catch (IOException e) {
			e.printStackTrace();
		}
		 return result2;
	}

	@Override
	public PurResult queryEshowPlayListDeatil(String id,String city_code) {
		PurResult result=new PurResult(1,"");
		List<EshowListDetailVO> data=new ArrayList<EshowListDetailVO>();
		String phpSession=null;
		String id2="Eshow"+city_code;
		try {
		phpSession=(String) redisTemplate.opsForValue().get(id2);
       if(phpSession==null||phpSession.equals("")){
			
				phpSession = getPHPSESSION(city_code,"123456");
				
				phpSession=phpSession.substring(0, phpSession.indexOf(";"));	
				redisTemplate.opsForValue().set(id, phpSession);
				redisTemplate.expire(id, 300, TimeUnit.SECONDS);
			
		}else
		{
			System.out.println("使用缓存PHPsession:"+phpSession);
		}
		String result3=postData("http://tcldsfb.ny360.cn:8088/index.php?r=content/get&id="+id,"",phpSession);
	   com.alibaba.fastjson.JSONObject jsonObject3 = com.alibaba.fastjson.JSONObject.parseObject(result3);
	   String JsonData=(String) jsonObject3.get("JsonData");
	   com.alibaba.fastjson.JSONObject jsonObject4 = com.alibaba.fastjson.JSONObject.parseObject(JsonData);
	   System.out.println("JsonData  ："+jsonObject4.toString());
	   Object jsonObject5=jsonObject4.get("Data");
		//JSONArray jsonArray=JSONArray.fromObject(jsonObject5.toString());
		//List<PlayListDetailVO> items = JSONArray.toList(jsonArray, new PlayListDetailVO(), new JsonConfig());
		Gson gson = new Gson();
		List<PlayListDetailVO> items = gson.fromJson(jsonObject5.toString(), new TypeToken<List<PlayListDetailVO>>() {}.getType());//对于不是类的情况，用这个参数给出
		
		//查询我们素材，判断素材是否存在
		Map<String ,Object> params=new HashMap<String, Object>();
		params.put("page", 0);
		params.put("limit", 1000);
		
		List<Map<String ,Object>> sourceList = tvDao.querySourceList(params);
		
		for(PlayListDetailVO t:items)
		{
			List<EshowListDetailVO> data22=t.getElements();
			for(EshowListDetailVO e:data22)
			{
				int tt=0;
				//判断素材是否存在
				for(Map<String ,Object> source:sourceList)
				{
					if( e.getName().equals(source.get("title")))
					{
						tt=1;
					}
				}
				if(tt==0)
				{
					e.setIsHave(1);
				}else
				{
					e.setIsHave(2);
				}
				if(e.getDisplayType().equals("文本"))
				{
					
					String res=null;
					 URL url3 = new URL("http://36.99.46.138:8088/film"+e.getPath());
  		            HttpURLConnection conn = (HttpURLConnection)url3.openConnection();  
  		            //设置超时间为3秒
  		            conn.setConnectTimeout(3*1000);
  		            //防止屏蔽程序抓取而返回403错误
  		            conn.setRequestProperty("User-Agent", "Mozilla/4.0 (compatible; MSIE 5.0; Windows NT; DigExt)");
  		            //得到输入流
  		            InputStream inputStream = conn.getInputStream();  
  		            res = readInputStream(inputStream);
         	        e.setConent(res);
				}
				data.add(e);
			}
		}
		result.setRows(jsonObject4);
        System.out.print(data);
        result.setData(data);
        result.setCount(data.size());
		} catch (ClientProtocolException e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		} catch (IOException e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		}
		return result;
	}
    /**
     * 从输入流中获取字节数组
     * @param inputStream
     * @return
     * @throws IOException
     */
    public static  String readInputStream(InputStream inputStream) throws IOException {
        byte[] buffer = new byte[1024];
        int len = 0;
        ByteArrayOutputStream bos = new ByteArrayOutputStream();
        while((len = inputStream.read(buffer)) != -1) {
            bos.write(buffer, 0, len);
        }
        bos.close();
        return new String(bos.toByteArray(),"GBK");
    }
	@Override
	public List<Map<String, String>> queryOnlineTV() {
		return tvDao.queryOnlineTV();

	}
	
	@Override
	public PurResult queryEshowSubtitleList(String id) {
		PurResult result=new PurResult(1,"");
		//1.模拟登陆获取PHPSESSION--POST
		String phpSession=null;
		String county=id;
		id="Eshow"+id;
		try {
			phpSession=(String) redisTemplate.opsForValue().get(id);
			if(phpSession==null||phpSession.equals("")){
				phpSession = getPHPSESSION(county,"123456");
				phpSession=phpSession.substring(0, phpSession.indexOf(";"));	
				System.out.println("PHPSESSION:"+phpSession);
				redisTemplate.opsForValue().set(id, phpSession);
				redisTemplate.expire(id, 300, TimeUnit.SECONDS);
			}else
			{
		    System.out.print("缓存PHPsession...............");
			}
			//phpSession=phpSession.substring(phpSession.indexOf("=")+1, phpSession.indexOf(";"));		
			String data=postData("http://tcldsfb.ny360.cn:8088/index.php?r=subtitle/list&_dc=16027553619538","",phpSession);
			//2.获取跑马灯列表GET
			Gson gson = new Gson();
			List<SubtitleVO> items = gson.fromJson(data, new TypeToken<List<SubtitleVO>>() {}.getType());
			
			System.out.println(data);
			System.out.println(items);
			result.setData(items);
			result.setCount(100);
				
		} catch (ClientProtocolException e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		} catch (IOException e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		}
		return result;
	}

	@Override
	public Downdefault queryAdEshow() {
		// TODO Auto-generated method stub
		return tvDao.queryEshow();
	}
}
