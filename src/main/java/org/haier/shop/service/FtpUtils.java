package org.haier.shop.service;

import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.nio.channels.FileChannel;
import java.nio.channels.FileLock;
import java.util.HashMap;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import javax.servlet.http.HttpServletRequest;

import org.apache.commons.net.ftp.FTPClient;
import org.apache.commons.net.ftp.FTPFile;
import org.apache.commons.net.ftp.FTPReply;
import org.haier.shop.dao.TVDao;
import org.haier.util.eshow.Scpclient;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.context.WebApplicationContext;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;
import org.springframework.web.servlet.DispatcherServlet;


import org.slf4j.Logger;


/**
 * 
 * @param host:FTP服务器ID
 *
 */
public class FtpUtils implements  Runnable{
	private static final Logger logger = LoggerFactory.getLogger(FtpUtils.class);
	public FTPClient ftpClient = new FTPClient();
	private String host, // FTP服务器域名或IP
			username, // FTP登录帐号
			password, // FTP登录密码
			remotePath, // FTP服务器基础目录
			savePath, // FTP
			fileName;//文件名称
	
	private String db_id,
			eshow_id,
			title,
			size,
			url,
			md5,
			type
	;
	
	private Integer port;
	
	public final static String preEshowUrl = "http://tcldsfb.ny360.cn:8088/film/PlayList/";
	public final static String afterEshowUrl="/801/download.xml.source";
	public final static String afterEshowDownDefaultUrl = "/801/downdefault.xml.source";
	public final static String afterEshowDownpriorityUrl = "/801/downpriority.xml.source";
	
	@Autowired
	private TVDao tvDao;
	
	
			

	// 构造函数，用于初始化
	public FtpUtils(String host, String username, String password, String remotePath, String savePath, String fileName,
			Integer port) {
		super();
		this.host = host;
		this.username = username;
		this.password = password;
		this.remotePath = remotePath;
		this.savePath = savePath;
		this.fileName = fileName;
		this.port = port;
	}
	
	
	/**
	 * 
	 * @param host:服务器IP
	 * @param username：登录帐号
	 * @param password：登录密码
	 * @param remotePath：源文件目录
	 * @param savePath：保存文件地址
	 * @param fileName：文件名
	 * @param db_id：eshow db_id
	 * @param eshow_id：eshow eshow_id
	 * @param title：eshow title
	 * @param size：eshow 文件大小
	 * @param url:eshow url
	 * @param md5：eshow md5
	 * @param type：eshow type
	 * @param port：登录端口号
	 * @param tvDao
	 */
	public FtpUtils(String host, String username, String password, String remotePath, String savePath, String fileName,
			String db_id, String eshow_id, String title, String size, String url, String md5, String type, Integer port) {
		super();
		this.host = host;
		this.username = username;
		this.password = password;
		this.remotePath = remotePath;
		this.savePath = savePath;
		this.fileName = fileName;
		this.db_id = db_id;
		this.eshow_id = eshow_id;
		this.title = title;
		this.size = size;
		this.url = url;
		this.md5 = md5;
		this.type = type;
		this.port = port;
		ServletRequestAttributes sevletRequestAttributes = 
				(ServletRequestAttributes)RequestContextHolder.getRequestAttributes();
		HttpServletRequest request =  sevletRequestAttributes.getRequest();
		WebApplicationContext servletContext = (WebApplicationContext) 		
		request.getAttribute(DispatcherServlet.WEB_APPLICATION_CONTEXT_ATTRIBUTE);
		
		tvDao = servletContext.getBean("TVDao",TVDao.class);
	}



	//FTP下载
	/**
	 * 
	 * @param host
	 * @param port
	 * @param username
	 * @param password
	 * @param remotePath
	 * @param fileName
	 * @param savePath
	 * @return 返回下载状态：1、下载成功；2、登录返回码不合法；3、获取源文件信息失败，源文件不存在；4、文件下载未完成，需要重新下载
	 */
	public Integer downloadFile(String host, int port, String username, String password, String remotePath,
			String fileName, String savePath) {
		Integer result = 1;
		FTPClient ftp = new FTPClient();
		FileLock fl = null;
		try {
			int reply;
			ftp.setControlEncoding("UTF-8");
			ftp.connect(host, port);
			ftp.setFileType(FTPClient.BINARY_FILE_TYPE);//设置为二进制模式
			// 如果采用默认端口，可以使用ftp.connect(host)的方式直接连接FTP服务器
			ftp.login(username, password);// 登录
			reply = ftp.getReplyCode();
			//判断返回码是否合法，如果不合法，断开连接
			if (!FTPReply.isPositiveCompletion(reply)) {
				ftp.disconnect();
				return 2;
			}
			ftpClient.enterLocalPassiveMode(); 
			ftp.changeWorkingDirectory(remotePath);// 转移到FTP服务器目录
			FTPFile[] fs = ftp.listFiles();
			System.out.println("指定文件夹下的文件数量" + fs.length);
			for (FTPFile ff : fs) {
				System.out.println("当前文件的名字" + ff.getName());
				if (ff.getName().equals(fileName)) {
					File tempDir = new File(savePath);
					if(!tempDir.exists()) {
						tempDir.mkdirs();
					}
					File localFile = new File(savePath + "/" + ff.getName());
					System.out.println(localFile.exists());
					//如果本地文件不存在，直接创建新的文件并进行下载
					if (!localFile.exists()) {
						localFile.createNewFile();
						OutputStream is = new FileOutputStream(localFile);
						ftp.retrieveFile(ff.getName(), is);
						is.close();
						result = 6;
					} else {
						//如果本地文件已经存在，则继续下载
						Long sourceSize = ff.getSize();
						Long localSize = localFile.length();
						System.out.println("源文件大小"+sourceSize);
						System.out.println("本地文件大小"+localSize);
						if (localSize >= sourceSize) {
							System.out.println(ff.getName() + "已下载完成");
							return 1;
						} else {
							//此处先判断文件是否加锁，防止重复写入操作
							
							
							try {
								FileChannel channel = new FileOutputStream(savePath + "/" + ff.getName(),true).getChannel();
								fl = channel.lock(0, localFile.length(), true);
							}catch (Exception e) {
								e.printStackTrace();
								return 5;
							}
							
							// 开启断点续传功能
							System.out.println("开启断点续传,起始位置:"+localSize);
							FileOutputStream out = new FileOutputStream(localFile, true);
							ftp.setRestartOffset(localSize);
							System.out.println(remotePath+File.separator + fileName);
							System.out.println(new String(remotePath.getBytes("UTF8"),"ISO-8859-1")+File.separator+new String(fileName.getBytes("UTF8"),"ISO-8859-1"));
							System.out.println(new String(remotePath.getBytes("GBK"),"ISO-8859-1")+File.separator+new String(fileName.getBytes("GBK"),"ISO-8859-1"));
							System.out.println(encode(remotePath+File.separator+fileName, "ISO-8859-1"));
//							String url = new String((remotePath+File.separator+fileName).getBytes("UTF-8"),"ISO-8859-1");
							String url = ff.getName();
							System.out.println(new String(url.getBytes("GBK"),"ISO-8859-1"));
							System.out.println(new String(url.getBytes("UTF-8"),"ISO-8859-1"));
							System.out.println(new String(url.getBytes("ISO-8859-1"),"ISO-8859-1"));
//							InputStream in = ftp.retrieveFileStream(new String(url.getBytes("GBK"),"ISO-8859-1"));
//							InputStream in = ftp.retrieveFileStream(new String(url.getBytes("UTF-8"),"ISO-8859-1"));
							ftp.enterLocalPassiveMode(); 
							try {
								InputStream in = ftp.retrieveFileStream(new String(url.getBytes("UTF-8"),"ISO-8859-1"));
								System.out.println(in == null);
							}catch (Exception e) {
								System.out.println("UTF-8失败");
							}
							try {
								InputStream in = ftp.retrieveFileStream(new String(url.getBytes("GBK"),"ISO-8859-1"));
								System.out.println(in == null);
							} catch (Exception e) {
								System.out.println("GBK失败");
							}
							try {
								InputStream in = ftp.retrieveFileStream(new String(url.getBytes("ISO-8859-1"),"ISO-8859-1"));
								System.out.println(in == null);
							} catch (Exception e) {
								System.out.println("ISO-8859-1失败");
							}
							
							InputStream in = ftp.retrieveFileStream(url);
							byte[] bytes = new byte[1024];
							long step = sourceSize / 100;
							long process = localSize / step;
							Integer c = -1;
							if(null == in) {
								System.out.println("湖区偶文件失败!@");
								return 3;
							}
							while ((c = in.read(bytes)) != -1) {
								System.out.println(c);
								out.write(bytes, 0, c);
								localSize += c;
								long nowProcess = localSize / step;
								if (nowProcess > process) {
									process = nowProcess;
									if (process % 10 == 0) {
										System.out.println(ff.getName() + "当前下载进度" + process);
									}
								}
							}

							in.close();
							out.close();

							//存在异常时，释放
							if(fl != null) {
								fl.release();
								fl.close();
								fl = null;
							}
							boolean isDo = ftp.completePendingCommand();
							if (isDo) {
								result = 6;
							} else {
								result = 4;
							}
						}
					}
					break;
				}
			}

			ftp.logout();
		} catch (Exception e) {
			e.printStackTrace();
			result = 5;
		} finally {
			//不管最终操作结果如何，释放锁
			if(fl != null) {
				try {
					fl.release();
					fl.close();
					fl = null;
				} catch (IOException e) {
					e.printStackTrace();
				}
			}
			if (ftp.isConnected()) {
				try {
					ftp.disconnect();
				} catch (IOException ioe) {
				}
			}
		}
		return result;
	}
	
	public void run() {
		/*
		 * result状态说明：1、下载完成；2、登录返回状态不对；3、源文件不存在；4、下载未完成;5、下载失败（原因见打印日志）;6、新的下载并完成
		 */
		Integer result = this.downloadFile(host, port, username, password, remotePath, fileName, savePath);
//		Integer result = 6;
		System.out.println("下载结果：：："+result);
		switch (result) {
		case 2:
			logger.info("登录返回状态不对");
			break;
		case 3:
			logger.info("获取源文件失败");
			break;
		case 4:
			logger.info("文件下载未完成");
			break;
		case 5:
			logger.info("下载失败");
			break;
		case 6:
			logger.info("下载完成");
			//添加一条已完成的记录
			Map<String,Object> map = new HashMap<String,Object>();
			map.put("db_id", db_id);
			map.put("eshow_id", eshow_id);
			map.put("title", title);
			map.put("size", size);
			map.put("url", url);
			map.put("md5", md5);
			map.put("type", type);
			
			tvDao.addNewAdShowListMsg(map);
			map.put("url", "http://ad.buyhoo.cc/upload/yinong/"+fileName);
			if(fileName.endsWith(".jpg") || fileName.endsWith(".png") || fileName.endsWith(".jpeg")) {
				map.put("source_type", 1);
			}else if(fileName.equals(".mp4") || fileName.equals("flv")) {
				map.put("source_type", "3");
			}else if(fileName.equals(".txt") || fileName.endsWith(".xml")) {
				map.put("source_type", "2");
			}else {
				map.put("source_type", 4);
			}
			map.put("lai_yuan", 1);
			tvDao.addNewAdShowSourceList(map);
			
			String localFile = savePath.endsWith(File.separator)?savePath+fileName : savePath+File.separator+fileName;
			String romoteFile = savePath.endsWith(File.separator)?savePath : savePath+File.separator;
			String ftpip = "ftp.buyhoo.cc";
			Integer ftpport = 29022;
			String user = "root";
			String pwd = "p4J0iwoWXSh&#!p";
			boolean usePassword = true;
			String privateKey = "";
			//下载完成后，将文件信息转存到广告服务器
			Scpclient.putFile(localFile, romoteFile, ftpip, ftpport, user, pwd, usePassword, privateKey);
			break;
		default:
			break;
		}
	}
	
	public static String encode(String str, String charset)
            throws UnsupportedEncodingException {
        Pattern p = Pattern.compile("[\u4e00-\u9fa5]+");
        Matcher m = p.matcher(str);
        StringBuffer b = new StringBuffer();
        while (m.find()) {
            m.appendReplacement(b, URLEncoder.encode(m.group(0), charset));
        }
        m.appendTail(b);
        return b.toString();
    }
}
