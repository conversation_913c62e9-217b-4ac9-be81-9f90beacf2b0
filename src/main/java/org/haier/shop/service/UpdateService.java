package org.haier.shop.service;

import java.util.Map;

import org.haier.shop.entity.SqlPc;
import org.haier.shop.util.ShopsResult;

public interface UpdateService {
	
	/**
	 * 查询店铺总页数
	 * @param map
	 * @return
	 */
	public ShopsResult queryUpdatePageCount(Map<String,Object> map);
	
	/**
	 * 分页查询店铺升级信息
	 * @param map
	 * @return
	 */
	public ShopsResult queryUpdateMessageByPage(Map<String,Object> map);
	
	/**
	 * 添加新的店铺操作指令
	 * @return
	 */
	public ShopsResult operatingMachine(Map<String,Object> map,Integer operateType);
	
	/**
	 * 设置新版本
	 * @return
	 */
	public ShopsResult setNewEdition();
	
	/**
	 * 所有店铺的版本信息
	 * @return
	 */
	public ShopsResult queryVersionNumberList();
	/**
	 * 保存SQL信息
	 * @param sqlPc
	 * @return
	 */
	public ShopsResult saveSqlCmd(SqlPc sqlPc);
	
}
