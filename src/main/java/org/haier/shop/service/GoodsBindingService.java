package org.haier.shop.service;

import java.util.Map;

import org.haier.shop.util.PurResult;
import org.haier.shop.util.ShopsResult;

/**
 * 商品捆绑信息相关接口
 * <AUTHOR>
 */
public interface GoodsBindingService {
	
	/**
	 * 商品捆绑信息查询
	 * @param map
	 * @return
	 */
	public PurResult queryShopsBinding(Map<String,Object> map);
	
	/**
	 * 删除已有商品捆绑关系
	 * @param map
	 * @return
	 */
	public ShopsResult deleteBindingGoods(Map<String,Object> map);
	
	/**
	 * 修改商品捆绑消息
	 * @param map
	 * @return
	 */
	public ShopsResult modifyBinding(Map<String,Object> map);
	
	/**
	 * 添加商品的捆绑关系
	 * @param map
	 * @return
	 */
	public ShopsResult newBindingGoods(String goodsBarcodes,String goodsCounts,Long shopUnique,Double bindingTotal);
}
