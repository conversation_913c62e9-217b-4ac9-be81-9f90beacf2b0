package org.haier.shop.service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;

import org.haier.shop.dao.LoanDao;
import org.haier.shop.util.ShopsResult;
import org.springframework.stereotype.Service;

@Service
public class LoanServiceImpl implements LoanService{
	
	
	@Resource
	private LoanDao loanDao;
	
	public ShopsResult queryShopLoanList(String searchMsg , Integer page,Integer pageSize,Integer audit_status) {
		ShopsResult sr = new ShopsResult(1,"查询成功！");
		Map<String,Object> map = new HashMap<String,Object>();
		map.put("startNum", (page-1)*pageSize);
		map.put("pageSize", pageSize);
		map.put("searchMsg", searchMsg);
		map.put("auditStatus", audit_status == null ? 2 : audit_status);
		List<Map<String,Object>> shopList = loanDao.queryShopLoanList(map);
		sr.setData(shopList);
		sr.setCount(loanDao.queryShopLoanListCount(map));
		return sr;
	}
	public ShopsResult queryWindControl(String searchMsg , Integer page,Integer pageSize) {
		ShopsResult sr = new ShopsResult(1,"查询成功！");
		Map<String,Object> map = new HashMap<String,Object>();
		map.put("startNum", (page-1)*pageSize);
		map.put("pageSize", pageSize);
		map.put("searchMsg", searchMsg);
		List<Map<String,Object>> shopList = loanDao.queryWindControl(map);
		sr.setData(shopList);
		sr.setCount(loanDao.queryWindControlCount(map));
		return sr;
	}
	public ShopsResult queryWindControlDetail(String shop_unique , Integer page,Integer pageSize) {
		ShopsResult sr = new ShopsResult(1,"查询成功！");
		Map<String,Object> map = new HashMap<String,Object>();
		map.put("startNum", (page-1)*pageSize);
		map.put("pageSize", pageSize);
		map.put("shop_unique", shop_unique);
		List<Map<String,Object>> shopList = loanDao.queryWindControlDetail(map);
		sr.setData(shopList);
		sr.setCount(loanDao.queryWindControlDetailCount(map));
		return sr;
	}
	public ShopsResult queryWindControlDetail2(String shop_unique , Integer page,Integer pageSize) {
		ShopsResult sr = new ShopsResult(1,"查询成功！");
		Map<String,Object> map = new HashMap<String,Object>();
		map.put("startNum", (page-1)*pageSize);
		map.put("pageSize", pageSize);
		map.put("shop_unique", shop_unique);
		List<Map<String,Object>> shopList = loanDao.queryWindControlDetail2(map);
		sr.setData(shopList);
		sr.setCount(loanDao.queryWindControlDetail2Count(map));
		return sr;
	}
	
	public ShopsResult queryWindControlDetail3(String order_no , Integer page,Integer pageSize) {
		ShopsResult sr = new ShopsResult(1,"查询成功！");
		Map<String,Object> map = new HashMap<String,Object>();
		map.put("startNum", (page-1)*pageSize);
		map.put("pageSize", pageSize);
		map.put("order_no", order_no);
		List<Map<String,Object>> shopList = loanDao.queryWindControlDetail3(map);
		sr.setData(shopList);
		sr.setCount(loanDao.queryWindControlDetail3Count(map));
		return sr;
	}
	/**
	 * 查询借款规则
	 * @param sxRuleId
	 * @return
	 */
	public List<Map<String,Object>> querySxRuleMsg(Integer sxRuleId) {
		List<Map<String,Object>> list = loanDao.querySxRuleMsg(sxRuleId);
		return list;
	}
	
	public String queryShopLoanMoney(String shop_unique,Integer shop_loan_id) {
		Map<String,Object> map = new HashMap<String,Object>();
		map.put("shop_unique", shop_unique);
		map.put("shop_loan_id", shop_loan_id);
		Map<String,Object> resMap = loanDao.queryShopLoanDetail(map);
		
		if(null == resMap || resMap.isEmpty()) {
			return "0";
		}
		
		return resMap.get("loanMoney") == null ? "0" : resMap.get("loanMoney").toString();
	}
	/**
	 * 更新店铺审核信息
	 * @param map
	 * @return
	 */
	public ShopsResult updateShopLoanMsg(Integer shop_loan_id,Integer audit_status,String loan_money,String remarks,String refuse_reason,Integer new_loan_money) {
		ShopsResult sr = new ShopsResult(1, "更新成功！");
		Map<String,Object> map = new HashMap<String,Object>();
		map.put("refuse_reason", refuse_reason);
		map.put("remarks", remarks);
		map.put("shop_loan_id", shop_loan_id);
		map.put("audit_status", audit_status);
		map.put("new_loan_money", new_loan_money);
		map.put("loan_money", loan_money);
		if(null == audit_status) {
		}else if(audit_status == 2) {
			if(null != loan_money) {
				map.put("loanMoney", "-"+loan_money);
			}
		}else if(audit_status == 3) {
		}
		loanDao.updateShopLoanMsg(map);
		return sr;
	}
	
	/**
	 * 查询店铺
	 * @param shop_unique
	 * @return
	 */
	public ShopsResult queryLoanShopDetail(String shop_unique) {
		ShopsResult sr = new ShopsResult();
		Map<String,Object> map = loanDao.queryLoanPolicy();
		map.put("shop_unique", shop_unique);
		
		List<Map<String,Object>> shopList = loanDao.queryLoanShopList(map);
		if(null != shopList && !shopList.isEmpty()) {
			sr.setData(shopList.get(0));
		}
		sr.setStatus(1);
		sr.setMsg("查询成功！");
		return sr;
	}
	
	/**
	 * @param page 当前查询页数
	 * @param pageSize 每页查询的店铺数量
	 */
	public ShopsResult queryLoanShopList(Integer page,Integer pageSize,Integer audit_status) {
		ShopsResult sr = new ShopsResult(1,"查询成功");
		Map<String,Object> map = loanDao.queryLoanPolicy();
		if(null != map && !map.isEmpty()) {
			map.put("startNum", (page-1)*pageSize);
			map.put("pageSize", pageSize);
			map.put("audit_status", audit_status);
			List<Map<String,Object>> shopList = loanDao.queryLoanShopList(map);
			sr.setData(shopList);
			sr.setCount(loanDao.queryLoanShopCount());
		}else {
			sr.setCount(0);
		}
		return sr;
	}
	@Override
	public ShopsResult queryRules() {
		
		ShopsResult sr = new ShopsResult(1,"查询成功");
		Map<String,Object> map = loanDao.queryLoanPolicy();
		List<Map<String,Object>> ruleList = loanDao.queryRuleList();
		sr.setData(map);
		sr.setCord(ruleList);
		return sr;
	}
	@Override
	public ShopsResult updateRules(Map<String, Object> map) {
		ShopsResult sr = new ShopsResult(1,"修改成功");
		try {
			if(map.containsKey("id")&&map.get("id").equals(""))
			{
				//修改额度策略
				loanDao.updateRules(map);
			}else
			{
				//还款策略
				loanDao.updateRules2(map);
			}
		}catch(Exception e)
		{
			sr.setStatus(0);
			sr.setMsg(e.getMessage());
		}

		// TODO Auto-generated method stub
		return sr;
	}
	@Override
	public ShopsResult queryLoanOrderList(String searchMsg, Integer page, Integer pageSize, Integer audit_status,String num) {
		ShopsResult sr = new ShopsResult(1,"查询成功！");
		Map<String,Object> map = new HashMap<String,Object>();
		map.put("startNum", (page-1)*pageSize);
		map.put("pageSize", pageSize);
		map.put("shop_unique", searchMsg);
		map.put("audit_status", audit_status);
		map.put("num", num);
		List<Map<String,Object>> shopList = loanDao.queryLoanOrderList(map);
		sr.setData(shopList);
		sr.setCount(loanDao.queryLoanOrderCount(map));
		return sr;
	}
	@Override
	public List<Map<String, Object>> queryShopLoanList2() {
		Map<String,Object> map = new HashMap<String,Object>();
		map.put("auditStatus",2);
		List<Map<String,Object>> shopList = loanDao.queryShopLoanList(map);
		return shopList;
	}
}
