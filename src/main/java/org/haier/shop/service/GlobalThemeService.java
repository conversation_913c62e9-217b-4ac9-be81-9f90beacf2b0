package org.haier.shop.service;

import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;

import org.haier.shop.dao.GlobalThemeDao;
import org.haier.shop.entity.beans.PageData;
import org.haier.shop.entity.globalSelect.FarmDetailVO;
import org.haier.shop.entity.globalSelect.GlobalDetailVO;
import org.haier.shop.redis.RedisCache;
import org.haier.shop.util.FTPConfig;
import org.haier.shop.util.PurResult;
import org.haier.shop.util.SFTPUtil;
import org.haier.shop.util.ShopsResult;
import org.haier.shop.util.ShopsUtil;
import org.haier.shop.util.UUIDUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import net.sf.json.JSONArray;
import net.sf.json.JsonConfig;

@Service
public class GlobalThemeService{
	
	@Resource
	private GlobalThemeDao globalThemeDao;
	
	@Autowired
	private RedisCache redis;

	public PurResult queryGlobalSupplierList(Map<String,Object> map){
		PurResult result = new PurResult();
		try {
			List<Map<String,Object>> list = globalThemeDao.queryGlobalSupplierList(map);
	    	Integer count = globalThemeDao.queryGlobalSupplierCount(map);
	    	result.setStatus(1);
			result.setMsg("成功");
			result.setCount(count);
			result.setData(list);
		} catch (Exception e) {
			e.printStackTrace();
			result.setStatus(0);
			result.setMsg("异常");
		}
		return result;
	}
	
	public PurResult queryGlobalOrderList(Map<String,Object> map){
		PurResult result = new PurResult();
		try {
			
			List<Map<String,Object>> list = globalThemeDao.getGlobalOrderList(map);
	    	Integer count = globalThemeDao.getGlobalOrderListCount(map);
	    	result.setStatus(1);
			result.setMsg("成功");
			result.setCount(count);
			result.setData(list);
		} catch (Exception e) {
			e.printStackTrace();
			result.setStatus(0);
			result.setMsg("异常");
		}
		return result;
	}
	
	public PurResult queryGlobalGoodShelfList(Map<String,Object> map){
		PurResult result = new PurResult();
		try {
			List<Map<String,Object>> list = globalThemeDao.queryGlobalGoodShelfList(map);
	    	Integer count = globalThemeDao.queryGlobalGoodShelfCount(map);
	    	result.setStatus(1);
			result.setMsg("成功");
			result.setCount(count);
			result.setData(list);
		} catch (Exception e) {
			e.printStackTrace();
			result.setStatus(0);
			result.setMsg("异常");
		}
		return result;
	}
	
	//获取有商家的省份列表
	public List<Map<String ,Object>> getProvinceList(List<Map<String ,Object>> list)
	{
		return globalThemeDao.getProvinceList(list);
	}
	
	public List<Map<String ,Object>> getFarmAuditList(Map<String,Object> map){
		return globalThemeDao.getFarmAuditList(map);
	}
	//获取有商家的城市列表
	public List<Map<String ,Object>> getCityList(List<Map<String ,Object>> list){
		return globalThemeDao.getCityList(list);
	}
	//获取有商家的区域列表
	public List<Map<String ,Object>> getAreaList(Map<String,Object> map){
		return globalThemeDao.getAreaList(map);
	}
	//获取有商家的区域列表
	public List<Map<String ,Object>> getFarmAreaList(Map<String,Object> map){
		return globalThemeDao.getFarmAreaList(map);
	}
	public PurResult updateGlobalThemeStatus(Map<String,Object> map){
		PurResult result = new PurResult();
		try {
			globalThemeDao.updateGlobalThemeStatus(map);
	    	result.setStatus(1);
			result.setMsg("成功");
		}catch (Exception e) {
			e.printStackTrace();
			result.setStatus(0);
			result.setMsg("异常");
		}
		return result;
	}
	
	@Transactional
	public PurResult insertThemeAudit(Map<String,Object> map){
		PurResult result = new PurResult();
		try {
			map.put("create_time", new Date());
			map.put("theme_id", map.get("id"));
			globalThemeDao.updateGlobalThemeStatus(map);
			globalThemeDao.insertThemeAudit(map);
	    	result.setStatus(1);
			result.setMsg("成功");
		}catch (Exception e) {
			e.printStackTrace();
			result.setStatus(0);
			result.setMsg("异常");
		}
		return result;
	}
	
	public PurResult upperShelfGlobal(Map<String,Object> map){
		PurResult result = new PurResult();
		try {
			map.put("create_time", new Date());
			map.put("shelf_status", "1");
			globalThemeDao.insertGlobalShelf(map);
	    	result.setStatus(1);
			result.setMsg("成功");
		}catch (Exception e) {
			e.printStackTrace();
			result.setStatus(0);
			result.setMsg("异常");
		}
		return result;
	}
	@Transactional
	public PurResult upperShelfGlobal2(Map<String,Object> map){
		PurResult result = new PurResult();
		try {
			map.put("create_time", new Date());
			
			if(map.containsKey("id"))
			{
				globalThemeDao.updateGoodShelfStatus(map);
				
			}else
			{
				globalThemeDao.insertGlobalShelf(map);
			}
			
	    	result.setStatus(1);
			result.setMsg("成功");
		}catch (Exception e) {
			e.printStackTrace();
			result.setStatus(0);
			result.setMsg("异常");
		}
		return result;
	}
	public PurResult updateGoodShelfStatus(Map<String,Object> map){
		PurResult result = new PurResult();
		try {
			globalThemeDao.updateGoodShelfStatus(map);
	    	result.setStatus(1);
			result.setMsg("成功");
		}catch (Exception e) {
			e.printStackTrace();
			result.setStatus(0);
			result.setMsg("异常");
		}
		return result;
	}
	
	
	@Transactional
	@SuppressWarnings("unchecked")
	public PurResult addGlobalTheme(Map<String,Object> map){
		PurResult result = new PurResult();
		JSONArray areaJson=JSONArray.fromObject(map.get("areaJson"));
		JSONArray goodJson=JSONArray.fromObject(map.get("detailJson"));
		
		List<PageData> areaList = JSONArray.toList(areaJson, new PageData(), new JsonConfig());
		List<PageData> goodsList = JSONArray.toList(goodJson, new PageData(), new JsonConfig());
		
		int num=globalThemeDao.addGlobalTheme(map);
		if(num>0)
		{
			for(PageData area:areaList)
			{
				area.put("id", map.get("id"));
			}
			globalThemeDao.addGlobalThemeArea(areaList);
			for(PageData good:goodsList)
			{
				good.put("id", map.get("id"));
			}
			globalThemeDao.addGlobalThemeGoodDetail(goodsList);
		}
		
	    result.setStatus(1);
		result.setMsg("成功");
		
		return result;
	}
	
	public GlobalDetailVO queryGlobalThemeDetail(Map<String,Object> map){

		return globalThemeDao.queryGlobalThemeDetail(map);
	}
	
	@Transactional
	public PurResult editExpressInfo(Map<String,Object> map){
		PurResult result = new PurResult();
		try {
			globalThemeDao.insertExpress(map);
			map.put("handle_status", "2");
			map.put("send_datetime", new Date());
			globalThemeDao.updateGlobalSubList(map);
	    	result.setStatus(1);
			result.setMsg("成功");
		}catch (Exception e) {
			e.printStackTrace();
			result.setStatus(0);
			result.setMsg("异常");
		}
		return result;
	}
	
	public PurResult getGlobalOrderDetail(Map<String,Object> map){
		PurResult result = new PurResult();
		try {
			List<Map<String,Object>> list = globalThemeDao.getGlobalOrderDetail(map);
	    	Integer count = list.size();
	    	result.setStatus(1);
			result.setMsg("成功");
			result.setCount(count);
			result.setData(list);
		} catch (Exception e) {
			e.printStackTrace();
			result.setStatus(0);
			result.setMsg("异常");
		}
		return result;
	}
	public PurResult queryPTGGShelfList(Map<String,Object> map){
		PurResult result = new PurResult();
			List<Map<String,Object>> list = globalThemeDao.queryPTGGShelfList(map);
			Integer count = globalThemeDao.queryPTGGShelfCount(map);
	    	result.setStatus(1);
			result.setMsg("成功");
			result.setCount(count);
			result.setData(list);
			return result;
	}
	@Transactional
	public PurResult updateGoodShelf(Map<String,Object> map){
		PurResult result = new PurResult();
		
		String type=(String) map.get("type");
		
		if(type.equals("A"))//全部上架
		{
			map.put("shelf_status", 1);
			globalThemeDao.updateGoodShelf(map);
			//从未上架的店铺
			List<Map<String,Object>> list2 = globalThemeDao.queryPTGGShelf2List(map);
			globalThemeDao.addGoodShelfList(list2);
			
			
		}else if(type.equals("B"))//全部下架
		{
			map.put("shelf_status", 2);
			globalThemeDao.updateGoodShelf(map);
		}else if(type.equals("C"))//强制上架
		{
			map.put("shelf_status", 1);
			globalThemeDao.updateGoodShelf(map);
			//从未上架的店铺
			List<Map<String,Object>> list2 = globalThemeDao.queryPTGGShelf2List(map);
			if(list2!=null&&list2.size()>0)
			{
				globalThemeDao.addGoodShelfList(list2);
			}
			
			//强制上架
			map.put("compulsory", 1);
			globalThemeDao.updateGoodShelfCompulsory(map);
		}else if(type.equals("D"))//全部下架
		{
			map.put("compulsory", 0);
			globalThemeDao.updateGoodShelfCompulsory(map);
		}
		
	    result.setStatus(1);
		result.setMsg("成功");
		return result;
	}
	
	public PurResult queryGlobalSecretaryList(Map<String,Object> map){
		PurResult result = new PurResult();
			List<Map<String,Object>> list = globalThemeDao.queryGlobalSecretaryList(map);
			Integer count = globalThemeDao.queryGlobalSecretaryCount(map);
	    	result.setStatus(1);
			result.setMsg("成功");
			result.setCount(count);
			result.setData(list);
			return result;
	}
	public PurResult addSecretary(Map<String,Object> map){
		PurResult result = new PurResult();
		globalThemeDao.addSecretary(map);
	    result.setStatus(1);
		result.setMsg("成功");
		return result;
	}
	public PurResult deleteGlobalSecretary(Map<String,Object> map){
		PurResult result = new PurResult();
		globalThemeDao.deleteGlobalSecretary(map);
	    result.setStatus(1);
		result.setMsg("成功");
		return result;
	}
	public Map<String,Object> querySecretary(String map){

		return globalThemeDao.querySecretary(map);
	}
	public PurResult updateSecretary(Map<String,Object> map){

		PurResult result = new PurResult();
		globalThemeDao.updateSecretary(map);
	    result.setStatus(1);
		result.setMsg("成功");
		return result;
	}
	
	public ShopsResult queryGoodsKinds(String shop_unique, String area_dict_parent_num) {
		ShopsResult shop=new ShopsResult();
		Map<String,Object> map=new HashMap<String,Object>();
		if(null==area_dict_parent_num){
			area_dict_parent_num="0";
		}
		map.put("area_dict_parent_num", area_dict_parent_num);
		//查询当前店铺使用的分类类型，1：默认分类；2：自定义分类
		List<Map<String,Object>> kindList= globalThemeDao.queryArea(map);
		
		shop.setStatus(0);
		shop.setMsg("商品分类查询成功！");
		shop.setData(kindList);
		return shop;
	}
	public PurResult queryProductsList(Map<String,Object> map){
		PurResult result = new PurResult();
		try {
			int type =globalThemeDao.queryShop(map);
			if(type==4)
			{
				
				map.put("audit_status", 1);
			}
			List<Map<String,Object>> list = globalThemeDao.queryProductsList(map);
	    	Integer count = globalThemeDao.queryProductsCount(map);
	    	result.setStatus(1);
			result.setMsg("成功");
			result.setCount(count);
			result.setData(list);
		} catch (Exception e) {
			e.printStackTrace();
			result.setStatus(0);
			result.setMsg("异常");
		}
		return result;
	}
	
	@Transactional
	@SuppressWarnings( "unchecked" )
	public PurResult addProduct(Map<String,Object> map,HttpServletRequest request,List<PageData> peopleList){
		PurResult result = new PurResult();
		JSONArray areaJson=JSONArray.fromObject(map.get("areaJson"));
		JSONArray goodJson=JSONArray.fromObject(map.get("detailJson"));
		JSONArray goodJson_sup=JSONArray.fromObject(map.get("detailJson_sup"));
		Object[] shelf_status_str=(Object[]) JSONArray.fromObject(map.get("shelf_status_str")).toArray();
		map.put("shelf_status", 0);
		map.put("sup_shelf_status", 0);
		for (Object value : shelf_status_str) {
			if("quan".equals(value.toString())){
				map.put("shelf_status", 1);
			}else if("sup".equals(value.toString())){
				map.put("sup_shelf_status", 1);
			}
		}
		List<PageData> areaList = JSONArray.toList(areaJson, new PageData(), new JsonConfig());//区域
		List<PageData> goodsList = JSONArray.toList(goodJson, new PageData(), new JsonConfig());//规格
		List<PageData> goodsList_sup = JSONArray.toList(goodJson_sup, new PageData(), new JsonConfig());//规格

		//新增农产品
		globalThemeDao.addProduct(map);
		//新增农产品销售统计
		globalThemeDao.addProductStatistics(map.get("id").toString());
		//增加上架的店铺列表
		map.put("list", areaList);
		globalThemeDao.addFarmProductShelfList(map);
		//增加上架的规格列表
		for(PageData pageData : goodsList) {
			map.putAll(pageData);
			globalThemeDao.addFarmProductShelfPriceList(map);
		}
		
		//新增全球精选规格
		if(goodsList.size()>0){
		for(PageData t:goodsList)
		{
			t.put("farm_products_id", map.get("id"));
		}
		if("1".equals(map.get("shelf_status").toString())){
			globalThemeDao.addProductSpec(goodsList);
		}
		}
		if(goodsList_sup.size()>0){
		//新增供货商城规格
		for(PageData t:goodsList_sup)
		{
			t.put("farm_products_id", map.get("id"));
		}
		if("1".equals(map.get("sup_shelf_status").toString())){
			globalThemeDao.addProductSpec_sup(goodsList_sup);
		}
		}
		//新增农户
		for(PageData p:peopleList)
		{
			p.put("farm_products_id", map.get("id"));
		}
		globalThemeDao.addProductPeople(peopleList);
		//新增区域
		for(PageData a:areaList)
		{
			a.put("farm_products_id", map.get("id"));
			
		}
		Object yanshiStatus = redis.getObject("yanshiStatus");
		if(null != yanshiStatus && yanshiStatus.toString().equals("1")) {
			boolean flag = true;
			for(PageData a:areaList) {
				if(a.get("county").toString().equals("371302")) {
					flag = false;
				}
			}
			
			if(flag) {
				PageData a = new PageData();
				a.put("count", "371302");
				a.put("farm_products_id", map.get("id"));
				areaList.add(a);
			}
		}
		
		globalThemeDao.addProductArea(areaList);
		
	    result.setStatus(1);
		result.setMsg("成功");
		
		return result;
	}
	
	@Transactional
	@SuppressWarnings( "unchecked" )
	public PurResult addProduct2(Map<String,Object> map,HttpServletRequest request,List<PageData> peopleList){
		System.out.println(map.toString()+">>>>>>>>>>>>");
		PurResult result = new PurResult();
		JSONArray areaJson=JSONArray.fromObject(map.get("areaJson"));
		JSONArray goodJson=JSONArray.fromObject(map.get("detailJson"));
		JSONArray goodJson_sup=JSONArray.fromObject(map.get("detailJson_sup"));
		Object[] shelf_status_str=(Object[]) JSONArray.fromObject(map.get("shelf_status_str")).toArray();
		map.put("shelf_status", 0);
		map.put("sup_shelf_status", 0);
		for (Object value : shelf_status_str) {
			if("quan".equals(value)){
				map.put("shelf_status", 1);
			}else if("sup".equals(value)){
				map.put("sup_shelf_status", 1);
			}
		}
		List<PageData> areaList = JSONArray.toList(areaJson, new PageData(), new JsonConfig());//区域
		List<PageData> goodsList = JSONArray.toList(goodJson, new PageData(), new JsonConfig());//规格
		List<PageData> goodsList_sup = JSONArray.toList(goodJson_sup, new PageData(), new JsonConfig());//规格

		int type =globalThemeDao.queryShop(map);
		if(type==4)
		{
			map.put("audit_status", 1);
		}else
		{
			map.put("audit_status", 0);
		}
		//生产条码
		String barcode ="N"+System.currentTimeMillis()/ 1000+(int)((Math.random()*9+1)*1000); //10位数的时间戳+4位随机数
		map.put("barcode", barcode);

		//新增农产品
		globalThemeDao.addProduct(map);
		//新增规格
		if(goodsList.size()>0){
		for(PageData t:goodsList)
		{
			t.put("farm_products_id", map.get("id"));
			
		}
		globalThemeDao.addProductSpec(goodsList);
		}
		//新增供货商城规格
		if(goodsList_sup.size()>0){
		for(PageData t:goodsList_sup)
		{
			t.put("farm_products_id", map.get("id"));
		}
		globalThemeDao.addProductSpec_sup(goodsList_sup);
		}
		//新增农户
		for(PageData p:peopleList)
		{
			p.put("farm_products_id", map.get("id"));
		}
		globalThemeDao.addProductPeople(peopleList);
		//新增区域
		for(PageData a:areaList)
		{
			a.put("farm_products_id", map.get("id"));
		}
		Object yanshiStatus = redis.getObject("yanshiStatus");
		if(null != yanshiStatus && yanshiStatus.toString().equals("1")) {
			boolean flag = true;
			for(PageData a:areaList) {
				if(a.get("county").toString().equals("371302")) {
					flag = false;
				}
			}
			
			if(flag) {
				PageData a = new PageData();
				a.put("count", "371302");
				a.put("farm_products_id", map.get("id"));
				areaList.add(a);
			}
		}
		globalThemeDao.addProductArea(areaList);
		
	    result.setStatus(1);
		result.setMsg("成功");
		
		return result;
	}
	public List<Map<String, Object>> querySecretaryList() {
		return globalThemeDao.querySecretaryList();

	}
	public FarmDetailVO queryFarmDetail(Map<String,Object> map){

		return globalThemeDao.queryFarmDetail(map);
	}

	@Transactional
	public PurResult updateFarmStatus(int id,int shelf_status,int good_status){
		PurResult result = new PurResult();
		
		Map<String,Object> params=new HashMap<String,Object>();
		
		params.put("id", id);
		params.put("shelf_status", shelf_status);
		params.put("good_status", good_status);
		if(good_status==2)
		{
			params.put("flag", 0);
		}else
		{
			params.put("flag", 1);
			params.put("good_status", 1);
		}
		//下架
		if(shelf_status==0)
		{
			//全部店铺下架
			params.put("audit_status", 0);
			globalThemeDao.updateFarmShopStatus(params);
		}else
		{
			params.put("audit_status", null);
		}
		
		
		globalThemeDao.updateFarmStatus(params);
		
	    result.setStatus(1);
		result.setMsg("成功");
		
		return result;
	}
	
	@SuppressWarnings("unchecked")
	@Transactional
	public PurResult updateAllFarmStatus(int shelf_status,String data,int good_status){
		PurResult result = new PurResult();
		
		Map<String,Object> params=new HashMap<String,Object>();
		
		JSONArray goodJson=JSONArray.fromObject(data);
		
		List<PageData> goodsList = JSONArray.toList(goodJson, new PageData(), new JsonConfig());
		

		
		params.put("shelf_status", shelf_status);
		params.put("good_status", good_status);
		if(good_status==2)
		{
			params.put("flag", 0);
		}else
		{
			params.put("flag", 1);
			params.put("good_status", 1);
		}
		//下架
		if(shelf_status==0)
		{
			//全部店铺下架
			params.put("audit_status", 0);
			globalThemeDao.updateAllFarmShopStatus(params,goodsList);
			
		}else
		{
			for(PageData t:goodsList)
			{
				int audit_status=(Integer)t.get("audit_status");
				if(audit_status!=1)
				{
					result.setStatus(0);
					result.setMsg(t.get("goods_name")+"未审核无法上架！" );
					return result;
				}
			}
			params.put("audit_status", null);
		}
		
		
		globalThemeDao.updateAllFarmStatus(params,goodsList);
		
	    result.setStatus(1);
		result.setMsg("成功");
		
		return result;
	}
	
	
	/**
	 * 商品分类查询
	 * @param goods_kind_parunique 商品分类大类编号
	 */
	public ShopsResult queryfarmKinds( String parentId,Integer kindStatus,Integer kind_type) {
		ShopsResult shop=new ShopsResult();
		Map<String,Object> map = new HashMap<String,Object>();
		map.put("parentId", parentId);
		map.put("kindStatus", kindStatus);
		map.put("kind_type", kind_type);
		
		List<Map<String,Object>> kindList= globalThemeDao.queryGoodsKind(map);
		
		shop.setStatus(0);
		shop.setMsg("商品分类查询成功！");
		shop.setData(kindList);
		return shop;
	}
	
	@Transactional
	@SuppressWarnings( "unchecked" )
	public PurResult updateProduct(Map<String,Object> map,HttpServletRequest request) throws Exception{
		PurResult result = new PurResult();
		JSONArray areaJson=JSONArray.fromObject(map.get("areaJson"));
		JSONArray goodJson=JSONArray.fromObject(map.get("detailJson"));
		JSONArray peopleJson=JSONArray.fromObject(map.get("peopleJson"));
		JSONArray goodJson_sup=JSONArray.fromObject(map.get("detailJson_sup"));
		Object[] shelf_status_str=(Object[]) JSONArray.fromObject(map.get("shelf_status_str")).toArray();
		map.put("audit_status", 0);
		map.put("shelf_status", 0);
		map.put("sup_shelf_status", 0);
		for (Object value : shelf_status_str) {
			if("quan".equals(value)){
				map.put("shelf_status", 1);
			}else if("sup".equals(value)){
				map.put("sup_shelf_status", 1);
			}
		}
		List<PageData> areaList = JSONArray.toList(areaJson, new PageData(), new JsonConfig());//区域
		List<PageData> goodsList = JSONArray.toList(goodJson, new PageData(), new JsonConfig());//规格
		List<PageData> goodsList_sup = JSONArray.toList(goodJson_sup, new PageData(), new JsonConfig());//规格
		List<PageData> peopleList = JSONArray.toList(peopleJson, new PageData(), new JsonConfig());//农户
		List<PageData> OldPeopleList = globalThemeDao.quertPeolpleList(map);
		//
		SFTPUtil sftp = new SFTPUtil(FTPConfig.username, FTPConfig.password, FTPConfig.host, FTPConfig.port);   
        sftp.login(); 
		//生产条码
		MultipartFile file=null;
		//上传商品图片
		file=ShopsUtil.testMulRequest(request, "goodsPicturePath1");
		if(file!=null){
			String orName=file.getOriginalFilename();//获取文件原名称
			String lastName=orName.substring(orName.lastIndexOf("."));
			String newName=UUIDUtil.getUUID32()+lastName;
			String filePathDetail="/"+"image"+"/"+map.get("shop_unique");
			String filePath = this.getClass().getClassLoader().getResource("../../").getPath();
			filePath=filePath.substring(0, filePath.length()-request.getContextPath().length())+filePathDetail;
			ShopsUtil.savePicture(file, filePath, newName,"2");
		    InputStream is = file.getInputStream();   
	        boolean flag=sftp.upload(FTPConfig.goods_path+"/"+map.get("shop_unique"), newName, is);  
			if(flag){
				map.put("goods_image", filePathDetail+"/"+newName);
			}
		}
		//上传商品详情图片
		file=ShopsUtil.testMulRequest(request, "goodsPicturePath2");
		if(file!=null){
			String orName=file.getOriginalFilename();//获取文件原名称
			String lastName=orName.substring(orName.lastIndexOf("."));
			String newName=UUIDUtil.getUUID32()+lastName;
			String filePathDetail="/"+"image"+"/"+map.get("shop_unique");
			String filePath = this.getClass().getClassLoader().getResource("../../").getPath();
			filePath=filePath.substring(0, filePath.length()-request.getContextPath().length())+filePathDetail;
			ShopsUtil.savePicture(file, filePath, newName,"2");
		    InputStream is = file.getInputStream();   
	        boolean flag=sftp.upload(FTPConfig.goods_path+"/"+map.get("shop_unique"), newName, is);  
			if(flag){
				map.put("detail_image", filePathDetail+"/"+newName);
			}
		}		
		//上传商品视频
//		SFTPUtil sftpVideo = new SFTPUtil(FTPConfig.username, FTPConfig.password, FTPConfig.host, FTPConfig.port);   
//		sftpVideo.login();
		MultipartFile fileVideo=null;
		fileVideo=ShopsUtil.testMulRequest(request, "goodsPicturePathVideo");
		System.out.println("fileVideo:"+fileVideo);
		if(fileVideo!=null){
			String orName=fileVideo.getOriginalFilename();//获取文件原名称
			System.out.println("orName:"+orName);
			String lastName=orName.substring(orName.lastIndexOf("."));
			String newName=UUIDUtil.getUUID32()+lastName;
			String filePathDetail="/"+"image"+"/"+map.get("shop_unique");
			String filePath = this.getClass().getClassLoader().getResource("../../").getPath();
			filePath=filePath.substring(0, filePath.length()-request.getContextPath().length())+filePathDetail;
		    InputStream is = fileVideo.getInputStream();   
	        boolean flag=sftp.upload(FTPConfig.goods_path+"/"+map.get("shop_unique"), newName, is);  
			if(flag){
				map.put("goods_video", filePathDetail+"/"+newName);
			}
		}
		//新增农产品
		globalThemeDao.updateProduct(map);
		globalThemeDao.delProductSpec(map);
		
		globalThemeDao.delProductArea(map);
		globalThemeDao.delProductPeople(map);
		//新增规格
		for(PageData t:goodsList)
		{
			t.put("farm_products_id", map.get("id"));
		}
		if("1".equals(map.get("shelf_status").toString())){
			globalThemeDao.delProductSpec(map);
			globalThemeDao.addProductSpec(goodsList);
		}else{
			globalThemeDao.delProductSpec(map);
			
		}
		
		//新增规格
		for(PageData t:goodsList_sup)
		{
			t.put("farm_products_id", map.get("id"));

		}
		if("1".equals(map.get("sup_shelf_status").toString())){
			globalThemeDao.delProductSpec_sup(map);
			globalThemeDao.addProductSpec_sup(goodsList_sup);
		}else{
			globalThemeDao.delProductSpec_sup(map);
			
		}
		
		//新增农户
		for(PageData p:peopleList)
		{
			p.put("farm_products_id", map.get("id"));
			MultipartFile file1=null;
			file1=ShopsUtil.testMulRequest(request, p.get("aptitudes_imgages").toString());
			if(file1!=null){
				String orName=file1.getOriginalFilename();//获取文件原名称
				String lastName=orName.substring(orName.lastIndexOf("."));
				String newName=UUIDUtil.getUUID32()+lastName;
				String filePathDetail="/"+"image"+"/"+map.get("shop_unique");
				String filePath = this.getClass().getClassLoader().getResource("../../").getPath();
				filePath=filePath.substring(0, filePath.length()-request.getContextPath().length())+filePathDetail;
				ShopsUtil.savePicture(file1, filePath, newName,"2");
				InputStream is = file1.getInputStream();   
		        boolean flag=sftp.upload(FTPConfig.goods_path+"/"+map.get("shop_unique"), newName, is); 
				if(flag){
					p.put("aptitudes_imgages", filePathDetail+"/"+newName);
				}else
				{
					p.put("aptitudes_imgages", null);
				}
			}else
			{
				p.put("aptitudes_imgages", null);
				for(PageData t :OldPeopleList)
				{
					String peopleId=String.valueOf(t.get("peopleId"));
					String id=String.valueOf(p.get("id"));
					if(peopleId.equals(id))
					{
						 if(t.containsKey("aptitudes_imgages"))
						{
							p.put("aptitudes_imgages", t.get("aptitudes_imgages"));
						}
					}
				}
			}
			MultipartFile file2=null;
			file2=ShopsUtil.testMulRequest(request, p.get("aptitudes_imgages2").toString());
			if(file2!=null){
				String orName=file2.getOriginalFilename();//获取文件原名称
				String lastName=orName.substring(orName.lastIndexOf("."));
				String newName=UUIDUtil.getUUID32()+lastName;
				String filePathDetail="/"+"image"+"/"+map.get("shop_unique");
				String filePath = this.getClass().getClassLoader().getResource("../../").getPath();
				filePath=filePath.substring(0, filePath.length()-request.getContextPath().length())+filePathDetail;
				ShopsUtil.savePicture(file2, filePath, newName,"2");
				InputStream is = file2.getInputStream();   
		        boolean flag=sftp.upload(FTPConfig.goods_path+"/"+map.get("shop_unique"), newName, is); 
				if(flag){
					p.put("aptitudes_imgages2", filePathDetail+"/"+newName);
				}else
				{
					p.put("aptitudes_imgages2", null);
				}
			}else
			{
				p.put("aptitudes_imgages2", null);
				for(PageData t :OldPeopleList)
					{
						String peopleId=String.valueOf(t.get("peopleId"));
						String id=String.valueOf(p.get("id"));
						if(peopleId.equals(id))
						{
							 if(t.containsKey("aptitudes_imgages2"))
							{
								p.put("aptitudes_imgages2", t.get("aptitudes_imgages2"));
							}
						}
					}
			}
			
			MultipartFile file3=null;
			file3=ShopsUtil.testMulRequest(request,p.get("aptitudes_imgages3").toString());
			if(file3!=null){
				String orName=file3.getOriginalFilename();//获取文件原名称
				String lastName=orName.substring(orName.lastIndexOf("."));
				String newName=UUIDUtil.getUUID32()+lastName;
				String filePathDetail="/"+"image"+"/"+map.get("shop_unique");
				String filePath = this.getClass().getClassLoader().getResource("../../").getPath();
				filePath=filePath.substring(0, filePath.length()-request.getContextPath().length())+filePathDetail;
				ShopsUtil.savePicture(file3, filePath, newName,"2");
				InputStream is = file3.getInputStream();   
		        boolean flag=sftp.upload(FTPConfig.goods_path+"/"+map.get("shop_unique"), newName, is);
				if(flag){
					p.put("aptitudes_imgages3", filePathDetail+File.separator+newName);
				}else
				{
					p.put("aptitudes_imgages3", null);
				}
			}else
			{
				p.put("aptitudes_imgages3", null);
				for(PageData t :OldPeopleList)
				{
					String peopleId=String.valueOf(t.get("peopleId"));
					String id=String.valueOf(p.get("id"));
					if(peopleId.equals(id))
					{
						 if(t.containsKey("aptitudes_imgages3"))
						{
							p.put("aptitudes_imgages3", t.get("aptitudes_imgages3"));
						}
					}
				}
			}
			
			
			
		}
        sftp.logout(); 
		globalThemeDao.addProductPeople(peopleList);
		//新增区域
		for(PageData a:areaList)
		{
			a.put("farm_products_id", map.get("id"));
			
		}
		globalThemeDao.addProductArea(areaList);
		
	    result.setStatus(1);
		result.setMsg("成功");
		
		return result;
	}

	public Map<String, Object> querySetAddMoney() {
		return globalThemeDao.queryPlatformAddMoney();
	}

	public ShopsResult updateAddMoney(String add_money_quan, String add_money_sup) {
		ShopsResult result=new ShopsResult();
		Map<String,Object> params=new HashMap<>();
		params.put("add_money_quan", add_money_quan);
		params.put("add_money_sup", add_money_sup);
		globalThemeDao.updateAddMoney(params);
		//设置全球精选价格
		globalThemeDao.updateProductSpecAddMoney(params);
		//设置供货商城价格
		globalThemeDao.updateProductSpecSupAddMoney(params);
		result.setStatus(1);
		return result;
	}

	public PurResult updateProduct2(Map<String, Object> map, HttpServletRequest request, List<PageData> peopleList) {
		PurResult result = new PurResult();
		JSONArray areaJson=JSONArray.fromObject(map.get("areaJson"));
		JSONArray goodJson=JSONArray.fromObject(map.get("detailJson"));
		JSONArray peopleJson=JSONArray.fromObject(map.get("peopleJson"));
		JSONArray goodJson_sup=JSONArray.fromObject(map.get("detailJson_sup"));
		Object[] shelf_status_str=(Object[]) JSONArray.fromObject(map.get("shelf_status_str")).toArray();
		map.put("audit_status", 0);
		map.put("shelf_status", 0);
		map.put("sup_shelf_status", 0);
		for (Object value : shelf_status_str) {
			if("quan".equals(value)){
				map.put("shelf_status", 1);
			}else if("sup".equals(value)){
				map.put("sup_shelf_status", 1);
			}
		}
		List<PageData> areaList = JSONArray.toList(areaJson, new PageData(), new JsonConfig());//区域
		List<PageData> goodsList = JSONArray.toList(goodJson, new PageData(), new JsonConfig());//规格
		List<PageData> goodsList_sup = JSONArray.toList(goodJson_sup, new PageData(), new JsonConfig());//规格
		List<PageData> OldPeopleList = globalThemeDao.quertPeolpleList(map);
		
		//新增农产品
		globalThemeDao.updateProduct(map);
		globalThemeDao.delProductSpec(map);
		globalThemeDao.delProductSpec_sup(map);
		globalThemeDao.delProductArea(map);
		globalThemeDao.delProductPeople(map);
		//查询平台加价
		Map<String,Object> add_money_map=globalThemeDao.queryPlatformAddMoney();
		//新增规格
		for(PageData t:goodsList)
		{
			t.put("farm_products_id", map.get("id"));
			t.put("add_money_quan", add_money_map.get("add_money_quan"));

		}
		globalThemeDao.addProductSpec(goodsList);
		//新增规格
		for(PageData t:goodsList_sup)
		{
			t.put("farm_products_id", map.get("id"));
			t.put("add_money_sup", add_money_map.get("add_money_sup"));

		}
		globalThemeDao.addProductSpec_sup(goodsList_sup);
		//新增农户
		for(PageData p:peopleList)
		{
			p.put("farm_products_id", map.get("id"));
			if(p.get("aptitudes_imgages")==null){
				for(PageData t :OldPeopleList)
				{
					String peopleId=String.valueOf(t.get("peopleId"));
					String id=String.valueOf(p.get("id"));
					if(peopleId.equals(id))
					{
						 if(t.containsKey("aptitudes_imgages"))
						{
							p.put("aptitudes_imgages", t.get("aptitudes_imgages"));
						}
					}
				}
				
			}else if(p.get("aptitudes_imgages2")==null){
				for(PageData t :OldPeopleList)
				{
					String peopleId=String.valueOf(t.get("peopleId"));
					String id=String.valueOf(p.get("id"));
					if(peopleId.equals(id))
					{
						 if(t.containsKey("aptitudes_imgages2"))
						{
							p.put("aptitudes_imgages2", t.get("aptitudes_imgages2"));
						}
					}
				}
				
			}else if(p.get("aptitudes_imgages3")==null){
				for(PageData t :OldPeopleList)
				{
					String peopleId=String.valueOf(t.get("peopleId"));
					String id=String.valueOf(p.get("id"));
					if(peopleId.equals(id))
					{
						 if(t.containsKey("aptitudes_imgages3"))
						{
							p.put("aptitudes_imgages3", t.get("aptitudes_imgages3"));
						}
					}
				}
				
			}
			
		}
		globalThemeDao.addProductPeople(peopleList);
		//新增区域
		for(PageData a:areaList)
		{
			a.put("farm_products_id", map.get("id"));
			
		}
		globalThemeDao.addProductArea(areaList);
		
	    result.setStatus(1);
		result.setMsg("成功");
		
		return result;
	}
}
