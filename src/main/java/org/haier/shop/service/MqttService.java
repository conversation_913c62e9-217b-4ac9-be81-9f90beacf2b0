package org.haier.shop.service;

import org.haier.shop.mqtt.MqttForGoodsUpdate;

import java.util.List;

/**
 * @Description MQTT服务
 * @ClassName MqttService
 * <AUTHOR>
 * @Date 2024/9/24 17:50
 **/
public interface MqttService {

    /**
     * 发送mqtt商品更新通知
     * @param goodsUpdates
     * @param shopUnique
     */
    void sendGoodsUpdate(List<MqttForGoodsUpdate> goodsUpdates, String shopUnique);
}
