package org.haier.shop.service;

import java.util.List;
import java.util.Map;

import javax.servlet.http.HttpServletRequest;

import org.haier.shop.util.PurResult;
import org.haier.util.eshow.AreaVO;
import org.haier.util.eshow.Downdefault;
import org.haier.util.eshow.TvModelVO;
import org.springframework.ui.Model;

import cn.jpush.api.push.PushResult;


public interface TVService {
	public PurResult queryAreaPalyList(String province_code,String city_code,String county_code,String start_date,String end_date);
	
	public PurResult queryPalyList(Map<String,Object> map);
	
	public PurResult downYinong(String url,String title,String db_id,String eshow_id,
			String size,String md5,String type);
	
	public PurResult queryPeopleList(Map<String,Object> map)throws Exception;
	
	public PurResult queryTVList(Map<String,Object> map)throws Exception;
	
	public PushResult pushTVMessage(Map<String,String> map)throws Exception;
	
	public PushResult pushTVMessageALL(Map<String,String> map)throws Exception;
	
	public PurResult updateRegistrationId(Map<String,Object> map)throws Exception;

	public PurResult queryAllShops();

	public PurResult addAdPeople(Map<String, Object> params);

	public PurResult queryAdPeopleDetail(Map<String, Object> params);

	public PurResult updateAdPeople(Map<String, Object> params);

	public PurResult deleteAdPeople(Map<String, Object> params);
	
	public PurResult deleteTV(String id)throws Exception;
	
	public PurResult updateTV(Map<String,Object> map )throws Exception;
	
	public Map<String,Object>  getTV(String id)throws Exception;

	public PurResult queryShopAreaList(Map<String, Object> params);
	
	public PurResult updateLockStatus(Map<String, Object> params);
	
	public PurResult quetyMedia(String countyCody);
	
	public PurResult querySourceList(Map<String, Object> params);
	
	public List<AreaVO> queryAreaList(Map<String, Object> params);
	
	public List<TvModelVO> queryModelList(Map<String, Object> params);
	
	public PurResult addArea(AreaVO params);

	public PurResult addTVSource(String title, String url, String source_type, String lai_yuan, String content,
			HttpServletRequest request) throws Exception;

	public PurResult deleteTVSource(String id);

	public PurResult updateLockStatusSource(Map<String, Object> params);

	public PurResult queryAllSource();

	public List<Map<String, Object>> queryAllAreaList();

	public List<Map<String, Object>> queryAllCityList();

	public PurResult addAreaPlay(String title, String play_type, String source_id, String areaJson, String type);

	public PurResult updatePlayStatus(Map<String, Object> params);
	
	public PurResult updateTVTiming(Map<String, Object> params);

	public PurResult deleteAreaPlay(String id);

	public void queryPlayInfo(String id, Model model);

	public PurResult editAreaPlay(String title, String play_type, String source_id, String areaJson, String id, String type);

	public PurResult addAreaCode(String city_code);

	public List<Map<String, String>> queryOnlineTV();
	
	public PurResult queryEshowPlayList(String city_code);
	
	public PurResult queryEshowSubtitleList(String city_code);
	
	public PurResult queryEshowPlayListDeatil(String id,String county);
	
	public String toEshowPlayDetail(String id,String city_code);
	
	public Downdefault queryAdEshow();
	
	
}
