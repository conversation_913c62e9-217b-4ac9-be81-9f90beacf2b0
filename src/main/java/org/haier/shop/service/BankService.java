package org.haier.shop.service;

import java.util.Map;

import javax.servlet.http.HttpServletRequest;

import org.haier.shop.util.PurResult;

public interface BankService {
	
	/**
	 * 查询银行列表
	 * @return
	 */
	public PurResult getBankList(Map<String ,Object> params);
	
	/**
	 * 添加银行信息
	 * @return
	 */
	public PurResult addBank(Map<String ,Object> params,HttpServletRequest request);
	
	/**
	 * 获取银行信息
	 * @return
	 */
	public Map<String ,Object> getBank(String bank_id);
	
	/**
	 * 修改银行信息
	 * @return
	 */
	public PurResult updateBank(Map<String ,Object> params,HttpServletRequest request);
	
	/**
	 * 删除银行信息
	 * @return
	 */
	public PurResult deleteBank(String bank_id);
}
