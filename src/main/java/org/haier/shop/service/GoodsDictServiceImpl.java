package org.haier.shop.service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;

import org.haier.shop.dao.GoodsDao;
import org.haier.shop.dao.GoodsDictDao;
import org.haier.shop.entity.BaseGoods;
import org.haier.shop.util.PurResult;
import org.haier.shop.util.ShopsResult;
import org.springframework.stereotype.Service;

@Service("goodsDictService")
public class GoodsDictServiceImpl implements GoodsDictService{
	@Resource
	private GoodsDictDao dictDao;
	@Resource
	private GoodsDao goodsDao;
	/**
	 * 
	 * 商品公共信息查询
	 * @param goods_barcode 商品条码
	 * @param shop_unique 供货商编号
	 * @return
	 */
	public ShopsResult queryBaseGoodsMessage(String goodsBarcode,String shopUnique){
		ShopsResult shop=new ShopsResult();
		Map<String,Object> map=new HashMap<String,Object>();
		map.put("goodsBarcode", goodsBarcode);
		map.put("shopUnique", shopUnique);
		BaseGoods mp=goodsDao.queryBaseGoodsMessage(map);
		if(null==mp){
			shop.setStatus(1);
			shop.setMsg("没有满足条件的商品信息！");
			return shop;
		}
		shop.setData(mp);
		shop.setStatus(1);
		shop.setMsg("查询成功！");
		return shop;
	}
	
	public PurResult getGoodsDictList(Map<String, Object> params) {
		PurResult result=new PurResult();			
		List<Map<String,Object>> goodsList=dictDao.getGoodsDictList(params);
		Integer count = dictDao.getGoodsDictListCount(params);
		result.setStatus(1);
		result.setMsg("查询成功");
		result.setCount(count);
		result.setData(goodsList);
		return result;
	}
	
	public PurResult updateGoodsDictImg(String goods_barcode,String goods_picturepath, HttpServletRequest request) {
		PurResult shop=new PurResult();
		Map<String,Object> map=new HashMap<String, Object>();
		map.put("goods_barcode", goods_barcode);
		map.put("goods_picturepath", goods_picturepath);
		int k=dictDao.updateGoodsDictMsg(map);
		if(0==k){
			shop.setStatus(0);
			shop.setMsg("更新失败！");
			return shop;
		}
		shop.setStatus(1);
		shop.setMsg("更新成功！");
		return shop;
	}
	
	public ShopsResult updateGoodsDictMsg(Map<String,Object> map){
		ShopsResult sr = new ShopsResult(1, "更新成功！");
		try {
			if(map.get("barcode")==null || map.get("barcode").toString().equals("")){
				sr.setStatus(0);
				sr.setMsg("商品条码不能为空");
				return sr;
			}
			dictDao.updateGoodsDictMsg(map);
		} catch (Exception e) {
			e.printStackTrace();
			sr.setStatus(0);
			sr.setMsg("系统错误！");
		}
		return sr;
	}
}
