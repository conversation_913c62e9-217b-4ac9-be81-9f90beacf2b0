package org.haier.shop.service;

import java.sql.Timestamp;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;

import org.haier.shop.dao.CountDao;
import org.haier.shop.util.ShopsResult;
import org.springframework.stereotype.Service;

@Service
public class CountServiceImpl implements CountService{
	@Resource
	private CountDao countDao;
	
	
	
	/**
	 * 各时间段内用户注册量
	 * @param startTime
	 * @param endTime
	 * @return
	 */
	public ShopsResult cusCountByTime(Timestamp startTime,Timestamp endTime){
		ShopsResult shop=new ShopsResult();
		Map<String,Object> map=new HashMap<String,Object>();
		map.put("startTime", startTime);
		map.put("endTime", endTime);
		Map<String,Object> result=countDao.cusCountByTime(map);
		shop.setStatus(0);
		shop.setMsg("查询成功");
		shop.setData(result);
		return shop;
	}
	/**
	 * 各时间段内用户注册方式（ANDRIOD,IOS,微信）
	 * @param startTime
	 * @param endTime
	 * @return
	 */
	public ShopsResult cusCountByPhone(Timestamp startTime,Timestamp endTime){
		ShopsResult shop=new ShopsResult();
		Map<String,Object> map=new HashMap<String,Object>();
		map.put("startTime", startTime);
		map.put("endTime", endTime);
		List<Map<String,Object>> result=countDao.cusCountByPhone(map);
		shop.setStatus(0);
		shop.setMsg("查询成功");
		shop.setData(result);
		return shop;
	}
	/**
	 * 各时间段内活跃用户数量
	 * @param startTime
	 * @param endTime
	 * @return
	 */
	public ShopsResult cusCountByActive(Timestamp startTime,Timestamp endTime){
		ShopsResult shop=new ShopsResult();
		Map<String,Object> map=new HashMap<String,Object>();
		map.put("startTime", startTime);
		map.put("endTime", endTime);
		Map<String,Object> result =countDao.cusCountByActive(map);
		shop.setStatus(0);
		shop.setMsg("查询成功");
		shop.setData(result);
		return shop;
	}
	
	/**
	 *  各时间段内活跃用户数量(按区域)
	 * @param startTime
	 * @param endTime
	 * @return
	 */
	public ShopsResult cusCountByActArea(Timestamp startTime,Timestamp endTime){
		ShopsResult shop=new ShopsResult();
		Map<String,Object> map=new HashMap<String,Object>();
		map.put("startTime", startTime);
		map.put("endTime", endTime);
		List<Map<String,Object>> result=countDao.cusCountByActArea(map);
		shop.setStatus(0);
		shop.setMsg("查询成功");
		shop.setData(result);
		return shop;
	}
	/**
	 * 阶段内客户购买力(分区县,分时间)
	 * @param startTime
	 * @param endTime
	 * @return
	 */
	public ShopsResult cusPurPowerByArea(Timestamp startTime,Timestamp endTime){
		ShopsResult shop=new ShopsResult();
		Map<String,Object> map=new HashMap<String,Object>();
		map.put("startTime", startTime);
		map.put("endTime", endTime);
		List<Map<String,Object>> result=countDao.cusPurPowerByArea(map);
		shop.setStatus(0);
		shop.setMsg("查询成功");
		shop.setData(result);
		return shop;
	}
	/**
	 * 所有地区客户购买能力
	 * @param startTime
	 * @param endTime
	 * @return
	 */
	public ShopsResult cusPurPower(Timestamp startTime,Timestamp endTime){
		ShopsResult shop=new ShopsResult();
		Map<String,Object> map=new HashMap<String,Object>();
		map.put("startTime", startTime);
		map.put("endTime", endTime);
		Map<String,Object> result=countDao.cusPurPower(map);
		shop.setStatus(0);
		shop.setMsg("查询成功");
		shop.setData(result);
		return shop;
	}
	/**
	 *  各地区商户注册量
	 * @param startTime
	 * @param endTime
	 * @return
	 */
	public ShopsResult shopCountByArea(Timestamp startTime,Timestamp endTime){
		ShopsResult shop=new ShopsResult();
		Map<String,Object> map=new HashMap<String,Object>();
		map.put("startTime", startTime);
		map.put("endTime", endTime);
		List<Map<String,Object>> result=countDao.shopCountByArea(map);
		shop.setStatus(0);
		shop.setMsg("查询成功");
		shop.setData(result);
		return shop;
	}
	/**
	 * 各地区的订单量，订单金额统计，商品销售数量统计
	 * @param startTime
	 * @param endTime
	 * @return
	 */
	public ShopsResult statisByArea(Timestamp startTime,Timestamp endTime){
		ShopsResult shop=new ShopsResult();
		Map<String,Object> map=new HashMap<String,Object>();
		map.put("startTime", startTime);
		map.put("endTime", endTime);
		List<Map<String,Object>> result=countDao.statisByArea(map);
		shop.setStatus(0);
		shop.setMsg("查询成功");
		shop.setData(result);
		return shop;
	}
}
