package org.haier.shop.service;

import java.util.List;
import java.util.Map;

import javax.servlet.http.HttpServletRequest;

import org.haier.shop.util.PurResult;

public interface SysAgreementService {
	
	//获取协议详情
	public Map<String ,Object> querySysAgreement(String code);
	
	//修改协议详情
	public void updateSysAgreement(Map<String ,Object> params);
	
	public PurResult addFaultHanding(Map<String ,Object> params);
	
	public PurResult queryfaultHanding(HttpServletRequest request);
}
