package org.haier.shop.service;

import java.util.Map;

import org.haier.shop.util.ShopsResult;

public interface DataScreenService {
	
	public ShopsResult queryOnlineCount();
	
	public ShopsResult newShopsCountYN();
	
	/**
	 * 活跃店铺信息查询益农版
	 * @return
	 */
	public ShopsResult getActiveShopsMessageYN();
	
	/**
	 * 销量排行前五商品
	 * @return
	 */
	public ShopsResult top5GoodsMessage();
	public ShopsResult queryExpressCount();
	
	/**
	 * 各种支付方式占比
	 * @return
	 */
	public ShopsResult payTypeMessage();
	
	/**
	 * 订单达成率查询
	 * @return
	 */
	public ShopsResult proportionOfOrder();

	/**
	 * 查询指定时间内每一秒的订单数量和订单总金额
	 * @return
	 */
	public ShopsResult getSaleListTotalMessageBySecond();
	
	/**
	 * 相比昨日销售营业额对比
	 * @return
	 */
	public ShopsResult onLineSaleComparsionYesterday();
	
	/**
	 * 销量前五十商品名称查询
	 * @return
	 */
	public ShopsResult top50characterCloud();
	/**
	 * 系统当前时间
	 * @return
	 */
	public ShopsResult selectNow();
	/**
	 * 活跃店铺的信息查询
	 * @return
	 */
	public ShopsResult getActiveShopsMessage();
	
	
	/**
	 * 当日流水及订单量
	 * @return
	 */
	public ShopsResult getDailyTurnover();
	
	public ShopsResult getYestodayOrder();
	
	public ShopsResult queryEvaluateList();
	
	/**
	 * 本周新增店铺数量
	 * @return
	 */
	public ShopsResult newShopsCount();
	
	/**
	 * @return
	 */
	public ShopsResult orderTotalByHours(Double num);
	
	/**
	 * 最新订单信息
	 * @return
	 */
	public ShopsResult lastestOrder();
	/**
	 * 最新订单信息
	 * @return
	 */
	public ShopsResult queryYN();
	/**
	 * 销量前五的店铺销售状况
	 * @return
	 */
	public ShopsResult top5ShopsList();
	
	
	/**
	 * 商品分类销量
	 * @return
	 */
	public ShopsResult kindSaleRatio();
	
	public ShopsResult goodSalePriceCount();
	
	public ShopsResult getOrderList();
	
	public ShopsResult getShopList();
	
	public ShopsResult getRiderList();
	
	/**
	 * 近一月销售额
	 * @return
	 */
	public ShopsResult lastMonthSaleTotal(Map<String,Object> map);
	
	/**
	 * 近一月销售额
	 * @return
	 */
	public ShopsResult lastMonthSaleTotalYN(Map<String,Object> map);

	/**
	 * 线上采购额昨日同时段对比查询
	 * @return
	 */
	public ShopsResult onLinePurComparsionYesterday();
	
	/**
	 * 60S内采购订单数量统计
	 * @return
	 */
	public ShopsResult getPurListTotalMessageBySecond();
	
	/**
	 * 根据选择的省份，查询其下地级市列表
	 * @param map
	 * @return
	 */
	public ShopsResult getCitiesInProvince(Map<String,Object> map);
	
	/**
	 * 今日销量前三的商品信息查询
	 * @param map
	 * @return
	 */
	public ShopsResult queryTop3GrossProfit(Map<String,Object> map);
	
	/**
	 * 各类型的店铺数量查询
	 */
	public ShopsResult queryShopCountByType();
	
	public ShopsResult updateShopMsg();
	
	/**
	 * 获取各区县的站点数量
	 * @return
	 */
	public ShopsResult queryYNshopByArea(String areaDictNum);
}
