package org.haier.shop.service;

import java.io.IOException;
import java.net.URLEncoder;
import java.sql.Timestamp;
import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Date;
import java.util.GregorianCalendar;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Random;
import java.util.SortedMap;
import java.util.TreeMap;

import org.haier.meituan.util.MUtil;
import org.haier.shop.dao3.ShopSoftDao;
import org.haier.shop.util.AlipayConfig;
import org.haier.shop.util.PurResult;
import org.haier.shop.util.UUIDUtil;
import org.haier.shop.util.wxPay.HttpUtil;
import org.haier.shop.util.wxPay.PayConfigUtil;
import org.haier.shop.util.wxPay.PayToolUtil;
import org.haier.shop.util.wxPay.XMLUtil4jdom;
import org.jdom.JDOMException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.alibaba.fastjson.JSONObject;
import com.alipay.api.AlipayApiException;
import com.alipay.api.AlipayClient;
import com.alipay.api.DefaultAlipayClient;
import com.alipay.api.request.AlipayTradePrecreateRequest;
import com.alipay.api.response.AlipayTradePrecreateResponse;

@Service
public class ShopSoftServiceImpl implements ShopSoftService{
	
	@Autowired
    private ShopSoftDao shopSoftDao;

	@Override
	public List<Map<String, Object>> queryShopSoftList(Map<String, Object> params) {
		return shopSoftDao.queryShopSoftList(params);
	}

	@Override
	public Integer queryShopSoftListCount(Map<String, Object> params) {
		return shopSoftDao.queryShopSoftListCount(params);
	}

	@Override
	public List<Map<String, Object>> queryShopSoftProfitList(Map<String, Object> params) {
		return shopSoftDao.queryShopSoftProfitList(params);
	}

	@Override
	public Integer queryShopSoftProfitListCount(Map<String, Object> params) {
		return shopSoftDao.queryShopSoftProfitListCount(params);
	}

	@Override
	@Transactional
	public void deleteShopSoftProfit(String id) {
		shopSoftDao.deleteShopSoftProfit(id);
	}

	@Override
	public List<Map<String, Object>> querySysSoftSettingList() {
		return shopSoftDao.querySysSoftSettingList();
	}

	@Override
	public String weixinPay(Map<String, Object> params) {
		try {
			String profit_no = MUtil.strObject(params.get("profit_no"));
			String bug_type = MUtil.strObject(params.get("bug_type"));
			String cdkey_code = MUtil.strObject(params.get("cdkey_code"));
			String shop_unique = MUtil.strObject(params.get("shop_unique"));
			String device_no = MUtil.strObject(params.get("device_no"));
			
			String soft_code = "";
			if(bug_type.equals("1")) {//购买
				soft_code = UUIDUtil.getUUID32();
			}else if(bug_type.equals("2")) {//续费
				//获取店铺软件信息
				Map<String ,Object> shopSoft = shopSoftDao.queryShopSoft(cdkey_code);
				soft_code = MUtil.strObject(shopSoft.get("soft_code"));
			}
			
			//获取软件配置信息
	        String setting_code = MUtil.strObject(params.get("setting_code"));
	        Map<String ,Object> setting = shopSoftDao.querySysSoftSetting(setting_code);
	        Double price = Double.valueOf(MUtil.strObject(setting.get("price")));
	        Double discount = Double.valueOf(MUtil.strObject(setting.get("discount")));
	        Integer total_fee = (int) (price*discount*100);
			
			//添加商家购买软件记录表
			Map<String ,Object> shopSoftProfitParams = new HashMap<String ,Object>();
			shopSoftProfitParams.put("profit_no", profit_no);
			shopSoftProfitParams.put("soft_code", soft_code);
			shopSoftProfitParams.put("shop_unique", shop_unique);
			shopSoftProfitParams.put("pay_money", price*discount);
			shopSoftProfitParams.put("soft_time", setting.get("time"));
			shopSoftProfitParams.put("bug_type", bug_type);
			shopSoftProfitParams.put("pay_type", 2);//微信
			shopSoftProfitParams.put("setting_code", setting_code);
			shopSoftProfitParams.put("cdkey_code", cdkey_code);
			shopSoftProfitParams.put("device_no", device_no);
			shopSoftDao.addShopSoftProfit(shopSoftProfitParams);
//			String shop_soft_profit_id = MUtil.strObject(shopSoftProfitParams.get("id"));
			
	        String appid = PayConfigUtil.APP_ID;  // appid  
	        String mch_id = PayConfigUtil.MCH_ID; // 商户号
	        String key = PayConfigUtil.API_KEY; // key  
	        
	        String currTime = PayToolUtil.getCurrTime();  
	        String strTime = currTime.substring(8, currTime.length());  
	        String strRandom = PayToolUtil.buildRandom(4) + "";  
	        String nonce_str = strTime + strRandom; 
	        // 回调接口   
	        String notify_url = PayConfigUtil.NOTIFY_URL_SOFT;
	        String trade_type = "NATIVE";
	        
	        SortedMap<Object,Object> packageParams = new TreeMap<Object,Object>();  
	        packageParams.put("appid", appid);  
	        packageParams.put("mch_id", mch_id);  
	        packageParams.put("nonce_str", nonce_str);  
	        packageParams.put("body", "百货收银软件");  //（调整为自己的名称）
	        packageParams.put("out_trade_no", profit_no);  
	        packageParams.put("total_fee", total_fee.toString()); //价格的单位为分  
	        packageParams.put("spbill_create_ip", MUtil.strObject(params.get("spbill_create_ip")));  
	        packageParams.put("notify_url", notify_url);  
	        packageParams.put("trade_type", trade_type);
//	        Map<String ,Object> attachMap = new HashMap<String ,Object>();
//	        attachMap.put("cdkey_code", cdkey_code);
//	        attachMap.put("shop_unique", shop_unique);
//	        attachMap.put("setting_code", setting_code);
////	        attachMap.put("bug_type", bug_type);
////	        attachMap.put("soft_code", soft_code);
//	        attachMap.put("id", shop_soft_profit_id);
//	        packageParams.put("attach",JSONObject.toJSON(attachMap).toString());//附加参数
	  
	        String sign = PayToolUtil.createSign("UTF-8", packageParams,key);  
	        packageParams.put("sign", sign);
	          
	        String requestXML = PayToolUtil.getRequestXml(packageParams);  
	        System.out.println(requestXML);  
	   
	        String resXml = HttpUtil.postData(PayConfigUtil.UFDODER_URL, requestXML);  
	        
			Map map = XMLUtil4jdom.doXMLParse(resXml);
			
			String urlCode = (String) map.get("code_url");  
		        
		    return urlCode;
		} catch (JDOMException e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		} catch (IOException e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		}  
		return "";
	}
	
	@Override
	public String aliPay(Map<String, Object> params) throws AlipayApiException {
		try {
			String profit_no = MUtil.strObject(params.get("profit_no"));
			String bug_type = MUtil.strObject(params.get("bug_type"));
			String cdkey_code = MUtil.strObject(params.get("cdkey_code"));
			String shop_unique = MUtil.strObject(params.get("shop_unique"));
			String device_no = MUtil.strObject(params.get("device_no"));
			
			String soft_code = "";
			if(bug_type.equals("1")) {//购买
				soft_code = UUIDUtil.getUUID32();
			}else if(bug_type.equals("2")) {//续费
				//获取店铺软件信息
				Map<String ,Object> shopSoft = shopSoftDao.queryShopSoft(cdkey_code);
				soft_code = MUtil.strObject(shopSoft.get("soft_code"));
			}
			
			//获取软件配置信息
	        String setting_code = MUtil.strObject(params.get("setting_code"));
	        Map<String ,Object> setting = shopSoftDao.querySysSoftSetting(setting_code);
	        Double price = Double.valueOf(MUtil.strObject(setting.get("price")));
	        Double discount = Double.valueOf(MUtil.strObject(setting.get("discount")));
	        Double total_amount = price*discount;
			
			//添加商家购买软件记录表
			Map<String ,Object> shopSoftProfitParams = new HashMap<String ,Object>();
			shopSoftProfitParams.put("profit_no", profit_no);
			shopSoftProfitParams.put("soft_code", soft_code);
			shopSoftProfitParams.put("shop_unique", shop_unique);
			shopSoftProfitParams.put("pay_money", price*discount);
			shopSoftProfitParams.put("soft_time", setting.get("time"));
			shopSoftProfitParams.put("bug_type", bug_type);
			shopSoftProfitParams.put("pay_type", 1);//支付宝
			shopSoftProfitParams.put("setting_code", setting_code);
			shopSoftProfitParams.put("cdkey_code", cdkey_code);
			shopSoftProfitParams.put("device_no", device_no);
			shopSoftDao.addShopSoftProfit(shopSoftProfitParams);
//			String shop_soft_profit_id = MUtil.strObject(shopSoftProfitParams.get("id"));
			
			 //附加参数
	        Map<String ,Object> attachMap = new HashMap<String ,Object>();
//	        attachMap.put("cdkey_code", params.get("cdkey_code"));
//	        attachMap.put("shop_unique", params.get("shop_unique"));
//	        attachMap.put("setting_code", params.get("setting_code"));
//	        attachMap.put("bug_type", params.get("bug_type"));
//	        attachMap.put("soft_code", soft_code);
//	        attachMap.put("id", shop_soft_profit_id);
	        
	        String passback_params = URLEncoder.encode(JSONObject.toJSON(attachMap).toString(),"UTF-8");
			
			AlipayClient alipayClient = new DefaultAlipayClient(AlipayConfig.gatewayUrl, AlipayConfig.app_id, AlipayConfig.merchant_private_key, "json", AlipayConfig.charset, AlipayConfig.alipay_public_key, AlipayConfig.sign_type);
			AlipayTradePrecreateRequest request = new AlipayTradePrecreateRequest();
			request.setReturnUrl(AlipayConfig.return_url_soft);
			request.setNotifyUrl(AlipayConfig.return_url_soft);
			request.setBizContent("{\"out_trade_no\":\""+ profit_no +"\"," 
	    			+ "\"total_amount\":\""+ String.format("%.2f", total_amount) +"\"," 
	    			+ "\"subject\":\"百货收银软件\","
	    			+ "\"passback_params\":\""+ passback_params +"\"}");
			AlipayTradePrecreateResponse response = alipayClient.execute(request);
			if(response.isSuccess()){
				return response.getQrCode();
			}else{
				return "";
			}
		} catch (IOException e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		}  
		return "";
	}

	@Override
	@Transactional
	public boolean paySuccess(Map<String, Object> params) {
		boolean flag = false;
		try {
			String profit_no = MUtil.strObject(params.get("profit_no"));
			String trans_num = MUtil.strObject(params.get("trans_num"));
			String pay_money = MUtil.strObject(params.get("pay_money"));
			System.out.println("profit_no:"+profit_no);
			//获取购买记录信息
			Map<String ,Object> shopSoftProfitParams = new HashMap<String ,Object>();
			shopSoftProfitParams.put("profit_no", profit_no);
			Map<String ,Object> shopSoftProfit = shopSoftDao.getShopSoftProfit(shopSoftProfitParams);
			String bug_type = MUtil.strObject(shopSoftProfit.get("bug_type"));
			String soft_code = MUtil.strObject(shopSoftProfit.get("soft_code"));
			String setting_code = MUtil.strObject(shopSoftProfit.get("setting_code"));
			String cdkey_code = MUtil.strObject(shopSoftProfit.get("cdkey_code"));
			String shop_unique = MUtil.strObject(shopSoftProfit.get("shop_unique"));
			String shop_soft_profit_id = MUtil.strObject(shopSoftProfit.get("id"));
			String device_no = MUtil.strObject(shopSoftProfit.get("device_no"));
			
	        if(bug_type.equals("1")) {//购买
	        	Map<String ,Object> shopSoft = shopSoftDao.queryShopSoft(cdkey_code);
	        	if(shopSoft != null) {//说明已经回调成功
	        		return true;
	        	}
			}
			
			//获取软件配置信息
	        Map<String ,Object> setting = shopSoftDao.querySysSoftSetting(setting_code);
			
	        String due_time = "";
			Integer time = Integer.parseInt(MUtil.strObject(setting.get("time")));
			if(time != null && time == 0) {//永久
				due_time = "9999-01-01 00:00:00";
			}else {
				Calendar calendar = new GregorianCalendar();
				if(bug_type.equals("1")) {//购买
					Date now = new Date();
					calendar.setTime(now); 
				}else if(bug_type.equals("2")) {//续费
					Map<String ,Object> shopSoft = shopSoftDao.queryShopSoft(cdkey_code);
					Timestamp old_due_time = (Timestamp) shopSoft.get("due_time");
					calendar.setTime(old_due_time);
				}
				calendar.add(calendar.YEAR, time);
				Date date = calendar.getTime();
	            SimpleDateFormat sdf1 = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
	            due_time = sdf1.format(date);
			}
	        
			if(bug_type.equals("1")) {//购买
				//添加店铺软件信息
				Map<String ,Object> shopSoftParams = new HashMap<String ,Object>();
				shopSoftParams.put("soft_code", soft_code);
				shopSoftParams.put("cdkey_code", cdkey_code);
				shopSoftParams.put("shop_unique", shop_unique);
				shopSoftParams.put("due_time", due_time);
				shopSoftParams.put("device_no", device_no);
				shopSoftParams.put("status", 2);
				shopSoftDao.addShopSoft(shopSoftParams);
			}else if(bug_type.equals("2")) {//续费
				//修改店铺软件信息
				Map<String ,Object> shopSoftParams = new HashMap<String ,Object>();
				shopSoftParams.put("soft_code", soft_code);
				shopSoftParams.put("due_time", due_time);
				shopSoftParams.put("shop_unique", shop_unique);
				shopSoftParams.put("status", 2);
				shopSoftDao.updateShopSoft(shopSoftParams);
			}
			
			//修改购买记录成功
			shopSoftProfitParams.put("pay_status", 2);//支付成功
			shopSoftProfitParams.put("trans_num", trans_num);
			shopSoftDao.updateShopSoftProfit(shopSoftProfitParams);
			
			Double pt_profit = 0.00;
			
			//获取商家代理及上级代理信息
			Map<String ,Object> agencyInfo = shopSoftDao.queryAgencyInfo(shop_unique);
			String agency_code = MUtil.strObject(agencyInfo.get("agency_code"));
			String agency_type = MUtil.strObject(agencyInfo.get("agency_type"));
			String p_agency_code = MUtil.strObject(agencyInfo.get("p_agency_code"));
			String p_agency_type = MUtil.strObject(agencyInfo.get("p_agency_type"));
			
			if(agency_type != null && agency_type.equals("1")) {//县级代理
				Double agency_profit = Double.valueOf(MUtil.strObject(setting.get("area_agent_price")));
				//修改县级代理余额
				Map<String ,Object> agencyAmountParams = new HashMap<String ,Object>(); 
				agencyAmountParams.put("agency_code", agency_code);
				agencyAmountParams.put("shop_profit", agency_profit);
				Map<String ,Object> account = shopSoftDao.queryAgencyAccount(agencyAmountParams);
				if(account != null) {
					shopSoftDao.updateAgencyAccount(agencyAmountParams);
				}else {
					shopSoftDao.addAgencyAccount(agencyAmountParams);
				}
				//添加商家购买软件县级代理分润表
				Map<String ,Object> profitParams = new HashMap<String ,Object>();
				profitParams.put("shop_soft_profit_id", shop_soft_profit_id);
				profitParams.put("agency_code", agency_code);
				profitParams.put("agency_profit", agency_profit);
				shopSoftDao.addShopSoftProfitDetail(profitParams);
				
				if(p_agency_type != null && p_agency_type.equals("0")) {//上级为市级代理
					Double p_agency_profit = Double.valueOf(MUtil.strObject(setting.get("city_agent_price")))-Double.valueOf(MUtil.strObject(setting.get("area_agent_price")));
					//修改市级代理余额
					Map<String ,Object> agencyAmountParams1 = new HashMap<String ,Object>(); 
					agencyAmountParams1.put("agency_code", p_agency_code);
					agencyAmountParams1.put("shop_profit", p_agency_profit);
					Map<String ,Object> account1 = shopSoftDao.queryAgencyAccount(agencyAmountParams1);
					if(account1 != null) {
						shopSoftDao.updateAgencyAccount(agencyAmountParams1);
					}else {
						shopSoftDao.addAgencyAccount(agencyAmountParams1);
					}
					
					//添加商家购买软件市级代理分润表
					Map<String ,Object> profitParams1 = new HashMap<String ,Object>();
					profitParams1.put("shop_soft_profit_id", shop_soft_profit_id);
					profitParams1.put("agency_code", p_agency_code);
					profitParams1.put("agency_profit", p_agency_profit);
					shopSoftDao.addShopSoftProfitDetail(profitParams1);
					
					pt_profit = Double.valueOf(pay_money)-agency_profit-p_agency_profit;
				}else if(p_agency_type != null && p_agency_type.equals("2")){//上级为总平台
					pt_profit = Double.valueOf(pay_money)-agency_profit;
				}
			}
			
			if(agency_type != null && agency_type.equals("0")) {//市级代理
				Double agency_profit = Double.valueOf(MUtil.strObject(setting.get("city_agent_price")));
				//修改市级代理余额
				Map<String ,Object> agencyAmountParams = new HashMap<String ,Object>(); 
				agencyAmountParams.put("agency_code", agency_code);
				agencyAmountParams.put("shop_profit", agency_profit);
				Map<String ,Object> account = shopSoftDao.queryAgencyAccount(agencyAmountParams);
				if(account != null) {
					shopSoftDao.updateAgencyAccount(agencyAmountParams);
				}else {
					shopSoftDao.addAgencyAccount(agencyAmountParams);
				}
				
				//添加商家购买软件市级代理分润表
				Map<String ,Object> profitParams = new HashMap<String ,Object>();
				profitParams.put("shop_soft_profit_id", shop_soft_profit_id);
				profitParams.put("agency_code", agency_code);
				profitParams.put("agency_profit", agency_profit);
				shopSoftDao.addShopSoftProfitDetail(profitParams);
				
				if(p_agency_type != null && p_agency_type.equals("2")){//上级为总平台
					pt_profit = Double.valueOf(pay_money)-agency_profit;
				}
			}
			if(agency_type != null && agency_type.equals("2")) {//总平台
				pt_profit = Double.valueOf(pay_money);
			}
			
			//修改总平台余额
			Map<String ,Object> ptAgencyAmountParams = new HashMap<String ,Object>(); 
			ptAgencyAmountParams.put("agency_code", "110");
			ptAgencyAmountParams.put("shop_profit", pt_profit);
			Map<String ,Object> account = shopSoftDao.queryAgencyAccount(ptAgencyAmountParams);
			if(account != null) {
				shopSoftDao.updateAgencyAccount(ptAgencyAmountParams);
			}else {
				shopSoftDao.addAgencyAccount(ptAgencyAmountParams);
			}
			
			//添加商家购买软件平台分润表
			Map<String ,Object> ptProfitParams = new HashMap<String ,Object>();
			ptProfitParams.put("shop_soft_profit_id", shop_soft_profit_id);
			ptProfitParams.put("agency_code", "110");
			ptProfitParams.put("agency_profit", pt_profit);
			shopSoftDao.addShopSoftProfitDetail(ptProfitParams);
			
			flag = true;
		} catch (Exception e) {
			e.printStackTrace();
		}
		return flag;
	}

	@Override
	public Map<String, Object> querySysSoftSetting(String code) {
		return shopSoftDao.querySysSoftSetting(code);
	}
	
	@Override
	public String getCdkeyCode(int n) {
		String string = "0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ";//保存数字0-9 和 大小写字母
		char[] ch = new char[n];
		for (int i = 0; i < n; i++) {
			Random random = new Random();//创建一个新的随机数生成器
			int index = random.nextInt(string.length());//返回[0,string.length)范围的int值    作用：保存下标
			ch[i] = string.charAt(index);//charAt() : 返回指定索引处的 char 值   ==》保存到字符数组对象ch里面
		}
		//将char数组类型转换为String类型保存到result
		String result = String.valueOf(ch);
		//查询激活码是否重复
		Integer count = shopSoftDao.getCdkeyCodeCount(result);
		if(count > 0) {
			return getCdkeyCode(n);
		}else {
			return result;
		}
	}
	
	@Override
	public PurResult isPaySuccess(Map<String, Object> params) {
		PurResult result = new PurResult();
		try {
			Map<String ,Object> shopSoftProfit = shopSoftDao.getShopSoftProfit(params);
			Integer pay_status = Integer.parseInt(MUtil.strObject(shopSoftProfit.get("pay_status")));
			result.setData(params.get("profit_no"));
			if(pay_status != 2) {
				result.setStatus(2);
				result.setMsg("未支付成功");
				
				return result;
			}
			result.setStatus(1);
			result.setMsg("支付成功");
		} catch (Exception e) {
			e.printStackTrace();
			result.setStatus(0);
			result.setMsg("异常");
		}
		
		return result;
	}
}
