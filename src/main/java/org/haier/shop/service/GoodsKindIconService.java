package org.haier.shop.service;

import org.haier.shop.util.MyException;
import org.haier.shop.util.PurResult;
import org.haier.shop.util.ShopsResult;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import java.util.Map;

public interface GoodsKindIconService {
	/**
	 * 添加图标信息
	 * @param file 图标文件
	 * @param shop_type 行业类型
	 * @param icon_type 是否自定义
	 * @param shop_unique 商铺唯一标识符
	 * @param create_user 创建人
	 * @return
	 */
	public ShopsResult addGoodsKindIcon(MultipartFile file, Integer shop_type, Integer icon_type, String shop_unique, String create_user, HttpServletRequest request) throws MyException;

	public ShopsResult queryListByIconType(Integer icon_type, String shop_unique);

}
