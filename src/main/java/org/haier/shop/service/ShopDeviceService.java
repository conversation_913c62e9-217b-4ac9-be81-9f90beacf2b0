package org.haier.shop.service;

import java.util.List;
import java.util.Map;

import org.haier.shop.util.PurResult;

public interface ShopDeviceService {
	/**
	 * 查询店铺设备列表
	 * @param shop_unique 店铺编号
	 * @param device_status 状态：1未激活 2已激活 3禁用
	 * @param start_time 开始时间
	 * @param end_time 结束时间
	 * @param startNum
	 * @param pageSize
	 * @return
	 */
	public List<Map<String ,Object>> queryShopDeviceList(Map<String,Object> params);
	
	/**
	 * 查询店铺设备列表总条数
	 * @param shop_unique 店铺编号
	 * @param device_status 状态：1未激活 2已激活 3禁用
	 * @param start_time 开始时间
	 * @param end_time 结束时间
	 * @param startNum
	 * @param pageSize
	 * @return
	 */
	public Integer queryShopDeviceListCount(Map<String,Object> params);
	
	/**
	 * 查询店铺设备汇总信息
	 * @param shop_unique 店铺编号
	 * @return
	 */
	public Map<String ,Object> queryShopDeviceSumInfo(String shop_unique);
	
	/**
	 * 查询店铺设备详情信息
	 * @param shop_device_id 店铺设备id
	 * @return
	 */
	public Map<String ,Object> queryShopDeviceDetail(String shop_device_id);
	
	/**
	 * 查询店铺设备申请列表
	 * @param shop_unique 店铺编号
	 * @param message 设备名称/经办人
	 * @param apply_status 申请状态：1 申请中 2待收货 3已完成 10已取消
	 * @param start_time 开始时间
	 * @param end_time 结束时间
	 * @param startNum
	 * @param pageSize
	 * @return
	 */
	public List<Map<String ,Object>> shopDeviceApplyList(Map<String,Object> params);
	
	/**
	 * 查询店铺设备申请列表总条数
	 * @param shop_unique 店铺编号
	 * @param message 设备名称/经办人
	 * @param apply_status 申请状态：1 申请中 2待收货 3已完成 10已取消
	 * @param start_time 开始时间
	 * @param end_time 结束时间
	 * @param startNum
	 * @param pageSize
	 * @return
	 */
	public Integer shopDeviceApplyListCount(Map<String,Object> params);
	
	/**
	 * 查询设备类型列表
	 * @return
	 */
	public List<Map<String ,Object>> queryDeviceTypeList();
	
	/**
	 * 添加设备申请记录
	 * @param shop_unique 店铺编号
	 * @param oper_name 经办人姓名
	 * @param shop_name 店铺名称
	 * @param sum_deposit 总押金
	 * @param sum_count 总数量
	 * @param apply_remarks 备注
	 * @param device_list [
	 * 		{
	 * 			device_type_id 设备列表id
	 * 			device_count 申请设备数量
	 * 			device_deposit 单台设备押金
	 * 			sum_deposit 合计押金
	 * 			device_type 类型（1 收银机 ，需要输入编号 2 普通设备）
	 * 		}
	 * ]
	 * @return
	 */
	public PurResult addDeviceApply(Map<String ,Object> params);
	
	/**
	 * 修改设备申请记录
	 * @param id 设备申请id
	 * @return
	 */
	public PurResult updateDeviceApply(Map<String ,Object> params);
	
	/**
	 * 查询店铺设备申请详情信息
	 * @param id 设备申请id
	 * @return
	 */
	public Map<String ,Object> queryDeviceApplyDetail(String id);
	
	/**
	 * 查询店铺设备退换列表
	 * @param shop_unique 店铺编号
	 * @param message 设备名称/设备编号/经办人
	 * @param shop_service_apply_type 申请类型：1、换货；2、退货；
	 * @param shop_service_handle_status 订单处理状态：1、未处理（新申请）；2、已受理；3、服务进行中；4、服务完成；10、申请以取消；
	 * @param start_time 开始时间
	 * @param end_time 结束时间
	 * @param startNum
	 * @param pageSize
	 * @return
	 */
	public List<Map<String ,Object>> shopDeviceReturnList(Map<String,Object> params);
	
	/**
	 * 查询店铺设备退换列表总条数
	 * @param shop_unique 店铺编号
	 * @param message 设备名称/设备编号/经办人
	 * @param shop_service_apply_type 申请类型：1、换货；2、退货；
	 * @param shop_service_handle_status 订单处理状态：1、未处理（新申请）；2、已受理；3、服务进行中；4、服务完成；10、申请以取消；
	 * @param start_time 开始时间
	 * @param end_time 结束时间
	 * @param startNum
	 * @param pageSize
	 * @return
	 */
	public Integer shopDeviceReturnListCount(Map<String,Object> params);
	
	/**
	 * 添加设备退换记录
	 * @param shop_name 店铺名称
	 * @param shop_address 店铺地址
	 * @param shop_user_name 店铺联系人
	 * @param shop_user_tel 联系电话
	 * @param shop_unique 店铺编号
	 * @param shop_service_sum_deposit 总押金
	 * @param shop_service_sum_count 总数量
	 * @param shop_service_apply_type 申请类型：1、换货；2、退货
	 * @param shop_service_remarks 备注
	 * @param apply_user_name 经办人
	 * @param device_list [
	 * 		{
	 * 			device_type_id 设备列表id
	 * 			shop_service_detail_count 申请设备数量
	 * 			shop_service_detail_deposit 押金
	 * 			shop_service_detail_device_no 设备编号
	 * 			shop_device_id 店铺设备id
	 * 		}
	 * ]
	 * @return
	 */
	public PurResult addDeviceReturn(Map<String ,Object> params);
	
	/**
	 * 查询店铺设备退换详情信息
	 * @param id 设备退换申请id
	 * @return
	 */
	public Map<String ,Object> queryDeviceReturnDetail(String id);
	
	/**
	 * 修改设备售后记录
	 * @param id 设备申请id
	 * @return
	 */
	public PurResult updateShopService(Map<String ,Object> params);
	
	/**
	 * 查询店铺设备维修列表
	 * @param shop_unique 店铺编号
	 * @param message 设备名称/设备编号
	 * @param start_time 开始时间
	 * @param end_time 结束时间
	 * @param startNum
	 * @param pageSize
	 * @return
	 */
	public List<Map<String ,Object>> shopDeviceRepairList(Map<String,Object> params);
	
	/**
	 * 查询店铺设备维修列表总条数
	 * @param shop_unique 店铺编号
	 * @param message 设备名称/设备编号
	 * @param start_time 开始时间
	 * @param end_time 结束时间
	 * @param startNum
	 * @param pageSize
	 * @return
	 */
	public Integer shopDeviceRepairListCount(Map<String,Object> params);
	
	/**
	 * 添加设备保修记录
	 * @param shop_name 店铺名称
	 * @param shop_address 店铺地址
	 * @param shop_user_name 店铺联系人
	 * @param shop_user_tel 联系电话
	 * @param shop_unique 店铺编号
	 * @param shop_service_sum_deposit 总押金
	 * @param shop_service_sum_count 总数量
	 * @param shop_service_apply_type 申请类型：1、换货；2、退货
	 * @param shop_service_remarks 备注
	 * @param apply_user_name 经办人
	 * @param device_type_id 设备列表id
	 * @param shop_service_detail_count 申请设备数量
	 * @param shop_service_detail_deposit 押金
	 * @param shop_service_detail_device_no 设备编号
	 * @param shop_device_id 店铺设备id
	 * @return
	 */
	public PurResult addDeviceRepair(Map<String ,Object> params);
	
	/**
	 * 获取设备激活码信息
	 * @param shop_unique 店铺编号
	 * @param device_no 设备编号
	 * @return
	 */
	public PurResult getShopDeviceCdkeyInfo(Map<String ,Object> params);
	
	/**
	 * 设备激活码激活
	 * @param shop_unique 店铺编号
	 * @param device_no 设备编号
	 * @param cdkey_code 激活码
	 * @return
	 */
	public PurResult cdkeyActivation(String shop_unique,String device_no,String cdkey_code);
}
