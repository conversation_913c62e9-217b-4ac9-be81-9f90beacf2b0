package org.haier.shop.service;

import java.util.Map;

import org.haier.shop.util.MyException;
import org.haier.shop.util.PurResult;
import org.haier.shop.util.ShopsResult;

public interface GoodsKindService {
	/**
	 * 商品分类查询
	 * @param shop_unique 店铺编号
	 * @param goods_kind_parunique 商品分类父类编号
	 * @return
	 */
	public ShopsResult queryGoodsKinds(String shop_unique,String goods_kind_parunique);
	
	/**
	 * 商品分类查询（包含子类）
	 * @return
	 */
	public ShopsResult queryGoodsGroups(String shop_unique);
	/**
	 * 删除已有商品分类，添加新的商品分类
	 * @param shop_uniqeu
	 * @param goodsKindsUniques
	 * @return
	 */
	public ShopsResult modifyGoodsKinds(String shop_unique,String[] goodsKindsUniques);
	
	/**
	 * 添加新的商品分类申请
	 * @param map
	 * @return
	 */
	public ShopsResult sureNewKind(Map<String,Object> map);
	
	public PurResult addGoodsKinds(String shop_unique, String goods_kind_unique);

	public PurResult deleteGoodsKind(String shop_unique, String goods_kind_unique);
	
	public PurResult queryAllGoodsKinds(String shop_unique);
	/**
	 * 查询店内所有一级二级商品分类
	 * @return
	 */
	public ShopsResult queryAllKindsInShops(Map<String,Object> map);
	
	/**
	 * 查询商品分类信息
	 * @param map
	 * @return
	 * @throws MyException
	 */
	public ShopsResult queryGoodsKindsWithGoodsCount(Map<String,Object> map) throws MyException;
	
	/**
	 * 更新商品分类信息
	 * @param map
	 * @return
	 * @throws MyException
	 */
	public ShopsResult modifyGoodsKindMsg(Map<String,Object> map)throws MyException;
	/**
	 * 删除商品分类信息
	 * @param map
	 * @return
	 * @throws MyException
	 */
	public ShopsResult deleteGoodsKind(Map<String,Object> map) throws MyException;
	
	/**
	 * 查询店铺所有一级分类 
	 * @param map
	 * @return
	 * @throws MyException
	 */
	public ShopsResult queryAllGroupMsgByType(Map<String,Object> map) throws MyException;
	/**
	 * 添加新的商品分类信息
	 * @param map
	 * @return
	 * @throws MyException 
	 */
	public ShopsResult addNewGoodsKindLay(Map<String,Object> map) throws MyException;
	
	/**
	 * 修改商品分类信息
	 * @param shopUnique
	 * @param kindType
	 * @return
	 */
	public ShopsResult useCustomeKind(String shopUnique,Integer kindType);
	
	/**
	 * 获取当前店铺的分类使用信息
	 * @param shopUnique
	 * @return
	 */
	public ShopsResult getShopNowKindType(String shopUnique);
}
