package org.haier.shop.service;

import java.util.HashMap;
import java.util.Map;

import javax.annotation.Resource;

import org.haier.shop.dao.ShopAddressDao;
import org.haier.shop.util.ShopsResult;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Service
public class ShopAddressServiceImpl implements ShopAddressService{
	
	@Resource
	private ShopAddressDao addressDao;
	
	/**
	 * 添加或修改店铺地址
	 * @param id 原地址ID
	 * @param shop_unique 店铺编号
	 * @param province_code 省编码
	 * @param city_code 市编码
	 * @param county_code 区编码
	 * @param longitude 经度
	 * @param latitude 维度
	 * @param contacts 联系人
	 * @param address_detail 收货地址
	 * @param contacts_phone 联系方式
	 * @param default_status 1、默认地址；0、非默认地址
	 * @param valid_status 1、正常；0、删除
	 * @return
	 */
	@Transactional
	public ShopsResult addNewShopAddress(
			String id,
			String shop_unique,
			String province_code,
			String city_code,
			String county_code,
			Double longitude,
			Double latitude,
			String contacts,
			String address_detail,
			String contacts_phone,
			Integer default_status,
			Integer valid_status
			) {
		ShopsResult sr = new ShopsResult(1, "操作成功");
		Map<String,Object> map = new HashMap<String,Object>();
		map.put("shop_unique", shop_unique);
		if(default_status == 1) {
			map.put("rid", id == null ? "-1":id);
			map.put("default_status", 0);
			//将所有现有地址更改为非默认地址
			addressDao.modifyShopAddressStatus(map);
			map.clear();
		}
		map.put("shop_unique", shop_unique);
		map.put("province_code", province_code);
		map.put("city_code", city_code);
		map.put("county_code", county_code);
		map.put("longitude", longitude);
		map.put("latitude", latitude);
		map.put("contacts", contacts);
		map.put("address_detail", address_detail);
		map.put("contacts_phone", contacts_phone);
		map.put("default_status", default_status);
		map.put("valid_status", valid_status == null ? 1 :valid_status);
		if(id != null && !id.equals("")) {
			map.put("id", id);
			addressDao.modifyShopAddressStatus(map);
		}else {
			addressDao.addNewShopAddress(map);
		}
		
		
		return sr;
	}
	
	/**
	 * 查询店铺的所有收货地址信息
	 * @param shopUnique
	 * @return
	 */
	public ShopsResult queryShopAddressList(String shopUnique) {
		ShopsResult sr = new ShopsResult(1,"查询成功！");
		sr.setData(addressDao.queryShopAddressList(shopUnique));
		return sr;
	}
}
