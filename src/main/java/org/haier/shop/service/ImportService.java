package org.haier.shop.service;

import java.util.List;
import java.util.Map;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.haier.shop.entity.importMsg.ImportSupplierGoods;
import org.haier.shop.util.ShopsResult;
import org.springframework.web.bind.annotation.RequestBody;

public interface ImportService {

	public ShopsResult queryGoodsMsg(List<String> list, String shopUnique, Integer count);
	public ShopsResult updateGoodsSupplier(ImportSupplierGoods importSupplierGoods);
	/**
	 * 分类信息导入
	 * @param request
	 * @param response
	 * @param datas
	 * @return
	 */
	public ShopsResult importGoodsKind (HttpServletRequest request,HttpServletResponse response,Map<String ,Object> datas);
	/**
	 * 商品信息导入
	 * @param request
	 * @param response
	 * @param datas
	 * @return
	 */
	public ShopsResult goodsImport(HttpServletRequest request,HttpServletResponse response,Map<String ,Object> datas);
	
	/**
	 * 会员信息导入
	 * @param request
	 * @param response
	 * @param datas
	 * @return
	 * 步骤一：获取上传的文件信息，将文件信息保存，
	 * 步骤二：读取文件信息，并将文件信息读取为List列表；
	 * 步骤三：删除已有的会员信息，防止重复插入；
	 * 步骤四：查询会员相关补充信息，例如：会员等级信息等
	 * 步骤五：将补充后的会员信息导入，并返回提示信息
	 */
	public ShopsResult customerImport(HttpServletRequest request,HttpServletResponse response,Map<String ,Object> datas,String saleListCashier);
	
	/**
	 * 订单信息导入
	 * @param request
	 * @param response
	 * @param datas
	 * @param saleListCashier
	 * @return
	 */
	public ShopsResult importSaleList(HttpServletRequest request,HttpServletResponse response,Map<String ,Object> datas);

	public ShopsResult importGoodsModifyPrice(HttpServletRequest request, HttpServletResponse response,
			Map<String, Object> datas);
}
