package org.haier.shop.service;

import java.io.File;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.UUID;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;

import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.HexUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpUtil;
import cn.hutool.json.JSONUtil;
import org.haier.shop.config.ProjectConfig;
import org.haier.meituan.util.MUtil;
import org.haier.shop.controller.SmsThread;
import org.haier.shop.dao.*;
import org.haier.shop.dao2.ShopTDao;
import org.haier.shop.entity.ShopBeansVO;
import org.haier.shop.entity.ShopPayMsg;
import org.haier.shop.entity.beans.PageData;
import org.haier.shop.entity.shop.StatisticShopVO;
import org.haier.shop.util.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;

import com.alibaba.fastjson.JSONObject;

@Service
@Transactional
public class FeedBackServiceImpl implements FeedBackService{
	@Resource
	private FeedBackDao feedBackDao;
	@Resource
	private GoldDao goldDao;
	@Resource
	private ShopTDao shopTDao;
	@Resource
	private ShopDao shopDao;
	@Resource
	private BeansDao beansDao;
	@Resource
	private PayTypeDao payTypeDao;

	@Resource
	private GoodsService goodsService;

	private static final Logger logger = LoggerFactory.getLogger(FeedBackServiceImpl.class);

	public PurResult queryFeedBackList(String orderMessage, String startTime, String endTime,
			String feed_back_source, String feed_back_type, String feed_back_status, Integer pageNum,
			Integer pageSize) {
		PurResult result=new PurResult();
		try {
			Map<String,Object> map=new HashMap<String, Object>();
			if(null==orderMessage||"".equals(orderMessage.trim())){
				orderMessage=null;
			}else{
				orderMessage="%"+orderMessage+"%";
				map.put("orderMessage", orderMessage);
			}
			if(startTime!=null&&!"".equals(startTime.trim())){
				map.put("startTime", startTime);
			}
			if(endTime!=null&&!"".equals(endTime.trim())){
				map.put("endTime", endTime);
			}
			if(!"-1".equals(feed_back_source)){
				map.put("feed_back_source", feed_back_source);
			}
			if(!"-1".equals(feed_back_type)){
				map.put("feed_back_type", feed_back_type);
			}
			if(!"-1".equals(feed_back_status)){
				map.put("feed_back_status", feed_back_status);
			}
			Map<String,Object> countMap=feedBackDao.queryFeedBackListCount(map);
			Integer count = Integer.parseInt(MUtil.strObject(countMap.get("count")));
			
			map.put("pageSize", pageSize);
			map.put("startNum", (pageNum-1)*pageSize);
			List<Map<String,Object>> orderList=feedBackDao.queryFeedBackList(map);
			result.setStatus(1);
			result.setMsg("查询成功！");
			result.setData(orderList);
			result.setCount(count);
		} catch (Exception e) {
			e.printStackTrace();
			result.setStatus(0);
			result.setMsg("异常");
		}
		return result;
	}


	public PurResult targetFeedBack(Integer feed_back_id,Integer feed_back_target_status) {
		PurResult result=new PurResult();
		Map<String,Object> map=new HashMap<String, Object>();
		map.put("feed_back_id", feed_back_id);
		map.put("feed_back_target_status", feed_back_target_status);
		feedBackDao.targetFeedBack(map);
		result.setStatus(1);
		result.setMsg("查询成功！");
		return result;
	}

	public Map<String,Object> queryFeedBackDetail(String feed_back_id) {
		return feedBackDao.queryFeedBackDetail(feed_back_id);
	}
	
	public List<Map<String,Object>> queryFeedBackImageList(String feed_back_id) {
		return feedBackDao.queryFeedBackImageList(feed_back_id);
	}

	public PurResult saveChuLi(String feed_back_id, String feed_back_remark) {
		PurResult result=new PurResult();
		Map<String,Object> map=new HashMap<String, Object>();
		map.put("feed_back_id", feed_back_id);
		map.put("feed_back_remark", feed_back_remark);
		feedBackDao.saveChuLi(map);
		result.setStatus(1);
		result.setMsg("查询成功！");
		return result;
	}


	public PurResult queryFeedBackPhoneList(String orderMessage, Integer pageNum, Integer pageSize) {
		PurResult result=new PurResult();
		try {
			Map<String,Object> map=new HashMap<String, Object>();
			if(null==orderMessage||"".equals(orderMessage.trim())){
				orderMessage=null;
			}else{
				orderMessage="%"+orderMessage+"%";
				map.put("orderMessage", orderMessage);
			}
			
			Map<String,Object> countMap=feedBackDao.queryFeedBackPhoneListCount(map);
			Integer count = Integer.parseInt(MUtil.strObject(countMap.get("count")));
			map.put("pageSize", pageSize);
			map.put("startNum", (pageNum-1)*pageSize);
			List<Map<String,Object>> orderList=feedBackDao.queryFeedBackPhoneList(map);
			result.setStatus(1);
			result.setMsg("查询成功！");
			result.setData(orderList);
			result.setCount(count);
		} catch (Exception e) {
			e.printStackTrace();
			result.setStatus(0);
			result.setMsg("异常");
		}
		return result;
	}


	public PurResult addFeedBackPhone(String feed_back_name, String feed_back_phone, String feed_back_type,
			String feed_back_flag) {
			PurResult result=new PurResult();
			Map<String,Object> map=new HashMap<String, Object>();
			map.put("feed_back_name", feed_back_name);
			map.put("feed_back_phone", feed_back_phone);
			map.put("feed_back_type", feed_back_type);
			map.put("feed_back_flag", feed_back_flag);
			feedBackDao.addFeedBackPhone(map);
			result.setStatus(1);
			result.setMsg("查询成功！");
			return result;
	}


	public Map<String, Object> getFeedBackPhoneInfo(String feed_back_phone_id) {
		Map<String,Object> map=new HashMap<String, Object>();
		map.put("feed_back_phone_id", feed_back_phone_id);
		Map<String, Object> map2=feedBackDao.getFeedBackPhoneInfo(map);
		return map2;
	}


	public PurResult editFeedBackPhone(String feed_back_phone_id, String feed_back_name, String feed_back_phone,
			String feed_back_type, String feed_back_flag) {
		PurResult result=new PurResult();
		Map<String,Object> map=new HashMap<String, Object>();
		map.put("feed_back_phone_id", feed_back_phone_id);
		map.put("feed_back_name", feed_back_name);
		map.put("feed_back_phone", feed_back_phone);
		map.put("feed_back_type", feed_back_type);
		map.put("feed_back_flag", feed_back_flag);
		feedBackDao.editFeedBackPhone(map);
		result.setStatus(1);
		result.setMsg("查询成功！");
		return result;
	}


	public PurResult deleteFeedBackPhone(String feed_back_phone_id) {
		PurResult result=new PurResult();
		Map<String,Object> map=new HashMap<String, Object>();
		map.put("feed_back_phone_id", feed_back_phone_id);
		feedBackDao.deleteFeedBackPhone(map);
		result.setStatus(1);
		result.setMsg("查询成功！");
		return result;
	}


	public PurResult queryShopQualificationList(String orderMessage, Integer pageNum, Integer pageSize) {
		PurResult result=new PurResult();
		Map<String,Object> map=new HashMap<String, Object>();
		if(null==orderMessage||"".equals(orderMessage.trim())){
			orderMessage=null;
		}else{
			orderMessage="%"+orderMessage+"%";
			map.put("orderMessage", orderMessage);
		}
		Map<String,Object> countMap=feedBackDao.queryShopQualificationListCount(map);
		Long count= (Long) countMap.get("count");
		if(count%pageSize==0){
			result.setCord(count/pageSize);
		}else{
			result.setCord(count/pageSize+1);
		}
		map.put("pageSize", pageSize);
		map.put("startNum", (pageNum-1)*pageSize);
		List<Map<String,Object>> orderList=feedBackDao.queryShopQualificationList(map);
		result.setStatus(0);
		result.setMsg("查询成功！");
		result.setData(orderList);
		return result;
	}


	public PurResult queryShopQualificationDetail(String shop_qualification_id) {
		PurResult result=new PurResult();
		Map<String,Object> map=new HashMap<String, Object>();
		map.put("shop_qualification_id", shop_qualification_id);
		List<Map<String,Object>> countMap=feedBackDao.queryShopQualificationDetail(map);
		result.setStatus(1);
		result.setMsg("查询成功！");
		result.setData(countMap);
		return result;
	}


	public PurResult queryShopExamineList(String orderMessage, String examinestatus, Integer pageNum,Integer pageSize,String area_dict_num,String shop_type) {
		PurResult result=new PurResult();
		try {
			Map<String,Object> map=new HashMap<String, Object>();
			if(null==orderMessage||"".equals(orderMessage.trim())){
				orderMessage=null;
			}else{
				orderMessage="%"+orderMessage+"%";
				map.put("orderMessage", orderMessage);
			}
			map.put("examinestatus", examinestatus);
			map.put("area_dict_num", area_dict_num);
			map.put("shop_type", shop_type);
			
			Map<String,Object> countMap=feedBackDao.queryShopExamineListCount(map);
			Integer count = Integer.parseInt(MUtil.strObject(countMap.get("count")));
			
			map.put("pageSize", pageSize);
			map.put("startNum", (pageNum-1)*pageSize);
			List<Map<String,Object>> orderList=feedBackDao.queryShopExamineList(map);
			
			result.setData(orderList);
			result.setCount(count);
			result.setStatus(1);
			result.setMsg("成功！");
		} catch (Exception e) {
			e.printStackTrace();
			result.setStatus(0);
			result.setMsg("异常");
		}
		return result;
	}


	public PurResult queryShopExamineDetail(String shop_unique, String type) {
		PurResult result=new PurResult();
		Map<String,Object> map=new HashMap<String, Object>();
		map.put("shop_unique", shop_unique);
		if("1".equals(type)){
			//查询商家
			Map<String,Object> shopInfo = feedBackDao.queryShopExamineDetail(map);
			//商家免密信息
			Map<String ,Object> payInfo = feedBackDao.queryShopPayInfo(shop_unique);
			shopInfo.put("payInfo", payInfo);
			result.setData(shopInfo);
		}else if("2".equals(type)){
			//查询供货商
			Map<String,Object> orderList=feedBackDao.querySupplierExamineDetail(map);
			result.setData(orderList);
		}
		result.setStatus(1);
		result.setMsg("成功");
		return result;
	}


	public PurResult saveShopExamine(String shop_unique, String type, String examinestatus, String examinestatus_reason,String pay_info_id,String examine_status,String userPhone, HttpServletRequest request) {
		PurResult result=new PurResult();
		Map<String,Object> map=new HashMap<String, Object>();
		map.put("shop_unique", shop_unique);
		map.put("examinestatus", examinestatus);
		map.put("examinestatus_reason", examinestatus_reason);
		if("1".equals(type)){			
			//审核通过(1.百货豆开通。2.分销功能开通3.返利功能开通，默认2%。4.金圈币默认开通。5.小程序默认开通 6.虚拟分类常用)
			if("4".equals(examinestatus)) {
				map.put("is_dis", "2");
				map.put("show_buy_status", "1");
				map.put("beans_agreement", "1");	
				//添加百货豆默认抵扣比例信息
				PageData pd = new PageData();
				ShopBeansVO beanDetai = beansDao.getShopBeans(shop_unique);
				pd.put("diKou", beanDetai.getDiKou());
				pd.put("shopUnique", shop_unique);
				beansDao.addDikou(pd);
				//添加会员返利
				Map<String,Object> params=new HashMap<String, Object>();
				params.put("shop_unique", shop_unique);
				params.put("reward", "20");
				params.put("reward_type", "0");
				params.put("valid", "1");
				feedBackDao.addMemReward(params);
				//开通小程序
				Map<String, Object> goldRule=goldDao.queryGoldRule();
				goldRule.put("shop_unique", shop_unique);
				goldRule.put("device_num", "0");
				goldDao.addGlodShop(goldRule);
				goldRule.put("giveOut_type", "0");
				goldRule.put("start_date", new Date());
				goldDao.addGlodOrder(goldRule);
				//开通分销
				Map<String ,Object> disParams = new HashMap<String, Object>(); 
				disParams.put("shop_unique", shop_unique);
				List<Map<String ,Object>> disLevelList = shopDao.getDisLevelList(disParams);
				if(disLevelList.size() == 0) {
					disParams.put("shop_unique", "0");
					List<Map<String ,Object>> disLevelListMoren = shopDao.getDisLevelList(disParams);
					List<Map<String ,Object>> list = new ArrayList<Map<String ,Object>>();
					for(int i=0;i<disLevelListMoren.size();i++) {
						Map<String ,Object> maps = new HashMap<String, Object>();  
						maps.put("shop_unique", shop_unique);
						maps.put("dis_level_name", disLevelListMoren.get(i).get("dis_level_name"));
						maps.put("level", disLevelListMoren.get(i).get("level"));
						maps.put("one_commission_ratio", disLevelListMoren.get(i).get("one_commission_ratio"));
						maps.put("two_commission_ratio", disLevelListMoren.get(i).get("two_commission_ratio"));
						list.add(maps);
					}
					shopDao.addDisLevel(list);
				}

				//6.20230704 增  虚拟分类常用 99990
				Map<String,Object> inventedMsg = new HashMap<String,Object>();
				map.put("goods_kind_unique", 99990);
				map.put("goods_kind_name", "常用");
				map.put("valid_type", 1);
				map.put("kind_sort", 1);
				map.put("shop_unique", shop_unique);
				shopDao.addNewGoodsKindInventedMsg(map);
				Map<String,Object> shopParams = new HashMap<String, Object>();
				shopParams.put("shop_unique", shop_unique);
				Map<String, Object> shopMap = feedBackDao.queryShopExamineDetail(shopParams);

				// 同步商户信息到纳统系统
				try {
					String invitationCode = (String) shopMap.get("invitation_code");
					if (StrUtil.isNotBlank(invitationCode)) {
						ShopPayMsg payMsgParams = new ShopPayMsg();
						payMsgParams.setShopUnique(shop_unique);
						payMsgParams.setValidType("1");
						ShopPayMsg shopPayMsg = payTypeDao.getShopPayMsg(payMsgParams);
						if (StrUtil.isNotBlank(invitationCode)) {
							StatisticShopVO shopVO = new StatisticShopVO();
							shopVO.setShopName((String) shopMap.get("NAME"));
							shopVO.setShopUnique((Long) shopMap.get("shop_unique"));
							shopVO.setAddress((String) shopMap.get("shop_address_detail"));
							shopVO.setShopPhone((String) shopMap.get("shop_phone"));
							shopVO.setInvitationCode((String) shopMap.get("invitation_code"));
							shopVO.setShopType((Integer) shopMap.get("shop_type"));
							if (null != shopPayMsg) {
								shopVO.setLegalPerson(shopPayMsg.getLegalPerson());
								shopVO.setLegalPhone(shopPayMsg.getUserPhone());
							}
							String url = ProjectConfig.STATISTIC_URL + Load.STATISITC_SHOP_URL;
							String data = JSONUtil.toJsonStr(shopVO);
							System.out.println("同步到纳统企业，请求地址:" + url + ", 数据：" + data);
							String syncResult = HttpUtil.post(url, data);
							System.out.println("同步商家数据到企业纳统返回结果:" + syncResult);
						}
					}

				} catch (Exception e) {
					System.err.println("--------------同步纳统信息失败：{}------" + e.getMessage());
					logger.error("--------------同步纳统信息失败：{}------", e);
					throw new RuntimeException(e);
				}

				// 商圈注册店铺，默认增加一个商品
				Integer shopType = (Integer) shopMap.get("shop_type");
				if (ObjectUtil.equals(14, shopType)) {
					addDefaultGoods(shop_unique, request);
				}
			}
			feedBackDao.updateShopExamine(map);
			
			shopTDao.updateShopExamine(map);
			
			//更新商家支付信息
			Map<String ,Object> params = new HashMap<>();
			params.put("id", pay_info_id);
			params.put("examine_status", examine_status);
			feedBackDao.updateShopPayInfo(params);
			
			//20221109 新增短信通知
			//短信发送，搞成本地不发送				
			try 
			{
				JSONObject jo = new JSONObject();
				if("4".equals(examinestatus)) {
					jo.put("reason", null);
					SmsThread smsThread=new SmsThread(userPhone,jo.toString(),"SMS_257721605");
					Thread t=new Thread(smsThread);
					t.start();
					
				}else
				{
					jo.put("reason", examinestatus_reason);
					SmsThread smsThread=new SmsThread(userPhone,jo.toString(),"SMS_257836999");
					Thread t=new Thread(smsThread);
					t.start();
				}
				
				
			}catch(Exception e)
			{
				e.printStackTrace();
			}
			
			
			

		}else if("2".equals(type)){
			//更新供货商
			feedBackDao.updateSupplierExamine(map);
		}
		result.setStatus(1);
		result.setMsg("成功");
		return result;
	}

	/**
	 * 创建商圈用户默认商品
	 * @param shop_unique
	 */
	private void addDefaultGoods(String shop_unique, HttpServletRequest request) {
		String goodsBrand = "";
		String kindUnique = "98001";
		String goodsMessage = "90909090@线下商品@1@1@1@@!]|@1@1@@1@1@0@1@0@";
		String goodsRemarks = "";
		String foreignKey = "";
		Integer goodsGround = 1;
		String ip = null;
		String userAgent = null;
		String goods_barcode= "90909090";
		String outStockCount = "0";
		String autoGoodsJson = null;
		String goodsDelete = null;
		String staff_id = null;
		String operateType = null;
		Integer sameType = 1;
		Integer goodsChengType = 0;
		String defaultSupplierUnique = null;
		String manager_unique = null;
		goodsService.saveGoodsMessage(operateType, shop_unique, goodsBrand, kindUnique, goodsMessage, goodsRemarks, foreignKey,  goodsGround, request, ip, userAgent, goods_barcode, outStockCount, autoGoodsJson, goodsDelete, staff_id, sameType, goodsChengType, defaultSupplierUnique, manager_unique);
	}

	public static void main(String[] args) {
		System.out.println(DateUtil.format(DateUtil.date(), DatePattern.NORM_DATETIME_MS_FORMAT));
		System.out.println(DateUtil.format(DateUtil.date(), DatePattern.NORM_DATETIME_MS_FORMAT));
		System.out.println(DateUtil.format(DateUtil.date(), DatePattern.NORM_DATETIME_MS_FORMAT));
		System.out.println(DateUtil.current());
		System.out.println(HexUtil.toHex(1));
		System.out.println(HexUtil.toHex(2));
		System.out.println(HexUtil.toHex(3));
		System.out.println(HexUtil.toHex(10));
		System.out.println(HexUtil.toHex(11));
		System.out.println(HexUtil.toHex(12));
		System.out.println(HexUtil.toHex(13));
		System.out.println(HexUtil.toHex(14));
		System.out.println(HexUtil.toHex(15));

	}




	public PurResult queryGoodsKindImageList(String orderMessage, Integer pageNum, Integer pageSize) {
		PurResult result=new PurResult();
		try {
			Map<String,Object> map=new HashMap<String, Object>();
			if(null==orderMessage||"".equals(orderMessage.trim())){
				orderMessage=null;
			}else{
				orderMessage="%"+orderMessage+"%";
				map.put("orderMessage", orderMessage);
			}
			Map<String,Object> countMap=feedBackDao.queryGoodsKindImageListCount(map);
			Integer count= Integer.parseInt(MUtil.strObject(countMap.get("count")));
			
			map.put("pageSize", pageSize);
			map.put("startNum", (pageNum-1)*pageSize);
			List<Map<String,Object>> orderList=feedBackDao.queryGoodsKindImageList(map);
			
			result.setStatus(1);
			result.setMsg("成功");
			result.setCount(count);
			result.setData(orderList);
		} catch (Exception e) {
			e.printStackTrace();
			result.setStatus(0);
			result.setMsg("异常");
		}
		return result;
	}


	public Map<String, Object> queryGoodsKindImageDetail(String goods_kind_unique) {
		Map<String,Object> map=new HashMap<String, Object>();
		map.put("goods_kind_unique", goods_kind_unique);
		Map<String, Object> map2=feedBackDao.queryGoodsKindImageDetail(map);
		return map2;
	}


	public ShopsResult editGoodsKindImage(HttpServletRequest request, String goods_kind_unique) {
		ShopsResult shop=new ShopsResult();
		Map<String,Object> map=new HashMap<String, Object>();
		map.put("goods_kind_unique", goods_kind_unique);
		MultipartFile file=null;
		if(request instanceof MultipartHttpServletRequest){
			MultipartHttpServletRequest multipartHttpServletRequest =(MultipartHttpServletRequest)request;
			Map<String, MultipartFile> mp = multipartHttpServletRequest.getFileMap();
			file = mp.get("goods_kind_image");
			
		}
		if(file!=null){
			String orName=file.getOriginalFilename();
			String lastName=orName.substring(orName.lastIndexOf("."));
			String filePath = this.getClass().getClassLoader().getResource("../../").getPath();
			File dir=new File(filePath);
			dir = dir.getParentFile();
			filePath = dir.getAbsolutePath() + File.separator + "image" + File.separator + goods_kind_unique + File.separator;
			dir = new File(filePath);
			if(!dir.exists()) {
				dir.mkdirs();
			}
			String shop_pictureName=UUID.randomUUID()+lastName;
			PicSaveUtil.handleFileUpId(file, request, filePath, shop_pictureName);//图片的保存
			String shop_picture_path="image" + File.separator + goods_kind_unique+ File.separator+shop_pictureName;
			if(shop_picture_path!=null&&!"".equals(shop_picture_path)){
				map.put("goods_kind_image", shop_picture_path);
			}
		}
		MultipartFile file2=null;
		if(request instanceof MultipartHttpServletRequest){
			MultipartHttpServletRequest multipartHttpServletRequest =(MultipartHttpServletRequest)request;
			Map<String, MultipartFile> mp = multipartHttpServletRequest.getFileMap();
			file2 = mp.get("goods_kind_hui_image");
			
		}
		if(file2!=null){
			String orName=file2.getOriginalFilename();
			String lastName=orName.substring(orName.lastIndexOf("."));
			String filePath = this.getClass().getClassLoader().getResource("../../").getPath();
			File dir=new File(filePath);
			dir = dir.getParentFile();
			filePath = dir.getAbsolutePath() + File.separator + "image" + File.separator + goods_kind_unique + File.separator;
			dir = new File(filePath);
			if(!dir.exists()) {
				dir.mkdirs();
			}
			String shop_pictureName=UUID.randomUUID()+lastName;
			PicSaveUtil.handleFileUpId(file2, request, filePath, shop_pictureName);//图片的保存
			String shop_picture_path="image" + File.separator + goods_kind_unique+ File.separator+shop_pictureName;
			if(shop_picture_path!=null&&!"".equals(shop_picture_path)){
				map.put("goods_kind_hui_image", shop_picture_path);
			}
		}
//		System.out.println("商家端信息更新MAP::::："+map);
		int k=feedBackDao.editGoodsKindImage(map);
//		System.out.println(k);
		if(0==k){
			shop.setStatus(0);
			shop.setMsg("更新失败！");
			return shop;
		}
		shop.setStatus(1);
		shop.setMsg("更新成功！");
		return shop;
	}



	
	
}
