package org.haier.shop.service;

import org.haier.shop.dao.MonitorInfoDao;
import org.haier.shop.util.PurResult;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.dao.DataAccessException;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Service
public class MonitorInfoServiceImpl implements MonitorInfoService{
    @Autowired
    private MonitorInfoDao monitorInfoDao;

    @Override
	public List<Map<String, Object>> showSixMonitor() {
    	Map<String,Object> map = new HashMap<String,Object>();
    	
    	List<Map<String,Object>> list = monitorInfoDao.queryMonitorList(map);
    	
		return list;
	}
    
    @Override
    public PurResult queryUnicomMonitorList(Map<String, Object> params) {
        PurResult result = new PurResult();
        try {
            List<Map<String ,Object>> list = monitorInfoDao.queryUnicomMonitorList(params);
            Integer count = monitorInfoDao.queryUnicomMonitorListCount(params);
            result.setData(list);
            result.setCount(count);
            result.setStatus(1);
            result.setMsg("成功！");
        } catch (Exception e) {
            e.printStackTrace();
            result.setStatus(0);
            result.setMsg("系统异常");
        }
        return result;
    }

    @Override
    public PurResult addMonitorInfo(Map<String, Object> params) {
        PurResult result = new PurResult();
        try {
            params.put("create_time", new Date());
            Integer count = monitorInfoDao.addMonitorInfo(params);
            result.setStatus(1);
            result.setMsg("成功！");
        }catch (DataAccessException ee){
            ee.printStackTrace();
            result.setStatus(0);
            result.setMsg("序列号重复录入！");
        }
        catch (Exception e) {
            e.printStackTrace();
            result.setStatus(0);
            result.setMsg("系统异常");
        }
        return result;
    }
    @Override
    public Map<String ,Object> selectMonitorInfoByMonitorInfoId(Long monitorInfoId)
    {
        return monitorInfoDao.selectMonitorInfoByMonitorInfoId(monitorInfoId);
    }

    @Override
    public PurResult updateMonitorInfo(Map<String, Object> params) {
        PurResult result = new PurResult();
        try {
            monitorInfoDao.updateMonitorInfo(params);
            result.setStatus(1);
            result.setMsg("成功！");
        } catch (Exception e) {
            e.printStackTrace();
            result.setStatus(0);
            result.setMsg("系统异常");
        }
        return result;
    }

	

}
