package org.haier.shop.service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;

import org.haier.meituan.util.MUtil;
import org.haier.shop.dao.InfoPushDao;
import org.haier.shop.dao.NoticeDao;
import org.haier.shop.redis.RedisCache;
import org.haier.shop.task.SentMsgToAndroid;
import org.haier.shop.util.HandleMessyCode;
import org.haier.shop.util.JPushClientUtil;
import org.haier.shop.util.PurResult;
import org.haier.shop.util.ServletsUtil;
import org.haier.shop.util.mqtt.ClientMQTT;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.google.gson.JsonObject;

@Service("noticeService")
public class NoticeServiceImpl implements NoticeService{
	
	@Resource
	private NoticeDao noticeDao;
	@Resource
	private InfoPushDao infoPushDao;
	@Resource
	private RedisCache redis;

	@Override
	public PurResult queryNoticeList(Map<String, Object> params) {
		PurResult result = new PurResult();
		try {
			List<Map<String ,Object>> list = noticeDao.queryNoticeList(params);
			Integer count = noticeDao.queryNoticeListCount(params);
			result.setData(list);
			result.setCount(count);
			
			result.setStatus(1);
			result.setMsg("成功");
		} catch (Exception e) {
			e.printStackTrace();
			result.setStatus(0);
			result.setMsg("异常");
		}
		
		return result;
	}

	@Override
	@Transactional
	public PurResult addNotice(HttpServletRequest request) {
		PurResult result = new PurResult();
		try {
			Map<String ,Object> params = HandleMessyCode.handle(ServletsUtil.getParameters(request));
			noticeDao.addNotice(params);
			Integer notice_revoke = Integer.parseInt(MUtil.strObject(params.get("notice_revoke")));
			if(notice_revoke == 2) {//发布
				//获取店铺列表
				List<Map<String ,Object>> shopList = noticeDao.queryShopList(params);
				if(shopList.size() > 0) {
					for(int i=0;i<shopList.size();i++) {
						shopList.get(i).put("notice_id", params.get("notice_id"));
					}
					//添加店铺通知信息
					noticeDao.addNoticeShopList(shopList);
					//查询商家
					List<Map<String,Object>> list= infoPushDao.queryShopList(params);
					for (Map<String, Object> map : list) {
						JsonObject js=new JsonObject();
						js.addProperty("type", 1);
						js.addProperty("notice_shop_id", map.get("notice_shop_id").toString());
						JPushClientUtil.notifyAndoidPos(map.get("pc_registration_id").toString(), "消息通知", 2, js.toString());
						System.out.println("推送成功");
					}
					
					//开启子线程，依次向已登录的收银设备推送消息通知
					if(null != redis) {
						System.out.println(redis.getListKet(ClientMQTT.MQTTPUBKEY));
						SentMsgToAndroid msg = new SentMsgToAndroid(noticeDao,redis);
						Thread t = new Thread(msg);
						t.start();
					}else {
						System.out.println("为什么redis是空");
					}
				}
			}
			result.setStatus(1);
			result.setMsg("成功");
		} catch (Exception e) {
			e.printStackTrace();
			result.setStatus(0);
			result.setMsg("异常");
		}
		
		return result;
	}

	@Override
	public Map<String, Object> queryNoticeDetail(String notice_id) {
		Map<String ,Object> notice = noticeDao.queryNoticeDetail(notice_id);
		return notice;
	}

	@Override
	@Transactional
	public PurResult deleteNotice(String notice_id) {
		PurResult result = new PurResult();
		try {
			noticeDao.deleteNotice(notice_id);
			noticeDao.deleteNoticeShop(notice_id);
			result.setStatus(1);
			result.setMsg("成功");
		} catch (Exception e) {
			e.printStackTrace();
			result.setStatus(0);
			result.setMsg("异常");
		}
		
		return result;
	}

	@Override
	@Transactional
	public PurResult updateNotice(HttpServletRequest request) {
		PurResult result = new PurResult();
		try {
			Map<String ,Object> params = HandleMessyCode.handle(ServletsUtil.getParameters(request));
			noticeDao.updateNotice(params);
			Integer notice_revoke = Integer.parseInt(MUtil.strObject(params.get("notice_revoke")));
			if(notice_revoke == 2) {//发布
				//删除店铺通知信息
				noticeDao.deleteNoticeShop(MUtil.strObject(params.get("notice_id")));
				//获取店铺列表
				List<Map<String ,Object>> shopList = noticeDao.queryShopList(params);
				if(shopList.size() > 0) {
					for(int i=0;i<shopList.size();i++) {
						shopList.get(i).put("notice_id", params.get("notice_id"));
					}
					//添加店铺通知信息
					noticeDao.addNoticeShopList(shopList);
				}
			}
			result.setStatus(1);
			result.setMsg("成功");
		} catch (Exception e) {
			e.printStackTrace();
			result.setStatus(0);
			result.setMsg("异常");
		}
		
		return result;
	}

	@Override
	@Transactional
	public PurResult releaseNotice(String notice_id,String notice_type) {
		PurResult result = new PurResult();
		try {
			//获取店铺列表
			Map<String ,Object> params = new HashMap<>();
			params.put("notice_type", notice_type);
			List<Map<String ,Object>> shopList = noticeDao.queryShopList(params);
			if(shopList.size() > 0) {
				for(int i=0;i<shopList.size();i++) {
					shopList.get(i).put("notice_id", notice_id);
				}
				//添加店铺通知信息
				noticeDao.addNoticeShopList(shopList);
			}
			//修改通知状态为已发布
			params.put("notice_id", notice_id);
			params.put("notice_revoke", "2");//已发布
			noticeDao.updateNotice(params);
			result.setStatus(1);
			result.setMsg("成功");
		} catch (Exception e) {
			e.printStackTrace();
			result.setStatus(0);
			result.setMsg("异常");
		}
		
		return result;
	}

	@Override
	@Transactional
	public PurResult withdrawNotice(String notice_id) {
		PurResult result = new PurResult();
		try {
			//删除店铺通知信息
			noticeDao.deleteNoticeShop(notice_id);
			//修改通知状态为已发布
			Map<String ,Object> params = new HashMap<>();
			params.put("notice_id", notice_id);
			params.put("notice_revoke", "3");//已撤回
			noticeDao.updateNotice(params);
			
			result.setStatus(1);
			result.setMsg("成功");
		} catch (Exception e) {
			e.printStackTrace();
			result.setStatus(0);
			result.setMsg("异常");
		}
		
		return result;
	}
}
