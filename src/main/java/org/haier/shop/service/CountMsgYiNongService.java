package org.haier.shop.service;

import org.haier.shop.util.PurResult;

public interface CountMsgYiNongService {


	PurResult queryShopIsCenter(String shop_unique);

	PurResult queryChildSaleCount(String shop_unique, String type);

	PurResult queryOrderMsg(String shop_unique, String type);

	PurResult querySaleCount(String shop_unique, String type);

	PurResult queryCenterSaleCount(String shop_unique);

	PurResult queryOrderMsgPurchase(String type);

	PurResult querySaleMoneyByPurchase();

	PurResult queryGoodsListPurchase(String type);

	PurResult queryOrderListPurchase(String type);

	PurResult querySaleCountPurchase(String type);

	PurResult querySaleCountByMonth(String type);

	PurResult kindSaleRatio(String type);

	PurResult querySaleTB(String type);

	PurResult querySaleMoneyByPurchase2();

	PurResult queryPinPaiList();
}
