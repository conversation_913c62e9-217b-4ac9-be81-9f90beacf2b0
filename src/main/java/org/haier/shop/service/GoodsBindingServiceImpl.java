package org.haier.shop.service;

import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;

import org.haier.shop.dao.GoodsBindingDao;
import org.haier.shop.entity.Binding;
import org.haier.shop.util.PurResult;
import org.haier.shop.util.ShopsResult;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Service
public class GoodsBindingServiceImpl implements GoodsBindingService{
	@Resource
	private GoodsBindingDao bindDao;
	
	
	/**
	 * 商品捆绑信息查询
	 * @param map
	 * @return
	 */
	public PurResult queryShopsBinding(Map<String,Object> map){
		PurResult sr=new PurResult();
		try {
			List<Binding> data = bindDao.queryShopsBinding(map);
			Integer count = bindDao.queryShopsBindingCount(map);
			
			sr.setData(data);
			sr.setCount(count);
			sr.setStatus(1);
			sr.setMsg("成功");
		} catch (Exception e) {
			e.printStackTrace();
			sr.setStatus(0);
			sr.setMsg("异常");
		}
		return sr;
	}
	
	/**
	 * 删除已有商品捆绑关系
	 * @param map
	 * @return
	 */
	@Transactional
	public ShopsResult deleteBindingGoods(Map<String,Object> map){
		ShopsResult sr=new ShopsResult();
		int k=bindDao.deleteBindingGoods(map);
		if(k==0){
			sr.setStatus(1);
			sr.setMsg("删除商品捆绑关系失败！");
			return sr;
		}
		sr.setStatus(0);
		sr.setMsg("删除成功！");
		return sr;
	}
	
	/**
	 * 修改商品捆绑消息
	 * @param map
	 * @return
	 */
	@Transactional
	public ShopsResult modifyBinding(Map<String,Object> map){
		ShopsResult sr=new ShopsResult();
		int k=bindDao.modifyBinding(map);
		if(k==0){
			sr.setStatus(1);
			sr.setMsg("修改商品捆绑消息失败！");
			return sr;
		}
		sr.setStatus(0);
		sr.setMsg("修改成功！");
		return sr;
	}
	
	/**
	 * 添加商品的捆绑关系
	 * @param map
	 * @return
	 */
	@Transactional
	public ShopsResult newBindingGoods(String goodsBarcodes,String goodsCounts,Long shopUnique,Double bindingTotal){
		ShopsResult ns=new ShopsResult();
		List<Map<String,Object>> resource=new ArrayList<Map<String,Object>>();
		Long bindingUnique=new Date().getTime();
		String[] goodsBarcodesArr = goodsBarcodes.split(",");
		String[] goodsCountsArr = goodsCounts.split(",");
		for(int i=0;i<goodsBarcodesArr.length;i++){
			Map<String,Object> map=new HashMap<String, Object>();
			map.put("shopUnique", shopUnique);
			map.put("goodsBarcode", goodsBarcodesArr[i]);
			map.put("goodsCount", goodsCountsArr[i]);
			map.put("bindingUnique", bindingUnique);
			map.put("bindingTotal", bindingTotal);
			resource.add(map);
		}
		int k=bindDao.newBindingGoods(resource);
		if(0==k){
			ns.setStatus(1);
			ns.setMsg("添加失败！");
			return ns;
		}
		ns.setStatus(0);
		ns.setMsg("添加成功！");
		return ns;
	}
}
