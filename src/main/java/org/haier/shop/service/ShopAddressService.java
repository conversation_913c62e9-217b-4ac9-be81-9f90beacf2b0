package org.haier.shop.service;

import org.haier.shop.util.ShopsResult;
import org.springframework.transaction.annotation.Transactional;

public interface ShopAddressService {
	
	/**
	 * 添加或修改店铺地址
	 * @param id 原地址ID
	 * @param shop_unique 店铺编号
	 * @param province_code 省编码
	 * @param city_code 市编码
	 * @param county_code 区编码
	 * @param longitude 经度
	 * @param latitude 维度
	 * @param contacts 联系人
	 * @param address_detail 收货地址
	 * @param contacts_phone 联系方式
	 * @param default_status 1、默认地址；0、非默认地址
	 * @param valid_status 1、正常；0、删除
	 * @return
	 */
	@Transactional
	public ShopsResult addNewShopAddress(
			String id,
			String shop_unique,
			String province_code,
			String city_code,
			String county_code,
			Double longitude,
			Double latitude,
			String contacts,
			String address_detail,
			String contacts_phone,
			Integer default_status,
			Integer valid_status
			);
	/**
	 * 查询店铺的所有收货地址信息
	 * @param shopUnique
	 * @return
	 */
	public ShopsResult queryShopAddressList(String shopUnique);
}
