package org.haier.shop.service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;

import org.haier.meituan.util.MUtil;
import org.haier.shop.dao.InfoPushDao;
import org.haier.shop.util.HandleMessyCode;
import org.haier.shop.util.PurResult;
import org.haier.shop.util.ServletsUtil;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Service("infoService")
public class InfoPushServiceImpl implements InfoPushService{
	
	@Resource
	private InfoPushDao infoPushDao;

	@Override
	public PurResult queryInfoList(Map<String, Object> params) {
		PurResult result = new PurResult();
		try {
			List<Map<String ,Object>> list = infoPushDao.queryInfoList(params);
			Integer count = infoPushDao.queryInfoListCount(params);
			result.setData(list);
			result.setCount(count);
			
			result.setStatus(1);
			result.setMsg("成功");
		} catch (Exception e) {
			e.printStackTrace();
			result.setStatus(0);
			result.setMsg("异常");
		}
		
		return result;
	}

	@Override
	@Transactional
	public PurResult addInfo(HttpServletRequest request) {
		PurResult result = new PurResult();
		try {
			Map<String ,Object> params = HandleMessyCode.handle(ServletsUtil.getParameters(request));
			infoPushDao.addInfo(params);
			String info_id = MUtil.strObject(params.get("info_id"));
			Integer push_area = Integer.parseInt(MUtil.strObject(params.get("push_area")));
			if(push_area == 2) {
				//添加推送区域
				String push_areas = MUtil.strObject(params.get("push_areas"));
				if(push_areas != null && !push_areas.equals("")) {
					List<Map<String ,Object>> push_areas_list = MUtil.strToList(push_areas);
					if(push_areas_list.size() > 0) {
						for(int i=0;i<push_areas_list.size();i++) {
							push_areas_list.get(i).put("info_id", info_id);
						}
						infoPushDao.addInfoPushAreaList(push_areas_list);
					}
				}
			}
			
			result.setStatus(1);
			result.setMsg("成功");
		} catch (Exception e) {
			e.printStackTrace();
			result.setStatus(0);
			result.setMsg("异常");
		}
		
		return result;
	}

	@Override
	public Map<String, Object> queryInfoDetail(String info_id) {
		Map<String ,Object> info = infoPushDao.queryInfoDetail(info_id);
		//获取推送区域
		List<Map<String ,Object>> infoPushAreaList = infoPushDao.queryInfoPushAreaList(info_id);
		info.put("infoPushAreaList", infoPushAreaList);
		return info;
	}

	@Override
	@Transactional
	public PurResult deleteInfo(String info_id) {
		PurResult result = new PurResult();
		try {
			infoPushDao.deleteInfo(info_id);
			infoPushDao.deleteInfoPushArea(info_id);
			result.setStatus(1);
			result.setMsg("成功");
		} catch (Exception e) {
			e.printStackTrace();
			result.setStatus(0);
			result.setMsg("异常");
		}
		
		return result;
	}

	@Override
	@Transactional
	public PurResult sendInfo(String info_id) {
		PurResult result = new PurResult();
		try {
			//获取消息详情
			Map<String ,Object> info = infoPushDao.queryInfoDetail(info_id);
			Integer push_area = Integer.parseInt(MUtil.strObject(info.get("push_area")));
			List<Map<String ,Object>> memberList = new ArrayList<Map<String ,Object>>();
			if(push_area == 1) {//全部
				memberList = infoPushDao.queryAllMemberList();
			}else {//按区域
				memberList = infoPushDao.queryMemberListByArea(info_id);
			}
			if(memberList.size() > 0) {
				for(int i=0;i<memberList.size();i++) {
					memberList.get(i).put("info_id", info_id);
					memberList.get(i).put("info_title", MUtil.strObject(info.get("info_title")));
					memberList.get(i).put("info_content", MUtil.strObject(info.get("info_content")));
				}
				//添加会员推送信息
				infoPushDao.addInfoPushMemberList(memberList);
			}
			//修改推送消息已发送
			Map<String ,Object> params = new HashMap<String ,Object>();
			params.put("info_id", info_id);
			params.put("status", 2);
			infoPushDao.updateInfo(params);
			result.setStatus(1);
			result.setMsg("成功");
		} catch (Exception e) {
			e.printStackTrace();
			result.setStatus(0);
			result.setMsg("异常");
		}
		
		return result;
	}

	@Override
	@Transactional
	public PurResult updateInfo(HttpServletRequest request) {
		PurResult result = new PurResult();
		try {
			Map<String ,Object> params = HandleMessyCode.handle(ServletsUtil.getParameters(request));
			infoPushDao.updateInfo(params);
			String info_id = MUtil.strObject(params.get("info_id"));
			Integer push_area = Integer.parseInt(MUtil.strObject(params.get("push_area")));
			//删除推送区域
			infoPushDao.deleteInfoPushArea(info_id);
			if(push_area == 2) {
				//添加推送区域
				String push_areas = MUtil.strObject(params.get("push_areas"));
				if(push_areas != null && !push_areas.equals("")) {
					List<Map<String ,Object>> push_areas_list = MUtil.strToList(push_areas);
					if(push_areas_list.size() > 0) {
						for(int i=0;i<push_areas_list.size();i++) {
							push_areas_list.get(i).put("info_id", info_id);
						}
						infoPushDao.addInfoPushAreaList(push_areas_list);
					}
				}
			}
			result.setStatus(1);
			result.setMsg("成功");
		} catch (Exception e) {
			e.printStackTrace();
			result.setStatus(0);
			result.setMsg("异常");
		}
		
		return result;
	}

	@Override
	public PurResult getInfoPushMemberList(Map<String, Object> params) {
		PurResult result = new PurResult();
		try {
			List<Map<String ,Object>> list = infoPushDao.getInfoPushMemberList(params);
			result.setData(list);
			
			result.setStatus(1);
			result.setMsg("成功");
		} catch (Exception e) {
			e.printStackTrace();
			result.setStatus(0);
			result.setMsg("异常");
		}
		
		return result;
	}

	@Override
	public PurResult getInfoPushMember(String member_info_id) {
		PurResult result = new PurResult();
		try {
			Map<String ,Object> detail = infoPushDao.getInfoPushMember(member_info_id);
			//修改消息为已读
			Map<String ,Object> params = new HashMap<String ,Object>();
			params.put("id", member_info_id);
			params.put("is_read", 2);//已读
			infoPushDao.updateInfoPushMember(params);
			
			result.setData(detail);
			result.setStatus(1);
			result.setMsg("成功");
		} catch (Exception e) {
			e.printStackTrace();
			result.setStatus(0);
			result.setMsg("异常");
		}
		
		return result;
	}
	
}
