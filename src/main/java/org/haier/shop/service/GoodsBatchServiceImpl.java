package org.haier.shop.service;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import org.haier.shop.dao.GoodsBatchMapper;
import org.haier.shop.dao.GoodsDao;
import org.haier.shop.dao.GoodsSaleBatchMapper;
import org.haier.shop.entity.GoodsBatch;
import org.haier.shop.entity.GoodsEntity;
import org.haier.shop.entity.GoodsSaleBatch;
import org.haier.shop.params.goods.OutStockBatchParam;
import org.haier.shop.params.goodsBatch.GoodsBatchChooseParams;
import org.haier.shop.params.goodsBatch.GoodsBatchChooseViewParams;
import org.haier.shop.params.goodsBatch.GoodsBatchExportParams;
import org.haier.shop.params.goodsBatch.GoodsBatchQueryParams;
import org.haier.shop.result.goodsBatch.GoodsBatchChooseVO;
import org.haier.shop.result.goodsBatch.GoodsBatchVO;
import org.haier.shop.util.LoadOutObjectXLSUtil;
import org.haier.shop.util.PurResult;
import org.haier.shop.util.XLSCallBack;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.OutputStream;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.net.URLEncoder;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @Description 商品入库批次表
 * @ClassName GoodsBatch
 * <AUTHOR>
 * @Date 2024-04-28
 **/
@Service
public class GoodsBatchServiceImpl implements GoodsBatchService {

    @Resource
    private GoodsBatchMapper goodsBatchMapper;
    @Resource
    private GoodsSaleBatchMapper goodsSaleBatchMapper;
    @Resource
    private GoodsDao goodsDao;

    @Override
    public PurResult selectPage(GoodsBatchQueryParams params) {
        List<GoodsBatchVO> list = goodsBatchMapper.selectPage(params);
        Long count = goodsBatchMapper.selectPageCount(params);
        PurResult purResult = new PurResult();
        purResult.setCount(count.intValue());
        purResult.setRows(list);
        purResult.setStatus(1);
        purResult.setMsg("查询成功");
        return purResult;
    }

    @Override
    public PurResult batchSelectList(GoodsBatchChooseParams params) {
        PurResult purResult = new PurResult(PurResult.FAIL, "操作失败");
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put("shopUnique", params.getShopUnique());
        paramMap.put("goodsBarcode", params.getGoodsBarcode());
        GoodsEntity goodsEntity = goodsDao.queryOneByParam(paramMap);
        if (ObjectUtil.isNull(goodsEntity)) {
            purResult.setMsg("商品不存在");
            return purResult;
        }
        BigDecimal goodsContain = goodsEntity.getGoodsContain();
        if (!StrUtil.equals(goodsEntity.getGoodsBarcode(), String.valueOf(goodsEntity.getForeignKey()))) {
            params.setGoodsBarcode(String.valueOf(goodsEntity.getForeignKey()));
        }
        List<GoodsBatch> list = goodsBatchMapper.selectBatchSelectList(params);
        final BigDecimal[] totalOutCount = {BigDecimal.ZERO};
        totalOutCount[0] = BigDecimal.ZERO;
        BigDecimal watOutCount = params.getOutCount();
        List<GoodsBatchChooseVO> listVO = list.stream().map(v -> {
            GoodsBatchChooseVO vo = new GoodsBatchChooseVO();
            BeanUtil.copyProperties(v, vo);
            vo.setOutCount(BigDecimal.ZERO);
            vo.setGoodsContain(goodsEntity.getGoodsContain());
            vo.setGoodsInPrice(NumberUtil.mul(vo.getGoodsInPrice(), goodsEntity.getGoodsContain()).setScale(2, RoundingMode.HALF_UP));
            if (CollectionUtil.isNotEmpty(params.getGoodsBatchList())) {
                params.getGoodsBatchList().stream().forEach(b -> {
                    if (ObjectUtil.equals(Long.valueOf(goodsEntity.getGoodsId()), b.getGoodsId())) {
                        if (CollectionUtil.isNotEmpty(b.getBatchList())) {
                            Optional<OutStockBatchParam> optional = b.getBatchList().stream().filter(bu -> StrUtil.equals(bu.getBatchUnique(), vo.getBatchUnique())).findFirst();
                            if (optional.isPresent()) {
                                vo.setOutCount(optional.get().getGoodsCount());
                            } else {
                                vo.setOutCount(BigDecimal.ZERO);
                            }
                        }
                    } else {
                        if (CollectionUtil.isNotEmpty(b.getBatchList())) {
                            Optional<OutStockBatchParam> optional = b.getBatchList().stream().filter(bu -> StrUtil.equals(bu.getBatchUnique(), vo.getBatchUnique())).findFirst();
                            if (optional.isPresent()) {
                                vo.setGoodsCount(NumberUtil.sub(vo.getGoodsCount(), NumberUtil.mul(optional.get().getGoodsCount(), optional.get().getGoodsContain())));
                            }
                        }
                    }

                });
            }
            totalOutCount[0] = NumberUtil.add(totalOutCount[0], vo.getOutCount());
            if (!StrUtil.equals(goodsEntity.getGoodsBarcode(), String.valueOf(goodsEntity.getForeignKey()))) {
                vo.setGoodsCount(NumberUtil.div(vo.getGoodsCount(), goodsContain, 0, RoundingMode.DOWN));
            }
            return vo;
        }).collect(Collectors.toList());
        watOutCount = NumberUtil.sub(watOutCount, totalOutCount[0]);
        listVO = listVO.stream().filter(v -> BigDecimal.ZERO.compareTo(v.getGoodsCount()) < 0).collect(Collectors.toList());
//        if (CollectionUtil.isNotEmpty(listVO) && StrUtil.isNotBlank(params.getListUnique())) {
//            GoodsSaleBatch goodsSaleBatch = new GoodsSaleBatch();
//            goodsSaleBatch.setShopUnique(params.getShopUnique());
//            goodsSaleBatch.setStockListUnique(params.getListUnique());
//            goodsSaleBatch.setGoodsBarcode(params.getGoodsBarcode());
//            List<GoodsSaleBatch> saleBatchList = goodsSaleBatchMapper.findList(goodsSaleBatch);
//            if (CollectionUtil.isNotEmpty(saleBatchList)) {
//                Map<String, BigDecimal> saleCountMap = saleBatchList.stream().filter(v -> BigDecimal.ZERO.compareTo(v.getGoodsOutCount()) < 0).collect(Collectors.toMap(GoodsSaleBatch::getBatchUnique, GoodsSaleBatch::getGoodsOutCount));
//                listVO.stream().forEach(v -> {
//                    v.setOutCount(saleCountMap.getOrDefault(v.getBatchUnique(), BigDecimal.ZERO));
//                    totalOutCount[0] = NumberUtil.add(totalOutCount[0], v.getOutCount());
//                });
//                if (totalOutCount[0].compareTo(params.getOutCount()) > 0) {
//                    listVO.stream().forEach(v -> v.setOutCount(BigDecimal.ZERO));
//                } else {
//                    watOutCount = NumberUtil.sub(params.getOutCount(), totalOutCount[0]);
//                }
//            }
//        }
        purResult.setData(listVO);
        purResult.setCord(watOutCount);
        purResult.setStatus(1);
        purResult.setMsg("查询成功");
        return purResult;
    }

    @Override
    public PurResult batchSelectListView(GoodsBatchChooseViewParams params) {
        PurResult purResult = new PurResult(PurResult.SUCCESS, "操作成功");
        GoodsSaleBatch goodsSaleBatch = new GoodsSaleBatch();
        goodsSaleBatch.setShopUnique(params.getShopUnique());
        goodsSaleBatch.setStockListUnique(params.getListUnique());
        goodsSaleBatch.setGoodsBarcode(params.getGoodsBarcode());
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put("shopUnique", params.getShopUnique());
        paramMap.put("goodsBarcode", params.getGoodsBarcode());
        GoodsEntity goodsEntity = goodsDao.queryOneByParam(paramMap);
        if (ObjectUtil.isNull(goodsEntity)) {
            purResult.setMsg("商品不存在");
            return purResult;
        }
        BigDecimal goodsContain = goodsEntity.getGoodsContain();
        List<GoodsSaleBatch> list = goodsSaleBatchMapper.findList(goodsSaleBatch);
        final BigDecimal[] totalOutCount = {BigDecimal.ZERO};
        totalOutCount[0] = BigDecimal.ZERO;
        BigDecimal watOutCount = params.getOutCount();
        if (CollectionUtil.isNotEmpty(list)) {
            Map<String, BigDecimal> saleMap = list.stream().collect(Collectors.toMap(GoodsSaleBatch::getBatchUnique, GoodsSaleBatch::getGoodsOutCount));
            List<GoodsBatch> goodsBatches = goodsBatchMapper.selectList(params.getShopUnique(), saleMap.keySet());
            List<GoodsBatchChooseVO> listVO = goodsBatches.stream().map(v -> {
                GoodsBatchChooseVO vo = new GoodsBatchChooseVO();
                BeanUtil.copyProperties(v, vo);
                vo.setGoodsInPrice(NumberUtil.mul(vo.getGoodsInPrice(), goodsEntity.getGoodsContain()).setScale(2, RoundingMode.HALF_UP));
                if (!StrUtil.equals(goodsEntity.getGoodsBarcode(), String.valueOf(goodsEntity.getForeignKey()))) {
                    vo.setGoodsCount(NumberUtil.div(v.getGoodsCount(), goodsContain, 0, RoundingMode.DOWN));
                }
                vo.setOutCount(saleMap.getOrDefault(vo.getBatchUnique(), BigDecimal.ZERO));
                totalOutCount[0] = NumberUtil.add(totalOutCount[0], vo.getOutCount());
                return vo;
            }).collect(Collectors.toList());
            if (totalOutCount[0].compareTo(params.getOutCount()) > 0) {
                listVO.stream().forEach(v -> v.setOutCount(BigDecimal.ZERO));
            } else {
                watOutCount = NumberUtil.sub(params.getOutCount(), totalOutCount[0]);
            }
            purResult.setData(listVO);
        } else {
            purResult.setData(Collections.EMPTY_LIST);
        }
        purResult.setCord(watOutCount);
        return purResult;
    }

    @Override
    public void exportToExcel(GoodsBatchExportParams params, HttpServletRequest request, HttpServletResponse response) {
        String fileName = "商品库存批次";
        try {
            OutputStream os = response.getOutputStream();
            response.setContentType("application/x-msdownload; charset=UTF-8");// 告知浏览器文件类型
            String agent = request.getHeader("user-agent");
            if (agent.contains("Firefox")) {
                response.setHeader("Content-Disposition","attachment;filename=" + new String(( fileName + ".xls").getBytes("GB2312"), "ISO-8859-1"));// 设置文件头
            } else {
                response.setHeader("Content-Disposition", "attachment;filename=" + URLEncoder.encode(fileName + ".xls", "UTF-8"));// 设置文件头
            }

            LoadOutObjectXLSUtil<GoodsBatchVO> objectXLS = new LoadOutObjectXLSUtil<GoodsBatchVO>();
            String[] titles = {"批次号", "入库单号", "商品大类", "商品小类", "商品编码", "商品名称", "入库单价", "入库数量", "剩余数量", "入库时间", "生产日期", "保质期(天)", "到期日", "到期状态"};
            List<GoodsBatchVO> list = goodsBatchMapper.selectListAll(params);
            objectXLS.generateXLS(os, list, new XLSCallBack<GoodsBatchVO>() {
                @Override
                public String getTitle() {
                    return fileName;
                }

                @Override
                public String[] getColumnsName() {
                    return titles;
                }

                @Override
                public int[] getColumnsWidth() {
                    return new int[]{
                            80 * 80,
                            80 * 80,
                            80 * 80,
                            80 * 80,
                            80 * 80,
                            80 * 80,
                            80 * 80,
                            80 * 80,
                            80 * 80,
                            80 * 80,
                            80 * 80,
                            80 * 80,
                            80 * 80
                    };
                }

                @Override
                public Object[] getValues(GoodsBatchVO vo) {
                    String expStatusName = "正常";
                    if (ObjectUtil.equals(Integer.valueOf(1), vo.getExpStatus())) {
                        expStatusName = "临期";
                    } else if (ObjectUtil.equals(Integer.valueOf(2), vo.getExpStatus())) {
                        expStatusName = "过期";
                    }
                    return new String[]{ vo.getBatchUnique(),
                            vo.getStockListUnique(),
                            StrUtil.nullToEmpty(vo.getGoodsKindNameFirst()),
                            StrUtil.nullToEmpty(vo.getGoodsKindNameSecond()),
                            vo.getGoodsBarcode(),
                            vo.getGoodsName(),
                            vo.getGoodsInPrice().toPlainString(),
                            vo.getGoodsInCount().toPlainString(),
                            vo.getGoodsCount().toPlainString(),
                            DateUtil.formatDateTime(vo.getCreateTime()),
                            DateUtil.formatDate(vo.getGoodsProd()),
                            ObjectUtil.isNotNull(vo.getGoodsLife()) ? String.valueOf(vo.getGoodsLife()) : StrUtil.EMPTY,
                            DateUtil.formatDate(vo.getGoodsProd()),
                            expStatusName
                    };
                }

                @Override
                public int[][] MergedRegion() {
                    return new int[0][];
                }

                @Override
                public List<Object[]> getheaderValue() {
                    return null;
                }
            });
            os.flush();
            os.close();
        } catch (IOException e) {
            e.printStackTrace();
        }
    }
}