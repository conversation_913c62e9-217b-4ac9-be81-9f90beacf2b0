package org.haier.shop.service;

import java.util.List;
import java.util.Map;

import org.haier.shop.util.PurResult;

public interface GoldService {
	

	public PurResult queryGoldShopList(Map<String, Object> params);
	
	public PurResult queryGoldDeviceList(Map<String, Object> params);
	
	public PurResult queryGoldOrderList(Map<String, Object> params);
	
	public Map<String, Object> queryGoldRule();
	
	public PurResult updatePtRule(Map<String, Object> params);
	
	public List<Map<String, Object>> queryGoldDevice(String shop_unique);
	
	public Map<String, Object> queryGoldOrder(String shop_unique);
	
	public PurResult updateGoldShopPt(Map<String, Object> params,List<Map<String, Object>> queryGoldDevice);
	
	public List<Map<String, Object>> queryGoldShop();
	
	public PurResult addGoldShopPt(Map<String, Object> params,List<Map<String, Object>> queryGoldDevice);
	
	public PurResult updateGoldbyHand(Map<String, Object> params);
	
	public PurResult delGoldDetail(Map<String, Object> params);
	
}
