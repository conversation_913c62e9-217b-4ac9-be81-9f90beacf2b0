package org.haier.shop.service;

import java.math.BigDecimal;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.SortedMap;
import java.util.TreeMap;

import javax.annotation.Resource;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.http.HttpUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import org.apache.shiro.SecurityUtils;
import org.apache.shiro.session.Session;
import org.apache.shiro.subject.Subject;
import org.haier.meituan.util.MUtil;
import org.haier.shop.config.JQVersionTwoConfig;
import org.haier.shop.dao.DisDao;
import org.haier.shop.dao.ManagerDao;
import org.haier.shop.entity.Staff;
import org.haier.shop.params.card.AddBusinessCardParams;
import org.haier.shop.params.card.jqtwo.AddCardParams;
import org.haier.shop.util.PurResult;
import org.haier.shop.util.wxPay.ClientCustomSSL;
import org.haier.shop.util.wxPay.PayConfigUtil;
import org.haier.shop.util.wxPay.PayToolUtil;
import org.haier.shop.util.wxPay.XMLUtil4jdom;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import cn.hutool.core.util.NumberUtil;

@Service("disService")
@Transactional
public class DisServiceImpl implements DisService{
	
	@Resource
	private DisDao disDao;

	@Resource
	private ManagerDao managerDao;
	@Override
	public PurResult shopDisList(Map<String, Object> params) {
		PurResult result = new PurResult();
		try {
			List<Map<String ,Object>> list = disDao.queryShopDisList(params);
			for(int i=0;i<list.size();i++) {
				Map<String ,Object> lowerParams = new HashMap<String ,Object>();
				lowerParams.put("shop_unique", list.get(i).get("shop_unique"));
				lowerParams.put("cus_unique", list.get(i).get("cus_unique"));
				Integer lower_num = disDao.queryShopDisLowerListCount(lowerParams);
				list.get(i).put("lower_num", lower_num);
			}
			Integer count = disDao.queryShopDisListCount(params);
			result.setData(list);
			result.setCount(count);
			result.setStatus(1);
			result.setMsg("成功");
		} catch (Exception e) {
			e.printStackTrace();
			result.setStatus(0);
			result.setMsg("异常");
		}
		
		return result;
	}

	@Override
	public PurResult queryCustomerList(Map<String, Object> params) {
		PurResult result = new PurResult();
		try {
			List<Map<String ,Object>> list = disDao.queryCustomerList(params);
			result.setData(list);
			result.setStatus(1);
			result.setMsg("成功");
		} catch (Exception e) {
			e.printStackTrace();
			result.setStatus(0);
			result.setMsg("异常");
		}
		
		return result;
	}
	
	@Override
	public PurResult addDisRelation(String cus_uniques,String shop_unique,String dis_level_id) {
		PurResult result = new PurResult();
		try {
			String[] cus_uniqueArr = cus_uniques.split(",");
			String cus_name = "";
			for(int i =0;i<cus_uniqueArr.length;i++) {
				Map<String ,Object> params = new HashMap<String ,Object>();
				String one_cus_unique=cus_uniqueArr[i];
				params.put("cus_unique", one_cus_unique);
				params.put("one_cus_unique", one_cus_unique);
				params.put("shop_unique", shop_unique);
				Map<String ,Object> resultDis = disDao.queryDisRelation(params);
				if(resultDis != null) {
					resultDis.put("status", "1");
					resultDis.put("one_cus_unique", one_cus_unique);
					disDao.updateDisRelation(resultDis);
					cus_name = cus_name+','+MUtil.strObject(resultDis.get("cus_name"));
				}else {
					disDao.addDisRelation(params);
				}
			}
			if(cus_name.length()>0){
				cus_name = cus_name.substring(1, cus_name.length());
			}
			result.setData(cus_name);
			result.setMsg("成功");
			result.setStatus(1);
		} catch (Exception e) {
			e.printStackTrace();
			result.setStatus(0);
			result.setMsg("异常");
		}
		
		return result;
	}

	@Override
	public Map<String, Object> queryDisRelation(String dis_relation_id) {
		Map<String ,Object> params = new HashMap<String ,Object>();
		params.put("dis_relation_id", dis_relation_id);
		Map<String ,Object> disRelation = disDao.queryDisRelation(params);
		//获取发展一级会员数
		Map<String ,Object> lowerParams = new HashMap<String ,Object>();
		lowerParams.put("shop_unique", disRelation.get("shop_unique"));
		lowerParams.put("cus_unique", disRelation.get("cus_unique"));
		lowerParams.put("cus_type", "1");
		/*//获取下级分销商数
		Integer dis_num = disDao.queryChildDisCount(lowerParams);
		disRelation.put("dis_num", dis_num);*/
		//获取下级会员数
		Integer count = disDao.queryShopDisChildCusListCount(lowerParams);
		disRelation.put("cus_num", count==null?0:count);
		return disRelation;
	}

	@Override
	public PurResult updateDisRelation(Map<String, Object> params) {
		PurResult result = new PurResult();
		try {
			disDao.updateDisRelation(params);
			result.setStatus(1);
			result.setMsg("成功");

			//如果审核通过，则同步创建企业名片信息
			if("1".equals(params.get("status").toString())) {
				Subject subject = SecurityUtils.getSubject();
				Session session = subject.getSession();
				Staff staff = (Staff) session.getAttribute("staff");

				try {
					//获取会员当前信息，同步到企业名片
					Map<String ,Object> cusInfo = managerDao.platformCustomerDetail(params.get("one_cus_unique").toString());


					//审核通过了，自动创建企业名片信息
					AddCardParams addCardParams = new AddCardParams();
					addCardParams.setUserId(Long.parseLong(params.get("one_cus_unique").toString()));
					addCardParams.setUserType(3);
					addCardParams.setShopId(staff.getShop_unique());
					addCardParams.setShopName(staff.getShop_name());
					addCardParams.setPosition("团长");
					if (ObjectUtil.isNotNull(cusInfo.get("cus_phone"))) {
						addCardParams.setMobile(cusInfo.get("cus_phone").toString());
						addCardParams.setWeChat(cusInfo.get("cus_phone").toString());
					}
					if (ObjectUtil.isNotNull(cusInfo.get("cus_protrait"))) {
						addCardParams.setIcon("cus_protrait");
					}
					if (ObjectUtil.isNotNull(cusInfo.get("cus_name"))) {
						addCardParams.setUserName(cusInfo.get("cus_name").toString());
					}
					addCardParams.setDefaultCard(1);

					System.out.println("添加企业名片上传的参数为" + addCardParams.toString());
					String res = HttpUtil.post(JQVersionTwoConfig.addCard, JSON.toJSONString(addCardParams));
					System.out.println("添加企业名片返回的参数为" + res);
					JSONObject.parseObject(res);
				} catch (Exception e) {
					e.printStackTrace();
				}
			}


		} catch (Exception e) {
			e.printStackTrace();
			result.setStatus(0);
			result.setMsg("异常");
		}
		
		return result;
	}

	@Override
	public PurResult shopDisLowerList(Map<String, Object> params) {
		PurResult result = new PurResult();
		try {
			List<Map<String ,Object>> list = disDao.queryShopDisLowerList(params);
			Integer count = disDao.queryShopDisLowerListCount(params);
			result.setData(list);
			result.setCount(count);
			result.setStatus(1);
			result.setMsg("成功");
		} catch (Exception e) {
			e.printStackTrace();
			result.setStatus(0);
			result.setMsg("异常");
		}
		
		return result;
	}

	@Override
	public PurResult disTradingList(Map<String, Object> params) {
		PurResult result = new PurResult();
		try {
			List<Map<String ,Object>> list = disDao.disTradingList(params);
			Integer count = disDao.disTradingListCount(params);
			result.setData(list);
			result.setCount(count);
			result.setStatus(1);
			result.setMsg("成功");
		} catch (Exception e) {
			e.printStackTrace();
			result.setStatus(0);
			result.setMsg("异常");
		}
		
		return result;
	}

	@Override
	public PurResult shopDisDetailStatistics(Map<String, Object> params) {
		PurResult result = new PurResult();
		try {
			//佣金
			//一级佣金
			params.put("trading_type", "1");
			Double oneCommission = disDao.queryCommission(params);
			//二级佣金
			params.put("trading_type", "2");
			Double twoCommission = disDao.queryCommission(params);
			//佣金总额
			Double totalCommission = oneCommission+twoCommission;
			
			//发展会员数
			//发展一级会员数
			params.put("cus_type", "1");
			Integer one_lower_num = disDao.queryShopDisLowerListCount(params);
			//发展二级会员数
			params.put("cus_type", "2");
			Integer two_lower_num = disDao.queryShopDisLowerListCount(params);
			//会员总数
			Integer total_lower_num = one_lower_num+two_lower_num;
			
			Map<String ,Object> data = new HashMap<String ,Object>();
			data.put("oneCommission", oneCommission);
			data.put("twoCommission", twoCommission);
			data.put("totalCommission", totalCommission);
			data.put("one_lower_num", one_lower_num);
			data.put("two_lower_num", two_lower_num);
			data.put("total_lower_num", total_lower_num);
			
			result.setData(data);
			result.setStatus(1);
			result.setMsg("成功");
		} catch (Exception e) {
			e.printStackTrace();
			result.setStatus(0);
			result.setMsg("异常");
		}
		
		return result;
	}
	
	@Override
	public PurResult commissionList(Map<String, Object> params) {
		PurResult result = new PurResult();
		try {
			List<Map<String ,Object>> list = disDao.queryCommissionList(params);
			Integer count = disDao.queryCommissionListCount(params);
			result.setData(list);
			result.setCount(count);
			result.setStatus(1);
			result.setMsg("成功");
		} catch (Exception e) {
			e.printStackTrace();
			result.setStatus(0);
			result.setMsg("异常");
		}
		
		return result;
	}
	
	@Override
	public Map<String, Object> queryCommissionInfo(String shop_unique) {
		Map<String ,Object> commissionInfo = new HashMap<String ,Object>();
		try {
			Map<String ,Object> params = new HashMap<String ,Object>();
			params.put("shop_unique", shop_unique);
			
			//查询已到账佣金
			params.put("trading_status", 1);
			Double total_trading_amount = disDao.querySumCommission(params);
			params.put("trading_status", 2);
			//查询未到账佣金
			Double not_total_trading_amount = disDao.querySumCommission(params);
			//查询已提现佣金
			Double cash_money = disDao.querySumCashMoney(params);
			commissionInfo.put("total_trading_amount", total_trading_amount);
			commissionInfo.put("not_total_trading_amount", not_total_trading_amount);
			commissionInfo.put("cash_money", cash_money);
			
		} catch (Exception e) {
			e.printStackTrace();
		}
		return commissionInfo;
	}
	
	@Override
	public PurResult commissionStatistics(Map<String, Object> params) {
		PurResult result = new PurResult();
		try {
			List<Map<String ,Object>> list = disDao.queryCommissionStatisticsList(params);
			result.setData(list);
			result.setStatus(1);
			result.setMsg("成功");
		} catch (Exception e) {
			e.printStackTrace();
			result.setStatus(0);
			result.setMsg("异常");
		}
		
		return result;
	}
	
	@Override
	public PurResult disOrderList(Map<String, Object> params) {
		PurResult result = new PurResult();
		try {
			List<Map<String ,Object>> list = disDao.queryDisOrderList(params);
			Integer count = disDao.queryDisOrderListCount(params);
			result.setData(list);
			result.setCount(count);
			result.setStatus(1);
			result.setMsg("成功");
		} catch (Exception e) {
			e.printStackTrace();
			result.setStatus(0);
			result.setMsg("异常");
		}
		
		return result;
	}
	
	@Override
	public Map<String, Object> queryDisOrderDetail(String sale_list_unique) {
		Map<String ,Object> disOrderDetail = new HashMap<String ,Object>();
		try {
			//获取订单基本信息
			disOrderDetail = disDao.queryDisOrder(sale_list_unique);
			//获取订单商品佣金详情
			List<Map<String ,Object>> goodsList = disDao.queryDisOrderGoodsList(sale_list_unique);
			
			disOrderDetail.put("goodsList", goodsList);
			
		} catch (Exception e) {
			e.printStackTrace();
		}
		return disOrderDetail;
	}

	@Override
	public void disLevelAutoDown() {
		//获取所有分销商等级信息
		List<Map<String ,Object>> levelList = disDao.queryAllDisLevelList();
		
		//获取分销商信息
		List<Map<String ,Object>> disList = disDao.queryAllDisList();
		for(int i=0;i<disList.size();i++) {
			Map<String ,Object> dis = disList.get(i);
			//获取分销商发展分销商数量
			Integer disCount = disDao.queryDisNextDisCount(dis);
			//获取分销商发展会员数量
			Integer cusCount = disDao.queryDisNextCount(dis)-disCount;
			for(int j=0;j<levelList.size();j++) {
				if(disList.get(i).get("shop_unique").equals(levelList.get(j).get("shop_unique")) && disList.get(i).get("dis_level_id").equals(levelList.get(j).get("dis_level_id"))) {
					//成为分销商至今天数
					Integer day_num = Integer.parseInt(MUtil.strObject(disList.get(i).get("day_num")));
					//累计佣金
					Double total_revenue = Double.valueOf(MUtil.strObject(disList.get(i).get("total_revenue")));
					
					Double drop_commission_amount = Double.valueOf(MUtil.strObject(levelList.get(j).get("drop_commission_amount")));
					Integer drop_commission_time = Integer.parseInt(MUtil.strObject(levelList.get(j).get("drop_commission_time")));
					
					Integer drop_dis_num = Integer.parseInt(MUtil.strObject(levelList.get(j).get("drop_dis_num")));
					Integer drop_dis_time = Integer.parseInt(MUtil.strObject(levelList.get(j).get("drop_dis_time")));
					
					Integer drop_cus_num = Integer.parseInt(MUtil.strObject(levelList.get(j).get("drop_cus_num")));
					Integer drop_cus_time = Integer.parseInt(MUtil.strObject(levelList.get(j).get("drop_cus_time")));
					
					if(day_num >= drop_commission_time && total_revenue < drop_commission_amount
					 || day_num >= drop_dis_time && disCount < drop_dis_num 
					 || day_num >= drop_cus_time && cusCount < drop_cus_num) {//分销商降级，否则保级成功
						Integer drop_is_cus = Integer.parseInt(MUtil.strObject(levelList.get(j).get("drop_is_cus")));
						String next_level_id = MUtil.strObject(levelList.get(j).get("next_level_id"));
						//修改分销商等级
						Map<String ,Object> params = new HashMap<String ,Object>(); 
						params.put("dis_relation_id", disList.get(i).get("dis_relation_id"));
						if(drop_is_cus == 1) {//直接降级为会员
							params.put("dis_level_id", null);
						}else {
							params.put("dis_level_id", next_level_id);
						}
					}
				}
			}
		}
	}
	
	@Override
	public PurResult disWithdList(Map<String, Object> params) {
		PurResult result = new PurResult();
		try {
			List<Map<String ,Object>> list = disDao.disWithdList(params);
			for (Map<String, Object> map : list) {
				//查询店铺名称
				Map<String, Object> shop_name= disDao.queryDisWithShopName(map);
				map.put("shop_name", shop_name.get("shop_name"));
			}
			Integer count = disDao.disWithdListCount(params);
			result.setData(list);
			result.setCount(count);
			result.setStatus(1);
			result.setMsg("成功");
		} catch (Exception e) {
			e.printStackTrace();
			result.setStatus(0);
			result.setMsg("异常");
		}
		
		return result;
	}

	@Override
	public PurResult weixinTransfers(Map<String, Object> params) {
		PurResult result = new PurResult();
		try {
			BigDecimal bigDecimal_total_fee = NumberUtil.mul(MUtil.strObject(params.get("trading_amount")), "100");//微信请求金额单位为分
			Integer amount = bigDecimal_total_fee.intValue();
			SortedMap<Object,Object> parametersMap = new TreeMap<Object,Object>();
			parametersMap.put("mch_appid", PayConfigUtil.JINQUAN_APP_ID);//应用ID
			parametersMap.put("mchid", PayConfigUtil.JINQUAN_MCH_ID);//商户号
			parametersMap.put("nonce_str", MUtil.getRandomString(32));//32位随机字符串
			parametersMap.put("partner_trade_no", MUtil.strObject(params.get("trading_flow")));//商户订单号，需保持唯一性
			parametersMap.put("openid", MUtil.strObject(params.get("openid")));//用户标识
			parametersMap.put("check_name", "NO_CHECK");//不强校验真实姓名
//			parametersMap.put("re_user_name", MUtil.strObject(params.get("re_user_name")));//强校验真实姓名
			parametersMap.put("amount", amount.toString());//付款金额
			parametersMap.put("desc", "分销商提现");//用户提现
			parametersMap.put("spbill_create_ip", MUtil.strObject(params.get("spbill_create_ip")));//Ip地址；该IP同在商户平台设置的IP白名单中的IP没有关联，该IP可传用户端或者服务端的IP。
			String sign = PayToolUtil.createSign("utf-8",parametersMap,PayConfigUtil.JINQUAN_API_KEY);//生成签名，签名算法
			parametersMap.put("sign", sign);
			String xml = PayToolUtil.getRequestXml(parametersMap);//拼接xml请求参数
			System.out.println("------微信企业付款参数："+xml);
			String weixinPost = ClientCustomSSL.doRefund(PayConfigUtil.TRANSFERS, xml,PayConfigUtil.JINQUAN_MCH_ID);
			System.out.println("======微信企业付款返回参数："+weixinPost);
			Map<String ,Object> map = XMLUtil4jdom.doXMLParse(weixinPost);
			String result_code = MUtil.strObject(map.get("result_code"));
			if(result_code.equals("SUCCESS")) {
				//修改提现状态为成功
				params.put("trading_status", 4);
				disDao.updateDisTradingStatus(params);
				//查询每个店铺的提现金额
				List<Map<String, Object>> shops= disDao.queryShopCashMoney(params);
				for (Map<String, Object> shop : shops) {
					//修改分销商累计提现金额
					params.put("total_withd", shop.get("trading_amount"));
					params.put("shop_unique", shop.get("shop_unique"));
					disDao.updateDisRelation(params);
				}
				result.setStatus(1);
				result.setMsg("成功");
			}else {
				//修改提现状态为失败
				params.put("trading_status", 5);
				disDao.updateDisTradingStatus(params);
				//查询每个店铺的提现金额
				List<Map<String, Object>> shops= disDao.queryShopCashMoney(params);
				for (Map<String, Object> shop : shops) {
					//修改分销商累计提现金额
					params.put("balance", shop.get("trading_amount"));
					params.put("shop_unique", shop.get("shop_unique"));
					disDao.updateDisRelation(params);
				}
				result.setStatus(2);
				result.setMsg(MUtil.strObject(map.get("err_code_des")));
			}
		} catch (Exception e) {
			e.printStackTrace();
			result.setStatus(0);
			result.setMsg("异常");
		}
		
		return result;
	}

	@Override
	public PurResult queryShopDisChildCusList(Map<String, Object> params) {
		PurResult result = new PurResult();
		try {
			List<Map<String ,Object>> list = disDao.queryShopDisChildCusList(params);
			Integer count = disDao.queryShopDisChildCusListCount(params);
			result.setData(list);
			result.setCount(count==null?0:count);
			result.setStatus(1);
			result.setMsg("成功");
		} catch (Exception e) {
			e.printStackTrace();
			result.setStatus(0);
			result.setMsg("异常");
		}
		return result;
	}

	@Override
	public PurResult queryShopDisTradingAmountList(Map<String, Object> params) {
		PurResult result = new PurResult();
		try {
			List<Map<String ,Object>> list = disDao.queryShopDisTradingAmountList(params);
			Integer count = disDao.queryShopDisTradingAmountListCount(params);
			result.setData(list);
			result.setCount(count==null?0:count);
			result.setStatus(1);
			result.setMsg("成功");
		} catch (Exception e) {
			e.printStackTrace();
			result.setStatus(0);
			result.setMsg("异常");
		}
		return result;
	}

	@Override
	public PurResult queryDisGoodsList(Map<String, Object> params) {
		PurResult result = new PurResult();
		try {
			List<Map<String ,Object>> list = disDao.queryDisGoodsList(params);
			Integer count = disDao.queryDisGoodsListCount(params);
			result.setData(list);
			result.setCount(count==null?0:count);
			result.setStatus(1);
			result.setMsg("成功");
		} catch (Exception e) {
			e.printStackTrace();
			result.setStatus(0);
			result.setMsg("异常");
		}
		return result;
	}

	@Override
	public PurResult queryCusRelationList(Map<String, Object> params) {
		PurResult result = new PurResult();
		try {
			List<Map<String ,Object>> list = disDao.queryCusRelationList(params);
			Integer count = disDao.queryCusRelationListCount(params);
			result.setData(list);
			result.setCount(count);
			result.setStatus(1);
			result.setMsg("成功");
		} catch (Exception e) {
			e.printStackTrace();
			result.setStatus(0);
			result.setMsg("异常");
		}
		
		return result;
	}

	@Override
	public PurResult addSetCommissionRatio(Double commission_ratio, String shop_unique) {
		PurResult result = new PurResult();
		try {
				Map<String ,Object> params = new HashMap<String ,Object>();
				params.put("commission_ratio", commission_ratio);
				params.put("shop_unique", shop_unique);
				int i=disDao.updateSetCommissionRatio(params);
				if(i<=0){
					disDao.addSetCommissionRatio(params);
				}
				
			result.setStatus(1);
			result.setMsg("成功");
		} catch (Exception e) {
			e.printStackTrace();
			result.setStatus(0);
			result.setMsg("异常");
		}
		
		return result;
	}

	@Override
	public PurResult queryShopDisCashAmountList(Map<String, Object> params) {
		PurResult result = new PurResult();
		try {
			List<Map<String ,Object>> list = disDao.queryShopDisCashAmountList(params);
			Integer count = disDao.queryShopDisCashAmountListCount(params);
			result.setData(list);
			result.setCount(count==null?0:count);
			result.setStatus(1);
			result.setMsg("成功");
		} catch (Exception e) {
			e.printStackTrace();
			result.setStatus(0);
			result.setMsg("异常");
		}
		return result;
	}
}
