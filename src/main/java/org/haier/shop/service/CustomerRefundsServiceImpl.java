package org.haier.shop.service;

import org.haier.shop.dao.CustomerRefundsMapper;
import org.haier.shop.util.PurResult;
import org.haier.shop.vo.CustomerRefundsVO;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * @Description 会员退费记录
 * @ClassName CustomerRefundsService
 * <AUTHOR>
 * @Date 2024/4/15 11:19
 **/
@Service
public class CustomerRefundsServiceImpl implements CustomerRefundsService {
    @Resource
    private CustomerRefundsMapper customerRefundsMapper;

    @Override
    public PurResult queryCustomerRefundsPage(Map<String, Object> map) {
        PurResult result = new PurResult();
        BigDecimal refundTotalMoney = customerRefundsMapper.queryRefundTotalMoney(map);
        List<CustomerRefundsVO> list = customerRefundsMapper.pageList(map);
        result.setStatus(1);
        result.setMsg("查询成功！");
        result.setData(list);
        result.setCount(customerRefundsMapper.pageListCount(map));
        result.setCord(refundTotalMoney);
        return result;
    }

    @Override
    public List<CustomerRefundsVO> queryCustomerRefundsList(Map<String, Object> paramsMap) {
        return customerRefundsMapper.selectList(paramsMap);
    }
}
