package org.haier.shop.service;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import org.haier.shop.dao.*;
import org.haier.shop.entity.*;
import org.haier.shop.params.goods.*;
import org.haier.shop.params.goodsBatch.GoodsSaleBatchData;
import org.haier.shop.vo.GoodsBatchOutData;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import javax.xml.crypto.Data;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @Description
 * @ClassName InventoryManagerServiceImpl
 * <AUTHOR>
 * @Date 2024/4/28 15:06
 **/
@Service
public class InventoryManagerServiceImpl implements InventoryManagerService {

    @Resource
    private GoodsBatchMapper goodsBatchMapper;
    @Resource
    private ShopsConfigDao shopsConfigDao;
    @Resource
    private ShopStockMapper shopStockMapper;
    @Resource
    private ShopStockDetailMapper shopStockDetailMapper;
    @Resource
    private GoodsSaleBatchMapper goodsSaleBatchMapper;
    @Resource
    private GoodsDao goodsDao;

    @Override
    public boolean dealGoodsInventory(GoodsInventoryParam params) {
        boolean flag = false;
        switch (params.getGoodsInPriceType()) {
            case 0:
                //最近入库价
                break;
            case 1:
                //移动加权平均
                break;
            case 2:
                //先进先出
                flag = craeteGoodsBatch(params);
                break;
        }
        return flag;
    }
    @Override
    public List<GoodsSaleBatch> saleGoodsBatch(ShopsConfig shopsConfig, OutStockParam outStockParam) {
        List<GoodsSaleBatch> saleBatchList = new ArrayList<>();
        List<GoodsBatch> goodsBatchUpdateList = new ArrayList<>();
        Map<Long, List<OutStockBatchParam>> batchMap = outStockParam.getGoodsBatchList().stream().collect(Collectors.toMap(k -> k.getGoodsId(), v -> v.getBatchList()));
        Date currentDate = DateUtil.date();
        for (OutStockGoodsParam goodsParam : outStockParam.getGoodsList()) {
            BigDecimal totalCount = goodsParam.getGoodsCount();
            List<OutStockBatchParam> selectBatchList = batchMap.get(goodsParam.getGoodsId());
            if (ObjectUtil.isEmpty(selectBatchList)) {
                // 未选择指定批次出库
                List<GoodsBatch> goodsBatches = goodsBatchMapper.selectAvailableList(outStockParam.getShopUnique(), goodsParam.getGoodsBarcode());
                for (GoodsBatch goodsBatch : goodsBatches) {
                    if (totalCount.compareTo(BigDecimal.ZERO) > 0) {
                        GoodsSaleBatch goodsSaleBatch = new GoodsSaleBatch();
                        goodsSaleBatch.setShopUnique(outStockParam.getShopUnique());
                        goodsSaleBatch.setStockListUnique(outStockParam.getListUnique());
                        goodsSaleBatch.setGoodsBarcode(goodsBatch.getGoodsBarcode());
                        goodsSaleBatch.setGoodsInPrice(goodsBatch.getGoodsInPrice());
                        goodsSaleBatch.setGoodsOutPrice(goodsParam.getStockPrice());
                        goodsSaleBatch.setCreateId(outStockParam.getUserId());
                        goodsSaleBatch.setCreateTime(currentDate);
                        goodsSaleBatch.setUpdateId(outStockParam.getUserId());
                        goodsSaleBatch.setBatchUnique(goodsBatch.getBatchUnique());
                        goodsSaleBatch.setUpdateTime(currentDate);
                        if (totalCount.compareTo(goodsBatch.getGoodsCount()) > 0) {
                            goodsSaleBatch.setGoodsOutCount(goodsBatch.getGoodsCount());
                            totalCount = NumberUtil.sub(totalCount, goodsBatch.getGoodsCount());
                            goodsBatch.setGoodsCount(BigDecimal.ZERO);
                            goodsBatch.setUpdateTime(currentDate);
                            goodsBatch.setUpdateId(outStockParam.getUserId());
                        } else {
                            goodsBatch.setGoodsCount(NumberUtil.sub(goodsBatch.getGoodsCount(), totalCount));
                            goodsSaleBatch.setGoodsOutCount(totalCount);
                            totalCount = BigDecimal.ZERO;
                            goodsBatch.setUpdateTime(currentDate);
                            goodsBatch.setUpdateId(outStockParam.getUserId());
                        }
                        saleBatchList.add(goodsSaleBatch);
                        goodsBatchUpdateList.add(goodsBatch);
                    } else {
                        break;
                    }
                }
//                if (totalCount.compareTo(BigDecimal.ZERO) > 0) {
//                    throw new RuntimeException("商品名称: " + goodsParam.getGoodsName() + "(" + goodsParam.getGoodsBarcode() + ") 对应库存不足，无法完成出库");
//                }
            } else {
                //选择指定批次出库
                //1、先校验批次数量是否小于等于出库数量
                BigDecimal selectCount = BigDecimal.ZERO;
                for (OutStockBatchParam outStockBatchParam : selectBatchList) {
                    selectCount = NumberUtil.add(selectCount, outStockBatchParam.getGoodsCount());
                }
                if (selectCount.compareTo(goodsParam.getGoodsCount()) > 0) {
                    throw new RuntimeException("商品名称: " + goodsParam.getGoodsName() + "(" + goodsParam.getGoodsBarcode() + ") 选择出库批次总数量超出实际出库总数量");
                }
                //2、优先出库已选中批次
                for (OutStockBatchParam outStockBatchParam : selectBatchList) {
                    GoodsBatch goodsBatch = goodsBatchMapper.selectByBatchUnique(outStockParam.getShopUnique(), outStockBatchParam.getBatchUnique());
                    if (ObjectUtil.isNull(goodsBatch) || goodsBatch.getGoodsCount().compareTo(outStockBatchParam.getGoodsCount()) < 0) {
                        throw new RuntimeException("商品名称: " + goodsParam.getGoodsName() + "(" + goodsParam.getGoodsBarcode() + ") 对应出库批次号: " + outStockBatchParam.getBatchUnique() + "，库存不足");
                    }
                    GoodsSaleBatch goodsSaleBatch = new GoodsSaleBatch();
                    goodsSaleBatch.setShopUnique(outStockParam.getShopUnique());
                    goodsSaleBatch.setStockListUnique(outStockParam.getListUnique());
                    goodsSaleBatch.setGoodsBarcode(goodsBatch.getGoodsBarcode());
                    goodsSaleBatch.setGoodsInPrice(goodsBatch.getGoodsInPrice());
                    goodsSaleBatch.setGoodsOutPrice(goodsParam.getStockPrice());
                    goodsSaleBatch.setCreateId(outStockParam.getUserId());
                    goodsSaleBatch.setCreateTime(currentDate);
                    goodsSaleBatch.setUpdateId(outStockParam.getUserId());
                    goodsSaleBatch.setUpdateTime(currentDate);
                    goodsSaleBatch.setBatchUnique(goodsBatch.getBatchUnique());
                    goodsSaleBatch.setGoodsOutCount(outStockBatchParam.getGoodsCount());
                    goodsBatch.setGoodsCount(NumberUtil.sub(goodsBatch.getGoodsCount(), outStockBatchParam.getGoodsCount()));
                    goodsBatch.setUpdateTime(currentDate);
                    goodsBatch.setUpdateId(outStockParam.getUserId());
                    saleBatchList.add(goodsSaleBatch);
                    goodsBatchUpdateList.add(goodsBatch);
                }

                totalCount = NumberUtil.sub(totalCount, selectCount);
                if (totalCount.compareTo(BigDecimal.ZERO) > 0) {
                    //3、选择批次出库完后，还有待出库数量，继续以先进先出方式出库
                    // 备注：需要判断先进先出库存 是否包含已选择出库批次， 如果含已选择批次 需要特殊处理
                    List<GoodsBatch> goodsBatches = goodsBatchMapper.selectAvailableList(outStockParam.getShopUnique(), goodsParam.getGoodsBarcode());
                    for (GoodsBatch goodsBatch : goodsBatches) {
                        if (totalCount.compareTo(BigDecimal.ZERO) > 0) {
                            //先进先出方式与已选批次重合处理
                            List<GoodsBatch> gbList = goodsBatchUpdateList.stream().filter(v -> v.getBatchUnique().equals(goodsBatch.getBatchUnique())).collect(Collectors.toList());
                            if (CollectionUtil.isNotEmpty(gbList)) {
                                GoodsBatch gb = gbList.get(0);
                                if (gb.getGoodsCount().compareTo(BigDecimal.ZERO) > 0) {
                                    if (totalCount.compareTo(gb.getGoodsCount()) > 0) {
                                        saleBatchList.stream().forEach(v -> {
                                            if (StrUtil.equals(v.getBatchUnique(), goodsBatch.getBatchUnique())) {
                                                v.setGoodsOutCount(goodsBatch.getGoodsCount());
                                            }
                                        });
                                        totalCount = NumberUtil.sub(totalCount, gb.getGoodsCount());
                                        goodsBatchUpdateList.stream().forEach(v -> {
                                            if (StrUtil.equals(v.getBatchUnique(), goodsBatch.getBatchUnique())) {
                                                v.setGoodsCount(BigDecimal.ZERO);
                                            }
                                        });
                                    } else {
                                        BigDecimal finalTotalCount = totalCount;
                                        saleBatchList.stream().forEach(v -> {
                                            if (StrUtil.equals(v.getBatchUnique(), goodsBatch.getBatchUnique())) {
                                                v.setGoodsOutCount(NumberUtil.add(v.getGoodsOutCount(), finalTotalCount));
                                            }
                                        });
                                        totalCount = BigDecimal.ZERO;
                                        goodsBatchUpdateList.stream().forEach(v -> {
                                            if (StrUtil.equals(v.getBatchUnique(), goodsBatch.getBatchUnique())) {
                                                v.setGoodsCount(NumberUtil.sub(gb.getGoodsCount(), finalTotalCount));
                                            }
                                        });
                                    }
                                }
                            } else {
                                GoodsSaleBatch goodsSaleBatch = new GoodsSaleBatch();
                                goodsSaleBatch.setShopUnique(outStockParam.getShopUnique());
                                goodsSaleBatch.setStockListUnique(outStockParam.getListUnique());
                                goodsSaleBatch.setGoodsBarcode(goodsBatch.getGoodsBarcode());
                                goodsSaleBatch.setGoodsInPrice(goodsBatch.getGoodsInPrice());
                                goodsSaleBatch.setGoodsOutPrice(goodsParam.getStockPrice());
                                goodsSaleBatch.setCreateId(outStockParam.getUserId());
                                goodsSaleBatch.setCreateTime(currentDate);
                                goodsSaleBatch.setUpdateId(outStockParam.getUserId());
                                goodsSaleBatch.setUpdateTime(currentDate);
                                goodsSaleBatch.setBatchUnique(goodsBatch.getBatchUnique());
                                if (totalCount.compareTo(goodsBatch.getGoodsCount()) > 0) {
                                    goodsSaleBatch.setGoodsOutCount(goodsBatch.getGoodsCount());
                                    totalCount = NumberUtil.sub(totalCount, goodsBatch.getGoodsCount());
                                    goodsBatch.setGoodsCount(BigDecimal.ZERO);
                                    goodsBatch.setUpdateTime(currentDate);
                                    goodsBatch.setUpdateId(outStockParam.getUserId());
                                } else {
                                    goodsBatch.setGoodsCount(NumberUtil.sub(goodsBatch.getGoodsCount(), totalCount));
                                    goodsSaleBatch.setGoodsOutCount(totalCount);
                                    goodsBatch.setUpdateTime(currentDate);
                                    goodsBatch.setUpdateId(outStockParam.getUserId());
                                    totalCount = BigDecimal.ZERO;
                                }
                                saleBatchList.add(goodsSaleBatch);
                                goodsBatchUpdateList.add(goodsBatch);
                            }
                        } else {
                            break;
                        }
                    }
//                    if (totalCount.compareTo(BigDecimal.ZERO) > 0) {
//                        throw new RuntimeException("商品名称: " + goodsParam.getGoodsName() + "(" + goodsParam.getGoodsBarcode() + ") 对应库存不足，无法完成出库");
//                    }
                }
            }
        }
        // 如果无需审核的话 自动减少批次剩余数量
        if (ObjectUtil.equals(Integer.valueOf(0), shopsConfig.getIsIoboundInspect())) {
            goodsBatchMapper.updateBatchCount(goodsBatchUpdateList);
        }
        if (CollectionUtil.isNotEmpty(saleBatchList)) {
            goodsSaleBatchMapper.insertBatch(saleBatchList);
        }
        return saleBatchList;
    }

    @Override
    public List<GoodsSaleBatchData> createGoodsSaleBatch(OutStockParam outStockParam, OutStockGoodsParam goodsParam, boolean validateBatch, List<GoodsSaleBatchData> newGoodsSaleBatchList, GoodsBatchOutData goodsBatchOutData) {
        Map<Long, List<OutStockBatchParam>> batchSelectMap = outStockParam.getGoodsBatchList().stream().collect(Collectors.toMap(OutStockGoodsBatchParam::getGoodsId, OutStockGoodsBatchParam::getBatchList));
        List<OutStockBatchParam> batchSelectList = batchSelectMap.get(goodsParam.getGoodsId());
        BigDecimal totalCount = BigDecimal.ZERO;
        Map<String, GoodsSaleBatchData> goodsSaleBatchMap = new HashMap<>();
        List<GoodsBatch> hasGoodsBatchList = goodsBatchMapper.selectAvailableList(outStockParam.getShopUnique(), goodsParam.getForeignKey());
        if (CollectionUtil.isEmpty(hasGoodsBatchList) && CollectionUtil.isNotEmpty(batchSelectList)) {
            throw new RuntimeException("商品名称：" + goodsParam.getGoodsName() + "(" + goodsParam.getGoodsBarcode() + ") 对应批次库存不足");
        }
        hasGoodsBatchList.stream().forEach(b -> {
            newGoodsSaleBatchList.stream().filter(v -> StrUtil.equals(b.getBatchUnique(), v.getBatchUnique())).forEach(f -> {
                b.setGoodsCount(NumberUtil.sub(b.getGoodsCount(), NumberUtil.mul(f.getGoodsContain(), f.getGoodsOutCount())));
            });
        });
        Date currentDate = DateUtil.date();
        Map<String, GoodsBatch> goodsBatchUpdateMap = new HashMap<>();
        goodsBatchUpdateMap.putAll(goodsBatchOutData.getGoodsBatchMap());
        List<GoodsSaleBatchData> goodsSaleBatchDataList = goodsBatchOutData.getGoodsSaleBatchDataList().stream().filter(v -> ObjectUtil.equals(v.getGoodsBarcode(), goodsParam.getGoodsBarcode())).collect(Collectors.toList());
        if (CollectionUtil.isNotEmpty(goodsSaleBatchDataList)) {
            for (GoodsSaleBatchData batchData : goodsSaleBatchDataList) {
                totalCount = NumberUtil.add(totalCount, batchData.getGoodsOutCount());
                batchData.setGoodsOutPrice(goodsParam.getStockPrice());
                goodsSaleBatchMap.put(batchData.getBatchUnique(), batchData);
            }
        }

        BigDecimal waitOutCount = NumberUtil.sub(goodsParam.getGoodsCount(), totalCount);
        if (BigDecimal.ZERO.compareTo(waitOutCount) < 0) {
            if (StrUtil.equals(goodsParam.getGoodsBarcode(), goodsParam.getForeignKey())) {
                //先进先出
                for (GoodsBatch goodsBatch : hasGoodsBatchList) {
                    GoodsBatch hasGoodsBatch = goodsBatchUpdateMap.get(goodsBatch.getBatchUnique());
                    if (ObjectUtil.isNotNull(hasGoodsBatch)) {
                        goodsBatch = hasGoodsBatch;
                    }
                    if (waitOutCount.compareTo(BigDecimal.ZERO) > 0) {

                        GoodsSaleBatchData goodsSaleBatch = goodsSaleBatchMap.get(goodsBatch.getBatchUnique());
                        if (ObjectUtil.isNull(goodsSaleBatch)) {
                            goodsSaleBatch = new GoodsSaleBatchData();
                            goodsSaleBatch.setShopUnique(outStockParam.getShopUnique());
                            goodsSaleBatch.setStockListUnique(outStockParam.getListUnique());
                            goodsSaleBatch.setGoodsBarcode(goodsParam.getGoodsBarcode());
                            goodsSaleBatch.setGoodsInPrice(goodsBatch.getGoodsInPrice());
                            goodsSaleBatch.setGoodsOutPrice(goodsParam.getStockPrice());
                            goodsSaleBatch.setCreateId(outStockParam.getUserId());
                            goodsSaleBatch.setCreateTime(currentDate);
                            goodsSaleBatch.setUpdateId(outStockParam.getUserId());
                            goodsSaleBatch.setBatchUnique(goodsBatch.getBatchUnique());
                            goodsSaleBatch.setUpdateTime(currentDate);
                            goodsSaleBatch.setGoodsOutCount(BigDecimal.ZERO);
                            goodsSaleBatch.setGoodsContain(goodsParam.getGoodsContain());
                        }
                        if (waitOutCount.compareTo(goodsBatch.getGoodsCount()) > 0) {
                            goodsSaleBatch.setGoodsOutCount(NumberUtil.add(goodsSaleBatch.getGoodsOutCount(), goodsBatch.getGoodsCount()));
                            waitOutCount = NumberUtil.sub(waitOutCount, goodsBatch.getGoodsCount());
                            goodsBatch.setGoodsCount(BigDecimal.ZERO);
                            goodsBatch.setUpdateTime(currentDate);
                            goodsBatch.setUpdateId(outStockParam.getUserId());
                        } else {
                            goodsBatch.setGoodsCount(NumberUtil.sub(goodsBatch.getGoodsCount(), waitOutCount));
                            goodsSaleBatch.setGoodsOutCount(NumberUtil.add(goodsSaleBatch.getGoodsOutCount(), waitOutCount));
                            waitOutCount = BigDecimal.ZERO;
                            goodsBatch.setUpdateTime(currentDate);
                            goodsBatch.setUpdateId(outStockParam.getUserId());
                        }
                        goodsSaleBatchMap.put(goodsSaleBatch.getBatchUnique(), goodsSaleBatch);
                        goodsBatchUpdateMap.put(goodsBatch.getBatchUnique(), goodsBatch);
                    } else {
                        break;
                    }
                }
            } else {
                // 多规格
                //先进先出
                for (GoodsBatch goodsBatch : hasGoodsBatchList) {
                    GoodsBatch hasGoodsBatch = goodsBatchUpdateMap.get(goodsBatch.getBatchUnique());
                    if (ObjectUtil.isNotNull(hasGoodsBatch)) {
                        goodsBatch = hasGoodsBatch;
                    }
                    BigDecimal hasCount = NumberUtil.div(goodsBatch.getGoodsCount(), goodsParam.getGoodsContain(), 0, RoundingMode.DOWN);
                    if (BigDecimal.ZERO.compareTo(hasCount) >= 0) {
                        continue;
                    }
                    if (waitOutCount.compareTo(BigDecimal.ZERO) > 0) {
                        GoodsSaleBatchData goodsSaleBatch = goodsSaleBatchMap.get(goodsBatch.getBatchUnique());
                        if (ObjectUtil.isNull(goodsSaleBatch)) {
                            goodsSaleBatch = new GoodsSaleBatchData();
                            goodsSaleBatch.setShopUnique(outStockParam.getShopUnique());
                            goodsSaleBatch.setStockListUnique(outStockParam.getListUnique());
                            goodsSaleBatch.setGoodsBarcode(goodsParam.getGoodsBarcode());
                            goodsSaleBatch.setGoodsInPrice(goodsBatch.getGoodsInPrice());
                            goodsSaleBatch.setGoodsOutPrice(goodsParam.getStockPrice());
                            goodsSaleBatch.setCreateId(outStockParam.getUserId());
                            goodsSaleBatch.setCreateTime(currentDate);
                            goodsSaleBatch.setUpdateId(outStockParam.getUserId());
                            goodsSaleBatch.setBatchUnique(goodsBatch.getBatchUnique());
                            goodsSaleBatch.setUpdateTime(currentDate);
                            goodsSaleBatch.setGoodsOutCount(BigDecimal.ZERO);
                            goodsSaleBatch.setGoodsContain(goodsParam.getGoodsContain());
                        }
                        if (waitOutCount.compareTo(hasCount) > 0) {
                            goodsSaleBatch.setGoodsOutCount(NumberUtil.add(goodsSaleBatch.getGoodsOutCount(), hasCount));
                            waitOutCount = NumberUtil.sub(waitOutCount, hasCount);
                            goodsBatch.setGoodsCount(NumberUtil.sub(goodsBatch.getGoodsCount(), NumberUtil.mul(hasCount, goodsParam.getGoodsContain())));
                            goodsBatch.setUpdateTime(currentDate);
                            goodsBatch.setUpdateId(outStockParam.getUserId());
                        } else {
                            goodsBatch.setGoodsCount(NumberUtil.sub(goodsBatch.getGoodsCount(), NumberUtil.mul(waitOutCount, goodsParam.getGoodsContain())));
                            goodsSaleBatch.setGoodsOutCount(NumberUtil.add(goodsSaleBatch.getGoodsOutCount(), waitOutCount));
                            waitOutCount = BigDecimal.ZERO;
                            goodsBatch.setUpdateTime(currentDate);
                            goodsBatch.setUpdateId(outStockParam.getUserId());
                        }
                        goodsSaleBatchMap.put(goodsSaleBatch.getBatchUnique(), goodsSaleBatch);
                        goodsBatchUpdateMap.put(goodsBatch.getBatchUnique(), goodsBatch);
                    } else {
                        break;
                    }
                }
            }

        }
        List<GoodsSaleBatchData> goodsSaleBatchList = new ArrayList<>(goodsSaleBatchMap.values());
        if (CollectionUtil.isNotEmpty(goodsSaleBatchList)) {
            List<GoodsSaleBatch> list = BeanUtil.copyToList(goodsSaleBatchList, GoodsSaleBatch.class);
            goodsSaleBatchMapper.insertBatch(list);
        }
        List<GoodsBatch> goodsBatches = new ArrayList<>(goodsBatchUpdateMap.values());
        if (validateBatch && CollectionUtil.isNotEmpty(goodsBatches)) {
            goodsBatchMapper.updateBatchCount(goodsBatches);
        }
        return goodsSaleBatchList;
    }

    @Override
    public void subGoodsBatchCount(ShopStockDetail shopStockDetail, List<ShopStockData> shopStockDataList) {
        GoodsSaleBatch querySaleBatch = new GoodsSaleBatch();
        querySaleBatch.setShopUnique(Long.parseLong(shopStockDetail.getShopUnique()));
        querySaleBatch.setStockListUnique(shopStockDetail.getListUnique());
        List<GoodsSaleBatch> goodsSaleBatchList = goodsSaleBatchMapper.findList(querySaleBatch);
        Map<String, GoodsBatch> goodsBatchUpdateMap = new HashMap<>();
        Map<String, GoodsSaleBatch> goodsSaleBatchMap = new HashMap<>();
        Date currentDate = DateUtil.date();
        for (ShopStockData shopStockData : shopStockDataList) {
            List<GoodsBatch> hasGoodsBatchList = goodsBatchMapper.selectAvailableList(Long.parseLong(shopStockDetail.getShopUnique()), shopStockData.getForeignKey());
            List<GoodsSaleBatch> goodsSaleBatches = goodsSaleBatchList.stream().filter(f -> StrUtil.equals(f.getGoodsBarcode(), shopStockData.getGoodsBarcode())).collect(Collectors.toList());
            BigDecimal waitOutCount = shopStockData.getGoodsCount();
            BigDecimal saleBatchTotalCount = BigDecimal.ZERO;
            if (CollectionUtil.isNotEmpty(goodsSaleBatches)) {
                for (GoodsSaleBatch goodsSaleBatch : goodsSaleBatches) {
                    BigDecimal smallCount = NumberUtil.mul(shopStockData.getGoodsContain(), goodsSaleBatch.getGoodsOutCount());
                    GoodsBatch goodsBatch = goodsBatchUpdateMap.get(goodsSaleBatch.getBatchUnique());
                    if (ObjectUtil.isNull(goodsBatch)) {
                        Optional<GoodsBatch> optional = hasGoodsBatchList.stream().filter(v -> StrUtil.equals(v.getBatchUnique(), goodsSaleBatch.getBatchUnique())).findFirst();
                        if (optional.isPresent()) {
                            goodsBatch = optional.get();
                        } else {
                            throw new RuntimeException("商品条码：" + shopStockData.getGoodsBarcode() + " 对应出库批次：" + goodsBatch.getBatchUnique() + " 库存不足");
                        }
                    }
                    if (goodsBatch.getGoodsCount().compareTo(smallCount) < 0) {
                        throw new RuntimeException("商品条码：" + shopStockData.getGoodsBarcode() + " 对应出库批次：" + goodsBatch.getBatchUnique() + " 库存不足");
                    }
                    goodsBatch.setGoodsCount(NumberUtil.sub(goodsBatch.getGoodsCount(), smallCount));
                    goodsBatch.setUpdateTime(currentDate);
                    goodsBatch.setUpdateId(shopStockDetail.getUpdateId());
                    goodsBatchUpdateMap.put(goodsBatch.getBatchUnique(), goodsBatch);
                    goodsSaleBatchMap.put(goodsSaleBatch.getBatchUnique(), goodsSaleBatch);
                    saleBatchTotalCount = NumberUtil.add(saleBatchTotalCount, goodsSaleBatch.getGoodsOutCount());
                }
            }
            if (waitOutCount.compareTo(saleBatchTotalCount) < 0) {
                throw new RuntimeException("商品条码：" + shopStockData.getGoodsBarcode() + " 应出库数量不能小于对应出库批次总数量");
            }
            waitOutCount = NumberUtil.sub(waitOutCount, saleBatchTotalCount);
            if (waitOutCount.compareTo(BigDecimal.ZERO) > 0) {
                //先进先出
                for (GoodsBatch goodsBatch : hasGoodsBatchList) {
                    GoodsBatch hasGoodsBatch = goodsBatchUpdateMap.get(goodsBatch.getBatchUnique());
                    if (ObjectUtil.isNotNull(hasGoodsBatch)) {
                        goodsBatch = hasGoodsBatch;
                    }
                    BigDecimal bigCount = goodsBatch.getGoodsCount();
                    if (!StrUtil.equals(shopStockData.getGoodsBarcode(), shopStockData.getForeignKey())) {
                        bigCount = NumberUtil.div(goodsBatch.getGoodsCount(), shopStockData.getGoodsContain(), 0, RoundingMode.DOWN);
                    }
                    if (waitOutCount.compareTo(BigDecimal.ZERO) > 0) {
                        GoodsSaleBatch goodsSaleBatch = goodsSaleBatchMap.get(goodsBatch.getBatchUnique());
                        if (ObjectUtil.isNull(goodsSaleBatch)) {
                            goodsSaleBatch = new GoodsSaleBatchData();
                            goodsSaleBatch.setShopUnique(goodsBatch.getShopUnique());
                            goodsSaleBatch.setStockListUnique(shopStockData.getListUnique());
                            goodsSaleBatch.setGoodsBarcode(shopStockData.getGoodsBarcode());
                            goodsSaleBatch.setGoodsInPrice(goodsBatch.getGoodsInPrice());
                            goodsSaleBatch.setGoodsOutPrice(shopStockData.getStockPrice());
                            goodsSaleBatch.setCreateId(shopStockDetail.getUpdateId());
                            goodsSaleBatch.setCreateTime(currentDate);
                            goodsSaleBatch.setUpdateId(shopStockDetail.getUpdateId());
                            goodsSaleBatch.setBatchUnique(goodsBatch.getBatchUnique());
                            goodsSaleBatch.setUpdateTime(currentDate);
                            goodsSaleBatch.setGoodsOutCount(BigDecimal.ZERO);
                        }
                        if (waitOutCount.compareTo(bigCount) > 0) {
                            goodsSaleBatch.setGoodsOutCount(NumberUtil.add(goodsSaleBatch.getGoodsOutCount(), bigCount));
                            waitOutCount = NumberUtil.sub(waitOutCount, bigCount);
                            goodsBatch.setGoodsCount(NumberUtil.sub(goodsBatch.getGoodsCount(), NumberUtil.mul(bigCount, shopStockData.getGoodsContain())));
                            goodsBatch.setUpdateTime(currentDate);
                            goodsBatch.setUpdateId(shopStockDetail.getUpdateId());
                        } else {
                            goodsBatch.setGoodsCount(NumberUtil.sub(goodsBatch.getGoodsCount(), NumberUtil.mul(waitOutCount, shopStockData.getGoodsContain())));
                            goodsSaleBatch.setGoodsOutCount(NumberUtil.add(goodsSaleBatch.getGoodsOutCount(), waitOutCount));
                            waitOutCount = BigDecimal.ZERO;
                            goodsBatch.setUpdateTime(currentDate);
                            goodsBatch.setUpdateId(shopStockDetail.getUpdateId());
                        }
                        goodsSaleBatchMap.put(goodsSaleBatch.getBatchUnique(), goodsSaleBatch);
                        goodsBatchUpdateMap.put(goodsBatch.getBatchUnique(), goodsBatch);
                    } else {
                        break;
                    }
                }
            } else {
                break;
            }
        }
        if (CollectionUtil.isNotEmpty(goodsBatchUpdateMap.values())) {
            List<GoodsBatch> goodsBatchList = new ArrayList<>(goodsBatchUpdateMap.values());
            goodsBatchMapper.updateBatchCount(goodsBatchList);
        }
        if (CollectionUtil.isNotEmpty(goodsSaleBatchMap.values())) {
            List<GoodsSaleBatch> goodsSaleBatches = new ArrayList<>(goodsSaleBatchMap.values());
            List<GoodsSaleBatch> updateList = goodsSaleBatches.stream().filter(v -> ObjectUtil.isNotNull(v.getGoodsSaleBatchId())).collect(Collectors.toList());
            if (CollectionUtil.isNotEmpty(updateList)) {
                goodsSaleBatchMapper.deleteByListUnique(Long.parseLong(shopStockDetail.getShopUnique()), shopStockDetail.getListUnique());
            }
            goodsSaleBatchMapper.insertBatch(goodsSaleBatches);
        }
    }

    @Override
    public GoodsBatchOutData dealOutSelectBatch(OutStockParam outStockParam) {
        List<OutStockGoodsBatchParam> list = outStockParam.getGoodsBatchList();
        GoodsBatchOutData goodsBatchOutData = new GoodsBatchOutData();
        Map<String, GoodsBatch> goodsBatchMap = new HashMap<>();
        if (CollectionUtil.isEmpty(list)) {
            goodsBatchOutData.setGoodsBatchMap(goodsBatchMap);
            goodsBatchOutData.setGoodsSaleBatchDataList(Collections.EMPTY_LIST);
            return goodsBatchOutData;
        }
        Date currentDate = DateUtil.date();

        List<GoodsSaleBatchData> goodsSaleBatchList = new ArrayList<>();
        for (OutStockGoodsBatchParam batchParam : list) {
            Map<String, Object> goodsQuery = new HashMap<>();
            goodsQuery.put("shopUnique", outStockParam.getShopUnique());
            goodsQuery.put("goodsId", batchParam.getGoodsId());
            GoodsEntity goodsEntity = goodsDao.queryOneByParam(goodsQuery);
            if (ObjectUtil.isNull(goodsEntity)) {
                throw new RuntimeException("商品ID: " + batchParam.getGoodsId() + " 对应商品不存在");
            }
            List<OutStockBatchParam> batchParamList = batchParam.getBatchList();
            if (CollectionUtil.isEmpty(batchParamList)) {
               continue;
            }
            for (OutStockBatchParam outStockBatchParam : batchParamList) {
                GoodsBatch goodsBatch = goodsBatchMap.get(outStockBatchParam.getBatchUnique());
                if (ObjectUtil.isNull(goodsBatch)) {
                    goodsBatch = goodsBatchMapper.selectByBatchUnique(outStockParam.getShopUnique(), outStockBatchParam.getBatchUnique());
                }
                if (ObjectUtil.isNull(goodsBatch)) {
                    throw new RuntimeException("库存批次：" + outStockBatchParam.getBatchUnique() + " 不存在");
                }
                if (!StrUtil.equals(goodsEntity.getGoodsBarcode(), String.valueOf(goodsEntity.getForeignKey()))) {
                    //多规格
                    BigDecimal hasBatchCount = NumberUtil.div(goodsBatch.getGoodsCount(), goodsEntity.getGoodsContain(), 0, RoundingMode.DOWN);
                    if (hasBatchCount.compareTo(outStockBatchParam.getGoodsCount()) < 0) {
                        throw new RuntimeException("商品条码：" + goodsEntity.getGoodsBarcode() + " 对应出库批次：" + goodsBatch.getBatchUnique() + " 库存不足");
                    }
                    goodsBatch.setGoodsCount(NumberUtil.sub(goodsBatch.getGoodsCount(), NumberUtil.mul(outStockBatchParam.getGoodsCount(), goodsEntity.getGoodsContain())));
                } else {
                    if (goodsBatch.getGoodsCount().compareTo(outStockBatchParam.getGoodsCount()) < 0) {
                        throw new RuntimeException("商品条码：" + goodsEntity.getGoodsBarcode() + " 对应出库批次：" + goodsBatch.getBatchUnique() + " 库存不足");
                    }
                    goodsBatch.setGoodsCount(NumberUtil.sub(goodsBatch.getGoodsCount(), outStockBatchParam.getGoodsCount()));
                }
                goodsBatch.setUpdateId(outStockParam.getUserId());
                goodsBatch.setUpdateTime(currentDate);
                goodsBatchMap.put(goodsBatch.getBatchUnique(), goodsBatch);

                GoodsSaleBatchData goodsSaleBatch = new GoodsSaleBatchData();
                goodsSaleBatch.setShopUnique(outStockParam.getShopUnique());
                goodsSaleBatch.setStockListUnique(outStockParam.getListUnique());
                goodsSaleBatch.setGoodsBarcode(goodsEntity.getGoodsBarcode());
                goodsSaleBatch.setGoodsInPrice(goodsBatch.getGoodsInPrice());
                goodsSaleBatch.setCreateId(outStockParam.getUserId());
                goodsSaleBatch.setCreateTime(currentDate);
                goodsSaleBatch.setUpdateId(outStockParam.getUserId());
                goodsSaleBatch.setBatchUnique(goodsBatch.getBatchUnique());
                goodsSaleBatch.setUpdateTime(currentDate);
                goodsSaleBatch.setGoodsOutCount(outStockBatchParam.getGoodsCount());
                goodsSaleBatch.setGoodsContain(goodsEntity.getGoodsContain());
                goodsSaleBatchList.add(goodsSaleBatch);
            }
        }
        goodsBatchOutData.setGoodsBatchMap(goodsBatchMap);
        goodsBatchOutData.setGoodsSaleBatchDataList(goodsSaleBatchList);
        if (CollectionUtil.isNotEmpty(goodsSaleBatchList)) {
            List<GoodsSaleBatch> goodsSaleBatches = goodsSaleBatchList.stream().map(v -> {
                GoodsSaleBatch gsb = new GoodsSaleBatch();
                BeanUtil.copyProperties(v, gsb);
                return gsb;
            }).collect(Collectors.toList());
            goodsSaleBatchMapper.insertBatch(goodsSaleBatches);
        }
        return goodsBatchOutData;
    }

    private boolean craeteGoodsBatch(GoodsInventoryParam params) {
        List<GoodsInventoryDetailParam> list = params.getGoodsList();
        Long time = System.currentTimeMillis();
        List<GoodsBatch> goodsBatches = new ArrayList<>();
        List<GoodsEntity> goodsUpdateList = new ArrayList<>();
        for (int i=0; i<list.size(); i++) {
            GoodsBatch goodsBatch = new GoodsBatch();
            BeanUtil.copyProperties(list.get(i), goodsBatch);
            Map<String, Object> goodsQuery = new HashMap<>();
            goodsQuery.put("shopUnique", params.getShopUnique());
            goodsQuery.put("goodsBarcode", list.get(i).getGoodsBarcode());
            GoodsEntity goods = goodsDao.queryOneByParam(goodsQuery);
            if (ObjectUtil.isNull(goods)) {
                throw new RuntimeException("商品条码: " +  list.get(i).getGoodsBarcode() + " ，不存在，无法完成入库");
            }
            if (!StrUtil.equals(goods.getGoodsBarcode(), String.valueOf(goods.getForeignKey()))) {
                //需要转换规格
                goodsQuery = new HashMap<>();
                goodsQuery.put("shopUnique", params.getShopUnique());
                goodsQuery.put("goodsBarcode", list.get(i).getGoodsBarcode());
                GoodsEntity smallGoods = goodsDao.queryParantGoods(goodsQuery);
                if (ObjectUtil.isNotNull(smallGoods)) {
                    goodsBatch.setGoodsBarcode(smallGoods.getGoodsBarcode());
                    goodsBatch.setGoodsInCount(NumberUtil.mul(list.get(i).getGoodsInCount(), goods.getGoodsContain()));
                    goodsBatch.setGoodsInPrice(NumberUtil.div(list.get(i).getGoodsInPrice(), goodsBatch.getGoodsInCount(), RoundingMode.HALF_UP.ordinal()));
                    goodsBatch.setGoodsCount(goodsBatch.getGoodsInCount());
                    goods = smallGoods;
                }
            }
            goods.setGoodsCount(NumberUtil.add(goods.getGoodsCount(), goodsBatch.getGoodsInCount()));
            goodsUpdateList.add(goods);
            goodsBatch.setShopUnique(params.getShopUnique());
            goodsBatch.setBatchUnique(StrUtil.concat(true, String.valueOf(time), String.format("%03d", i)));
            goodsBatch.setStockListUnique(params.getStockListUnique());
            goodsBatch.setCreateId(params.getStaffId());
            goodsBatch.setCreateTime(DateUtil.date());
            goodsBatch.setUpdateTime(goodsBatch.getCreateTime());
            goodsBatch.setUpdateId(goodsBatch.getCreateId());
            goodsBatches.add(goodsBatch);
        }
        int n = goodsBatchMapper.insertBatch(goodsBatches);
        if (n > 0) {
            goodsDao.updateGoodsCount(goodsUpdateList);
        }
        return n > 0;
    }
}
