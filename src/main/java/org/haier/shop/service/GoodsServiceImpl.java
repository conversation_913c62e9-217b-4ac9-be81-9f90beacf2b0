package org.haier.shop.service;


import java.io.File;
import java.io.FileInputStream;
import java.io.InputStream;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.net.HttpURLConnection;
import java.net.URL;
import java.sql.Timestamp;
import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Calendar;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.UUID;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpUtil;
import com.alibaba.fastjson.JSON;
import com.jcraft.jsch.SftpException;
import org.apache.commons.lang3.StringUtils;
import org.haier.meituan.util.MUtil;
import org.haier.shop.dao.*;
import org.haier.shop.dao2.SupplierInfoDao;
import org.haier.shop.entity.*;
import org.haier.shop.entity.goodsRecord.RecordGoods;
import org.haier.shop.entity.goodsRecord.RecordGoodsOper;
import org.haier.shop.entity.goodsRecord.RecordGoodsOperParams;
import org.haier.shop.enums.StockKindEnum;
import org.haier.shop.enums.StockOriginEnum;
import org.haier.shop.enums.StockReourceEnum;
import org.haier.shop.enums.goodsEnum.DeviceSourceEnum;
import org.haier.shop.enums.goodsEnum.OperSourceEnum;
import org.haier.shop.enums.goodsEnum.OperTypeEnum;
import org.haier.shop.enums.goodsEnum.UserTypeEnum;
import org.haier.shop.params.goods.GoodsInventoryDetailParam;
import org.haier.shop.params.goods.GoodsInventoryParam;
import org.haier.shop.redis.RedisCache;
import org.haier.shop.util.*;
import org.haier.shop.util.mqtt.MqttxUtil;
import org.jetbrains.annotations.NotNull;
import org.jsoup.nodes.Document;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;

import net.sf.json.JSONArray;
import net.sf.json.JSONObject;


//import io.goeasy.GoEasy;

@Service
public class GoodsServiceImpl implements GoodsService {
    @Resource
    private GoodsDao goodsDao;
    @Resource
    private GoodsDictDao dictDao;
    @Resource
    private ImportDao impDao;

    @Resource
    private SupplierInfoDao supplierInfoDao;

    @Resource
    private Goods_kindDao kindDao;

    @Resource
    private ShopDao shopDao;
    @Resource
    private ShopStaffDao staffDao;
    @Resource
    private OilDao oilDao;

    @Resource
    private RedisCache redis;
	@Resource
	private ShopsConfigDao shopsConfigDao;
	@Resource
	private StockDao stockDao;
	@Resource
	private InventoryManagerService inventoryManagerService;

    
    /**
     * 保存商品修改信息
     * @param shopUnique 店铺编码
     * @param goodsMsg 商品信息，格式：商品条码：新增库存，新的商品进价，新的商品售价
     * @return
     */
    @Transactional
	public ShopsResult importGoodsBaseMsg(String shopUnique,String goodsMsg,HttpServletRequest request) {
		ShopsResult sr = new ShopsResult(1, "修改成功!");
		List<Map<String,Object>> list = new ArrayList<Map<String,Object>>();
		Map<String,Object> map = new HashMap<String,Object>();
		map.put("shop_unique", shopUnique);
    	map.put("shopUnique", shopUnique);
    	List<Map<String,Object>> stockList = new ArrayList<Map<String,Object>>();
    	List<Map<String,Object>> priceList = new ArrayList<Map<String,Object>>();
		if(null != goodsMsg && !goodsMsg.equals("")) {
    		String[] goods = goodsMsg.split(";");
    		for(Integer i = 0; i < goods.length; i++) {
    			Map<String,Object> tmpMap = new HashMap<String,Object>();
    			String[] tmpGoods = goods[i].split(":");
    			if(tmpGoods.length == 4 && !tmpGoods[0].equals("")) {
    				tmpMap.put("goodsName", tmpGoods[0]);
    				tmpMap.put("goods_name", tmpGoods[0]);
    				tmpMap.put("stockCount", tmpGoods[1]);
    				tmpMap.put("newInPrice", tmpGoods[2]);
    				tmpMap.put("newSalePrice", tmpGoods[3]);
    				tmpMap.put("shopUnique", shopUnique);
    				list.add(tmpMap);
    				
    				//需要分成两份
    				if(!tmpGoods[1].equals("")) {
    					stockList.add(tmpMap);
    				}
    				if(!tmpGoods[2].equals("") || !tmpGoods[3].equals("")) {
    					priceList.add(tmpMap);
    				}
    			}
    		}
    	}
		map.put("list", list);
		
	
		//批量修改商品库存，价格信息
		if(null != list && !list.isEmpty()) {
			//分别修改商品库存和价格
			if(null != stockList) {
				map.put("stockList", stockList);
				goodsDao.importGoodsBaseMsg(map);
			}
			if(null != priceList) {
				map.put("priceList", priceList);
				goodsDao.importGoodsMsg(map);
			}
			
			
			List<Map<String,Object>> goodsList = goodsDao.queryGoodsBaseMsg(map);
	    	
	    	for(Integer j = 0 ; j < list.size(); j++) {
	    		String newGoodsName = list.get(j).get("goods_name").toString();
	    		for(Integer i = 0; i < goodsList.size(); i++) {
	    			Map<String,Object> tmpGoods = goodsList.get(i);
	    			String goodsName = tmpGoods.get("goods_name").toString();
	    			if(goodsName.equals(newGoodsName)) {
	    				list.get(j).putAll(tmpGoods);
	    				break;
	    			}
	    		}
	    	}
		}
		
		//出入库订单号
		String stockListUnique = System.nanoTime() + "";
		for(Integer m = 0; m < list.size(); m++) {
			if(null != list.get(m).get("goods_barcode") && !list.get(m).get("stockCount").toString().equals("")) {
				BigDecimal stockCount = new BigDecimal(list.get(m).get("stockCount").toString());
				BigDecimal goodsCount = new BigDecimal(list.get(m).get("goods_count").toString());
				Map<String,Object> stockMap = new HashMap<String,Object>();
				stockMap.put("goods_barcode", list.get(m).get("goods_barcode"));
				stockMap.put("goods_count", list.get(m).get("stockCount"));
				stockMap.put("stock_count", stockCount.add(goodsCount).setScale(2,BigDecimal.ROUND_HALF_UP).doubleValue());
				stockMap.put("stock_type", 1);
				stockMap.put("shop_unique", shopUnique);
				stockMap.put("stock_price", list.get(m).get("newInPrice"));
				Staff staff = (Staff) request.getSession().getAttribute("staff");
				stockMap.put("staff_id", staff.getStaff_id());
				stockMap.put("list_unique", stockListUnique);
				goodsDao.addShopStock(stockMap);
			}
		}
		
		return sr;
	}
    
    public ShopsResult importGoodsKindMsg(String goodsMsg,String shopUnique) {
    	ShopsResult sr = new ShopsResult(1,"查询成功!");
    	if(null == goodsMsg || goodsMsg.equals("")) {
    		return sr;
    	}
    	List<Map<String,Object>> list = new ArrayList<Map<String,Object>>();
    	Map<String,Object> map = new HashMap<String,Object>();
    	map.put("shop_unique", shopUnique);
    	
    	if(null != goodsMsg && !goodsMsg.equals("")) {
    		String[] goods = goodsMsg.split(";");
    		
    		for(Integer i = 0; i < goods.length; i++) {
    			Map<String,Object> tmpMap = new HashMap<String,Object>();
    			String[] tmpGoods = goods[i].split(":");
    			
    			if(tmpGoods.length == 2) {
    				tmpMap.put("goodsBarcode", tmpGoods[0]);
    				tmpMap.put("kindUnique", tmpGoods[1]);
    				list.add(tmpMap);
    			}
    		}
    	}
    	//将数据从数据库中查出
    	map.put("list", list);
    	
    	//将数据更新到数据库
    	goodsDao.updateGoodsKind(map);
    	
    	
    	return sr;
    }
    
    public ShopsResult queryGoodsKindMsg(String goodsMsg,String shopUnique) {
    	ShopsResult sr = new ShopsResult(1,"查询成功!");
    	if(null == goodsMsg || goodsMsg.equals("")) {
    		return sr;
    	}
    	List<Map<String,Object>> list = new ArrayList<Map<String,Object>>();
    	Map<String,Object> map = new HashMap<String,Object>();
    	map.put("shop_unique", shopUnique);
    	
    	if(null != goodsMsg && !goodsMsg.equals("")) {
    		String[] goods = goodsMsg.split(";");
    		
    		for(Integer i = 0; i < goods.length; i++) {
    			Map<String,Object> tmpMap = new HashMap<String,Object>();
    			String[] tmpGoods = goods[i].split(":");
    			
    			if(tmpGoods.length == 2) {
    				tmpMap.put("goodsBarcode", tmpGoods[0]);
    				tmpMap.put("kindUnique", tmpGoods[1]);
    				list.add(tmpMap);
    			}
    		}
    	}
    	//将数据从数据库中查出
    	map.put("list", list);
    	
    	List<Map<String,Object>> goodsList = goodsDao.queryGoodsKindMsg(map);
  
    	List<Map<String,Object>> kindList = goodsDao.queryShopKinds(map);
    	for(Integer i = 0; i < goodsList.size(); i++) {
    		Map<String,Object> goodsMap = goodsList.get(i);
    		String goodsBarcode = goodsMap.get("goods_barcode").toString();
    		for(Integer j = 0; j < list.size(); j++) {
    			String barcode = list.get(j).get("goodsBarcode").toString();
    			if(goodsBarcode.equals(barcode)) {
    				goodsMap.put("kindUnique", list.get(j).get("kindUnique"));
    				break;
    			}
    		}
    		String kindUnique = goodsMap.get("kindUnique").toString();
    		for(Integer m = 0; m < kindList.size(); m++) {
    			String kind = kindList.get(m).get("goods_kind_unique").toString();
    			System.out.println(kind + "\\\\\\" + kindUnique + "===" +kind.equals(kindUnique));
    			if(kindUnique.equals(kind)) {
    				goodsMap.put("kindName", kindList.get(m).get("goods_kind_name"));
    			}
    		}
    	}
    	
    	sr.setData(goodsList);
    	
    	return sr;
    }
    /**
     * 查询商品的基础信息
     * @param goodsMsg 商品基本信息
     * @param shopUnique
     * @return
     */
    public ShopsResult queryGoodsBaseMsg(String goodsMsg,String shopUnique) {
    	ShopsResult sr = new ShopsResult(1,"查询成功!");
    	
    	List<Map<String,Object>> list = new ArrayList<Map<String,Object>>();
    	
    	Map<String,Object> map = new HashMap<String,Object>();
    	map.put("shop_unique", shopUnique);
    	if(null != goodsMsg && !goodsMsg.equals("")) {
    		String[] goods = goodsMsg.split(";");
    		for(Integer i = 0; i < goods.length; i++) {
    			Map<String,Object> tmpMap = new HashMap<String,Object>();
    			String[] tmpGoods = goods[i].split(":");
    			if(tmpGoods.length == 4 && !tmpGoods[0].equals("") && !(tmpGoods[1].equals("") && tmpGoods[2].equals("") && tmpGoods[3].equals(""))) {
    				tmpMap.put("goods_name", tmpGoods[0]);
    				tmpMap.put("stockCount", tmpGoods[1]);
    				tmpMap.put("newInPrice", tmpGoods[2]);
    				tmpMap.put("newSalePrice", tmpGoods[3]);
    				list.add(tmpMap);
    			}
    		}
    	}
    	if(null == list || list.isEmpty()) {
    		sr.setStatus(1);
    		sr.setMsg("没有满足条件的商品信息");
    		sr.setCount(0);
    		return sr;
    	}
    	map.put("list", list);
    	
    	List<String> nameList = goodsDao.queryGoodsBaseRepeat(map);
		if(null != nameList && !nameList.isEmpty()) {
			sr.setStatus(0);
			sr.setMsg("本店存在名称相同的商品：" + nameList.get(0));
			return sr;
		}
		
    	List<Map<String,Object>> goodsList = goodsDao.queryGoodsBaseMsg(map);
    	
    	for(Integer j = 0; j < list.size(); j++) {
    		String newGoodsName = list.get(j).get("goods_name").toString();
    		for(Integer i = 0; i < goodsList.size(); i++) {
    			Map<String,Object> tmpGoods = goodsList.get(i);
        		String goodsName = tmpGoods.get("goods_name").toString();
        		
        		if(newGoodsName.equals(goodsName)) {
        			list.get(j).putAll(tmpGoods);
        			break;
        		}
    		}
    	}
    	
    	Comparator<Map<String, Object>> c = new Comparator<Map<String,Object>>(){
    		public int compare(Map<String,Object> m1,Map<String,Object> m2) {
    			String b1 = m1.get("goods_barcode") == null ? "" : m1.get("goods_barcode").toString();
    			String b2 = m2.get("goods_barcode") == null ? "" : m2.get("goods_barcode").toString();
    			return b1.compareTo(b2);
    		}
    	};
    	
    	Collections.sort(list,c);
    	
    	sr.setData(list);
    	sr.setCount(list.size());
    	
    	return sr;
    }
    /**
     * 添加云端商品
     *
     * @param goods_barcode     商品条码
     * @param goods_name        商品名称
     * @param goods_in_price    商品进价
     * @param goods_sale_price  商品售价
     * @param goods_cus_price   商品会员价
     * @param goods_count       库存数量
     * @param goods_standard    商品规格
     * @param goods_unit        销售单位
     * @param goods_picturepath 图片
     * @param addIndex          前端生产的添加号，防止重复添加
     * @param request
     * @return
     */
    public ShopsResult addNewGoodsByCloud(String goods_barcode, String goods_name, Double goods_in_price, Double goods_sale_price, Double goods_cus_price,
                                          Double goods_count, String goods_standard, String goods_unit, String goods_picturepath, HttpServletRequest request, Integer addIndex
    ) {
        ShopsResult sr = new ShopsResult(1, "添加成功!");

        if (redis.getObject(addIndex + goods_barcode) != null) {
            sr.setStatus(0);
            sr.setMsg("该商品已申请添加，请勿重复操作");
            return sr;
        }

        //需要防止商品重复添加
        Staff staff = (Staff) request.getSession().getAttribute("staff");
        Long shop_unique = staff.getShop_unique();

        Map<String, Object> map = new HashMap<String, Object>();
        map.put("goods_barcode", goods_barcode);
        map.put("shop_unique", shop_unique);
        if (goodsDao.queryGoodsDetail(map) != null) {
            sr.setStatus(1);
            sr.setMsg("该商品已存在！");
            return sr;
        }

        //查询商品分类信息,将商品导入默认分类
        String goodsKind = goodsDao.queryFenLeiLimit(shop_unique + "");

        Map<String, Object> goods = new HashMap<String, Object>();
        goods.put("goods_barcode", goods_barcode);
        goods.put("goods_name", goods_name);
        goods.put("goods_kind_unique", goodsKind);
        goods.put("goods_in_price", goods_in_price);
        goods.put("goods_sale_price", goods_sale_price);
        goods.put("goods_web_sale_price", goods_sale_price);
        goods.put("goodsCusPrice", goods_cus_price);
        goods.put("goods_count", goods_count);
        goods.put("goods_standard", goods_standard);
        goods.put("goods_unit", goods_unit);
        goods.put("goods_alias", ChineseCharToEn.getAllFirstLetter(goods_name).toUpperCase());
        goods.put("foreignKey", goods_barcode);
        goods.put("shop_unique", shop_unique);
        goods.put("goods_picturepath", goods_picturepath);
        int k = goodsDao.addNewGoods(goods);
        
        goodsDao.updateGoodsContain(map);
        

        //如果商品库存不为0,添加商品出入口记录
        if (goods_count.compareTo(0.0) > 0) {
            //添加商品出入库记录
            Map<String, Object> stockMap = new HashMap<String, Object>();
            stockMap.put("goods_barcode", goods_barcode);
            stockMap.put("goods_count", goods_count);
            stockMap.put("stock_count", goods_count);
            stockMap.put("stock_type", 1);
            stockMap.put("shop_unique", shop_unique);
            stockMap.put("stock_price", goods_in_price);
            stockMap.put("staff_id", staff.getStaff_id());

            goodsDao.addShopStock(stockMap);
        }

        return sr;
    }

    /**
     * 从大库中查询商品
     *
     * @param page
     * @param pageSize
     * @param goodsMsg
     * @param goodsList
     * @return
     */
    public ShopsResult queryBaseGoods(Integer page, Integer pageSize, String goodsMsg, String goodsList, String shopUnique) {
        ShopsResult sr = new ShopsResult(1, "查询成功!");

        Map<String, Object> map = new HashMap<String, Object>();
        map.put("goodsMsg", goodsMsg);
        map.put("goodsList", goodsList);
        map.put("shopUnique", shopUnique);
        if (null != page && null != pageSize) {
            map.put("startNum", (page - 1) * pageSize);
            map.put("pageSize", pageSize);
        }

        List<Map<String, Object>> list = goodsDao.queryBaseGoods(map);

        Integer count = goodsDao.queryBaseGoodsCount(map);

        sr.setData(list);
        sr.setCount(count);

        return sr;
    }

    /**
     * 删除首页商品信息
     *
     * @param shopUnique
     * @param goods_barcode
     * @return
     */
    public ShopsResult deleteGoodsIndex(String shopUnique, String goods_barcode) {
        ShopsResult sr = new ShopsResult(1, "删除成功!");

        Map<String, Object> map = new HashMap<String, Object>();
        map.put("shop_unique", shopUnique);
        map.put("goods_barcode", goods_barcode);

        goodsDao.deleteGoodsIndex(map);

        return sr;
    }

    //查询放在首页的商品信息

    /**
     * 修改商品列表
     *
     * @param shopUnique
     * @param goodsBarcodes
     * @return
     */
    public ShopsResult addNewGoodsIndex(String shopUnique, @NotNull String goodsBarcodes) {
        ShopsResult sr = new ShopsResult(1, "保存成功!");
        Map<String, Object> map = new HashMap<String, Object>();
        map.put("shop_unique", shopUnique);
        String[] barcodes = goodsBarcodes.split(";");
        map.put("list", barcodes);

        //删除已有数据
        goodsDao.deleteGoodsIndex(map);

        //将新的数据添加
        goodsDao.addNewGoodsIndex(map);
        return sr;
    }

    /**
     * 查询小程序首页显示的商品信息
     *
     * @param shopUnique
     * @return
     */
    public ShopsResult queryGoodsIndexSort(String shopUnique) {
        ShopsResult sr = new ShopsResult(1, "查询成功");
        Map<String, Object> map = new HashMap<String, Object>();
        map.put("shop_unique", shopUnique);
        List<Map<String, Object>> list = goodsDao.queryGoodsIndexSort(map);
        sr.setData(list);
        return sr;
    }

    /**
     * 1、查询加油站品牌信息
     *
     * @param goodsBarcode
     * @param shopUnique
     * @return
     */
    public Map<String, Object> getOilGoodsDetail(String goodsBarcode, String shopUnique) {
        Map<String, Object> map = new HashMap<String, Object>();
        map.put("shopUnique", shopUnique);
        map.put("goodsBarcode", goodsBarcode);

        List<Map<String, Object>> list = goodsDao.queryOilGoodsList(map);

        if (null != list && !list.isEmpty()) {
            return list.get(0);
        }
        return null;
    }

    @Transactional
    public ShopsResult deleteOilGoods(String goodsBarcode, String shopUnique, Integer staffId, Integer equipmentType, String macId, String gunNum, String oilNum) {
        ShopsResult sr = new ShopsResult(1, "删除成功!");

//		addNewGoodsDelete
        //删除商品，添加商品删除记录
        Map<String, Object> map = new HashMap<String, Object>();
        map.put("shopUnique", shopUnique);
        String[] barcodes = goodsBarcode.split(";");
        map.put("barcodes", barcodes);

        goodsDao.deleteGoodsList(map);

        //添加商品删除记录
        List<Map<String, Object>> barList = new ArrayList<Map<String, Object>>();
        Map<String, Object> barMap = new HashMap<String, Object>();
        barMap.put("staffId", staffId);
        barMap.put("shopUnique", shopUnique);
        barMap.put("goodsBarcode", goodsBarcode);
        barMap.put("equipmentType", 1);
        barMap.put("macId", macId);

        barList.add(barMap);

        map.put("barcodes", barList);

        goodsDao.addNewGoodsDelete(map);

        //删除商品关联关系
        map.put("goodsBarcode", goodsBarcode);
        oilDao.deleteOilRelation(map);

        return sr;
    }

    /**
     * 新增或添加商品信息
     *
     * @param shopUnique     店铺编号
     * @param gunNum         油枪编号
     * @param oilNum         油号
     * @param goodsSalePrice 商品售价
     * @param goodsInPrice   商品进价
     * @param goodsCount     商品库存
     * @param goodsId        商品ID
     * @returnaddNewOilMsg
     */
    public ShopsResult addNewOilMsg(String shopUnique, Integer gunNum, String gunName, Integer oilNum, String oilName,
                                    Double goodsSalePrice, Double goodsInPrice, Double goodsCount, Integer goodsId) {
        ShopsResult sr = new ShopsResult(1, "操作成功!");
        Map<String, Object> map = new HashMap<String, Object>();
        map.put("shopUnique", shopUnique);
        map.put("gunNum", gunNum);

        if (null != goodsId) {
            //更新商品信息，注意商品库存不更改，不允许通过这里修改库存
            map.clear();
            map.put("goods_id", goodsId);
            map.put("goods_sale_price", goodsSalePrice);
            map.put("goods_in_price", goodsInPrice);
            map.put("goods_name", gunName + oilName);
            //修改商品价格问题
            goodsDao.updateGoodsMessage(map);
            //
            goodsDao.updateGoodsContain(map);
            //修改对应关系

            map.put("oilNum", oilNum);
            map.put("shopUnique", shopUnique);
            map.put("gunNum", gunNum);
            goodsDao.modifyOilGoodsRelation(map);

            return sr;
        }

        //查询是否已添加，防止重复添加
        List<Map<String, Object>> gunList = goodsDao.queryOilGoodsList(map);
        if (null != gunList && !gunList.isEmpty()) {
            sr.setStatus(0);
            sr.setMsg("该油枪已添加，请选择其他油枪");
            return sr;
        }

        //查询油枪分类信息，做好准备工作
        map.put("shop_unique", shopUnique);
        map.put("goods_kind_name", "加油");
        map.put("valid_type", 1);
        String kindUnique = null;
        List<Map<String, Object>> kindMap = kindDao.queryGoodsKind(map);
        if (null == kindMap || kindMap.isEmpty()) {
            //加油分类不存在，创建加油分类
            Map<String, Object> parKindMap = new HashMap<String, Object>();
            parKindMap.put("shopUnique", shopUnique);
            parKindMap.put("groupUnique", "0");
            parKindMap.put("kindName", "加油大类");
            parKindMap.put("kindUnique", Load.OILPARKINDUNIQUE);
            parKindMap.put("kindType", 2);

            kindDao.addNewGoodsKindLay(parKindMap);

            parKindMap.put("shopUnique", shopUnique);
            parKindMap.put("groupUnique", Load.OILPARKINDUNIQUE);
            parKindMap.put("kindName", "加油");
            parKindMap.put("kindUnique", Load.OILSUBKINDUNIQUE);
            parKindMap.put("kindType", 2);
            kindDao.addNewGoodsKindLay(parKindMap);
            kindUnique = Load.OILSUBKINDUNIQUE;
        } else {
            kindUnique = kindMap.get(0).get("goods_kind_unique").toString();
        }

        //添加新商品
        map.put("goods_name", goodsId);
        map.put("goods_barcode", gunNum + "" + oilNum);
        map.put("goods_name", gunName + oilName);
        map.put("goods_sale_price", goodsSalePrice);
        map.put("goods_in_price", goodsInPrice);
        map.put("goods_kind_unique", kindUnique);
        map.put("goodsCusPrice", goodsSalePrice);
        map.put("goods_count", goodsCount);
        map.put("goods_contain", 1);
        map.put("foreignKey", gunNum + "" + oilNum);
        map.put("goods_web_sale_price", goodsSalePrice);

        goodsDao.addNewGoods(map);
        
        goodsDao.updateGoodsContain(map);

        //添加数据到关系库
        Map<String, Object> oilRelationMap = new HashMap<String, Object>();
        oilRelationMap.put("shopUnique", shopUnique);
        oilRelationMap.put("goodsBarcode", gunNum + "" + oilNum);
        oilRelationMap.put("oilGunNum", gunNum);
        oilRelationMap.put("oilNum", oilNum);

        goodsDao.addNewOilRelation(oilRelationMap);

        return sr;
    }


    /**
     * 查询油品，油枪列表（油枪列表不包含当前店铺已有编号）
     *
     * @return
     */
    @Override
    public List<Map<String, Object>> queryUseAbleOilList(Integer parType, String shopUnique, Integer allMsg) {
        Map<String, Object> map = new HashMap<String, Object>();
        map.put("shopUnique", shopUnique);
        map.put("parType", parType);
        map.put("allMsg", allMsg);
        List<Map<String, Object>> list = goodsDao.queryUseAbleOilList(map);

        return list;
    }

    /**
     * 查询油品信息列表
     *
     * @param shopUnique
     * @return
     */
    public ShopsResult queryOilGoodsList(String shopUnique) {
        ShopsResult sr = new ShopsResult(1, "查询成功！");

        Map<String, Object> map = new HashMap<String, Object>();
        map.put("shopUnique", shopUnique);

        List<Map<String, Object>> list = goodsDao.queryOilGoodsList(map);
        Integer count = goodsDao.queryOilGoodsCount(map);

        sr.setData(list);
        sr.setCount(count);

        return sr;
    }

    /**
     * 查询摄像头列表
     *
     * @param shopUnique 摄像头所属的店铺编号
     * @param goodsId    商品编号
     * @return
     */
    public List<Map<String, Object>> queryMonitorList(String shopUnique, String goodsId) {
        Map<String, Object> map = new HashMap<String, Object>();
        map.put("goodsId", goodsId);

        List<Map<String, Object>> list = goodsDao.queryMonitorList(map);

        return list;
    }

    public PurResult uploadImg(MultipartFile[] file, String shop_unique, HttpServletRequest request) {
        PurResult pr = new PurResult(1, "更新成功!");
        List<Map<String, Object>> barcodes = new ArrayList<Map<String, Object>>();

        //获取服务器所在路径
        String absPath = this.getClass().getClassLoader().getResource("../../").getPath();
        String filePathDetail = File.separator + "image" + File.separator + shop_unique + File.separator;
        String filePath = absPath.substring(0, absPath.length() - request.getContextPath().length()) + filePathDetail;


        //将文件存储到本地，
        File floder = new File(filePath);
        if (!floder.exists()) {
            floder.mkdirs();
        }

        for (Integer i = 0; i < file.length; i++) {
            try {
                Map<String, Object> tempMap = new HashMap<String, Object>();
                String goodsName = file[i].getOriginalFilename();
                String lastName = goodsName.substring(goodsName.lastIndexOf("."));

                UUID uuid = UUID.randomUUID();
                String newName = goodsName;


                //将文件保存到文件
                ShopsUtil.savePicture(file[i], filePath, goodsName, "2");
                //如果图片尺寸过大，压缩
                if (file[i].getSize() > 1024 * 1024) {
                    newName = uuid + lastName;
                    ShopsUtil.targetZoomOut(filePath + File.separator + goodsName, filePath + File.separator + newName, filePath);
                }

                //获取本地文件的文件流
                File tempFile = new File(filePath + newName);
                FileInputStream fis = new FileInputStream(tempFile);
                //将图片上传到文件
                ShopsUtil.ftpUpload(File.separator + shop_unique, newName, fis);//存储到文件服务器


                tempMap.put("barcode", goodsName.split("\\.")[0]);
                tempMap.put("url", File.separator + "image" + File.separator + shop_unique + File.separator + newName);

                barcodes.add(tempMap);

            } catch (Exception e) {
                e.printStackTrace();
                continue;
            }
        }

        //批量修改上传的商品图片路径
        Map<String, Object> params = new HashMap<String, Object>();
        params.put("shop_unique", shop_unique);
        params.put("list", barcodes);

        //更新商品图片信息
        goodsDao.modifyGoodsPicList(params);

        return pr;
    }

    /**
     * 查询店铺的自营供货商信息
     *
     * @param shopUnique
     * @return
     */
    public PurResult getGoodsSupplierMsg(String shopUnique) {
        PurResult result = new PurResult();
        result.setStatus(1);
        result.setMsg("查询成功");
        List<Map<String, Object>> supList = goodsDao.getGoodsSupplierMsg(shopUnique);
        result.setData(supList);
        return result;
    }

    /**
     * 商品信息查询！
     *
     * @param shop_unique          店铺编号
     * @param goodsMessage         输入的商品信息
     * @param goods_kind_unique    商品分类编号
     * @param goods_kind_parunique 商品大类编号
     */
    public PurResult getGoodList(Map<String, Object> params) {
        PurResult result = new PurResult();

        List<Map<String, Object>> goodsList = goodsDao.getGoodList(params);
        Integer count = goodsDao.getGoodListCount(params);
        for (Map<String, Object> map : goodsList) {
//            if (map.get("goods_picturepath") != null) {
//                if ("200".equals(isImagesTrue("http://file.buyhoo.cc/middle" + map.get("goods_picturepath")))) {
//                    map.put("goods_picturepath", "/middle" + map.get("goods_picturepath"));
//                }
//            }
			if (map.get("goods_picturepath") != null) {
				if (!map.get("goods_picturepath").toString().startsWith("http")) {
					map.put("goods_picturepath", "/middle" + map.get("goods_picturepath"));
				}
			}
        }
        result.setStatus(1);
        result.setMsg("查询成功");
        result.setCount(count);
        result.setData(goodsList);
        return result;
    }

    public static String isImagesTrue(String posturl) {
        try {
            posturl = posturl.replaceAll("\\\\", "/");
            URL url = new URL(posturl);
            HttpURLConnection urlcon = (HttpURLConnection) url.openConnection();
            urlcon.setRequestMethod("GET");
            urlcon.setRequestProperty("Content-type", "application/x-www-form-urlencoded");
            if (urlcon.getResponseCode() == HttpURLConnection.HTTP_OK) {
                //System.out.println(HttpURLConnection.HTTP_OK + posturl + ":posted ok!");
                return "200";
            } else {
                //System.out.println(urlcon.getResponseCode() + posturl + ":Bad post...");
                return "404";
            }
        } catch (Exception e) {
            //e.printStackTrace();
            return "404";
        }

    }

    /**
     * 价签打印
     *
     * @param shop_unique          店铺编号
     * @param goodsMessage         输入的商品信息
     * @param goods_kind_unique    商品分类编号
     * @param goods_kind_parunique 商品大类编号
     */
    public PurResult getGoodPrintList(Map<String, Object> params) {
        PurResult result = new PurResult();

        List<Map<String, Object>> goodsList = goodsDao.getGoodPrintList(params);

        Integer count = goodsDao.getGoodPrintListCount(params);


        if (params.containsKey("printType")) {
            String printType = (String) params.get("printType");
            if (printType.equals("1")) {

                String oldData = (String) params.get("oldData");
                if (!oldData.equals("Y")) {
                    int limit = Integer.parseInt(params.get("limit").toString());

                    List<Map<String, Object>> old_goodsList = strToList(oldData, HashMap.class);


                    if (limit > old_goodsList.size()) {
                        count = old_goodsList.size() + count;
                        for (Map<String, Object> t : old_goodsList) {
                            t.put("LAY_CHECKED", true);
                            int m = 0;
                            for (Map<String, Object> e : goodsList) {
                                e.put("LAY_CHECKED", true);
                                if (e.get("goods_barcode").equals(t.get("goods_barcode"))) {
                                    m = m + 1;
                                }
                            }
                            if (m == 0) {
                                goodsList.add(t);
                            }

                        }
                    }

                }
            }

        }

        result.setStatus(1);
        result.setMsg("查询成功");
        result.setCount(count);
        result.setData(goodsList);
        return result;
    }

    /**
     * 商品详情查询
     *
     * @param shop_unique   店铺编号
     * @param goods_barcode 商品条码
     * @return
     */
    public ShopsResult queryGoodsDetail(String shop_unique, String goods_barcode) {
        ShopsResult shop = new ShopsResult();
        Map<String, Object> map = new HashMap<String, Object>();
        map.put("shop_unique", shop_unique);
        map.put("goods_barcode", goods_barcode);
        Map<String, Object> goods = goodsDao.queryGoodsDetail(map);
        if (null == goods || goods.size() == 0) {
            shop.setStatus(1);
            shop.setMsg("店铺没有该商品的信息！");
            return shop;
        }
        shop.setData(goods);
        shop.setStatus(0);
        shop.setMsg("查询成功！");
//		GoEasy goeasy=new GoEasy("BC-91e94f22780e4ed697c2f0837f1de9c5");
//		goeasy.publish("测试频道", "测试");
        return shop;
    }

    /**
     * 更新商品信息
     *
     * @param goods_barcode 商品条码
     * @param goods_name 商品名称
     * @param goods_kind_unique 商品分类编号
     * @param goods_brand 商品品牌
     * @param goods_in_price 商品进价
     * @param goods_sale_price 商品售价
     * @param goods_life 商品保质期
     * @param goods_points 会员积分
     * @param goods_count 商品库存量
     * @param goods_sold 销售数量
     * @param goods_standard 商品规格
     * @param default_supplier_unique 默认供货商编号
     * @param shop_unique                    商铺编号
     * @param staff_id
     * @return
     */
    @Transactional
    public ShopsResult updateGoodsMessage(String shop_unique, String goods_barcode, String goods_name, String goods_kind_unique,
                                          String goods_brand, Double goods_in_price, Double goods_sale_price, String goods_unit,
                                          Integer goods_life, Integer goods_points, Double goods_count, Double goods_sold, String goods_standard, String default_supplier_unique,
                                          Double goods_cus_price, Double goods_web_sale_price, String staff_id, HttpServletRequest request) {
        ShopsResult shop = new ShopsResult();
        if (null == goods_barcode || goods_barcode.equals("")) {
            shop.setStatus(1);
            shop.setMsg("请输入商品条码");
            return shop;
        }

        if (null == shop_unique || shop_unique.equals("")) {
            shop.setStatus(1);
            shop.setMsg("店铺信息不能为空");
            return shop;
        }
        Map<String, Object> map = new HashMap<String, Object>();
        Map<String, Object> dictMap = new HashMap<String, Object>();
        map.put("shop_unique", shop_unique);
        map.put("goods_barcode", goods_barcode);
        dictMap.put("goods_barcode", goods_barcode);
        map.put("goods_name", goods_name);
        map.put("goods_kind_unique", goods_kind_unique);
        map.put("goods_brand", goods_brand);
        map.put("goods_in_price", goods_in_price);
        map.put("goods_sale_price", goods_sale_price);
        map.put("goods_unit", goods_unit);
        map.put("goods_life", goods_life);
        map.put("goods_points", goods_points);
        map.put("goods_count", goods_count);
        map.put("goods_sold", goods_sold);
        map.put("goods_standard", goods_standard);
        map.put("default_supplier_unique", default_supplier_unique);
        String update_time = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
        map.put("update_time", update_time);
        map.put("goodsCusPrice", new BigDecimal(goods_cus_price).setScale(2, BigDecimal.ROUND_HALF_UP));
        map.put("goods_web_sale_price", goods_web_sale_price);

		/**
		 * 此处需要添加
		 * 1、商品上下级状态
		 * 2、商品foreignKey
		 * 3、goodStockPrice（这个真的有吗）
		 * 4、goods_id，
		 * 5、goods_cus_price，会员价，没有就不更新前端不就好了吗
		 * 6、supplierUnique，供货商信息
		 */
		map.put("goods_cus_price", map.get("goodsCusPrice"));
		map.put("supplierUnique", default_supplier_unique);
		map.put("goodStockPrice",goods_sale_price);
		map.put("goods_id","");
		map.put("foreignKey","");
		map.put("shelf_state",null);

		Map<String, Object> params = new HashMap<>();
		params.put("shop_unique", shop_unique);
		params.put("goods_barcode", goods_barcode);
		RecordGoods sourceGoods = goodsDao.selectSourceGoods(params);
		RecordGoods resultGoods = new RecordGoods();
		BeanUtil.copyProperties(sourceGoods, resultGoods);
		resultGoods.setShopUnique(Long.parseLong(shop_unique));
		resultGoods.setGoodsBarcode(goods_barcode);
		resultGoods.setGoodsName(goods_name);
		if (StrUtil.isNotBlank(goods_kind_unique)) {
			resultGoods.setGoodsKindUnique(Long.parseLong(goods_kind_unique));
		}
		if (StrUtil.isNotBlank(goods_brand)){
			resultGoods.setGoodsBrand(goods_brand);
		}
		resultGoods.setGoodsInPrice(new BigDecimal(goods_in_price).setScale(2,BigDecimal.ROUND_HALF_UP));
		resultGoods.setGoodsSalePrice(new BigDecimal(goods_sale_price).setScale(2, BigDecimal.ROUND_HALF_UP));
		if (StrUtil.isNotBlank(goods_unit)) {
			resultGoods.setGoodsUnit(goods_unit);
		}
		if (ObjectUtil.isNotNull(goods_life)){
			resultGoods.setGoodsLife(goods_life);
		}
		if (ObjectUtil.isNotNull(goods_points)){
			resultGoods.setGoodsPoints(goods_points);
		}
		resultGoods.setGoodsCount(new BigDecimal(goods_count).setScale(2, BigDecimal.ROUND_HALF_UP));
		resultGoods.setGoodsSold(new BigDecimal(goods_sold).setScale(2, BigDecimal.ROUND_HALF_UP));
		if (StrUtil.isNotBlank(goods_standard)){
			resultGoods.setGoodsStandard(goods_standard);
		}
		if (ObjectUtil.isNotNull(default_supplier_unique)){
			resultGoods.setDefaultSupplierUnique(default_supplier_unique);
		}
		resultGoods.setUpdateTime(update_time);
		resultGoods.setGoodsCusPrice(String.valueOf(new BigDecimal(goods_cus_price).setScale(2,BigDecimal.ROUND_HALF_UP)));
		resultGoods.setGoodsWebSalePrice(new BigDecimal(goods_web_sale_price).setScale(2, BigDecimal.ROUND_HALF_UP));
		int k = goodsDao.updateGoodsMessage(map);
        goodsDao.updateGoodsContain(map);

		//查询当前商品的id和foreignKey
		Map<String,Object> baseGoodsMap = goodsDao.selectBaseGoodsMsg(params);
		System.out.println("商品基本信息" + baseGoodsMap);
		map.putAll(baseGoodsMap);
        
        if (k == 0) {
            shop.setStatus(1);
            shop.setMsg("更新失败");
            return shop;
        }

        //mqtt 同步商品
        try {
            RedisCache rc = new RedisCache("");
            Object mac = rc.getObject("topic_" + shop_unique);
            if (mac != null) {
                @SuppressWarnings("unchecked")
                List<String> macIdList = (List<String>) mac;
                //2 MQTT 发送消息
                for (String macid : macIdList) {

                    Map<String, Object> data = new HashMap<String, Object>();
                    data.put("ctrl", "msg_goods_update");
                    data.put("ID", macid);
                    data.put("status", 200);
                    List<Map<String, Object>> returnList = new ArrayList<>();
                    returnList.add(map);
                    data.put("data", returnList);
                    data.put("count", 1);
                    MqttxUtil.sendMapMsg(data, macid);
                }

            }
            //清除缓存
            String rcId = "pcGoods" + shop_unique + 1 + 3 + null;
            Object res = rc.getObject(rcId);
            if (null != res) {
                rc.removeObject(rcId);
            }
            String rcId2 = "pcGoods" + shop_unique + 0 + 3 + null;
            Object res2 = rc.getObject(rcId2);
            if (null != res2) {
                rc.removeObject(rcId2);
            }


        } catch (Exception e) {
            e.printStackTrace();
        }

        shop.setStatus(0);
        shop.setMsg("商品信息更新成功！");

		try {
			System.out.println("我要发送商品更新记录");
			//操作信息
			RecordGoodsOper recordGoodsOper = new RecordGoodsOper();
			recordGoodsOper.setGoodsBarcode(sourceGoods.getGoodsBarcode());
			recordGoodsOper.setShopUnique(sourceGoods.getShopUnique());
			recordGoodsOper.setOperType(OperTypeEnum.OPER_TYPE_UPDATE.getValue());
			recordGoodsOper.setDeviceSource(DeviceSourceEnum.PC_WEB.getValue());
			recordGoodsOper.setUserType(UserTypeEnum.SHOP_STAFF.getValue());
			recordGoodsOper.setCreateTtime(DateUtil.formatDateTime(DateUtil.date()));
			recordGoodsOper.setOperSource(OperSourceEnum.GOODS_CHANGE.getValue());
			recordGoodsOper.setDeviceSourceMsg(DeviceSourceEnum.PC_WEB.getLabel());
			if (StrUtil.isNotBlank(staff_id)) {
				recordGoodsOper.setUserId(staff_id);
			}
			RecordGoodsOperParams recordGoodsOperParams = new RecordGoodsOperParams();
			recordGoodsOperParams.setSourceGoods(sourceGoods);
			recordGoodsOperParams.setResultGoods(resultGoods);
			recordGoodsOperParams.setRecordGoodsOper(recordGoodsOper);
			String host = UtilForJAVA.getHostByServerName(request.getServerName());
			String url = StringUtils.join(host,"/shopmanager/record/recordGoodsOper.do");
			String json = com.alibaba.fastjson.JSONObject.toJSONString(recordGoodsOperParams);
			System.out.println("-------------------同步商品操作记录，url:"+ url);
			System.out.println("-------------------同步商品操作记录，参数:"+ json);
			String result = HttpUtil.post(url, json);
			System.out.println("-------------------同步商品操作记录，返回值:"+ result);
		} catch (Exception e) {
			e.printStackTrace();
		}
		return shop;
    }

    /**
     * 商品供应商查询
     * 此方法只能查询店铺所在区县的供应商中提供该商品的供应商
     *
     * @param shop_unique   店铺编号
     * @param goods_barcode 商品条码
     * @return
     */
    public ShopsResult queryGoodsSupplier(String shopUnique, String goodsBarcode) {
        ShopsResult shop = new ShopsResult();
        Map<String, Object> map = new HashMap<String, Object>();
        map.put("shopUnique", shopUnique);
        map.put("goodsBarcode", goodsBarcode);
        List<Map<String, Object>> supplier = goodsDao.queryGoodsSupplier(map);
        if (supplier.size() == 0) {
            shop.setStatus(1);
            shop.setMsg("没有满足条件的供货商信息！");
            return shop;
        }
        shop.setStatus(0);
        shop.setMsg("商品供应商查询成功！");
        shop.setData(supplier);
        return shop;
    }

    /**
     * 更新图片信息
     *
     * @param shop_unique   供货商编号
     * @param request       图片请求
     * @param goods_barcode 商品条码
     * @return
     */
    @Transactional
    public ShopsResult updateGoodsImage(String shop_unique, HttpServletRequest request, String goods_barcode, String goods_name, String goods_kind_unique, String goods_brand, Double goods_in_price, Double goods_sale_price,
                                        Integer goods_life, Integer goods_points, Integer goods_count, Integer goods_sold, String goods_standard, String default_supplier_unique, String goods_address, String goods_remarks, String goods_unit, String sup_goods_barcode) {
        ShopsResult shop = new ShopsResult();
        Map<String, Object> map = new HashMap<String, Object>();
        Map<String, Object> dictMap = new HashMap<String, Object>();
        String goods_picturepath = dowithGoodsPicture(shop_unique, request, goods_barcode);
        map.put("shop_unique", shop_unique);
        map.put("goods_picturepath", goods_picturepath);
        dictMap.put("goods_picturepath", goods_picturepath);
        map.put("goods_barcode", goods_barcode);
        dictMap.put("goods_barcode", goods_barcode);
        map.put("goods_name", goods_name);
        map.put("goods_kind_unique", goods_kind_unique);
//		dictMap.put("goods_kind_unique", goods_kind_unique);
        map.put("goods_brand", goods_brand);
        dictMap.put("goods_brand", goods_brand);
        map.put("goods_in_price", goods_in_price);
        map.put("goods_sale_price", goods_sale_price);
        map.put("goods_life", goods_life);
        map.put("goods_points", goods_points);
        map.put("goods_count", goods_count);
        map.put("goods_sold", goods_sold);
        map.put("goods_standard", goods_standard);
        dictMap.put("goods_standard", goods_standard);
        map.put("goods_address", goods_address);
        map.put("goods_remarks", goods_remarks);
        map.put("sup_goods_barcode", sup_goods_barcode);
        map.put("default_supplier_unique", default_supplier_unique);
        map.put("update_time", new Timestamp(new Date().getTime()));
        map.put("goods_unit", goods_unit);
        int k = goodsDao.updateGoodsMessage(map);
        //goodsDao.updateGoodsMessage(dictMap);//更新商品图片同时，更新其他店铺商品图片
        if (k == 0) {
            shop.setStatus(1);
            shop.setMsg("更新失败");
            return shop;
        }
        shop.setStatus(0);
        shop.setMsg("更新商品图片成功！");
        //
        k = dictDao.updateGoodsDictMsg(map);
        int daku = dictDao.queryGoodsDictCunZai(map);
        if (k == 0 && daku == 0) {
            dictDao.addNewGoodsDict(map);
        }
        return shop;
    }

    /**
     * 图片处理
     *
     * @param shop_unique
     * @param request
     * @param goods_barcode
     * @return
     */
    public String dowithGoodsPicture(String shop_unique, HttpServletRequest request, String goods_barcode) {
        MultipartFile file = null;
        String goods_pictureName = null;
        if (request instanceof MultipartHttpServletRequest) {
            MultipartHttpServletRequest multipartHttpServletRequest = (MultipartHttpServletRequest) request;
            Map<String, MultipartFile> mp = multipartHttpServletRequest.getFileMap();
            file = mp.get("goods_picture");
        }
        if (null != file) {
            String orName = file.getOriginalFilename();
            String lastName = orName.substring(orName.lastIndexOf("."));
            String goods_dir = File.separator + "home" + File.separator + "apache-tomcat-7.0.42"
                    + File.separator + "webapps" + File.separator + "image" + File.separator + shop_unique
                    + File.separator;
            File dir = new File(goods_dir);
            if (!dir.exists()) {
                dir.mkdirs();
            }
            goods_pictureName = goods_barcode + lastName;
            PicSaveUtil.handleFileUpId(file, request, goods_dir, goods_pictureName);//保存图片
        } else {
            return null;
        }
        String goods_picturepath = "image" + File.separator + shop_unique + File.separator + goods_pictureName;
        return goods_picturepath;
    }

    /**
     * 添加新商品
     *
     * @param shop_unique                    商铺编号
     * @param goods_barcode 商品条码
     * @param goods_name 商品名称
     * @param goods_kind_unique 商品分类编号
     * @param goods_brand 商品品牌
     * @param goods_in_price 商品进价
     * @param goods_sale_price 商品售价
     * @param goods_life 商品保质期
     * @param goods_points 会员积分
     * @param goods_count 商品库存量
     * @param goods_sold 销售数量
     * @param goods_standard 商品规格
     * @param default_supplier_unique 默认供货商编号
     * @return
     */
    @Transactional
    public ShopsResult addNewGoods(String shop_unique, String goods_barcode, String goods_name, String goods_kind_unique, String goods_brand, Double goods_in_price, Double goods_sale_price,
                                   Integer goods_life, Integer goods_points, Integer goods_count, Integer goods_sold, String goods_standard, String default_supplier_unique, String goods_address, String goods_remarks) {
        ShopsResult shop = new ShopsResult();
        Map<String, Object> map = new HashMap<String, Object>();
        map.put("goods_barcode", goods_barcode);
        map.put("shop_unique", shop_unique);
        if (goodsDao.queryGoodsDetail(map) != null) {
            shop.setStatus(1);
            shop.setMsg("该商品已存在！");
            return shop;
        }
        map.put("goods_name", goods_name);
        map.put("goods_kind_unique", goods_kind_unique);
        map.put("goods_brand", goods_brand);
        map.put("goods_in_price", goods_in_price);
        map.put("goods_sale_price", goods_sale_price);
        map.put("goods_life", goods_life);
        map.put("goods_points", goods_points);
        map.put("goods_count", goods_count);
        map.put("goods_sold", goods_sold);
        map.put("goods_standard", goods_standard);
        map.put("default_supplier_unique", default_supplier_unique);
        map.put("goods_alias", ChineseCharToEn.getAllFirstLetter(goods_name).toUpperCase());
        map.put("goods_address", goods_address);
        map.put("goods_remarks", goods_remarks);
        int k = goodsDao.addNewGoods(map);

        if (k != 1) {
            shop.setStatus(1);
            shop.setMsg("添加失败！");
            return shop;
        }
        k = dictDao.updateGoodsDictMsg(map);
        int daku = dictDao.queryGoodsDictCunZai(map);
        if (k == 0 && daku == 0) {
            dictDao.addNewGoodsDict(map);
        }
        shop.setStatus(0);
        shop.setMsg("添加成功！");
        return shop;
    }

    public ShopsResult goodsTest() {
        ShopsResult ns = new ShopsResult();
        List<String> list = goodsDao.testSame();
        int k = goodsDao.deleteSame(list);
        if (k == 0) {
            ns.setStatus(1);
            ns.setMsg("删除失败！");
            return ns;
        }
        ns.setStatus(0);
        ns.setData(list);
        return ns;
    }

    public ShopsResult selectName(Integer pageNum) {
        ShopsResult ns = new ShopsResult();
        Map<String, Object> map = new HashMap<String, Object>();
        map.put("startNum", pageNum * 100);
        List<Map<String, Object>> result = goodsDao.selectName(map);
        ns.setData(result);
        return ns;
    }

    public ShopsResult selectGoods(Integer pageNum) {
        ShopsResult ns = new ShopsResult();
        Map<String, Object> map = new HashMap<String, Object>();
        map.put("startNum", pageNum * 100);
        List<Map<String, Object>> result = goodsDao.selectGoods(map);
        ns.setData(result);
        return ns;
    }


    /**
     * 商品信息查询，用于饼状图制作
     *
     * @param shop_unique
     * @param days
     * @return
     */
    public ShopsResult goodsCount(String shop_unique, Integer days) {
        ShopsResult ns = new ShopsResult();
        Map<String, Object> map = new HashMap<String, Object>();
        map.put("shop_unique", shop_unique);
        map.put("days", days);
//		map.put("stockType", 3);
        List<Map<String, Object>> result = goodsDao.goodsCount(map);

        ns.setStatus(0);
        ns.setMsg("查询成功！");
        ns.setData(result);
        return ns;
    }

    /**
     * 商品信息查询，用于饼状图金额制作
     *
     * @param shop_unique
     * @param days
     * @return
     */
    public ShopsResult goodsAmount(String shop_unique, Integer days) {
        ShopsResult ns = new ShopsResult();
        Map<String, Object> map = new HashMap<String, Object>();
        map.put("shop_unique", shop_unique);
        map.put("days", days);
        List<Map<String, Object>> result = goodsDao.goodsAmount(map);

        ns.setStatus(0);
        ns.setMsg("查询成功！");
        ns.setData(result);
        return ns;
    }

    /**
     * 商品信息查询页数查询
     *
     * @param shop_unique
     * @param goodsMessage
     * @param goods_kind_unique
     * @param goods_kind_parunique
     * @param stockType
     * @return
     */
    public ShopsResult queryGoodsPages(String shop_unique, String goodsMessage, String goods_kind_unique, String goods_kind_parunique, Integer stockType, Integer pageSize) {
        ShopsResult shop = new ShopsResult();
        Map<String, Object> map = new HashMap<String, Object>();
        if ("-1".equals(goods_kind_parunique)) {
            goods_kind_parunique = null;
        }
        if ("-1".equals(goods_kind_unique)) {
            goods_kind_unique = null;
        }
        if (null == goodsMessage || "".equals(goodsMessage)) {
            goodsMessage = null;
        } else {
            goodsMessage = "%" + goodsMessage + "%";
        }
        map.put("stockType", stockType);
        map.put("shop_unique", shop_unique);
        map.put("goodsMessage", goodsMessage);
        map.put("goods_kind_parunique", goods_kind_parunique);
        map.put("goods_kind_unique", goods_kind_unique);
        map.put("pageSize", pageSize);
        map.put("days", 30);
        map.put("pageSize", pageSize);
//		int k=0;
        int k = goodsDao.queryGoodsPages(map);
        if (k == 0) {
            shop.setStatus(1);
            shop.setMsg("没有满足条件的信息！");
            return shop;
        }
        shop.setData(k);
        shop.setStatus(0);
        shop.setMsg("查询成功！");
        return shop;
    }

    /**
     * 商品捆绑信息查询
     *
     * @param map
     * @return
     */
    public ShopsResult bindGoodsAddSearch(Map<String, Object> map) {
        ShopsResult ns = new ShopsResult();
        List<Map<String, Object>> data = goodsDao.bindGoodsAddSearch(map);
        if (data.isEmpty()) {
            ns.setStatus(1);
            ns.setMsg("没有满足条件的商品信息!");
            return ns;
        }
        ns.setStatus(0);
        ns.setMsg("查询成功！");
        ns.setData(data);
        return ns;
    }

    /**
     * 促销商品页数查询
     *
     * @param map
     * @return
     */
    public ShopsResult promotionGoodsPages(Map<String, Object> map) {
        ShopsResult sr = new ShopsResult();
        Integer pages = goodsDao.promotionGoodsPages(map);
        if (0 == pages) {
            sr.setStatus(1);
            sr.setMsg("没有符合条件的商品信息！");
            return sr;
        }
        sr.setStatus(0);
        sr.setMsg("查询成功！");
        sr.setData(pages);
        return sr;
    }


    /**
     * 商品信息查询
     *
     * @param map
     * @return
     */
    public ShopsResult promotionGoodsSearchByPage(Map<String, Object> map) {
        ShopsResult sr = new ShopsResult();
        List<Map<String, Object>> data = goodsDao.promotionGoodsSearchByPage(map);
        if (null == data || data.isEmpty()) {
            sr.setStatus(1);
            sr.setMsg("没有满足条件的商品信息！");
            return sr;
        }
        sr.setStatus(0);
        sr.setMsg("查询成功！");
        sr.setData(data);
        return sr;
    }

    /**
     * 取消促销活动（将促销数量改为0，并将折扣改为1）
     *
     * @param map
     * @return
     */
    @Transactional
    public ShopsResult cancelPromotion(Map<String, Object> map) {
        ShopsResult sr = new ShopsResult();
        int k = goodsDao.cancelPromotion(map);
        if (k == 0) {
            sr.setStatus(1);
            sr.setMsg("取消失败！");
            return sr;
        }
        sr.setStatus(0);
        sr.setMsg("取消成功！");
        return sr;
    }

    /**
     * 商品详情查询
     *
     * @param map
     * @return
     */
    public ShopsResult goodsDetail(Map<String, Object> map) {
        ShopsResult sr = new ShopsResult();

        Timestamp systemTime = new Timestamp(new Date().getTime());
        map.put("systemTime", systemTime);
        BaseGoods data = goodsDao.goodsDetail(map);
        if (null == data) {
            sr.setStatus(0);
            sr.setMsg("商品条码或店铺编号错误！");
            return sr;
        }
        sr.setStatus(1);
        sr.setMsg("查询成功！");
        sr.setData(data);
        return sr;
    }

    /**
     * 查询商品供货商供应商品信息
     *
     * @param map
     * @return
     */
    public ShopsResult querySupplierGoods(Map<String, Object> map) {
        ShopsResult sr = new ShopsResult();
        List<Map<String, Object>> data = goodsDao.querySupplierGoods(map);
        if (null == data || data.isEmpty()) {
            sr.setStatus(2);
            sr.setMsg("没有满足条件的信息！");
            return sr;
        }
        sr.setStatus(1);
        sr.setMsg("查询成功！");
        sr.setData(data);
        return sr;
    }

    /**
     * 更新商品信息
     *
     * @param map
     * @return
     */
    @Transactional
    public ShopsResult updateListGoodsMessage(Map<String, Object> map, String goodsMessage,
                                              HttpServletRequest request, Double goodsCount, String goodsBarcode, Long shopUnique, Long foreignKey) {
        ShopsResult sr = new ShopsResult();
//		List<Map<String,Object>> ordata=new ArrayList<Map<String,Object>>();//存放需要保存的数据
        String[] listGoods = goodsMessage.split("]@");
        MultipartFile file = ShopsUtil.testMulRequest(request, "goodsPicture");
        Map<String, Object> nmap = new HashMap<String, Object>();//存放需要新添加的商品
        List<String> barcodes = new ArrayList<String>();//
        if (null != file) {//图片信息处理
            String orName = file.getOriginalFilename();
            String lastName = orName.substring(orName.lastIndexOf("."));
            String ngoods = goodsBarcode + Math.round(Math.random() * 100) + lastName;
            String filePath = File.separator + "home" + File.separator + "apache-tomcat-7.0.42"
                    + File.separator + "webapps" + File.separator + "image" + File.separator + shopUnique;
            String ngoodsPath = "image" + File.separator + shopUnique + File.separator + ngoods;
            //存到文件服务器-start
            SFTPUtil sftp = new SFTPUtil(FTPConfig.username, FTPConfig.password, FTPConfig.host, FTPConfig.port);
            sftp.login();
            InputStream is;
            boolean flag = false;
            try {
                is = file.getInputStream();
                sftp.upload(FTPConfig.goods_path + "/" + shopUnique, ngoods, is);
            } catch (Exception e) {
                e.printStackTrace();
            }
            //存到文件服务器-end
            flag = ShopsUtil.savePicture(file, filePath, ngoods, "2");
            if (flag) {
                map.put("goodsPicturepath", ngoodsPath);
            }
        }
        for (int i = 0; i < listGoods.length; i++) {//商品信息处理
            String[] goods = listGoods[i].split(":;:");
            for (int j = 0; j < goods.length; j++) {
                if (goods[j].equals("@$@")) {
                    goods[j] = null;
                }
            }
            if (null == goods[0]) {
                break;
            }
            nmap.put("goodsBarcode", goods[0]);
            barcodes.add(goods[0]);
            nmap.put("goodsName", goods[1]);
            nmap.put("goodsAlias", ChineseCharToEn.getAllFirstLetter(goods[1]));
            nmap.put("goodsUnit", goods[2]);
            nmap.put("goodsInPrice", goods[3]);
            nmap.put("goodsSalePrice", goods[4]);
            Integer contain = 0;
            contain = (new Double(goods[5])).intValue();
            nmap.put("goodsContain", contain);
            if (null != contain) {
                nmap.put("goodsCount", new Double(goodsCount / contain).intValue());
            }
            nmap.put("updateTime", new Timestamp(new Date().getTime()));
            nmap.putAll(map);
            int l = dictDao.moifyGoodsDictMessage(nmap);//测试更新大库信息
            int k = goodsDao.toUpdateGoodsMessage(nmap);//测试更新商品信息
            if (l == 0) {
                dictDao.newGoodsDict(nmap);//若更新数据失败，则添加大库信息
            }
            if (k == 0) {
                goodsDao.newGoodsMessage(nmap);//若更新数据失败，则添加店铺商品信息
            }
        }
        Map<String, Object> bar = new HashMap<String, Object>();
        bar.put("list", barcodes);
        bar.put("foreignKey", foreignKey);
        goodsDao.updateGoodsForeignKey(bar);//更新商品的包装外键关键词
        sr.setStatus(1);
        sr.setMsg("更新成功！");
        return sr;
    }


    public ShopsResult aaa() {
        ShopsResult sr = new ShopsResult();
        //查询需要自动采购的店铺信息及店铺的相关设置信息
        List<Map<String, Object>> shops = goodsDao.queryShopUniqueNeedAutoPur();
        List<Map<String, Object>> newSubList = new ArrayList<Map<String, Object>>();//记录子订单信息
        List<Map<String, Object>> listDetail = new ArrayList<Map<String, Object>>();//记录子订单详情
        for (int i = 0; i < shops.size(); i++) {
            List<Map<String, Object>> goods = new ArrayList<Map<String, Object>>();
            String outStockRemindType = shops.get(i).get("outStockRemindType").toString();
            if (outStockRemindType.equals("1")) {
                goods.addAll(goodsDao.queryGoodsSaleMessage(shops.get(i)));
            } else if (outStockRemindType.equals("2")) {
                goods.addAll(goodsDao.queryGoodsSaleMessageByCount(shops.get(i)));
            }
            if (null == goods || goods.isEmpty()) {
                continue;
            } else {
                //查询店铺的所有供货商信息，并存储，用于分别生成店铺的子订单
                Map<String, String> supList = new HashMap<String, String>();
                for (int k = 0; k < goods.size(); k++) {
                    if (supList.get("supplierUnique") != null) {
                        continue;
                    }
                    supList.put(goods.get(k).get("supplierUnique").toString(), goods.get(k).get("supplierUnique").toString());
                }
                Map<String, Object> map = shops.get(i);//店铺相关配置信息
                //主订单信息！！！
                Map<String, Object> mainList = new HashMap<String, Object>();
                String purListParunique = new Date().getTime() + "" + (Math.round(Math.random() * 1000 + 1000));//注定的编号
                mainList.put("purchaseListUnique", purListParunique);
                mainList.putAll(map);
                mainList.put("purchaseListStatus", 1);
                mainList.put("purchaseListRemark", "自动采购主订单信息！");
                Double mainSaleTotal = 0.0;//主订单总商品销售额
                Double mainSum = 0.0;//主订单总商品数量
                Double mainTotal = 0.0;//主订单总商品金额
                Set<String> set = supList.keySet();
                int m = 0;
                //循环依次记录子订单信息，并修改主订单信息
                for (String str : set) {
                    //子订单相关信息
                    Double sum = 0.0;
                    Double total = 0.0;
                    Double saleTotal = 0.0;
                    String purListUnique = purListParunique + m;
                    Map<String, Object> subMap = new HashMap<String, Object>();
                    for (int n = 0; n < goods.size(); n++) {
//									System.out.println(goods.get(n));
                        //若为同一个供货商，则将商品信息添加至该子订单中
                        if (str.equals(goods.get(n).get("supplierUnique").toString())) {
                            Long detailCount = Math.round(Double.parseDouble(goods.get(n).get("purCount").toString())) + 1;
                            if (shops.get(i).get("outStockRemindType").toString().equals("1")) {
                            } else {
                                detailCount -= 1;
                            }
                            sum = ShopsUtil.addDoubleSum(sum, detailCount);

                            total = ShopsUtil.addDoubleSum(total,
                                    ShopsUtil.multiplicationDouble(detailCount, Double.parseDouble(goods.get(n).get("goodsPrice").toString()))
                            );
                            saleTotal = ShopsUtil.addDoubleSum(saleTotal,
                                    ShopsUtil.multiplicationDouble(detailCount, Double.parseDouble(goods.get(n).get("goodsSalePrice").toString()))
                            );
                            Map<String, Object> gmap = goods.get(n);
                            gmap.put("purchaseListUnique", purListUnique);
                            gmap.put("purchaseListParunique", purListParunique);
                            gmap.put("purCount", detailCount);
                            listDetail.add(gmap);
                        }
                    }
//								System.out.println(2);

                    //修改主订单信息
                    mainSum = ShopsUtil.addDoubleSum(mainSum, sum);
                    mainTotal = ShopsUtil.addDoubleSum(mainTotal, total);
                    mainSaleTotal = ShopsUtil.addDoubleSum(mainSaleTotal, saleTotal);

                    //修改子订单信息
                    if (sum > 0) {
                        subMap.put("purchaseListSum", sum);
                        subMap.put("purchaseListTotal", total);
                        subMap.put("purchaseListSaleTotal", saleTotal);
                        subMap.put("purchaseListUnique", purListUnique);
                        subMap.put("purchaseListRemark", "自动采购订单");
                        subMap.put("purchaseListParunique", purListParunique);
                        subMap.put("supplierUnique", str);
                        subMap.putAll(shops.get(i));
                        newSubList.add(subMap);
                    }
                    m++;
//							System.out.println(3);
                }
//						System.out.println(4);
                if (mainSum > 0) {
                    mainList.put("purchaseListSum", mainSum);
                    mainList.put("purchaseListTotal", mainTotal);
                    mainList.put("purchaseListSaleTotal", mainSaleTotal);
                    mainList.put("purchaseListParunique", 0);
                    mainList.put("supplierUnique", "111");
                    mainList.put("purchaseListRemark", "自动采购订单主订单！");
                    newSubList.add(mainList);
                }
            }
//					System.out.println("循环结束！！！");
        }
//				System.err.println("添加订单至数据库");
//				System.out.println(newSubList.size());
//				for(int i=0;i<newSubList.size();i++){
//					System.out.println();
//					System.out.println();
//					System.out.println(i);
//					System.out.println(newSubList.get(i).get("purchaseListUnique"));
//					System.out.println(newSubList.get(i).get("shopUnique"));
//					System.out.println(newSubList.get(i).get("purchaseListSum"));
//					System.out.println(newSubList.get(i).get("purchaseListTotal"));
//					System.out.println(newSubList.get(i).get("purchaseListRemark"));
//					System.out.println(newSubList.get(i).get("purchaseListParunique"));
//					System.out.println(newSubList.get(i).get("supplierUnique"));
//					System.out.println(newSubList.get(i).get("purchaseListSaleTotal"));
//					System.out.println(">>>>>>>>>>>");
//				}
        if (newSubList.size() == 0) {
            sr.setStatus(1);
            sr.setMsg("没有需要订购的商品！");
            return sr;
        }
        int k = goodsDao.addNewAutoPurList(newSubList);
        System.out.println(k);
//				for(int i=0;i<listDetail.size();i++){
//					System.out.println(listDetail.get(i).get("purchaseListUnique"));
//					System.out.println(listDetail.get(i).get("purchaseListParunique"));
//					System.out.println(listDetail.get(i).get("goodsName"));
//					System.out.println(listDetail.get(i).get("goodsBarcode"));
//					System.out.println(listDetail.get(i).get("goodsSalePrice"));
//					System.out.println(listDetail.get(i).get("purCount"));
//					System.out.println(listDetail.get(i).get("goodsPrice"));
//					System.out.println(listDetail.get(i).get("supplierUnique"));
//					System.out.println(">>>>>>>>>>>>>>>>>>>");
//				}

        k = goodsDao.addNewAutoPurDetail(listDetail);
        return sr;
    }

    /**
     * 查询商品今日销售统计总页数
     *
     * @param map
     * @return
     */
    public ShopsResult queryPagesForRanking(Map<String, Object> map) {
        ShopsResult sr = new ShopsResult();
        Integer pageCount = goodsDao.queryPagesForRanking(map);
        if (0 == pageCount) {
            sr.setStatus(2);
            sr.setMsg("该时间段内没有销售商品");
            return sr;
        }
        sr.setStatus(1);
        sr.setMsg("查询成功！");
        sr.setData(pageCount);
        return sr;
    }

    /**
     * 商品日销量：
     *
     * @param map
     * @return
     */
    public ShopsResult queryGoodsRanking(Map<String, Object> map) {
        ShopsResult sr = new ShopsResult();
        System.out.println(map);
        List<Map<String, Object>> data = goodsDao.queryGoodsRanking(map);
        if (null == data || data.isEmpty()) {
            sr.setStatus(2);
            sr.setMsg("没有满足条件的信息！");
            return sr;
        }
        sr.setStatus(1);
        sr.setMsg("查询成功！");
        sr.setData(data);
        return sr;
    }

    /**
     * 商品日销量界面：商品销售详情
     *
     * @param map
     * @return
     */
    public ShopsResult queryRankingDetail(Map<String, Object> map) {
        ShopsResult sr = new ShopsResult();
        List<Map<String, Object>> data = goodsDao.queryRankingDetail(map);
        if (null == data || data.isEmpty()) {
            sr.setStatus(2);
            sr.setMsg("没有满足条件的信息！");
            return sr;
        }
        sr.setStatus(1);
        sr.setMsg("查询成功！");
        sr.setData(data);
        return sr;
    }

    /**
     * 出入库记录：页数查询
     *
     * @param map
     * @return
     */
    public ShopsResult queryStockRecordPages(Map<String, Object> map) {
        ShopsResult sr = new ShopsResult();
        Integer data = goodsDao.queryStockRecordPages(map);
        sr.setData(data);
        sr.setStatus(1);
        sr.setMsg("查询成功！");
        return sr;
    }

    /**
     * 商品出入库记录查询
     *
     * @param map
     * @return
     */
    public PurResult queryGoodsRecordByPage(Map<String, Object> map) {
        PurResult sr = new PurResult();
        List<Map<String, Object>> data = goodsDao.queryGoodsRecordByPage(map);
        Integer count = goodsDao.queryStockRecordPages(map);
        if (null == data || data.isEmpty()) {
            sr.setStatus(0);
            sr.setMsg("没有满足条件的信息！");
            return sr;
        }
        sr.setStatus(1);
        sr.setMsg("查询成功！");
        sr.setData(data);
        sr.setCount(count);
        return sr;
    }

    /**
     * 商品出入库记录查询
     *
     * @param map
     * @return
     */
    public List<GoodsInfo> ExcelGoodsRecord(Map<String, Object> map) {
        List<GoodsInfo> data = goodsDao.ExcelGoodsRecord(map);
        return data;
    }

    public GoodsInfo ExcelGoodsRecordCount(Map<String, Object> map) {
        GoodsInfo data = goodsDao.ExcelGoodsRecordCount(map);
        return data;
    }

    public List<GoodsInfo> ExcelGoodsAlloration(Map<String, Object> map) {
        List<GoodsInfo> data = goodsDao.ExcelGoodsAlloration(map);
        return data;
    }

    /**
     * 商品出入库记录查询
     *
     * @param map
     * @return
     */
    public List<GoodsInfo> ExcelGoodsInfo(Map<String, Object> map) {
        List<GoodsInfo> data = goodsDao.ExcelGoodsInfo(map);
        return data;
    }

    /**
     * 店铺商品进货量查询
     *
     * @param map
     * @return
     */
    public ShopsResult goodsCostQuery(Map<String, Object> map) {
        ShopsResult sr = new ShopsResult();
        List<Map<String, Object>> data = goodsDao.goodsCostQuery(map);
        if (null == data || data.isEmpty()) {
            sr.setStatus(2);
            sr.setMsg("没有满足条件的商品信息！");
            return sr;
        }
        sr.setStatus(1);
        sr.setMsg("查询成功！");
        sr.setData(data);
        return sr;
    }

    /**
     * 删除店铺的重复商品并保留最大ID的商品信息
     *
     * @param map
     * @return
     */
    @Transactional
    public ShopsResult deleteSameGoods(String shopUnique) {
        ShopsResult sr = new ShopsResult();
        List<Map<String, Object>> list = goodsDao.querySameGoodsMessage(shopUnique);
        if (null == list || list.isEmpty()) {
            sr.setStatus(2);
            sr.setMsg("没有满足条件的商品信息");
            return sr;
        }
        for (int i = 0; i < list.size(); i++) {
            System.out.println(list.get(i));
        }
        int k = 0;
        Map<String, Object> map = new HashMap<String, Object>();
        map.put("shopUnique", shopUnique);
        map.put("list", list);
        k = goodsDao.deleteSameGoods(map);
        sr.setStatus(1);
        sr.setMsg("查询成功！");
        sr.setData(k);
        return sr;
    }

    public ShopsResult queryGoodsSaleMessagePages(Map<String, Object> map) {
        ShopsResult sr = new ShopsResult();
        System.out.println(map);
        Integer data = goodsDao.queryGoodsSaleMessagePages(map);

        sr.setData(data);
        sr.setStatus(1);
        sr.setMsg("查询成功！");
        return sr;
    }

    /**
     * 商品销量排行界面；分页查询
     *
     * @param map
     * @return
     */
    public ShopsResult queryGoodsByPage(Map<String, Object> map) {
        ShopsResult sr = new ShopsResult();
        List<Map<String, Object>> data = goodsDao.queryGoodsByPage(map);
        sr.setStatus(1);
        sr.setMsg("查询成功！");
        sr.setData(data);
        return sr;
    }

    /**
     * 商品横向比较页面：页数查询
     *
     * @param map
     * @return
     */
    public ShopsResult queryGoodsLateralSalePages(Map<String, Object> map) {
        ShopsResult sr = new ShopsResult();
        Integer data = goodsDao.queryGoodsLateralSalePages(map);

        sr.setStatus(1);
        sr.setMsg("查询成功！");
        sr.setData(data);
        return sr;
    }


    /**
     * 分页查询店铺某商品销售信息
     *
     * @param map
     * @return
     */
    public ShopsResult queryGoodsLateralSaleByPage(Map<String, Object> map) {
        ShopsResult sr = new ShopsResult();
        List<Map<String, Object>> data = goodsDao.queryGoodsLateralSaleByPage(map);
        sr.setStatus(1);
        sr.setMsg("查询成功！");
        sr.setData(data);
        return sr;
    }


    public ShopsResult queryGoodsProtraitPages(Map<String, Object> map) {
        ShopsResult sr = new ShopsResult();

        Integer data = goodsDao.queryGoodsProtraitPages(map);
        sr.setStatus(1);
        sr.setMsg("查询成功！");
        sr.setData(data);
        return sr;
    }

    public ShopsResult queryGoodsProtraitByPage(Map<String, Object> map) {
        System.out.println("商品销量纵向比较：：：" + map);
        ShopsResult sr = new ShopsResult();

        List<Map<String, Object>> data = goodsDao.queryGoodsProtraitByPage(map);
        sr.setStatus(1);
        sr.setMsg("查询成功！");
        sr.setData(data);
        return sr;
    }

    /**
     * 输入条码后，获取云库商品或本店商品
     *
     * @param map
     * @return
     */
    public PurResult getCloudMessage(Map<String, Object> map) {
        PurResult pr = new PurResult();
        CloudGoodsMain data = goodsDao.getCloudMessage(map);
        if (null == data || data.getForeignKey() == null) {
            pr.setStatus(2);
            pr.setMsg("没有满足条件的商品信息！");
            return pr;
        }
        //若商品分类信息为自定义分类,则无需修改商品分类,若为系统分类信息,则检查信息
        if (data.getKindT() == 2) {//若为dict表的分类,先检测是否店铺分类信息,若为系统,则不操作,否则,更新为默认自定义
            Integer k = impDao.queryShopKindType(map.get("shop_unique").toString());
            if (k == 1) {
                data.setKindName("条码");
                data.setKindUnique(Load.DEFAULTSELFKINDUNIQUE);
                data.setGroupName("条码");
                data.setGroupUnique("98000");
            }
        }

        //获取该商品自动采购设置信息
        map.put("goods_barcode", data.getForeignKey());
        List<Map<String, Object>> auto = goodsDao.getGoodsAutoPurchase(map);
        for (int i = 0; i < auto.size(); i++) {
            Map<String, Object> params = new HashMap<String, Object>();
            params.put("company_code", auto.get(i).get("company_code"));
            params.put("goods_barcode", auto.get(i).get("sup_goods_barcode"));
            Map<String, Object> goodsInfo = supplierInfoDao.getGoodsInfo(params);
            auto.get(i).put("wholesale_price", goodsInfo.get("wholesale_price"));
        }
        data.setAuto(auto);

        pr.setStatus(1);
        pr.setMsg("查询成功！");
        pr.setData(data);
        return pr;
    }

    /**
     * 将商品信息保存，并更新云库商品
     * 此处为保证所有商品的foreignKey一致，将所有同条码的商品更新为相同
     * 此处先更新本店铺的商品信息，若更新不成功，则将新商品添加，否则仅更新
     *
     * @param map
     * @return
     */
    @Transactional
    public PurResult saveGoodsMessage(String operateType, String shop_unique, String goodsBrand, String kindUnique, String goodsMessage, String goodsRemarks, String foreignKey,
                                      Integer goodsGround, HttpServletRequest request, String ip, String userAgent, String goods_barcode,
                                      String outStockCount, String autoGoodsJson, String goodsDelete, String staff_id, Integer sameType, Integer goodsChengType, String defaultSupplierUnique
            , String manager_unique) {
        SFTPUtil sftp = new SFTPUtil(FTPConfig.username, FTPConfig.password, FTPConfig.host, FTPConfig.port);
        sftp.login();

		List<RecordGoods> recordGoodsList = new ArrayList<>();

        //已删除的规格信息
        String[] barDel = null;
		if(null != goodsDelete && !goodsDelete.equals("")){
			barDel = goodsDelete.split(",");
		}else {
			barDel = new String[0];
		}
        List<String> barDell = new ArrayList<>();
        PurResult pr = new PurResult();
        Map<String, Object> pmap = new HashMap<String, Object>();//pmap存储基本信息
        Map<String, Object> fmap = new HashMap<String, Object>();//存放用于更新foreignKey的商品信息，仅商品条码和foreignKey
        pmap.put("shop_unique", shop_unique);
        pmap.put("goods_brand", goodsBrand);
        pmap.put("goods_kind_unique", kindUnique);
        pmap.put("goods_remarks", goodsRemarks);
        pmap.put("goodsGround", goodsGround);
        pmap.put("sameType", sameType);
        pmap.put("goodsChengType", goodsChengType);
        pmap.put("defaultSupplierUnique", defaultSupplierUnique);
        if (null == foreignKey || foreignKey == "") {
            foreignKey = goodsMessage.split(";")[0].split("@")[0];
        }
        pmap.put("foreignKey", foreignKey);//若未上传关联外键，则以最小单位的商品信息作为包装外键
        fmap.put("foreignKey", foreignKey);
        String[] goodsMessages = goodsMessage.split(";");
        //若已删除的规格信息里有需要新增的规格信息，移除
        for (String s : barDel) {
            boolean flag = false;
            for (String gs : goodsMessages) {
                if (s.equals(gs) && !s.equals("")) {
                    flag = true;
                    break;
                }
            }
            if (flag) {
                barDell.add(s);
            }
        }
        //将选择的商品删除
        if (barDell != null && barDell.size() > 0) {
            String[] dbs = new String[barDell.size()];
            for (int i = 0; i < barDell.size(); i++) {
                dbs[i] = barDell.get(i);
            }

            Map<String, Object> gdm = new HashMap<String, Object>();
            gdm.put("shopUnique", shop_unique);
            gdm.put("barcodes", dbs);
            goodsDao.deleteGoodsList(gdm);

            //添加商品删除记录
            goodsDao.addNewGoodsDelete(gdm);
        }
        // 最小包装规格的商品数量
        BigDecimal goodsCountOnMinGoodsContain = BigDecimal.ZERO;
        //将需要移除的
        String[] barcodes = new String[goodsMessages.length];
        List<Map<String, Object>> returnList = new ArrayList<>();
		List<Map<String, Object>> stockList = new ArrayList<>();
		Integer stockType = 1;
		Integer goodsInPriceType = 0;
		Integer auditStatus = 0;
		ShopsConfig shopsConfig = shopsConfigDao.selectByShopUnique(shop_unique);
		if (ObjectUtil.isNotNull(shopsConfig)) {
			if (ObjectUtil.isNotNull(shopsConfig.getGoodsInPriceType())) {
				goodsInPriceType = shopsConfig.getGoodsInPriceType();
			}
			if (ObjectUtil.isNotNull(shopsConfig.getIsIoboundInspect()) && ObjectUtil.equals(Integer.valueOf(0), shopsConfig.getIsIoboundInspect())) {
				auditStatus = 1;
			}
		}
        for (int i = 0; i < goodsMessages.length; i++) {
            Map<String, Object> map = new HashMap<String, Object>();//存放更新商品所需要的信息
            map.putAll(pmap);
            String[] message = StrUtil.splitToArray(goodsMessages[i], "@");
            if (i == 0){
                goodsCountOnMinGoodsContain = new BigDecimal(message[11]);
            }
			Map<String, Object> goodsParams = new HashMap<>();
			goodsParams.put("shop_unique", shop_unique);
			goodsParams.put("goods_barcode", message[0]);
			RecordGoods sourceGoods = goodsDao.selectSourceGoods(goodsParams);
            // 当前商品包装规格
            BigDecimal goodsContain = new BigDecimal(String.valueOf(message[4]));
            BigDecimal goodsCount = goodsCountOnMinGoodsContain.divide(goodsContain,RoundingMode.DOWN);
            barcodes[i] = message[0];
            map.put("goods_barcode", message[0]);
            map.put("goods_name", message[1]);
            map.put("goods_alias", ChineseCharToEn.getAllFirstLetter(message[1]));
            map.put("goods_in_price", message[2]);
            map.put("goods_sale_price", message[3]);
            map.put("goods_contain", message[4]);
            map.put("foreignKey", foreignKey);
            map.put("goods_unit", message[5]);
            map.put("order_sort", message[7]);
            String update_time = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
            map.put("update_time", update_time);
            map.put("goodsCusPrice", message[8]);
            map.put("goods_standard", message[9]);
            map.put("goods_web_sale_price", message[10]);
            map.put("goods_count", goodsCount.doubleValue());
            map.put("min_sale_count", message[13]);


			/**
			 * 此处需要添加
			 * 1、商品上下级状态
			 * 2、商品foreignKey
			 * 3、goodStockPrice（这个真的有吗）
			 * 4、goods_id，
			 * 5、goods_cus_price，会员价，没有就不更新前端不就好了吗
			 * 6、supplierUnique，供货商信息
			 */
			map.put("goods_cus_price", map.get("goodsCusPrice"));
			map.put("supplierUnique", defaultSupplierUnique);
			map.put("goodStockPrice", message[3]);

            Double goods_count = Double.valueOf(message[12]) - Double.valueOf(message[11]);
            Double stock_price = Double.valueOf(message[2]);
            if (Double.valueOf(message[11]) < Double.valueOf(message[12])) {
				stockType = 2;//出库
                stock_price = Double.valueOf(message[3]);
            } else {
                goods_count = -goods_count;
            }
			Integer goodsLife = null;
			if (message.length >13 && StrUtil.isNotBlank(message[14])) {
				goodsLife = Integer.parseInt(message[14]);
			}
			map.put("goods_life", goodsLife);
            if (i == 0 && goods_count != 0.00) {
				//添加库存记录
				Map<String, Object> params = new HashMap<String, Object>();
				String goodsProd = null;
				if (message.length > 14 ) {
					goodsProd = message[15];
				}
				Date prod = null;
				Date exp = null;
				if (null != goodsLife && null != goodsProd) {
					try {
						prod = DateUtil.parseDate(goodsProd);
					} catch (Exception e) {
						System.err.println("格式化生产日期[" + goodsProd + "]失败, 需要格式为：yyyy-MM-dd");
					}
					exp = DateUtil.offsetDay(prod, goodsLife);
				}
				params.put("goodsLife", goodsLife);
				params.put("goodsProd", prod);
				params.put("goodsExp", exp);
                params.put("goodsBarcode", message[0]);
                params.put("goodsCount", goods_count);
                params.put("stockType", stockType);
                params.put("stockCount", message[11]);
                params.put("shopUnique", shop_unique);
                params.put("stockPrice", stock_price);
                params.put("staffId", staff_id);
				stockList.add(params);
            }

            if (message[4].equals("1")) {
                map.put("out_stock_count", outStockCount);
            }

            MultipartFile file = null;
            file = ShopsUtil.testMulRequest(request, "goodsPicturePath" + (i + 1));
            if (file != null) {
                String orName = file.getOriginalFilename();//获取文件原名称
                String lastName = orName.substring(orName.lastIndexOf("."));
                String newName = message[0] + Math.round(Math.random() * 100) + lastName;
                String filePathDetail = File.separator + "image" + File.separator + shop_unique;
                //存到文件服务器-start

                try {
                    byte[] bytes = ImageZipUtils.file2byte(ImageZipUtils.MultipartFileToFile(file));
                    byte[] bimg = ImageZipUtils.compressPicForScale(bytes, 100, 1, true, 800, 800);
                    sftp.upload(FTPConfig.goods_path + "/" + shop_unique, newName, ImageZipUtils.byte2Input(bimg));
                    byte[] smmg = ImageZipUtils.compressPicForScale(bytes, 50, 1, true, 345, 345);

                    sftp.upload(FTPConfig.goods_middle_path + "/" + shop_unique, newName, ImageZipUtils.byte2Input(smmg));
                    sftp.upload(FTPConfig.goods_small_path + "/" + shop_unique, newName, ImageZipUtils.byte2Input(smmg));
                    String fName = filePathDetail + File.separator + newName;
                    //小程序上下架那边会在url中传递图片，如果是\的话tomcat会报错(The valid characters are defined in RFC 7230 and RFC 3986问题)，将\改为/
                    map.put("goods_picturepath", fName.replaceAll("\\\\", "/"));
                } catch (SftpException e) {
                    e.printStackTrace();
                }

            } else if (!"!]|".equals(message[6])) {
                map.put("goods_picturepath", message[6].startsWith("/") ? message[6] : ("/" + message[6]));
            }
            //更新本店商品信息
//			System.out.println("data="+map);
            int k = goodsDao.updateGoodsMessage(map);

			//查询当前商品的id和foreignKey
			Map<String,Object> baseGoodsMap = goodsDao.selectBaseGoodsMsg(goodsParams);
			System.out.println("商品基本信息" + baseGoodsMap);
			if (ObjectUtil.isNotNull(baseGoodsMap)) {
				map.putAll(baseGoodsMap);
			}


//			System.out.println("更新本店商品："+map);
            if (k == 0) {//更新失败，本店不包含此商品，则添加新的商品信息
				if (ObjectUtil.equals(Integer.valueOf(0), auditStatus)) {
					map.put("goods_count", 0);
				}
                goodsDao.addNewGoods(map);
                map.put("recordType", Load.ADDNEWGOODSRECORD);
            } else {
                map.put("recordType", Load.MODIFYGOODSRECORD);
            }
            goodsDao.updateGoodsContain(map);

            //检测商品的最小起定量信息
            setGoodsOnlineSetting(map);

            map.put("ip", ip);
//			System.out.println(ip);
            map.put("userAgent", userAgent);
//			goodsDao.addNewGoodsRecord(map);
            //将信息更新到大库中
            k = dictDao.updateGoodsDictMsg(map);
            int daku = dictDao.queryGoodsDictCunZai(map);
            if (k == 0 && daku == 0) {
                dictDao.addNewGoodsDict(map);
            }
            if (i > 10) {
                break;
            }

			RecordGoods resultGoods = goodsDao.selectSourceGoods(goodsParams);
			try{
				//修改前的商品信息
				//操作信息
				RecordGoodsOper recordGoodsOper = new RecordGoodsOper();
				if(null != sourceGoods){
					recordGoodsOper.setGoodsId(Long.valueOf(sourceGoods.getGoodsId()));
					recordGoodsOper.setGoodsBarcode(sourceGoods.getGoodsBarcode());
					recordGoodsOper.setShopUnique(sourceGoods.getShopUnique());
				}
				recordGoodsOper.setOperType(OperTypeEnum.OPER_TYPE_UPDATE.getValue());
				recordGoodsOper.setDeviceSource(DeviceSourceEnum.PC_WEB.getValue());
				recordGoodsOper.setUserType(UserTypeEnum.SHOP_STAFF.getValue());
				recordGoodsOper.setCreateTtime(DateUtil.formatDateTime(DateUtil.date()));
				recordGoodsOper.setOperSource(OperSourceEnum.GOODS_CHANGE.getValue());
				recordGoodsOper.setDeviceSourceMsg(userAgent);
				if (StrUtil.isNotBlank(staff_id)) {
					recordGoodsOper.setUserId(staff_id);
				}
				RecordGoodsOperParams recordGoodsOperParams = new RecordGoodsOperParams();
				recordGoodsOperParams.setSourceGoods(sourceGoods);
				recordGoodsOperParams.setRecordGoodsOper(recordGoodsOper);
				recordGoodsOperParams.setResultGoods(resultGoods);
				String host = UtilForJAVA.getHostByServerName(request.getServerName());
				String url = StringUtils.join(host,"/shopmanager/record/recordGoodsOper.do");
				String json = com.alibaba.fastjson.JSONObject.toJSONString(recordGoodsOperParams);
				System.out.println("-------------------同步商品操作记录，url:"+ url);
				System.out.println("-------------------同步商品操作记录，参数:"+ json);
				String result = HttpUtil.post(url, json);
				System.out.println("-------------------同步商品操作记录，返回值:"+ result);
			} catch (Exception e){
				e.printStackTrace();
			}
            returnList.add(map);
        }
		// 处理库存
		if (StrUtil.equals("add", operateType) && stockList.size() > 0) {
			GoodsInventoryParam inventoryParam = new GoodsInventoryParam();
			inventoryParam.setShopUnique(Long.parseLong(shop_unique));
			inventoryParam.setStaffId(Long.parseLong(staff_id));
			inventoryParam.setStockListUnique(UniqueUtils.createListUnique());
			inventoryParam.setGoodsInPriceType(goodsInPriceType);
			// 处理库存
			Map<String, Object> stockDetail = new HashMap<>();
			stockDetail.put("list_unique", inventoryParam.getStockListUnique());
			stockDetail.put("source_unique", StrUtil.EMPTY);
			stockDetail.put("stock_kind", StockKindEnum.INIT.getValue());
			stockDetail.put("audit_status", auditStatus);
			stockDetail.put("shop_unique", shop_unique);
			stockDetail.put("stock_type", stockType);
			stockDetail.put("stock_time", DateUtil.date());
			stockDetail.put("stock_resource", StockReourceEnum.BY_HAND.getValue());
			stockDetail.put("stock_origin", StockOriginEnum.WEB.getValue());
			stockDetail.put("staff_id", staff_id);

			List<GoodsInventoryDetailParam> detailParamsList = new ArrayList<>();
			final BigDecimal[] totalPrice = {BigDecimal.ZERO};
			final BigDecimal[] totalCount = {BigDecimal.ZERO};
			stockList.stream().forEach(m -> {
				m.put("stockResource", StockReourceEnum.BY_HAND.getValue());
				m.put("listUnique", inventoryParam.getStockListUnique());
				m.put("stockOrigin", StockOriginEnum.WEB.getValue());
				GoodsInventoryDetailParam detailParam = new GoodsInventoryDetailParam();
				detailParam.setGoodsBarcode((String) m.get("goodsBarcode"));
				detailParam.setGoodsProd((Date) m.get("goodsProd"));
				detailParam.setGoodsExp((Date) m.get("goodsExp"));
				detailParam.setGoodsLife((Integer) m.get("goodsLife"));
				detailParam.setGoodsInCount(new BigDecimal(m.getOrDefault("goodsCount", "0").toString()));
				detailParam.setGoodsCount(detailParam.getGoodsInCount());
				detailParam.setGoodsInPrice(new BigDecimal(m.getOrDefault("stockPrice", "0").toString()));
				detailParamsList.add(detailParam);
				totalCount[0] = NumberUtil.add(totalCount[0], detailParam.getGoodsInCount());
				totalPrice[0] = NumberUtil.add(totalPrice[0], NumberUtil.mul(detailParam.getGoodsInCount(), detailParam.getGoodsInPrice()));
			});
			stockDetail.put("total_count", totalCount[0]);
			stockDetail.put("total_amount", totalPrice[0]);
			stockDetail.put("update_id", staff_id);
			stockDetail.put("update_time", DateUtil.date());
			stockDao.addIntoStockDetail(stockDetail);
			inventoryParam.setGoodsList(detailParamsList);
			stockDao.newStockRecords(stockList);
			//不需要审核的话，处理批次库存
			if (ObjectUtil.equals(Integer.valueOf(1), auditStatus)) {
				inventoryManagerService.dealGoodsInventory(inventoryParam);
			}
		}

        //将本店需要单飞的商品单飞
        for (String barcode : barDel) {
            Map<String, Object> oneMap = new HashMap<String, Object>();
            oneMap.put("shop_unique", shop_unique);
            oneMap.put("goods_barcode", barcode);
            oneMap.put("goods_contain", 1);
            oneMap.put("foreign_key", barcode);

            goodsDao.updateGoodsMessage(oneMap);
        }
        fmap.put("barcodes", barcodes);
        //mqtt 同步商品
        try {
            RedisCache rc = new RedisCache("");
            Object mac = rc.getObject("topic_" + shop_unique);
            if (mac != null) {
                @SuppressWarnings("unchecked")
                List<String> macIdList = (List<String>) mac;
                //2 MQTT 发送消息
                for (String macid : macIdList) {

                    Map<String, Object> data = new HashMap<String, Object>();
                    data.put("ctrl", "msg_goods_update");
                    data.put("ID", macid);
                    data.put("status", 200);
                    data.put("data", returnList);
                    data.put("count", 1);
                    MqttxUtil.sendMapMsg(data, macid);
                }

            }
            //清除缓存
            String rcId = "pcGoods" + shop_unique + 1 + 3 + null;
            Object res = rc.getObject(rcId);
            if (null != res) {
                rc.removeObject(rcId);
            }
            String rcId2 = "pcGoods" + shop_unique + 0 + 3 + null;
            Object res2 = rc.getObject(rcId2);
            if (null != res2) {
                rc.removeObject(rcId2);
            }


        } catch (Exception e) {
            e.printStackTrace();
        }

        if (autoGoodsJson != null && !autoGoodsJson.equals("")) {
            //删除商品自动补货设置
            Map<String, Object> paramsDelete = new HashMap<String, Object>();
            paramsDelete.put("shop_unique", shop_unique);
            paramsDelete.put("goods_barcode", goods_barcode);
            goodsDao.deleteGoodsAuto(paramsDelete);
            //添加商品自动补货信息
            addGgoodsAutoPurchase(autoGoodsJson);
        }
        //商品资质信息
        Map<String, Object> zizhi = new HashMap<String, Object>();
        zizhi.put("shop_unique", shop_unique);
        zizhi.put("goods_barcode", goods_barcode);
        MultipartFile fileA = null;
        MultipartFile fileB = null;
        MultipartFile fileC = null;
        fileA = ShopsUtil.testMulRequest(request, "goodsPicturePathA");
        fileB = ShopsUtil.testMulRequest(request, "goodsPicturePathB");
        fileC = ShopsUtil.testMulRequest(request, "goodsPicturePathC");
        if (fileA != null) {
            String orName = fileA.getOriginalFilename();//获取文件原名称
            String lastName = orName.substring(orName.lastIndexOf("."));
            String newName = goods_barcode + Math.round(Math.random() * 100) + lastName;
            String filePathDetail = File.separator + "image" + File.separator + shop_unique;

            try {
                byte[] bytes = ImageZipUtils.file2byte(ImageZipUtils.MultipartFileToFile(fileA));
                byte[] bimg = ImageZipUtils.compressPicForScale(bytes, 100, 1, true, 800, 800);
                sftp.upload(FTPConfig.goods_path + "/" + shop_unique, newName, ImageZipUtils.byte2Input(bimg));
                zizhi.put("goodsPicturePathA", filePathDetail + File.separator + newName);
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
        if (fileB != null) {
            String orName = fileB.getOriginalFilename();//获取文件原名称
            String lastName = orName.substring(orName.lastIndexOf("."));
            String newName = goods_barcode + Math.round(Math.random() * 100) + lastName;
            String filePathDetail = File.separator + "image" + File.separator + shop_unique;

            try {
                byte[] bytes = ImageZipUtils.file2byte(ImageZipUtils.MultipartFileToFile(fileB));
                byte[] bimg = ImageZipUtils.compressPicForScale(bytes, 100, 1, true, 800, 800);
                sftp.upload(FTPConfig.goods_path + "/" + shop_unique, newName, ImageZipUtils.byte2Input(bimg));
                zizhi.put("goodsPicturePathB", filePathDetail + File.separator + newName);
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
        if (fileC != null) {
            String orName = fileC.getOriginalFilename();//获取文件原名称
            String lastName = orName.substring(orName.lastIndexOf("."));
            String newName = goods_barcode + Math.round(Math.random() * 10000) + lastName;
            String filePathDetail = File.separator + "image" + File.separator + shop_unique;

            try {
                byte[] bytes = ImageZipUtils.file2byte(ImageZipUtils.MultipartFileToFile(fileC));
                byte[] bimg = ImageZipUtils.compressPicForScale(bytes, 100, 1, true, 800, 800);
                sftp.upload(FTPConfig.goods_path + "/" + shop_unique, newName, ImageZipUtils.byte2Input(bimg));
                zizhi.put("goodsPicturePathC", filePathDetail + File.separator + newName);
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
        if (zizhi.containsKey("goodsPicturePathA") || zizhi.containsKey("goodsPicturePathB") || zizhi.containsKey("goodsPicturePathC")) {

            int num = goodsDao.quertGgoodsCertif(zizhi);
            //添加商品资质信息
            if (num > 0) {
                goodsDao.delGgoodsCertif(zizhi);
            } else {
                goodsDao.addGgoodsCertif(zizhi);
            }

        }

        pr.setStatus(1);
        pr.setMsg("保存成功！");
        return pr;
    }

    /**
     * 将商品信息保存，并更新云库商品
     * 此处为保证所有商品的foreignKey一致，将所有同条码的商品更新为相同
     * 此处先更新本店铺的商品信息，若更新不成功，则将新商品添加，否则仅更新
     *
     * @param map
     * @return
     */
    @Transactional
    public PurResult saveGoodsMessage_wj(String shop_unique, String goodsBrand, String kindUnique, String goodsMessage, String goodsRemarks, String foreignKey,
                                         Integer goodsGround, HttpServletRequest request, String ip, String userAgent, String goods_barcode,
                                         String outStockCount, String autoGoodsJson, String goodsDelete, String staff_id, Integer sameType, Integer goodsChengType, String defaultSupplierUnique
            , String manager_unique) {
        //已删除的规格信息
        String[] barDel = goodsDelete.split(",");
        List<String> barDell = new ArrayList<>();
        PurResult pr = new PurResult();
        Map<String, Object> pmap = new HashMap<String, Object>();//pmap存储基本信息
        Map<String, Object> fmap = new HashMap<String, Object>();//存放用于更新foreignKey的商品信息，仅商品条码和foreignKey
        pmap.put("shop_unique", shop_unique);
        pmap.put("goods_brand", goodsBrand);
        pmap.put("goods_kind_unique", kindUnique);
        pmap.put("goods_remarks", goodsRemarks);
        pmap.put("goodsGround", goodsGround);
        pmap.put("sameType", sameType);
        pmap.put("goodsChengType", goodsChengType);
        pmap.put("defaultSupplierUnique", defaultSupplierUnique);
        if (null == foreignKey || foreignKey == "") {
            foreignKey = goodsMessage.split(";")[0].split(":")[0];
        }
        pmap.put("foreignKey", foreignKey);//若未上传关联外键，则以最小单位的商品信息作为包装外键
        fmap.put("foreignKey", foreignKey);
        String[] goodsMessages = goodsMessage.split(";");
        //若已删除的规格信息里有需要新增的规格信息，移除
        for (String s : barDel) {
            boolean flag = false;
            for (String gs : goodsMessages) {
                if (s.equals(gs)) {
                    flag = true;
                    break;
                }
            }
            if (!flag) {
                barDell.add(s);
            }
        }
        //将选择的商品删除
        if (barDell != null && barDell.size() > 0) {
            String[] dbs = new String[barDell.size()];
            for (int i = 0; i < barDell.size(); i++) {
                dbs[i] = barDell.get(i);
            }

            Map<String, Object> gdm = new HashMap<String, Object>();
            gdm.put("shopUnique", shop_unique);
            gdm.put("barcodes", dbs);
            goodsDao.deleteGoodsList(gdm);

            //添加商品删除记录
            goodsDao.addNewGoodsDelete(gdm);
        }

        //将需要移除的
        String[] barcodes = new String[goodsMessages.length];
        for (int i = 0; i < goodsMessages.length; i++) {
            Map<String, Object> map = new HashMap<String, Object>();//存放更新商品所需要的信息
            map.putAll(pmap);
            String[] message = goodsMessages[i].split(":");
            barcodes[i] = message[0];
            map.put("goods_barcode", message[0]);
            map.put("goods_name", message[1]);
            map.put("goods_alias", ChineseCharToEn.getAllFirstLetter(message[1]));
            map.put("goods_in_price", message[2]);
            map.put("goods_sale_price", message[3]);
            map.put("goods_contain", message[4]);
            map.put("foreignKey", foreignKey);
            map.put("goods_unit", message[5]);
            map.put("order_sort", message[7]);
            map.put("goodsCusPrice", message[8]);
            map.put("goods_standard", message[9]);
            map.put("goods_web_sale_price", message[10]);
            map.put("goods_count", message[11]);
            map.put("min_sale_count", message[13]);
            Double goods_count = Double.valueOf(message[12]) - Double.valueOf(message[11]);
            String stock_type = "1";//入库
            Double stock_price = Double.valueOf(message[2]);
            if (Double.valueOf(message[11]) < Double.valueOf(message[12])) {
                stock_type = "2";//出库
                stock_price = Double.valueOf(message[3]);
            } else {
                goods_count = -goods_count;
            }
            if (goods_count != 0.00) {
                //添加库存记录
                Map<String, Object> params = new HashMap<String, Object>();
                params.put("goods_barcode", message[0]);
                params.put("goods_count", goods_count);
                params.put("stock_type", stock_type);
                params.put("stock_count", message[11]);
                params.put("shop_unique", shop_unique);
                params.put("stock_price", stock_price);
                params.put("staff_id", staff_id);
                goodsDao.addShopStock(params);
            }

            if (message[4].equals("1")) {
                map.put("out_stock_count", outStockCount);
            }

            String goodsPicturePath = message[6];
            if (goodsPicturePath != null && goodsPicturePath != "" && !goodsPicturePath.equals("")) {
                UUID uuid = UUID.randomUUID();
                String newName = uuid.toString() + ".png";
                ;
                String filePathDetail = File.separator + "image" + File.separator + shop_unique;
                String filePath = this.getClass().getClassLoader().getResource("../../").getPath();
                filePath = filePath.substring(0, filePath.length() - request.getContextPath().length()) + filePathDetail;
                boolean flag = ShopsUtil.GenerateImage(goodsPicturePath, filePath, newName);

                if (flag) {
                    map.put("goods_picturepath", filePathDetail + File.separator + newName);
                }
            } else if (!"!]|".equals(message[6])) {
                map.put("goods_picturepath", message[6].startsWith("/") ? message[6] : ("/" + message[6]));
            }
            //更新本店商品信息
//			System.out.println("data="+map);
            int k = goodsDao.updateGoodsMessage(map);

            goodsDao.updateGoodsContain(map);
            //更新商品信息后，添加更新记录

//			System.out.println("更新本店商品："+map);
            if (k == 0) {//更新失败，本店不包含此商品，则添加新的商品信息
                goodsDao.addNewGoods(map);
                map.put("recordType", Load.ADDNEWGOODSRECORD);
            } else {
                map.put("recordType", Load.MODIFYGOODSRECORD);
            }
            
            goodsDao.updateGoodsContain(map);

            //检测商品的最小起定量信息
            setGoodsOnlineSetting(map);

            map.put("ip", ip);
//			System.out.println(ip);
            map.put("userAgent", userAgent);
//			goodsDao.addNewGoodsRecord(map);
            //将信息更新到大库中
            k = dictDao.updateGoodsDictMsg(map);
            int daku = dictDao.queryGoodsDictCunZai(map);
            if (k == 0 && daku == 0) {
                dictDao.addNewGoodsDict(map);
            }
            //查询店铺是否是总店
//			int shop_class=goodsDao.queryShopClass(map);
//			if(shop_class==0){
//				//总店同步所有店铺
//				Map<String,Object> params=new HashMap<String, Object>();
//				params.put("managerUnique", manager_unique);
//				params.put("pageSize", 200);
//				params.put("startNum", (1-1)*200);
//				List<Map<String,Object>> shop_list = staffDao.queryAllShopsByPage(params);
//				Map<String, Object> newMap=new HashMap<>(map);
//				for (Map<String, Object> shop : shop_list) {
//					String shopUnique=shop.get("shopUnique").toString();
//					if(!shop_unique.equals(shopUnique)){
//						newMap.put("shop_unique", shopUnique);
//						newMap.put("goods_count", null);
//						int z=goodsDao.updateGoodsMessage(newMap);
//						if(z==0){//更新失败，本店不包含此商品，则添加新的商品信息
//							System.out.println("新增同步");
//							goodsDao.addNewGoods(newMap);
//						}else{
//							System.out.println("更新同步");
//						}
//					}
//				}
//			}
            if (i > 20) {
                break;
            }
        }
        fmap.put("barcodes", barcodes);

        if (autoGoodsJson != null && !autoGoodsJson.equals("")) {
            //删除商品自动补货设置
            Map<String, Object> paramsDelete = new HashMap<String, Object>();
            paramsDelete.put("shop_unique", shop_unique);
            paramsDelete.put("goods_barcode", goods_barcode);
            goodsDao.deleteGoodsAuto(paramsDelete);
            //添加商品自动补货信息
            addGgoodsAutoPurchase(autoGoodsJson);
        }

        pr.setStatus(1);
        pr.setMsg("保存成功！");
        return pr;
    }

    public void setGoodsOnlineSetting(Map<String, Object> map) {
        //获取对应的最小销售量信息，如果不存在，则添加
        Map<String, Object> onlineMap = goodsDao.queryGoodsOnlineSaleMsg(map);
        System.out.println(onlineMap.get("min_sale_count").toString());
        if (null != onlineMap && (onlineMap.get("min_sale_count") == null || Double.parseDouble(onlineMap.get("min_sale_count").toString()) == -1.0)) {
            if (Integer.parseInt(map.get("min_sale_count").toString()) != 0) {

                //需要添加
                onlineMap.put("min_sale_count", map.get("min_sale_count"));
//			onlineMap.put("increment_count", message[14]);//如果添加每次修改的数量，使用此参数
                goodsDao.addNewGoodsOnlineSetting(onlineMap);
            }

        } else if (null != onlineMap && (onlineMap.get("min_sale_count") != null && Double.parseDouble(onlineMap.get("min_sale_count").toString()) != -1.0)) {
            //判断是否需要更新
            if (null != map.get("min_sale_count") && Integer.parseInt(map.get("min_sale_count").toString()) < 0) {
                onlineMap.put("min_sale_count", 0);
            } else {
                onlineMap.put("min_sale_count", map.get("min_sale_count"));
            }
            goodsDao.modifyGoodsOnlineSetting(onlineMap);
        }
    }

    public PurResult getGoodsBaseMessage(Map<String, Object> map) {
        PurResult pr = new PurResult();
        List<Map<String, Object>> data = goodsDao.getGoodsBaseMessage(map);
        if (null == data || data.isEmpty()) {
            pr.setStatus(2);
            pr.setMsg("没有满足条件的商品信息！");
            return pr;
        }
        pr.setStatus(1);
        pr.setMsg("查询成功");
        pr.setData(data);
        return pr;
    }

    public ShopsResult printAllorationList(Map<String, Object> map) {
        ShopsResult sr = new ShopsResult();
        List<GoodsInfo> data = goodsDao.ExcelGoodsAlloration(map);
        if (null == data || data.isEmpty()) {
            sr.setStatus(2);
            sr.setMsg("没有满足条件的订单信息！");
            return sr;
        }
        sr.setStatus(1);
        sr.setMsg("查询成功！");
        sr.setData(data);
        return sr;
    }

    /**
     * 商品销售统计界面查询
     *
     * @param map
     * @return
     */
    public PurResult queryGoodsSaleStatistics(Map<String, Object> params) {
        PurResult result = new PurResult();

        List<Map<String, Object>> data = goodsDao.queryGoodsSaleStatistics(params);

        Map<String, Object> m = this.goodsSaleStatistics(params);

        result.setCord(m);
        result.setData(data);
//		}
        result.setStatus(1);
        result.setMsg("查询成功");
        params.put("page", 0);
        params.put("limit", 100000);
        result.setCount(goodsDao.queryGoodsSaleStatisticsOrderBySale(params).size());

        return result;
    }

    //比较两个时间是否超过30天
    public boolean isValidDate(String startTime, String endTime) {

        boolean convertSuccess = true;
        //时间格式定义
        SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd");
        String nowDate;
        try {
            nowDate = format.format(format.parse(endTime));

            //获取30天前的时间日期--minDate

            Calendar calc = Calendar.getInstance();

            calc.add(Calendar.DAY_OF_MONTH, -30);

            String minDate = format.format(calc.getTime());

            //设置lenient为false. 否则SimpleDateFormat会比较宽松地验证日期，比如2007/02/29会被接受，并转换成2007/03/01

            format.setLenient(false);

            //获取字符串转换后的时间--strDate

            String strDate = format.format(format.parse(startTime));

            //判断传的STR时间，是否在当前时间之前，且在30天日期之后-----测试的时候打印输出结果

            //System.out.println("nowDate.compareTo(strDate):"+ nowDate.compareTo(strDate));

            //System.out.println("strDate.compareTo(minDate):"+ strDate.compareTo(minDate));

            if (nowDate.compareTo(strDate) >= 0 && strDate.compareTo(minDate) >= 0) {

                convertSuccess = true;

            } else {

                convertSuccess = false;

            }
        } catch (java.text.ParseException e) {
            // TODO Auto-generated catch block
            e.printStackTrace();
        }
        return convertSuccess;

    }

    /**
     * 商品销售明细
     *
     * @param goods_barcode 商品编码
     * @param shop_unique   店铺编码
     * @param startTime     开始时间
     * @param endTime       结束时间
     * @return
     */
    public PurResult queryGoodsSaleDetail(Map<String, Object> params) {
        PurResult result = new PurResult();
        List<Map<String, Object>> list = goodsDao.queryGoodsSaleDetail(params);
        Integer count = goodsDao.queryGoodsSaleDetailCount(params);
        result.setStatus(1);
        result.setMsg("查询成功");
        result.setCount(count);
        result.setData(list);

        return result;
    }

    /**
     * 下载全部销售数据
     *
     * @param map
     * @return
     */
    public List<Map<String, Object>> downloadSaleStatisticsExcel(Map<String, Object> map) {
        List<Map<String, Object>> data = goodsDao.querySaleAll(map);//查询所有数据，速度较慢
        return data;
    }

    /**
     * 查询商品详情
     *
     * @return
     */
    public PurResult queryGoodsStatisticsDetail(Map<String, Object> params) {
        PurResult result = new PurResult();
        List<Map<String, Object>> list = goodsDao.queryGoodsStatisticsDetail(params);
        result.setStatus(1);
        result.setMsg("查询成功");
        result.setCount(goodsDao.queryGoodsStatisticsDetailPageCount(params));
        result.setData(list);
        return result;
    }

    public List<Map<String, Object>> downloadSaleStatisticsDetailExcel(Map<String, Object> map) {
        return goodsDao.queryGoodsStatisticsDetail(map);
    }

    public Map<String, Object> downloadSaleStatisticsCount(Map<String, Object> map) {
        return goodsDao.downloadSaleStatisticsCount(map);
    }

    /**
     * 商品出入库明细界面商品信息查询
     *
     * @param map
     * @return
     */
    public ShopsResult queryGoodsMessageForStatisticsDetail(Map<String, Object> map) {
        ShopsResult sr = new ShopsResult(1, "查询成功");
        List<Map<String, Object>> data = goodsDao.queryGoodsMessageForStatisticsDetail(map);
        sr.setData(data);
        return sr;
    }


    /**
     * 统计销售数据并下载
     *
     * @param map
     * @return
     */
    public List<Map<String, Object>> downloadGoodsSaleStatisticsExcel(Map<String, Object> map) {
        return goodsDao.downloadGoodsSaleStatisticsExcel(map);
    }

    /**
     * 统计销售数据并下载-宁宇-全部店铺
     *
     * @param map
     * @return
     */
    public List<Map<String, Object>> downloadGoodsSaleStatisticsExcel_NYALL(Map<String, Object> map) {
        return goodsDao.downloadGoodsSaleStatisticsExcel_NYALL(map);
    }

    /**
     * 删除商品信息界面，查询分页数量
     *
     * @param map
     * @return
     */
    public ShopsResult toQueryGoodsPage(Map<String, Object> map) {
        ShopsResult sr = new ShopsResult(1, "查询成功！");
        Integer pageCount = goodsDao.toQueryGoodsPage(map);
        sr.setData(pageCount);
        return sr;
    }


    /**
     * 删除商品信息，并修改同步信息至删除
     *
     * @param map
     * @return
     */
    @Transactional
    public ShopsResult deleteGoodsList(Map<String, Object> map, String[] barcodes) {
        ShopsResult sr = new ShopsResult(1, "删除成功！");
        //查询商品详情信息,将其他规格商品同期删除
        for (String bar : barcodes) {
            map.put("goodsBarcode", bar);
            //查询该商品的其他规格
            List<Map<String, Object>> gl = goodsDao.queryGoodsStandardMsg(map);
            if (null != gl && !gl.isEmpty()) {
                //
                String[] bs = new String[gl.size()];
                if (gl.get(0).get("goodsBarcode").toString().equals(bar) && gl.get(0).get("goodsContain").toString().equals("1")) {//若为单位，则全部删除
                    for (int i = 0; i < gl.size(); i++) {
                        bs[i] = gl.get(i).get("goodsBarcode").toString();
                    }
                } else {//删除自身
                    bs[0] = bar;
                    bs = Arrays.copyOf(bs, 1);
                }
                map.put("barcodes", bs);

                Integer k = goodsDao.deleteGoodsList(map);

                //删除对应的油品关系
                for (Integer i = 0; i < bs.length; i++) {
                    Map<String, Object> oilMap = new HashMap<String, Object>();
                    oilMap.put("shopUnique", map.get("shopUnique"));
                    oilMap.put("goodsBarcode", bs[i]);

                    oilDao.deleteOilRelation(oilMap);
                }
                if (k > 0) {
                    goodsDao.addNewGoodsDelete(map);
                }
            }

        }
        return sr;
    }

    /**
     * 删除所有查询到的商品信息
     *
     * @param map
     * @return
     */
    @Transactional
    public ShopsResult deleteGoodsSearch(Map<String, Object> map) {
        ShopsResult sr = new ShopsResult(1, "查询成功！");
        //查询商品信息，
        List<String> list = goodsDao.queryGoodsForDelete(map);
        if (list.size() == 0) {
            sr.setStatus(2);
            sr.setMsg("没有选中的商品信息");
            return sr;
        }
        //删除商品信息
        String[] barcodes = new String[list.size()];
        for (int i = 0; i < list.size(); i++) {
            barcodes[i] = list.get(i).trim();
        }
        map.put("barcodes", barcodes);
        System.out.println(map);
        int k = goodsDao.deleteGoodsList(map);
        //添加商品删除记录
        if (k >= 0) {
            goodsDao.addNewGoodsDelete(map);
        }
        return sr;
    }


    @Override
    public PurResult getAutoPurchase(String shop_unique) {
        PurResult result = new PurResult();
        try {
            Integer auto_purchase = goodsDao.getAutoPurchase(shop_unique);
            result.setData(auto_purchase);
            result.setStatus(1);
            result.setMsg("成功");
        } catch (Exception e) {
            result.setStatus(0);
            result.setMsg("异常");
        }

        return result;
    }

    @Override
    @Transactional
    public PurResult updateAutoPurchase(Map<String, Object> params) {
        PurResult result = new PurResult();
        try {
            goodsDao.updateAutoPurchase(params);

            result.setStatus(1);
            result.setMsg("成功");
        } catch (Exception e) {
            result.setStatus(0);
            result.setMsg("异常");
        }

        return result;
    }

    /**
     * 添加商品自动采购设置
     *
     * @param goods_json[{ shop_unique 店铺唯一标示
     *                     goods_barcode 商品唯一标示
     *                     company_code 供货商code
     *                     company_name 供货商名称
     *                     goods_spec_name 商品规格名称
     *                     compose_specs_id 规格组合id
     *                     goods_count 商品数量
     *                     goodsunit_name 商品单位名称
     *                     sup_goods_id 供货商商品id
     *                     sup_goods_barcode 供货商商品code
     *                     goods_name 商品名称
     *                     wholesale_price 供货商商品售价
     *                     base_barcode 商品基本单位商品编码
     *                     proportion_num 换算比例
     *                     staff_id 创建人
     *                     }]
     * @return
     */
    @Transactional
    public void addGgoodsAutoPurchase(String goods_json) {
        try {
            List<Map<String, Object>> goodsList = strToList(goods_json, HashMap.class);
            for (int i = 0; i < goodsList.size(); i++) {
                String shop_unique = MUtil.strObject(goodsList.get(i).get("shop_unique"));//店铺唯一标示
                String sup_goods_barcode = MUtil.strObject(goodsList.get(i).get("goods_barcode"));//供货商商品唯一标示
                String goods_name = MUtil.strObject(goodsList.get(i).get("goods_name"));//商品名称
                Double wholesale_price = Double.valueOf(MUtil.strObject(goodsList.get(i).get("wholesale_price")));//供货商商品售价
                String base_barcode = MUtil.strObject(goodsList.get(i).get("base_barcode"));//商品基本单位商品编码
                Double proportion_num = Double.parseDouble(MUtil.strObject(goodsList.get(i).get("proportion_num")));//换算比例
                //获取店铺该商品信息
                Map<String, Object> paramsGoods = new HashMap<String, Object>();
                paramsGoods.put("shop_unique", shop_unique);
                paramsGoods.put("goods_barcode", sup_goods_barcode);
                Map<String, Object> goodsInfo = goodsDao.queryGoodsDetail(paramsGoods);
                if (goodsInfo == null) {
                    //添加商品信息
                    paramsGoods.put("goods_name", goods_name);
                    paramsGoods.put("goods_sale_price", -wholesale_price);//负数未定价
                    paramsGoods.put("goods_in_price", wholesale_price);//商家进价
                    paramsGoods.put("foreignKey", base_barcode);
                    paramsGoods.put("goods_contain", proportion_num);
                    paramsGoods.put("goods_kind_unique", goodsDao.getGoodsKindUnique(shop_unique));
                    goodsDao.addNewGoods(paramsGoods);
                }
            }
            goodsDao.addGoodsAutoPurchase(goodsList);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    //字符串转换为List集合
    @SuppressWarnings("unchecked")
    private static List<Map<String, Object>> strToList(String str, @SuppressWarnings("rawtypes") Class<HashMap> clazz) {
        JSONArray json = JSONArray.fromObject(str);
        JSONObject object = null;
        Map<String, Object> t = null;
        List<Map<String, Object>> list = new ArrayList<>();
        for (int i = 0; i < json.size(); i++) {
            object = JSONObject.fromObject(json.get(i));
            t = (Map<String, Object>) JSONObject.toBean(object, clazz);
            list.add(t);
        }
        return list;
    }

    @Override
    public void goodsAutoPurchase() {
        //获取开启自动补货的商家商品列表
        List<Map<String, Object>> goodsList = goodsDao.getAutoPurchaseGoodsList();
        List<Map<String, Object>> autoInfoList = new ArrayList<Map<String, Object>>();
        for (int i = 0; i < goodsList.size(); i++) {
            Map<String, Object> goodsInfo = goodsList.get(i);
            //获取商品自动补货设置
            List<Map<String, Object>> autoInfo = goodsDao.getGoodsAutoPurchase(goodsInfo);
            autoInfoList.addAll(autoInfo);
        }
        //通知集合
        List<Map<String, Object>> shopNoticeList = new ArrayList<Map<String, Object>>();
        //百货豆赠送百分比
        Integer percentage_num = supplierInfoDao.getPercentageNum();

        for (int i = 0; i < autoInfoList.size(); i++) {
            String goods_barcode = MUtil.strObject(autoInfoList.get(i).get("sup_goods_barcode"));//商品条形码
            String compose_specs_id = MUtil.strObject(autoInfoList.get(i).get("compose_specs_id"));//规格组合id
            Integer goods_count = Integer.parseInt(MUtil.strObject(autoInfoList.get(i).get("goods_count")));//商品数量
            String goodsunit_name = MUtil.strObject(autoInfoList.get(i).get("goodsunit_name"));//商品单位名称
            String goods_spec_name = MUtil.strObject(autoInfoList.get(i).get("goods_spec_name"));//商品规则名称
            String company_code = MUtil.strObject(autoInfoList.get(i).get("company_code"));//供货商code
            String shop_unique = MUtil.strObject(autoInfoList.get(i).get("shop_unique"));//店铺唯一标示
            String sup_goods_id = MUtil.strObject(autoInfoList.get(i).get("sup_goods_id"));// 供货商商品id

            //获取该商品规格的总库存
            Map<String, Object> params1 = new HashMap<String, Object>();
            params1.put("goods_code", sup_goods_id);
            params1.put("company_code", company_code);
            if (!compose_specs_id.equals("")) {
                params1.put("compose_specs_id", compose_specs_id);
            }
            Integer totalCount = supplierInfoDao.getTotalCount(params1);
            if (totalCount == null) {
                totalCount = 0;
            }

            //获取店铺信息
            Map<String, Object> paramsShop = new HashMap<String, Object>();
            paramsShop.put("shop_unique", shop_unique);
            Map<String, Object> shopInfo = shopDao.queryShopMessage(paramsShop);

            if (goods_count > totalCount) {
                //当前商品库存不足
                //添加通知
                Map<String, Object> shopNotice = new HashMap<String, Object>();
                String shop_phone = MUtil.strObject(shopInfo.get("shop_phone"));
                shopNotice.put("shop_phone", shop_phone);
                shopNotice.put("shop_unique", shop_unique);
                shopNotice.put("goods_barcode", goods_barcode);
                shopNoticeList.add(shopNotice);
            } else {//创建订单
                //获取供货商商品信息
                Map<String, Object> paramsSupplierGoods = new HashMap<String, Object>();
                paramsSupplierGoods.put("goods_barcode", goods_barcode);
                paramsSupplierGoods.put("company_code", company_code);
                Map<String, Object> supplierGoodsInfo = supplierInfoDao.getGoodsInfo(paramsSupplierGoods);

                String goods_name = MUtil.strObject(supplierGoodsInfo.get("goods_name"));//商品名称
                Double wholesale_price = Double.valueOf(MUtil.strObject(supplierGoodsInfo.get("wholesale_price")));//销售价
                Integer goods_beans_count = Integer.parseInt(MUtil.strObject(supplierGoodsInfo.get("goods_beans_count")));//每件赠送多少百货豆
                String beans_goods_give_id = MUtil.strObject(supplierGoodsInfo.get("beans_goods_give_id"));
                Integer present_beans_count = goods_beans_count;
                goods_beans_count = (goods_beans_count * percentage_num) / 100;

                //添加订单主表
                Map<String, Object> paramsOrder = new HashMap<String, Object>();
                String order_code = createOrderNum();
                paramsOrder.put("order_code", order_code);
                paramsOrder.put("company_code", company_code);
                paramsOrder.put("order_remarks", "");
                paramsOrder.put("pay_mode", 1);//支付方式：1、货到付款；2、支付宝；3、微信;4、其他
                paramsOrder.put("pay_status", 1);//支付状态：1、欠款；2、已结清
                paramsOrder.put("customer_code", shop_unique);//客户编号
                paramsOrder.put("collect_name", MUtil.strObject(shopInfo.get("shop_name")));//收货人姓名
                paramsOrder.put("collect_phone", MUtil.strObject(shopInfo.get("shop_phone")));//联系电话
                paramsOrder.put("collect_address", MUtil.strObject(shopInfo.get("shop_address_detail")));//收货地址
                paramsOrder.put("order_type", 0);//订单类型 0：自动下单；1：客户下单
                paramsOrder.put("order_money", wholesale_price * goods_count);//订单总金额
                paramsOrder.put("shop_latitude", MUtil.strObject(shopInfo.get("shop_latitude")));//店铺纬度
                paramsOrder.put("shop_longitude", MUtil.strObject(shopInfo.get("shop_longitude")));//店铺经度
                Integer present_beans = 0;
                Integer platform_beans = 0;

                //添加云商订单消息提醒
                Map<String, Object> sysmsgParams = new HashMap<String, Object>();
                sysmsgParams.put("sysmsg_content", "您有一条新的云商订单，请查看！");
                sysmsgParams.put("source_code", order_code);
                sysmsgParams.put("company_code", company_code);
                sysmsgParams.put("sysmsg_type", "1");
                supplierInfoDao.addSysmsg(sysmsgParams);
                Integer sysmsg_id = Integer.parseInt(MUtil.strObject(sysmsgParams.get("sysmsg_id")));

                //获取业务员id
                List<String> userList = supplierInfoDao.getUserList(company_code);
                List<Map<String, Object>> sysmsgUserList = new ArrayList<Map<String, Object>>();
                for (int k = 0; k < userList.size(); k++) {
                    Map<String, Object> sysmsgUser = new HashMap<String, Object>();
                    sysmsgUser.put("sysmsg_id", sysmsg_id);
                    sysmsgUser.put("staffer_id", userList.get(k));
                    sysmsgUserList.add(sysmsgUser);
                }
                //添加供货商消息用户表
                supplierInfoDao.addSysmsgUser(sysmsgUserList);
                //推送消息
                Map<String, String> extra = new HashMap<String, String>();
                extra.put("msgType", "1");
                extra.put("source_id", order_code);
                PushThread pushThread = new PushThread(userList, "智慧云商", "2", "您有一条新的云商订单，请查看！", extra);
                Thread t = new Thread(pushThread);
                t.start();

                //添加订单详情表
                Map<String, Object> paramsOrderdetail = new HashMap<String, Object>();
                paramsOrderdetail.put("order_code", order_code);
                paramsOrderdetail.put("goods_code", goods_barcode);
                paramsOrderdetail.put("goods_cost", Double.valueOf(MUtil.strObject(supplierGoodsInfo.get("goods_price"))));
                paramsOrderdetail.put("goods_price", wholesale_price);
                paramsOrderdetail.put("goods_name", goods_name);
                paramsOrderdetail.put("goods_unit", goodsunit_name);
                paramsOrderdetail.put("goods_id", sup_goods_id);
                Double fact_price = wholesale_price * goods_count;
                paramsOrderdetail.put("goods_count", goods_count);
                paramsOrderdetail.put("fact_price", fact_price);

                String promtion_code = null;
                Integer count_beans = null;
                Integer total_bean = null;
                if (!beans_goods_give_id.equals("0")) {
                    Map<String, Object> beansGoodsGiveInfo = supplierInfoDao.getBeansGoodsGiveInfo(beans_goods_give_id);
                    if (beansGoodsGiveInfo == null) {
                        beans_goods_give_id = null;
                    } else {
                        Integer counts = Integer.parseInt(MUtil.strObject(beansGoodsGiveInfo.get("counts")));
                        Integer residue_count = 0;
                        if (counts >= goods_count) {
                            total_bean = goods_beans_count * goods_count;

                            //计算满足规则赠送百货豆
                            present_beans = present_beans_count * goods_count;
                            //实际赠送的百货豆
                            platform_beans = goods_beans_count * goods_count;

                            residue_count = goods_count;
                        } else {
                            total_bean = goods_beans_count * (counts);

                            //计算满足规则赠送百货豆
                            present_beans = present_beans_count * (counts);
                            //实际赠送的百货豆
                            platform_beans = goods_beans_count * (counts);

                            residue_count = counts;
                        }
                        count_beans = goods_beans_count;
                        promtion_code = MUtil.strObject(beansGoodsGiveInfo.get("promtion_code"));

                        //修改商品百货豆规则已经补贴数量
                        Map<String, Object> residueCountParams = new HashMap<String, Object>();
                        residueCountParams.put("residue_count", residue_count);
                        residueCountParams.put("id", beans_goods_give_id);
                        supplierInfoDao.updateResidueCount(residueCountParams);
                        //修改商品百货豆促销活动使用数量
                        Map<String, Object> useCountParams = new HashMap<String, Object>();
                        useCountParams.put("use_count", residue_count * present_beans_count);
                        useCountParams.put("promtion_code", promtion_code);
                        supplierInfoDao.updateUseCount(useCountParams);
                    }
                }

                paramsOrderdetail.put("goods_beans_count", total_bean);
                paramsOrderdetail.put("count_beans", count_beans);
                paramsOrderdetail.put("promtion_code", promtion_code);
                paramsOrderdetail.put("beans_goods_give_id", beans_goods_give_id);
                supplierInfoDao.insertOrderDetail(paramsOrderdetail);
                Integer orderdetail_id = Integer.parseInt(MUtil.strObject(paramsOrderdetail.get("orderdetail_id")));

                paramsOrder.put("present_beans", present_beans);//满足规则赠送百货豆
                paramsOrder.put("platform_beans", platform_beans);//实际赠送的百货豆
                supplierInfoDao.insertOrder(paramsOrder);

                //添加订单详情规则表
                Map<String, Object> paramsOrderSpec = new HashMap<String, Object>();
                paramsOrderSpec.put("spec_name", goods_spec_name);
                paramsOrderSpec.put("orderdetail_id", orderdetail_id);
                if (!compose_specs_id.equals("")) {
                    paramsOrderSpec.put("compose_specs_id", compose_specs_id);
                }
                supplierInfoDao.insertOrderGoodsSpec(paramsOrderSpec);
                Integer sup_order_goodsSpecs_id = Integer.parseInt(MUtil.strObject(paramsOrderSpec.get("sup_order_goodsSpecs_id")));
                //获取该商品规格所在仓库信息
                Map<String, Object> paramsStock = new HashMap<String, Object>();
                paramsStock.put("goods_code", sup_goods_id);
                paramsStock.put("company_code", company_code);
                if (!compose_specs_id.equals("")) {
                    paramsStock.put("compose_specs_id", compose_specs_id);
                }
                Integer goods_count1 = goods_count;
                List<Map<String, Object>> goodsstockList = supplierInfoDao.getGoodsstockList(paramsStock);
                for (int n = 0; n < goodsstockList.size(); n++) {
                    Integer count = Integer.parseInt(MUtil.strObject(goodsstockList.get(n).get("goods_count")));
                    String goodsstock_id = MUtil.strObject(goodsstockList.get(n).get("goodsstock_id"));
                    String depot_name = MUtil.strObject(goodsstockList.get(n).get("depot_name"));
                    String shelves_name = MUtil.strObject(goodsstockList.get(n).get("shelves_name"));
                    if (count >= goods_count1) {//库存足
                        //修改商品规格所在仓库库存信息
                        Map<String, Object> paramsStockCount = new HashMap<String, Object>();
                        paramsStockCount.put("goods_count", goods_count1);
                        paramsStockCount.put("goodsstock_id", goodsstock_id);
                        supplierInfoDao.updateGoodsStockCount(paramsStockCount);
                        //添加订单库存表
                        Map<String, Object> paramsOrderStock = new HashMap<String, Object>();
                        paramsOrderStock.put("sup_order_goodsSpecs_id", sup_order_goodsSpecs_id);
                        paramsOrderStock.put("goodsstock_id", goodsstock_id);
                        paramsOrderStock.put("goods_count", goods_count1);
                        paramsOrderStock.put("depot_name", depot_name);
                        paramsOrderStock.put("shelves_name", shelves_name);
                        supplierInfoDao.insertOrderGoodsStock(paramsOrderStock);
                        //跳出循环
                        break;
                    } else {//库存不足
                        //修改商品规格所在仓库库存信息
                        Map<String, Object> paramsStockCount = new HashMap<String, Object>();
                        paramsStockCount.put("goods_count", count);
                        paramsStockCount.put("goodsstock_id", goodsstock_id);
                        supplierInfoDao.updateGoodsStockCount(paramsStockCount);
                        //添加订单库存表
                        Map<String, Object> paramsOrderStock = new HashMap<String, Object>();
                        paramsOrderStock.put("sup_order_goodsSpecs_id", sup_order_goodsSpecs_id);
                        paramsOrderStock.put("goodsstock_id", goodsstock_id);
                        paramsOrderStock.put("goods_count", count);
                        paramsOrderStock.put("depot_name", depot_name);
                        paramsOrderStock.put("shelves_name", shelves_name);
                        supplierInfoDao.insertOrderGoodsStock(paramsOrderStock);

                        goods_count1 = goods_count1 - count;
                    }
                }
                //获取店铺该商品信息
                Map<String, Object> paramsGoods = new HashMap<String, Object>();
                paramsGoods.put("shop_unique", shop_unique);
                paramsGoods.put("goods_barcode", goods_barcode);
                Map<String, Object> goodsInfo = goodsDao.queryGoodsDetail(paramsGoods);
                if (goodsInfo != null) {
                    //修改商品已采购待入库数量
                    paramsGoods.put("goods_count", goods_count);
                    goodsDao.updateStayStockCount(paramsGoods);
                }
            }
        }
        //给商户发送信息
//		shopNoticeList
    }

    //创建订单编号
    private static String createOrderNum() {
        SimpleDateFormat df = new SimpleDateFormat("yyyyMMddHHmmss");//设置日期格式
        String datestr = df.format(new Date());
        String sj_num = "" + (int) (1 + Math.random() * 9) + (int) (1 + Math.random() * 9) + (int) (1 + Math.random() * 9) + (int) (1 + Math.random() * 9);
        String order_num = "DD" + datestr + sj_num;
        return order_num;
    }

    /**
     * 烟草导入
     *
     * @param map
     * @return
     */
    @Transactional
    public ShopsResult yancaoInsert(Map<String, Object> map) {
        ShopsResult sr = new ShopsResult(1, "操作成功！");

        //先判定商品分类，在根据商品分类类型判断商品的分类信息

        Integer k = goodsDao.yancaoInsert(map);

        if (k == 0) {
            sr.setStatus(0);
            sr.setMsg("导入失败！");
        }
        return sr;
    }

    /**
     * 统计某商店录码数量
     *
     * @param map
     * @return
     */
    public ShopsResult queryGoodsNewCountForShop(Map<String, Object> map) {
        ShopsResult sr = new ShopsResult(1, "查询成功");
        try {
            List<Map<String, Object>> list = goodsDao.queryGoodsNewCountForShop(map);

            sr.setCount(goodsDao.queryGoodsNewCountForShopAllCount(map));
            sr.setData(list);
        } catch (Exception e) {
            e.printStackTrace();
            sr.setStatus(0);
            sr.setMsg("异常");
        }
        return sr;
    }

    /**
     * 创建新的条码
     *
     * @param goodsBarcode
     * @param barcodes
     * @return
     */
    public ShopsResult queryGoodsBarcodeSameForeignkey(String goodsBarcode, String barcodes, String shopUnique) {
        try {
            ShopsResult sr = new ShopsResult(1, "查询成功！");

            Map<String, Object> map = new HashMap<String, Object>();
            map.put("goodsBarcode", goodsBarcode);
            map.put("shopUnique", shopUnique);

            String[] bs;
            if (null != barcodes) {
                bs = barcodes.split(";");
                bs = Arrays.copyOf(bs, bs.length + 1);
                bs[bs.length - 1] = goodsBarcode;

            } else {
                bs = new String[1];
                bs[0] = goodsBarcode;
            }

            for (int i = 0; i < bs.length; i++) {
                System.out.println(bs[i]);
            }

            map.put("list", bs);
            String barcode = goodsDao.queryGoodsBarcodeSameForeignkey(map);
            if (null != barcode) {//除了该商品之外，本店还包含其他规格的该商品
                sr.setData(barcode);
            } else {//除了该商品之外，不考虑系统的其他规格，本店独有
                //创建新的条码
                Integer c = 1000000;
                while (true) {
                    String nGoodsBarcode = String.valueOf(Long.valueOf(goodsBarcode) + c);
                    c++;
                    boolean flag = false;//新条码与已有条码是否重复，默认不重复
                    //检测新条码是否可用
                    //新条码与已有条码不能重复
                    for (int i = 0; i < bs.length; i++) {
                        if (bs[i].equals(nGoodsBarcode)) {
                            flag = true;
                            break;
                        }
                    }
                    if (flag) {//该条码已存在
                        continue;
                    }
                    map.put("goodsBarcode", nGoodsBarcode);
                    barcode = goodsDao.queryGoodsBarcodeSameForeignkey(map);//
                    if (null == barcode) {//新条码可用
                        sr.setData(nGoodsBarcode);
                        break;
                    }
                }
            }

            return sr;
        } catch (Exception e) {
            e.printStackTrace();
            throw new MyException(1, "系统错误！");
        }
    }

    /**
     * 获取商品详情
     *
     * @param map
     * @return
     */
    public ShopsResult getGoodsDetail(Map<String, Object> map) {
        ShopsResult sr = new ShopsResult(1, "查询成功！");
        try {
            //查询本店是否有该商品
            CloudGoodsMain goods = goodsDao.getGoodsDetailInShop(map);
            if (null != goods) {
                if (null == goods.getForeignKey()) {
                    List<CloudGoodsDetail> gl = goods.getListDetail();
                    for (int i = 0; i < gl.size(); i++) {
                        if (gl.get(i).getGoodsContain() == 1) {
                            goods.setForeignKey(gl.get(i).getGoodsBarcode());
                        }
                    }
                }
                //获取该商品自动采购设置信息
                map.put("goods_barcode", goods.getForeignKey());
                map.put("shop_unique", map.get("shopUnique"));
                List<Map<String, Object>> auto = goodsDao.getGoodsAutoPurchase(map);
                map.remove("shop_unique");
                for (int i = 0; i < auto.size(); i++) {
                    Map<String, Object> params = new HashMap<String, Object>();
                    params.put("company_code", auto.get(i).get("company_code"));
                    params.put("goods_barcode", auto.get(i).get("sup_goods_barcode"));
                    Map<String, Object> goodsInfo = supplierInfoDao.getGoodsInfo(params);
                    auto.get(i).put("wholesale_price", goodsInfo.get("wholesale_price"));
                }
                goods.setAuto(auto);

            } else {//本店无此商品
                goods = goodsDao.getGoodsDetailInDict(map);
                if (null == goods) {
                    sr.setStatus(0);
                    sr.setMsg("暂无相关产品信息");
                    return sr;
                }
                if (null == goods.getForeignKey()) {
                    List<CloudGoodsDetail> gl = goods.getListDetail();
                    for (int i = 0; i < gl.size(); i++) {
                        if (gl.get(i).getGoodsContain() == 1) {
                            goods.setForeignKey(gl.get(i).getGoodsBarcode());
                        }
                    }
                }
                //验证商品分类信息,若自定义分类，则修改自定义分类
                if (goods.getHaveType() == 0) {
                    Integer kindType = impDao.queryShopKindType(map.get("shopUnique").toString());
                    if (2 == kindType) {
                        goods.setKindName(Load.DEFAULTSELFKINDNAME);
                        goods.setKindUnique(Load.DEFAULTSELFKINDUNIQUE);
                        goods.setGroupName(Load.DEFAULTSELFGROUPNAME);
                        goods.setGroupUnique(Load.DEFAULTSELFGROUPUNIQUE);
                    }
                }
            }
            sr.setData(goods);
        } catch (Exception e) {
            e.printStackTrace();
            throw new MyException(0, "系统错误！");
        }
        return sr;
    }

    /**
     * 更新商品图片
     *
     * @param shop_unique   店铺编码
     * @param goods_barcode 商品编码
     * @param file          商品图片文件
     * @return
     */
    @Transactional
    public PurResult updateGoodsImg(String shop_unique, String goods_barcode, MultipartFile file, HttpServletRequest request) {
        PurResult pr = new PurResult();
        try {
            Map<String, Object> params = new HashMap<String, Object>();
            params.put("shop_unique", shop_unique);
            params.put("goods_barcode", goods_barcode);
            if (file != null) {
                String orName = file.getOriginalFilename();//获取文件原名称
                String lastName = orName.substring(orName.lastIndexOf("."));
                lastName.replace(".JPG", ".jpg");
                lastName.replace(".JPEG", ".jpeg");
                lastName.replace(".PNG", ".png");
                String newName = goods_barcode + Math.round(Math.random() * 100) + lastName;
                String filePathDetail = File.separator + "image" + File.separator + shop_unique;
                SFTPUtil sftp = new SFTPUtil(FTPConfig.username, FTPConfig.password, FTPConfig.host, FTPConfig.port);
                sftp.login();

                //存到文件服务器-start
                byte[] bytes = ImageZipUtils.file2byte(ImageZipUtils.MultipartFileToFile(file));
                byte[] bimg = ImageZipUtils.compressPicForScale(bytes, 100, 1, true, 800, 800);

                sftp.upload(FTPConfig.goods_path + "/" + shop_unique, newName, ImageZipUtils.byte2Input(bimg));

                byte[] smmg = ImageZipUtils.compressPicForScale(bytes, 50, 1, true, 345, 345);

                sftp.upload(FTPConfig.goods_middle_path + "/" + shop_unique, newName, ImageZipUtils.byte2Input(smmg));
                sftp.upload(FTPConfig.goods_small_path + "/" + shop_unique, newName, ImageZipUtils.byte2Input(smmg));

                params.put("goods_picturepath", filePathDetail + File.separator + newName);
                pr.setData(StringUtils.join("http://file.buyhoo.cc/middle/image/", shop_unique, "/", newName));
            }

            goodsDao.updateGoodsMessage(params);
            pr.setStatus(1);
            pr.setMsg("成功！");
        } catch (Exception e) {
            e.printStackTrace();
            pr.setStatus(0);
            pr.setMsg("异常");
        }

        return pr;
    }

    /**
     * 更新大库商品信息
     *
     * @param barcode
     * @param file
     * @param request
     * @return
     */
    @Transactional
    public ShopsResult updateGoodsDictImg(String barcode, MultipartFile file, HttpServletRequest request) {
        ShopsResult sr = new ShopsResult(1, "上传成功！");

        try {
            Map<String, Object> params = new HashMap<String, Object>();
            params.put("barcode", barcode);
            if (file != null) {
                String orName = file.getOriginalFilename();//获取文件原名称
                String lastName = orName.substring(orName.lastIndexOf("."));
                String newName = barcode + lastName;
                String filePathDetail = File.separator + "image" + File.separator + "goodsDict";
                String filePath = request.getSession().getServletContext().getRealPath(File.separator);
                filePath = filePath.substring(0, filePath.length() - request.getContextPath().length()) + filePathDetail;

                SFTPUtil sftp = new SFTPUtil(FTPConfig.username, FTPConfig.password, FTPConfig.host, FTPConfig.port);
                sftp.login();
                //存到文件服务器-start
                InputStream is;
                boolean flag = false;
                try {
                    is = file.getInputStream();
                    sftp.upload(FTPConfig.goods_path + "/goodsDict", newName, is);
                } catch (Exception e) {
                    e.printStackTrace();
                }
                //存到文件服务器-end
                flag = ShopsUtil.savePicture(file, filePath, newName, "2");

                if (flag) {
                    params.put("picture_name", filePathDetail + File.separator + newName);
                    dictDao.updateGoodsDictMsg(params);
                } else {
                    sr.setMsg("更新图片失败");
                    sr.setStatus(0);
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
            sr.setStatus(0);
            sr.setMsg("异常");
        }
        return sr;
    }

    @Override
    public List<Map<String, Object>> getAllShopList() {
        return goodsDao.getAllShopList();
    }

    @Override
    public List<Map<String, Object>> getGoodsDetails(Map<String, Object> params) {
        return goodsDao.getGoodsDetails(params);
    }

    /**
     * 更新商品详细信息
     *
     * @param goods_id 商品id
     * @return
     */
    @Transactional
    public PurResult updateGoodsDetails(MultipartFile file1, MultipartFile file2, MultipartFile file3, MultipartFile file4, HttpServletRequest request, String monitor_info_id) {
        PurResult pr = new PurResult();
        try {
            Map<String, Object> params = ServletsUtil.getParameters(request);
            String goods_id = MUtil.strObject(params.get("goods_id"));
            String shop_unique = MUtil.strObject(params.get("shop_unique"));
            String update_staff = MUtil.strObject(params.get("update_staff"));
//			String details_img_url1 = MUtil.strObject(params.get("details_img_url1"));
//			String details_img_url2 = MUtil.strObject(params.get("details_img_url2"));
//			String details_img_url3 = MUtil.strObject(params.get("details_img_url3"));
//			String details_img_url4 = MUtil.strObject(params.get("details_img_url4"));
//			String detail_base64 = MUtil.strObject(params.get("detail_base64"));
            String content = MUtil.strObject(params.get("content"));


            SFTPUtil sftp = new SFTPUtil(FTPConfig.username, FTPConfig.password, FTPConfig.host, FTPConfig.port);
            sftp.login();

            if (null != monitor_info_id && !monitor_info_id.equals("")) {
                //
                Map<String, Object> param = new HashMap<String, Object>();
                param.put("goodsId", goods_id);
                param.put("monitorInfoId", monitor_info_id);

                goodsDao.modifyGoodsMonitor(param);
            }

            if (file1 == null && file2 == null && file3 == null) {
                //不做操作
            } else {
                //删掉该商品全部轮播图信息
                goodsDao.deleteGoodsDetailImg(goods_id);
                Map<String, Object> ImgUrlParams = new HashMap<String, Object>();
                ImgUrlParams.put("goods_id", goods_id);
                ImgUrlParams.put("type", 1);
                ImgUrlParams.put("update_staff", update_staff);
                if (file1 != null) {//轮播图1
                    String orName = file1.getOriginalFilename();//获取文件原名称
                    String lastName = orName.substring(orName.lastIndexOf("."));
                    String newName = goods_id + Math.round(Math.random() * 100) + lastName;
                    String filePathDetail = File.separator + "image" + File.separator + shop_unique;

                    //存到文件服务器-start
                    try {
                        byte[] bytes = ImageZipUtils.file2byte(ImageZipUtils.MultipartFileToFile(file1));
                        byte[] bimg = ImageZipUtils.compressPicForScale(bytes, 100, 1, true, 800, 800);
                        sftp.upload(FTPConfig.goods_path + "/" + shop_unique, newName, ImageZipUtils.byte2Input(bimg));

                    } catch (SftpException e) {
                        e.printStackTrace();
                    }
                    //存到文件服务器-end
                    ImgUrlParams.put("details_img_url", FTPConfig.FILEURL + filePathDetail + File.separator + newName);
                    //添加轮播图
                    goodsDao.addGoodsDetailImg(ImgUrlParams);
                }
                if (file2 != null) {//轮播图2
                    String orName = file2.getOriginalFilename();//获取文件原名称
                    String lastName = orName.substring(orName.lastIndexOf("."));
                    String newName = goods_id + Math.round(Math.random() * 100) + lastName;
                    String filePathDetail = File.separator + "image" + File.separator + shop_unique;

                    //存到文件服务器-start
                    try {
                        byte[] bytes = ImageZipUtils.file2byte(ImageZipUtils.MultipartFileToFile(file2));
                        byte[] bimg = ImageZipUtils.compressPicForScale(bytes, 100, 1, true, 800, 800);
                        sftp.upload(FTPConfig.goods_path + "/" + shop_unique, newName, ImageZipUtils.byte2Input(bimg));

                    } catch (SftpException e) {
                        e.printStackTrace();
                    }
                    ImgUrlParams.put("details_img_url", FTPConfig.FILEURL + filePathDetail + File.separator + newName);
                    //添加轮播图
                    goodsDao.addGoodsDetailImg(ImgUrlParams);
                }
                if (file3 != null) {//轮播图3
                    String orName = file3.getOriginalFilename();//获取文件原名称
                    String lastName = orName.substring(orName.lastIndexOf("."));
                    String newName = goods_id + Math.round(Math.random() * 100) + lastName;
                    String filePathDetail = File.separator + "image" + File.separator + shop_unique;

                    //存到文件服务器-start
                    try {
                        byte[] bytes = ImageZipUtils.file2byte(ImageZipUtils.MultipartFileToFile(file3));
                        byte[] bimg = ImageZipUtils.compressPicForScale(bytes, 100, 1, true, 800, 800);
                        sftp.upload(FTPConfig.goods_path + "/" + shop_unique, newName, ImageZipUtils.byte2Input(bimg));

                    } catch (SftpException e) {
                        e.printStackTrace();
                    }
                    //存到文件服务器-end
                    ImgUrlParams.put("details_img_url", FTPConfig.FILEURL + filePathDetail + File.separator + newName);
                    //添加轮播图
                    goodsDao.addGoodsDetailImg(ImgUrlParams);
                }
            }

            if (file4 != null) {
                //删掉该商品全部视频信息
                goodsDao.deleteGoodsDetailVedio(goods_id);
                Map<String, Object> ImgUrlParams = new HashMap<String, Object>();
                ImgUrlParams.put("goods_id", goods_id);
                ImgUrlParams.put("type", 3);
                ImgUrlParams.put("update_staff", update_staff);

                if (file4 != null) {//轮播视频
                    String orName = file4.getOriginalFilename();//获取文件原名称
                    String lastName = orName.substring(orName.lastIndexOf("."));
                    String newName = goods_id + Math.round(Math.random() * 100) + lastName;
                    String filePathDetail = File.separator + "image" + File.separator + shop_unique;
                    //存到文件服务器-start
                    InputStream is;
                    try {
                        is = file4.getInputStream();
                        sftp.upload(FTPConfig.goods_path + "/" + shop_unique, newName, is);
                    } catch (Exception e) {
                        e.printStackTrace();
                    }
                    //存到文件服务器-end
                    ImgUrlParams.put("details_img_url", FTPConfig.FILEURL + filePathDetail + File.separator + newName);
                    goodsDao.addGoodsDetailImg(ImgUrlParams);
                }
            }

            //商品详情
            Map<String, Object> ImgUrlParams = new HashMap<String, Object>();
            ImgUrlParams.put("goods_id", goods_id);
            ImgUrlParams.put("type", 4);
            ImgUrlParams.put("update_staff", update_staff);
            ImgUrlParams.put("details_img_url", content);

            //获取商品详情信息
            List<Map<String, Object>> goodsDetails = goodsDao.getGoodsDetails(ImgUrlParams);
            if (goodsDetails.size() > 0) {
                //修改商品详情
                goodsDao.updateGoodsDetailImg(ImgUrlParams);
            } else {
                //添加商品详情
                goodsDao.addGoodsDetailImg(ImgUrlParams);
            }
            pr.setStatus(1);
            pr.setMsg("成功！");
        } catch (Exception e) {
            e.printStackTrace();
            pr.setStatus(0);
            pr.setMsg("异常");
        }

        return pr;
    }

    @Transactional
    public PurResult batchUpdateCount(HttpServletRequest request) {
        PurResult pr = new PurResult();
        try {
            Map<String, Object> params = ServletsUtil.getParameters(request);
            //删除范围：1查询全删 2本页全删
            String updateRange = MUtil.strObject(params.get("updateRange"));
            if (updateRange != null && updateRange.equals("1")) {
                //查询商品信息，
                List<String> list = goodsDao.queryGoodsForDelete(params);
                if (list.size() == 0) {
                    pr.setStatus(2);
                    pr.setMsg("没有需要修改的商品");
                    return pr;
                }
                //删除商品信息
                String[] barcodes = new String[list.size()];
                for (int i = 0; i < list.size(); i++) {
                    barcodes[i] = list.get(i).trim();
                }
                params.put("barcodes", barcodes);
            } else if (updateRange != null && updateRange.equals("2")) {
                String goodsBarcodes = MUtil.strObject(params.get("goodsBarcodes"));
                if (goodsBarcodes != null && !goodsBarcodes.equals("")) {
                    String[] barcodes = goodsBarcodes.split(";");
                    params.put("barcodes", barcodes);
                } else {
                    pr.setStatus(2);
                    pr.setMsg("没有需要修改的商品");
                    return pr;
                }
            }
            goodsDao.batchUpdateCount(params);
            pr.setStatus(1);
            pr.setMsg("成功！");
        } catch (Exception e) {
            e.printStackTrace();
            pr.setStatus(0);
            pr.setMsg("异常");
        }

        return pr;
    }

    public List<Map<String, Object>> queryGoodsSaleStatisticsExcel(Map<String, Object> params) {
        return goodsDao.queryGoodsSaleStatistics(params);
    }

    public Map<String, Object> queryGoodsSaleStatisticsCountExcel(Map<String, Object> params) {
        return goodsDao.queryGoodsSaleStatis(params);
    }

    @Override
    public Map<String, Object> goodsSaleStatistics(Map<String, Object> params) {
    	params.remove("page");
    	params.remove("limit");
        List<Map<String, Object>> maps = goodsDao.queryGoodsSaleStatistics(params);
        BigDecimal saleCount =  new BigDecimal(0); // 商品数量
        BigDecimal giftCount =  new BigDecimal(0); // 赠送数量
        BigDecimal saleSum = new BigDecimal(0); // 销售金额
        BigDecimal purSum = new BigDecimal(0); // 销售成本
        BigDecimal grossProfit  = new BigDecimal(0); // 销售利率
        // retCount 退货数量
        // retTotal 退货退货价总值
        // retPur 退货原价总值
        for (Map<String, Object> map : maps) {
            saleCount = saleCount.add(new BigDecimal(String.valueOf(map.get("saleCount"))));
            giftCount = giftCount.add(new BigDecimal(String.valueOf(map.get("giftCount"))));
            saleSum = saleSum.add(new BigDecimal(String.valueOf(map.get("saleSum"))));
            purSum = purSum.add(new BigDecimal(String.valueOf(map.get("purSum"))));

			if (purSum.compareTo(new BigDecimal(0)) == 0){
				purSum = new BigDecimal(1);
            }
        }
        if (saleSum.compareTo(new BigDecimal(0)) > 0){
            grossProfit = saleSum.divide(purSum,2, RoundingMode.HALF_UP);
        }

        Map<String, Object> result = new HashMap<>();
        result.put("saleCount",saleCount);
        result.put("giftCount",giftCount);
        result.put("saleSum",saleSum);
        result.put("purSum",purSum);
        result.put("grossProfit",grossProfit);
        return result;
    }

    @Override
    public List<Map<String, Object>> downloadSingleGoodsExcel(Map<String, Object> map) {
        return goodsDao.downloadSingleGoodsExcel(map);

    }

    @Override
    public PurResult querySperGoods(String goods_id, String goods_name, String goods_barcode) {
        PurResult pr = new PurResult(100001, "操作成功!");
        String finalURL = "";
        String keyword = goods_name;
        List<String> photoUrl = new ArrayList<String>();
        Document document = null;
        try {
            //爬取方法1-360
            String url = "http://image.so.com/j";
            Map<String, String> param = new HashMap<String, String>();
            param.put("q", keyword);
            param.put("src", "srp");
            param.put("correct", keyword);
            param.put("pn", "60");
            param.put("sn", "0");
            String string = HttpClientUtil.doGet(url, param);
            // System.out.println(string);
            com.alibaba.fastjson.JSONArray jsonArray = JSON.parseArray(JSON.parseObject(string).get("list").toString());
            for (int i = 0; i < jsonArray.size(); i++) {
                String href = jsonArray.getJSONObject(i).get("thumb").toString();
                photoUrl.add(href);
            }
            pr.setData(photoUrl);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return pr;
    }

    @Override
    public void updatePhoto(Map<String, Object> map) {
        goodsDao.updatePhoto(map);
    }

	@Override
	public ShopsResult getGoodBatchList(Map<String, Object> params) {
		ShopsResult result = new ShopsResult();
		List<Map<String, Object>> goodsList = goodsDao.getGoodList(params);
		Integer count = goodsDao.getGoodListCount(params);
		result.setStatus(1);
		result.setMsg("查询成功");
		result.setCount(count);
		result.setData(goodsList);
		return result;
	}
}
