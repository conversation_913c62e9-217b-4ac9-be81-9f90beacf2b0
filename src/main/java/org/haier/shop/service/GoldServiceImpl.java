package org.haier.shop.service;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;

import org.haier.shop.dao.GoldDao;
import org.haier.shop.util.PurResult;
import org.springframework.stereotype.Service;

@Service
public class GoldServiceImpl implements GoldService{
	@Resource
	private GoldDao goldDao;
	
	public PurResult queryGoldShopList(Map<String,Object> map){
		PurResult sr=new PurResult();
		List<Map<String,Object>> data = goldDao.queryGoldShopList(map);
		for(Map<String,Object> params:data) {			
			params.put("deviceList", goldDao.getGoldDeviceRewardList(params));
		}
		
		Integer count = goldDao.queryGoldShopCount(map);
			
		sr.setData(data);
		sr.setCount(count);
		sr.setStatus(1);
		sr.setMsg("成功");
		
		return sr;
	}

	public PurResult queryGoldDeviceList(Map<String, Object> params) {
		PurResult sr=new PurResult();
		List<Map<String,Object>> data = goldDao.queryGoldDeviceList(params);
		Integer count = goldDao.queryGoldDeviceCount(params);
			
		sr.setData(data);
		sr.setCount(count);
		sr.setStatus(1);
		sr.setMsg("成功");
		
		return sr;
	}
	public PurResult queryGoldOrderList(Map<String, Object> params) {
		PurResult sr=new PurResult();
		List<Map<String,Object>> data = goldDao.queryGoldOrderList(params);
		Integer count = goldDao.queryGoldOrderCount(params);
			
		sr.setData(data);
		sr.setCount(count);
		sr.setStatus(1);
		sr.setMsg("成功");
		
		return sr;
	}
	public Map<String, Object> queryGoldRule() {

		return goldDao.queryGoldRule();
	}	
	
	public PurResult updatePtRule(Map<String, Object> params) {
		PurResult sr=new PurResult();
		goldDao.updatePtRule(params);
		if(params.get("type").equals("aa"))
		{
			goldDao.updatePtRule2(params);
		}
		if(params.get("type2").equals("bb"))
		{
			goldDao.updatePtRule3(params);
		}
		if(params.get("type3").equals("cc"))
		{
			goldDao.updatePtRule4(params);
		}
		sr.setStatus(1);
		sr.setMsg("成功");
		
		return sr;
	}
	public List<Map<String, Object>>  queryGoldDevice(String shop_unique ) {

		return goldDao.queryGoldDevice(shop_unique);
	}
	public Map<String, Object> queryGoldOrder(String shop_unique ) {

		return goldDao.queryGoldOrder(shop_unique);
	}

	public PurResult updateGoldShopPt(Map<String, Object> params, List<Map<String, Object>> list) {
		PurResult sr=new PurResult();
		if(params.get("order_type").equals("Y"))//Y 没有禅参加订单返利
		{
			goldDao.addGlodOrder(params);
		}else
		{
			goldDao.updateGlodOrder(params);
		}
		
		if(list!=null&&list.size()>0)
		{
			List<Map<String ,Object>> addList= new ArrayList<Map<String ,Object>>();
			List<Map<String ,Object>> updateList= new ArrayList<Map<String ,Object>>();
			List<Map<String ,Object>> data=goldDao.queryGoldDevice(params.get("shop_unique").toString());
			
			for(Map<String ,Object> t:list)
			{
					int num=0;
					for(Map<String ,Object> tt:data)
					{
						if(t.get("device_no").equals(tt.get("device_no")))
						{
							t.put("id", tt.get("id"));
							num=1;
							break;
						}
					}
				if(num>0)
				{
					updateList.add(t);
				}else
				{
					addList.add(t);
				}
			}
			
			if(addList!=null&&addList.size()>0)
			{
				goldDao.addGlodDevice(addList);
			}
			if(updateList!=null&&updateList.size()>0)
			{
				goldDao.updateGlodDevice(updateList);
			}
		}
		sr.setStatus(1);
		sr.setMsg("成功");
		return sr;
	}

	public List<Map<String, Object>> queryGoldShop() {
		// TODO Auto-generated method stub
		return goldDao.queryGoldShop();
	}

	public PurResult addGoldShopPt(Map<String, Object> params, List<Map<String, Object>> queryGoldDevice) {
		
		PurResult sr=new PurResult();
		
		goldDao.addGlodOrder(params);
		int num=0;
		if(queryGoldDevice!=null&&queryGoldDevice.size()>0)
		{
			goldDao.addGlodDevice(queryGoldDevice);
			num=queryGoldDevice.size();
		}
		params.put("device_num", num);
		goldDao.addGlodShop(params);
		sr.setStatus(1);
		sr.setMsg("成功");
		return sr;
	}

	public PurResult updateGoldbyHand(Map<String, Object> params) {
		PurResult sr=new PurResult();
		params.put("give_type", 1);
		params.put("device_no", "");
		params.put("valid_order_num", 0);
		List<Map<String ,Object>> data=new ArrayList<Map<String ,Object>>();
		data.add(params);
		//给店铺添加金圈币
		goldDao.updateShopGold(data);
		//添加发放记录
		goldDao.addShopGoldRewards(data);
		sr.setStatus(1);
		sr.setMsg("成功");
		return sr;
	}

	@Override
	public PurResult delGoldDetail(Map<String, Object> params) {
		// TODO Auto-generated method stub
		PurResult sr=new PurResult();

		List<Map<String ,Object>> data=new ArrayList<Map<String ,Object>>();
		data.add(params);
		//给店铺减去金圈币
		goldDao.updateShopGold(data);
		//添加发放记录
		goldDao.delGoldDetail(params);
		sr.setStatus(1);
		sr.setMsg("成功");
		return sr;
	}
	
}
