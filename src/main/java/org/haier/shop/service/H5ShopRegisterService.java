package org.haier.shop.service;

import java.util.Map;

import javax.servlet.http.HttpServletRequest;

import org.haier.shop.util.ShopsResult;

public interface H5ShopRegisterService {
	
	/**
	 * 注册新店铺
	 * @param shop_name
	 * @param manager_account
	 * @return
	 */
	public ShopsResult register(String shop_name,String manager_account,String is_esign,String esign_num);
	
	/**
	 * 获取设备信息
	 * @return
	 */
	public ShopsResult queryDeviceList();
	
	/**
	 * 购买设备软件---微信支付
	 * @param shop_unique 店铺编号
	 * @return
	 */
	public ShopsResult weChatPay(Map<String ,Object> params);
	
	/**
	 * 店铺注册时购买设备支付成功业务处理
	 * @param 
	 * @return
	 */
	public boolean deviceSoftPaySuccess(Map<String,Object> params);
	
	/**
	 * 获取支付信息
	 * @param out_trade_no 支付编号
	 * @return
	 */
	public ShopsResult queryPayInfo(String out_trade_no);
	
	/**
	 * 修改店铺信息
	 * @param shop_unique 店铺编号
	 * @return
	 */
	public ShopsResult updateShopInfo(HttpServletRequest request);
	
	/**
	 * 获取店铺信息
	 * @param shop_unique 店铺编号
	 * @return
	 */
	public ShopsResult queryShopInfo(String shop_unique);
	
	/**
	 * 获取电子合同信息
	 * @param able_num 能人编号
	 * @return
	 */
	public ShopsResult queryESignInfo(String able_num);
	
	/**
	 * 修改电子签单信息
	 * @param unicom_able_id 能人id
	 * @return
	 */
	public ShopsResult saveEsign(HttpServletRequest request);
}
