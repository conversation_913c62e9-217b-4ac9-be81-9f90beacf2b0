package org.haier.shop.service;

import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpUtil;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.logging.Log;
import org.apache.shiro.SecurityUtils;
import org.apache.shiro.session.Session;
import org.apache.shiro.subject.Subject;
import org.haier.meituan.util.MUtil;
import org.haier.shop.dao.*;
import org.haier.shop.entity.*;
import org.haier.shop.entity.goodsRecord.RecordGoods;
import org.haier.shop.entity.goodsRecord.RecordGoodsOper;
import org.haier.shop.entity.goodsRecord.RecordGoodsOperParams;
import org.haier.shop.enums.*;
import org.haier.shop.enums.goodsEnum.DeviceSourceEnum;
import org.haier.shop.enums.goodsEnum.OperSourceEnum;
import org.haier.shop.enums.goodsEnum.OperTypeEnum;
import org.haier.shop.enums.goodsEnum.UserTypeEnum;
import org.haier.shop.params.goods.*;
import org.haier.shop.params.purchase.PurchaseGoodsAddParams;
import org.haier.shop.params.purchase.PurchaseGoodsDetailParam;
import org.haier.shop.params.purchase.PurchaseGoodsDetailRetParam;
import org.haier.shop.params.purchase.PurchaseGoodsRetParam;
import org.haier.shop.util.IPGet;
import org.haier.shop.util.PurResult;
import org.haier.shop.util.UniqueUtils;
import org.haier.shop.util.UtilForJAVA;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import net.sf.json.JSONArray;
import net.sf.json.JSONObject;

/**
 * 商家自采购进货单service实现类
 */
@Service
public class SelfPurchaseServiceImpl implements SelfPurchaseService{
	
	@Resource
	private SelfPurchaseDao selfPurchaseDao;
	
	@Resource
	private SupplierDao supplierDao;
	@Resource
	private SelfPurchaseDetailMapper selfPurchaseDetailMapper;
	@Resource
	private StockService stockService;
	@Resource
	private SelfPurchaseRetMapper selfPurchaseRetMapper;
	@Resource
	private SelfPurchaseRetDetailMapper selfPurchaseRetDetailMapper;
	@Resource
	private ShopsConfigDao shopsConfigDao;
	@Resource
	private GoodsBatchMapper goodsBatchMapper;

	/**
	 * 添加进货单信息
	 *
	 * @return
	 */
	@Transactional(rollbackFor = {Exception.class})
	public PurResult insertSelfRetPurchase(PurchaseGoodsRetParam retParam) {
		PurResult pr = new PurResult(PurResult.FAIL, "操作失败");
		SelfPurchaseRet ret = new SelfPurchaseRet();
		BeanUtil.copyProperties(retParam, ret);
		ret.setSelfPurchaseRetUnique("T" + UniqueUtils.createListUnique());
		Date currentDate = DateUtil.date();
		ret.setUpdateTime(currentDate);
		ret.setCreateTime(currentDate);
		int n = selfPurchaseRetMapper.insert(ret);
		Long staffId = Long.parseLong(retParam.getCreateStaffId());
		if (n > 0) {

			OutStockParam outStockParam = new OutStockParam();
			outStockParam.setShopUnique(ret.getShopUnique());
			outStockParam.setListUnique(UniqueUtils.createListUnique());
			outStockParam.setSourceUnique(ret.getSelfPurchaseRetUnique());
			outStockParam.setUserId(staffId);
			outStockParam.setStockResource(StockReourceEnum.BY_ORDER.getValue());
			List<OutStockGoodsParam> goodsList = new ArrayList<>();
			List<OutStockGoodsBatchParam> goodsBatchList = new ArrayList<>();
			List<PurchaseGoodsDetailRetParam> detailParamList = retParam.getGoodsDetailList();
			List<SelfPurchaseRetDetail> detailList = new ArrayList<>();
			for (PurchaseGoodsDetailRetParam detailParam : detailParamList) {
				SelfPurchaseRetDetail detail = new SelfPurchaseRetDetail();
				BeanUtil.copyProperties(detailParam, detail);
				detail.setSelfPurchaseUnique(ret.getSelfPurchaseUnique());
				detail.setSelfPurchaseRetUnique(ret.getSelfPurchaseRetUnique());
				detailList.add(detail);

				OutStockGoodsParam outStockGoodsParam = new OutStockGoodsParam();
				outStockGoodsParam.setGoodsId(detailParam.getGoodsId());
				outStockGoodsParam.setGoodsName(detailParam.getGoodsName());
				outStockGoodsParam.setGoodsBarcode(detailParam.getGoodsBarcode());
				outStockGoodsParam.setGoodsCount(detailParam.getGoodsCount());
				outStockGoodsParam.setStockPrice(detailParam.getGoodsInPrice());
				outStockGoodsParam.setSupplierUnique(String.valueOf(ret.getSupplierUnique()));
				goodsList.add(outStockGoodsParam);

				OutStockGoodsBatchParam outStockGoodsBatchParam = new OutStockGoodsBatchParam();
				outStockGoodsBatchParam.setGoodsId(detailParam.getGoodsId());
				outStockGoodsBatchParam.setBatchList(detailParam.getBatchParamList());
				goodsBatchList.add(outStockGoodsBatchParam);
			}

			outStockParam.setGoodsList(goodsList);
			outStockParam.setGoodsBatchList(goodsBatchList);

			// 添加退货商品明细
			selfPurchaseRetDetailMapper.insertBatch(detailList);

			// 添加出入库记录
			stockService.addOutStock(outStockParam);

			pr.setStatus(PurResult.SUCCESS.intValue());
			pr.setMsg("操作成功");
		}
		return pr;
	}
	/**
	 * 添加退货订单
	 * @param shopUnique 店铺编号
	 * @param selfPurchaseUnique 进货订单号
	 * @param goodsDetail 商品详情，JSONArray格式
	 * @param supplierUnique 供货商编号
	 * @param staffId 操作员工编号
	 * @return
	 */
	@Transactional
	public PurResult addNewReturnPurList(
			String shopUnique,String selfPurchaseUnique,String goodsDetail,String supplierUnique,String staffId,String remark
		) {
		PurResult pr = new PurResult(1, "退货成功!");
		
		Map<String,Object> map = new HashMap<String,Object>();
		map.put("self_purchase_unique", selfPurchaseUnique);
		map.put("shopUnique", shopUnique);
		
		/*
		 * 退款订单编号
		 */
		String selfPurchaseRetUnique = System.nanoTime() + "";
		/*
		 * 0退货操作流程
		 * 1、获取订单信息和已退货信息，防止重复提交
		 * 2、获取当前订单信息，防止退货数量大于当前订单数量
		 * 3、添加退货记录
		 * 4、添加退货详情记录
		 * 5、添加出入库记录
		 * 6、修改库存
		 */
		//退货记录
		Map<String,Object> retMap = new HashMap<String,Object>();
		//退货详情记录
		List<Map<String,Object>> retDetailList = new ArrayList<Map<String,Object>>();

		List<Map<String,Object>> goodsList = selfPurchaseDao.getSelfPurchaseReturnDetailList(map);
		if(null == goodsList || goodsList.isEmpty()) {
			pr.setStatus(0);
			pr.setMsg("订单信息不存在!");
			return pr;
		}
		
		retMap.put("selfPurchaseUnique", selfPurchaseUnique);
		retMap.put("selfPurchaseRetUnique", selfPurchaseRetUnique);
		retMap.put("shopUnique", shopUnique);
		retMap.put("supplierUnique", supplierUnique);
		retMap.put("staffId", staffId);

		BigDecimal totalCount = new BigDecimal(0);
		BigDecimal totalPrice = new BigDecimal(0);
		JSONArray ja = JSONArray.fromObject(goodsDetail);


		OutStockParam outStockParam = new OutStockParam();
		outStockParam.setShopUnique(Long.parseLong(shopUnique));
		outStockParam.setListUnique(UniqueUtils.createListUnique());
		outStockParam.setSourceUnique(selfPurchaseRetUnique);
		outStockParam.setStockResource(StockReourceEnum.BY_ORDER.getValue());
		outStockParam.setUserId(Long.parseLong(staffId));
		List<OutStockGoodsParam> outStockGoodsParamList = new ArrayList<>();
		List<OutStockGoodsBatchParam> outStockGoodsBatchParamList = new ArrayList<>();

		ShopsConfig shopsConfig = shopsConfigDao.selectByShopUnique(shopUnique);

		//校验批次数量
		Map<String, GoodsBatch> goodsBatchMap = new HashMap<>();
		if (ObjectUtil.isNotNull(shopsConfig) && ObjectUtil.equals(GoodsInPriceTypeEnums.BATCH.getCode(), shopsConfig.getGoodsInPriceType())) {
			List<GoodsBatch> goodsBatchList = goodsBatchMapper.selectBySourceUnique(Long.parseLong(shopUnique), selfPurchaseUnique);
			if (CollectionUtil.isNotEmpty(goodsBatchList)) {
				goodsBatchMap = goodsBatchList.stream().collect(Collectors.toMap(GoodsBatch::getSourceBarcode, v -> v));
			}
		}

		for(Integer i = 0; i < ja.size(); i++) {
			JSONObject jo = ja.getJSONObject(i);
			String goodsBarcode = jo.getString("goods_barcode");
			String goodsName = jo.getString("goods_name");
			Long goodsId = jo.getLong("goods_id");
			BigDecimal count = new BigDecimal(jo.getDouble("count"));
			BigDecimal price = new BigDecimal(jo.getDouble("price"));

			GoodsBatch goodsBatch = goodsBatchMap.get(goodsBarcode);
			if (ObjectUtil.isNotNull(goodsBatch)) {
				BigDecimal rCount = NumberUtil.div(goodsBatch.getGoodsCount(), goodsBatch.getGoodsContain());
				if (rCount.compareTo(count) < 0) {
					throw new RuntimeException("商品：" + goodsName + "(" + goodsBarcode +") 对应批次：" + goodsBatch.getBatchUnique() + " 库存不足，无法退货");
				} else {
					List<OutStockBatchParam> batchList = new ArrayList<>();
					OutStockGoodsBatchParam batchParam = new OutStockGoodsBatchParam();
					batchParam.setGoodsId(goodsId);
					OutStockBatchParam outStockBatchParam = new OutStockBatchParam();
					outStockBatchParam.setBatchUnique(goodsBatch.getBatchUnique());
					outStockBatchParam.setGoodsCount(count);
					outStockBatchParam.setGoodsContain(goodsBatch.getGoodsContain());
					batchList.add(outStockBatchParam);
					batchParam.setBatchList(batchList);
					outStockGoodsBatchParamList.add(batchParam);
				}
			}
			OutStockGoodsParam stockGoodsParam = new OutStockGoodsParam();
			stockGoodsParam.setGoodsId(goodsId);
			stockGoodsParam.setGoodsName(goodsName);
			stockGoodsParam.setGoodsBarcode(goodsBarcode);
			stockGoodsParam.setGoodsCount(count);
			stockGoodsParam.setStockPrice(price);
			stockGoodsParam.setSupplierUnique(supplierUnique);
			outStockGoodsParamList.add(stockGoodsParam);


			totalCount = totalCount.add(count);
			totalPrice = totalPrice.add(count.multiply(price));

			Map<String,Object> retDetailMap = new HashMap<String,Object>();
			
			retDetailMap.put("selfPurchaseDetailId", jo.getString("self_purchase_detail_id"));
			retDetailMap.put("selfPurchaseUnique", selfPurchaseUnique);
			retDetailMap.put("selfPurchaseRetUnique", selfPurchaseRetUnique);
			retDetailMap.put("goodsName", goodsName);
			retDetailMap.put("goodsBarcode", goodsBarcode);
			retDetailMap.put("goodsInPrice", price);
			retDetailMap.put("goodsCount", count);
			retDetailMap.put("unitName", jo.getString("unitName"));
			retDetailMap.put("giftType", "1");
			retDetailList.add(retDetailMap);

		}
		
		retMap.put("totalCount", totalCount.setScale(2,BigDecimal.ROUND_HALF_UP));
		retMap.put("totalPrice", totalPrice.setScale(2,BigDecimal.ROUND_HALF_UP));
		retMap.put("remark", remark);
		retMap.put("staffId", staffId);
		
		//添加退款记录
		selfPurchaseDao.addNewReturnPurList(retMap);
		selfPurchaseDao.addNewReturnPurDetail(retDetailList);

		outStockParam.setGoodsList(outStockGoodsParamList);
		outStockParam.setGoodsBatchList(outStockGoodsBatchParamList);
		stockService.addOutStock(outStockParam);
		return pr;
	}
	
	public PurResult addSelfPurPay(String order_code,Double payMoney,
			Double surplusMoney,String staff_id,String shop_unique
			) {
		PurResult pr = new PurResult(1, "修改成功！");
		Map<String,Object> params = new HashMap<String,Object>();
		params.put("self_purchase_unique", order_code);
		params.put("need_topay", surplusMoney);//剩余未付金额
		params.put("pay_money", payMoney);//本次支付金额
		params.put("staff_id", staff_id);//操作员工编号
		params.put("source_type", 1);//操作终端：1商家后台 2Android 3ios
		params.put("network_ip", IPGet.ExtranetIP);//操作网络ip
		
		//需要验证本次的金额是否超额，足额，超额提示超额，足额把订单确认
		params.put("shop_unique", shop_unique);
		Map<String,Object> purMap = selfPurchaseDao.getSelfPurchase(params);
		if(null == purMap || purMap.isEmpty()) {
			pr.setStatus(0);
			pr.setMsg("没有满足条件的订单编号");
			return pr;
		}
		BigDecimal arrears_price = new BigDecimal(purMap.get("arrears_price").toString());
		
		BigDecimal payMoneyB = new BigDecimal(payMoney);
		BigDecimal surplusMoneyB = new BigDecimal(surplusMoney);
		
		if(payMoneyB.add(surplusMoneyB).setScale(2,BigDecimal.ROUND_HALF_UP).compareTo(arrears_price) != 0) {
			pr.setStatus(0);
			pr.setMsg("本次还款金额和待还款金额与订单待支付金额不匹配");
			return pr;
		}
		
		if(payMoneyB.compareTo(arrears_price) > 0) {
			pr.setStatus(0);
			pr.setMsg("本次还款金额大于待还款金额");
			return pr;
		}
		
		if(surplusMoneyB.compareTo(arrears_price) > 0) {
			pr.setStatus(0);
			pr.setMsg("本次还款金额不正确");
			return pr;
		}
		
		if(payMoney.compareTo(0.0) <= 0) {
			pr.setStatus(0);
			pr.setMsg("本次还款金额必须大于0");
			return pr;
		}
		
		
		Map<String,Object> upSelfMap = new HashMap<String,Object>();
		upSelfMap.put("self_purchase_unique", order_code);
		upSelfMap.put("shop_unique", shop_unique);
		upSelfMap.put("update_staff_id", staff_id);
		upSelfMap.put("arrears_price", surplusMoney);
		
		if(payMoneyB.compareTo(arrears_price) == 0) {
			//还款完成，将订单状态更新为已还款完成，并将待还款额设置为0
			upSelfMap.put("pay_status", "0");
		}else {
			//还款未完成，将还款额更新
			upSelfMap.put("pay_status", "1");
		}
		selfPurchaseDao.updateSelfPurchase(upSelfMap);
		
		selfPurchaseDao.insertSelfPurchasePay(params);
		return pr;
	}
	
	public List<Map<String,Object>> querySaleList(){
		
		Map<String,Object> map = new HashMap<String,Object>();
		map.put("startTime", "2021-08-17");
		map.put("endTime", "2021-09-02");
		List<Map<String,Object>> list = selfPurchaseDao.querySaleList(map);
		
		map.put("startTime", map.get("endTime"));
		map.put("endTime", "2021-09-17");
		List<Map<String,Object>> list1 = selfPurchaseDao.querySaleList(map);
		
		map.put("startTime", map.get("endTime"));
		map.put("endTime", "2021-10-02");
		List<Map<String,Object>> list2 = selfPurchaseDao.querySaleList(map);
		
		map.put("startTime", map.get("endTime"));
		map.put("endTime", "2021-10-17");
		List<Map<String,Object>> list3 = selfPurchaseDao.querySaleList(map);
		
		map.put("startTime", map.get("endTime"));
		map.put("endTime", "2021-11-02");
		List<Map<String,Object>> list4 = selfPurchaseDao.querySaleList(map);
		
		map.put("startTime", map.get("endTime"));
		map.put("endTime", "2021-11-17");
		List<Map<String,Object>> list5 = selfPurchaseDao.querySaleList(map);
		
		for(Map<String, Object> m : list) {
			for(Map<String,Object> n : list1) {
				if(m.get("shop_unique").toString().equals(n.get("shop_unique").toString())) {
					BigDecimal balance = new BigDecimal(m.get("rec").toString());
					BigDecimal b = new BigDecimal(n.get("rec").toString());
					m.put("rec", balance.add(b));
					break;
				}
			}
		}
		
		for(Map<String, Object> m : list) {
			for(Map<String,Object> n : list1) {
				if(m.get("shop_unique").toString().equals(n.get("shop_unique").toString())) {
					BigDecimal balance = new BigDecimal(m.get("rec").toString());
					BigDecimal b = new BigDecimal(n.get("rec").toString());
					m.put("rec", balance.add(b));
					break;
				}
			}
		}
		
		for(Map<String, Object> m : list) {
			for(Map<String,Object> n : list2) {
				if(m.get("shop_unique").toString().equals(n.get("shop_unique").toString())) {
					BigDecimal balance = new BigDecimal(m.get("rec").toString());
					BigDecimal b = new BigDecimal(n.get("rec").toString());
					m.put("rec", balance.add(b));
					break;
				}
			}
		}
		
		for(Map<String, Object> m : list) {
			for(Map<String,Object> n : list3) {
				if(m.get("shop_unique").toString().equals(n.get("shop_unique").toString())) {
					BigDecimal balance = new BigDecimal(m.get("rec").toString());
					BigDecimal b = new BigDecimal(n.get("rec").toString());
					m.put("rec", balance.add(b));
					break;
				}
			}
		}
		
		for(Map<String, Object> m : list) {
			for(Map<String,Object> n : list4) {
				if(m.get("shop_unique").toString().equals(n.get("shop_unique").toString())) {
					BigDecimal balance = new BigDecimal(m.get("rec").toString());
					BigDecimal b = new BigDecimal(n.get("rec").toString());
					m.put("rec", balance.add(b));
					break;
				}
			}
		}
		
		for(Map<String, Object> m : list) {
			for(Map<String,Object> n : list5) {
				if(m.get("shop_unique").toString().equals(n.get("shop_unique").toString())) {
					BigDecimal balance = new BigDecimal(m.get("rec").toString());
					BigDecimal b = new BigDecimal(n.get("rec").toString());
					m.put("rec", balance.add(b));
					break;
				}
			}
		}
		
		for(Map<String,Object> m : list) {
			System.out.print(m.get("shop_unique") + ",");
			System.out.print(m.get("COUNT(*)") + ",");
			System.out.println(m.get("rec"));
		}
		
		return list;
	}
	
	@Override
	public PurResult getSelfPurchaseList(Map<String, Object> params) {
		PurResult purResult=new PurResult();
		try {
			//获取列表
			List<Map<String,Object>> list = selfPurchaseDao.getSelfPurchaseList(params);
			//获取总条数
			Integer count = selfPurchaseDao.getSelfPurchaseListCount(params);
			purResult.setData(list);
			purResult.setCountNum(count);
			purResult.setStatus(1);
			purResult.setMsg("成功");
		} catch (Exception e) {
			e.printStackTrace();
			purResult.setStatus(0);
			purResult.setMsg("系统错误");
		}
		return purResult;
	}

	/**
	 * 查询订单详情
	 * @param self_purchase_unique
	 * @return
	 */
	public PurResult getSelfPurchaseReturnDetailList(String self_purchase_unique,String shop_unique) {
		PurResult pr = new PurResult(1, "查询成功!");
		try {
			Map<String,Object> params = new HashMap<String,Object>();
			params.put("self_purchase_unique", self_purchase_unique);
			params.put("shop_unique", shop_unique);
			params.put("shopUnique", shop_unique);
			//获取进货单信息
			Map<String,Object> selfPurchase = selfPurchaseDao.getSelfPurchase(params);
			if(null != selfPurchase && !selfPurchase.isEmpty()) {
				List<Map<String,Object>> detailList = selfPurchaseDao.getSelfPurchaseReturnDetailList(params);
				selfPurchase.put("detailList", detailList);
			}
			
			pr.setData(selfPurchase);
		}catch (Exception e) {
			e.printStackTrace();
			pr.setStatus(0);
			pr.setMsg("系统错误");
		}
		
		return pr;
	}
	
	/**
	 * 查询退货订单详情
	 * @param selfPurchaseRetUnique
	 * @return
	 */
	public PurResult getSelfRetPurchase(String selfPurchaseRetUnique) {
		PurResult pr = new PurResult(1, "查询成功!");
		try {
			Map<String,Object> map = new HashMap<String,Object>();
			map.put("selfPurchaseRetUnique", selfPurchaseRetUnique);
			
			Map<String,Object> retOrderDetail = selfPurchaseDao.getSelfRetPurchase(map);
			if(null == retOrderDetail || retOrderDetail.isEmpty()) {
				pr.setStatus(0);
				pr.setMsg("订单信息不存在!");
				return pr;
			}
			
			List<Map<String,Object>> retOrderDetailList = selfPurchaseDao.getSelfRetPurchaseDetail(map);
			
			retOrderDetail.put("list", retOrderDetailList);
			
			Subject subject = SecurityUtils.getSubject();
			Session session = subject.getSession();
			Staff staff = (Staff) session.getAttribute("staff");
			
			retOrderDetail.put("shopName", staff.getShop_name());
			
			pr.setData(retOrderDetail);
		}catch (Exception e) {
			e.printStackTrace();
			pr.setStatus(0);
			pr.setMsg("系统错误!");
		}
		
		return pr;
	}

	@Override
	public PurResult getSelfPurchase(Map<String, Object> params) {
		PurResult purResult=new PurResult();
		try {
			//获取进货单信息
			Map<String,Object> selfPurchase = selfPurchaseDao.getSelfPurchase(params);
			//获取进货单详情列表
			List<Map<String,Object>> detailList = selfPurchaseDao.getSelfPurchaseDetailList(params);
			//获取进货单支付记录列表
			List<Map<String,Object>> payList = selfPurchaseDao.getSelfPurchasePayList(params);
			selfPurchase.put("detailList", detailList);
			selfPurchase.put("payList", payList);
			purResult.setData(selfPurchase);
			purResult.setStatus(1);
			purResult.setMsg("成功");
		} catch (Exception e) {
			e.printStackTrace();
			purResult.setStatus(0);
			purResult.setMsg("系统错误");
		}
		return purResult;
	}

	@Override
	@Transactional(rollbackFor = {Exception.class})
	public PurResult insertSelfPurchase(PurchaseGoodsAddParams addParams) {
		PurResult purResult = new PurResult(PurResult.FAIL, "操作失败");
		try {
			String self_purchase_unique = createOrderNum();//自采购进货单唯一标示
			Date currentDate = DateUtil.date();
			//添加进货单信息
			Map<String, Object> params = new HashMap<>();
			params.put("self_purchase_unique", self_purchase_unique);
			params.put("shop_unique", addParams.getShopUnique());
			params.put("supplier_unique", addParams.getSupplierUnique());
			params.put("total_count", addParams.getTotalCount());
			params.put("total_price", addParams.getTotalPrice());
			params.put("arrears_price", addParams.getArrearsPrice());
			params.put("pay_status", addParams.getPayStatus());
			params.put("purchase_status", addParams.getPurchaseStatus());
			params.put("remark", addParams.getRemark());
			params.put("create_time", currentDate);
			params.put("update_time", currentDate);
			params.put("create_staff_id", addParams.getCreateStaffId());
			params.put("update_staff_id", addParams.getUpdateStaffId());
			params.put("self_purchase_unique", self_purchase_unique);
			int n = selfPurchaseDao.insertSelfPurchase(params);
			if (n > 0) {
				//添加出入库记录
				InStockParam inStockParam = new InStockParam();
				inStockParam.setStockResource(StockReourceEnum.BY_ORDER.getValue());
				inStockParam.setShopUnique(addParams.getShopUnique());
				inStockParam.setUserId(addParams.getCreateStaffId());
				inStockParam.setListUnique(UniqueUtils.createListUnique());
				inStockParam.setSourceUnique(self_purchase_unique);
				inStockParam.setSupplierUnique(String.valueOf(addParams.getSupplierUnique()));
				List<InStockGoodsParam> stockDetailList = new ArrayList<>();
				//添加进货单详情
				List<PurchaseGoodsDetailParam> detailList = addParams.getGoodsDetails();
				List<SelfPurchaseDetail> selfPurchaseDetailList = new ArrayList<>();
				for (PurchaseGoodsDetailParam detailParam : detailList) {
					SelfPurchaseDetail detail = new SelfPurchaseDetail();
					BeanUtil.copyProperties(detailParam, detail);
					detail.setSelfPurchaseUnique(self_purchase_unique);
					selfPurchaseDetailList.add(detail);

					InStockGoodsParam inStockGoodsParam = new InStockGoodsParam();
					inStockGoodsParam.setGoodsName(detailParam.getGoodsName());
					inStockGoodsParam.setGoodsBarcode(detailParam.getGoodsBarcode());
					inStockGoodsParam.setGoodsCount(detailParam.getGoodsCount());
					inStockGoodsParam.setStockPrice(detailParam.getGoodsInPrice());
					inStockGoodsParam.setGoodsProd(detailParam.getGoodsProd());
					inStockGoodsParam.setGoodsLife(detailParam.getGoodsLife());
					inStockGoodsParam.setGoodsExp(detailParam.getGoodsExp());
					stockDetailList.add(inStockGoodsParam);
				}
				selfPurchaseDetailMapper.insertBatch(selfPurchaseDetailList);
				inStockParam.setGoodsList(stockDetailList);
				//添加支付记录
				Map<String ,Object> payParams = new HashMap<String, Object>();
				payParams.put("self_purchase_unique", self_purchase_unique);
				payParams.put("need_topay", addParams.getArrearsPrice());//剩余未付金额
				payParams.put("pay_money", NumberUtil.sub(addParams.getTotalPrice(), addParams.getArrearsPrice()));//本次支付金额
				payParams.put("staff_id", addParams.getCreateStaffId());//操作员工编号
				payParams.put("source_type", 1);//操作终端：1商家后台 2Android 3ios
				payParams.put("network_ip", addParams.getNetworkIp());//操作网络ip
				selfPurchaseDao.insertSelfPurchasePay(payParams);
				purResult = stockService.addIntoStock(inStockParam);
			}
		} catch (Exception e) {
			e.printStackTrace();
			purResult.setStatus(0);
			purResult.setMsg("系统错误");
		}
		return purResult;
	}

	@Override
	@Transactional
	public PurResult insertSelfPurchasePay(Map<String, Object> params) {
		PurResult purResult=new PurResult();
		try {
			//获取进货单信息
			Map<String,Object> selfPurchase = selfPurchaseDao.getSelfPurchase(params);
			
			//剩余未付金额=欠款-当前支付
			Double need_topay = Double.valueOf(MUtil.strObject(selfPurchase.get("arrears_price"))) - Double.valueOf(MUtil.strObject(params.get("pay_money")));
			
			//添加支付记录
			params.put("need_topay", need_topay);//剩余未付金额
			selfPurchaseDao.insertSelfPurchasePay(params);
			
			//支付状态：0全部支付 1欠款
			Integer pay_status = 0;
			if(need_topay > 0){
				pay_status = 1;
			}
			
			//修改进货单信息
			Map<String ,Object> updateParams = new HashMap<String, Object>();
			updateParams.put("self_purchase_unique", params.get("self_purchase_unique"));
			updateParams.put("shop_unique", params.get("shop_unique"));
			updateParams.put("arrears_price", need_topay);
			updateParams.put("pay_status", pay_status);
			updateParams.put("update_staff_id", params.get("staff_id"));
			selfPurchaseDao.updateSelfPurchase(updateParams);
			
			purResult.setStatus(1);
			purResult.setMsg("成功");
		} catch (Exception e) {
			e.printStackTrace();
			purResult.setStatus(0);
			purResult.setMsg("系统错误");
		}
		return purResult;
	}


	@Override
	@Transactional
	public PurResult updateSelfPurchase(Map<String, Object> params) {
		PurResult purResult=new PurResult();
		try {
			selfPurchaseDao.updateSelfPurchase(params);
			Integer purchase_status = Integer.parseInt(MUtil.strObject(params.get("purchase_status")));
			
			if(purchase_status == 2){//取消订单
				//获取订单详情列表
				List<Map<String ,Object>> detailList = selfPurchaseDao.getSelfPurchaseDetailList(params);
				for(int i=0;i<detailList.size();i++){
					//判断是否有此商品
					HashMap<String, Object> shopParams = new HashMap<String, Object>();
					shopParams.put("shop_unique", params.get("shop_unique"));
					shopParams.put("goods_barcode", detailList.get(i).get("goods_barcode"));
					Map<String, Object> goodsInfo = supplierDao.getGoodsInfo(shopParams);
					Double goods_count = Double.valueOf(MUtil.strObject(detailList.get(i).get("goods_count")));
					Double stock_count = 0.00;
					if(goodsInfo != null){
						//更新商品数量和采购价
						Double goodsCount= ((BigDecimal)goodsInfo.get("goods_count")).doubleValue();
						stock_count = goodsCount - goods_count;
						goodsInfo.put("goods_count", stock_count);
						goodsInfo.put("goods_in_price", Double.valueOf(MUtil.strObject(detailList.get(i).get("goods_in_price"))));
						supplierDao.updateGoodsCount(goodsInfo);
						
						//添加出库记录
						Map<String,Object> smap=new HashMap<String, Object>();
						smap.put("goods_barcode", detailList.get(i).get("goods_barcode"));
						smap.put("goods_count",goods_count);
						smap.put("stock_count",stock_count);
						smap.put("stock_type",2);
						smap.put("shop_unique",params.get("shop_unique"));
						smap.put("stock_resource", 3);
						smap.put("stock_price", Double.valueOf(MUtil.strObject(detailList.get(i).get("goods_in_price"))));
						smap.put("stock_origin", params.get("stock_origin"));
						smap.put("staff_id", params.get("update_staff_id"));
						supplierDao.addShopStock(smap);
					}
				}
			}
			
			purResult.setStatus(1);
			purResult.setMsg("成功");
		} catch (Exception e) {
			e.printStackTrace();
			purResult.setStatus(0);
			purResult.setMsg("系统错误");
		}
		return purResult;
	}


	@Override
	@Transactional
	public PurResult deleteSelfPurchase(Map<String, Object> params) {
		PurResult purResult=new PurResult();
		try {
			selfPurchaseDao.deleteSelfPurchase(params);
			purResult.setStatus(1);
			purResult.setMsg("成功");
		} catch (Exception e) {
			e.printStackTrace();
			purResult.setStatus(0);
			purResult.setMsg("系统错误");
		}
		return purResult;
	}

	
	//创建编号
    private static String createOrderNum(){
    	SimpleDateFormat df = new SimpleDateFormat("yyyyMMddHHmmss");//设置日期格式
    	String datestr = df.format(new Date());
    	String sj_num = ""+(int)(1+Math.random()*9)+(int)(1+Math.random()*9)+(int)(1+Math.random()*9)+(int)(1+Math.random()*9);
    	String order_num = "SP" + datestr + sj_num;
    	return order_num;
    }
}
