package org.haier.shop.service;

import java.math.BigDecimal;
import java.util.Map;

import org.haier.shop.util.PurResult;

public interface SupplierService {
	
	public PurResult deleteAllGoodsSupplier(String shopUnique,String supplierUnique);

	PurResult querySupplierList(String shop_unique, Integer pageNum, Integer pageSize,String supMsg);

	PurResult getMapCenter(String shop_unique);

	PurResult saveSupplier(String shop_unique, String shop_name, String shop_address_detail, String shop_phone,
			String manager_name, BigDecimal shop_latitude, BigDecimal shop_longitude, String province, String city,
			String district, String supplier_kind_id,String settlementBank,
			String settlementName,
			String settlementCard,String supplierRemark,String supplierUnique);

	Map<String, Object> getSupplierById(Integer supplier_id);

	PurResult editSupplier(String supplier_unique, String shop_name, String shop_address_detail, String shop_phone,
			String manager_name, BigDecimal shop_latitude, BigDecimal shop_longitude, String province, String city,
			String district, String shop_unique, String supplier_kind_id,String settlementBank,
			String settlementName,
			String settlementCard,
			String supplierRemark);

	PurResult deleteP(Integer supplier_id);

	PurResult querySupplierByContries(String area_dict_num);

	PurResult querySupplierByName(String searchShopName);

	PurResult submitOrder(String supplier_unique, String shop_unique, Integer purchase_list_sum,
			BigDecimal purchase_list_total, String detailJson, String login_shop_unique);
	
	/**
	 * 供货商供应商品界面-分页数量查询
	 */
	public PurResult querySupGoodsPages(Map<String,Object> map);
	/**
	 * 供应商供应商品界面-分页查询
	 * @param map
	 * @return
	 */
	public PurResult querySupGoodsByPage(Map<String,Object> map);
	
	/**
	 * 清除店内商品的供货商信息
	 * @param map
	 * @return
	 */
	public PurResult clearSupGoodsMsgPage(Map<String,Object> map);
	
	
	/**
	 * 商品进价信息保存
	 * @param map
	 * @return
	 */
	public PurResult saveGoodsInPrice(Map<String,Object> map);

	PurResult querySupplierKindList(String shop_unique, int page, int pageSize, String supMsg);

	PurResult saveSupplierKind(String shop_unique, String supplier_kind_name);

	PurResult deleteSupplierKind(Integer id);

	Map<String, Object> getSupplierKindById(Integer id);

	PurResult editSupplierKind(String id, String supplier_kind_name);

	PurResult querySupplierKindByShopUnique(String shop_unique);
}
