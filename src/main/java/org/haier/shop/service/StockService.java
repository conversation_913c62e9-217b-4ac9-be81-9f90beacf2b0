package org.haier.shop.service;

import java.util.List;
import java.util.Map;

import org.haier.shop.params.goods.InStockParam;
import org.haier.shop.params.goods.OutStockParam;
import org.haier.shop.params.stock.ShopOutStockAuditParam;
import org.haier.shop.params.stock.ShopStockDetailQueryParam;
import org.haier.shop.util.PurResult;
import org.haier.shop.util.ShopsResult;

import javax.servlet.http.HttpServletRequest;


public interface StockService {

	public PurResult queryShopStockRecordList(Map<String, Object> map);
	public PurResult queryShopStockRecordDetail(Long shopUnique, String fail_id);
	public PurResult addIntoStock(InStockParam inStockParam);
	public PurResult listPage(ShopStockDetailQueryParam params);
	public ShopsResult deleteStock(Map<String, Object> map);
	public ShopsResult queryStockDetail(Map<String, Object> map);
	public ShopsResult addAuditStock(String list_unique, String shop_unique, String audit_status,
                                     String stock_type_code, String staff_id, String audit_content, HttpServletRequest request);
	public PurResult editIntoStock(InStockParam inStockParam);
	public PurResult addOutStock(OutStockParam outStockParam);

	public PurResult editOutStock(OutStockParam outStockParam);
	public PurResult queryGoodsModifyPriceList(Map<String, Object> params);
	
	public List<Map<String, Object>> getGoodsModifyPriceList(Map<String, Object> params);
	public PurResult addGoodsModify(String shop_list, String detailJson, String user_id, String shop_unique);
	public ShopsResult deleteGoodsModify(Map<String, Object> map);
	public ShopsResult queryGoodsModifyDetail(Map<String, Object> map);
	public ShopsResult addAuditGoodsModify(String list_unique, String audit_status, String user_id, HttpServletRequest request);
	public PurResult updateGoodsModify(String shop_list, String detailJson, String user_id, String shop_unique, String list_unique);
	public PurResult queryGoodsModifyInPriceList(Map<String, Object> params);
	public PurResult addGoodsModifyInPrice(String shop_list, String detailJson, String user_id, String shop_unique);
	public ShopsResult addAuditGoodsModifyInPrice(String list_unique, String audit_status, String user_id, HttpServletRequest request);
	public PurResult updateGoodsModifyInPrice(String shop_list, String detailJson, String user_id, String shop_unique,
			String list_unique);

    PurResult outAuditStock(ShopOutStockAuditParam auditParam);
}
