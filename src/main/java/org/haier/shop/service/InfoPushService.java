package org.haier.shop.service;

import java.util.Map;

import javax.servlet.http.HttpServletRequest;

import org.haier.shop.util.PurResult;

public interface InfoPushService {
	
	/**
	 * 查询列表
	 * @return
	 */
	public PurResult queryInfoList(Map<String,Object> params);
	
	/**
	 * 添加
	 * @return
	 */
	public PurResult addInfo(HttpServletRequest request);
	
	/**
	 * 获取详情
	 * @return
	 */
	public Map<String ,Object> queryInfoDetail(String info_id);
	
	/**
	 * 修改
	 * @return
	 */
	public PurResult updateInfo(HttpServletRequest request);
	
	/**
	 * 删除广告
	 * @return
	 */
	public PurResult deleteInfo(String info_id);
	
	/**
	 * 发送消息推送
	 * @return
	 */
	public PurResult sendInfo(String info_id);
	
	/**
	 * 查询会员消息列表
	 * @return
	 */
	public PurResult getInfoPushMemberList(Map<String,Object> params);
	
	/**
	 * 查询会员消息详情
	 * @return
	 */
	public PurResult getInfoPushMember(String member_info_id);
}
