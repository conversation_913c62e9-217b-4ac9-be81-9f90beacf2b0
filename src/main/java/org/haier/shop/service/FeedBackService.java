package org.haier.shop.service;

import java.util.List;
import java.util.Map;

import javax.servlet.http.HttpServletRequest;

import org.haier.shop.util.PurResult;
import org.haier.shop.util.ShopsResult;

public interface FeedBackService {

	PurResult queryFeedBackList(String orderMessage, String startTime, String endTime, String feed_back_source,
			String feed_back_type, String feed_back_status, Integer pageNum, Integer pageSize);

	PurResult targetFeedBack(Integer feed_back_id, Integer feed_back_target_status);

	Map<String ,Object> queryFeedBackDetail(String feed_back_id);
	List<Map<String ,Object>> queryFeedBackImageList(String feed_back_id);

	PurResult saveChuLi(String feed_back_id, String feed_back_remark);

	PurResult queryFeedBackPhoneList(String orderMessage, Integer pageNum, Integer pageSize);

	PurResult addFeedBackPhone(String feed_back_name, String feed_back_phone, String feed_back_type,
			String feed_back_flag);

	Map<String, Object> getFeedBackPhoneInfo(String feed_back_phone_id);

	PurResult editFeedBackPhone(String feed_back_phone_id, String feed_back_name, String feed_back_phone,
			String feed_back_type, String feed_back_flag);

	PurResult deleteFeedBackPhone(String feed_back_phone_id);

	PurResult queryShopQualificationList(String orderMessage, Integer pageNum, Integer pageSize);

	PurResult queryShopQualificationDetail(String shop_qualification_id);

	PurResult queryShopExamineList(String orderMessage, String examinestatus, Integer pageNum, Integer pageSize,String area_dict_num,String shop_type);

	PurResult queryShopExamineDetail(String shop_unique, String type);

	PurResult saveShopExamine(String shop_unique, String type, String examinestatus, String examinestatus_reason,String pay_info_id,String examine_status,String userPhone,HttpServletRequest request);

	PurResult queryGoodsKindImageList(String orderMessage, Integer pageNum, Integer pageSize);

	Map<String, Object> queryGoodsKindImageDetail(String goods_kind_unique);

	ShopsResult editGoodsKindImage(HttpServletRequest request, String goods_kind_unique);

	


}
