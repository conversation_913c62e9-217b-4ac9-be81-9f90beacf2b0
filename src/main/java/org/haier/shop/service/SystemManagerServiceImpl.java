package org.haier.shop.service;

import java.io.InputStream;
import java.util.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;

import org.haier.shop.config.SysConfig;
import org.haier.shop.dao.SystemManagerDao;
import org.haier.shop.dao.UtilDao;
import org.haier.shop.util.*;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

@Service
public class SystemManagerServiceImpl implements SystemManagerService{
	@Resource
	private SystemManagerDao sysDao;
	@Resource
	public UtilDao utilDao;


	/**
	 * 向收银设备发送mqtt指令
	 * @param shopUnique 店铺编号
	 * @param macId 收银机macId
	 * @param dayCount
	 * @return
	 */
	public ShopsResult sendUpdloadCmd(String shopUnique, String macId, Integer dayCount, String startTime, String endTime){
		ShopsResult shopsResult = new ShopsResult(1,"发成成功！");

		//向收银设备发送mqtt指令，新旧服务器都要发
		Map<String, Object> mqttMap = new HashMap<>();
		mqttMap.put("shopUnique", shopUnique);
		mqttMap.put("macId", macId);

		Map<String, Object> dataMap = new HashMap<>();
		Map<String, Object> msgMap = new HashMap<>();

		dataMap.put("shopUnique", shopUnique);
		dataMap.put("macId", macId);
		dataMap.put("dayCount", dayCount == null ? 3 : dayCount);
		dataMap.put("startTime", startTime);
		dataMap.put("endTime", endTime);

		List<Map<String, Object>> dataList = new ArrayList<>();
		dataList.add(dataMap);

		msgMap.put("ctrl", "msg_log_upload");
		msgMap.put("status", 200);
		msgMap.put("msg", "请上传日志！");
		msgMap.put("data", dataList);
		msgMap.put("count", dayCount == null ? 3 : dayCount);

		mqttMap.put("msg", net.sf.json.JSONObject.fromObject(msgMap).toString());

		HttpsUtil.doPost(SysConfig.publicMqttUrl, mqttMap);

		try{
			//向新的服务器发送mqttMsg
		}catch (Exception e) {
			e.printStackTrace();
		}

		return shopsResult;
	}
	/**
	 * 查询店铺信息
	 * @param page 页码
	 * @param limit 单页查询数量
	 * @param shopMsg 搜索框信息
	 * @return
	 */
	public ShopsResult queryShopMacList(Integer page,Integer limit,String shopMsg){
		ShopsResult shopsResult = new ShopsResult(1, "查询成功!");
		Map<String, Object> map = new HashMap<>();
		map.put("startNum", (page - 1) * limit);
		map.put("pageSize", limit);
		map.put("shopMsg", shopMsg);

		List<Map<String, Object>> list = utilDao.queryShopMacList(map);

		Integer count = utilDao.queryShopMacCount(map);

		shopsResult.setData(list);
		shopsResult.setCount(count);

		return shopsResult;
	}


	public ShopsResult uploadTest(HttpServletRequest request,MultipartFile file,String id,String value){
		ShopsResult shopsResult = new ShopsResult(1,"上传成功!");
		Map<String,Object> map = new HashMap<>();
		map.put("id",id);
		map.put("value",value);
		shopsResult.setData(map);

		return shopsResult;
	}
	/**
	 * 上传APP升级文件
	 * @param appId 1、APP商城；2、百货商家端；3、供货商（金圈云商）；4、
	 * @param appType
	 * @param updateVersion
	 * @param updateDes
	 * @param updateInstall
	 * @param appName
	 * @param updateLog
	 * @param request
	 * @return
	 */
	public ShopsResult uploadApp(Integer appId, Integer appType, String updateVersion,String updateDes,
								 Integer updateInstall,String appName,String updateLog,HttpServletRequest request,MultipartFile file,Integer code) {
		ShopsResult sr = new ShopsResult(1,"上传成功!");
		long beginTime = System.currentTimeMillis();
		System.out.println("======开始时间======="+beginTime);

		//接收文件上传到文件服务器
		SFTPUtil sftp = new SFTPUtil(FTPConfig.username, FTPConfig.password, FTPConfig.host, FTPConfig.port);
		sftp.login();
		try {

			if(file!=null){


			InputStream is = null;
			String filename=file.getOriginalFilename();
			String firstName=filename.substring(0,filename.lastIndexOf("."));
			String lastName = filename.substring(filename.lastIndexOf("."));


//	    	 is = new FileInputStream(isFile);
			is=file.getInputStream();
			String path="appUpdate/";
			List<Map<String,Object>> data=new ArrayList<>();//pc 更新详情
			if(appId==5) {
				path="shouyinji/";
				if(!filename.endsWith(".zip") && !filename.endsWith(".tar")&& !filename.endsWith(".7z")) {
					sr.setStatus(0);
					sr.setMsg("文件格式错误！【支持 zip,tar,7z】");
					return sr;
				}
//
			}
//			boolean flag=  sftp.upload(FTPConfig.file_path + path  +updateVersion  , filename, is);
//
			boolean flag=  sftp.uploadByProgress(FTPConfig.file_path + path  +updateVersion  , filename, file);


			if(flag) {

				if (appId == 5) {
					//解压
					flag = ExtractUtils.remoteZipToFile(FTPConfig.file_path + "shouyinji/" + updateVersion, filename, FTPConfig.file_path + "shouyinji/" + updateVersion+"/"+firstName);

					if (!flag) {
						sr.setStatus(0);
						sr.setMsg("解压失败,请联系客服！");
						return sr;
					}
					// 保存更新详情
					sftp.getSftpPathList(FTPConfig.file_path + "shouyinji/" + updateVersion + "/"+firstName, data, FTPConfig.file_path + "shouyinji/" + updateVersion + "/"+firstName);


				}

				Map<String, Object> map = new HashMap<String, Object>();
				map.put("appId", appId);
				map.put("appType", appType);
				map.put("appName", appName);
				map.put("updateVersion", updateVersion);
				map.put("updateDes", updateDes);
				map.put("updateInstall", updateInstall);
				map.put("updateId", sysDao.queryMaxId() + 1);
				map.put("updateLog", updateLog);
				map.put("updateUrl", FTPConfig.FILEURL + "file/" + path + updateVersion + "/" + filename);
				map.put("code", code);
				sysDao.cancelOldRecord(map);
				sysDao.addAppUpdateRecord(map);

				if (appId == 5 && data != null && data.size() > 0) {
					for (Map<String, Object> item : data) {
						item.put("app_update_id", map.get("app_update_id"));
					}
					utilDao.addUpdateFilesMessageNew(data);
				}
			}else {
				sr.setStatus(0);
				sr.setMsg("请选择文件！");
			}


			}else {
				sr.setStatus(0);
				sr.setMsg("上传失败,请稍后重试！");
			}

		}catch (Exception e) {
			e.printStackTrace();
		}
		long endTime = System.currentTimeMillis();
		System.out.println("==消耗时间=="+(endTime-beginTime)+"ms");
		//保存文件记录
		return sr;
	}
	public ShopsResult createMd5(Integer id , String url) {
		ShopsResult sr = new ShopsResult(1,"操作成功!");
		try {
		//接收文件上传到文件服务器
		SFTPUtil sftp = new SFTPUtil(FTPConfig.username, FTPConfig.password, FTPConfig.host, FTPConfig.port);
		sftp.login();
		String md5= sftp.getSftpPathMd5(url);
		Map<String,Object> data=new HashMap<>();
		data.put("id",id);
		data.put("md5",md5);
		utilDao.updateFilesMessage(data);
		sftp.logout();
		}catch (Exception e) {
			e.printStackTrace();
		}
		//保存文件记录
		return sr;
	}
	public ShopsResult appPacketUnzip(Integer id, Integer appType, String url) {
		ShopsResult sr = new ShopsResult(1,"上传成功!");
		long beginTime = System.currentTimeMillis();
		System.out.println("======开始时间======="+beginTime);

		//接收文件上传到文件服务器
		SFTPUtil sftp = new SFTPUtil(FTPConfig.username, FTPConfig.password, FTPConfig.host, FTPConfig.port);
		sftp.login();
//		try {
//
//
//					if(!url.endsWith(".zip") && !url.endsWith(".tar")&& !url.endsWith(".7z")) {
//						sr.setStatus(0);
//						sr.setMsg("文件格式错误！【支持 zip,tar,7z】");
//						return sr;
//					}
//					List<Map<String,Object>> data=new ArrayList<>();//pc 更新详情
//
//						//解压
//			        boolean flag = ExtractUtils.remoteZipToFile(FTPConfig.file_path + "shouyinji/" + updateVersion, "pos_version2" + lastName, FTPConfig.file_path + "shouyinji/" + updateVersion + "/pos_version2");
//
//					if (!flag) {
//							sr.setStatus(0);
//							sr.setMsg("解压失败,请联系客服！");
//							return sr;
//					}
//						// 保存更新详情
//						sftp.getSftpPathList(FTPConfig.file_path + "shouyinji/" + updateVersion + "/pos_version2", data, FTPConfig.file_path + "shouyinji/" + updateVersion + "/pos_version2");
//
//
//
//
//
//					if (data != null && data.size() > 0) {
//						for (Map<String, Object> item : data) {
//							item.put("app_update_id", id);
//						}
//						utilDao.addUpdateFilesMessageNew(data);
//					}
//
//
//
//		}catch (Exception e) {
//			e.printStackTrace();
//		}
//		long endTime = System.currentTimeMillis();
//		System.out.println("==消耗时间=="+(endTime-beginTime)+"ms");
		//保存文件记录
		return sr;
	}
	public ShopsResult uploadAppPacket(HttpServletRequest request,MultipartFile file) {
		ShopsResult sr = new ShopsResult(1,"上传成功!");
		long beginTime = System.currentTimeMillis();
		System.out.println("======开始时间======="+beginTime);
		try {
		//接收文件上传到文件服务器
		SFTPUtil sftp = new SFTPUtil(FTPConfig.username, FTPConfig.password, FTPConfig.host, FTPConfig.port);
		sftp.login();
			if(file!=null){
				InputStream is = null;
				String filename=file.getOriginalFilename();
				is=file.getInputStream();
				String path="appUpdate/";
				sftp.upload(FTPConfig.file_path + path    , filename, is);
				sr.setData(FTPConfig.FILEURL + "file/" + path + filename);

			}else {
				sr.setStatus(0);
				sr.setMsg("上传失败,请稍后重试！");
			}

		}catch (Exception e) {
			e.printStackTrace();
		}
		long endTime = System.currentTimeMillis();
		System.out.println("==消耗时间=="+(endTime-beginTime)/1000+"s");
		//保存文件记录
		return sr;
	}



	/**
	 * 分页查询APP升级记录
	 * @param page 页码
	 * @param pageSize 单页查询数量
	 * @param project_id 项目ID
	 * @param appId app类型：1、APP商城；2、APP商家端；3、供货商；4、物流端；5、PC客户端；6、一刻钟到家小程序；7、云商系统
	 * @param appType
	 * @param startTime
	 * @param endTime
	 * @return
	 */
	public ShopsResult queryAppVersionList(Integer page, Integer pageSize,Integer project_id, Integer appId, Integer appType, String startTime, String endTime) {
		ShopsResult sr = new ShopsResult(1, "查询成功!");
		Map<String,Object> map = new HashMap<String, Object>();
		map.put("appType", appType);
		map.put("appId", appId);
		map.put("startTime", startTime);
		map.put("endTime", endTime);
		map.put("startNum", (page - 1) * pageSize);
		map.put("pageSize", pageSize);
		map.put("project_id" , project_id);

		List<Map<String,Object>> list = sysDao.queryAppVersionList(map);
		Integer count = sysDao.queryAppVersionCount(map);
		sr.setData(list);
		sr.setCount(count);

		return sr;
	}


	/**
	 * 店铺免密信息统计（全）
	 * @return
	 */
	public ShopsResult queryShopsUserMsg(){
		ShopsResult sr=new ShopsResult(1, "查询成功！");
		List<Map<String,Object>> data=sysDao.queryShopsUserMsg();
		if(null==data){
			sr.setStatus(2);
			sr.setMsg("没有免密信息统计结果");
			return sr;
		}
		sr.setData(data);
		return sr;
	}

	/**
	 * 周期内免密使用情况
	 * @return
	 */
	public ShopsResult mianmiStatisQuery(Map<String,Object> map){
		ShopsResult sr=new ShopsResult(1,"查询成功！");
		Map<String,Object> data=sysDao.mianmiStatisQuery(map);
		data.put("mianmiUsesCount", sysDao.mianmiUsesCount(map));
		sr.setData(data);
		return sr;
	}

	/**
	 * 免密支付走势图数据列表查询
	 * @param map
	 * @return
	 */
	public ShopsResult mianmiStatisticsPic(Map<String,Object> map){
		ShopsResult sr=new ShopsResult(1, "查询成功！");
		List<Map<String,Object>> data=sysDao.mianmiStatisticsPic(map);
		List<Map<String,Object>> cord=sysDao.mianmiStatisticsShops(map);
		sr.setData(data);
		sr.setCord(cord);
		return sr;
	}

	public ShopsResult queryAppUploadDetail(String id ) {

		ShopsResult sr=new ShopsResult(1, "查询成功！");
		List<Map<String,Object>> list = sysDao.queryAppUploadDetail(id);
//		Integer count = sysDao.queryAppUploadDetailCount(map);
		sr.setData(list);
//		sr.setCount(count);

		return sr;
	}
}
