package org.haier.shop.service;

import cn.hutool.core.util.ObjectUtil;
import org.apache.commons.collections.MapUtils;
import org.haier.meituan.util.MUtil;
import org.haier.shop.dao.Goods_kindDao;
import org.haier.shop.dao.ImportDao;
import org.haier.shop.entity.AllKindsInShop;
import org.haier.shop.entity.GoodsGroups;
import org.haier.shop.redis.RedisCache;
import org.haier.shop.util.Load;
import org.haier.shop.util.MyException;
import org.haier.shop.util.PurResult;
import org.haier.shop.util.ShopsResult;
import org.haier.shop.util.mqtt.MqttxUtil;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.ObjectUtils;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Service("goodsKindService")
public class GoodsKindServiceImpl implements GoodsKindService{
	@Resource 
	private Goods_kindDao kindDao;
	@Resource
	private ImportDao importDao;

	/**
	 * 商品分类查询
	 * @param shop_unique 店铺编号
	 * @param goods_kind_parunique 商品分类大类编号
	 */
	public ShopsResult queryGoodsKinds(String shop_unique, String goods_kind_parunique) {
		ShopsResult shop=new ShopsResult();
		try {
		Map<String,Object> map=new HashMap<String,Object>();
		map.put("shop_unique", shop_unique);
		if(null==goods_kind_parunique){
			goods_kind_parunique="0";
		}
		map.put("goods_kind_parunique", goods_kind_parunique);
		//查询当前店铺使用的分类类型，1：默认分类；2：自定义分类
		Integer kindType=importDao.queryShopKindType(shop_unique);
		map.put("kindType", kindType);
		map.put("valid_type",1);
		List<Map<String,Object>> kindList= kindDao.queryGoodsKind(map);
		
		shop.setStatus(0);
		shop.setMsg("商品分类查询成功！");
		shop.setData(kindList);
		} catch (MyException e) {
			return new ShopsResult(e.status, e.msg);
		}
		return shop;
	}
	
	/**
	 * 商品分类查询（包含子类）
	 * @return
	 */
	public ShopsResult queryGoodsGroups(String shop_unique){
		ShopsResult shop=new ShopsResult();
		Map<String,Object> map=new HashMap<String, Object>();
		map.put("shop_unique", 0);
		List<GoodsGroups> result=kindDao.queryGoodsGroups(map);
		map.put("shop_unique", shop_unique);
		List<GoodsGroups> checkResult=kindDao.queryGoodsGroups(map);
		shop.setData(result);
		shop.setMsg("查询成功！");
		shop.setStatus(0);
		shop.setCord(checkResult);;
		return shop;
	}
	
	/**
	 * 删除已有商品分类信息，添加新选商品分类信息
	 * 
	 */
	@Transactional
	public ShopsResult modifyGoodsKinds(String shop_unique,String[] goodsKindsUniques){
		ShopsResult shop=new ShopsResult();
		List<String> uniques=new ArrayList<String>();
		Map<String,Object> map=new HashMap<String,Object>();
		
		
		map.put("shop_unique", shop_unique);
		map.put("uniques", uniques);
		int k=kindDao.deleteGoodsKinds(map);//删除已有商品分类信息
		if(goodsKindsUniques!=null){
			for(String unique:goodsKindsUniques){
				uniques.add(unique);
			}
			k=kindDao.addGoodsKinds(map);
		}
		if(k==0){
			shop.setStatus(1);
			shop.setMsg("删除失败！");
			return shop;
		}
		shop.setStatus(0);
		shop.setMsg("更新成功！");
		return shop;
	}
	/**
	 * 添加新的商品分类申请
	 * @param map
	 * @return
	 */
	public ShopsResult sureNewKind(Map<String,Object> map){
		ShopsResult sr=new ShopsResult();
		int k=kindDao.sureNewKind(map);
		if(k==0){
			sr.setStatus(2);
			sr.setMsg("添加申请失败");
			return sr;
		}
		sr.setStatus(1);
		sr.setMsg("添加申请成功！");
		return sr;
	}

	@Transactional
	public PurResult addGoodsKinds(String shop_unique, String goods_kind_unique) {
		//查询二级分类信息
				PurResult shop=new PurResult();
				Map<String,Object> map=new HashMap<String,Object>();
				map.put("shop_unique", 0);
				map.put("goods_kind_unique", goods_kind_unique);
				List<Map<String, Object>> data=kindDao.queryGoodsKindsByGoodsKindUnique(map);
				//查询一级分类
				Long goods_kind_parunique=(Long) data.get(0).get("goods_kind_parunique");
				map.put("goods_kind_unique", goods_kind_parunique);
				List<Map<String, Object>> parentKind=kindDao.queryGoodsKindsByGoodsKindUnique(map);
				//查询一级分类是否存在
				map.put("shop_unique", shop_unique);
				List<Map<String, Object>> parentKindList=kindDao.queryGoodsKindsByGoodsKindUnique(map);
				if(parentKindList!=null&&parentKindList.size()>0){
					
				}else{
					//添加一级分类
					Map<String,Object> params=new HashMap<String,Object>();
					params.put("shop_unique", shop_unique);
					params.put("goods_kind_unique", goods_kind_parunique);
					params.put("goods_kind_parunique", 0);
					params.put("goods_kind_name", parentKind.get(0).get("goods_kind_name"));
					kindDao.addNewGoodsKind(params);
				}
				//添加二级分类
				Map<String,Object> params=new HashMap<String,Object>();
				params.put("shop_unique", shop_unique);
				params.put("goods_kind_unique", goods_kind_unique);
				params.put("goods_kind_parunique",goods_kind_parunique);
				params.put("goods_kind_name", data.get(0).get("goods_kind_name"));
				kindDao.addNewGoodsKind(params);
				return shop;
	}

	@Transactional
	public PurResult deleteGoodsKind(String shop_unique, String goods_kind_unique) {
		PurResult shop=new PurResult();
		Map<String,Object> map=new HashMap<String,Object>();
		map.put("shop_unique", shop_unique);
		map.put("goods_kind_unique", goods_kind_unique);
		kindDao.deleteGoodsKind(map);
		return shop;
	}
	//查询所有分类
	public PurResult queryAllGoodsKinds(String shop_unique) {
		PurResult shop=new PurResult();
		Map<String,Object> map=new HashMap<String,Object>();
		map.put("shop_unique", shop_unique);
		List<Map<String, Object>> data=kindDao.queryAllGoodsKinds(map);//删除已有商品分类信息
		shop.setData(data);
		shop.setStatus(1);
		return shop;
	}
	
	/**
	 * 查询店内所有一级二级商品分类
	 * @return
	 */
	public ShopsResult queryAllKindsInShops(Map<String,Object> map){
		ShopsResult sr=new ShopsResult(1,"查询成功！");
		List<AllKindsInShop> data=kindDao.queryAllKindsInShops(map);
		sr.setData(data);
		return sr;
	}
	
	/**
	 * 查询商品分类信息
	 * @param map
	 * @return
	 * @throws MyException
	 */
	public ShopsResult queryGoodsKindsWithGoodsCount(Map<String,Object> map) throws MyException{
		ShopsResult sr=new ShopsResult(1,"查询成功！");
		try {
			map.put("url", Load.IMGDOMAINNAME);
			List<Map<String,Object>> list=kindDao.queryGoodsKindsWithGoodsCount(map);
			if(null==list||list.isEmpty()){
				sr.setStatus(2);
				sr.setMsg("没有满足条件的分类信息");
			}
			for (Map<String,Object> m : list) {
				String pic = (String) m.get("goodsKindIconPicture");
				m.put("goodsKindIconPicture", ObjectUtil.isEmpty(pic) ? "" : pic.replaceAll("\\\\","/"));
			}
			sr.setData(list);
			sr.setCount(kindDao.queryGoodsKindsCount(map));
		} catch (Exception e) {
			e.printStackTrace();
			throw new MyException(0,"系统异常，请联系客服");
		}
		return sr;
	}
	
	/**
	 * 更新商品分类信息
	 * @param map
	 * @return
	 * @throws MyException
	 */
	@Transactional
	public ShopsResult modifyGoodsKindMsg(Map<String,Object> map)throws MyException{
		ShopsResult sr=new ShopsResult(1,"更新成功！");
		try {
			//根据kindId查询分类是否存在
			Map<String,Object> queryMap = new HashMap<>();
			queryMap.put("goods_kind_id",map.get("kindId"));
			queryMap.put("kindType",2);
			List<Map<String, Object>> allGoodsKind = kindDao.getAllGoodsKind(queryMap);
			if (ObjectUtils.isEmpty(allGoodsKind)){
				sr.setStatus(0);
				sr.setMsg("该分类已删除或登录失效，请刷新重试");
				return sr;
			}
			Map<String, Object> oldKind = allGoodsKind.get(0);

			//如果是父分类需要上传图片
			if (oldKind.get("goods_kind_parunique").toString().equals("0") && ObjectUtil.isEmpty(map.get("iconId"))) {
				sr.setStatus(0);
				sr.setMsg("请选择分类图片");
				return sr;
			}

			//判断修改的分类名是否已存在
			Map<String,Object> queryAllMap = new HashMap<>();
			queryAllMap.put("shop_unique",oldKind.get("shop_unique"));
			queryAllMap.put("goods_kind_parunique",oldKind.get("goods_kind_parunique"));
			queryAllMap.put("kindType",2);
			queryAllMap.put("goods_kind_name",map.get("kindName"));
			List<Map<String, Object>> allKinds = kindDao.getAllGoodsKind(queryAllMap);
			if (allKinds.size() > 1 || (allKinds.size() == 1 && !MapUtils.getString(map,"kindId").equals(MapUtils.getString(allKinds.get(0),"goods_kind_id")))){
				sr.setStatus(0);
				sr.setMsg("该分类名称已存在，请勿重复添加！");
				return sr;
			}
			kindDao.modifyGoodsKindMsg(map);

			List<Map<String,Object>> mqttData=kindDao.queryMqttKind(map);
			if(mqttData!=null&&mqttData.get(0).containsKey("shop_unique"))
			{
				//20220909 新增MQTT -通知收银机有商品分类更新----start
				RedisCache rc = new RedisCache("");
				//查询shop_unique
				Object mac = rc.getObject("topic_"+mqttData.get(0).get("shop_unique"));
				@SuppressWarnings("unchecked")
				List<String> macIdList = (List<String>)mac;
				//2 MQTT 发送消息
				if(macIdList!=null)
				{
					for(String macid : macIdList)
					 {
						Map<String,Object> data=new HashMap<String,Object>();
						data.put("ctrl", "msg_goods_kind_update");
						data.put("ID", macid);
						data.put("status", 200);
						data.put("data",mqttData);
						data.put("count",1 );
						MqttxUtil.sendMapMsg(data, macid);
					 }
				}

			}

		}catch (MyException me){
			throw new MyException(0,me.getMsg());
		}catch (Exception e) {
			e.printStackTrace();
			throw new MyException(0,"系统异常，请联系客服");
		}
		return sr;
	}
	
	@Transactional
	public ShopsResult deleteGoodsKind(Map<String,Object> map) throws MyException{
		ShopsResult sr=new ShopsResult(1,"更新成功！");
		List<Map<String,Object>> list = new ArrayList<Map<String,Object>>();//用于分类信息的修改
		Map<String,Object> gmap=new HashMap<String,Object>();//用于商品信息的修改
		try {
			//若为删除，则删除时，应将其下小分类统一删除
			if(map.get("validType").toString().equals("2")){
				if(MUtil.strObject(map.get("groupUnique")).equals("0")){//更新的为大分类，需同时将小分类信息删除
					list = kindDao.queryKindMsg(map);
				}
			}else{//若为添加，添加小分类时，需将其大分类一起启用
				if(!MUtil.strObject(map.get("groupUnique")).equals("0")){
					list = kindDao.queryGroupMsg(map);
				}
			}
			list.add(map);
			
			for(int i=0;i<list.size();i++){
				Map<String,Object> kmap=list.get(i);
				kmap.put("validType", map.get("validType"));
			}
			if(map.get("validType").toString().equals("2")){//如果删除商品分类，需要将其下的商品信息更改为默认分类
				if(map.get("kindType").toString().equals("2")){//自定义分类
					gmap.put("kindUnique",Load.DEFAULTSELFKINDUNIQUE);//设置新的默认分类信息
				}else{//系统分类信息
					gmap.put("kindUique",Load.DEFAULTSYSKINDUNIQUE);//设置新的默认分类信息
				}
				gmap.put("shopUnique", map.get("shopUnique"));
				//将商品分类信息转换为默认条码分类
				gmap.put("list", list);
				kindDao.modifyGoodsKindForKindDelete(gmap);
			}
			
			//将分类设置为目标状态
			kindDao.modifyGoodsKindMsgs(list);
			
			//20220909 新增MQTT -通知收银机有商品分类更新----start
	 		RedisCache rc = new RedisCache("");
	 		Object mac = rc.getObject("topic_"+map.get("shopUnique"));
	 		@SuppressWarnings("unchecked")
			List<String> macIdList = (List<String>)mac;
	 		List<Map<String,Object>> mqttData=kindDao.queryMqttKind(map);
	       	//2 MQTT 发送消息
	 		if(macIdList!=null)
	 		{
	    	 for(String macid : macIdList)
	    	 {
	 	        Map<String,Object> data=new HashMap<String,Object>();
		        data.put("ctrl", "msg_goods_kind_delete");
		        data.put("ID", macid);
		        data.put("status", 200);
		        data.put("data",mqttData);
		        data.put("count",1 );
		        MqttxUtil.sendMapMsg(data, macid); 
	    	 }
	 		}
	    	 
		} catch (Exception e) {
			sr = new ShopsResult(0,"异常！");
			e.printStackTrace();
			throw new MyException(0,"系统异常，请联系客服");
		}
		return sr;
	}
	
	public ShopsResult queryAllGroupMsgByType(Map<String,Object> map) throws MyException{
		ShopsResult sr=new ShopsResult(1,"查询成功!");
		try {
			List<Map<String, Object>> list=kindDao.queryAllGroupMsgByType(map);
			if(null==list||list.isEmpty()){
				sr.setStatus(0);
				sr.setMsg("没有满足条件的结果");
			}else{
				sr.setData(list);
			}
		} catch (Exception e) {
			throw new MyException(0,"系统异常，请联系客服");
		}
		return sr;
	}
	
	/**
	 * 添加新的商品分类信息
	 * @param map
	 * @return
	 * @throws MyException 
	 */
	@Transactional
	public ShopsResult addNewGoodsKindLay(Map<String,Object> map) throws MyException{
		ShopsResult sr=new ShopsResult(1,"添加成功!");
		try{

			Map<String,Object> queryMap = new HashMap<>();
			queryMap.put("shop_unique",map.get("shopUnique"));
			queryMap.put("goods_kind_parunique",map.get("groupUnique"));
			queryMap.put("kindType",2);
			queryMap.put("goods_kind_name",map.get("kindName"));
			List<Map<String, Object>> allGoodsKind = kindDao.getAllGoodsKind(queryMap);
			if (!ObjectUtils.isEmpty(allGoodsKind)) throw new MyException(0,"该分类名称已存在，请勿重复添加！");

			//编写分类编号
			Long kindUnique=kindDao.queryNowMaxUnique(map);
			if(null==kindUnique&&map.get("groupUnique").equals("0")){
				kindUnique=100000l;
			}else if(null==kindUnique){
				kindUnique=Long.parseLong(map.get("groupUnique").toString())+1;
			}else if(null !=kindUnique && map.get("groupUnique").equals("0")){
				kindUnique=(long) Math.ceil(kindUnique*1.1);//todo
			}else{
				kindUnique+=1;
			}
			
			map.put("kindUnique", kindUnique);
			map.put("kindType", 2);
			map.put("goods_kind_icon_id",ObjectUtil.isEmpty(map.get("iconId")) ? -1 : map.get("iconId"));
			//将分类信息添加到数据库保存
			kindDao.addNewGoodsKindLay(map);
			
			//20220909 新增MQTT -通知收银机有商品分类更新----start
		 		RedisCache rc = new RedisCache("");
		 		Object mac = rc.getObject("topic_"+map.get("shopUnique"));
		 		@SuppressWarnings("unchecked")
				List<String> macIdList = (List<String>)mac;
		 		List<Map<String,Object>> mqttData=kindDao.queryMqttKind(map);
		       	//2 MQTT 发送消息
		 		if(macIdList!=null)
		 		{
		    	 for(String macid : macIdList)
		    	 {
		 	        Map<String,Object> data=new HashMap<String,Object>();
			        data.put("ctrl", "msg_goods_kind_add");//添加成功
			        data.put("ID", macid);
			        data.put("status", 200);
			        data.put("data",mqttData);
			        data.put("count",1 );
			        MqttxUtil.sendMapMsg(data, macid); 
		    	 }
		 		}
		    	 
		}catch (MyException me){
			throw new MyException(0,me.getMsg());
		}
		catch(Exception e){
			e.printStackTrace();
			throw new MyException(0,"添加失败！");
		}
		return sr;
	}
	
	/**
	 * 修改商品分类信息
	 * @param shopUnique
	 * @param kindType
	 * @return
	 */
	@Transactional
	public ShopsResult useCustomeKind(String shopUnique,Integer kindType) {
		ShopsResult sr=new ShopsResult(1, "修改成功！");
		//修改店铺分类使用信息
		Map<String,Object> map=new HashMap<String,Object>();
		map.put("shopUnique", shopUnique);
		map.put("kindType", kindType);
		//检测店铺当前分类是否更改，若不更改，直接返回
		Integer c=kindDao.getShopNowKindType(shopUnique);
		if(kindType==c){
			return sr;
		}
		int k = kindDao.updateShopsMessage(map);
		
		if(k>0){
			if(kindType==1){//使用系统分类
				/*
				 * 步骤1、查询所有商品在goods_dict中的分类信息，
				 * 步骤2、将店铺内所有商品分类信息更新
				 */
				List<Map<String,Object>> list=kindDao.queryGoodsDictKind(map);
				if(null!=list&&!list.isEmpty()){
					kindDao.rebackSystemKind(list);
				}
			}else if(kindType==2){
				/*
				 * 将店内所有商品分类信息更新为自定义默认分类
				 */
				map.put("kindUnique", Load.DEFAULTSELFKINDUNIQUE);
				kindDao.useCustomeKind(map);
			}
		}else{
			sr.setStatus(2);
			sr.setMsg("操作失败");
		}
		return sr;
	}
	
	/**
	 * 获取当前店铺的分类使用信息
	 * @param shopUnique
	 * @return
	 */
	public ShopsResult getShopNowKindType(String shopUnique) {
		ShopsResult sr = new ShopsResult(1, "查询成功！");
		if(null == shopUnique) {
			sr.setStatus(0);
			sr.setMsg("查询失败");
		}else {
			Integer c=kindDao.getShopNowKindType(shopUnique);
			sr.setData(c);
		}
		return sr;
	}
}
