package org.haier.shop.service;

import cn.hutool.core.util.ObjectUtil;
import org.haier.meituan.util.MUtil;
import org.haier.shop.dao.ShopDao;
import org.haier.shop.dao.ShopStaffDao;
import org.haier.shop.dao.SupplierDao;
import org.haier.shop.entity.Staff;
import org.haier.shop.params.shopStaff.ShopIoBoundInspectEditParams;
import org.haier.shop.result.shop.ShopConfigQueryResult;
import org.haier.shop.util.PurResult;
import org.haier.shop.util.ShopsResult;
import org.haier.shop.util.ShopsUtil;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpSession;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Service
public class StaffServiceImpl implements StaffService {
    @Resource
    private ShopStaffDao staffDao;
    @Resource
    private ShopDao shopDao;

    @Resource
    private SupplierDao supplierDao;

    /**
     * 管理员登录
     *
     * @param map
     * @return
     */
    public ShopsResult staffLoginByAccountPwd(HttpServletRequest request, Map<String, Object> map) {
        ShopsResult sr = new ShopsResult();
        Map<String, Object> data = staffDao.staffLoginByAccountPwd(map);
        if (null == data || data.isEmpty()) {
            sr.setStatus(1);
            sr.setMsg("账号或密码错误!");
            return sr;
        } else if ((Integer) data.get("examinestatus") == 2) {
            sr.setStatus(1);
            sr.setMsg("该店铺正在审核中");
            return sr;
        } else if ((Integer) data.get("examinestatus") == 3) {
            sr.setStatus(1);
            sr.setMsg("审核未通过!  【" + data.get("examinestatus_reason") + "】");
            return sr;
        }
        HttpSession session = request.getSession(true);
        session.setAttribute("shop_unique", data.get("shopUnique"));
        sr.setStatus(0);
        sr.setMsg("登录成功！");
        sr.setData(data);
        return sr;
    }


    /**
     * 根据管理员信息，查询管理员管理的店铺信息
     *
     * @param map
     * @return
     */
    public ShopsResult queryShopsByManager(Map<String, Object> map) {
        ShopsResult sr = new ShopsResult();
        List<Map<String, Object>> data = staffDao.queryShopsByManager(map);
        if (null == data || data.isEmpty()) {
            sr.setStatus(1);
            sr.setMsg("账号或密码错误!");
            return sr;
        }
        sr.setData(data);
        sr.setStatus(0);
        sr.setMsg("查询成功！");
        return sr;
    }


    /**
     * 查询管理员旗下所有店铺及其管理员权限信息
     *
     * @param map
     * @return
     */
    public ShopsResult queryShopManager(Map<String, Object> map) {
        ShopsResult sr = new ShopsResult();
        List<Map<String, Object>> data = staffDao.queryShopManager(map);
        if (null == data || data.isEmpty()) {
            sr.setStatus(1);
            sr.setMsg("没有满足条件的信息！");
            return sr;
        }
        sr.setStatus(0);
        sr.setMsg("查询成功！");
        sr.setData(data);
        return sr;
    }

    /**
     * 修改员工权限
     *
     * @param map
     * @return
     */
    @Transactional
    public ShopsResult modifyStaffPower(Map<String, Object> map) {
        ShopsResult sr = new ShopsResult();
        int k = staffDao.modifyStaffPower(map);

        if (k == 0) {
            sr.setStatus(1);
            sr.setMsg("更新失败！");
            return sr;
        }
        sr.setStatus(0);
        sr.setMsg("保存成功！");
        return sr;
    }

    /**
     * 根据店铺名获取相关管理员信息
     *
     * @param map
     * @return
     */
    public ShopsResult queryShopManagers(Map<String, Object> map) {
        ShopsResult sr = new ShopsResult();
        List<Map<String, Object>> data = staffDao.queryShopManagers(map);
        sr.setStatus(0);
        sr.setMsg("保存成功！");
        sr.setData(data);
        return sr;
    }

    /**
     * 根据员工编号，修改相应权限
     *
     * @param map
     * @return
     */
    public ShopsResult queryManagerPower(Map<String, Object> map) {
        ShopsResult sr = new ShopsResult();
        Map<String, Object> data = staffDao.queryManagerPower(map);
        sr.setStatus(0);
        sr.setMsg("保存成功！");
        sr.setData(data);
        return sr;
    }


    /**
     * 会员信息页数查询
     *
     * @param map
     * @return
     */
    public ShopsResult queryStaffsPages(Map<String, Object> map) {
        ShopsResult sr = new ShopsResult();
        int k = staffDao.queryStaffsPages(map);
        if (k == 0) {
            sr.setStatus(2);
            sr.setMsg("没有满足条件的信息！");
            return sr;
        }
        sr.setStatus(1);
        sr.setMsg("查询成功!");
        sr.setData(k);
        return sr;
    }

    /**
     * 员工信息分页查询
     *
     * @param map
     * @return
     */
    public PurResult queryStaffByPage(Map<String, Object> map) {
        PurResult result = new PurResult();
        try {
            int count = staffDao.queryStaffsPages(map);
            List<Map<String, Object>> list = staffDao.queryStaffByPage(map);

            result.setStatus(1);
            result.setMsg("成功");
            result.setCount(count);
            result.setData(list);
        } catch (Exception e) {
            e.printStackTrace();
            result.setStatus(0);
            result.setMsg("异常");
        }
        return result;
    }

    /**
     * 查询员工信息详情
     *
     * @param map
     * @return
     */
    public ShopsResult queryStaffDetailMessage(Map<String, Object> map) {
        ShopsResult sr = new ShopsResult();
        Map<String, Object> data = staffDao.queryStaffDetailMessage(map);
        if (null == data || data.isEmpty()) {
            sr.setStatus(2);
            sr.setMsg("没有满足条件的信息！");
            return sr;
        }
        sr.setStatus(1);
        sr.setData(data);
        sr.setMsg("查询成功！");
        return sr;
    }

    /**
     * 更新会员信息
     *
     * @param map
     * @return
     */
    @Transactional
    public ShopsResult updateStaffBaseMessage(Map<String, Object> map, String role_code, String staffAccount_old) {
        ShopsResult sr = new ShopsResult();
//		//员工头像信息
//		MultipartFile file=ShopsUtil.testMulRequest(request, "staffProtrait");
//		if(file!=null){
//			String orName=file.getOriginalFilename();
//			String lastName=orName.substring(orName.lastIndexOf("."));
//			String nStaffProtrait=shopUnique+staffId+Math.round(Math.random()*100)+lastName;
//			String filePath=File.separator + "home" + File.separator + "apache-tomcat-7.0.42"
//					+ File.separator + "webapps" + File.separator + "image"+File.separator+shopUnique;
//			boolean flag=ShopsUtil.savePicture(file, filePath, nStaffProtrait);
//			if(flag){
//				map.put("staffProtrait", "image"+File.separator+shopUnique+File.separator+nStaffProtrait);
//			}
//		}
        String staffAccount = MUtil.strObject(map.get("staffAccount"));
        Map<String, Object> map1 = new HashMap<String, Object>();
        map1.put("staffAccount", staffAccount);
        Map<String, Object> rmap = staffDao.staffLoginByAccountPwd(map1);
        if (null != rmap) {
            String rStaffid = MUtil.strObject(rmap.get("staffId"));
            String staffId = MUtil.strObject(map.get("staffId"));
            if (!rStaffid.equals(staffId)) {
                sr.setStatus(2);
                sr.setMsg("该登录账号已存在");
                return sr;
            }
        }

        int k = staffDao.updateStaffBaseMessage(map);
        if (k == 0) {
            sr.setStatus(2);
            sr.setMsg("没有该员工号");
            return sr;
        }
//		k=staffDao.modifyStaffPower(map);
//		if(k==0){
//			sr.setStatus(2);
//			sr.setMsg("更新员工权限失败！");
//			return sr;
//		}
        if (role_code != null && !role_code.equals("")) {
            //修改员工与角色关联关系
            Map<String, Object> params = new HashMap<String, Object>();
            params.put("staff_id", map.get("staffId"));
            params.put("role_code", role_code);
            Map<String, Object> staffRole = staffDao.getStaffRole(params);
            if (staffRole != null) {
                staffDao.updateStaffRole(params);
            } else {
                staffDao.addStaffRole(params);
            }
        } else {
            //删除员工角色
            staffDao.deleteStaffRole(MUtil.strObject(map.get("staffId")));
        }
        sr.setStatus(1);
        sr.setMsg("更新成功！");
        return sr;
    }

    /**
     * 添加新的员工信息
     *
     * @return
     */
    @Transactional
    public ShopsResult addNewStaff(
            HttpServletRequest request,
            Long shopUnique,
            String staffAccount,
            String staffPwd,
            String staffName,
            String staffPhone,
            Integer staffPosition,
//			Integer powerPrice,
//			Integer powerCount,
//			Integer powerSupplier,
//			Integer powerKind,
//			Integer powerInPrice,
//			Integer powerName,
//			Integer powerDelete,
//			Integer powerPur,
//			Integer powerAdd,
//			Integer powerRecharge
            String role_code,
            String county,
            String managerUnique
    ) {
        ShopsResult sr = new ShopsResult();
        Staff staff = new Staff();
        staff.setCounty(county);
        staff.setShop_unique(shopUnique);
        staff.setPwd_ok(staffPwd);
        staff.setManager_unique(managerUnique);
        staff.setStaff_pwd(ShopsUtil.string2MD5(staffPwd));
        staff.setStaff_name(staffName);
        staff.setStaff_phone(staffPhone);
        staff.setStaff_position(staffPosition);
        staff.setStaff_account(staffAccount);
        //staff.setStaff_birthday();
        Map<String, Object> map = new HashMap<String, Object>();
        map.put("staffAccount", staffAccount);
        Map<String, Object> rmap = staffDao.staffLoginByAccountPwd(map);
        if (null != rmap) {
            sr.setStatus(2);
            sr.setMsg("该登录账号已存在");
            return sr;
        }
//		MultipartFile file=ShopsUtil.testMulRequest(request, "staffProtrait");
//		if(null!=file){
//			String orName=file.getOriginalFilename();
//			String lastName=orName.substring(orName.lastIndexOf("."));
//			String filePath=File.separator + "home" + File.separator + "apache-tomcat-7.0.42"
//					+ File.separator + "webapps" + File.separator + "image"+File.separator+shopUnique;
//			String nStaffProtrait=shopUnique+""+Math.round(Math.random()*100)+lastName;
//			boolean flag=ShopsUtil.savePicture(file, filePath, nStaffProtrait);
//			if(flag){
//				map.put("staffProtrait", "image"+File.separator+shopUnique+File.separator+nStaffProtrait);
//			}
//		}
        int k = staffDao.newStaff(staff);
        if (k == 0) {
            sr.setStatus(2);
            sr.setMsg("添加失败！");
            return sr;
        }
//		map.put("staffId",staff.getStaff_id());
//		map.put("powerPrice",powerPrice);
//		map.put("powerInPrice",powerInPrice);
//		map.put("powerCount",powerCount);
//		map.put("powerName",powerName);
//		map.put("powerKind",powerKind);
//		map.put("powerSupplier",powerSupplier);
//		map.put("powerPur",powerPur);
//		map.put("powerAdd",powerAdd);
//		map.put("powerDelete",powerDelete);
//		map.put("powerRecharge", powerRecharge);
//		System.out.println(map);
//		k=staffDao.newStaffPower(map);
        if (role_code != null && !role_code.equals("")) {
            //添加员工与角色关联关系
            Map<String, Object> params = new HashMap<String, Object>();
            params.put("staff_id", staff.getStaff_id());
            params.put("role_code", role_code);
            staffDao.addStaffRole(params);
        }

        sr.setStatus(1);
        sr.setMsg("添加成功！");
        return sr;
    }

    /**
     * 管理员旗下店铺数量页数查询
     *
     * @param map
     * @return
     */
    public ShopsResult queryAllShopsPages(Map<String, Object> map) {
        ShopsResult sr = new ShopsResult();
        Integer count = staffDao.queryAllShopsPages(map);
        sr.setData(count);
        sr.setStatus(1);
        sr.setMsg("查询成功！");
        return sr;
    }

    /**
     * 查询同管理员的店铺信息列表
     *
     * @param map
     * @return
     */
    public PurResult queryAllShopsByPage(Map<String, Object> map) {
        PurResult result = new PurResult();
        try {
            List<Map<String, Object>> list = staffDao.queryAllShopsByPage(map);
            Integer count = staffDao.queryAllShopsPages(map);
            result.setStatus(1);
            result.setMsg("成功");
            result.setCount(count);
            result.setData(list);
        } catch (Exception e) {
            e.printStackTrace();
            result.setStatus(0);
            result.setMsg("异常");
        }
        return result;
    }

    /**
     * 销售详情
     *
     * @param map
     * @return
     */
    public PurResult queryGoodsSaleByStaffPage(Map<String, Object> map) {
        PurResult result = new PurResult();
        try {
            List<Map<String, Object>> list = staffDao.queryGoodsSaleByStaffPage(map);
            Integer count = staffDao.queryGoodsSaleByStaff(map);
            result.setStatus(1);
            result.setMsg("成功");
            result.setCount(count);
            result.setData(list);
        } catch (Exception e) {
            e.printStackTrace();
            result.setStatus(0);
            result.setMsg("异常");
        }
        return result;
    }

    public Map<String, Object> getStaffById(Integer staffId) {
        Map<String, Object> map = new HashMap<String, Object>();
        map.put("staffId", staffId);
        Map<String, Object> data = staffDao.getStaffById(map);
        return data;
    }

    public Map<String, Object> queryShopInfoByShopUnique(Long shopUnique) {
        Map<String, Object> map = new HashMap<String, Object>();
        map.put("shopUnique", shopUnique);
        Map<String, Object> result = staffDao.queryShopInfoByShopUnique(map);

        //查询店铺实景图片
        List<Map<String, Object>> imageList = staffDao.queryShopImageList(map);

        for (int i = 0; imageList != null && i < imageList.size(); i++) {
            Map<String, Object> array_element = imageList.get(i);
            result.put("shop_image_path" + (i + 1), array_element.get("shop_image_path"));

        }
        return result;
    }

    /**
     * 根据登录账户获取登录用户信息
     *
     * @param staff_account 登录账号
     * @return
     */

    public Staff getStaffByAccount(String staff_account) {
        return staffDao.getStaffByAccount(staff_account);
    }

    /**
     * 删除员工
     *
     * @param staffId 员工id
     * @return
     */
    @Transactional
    public PurResult deleteStaff(String staffId) {
        PurResult result = new PurResult();
        try {
            //删除员工信息
            staffDao.deleteStaff(staffId);
            //删除员工角色信息
            staffDao.deleteStaffRole(staffId);
            result.setStatus(1);
            result.setMsg("成功");
        } catch (Exception e) {
            e.printStackTrace();
            result.setStatus(0);
            result.setMsg("异常");
        }
        return result;
    }

    @Override
    public ShopsResult editIoBoundInspect(ShopIoBoundInspectEditParams params) {
        ShopsResult result = new ShopsResult(1, "保存成功");
        ShopConfigQueryResult shopConfigQueryResult = shopDao.queryShopConfig(params.getShopUnique());
        if (ObjectUtil.isNotEmpty(shopConfigQueryResult)) {
            shopConfigQueryResult.setIsIoboundInspect(params.getIsIoBoundInspect());
            shopDao.updateShopConfig(shopConfigQueryResult);
        } else {
            shopConfigQueryResult = new ShopConfigQueryResult();
            shopConfigQueryResult.setShopUnique(Long.valueOf(params.getShopUnique()));
            shopConfigQueryResult.setIsIoboundInspect(params.getIsIoBoundInspect());
            shopDao.insertShopConfig(shopConfigQueryResult);
        }
        return result;
    }

    @Override
    public ShopConfigQueryResult queryShopConfig(String shopUnique) {
        return shopDao.queryShopConfig(shopUnique);
    }
}
