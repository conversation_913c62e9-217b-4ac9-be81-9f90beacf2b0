package org.haier.shop.service;

import org.haier.shop.params.speech.AddNewSpeechCmdParams;
import org.haier.shop.params.speech.QuerySpeechCmdListParams;
import org.haier.shop.params.speech.QuerySpeechListParams;
import org.haier.shop.util.ShopsResult;

public interface SpeechService {
    /**
     * 查询满足条件的语音列表
     * @param querySpeechListParams
     * @return
     */
    public ShopsResult querySpeechListByParam(QuerySpeechListParams querySpeechListParams);

    /**
     * 查询语音指令列表
     * @param params
     * @return
     */
    public ShopsResult querySpeechCmdList(QuerySpeechCmdListParams params);

    /**
     * 新增语音指令
     * @return
     */
    public ShopsResult addNewSpeechCmd(AddNewSpeechCmdParams params);

}
