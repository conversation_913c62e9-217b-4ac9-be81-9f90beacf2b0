package org.haier.shop.service;

import java.util.Map;

import javax.servlet.http.HttpServletRequest;

import org.haier.shop.util.PurResult;
import org.haier.shop.util.ShopsResult;

public interface GoodsDictService {
	/**
	 * 商品公共信息查询
	 * @param goods_barcode 商品条码
	 * @param shop_unique 供货商编号
	 * @return
	 */
	public ShopsResult queryBaseGoodsMessage(String goods_barcode,String shop_unique);
	
	public PurResult getGoodsDictList(Map<String,Object> map);
	
	public PurResult updateGoodsDictImg(String goods_barcode,String goods_picturepath, HttpServletRequest request);
	
	public ShopsResult updateGoodsDictMsg(Map<String,Object> map);
	
}
