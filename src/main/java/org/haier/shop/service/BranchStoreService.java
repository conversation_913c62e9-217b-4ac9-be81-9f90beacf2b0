package org.haier.shop.service;

import java.util.Map;

import org.haier.shop.util.PurResult;

public interface BranchStoreService {
	
	/**
	 * 总店查询分店列表
	 * @param company_code
	 * @param shop_name 
	 * @param startNum
	 * @param pageSize
	 * @return
	 */
	public PurResult getBranchStoreList(Map<String ,Object> params);
	
	/**
	 * 查询分店详情
	 * @param shop_unique 店铺唯一编号
	 * @return
	 */
	public PurResult getBranchStore(String shop_unique);
	
	/**
	 * 删除分店信息，逻辑删除
	 * @param shop_unique 店铺唯一编号
	 * @return
	 */
	public PurResult deleteBranchStore(String shop_unique);
}
