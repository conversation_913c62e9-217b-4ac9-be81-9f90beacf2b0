package org.haier.shop.service;

import org.haier.shop.entity.inventoryTask.TaskListResp;
import org.haier.shop.util.PurResult;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.Map;

public interface InventoryTaskService {

    /**
     * 任务列表
     * @param map
     * @return
     */
    public PurResult taskList(Map<String,Object> map, HttpServletRequest request);

    /**
     * 盘库单预览
     * @param request
     * @return
     */
    public PurResult taskPreview(HttpServletRequest request);

    /**
     * 单个商品明细-商品盘点
     * @param request
     * @return
     */
    public PurResult taskGoodsDetail(HttpServletRequest request);

    /**
     * 导出excel
     * @param request
     * @return
     */
    public void exportInventoryExcel(HttpServletRequest request, HttpServletResponse response);

}
