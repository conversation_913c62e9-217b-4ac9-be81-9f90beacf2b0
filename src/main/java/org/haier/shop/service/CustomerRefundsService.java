package org.haier.shop.service;

import org.haier.shop.util.PurResult;
import org.haier.shop.vo.CustomerRefundsVO;

import java.util.List;
import java.util.Map;

/**
 * @Description 会员退费记录
 * @ClassName CustomerRefundsService
 * <AUTHOR>
 * @Date 2024/4/15 11:19
 **/
public interface CustomerRefundsService {
    PurResult queryCustomerRefundsPage(Map<String, Object> map);

    List<CustomerRefundsVO> queryCustomerRefundsList(Map<String, Object> paramsMap);
}
