package org.haier.shop.service;

import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import javax.annotation.Resource;

import org.haier.meituan.util.MUtil;
import org.haier.shop.dao.CountMsgDao;
import org.haier.shop.util.DateUtils;
import org.haier.shop.util.PurResult;
import org.haier.shop.util.ShopsResult;
import org.haier.shop.util.UtilForJAVA;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;


@Service("countMsgService")
@Transactional
public class CountMsgServiceImpl implements CountMsgService{
	
	@Resource
	private CountMsgDao countMsgDao;
	
	/**
	 * 
	 * @param shopUnique 店铺编号
	 * @param startTime 开始时间
	 * @param endTime 结束时间
	 * @param goods_big_class1
	 * @param goods_small_class1
	 * @param supplierUnique1
	 * @param goodsMessage1
	 * @param goods_big_class2
	 * @param goods_small_class2
	 * @param supplierUnique2
	 * @param goodsMessage2
	 * @return
	 */
	public PurResult queryCountStatisticsSameGoods(String shopUnique,String startTime,String endTime
			,Integer goods_big_class1,Integer goods_small_class1,String supplierUnique1,String goodsMessage1
			,Integer goods_big_class2,Integer goods_small_class2,String supplierUnique2,String goodsMessage2
			) {
		PurResult pr = new PurResult(1,"查询成功！");
		List<Map<String,Object>> list = new ArrayList<>();
		
		Map<String,Object> map = new HashMap<String,Object>();
		map.put("shopUnique", shopUnique);
		map.put("startTime", startTime);
		map.put("endTime", endTime);
		map.put("goods_big_class", goods_big_class1);
		map.put("goods_small_class", goods_small_class1);
		map.put("supplierUnique", supplierUnique1);
		map.put("goodsMessage", goodsMessage1);
		
		Map<String,Object> m1 = null;
		if((goods_big_class1 == null || goods_big_class1 == -1)
		&& (goods_small_class1 == null || goods_small_class1 == -1)
		&& (supplierUnique1 == null || supplierUnique1.equals("") || supplierUnique1.equals("-1"))
		&& (goodsMessage1 == null || goodsMessage1.trim().equals(""))
				) {
			m1= countMsgDao.queryCountStatisticsSameTime(map);
		}else {
			m1 = countMsgDao.queryCountStatisticsSameGoods(map);
		}
		Map<String,Object> r1 = countMsgDao.queryCountStatisticsReturn(map);
		
		
		
		map.put("shopUnique", shopUnique);
		map.put("startTime", startTime);
		map.put("endTime", endTime);
		map.put("goods_big_class", goods_big_class2);
		map.put("goods_small_class", goods_small_class2);
		map.put("supplierUnique", supplierUnique2);
		map.put("goodsMessage", goodsMessage2);
		Map<String,Object> m2 = null;
		if((goods_big_class2 == null || goods_big_class2 == -1)
			&& (goods_small_class2 == null || goods_small_class2 == -1)
			&& (supplierUnique2 == null || supplierUnique2.equals("") || supplierUnique2.equals("-1"))
			&& (goodsMessage2 == null || goodsMessage2.trim().equals(""))
			) {
				m2= countMsgDao.queryCountStatisticsSameTime(map);
		}else {
			m2 = countMsgDao.queryCountStatisticsSameGoods(map);
		}
		Map<String,Object> r2 = countMsgDao.queryCountStatisticsReturn(map);
		
	
		
		if(null == m2) {
			m2 = new HashMap<String,Object>();
			m2.put("saleListTotal", 0);
			m2.put("saleListCount", 0);
			m2.put("saleListPur", 0);
			m2.put("saleListCount", 0);
		}
		if(null == m1) {
			m1 = new HashMap<String,Object>();
			m1.put("saleListTotal", 0);
			m1.put("saleListCount", 0);
			m1.put("saleListPur", 0);
			m1.put("saleListCount", 0);
		}
		//退款的商品如何处理
		if(null != r1) {
			//修改对应销售额，成本，商品销量
			BigDecimal saleTotal = new BigDecimal(m1.get("saleListTotal").toString());
			BigDecimal returnTotal = new BigDecimal(r1.get("returnTotal").toString());
			saleTotal = saleTotal.subtract(returnTotal).setScale(2,BigDecimal.ROUND_HALF_UP);
			
			BigDecimal saleListPur = new BigDecimal(m1.get("saleListPur").toString());
			BigDecimal returnPruTotal = new BigDecimal(r1.get("returnPruTotal").toString());
			saleListPur = saleListPur.subtract(returnPruTotal);
			
			BigDecimal goodsCount = new BigDecimal(m1.get("goodsCount").toString());
			BigDecimal returnGoodsCount = new BigDecimal(r1.get("returnGoodsCount").toString());
			goodsCount = goodsCount.subtract(returnGoodsCount);
			
			m1.put("goodsCount", goodsCount);
			m1.put("saleTotal", saleTotal);
			m1.put("saleListPur", saleListPur);
		}
		
		if(null != r2) {
			//修改对应销售额，成本，商品销量
			BigDecimal saleTotal = new BigDecimal(m2.get("saleListTotal").toString());
			BigDecimal returnTotal = new BigDecimal(r2.get("returnTotal").toString());
			saleTotal = saleTotal.subtract(returnTotal).setScale(2,BigDecimal.ROUND_HALF_UP);
			
			BigDecimal saleListPur = new BigDecimal(m2.get("saleListPur").toString());
			BigDecimal returnPruTotal = new BigDecimal(r2.get("returnPruTotal").toString());
			saleListPur = saleListPur.subtract(returnPruTotal);
			
			BigDecimal goodsCount = new BigDecimal(m2.get("goodsCount").toString());
			BigDecimal returnGoodsCount = new BigDecimal(r2.get("returnGoodsCount").toString());
			goodsCount = goodsCount.subtract(returnGoodsCount);
			
			m2.put("goodsCount", goodsCount);
			m2.put("saleTotal", saleTotal);
			m2.put("saleListPur", saleListPur);
		}
		
		//计算增长率
		if(null != m1 && null != m2) {
			BigDecimal t1 = new BigDecimal(m1.get("saleListTotal").toString());
			BigDecimal t2 = new BigDecimal(m2.get("saleListTotal").toString());
			
			if(t2.compareTo( new BigDecimal(0)) == 0) {
				if(t1.compareTo(new BigDecimal(0))  == 0) {
					m1.put("growthRate", "0%");
				}else {
					m1.put("growthRate", "100%");
				}
			}else {
				t1 = t1.subtract(t2).setScale(6,BigDecimal.ROUND_HALF_UP);
				t1 = t1.divide(t2,4,BigDecimal.ROUND_HALF_UP).multiply(new BigDecimal(100)).setScale(2,BigDecimal.ROUND_HALF_UP);
				m1.put("growthRate", (t1 + "%"));
			}
		}
		
		m2.put("growthRate", "0%");
		list.add(m1);
		list.add(m2);
		
		pr.setData(list);
		
		return pr;
	}
	
	/**
	 * 
	 * @param shopUnique
	 * @param startTime1
	 * @param endTime1
	 * @param startTime2
	 * @param endTime2
	 * @param goods_big_class
	 * @param good_small_class
	 * @param supplierUnique
	 * @param goodsMessage
	 * @return
	 */
	public PurResult queryCountStatisticsSameTime(String shopUnique,String startTime1,String endTime1,String startTime2,String endTime2
			,Integer goods_big_class,Integer goods_small_class,String supplierUnique,String goodsMessage
			) {
		PurResult pr = new PurResult(1,"查询成功!");
		
		List<Map<String,Object>> list = new ArrayList<>();
		
		Map<String,Object> map = new HashMap<String,Object>();
		map.put("shopUnique", shopUnique);
		map.put("startTime", startTime1);
		map.put("endTime", endTime1);
		map.put("goods_big_class", goods_big_class);
		map.put("goods_small_class", goods_small_class);
		map.put("supplierUnique", supplierUnique);
		map.put("goodsMessage", goodsMessage);
		
		Map<String,Object> m1 = null;
		if((goods_big_class == null || goods_big_class == -1)
		&& (goods_small_class == null || goods_small_class == -1)
		&& (supplierUnique == null || supplierUnique.equals("") || supplierUnique.equals("-1"))
		&& (goodsMessage == null || goodsMessage.trim().equals(""))
				) {
			m1= countMsgDao.queryCountStatisticsSameTime(map);
		}else {
			m1 = countMsgDao.queryCountStatisticsSameGoods(map);
		}
		Map<String,Object> r1 = countMsgDao.queryCountStatisticsReturn(map);
		
		map.put("startTime", startTime2);
		map.put("endTime", endTime2);
		
		Map<String,Object> m2 = null;
		if((goods_big_class == null || goods_big_class == -1)
		&& (goods_small_class == null || goods_small_class == -1)
		&& (supplierUnique == null || supplierUnique.equals("") || supplierUnique.equals("-1"))
		&& (goodsMessage == null || goodsMessage.trim().equals(""))
				) {
			m2= countMsgDao.queryCountStatisticsSameTime(map);
		}else {
			m2 = countMsgDao.queryCountStatisticsSameGoods(map);
		}
		Map<String,Object> r2 = countMsgDao.queryCountStatisticsReturn(map);
		if(null == m2) {
			m2 = new HashMap<String,Object>();
			m2.put("saleListTotal", 0);
			m2.put("saleListCount", 0);
			m2.put("saleListPur", 0);
			m2.put("saleListCount", 0);
		}
		if(null == m1) {
			m1 = new HashMap<String,Object>();
			m1.put("saleListTotal", 0);
			m1.put("saleListCount", 0);
			m1.put("saleListPur", 0);
			m1.put("saleListCount", 0);
		}
		//退款的商品如何处理
		if(null != r1) {
			//修改对应销售额，成本，商品销量
			BigDecimal saleTotal = new BigDecimal(m1.get("saleListTotal").toString());
			BigDecimal returnTotal = new BigDecimal(r1.get("returnTotal").toString());
			saleTotal = saleTotal.subtract(returnTotal).setScale(2,BigDecimal.ROUND_HALF_UP);
			
			BigDecimal saleListPur = new BigDecimal(m1.get("saleListPur").toString());
			BigDecimal returnPruTotal = new BigDecimal(r1.get("returnPruTotal").toString());
			saleListPur = saleListPur.subtract(returnPruTotal);
			
			BigDecimal goodsCount = new BigDecimal(m1.get("goodsCount").toString());
			BigDecimal returnGoodsCount = new BigDecimal(r1.get("returnGoodsCount").toString());
			goodsCount = goodsCount.subtract(returnGoodsCount);
			
			m1.put("goodsCount", goodsCount);
			m1.put("saleTotal", saleTotal);
			m1.put("saleListPur", saleListPur);
		}
		
		if(null != r2) {
			//修改对应销售额，成本，商品销量
			BigDecimal saleTotal = new BigDecimal(m2.get("saleListTotal").toString());
			BigDecimal returnTotal = new BigDecimal(r2.get("returnTotal").toString());
			saleTotal = saleTotal.subtract(returnTotal).setScale(2,BigDecimal.ROUND_HALF_UP);
			
			BigDecimal saleListPur = new BigDecimal(m2.get("saleListPur").toString());
			BigDecimal returnPruTotal = new BigDecimal(r2.get("returnPruTotal").toString());
			saleListPur = saleListPur.subtract(returnPruTotal);
			
			BigDecimal goodsCount = new BigDecimal(m2.get("goodsCount").toString());
			BigDecimal returnGoodsCount = new BigDecimal(r2.get("returnGoodsCount").toString());
			goodsCount = goodsCount.subtract(returnGoodsCount);
			
			m2.put("goodsCount", goodsCount);
			m2.put("saleTotal", saleTotal);
			m2.put("saleListPur", saleListPur);
		}
		
		//计算增长率
		if(null != m1 && null != m2) {
			BigDecimal t1 = new BigDecimal(m1.get("saleListTotal").toString());
			BigDecimal t2 = new BigDecimal(m2.get("saleListTotal").toString());
			
			if(t2.compareTo( new BigDecimal(0)) == 0) {
				if(t1.compareTo(new BigDecimal(0))  == 0) {
					m1.put("growthRate", "0%");
				}else {
					m1.put("growthRate", "100%");
				}
			}else {
				t1 = t1.subtract(t2).setScale(6,BigDecimal.ROUND_HALF_UP);
				t1 = t1.divide(t2,4,BigDecimal.ROUND_HALF_UP).multiply(new BigDecimal(100)).setScale(2,BigDecimal.ROUND_HALF_UP);
				m1.put("growthRate", (t1 + "%"));
			}
		}
		
		m2.put("growthRate", "0%");
		
		list.add(m1);
		list.add(m2);
		
		pr.setData(list);
		
		return pr;
	}
	
	//查询营业额
	public PurResult queryTurnover(String shop_unique, String type) {
		PurResult pr=new PurResult();
		Map<String,Object> map=new HashMap<String, Object>();
		map.put("shop_unique", shop_unique);
		map.put("type", type);
		Map<String,Object> list=countMsgDao.queryTurnover(map);
		
		pr.setData(list);
		pr.setStatus(0);
		pr.setMsg("查询成功！");
		return pr;
	}
	//查询订单信息
	public PurResult queryOrderMsg(String shop_unique, String type) {
		PurResult pr=new PurResult();
		Map<String,Object> resultMap=new HashMap<String, Object>();
		Map<String,Object> map=new HashMap<String, Object>();
		map.put("shop_unique", shop_unique);
		map.put("type", type);
		//查询订单数
		Map<String,Object> orderCount=countMsgDao.queryOrderCount(map);
		resultMap.put("orderCount",orderCount.get("orderCount"));
		//查询已付款和分销金额
		Map<String,Object> moneyAlready=countMsgDao.queryMoneyAlready(map);
		resultMap.put("moneyAlready",moneyAlready.get("moneyAlready")); 
		resultMap.put("disMoney", moneyAlready.get("disMoney"));
		//查询待付款
		Map<String,Object> moneyNotAlready=countMsgDao.queryMoneyNotAlready(map);
		resultMap.put("moneyNotAlready",moneyNotAlready.get("moneyNotAlready"));
		//查询毛利润
		Map<String,Object> grossProfit=countMsgDao.queryGrossProfit(map);
		resultMap.put("grossProfit",UtilForJAVA.addDouble(grossProfit.get("grossProfit"),moneyAlready.get("disMoney"),2));
		//查询客单价
		Map<String,Object> unitPrice=countMsgDao.queryUnitPrice(map);
		if(unitPrice!=null){
			resultMap.put("unitPrice",unitPrice.get("unitPrice"));
		}else{
			resultMap.put("unitPrice",0);
		}
		
		pr.setData(resultMap);
		pr.setStatus(1);
		pr.setMsg("查询成功！");
		return pr;
	}
	//分类销量占比
	public PurResult queryKindSaleProportion(String shop_unique, String type) {
		PurResult pr=new PurResult();
		/*
		//查询所有分类
		Map<String,Object> map2=new HashMap<String, Object>();
		map2.put("supplier_unique", supplier_unique);
		map2.put("goods_kind_parunique", 0);
		List<Map<String,Object>> list=kindsDao.queryGoodsKinds(map2);
		Map<String,Object> map=new HashMap<String, Object>();
		map.put("supplier_unique", supplier_unique);
		map.put("type", type);
		List<Map<String,Object>> kindSaleProportion=countMsgDao.queryKindSaleProportion(map);
		List<Map<String,Object>> KindListMap=new ArrayList<Map<String,Object>>();
		for (Map<String, Object> map3 : list) {
			Map<String,Object> kindMap=new HashMap<String, Object>();
			String goods_kind_name= (String) map3.get("goods_kind_name");
			kindMap.put("name", goods_kind_name);
			kindMap.put("value", 0);
			for (Map<String, Object> map4 : kindSaleProportion) {
				String name= (String) map4.get("name");
				if(name.equals(goods_kind_name)){
					Long value= (Long) map4.get("value");
					kindMap.put("value", value);
				}
				
			}
			KindListMap.add(kindMap);
		}
		pr.setData(KindListMap);
		*/
		Map<String,Object> params = new HashMap<String, Object>();
		params.put("shop_unique", shop_unique);
		params.put("type", type);
		
		List<Map<String,Object>> kindSaleProportion = countMsgDao.queryKindSaleProportion(params);
		List<Map<String,Object>> newkindSaleProportion = new ArrayList<Map<String,Object>>();
		String[] names = new String[kindSaleProportion.size()];  
		for (int i=0; i<kindSaleProportion.size(); i++) {
			Double saleTotal = Double.valueOf(MUtil.strObject(kindSaleProportion.get(i).get("saleTotal")));
			Map<String ,Object> map = new HashMap<String, Object>();
			map.put("value", saleTotal);
			map.put("name", kindSaleProportion.get(i).get("kindName"));
			newkindSaleProportion.add(map);
			names[i] = MUtil.strObject(kindSaleProportion.get(i).get("kindName"));
		}
		pr.setCord(names);
		
		if(newkindSaleProportion.size() == 0){
			Map<String,Object> mm=new HashMap<String, Object>();
			mm.put("name", "1");
			mm.put("value", 0);
			newkindSaleProportion.add(mm);
		}
		pr.setData(newkindSaleProportion);
		pr.setStatus(1);
		pr.setMsg("查询成功！");
		return pr;
	}
	//查询销售走势
	public PurResult querySaleTrend(String shop_unique, String type, String startDate, String endDate) {
		PurResult pr=new PurResult();
		Map<String,Object> map=new HashMap<String, Object>();
		map.put("shop_unique", shop_unique);
		map.put("type", type);
		if(startDate!=null&&!"".equals(startDate)){
			map.put("startDate", DateUtils.parse(startDate));
		}
		if(endDate!=null&&!"".equals(endDate)){
			map.put("endDate", DateUtils.parse(endDate+" 23:59:59"));
		}
		String[] dates=null;
		int length=0;
		if("3".equals(type)){
			length=7;
		}else if ("4".equals(type)){
			length=30;
		}
		if("3".equals(type)||"4".equals(type)){
			dates=new String[length];
			//周销售
			int j=0;
			for (int i = length; i > 0; i--) {
				SimpleDateFormat sdf = new SimpleDateFormat("MM-dd");
		        Calendar c = Calendar.getInstance();  
		        c.add(Calendar.DATE, - i);  
		        Date monday = c.getTime();
		        String preMonday = sdf.format(monday);
//		        System.out.println(preMonday);
		        dates[j]=preMonday;
		        j++;
			}
		}else if("5".equals(type)){
			dates=DateUtils.getBetweenDates(DateUtils.parse(startDate), DateUtils.parse(endDate));
		}else if("1".equals(type)){
			dates=new String[24];
			for (int i = 0; i < 24; i++) {
				dates[i]=i+"";
			}
		}
		List<Map<String,Object>> saleTrend=null;
		if("1".equals(type)){
			saleTrend=countMsgDao.querySaleTrendToday(map);
		}else{
		    saleTrend=countMsgDao.querySaleTrend(map);
		}
		
		List<Map<String,Object>> saleListMap=new ArrayList<Map<String,Object>>();
		for(String date: dates){
			Map<String, Object> saleMap=new HashMap<String, Object>();
			saleMap.put("purchase_list_date", date);
			saleMap.put("money", 0);
			for (Map<String, Object> map2 : saleTrend) {
				if(date.equals(map2.get("purchase_list_date"))){
					saleMap.put("money", map2.get("money"));
				}
			}
			saleListMap.add(saleMap);
		}
		pr.setData(saleListMap);
		pr.setStatus(1);
		pr.setMsg("查询成功！");
		return pr;
	}
	//查询超市进货前5
	public PurResult queryShopPurchaseTop5(String shop_unique, String type) {
		PurResult pr=new PurResult();
		Map<String,Object> map=new HashMap<String, Object>();
		map.put("shop_unique", shop_unique);
		map.put("type", type);
		List<Map<String,Object>> shopData=countMsgDao.queryShopPurchaseTo5(map);
		List<Map<String,Object>> shopDataAll=countMsgDao.queryShopPurchase(map);
		//计算库存占比
		Double  sum=0.0;
		for (Map<String, Object> map2 : shopDataAll) {
				BigDecimal s=(BigDecimal) map2.get("purchase_list_total");
				sum+= s.doubleValue();
		}
		for (Map<String, Object> map2 : shopData) {
					BigDecimal purchase_list_total=(BigDecimal) map2.get("purchase_list_total");
					BigDecimal cc= BigDecimal.valueOf(purchase_list_total.doubleValue()/sum*100).setScale(2,   BigDecimal.ROUND_DOWN);
					//int i=(int) (cc.doubleValue()*100);
					map2.put("proportion", cc+"%");
		}
		pr.setData(shopData);
		pr.setStatus(1);
		pr.setMsg("查询成功！");
		return pr;
	}
	//查询商品销售前5
	public PurResult queryGoodsSaleTop5(String shop_unique, String type) {
		PurResult pr=new PurResult();
		Map<String,Object> map=new HashMap<String, Object>();
		map.put("shop_unique", shop_unique);
		map.put("type", type);
		List<Map<String,Object>> shopData=countMsgDao.queryGoodsSaleTop5(map);
		Map<String,Object> shopDataAll=countMsgDao.queryGoodsSaleAll(map);
		//计算库存占比
		if(shopDataAll!=null){
			Double  sum=((BigDecimal)shopDataAll.get("count")).doubleValue();
			for (Map<String, Object> map2 : shopData) {
					BigDecimal purchase_list_total=(BigDecimal) map2.get("count");
					BigDecimal cc= BigDecimal.valueOf(purchase_list_total.doubleValue()/sum*100).setScale(2,   BigDecimal.ROUND_DOWN);
					map2.put("proportion", cc+"%");
			}
		}
		pr.setData(shopData);
		pr.setStatus(1);
		pr.setMsg("查询成功！");
		return pr;
	}
	public PurResult querySalesmanTop5(String shop_unique, String type) {
		PurResult pr=new PurResult();
		Map<String,Object> map=new HashMap<String, Object>();
		map.put("shop_unique", shop_unique);
		map.put("type", type);
		List<Map<String,Object>> shopData=countMsgDao.querySalesmanTop5(map);
		List<Map<String,Object>> shopDataAll=countMsgDao.querySalesmanAll(map);
		//计算库存占比
		Double  sum=0.0;
		for (Map<String, Object> map2 : shopDataAll) {
				BigDecimal s=(BigDecimal) map2.get("purchase_list_total");
				sum+= s.doubleValue();
		}
		for (Map<String, Object> map2 : shopData) {
				BigDecimal purchase_list_total=(BigDecimal) map2.get("purchase_list_total");
				BigDecimal cc= BigDecimal.valueOf(purchase_list_total.doubleValue()/sum*100).setScale(2,   BigDecimal.ROUND_DOWN);
				map2.put("proportion", cc+"%");
		}
		pr.setData(shopData);
		pr.setStatus(1);
		pr.setMsg("查询成功！");
		return pr;
	}
	public PurResult queryPayProportion(String shop_unique, String type) {
		PurResult pr=new PurResult();
		Map<String,Object> params = new HashMap<String, Object>();
		params.put("shop_unique", shop_unique);
		params.put("type", type);
		List<Map<String,Object>> kindSaleProportion = countMsgDao.queryPayProportion(params);
		List<Map<String,Object>> newkindSaleProportion = new ArrayList<Map<String,Object>>();
		String[] names = new String[kindSaleProportion.size()]; 
		for (int i=0; i<kindSaleProportion.size(); i++) {
			Double saleTotal = Double.valueOf(MUtil.strObject(kindSaleProportion.get(i).get("saleTotal")));
			Map<String ,Object> map = new HashMap<String, Object>();
			map.put("value", saleTotal);
			map.put("name", kindSaleProportion.get(i).get("payment"));
			newkindSaleProportion.add(map);
			names[i] = MUtil.strObject(kindSaleProportion.get(i).get("payment"));
		}
		pr.setCord(names);
		
		if(newkindSaleProportion.size() == 0){
			Map<String,Object> mm=new HashMap<String, Object>();
			mm.put("name", "1");
			mm.put("value", 0);
			newkindSaleProportion.add(mm);
		}
		pr.setData(newkindSaleProportion);
		pr.setStatus(1);
		pr.setMsg("查询成功！");
		return pr;
	}
	public ShopsResult orderTotalByHours(String shop_unique) {
		ShopsResult sr=new ShopsResult();
		List<Integer> arr=new ArrayList<Integer>();
		for(int i=0;i<24;i++){
			arr.add(i);
		}
		Map<String,Object> map=new HashMap<String, Object>();
		map.put("shop_unique", shop_unique);
		map.put("list", arr);
//		Calendar startTime=Calendar.getInstance();
//		map.put("endTime", new Timestamp(startTime.getTimeInMillis()));//今日截至时间
//		startTime.set(Calendar.HOUR_OF_DAY, 0);
//		startTime.set(Calendar.MINUTE, 0);
//		startTime.set(Calendar.SECOND, 0);
//		startTime.set(Calendar.MILLISECOND, 0);
//		Timestamp time=new Timestamp(startTime.getTimeInMillis());
//		map.put("startTime", time);
		map.put("time", 1);
		List<Map<String,Object>> data=countMsgDao.orderTotalByHours(map);
		sr.setData(data);
//		startTime.add(Calendar.DATE , -1);//昨日数据
//		map.put("endTime", time);
//		map.put("startTime", new Timestamp(startTime.getTimeInMillis()));
		map.put("time", 2);
		data=countMsgDao.orderTotalByHours(map);
		sr.setCord(data);
		sr.setMsg("查询成功！");
		sr.setStatus(1);
		return sr;
	}
	
	/**
	 * 百货豆统计
	 * @param map
	 * @return
	 */
	public ShopsResult queryShopStatisticsMsgPage(Map<String,Object> map){
		ShopsResult sr=new ShopsResult(1, "查询成功！");
		System.out.println(map);
		Map<String,Object> data=countMsgDao.queryShopStatisticsMsgPage(map);
		sr.setData(data);
		return sr;
	}
	
	/**
	 * 积分统计
	 * @param map
	 * @return
	 */
	public ShopsResult queryShopStatisticsMsgPagePoints(Map<String,Object> map){
		ShopsResult sr=new ShopsResult(1, "查询成功！");
		Map<String,Object> data=countMsgDao.queryShopStatisticsMsgPagePoints(map);
		sr.setData(data);
		return sr;
	}
	/**
	 * 分时段百货豆交易统计
	 * @param map
	 * @return
	 */
	public ShopsResult queryBeansAccountByDays(Map<String,Object> map){
		ShopsResult sr=new ShopsResult(1, "查询成功！");
		List<Map<String,Object>> data=countMsgDao.queryBeansAccountByDays(map);
		sr.setData(data);
		return sr;
	}
	
	public ShopsResult pointsStatisticsByDay(Map<String,Object> map){
		ShopsResult sr=new ShopsResult(1, "查询成功！");
		List<Map<String,Object>> data=countMsgDao.pointsStatisticsByDay(map);
		sr.setData(data);
		return sr;
	}
	
	/**
	 * 公共优惠券店内销售统计
	 * @param map
	 * @return
	 */
	public ShopsResult queryCouponPublicStatistics(Map<String,Object> map){
		ShopsResult sr=new ShopsResult(1, "查询成功！");
		System.out.println(new Date());
		Map<String,Object> data=countMsgDao.queryCouponPublicStatistics(map);
		sr.setData(data);
		System.out.println(new Date());
		return sr;
	}
	
	/**
	 * 周期内平台优惠券店内使用情况统计
	 * @param map
	 * @return
	 */
	public ShopsResult queryPubCouponMsg(Map<String,Object> map){
		ShopsResult sr=new ShopsResult(1,"查询成功！");
		List<Map<String,Object>> data=countMsgDao.queryPubCouponMsg(map);
		sr.setData(data);
		return sr;
	}
	
	/**
	 * 本店优惠券发放及使用情况
	 * @param map
	 * @return
	 */
	public ShopsResult ourShopCouponMsg(Map<String,Object> map){
		ShopsResult sr=new ShopsResult(1, "查询成功！");
		Map<String,Object> data=countMsgDao.ourShopCouponMsg(map);
		if(null==data){
			Map<String,Object> res=new HashMap<String,Object>();
			res.put("todayCouponAmount", "0");
			res.put("usedCount", "0");
			res.put("allCouponAmount", "0");
			res.put("allUsedCount", "0");
			res.put("takeCount", "0");
			res.put("waitUseCount", "0");
			res.put("couponAmount", "0");
			sr.setData(res);
			return sr;
		}
		sr.setData(data);
		return sr;
	}
	
	/**
	 * 本店优惠券使用情况
	 * @param map
	 * @return
	 */
	public ShopsResult ourCouponUseMsgByDay(Map<String,Object> map){
		ShopsResult sr=new ShopsResult(1,"查询成功！");
		List<Map<String,Object>> data=countMsgDao.ourCouponUseMsgByDay(map);
		sr.setData(data);
		return sr;
	}
	
	/**
	 * 店铺内经营金额统计
	 * @param map
	 * @return
	 */
	public ShopsResult accountStatistics(Map<String,Object> map){
		ShopsResult sr=new ShopsResult(1, "查询成功");
		List<Map<String,Object>> data=countMsgDao.accountStatistics(map);
		sr.setData(data);
		return sr;
	}
	
	
	/**
	 * 提现记录
	 * @param map
	 * @return
	 */
	public ShopsResult queryTakeCashList(Map<String,Object> map){
		ShopsResult sr=new ShopsResult(1, "查询成功");
		System.out.println(map);
		List<Map<String,Object>> data=countMsgDao.queryTakeCashList(map);
		sr.setData(data);
		return sr;
	}
	
	/**
	 * 订单收入详情界面及分页
	 * @param map
	 * @return
	 */
	public ShopsResult queryOrderListNetWork(Map<String,Object> map){
		ShopsResult sr=new ShopsResult(1, "查询成功！");
		List<Map<String,Object>> data=countMsgDao.queryOrderListNetWork(map);
		if(map.get("pageNum").toString().equals("1")){
			Integer pageCount=countMsgDao.queryOrderListNetWorkPages(map);
			sr.setCord(pageCount);
		}
		sr.setData(data);
		return sr;
	}
}


