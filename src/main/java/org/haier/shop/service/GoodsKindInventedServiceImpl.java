package org.haier.shop.service;


import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;

import org.haier.shop.dao.GoodsKindInventedDao;
import org.haier.shop.util.ShopsResult;
import org.springframework.stereotype.Service;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import com.alibaba.fastjson.JSONArray;

/**
* @author: 作者:王恩龙
* @version: 2023年3月29日 下午5:33:43
*
*/
@Service
public class GoodsKindInventedServiceImpl implements GoodsKindInventedService{
	
	@Resource
	private GoodsKindInventedDao inventedDao;
	
	/**
	 * 批量更新虚拟分类排序
	 * @param list<Map<String,Object>> goodsKindInventedId：虚拟分类ID;kindSort:虚拟分类排序
	 * @return
	 */
	public ShopsResult modifyGoodsKindInventedSort(JSONArray list) {
		ShopsResult sr = new ShopsResult(1,"更新成功!");
		
		List<Map<String,Object>> kindList = new ArrayList<Map<String,Object>>();
		for(Integer i = 0; i < list.size(); i++) {
			Map<String,Object> map = new HashMap<String,Object>();
			map.put("goodsKindInventedId", list.getJSONObject(i).get("goodsKindInventedId"));
			map.put("kindSort", list.getJSONObject(i).get("kindSort"));
			kindList.add(map);
		}
		
		Integer count = inventedDao.modifyGoodsKindInventedSort(kindList);
		sr.setCount(count);
		return sr;
	}
	/**
	 * 删除指定虚拟分类下的商品或删除指定ID的虚拟分类下商品
	 * @param goods_kind_invented_id
	 * @param goods_kind_invented_goods_id
	 * @return
	 */
	public ShopsResult deleteGoodsKindInventedGoods(Integer goods_kind_invented_id , Integer goods_kind_invented_goods_id) {
		ShopsResult sr = new ShopsResult(1, "操作成功!");
		Map<String,Object> map = new HashMap<String,Object>();
		map.put("goods_kind_invented_id", goods_kind_invented_id);
		map.put("goods_kind_invented_goods_id", goods_kind_invented_goods_id);
		
		inventedDao.deleteGoodsKindInventedGoods(map);
		
		
		return sr;
	}
	/**
	 * 查询店铺商品信息，排除指定虚拟分类
	 * @param shop_unique 店铺编号
	 * @param goods_kind_invented_id 虚拟分类ID
	 * @param page 页码
	 * @param limit 单页查询数量
	 * @param search_name 输入框搜索内容
	 * @return
	 */
	public ShopsResult queryGoodsList(String shop_unique, Integer goods_kind_invented_id, Integer page , Integer limit, String search_name,String barcodeList,String parUnique,String kindUnique) {
		ShopsResult sr = new ShopsResult(1, "查询成功!");
		
		Map<String,Object> map = new HashMap<String,Object>();
		map.put("shop_unique", shop_unique);
		map.put("startNum", (page - 1) * limit);
		map.put("pageSize", limit);
		map.put("search_name", search_name);
		map.put("goods_kind_invented_id", goods_kind_invented_id);
		map.put("parUnique", parUnique);
		map.put("kindUnique", kindUnique);
		map.put("list", barcodeList == null ? null : barcodeList.split(","));
		
		List<Map<String,Object>> goodsList = inventedDao.queryGoodsList(map);
		Integer goodsCount = inventedDao.queryGoodsCount(map);
		
		sr.setData(goodsList);
		sr.setCount(goodsCount);
		
		return sr;
	}
	/**
	 * 添加或更新虚拟分类信息
	 * @param goods_kind_invented_id
	 * @param goods_kind_unique
	 * @param goods_kind_name
	 * @param goods_list
	 * @param shop_unique
	 * @param valid_type 1、有效；0、无效
	 * @return
	 */
	public ShopsResult addGoodsKindInvented(Integer goods_kind_invented_id,Integer goods_kind_unique,String goods_kind_name,String goods_list,String shop_unique,Integer valid_type) {
		ShopsResult sr = new ShopsResult(1, "操作成功!");
		Map<String,Object> map = new HashMap<String,Object>();
		map.put("shop_unique", shop_unique);
		
		if(null == goods_kind_invented_id) {
			//新建虚拟分类信息
			if(null == goods_kind_unique) {
				//创建虚拟商品分类信息
				goods_kind_unique = inventedDao.queryMaxGoodsKindUnique(map) + 1;
			}
			
			//获取虚拟分类的排序
			Integer kindSort = inventedDao.queryMaxShopSort(shop_unique);
			map.put("kind_sort", kindSort);
		}
		map.put("goods_kind_invented_id", goods_kind_invented_id);
		map.put("goods_kind_unique", goods_kind_unique);
		map.put("goods_kind_name", goods_kind_name);
		map.put("shop_unique", shop_unique);
		
		if(null != valid_type && valid_type == 0) {
			map.put("valid_type", 0);
			inventedDao.updateGoodsKindInventedMsg(map);
			
			Map<String,Object> detail = inventedDao.queryGoodsInventedDetail(goods_kind_invented_id + "");
			map.put("shop_unique", shop_unique);
			map.put("kind_sort", detail.get("kind_sort"));
			//需要将序号大于该ID的序号-1，防止拖动排序时，产生相同的值
			inventedDao.subGoodsKindSort(map);
			return sr;
		}
		
		JSONArray ja = JSONArray.parseArray(goods_list);
		map.put("list", ja);
		if(null == goods_kind_invented_id) {
			//新建
			inventedDao.addNewGoodsKindInventedMsg(map);
			//保存商品列表
			if(null != ja && !ja.isEmpty()) {
				inventedDao.addGoodsKindInventedGoodsList(map);
			}
		}else {
			//更新
			inventedDao.updateGoodsKindInventedMsg(map);
			//删除现有商品信息
			inventedDao.deleteGoodsKindInventedGoods(map);
			//新增商品信息
			if(null != ja && !ja.isEmpty()) {
				inventedDao.addGoodsKindInventedGoodsList(map);
			}
		}
		
		return sr;
	}
	/**
	 * 查询虚拟分类下的商品列表信息
	 * @param goods_kind_invented_id 虚拟分类ID
	 * @param goods_kind_unique 虚拟分类编号
	 * @return
	 */
	public ShopsResult queryGoodsKindInventedGoodsList(Integer goods_kind_invented_id,String goods_kind_unique,Integer page,Integer limit) {
		ShopsResult sr = new ShopsResult(1, "查询成功!");
		Map<String,Object> map = new HashMap<String,Object>();
		map.put("goods_kind_invented_id", goods_kind_invented_id);
		map.put("goods_kind_unique", goods_kind_unique);
		if(null != page && limit != null) {
			map.put("startNum", (page - 1) * limit);
			map.put("limit", limit);
		}
		
		List<Map<String,Object>> goodsList = inventedDao.queryGoodsKindInventedGoodsList(map);
		Integer count = inventedDao.queryGoodsKindInventedGoodsCount(map);
		sr.setCount(count);
		sr.setData(goodsList);
		
		return sr;
	}
	
	/**
	 * 查询店铺内虚拟分类信息
	 * @param shop_unique 店铺编号
	 * @param page 页码
	 * @param pageSize 单页查询数量
	 * @return
	 */
	public ShopsResult queryGoodsKindInventedList(String shop_unique ,String search_name, Integer page, Integer pageSize) {
		ShopsResult sr = new ShopsResult(1,"查询成功!");
		Map<String,Object> map = new HashMap<String,Object>();
		map.put("shop_unique", shop_unique);
		map.put("startNum", (page - 1) * pageSize);
		map.put("pageSize", pageSize);
		map.put("search_name",search_name);
		
		List<Map<String,Object>> list = inventedDao.queryGoodsKindInventedList(map);
		Integer count = inventedDao.queryGoodsKindInventedCount(map);
		
		sr.setCount(count);
		sr.setData(list);
		
		return sr;
	}
}
