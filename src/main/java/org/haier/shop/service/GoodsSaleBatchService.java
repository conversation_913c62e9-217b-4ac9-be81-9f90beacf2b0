package org.haier.shop.service;

import org.haier.shop.entity.ShopStockDetail;
import org.haier.shop.params.goods.OutStockParam;
import org.haier.shop.params.goods.ShopStockData;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * @Description
 * @ClassName GoodsSaleBatchService
 * <AUTHOR>
 * @Date 2024/5/24 15:36
 **/
public interface GoodsSaleBatchService {
    Map<String, BigDecimal> addAndAuditBatch(ShopStockDetail shopStockDetail, OutStockParam outStockParam);

    void addAndWaitAuditBatch(ShopStockDetail shopStockDetail, OutStockParam outStockParam);

    Map<String, BigDecimal> subGoodsBatchCount(ShopStockDetail shopStockDetail, List<ShopStockData> shopStockDataList);
}
