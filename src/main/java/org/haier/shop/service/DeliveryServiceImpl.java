package org.haier.shop.service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;

import org.haier.shop.dao.DeliveryDao;
import org.haier.shop.entity.ShopCourier;
import org.haier.shop.util.PurResult;
import org.haier.shop.util.ShopsResult;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Service
@Transactional
public class DeliveryServiceImpl implements DeliveryService{
	
	@Resource
	private DeliveryDao deliveryDao;
	
	public Map<String, Object> queryShopDelivery(String shop_unique) {
		return deliveryDao.queryShopDelivery(shop_unique);
	}


	public ShopsResult updateShopDelivery(Map<String, Object> params) {
		ShopsResult shopsResult = new ShopsResult();
		int count = deliveryDao.updateShopDelivery(params);
		
		if(count > 0){
			shopsResult.setStatus(1);
			shopsResult.setMsg("修改成功！");
		}
		
		return shopsResult;
	}


	public PurResult getShopCourierList(ShopCourier shopCourier) {
		PurResult result = new PurResult();
		int count = deliveryDao.getShopCourierListCount(shopCourier);
		List<ShopCourier> list = deliveryDao.getShopCourierList(shopCourier);
		
		result.setStatus(1);
		result.setMsg("成功");
		result.setCount(count);
		result.setData(list);
		return result;
	}


	public ShopsResult addShopCourier(ShopCourier shopCourier) {
		ShopsResult shopsResult = new ShopsResult();
		int count = deliveryDao.addShopCourier(shopCourier);
		
		if(count > 0){
			shopsResult.setStatus(1);
			shopsResult.setMsg("添加成功！");
		}
		
		return shopsResult;
	}


	public ShopCourier getShopCourier(String courier_id) {
		return deliveryDao.getShopCourier(courier_id);
	}


	public ShopsResult updateShopCourier(ShopCourier shopCourier) {
		ShopsResult shopsResult = new ShopsResult();
		int count = deliveryDao.updateShopCourier(shopCourier);
		
		if(count > 0){
			shopsResult.setStatus(1);
			shopsResult.setMsg("修改成功！");
		}
		
		return shopsResult;
	}


	public ShopsResult deleteShopCourier(String courier_id) {
		ShopsResult shopsResult = new ShopsResult();
		int count = deliveryDao.deleteShopCourier(courier_id);
		
		if(count > 0){
			shopsResult.setStatus(1);
			shopsResult.setMsg("删除成功！");
		}
		
		return shopsResult;
	}


	public PurResult getShopSpecialList(Map<String, Object> params) {
		PurResult result = new PurResult();
		params.put("is_special", "1");//特殊商品
		int count = deliveryDao.getShopSpecialListCount(params);
		List<Map<String ,Object>> list = deliveryDao.getShopSpecialList(params);
		result.setStatus(1);
		result.setCount(count);
		result.setData(list);
		result.setMsg("查询成功！");
		return result;
	}


	public ShopsResult deleteShopSpecial(String goods_id) {
		ShopsResult shopsResult = new ShopsResult();
		Map<String ,Object> params = new HashMap<String, Object>();
		params.put("goods_id", goods_id);
		params.put("is_special", "0");//是否特殊商品：0否 1是
		params.put("is_one_delivery", "0");//是否单件配送：0否 1是
		params.put("single_delivery_fee", "0.00");//特殊商品单件配送费
		
		int count = deliveryDao.updateShopSpecial(params);
		
		if(count > 0){
			shopsResult.setStatus(1);
			shopsResult.setMsg("删除成功！");
		}
		
		return shopsResult;
	}


	public PurResult getShopGoodsList(Map<String, Object> params) {
		PurResult result = new PurResult();
		params.put("is_special", "0");//是否特殊商品：0否 1是
		int count = deliveryDao.getShopSpecialListCount(params);
		List<Map<String ,Object>> list = deliveryDao.getShopSpecialList(params);
		result.setStatus(1);
		result.setCount(count);
		result.setData(list);
		result.setMsg("查询成功！");
		return result;
	}


	public ShopsResult addShopSpecial(String goods_infos) {
		ShopsResult shopsResult = new ShopsResult();
		String[] goods_info = goods_infos.split(",");
		
		int count = 0;
		for(int i=0;i<goods_info.length;i++){
			String[] special = goods_info[i].split(":");
			Map<String ,Object> params = new HashMap<String, Object>();
			params.put("goods_id", special[0]);
			params.put("is_special", "1");//是否特殊商品：0否 1是
			params.put("is_one_delivery", special[1]);//是否单件配送：0否 1是
			params.put("single_delivery_fee", special[2]);//特殊商品单件配送费
			
			count = count + deliveryDao.updateShopSpecial(params);
		}
		
		if(count == goods_info.length){
			shopsResult.setStatus(1);
			shopsResult.setMsg("添加成功！");
		}
		
		return shopsResult;
	}

}
