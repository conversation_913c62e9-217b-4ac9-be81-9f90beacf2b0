package org.haier.shop.service;

import org.haier.shop.params.goodsBatch.GoodsBatchChooseParams;
import org.haier.shop.params.goodsBatch.GoodsBatchChooseViewParams;
import org.haier.shop.params.goodsBatch.GoodsBatchExportParams;
import org.haier.shop.params.goodsBatch.GoodsBatchQueryParams;
import org.haier.shop.util.PurResult;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

/**
* @Description 商品入库批次表
* @ClassName GoodsBatch
* <AUTHOR> 
* @Date 2024-04-28
**/
public interface GoodsBatchService {

    PurResult selectPage(GoodsBatchQueryParams params);

    PurResult batchSelectList(GoodsBatchChooseParams params);

    PurResult batchSelectListView(GoodsBatchChooseViewParams params);

    void exportToExcel(GoodsBatchExportParams params, HttpServletRequest request, HttpServletResponse response);
}