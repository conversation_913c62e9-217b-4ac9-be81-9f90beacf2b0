package org.haier.shop.service;

import java.math.BigDecimal;
//import java.util.ArrayList;
//import java.util.Calendar;
//import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;

import org.haier.shop.dao.DataGoodsDao;
import org.haier.shop.util.ShopsResult;
import org.springframework.stereotype.Service;
@Service
public class DataGoodsServiceImpl implements DataGoodsService{
	
	@Resource
	private DataGoodsDao dataDao;
	
	@Override
	public ShopsResult queryDataGoodsHotMap() {
		ShopsResult sr=new ShopsResult();
		//查询热力图
		List<Map<String,Object>> data=dataDao.queryDataGoodsHotMap();
		//查询商品总销量
		Integer count=dataDao.queryDataGoodCount();
		sr.setCord(count);
		if(null==data||data.isEmpty()){
			sr.setStatus(2);
			sr.setMsg("没有满足条件的订单信息！");
			return sr;
		}
		sr.setStatus(1);
		sr.setMsg("查询成功！");
		sr.setData(data);
		return sr;
	}

	@Override
	public ShopsResult queryDataGoodsTopByCount() {
		ShopsResult sr=new ShopsResult();
		//查询商品销量排名
		List<Map<String,Object>> data=dataDao.queryDataGoodsTopByCount();
		Integer sum=dataDao.queryDataGoodCount();
		for (Map<String, Object> map : data) {
//			BigDecimal count=(BigDecimal) map.get("count");
//			System.out.println(map);
			Integer count=Integer.parseInt(map.get("count").toString());
			BigDecimal cc= BigDecimal.valueOf(count.doubleValue()/sum*100).setScale(2,   BigDecimal.ROUND_DOWN);
			map.put("proportion", cc+"%");
		}

		//查询销售金额最高
		Map<String, Object> goodsSalePrice=dataDao.queryDataGoodsSaleGoodsMax();
		//查询利润最大
		Map<String, Object> goodsLiRun=dataDao.queryDataGoodsLiRunMax();
		goodsSalePrice.putAll(goodsLiRun);
		sr.setCord(goodsSalePrice);
		if(null==data||data.isEmpty()){
			sr.setStatus(2);
			sr.setMsg("没有满足条件的订单信息！");
			return sr;
		}
		sr.setStatus(1);
		sr.setMsg("查询成功！");
		sr.setData(data);
		return sr;
	}

	@Override
	public ShopsResult queryGroupByAge() {
		ShopsResult sr=new ShopsResult();
//		List<Map<String, Object>> list=new ArrayList<>();
//		String age_nums[]={"6-18","19-29","30-39","40-59","60-80"};
//		for (String age_num : age_nums) {
//			HashMap< String, Object> params=new HashMap<String,Object>()
//			params.put("age_start", age_num.split("-")[0]);
//			params.put("age_end", age_num.split("-")[1]);
//			Integer count =dataDao.queryGroupByAge(params);
//			HashMap< String, Object> params2=new HashMap<String,Object>()
//			params2.put("count", count);
//			params2.put("age_num", age_num+"岁");
//			list.add(params2);
//		}
//		sr.setData(list);
		
		List<Map<String,Object>> list=dataDao.queryGroupByAgeCount();
		sr.setStatus(1);
		sr.setMsg("查询成功！");
		sr.setData(list);
		return sr;
	}

	@Override
	public ShopsResult queryBuyMoneyGroupByAge() {
		ShopsResult sr=new ShopsResult();
//		List<Map<String, Object>> list=new ArrayList<>();
//		String age_nums[]={"6-18","19-29","30-39","40-59","60-80"};
//		for (String age_num : age_nums) {
//			HashMap< String, Object> params=new HashMap<String,Object>()
//			params.put("age_start", age_num.split("-")[0]);
//			params.put("age_end", age_num.split("-")[1]);
//			Double money =dataDao.queryBuyMoneyGroupByAge(params);
//			params.clear();
//			params.put("money", money);
//			params.put("age_num", age_num+"岁");
//			list.add(params);
//		}
//		sr.setData(list);
		List<Map<String,Object>> list=dataDao.queryBuyMoneyGroupByAgeNum();
		sr.setStatus(1);
		sr.setMsg("查询成功！");
		sr.setData(list);
		return sr;
	}

	@Override
	public ShopsResult queryBuyMoneyGroupByCity() {
		ShopsResult sr=new ShopsResult();
		List<Map<String,Object>> data=dataDao.queryDataGoodsTopByCity();
		List<Map<String,Object>> sale_data=dataDao.querySaleMoneyTopByCity();
		sr.setCord(data);
//		List<Map<String,Object>> sd=new ArrayList<>();
//		for(int i=0;i<sale_data.size();i++){
//			Map<String,Object> map=new HashMap<String,Object>()
//			map.putAll(sale_data.get(i));
//			map.put("area_dict_content", sale_data.get(i).get("area_dict_content").toString().subSequence(0, 5));
//		}
		sr.setData(sale_data);
		sr.setStatus(1);
		sr.setMsg("查询成功！");
		return sr;
	}
}
