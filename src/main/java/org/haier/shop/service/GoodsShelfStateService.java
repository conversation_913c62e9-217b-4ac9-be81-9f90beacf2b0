package org.haier.shop.service;

import java.util.Map;

import org.haier.shop.util.PurResult;
import org.haier.shop.util.ShopsResult;

import javax.servlet.http.HttpServletRequest;

public interface GoodsShelfStateService {
	
	/**
	 * 商品信息查询
	 * @param shop_unique 店铺编号
	 * @param goods_message 输入的商品信息
	 * @param goods_kind_unique 商品分类编号
	 * @param shelf_state 上架状态：1、已上架；2、已下架
	 * @param time 时长
	 */
	public PurResult queryShelfStateGoodsMessage(Map<String ,Object> params);
	
	/**
	 * 下架所有搜索商品
	 * @param params
	 * @return
	 */
	public ShopsResult downQueryShelfStateGoodsMessage(Map<String,Object> params);
	
	/**
	 * 修改全部上下架状态
	 * @param shop_unique 店铺编号
	 * @param state 上架状态：1、已上架；2、已下架
	 * @param type 修改类型：1、线上  2线下收银
	 * @return
	 */
	public ShopsResult updateAllShelfState(String shop_unique,String state,String type);
	
	/**
     * 修改商品上下架状态
     *
     * @param goods_ids   商品id集合，已逗号隔开
     * @param shelf_state 上架状态：1、已上架；2、已下架
     * @param staff_id
     * @return
     */
	public ShopsResult updateShelfState(String goods_ids, String shelf_state, String staff_id, HttpServletRequest request);

	/**
     * 修改商品pc上下架状态
     *
     * @param goods_ids      商品id集合，已逗号隔开
     * @param pc_shelf_state pc收银上架状态：1、已上架；2、已下架
     * @param staff_id
     * @return
     */
	public ShopsResult updatePcShelfState(String goods_ids, String pc_shelf_state, String staff_id, HttpServletRequest request);
	
	/**
     * 修改商品售价
     *
     * @param goods_id             商品id
     * @param goods_sale_price     售价
     * @param goods_web_sale_price 网购价
     * @param staff_id
     * @return
     */
	public ShopsResult updateGoodsSalePrice(String goods_id, String goods_sale_price, String goods_web_sale_price, String staff_id, HttpServletRequest request);
}
