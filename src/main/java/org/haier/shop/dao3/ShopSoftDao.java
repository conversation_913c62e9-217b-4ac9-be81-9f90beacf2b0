package org.haier.shop.dao3;

import java.util.List;
import java.util.Map;

/**
 *商家软件管理Dao
 */
public interface ShopSoftDao {

	/**
	 * 获取所有的员工信息，并存储到缓存
	 * @return
	 */
	public List<Map<String,Object>> queryUserMsgList();
	/**
	 * 查询店铺软件列表
	 * @param shop_unique 店铺编号
	 * @param message 激活码/设备编号
	 * @param status 状态：1未激活 2已激活 3禁用
	 * @param field 排序字段
	 * @param order 排序正序倒叙
	 * @param startNum
	 * @param pageSize
	 * @return
	 */
	public List<Map<String ,Object>> queryShopSoftList(Map<String,Object> params);
	
	/**
	 * 查询店铺软件列表总条数
	 * @param shop_unique 店铺编号
	 * @param message 激活码/设备编号
	 * @param status 状态：1未激活 2已激活 3禁用
	 * @param field 排序字段
	 * @param order 排序正序倒叙
	 * @param startNum
	 * @param pageSize
	 * @return
	 */
	public Integer queryShopSoftListCount(Map<String,Object> params);
	
	/**
	 * 查询店铺软件购买记录列表
	 * @param shop_unique 店铺编号
	 * @param message 激活码/交易流水
	 * @param bug_type 支付类型：1支付宝 2微信
	 * @param startNum
	 * @param pageSize
	 * @return
	 */
	public List<Map<String ,Object>> queryShopSoftProfitList(Map<String,Object> params);
	
	/**
	 * 查询店铺软件购买记录列表总条数
	 * @param shop_unique 店铺编号
	 * @param message 激活码/交易流水
	 * @param bug_type 支付类型：1支付宝 2微信
	 * @param startNum
	 * @param pageSize
	 * @return
	 */
	public Integer queryShopSoftProfitListCount(Map<String,Object> params);
	
	/**
	 * 删除软件购买记录
	 * @param id
	 * @return
	 */
	public void deleteShopSoftProfit(String id);
	
	/**
	 * 查询系统软件设置列表
	 * @return
	 */
	public List<Map<String ,Object>> querySysSoftSettingList();
	
	/**
	 * 查询系统软件设置详情
	 * @return
	 */
	public Map<String ,Object> querySysSoftSetting(String code);
	
	/**
	 * 添加商家购买软件记录表
	 * @return
	 */
	public Integer addShopSoftProfit(Map<String,Object> params);
	
	/**
	 * 修改商家购买软件记录表
	 * @return
	 */
	public Integer updateShopSoftProfit(Map<String,Object> params);
	
	/**
	 * 获取商家购买软件记录详情
	 * @return
	 */
	public Map<String ,Object> getShopSoftProfit(Map<String,Object> params);
	
	/**
	 * 添加商家购买软件分润表
	 * @return
	 */
	public Integer addShopSoftProfitDetail(Map<String,Object> params);
	
	/**
	 * 获取代理账号信息
	 * @return
	 */
	public Map<String ,Object> queryAgencyAccount(Map<String,Object> params);
	
	/**
	 * 添加代理账号信息
	 * @return
	 */
	public Integer addAgencyAccount(Map<String,Object> params);
	
	/**
	 * 修改代理商发展店铺收益和账号余额
	 * @return
	 */
	public Integer updateAgencyAccount(Map<String,Object> params);
	
	/**
	 * 获取商家代理及上级代理信息
	 * @return
	 */
	public Map<String ,Object> queryAgencyInfo(String shop_unique);
	
	/**
	 * 添加店铺软件信息
	 * @return
	 */
	public Integer addShopSoft(Map<String,Object> params);
	
	/**
	 * 获取店铺软件信息
	 * @return
	 */
	public Map<String ,Object> queryShopSoft(String cdkey_code);
	
	/**
	 * 修改店铺软件信息
	 * @return
	 */
	public Integer updateShopSoft(Map<String,Object> params);
	
	/**
	 * 查询是否有此激活码
	 * @return
	 */
	public Integer getCdkeyCodeCount(String cdkey_code);
	
	/**
	 * 获取店铺软件支付信息
	 * @return
	 */
	public Map<String ,Object> queryPayInfo(String out_trade_no);
}
