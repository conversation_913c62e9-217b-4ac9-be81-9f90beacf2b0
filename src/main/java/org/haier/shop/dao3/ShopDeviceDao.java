package org.haier.shop.dao3;

import java.util.List;
import java.util.Map;

/**
 *商家设备管理Dao
 */
public interface ShopDeviceDao {

	/**
	 * 查询店铺设备列表
	 * @param shop_unique 店铺编号
	 * @param status 状态：1未激活 2已激活 3禁用
	 * @param start_time 开始时间
	 * @param end_time 结束时间
	 * @param startNum
	 * @param pageSize
	 * @return
	 */
	public List<Map<String ,Object>> queryShopDeviceList(Map<String,Object> params);
	
	/**
	 * 查询店铺设备列表总条数
	 * @param shop_unique 店铺编号
	 * @param status 状态：1未激活 2已激活 3禁用
	 * @param start_time 开始时间
	 * @param end_time 结束时间
	 * @param startNum
	 * @param pageSize
	 * @return
	 */
	public Integer queryShopDeviceListCount(Map<String,Object> params);
	
	/**
	 * 查询店铺设备汇总列表
	 * @param shop_unique 店铺编号
	 * @return
	 */
	public List<Map<String ,Object>> queryShopDeviceSum(String shop_unique);
	
	/**
	 * 查询店铺已激活设备数量
	 * @param shop_unique 店铺编号
	 * @param device_type_id 设备类型id
	 * @return
	 */
	public Integer queryActivatedNum(Map<String,Object> params);
	
	/**
	 * 查询店铺设备详情
	 * @param shop_unique 店铺编号
	 * @return
	 */
	public Map<String ,Object> queryShopDeviceDetail(String shop_device_id);
	
	/**
	 * 查询店铺设备申请列表
	 * @param shop_unique 店铺编号
	 * @param message 设备名称/经办人
	 * @param apply_status 申请状态：1 申请中 2待收货 3已完成 10已取消
	 * @param start_time 开始时间
	 * @param end_time 结束时间
	 * @param startNum
	 * @param pageSize
	 * @return
	 */
	public List<Map<String ,Object>> queryShopDeviceApplyList(Map<String,Object> params);
	
	/**
	 * 查询店铺设备申请列表总条数
	 * @param shop_unique 店铺编号
	 * @param message 设备名称/经办人
	 * @param apply_status 申请状态：1 申请中 2待收货 3已完成 10已取消
	 * @param start_time 开始时间
	 * @param end_time 结束时间
	 * @param startNum
	 * @param pageSize
	 * @return
	 */
	public Integer queryShopDeviceApplyListCount(Map<String,Object> params);
	
	/**
	 * 查询设备类型列表
	 * @return
	 */
	public List<Map<String ,Object>> queryDeviceTypeList();
	
	/**
	 * 查询店铺代理商code
	 * @return
	 */
	public String queryShopAgencyCode(String shop_unique);
	
	/**
	 * 添加设备申请主表
	 */
	public Integer addShopDeviceApply(Map<String,Object> params);
	
	/**
	 * 添加设备申请详情表
	 * @return
	 */
	public Integer addShopDeviceApplyDeatil(List<Map<String,Object>> list);
	
	/**
	 * 修改设备申请记录
	 * @return
	 */
	public Integer updateDeviceApply(Map<String, Object> params);
	
	/**
	 * 修改设备申请支付记录
	 * @return
	 */
	public Integer updateDevicePay(Map<String, Object> params);
	
	/**
	 * 查询设备申请记录信息
	 * @param id 设备申请id
	 * @return
	 */
	public Map<String ,Object> queryDeviceApply(String id);
	
	/**
	 * 查询设备申请记录详细列表
	 * @param id 设备申请id
	 * @return
	 */
	public List<Map<String ,Object>> queryDeviceApplyDetailList(String apply_id);
	
	/**
	 * 查询店铺设备退换列表
	 * @param shop_unique 店铺编号
	 * @param message 设备名称/设备编号/经办人
	 * @param shop_service_apply_type 申请类型：1、换货；2、退货；
	 * @param shop_service_handle_status 订单处理状态：1、未处理（新申请）；2、已受理；3、服务进行中；4、服务完成；10、申请以取消；
	 * @param start_time 开始时间
	 * @param end_time 结束时间
	 * @param startNum
	 * @param pageSize
	 * @return
	 */
	public List<Map<String ,Object>> shopDeviceReturnList(Map<String,Object> params);
	
	/**
	 * 查询店铺设备退换列表总条数
	 * @param shop_unique 店铺编号
	 * @param message 设备名称/设备编号/经办人
	 * @param shop_service_apply_type 申请类型：1、换货；2、退货；
	 * @param shop_service_handle_status 订单处理状态：1、未处理（新申请）；2、已受理；3、服务进行中；4、服务完成；10、申请以取消；
	 * @param start_time 开始时间
	 * @param end_time 结束时间
	 * @param startNum
	 * @param pageSize
	 * @return
	 */
	public Integer shopDeviceReturnListCount(Map<String,Object> params);
	
	/**
	 * 添加设备售后主表
	 */
	public Integer addShopService(Map<String,Object> params);
	
	/**
	 * 添加设备售后详情表
	 * @return
	 */
	public Integer addShopServiceDetail(List<Map<String,Object>> list);
	
	/**
	 * 查询设备退换记录信息
	 * @param id 设备退换id
	 * @return
	 */
	public Map<String ,Object> queryDeviceReturn(String id);
	
	/**
	 * 查询设备退换记录详细列表
	 * @param id 设备退换id
	 * @return
	 */
	public List<Map<String ,Object>> queryDeviceReturnDetailList(String shop_service_apply_no);
	
	/**
	 * 修改设备售后记录
	 * @return
	 */
	public Integer updateShopService(Map<String, Object> params);
	
	/**
	 * 查询店铺设备维修列表
	 * @param shop_unique 店铺编号
	 * @param message 设备名称/设备编号
	 * @param start_time 开始时间
	 * @param end_time 结束时间
	 * @param startNum
	 * @param pageSize
	 * @return
	 */
	public List<Map<String ,Object>> shopDeviceRepairList(Map<String,Object> params);
	
	/**
	 * 查询店铺设备维修列表总条数
	 * @param shop_unique 店铺编号
	 * @param message 设备名称/设备编号
	 * @param start_time 开始时间
	 * @param end_time 结束时间
	 * @param startNum
	 * @param pageSize
	 * @return
	 */
	public Integer shopDeviceRepairListCount(Map<String,Object> params);
	
	/**
	 * 获取设备激活码信息
	 * @return
	 */
	public Map<String ,Object> getShopDeviceCdkeyInfo(Map<String,Object> params);
	
	/**
	 * 添加设备申请支付记录
	 * @param pay_record_no 支付编号
	 * @param device_apply_id 设备申请id
	 * @param pay_money 支付金额
	 * @param pay_type 支付类型：1支付宝 2微信
	 * @return
	 */
	public Integer addShopDevicePay(Map<String,Object> params);
	
	/**
	 * 添加店铺信息
	 * @param shop_unique 店铺编号
	 * @param agency_code 代理商编号
	 * @param shop_name 店铺名称
	 * @return
	 */
	public Integer addShop(Map<String,Object> params);
	
	//查询设备申请支付记录
	public Map<String ,Object> updateShopDeviceApply(String pay_record_no);
}
