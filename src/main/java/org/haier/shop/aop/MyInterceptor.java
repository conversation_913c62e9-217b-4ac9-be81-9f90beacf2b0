package org.haier.shop.aop;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.apache.log4j.Logger;
import org.haier.shop.util.UtilForJAVA;
import org.springframework.web.servlet.HandlerInterceptor;
import org.springframework.web.servlet.ModelAndView;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.ObjectOutputStream;

public class MyInterceptor implements HandlerInterceptor {
	private Logger logger=Logger.getLogger(MyInterceptor.class);

	@Override
	public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler)
			throws Exception {
		response.setContentType("text/json;charset=UTF-8");
		response.setCharacterEncoding("UTF-8");


		if(null != handler && handler.toString().indexOf("org.haier.shop.controller.validate") >= 0) {
			return true;
		}

//		JSONObject object = null;
//		object = JSON.parseObject(JSON.toJSONString(handler));
//		if (!object.isEmpty()){
//			String beanType = object.getString("beanType");
//			if (StringUtils.isNotBlank(beanType) && beanType.startsWith("org.haier.shop.controller.validate")) return true;
//		}

		UtilForJAVA.recordLog(logger, request);
		return true;
	}

	public void postHandle(HttpServletRequest request, HttpServletResponse response, Object handler,
			ModelAndView modelAndView) throws Exception {
	}
	
	public void afterCompletion(HttpServletRequest request, HttpServletResponse response, Object handler, Exception ex)
			throws Exception {
	}

	public static boolean isSerializable(Object obj) {
		try {
			ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
			ObjectOutputStream objectOutputStream = new ObjectOutputStream(outputStream);
			objectOutputStream.writeObject(obj);
			objectOutputStream.close();
			outputStream.close();
			return true;
		} catch (IOException e) {
			return false;
		}
	}
}
