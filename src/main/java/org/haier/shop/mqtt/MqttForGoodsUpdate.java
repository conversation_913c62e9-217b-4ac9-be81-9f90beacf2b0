package org.haier.shop.mqtt;

import java.io.Serializable;
import java.math.BigDecimal;

/**
* @author: 作者:王恩龙
* @version: 2023年6月17日 上午10:51:45
* 本类仅用于商品修改后，向收银设备发送商品更新MQTT消息，不做其他用
*
*/
public class MqttForGoodsUpdate implements Serializable {
	private Long goods_id;
	private String goods_barcode;
	private Long shop_unique;
	private Integer goodsChengType;
	private BigDecimal goods_contain;
	private BigDecimal goods_in_price;

	private BigDecimal goodStockPrice;
	private String goods_standard;
	private String goods_name;
	private String goods_alias;
	private String goods_brand;
	private Integer pc_shelf_state;

	private Integer shelf_state;
	private BigDecimal goods_sale_price;
	private Long foreign_key;
	private String goods_cus_price;
	private Long goods_kind_unique;
	private BigDecimal goods_web_sale_price;
	private String update_time;
	private BigDecimal goods_count;
	private String goods_unit;
	private String supplierUnique;

	public Long getGoods_id() {
		return goods_id;
	}

	public void setGoods_id(Long goods_id) {
		this.goods_id = goods_id;
	}

	public String getGoods_barcode() {
		return goods_barcode;
	}

	public void setGoods_barcode(String goods_barcode) {
		this.goods_barcode = goods_barcode;
	}

	public Long getShop_unique() {
		return shop_unique;
	}

	public void setShop_unique(Long shop_unique) {
		this.shop_unique = shop_unique;
	}

	public Integer getGoodsChengType() {
		return goodsChengType;
	}

	public void setGoodsChengType(Integer goodsChengType) {
		this.goodsChengType = goodsChengType;
	}

	public BigDecimal getGoods_contain() {
		return goods_contain;
	}

	public void setGoods_contain(BigDecimal goods_contain) {
		this.goods_contain = goods_contain;
	}

	public BigDecimal getGoods_in_price() {
		return goods_in_price;
	}

	public void setGoods_in_price(BigDecimal goods_in_price) {
		this.goods_in_price = goods_in_price;
	}

	public BigDecimal getGoodStockPrice() {
		return goodStockPrice;
	}

	public void setGoodStockPrice(BigDecimal goodStockPrice) {
		this.goodStockPrice = goodStockPrice;
	}

	public String getGoods_standard() {
		return goods_standard;
	}

	public void setGoods_standard(String goods_standard) {
		this.goods_standard = goods_standard;
	}

	public String getGoods_name() {
		return goods_name;
	}

	public void setGoods_name(String goods_name) {
		this.goods_name = goods_name;
	}

	public String getGoods_alias() {
		return goods_alias;
	}

	public void setGoods_alias(String goods_alias) {
		this.goods_alias = goods_alias;
	}

	public String getGoods_brand() {
		return goods_brand;
	}

	public void setGoods_brand(String goods_brand) {
		this.goods_brand = goods_brand;
	}

	public Integer getPc_shelf_state() {
		return pc_shelf_state;
	}

	public void setPc_shelf_state(Integer pc_shelf_state) {
		this.pc_shelf_state = pc_shelf_state;
	}

	public Integer getShelf_state() {
		return shelf_state;
	}

	public void setShelf_state(Integer shelf_state) {
		this.shelf_state = shelf_state;
	}

	public BigDecimal getGoods_sale_price() {
		return goods_sale_price;
	}

	public void setGoods_sale_price(BigDecimal goods_sale_price) {
		this.goods_sale_price = goods_sale_price;
	}

	public Long getForeign_key() {
		return foreign_key;
	}

	public void setForeign_key(Long foreign_key) {
		this.foreign_key = foreign_key;
	}

	public String getGoods_cus_price() {
		return goods_cus_price;
	}

	public void setGoods_cus_price(String goods_cus_price) {
		this.goods_cus_price = goods_cus_price;
	}

	public Long getGoods_kind_unique() {
		return goods_kind_unique;
	}

	public void setGoods_kind_unique(Long goods_kind_unique) {
		this.goods_kind_unique = goods_kind_unique;
	}

	public BigDecimal getGoods_web_sale_price() {
		return goods_web_sale_price;
	}

	public void setGoods_web_sale_price(BigDecimal goods_web_sale_price) {
		this.goods_web_sale_price = goods_web_sale_price;
	}

	public String getUpdate_time() {
		return update_time;
	}

	public void setUpdate_time(String update_time) {
		this.update_time = update_time;
	}

	public BigDecimal getGoods_count() {
		return goods_count;
	}

	public void setGoods_count(BigDecimal goods_count) {
		this.goods_count = goods_count;
	}

	public String getGoods_unit() {
		return goods_unit;
	}

	public void setGoods_unit(String goods_unit) {
		this.goods_unit = goods_unit;
	}

	public String getSupplierUnique() {
		return supplierUnique;
	}

	public void setSupplierUnique(String supplierUnique) {
		this.supplierUnique = supplierUnique;
	}

	@Override
	public String toString() {
		return "MqttForGoodsUpdate [goods_id=" + goods_id + ", goods_barcode=" + goods_barcode + ", shop_unique="
				+ shop_unique + ", goodsChengType=" + goodsChengType + ", goods_contain=" + goods_contain
				+ ", goods_in_price=" + goods_in_price + ", goodStockPrice=" + goodStockPrice + ", goods_standard=" + goods_standard + ", goods_name="
				+ goods_name + ", goods_alias=" + goods_alias + ", goods_brand=" + goods_brand + ", shelf_state="
				+ shelf_state + ", pc_shelf_state="
				+ pc_shelf_state + ", goods_sale_price=" + goods_sale_price + ", foreign_key=" + foreign_key
				+ ", goods_cus_price=" + goods_cus_price + ", goods_kind_unique=" + goods_kind_unique
				+ ", goods_web_sale_price=" + goods_web_sale_price + ", update_time=" + update_time + ", goods_count="
				+ goods_count + ", goods_unit=" + goods_unit + "]";
	}
		
}