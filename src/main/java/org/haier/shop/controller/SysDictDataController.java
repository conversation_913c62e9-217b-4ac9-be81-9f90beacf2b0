package org.haier.shop.controller;
/**
* @author: 作者:王恩龙
* @version: 2023年6月14日 下午3:34:53
*
*/

import javax.annotation.Resource;

import org.haier.shop.service.SysDictDataService;
import org.haier.shop.util.ShopsResult;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

@RequestMapping("/sysDictData")
@Controller
public class SysDictDataController {
	@Resource
	private SysDictDataService dictDataService;
	
	/**
	 * 查询数据字典的键值对
	 * @param dict_type
	 * @return
	 */
	@RequestMapping("/querySysDictDataList.do")
	@ResponseBody
	public ShopsResult querySysDictDataList(String dict_type) {
		return dictDataService.querySysDictDataList(dict_type);
	}
}
