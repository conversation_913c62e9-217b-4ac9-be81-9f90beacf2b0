package org.haier.shop.controller.app;

import java.io.UnsupportedEncodingException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;

import org.haier.meituan.util.MUtil;
import org.haier.shop.service.SelfPurchaseService;
import org.haier.shop.service.ShopService;
import org.haier.shop.service.SupOrderService;
import org.haier.shop.util.PurResult;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;


/**
 * 进货订单查询控制器--商家app
 */

@Controller
@RequestMapping("/app/purchase")
public class AppPurchaseOrderController {
	
	@Resource
	private SupOrderService supOrderService;
	
	@Resource
	private ShopService shopService;
	
	@Resource
	private SelfPurchaseService selfPurchaseService;
	
	/**
	 * 获取进货单头部下拉列表
	 * @param shop_unique 店铺唯一标示
	 * @return
	 */
	@RequestMapping("/getPurchaseOrderTitleList.do")
	@ResponseBody
	public PurResult getPurchaseOrderTitleList(String shop_unique){
		PurResult result = new PurResult();
		try {
			List<Map<String ,Object>> list = new ArrayList<Map<String,Object>>();
			//获取商家信息
			Map<String ,Object> params = new HashMap<String, Object>();
			params.put("shop_unique", shop_unique);
			Map<String,Object> shopInfo = (Map<String, Object>) shopService.queryShopMessage(shop_unique).getData();
			if(shopInfo != null){
				Integer shop_class = Integer.parseInt(MUtil.strObject(shopInfo.get("shop_class")));//店铺分类：0普通商家；1：连锁；2加盟
				String is_other_purchase = MUtil.strObject(shopInfo.get("is_other_purchase"));//是否允许像其他供货商采购：0不允许 1允许
				if(shop_class == 0){
					Map<String ,Object> resultMap = new HashMap<String, Object>();
					resultMap.put("order_source", "1");
					resultMap.put("source_name", "云商进货单");
					list.add(resultMap);
				}else if(shop_class != 0 && is_other_purchase.equals("0")){
					Map<String ,Object> resultMap = new HashMap<String, Object>();
					resultMap.put("order_source", "1");
					resultMap.put("source_name", "总店进货单");
					list.add(resultMap);
				}else if(shop_class != 0 && is_other_purchase.equals("1")){
					Map<String ,Object> resultMap = new HashMap<String, Object>();
					resultMap.put("order_source", "1");
					resultMap.put("source_name", "云商/总店进货单");
					list.add(resultMap);
				}
				if(!is_other_purchase.equals("0")){
					Map<String ,Object> resultMap = new HashMap<String, Object>();
					resultMap.put("order_source", "2");
					resultMap.put("source_name", "自采购进货单");
					list.add(resultMap);
				}
			}
			
			result.setData(list);
			result.setStatus(1);
			result.setMsg("成功");
		} catch (Exception e) {
			result.setStatus(0);
			result.setMsg("异常");
		}
		return result;
	}
	
	/**
	 * 查询云商总店进货订单列表
	 * @param shop_unique
	 * @param supplier_info 供货商名称或手机号
	 * @param goods_info 商品名称或条码
	 * @param order_type 订单类型 0：自动下单；1：客户下单
	 * @param order_code 订单编号
	 * @param start_date 查询开始时间
	 * @param end_date 查询结束时间
	 * @param order_status 订单状态1: 待发货 2:待配送 3:配送中 4:已完成 ，全部时为空
	 * @param pageNum
	 * @param pageSize
	 * @return
	 */
	@RequestMapping("/getSupOrderList.do")
	@ResponseBody
	public PurResult getSupOrderList(
			@RequestParam(value="shop_unique",required=true)String shop_unique,
			String supplier_info,
			String goods_info,
			String order_type,
			String order_code,
			String start_date,
			String end_date,
			String order_status, 
			@RequestParam(value="pageNum",defaultValue="1")Integer pageNum, 
			@RequestParam(value="pageSize",defaultValue="8")Integer pageSize
			){
		try {
			supplier_info = new String(supplier_info.getBytes("ISO-8859-1"), "UTF-8");
			goods_info = new String(goods_info.getBytes("ISO-8859-1"), "UTF-8");
		} catch (UnsupportedEncodingException e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		}
		Map<String ,Object> params = new HashMap<String, Object>();
		params.put("shop_unique", shop_unique);
		params.put("supplier_info", supplier_info);
		params.put("goods_info", goods_info);
		params.put("order_type", order_type);
		params.put("order_code", order_code);
		params.put("start_date", start_date);
		params.put("end_date", end_date);
		params.put("order_status", order_status);
		params.put("startNum", (pageNum-1)*pageSize);
		params.put("pageSize", pageSize);
		return supOrderService.getSupOrderList(params,"1");
	}
	
	/**
	 * 获取云商总店进货单详情
	 * @param order_code 订单编号
	 * @return
	 */
	@RequestMapping("/getSupOrderDetail.do")
	@ResponseBody
	public PurResult getSupOrderDetail(String order_code){
		return supOrderService.getSupOrderDetail(order_code);
	}
	
	/**
	 * 取消云商进货单
	 * @param order_code 订单编号
	 * @param order_status 5:已取消
	 * @return
	 */	
	@RequestMapping("updateOrderStatus.do")
	@ResponseBody
	public PurResult updateOrderStatus(String order_code,String order_status){
		Map<String ,Object> params = new HashMap<String, Object>();
		params.put("order_code", order_code);
		params.put("order_status", order_status);
		return supOrderService.updateOrderStatus(params);
	}
	
	/**
	 * 查询自采购进货订单列表
	 * @param shop_unique
	 * @param supplier_info 供货商名称或手机号
	 * @param goods_info 商品名称或条码
	 * @param self_purchase_unique 订单编号
	 * @param start_date 查询开始时间
	 * @param end_date 查询结束时间
	 * @param purchase_status 采购单状态：1已完成 2已取消 ，全部时为空
	 * @param pageNum
	 * @param pageSize
	 * @return
	 */	
	@RequestMapping("getSelfPurchaseList.do")
	@ResponseBody
	public PurResult getSelfPurchaseList(
			@RequestParam(value="shop_unique",required=true)String shop_unique,
			String supplier_info,
			String goods_info,
			String self_purchase_unique,
			String start_date,
			String end_date,
			String purchase_status,
			@RequestParam(value="pageNum",defaultValue="1")Integer pageNum, 
			@RequestParam(value="pageSize",defaultValue="8")Integer pageSize
			){
		try {
			supplier_info = new String(supplier_info.getBytes("ISO-8859-1"), "UTF-8");
			goods_info = new String(goods_info.getBytes("ISO-8859-1"), "UTF-8");
		} catch (UnsupportedEncodingException e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		}
		Map<String ,Object> params = new HashMap<String, Object>();
		params.put("shop_unique", shop_unique);
		params.put("supplier_info", supplier_info);
		params.put("goods_info", goods_info);
		params.put("self_purchase_unique", self_purchase_unique);
		params.put("start_date", start_date);
		params.put("end_date", end_date);
		params.put("purchase_status", purchase_status);
		params.put("startNum", (pageNum-1)*pageSize);
		params.put("pageSize", pageSize);
		return selfPurchaseService.getSelfPurchaseList(params);
	}
	
	
	/**
	 * 查询自采购进货单详情
	 * @param shop_unique 店铺唯一标示
	 * @param self_purchase_unique 订单编号
	 * @return
	 */	
	@RequestMapping("getSelfPurchase.do")
	@ResponseBody
	public PurResult getSelfPurchase(String shop_unique,String self_purchase_unique){
		Map<String ,Object> params = new HashMap<String, Object>();
		params.put("shop_unique", shop_unique);
		params.put("self_purchase_unique", self_purchase_unique);
		return selfPurchaseService.getSelfPurchase(params);
	}
	
	/**
	 * 添加自采购进货单支付记录信息
	 * @param self_purchase_unique 自采购进货单唯一标示
	 * @param shop_unique 店铺唯一标示
	 * @param pay_money 本次支付金额
	 * @param staff_id 操作员工编号
	 * @param source_type 操作终端：1商家后台 2Android 3ios
	 * @param network_ip 操作网络ip
	 * @return
	 */	
	@RequestMapping("insertSelfPurchasePay.do")
	@ResponseBody
	public PurResult insertSelfPurchasePay(
			String self_purchase_unique,
			String shop_unique,
			String pay_money,
			String staff_id,
			String source_type,
			String network_ip
			){
		Map<String ,Object> params = new HashMap<String, Object>();
		params.put("self_purchase_unique", self_purchase_unique);
		params.put("shop_unique", shop_unique);
		params.put("pay_money", pay_money);
		params.put("staff_id", staff_id);
		params.put("source_type", source_type);
		params.put("network_ip", network_ip);
		return selfPurchaseService.insertSelfPurchasePay(params);
	}
	
	/**
	 * 取消自采购进货单
	 * @param self_purchase_unique 自采购进货单唯一标示
	 * @param shop_unique 店铺唯一标示
	 * @param update_staff_id 修改员工编号
	 * @aram stock_origin 操作来源：1、手机；2、PC端；3、web网页端；
	 * @return
	 */	
	@RequestMapping("updateSelfPurchase.do")
	@ResponseBody
	public PurResult updateSelfPurchase(
			String self_purchase_unique,
			String shop_unique,
			String update_staff_id,
			String stock_origin
			){
		Map<String ,Object> params = new HashMap<String, Object>();
		params.put("self_purchase_unique", self_purchase_unique);
		params.put("shop_unique", shop_unique);
		params.put("purchase_status", "2");
		params.put("update_staff_id", update_staff_id);
		params.put("stock_origin", stock_origin);
		return selfPurchaseService.updateSelfPurchase(params);
	}
}
