package org.haier.shop.controller.app;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.haier.shop.entity.Staff;
import org.haier.shop.entity.SysAction;
import org.haier.shop.entity.SysPermission;
import org.haier.shop.service.StaffService;
import org.haier.shop.service.SysMenuService;
import org.haier.shop.service.SysRoleService;
import org.haier.shop.util.PurResult;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

/**
 * 菜单权限控制器--商家app
 */

@Controller
@RequestMapping("/app/auth")
public class AppAuthController {
	
	@Autowired
    private SysRoleService roleService;
    
    @Autowired
    private SysMenuService menuService;
    
    @Autowired
    private StaffService staffService;
	
	/**
	 * 获取商家app菜单权限
	 * @param staff_account 登录账号
	 * @return
	 */
	@RequestMapping("/getShopAppAuthInfo.do")
	@ResponseBody
	public PurResult getPurchaseOrderTitleList(String staff_account){
		PurResult result = new PurResult();
		try {
			//获取商家信息
			Staff staff = staffService.getStaffByAccount(staff_account);
			//获取菜单权限列表
			Integer staff_position = staff.getStaff_position();
	    	String version = "";
	    	String terminal = "app";
			Integer type = staff.getShop_type();
			Integer shop_class = staff.getShop_class();
			if(shop_class == 0){
				version = "ordinary"; 
			}else if(shop_class == 1){
				version = "chain";
			}else if(shop_class == 2){
				version = "join";
			}else if(shop_class == 3){
				version = "admin";
				type = null;
				terminal = null;
			}
			Map<String ,Object> params = new HashMap<String, Object>();
			params.put("version", version);
			params.put("type", type);
			params.put("terminal", terminal);
			List<SysPermission> listLevel = new ArrayList<SysPermission>();
			List<SysAction> actionList = new ArrayList<SysAction>();
			if(staff_position == 3){//店主
	   			//获取一级菜单列表
	    		params.put("level", "1");
	        	listLevel = menuService.quertMenuList(params);
	        	for(int i=0;i<listLevel.size();i++){
	        		params.put("level", "2");
	        		params.put("parent_code", listLevel.get(i).getCode());
	        		List<SysPermission> listLevelTwo = menuService.quertMenuList(params);
	        		listLevel.get(i).setListLevelTwo(listLevelTwo);
	        	}
	        	//获取操作权限列表
	        	actionList = roleService.getActionList(params);
			}else{
				//获取角色关联一级菜单列表
				Integer staff_id = staff.getStaff_id();
				String role_code = (String) roleService.getRoleByStaffId(staff_id).get("role_code");
	        	params.put("role_code", role_code);
	    		params.put("level", "1");
	        	listLevel = roleService.getMenuListByRoleCode(params);
	        	for(int i=0;i<listLevel.size();i++){
	        		params.put("level", "2");
	        		params.put("parent_code", listLevel.get(i).getCode());
	        		List<SysPermission> listLevelTwo = roleService.getMenuListByRoleCode(params);
	        		listLevel.get(i).setListLevelTwo(listLevelTwo);
	        	}
	        	//获取角色关联的操作权限列表
	        	actionList = roleService.getActionListByRoleCode(params);
			}
			Map<String ,Object> resultMap = new HashMap<String, Object>();
			resultMap.put("listLevel", listLevel);
			resultMap.put("actionList", actionList);
			result.setData(resultMap);
			result.setStatus(1);
			result.setMsg("成功");
		} catch (Exception e) {
			result.setStatus(0);
			result.setMsg("异常");
		}
		return result;
	}
	
}
