package org.haier.shop.controller.h5;


import java.net.InetAddress;
import java.net.UnknownHostException;
import java.util.Map;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpSession;

import org.haier.shop.service.H5ShopRegisterService;
import org.haier.shop.service.ShopSoftService;
import org.haier.shop.service.SysAgreementService;
import org.haier.shop.util.HandleMessyCode;
import org.haier.shop.util.MUtil;
import org.haier.shop.util.ServletsUtil;
import org.haier.shop.util.ShopsResult;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

/**
 * h5扫码注册
 */
@Controller
@RequestMapping("/h5/register")
public class RegisterController {
	
	@Resource
	private H5ShopRegisterService h5ShopRegisterService;
	
	@Resource
	private ShopSoftService shopSoftService;
    
	@Resource
	private SysAgreementService sysAgreementService;
	
	//电子合同页面
	@RequestMapping("/esign.do")
	public String esign(String able_num,Model model){
		ShopsResult result = h5ShopRegisterService.queryESignInfo(able_num);
		model.addAttribute("result", result);
		return "/WEB-INF/h5Register/esign.jsp";
	}
	
	//注册页面
	@RequestMapping("/registerPage.do")
	public String register(String is_esign,String esign_num,Model model){
		model.addAttribute("is_esign", is_esign);
		model.addAttribute("esign_num", esign_num);
		return "/WEB-INF/h5Register/register.jsp";
	}
	
	//用户协议页面
	@RequestMapping("/userAgreePage.do")
	public String userAgreePage(Model model){
		//获取用户协议内容
		Map<String ,Object> userAgree = sysAgreementService.querySysAgreement("user_agree");
		model.addAttribute("userAgree",userAgree);
		return "/WEB-INF/h5Register/userAgree.jsp";
	}
	
	//设备申请页面
	@RequestMapping("/softBuy.do")
	public String softBuy(String shop_unique,String manager_account,String shop_name,Model model){
		model.addAttribute("shop_unique", shop_unique);
		model.addAttribute("manager_account", manager_account);
		model.addAttribute("shop_name", shop_name);
		return "/WEB-INF/h5Register/softBuy.jsp";
	}
	
	//支付成功页面
	@RequestMapping("/paySuccess.do")
	public String paySuccess(String NO,String manager_account,Model model){
		model.addAttribute("NO", NO);
		model.addAttribute("manager_account", manager_account);
		return "/WEB-INF/h5Register/paySuccess.jsp";
	}
	
	//完善店铺信息页面
	@RequestMapping("/shopInfo.do")
	public String shopInfo(String shop_unique,String manager_account,String pay_status,Model model){
		model.addAttribute("shop_unique", shop_unique);
		model.addAttribute("manager_account", manager_account);
		model.addAttribute("pay_status", pay_status);
		return "/WEB-INF/h5Register/shopInfo.jsp";
	}
	
	//选择地图页面
	@RequestMapping("/map.do")
	public String map(String shop_unique,String manager_account,String pay_status,Model model){
		model.addAttribute("shop_unique", shop_unique);
		model.addAttribute("manager_account", manager_account);
		model.addAttribute("pay_status", pay_status);
		return "/WEB-INF/h5Register/map.jsp";
	}
	
	//注册成功页面
	@RequestMapping("/registerSuccess.do")
	public String registerSuccess(String shop_unique,Model model){
		model.addAttribute("shop_unique", shop_unique);
		return "/WEB-INF/h5Register/registerSuccess.jsp";
	}
	
	/**
	 * 注册新店铺
	 * @param shop_name
	 * @param manager_account
	 * @param is_esign 是否需要签单 1不需要 2需要
	 * @param esign_num 电子签名编号
	 * @return
	 */
	@ResponseBody
	@RequestMapping("/register.do")
	public ShopsResult register(
			@RequestParam(value="shop_name",required=true)String shop_name,
			@RequestParam(value="manager_account",required=true)String manager_account,
			String is_esign,String esign_num){
		return h5ShopRegisterService.register(shop_name, manager_account, is_esign, esign_num);
	}
	
	/**
	 * 获取设备信息
	 * @return
	 */
	@ResponseBody
	@RequestMapping("/queryDeviceList.do")
	public ShopsResult queryDeviceList(){
		return h5ShopRegisterService.queryDeviceList();
	}
	
	/**
	 * 购买设备软件---微信支付
	 * @param shop_unique 店铺编号
	 * @return
	 */
	@ResponseBody
	@RequestMapping("/weChatPay.do")
	public ShopsResult weChatPay(HttpServletRequest request){
		Map<String, Object> params = HandleMessyCode.handle(ServletsUtil.getParameters(request));
		HttpSession session = request.getSession();
		String openid = MUtil.strObject(session.getAttribute("openid"));
		
        String spbill_create_ip = getIpAddr(request);
        
        String cdkey_code = shopSoftService.getCdkeyCode(10);
        
        params.put("openid", openid);
        params.put("spbill_create_ip", spbill_create_ip);
    	params.put("cdkey_code", cdkey_code);
		return h5ShopRegisterService.weChatPay(params);
	}
	
	/**
	 * 获取支付信息
	 * @param out_trade_no 支付编号
	 * @return
	 */
	@ResponseBody
	@RequestMapping("/queryPayInfo.do")
	public ShopsResult queryPayInfo(String out_trade_no){
		return h5ShopRegisterService.queryPayInfo(out_trade_no);
	}
	
	/**
	 * 修改店铺信息
	 * @param shop_unique 店铺编号
	 * @param shop_address_detail 店铺地址
	 * @param shopLatitude 纬度
	 * @param shopLongitude 经度
	 * @param shop_type 店铺类型:1、便利店；2、水果店；3、母婴店；4、益农中心站；5、益农标准站、简易站、专业站；0：其他
	 * @param manager_name 管理员姓名
	 * @param province 省份
	 * @param city 城市
	 * @param district 区域
	 * @param area_dict_num 区域编码
	 * @param manager_account 手机号
	 * @param pay_status 是否支付：2已支付
	 * @return
	 */
	@ResponseBody
	@RequestMapping("/updateShopInfo.do")
	public ShopsResult updateShopInfo(HttpServletRequest request){
		return h5ShopRegisterService.updateShopInfo(request);
	}
	
	/**
	 * 获取店铺信息
	 * @param shop_unique 店铺编号
	 * @return
	 */
	@ResponseBody
	@RequestMapping("/queryShopInfo.do")
	public ShopsResult queryShopInfo(String shop_unique){
		return h5ShopRegisterService.queryShopInfo(shop_unique);
	}
	
	/**
	 * 保存电子签单信息
	 * @param unicom_able_id 能人id
	 * @param esign_base64 电子签单base64
	 * @return
	 */
	@ResponseBody
	@RequestMapping("/saveEsign.do")
	public ShopsResult saveEsign(HttpServletRequest request){
		return h5ShopRegisterService.saveEsign(request);
	}
	
	/**
	 * 获取当前网络ip
	 * <AUTHOR>
	 * @param request
	 * @return
	 */
	public String getIpAddr(HttpServletRequest request) {
		String ipAddress = request.getHeader("x-forwarded-for");
		if (ipAddress == null || ipAddress.length() == 0 || "unknown".equalsIgnoreCase(ipAddress)) {
			ipAddress = request.getHeader("Proxy-Client-IP");
		}
		if (ipAddress == null || ipAddress.length() == 0 || "unknown".equalsIgnoreCase(ipAddress)) {
			ipAddress = request.getHeader("WL-Proxy-Client-IP");
		}
		if (ipAddress == null || ipAddress.length() == 0 || "unknown".equalsIgnoreCase(ipAddress)) {
			ipAddress = request.getRemoteAddr();
			if (ipAddress.equals("127.0.0.1") || ipAddress.equals("0:0:0:0:0:0:0:1")) {
				// 根据网卡取本机配置的IP
				InetAddress inet = null;
				try {
					inet = InetAddress.getLocalHost();
				} catch (UnknownHostException e) {
					e.printStackTrace();
				}
				ipAddress = inet.getHostAddress();
			}
		}
		// 对于通过多个代理的情况，第一个IP为客户端真实IP,多个IP按照','分割
		if (ipAddress != null && ipAddress.length() > 15) { // "***.***.***.***".length()
															// = 15
			if (ipAddress.indexOf(",") > 0) {
				ipAddress = ipAddress.substring(0, ipAddress.indexOf(","));
			}
		}
		return ipAddress;
	}
}
