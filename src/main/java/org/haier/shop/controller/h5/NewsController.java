package org.haier.shop.controller.h5;

import java.util.HashMap;
import java.util.Map;

import org.haier.shop.service.InfoPushService;
import org.haier.shop.util.PurResult;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

/**
 * 菜单权限控制器--商家app
 */

@Controller
@RequestMapping("/h5/news")
public class NewsController {
	
	@Autowired
    private InfoPushService infoPushService;
    
    /**
	 * 查询会员消息列表
	 * @param cus_unique 会员编号
	 * @param pageNum
	 * @param pageSize
	 * @return
	 */
	@RequestMapping("/getInfoPushMemberList.do")
	@ResponseBody
	public PurResult getInfoPushMemberList(
			@RequestParam(value="cus_unique",required=true)String cus_unique,
			@RequestParam(value="pageNum",defaultValue="1")Integer pageNum, 
			@RequestParam(value="pageSize",defaultValue="8")Integer pageSize
			){
		Map<String ,Object> params = new HashMap<String, Object>();
		params.put("cus_unique", cus_unique);
		params.put("startNum", (pageNum-1)*pageSize);
		params.put("pageSize", pageSize);
		return infoPushService.getInfoPushMemberList(params);
	}
	
	
	/**
	 * 查询会员消息详情
	 * @param cus_unique 会员编号
	 * @param pageNum
	 * @param pageSize
	 * @return
	 */
	@RequestMapping("/getInfoPushMember.do")
	@ResponseBody
	public PurResult getInfoPushMember(
			@RequestParam(value="member_info_id",required=true)String member_info_id
			){
		return infoPushService.getInfoPushMember(member_info_id);
	}
}
