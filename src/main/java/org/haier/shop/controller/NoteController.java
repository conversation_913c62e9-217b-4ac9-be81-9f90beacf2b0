package org.haier.shop.controller;

import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;

@Controller
@RequestMapping("/note")
public class NoteController {
	
	@RequestMapping("/note.do")
	public String note(String id) {
		return "/WEB-INF/note/note"+id+".jsp";
	}
	
	@RequestMapping("/to4041.do")
	public String note1() {
		return "/WEB-INF/note/404.jsp";
	}
	
	@RequestMapping("/to4042.do")
	public String note2() {
		return "/WEB-INF/note/404.jsp";
	}
	@RequestMapping("/to4043.do")
	public String note3() {
		return "/WEB-INF/note/404.jsp";
	}
	@RequestMapping("/to4044.do")
	public String note4() {
		return "/WEB-INF/note/404.jsp";
	}
	@RequestMapping("/to4045.do")
	public String note5() {
		return "/WEB-INF/note/404.jsp";
	}
	@RequestMapping("/to4046.do")
	public String note6() {
		return "/WEB-INF/note/404.jsp";
	}
	@RequestMapping("/to4047.do")
	public String note7() {
		return "/WEB-INF/note/404.jsp";
	}
	@RequestMapping("/to4048.do")
	public String note8() {
		return "/WEB-INF/note/404.jsp";
	}
	@RequestMapping("/to4049.do")
	public String note9() {
		return "/WEB-INF/note/404.jsp";
	}
	@RequestMapping("/to40410.do")
	public String note10() {
		return "/WEB-INF/note/404.jsp";
	}
	@RequestMapping("/to40411.do")
	public String note11() {
		return "/WEB-INF/note/404.jsp";
	}
	@RequestMapping("/to40412.do")
	public String note12() {
		return "/WEB-INF/note/404.jsp";
	}
	@RequestMapping("/to40413.do")
	public String note13() {
		return "/WEB-INF/note/404.jsp";
	}
	
	
}
