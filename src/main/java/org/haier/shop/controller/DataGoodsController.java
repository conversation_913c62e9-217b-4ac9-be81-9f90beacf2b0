package org.haier.shop.controller;

import javax.annotation.Resource;

import org.haier.shop.service.DataGoodsService;
import org.haier.shop.util.ShopsResult;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

@Controller
@RequestMapping("/dataGoods/")
public class DataGoodsController {
	@Resource
	private DataGoodsService dataService;
	//查询商品热力图
	@RequestMapping("/queryDataGoodsHotMap.do")
	@ResponseBody
	public ShopsResult queryDataGoodsHotMap(){
		return dataService.queryDataGoodsHotMap();
	}
	//查询商品销量排行
	@RequestMapping("/queryDataGoodsTopByCount.do")
	@ResponseBody
	public ShopsResult queryDataGoodsTopByCount(){
		return dataService.queryDataGoodsTopByCount();
	}
	//查询年龄分布图
	@RequestMapping("/queryGroupByAge.do")
	@ResponseBody
	public ShopsResult queryGroupByAge(){
		return dataService.queryGroupByAge();
	}
	//查询消费能力
	@RequestMapping("/queryBuyMoneyGroupByAge.do")
	@ResponseBody
	public ShopsResult queryBuyMoneyGroupByAge(){
		return dataService.queryBuyMoneyGroupByAge();
	}
	//查询各区县的商品销量和销售额
	@RequestMapping("/queryBuyMoneyGroupByCity.do")
	@ResponseBody
	public ShopsResult queryBuyMoneyGroupByCity(){
		return dataService.queryBuyMoneyGroupByCity();
	}
	
	
}

