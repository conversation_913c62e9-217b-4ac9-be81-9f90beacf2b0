package org.haier.shop.controller;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.PrintWriter;
import java.util.HashMap;
import java.util.Iterator;
import java.util.Map;
import java.util.SortedMap;
import java.util.TreeMap;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.haier.meituan.util.MUtil;
import org.haier.shop.dao3.ShopSoftDao;
import org.haier.shop.service.H5ShopRegisterService;
import org.haier.shop.service.ShopSoftService;
import org.haier.shop.util.AlipayConfig;
import org.haier.shop.util.XMLUtils;
import org.haier.shop.util.wxPay.PayConfigUtil;
import org.haier.shop.util.wxPay.PayToolUtil;
import org.jdom.JDOMException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import com.alipay.api.internal.util.AlipaySignature;

@Controller
@RequestMapping("/callback")
public class PayCallbackController{
    
    @Autowired
    private ShopSoftService shopSoftService;
    
    @Autowired
    private ShopSoftDao shopSoftDao;
    
    @Autowired
    private H5ShopRegisterService h5ShopRegisterService;
    
	/**
	 * 微信平台发起的回调方法，
	 * 调用我们这个系统的这个方法接口，将扫描支付的处理结果告知我们系统
	 * <AUTHOR>
	 * @throws JDOMException
	 * @throws Exception
	 */
	@RequestMapping(value = "weixinNotify.do", produces = "application/json;charset=UTF-8")
	@ResponseBody
	public void weixinNotify(HttpServletRequest request, HttpServletResponse response){
		System.out.println("微信支付回调接口开始");
    	try{
    		request.setCharacterEncoding("UTF-8");//设置请求格式
    		BufferedReader reader = request.getReader();// 获得 http body 内容
            StringBuffer buffer = new StringBuffer();
            String xmlString = null;
            String string;
            while ((string = reader.readLine()) != null) {
                buffer.append(string);
            }
            xmlString = buffer.toString();
            reader.close();
            Map map = XMLUtils.doXMLParse(xmlString);
            String return_code = MUtil.strObject(map.get("return_code"));//返回状态码
            if(return_code != null && return_code.equals("SUCCESS")){
            	//验证签名
            	if(checkSign(xmlString,PayConfigUtil.API_KEY)){
            		String profit_no = MUtil.strObject(map.get("out_trade_no"));//外部订单号
                	String trans_num = MUtil.strObject(map.get("transaction_id"));//微信支付订单号
                	int pay_money = Integer.parseInt(MUtil.strObject(map.get("total_fee")));//支付金额
                	Map<String ,Object> params = new HashMap<String ,Object>();
                	params.put("trans_num", trans_num);
                	params.put("pay_money", pay_money/100);
                	params.put("profit_no", profit_no);
                	boolean flag = shopSoftService.paySuccess(params);
                	if(flag) {
                		reponseWeiXin(response,"SUCCESS","OK");
                	}
            	}else {
            		reponseWeiXin(response,"FAIL","参数校验失败");
            	}
            }
    	}catch(JDOMException e){
    		reponseWeiXin(response,"FAIL","返回参数解析过程发生错误");
    		e.printStackTrace();
    	}catch(Exception e){
    		reponseWeiXin(response,"FAIL","请求参数时发生错误");
    		e.printStackTrace();
    	}
    	System.out.println("微信支付回调接口结束");
    }	
	
		/**
		 * 微信回调 ---店铺注册时购买设备支付回调
		 * 调用我们这个系统的这个方法接口，将扫描支付的处理结果告知我们系统
		 * <AUTHOR>
		 * @throws JDOMException
		 * @throws Exception
		 */
		@RequestMapping(value = "deviceSoftNotify.do", produces = "application/json;charset=UTF-8")
		@ResponseBody
		public void deviceSoftNotify(HttpServletRequest request, HttpServletResponse response){
			System.out.println("微信支付---店铺注册时购买设备支付回调接口开始");
	    	try{
	    		request.setCharacterEncoding("UTF-8");//设置请求格式
	    		BufferedReader reader = request.getReader();// 获得 http body 内容
	            StringBuffer buffer = new StringBuffer();
	            String xmlString = null;
	            String string;
	            while ((string = reader.readLine()) != null) {
	                buffer.append(string);
	            }
	            xmlString = buffer.toString();
	            reader.close();
	            Map map = XMLUtils.doXMLParse(xmlString);
	            String return_code = MUtil.strObject(map.get("return_code"));//返回状态码
	            if(return_code != null && return_code.equals("SUCCESS")){
	            	//验证签名
	            	if(checkSign(xmlString,PayConfigUtil.API_KEY)){
	            		String out_trade_no = MUtil.strObject(map.get("out_trade_no"));//外部订单号
	                	String trans_num = MUtil.strObject(map.get("transaction_id"));//微信支付订单号
	                	int pay_money = Integer.parseInt(MUtil.strObject(map.get("total_fee")));//支付金额
	                	Map<String ,Object> params = new HashMap<String ,Object>();
	                	params.put("trans_num", trans_num);
	                	params.put("pay_money", pay_money/100);
	                	params.put("out_trade_no", out_trade_no);
	                	boolean flag = h5ShopRegisterService.deviceSoftPaySuccess(params);
	                	if(flag) {
	                		reponseWeiXin(response,"SUCCESS","OK");
	                	}
	            	}else {
	            		reponseWeiXin(response,"FAIL","参数校验失败");
	            	}
	            }
	    	}catch(JDOMException e){
	    		reponseWeiXin(response,"FAIL","返回参数解析过程发生错误");
	    		e.printStackTrace();
	    	}catch(Exception e){
	    		reponseWeiXin(response,"FAIL","请求参数时发生错误");
	    		e.printStackTrace();
	    	}
	    	System.out.println("微信支付---店铺注册时购买设备支付回调接口结束");
	    }	
	
	
	/**
          *   支付宝回调路径return_url
     * @param request
     * @param response
	 * @throws Exception 
     */
	@RequestMapping("return_url.do")
    public String returnUrl(HttpServletRequest request, HttpServletResponse response) throws Exception{
        //获取支付宝POST过来反馈信息
        Map<String,String> params = new HashMap<String,String>();
        Map requestParams = request.getParameterMap();
        for (Iterator iter = requestParams.keySet().iterator(); iter.hasNext();) {
            String name = (String) iter.next();
            String[] values = (String[]) requestParams.get(name);
            String valueStr = "";
            for (int i = 0; i < values.length; i++) {
                valueStr = (i == values.length - 1) ? valueStr + values[i]
                            : valueStr + values[i] + ",";
            }
            //乱码解决，这段代码在出现乱码时使用。
            //valueStr = new String(valueStr.getBytes("ISO-8859-1"), "utf-8");
            params.put(name, valueStr);
        }
        boolean signVerified = AlipaySignature.rsaCheckV1(params, AlipayConfig.alipay_public_key, AlipayConfig.charset,AlipayConfig.sign_type);
        if(signVerified){
            //支付宝交易号
            String trade_no = new String(request.getParameter("trade_no").getBytes("ISO-8859-1"),"UTF-8");
            //付款金额
            String total_amount = new String(request.getParameter("total_amount").getBytes("ISO-8859-1"),"UTF-8");
            //交易状态
			String trade_status = new String(request.getParameter("trade_status").getBytes("ISO-8859-1"),"UTF-8");
			
			//商户订单号
			String profit_no = new String(request.getParameter("out_trade_no").getBytes("ISO-8859-1"),"UTF-8");
	        if(trade_status.equals("TRADE_FINISHED") || trade_status.equals("TRADE_SUCCESS")){
	        	//附加参数
//	            String passback_params = new String(request.getParameter("passback_params").getBytes("ISO-8859-1"),"UTF-8");
//	            passback_params = URLDecoder.decode(passback_params, "UTF-8");
	            // 这里是支付成功  
//	            Map<String ,Object> params1 = JSON.parseObject(passback_params, Map.class);
	            Map<String ,Object> params1 = new HashMap<String ,Object>();
	            params1.put("trans_num", trade_no);
	            params1.put("pay_money", total_amount);
	            params1.put("profit_no", profit_no);
	            boolean flag = shopSoftService.paySuccess(params1);
	            if(flag) {
	            	reponse(response,"success");
		        	System.out.println("回调成功");
	            }
            }else if(trade_status.equals("TRADE_CLOSED")){
            	//修改购买记录支付失败
    			Map<String ,Object> shopSoftProfitParams = new HashMap<String ,Object>();
    			shopSoftProfitParams.put("pay_status", 3);//支付失败
    			shopSoftProfitParams.put("profit_no", profit_no);
    			shopSoftDao.updateShopSoftProfit(shopSoftProfitParams);
    			reponse(response,"success");
            	System.out.println("回调成功");
            }
        }else {
        	reponse(response,"failure");
        	System.out.println("回调失败");
        }
       
        return null;
    }

	/**
	 * 支付回调返回
	 * @param response
	 * @param recode
	 */
	public void reponse(HttpServletResponse response, String message){
	   response.setCharacterEncoding("UTF-8");
       response.setContentType("text/plain");
       PrintWriter out = null;
       try
       {
           out = response.getWriter();
       }
       catch(IOException e)
       {
           e.printStackTrace();
       }
       out.write(message);
       out.close();
	}
	
	/**
	 * 微信回调返回
	 * @param response
	 * @param recode
	 */
	public void reponseWeiXin(HttpServletResponse response, String recode, String message){
		String msg = "<xml><return_code><![CDATA["+ recode +"]]></return_code><return_msg><![CDATA["+ message +"]]></return_msg></xml>";
    	response.setCharacterEncoding("UTF-8");
        response.setContentType("text/plain");
        PrintWriter out = null;
        try
        {
            out = response.getWriter();
        }
        catch(IOException e)
        {
            e.printStackTrace();
        }
        out.write(msg);
        out.close();
	}
	
	/**
	 * 签名校验
	 * @param xmlString
	 * @return
	 */
	@SuppressWarnings("unchecked")
	private boolean checkSign(String xmlString,String wechatKey) {
		SortedMap<Object, Object> map = null;
		Map<String,Object> mapO = null;
        try {
        	mapO = XMLUtils.doXMLParse(xmlString);
            map=new TreeMap<Object,Object>(mapO);
        } catch (Exception e) {
            e.printStackTrace();
        }
        String signFromAPIResponse = map.get("sign").toString();

        if (signFromAPIResponse == "" || signFromAPIResponse == null) {
            System.out.println("API返回的数据签名数据不存在，有可能被第三方篡改!!!");
            return false;
        }
        System.out.println("服务器回包里面的签名是:" + signFromAPIResponse);
        //清掉返回数据对象里面的Sign数据（不能把这个数据也加进去进行签名），然后用签名算法进行签名
        map.put("sign", "");
        //将API返回的数据根据用签名算法进行计算新的签名，用来跟API返回的签名进行比较
        String signForAPIResponse = PayToolUtil.createSign("UTF-8", map,wechatKey);
        if (!signForAPIResponse.equals(signFromAPIResponse)) {
            //签名验不过，表示这个API返回的数据有可能已经被篡改了
            System.out.println("API返回的数据签名验证不通过，有可能被第三方篡改!!! signForAPIResponse生成的签名为" + signForAPIResponse);
            return false;
        }
        System.out.println("恭喜，API返回的数据签名验证通过!!!");
        return true;
    }
}