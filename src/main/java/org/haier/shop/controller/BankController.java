package org.haier.shop.controller;

import java.util.HashMap;
import java.util.Map;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.haier.shop.service.BankService;
import org.haier.shop.util.HandleMessyCode;
import org.haier.shop.util.PurResult;
import org.haier.shop.util.ServletsUtil;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

@RequestMapping("/bank")
@Controller
public class BankController {
	
	private static final Logger logger = LoggerFactory.getLogger(BankController.class);
	
	@Resource
	private BankService bankService;

	
	@RequestMapping(value = "/addPage.do")
    public String addPage(){
    	logger.info("后台管理-添加银行页面");
        return "/WEB-INF/manager/addBank.jsp";
    }
    
    @RequestMapping(value="/queryList.do")
	@ResponseBody
	public PurResult queryList(
			HttpServletRequest request,
			@RequestParam(value = "page", defaultValue = "0") int page,
			@RequestParam(value = "limit", defaultValue = "8") int pageSize){
    	Map<String, Object> params = HandleMessyCode.handle(ServletsUtil.getParameters(request));
		params.put("limit", pageSize);
		params.put("page", (page-1)*pageSize);
		
		PurResult result = new PurResult();
		try {
			result = bankService.getBankList(params);
		} catch (Exception e) {
			logger.error("列表查询异常：",e);
			e.printStackTrace();
			result.setStatus(0);
			result.setMsg("异常");
		}
		return result;
	}
    
    @RequestMapping(value = "/addBank.do")
    @ResponseBody
    public PurResult addBank(String bank_name,String staff_id,HttpServletRequest request){
    	logger.info("后台管理-添加银行");
    	PurResult result = new PurResult();
    	try {
    		Map<String, Object> params = new HashMap<String, Object>();
    		params.put("bank_name", bank_name);
    		params.put("staff_id", staff_id);
    		result = bankService.addBank(params,request);
		} catch (Exception e) {
			logger.info("后台管理-添加银行异常："+e.getMessage());
			result.setStatus(0);
			result.setMsg("异常");
		}
        return result;
    }
    
    @RequestMapping(value = "/getBank.do")
    public String getBank(String bank_id,Model model){
    	logger.info("后台管理-获取银行详情");
    	try {
    		Map<String, Object> bank = bankService.getBank(bank_id);
    		model.addAttribute("bank", bank);
		} catch (Exception e) {
			return "/error";
		}
    	return "/WEB-INF/manager/editBank.jsp";
    }
    
    @RequestMapping(value = "/updateBank.do")
    @ResponseBody
    public PurResult updateBank(String bank_name,String bank_id,HttpServletRequest request){
    	logger.info("后台管理-修改银行");
    	PurResult result = new PurResult();
    	try {
    		Map<String, Object> params = new HashMap<String, Object>();
    		params.put("bank_name", bank_name);
    		params.put("bank_id", bank_id);
    		result = bankService.updateBank(params,request);
		} catch (Exception e) {
			logger.info("后台管理-修改银行异常："+e.getMessage());
			result.setStatus(0);
			result.setMsg("异常");
		}
        return result;
    }
    
    @RequestMapping(value = "/deleteBank.do")
    @ResponseBody
    public PurResult deleteBank(String bank_id){
    	logger.info("后台管理-删除银行");
    	PurResult result = new PurResult();
    	try {	
    		result = bankService.deleteBank(bank_id);
		} catch (Exception e) {
			logger.info("后台管理-删除银行异常："+e.getMessage());
			result.setStatus(0);
			result.setMsg("异常");
		}
        return result;
    }
}
