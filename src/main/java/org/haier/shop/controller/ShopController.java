package org.haier.shop.controller;

import java.awt.*;
import java.awt.image.BufferedImage;
import java.io.BufferedReader;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStreamReader;
import java.io.OutputStream;
import java.math.BigDecimal;
import java.net.HttpURLConnection;
import java.net.URL;
import java.net.URLEncoder;
import java.sql.Timestamp;
import java.text.DecimalFormat;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;
import javax.imageio.ImageIO;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.extra.qrcode.QrCodeUtil;

import org.apache.log4j.Logger;
import org.apache.poi.hssf.usermodel.HSSFSheet;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.Row;
import org.haier.log.annotation.RemoteLog;
import org.haier.log.enums.BusinessType;
import org.haier.shop.entity.ShopVO;
import org.haier.shop.entity.ShopsEntity;
import org.haier.shop.params.shop.ShopGoodsInPriceTypeEditParams;
import org.haier.shop.redis.RedisCache;
import org.haier.shop.result.shop.ShopConfigQueryResult;
import org.haier.shop.service.ShopService;
import org.haier.shop.util.*;
import org.haier.shop.util.mqtt.ClientMQTT;
import org.haier.shop.util.mqtt.MqttxUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.ResourceLoader;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import net.sf.json.JSONArray;
import net.sf.json.JSONObject;

@Controller
@RequestMapping("/html/shop")
public class ShopController {
    @Resource
    private ShopService shopService;

    @Resource
    private RedisCache redis;

    private static Logger log = Logger.getLogger(ShopController.class);

    /**
     * 创建收款二维码下载图片，并返回下载路径
     * 可以先判断是否存在，再创建
     * @param shopUnique
     * @return
     */
    @Autowired
    private ResourceLoader resourceLoader;

    @SuppressWarnings({ "rawtypes", "unchecked" })
	@RequestMapping("/downLoadPayCode.do")
    @ResponseBody
    public ShopsResult downLoadPayCode(String shopUnique,Integer imageSize,HttpServletRequest request,String shopName) {
    	ShopsResult sr = new ShopsResult(1,"创建成功!");
    	try {
    		//生成文件并返回进度
    		String absPath = this.getClass().getClassLoader().getResource("../../").getPath();
    		String filePathDetail = File.separator + "file" + File.separator;
    		
    		System.out.println(absPath + filePathDetail);
    		String contextPath = request.getContextPath();
    		System.out.println(contextPath);
    		
    		//网页支付通用吗
//    		String url = request.getScheme() + "://" + request.getServerName() + "/shopUpdate/wechat/payForShop.do?shopUnique=" + shopUnique;
    		//小程序支付通用码
    		String url = "https://buyhoo.cc/buyhooPay?type=aggregatedPay&shopUnique=" + shopUnique + "&shopName=" + shopName;
    		
    		//二维码的图片格式 
    		String format = "jpg"; 
    		File outputFile = new File(absPath + filePathDetail + shopUnique + "-" + imageSize + "." + format); 
    		
    		sr.setData(request.getScheme() + "://" + request.getServerName() + request.getContextPath() + filePathDetail + shopUnique + "-" + imageSize + "." + format);
    		if(outputFile.exists()) {
    			return sr;
    		}
    		File filePath = new File(absPath + filePathDetail);
    		if(!filePath.exists()) {
    			filePath.mkdirs();
    		}
    		outputFile.createNewFile();


            // 添加字体的属性设置
            Font font = new Font("创客贴金刚体", Font.BOLD, 100);
            try {
                org.springframework.core.io.Resource resource = resourceLoader.getResource("classpath:qrcode_background.png");
                File backGroundImageUrl = resource.getFile();
                //加载背景图片(也就是模板图)
                BufferedImage backGroundImage = ImageIO.read(backGroundImageUrl);
                //加载二维码图片(也就是需要合成到模板图上的图片)
                BufferedImage imageCode = QrCodeUtil.generate(url, 750, 750);
                //把背景图片当做为模板
                Graphics2D graphics = backGroundImage.createGraphics();
                //在模板上绘制图象(需要绘图的图片,左边距,上边距,图片宽度,图片高度,图像观察者)同一个模板一般是不会变的
                graphics.drawImage(imageCode, 141, 422, 750, 750, null);
                //设置字体
                graphics.setFont(font);
                //设置颜色
                graphics.setColor(Color.WHITE);
                //获取字体度量(字体度量是指对于指定字号的某种字体，在度量方面的各种属性)
                FontMetrics fontMetrics = graphics.getFontMetrics(font);
                //获取字体度量的宽度
                int textWidth = fontMetrics.stringWidth(shopName);
                //左边距=(模板图宽度-文字宽度)/2
                int widthX = (backGroundImage.getWidth() - textWidth) / 2;
                //g.drawString(title, 820, 2850);
                //绘制文字(内容，左边距,上边距)，同一个模板上边距一般也是不变的
                graphics.drawString(shopName, widthX, 150);
                //完成模板修改
                graphics.dispose();
                //获取新文件的地址
                //生成新的合成过的用户二维码并写入新图片,指定类型为png
                ImageIO.write(backGroundImage, "png", outputFile);
            } catch (Exception e) {
                e.printStackTrace();
            }

    	}catch (Exception e) {
    		e.printStackTrace();
    		sr.setStatus(0);
    		sr.setMsg("绘制二维码失败!");
		}
        
    	return sr;
    }
    /**
     * 创建下载文件
     *
     * @param startTime
     * @param endTime
     * @return data里返回文件路径信息，如果没有则在msg里提示
     */
    @RequestMapping("/createAccountFile.do")
    @ResponseBody
    public ShopsResult createAccountFile(String startTime, String endTime, HttpServletRequest request) {
        ShopsResult sr = new ShopsResult(1, "查询成功!");

        List<Map<String, Object>> list = shopService.createAccountFile(startTime, endTime);
        if (null == list || list.isEmpty()) {
            sr.setStatus(0);
            sr.setMsg("没有满足条件的信息");
            return sr;
        }

        //生成文件并返回进度
        String absPath = this.getClass().getClassLoader().getResource("../../").getPath();
        String filePathDetail = File.separator + "file" + File.separator;
        String filePath = absPath.substring(0, absPath.length() - request.getContextPath().length()) + filePathDetail;
        File dir = new File(filePath);
        if (!dir.exists()) {
            dir.mkdirs();
        }

        String fileName = startTime + "-" + endTime + ".xls";

        try {
            //生成文件，并将数据保存到文件中
            File file = new File(filePath + File.separator + fileName);
            if (!file.exists()) {
                file.createNewFile();
            }

            //将内容写入文件，并转化成本地文件

            @SuppressWarnings("resource")
            HSSFWorkbook wb = new HSSFWorkbook();
            HSSFSheet sheet = wb.createSheet(startTime + "至" + endTime);
            Integer rc = 0;
            Row rs = sheet.createRow(rc);
            rc++;

            for (Integer j = 0; j < 4; j++) {
                Cell cell = rs.createCell(j);
                switch (j) {
                    case 0:
                        cell.setCellValue("店铺编号");
                        break;
                    case 1:
                        cell.setCellValue("金额");
                        break;
                    case 2:
                        cell.setCellValue("超市名称");
                        break;
                    case 3:
                        cell.setCellValue("客户账户");
                        break;
                    default:
                        break;
                }
            }


            for (Integer i = 0; i < list.size(); i++) {
                Row row = sheet.createRow(rc);
                rc++;
                for (Integer j = 0; j < 4; j++) {
                    Cell cell = row.createCell(j);
                    switch (j) {
                        case 0:
                            cell.setCellValue(list.get(i).get("shop_unique").toString());
                            break;
                        case 1:
                            cell.setCellValue(list.get(i).get("payMoney").toString());
                            break;
                        case 2:
                            cell.setCellValue(list.get(i).get("shop_name").toString());
                            break;
                        case 3:
                            cell.setCellValue(list.get(i).get("cus_name").toString());
                            break;
                        default:
                            break;
                    }
                }
            }

            FileOutputStream output = new FileOutputStream(file);
            wb.write(output);
            output.flush();
            output.close();
            sr.setData(filePathDetail + fileName);
        } catch (Exception e) {
            e.printStackTrace();
        }

        return sr;
    }

    @RequestMapping("/getLocal.do")
    @ResponseBody
    public ShopsResult getLocal(HttpServletRequest request) {
        ShopsResult sr = new ShopsResult();

        sr.setData(request.getScheme() + "://" + request.getServerName());

        return sr;
    }


    @RequestMapping("/getLogList.do")
    @ResponseBody
    public ShopsResult getLogList(String shopUnique, HttpServletRequest request) {
        ShopsResult sr = new ShopsResult(1, "查询成功!");
        String filePath = "/mnt/myData/tomcat/tomcat1/webapps/pcLog/" + shopUnique;
        File f = new File(filePath);
        if (null == f || !f.exists()) {
            sr.setStatus(1);
            sr.setMsg("没有信息!");
            return sr;
        }
        //读取文件
        File[] fs = f.listFiles();
        List<Map<String, Object>> list = new ArrayList<Map<String, Object>>();

        String serverPath = request.getScheme() + "://" + request.getServerName() + "/pcLog/";

        for (File sf : fs) {
            if (sf.isFile()) {
                String fileName = sf.getName();
                String path = serverPath + shopUnique + File.separator + fileName;
                System.out.println(request.getLocalAddr());
                Map<String, Object> map = new HashMap<String, Object>();
                map.put("path", path);
                map.put("fileName", fileName);
                list.add(map);
            }
        }

        sr.setCount(list.size());
        sr.setData(list);

        return sr;
    }

    @RequestMapping("/getShopLogList.do")
    @ResponseBody
    public ShopsResult getShopLogList(String shopUnique, String startDate, Integer dayCount) {
        ShopsResult sr = new ShopsResult(1, "查询成功!");

        //获取收银机列表，向收银机发送获取消息通知，并记录日志信息
        String id = ClientMQTT.MQTTPUBKEY + shopUnique;
        Object o = redis.getObject(id);
        Integer count = 0;
        List<String> macIdList = null;
        if (null == o) {
            sr.setCount(0);
            return sr;
        } else {
            macIdList = (List<String>) o;

            //依次向该设备获取日志
            Map<String, Object> map = new HashMap<String, Object>();
            map.put("ctrl", "msg_log_upload");
            map.put("ID", "");
            map.put("status", "200");
            map.put("errcode", "0");
            map.put("msg", "请上传日志文件");
            map.put("count", dayCount == null ? 1 : dayCount);
            map.put("data", startDate == null ? "" : startDate);

            for (String macId : macIdList) {
                map.put("ID", macId);
                MqttxUtil.sendMapMsg(map, macId);
            }
        }

        return sr;
    }

    @RequestMapping("/toShopLogList.do")
    public String toShopLogList(String shopUnique, Model model) {
        model.addAttribute("dayCount", redis.getObject(ClientMQTT.LOGDAYCOUNT) == null ? 5 : redis.getObject(ClientMQTT.LOGDAYCOUNT));
        return "/WEB-INF/shop/toShopLogList.jsp";
    }

    @RequestMapping("/queryLoginRecord.do")
    @ResponseBody
    public ShopsResult queryLoginRecord(String shopUnique, String startTime, String endTime, Integer page, Integer limit, Integer handover) {
        log.debug("/shop/html/shop/queryLoginRecord.do?");
        log.debug("shopUnique=" + shopUnique + "&startTime=" + startTime + "&endTime=" + endTime + "&page=" + page + "&limit=" + limit + "&handover=" + handover);
        return shopService.queryLoginRecord(shopUnique, startTime, endTime, page, limit, handover);
    }

    @RequestMapping("/loginRecord.do")
    public String toLoginRecord() {
        return "/WEB-INF/shop/nyLoginRecord.jsp";
    }

    @RequestMapping("/toQueryShopByArea.do")
    public String toQueryShopByArea() {
        return "/WEB-INF/countMessage/queryShopByArea.jsp";
    }

    @RequestMapping("/toCusStatisticsByShop.do")
    public String toCusStatisticsByShop() {
        return "/WEB-INF/shop/cusStatisticsByShop.jsp";
    }

    /**
     * 打开宁宇会员信息统计界面
     *
     * @return
     */
    @RequestMapping("/toCusStatistics.do")
    public String toCusStatistics() {
        return "/WEB-INF/shop/cusStatistics.jsp";
    }

    /**
     * 中心站查询各站点的往日营业状态
     *
     * @param shopUnique
     * @param pageNum
     * @param pageSize
     * @param startDate
     * @param endDate
     * @return
     */
    @RequestMapping("/queryShopByArea.do")
    @ResponseBody
    public ShopsResult queryShopByArea(String shopUnique, Integer page, Integer limit, String startDate, String endDate, Integer searchType, String shopMsg) {
        try {
            return shopService.queryShopByArea(shopUnique, page, limit, startDate, endDate, searchType, shopMsg);
        } catch (Exception e) {
            e.printStackTrace();
            return new ShopsResult(0, "查询失败");
        }
    }

    /**
     * 查询店铺基本信息
     *
     * @param shop_unique
     * @return
     */
    @RequestMapping("/queryShopMessage.do")
    @ResponseBody
    public ShopsResult queryShopMessage(String shop_unique) {
        if (null == shop_unique) {
            return new ShopsResult(2, "没有店铺信息");
        }
        return shopService.queryShopMessage(shop_unique);
    }

    /**
     * 更新店铺信息
     *
     * @param request
     * @param shop_unique
     * @param shop_name
     * @param shop_phone
     * @param shop_address_detail
     * @param manager_pwd
     * @param shop_remark
     * @return
     */
    @RequestMapping("/updateShopDetail.do")
    @ResponseBody
    public ShopsResult updateShopDetail(HttpServletRequest request, String shop_unique, String shop_name, String shop_phone, String startTime, String endTime,
                                        String shop_address_detail, String manager_pwd, String shop_remark, String shop_seller_email, String area_dict_num,
                                        String shopLatitude,
                                        String shopLongitude
    ) {
        return shopService.updateShopDetail(request, shop_unique, shop_name, shop_phone, startTime, endTime, shop_address_detail,
                manager_pwd, shop_remark, shop_seller_email, area_dict_num, shopLongitude, shopLatitude);
    }

    /**
     * 店铺功能查询
     *
     * @param shop_unique
     * @return
     */
    @RequestMapping("/queryShopFunction.do")
    @ResponseBody
    public ShopsResult queryShopFunction(String shop_unique) {
        return shopService.queryShopFunction(shop_unique);
    }

    /**
     * 更新商品功能
     *
     * @param shop_unique
     * @param shop_flower
     * @param shop_deliverwater
     * @param shop_laundry
     * @param shop_express
     * @param shop_homemarking
     * @param shop_cake
     * @param shop_fruit
     * @param shop_pur
     * @return
     */
    @ResponseBody
    @RequestMapping("/updateShopFunction.do")
    public ShopsResult updateShopFunction(
            @RequestParam(value = "shop_unique", required = true) String shop_unique,
            String shop_flower,
            String shop_deliverwater,
            String shop_laundry,
            String shop_express,
            String shop_homemarking,
            String shop_cake,
            String shop_fruit,
            String shop_pur,
            Integer negative_sale,
            Integer below_cost,
            Integer auto_pur,
            Integer auto_pur_days,
            Integer unsalable_days,
            Integer out_stock_warning_days,
            Integer out_stock_days,
            Integer out_stock_remind_type,
            Integer auto_pur_count_days
    ) {
        return shopService.updateShopFunction(shop_unique, shop_flower, shop_deliverwater, shop_laundry, shop_express,
                shop_homemarking, shop_cake, shop_fruit, shop_pur, negative_sale, below_cost, auto_pur, auto_pur_days, unsalable_days, out_stock_warning_days, out_stock_days, out_stock_remind_type, auto_pur_count_days);
    }


    /**
     * 店铺供货商对账查询
     *
     * @param shop_unique
     * @param supMessage
     * @param startTime
     * @param endTime
     * @return
     */
    @ResponseBody
    @RequestMapping("/queryTurnOver.do")
    public ShopsResult queryTurnOver(String shop_unique, String supMessage, Timestamp startTime, Timestamp endTime) {
        return shopService.queryTurnOver(shop_unique, supMessage, startTime, endTime);
    }

    /**
     * 经营统计
     *
     * @param shop_unique
     * @param queryType
     * @return
     */
    @RequestMapping("/managerment.do")
    @ResponseBody
    public ShopsResult management(String shop_unique, Integer queryType, Integer pageNum) {
        return shopService.management(shop_unique, queryType, pageNum);
    }

    @RequestMapping("/verifyUser.do")
    @ResponseBody
    public ShopsResult verifyUserInfo(
            @RequestParam(value = "manager_account", required = true) String manager_account,
            @RequestParam(value = "manager_pwd", required = true) String manager_pwd
    ) {

        return shopService.verifyUserInfoService(manager_account, manager_pwd);
    }

    @RequestMapping("/verifyShop.do")
    @ResponseBody
    public ShopsResult verifyShopInfo(
            @RequestParam(value = "shop_name", required = true) String shop_name
    ) {
        return shopService.verifyShopInfoService(shop_name);
    }

    /**
     * 注册新商户
     *
     * @param shop_name           新店铺名称
     * @param manager_account     登录账户，建议手机号
     * @param manager_pwd         登录密码
     * @param shop_address_detail 详细地址
     * @param shop_phone          电话
     * @param examinestatus       审核状态，默认1，待审核
     * @param shop_type           店铺类型1、便利店；2、水果店；3、母婴店；4、益农中心站；5、益农标准站；6、宁宇总店；7、宁宇分店；8、五金店；9、学习；10、机关食堂；11、加油站；12、餐饮店；13、拼团店
     * @param shop_class          连锁类型：0、普通店铺；1、连锁；2、加盟；3、系统平台；4、普通分店；5、加盟分店
     * @param company_code        连锁加盟总店code,关联总店供货商company_code，用不到了
     * @param is_other_purchase   是否允许向其他供货商采购：1、允许；0、不允许；
     * @param area_dict_num       区县编码
     * @param shop_latitude       纬度
     * @param shop_longitude      经度
     * @param province            省
     * @param city                市
     * @param district            县区地址
     * @param user_name           管理员名称
     * @param shop_image_path     店铺头像地址
     * @param request
     * @return
     */
    @ResponseBody
    @RequestMapping("/register.do")
    public ShopsResult register(
            @RequestParam(value = "shop_name", required = true) String shop_name,
            @RequestParam(value = "manager_account", required = true) String manager_account,
            @RequestParam(value = "manager_pwd", required = true) String manager_pwd,
            @RequestParam(value = "shop_address_detail") String shop_address_detail,
            @RequestParam(value = "shop_phone", required = true) String shop_phone,
            @RequestParam(value = "examinestatus", defaultValue = "2") Integer examinestatus,
            @RequestParam(value = "shop_type", defaultValue = "1") Integer shop_type,
            Integer shop_class,
            String company_code,
            Integer is_other_purchase,
            String area_dict_num,
            String town_code,
            String shop_latitude,
            String shop_longitude,
            String province,
            String city,
            String district,
            String user_name,
            String shop_image_path,
            String license,
            String invitation_code,
            HttpServletRequest request
    ) {
        if (StringUtil.isBlank(town_code)) {
            String params = "key=0518502b5e3e90fad440e3dd35c26b68&location=" + shop_longitude + "," + shop_latitude;
            Map<String, Object> result = HttpsUtil.sendGet("https://restapi.amap.com/v3/geocode/regeo", params);
            JSONObject jsonObject = JSONObject.fromObject(result);
            if (jsonObject.get("status").equals("1")){
                JSONObject regeocode = jsonObject.getJSONObject("regeocode");
                JSONObject addressComponent = regeocode.getJSONObject("addressComponent");
                town_code = addressComponent.getString("towncode");
            }
        }
        return shopService.register(shop_name, manager_account, manager_pwd, shop_address_detail, shop_phone, examinestatus, shop_class, company_code, is_other_purchase,
                area_dict_num, shop_latitude, shop_longitude, province, city, district, shop_type, user_name, shop_image_path, license, request, town_code, invitation_code);
    }


    @ResponseBody
    @RequestMapping("/registerApp.do")
    public ShopsResult registerApp(
            @RequestParam(value = "shop_name", required = true) String shop_name,
            @RequestParam(value = "manager_account", required = true) String manager_account,
            @RequestParam(value = "manager_pwd", required = true) String manager_pwd,
            @RequestParam(value = "shop_address_detail", required = true) String shop_address_detail,
            @RequestParam(value = "shop_phone", required = true) String shop_phone,
            @RequestParam(value = "examinestatus", defaultValue = "2") Integer examinestatus,
            @RequestParam(value = "shop_type", defaultValue = "1") Integer shop_type,
            Integer shop_class,
            String company_code,
            Integer is_other_purchase,
            String area_dict_num,
            String town_code,
            String shop_latitude,
            String shop_longitude,
            String province,
            String city,
            String district,
            String code,
            String agencyCode,
            HttpServletRequest request
    ) {
        return shopService.registerApp(shop_name, manager_account, manager_pwd, shop_address_detail, shop_phone, examinestatus, shop_class, company_code, is_other_purchase,
                area_dict_num,
                shop_latitude,
                shop_longitude,
                province,
                city,
                district, shop_type, code, agencyCode, request, town_code);
    }

    /**
     * 创建新的管理员子店铺
     *
     * @throws Exception
     */
    @RemoteLog(title = "新增分店", businessType = BusinessType.INSERT)
    @RequestMapping("/createNewShop.do")
    @ResponseBody
    public ShopsResult createNewShop(
            HttpServletRequest request,
            @RequestParam(value = "shopName", required = true) String shopName,
            @RequestParam(value = "managerUnique", required = true) Long managerUnique,
            String shopPhone,
            String shopSellerEmail,
            String shopHours,
            Double shopLatitude,
            Double shopLongitude,
            String shopAddressDetail,
            String shopRemarks,
            String shop_announcement,
            String area_dict_num,
            @RequestParam(value = "province", required = true) String province,
            @RequestParam(value = "city", required = true) String city,
            @RequestParam(value = "district", required = true) String district,
            Integer examinestatus,
            Integer shop_class,
            Integer shop_type,
            String shop_hours
    ) throws Exception {
        Map<String, Object> map = new HashMap<String, Object>();
        map.put("shopName", shopName);
        map.put("shopPhone", shopPhone);
        map.put("shopAlias", ChineseCharToEn.getAllFirstLetter(shopName));
        map.put("shopSellerEmail", shopSellerEmail);
        map.put("managerUnique", managerUnique);
        map.put("shopHours", shopHours);
        map.put("shopLatitude", shopLatitude);
        map.put("shopLongitude", shopLongitude);
        map.put("shopAddressDetail", shopAddressDetail);
        map.put("shopRemarks", shopRemarks);
        map.put("shop_announcement", shop_announcement);
        map.put("area_dict_num", area_dict_num);
        map.put("examinestatus", examinestatus);
        map.put("shop_class", shop_class);
        map.put("shop_type", shop_type);
        map.put("shop_hours", shop_hours);
        if (shop_type != null && shop_type == 7) {
            map.put("cus_synchro_status", 1);
        }
        return shopService.createNewShop(map, request, province, city, district);
    }

    /**
     * 修改子店铺
     *
     * @throws Exception
     */
    @RemoteLog(title = "修改分店", businessType = BusinessType.UPDATE)
    @RequestMapping("/updateShop.do")
    @ResponseBody
    public ShopsResult updateShop(
            HttpServletRequest request,
            String shopName,
            Long managerUnique,
            @RequestParam(value = "shop_unique", required = true) Long shop_unique,
            String shopPhone,
            String shopSellerEmail,
            String shopHours,//营业时间json
            Double shopLatitude,
            Double shopLongitude,
            String shopAddressDetail,
            String shopRemarks,
            String shop_announcement,
            String province,
            String city,
            String district,
            String area_dict_num
            , Integer examinestatus
            , Integer shop_class
            , String company_code
            , Integer is_other_purchase
            , Integer shop_status
            , Integer is_collecting_express
            , String collecting_express_price
            , Integer is_send_express
            , Integer is_daikou
            , String send_express_price,
            String staff_id,
            String shop_hours,
            String expressRelation
    ) throws Exception {
        Map<String, Object> map = new HashMap<String, Object>();
        map.put("shop_name", shopName);
        map.put("shop_phone", shopPhone);
        map.put("shop_hours", shop_hours);
        map.put("shopAlias", ChineseCharToEn.getAllFirstLetter(shopName));
        map.put("shop_seller_email", shopSellerEmail);
        map.put("shopLatitude", shopLatitude);
        map.put("shopLongitude", shopLongitude);
        map.put("shop_address_detail", shopAddressDetail);
        map.put("shop_remark", shopRemarks != null ? shopRemarks.trim() : "");
        map.put("shopUnique", shop_unique);
        map.put("shop_unique", shop_unique);
        map.put("shop_announcement", shop_announcement);
        map.put("area_dict_num", area_dict_num);
        map.put("is_other_purchase", is_other_purchase);
        map.put("company_code", company_code);
        map.put("shop_class", shop_class);
        map.put("examinestatus", examinestatus);
        map.put("shop_status", shop_status);
        map.put("is_collecting_express", is_collecting_express);
        map.put("collecting_express_price", collecting_express_price);
        map.put("is_send_express", is_send_express);
        map.put("is_daikou", is_daikou);
        map.put("send_express_price", send_express_price);
        map.put("staff_id", staff_id);
        map.put("expressRelation", expressRelation);
        return shopService.updateShop(map, request, province, city, district, shopHours);
    }

    @RequestMapping("/countStroeTurnover.do")
    @ResponseBody
    public PurResult countStroeTurnover(
            @RequestParam(value = "page", defaultValue = "1") int page,
            @RequestParam(value = "limit", defaultValue = "15") int pageSize,
            String startTime,
            String endTime,
            @RequestParam(value = "order", defaultValue = "shopName") String order,
            @RequestParam(value = "orderType", defaultValue = "ASC") String orderType,
            Integer shopOnLine,
            Integer macOnLine,
            HttpServletRequest request
    ) {
        ShopsUtil.recordLog(log, request);
        Map<String, Object> map = new HashMap<String, Object>();
//		map.put("startNum", ((page-1)%23)*pageSize);//shopTest
        map.put("startNum", (page - 1) * pageSize);
        map.put("pageSize", pageSize);
        map.put("macOnLine", macOnLine);
        map.put("shopOnLine", shopOnLine);
        map.put("order", order);
        map.put("orderType", orderType);
        map.put("startTime", startTime);
        map.put("endTime", endTime);
        System.out.println(map);
        StringBuilder responseSb = new StringBuilder();
        StringBuilder shopsOnline = new StringBuilder();
        try {
            URL url = new URL("http://buyhoo.cc/shopmanager/app/shop/getMacOnline.do");
            HttpURLConnection httpConn = (HttpURLConnection) url.openConnection();
            httpConn.setDoOutput(true); // 使用 URL 连接进行输出
            httpConn.setDoInput(true); // 使用 URL 连接进行输入
            httpConn.setUseCaches(false); // 忽略缓存
            httpConn.setRequestMethod("GET"); // 设置URL请求方法

            //读取链接返回的数据流
            BufferedReader responseReader = new BufferedReader(
                    new InputStreamReader(httpConn.getInputStream(), "UTF-8"));//获取返回参数
            String readLine;
            while ((readLine = responseReader.readLine()) != null) {
                responseSb.append(readLine);
            }

            url = new URL("http://buyhoo.cc/shopmanager/pc/getShopsOnline.do");
            httpConn = (HttpURLConnection) url.openConnection();
            httpConn.setDoOutput(true); // 使用 URL 连接进行输出
            httpConn.setDoInput(true); // 使用 URL 连接进行输入
            httpConn.setUseCaches(false); // 忽略缓存
            httpConn.setRequestMethod("GET"); // 设置URL请求方法

            //读取链接返回的数据流
            BufferedReader responseReader1 = new BufferedReader(
                    new InputStreamReader(httpConn.getInputStream(), "UTF-8"));//获取返回参数
            String readLine1;
            while ((readLine1 = responseReader1.readLine()) != null) {
                shopsOnline.append(readLine1);
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        JSONObject json = JSONObject.fromObject(responseSb.toString());
        JSONArray data = (JSONArray) json.get("data");
        List<Map<String, Object>> resource = new ArrayList<Map<String, Object>>();
        for (int i = 0; i < data.size(); i++) {
            String shopUnique = data.get(i).toString().split(",")[0];
            boolean flag = true;
            for (int j = 0; j < resource.size(); j++) {
                if (shopUnique.equals(resource.get(j).get("shopUnique"))) {
                    flag = false;
                    break;
                }
            }
            if (flag) {
                Map<String, Object> mp = new HashMap<String, Object>();
                mp.put("shopUnique", data.get(i).toString().split(",")[0]);
                resource.add(mp);
            }
        }
        map.put("list", resource);

        JSONObject shopJson = JSONObject.fromObject(shopsOnline.toString());
        JSONArray shopData = (JSONArray) shopJson.get("data");
        List<Map<String, Object>> shopResource = new ArrayList<Map<String, Object>>();
        for (int j = 0; j < shopData.size(); j++) {
            @SuppressWarnings("unchecked")
            Map<String, Object> mp = (Map<String, Object>) shopData.get(j);//此处需要去重
            int m = 0;
            for (int k = 0; k < shopResource.size(); k++) {
                if (shopResource.get(k).get("shopUnique").toString().equals(mp.get("shopUnique").toString())) {
                    m = 0;
                    break;
                }
                m++;
            }
            if (m == shopResource.size()) {
                shopResource.add(mp);
            }
//			System.out.println(mp.get("shopUnique"));
        }
        map.put("shopData", shopResource);
//		System.out.println(map);
        return shopService.countStroeTurnover(map, data.size(), shopResource.size());
    }

    /**
     * 店铺
     *
     * @param map
     * @return
     */
    @RequestMapping("/countStroeTurnoverPages.do")
    @ResponseBody
    public ShopsResult countStroeTurnoverPages(
            @RequestParam(value = "pageSize", defaultValue = "20") Integer pageSize,
            Integer shopOnLine,
            Integer macOnLine
    ) {
        Map<String, Object> map = new HashMap<String, Object>();
        map.put("pageSize", pageSize);
        if (macOnLine == -1) {
            macOnLine = null;
        }
        if (shopOnLine == -1) {
            shopOnLine = null;
        }
        map.put("shopOnLine", shopOnLine);
        map.put("macOnLine", macOnLine);

        StringBuilder responseSb = new StringBuilder();
        StringBuilder shopsOnline = new StringBuilder();
        try {
            URL url = new URL("http://buyhoo.cc/shopmanager/app/shop/getMacOnline.do");
            HttpURLConnection httpConn = (HttpURLConnection) url.openConnection();
            httpConn.setDoOutput(true); // 使用 URL 连接进行输出
            httpConn.setDoInput(true); // 使用 URL 连接进行输入
            httpConn.setUseCaches(false); // 忽略缓存
            httpConn.setRequestMethod("GET"); // 设置URL请求方法

            //读取链接返回的数据流
            BufferedReader responseReader = new BufferedReader(
                    new InputStreamReader(httpConn.getInputStream(), "UTF-8"));//获取返回参数
            String readLine;
            while ((readLine = responseReader.readLine()) != null) {
                responseSb.append(readLine);
            }

            url = new URL("http://buyhoo.cc/shopmanager/pc/getCount.do");
            httpConn = (HttpURLConnection) url.openConnection();
            httpConn.setDoOutput(true); // 使用 URL 连接进行输出
            httpConn.setDoInput(true); // 使用 URL 连接进行输入
            httpConn.setUseCaches(false); // 忽略缓存
            httpConn.setRequestMethod("GET"); // 设置URL请求方法

            //读取链接返回的数据流
            BufferedReader responseReader1 = new BufferedReader(
                    new InputStreamReader(httpConn.getInputStream(), "UTF-8"));//获取返回参数
            String readLine1;
            while ((readLine1 = responseReader1.readLine()) != null) {
                shopsOnline.append(readLine1);
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        JSONObject json = JSONObject.fromObject(responseSb.toString());
        JSONArray data = (JSONArray) json.get("data");
        List<Map<String, Object>> resource = new ArrayList<Map<String, Object>>();
        for (int i = 0; i < data.size(); i++) {
            Map<String, Object> mp = new HashMap<String, Object>();
            mp.put("shopUnique", data.get(i).toString().split(",")[0]);
            resource.add(mp);
        }
        map.put("list", resource);

        JSONObject shopJson = JSONObject.fromObject(shopsOnline.toString());
        List<Map<String, Object>> shopResource = new ArrayList<Map<String, Object>>();
        if (null != shopJson.get("data") && !shopJson.get("data").equals("null")) {
            JSONArray shopData = (JSONArray) shopJson.get("data");
            for (int j = 0; j < shopData.size(); j++) {
                @SuppressWarnings("unchecked")
                Map<String, Object> mp = (Map<String, Object>) shopData.get(j);
                shopResource.add(mp);
            }
        }
        map.put("shopData", shopResource);
        return shopService.countStroeTurnoverPages(map);
    }

    /**
     * 店铺数量页数查询
     *
     * @param map
     * @return
     */
    @RequestMapping("/queryShopsPages.do")
    @ResponseBody
    public ShopsResult queryShopsPages(
            @RequestParam(value = "pageSize", defaultValue = "30") Integer pageSize
    ) {
        Map<String, Object> map = new HashMap<String, Object>();
        map.put("pageSize", pageSize);
        return shopService.queryShopsPages(map);
    }

    /**
     * 店铺销量总览界面-店铺列表查询
     *
     * @param map
     * @return
     */
    @RequestMapping("/queryShopsList.do")
    @ResponseBody
    public ShopsResult queryShopsList(
            @RequestParam(value = "pageNum", defaultValue = "1") Integer pageNum,
            @RequestParam(value = "pageSize", defaultValue = "25") Integer pageSize,
            @RequestParam(value = "order", defaultValue = "1") Integer order,
            @RequestParam(value = "orderType", defaultValue = "1") Integer orderType,
            String startDate,
            String endDate
    ) {
        Map<String, Object> map = new HashMap<String, Object>();
        map.put("startNum", (pageNum - 1) * pageSize);
        map.put("pageSize", pageSize);
        map.put("startDate", startDate);
        map.put("endDate", endDate);
        map.put("todayStartTime", startDate);
        map.put("todayEndTime", endDate);

        if (order == 1) {
            map.put("order", "saleTotal");
        }
        if (order == 2) {
            map.put("order", "yesPre");
        }
        if (order == 3) {
            map.put("order", "cycleRatio");
        }
        if (order == 4) {
            map.put("order", "listCount");
        }
        if (order == 5) {
            map.put("order", "unitPrice");
        }
        if (order == 6) {
            map.put("order", "totalCount");
        }
        if (order == 7) {
            map.put("order", "cusCount");
        }
        if (order == 8) {
            map.put("order", "crossProfit");
        }
        if (order == 9) {
            map.put("order", "stockRate");
        }
        if (order == 10) {
            map.put("order", "stockDays");
        }
        if (order == 11) {
            map.put("order", "turnOverRatio");
        }
        if (order == 12) {
            map.put("order", "lastOrderTime");
        }
        if (orderType == 1) {
            map.put("orderType", "DESC");
        }
        if (orderType == 2) {
            map.put("orderType", "ASC");
        }
        return shopService.queryShopsList(map);
    }

    /**
     * 查询某区域内的所有审核通过的非自营店铺信息
     *
     * @param areaDictNum
     * @return
     */
    @RequestMapping("/queryShopsListForGoods.do")
    @ResponseBody
    public ShopsResult queryShopsListForGoods(
            @RequestParam(value = "areaDictNum") String areaDictNum
    ) {
        return shopService.queryShopsListForGoods(areaDictNum);
    }

    @RequestMapping("/shopsOnLine.do")
    public String shopsOnLine(HttpServletRequest request) {
        return "/WEB-INF/shop/shopsOnLine.jsp";
    }

    @RequestMapping("/pcUpdate.do")
    public String pcUpdate(HttpServletRequest request) {
        return "/WEB-INF/shop/pcUpdate.jsp";
    }

    /**
     * 查询商家结算列表页面
     *
     * @return
     */
    @RequestMapping("/settlement.do")
    public String settlement() {
        return "/WEB-INF/shop/settlementList.jsp";
    }

    @RequestMapping("/toDownloadFile.do")
    public String toDownloadFile() {
        return "/WEB-INF/shop/downloadFile.jsp";
    }

    /**
     * 查询宁宇分店结算列表页面
     *
     * @return
     */
    @RequestMapping("/settlementNY.do")
    public String settlementNY() {
        return "/WEB-INF/shop/settlementNYList.jsp";
    }

    /**
     * 查询商家结算列表
     *
     * @param shop_name 店铺名称
     * @return
     */
    @RequestMapping("/settlementList.do")
    @ResponseBody
    public PurResult settlementList(
            @RequestParam(value = "page", defaultValue = "0") int page,
            @RequestParam(value = "limit", defaultValue = "8") int pageSize,
            String shop_name) {
        Map<String, Object> params = new HashMap<String, Object>();
//		params.put("startNum", ((page-1)%44)*pageSize);//shopTest
        params.put("startNum", (page - 1) * pageSize);
        params.put("pageSize", pageSize);
        params.put("shop_name", shop_name);
        return shopService.settlement(params);
    }

    /**
     * 查询宁宇分店结算列表
     *
     * @param shop_name 店铺名称
     * @return
     */
    @RequestMapping("/settlementList2.do")
    @ResponseBody
    public PurResult settlementList2(
            @RequestParam(value = "page", defaultValue = "0") int page,
            @RequestParam(value = "limit", defaultValue = "8") int pageSize,
            String sale_date,
            String shop_name) {
        Map<String, Object> params = new HashMap<String, Object>();
        params.put("startNum", (page - 1) * pageSize);
        params.put("pageSize", pageSize);
        params.put("shop_name", shop_name);
        params.put("start_date", sale_date.split("~")[0].trim());
        params.put("end_date", sale_date.split("~")[1].trim() + " 23:59:59");

        return shopService.settlement2(params);
    }

    /**
     * 查询商家结算记录列表页面
     *
     * @return
     */
    @RequestMapping("/shopCouponCashPage.do")
    public String shopCouponCashPage(String shop_unique, Model model) {
        model.addAttribute("shop_unique", shop_unique);
        return "/WEB-INF/shop/shopCouponCash.jsp";
    }

    /**
     * 查询商家结算记录列表
     *
     * @param shop_unique   店铺唯一编号
     * @param cash_mode     提现方式：1、商家提现 2、系统结算
     * @param handle_status 提现进度：1、待处理；2、已处理；3、驳回；4、其他
     * @return
     */
    @RequestMapping("/shopCouponCashList.do")
    @ResponseBody
    public PurResult shopCouponCashList(
            @RequestParam(value = "page", defaultValue = "0") int page,
            @RequestParam(value = "limit", defaultValue = "8") int pageSize,
            String shop_unique, String cash_mode, String handle_status) {
        Map<String, Object> params = new HashMap<String, Object>();
        params.put("startNum", (page - 1) * pageSize);
        params.put("pageSize", pageSize);
        params.put("shop_unique", shop_unique);
        params.put("cash_mode", cash_mode);
        params.put("handle_status", handle_status);
        return shopService.shopCouponCashList(params);
    }

    /**
     * 结算确认页
     *
     * @param shop_unique 店铺唯一编号
     * @return
     */
    @RequestMapping("/settlementConfirmPage.do")
    public String settlementConfirmPage(String shop_unique, Model model) {
        //获取商家银行卡信息
        Map<String, Object> cardInfo = shopService.getShopCardInfo(shop_unique);
        //获取商家余额
        ShopVO shopVO = shopService.getShopBalance(shop_unique);
        //线上扫码支付日收益
        Double dayBalance = shopService.getShopBalanceDay(shop_unique);
        //线下拉卡拉支付日收益
        Double dayBalance2 = shopService.getShopBalanceDay2(shop_unique);
        //到平台易通日收益
        Double dayBalance3 = shopService.getShopBalanceDay3(shop_unique);
        model.addAttribute("yitong", dayBalance3);
        //获取结算费率,千分之
        ShopVO rateVO = shopService.getSysRate(shop_unique);
        DecimalFormat df = new DecimalFormat("0.0000");
        String num = df.format((float) rateVO.getRate() / 1000);
        String num1 = df.format((float) rateVO.getRate1() / 10000);
        String appNum = df.format((float) rateVO.getAppRate() / 10000);
        Double rates = Double.valueOf(num);
        Float rates1 = Float.valueOf(num1);
        Double appRates = Double.valueOf(appNum);
        model.addAttribute("cardInfo", cardInfo);
        //总余额
        model.addAttribute("balance", shopVO.getBalance());
        //线上扫码支付金额
        model.addAttribute("shop_balance", shopVO.getShop_balance());
        //线下拉卡拉支付金额
        model.addAttribute("lkl_balance", shopVO.getLkl_balance());
        //商家百货豆
        model.addAttribute("shop_beans", shopVO.getShop_beans());
        Double bean = 0.00;
        if (shopVO.getShop_beans() < 0) {
            //百货豆抵扣
            String beanDiKou = df.format(((float) shopVO.getShop_beans() * -1 / 100) * (1 + rates));
            bean = Double.valueOf(beanDiKou);

        }
        model.addAttribute("bean", bean);
        //商家结算金额=商家余额-日收益
        String take_money = BigDecimal.valueOf(shopVO.getBalance() - dayBalance - dayBalance2 - dayBalance3).setScale(2, BigDecimal.ROUND_HALF_UP) + "";
        model.addAttribute("take_money", take_money);
        //实际到账金额
        double take_money2 = BigDecimal.valueOf(shopVO.getShop_balance() - dayBalance).setScale(2, BigDecimal.ROUND_HALF_UP).doubleValue();
        double take_money3 = BigDecimal.valueOf(shopVO.getLkl_balance() - dayBalance2 - dayBalance3).setScale(2, BigDecimal.ROUND_HALF_UP).doubleValue();
        //线下拉卡拉支付结算金额
        model.addAttribute("take_money2", take_money2);//结算金额=余额-日收益
        //线下拉卡拉支付结算金额
        model.addAttribute("take_money3", take_money3);//结算金额=余额-日收益
        System.out.println(take_money2 * (1 - appRates));
        BigDecimal take_money1 = ((new BigDecimal(String.valueOf(take_money2)).multiply(new BigDecimal("1").subtract(new BigDecimal(String.valueOf(appRates))))).setScale(2,BigDecimal.ROUND_HALF_UP)).
                add((new BigDecimal(String.valueOf(take_money3)).multiply(new BigDecimal("1").subtract(new BigDecimal(String.valueOf(rates1))))).setScale(2,BigDecimal.ROUND_HALF_UP)).setScale(2, BigDecimal.ROUND_HALF_UP);

        model.addAttribute("take_money1", take_money1.doubleValue());
        model.addAttribute("shop_unique", shop_unique);
        //线上扫码支付费率
        model.addAttribute("rates", rates);
        model.addAttribute("appRates", appRates);
        //线下拉卡拉支付费率
        model.addAttribute("rates1", rates1);
        model.addAttribute("dayBalance", dayBalance);
        model.addAttribute("dayBalance2", dayBalance2);

        return "/WEB-INF/shop/settlementConfirm.jsp";
    }

    /**
     * 宁宇分店结算详情界面
     *
     * @param shop_unique 店铺唯一编号
     * @return
     */
    @RequestMapping("/settlementConfirmNYPage.do")
    public String settlementConfirmNYPage(String shop_unique, String sale_date, Model model) {

        model.addAttribute("shop_unique", shop_unique);
        model.addAttribute("startTime", sale_date.split("~")[0].trim());
        model.addAttribute("endTime", sale_date.split("~")[1].trim());
        return "/WEB-INF/shop/settlementConfirmNY.jsp";
    }

    /**
     * 结算
     *
     * @param shop_unique 店铺唯一编号
     * @param take_money  提现金额
     * @param staff_id    申请者
     * @param manager_id  管理人员ID
     * @param card_id     提现第三方帐号：银行卡号，支付宝账号或微信号等
     * @param card_type   提现的账户类型：1、微信；2、支付宝；3、银行卡；4、其他
     * @param remarks     备注
     * @return
     */
    @RequestMapping("/settlementConfirm.do")
    @ResponseBody
    public PurResult settlementConfirm(
            String shop_unique,
            String take_money,
            String take_money2,
            String take_money3,
            String staff_id,
            String bean,
            String manager_id, String card_id, String card_type, String remarks, String phone, String payMoney, String shopName, String bankName, String bankAccount, String bank_customer_num) {
        Map<String, Object> params = new HashMap<String, Object>();
        params.put("shop_unique", shop_unique);
        params.put("take_money", take_money);
        params.put("take_money2", take_money2);
        params.put("take_money3", take_money3);
        params.put("staff_id", staff_id);
        params.put("manager_id", manager_id);
        params.put("card_id", card_id);
        params.put("card_type", card_type);
        params.put("remarks", remarks);
        params.put("bean", bean);
        params.put("actual_money", payMoney);
        params.put("bank_customer_num", bank_customer_num);
        return shopService.settlementConfirm(params, phone, payMoney, shopName, bankName, bankAccount);
    }
    /**
            * 跳转店铺聚合码 管理列表页
     * @return
     */
    @RequestMapping("/togetherCodePage.do")
    public String togetherCodePage() {
        return "/WEB-INF/shop/togetherCode.jsp";
    }

    /**
     * 跳转店铺管理列表页
     *
     * @return
     */
    @RequestMapping("/shopListPage.do")
    public String shopListPage() {
        return "/WEB-INF/shop/shopList.jsp";
    }

    /**
     * 跳转库存管理方式页面
     *
     * @return
     */
    @RequestMapping("/goodsInPriceType.do")
    public String inventoryManageMethod(String shopUnique, String shopName, Model model) {
        model.addAttribute("shopUnique", shopUnique);
        model.addAttribute("shopName", shopName);
        ShopConfigQueryResult shopConfigQueryResult = shopService.queryShopConfig(shopUnique);
        if (ObjectUtil.isNotEmpty(shopConfigQueryResult)) {
            model.addAttribute("goodsInPriceType", shopConfigQueryResult.getGoodsInPriceType());
        } else {
            model.addAttribute("goodsInPriceType", 0);
        }
        return "/WEB-INF/shop/goodsInPriceType.jsp";
    }

    /**
     * 益农-站点管理
     */
    @RequestMapping("/siteManagerList.do")
    public String siteManager() {
        return "/WEB-INF/shop/siteManagerList.jsp";
    }
    
    /**
     * 店铺管理列表
     *
     * @param shop_message    店铺信息
     * @param examinestatus   店铺审核状态：1，未提交申请；2，已提交申请；3，审核未通过；4，审核通过；5：已撤回
     * @param face_pay_status 人脸支付 0：未开通 1:已开通
     * @param beans_agreement 0、未开通（未签订协议）；1、开通
     * @param show_buy_status 微信小程序开通状态：0:不显示 1:显示 2:审核中 3:审核失败
     * @param reward          2019-6-13 会员返利，店铺查询使用
     * @return
     */
    @RequestMapping("/shopList.do")
    @ResponseBody
    public PurResult shopList(
            @RequestParam(value = "page", defaultValue = "1") int page,
            @RequestParam(value = "limit", defaultValue = "15") int pageSize,
            String shop_message,
            String examinestatus,
            String face_pay_status,
            String beans_agreement,
            String show_buy_status,
            String reward,
            String shop_type,
            String area_dict_num
    ) {
        Map<String, Object> params = new HashMap<String, Object>();
//		params.put("startNum", ((page-1)%44)*pageSize);//shopTest
        params.put("startNum", (page - 1) * pageSize);
        params.put("pageSize", pageSize);
        params.put("shop_message", shop_message);
        params.put("examinestatus", examinestatus);
        params.put("face_pay_status", face_pay_status);
        params.put("beans_agreement", beans_agreement);
        params.put("show_buy_status", show_buy_status);
        params.put("reward", reward);
        params.put("shop_type", shop_type);
        params.put("area_dict_num", area_dict_num);
        return shopService.shopList(params);
    }

    /**
     * 跳转店铺详情页
     *
     * @param shop_unique 店铺唯一编号
     * @return
     */
    @RequestMapping("/shopDetailPage.do")
    public String shopDetailPage(String shop_unique, Model model) {
        Map<String, Object> shopInfo = shopService.getShopDetail(shop_unique);
        model.addAttribute("shopInfo", shopInfo);
        return "/WEB-INF/shop/shop.jsp";
    }

    @RequestMapping("/downloadNYLoginRecordExcel.do")
    public void downloadNYLoginRecordExcel(
            String startTime,
            String endTime,
            String search_name,
            String shopUnique,
            Integer handover,
            HttpServletRequest request, HttpServletResponse response) {
        Map<String, Object> map = new HashMap<String, Object>();
        map.put("startTime", startTime);
        map.put("endTime", endTime);

        List<Map<String, Object>> list = (List<Map<String, Object>>) shopService.queryLoginRecord(shopUnique, startTime, endTime, null, null, handover).getData();
        if (null == list || list.isEmpty()) {
            return;
        }
        String fileName = "交接班记录.xls";
        //非空完成下载
        @SuppressWarnings("deprecation")
        String filePath = request.getRealPath("/");
        System.out.println(filePath);
        File file = new File(filePath + File.separator + fileName);
        List<TitleForExcel> tl = new ArrayList<>();

        TitleForExcel t2 = new TitleForExcel("shop_name", "店铺名称");
        TitleForExcel t3 = new TitleForExcel("loginTime", "接班时间");
        TitleForExcel t5 = new TitleForExcel("logoutTime", "交班时间");
        TitleForExcel t6 = new TitleForExcel("saleListTotal", "营业额");
        TitleForExcel t7 = new TitleForExcel("listCount", "订单数量");
        TitleForExcel t8 = new TitleForExcel("cashMoney0", "赊账");
        TitleForExcel t9 = new TitleForExcel("cashMoney1", "现金");
        TitleForExcel t10 = new TitleForExcel("cashMoney2", "支付宝");
        TitleForExcel t11 = new TitleForExcel("cashMoney3", "微信");
        TitleForExcel t12 = new TitleForExcel("cashMoney4", "本金");
        TitleForExcel t13 = new TitleForExcel("cashMoney5", "赠额");
        TitleForExcel t14 = new TitleForExcel("cashMoney13", "金圈平台");
        tl.add(t2);
        tl.add(t3);
        tl.add(t5);
        tl.add(t6);
        tl.add(t7);
        tl.add(t8);
        tl.add(t9);
        tl.add(t10);
        tl.add(t11);
        tl.add(t12);
        tl.add(t13);
        tl.add(t14);

        ExcelUtil.ExcelForListMap(list, file, tl);
        OutputStream os = null;
        FileInputStream fis = null;
        int bytesRead = 0;
        byte[] buffer = new byte[8192];

        try {
            if (!file.exists()) {
                file.createNewFile();
            }
            response.setContentType("application/x-msdownload; charset=UTF-8");// 告知浏览器文件类型
            String agent = request.getHeader("user-agent");
            if (agent.contains("Firefox")) {
                response.setHeader(
                        "Content-Disposition",
                        "attachment;filename="
                                + new String((fileName)
                                .getBytes("GB2312"), "ISO-8859-1"));// 设置文件头
            } else {
                response.setHeader(
                        "Content-Disposition",
                        "attachment;filename="
                                + URLEncoder.encode(fileName, "UTF-8"));// 设置文件头
            }
            fis = new FileInputStream(file);
            os = response.getOutputStream();
            while ((bytesRead = fis.read(buffer, 0, 8192)) != -1) {
                os.write(buffer, 0, bytesRead);
            }
            ;
            os.flush();
        } catch (IOException e) {
            e.printStackTrace();
        } finally {
            try {
                if (os != null) {
                    os.close();
                }
                if (fis != null) {
                    fis.close();
                }
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
    }
    
    
    
    /**
             * 店铺聚合管理列表
     *
     * @param shop_message    店铺信息
     * @param examinestatus   店铺审核状态：1，未提交申请；2，已提交申请；3，审核未通过；4，审核通过；5：已撤回
     * @return
     */
    @RequestMapping("/togetherCode.do")
    @ResponseBody
    public ShopsResult togetherCodePage(
            @RequestParam(value = "page", defaultValue = "1") int page,
            @RequestParam(value = "limit", defaultValue = "15") int pageSize,
            String shop_message,
            String examinestatus
  
    ) {
        Map<String, Object> params = new HashMap<String, Object>();
        params.put("startNum", (page - 1) * pageSize);
        params.put("pageSize", pageSize);
        params.put("shop_message", shop_message);
        params.put("examinestatus", examinestatus);
        return shopService.togetherCode(params);
    }
    
    /**
     * 跳转店铺聚合码详情页
     *
     * @param shop_unique 店铺唯一编号
     * @return
     */
    @RequestMapping("/togetherCodeDetailPage.do")
    public String togetherCodeDetailPage(String shop_unique, Model model) {
        Map<String, Object> togetherCodeInfo = shopService.getTogetherCodeInfo(shop_unique);
        model.addAttribute("shopInfo", togetherCodeInfo);
        return "/WEB-INF/shop/togetherCodeDetail.jsp";
    }
    
    	/**
		    * 聚合码审核
		*
		* @return
		*/
		@RequestMapping("/updateTogetherCode.do")
		@ResponseBody
		public ShopsResult updateTogetherCode(
		    String shop_unique,
		    String aggregate_audit_status,
		    String aggregation_code_id,
		    String aggregate_refuse_reason
		    
		) {
		return shopService.updateTogetherCode(shop_unique,aggregate_audit_status,aggregation_code_id,aggregate_refuse_reason);
		}

        /**
         * 设置库存管理方式
         *
         * @return
         */
        @PostMapping("/editGoodsInPriceType.do")
        @ResponseBody
        public ShopsResult editGoodsInPriceType(@Validated ShopGoodsInPriceTypeEditParams params){
            return shopService.editGoodsInPriceType(params);
        }
}
