package org.haier.shop.controller;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;

import org.haier.log.annotation.RemoteLog;
import org.haier.log.enums.BusinessType;
import org.haier.shop.params.purchase.PurchaseGoodsAddParams;
import org.haier.shop.params.purchase.PurchaseGoodsRetParam;
import org.haier.shop.redis.RedisCache;
import org.haier.shop.service.SelfPurchaseService;
import org.haier.shop.util.PurResult;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import net.sf.json.JSONArray;
import net.sf.json.JSONObject;

/**
 * 商家自采购进货单控制器
 */
@Controller
@RequestMapping("/selfPurchase")
public class SelfPurchaseController {
	
	@Resource
	private SelfPurchaseService selfPurchaseService;
	
	@Autowired
	private RedisCache rc;
	
	/**
	 * 添加退货订单详情
	 * @param shop_unique 店铺编号
	 * @param supplier_unique 供货商编号
	 * @param total_count 总数量
	 * @param total_price 总金额
	 * @param purchase_status
	 * @param remark
	 * @param create_staff_id
	 * @param update_staff_id
	 * @param goods_details[{
	 * 	goods_name 商品名称
	 *  goods_barcode 商品编码
	 *  goods_in_price 退货价
	 *  goods_count 商品数量
	 *  unit_name 商品单位
	 *  gift_type 是否赠品：1非赠品 2赠品
	 * }]
	 * @param network_ip
	 * @return
	 */
	@RemoteLog(title = "添加退货订单", businessType = BusinessType.INSERT)
	@PostMapping("/insertSelfRetPurchase.do")
	@ResponseBody
	public PurResult insertSelfRetPurchase(@RequestBody @Validated PurchaseGoodsRetParam retParam){
		return selfPurchaseService.insertSelfRetPurchase(retParam);
	}
	
	/**
	 * 
	 * @param shopUnique 店铺编号
	 * @param selfPurchaseUnique 进货单号
	 * @param goodsDetail 退货详情
	 * @param supplierUnique 供货商编号
	 * @param staffId 操作员工ID
	 * @param operationId 本次操作号，前端随机产生，方式页面重复提交
	 * @return
	 */
	@RemoteLog(title = "进货订单退款", businessType = BusinessType.INSERT)
	@RequestMapping("/addNewReturnPurList.do")
	@ResponseBody
	public PurResult addNewReturnPurList(
			String shopUnique,String selfPurchaseUnique,String goodsDetail,
			String supplierUnique,String staffId,String operationId,String remark
		) {
		PurResult pr = new PurResult(1, "提交成功!");
		try {
			if(null == rc.getObject("purRet" + operationId)) {
				rc.putObject("purRet" + operationId, operationId);
				return selfPurchaseService.addNewReturnPurList(shopUnique, selfPurchaseUnique, goodsDetail, 
						supplierUnique, staffId,remark);
			}else {
				pr.setStatus(0);
				pr.setMsg("退款操作已提交，请勿重复操作!");
			}
		}catch (Exception e) {
			e.printStackTrace();
			pr.setStatus(0);
			pr.setMsg("系统错误！");
		}
		return pr;
	}
	
	/**
	 * 查询订单详情
	 * @param self_purchase_unique
	 * @return
	 */
	@RequestMapping("/getSelfPurchaseReturnDetailList.do")
	@ResponseBody
	public PurResult getSelfPurchaseReturnDetailList(String self_purchase_unique,String shop_unique) {
		return selfPurchaseService.getSelfPurchaseReturnDetailList(self_purchase_unique,shop_unique);
	}
	/**
	 * 
	 * @param order_code 订单编号
	 * @param payMoney 本次支付金额
	 * @param surplusMoney 剩余支付金额
	 * @param staff_id 员工ID
	 * @param shop_unique 店铺编号
	 * @return
	 */
	@RequestMapping("/addSelfPurPay.do")
	@ResponseBody
	public PurResult addSelfPurPay(String order_code,Double payMoney,
			Double surplusMoney,String staff_id,String shop_unique
			) {
		return selfPurchaseService.addSelfPurPay(order_code, payMoney, surplusMoney, staff_id, shop_unique);
	}
	
	@RequestMapping("/querySaleList.do")
	@ResponseBody
	public PurResult querySaleList(){
		PurResult pr = new PurResult();
		
		pr.setData(selfPurchaseService.querySaleList());
		
		return pr;
	}
	/**
	 * 查询进货订单列表
	 * @param shop_unique
	 * @param search_str 订单编号/供货商名称
	 * @param start_date 查询开始时间
	 * @param end_date 查询结束时间
	 * @param pay_status 支付状态：0全部支付 1欠款
	 * @param purchase_status 采购单状态：0待收货 1已完成 2已取消
	 * @param pageNum
	 * @param pageSize
	 * @return
	 */	
	@RequestMapping("getSelfPurchaseList.do")
	@ResponseBody
	public PurResult getSelfPurchaseList(
			@RequestParam(value="shop_unique",required=true)String shop_unique,
			String search_str,
			String start_date,
			String end_date,
			String pay_status, 
			String purchase_status,
			@RequestParam(value="pageNum",defaultValue="1")Integer pageNum, 
			@RequestParam(value="pageSize",defaultValue="8")Integer pageSize
			){
		Map<String ,Object> params = new HashMap<String, Object>();
		params.put("shop_unique", shop_unique);
		params.put("search_str", search_str);
		params.put("start_date", start_date);
		params.put("end_date", end_date);
		params.put("pay_status", pay_status);
		params.put("purchase_status", purchase_status);
		params.put("startNum", (pageNum-1)*pageSize);
		params.put("pageSize", pageSize);
		return selfPurchaseService.getSelfPurchaseList(params);
	}
	
	
	@RequestMapping("getSelfRetPurchase.do")
	@ResponseBody
	public PurResult getSelfRetPurchase(String selfPurchaseRetUnique) {
		return selfPurchaseService.getSelfRetPurchase(selfPurchaseRetUnique);
	}
	
	/**
	 * 查询订单详情
	 * @param shop_unique 店铺唯一标示
	 * @param self_purchase_unique 订单编号
	 * @return
	 */	
	@RequestMapping("getSelfPurchase.do")
	@ResponseBody
	public PurResult getSelfPurchase(String shop_unique,String self_purchase_unique){
		Map<String ,Object> params = new HashMap<String, Object>();
		params.put("shop_unique", shop_unique);
		params.put("self_purchase_unique", self_purchase_unique);
		return selfPurchaseService.getSelfPurchase(params);
	}

	/**
	 * 添加进货单信息
	 * @param addParams
	 * @return
	 */
	@RemoteLog(title="添加进货单信息", businessType = BusinessType.INSERT)
	@PostMapping("/insertSelfPurchase.do")
	@ResponseBody
	public PurResult insertSelfPurchase(@RequestBody @Validated PurchaseGoodsAddParams addParams){
		return selfPurchaseService.insertSelfPurchase(addParams);
	}
	
	/**
	 * 添加进货单支付记录信息
	 * @param self_purchase_unique 自采购进货单唯一标示
	 * @param shop_unique 店铺唯一标示
	 * @param pay_money 本次支付金额
	 * @param staff_id 操作员工编号
	 * @param source_type 操作终端：1商家后台 2Android 3ios
	 * @param network_ip 操作网络ip
	 * @return
	 */	
	@RequestMapping("insertSelfPurchasePay.do")
	@ResponseBody
	public PurResult insertSelfPurchasePay(
			String self_purchase_unique,
			String shop_unique,
			String pay_money,
			String staff_id,
			String source_type,
			String network_ip
			){
		Map<String ,Object> params = new HashMap<String, Object>();
		params.put("self_purchase_unique", self_purchase_unique);
		params.put("shop_unique", shop_unique);
		params.put("pay_money", pay_money);
		params.put("staff_id", staff_id);
		params.put("source_type", source_type);
		params.put("network_ip", network_ip);
		return selfPurchaseService.insertSelfPurchasePay(params);
	}
	
	/**
	 * 修改进货单信息
	 * @param self_purchase_unique 自采购进货单唯一标示
	 * @param shop_unique 店铺唯一标示
	 * @param purchase_status 采购单状态：0待收货 1已完成 2已取消
	 * @param update_staff_id 修改员工编号
	 * @aram stock_origin 操作来源：1、手机；2、PC端；3、web网页端；
	 * @return
	 */	
	@RequestMapping("updateSelfPurchase.do")
	@ResponseBody
	public PurResult updateSelfPurchase(
			String self_purchase_unique,
			String shop_unique,
			String purchase_status,
			String update_staff_id,
			String stock_origin
			){
		Map<String ,Object> params = new HashMap<String, Object>();
		params.put("self_purchase_unique", self_purchase_unique);
		params.put("shop_unique", shop_unique);
		params.put("purchase_status", purchase_status);
		params.put("update_staff_id", update_staff_id);
		params.put("stock_origin", stock_origin);
		return selfPurchaseService.updateSelfPurchase(params);
	}
	
	/**
	 * 删除进货单信息
	 * @param self_purchase_unique 自采购进货单唯一标示
	 * @param shop_unique 店铺唯一标示
	 * @param del_flag 删除标示:0正常 1删除
	 * @return
	 */	
	@RequestMapping("deleteSelfPurchase.do")
	@ResponseBody
	public PurResult deleteSelfPurchase(
			String self_purchase_unique,
			String shop_unique,
			String del_flag
			){
		Map<String ,Object> params = new HashMap<String, Object>();
		params.put("self_purchase_unique", self_purchase_unique);
		params.put("shop_unique", shop_unique);
		params.put("del_flag", del_flag);
		return selfPurchaseService.deleteSelfPurchase(params);
	}
	
}
