package org.haier.shop.controller;

import org.haier.shop.params.speech.AddNewSpeechCmdParams;
import org.haier.shop.params.speech.QuerySpeechCmdListParams;
import org.haier.shop.params.speech.QuerySpeechListParams;
import org.haier.shop.service.SpeechService;
import org.haier.shop.util.ShopsResult;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.annotation.Resource;

@Controller
@RequestMapping("/speech")
public class SpeechController {
    @Resource
    private SpeechService speechService;

    /**
     * 根据参数查询语音列表
     * @param querySpeechListParams
     * @return
     */
    @RequestMapping("/querySpeechListByParam.do")
    @ResponseBody
    public ShopsResult querySpeechListByParam(QuerySpeechListParams querySpeechListParams)
    {
        return speechService.querySpeechListByParam(querySpeechListParams);
    }

    @RequestMapping("/toSpeechListPage.do")
    public String toSpeechListPage()
    {
    	return "/WEB-INF/speech/speechList.jsp";
    }

    /**
     * 查询语音指令列表
     * @param params
     * @return
     */
    @RequestMapping("/querySpeechCmdList.do")
    @ResponseBody
    public ShopsResult querySpeechCmdList(QuerySpeechCmdListParams params) {
        return speechService.querySpeechCmdList(params);
    }

    @RequestMapping("/toSpeechCmdListPage.do")
    public String toSpeechCmdListPage()
    {
    	return "/WEB-INF/speech/speechCmdList.jsp";
    }

    @RequestMapping("/toSpeechCmdAddPage.do")
    public String toSpeechCmdAddPage() {
    	return "/WEB-INF/speech/speechCmdAdd.jsp";
    }

    /**
     * 新增语音指令
     * @param params
     * @return
     */
    @RequestMapping("/addNewSpeechCmd.do")
    @ResponseBody
    public ShopsResult addNewSpeechCmd(@RequestBody AddNewSpeechCmdParams params) {
        System.out.println("请求的参数信息为" + params);
        return speechService.addNewSpeechCmd(params);
    }
}
