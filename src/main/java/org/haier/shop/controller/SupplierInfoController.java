package org.haier.shop.controller;

import java.util.HashMap;
import java.util.Map;

import javax.annotation.Resource;

import org.haier.shop.service.SupplierInfoService;
import org.haier.shop.util.PurResult;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;


@Controller
@RequestMapping("/supperlierInfo")
public class SupplierInfoController {

	@Resource
	private SupplierInfoService supplierInfoService;
	
	//跳转供货商列表页面
	@RequestMapping("/getSupplierListPage.do")
	public String getSupplierListPage(String goods_barcode,Model model){
		model.addAttribute("goods_barcode", goods_barcode);
		return "/WEB-INF/purchase/supplierList.jsp";
	}
	
	/**
	 * 获取商品所有供货商列表
	 * @param map
	 * @return
	 */
	@RequestMapping("/getSupplierList.do")
	@ResponseBody
	public PurResult getSupplierList(String goods_barcode,String shop_unique,String company_name){
		Map<String,Object> params=new HashMap<String, Object>();
		params.put("goods_barcode", goods_barcode);
		params.put("company_name", company_name);
		params.put("shop_unique", shop_unique);
		
		PurResult result = supplierInfoService.getSupplierList(params);
		return result;
	}
	
	//跳转供货商列表页面
	@RequestMapping("/getSupplierGoodsListPage.do")
	public String getSupplierGoodsListPage(String goods_barcode,String company_code,Model model){
		model.addAttribute("goods_barcode", goods_barcode);
		model.addAttribute("company_code", company_code);
		return "/WEB-INF/purchase/supplierGoodsList.jsp";
	}
	
	/**
	 * 获取当前供货商的当前商品信息
	 * @param map
	 * @return
	 */
	@RequestMapping("/getSupplierGoodsList.do")
	@ResponseBody
	public PurResult getSupplierGoodsList(String goods_barcode,String company_code,String shop_unique){
		Map<String,Object> params=new HashMap<String, Object>();
		params.put("goods_barcode", goods_barcode);
		params.put("company_code", company_code);
		params.put("shop_unique", shop_unique);
		PurResult result = supplierInfoService.getSupplierGoodsList(params);
		return result;
	}
	
	/**
	 * 保存订单信息
	 * @param shop_unique 店铺唯一标示
	 * @param order_remarks 订单备注
	* @param goods [{
	 * 	company_code 公司编号
	 *  goodsList [{
	 *  	goods_barcode 商品条形码
	 *  	compose_specs_id 规格组合id
	 *  	goods_count 商品数量
	 *  	goods_name 商品名称
	 *  	goodsunit_name 商品单位名称
	 *  	goods_spec_name 商品规则名称
	 *  	wholesale_price 销售价
	 *  	goods_id 商品id
	 *  	goods_price 成本价
	 *  	base_barcode 基本单位条形编码
	 *  	proportion_num 换算比例
	 *  	goods_beans_count 每单位实际赠送百货豆
	 *  	present_beans 每单位满足规则赠送百货豆
	 *  	beans_goods_give_id 优惠活动id
	 *  }]
	 * }]
	 * @return
	 */
	@RequestMapping("/saveOrder.do")
	@ResponseBody
	public PurResult saveOrder(String shop_unique,String order_remarks ,String goods){
		PurResult result = new PurResult();
		try {
			result = supplierInfoService.saveOrder(shop_unique,order_remarks,goods);
		} catch (Exception e) {
			result.setStatus(0);
			result.setMsg(e.getMessage());
		}
		
		return result;
	}
	//查询教育局设定价格
	@RequestMapping("/querySchoolContentList.do")
	@ResponseBody
	public PurResult querySchoolContentList(){
		PurResult result = supplierInfoService.querySchoolContentList();
		return result;
	}
	@RequestMapping("/querySchoolContent.do")
	public String querySchoolContent(String id,Model model){
		Map<String,Object> params=new HashMap<String, Object>();
		params.put("id", id);
		params = supplierInfoService.querySchoolContent(id);

		model.addAttribute("content", params.get("content"));
		return "/WEB-INF/note/note_t.jsp";
	}
	
}
