package org.haier.shop.controller;

import javax.annotation.Resource;

import org.haier.shop.service.ShopAddressService;
import org.haier.shop.util.ShopsResult;
import org.springframework.stereotype.Controller;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

@Controller
@RequestMapping("/address")
public class ShopAddressController {

	@Resource
	private ShopAddressService addressService;
	
	
	/**
	 * 添加或修改店铺地址
	 * @param id 原地址ID
	 * @param shop_unique 店铺编号
	 * @param province_code 省编码
	 * @param city_code 市编码
	 * @param county_code 区编码
	 * @param longitude 经度
	 * @param latitude 维度
	 * @param contacts 联系人
	 * @param address_detail 收货地址
	 * @param contacts_phone 联系方式
	 * @param default_status 1、默认地址；0、非默认地址
	 * @param valid_status 1、正常；0、删除
	 * @return
	 */
	@RequestMapping("/addNewShopAddress.do")
	@ResponseBody
	public ShopsResult addNewShopAddress(
			String id,
			String shop_unique,
			String province_code,
			String city_code,
			String county_code,
			Double longitude,
			Double latitude,
			String contacts,
			String address_detail,
			String contacts_phone,
			Integer default_status,
			Integer valid_status
			) {
		try {
			return addressService.addNewShopAddress(id, shop_unique, province_code, city_code, county_code,
					longitude, latitude, contacts, address_detail, contacts_phone, default_status, valid_status);
		} catch (Exception e) {
			e.printStackTrace();
			return new ShopsResult(0,"操作失败!");
		}
	}
	
	@RequestMapping("/toEditShopAddress.do")
	public String toEditShopAddress() {
		return "/WEB-INF/address/addShopAddress.jsp";
	}
	
	@RequestMapping("/toAddressManager.do")
	public String toAddressManager() {
		return "/WEB-INF/address/addressManager.jsp";
	}
	/**
	 * 查询店铺的所有收货地址信息
	 * @param shopUnique
	 * @return
	 */
	@RequestMapping("/queryShopAddressList.do")
	@ResponseBody
	public ShopsResult queryShopAddressList(String shopUnique) {
		try {
			return addressService.queryShopAddressList(shopUnique);
		} catch (Exception e) {
			e.printStackTrace();
			return new ShopsResult(1, "查询失败！");
		}
	}
}
