package org.haier.shop.controller;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.haier.shop.service.GoldService;
import org.haier.shop.util.HandleMessyCode;
import org.haier.shop.util.PurResult;
import org.haier.shop.util.ServletsUtil;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import net.sf.json.JSONArray;
import net.sf.json.JsonConfig;

@RequestMapping("/gold")
@Controller
public class GoldController {
	
	private static final Logger logger = LoggerFactory.getLogger(GoldController.class);
	
	@Resource
	private GoldService goldService;

	/**
	 * 平台-优惠策略-金圈币
	 * @return
	 */
	@RequestMapping(value = "/queryGoldPage.do")
    public String addPage(){
    	logger.info("优化策略-金圈币页面");
        return "/WEB-INF/gold/goldShopPt.jsp";
    }
	/**
	 * 平台-优惠策略-金圈币-详情
	 * @return
	 */
	@RequestMapping(value = "/toPageDetail.do")
    public String toPageDetail(Model model,String shop_unique){
    	logger.info("优化策略-金圈币店铺详情页面");
    	model.addAttribute("shop_unique", shop_unique);
        return "/WEB-INF/gold/goldShopDetailPt.jsp";
    }
	/**
	 * 平台-优惠策略-金圈币—编辑
	 * @return
	 */
	@RequestMapping(value = "/toPageEdit.do")
    public String toPageEdit(Model model,String shop_unique){
    	logger.info("优化策略-金圈币页面");
    	model.addAttribute("shop_unique", shop_unique);
    	List<Map<String, Object>> list=goldService.queryGoldDevice(shop_unique);
    	Map<String, Object> data=goldService.queryGoldOrder(shop_unique);
    	
    	if(data==null)
    	{
    		 data=goldService.queryGoldRule();
    		 data.put("giveOut_type", 1);
    		 data.put("order_type", "Y");
    		 model.addAttribute("data2", data);
    	}else
    	{
    		data.put("order_type", "N");
    		Map<String, Object> data2=goldService.queryGoldRule();
    		model.addAttribute("data2", data2);
    	}
    	if(list.isEmpty())
    	{
    		model.addAttribute("list", "A");
    	}else
    	{
    		model.addAttribute("list", list);
    	}
    	model.addAttribute("data", data);
    	model.addAttribute("shop_unique", shop_unique);
    	
        return "/WEB-INF/gold/goldShopEditPt.jsp";
    }
	/**
	 * 平台-优惠策略-金圈币—手动发放
	 * @return
	 */
	@RequestMapping(value = "/toPageHand.do")
    public String toPageHand(Model model,String shop_unique){
    	model.addAttribute("shop_unique", shop_unique);
    	
        return "/WEB-INF/gold/manualRelease.jsp";
    }
	/**
	 * 平台-优惠策略-金圈币—新增店铺
	 */
	
	@RequestMapping(value = "/toPageAdd.do")
    public String toPageAdd(Model model){
    	logger.info("优化策略-金圈币页面-新增店铺");
    	List<Map<String, Object>> list=goldService.queryGoldShop();
    	Map<String, Object> data2=goldService.queryGoldRule();
		model.addAttribute("data2", data2);
    	model.addAttribute("shopList", list);
    	
        return "/WEB-INF/gold/goldShopAddPt.jsp";
    }
	/**
	 * 平台-优惠策略-金圈币—默认规则
	 * @return
	 */
	@RequestMapping(value = "/goldRulePage.do")
    public String goldRulePage(Model model){
    	logger.info("优化策略-金圈币页面-默认规则");
    	
    	Map<String, Object> data=goldService.queryGoldRule();
    	
    	model.addAttribute("data", data);
        return "/WEB-INF/gold/goldRule.jsp";
    }
    /**
     * 平台-优惠策略-金圈币
     * @param request
     * @param page
     * @param pageSize
     * @return
     */
    @RequestMapping(value="/queryGoldShopList.do")
	@ResponseBody
	public PurResult queryGoldShopList(
			HttpServletRequest request,
			@RequestParam(value = "page", defaultValue = "0") int page,
			@RequestParam(value = "limit", defaultValue = "15") int pageSize){
    	Map<String, Object> params = HandleMessyCode.handle(ServletsUtil.getParameters(request));
		params.put("limit", pageSize);
		params.put("page", (page-1)*pageSize);
		
		PurResult result = new PurResult();
		try {
			result = goldService.queryGoldShopList(params);
		} catch (Exception e) {
			logger.error("列表查询异常：",e);
			e.printStackTrace();
			result.setStatus(0);
			result.setMsg("异常");
		}
		return result;
	}
    
    /**
     * 平台-优惠策略-金圈币——设备-发放记录
     * @param request
     * @param page
     * @param pageSize
     * @return
     */
    @RequestMapping(value="/queryGoldDeviceList.do")
	@ResponseBody
	public PurResult queryGoldDeviceList(
			HttpServletRequest request,
			@RequestParam(value = "page", defaultValue = "0") int page,
			@RequestParam(value = "limit", defaultValue = "8") int pageSize){
    	Map<String, Object> params = HandleMessyCode.handle(ServletsUtil.getParameters(request));
		params.put("limit", pageSize);
		params.put("page", (page-1)*pageSize);
		
		PurResult result = new PurResult();
		try {
			result = goldService.queryGoldDeviceList(params);
		} catch (Exception e) {
			logger.error("列表查询异常：",e);
			e.printStackTrace();
			result.setStatus(0);
			result.setMsg("异常");
		}
		return result;
	}
    /**
     * 删除发放记录，同事减去店铺对应金圈币
     */
    @RequestMapping(value="/delGoldDetail.do")
	@ResponseBody
	public PurResult delGoldDetail(HttpServletRequest request){
    	Map<String, Object> params = HandleMessyCode.handle(ServletsUtil.getParameters(request));
		
		PurResult result = new PurResult();
		try {
			result = goldService.delGoldDetail(params);
		} catch (Exception e) {
			logger.error("列表查询异常：",e);
			e.printStackTrace();
			result.setStatus(0);
			result.setMsg("异常");
		}
		return result;
	}
    /**
          * 平台-优惠策略-金圈币——设备-订单记录
     * @param request
     * @param page
     * @param pageSize
     * @return
     */
    @RequestMapping(value="/queryGoldOrderList.do")
	@ResponseBody
	public PurResult queryGoldOrderList(
			HttpServletRequest request,
			@RequestParam(value = "page", defaultValue = "0") int page,
			@RequestParam(value = "limit", defaultValue = "8") int pageSize){
    	Map<String, Object> params = HandleMessyCode.handle(ServletsUtil.getParameters(request));
		params.put("limit", pageSize);
		params.put("page", (page-1)*pageSize);
		
		PurResult result = new PurResult();
		try {
			result = goldService.queryGoldOrderList(params);
		} catch (Exception e) {
			logger.error("列表查询异常：",e);
			e.printStackTrace();
			result.setStatus(0);
			result.setMsg("异常");
		}
		return result;
	}
    /**
     * 平台-优惠策略-金圈币——修改默认规则
     * @param request
     * @return
     */
    @RequestMapping(value = "/updatePtRule.do")
    @ResponseBody
    public PurResult updatePtRule(HttpServletRequest request){
    	logger.info("后台管理-修改返利规则");
    	PurResult result = new PurResult();
    	try {
    		Map<String, Object> params = HandleMessyCode.handle(ServletsUtil.getParameters(request));

    		result = goldService.updatePtRule(params);
		} catch (Exception e) {
			result.setStatus(0);
			result.setMsg("异常");
		}
        return result;
    }
    
    /**
     * 平台-优惠策略-金圈币——修改默认规则
     * @param request
     * @return
     */
    @RequestMapping(value = "/updateGoldbyHand.do")
    @ResponseBody
    public PurResult updateGoldbyHand(HttpServletRequest request){
    	logger.info("后台管理-手动添加金圈币");
    	PurResult result = new PurResult();
    	try {
    		Map<String, Object> params = HandleMessyCode.handle(ServletsUtil.getParameters(request));

    		result = goldService.updateGoldbyHand(params);
		} catch (Exception e) {
			result.setStatus(0);
			result.setMsg("异常");
		}
        return result;
    }
    /**
     * 平台-优惠策略-店铺——修改规则
     * @param request
     * @return
     */
    @SuppressWarnings("unchecked")
	@RequestMapping(value = "/updateGoldShopPt.do")
    @ResponseBody
    public PurResult updateGoldShopPt(HttpServletRequest request){
    	logger.info("后台管理-修改返利规则");
    	PurResult result = new PurResult();
    	try {
    		Map<String, Object> params = HandleMessyCode.handle(ServletsUtil.getParameters(request));
    		List<Map<String, Object>> list=new ArrayList<Map<String, Object>>();
    		if(params.containsKey("detailJson"))
    		{
    			JSONArray jsonArray=JSONArray.fromObject(params.get("detailJson"));
    			 list = JSONArray.toList(jsonArray, new HashMap<String, Object>(), new JsonConfig());
    		}
    		
    		result = goldService.updateGoldShopPt(params,list);
		} catch (Exception e) {
			result.setStatus(0);
			result.setMsg("异常");
		}
        return result;
    } 
    /**
     * 平台-优惠策略-店铺——修改规则
     * @param request
     * @return
     */
    @SuppressWarnings("unchecked")
	@RequestMapping(value = "/addGoldShopPt.do")
    @ResponseBody
    public PurResult addGoldShopPt(HttpServletRequest request){
    	logger.info("后台管理-修改返利规则");
    	PurResult result = new PurResult();
    	try {
    		Map<String, Object> params = HandleMessyCode.handle(ServletsUtil.getParameters(request));
    		List<Map<String, Object>> list=new ArrayList<Map<String, Object>>();
    		if(params.containsKey("detailJson"))
    		{
    			JSONArray jsonArray=JSONArray.fromObject(params.get("detailJson"));
    			 list = JSONArray.toList(jsonArray, new HashMap<String, Object>(), new JsonConfig());
    		}
    		
    		result = goldService.addGoldShopPt(params,list);
		} catch (Exception e) {
			e.printStackTrace();
			result.setStatus(0);
			result.setMsg("异常");
		}
        return result;
    }
    
}
