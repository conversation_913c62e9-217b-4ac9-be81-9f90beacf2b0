package org.haier.shop.controller;

import java.io.File;
import java.io.UnsupportedEncodingException;
import java.util.Map;
import java.util.Set;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;

import org.haier.shop.dao.UtilDao;
import org.haier.shop.entity.Remarks;
import org.haier.shop.redis.RedisCache;
import org.haier.shop.service.TestService;
import org.haier.shop.test.QRCodeUtil;
import org.haier.shop.thread.PcUploadThread;
import org.haier.shop.util.HttpGetUtil;
import org.haier.shop.util.PCUpdateUtil;
import org.haier.shop.util.ShopsResult;
import org.haier.util.eshow.Scpclient;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import io.goeasy.GoEasy;
import net.sf.json.JSONArray;
import net.sf.json.JSONObject;

@Controller
@RequestMapping("/test")
public class TestController {
	@Resource
	private TestService testService;
	
	@Resource 
	private UtilDao utilDao;
	
	@Resource
	private RedisCache redis;
	
	@RequestMapping("/getFileMd5.do")
	@ResponseBody
	public ShopsResult getFileMd5(String filePath) {
		ShopsResult sr = new ShopsResult(1,"成功!");
		try {
			File file = new File(filePath);
			String md5 = PCUpdateUtil.getMd5ByFile(file);
			sr.setData(md5);
			return sr;
		}catch (Exception e) {
			e.printStackTrace();
		}
		return sr;
	}
	
	@RequestMapping("/updateShopTownCode.do")
	@ResponseBody
	public ShopsResult updateShopTownCode(Integer maxIndex) {
		return testService.updateShopTownCode(maxIndex);
	}
	
	@RequestMapping("/updateShopMsg.do")
	@ResponseBody
	public ShopsResult updateShopMsg(Integer maxSize ) {
		return testService.updateShopMsg(maxSize);
	}
	
	@RequestMapping("/sendUploadcmd.do")
	@ResponseBody
	public ShopsResult sendUploadcmd() {
		ShopsResult sr = new ShopsResult(1,"成功!");
		PcUploadThread p = new PcUploadThread(utilDao, redis);
		Thread t = new Thread(p);
		t.start();
		return sr;
	}
	
	@ResponseBody
	@RequestMapping("/testPic.do")
	public ShopsResult testPic() {
		ShopsResult sr = new ShopsResult(1,"chengg ");
		
	       try {
	    	String text = "http://test170.buyhoo.cc/service?params={\"shopUnique\":1640592846263,\"staffId\":14529}";
			QRCodeUtil.encode(text, "/mnt/myData/tomcat/tomcat6/webapps/image/staff/test.jpg", "/mnt/myData/tomcat/tomcat6/webapps/image/staff/", true);
			
		} catch (Exception e) {
			e.printStackTrace();
		}
		
		return sr;
	}
	
	@RequestMapping("/test110.do")
	@ResponseBody
	public ShopsResult test0(String ip,Integer port,String user,String password,String localFile,String romoteFile) {
		ShopsResult shop = new ShopsResult(1, "成功");
		ip = ip == null ? "*************":ip;
		port = port == null? 29022 : port;
		user = user == null ? "root" : user;
		password = "p4J0iwoWXSh&#!p";
		boolean usePassword = true;
		String  privateKey = "";
		localFile = localFile == null ? "/mnt/Mydata/logs/error.log" : localFile;
		romoteFile = romoteFile == null ?  "/mnt/myData/ad/upload/" :romoteFile;
		try {
			Scpclient.putFile(localFile, romoteFile, ip, port, user, password, usePassword, privateKey);
		}catch (Exception e) {
			System.out.println("putFile失败");
			e.printStackTrace();
		}
		
		return shop;
	}
	
	@RequestMapping("/test111.do")
	@ResponseBody
	public ShopsResult test1(Integer[] list, String[] name) {
		ShopsResult shop = new ShopsResult();
		for(int i=0;i<name.length;i++){
			System.out.println(name[i]);
		}
		System.out.println(name);
		shop.setData(name);
		return shop;
	}
	
	@RequestMapping("/test112.do")
	@ResponseBody
	public ShopsResult test2(String name,HttpServletRequest request) throws UnsupportedEncodingException{
		ShopsResult shop = new ShopsResult();
		for (int i = 0; i < 20; i++) {
			// System.out.println(list[i]);
		}
		name=new String(name.getBytes("iso-8859-1"),"utf-8");
		shop.setData(name+"涓枃涔辩爜闂寰椾笉鍒拌В鍐筹紒");
		return shop;
	}
	
	@RequestMapping("/test113.do")
	@ResponseBody
	public ShopsResult test3(HttpServletRequest request){
		ShopsResult shop = new ShopsResult();
		Object remarks=request.getParameter("remarks");
		System.out.println(remarks);
		shop.setData(remarks);
		return shop;
	}
	
	@ResponseBody
	@RequestMapping("/test114.do")
	public ShopsResult test4(@RequestBody Remarks[] remarks){
		ShopsResult shop = new ShopsResult();
		shop.setData(remarks);
		return shop;
	}
	
	@RequestMapping("/test115.do")
	@ResponseBody
	public ShopsResult test5(HttpServletRequest request){
		ShopsResult shop = new ShopsResult();
		Map<String, String[]> app=request.getParameterMap();
		System.out.println(app.keySet());
		Set<String> set=app.keySet();
		for(String name:set){
			System.out.println(app.get(name));
			System.out.println("remarks[]".equals(name));
			System.out.println(app.get("remarks[]").length);
		}
		String[] remarks=request.getParameterValues("remarks[]");
		JSONArray jsonArray=JSONArray.fromObject(remarks);
		JSONObject jsonObject=JSONObject.fromObject(jsonArray.get(1));
		shop.setData(jsonObject);
		shop.setMsg("测试成功！");
		return shop;
	}
	
	@ResponseBody
	@RequestMapping("/test116.do")
	public ShopsResult test6(HttpServletRequest request){
		ShopsResult shop = new ShopsResult();
		System.out.println(request.getParameterMap());
//		String[] remarks=request.getParameterValues("remarks[]");
		String remarks=request.getParameter("remarks");
		System.out.println(remarks==null);
		if(remarks!=null){
			JSONArray jsonArray=JSONArray.fromObject(remarks);
			JSONObject object=JSONObject.fromObject(jsonArray.get(0));
			JSONObject object1=JSONObject.fromObject(jsonArray.get(1));
			shop.setData(object);
			System.out.println(object);
			System.out.println(object.get("supplier_unique"));
			System.out.println(object.get("remark"));
			shop.setData(object1);
		}
		System.out.println(remarks);
//		shop.setData(remarks);
		remarks="1476781086919:]|;1482451115327:]|;1476781114605:]|;1480313408806:]|";
		return shop;
	}
	
	
	@ResponseBody
	@RequestMapping("/test117.do")
	public ShopsResult test7(HttpServletRequest request,String[] remarks){
		ShopsResult shop = new ShopsResult();
		System.out.println(request.getParameterMap());
		String[] remark=request.getParameterValues("remarks[]");
		System.out.println(remark);
		shop.setData(remark);
		shop.setData(remarks);
		return shop;
	}
	
	@RequestMapping("/testPush.do")
	@ResponseBody
	public ShopsResult testPush(String shopUnique){
		ShopsResult sr=new ShopsResult();
		GoEasy goeasy=new GoEasy("BC-91e94f22780e4ed697c2f0837f1de9c5",null);
		goeasy.publish("*************", "ceshi");
		sr.setStatus(1);
		sr.setMsg("chaxunchengg!");
		return sr;
	}
	
	
	@RequestMapping("/queryGoodsSupplierMsg.do")
	@ResponseBody
	public ShopsResult queryGoodsSupplierMsg(){
		return testService.queryGoodsSupplierMsg();
	}
	
	@RequestMapping("/testUrl.do")
	@ResponseBody
	public ShopsResult testUrl(){
		ShopsResult sr=new ShopsResult(1, "成功！");
		String par="staffAccount=***********&staffPwd=135246";
		String url="https://buyhoo.cc/shopUpdate/shopsStaff/staffLoginByAccountPwd.do";
		String res=HttpGetUtil.sendGet(url, par);
		sr.setData(res);
		return sr;
	}
}
