package org.haier.shop.controller;

import java.util.Map;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;

import org.apache.shiro.SecurityUtils;
import org.apache.shiro.session.Session;
import org.apache.shiro.subject.Subject;
import org.haier.log.annotation.RemoteLog;
import org.haier.log.enums.BusinessType;
import org.haier.meituan.util.MUtil;
import org.haier.shop.entity.Staff;
import org.haier.shop.service.GoodsShelfStateService;
import org.haier.shop.service.ShopService;
import org.haier.shop.service.SysAgreementService;
import org.haier.shop.util.HandleMessyCode;
import org.haier.shop.util.PurResult;
import org.haier.shop.util.ServletsUtil;
import org.haier.shop.util.ShopsResult;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

@Controller
@RequestMapping("/shelfState")
public class GoodsShelfStateController {

	@Resource
	private GoodsShelfStateService goodsShelfStateService;
	
	@Resource
	private ShopService shopService;
	
	@Resource
	private SysAgreementService sysAgreementService;
	
	//跳转商品上下架管理页面
	@RequestMapping("/shelfStatePage.do")
	public String shelfStatePage(HttpServletRequest request){
		return "/WEB-INF/goods/shelfState.jsp";
	}
	
	//跳转微信小程序商品上下架管理页面
	@RequestMapping("/wechatShelfStatePage.do")
	public String wechatShelfStatePage(Model model){
		Subject subject = SecurityUtils.getSubject();
		Session session = subject.getSession();
		Staff staff = (Staff) session.getAttribute("staff");
		String shop_unique = String.valueOf(staff.getShop_unique());
		
		//微信小程序开通状态：0:不显示 1:显示 2:审核中 3:审核失败
		Integer show_buy_status = 0;
		String show_buy_fail_reason = "";
		//获取商家信息
		ShopsResult result = shopService.queryShopMessage(shop_unique);
		if(result.getStatus() == 0){
			Map<String,Object> shopInfo = (Map<String, Object>) result.getData();
			show_buy_status = Integer.parseInt(MUtil.strObject(shopInfo.get("show_buy_status")));
			show_buy_fail_reason = MUtil.strObject(shopInfo.get("show_buy_fail_reason"));
		}
		if(show_buy_status == 0){
			//获取微信小程序开通协议
			Map<String ,Object> agreement = sysAgreementService.querySysAgreement("wechat");
			model.addAttribute("agreement", agreement);
			
			return "/WEB-INF/wechatApplet/protocol.jsp";
		}else if(show_buy_status == 1) {
			return "/WEB-INF/wechatApplet/shelfState.jsp";
		}else{
			model.addAttribute("show_buy_status", show_buy_status);
			model.addAttribute("show_buy_fail_reason", show_buy_fail_reason);
			return "/WEB-INF/wechatApplet/showBuyStatus.jsp";
		}
	}
	
	/**
	 * 商品信息查询
	 * @param shop_unique 店铺编号
	 * @param goods_message 输入的商品信息
	 * @param goods_kind_unique 商品分类编号
	 * @param shelf_state 上架状态：1、已上架；2、已下架
	 * @param time 时长
	 */
	@RequestMapping("/queryShelfStateGoodsMessage.do")
	@ResponseBody
	public PurResult queryShelfStateGoodsMessage(HttpServletRequest request,
			@RequestParam(value="page",defaultValue="1")Integer page,
			@RequestParam(value="limit",defaultValue="15")Integer limit){

		PurResult result = new PurResult();
		try {
			Map<String, Object> params = HandleMessyCode.handle(ServletsUtil.getParameters(request));
			params.put("limit", limit);
			params.put("page", (page-1)*limit);
			result=goodsShelfStateService.queryShelfStateGoodsMessage(params);
		} catch (Exception e) {
			result.setStatus(0);
			result.setMsg("查询失败!");
			e.printStackTrace();
		}		
		return result;
	}
	

	@RemoteLog(title = "修改搜索商品的上下架状态", businessType = BusinessType.UPDATE)
	@RequestMapping("/downQueryShelfStateGoodsMessage.do")
	@ResponseBody
	public ShopsResult downQueryShelfStateGoodsMessage(
			HttpServletRequest request) {
		ShopsResult result = new ShopsResult();
		try {
			Map<String, Object> params = HandleMessyCode.handle(ServletsUtil.getParameters(request));
			result=goodsShelfStateService.downQueryShelfStateGoodsMessage(params);
		} catch (Exception e) {
			result.setStatus(0);
			result.setMsg("下架失败!");
			e.printStackTrace();
		}
		return result;
	}
	/**
	 * 修改全部上下架状态
	 * @param shop_unique 店铺编号
	 * @param state 上架状态：1、已上架；2、已下架
	 * @param type 修改类型：1、线上  2线下收银
	 * @return
	 */
	@RemoteLog(title = "修改全部上下架状态", businessType = BusinessType.UPDATE)
	@RequestMapping("updateAllShelfState.do")
	@ResponseBody
	public ShopsResult updateAllShelfState(
			@RequestParam(value="shop_unique",required=true)String shop_unique,
			@RequestParam(value="state",required=true)String state,
			@RequestParam(value="type",required=true)String type){
		return goodsShelfStateService.updateAllShelfState(shop_unique, state,type);
	}
	
	/**
	 * 修改商品上下架状态
	 * @param goods_ids 商品id集合，已逗号隔开
	 * @param shelf_state 线上上架状态：1、已上架；2、已下架
	 * @return
	 */
	@RemoteLog(title="修改商品上下架状态", businessType = BusinessType.UPDATE)
	@RequestMapping("updateShelfState.do")
	@ResponseBody
	public ShopsResult updateShelfState(
			@RequestParam(value="staff_id",required=true)String staff_id,
			@RequestParam(value="goods_ids",required=true)String goods_ids,
			@RequestParam(value="shelf_state",required=true)String shelf_state, HttpServletRequest request){
		return goodsShelfStateService.updateShelfState(goods_ids, shelf_state, staff_id, request);
	}
	
	/**
	 * 修改商品pc上下架状态
	 * @param goods_ids 商品id集合，已逗号隔开
	 * @param pc_shelf_state pc收银上架状态：1、已上架；2、已下架
	 * @return
	 */
	@RemoteLog(title="修改商品pc上下架状态", businessType = BusinessType.UPDATE)
	@RequestMapping("updatePcShelfState.do")
	@ResponseBody
	public ShopsResult updatePcShelfState(
			@RequestParam(value = "staff_id") String staff_id,
			@RequestParam(value="goods_ids",required=true)String goods_ids,
			@RequestParam(value="pc_shelf_state",required=true)String pc_shelf_state, HttpServletRequest request){
		return goodsShelfStateService.updatePcShelfState(goods_ids, pc_shelf_state, staff_id, request);
	}
	
	/**
	 * 修改商品售价
	 * @param goods_id 商品id
	 * @param goods_sale_price 售价
	 * @param goods_web_sale_price 网购价
	 * @return
	 */
	@RemoteLog(title="修改商品售价", businessType = BusinessType.UPDATE)
	@RequestMapping("updateGoodsSalePrice.do")
	@ResponseBody
	public ShopsResult updateGoodsSalePrice(
			@RequestParam(value="goods_id",required=true)String goods_id,String goods_sale_price,String goods_web_sale_price, String staff_id, HttpServletRequest request){
		return goodsShelfStateService.updateGoodsSalePrice(goods_id, goods_sale_price,goods_web_sale_price, staff_id, request);
	}
}
