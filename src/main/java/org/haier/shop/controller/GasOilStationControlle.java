package org.haier.shop.controller;

import java.io.IOException;
import java.io.OutputStream;
import java.math.BigDecimal;
import java.net.URLEncoder;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.haier.meituan.util.MUtil;
import org.haier.shop.service.GasOilService;
import org.haier.shop.util.LoadOutObjectXLSUtil;
import org.haier.shop.util.PurResult;
import org.haier.shop.util.ServletsUtil;
import org.haier.shop.util.ShopsResult;
import org.haier.shop.util.StringUtil;
import org.haier.shop.util.XLSCallBack;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import com.sun.org.apache.bcel.internal.generic.NEW;

/**
 * 加油站
 * <AUTHOR>
 *
 */
@Controller
@RequestMapping("/gasOil")
public class GasOilStationControlle {
	
	@Autowired
	private GasOilService gasOilService;

	/**
	 * 查询加油数据统计
	 * @return
	 */
	@RequestMapping("/queryGasList.do")
	public String queryGasList(HttpServletRequest request) {
		return "/WEB-INF/gasStation/queryGasList.jsp";
	}
	
	@RequestMapping("/queryOilSaleDetailPage.do")
	public String queryOilSaleDetailPage(String sale_list_unique, String shop_unique,HttpServletRequest request){
		request.setAttribute("sale_list_unique", sale_list_unique);
		request.setAttribute("shop_unique", shop_unique);
		return "/WEB-INF/gasStation/gasOrderDetail.jsp";
	}
	
	/**
	 * 查询加油充值数据统计
	 * @return
	 */
	@RequestMapping("/queryGasRechList.do")
	public String queryGasRechList(HttpServletRequest request) {
		return "/WEB-INF/gasStation/queryGasRechargeList.jsp";
	}
	
	/**
	 * 查询加油充值数据详情
	 * @return
	 */
	@RequestMapping("/queryGasRechargeDetailPage.do")
	public String queryGasRechargeDetail(String sale_list_unique,HttpServletRequest request) {
		request.setAttribute("sale_list_unique", sale_list_unique);
		return "/WEB-INF/gasStation/gasRechargeDetail.jsp";
	}
	/**
	 * 查询站点列表
	 * @return
	 */
	@RequestMapping("/queryManagerOils.do")
	@ResponseBody
	public PurResult queryManagerOils(HttpServletRequest request) {
		PurResult result = new PurResult();
		Map<String, Object> params = ServletsUtil.getParameters(request);
		System.out.println(params);
		result = gasOilService.queryManagerOils(params);
		return result;
	}
	
	/**
	 * 查询油枪油品商品列表
	 * @return
	 */
	@RequestMapping("/queryManagerOilgun.do")
	@ResponseBody
	public PurResult queryManagerOilgun(HttpServletRequest request) {
		PurResult result = new PurResult();
		Map<String, Object> params = ServletsUtil.getParameters(request);
		result = gasOilService.queryManagerOilgun(params);
		return result;
	}
	
	
	/**
	 * 查询加油站订单列表
	 * @return
	 */
	@RequestMapping("/queryOilSaleList.do")
	@ResponseBody
	public PurResult queryOilSaleList(HttpServletRequest request) {
		PurResult result = new PurResult();
		Map<String, Object> params = ServletsUtil.getParameters(request);
		System.out.println(params);
		result = gasOilService.queryOilSaleList(params);
		return result;
	}
	
	
	/**
	 * 查询订单详情
	 * @param shop_unique
	 * @param sale_list_unique
	 * @return
	 */
	@RequestMapping("/queryGasOrderDetail.do")
	@ResponseBody
	public PurResult queryGasOrderDetail(
			@RequestParam(value="sale_list_unique")String sale_list_unique,
			@RequestParam(value="shop_unique")String shop_unique){
		return gasOilService.queryGasOrderDetail(sale_list_unique, shop_unique);
	}
	
	
	/**
	 * 订单详情EXCEL表生成并返回下载地址
	 * @param map
	 * @return
	 */
	@ResponseBody
	@RequestMapping("/saleListExcel.do")
	public void saleListExcel(HttpServletRequest request,HttpServletResponse response){
		Map<String, Object> params = ServletsUtil.getParameters(request);
		
		
		List<Map<String, Object>> data =gasOilService.saleListExcel(params);
		//查询统计信息
		Double saleTotal = 0.00;
		Double received = 0.00;
		for(Map<String, Object> map : data) {
			Double sale_total = Double.valueOf(map.get("sale_list_total").toString());
			saleTotal = saleTotal + sale_total;
			Double sale_received = Double.valueOf(map.get("sale_list_actually_received").toString());
			received = received + sale_received;
		}
		Map<String, Object> newCountMap=new HashMap<String, Object>();
		if(null==data||data.isEmpty()){
			newCountMap.put("sale_list_unique", "订单总数量:"+0);
			newCountMap.put("time", "订单总金额:"+0);
			newCountMap.put("cus_name", "订单总原价:"+0);
		}else{
			newCountMap.put("sale_list_unique", "订单总数量:"+data.size());
			newCountMap.put("sale_list_actually_received", "订单总金额:"+saleTotal);
			newCountMap.put("cus_name", "订单总原价:"+received);
		}
		data.add(newCountMap);
		loadOutClaimXLS(data,"加油订单记录Excel",request,response);
	}
	
	public void loadOutClaimXLS(List<Map<String, Object>> caseVoList, final String string,HttpServletRequest request,HttpServletResponse response) {
		try {
			response.setContentType(
					"application/x-msdownload; charset=UTF-8");// 告知浏览器文件类型
			String agent = request.getHeader("user-agent");
			if (agent.contains("Firefox")) {
				response.setHeader(
						"Content-Disposition",
						"attachment;filename="
								+ new String((string + ".xls")
										.getBytes("GB2312"), "ISO-8859-1"));// 设置文件头
			} else {
				response.setHeader(
						"Content-Disposition",
						"attachment;filename="
								+ URLEncoder.encode(string + ".xls", "UTF-8"));// 设置文件头
			}
			OutputStream os = response.getOutputStream();
			LoadOutObjectXLSUtil<Map<String, Object>> objectXLS = new LoadOutObjectXLSUtil<Map<String, Object>>();
			objectXLS.generateXLS(os, caseVoList, new XLSCallBack<Map<String, Object>>() {
				public Object[] getValues(Map<String, Object> tt) {
					String str = "";
					
					String sale_list_unique ="";
					sale_list_unique=String.valueOf( tt.get("sale_list_unique"));
					String sale_list_datetime = (String) tt.get("sale_list_datetime");
					if (StringUtil.blank(sale_list_datetime)) {
						sale_list_datetime = str;
					}
					String shop_name = (String) tt.get("shop_name");
					if (StringUtil.blank(shop_name)) {
						shop_name = str;
					}
					String goods_name = (String) tt.get("goods_name");
					if (StringUtil.blank(goods_name)) {
						goods_name = str;
					}
					String sale_list_actually_received = String.valueOf(tt.get("sale_list_actually_received"));
					if (StringUtil.blank(sale_list_actually_received)) {
						sale_list_actually_received = str;
					}
					String point_deduction = String.valueOf(tt.get("point_deduction"));
					if (StringUtil.blank(point_deduction)) {
						point_deduction = str;
					}
					String coupon_amount = String.valueOf(tt.get("coupon_amount"));
					if (StringUtil.blank(coupon_amount)) {
						coupon_amount = str;
					}
					String is_time = String.valueOf(tt.get("is_time"));
					if (StringUtil.blank(is_time)) {
						is_time = str;
					}
					String cus=String.valueOf(tt.get("cus"));
					if (StringUtil.blank(cus)) {
						cus = str;
					}
					
					return new String[] {sale_list_unique,sale_list_datetime,shop_name,goods_name,
							sale_list_actually_received,point_deduction,coupon_amount,is_time,cus};
				}

				public String getTitle() {
					return string;
				}

				public int[] getColumnsWidth() {
					return new int[] { 
										  80 * 80, 80 * 80, 80 * 80, 80 * 80,
										  80 * 80, 80 * 80, 80 * 80, 80 * 80,
										  80 * 80};
				}

				public String[] getColumnsName() {
					return new String[] { "订单编号","下单日期","站点名称","油枪油号",
										  "实收金额","积分抵扣","优惠券","限时折扣",
										  "会员折扣"};
				}

				public int[][] MergedRegion() {
					// TODO Auto-generated method stub
					return null;
				}

				public List<Object[]> getheaderValue() {
					// TODO Auto-generated method stub
					return null;
				}

			});
			os.flush();
		} catch (IOException e) {
			e.printStackTrace();
		}
	}
	
	
	
	/**
	 * 查询加油站充值列表
	 * @return
	 */
	@RequestMapping("/queryGasRechargeList.do")
	@ResponseBody
	public PurResult queryGasRechargeList(HttpServletRequest request) {
		PurResult result = new PurResult();
		Map<String, Object> params = ServletsUtil.getParameters(request);
		System.out.println(params);
		result = gasOilService.queryGasRechargeList(params);
		return result;
	}
	
	
	/**
	 * 查询充值详情
	 * @param sale_list_unique
	 * @return
	 */
	@RequestMapping("/queryGasRechargeDetail.do")
	@ResponseBody
	public PurResult queryGasRechargeDetail(
			@RequestParam(value="sale_list_unique")String sale_list_unique){
		return gasOilService.queryGasRechargeDetail(sale_list_unique);
	}
	
	
	/**
	 * 订单详情EXCEL表生成并返回下载地址
	 * @param map
	 * @return
	 */
	@ResponseBody
	@RequestMapping("/rechargeListExcel.do")
	public void rechargeListExcel(HttpServletRequest request,HttpServletResponse response){
		Map<String, Object> params = ServletsUtil.getParameters(request);
		
		
		List<Map<String, Object>> data =gasOilService.rechargeListExcel(params);
		//查询统计信息
		Double saleTotal = 0.00;
		for(Map<String, Object> map : data) {
			Double sale_total = Double.valueOf(map.get("recharge_money").toString());
			saleTotal = saleTotal + sale_total;
		}
		Map<String, Object> newCountMap=new HashMap<String, Object>();
		if(null==data||data.isEmpty()){
			newCountMap.put("sale_list_unique", "充值总数量:"+0);
			newCountMap.put("time", "充值总金额:"+0);
		}else{
			newCountMap.put("sale_list_unique", "充值总数量:"+data.size());
			newCountMap.put("recharge_money", "充值总金额:"+saleTotal);
		}
		data.add(newCountMap);
		loadOutClaimXLSRe(data,"加油充值记录Excel",request,response);
	}
	
	public void loadOutClaimXLSRe(List<Map<String, Object>> caseVoList, final String string,HttpServletRequest request,HttpServletResponse response) {
		try {
			response.setContentType(
					"application/x-msdownload; charset=UTF-8");// 告知浏览器文件类型
			String agent = request.getHeader("user-agent");
			if (agent.contains("Firefox")) {
				response.setHeader(
						"Content-Disposition",
						"attachment;filename="
								+ new String((string + ".xls")
										.getBytes("GB2312"), "ISO-8859-1"));// 设置文件头
			} else {
				response.setHeader(
						"Content-Disposition",
						"attachment;filename="
								+ URLEncoder.encode(string + ".xls", "UTF-8"));// 设置文件头
			}
			OutputStream os = response.getOutputStream();
			LoadOutObjectXLSUtil<Map<String, Object>> objectXLS = new LoadOutObjectXLSUtil<Map<String, Object>>();
			objectXLS.generateXLS(os, caseVoList, new XLSCallBack<Map<String, Object>>() {
				public Object[] getValues(Map<String, Object> tt) {
					String str = "";
					
					String sale_list_unique ="";
					sale_list_unique=String.valueOf( tt.get("sale_list_unique"));
					String sale_list_datetime = (String) tt.get("sale_list_datetime");
					if (StringUtil.blank(sale_list_datetime)) {
						sale_list_datetime = str;
					}
					String shop_name = (String) tt.get("shop_name");
					if (StringUtil.blank(shop_name)) {
						shop_name = str;
					}
					String goods_name = (String) tt.get("goods_name");
					if (StringUtil.blank(goods_name)) {
						goods_name = str;
					}
					String sale_list_actually_received = String.valueOf(tt.get("sale_list_actually_received"));
					if (StringUtil.blank(sale_list_actually_received)) {
						sale_list_actually_received = str;
					}
					String point_deduction = String.valueOf(tt.get("point_deduction"));
					if (StringUtil.blank(point_deduction)) {
						point_deduction = str;
					}
					String coupon_amount = String.valueOf(tt.get("coupon_amount"));
					if (StringUtil.blank(coupon_amount)) {
						coupon_amount = str;
					}
					String is_time = String.valueOf(tt.get("is_time"));
					if (StringUtil.blank(is_time)) {
						is_time = str;
					}
					String cus=String.valueOf(tt.get("cus"));
					if (StringUtil.blank(cus)) {
						cus = str;
					}
					
					return new String[] {sale_list_unique,sale_list_datetime,shop_name,goods_name,
							sale_list_actually_received,point_deduction,coupon_amount,is_time,cus};
				}

				public String getTitle() {
					return string;
				}

				public int[] getColumnsWidth() {
					return new int[] { 
										  80 * 80, 80 * 80, 80 * 80, 80 * 80,
										  80 * 80, 80 * 80, 80 * 80, 80 * 80,
										  80 * 80};
				}

				public String[] getColumnsName() {
					return new String[] { "订单编号","下单日期","站点名称","油枪油号",
										  "实收金额","积分抵扣","优惠券","限时折扣",
										  "会员折扣"};
				}

				public int[][] MergedRegion() {
					// TODO Auto-generated method stub
					return null;
				}

				public List<Object[]> getheaderValue() {
					// TODO Auto-generated method stub
					return null;
				}

			});
			os.flush();
		} catch (IOException e) {
			e.printStackTrace();
		}
	}
	
	
	@RequestMapping("/queryCouponName.do")
	@ResponseBody
	public PurResult queryCouponName(HttpServletRequest request) {
		PurResult result = new PurResult();
		Map<String, Object> params = ServletsUtil.getParameters(request);
		System.out.println(params);
		result = gasOilService.queryCouponName(params);
		return result;
	}
	@RequestMapping("/queryCouponCount.do")
	@ResponseBody
	public PurResult queryCouponCount(HttpServletRequest request) {
		PurResult result = new PurResult();
		Map<String, Object> params = ServletsUtil.getParameters(request);
		System.out.println(params);
		result = gasOilService.queryCouponCount(params);
		return result;
	}
	@RequestMapping("/queryGoodsName.do")
	@ResponseBody
	public PurResult queryGoodsName(HttpServletRequest request) {
		PurResult result = new PurResult();
		Map<String, Object> params = ServletsUtil.getParameters(request);
		System.out.println(params);
		result = gasOilService.queryGoodsName(params);
		return result;
	}
	@RequestMapping("/queryGoodsCount.do")
	@ResponseBody
	public PurResult queryGoodsCount(HttpServletRequest request) {
		PurResult result = new PurResult();
		Map<String, Object> params = ServletsUtil.getParameters(request);
		System.out.println(params);
		result = gasOilService.queryGoodsCount(params);
		return result;
	}
	@RequestMapping("/queryAddBalance.do")
	@ResponseBody
	public PurResult queryAddBalance(HttpServletRequest request) {
		PurResult result = new PurResult();
		Map<String, Object> params = ServletsUtil.getParameters(request);
		System.out.println(params);
		result = gasOilService.queryAddBalance(params);
		return result;
	}
	@RequestMapping("/queryCusLevelName.do")
	@ResponseBody
	public PurResult queryCusLevelName(HttpServletRequest request) {
		PurResult result = new PurResult();
		Map<String, Object> params = ServletsUtil.getParameters(request);
		System.out.println(params);
		result = gasOilService.queryCusLevelName(params);
		return result;
	}
}
