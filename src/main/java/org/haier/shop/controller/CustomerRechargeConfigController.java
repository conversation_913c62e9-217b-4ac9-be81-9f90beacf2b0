package org.haier.shop.controller;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;

import org.apache.shiro.SecurityUtils;
import org.apache.shiro.session.Session;
import org.apache.shiro.subject.Subject;
import org.haier.log.annotation.RemoteLog;
import org.haier.log.enums.BusinessType;
import org.haier.shop.entity.Staff;
import org.haier.shop.service.CustomerCheckOutService;
import org.haier.shop.service.CustomerRechargeConfigService;
import org.haier.shop.util.HandleMessyCode;
import org.haier.shop.util.PurResult;
import org.haier.shop.util.ServletsUtil;
import org.haier.shop.util.ShopsResult;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

@Controller
@RequestMapping("/config")
public class CustomerRechargeConfigController {
	@Resource
	private CustomerRechargeConfigService service;
	
	@Resource
	private CustomerCheckOutService cusService;

	@RequestMapping("/addCusOffRechargeConfig.do")
	@ResponseBody
	public ShopsResult addCusOffRechargeConfig(String rechargeName,Double money,String startTime,String endTime,String shopUnique,
			Integer isCoupon,String couponList,Integer isPoint,Double addPoint,Integer isGoods,String goodsList,Integer isBalance,Double addBalance,
			String isCusLevel,Integer cusLevelId
			) {
		return service.addCusOffRechargeConfig(rechargeName, money, startTime, endTime, shopUnique, isCoupon, couponList, 
				isPoint, addPoint, isGoods, goodsList, isBalance, addBalance, isCusLevel, cusLevelId);
	}
	
	@RequestMapping("/queryRechargeConfigList.do")
	@ResponseBody
	public PurResult queryRechargeConfigList(HttpServletRequest request,
			@RequestParam(value="page",defaultValue="1")Integer page,
			@RequestParam(value="limit",defaultValue="15")Integer limit){
		PurResult result = new PurResult();
		try {
			Map<String, Object> params = HandleMessyCode.handle(ServletsUtil.getParameters(request));
			params.put("limit", limit);
			params.put("page", (page-1)*limit);
			result=service.queryRechargeConfigList(params);
		} catch (Exception e) {
			result.setStatus(0);
			result.setMsg("查询失败!");
			e.printStackTrace();
		}		
		return result;
	}
	
	/**
	 * 新增配置
	 */
	@RemoteLog(title = "新增充值配置", businessType = BusinessType.INSERT)
	@RequestMapping("/addCustomerRechargeConfig.do")
	@ResponseBody
	public PurResult addCustomerRechargeConfig(HttpServletRequest request){
		PurResult result = new PurResult();
		try {
			Map<String, Object> params = HandleMessyCode.handle(ServletsUtil.getParameters(request));
			result=service.InsertRechargeConfig(params);
		} catch (Exception e) {
			result.setStatus(0);
			result.setMsg("操作失败!");
			e.printStackTrace();
		}		
		return result;
	}
	
	/**
	 * 更新配置
	 */
	@RemoteLog(title = "更新充值配置", businessType = BusinessType.UPDATE)
	@RequestMapping("/updateCustomerRechargeConfig.do")
	@ResponseBody
	public PurResult updateCustomerRechargeConfig(HttpServletRequest request){
		PurResult result = new PurResult();
		try {
			Map<String, Object> params = HandleMessyCode.handle(ServletsUtil.getParameters(request));
			result=service.updateRechargeConfig(params);
		} catch (Exception e) {
			result.setStatus(0);
			result.setMsg("操作失败!");
			e.printStackTrace();
		}		
		return result;
	}
	
	//跳转到充值配置页面
	@RequestMapping("/toCustomerRechargeConfig.do")
	public String toCustomerRechargeConfig(){
		
		Subject subject = SecurityUtils.getSubject();
		Session session = subject.getSession();
		Staff staff = (Staff) session.getAttribute("staff");
		
		Integer shop_type = staff.getShop_type();
		
		if(shop_type == 11) {
			return "/WEB-INF/manager/cusRechargeOilConfigList.jsp";
		}
		
		return "/WEB-INF/manager/customerRechargeConfigList.jsp";
		
		
	}
	
	//跳转到新增充值配置页面
	@RequestMapping("/addRechargeConfig.do")
	public String addRechargeConfig(HttpServletRequest request){
		
		Subject subject = SecurityUtils.getSubject();
		Session session = subject.getSession();
		Staff staff = (Staff) session.getAttribute("staff");
		
		Integer shop_type = staff.getShop_type();
		
		if(shop_type == 11) {
			//获取本店会员等级列表
			Map<String,Object> map = new HashMap<String,Object>();
			map.put("shopUnique", staff.getShop_unique());
			List<Map<String, Object>> sr = cusService.queryCusLevelList(map);
			request.setAttribute("list", sr);
			
			return "/WEB-INF/manager/addOilRecharge.jsp";
		}
		
		return "/WEB-INF/manager/addRecharge.jsp";
	}
	
	//跳转到编辑充值配置页面
	@RequestMapping("/editRechargeConfig.do")
	public String editRechargeConfig(){
		return "/WEB-INF/manager/updateRecharge.jsp";
	}
	
	//跳转到编辑充值配置页面
	@RequestMapping("/addOilRechargeDetail.do")
	public String addOilRechargeDetail(Long recharge_config_id,HttpServletRequest request){
		request.setAttribute("recharge_config_id", recharge_config_id);
		return "/WEB-INF/manager/addOilRechargeDetail.jsp";
	}
}
