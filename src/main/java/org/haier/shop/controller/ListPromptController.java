package org.haier.shop.controller;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;

import org.apache.log4j.Logger;
import org.haier.shop.entity.ListPrompt;
import org.haier.shop.service.ListPromptService;
import org.haier.shop.util.ShopsResult;
import org.haier.shop.util.ShopsUtil;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;


@RequestMapping("/listPrompt")
@Controller
public class ListPromptController {
	@Resource
	private ListPromptService lpService;
	/**
	 * 日志文件
	 */
	private static Logger log=Logger.getLogger(ListPromptController.class);
	
	/**
	 * 查询有效的订单数量提示信息
	 * @return
	 */
	@RequestMapping("/getValidPromptRule.do")
	@ResponseBody
	public ShopsResult getValidPromptRule(
			HttpServletRequest request
			){
		ShopsUtil.recordLog(log, request);
		return lpService.getValidPromptRule();
	}

	
	/**
	 * 将已有规则设置为无效状态
	 * @return
	 */
	@RequestMapping("/deleteListPrompt.do")
	@ResponseBody
	public ShopsResult deleteListPrompt(
			HttpServletRequest request,
			ListPrompt listPrompt
			){
		ShopsUtil.recordLog(log, request);
		return lpService.deleteListPrompt(listPrompt);
	}
	
	//跳转添加页面
	@RequestMapping("/addPrompt.do")
	public String addPrompt(){
		return "/WEB-INF/manager/addPrompt.jsp";
	}
	
	//跳转更新页面
	@RequestMapping("/editPrompt.do")
	public String editPrompt(String id,String listCount,String promptInfo,Model model){
		model.addAttribute("id", id);
		model.addAttribute("listCount", listCount);
		model.addAttribute("promptInfo", promptInfo);
		return "/WEB-INF/manager/editPrompt.jsp";
	}
	
	/**
	 * 添加或更新规则信息
	 * @param listPrompt
	 * @return
	 */
	@RequestMapping("/modifyListPrompt.do")
	@ResponseBody
	public ShopsResult modifyListPrompt(ListPrompt listPrompt){
		return lpService.modifyListPrompt(listPrompt);
	}
	
	@RequestMapping("/clearListPrompt.do")
	@ResponseBody
	public ShopsResult clearListPrompt(){
		return lpService.clearListPrompt();
	}
}
