package org.haier.shop.controller;
/**
* @author: 作者:王恩龙
* @version: 2023年6月14日 下午4:30:59
*
*/

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;

import org.haier.shop.service.ProjectMsgService;
import org.haier.shop.util.ShopsResult;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

@RequestMapping("/project")
@Controller
public class ProjectMsgController {
	@Resource
	private ProjectMsgService projectMsgService;
	
	@RequestMapping("/toAddNewProject.do")
	public String toAddNewProject() {
		return "/WEB-INF/sys/update/toAddNewProject.jsp";
	}
	
	@RequestMapping("/projectMsgList.do")
	public String projectMsgList() {
		return "/WEB-INF/sys/update/ProjectMsgList.jsp";
	}
	/**
	 * 添加新的项目名称
	 * @param project_name
	 * @return
	 */
	@RequestMapping("/addNewProjectMsg.do")
	@ResponseBody
	public ShopsResult addNewProjectMsg(String project_name,String project_type,String remarks) {
		return projectMsgService.addNewProjectMsg(project_name, project_type, remarks);
	}
	/**
	 * 查询指定页码的项目信息
	 * 
	 * @param page
	 * @param limit
	 * @return
	 */
	@RequestMapping("/queryProjectMsgList.do")
	@ResponseBody
	public ShopsResult queryProjectMsgList(Integer page, Integer limit,Integer project_type) {
		return projectMsgService.queryProjectMsgList(page, limit, project_type);
	}

	@RequestMapping("/pcUpdate.do")
	public String pcUpdate(HttpServletRequest request) {
		return "/WEB-INF/shop/pcUpdate2.jsp";
	}
	@RequestMapping("/toPcVersion.do")
	public String toPcVersion(HttpServletRequest request, String ids, Model model) {
		model.addAttribute("ids", ids);
		return "/WEB-INF/sys/update/pcVersionList.jsp";
	}

	/**
	 * 店铺设备版本管理
	 * @param page
	 * @param limit
	 * @return
	 */
	@RequestMapping("/queryShopsDeviceVeriosn.do")
	@ResponseBody
	public ShopsResult queryShopsDeviceVeriosn(String shopsMessage, Integer page,Integer limit,Integer handleStatus) {
		return projectMsgService.queryShopsDeviceVeriosn(page, limit, shopsMessage,handleStatus);
	}

	/**
	 * 版本更新
	 * @param ids
	 * @param version
	 * @return
	 */
	@RequestMapping("/updateShopsVersion.do")
	@ResponseBody
	public ShopsResult updateShopsVersion(String ids, String version ) {
		return projectMsgService.updateShopsVersion(ids, version);
	}
}
