package org.haier.shop.controller;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.haier.shop.service.TVService;
import org.haier.shop.util.HandleMessyCode;
import org.haier.shop.util.JPushClientUtil;
import org.haier.shop.util.PurResult;
import org.haier.shop.util.ServletsUtil;
import org.haier.util.eshow.AreaVO;
import org.haier.util.eshow.Downdefault;
import org.haier.util.eshow.TvModelVO;
//import org.haier.util.eshow.FtpUtil2;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;


import cn.jpush.api.push.PushResult;

@RequestMapping("/tv")
@Controller
public class TVController {
	
	private static final Logger logger = LoggerFactory.getLogger(TVController.class);
	
	@Resource
	private TVService tvService;

	@RequestMapping("/queryAreaPalyList.do")
	@ResponseBody
	public PurResult queryAreaPalyList(String province_code,String city_code,String county_code,String start_date,String end_date) {
		try {
			return tvService.queryAreaPalyList(province_code, city_code, county_code, start_date, end_date);
		}catch (Exception e) {
			e.printStackTrace();
			return new PurResult(1, "查询失败");
		}
	}

			
	@RequestMapping("/queryPalyList.do")
	@ResponseBody
	public PurResult queryPalyList(@RequestParam(value = "page", defaultValue = "0") int page,
			@RequestParam(value = "limit", defaultValue = "8") int pageSize,String play_status,String play_type) {
		try {
			Map<String, Object> params = new HashMap<String, Object>();
			params.put("limit", pageSize);
			params.put("page", (page-1)*pageSize);
    		params.put("play_status", play_status);
    		params.put("play_type", play_type);
			return tvService.queryPalyList(params);
		}catch (Exception e) {
			e.printStackTrace();
			return new PurResult(1, "查询失败");
		}
	}
	
	@RequestMapping("/downYinong.do")
	@ResponseBody
	public PurResult downYinong(String url,String title,String db_id,String id,
			String size,String md5,String type
			) {
		logger.info("开启文件下载");
		logger.info("url="+url+"&title="+title+"&db_id="+db_id+"&id="+id+"&size="+size+"&md5="+md5+"&type="+type);
		return tvService.downYinong(url, title,db_id,id,size,md5,type);
	}
	
	@RequestMapping(value = "/toPeopleList.do")
    public String toPeopleList(){
    	logger.info("平台后台管理-人员管理");
        return "/WEB-INF/tv/peopleList.jsp";
    }
	@RequestMapping(value = "/addAdPeoplePage.do")
	public String addAdPeoplePage(){
		logger.info("平台后台管理-添加人员");
		return "/WEB-INF/tv/addAdPeople.jsp";
	}
	@RequestMapping("/editAdPeoplePage.do")
	public String editAdPeoplePage(String id,HttpServletRequest request){
		request.setAttribute("id", id);
		return "/WEB-INF/tv/editAdPeople.jsp";
	}
	@RequestMapping(value = "/toTVList.do")
    public String toTVList(){
    	logger.info("平台后台管理-电视管理");
        return "/WEB-INF/tv/tvList.jsp";
    }
	@RequestMapping(value = "/toTVSourceList.do")
    public String toTVSourceList(){
    	logger.info("平台后台管理-电视素材管理");
        return "/WEB-INF/tv/tvSourceList.jsp";
    }
	@RequestMapping(value = "/queryYinongSource.do")
    public String queryYinongSource(){
    	logger.info("平台后台管理-查询益农当前素材");
        return "/WEB-INF/tv/yiNongSourceList.jsp";
    }
	@RequestMapping(value = "/queryYinongSubtitle.do")
    public String queryYinongSubtitle(){
    	logger.info("平台后台管理-查询益农跑马灯列表");
        return "/WEB-INF/tv/queryYinongSubtitleList.jsp";
    }
	@RequestMapping(value = "/queryYinongPlayList.do")
    public String queryYinongPlayList(){
    	logger.info("平台后台管理-查询益农当前播放单");
        return "/WEB-INF/tv/queryYinongPlayList.jsp";
    }
	@RequestMapping(value = "/toTVArea.do")
    public String toTVArea(){
    	logger.info("平台后台管理-电视区域管理");
        return "/WEB-INF/tv/area.jsp";
    }
	@RequestMapping(value = "/toTVModel.do")
    public String toTVModel(){
    	logger.info("平台后台管理-电视型号管理");
        return "/WEB-INF/tv/model.jsp";
    }
	@RequestMapping(value = "/queryPlayList.do")
    public String queryPlayList(){
    	logger.info("平台后台管理-电视播放单管理");
        return "/WEB-INF/tv/playList.jsp";
    }	
	@RequestMapping(value = "/area/add.do")
    public String addArea(){
    	logger.info("平台后台管理-新增区域");
        return "/WEB-INF/tv/addArea.jsp";
    }
	@RequestMapping(value = "/addAreaCodePage.do")
	public String addAreaCodePage(){
		logger.info("平台后台管理-新增区域鉴权码");
		return "/WEB-INF/tv/addAreaCode.jsp";
	}
	@RequestMapping(value = "/addTVSourcePage.do")
	public String addTVSourcePage(){
		logger.info("平台后台管理-新增素材页面");
		return "/WEB-INF/tv/addTVSource.jsp";
	}
	
	@RequestMapping(value = "/addAreaPlayPage.do")
	public String addAreaPlayPage(Model model){
		logger.info("平台后台管理-新增播放单页面");
		//查询所有市
		List<Map<String ,Object>> cityList= tvService.queryAllCityList();
		//查询所有区
		List<Map<String ,Object>> areaList= tvService.queryAllAreaList();
    	model.addAttribute("areaList", areaList);
    	model.addAttribute("cityList", cityList);
		return "/WEB-INF/tv/addAreaPlay.jsp";
	}
	@RequestMapping(value = "/toAreaPlayEdit.do")
	public String toAreaPlayEdit(Model model,String id){
		logger.info("平台后台管理-修改播放单页面");
		//查询所有市
		List<Map<String ,Object>> cityList= tvService.queryAllCityList();
		//查询所有区
		List<Map<String ,Object>> areaList= tvService.queryAllAreaList();
		model.addAttribute("areaList", areaList);
		model.addAttribute("cityList", cityList);
		//查询播放单
		tvService.queryPlayInfo(id,model);
		return "/WEB-INF/tv/editAreaPlay.jsp";
	}
	
	@RequestMapping("/playList.do")
	public String playList(Model model) {
		logger.info("区域播放列表");
		Downdefault data=tvService.queryAdEshow();
		model.addAttribute("startTiming", data.getStartTiming());
		model.addAttribute("timing", data.getTiming());
		return "/WEB-INF/tv/areaPayList.jsp";
	}
	
	@RequestMapping(value = "/toEshowPlayDetail.do")
	public String toEshowPlayDetail(Model model,String id,String county){

		String result=tvService.toEshowPlayDetail(id,county);
		model.addAttribute("playList", result);
		return "/WEB-INF/tv/eshowPlayList.jsp";
	}
	/**
	  * 查询人员列表
	 * @param request
	 * @param page
	 * @param pageSize
	 * @return
	 */
    @RequestMapping(value="/queryPeopleList.do")
	@ResponseBody
	public PurResult queryPeopleList(
			HttpServletRequest request,
			@RequestParam(value = "page", defaultValue = "0") int page,
			@RequestParam(value = "limit", defaultValue = "8") int pageSize){
    	Map<String, Object> params = HandleMessyCode.handle(ServletsUtil.getParameters(request));
		params.put("limit", pageSize);
		params.put("page", (page-1)*pageSize);
		
		PurResult result = new PurResult();
		try {
			result = tvService.queryPeopleList(params);
		} catch (Exception e) {
			logger.error("列表查询异常：",e);
			e.printStackTrace();
			result.setStatus(0);
			result.setMsg("异常");
		}
		return result;
	}
	/**
	  * 查询设备列表
	 * @param request
	 * @param page
	 * @param pageSize
	 * @return
	 */
   @RequestMapping(value="/queryTVList.do")
	@ResponseBody
	public PurResult queryTVList(
			HttpServletRequest request,
			@RequestParam(value = "page", defaultValue = "0") int page,
			@RequestParam(value = "limit", defaultValue = "8") int pageSize){
   	Map<String, Object> params = HandleMessyCode.handle(ServletsUtil.getParameters(request));
		params.put("limit", pageSize);
		params.put("page", (page-1)*pageSize);
		
		PurResult result = new PurResult();
		try {
			result = tvService.queryTVList(params);
		} catch (Exception e) {
			logger.error("列表查询异常：",e);
			e.printStackTrace();
			result.setStatus(0);
			result.setMsg("异常");
		}
		return result;
	} 
	/**
	  * 极光推送 控制电视
	 * @param request
	 * @param page
	 * @param pageSize
	 * @return
	 */
    @RequestMapping(value="/pushTVMessage.do")
	@ResponseBody
	public PushResult pushTVMessage(HttpServletRequest request,String mac,String type,String content){
 	Map<String, String> params = new HashMap<String,String>();
		
 	PushResult result = new PushResult();
		try {
			params.put("mac", mac);
			params.put("type", type);
			params.put("content", content);
			result = tvService.pushTVMessage(params);
		} catch (Exception e) {
			logger.error("列表查询异常：",e);
			e.printStackTrace();
		}
		return result;
	} 
    
	/**
	 * 登录传推送注册ID
	 * @param map
	 * @return
	 * @throws Exception 
	 */
	@RequestMapping("/updateRegistrationId.do")
	@ResponseBody
	public PurResult updateRegistrationId(
			@RequestParam(value="mac",required=true)Long mac,
			@RequestParam(value="registration_id",required=true)String registration_id
			,HttpServletRequest request
			) throws Exception{
		
		Map<String,Object> map=new HashMap<String, Object>();
		map.put("mac", mac);
		map.put("registration_id", registration_id);
		return tvService.updateRegistrationId(map);
	}
	/**
	  *  广播更新设备在线状态
	 * @param map
	 * @return
	 * @throws Exception 
	 */
	@RequestMapping("/updateTVstatus.do")
	@ResponseBody
	public PushResult updateTVstatus(HttpServletRequest request){
 	Map<String, String> params = new HashMap<String,String>();
		
 	PushResult result = new PushResult();
		try {
			params.put("type", "6");
			result = tvService.pushTVMessageALL(params);
		} catch (Exception e) {
			logger.error("列表查询异常：",e);
			e.printStackTrace();
		}
		return result;
	}
	
    @RequestMapping(value = "/deleteTV.do")
    @ResponseBody
    public PurResult deleteTV(String id){
    	logger.info("后台管理-删除电视");
    	PurResult result = new PurResult();
    	try {	
    		result = tvService.deleteTV(id);
		} catch (Exception e) {
			logger.info("后台管理-删除电视异常："+e.getMessage());
			result.setStatus(0);
			result.setMsg("异常");
		}
        return result;
    }
    @RequestMapping(value = "/deleteTVSource.do")
    @ResponseBody
    public PurResult deleteTVSource(String id){
    	logger.info("后台管理-删除素材");
    	PurResult result = new PurResult();
    	try {	
    		result = tvService.deleteTVSource(id);
    	} catch (Exception e) {
    		logger.info("后台管理-删除素材："+e.getMessage());
    		result.setStatus(0);
    		result.setMsg("异常");
    	}
    	return result;
    }
    @RequestMapping(value = "/deleteAreaPlay.do")
    @ResponseBody
    public PurResult deleteAreaPlay(String id){
    	logger.info("后台管理-删除播放单");
    	PurResult result = new PurResult();
    	try {	
    		result = tvService.deleteAreaPlay(id);
    	} catch (Exception e) {
    		logger.info("后台管理-删除播放单："+e.getMessage());
    		result.setStatus(0);
    		result.setMsg("异常");
    	}
    	return result;
    }
    
    @RequestMapping(value = "/updateTV.do")
    @ResponseBody
    public PurResult updateTV(String phone,String id,String address){
    	logger.info("后台管理-修改TV");
    	PurResult result = new PurResult();
    	try {
    		Map<String, Object> params = new HashMap<String, Object>();
    		params.put("address", address);
    		params.put("id", id);
    		params.put("phone", phone);
    		result = tvService.updateTV(params);
		} catch (Exception e) {
			logger.info("后台管理-修改TV异常："+e.getMessage());
			result.setStatus(0);
			result.setMsg("异常");
		}
        return result;
    }
    
    @RequestMapping(value = "/toTVedit.do")
    public String getBank(String id,Model model){
    	try {
    		Map<String, Object> tv = tvService.getTV(id);
    		model.addAttribute("tv", tv);
		} catch (Exception e) {
			return "/error";
		}
    	return "/WEB-INF/tv/editTV.jsp";
    }
   /*  
    @RequestMapping(value = "/addBank.do")
    @ResponseBody
    public PurResult addBank(String bank_name,String staff_id,HttpServletRequest request){
    	logger.info("后台管理-添加银行");
    	PurResult result = new PurResult();
    	try {
    		Map<String, Object> params = new HashMap<String, Object>();
    		params.put("bank_name", bank_name);
    		params.put("staff_id", staff_id);
    		result = bankService.addBank(params,request);
		} catch (Exception e) {
			logger.info("后台管理-添加银行异常："+e.getMessage());
			result.setStatus(0);
			result.setMsg("异常");
		}
        return result;
    }
 
    @RequestMapping(value = "/getBank.do")
    public String getBank(String bank_id,Model model){
    	logger.info("后台管理-获取银行详情");
    	try {
    		Map<String, Object> bank = bankService.getBank(bank_id);
    		model.addAttribute("bank", bank);
		} catch (Exception e) {
			return "/error";
		}
    	return "/WEB-INF/manager/editBank.jsp";
    }
    

    
    @RequestMapping(value = "/deleteBank.do")
    @ResponseBody
    public PurResult deleteBank(String bank_id){
    	logger.info("后台管理-删除银行");
    	PurResult result = new PurResult();
    	try {	
    		result = bankService.deleteBank(bank_id);
		} catch (Exception e) {
			logger.info("后台管理-删除银行异常："+e.getMessage());
			result.setStatus(0);
			result.setMsg("异常");
		}
        return result;
    }*/
	
	/**
	  *  查询所有店铺
	 * @param map
	 * @return
	 * @throws Exception 
	 */
	@RequestMapping("/queryAllShops.do")
	@ResponseBody
	public PurResult queryAllShops(HttpServletRequest request){
		
		PurResult result = new PurResult();
		try {
			result = tvService.queryAllShops();
		} catch (Exception e) {
			logger.error("列表查询异常：",e);
			e.printStackTrace();
		}
		return result;
	}
	/**
	 *  查询区域
	 * @param map
	 * @return
	 * @throws Exception 
	 */
	@RequestMapping("/queryShopAreaList.do")
	@ResponseBody
	public PurResult queryShopAreaList(String area_dict_parent_num){
		
		PurResult result = new PurResult();
		try {
			Map<String, Object> params=new HashMap<>();
			params.put("area_dict_parent_num", area_dict_parent_num);
			result = tvService.queryShopAreaList(params);
		} catch (Exception e) {
			logger.error("列表查询异常：",e);
			e.printStackTrace();
		}
		return result;
	}
	/**
	 *  添加人员
	 * @param map
	 * @return
	 * @throws Exception 
	 */
	@RequestMapping("/addAdPeople.do")
	@ResponseBody
	public PurResult addAdPeople(String people_name,String phone,String address,
			String shop_unique,String city_code,String county_code,String mac,String mac2){
		
		PurResult result = new PurResult();
		try {
			Map<String, Object> params=new HashMap<>();
			params.put("people_name", people_name);
			params.put("phone", phone);
			params.put("address", address);
			params.put("shop_unique", shop_unique);
			params.put("city_code", city_code);
			params.put("county_code", county_code);
			params.put("mac", mac);
			params.put("mac2", mac2);
			result = tvService.addAdPeople(params);
		} catch (Exception e) {
			logger.error("列表查询异常：",e);
			e.printStackTrace();
		}
		return result;
	}
	/**
	 *  查询人员
	 * @param map
	 * @return
	 * @throws Exception 
	 */
	@RequestMapping("/queryAdPeopleDetail.do")
	@ResponseBody
	public PurResult queryAdPeopleDetail(String id){
		
		PurResult result = new PurResult();
		try {
			Map<String, Object> params=new HashMap<>();
			params.put("id", id);
			result = tvService.queryAdPeopleDetail(params);
		} catch (Exception e) {
			logger.error("列表查询异常：",e);
			e.printStackTrace();
		}
		return result;
	}
	/**
	 *  修改人员
	 * @param map
	 * @return
	 * @throws Exception 
	 */
	@RequestMapping("/updateAdPeople.do")
	@ResponseBody
	public PurResult updateAdPeople(HttpServletRequest request){
		
	  Map<String, Object> params = HandleMessyCode.handle(ServletsUtil.getParameters(request));
		PurResult result = new PurResult();
		try {
			result = tvService.updateAdPeople(params);
		} catch (Exception e) {
			logger.error("列表查询异常：",e);
			e.printStackTrace();
		}
		return result;
	}
	/**
	 *  删除人员
	 * @param map
	 * @return
	 * @throws Exception 
	 */
	@RequestMapping("/deleteAdPeople.do")
	@ResponseBody
	public PurResult deleteAdPeople(String id){
		
		PurResult result = new PurResult();
		try {
			Map<String, Object> params=new HashMap<>();
			params.put("id", id);
			result = tvService.deleteAdPeople(params);
		} catch (Exception e) {
			logger.error("列表查询异常：",e);
			e.printStackTrace();
		}
		return result;
	}
	/**
	 * 电视锁定
	 * @param id
	 * @param lock_status
	 * @return
	 */
	
	@RequestMapping("/updateLockStatus.do")
	@ResponseBody
	public PurResult updateLockStatus(String id,String lock_status) {
		PurResult result=new PurResult();
		try {
			Map<String, Object> params=new HashMap<>();
			params.put("id", id);
			params.put("lock_status", lock_status);
	 		result=tvService.updateLockStatus(params);

		} catch (Exception e) {
			e.printStackTrace();
			result.setStatus(0);
			result.setMsg("异常");
		}
			return result;
		}
	
	@RequestMapping("/updateLockStatusSource.do")
	@ResponseBody
	public PurResult updateLockStatusSource(String id,String lock_status) {
		PurResult result=new PurResult();
		try {
			Map<String, Object> params=new HashMap<>();
			params.put("id", id);
			params.put("use_status", lock_status);
			result=tvService.updateLockStatusSource(params);
			
		} catch (Exception e) {
			e.printStackTrace();
			result.setStatus(0);
			result.setMsg("异常");
		}
		return result;
	}
	@RequestMapping("/updatePlayStatus.do")
	@ResponseBody
	public PurResult updatePlayStatus(String id,String play_status) {
		PurResult result=new PurResult();
		try {
			Map<String, Object> params=new HashMap<>();
			params.put("id", id);
			params.put("play_status", play_status);
			result=tvService.updatePlayStatus(params);
			
		} catch (Exception e) {
			e.printStackTrace();
			result.setStatus(0);
			result.setMsg("异常");
		}
		return result;
	}
	/**
	 * 修改电视定时状态
	 * @param startTiming
	 * @return
	 */
	@RequestMapping("/updateTVTiming.do")
	@ResponseBody
	public PurResult updateTVTiming(String startTiming) {
		PurResult result=new PurResult();
		try {
			Map<String, Object> params=new HashMap<>();
			params.put("startTiming", startTiming);
			result=tvService.updateTVTiming(params);
			
		} catch (Exception e) {
			e.printStackTrace();
			result.setStatus(0);
			result.setMsg("异常");
		}
		return result;
	}
	
	/**
	 * 益农当前素材查询
	 */
	@RequestMapping("/queryYinongSourceNow.do")
	@ResponseBody
	public PurResult queryYinongSourceNow(String countyCody) {
		PurResult result=new PurResult();
		//Downdefault data=taskDao.queryEshow();
		try {
			//查询默认广告列表
			result=tvService.quetyMedia(countyCody);


		} catch (Exception e) {
			e.printStackTrace();
			result.setStatus(0);
			result.setMsg("异常");
		}
			return result;
		}
	
	/**
	 * 
	 * 益农当前素材查询
	 */
	
	@RequestMapping("/querySourceList.do")
	@ResponseBody
	public PurResult querySourceList(@RequestParam(value = "page", defaultValue = "0") int page,
			@RequestParam(value = "limit", defaultValue = "8") int pageSize,String source_type,String use_status) {
		PurResult result=new PurResult();
		try {
			Map<String, Object> params = new HashMap<String, Object>();
			params.put("limit", pageSize);
			params.put("page", (page-1)*pageSize);
    		params.put("source_type", source_type);
    		params.put("use_status", use_status);
			//查询默认广告列表
			result=tvService.querySourceList(params);


		} catch (Exception e) {
			e.printStackTrace();
			result.setStatus(0);
			result.setMsg("异常");
		}
			return result;
		}
	/**
	 * 电视区域列表
	 * @param request
	 * @return
	 */
    @RequestMapping(value="/queryAreaList.do")
	@ResponseBody
	public PurResult queryAreaList(HttpServletRequest request){
    	Map<String, Object> params = ServletsUtil.getParameters(request);
		
		PurResult result = new PurResult();
		try {
			List<AreaVO> list = tvService.queryAreaList(params);
	    	result.setStatus(1);
			result.setMsg("成功");
			result.setData(list);
		} catch (Exception e) {
			logger.error("列表查询异常：",e);
			result.setStatus(0);
			result.setMsg("异常");
		}
		return result;
	}
    /**
	 * 电视型号列表
	 * @param request
	 * @return
	 */
    @RequestMapping(value="/queryModelList.do")
	@ResponseBody
	public PurResult queryModelList(HttpServletRequest request){
    	Map<String, Object> params = ServletsUtil.getParameters(request);
		
		PurResult result = new PurResult();
		try {
			List<TvModelVO> list = tvService.queryModelList(params);
	    	result.setStatus(1);
			result.setMsg("成功");
			result.setData(list);
		} catch (Exception e) {
			logger.error("列表查询异常：",e);
			result.setStatus(0);
			result.setMsg("异常");
		}
		return result;
	}
    /**
     * 新增区域
     * @param permission
     * @return
     */
    @RequestMapping(value = "/addArea.do")
    @ResponseBody
    public PurResult addArea(AreaVO areaVO){
    	logger.info("后台管理-添加电视区域");
    	PurResult result = new PurResult();
    	try {
    		result=tvService.addArea(areaVO);
		} catch (Exception e) {
			logger.info("后台管理-添加电视区域异常："+e.getMessage());
			result.setStatus(0);
			result.setMsg("异常");
		}
        return result;
    }
    //添加素材
    @RequestMapping("/addTVSource.do")
	@ResponseBody
	public PurResult addTVSource(
			String title,
			String url,
			String source_type,
			String lai_yuan,
			String content,
			HttpServletRequest request
			) throws Exception{
		return tvService.addTVSource(title, url, source_type, lai_yuan, content, request);
	}
    //查询所有素材
    @RequestMapping("/queryAllSource.do")
    @ResponseBody
    public PurResult queryAllSource(
    		) {
    	return tvService.queryAllSource();
    }
    //添加播放单
    @RequestMapping("/addAreaPlay.do")
    @ResponseBody
    public PurResult addAreaPlay(
    		String title,
    		String play_type,
    		String type,
    		String source_id,
    		String areaJson
    		) {
    	return tvService.addAreaPlay(title,play_type,source_id,areaJson,type);
    }
    //修改播放单
    @RequestMapping("/editAreaPlay.do")
    @ResponseBody
    public PurResult editAreaPlay(
    		String title,
    		String play_type,
    		String type,
    		String source_id,
    		String areaJson,
    		String id
    		) {
    	return tvService.editAreaPlay(title,play_type,source_id,areaJson,id,type);
    }
    //生产鉴权码
    @RequestMapping("/addAreaCode.do")
    @ResponseBody
    public PurResult addAreaCode(
    		String city_code
    		) {
    	return tvService.addAreaCode(city_code);
    }

    //查询ESHOW 播放单
    @RequestMapping("/queryEshowPlayList.do")
    @ResponseBody
    public PurResult queryEshowPlayList(String county
    		) {
    	return tvService.queryEshowPlayList(county);
    }
    //查询ESHOW 跑马灯
    @RequestMapping("/queryEshowSubtitleList.do")
    @ResponseBody
    public PurResult queryEshowSubtitleList(String county
    		) {
    	return tvService.queryEshowSubtitleList(county);
    }
    //查询ESHOW 播放单详情
    @RequestMapping("/queryEshowPlayListDeatil.do")
    @ResponseBody
    public PurResult queryEshowPlayListDeatil(String id,String county
    		) {
    	return tvService.queryEshowPlayListDeatil(id,county);
    }
    

    //全部电视刷新播放列表
    @RequestMapping("/allRefresh.do")
    @ResponseBody
    public PurResult allRefresh(
    		String type
    		) {
    	List<Map<String,String>> tv_list=tvService.queryOnlineTV();
		for (Map<String, String> map : tv_list) {
			if(map!=null){
				map.put("type",type);
				map.put("content","close");
				try {
					JPushClientUtil.notifyTV(map.get("mac").toString(),"TV",map);
				} catch (Exception e) {
				}
			}
			
		}
    	return new PurResult(1,"");
    }
	@RequestMapping(value = "/toEshowPlayPreviewimg.do")
	public String toEshowPlayPreviewimg(Model model,String id,String county){
		String result=tvService.toEshowPlayDetail(id,county);
		model.addAttribute("playList", result);
		return "/WEB-INF/tv/eshowPlayPreviewimg.jsp";
	}
}
