package org.haier.shop.controller;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.servlet.http.HttpServletRequest;

import org.haier.shop.service.ShopDeviceService;
import org.haier.shop.util.HandleMessyCode;
import org.haier.shop.util.PurResult;
import org.haier.shop.util.ServletsUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

@Controller
@RequestMapping("/shopDevice")
public class ShopDeviceController{
    private static final Logger logger = LoggerFactory.getLogger(ShopDeviceController.class);
    
    @Autowired
    private ShopDeviceService shopDeviceService;
    
    @RequestMapping(value = "/shopDeviceListPage.do")
    public String shopDeviceListPage(){
    	logger.info("跳转店铺设备列表页面");
        return "/WEB-INF/device/ShopDeviceList.jsp";
    }
    
    @RequestMapping(value="/shopDeviceList.do")
	@ResponseBody
	public PurResult shopDeviceList(
			HttpServletRequest request,
			@RequestParam(value = "page", defaultValue = "1") int page,
			@RequestParam(value = "limit", defaultValue = "8") int pageSize){
    	Map<String, Object> params = HandleMessyCode.handle(ServletsUtil.getParameters(request));
		params.put("pageSize", pageSize);
		params.put("startNum", (page-1)*pageSize);
		
		PurResult result = new PurResult();
		try {
			List<Map<String ,Object>> list = shopDeviceService.queryShopDeviceList(params);
	    	Integer count = shopDeviceService.queryShopDeviceListCount(params);
	    	result.setStatus(1);
			result.setMsg("成功");
			result.setCount(count);
			result.setData(list);
		} catch (Exception e) {
			logger.error("列表查询异常：",e);
			e.printStackTrace();
			result.setStatus(0);
			result.setMsg("异常");
		}
		return result;
	}
    
    @RequestMapping(value = "/shopDeviceSummaryPage.do")
    public String shopDeviceSummaryPage(String shop_unique,Model model){
    	logger.info("跳转店铺设备汇总页面");
    	//获取店铺汇总信息
    	Map<String ,Object> deviceSum = shopDeviceService.queryShopDeviceSumInfo(shop_unique);
    	model.addAttribute("deviceSum", deviceSum);
        return "/WEB-INF/device/ShopDeviceSummary.jsp";
    }
    
    @RequestMapping(value = "/shopDeviceDetailPage.do")
    public String shopDeviceDetailPage(String shop_device_id,Model model){
    	logger.info("跳转店铺设备详情页面");
    	//获取店铺设备详情
    	Map<String ,Object> deviceDeatil = shopDeviceService.queryShopDeviceDetail(shop_device_id);
    	model.addAttribute("deviceDeatil",deviceDeatil);
        return "/WEB-INF/device/ShopDeviceDetail.jsp";
    }
    
    @RequestMapping(value = "/shopDeviceApplyListPage.do")
    public String shopDeviceApplyListPage(){
    	logger.info("跳转店铺设备申请列表页面");
        return "/WEB-INF/device/ShopDeviceApplyList.jsp";
    }
    
    @RequestMapping(value="/shopDeviceApplyList.do")
	@ResponseBody
	public PurResult shopDeviceApplyList(
			HttpServletRequest request,
			@RequestParam(value = "page", defaultValue = "1") int page,
			@RequestParam(value = "limit", defaultValue = "8") int pageSize){
    	Map<String, Object> params = HandleMessyCode.handle(ServletsUtil.getParameters(request));
		params.put("pageSize", pageSize);
		params.put("startNum", (page-1)*pageSize);
		
		PurResult result = new PurResult();
		try {
			List<Map<String ,Object>> list = shopDeviceService.shopDeviceApplyList(params);
	    	Integer count = shopDeviceService.shopDeviceApplyListCount(params);
	    	result.setStatus(1);
			result.setMsg("成功");
			result.setCount(count);
			result.setData(list);
		} catch (Exception e) {
			logger.error("列表查询异常：",e);
			e.printStackTrace();
			result.setStatus(0);
			result.setMsg("异常");
		}
		return result;
	}
    
    @RequestMapping(value = "/deviceApplyPage.do")
    public String deviceApplyPage(){
    	logger.info("跳转店铺设备申请页面");
        return "/WEB-INF/device/ShopDeviceApply.jsp";
    }
    
    @RequestMapping(value = "/deviceTypePage.do")
    public String deviceTypePage(Model model){
    	logger.info("跳转设备选择页面");
    	//获取设备类型信息
    	List<Map<String ,Object>> deviceType = shopDeviceService.queryDeviceTypeList();
    	model.addAttribute("deviceType", deviceType);
        return "/WEB-INF/device/ShopDeviceType.jsp";
    }
    
    /**
	 * 添加设备申请记录
	 * @param shop_unique 店铺编号
	 * @param oper_name 经办人姓名
	 * @param shop_name 店铺名称
	 * @param sum_deposit 总押金
	 * @param sum_count 总数量
	 * @param apply_remarks 备注
	 * @param device_list [
	 * 		{
	 * 			device_type_id 设备列表id
	 * 			device_count 申请设备数量
	 * 			device_deposit 单台设备押金
	 * 			sum_deposit 合计押金
	 * 			device_type 类型（1 收银机 ，需要输入编号 2 普通设备）
	 * 		}
	 * ]
	 * @return
	 */
    @RequestMapping(value="/addDeviceApply.do")
	@ResponseBody
	public PurResult addDeviceApply(HttpServletRequest request){
    	Map<String, Object> params = HandleMessyCode.handle(ServletsUtil.getParameters(request));
		
		PurResult result = new PurResult();
		try {
			result = shopDeviceService.addDeviceApply(params);
		} catch (Exception e) {
			e.printStackTrace();
			result.setStatus(0);
			result.setMsg("异常");
		}
		return result;
	}
    
    /**
	 * 修改设备申请记录
	 * @param id 设备申请id
	 * @return
	 */
    @RequestMapping(value="/updateDeviceApply.do")
	@ResponseBody
	public PurResult updateDeviceApply(String id,String valid_sign,String apply_status){
		PurResult result = new PurResult();
		try {
			Map<String, Object> params = new HashMap<String ,Object>(); 
			params.put("id", id);
			params.put("valid_sign", valid_sign);
			params.put("apply_status", apply_status);
			result = shopDeviceService.updateDeviceApply(params);
		} catch (Exception e) {
			e.printStackTrace();
			result.setStatus(0);
			result.setMsg("异常");
		}
		return result;
	}
    
    @RequestMapping(value = "/deviceApplyDetailPage.do")
    public String deviceApplyDetailPage(String id,Model model){
    	logger.info("跳转设备申请详情页面");
    	//获取设备申请详情信息
    	Map<String ,Object> deviceApply = shopDeviceService.queryDeviceApplyDetail(id);
    	model.addAttribute("deviceApply", deviceApply);
        return "/WEB-INF/device/ShopDeviceApplyDetail.jsp";
    }
    
    @RequestMapping(value = "/shopDeviceReturnListPage.do")
    public String shopDeviceReturnListPage(){
    	logger.info("跳转店铺设备退换列表页面");
        return "/WEB-INF/device/ShopDeviceReturnList.jsp";
    }
    
    @RequestMapping(value="/shopDeviceReturnList.do")
	@ResponseBody
	public PurResult shopDeviceReturnList(
			HttpServletRequest request,
			@RequestParam(value = "page", defaultValue = "1") int page,
			@RequestParam(value = "limit", defaultValue = "8") int pageSize){
    	Map<String, Object> params = HandleMessyCode.handle(ServletsUtil.getParameters(request));
		params.put("pageSize", pageSize);
		params.put("startNum", (page-1)*pageSize);
		
		PurResult result = new PurResult();
		try {
			List<Map<String ,Object>> list = shopDeviceService.shopDeviceReturnList(params);
	    	Integer count = shopDeviceService.shopDeviceReturnListCount(params);
	    	result.setStatus(1);
			result.setMsg("成功");
			result.setCount(count);
			result.setData(list);
		} catch (Exception e) {
			logger.error("列表查询异常：",e);
			e.printStackTrace();
			result.setStatus(0);
			result.setMsg("异常");
		}
		return result;
	}
    
    @RequestMapping(value = "/deviceReturnPage.do")
    public String deviceReturnPage(){
    	logger.info("跳转店铺设备退换页面");
        return "/WEB-INF/device/ShopDeviceReturn.jsp";
    }
    
    @RequestMapping(value = "/deviceReturnChosePage.do")
    public String deviceReturnChosePage(String shop_unique,Model model){
    	logger.info("跳转退换设备选择页面");
    	//获取店铺设备列表
    	Map<String ,Object> params = new HashMap<String ,Object>();
    	params.put("shop_unique", shop_unique);
    	List<Map<String ,Object>> shopDeviceList = shopDeviceService.queryShopDeviceList(params);
    	model.addAttribute("shopDeviceList", shopDeviceList);
        return "/WEB-INF/device/ShopDeviceChose.jsp";
    }
    
    /**
	 * 添加设备退换记录
	 * @param shop_name 店铺名称
	 * @param shop_address 店铺地址
	 * @param shop_user_name 店铺联系人
	 * @param shop_user_tel 联系电话
	 * @param shop_unique 店铺编号
	 * @param shop_service_sum_deposit 总押金
	 * @param shop_service_sum_count 总数量
	 * @param shop_service_apply_type 申请类型：1、换货；2、退货
	 * @param shop_service_remarks 备注
	 * @param apply_user_name 经办人
	 * @param device_list [
	 * 		{
	 * 			device_type_id 设备列表id
	 * 			shop_service_detail_count 申请设备数量
	 * 			shop_service_detail_deposit 押金
	 * 			shop_service_detail_device_no 设备编号
	 * 			shop_device_id 店铺设备id
	 * 		}
	 * ]
	 * @return
	 */
    @RequestMapping(value="/addDeviceReturn.do")
	@ResponseBody
	public PurResult addDeviceReturn(HttpServletRequest request){
    	Map<String, Object> params = HandleMessyCode.handle(ServletsUtil.getParameters(request));
		
		PurResult result = new PurResult();
		try {
			result = shopDeviceService.addDeviceReturn(params);
		} catch (Exception e) {
			e.printStackTrace();
			result.setStatus(0);
			result.setMsg("异常");
		}
		return result;
	}
    
    @RequestMapping(value = "/deviceReturnDetailPage.do")
    public String deviceReturnDetailPage(String id,Model model){
    	logger.info("跳转设备退换详情页面");
    	//获取设备退换详情信息
    	Map<String ,Object> deviceReturn = shopDeviceService.queryDeviceReturnDetail(id);
    	model.addAttribute("deviceReturn", deviceReturn);
    	
        return "/WEB-INF/device/ShopDeviceReturnDetail.jsp";
    }
    
    /**
	 * 修改店铺设备售后信息
	 * @param id 
	 * @param shop_service_handle_status 状态 10：已取消
	 * @return
	 */
    @RequestMapping(value="/updateShopService.do")
	@ResponseBody
	public PurResult updateShopService(HttpServletRequest request){
    	Map<String, Object> params = HandleMessyCode.handle(ServletsUtil.getParameters(request));
		
		PurResult result = new PurResult();
		try {
			result = shopDeviceService.updateShopService(params);
		} catch (Exception e) {
			e.printStackTrace();
			result.setStatus(0);
			result.setMsg("异常");
		}
		return result;
	}
    
    @RequestMapping(value = "/shopDeviceRepairListPage.do")
    public String shopDeviceRepairListPage(){
    	logger.info("跳转店铺设备维修记录列表页面");
        return "/WEB-INF/device/ShopDeviceRepairList.jsp";
    }
    
    @RequestMapping(value="/shopDeviceRepairList.do")
	@ResponseBody
	public PurResult shopDeviceRepairList(
			HttpServletRequest request,
			@RequestParam(value = "page", defaultValue = "1") int page,
			@RequestParam(value = "limit", defaultValue = "8") int pageSize){
    	Map<String, Object> params = HandleMessyCode.handle(ServletsUtil.getParameters(request));
		params.put("pageSize", pageSize);
		params.put("startNum", (page-1)*pageSize);
		
		PurResult result = new PurResult();
		try {
			List<Map<String ,Object>> list = shopDeviceService.shopDeviceRepairList(params);
	    	Integer count = shopDeviceService.shopDeviceRepairListCount(params);
	    	result.setStatus(1);
			result.setMsg("成功");
			result.setCount(count);
			result.setData(list);
		} catch (Exception e) {
			logger.error("列表查询异常：",e);
			e.printStackTrace();
			result.setStatus(0);
			result.setMsg("异常");
		}
		return result;
	}
    
    @RequestMapping(value = "/deviceRepairPage.do")
    public String deviceRepairPage(String shop_unique,Model model){
    	logger.info("跳转店铺设备保修页面");
    	//获取店铺设备列表
    	Map<String ,Object> params = new HashMap<String ,Object>();
    	params.put("shop_unique", shop_unique);
    	List<Map<String ,Object>> shopDeviceList = shopDeviceService.queryShopDeviceList(params);
    	model.addAttribute("shopDeviceList", shopDeviceList);
        return "/WEB-INF/device/ShopDeviceRepair.jsp";
    }
    
    /**
	 * 添加设备维修记录
	 * @param shop_name 店铺名称
	 * @param shop_address 店铺地址
	 * @param shop_user_name 店铺联系人
	 * @param shop_user_tel 联系电话
	 * @param shop_unique 店铺编号
	 * @param shop_service_sum_deposit 总押金
	 * @param shop_service_sum_count 总数量
	 * @param shop_service_apply_type 申请类型：3维修
	 * @param shop_service_remarks 备注
	 * @param apply_user_name 经办人
	 * @param device_type_id 设备列表id
	 * @param shop_service_detail_count 申请设备数量
	 * @param shop_service_detail_deposit 押金
	 * @param shop_service_detail_device_no 设备编号
	 * @param shop_device_id 店铺设备id
	 * @return
	 */
    @RequestMapping(value="/addDeviceRepair.do")
	@ResponseBody
	public PurResult addDeviceRepair(HttpServletRequest request){
    	Map<String, Object> params = HandleMessyCode.handle(ServletsUtil.getParameters(request));
		
		PurResult result = new PurResult();
		try {
			result = shopDeviceService.addDeviceRepair(params);
		} catch (Exception e) {
			e.printStackTrace();
			result.setStatus(0);
			result.setMsg("异常");
		}
		return result;
	}
    
    @RequestMapping(value = "/deviceRepairDetailPage.do")
    public String deviceRepairDetailPage(String id,Model model){
    	logger.info("跳转设备保修详情页面");
    	//获取设备退换详情信息
    	Map<String ,Object> deviceReturn = shopDeviceService.queryDeviceReturnDetail(id);
    	model.addAttribute("deviceReturn", deviceReturn);
    	
        return "/WEB-INF/device/ShopDeviceRepairDetail.jsp";
    }
}