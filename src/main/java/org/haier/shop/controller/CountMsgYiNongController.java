package org.haier.shop.controller;

import javax.annotation.Resource;

import org.haier.shop.service.CountMsgYiNongService;
import org.haier.shop.util.PurResult;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

@Controller
@RequestMapping("/countMsgYiNong")
public class CountMsgYiNongController {
	@Resource
	private CountMsgYiNongService countMsgYiNongService;
	
	//统计信息页面
	@RequestMapping("/countMsgYiNongPage.do")
	public String countMsgPage(){
		return "/WEB-INF/countMessage/countMsgYiNongPage2.jsp";
	}
	//供货商大数据页面
	@RequestMapping("/purchaseDataPage.do")
	public String purchaseDataPage(){
		return "/WEB-INF/countMessage/purchaseDataPage.jsp";
	}
	@RequestMapping("/purchaseSort.do")
	public String purchaseSort(){
		return "/WEB-INF/countMessage/purchaseSort.jsp";
	}
	
	//查询店铺是否是中心站
	@RequestMapping("/queryShopIsCenter.do")
	@ResponseBody
	public PurResult queryShopIsCenter(String shop_unique){
		return countMsgYiNongService.queryShopIsCenter(shop_unique);
	}
	//查询下属站点销售统计
	@RequestMapping("/queryChildSaleCount.do")
	@ResponseBody
	public PurResult queryChildSaleCount(String shop_unique,String type){
		return countMsgYiNongService.queryChildSaleCount(shop_unique,type);
	}
	//查询中心站销售统计
	@RequestMapping("/queryOrderMsg.do")
	@ResponseBody
	public PurResult queryOrderMsg(String shop_unique,String type){
		return countMsgYiNongService.queryOrderMsg(shop_unique,type);
	}
	//查询中心站销售趋势
	@RequestMapping("/querySaleCount.do")
	@ResponseBody
	public PurResult querySaleCount(String shop_unique,String type){
		return countMsgYiNongService.querySaleCount(shop_unique,type);
	}
	//查询中心站地区销售统计
	@RequestMapping("/queryCenterSaleCount.do")
	@ResponseBody
	public PurResult queryCenterSaleCount(String shop_unique){
		return countMsgYiNongService.queryCenterSaleCount(shop_unique);
	}
	
	
	//查询供货商销售统计
	@RequestMapping("/queryOrderMsgPurchase.do")
	@ResponseBody
	public PurResult queryOrderMsgPurchase(String type){
		return countMsgYiNongService.queryOrderMsgPurchase(type);
	}
	//按供货商排名
	@RequestMapping("/querySaleMoneyByPurchase.do")
	@ResponseBody
	public PurResult querySaleMoneyByPurchase(){
		return countMsgYiNongService.querySaleMoneyByPurchase();
	}
	@RequestMapping("/querySaleMoneyByPurchase2.do")
	@ResponseBody
	public PurResult querySaleMoneyByPurchase2(){
		return countMsgYiNongService.querySaleMoneyByPurchase2();
	}
	@RequestMapping("/queryGoodsListPurchase.do")
	@ResponseBody
	public PurResult queryGoodsListPurchase(String type){
		return countMsgYiNongService.queryGoodsListPurchase(type);
	}
	@RequestMapping("/queryOrderListPurchase.do")
	@ResponseBody
	public PurResult queryOrderListPurchase(String type){
		return countMsgYiNongService.queryOrderListPurchase(type);
	}
	
	//查询销售趋势
	@RequestMapping("/querySaleCountPurchase.do")
	@ResponseBody
	public PurResult querySaleCountPurchase(String type){
		return countMsgYiNongService.querySaleCountPurchase(type);
	}
	//查询销售统计按月份
	@RequestMapping("/querySaleCountByMonth.do")
	@ResponseBody
	public PurResult querySaleCountByMonth(String type){
		return countMsgYiNongService.querySaleCountByMonth(type);
	}
	//分类占比
	@RequestMapping("/kindSaleRatio.do")
	@ResponseBody
	public PurResult kindSaleRatio(String type){
		return countMsgYiNongService.kindSaleRatio(type);
	}
	//销售环比
	@RequestMapping("/querySaleTB.do")
	@ResponseBody
	public PurResult querySaleTB(String type){
		return countMsgYiNongService.querySaleTB(type);
	}
	@RequestMapping("/queryPinPaiList.do")
	@ResponseBody
	public PurResult queryPinPaiList(){
		return countMsgYiNongService.queryPinPaiList();
	}
}
