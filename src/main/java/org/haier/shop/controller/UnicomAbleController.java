package org.haier.shop.controller;

import java.util.HashMap;
import java.util.Map;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;

import org.haier.shop.service.UnicomAbleService;
import org.haier.shop.util.HandleMessyCode;
import org.haier.shop.util.PurResult;
import org.haier.shop.util.ServletsUtil;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

@RequestMapping("/unicomAble")
@Controller
public class UnicomAbleController {
	
	@Resource
	private UnicomAbleService unicomAbleService;

	//跳转联通能人列表页面
	@RequestMapping(value = "/toAbleListPage.do")
    public String toAbleListPage(){
        return "/WEB-INF/unicom/ableList.jsp";
    }
    
	//查询能人列表
    @RequestMapping(value="/queryUnicomAbleList.do")
	@ResponseBody
	public PurResult queryUnicomAbleList(
			HttpServletRequest request,
			@RequestParam(value = "page", defaultValue = "0") int page,
			@RequestParam(value = "limit", defaultValue = "8") int pageSize){
    	Map<String, Object> params = HandleMessyCode.handle(ServletsUtil.getParameters(request));
		params.put("pageSize", pageSize);
		params.put("startNum", (page-1)*pageSize);
		
		PurResult result = new PurResult();
		try {
			result = unicomAbleService.queryUnicomAbleList(params);
		} catch (Exception e) {
			e.printStackTrace();
			result.setStatus(0);
			result.setMsg("异常");
		}
		return result;
	}
    
    //跳转联通能人添加页面
  	@RequestMapping(value = "/toAddAblePage.do")
      public String toAddAblePage(){
          return "/WEB-INF/unicom/addAble.jsp";
      }
      
  	//添加能人信息
    @RequestMapping(value="/addAble.do")
  	@ResponseBody
  	public PurResult addAble(HttpServletRequest request){
      	Map<String, Object> params = HandleMessyCode.handle(ServletsUtil.getParameters(request));
  		PurResult result = new PurResult();
  		try {
  			result = unicomAbleService.addUnicomAble(params);
  		} catch (Exception e) {
  			e.printStackTrace();
  			result.setStatus(0);
  			result.setMsg("异常");
  		}
  		return result;
  	}
      
    //跳转联通能人修改页面
  	@RequestMapping(value = "/toUpdateAblePage.do")
    public String toUpdateAblePage(String unicom_able_id,Model model){
  		//查询能人详情
  		Map<String ,Object> params = new HashMap<String ,Object>();
  		params.put("unicom_able_id", unicom_able_id);
  		Map<String ,Object> detail = unicomAbleService.queryUnicomAbleDetail(params);
  		model.addAttribute("ableDetail", detail);
        return "/WEB-INF/unicom/updateAble.jsp";
    }
      
  	//修改能人信息
    @RequestMapping(value="/updateAble.do")
  	@ResponseBody
  	public PurResult updateAble(HttpServletRequest request){
      	Map<String, Object> params = HandleMessyCode.handle(ServletsUtil.getParameters(request));
  		PurResult result = new PurResult();
  		try {
  			result = unicomAbleService.updateUnicomAble(params);
  		} catch (Exception e) {
  			e.printStackTrace();
  			result.setStatus(0);
  			result.setMsg("异常");
  		}
  		return result;
  	}
      
  	//删除能人
    @RequestMapping(value="/deleteAble.do")
  	@ResponseBody
  	public PurResult deleteAble(String unicom_able_id){
  		PurResult result = new PurResult();
  		try {
  			result = unicomAbleService.deleteUnicomAble(unicom_able_id);
  		} catch (Exception e) {
  			e.printStackTrace();
  			result.setStatus(0);
  			result.setMsg("异常");
  		}
  		return result;
  	}
      
    //跳转联通能人签单列表页面
  	@RequestMapping(value = "/toAbleEsignListPage.do")
    public String toAbleEsignListPage(String unicom_able_id,Model model){
  		  model.addAttribute("unicom_able_id", unicom_able_id);
          return "/WEB-INF/unicom/ableEsignList.jsp";
    }
      
  	//查询能人签单列表
    @RequestMapping(value="/queryUnicomAbleEsignList.do")
  	@ResponseBody
  	public PurResult queryUnicomAbleEsignList(
  			HttpServletRequest request,
  			@RequestParam(value = "page", defaultValue = "0") int page,
  			@RequestParam(value = "limit", defaultValue = "8") int pageSize){
      	Map<String, Object> params = HandleMessyCode.handle(ServletsUtil.getParameters(request));
      	params.put("pageSize", pageSize);
		params.put("startNum", (page-1)*pageSize);
  		
  		PurResult result = new PurResult();
  		try {
  			result = unicomAbleService.queryUnicomAbleEsignList(params);
  		} catch (Exception e) {
  			e.printStackTrace();
  			result.setStatus(0);
  			result.setMsg("异常");
  		}
  		return result;
  	}
}
