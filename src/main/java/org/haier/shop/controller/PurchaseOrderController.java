package org.haier.shop.controller;

import java.io.IOException;
import java.io.OutputStream;
import java.net.URLEncoder;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import cn.hutool.core.util.ObjectUtil;
import org.apache.shiro.SecurityUtils;
import org.apache.shiro.session.Session;
import org.apache.shiro.subject.Subject;
import org.haier.meituan.util.MUtil;
import org.haier.shop.dao.ShopsConfigDao;
import org.haier.shop.entity.ShopsConfig;
import org.haier.shop.entity.Staff;
import org.haier.shop.enums.GoodsInPriceTypeEnums;
import org.haier.shop.service.ShopService;
import org.haier.shop.service.SupOrderService;
import org.haier.shop.util.LoadOutObjectXLSUtil;
import org.haier.shop.util.PurResult;
import org.haier.shop.util.ShopsResult;
import org.haier.shop.util.XLSCallBack;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;


/**
 * 进货订单查询控制器--新版本
 * 查询供货商数据库，供货商云商订单
 */

@Controller
@RequestMapping("/purchase")
public class PurchaseOrderController {
	
	@Resource
	private SupOrderService supOrderService;
	
	@Resource
	private ShopService shopService;
	@Resource
	private ShopsConfigDao shopsConfigDao;
	
	@RequestMapping("/addNewPurRet.do")
	public String addNewPurRet(String shop_unique,Model model) {
		//是否允许自采购:0 不允许 1允许
		Integer isSelfPurchase = 1;
		if(null == shop_unique || shop_unique.equals("")) {
			Subject subject = SecurityUtils.getSubject();
			Session session = subject.getSession();
			Staff staff = (Staff) session.getAttribute("staff");
			shop_unique = String.valueOf(staff.getShop_unique());
		}
		//获取商家信息
		Map<String ,Object> params = new HashMap<String, Object>();
		params.put("shop_unique", shop_unique);
		ShopsResult result = shopService.queryShopMessage(shop_unique);
		if(result.getStatus() == 0){
			Map<String,Object> shopInfo = (Map<String, Object>) result.getData();
			Integer shop_class = Integer.parseInt(MUtil.strObject(shopInfo.get("shop_class")));//店铺分类：0普通商家；1：连锁；2加盟
			String is_other_purchase = MUtil.strObject(shopInfo.get("is_other_purchase"));//是否允许像其他供货商采购：0不允许 1允许
			if(shop_class != 0 && is_other_purchase.equals("0")){//连锁加盟并且不允许向其他供货商采购
				isSelfPurchase = 0;
			}
		}
		model.addAttribute("isSelfPurchase", isSelfPurchase);
		ShopsConfig shopsConfig = shopsConfigDao.selectByShopUnique(shop_unique);
		if (ObjectUtil.isNotNull(shopsConfig) && ObjectUtil.equals(GoodsInPriceTypeEnums.BATCH.getCode(), shopsConfig.getGoodsInPriceType())) {
			return "/WEB-INF/purchase/addNewPurRetBatch.jsp";
		}
		return "/WEB-INF/purchase/addNewPurRet.jsp";
	}
	
	/**
	 * 查询退款订单信息
	 * @param shopUnique 店铺编号
	 * @param page 页码
	 * @param pageSize 单页查询数量
	 * @param startTime 开始时间
	 * @param endTime 结束时间
	 * @param msg 输入框输入的信息，查询单号，供货商名称
	 * @return
	 */
	@RequestMapping("/getSupRetOrderList.do")
	@ResponseBody
	public PurResult getSupRetOrderList(String shopUnique,Integer page,Integer limit,String startTime,String endTime,String msg ) {
		return supOrderService.getSupRetOrderList(shopUnique, page, limit, startTime, endTime, msg);
	}
	
	
	@RequestMapping("/toChooseGoods.do")
	public String toChooseGoods() {
		return "/WEB-INF/purchase/choseGoods.jsp";
	}
	
	/**
	 * 跳转进货退单列表页
	 * @return
	 */
	@RequestMapping("/supRetOrderListPage.do")
	public String supRetOrderListPage(String shop_unique,Model model){
		//是否允许自采购:0 不允许 1允许
		Integer isSelfPurchase = 1;
		if(null == shop_unique || shop_unique.equals("")) {
			Subject subject = SecurityUtils.getSubject();
			Session session = subject.getSession();
			Staff staff = (Staff) session.getAttribute("staff");
			shop_unique = String.valueOf(staff.getShop_unique());
		}
		//获取商家信息
		Map<String ,Object> params = new HashMap<String, Object>();
		params.put("shop_unique", shop_unique);
		ShopsResult result = shopService.queryShopMessage(shop_unique);
		if(result.getStatus() == 0){
			Map<String,Object> shopInfo = (Map<String, Object>) result.getData();
			Integer shop_class = Integer.parseInt(MUtil.strObject(shopInfo.get("shop_class")));//店铺分类：0普通商家；1：连锁；2加盟
			String is_other_purchase = MUtil.strObject(shopInfo.get("is_other_purchase"));//是否允许像其他供货商采购：0不允许 1允许
			if(shop_class != 0 && is_other_purchase.equals("0")){//连锁加盟并且不允许向其他供货商采购
				isSelfPurchase = 0;
			}
		}
		model.addAttribute("isSelfPurchase", isSelfPurchase);
		return "/WEB-INF/purchase/purchaseRetOrderList.jsp";
	}
	
	/**
	 * 跳转进货订单列表页
	 * @return
	 */
	@RequestMapping("/supOrderListPage.do")
	public String supOrderListPage(String shop_unique,Model model){
		//是否允许自采购:0 不允许 1允许
		Integer isSelfPurchase = 1;
		if(null == shop_unique || shop_unique.equals("")) {
			Subject subject = SecurityUtils.getSubject();
			Session session = subject.getSession();
			Staff staff = (Staff) session.getAttribute("staff");
			shop_unique = String.valueOf(staff.getShop_unique());
		}
		//获取商家信息
		Map<String ,Object> params = new HashMap<String, Object>();
		params.put("shop_unique", shop_unique);
		ShopsResult result = shopService.queryShopMessage(shop_unique);
		if(result.getStatus() == 0){
			Map<String,Object> shopInfo = (Map<String, Object>) result.getData();
			Integer shop_class = Integer.parseInt(MUtil.strObject(shopInfo.get("shop_class")));//店铺分类：0普通商家；1：连锁；2加盟
			String is_other_purchase = MUtil.strObject(shopInfo.get("is_other_purchase"));//是否允许像其他供货商采购：0不允许 1允许
			if(shop_class != 0 && is_other_purchase.equals("0")){//连锁加盟并且不允许向其他供货商采购
				isSelfPurchase = 0;
			}
		}
		model.addAttribute("isSelfPurchase", isSelfPurchase);
		return "/WEB-INF/purchase/purchaseOrderList.jsp";
	}
	
	/**
	 * 查询进货订单列表
	 * @param shop_unique
	 * @param search_str 订单编号/供货商名称
	 * @param start_date 查询开始时间
	 * @param end_date 查询结束时间
	 * @param order_status 订单状态1: 待发货 2:待配送 3:配送中 4:已完成
	 * @param pay_status 支付状态：1、欠款；2、已结清
	 * @param order_type 订单类型 0：自动下单；1：客户下单
	 * @param purchase_status 采购单状态：0待收货 1已完成 2已取消
	 * @param startNum
	 * @param pageSize
	 * @param order_source 订单来源：1云商/总店 2自采购
	 * @return
	 */
	@RequestMapping("getSupOrderList.do")
	@ResponseBody
	public PurResult getSupOrderList(
			@RequestParam(value="shop_unique",required=true)String shop_unique,
			String search_str,
			String start_date,
			String end_date,
			String order_status, 
			String pay_status,
			String order_type,
			String order_source,
			String purchase_status,
			Integer page,
			Integer limit
			){
		Map<String ,Object> params = new HashMap<String, Object>();
		params.put("shop_unique", shop_unique);
		params.put("search_str", search_str);
		params.put("start_date", start_date);
		params.put("end_date", end_date);
		params.put("order_status", order_status);
		params.put("pay_status", pay_status);
		params.put("order_type", order_type);
		params.put("purchase_status", purchase_status);
		if(page != null){
			params.put("startNum", (page-1)*limit);
			params.put("pageSize", limit);
		}
		return supOrderService.getSupOrderList(params,order_source);
	}
	
	@RequestMapping("/supPurchaseReturnApplyDetail.do")
	public String supPurchaseReturnApplyDetail(Model model,String order_code,String order_source,String shop_unique) {
		model.addAttribute("order_code", order_code);
		if(order_source.equals("1")){//订单来源：1云商/总店
			return "/WEB-INF/purchase/purchaseOrderDetail.jsp";
		}else if(order_source.equals("2")){//订单来源 :2自采购
			model.addAttribute("shop_unique", shop_unique);
			return "/WEB-INF/purchase/selfPurchaseReturnApplyDetail.jsp";
		}
		return "";
	}
	
	/**
	 * 跳转退货订单详情页
	 * @param model
	 * @param selfPurchaseRetUnique
	 * @param shopUnique
	 * @return
	 */
	@RequestMapping("/supRetOrderDetailPage.do")
	public String supRetOrderDetailPage(Model model,String selfPurchaseRetUnique,String shopUnique) {
		model.addAttribute("selfPurchaseRetUnique", selfPurchaseRetUnique);
		
		return "/WEB-INF/purchase/selfPurchaseRetOrderDetail.jsp";
	}
	
	/**
	 * 跳转进货订单详情页
	 * @return
	 */
	@RequestMapping("/supOrderDetailPage.do")
	public String supOrderDetailPage(Model model,String order_code,String order_source,String shop_unique){
		model.addAttribute("order_code", order_code);
		if(order_source.equals("1")){//订单来源：1云商/总店
			return "/WEB-INF/purchase/purchaseOrderDetail.jsp";
		}else if(order_source.equals("2")){//订单来源 :2自采购
			model.addAttribute("shop_unique", shop_unique);
			return "/WEB-INF/purchase/selfPurchaseOrderDetail.jsp";
		}
		return "";
	}
	
	@RequestMapping("/selfPurchaseOrderPay.do")
	public String selfPurchaseOrderPay(Model model,String order_code,String order_source,String shop_unique) {
		model.addAttribute("order_code", order_code);
		if(order_source.equals("1")){//订单来源：1云商/总店
			return "/WEB-INF/purchase/purchaseOrderDetail.jsp";
		}else if(order_source.equals("2")){//订单来源 :2自采购
			model.addAttribute("shop_unique", shop_unique);
			return "/WEB-INF/purchase/selfPurchaseOrderPay.jsp";
		}
		return "";
	}
	
	/**
	 * 获取订单详情
	 * @param order_code 订单编号
	 * @return
	 */
	@RequestMapping("getSupOrderDetail.do")
	@ResponseBody
	public PurResult getSupOrderDetail(String order_code){
		return supOrderService.getSupOrderDetail(order_code);
	}
	
	/**
	 * 取消云商进货单
	 * @param order_code 订单编号
	 * @param order_status 5:已取消
	 * @return
	 */	
	@RequestMapping("updateOrderStatus.do")
	@ResponseBody
	public PurResult updateOrderStatus(String order_code,String order_status){
		Map<String ,Object> params = new HashMap<String, Object>();
		params.put("order_code", order_code);
		params.put("order_status", order_status);
		return supOrderService.updateOrderStatus(params);
	}
	
	/**
	 * 下载进货订单excel表
	 * @param map
	 * @param shopUnique
	 * @return
	 */
	@RequestMapping("/purListExcel.do")
	@ResponseBody
	public void purListExcel(
			@RequestParam(value="shop_unique",required=true)String shop_unique,
			String search_str,
			String start_date,
			String end_date,
			String order_status, 
			String pay_status,
			String order_type,
			String order_source,
			String purchase_status,
			HttpServletRequest request,HttpServletResponse response
			){
		Map<String ,Object> params = new HashMap<String, Object>();
		params.put("shop_unique", shop_unique);
		params.put("search_str", search_str);
		params.put("start_date", start_date);
		params.put("end_date", end_date);
		params.put("order_status", order_status);
		params.put("pay_status", pay_status);
		params.put("order_type", order_type);
		params.put("purchase_status", purchase_status);
		if(order_source.equals("1")){
			List<Map<String ,Object>> list = supOrderService.getOrderList(params);
			
			loadOutClaimXLS(list,"云商或总店进货记录Excel",request,response);
		}else if(order_source.equals("2")){
			List<Map<String ,Object>> list = supOrderService.getSelfOrderList(params);
			
			loadOutClaimXLSSelf(list,"自采购进货记录Excel",request,response);
		}
	}
	
	public void loadOutClaimXLS(List<Map<String ,Object>> list, final String fileName,HttpServletRequest request,HttpServletResponse response) {
		try {
			response.setContentType(
					"application/x-msdownload; charset=UTF-8");// 告知浏览器文件类型
			String agent = request.getHeader("user-agent");
			if (agent.contains("Firefox")) {
				response.setHeader(
						"Content-Disposition",
						"attachment;filename="
								+ new String((fileName + ".xls")
										.getBytes("GB2312"), "ISO-8859-1"));// 设置文件头
			}else{
				response.setHeader(
						"Content-Disposition",
						"attachment;filename="
								+ URLEncoder.encode(fileName + ".xls", "UTF-8"));// 设置文件头
			}
			OutputStream os = response.getOutputStream();
			LoadOutObjectXLSUtil<Map<String ,Object>> objectXLS = new LoadOutObjectXLSUtil<Map<String ,Object>>();
			objectXLS.generateXLS(os, list, new XLSCallBack<Map<String ,Object>>(){
				public Object[] getValues(Map<String ,Object> tt) {
					
					String order_code = MUtil.strObject(tt.get("order_code"));
					String company_name = MUtil.strObject(tt.get("company_name"));
					String service_phone = MUtil.strObject(tt.get("service_phone"));
					String create_date = MUtil.strObject(tt.get("create_date"));
					create_date = create_date.substring(0, create_date.indexOf('.'));
					String order_money = MUtil.strObject(tt.get("order_money"));
					String order_status = MUtil.strObject(tt.get("order_status"));
					String pay_mode = MUtil.strObject(tt.get("pay_mode"));
					String pay_status = MUtil.strObject(tt.get("pay_status"));
					String pay_money = MUtil.strObject(tt.get("pay_money"));
					String order_type = MUtil.strObject(tt.get("order_type"));
					
					String company_region = MUtil.strObject(tt.get("company_region"));
					if(!company_region.isEmpty()){
						company_region = company_region.replace("/", "");
					}
					String company_location = MUtil.strObject(tt.get("company_location"));
					String address = company_region+company_location;
					
					//订单状态：1: 待发货 2:待配送 3:配送中 4:已完成
					if(order_status.equals("1")){
						order_status = "待发货";
					}else if(order_status.equals("2")){
						order_status = "待配送";
					}else if(order_status.equals("3")){
						order_status = "配送中";
					}else if(order_status.equals("4")){
						order_status = "已完成";
					}
					//支付方式：1、货到付款；2、支付宝；3、微信;4、其他
					if(pay_mode.equals("1")){
						pay_mode = "货到付款";
					}else if(pay_mode.equals("2")){
						pay_mode = "支付宝";
					}else if(pay_mode.equals("3")){
						pay_mode = "微信";
					}else if(pay_mode.equals("4")){
						pay_mode = "其他";
					}
					//支付状态：1、欠款；2、已结清
					if(pay_status.equals("1")){
						pay_status = "欠款";
					}else if(pay_status.equals("2")){
						pay_status = "已结清";
					}
					//订单类型 0：自动下单；1：客户下单
					if(order_type.equals("0")){
						order_type = "自动下单";
					}else if(order_type.equals("1")){
						order_type = "手动下单";
					}
					return new String[] {order_code,company_name,service_phone,address,create_date,
										order_money,order_status,pay_mode,pay_status,pay_money,
										order_type};
				}

				public String getTitle() {
					return fileName;
				}

				public int[] getColumnsWidth() {
					return new int[] {80 * 80, 80 * 80,80 * 80,80 * 80,80 * 80,
									 80 * 80, 80 * 80,80 * 80,80 * 80,80 * 80,
									 80 * 80};
				}

				public String[] getColumnsName() {
					return new String[] {"订单编号","供货商名称","联系电话","供货商地址","下单时间",
										"订单总金额","订单状态","付款方式","付款状态","已支付金额",
										"订单类型"};
				}

				public int[][] MergedRegion() {
					// TODO Auto-generated method stub
					return null;
				}

				public List<Object[]> getheaderValue() {
					// TODO Auto-generated method stub
					return null;
				}
			});
			os.flush();
		} catch (IOException e) {
			e.printStackTrace();
		}
	}
	
	public void loadOutClaimXLSSelf(List<Map<String ,Object>> list, final String fileName,HttpServletRequest request,HttpServletResponse response) {
		try {
			response.setContentType(
					"application/x-msdownload; charset=UTF-8");// 告知浏览器文件类型
			String agent = request.getHeader("user-agent");
			if (agent.contains("Firefox")) {
				response.setHeader(
						"Content-Disposition",
						"attachment;filename="
								+ new String((fileName + ".xls")
										.getBytes("GB2312"), "ISO-8859-1"));// 设置文件头
			}else{
				response.setHeader(
						"Content-Disposition",
						"attachment;filename="
								+ URLEncoder.encode(fileName + ".xls", "UTF-8"));// 设置文件头
			}
			OutputStream os = response.getOutputStream();
			LoadOutObjectXLSUtil<Map<String ,Object>> objectXLS = new LoadOutObjectXLSUtil<Map<String ,Object>>();
			objectXLS.generateXLS(os, list, new XLSCallBack<Map<String ,Object>>(){
				public Object[] getValues(Map<String ,Object> tt) {
					
					String self_purchase_unique = MUtil.strObject(tt.get("self_purchase_unique"));
					String supplier_name = MUtil.strObject(tt.get("supplier_name"));
					String supplier_phone = MUtil.strObject(tt.get("supplier_phone"));
					String supplier_address = MUtil.strObject(tt.get("supplier_address"));
					String create_time = MUtil.strObject(tt.get("create_time"));
					create_time = create_time.substring(0, create_time.indexOf('.'));
					String total_price = MUtil.strObject(tt.get("total_price"));
					String total_count = MUtil.strObject(tt.get("total_count"));
					String purchase_status = MUtil.strObject(tt.get("purchase_status"));
					String pay_status = MUtil.strObject(tt.get("pay_status"));
					Double arrears_price = Double.valueOf(MUtil.strObject(tt.get("arrears_price")));
					Double pay_money = Double.valueOf(total_price)-arrears_price;
					
					//采购单状态：0待收货 1已完成 2已取消
					if(purchase_status.equals("0")){
						purchase_status = "待收货";
					}else if(purchase_status.equals("1")){
						purchase_status = "已完成";
					}else if(purchase_status.equals("2")){
						purchase_status = "已取消";
					}
					
					//支付状态：支付状态：0全部支付 1欠款
					if(pay_status.equals("1")){
						pay_status = "欠款";
					}else if(pay_status.equals("0")){
						pay_status = "已结清";
					}
					
					return new String[] {self_purchase_unique,supplier_name,supplier_phone,supplier_address,create_time,
										total_price,total_count,purchase_status,pay_status,pay_money.toString()};
				}

				public String getTitle() {
					return fileName;
				}

				public int[] getColumnsWidth() {
					return new int[] {80 * 80, 80 * 80,80 * 80,80 * 80,80 * 80,
									 80 * 80, 80 * 80,80 * 80,80 * 80,80 * 80,
									 80 * 80};
				}

				public String[] getColumnsName() {
					return new String[] {"订单编号","供货商名称","联系电话","供货商地址","下单时间",
										"订单总金额","商品总数量","订单状态","付款状态","已支付金额"};
				}

				public int[][] MergedRegion() {
					// TODO Auto-generated method stub
					return null;
				}

				public List<Object[]> getheaderValue() {
					// TODO Auto-generated method stub
					return null;
				}
			});
			os.flush();
		} catch (IOException e) {
			e.printStackTrace();
		}
	}
}
