package org.haier.shop.controller;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;

import org.haier.shop.service.AdService;
import org.haier.shop.util.PurResult;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

@RequestMapping("/ad")
@Controller
public class AdController {
	
	@Resource
	private AdService adService;
	
	/**
	 * 跳转广告列表页面
	 * @return
	 */
	@RequestMapping("/adListPage.do")
	public String adListPage(){
		return "/WEB-INF/ad/adList.jsp";
	}
	
	/**
	 * 查询广告列表
	 * @return
	 */
	@RequestMapping("/adList.do")
	@ResponseBody
	public PurResult adList(
			String adMessage,
			@RequestParam(value = "page", defaultValue = "1") int page,
			@RequestParam(value = "limit", defaultValue = "8") int pageSize){
		Map<String,Object> params = new HashMap<String, Object>();
		params.put("adMessage", adMessage);
		params.put("pageSize", pageSize);
		params.put("startNum", (page-1)*pageSize);
		return adService.queryAdList(params);
	}
	
	
	/**
	 * 跳转添加广告页面
	 * @return
	 */
	@RequestMapping("/addAdPage.do")
	public String addAdPage(){
		return "/WEB-INF/ad/addAd.jsp";
	}
	
	/**
	 * 添加广告
	 * @return
	 */
	@RequestMapping("/addAd.do")
	@ResponseBody
	public PurResult addAd(HttpServletRequest request){
		return adService.addAd(request);
	}
	
	/**
	 * 跳转修改广告页面
	 * @return
	 */
	@RequestMapping("/updateAdPage.do")
	public String updateAdPage(String sys_ad_id,Model model){
		//获取广告详情
		Map<String ,Object> adDetail = adService.queryAdDetail(sys_ad_id);
		model.addAttribute("adDetail", adDetail);
		return "/WEB-INF/ad/updateAd.jsp";
	}
	
	/**
	 * 修改广告
	 * @return
	 */
	@RequestMapping("/updateAd.do")
	@ResponseBody
	public PurResult updateAd(HttpServletRequest request){
		return adService.updateAd(request);
	}
	
	/**
	 * 跳转广告详情页面
	 * @return
	 */
	@RequestMapping("/adDetails.do")
	public String adDetails(String sys_ad_id,Model model){
		//获取广告详情
		Map<String ,Object> adDetail = adService.queryAdDetail(sys_ad_id);
		//获取广告投放详情列表
		List<Map<String ,Object>> adLogList = adService.queryAdLogList(sys_ad_id);
		adDetail.put("adLogList", adLogList);
		model.addAttribute("adDetail", adDetail);
		return "/WEB-INF/ad/adDetails.jsp";
	}
	
	/**
	 * 修改广告状态
	 * @return
	 */
	@RequestMapping("/updateAdStatus.do")
	@ResponseBody
	public PurResult updateAdStatus(String sys_ad_id,String ad_status){
		Map<String,Object> params = new HashMap<String, Object>();
		params.put("sys_ad_id", sys_ad_id);
		params.put("ad_status", ad_status);
		return adService.updateAdStatus(params);
	}
	
	/**
	 * 删除广告
	 * @return
	 */
	@RequestMapping("/deleteAd.do")
	@ResponseBody
	public PurResult deleteAd(String sys_ad_id,HttpServletRequest request){
		return adService.deleteAd(sys_ad_id,request);
	}
	
	/**
	 * 跳转添加区域页面
	 * @return
	 */
	@RequestMapping("/addAdAreaPage.do")
	public String auth(){
		return "/WEB-INF/ad/addArea.jsp";
	}
	
	/**       
	* 获取区域列表
	* @return
	*/
	@RequestMapping(value="/areaList.do", method=RequestMethod.GET)
	@ResponseBody
	public PurResult areaList(String code,Model model){
		PurResult result = new PurResult();
		try {
			//获取所有省份
			List<Map<String ,Object>> provinceList = adService.queryAreaDictList("0");
			
			//获取所有的城市和区域
			List<Map<String ,Object>> dictList = adService.queryAreaDictList(null);
        	
			//存储json结构菜单
   			List<Map<String,Object>> permsList = new ArrayList<Map<String,Object>>();
   			for(int i=0;i<provinceList.size();i++) {
   				Map<String,Object> provinceMap = new HashMap<String,Object>();
   				provinceMap.put("name", provinceList.get(i).get("area_dict_content"));
   				provinceMap.put("checked", false);
   				
   				//获取该省份下城市
   				String province_dict_num = (String) provinceList.get(i).get("area_dict_num");
   				List<Map<String ,Object>> cityList = new ArrayList<Map<String,Object>>();
   				for(int j=0;j<dictList.size();j++) {
   					if(province_dict_num.equals(dictList.get(j).get("area_dict_parent_num"))) {
   						cityList.add(dictList.get(j));
   					}
   				}
   				
   				List<Map<String ,Object>> permsList2 = new ArrayList<Map<String,Object>>();
   				for(int j=0;j<cityList.size();j++) {
   					Map<String,Object> cityMap = new HashMap<String,Object>();
   					cityMap.put("name", cityList.get(j).get("area_dict_content"));
   					cityMap.put("checked", false);
   					
   					//获取该城市下区域
   	   				String city_dict_num = (String) cityList.get(j).get("area_dict_num");
   	   				List<Map<String ,Object>> areaList = new ArrayList<Map<String,Object>>();
   	   				for(int m=0;m<dictList.size();m++) {
   	   					if(city_dict_num.equals(dictList.get(m).get("area_dict_parent_num"))) {
	   	   					Map<String,Object> areaMap = new HashMap<String,Object>();
		   	   				areaMap.put("name", dictList.get(m).get("area_dict_content"));
		   	   				areaMap.put("value", provinceList.get(i).get("area_dict_num")+","+cityList.get(j).get("area_dict_num")+","+dictList.get(m).get("area_dict_num")
		   	   									+","+provinceList.get(i).get("area_dict_content")+cityList.get(j).get("area_dict_content")+dictList.get(m).get("area_dict_content"));
		   	   				areaMap.put("checked", false);
		   	   				areaList.add(areaMap);
   	   					}
   	   				}
   	   				cityMap.put("list", areaList);
   	   				permsList2.add(cityMap);
   				}
   				provinceMap.put("list", permsList2);
   	   			permsList.add(provinceMap);	
   			}
			
        	result.setStatus(1);
			result.setMsg("成功");
			result.setData(permsList);
		} catch (Exception e) {
			e.printStackTrace();
			result.setStatus(0);
			result.setMsg("异常");
		}
		return result;
	}
	
	/**
	 * 跳转选择优惠券页面
	 * @return
	 */
	@RequestMapping("/addCouponPage.do")
	public String addCouponPage(){
		return "/WEB-INF/ad/addCoupon.jsp";
	}
}
