package org.haier.shop.controller;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import net.sf.json.JSONArray;
import org.haier.customer.entity.Shops;
import org.haier.shop.entity.importMsg.ImportSupplierGoods;
import org.haier.shop.entity.importMsg.ImportSupplierGoodsDetail;
import org.haier.shop.service.ImportService;
import org.haier.shop.util.MyException;
import org.haier.shop.util.ShopsResult;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

@Controller
@RequestMapping("/import")
public class ImportController {
	@Resource
	private ImportService importService;

	@RequestMapping("/toImportGoodsSupplier.do")
	public String toImportGoodsSupplier(){
		return "/WEB-INF/import/importGoodsSupplier.jsp";
	}

	@RequestMapping("/queryGoodsMsg.do")
	@ResponseBody
	public ShopsResult queryGoodsMsg(String msg,Integer page, Integer limit, String shopUnique){
		ShopsResult shopsResult = new ShopsResult();

		List<String> list = new ArrayList<>();

		JSONArray ja = JSONArray.fromObject(msg);

		for(Integer i = (page -1) * limit; (i < page * limit) && (i < ja.size()); i++){
			list.add(ja.getJSONObject(i).getString("goodsBarcode"));
		}

		return importService.queryGoodsMsg(list, shopUnique, ja.size());
	}
	@RequestMapping("/updateGoodsSupplier.do")
	@ResponseBody
	public ShopsResult updateGoodsSupplier(String msg, String shopUnique){
		ShopsResult sr = new ShopsResult(1, "操作成功");
		JSONArray ja = JSONArray.fromObject(msg);

		ImportSupplierGoods importSupplierGoods = new ImportSupplierGoods();
		importSupplierGoods.setShopUnique(shopUnique);
		importSupplierGoods.setGoodsList(null);

		List<ImportSupplierGoodsDetail> list = new ArrayList<>();
		for(Integer i = 0; i < ja.size(); i++){
			ImportSupplierGoodsDetail goodsDetail = new ImportSupplierGoodsDetail();
			goodsDetail.setGoodsBarcode(ja.getJSONObject(i).getString("goodsBarcode"));
			goodsDetail.setSupplierCode(ja.getJSONObject(i).getString("supplierCode"));

			list.add(goodsDetail);
		}
		importSupplierGoods.setGoodsList(list);

		return importService.updateGoodsSupplier(importSupplierGoods);
	}
	
	@RequestMapping("/importGoods.do")
	@ResponseBody
	public ShopsResult ImportGoods(HttpServletRequest request,
			HttpServletResponse response,
			@RequestParam(value="shopUnique")String shopUnique,
			@RequestParam(value="sheetName",defaultValue="goods")String sheetName){
		Map<String,Object> datas=new HashMap<String, Object>();
		datas.put("shopUnique",shopUnique);
		datas.put("fileName", "商品导入");
		datas.put("sheetName", sheetName);
		return importService.goodsImport(request, response, datas);
	}
	
	@RequestMapping("/importGoodsKind.do")
	@ResponseBody
	public ShopsResult importGoodsKind(
			HttpServletRequest request,
			HttpServletResponse response,
			@RequestParam(value="shopUnique")String shopUnique,
			@RequestParam(value="sheetName",defaultValue="goods")String sheetName
			) {
		Map<String,Object> datas=new HashMap<String, Object>();
		datas.put("shopUnique",shopUnique);
		datas.put("fileName", "商品导入");
		datas.put("sheetName", sheetName);
		
		return importService.importGoodsKind(request, response, datas);
	}
	/**
	 *商品调价导入 
	 */
	@RequestMapping("/importGoodsModifyPrice.do")
	@ResponseBody
	public ShopsResult importGoodsModifyPrice(HttpServletRequest request,
			HttpServletResponse response,
			@RequestParam(value="shopUnique")String shopUnique,
			@RequestParam(value="sheetName",defaultValue="goods")String sheetName){
		Map<String,Object> datas=new HashMap<String, Object>();
		datas.put("shopUnique",shopUnique);
		datas.put("fileName", "商品导入");
		datas.put("sheetName", "Sheet1");
		return importService.importGoodsModifyPrice(request, response, datas);
	}
	
	/**
	 * 会员信息导入
	 * @param request
	 * @param response
	 * @param shopUnique
	 * @param sheetName
	 * @return
	 */
	@RequestMapping("/importCustomer.do")
	@ResponseBody
	public ShopsResult importCustomer(HttpServletRequest request,HttpServletResponse response,
			@RequestParam(value="shopUnique")String shopUnique,
			@RequestParam(value="sheetName",defaultValue="customer")String sheetName,
			String saleListCashier
			){
		Map<String,Object> maps=new HashMap<String, Object>();
		response.setHeader("Access-Control-Allow-Origin", "*");
		maps.put("shopUnique",shopUnique);
		maps.put("fileName", "会员导入");
		maps.put("sheetName", sheetName);
//		System.out.println(maps);
		return importService.customerImport(request, response, maps,saleListCashier);
	}
	
	
	@RequestMapping("/importSaleList.do")
	@ResponseBody
	public ShopsResult importSaleList(HttpServletRequest request
			,HttpServletResponse response
			,@RequestParam(value = "shopUnique")String shopUnique
			,@RequestParam(value="sheetName",defaultValue="saleList")String sheetName
//			,@RequestParam(value="staffId") Integer staffId
			){
		Map<String,Object> datas=new HashMap<String, Object>();
		datas.put("shopUnique",shopUnique);
		datas.put("fileName", "订单导入");
		datas.put("sheetName", sheetName);
//		datas.put("staffId", staffId);
		try{
			
			return importService.importSaleList(request, response, datas);
		}catch(MyException e){
			return new ShopsResult(e.getStatus(), e.getMsg());
		}
	}
}
