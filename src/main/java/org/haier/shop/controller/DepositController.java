package org.haier.shop.controller;

import java.util.HashMap;
import java.util.Map;

import javax.annotation.Resource;

import org.haier.shop.service.DepositService;
import org.haier.shop.util.PurResult;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

@Controller
@RequestMapping("/deposit")
public class DepositController {
	
	@Resource
	private DepositService depositService;

	/**
	 * 寄存详情页面
	 * @return
	 */
	@RequestMapping("/queryDepositPage.do")
	public String queryDepositPage(String deposit_id,Model model){
		model.addAttribute("deposit_id",deposit_id);
		return "/WEB-INF/goods/goodsDepositDetail.jsp";
	}
	
	/**
	 * 查询寄存详情
	 * @return
	 */
	@RequestMapping("/queryDeposit.do")
	@ResponseBody
	public PurResult queryDeposit(
			@RequestParam(value="shop_unique")String shop_unique,
			@RequestParam(value="deposit_id")String deposit_id
			){
		Map<String,Object> params = new HashMap<String, Object>();
		params.put("shop_unique", shop_unique);
		params.put("deposit_id",deposit_id);
		return depositService.queryDeposit(params);
	}
}
