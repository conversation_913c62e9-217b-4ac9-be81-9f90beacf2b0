package org.haier.shop.controller;

import java.io.IOException;
import java.io.OutputStream;
import java.net.URLEncoder;
import java.sql.Timestamp;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import com.alibaba.fastjson.JSON;
import org.haier.log.annotation.RemoteLog;
import org.haier.log.enums.BusinessType;
import org.haier.shop.dao.GoodsDao;
import org.haier.shop.entity.GoodsInfo;
import org.haier.shop.entity.PurListMain;
import org.haier.shop.service.PurchaseListService;
import org.haier.shop.util.HandleMessyCode;
import org.haier.shop.util.LoadOutObjectXLSUtil;
import org.haier.shop.util.PurResult;
import org.haier.shop.util.ServletsUtil;
import org.haier.shop.util.ShopsResult;
import org.haier.shop.util.StringUtil;
import org.haier.shop.util.XLSCallBack;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;


/**
 * 进货订单查询
 * 	@param shop_unique
 * @param orderMessage
 * @param startTime
 * @param endTime
 * @param order
 * @param orderType
 * @param pageNum
 * @param pageSize
 * @param receipt_status
 * @param paystatus
 * @return
 */

@Controller
@RequestMapping("/html/purchase")
public class PurchaseListController {
	@Resource
	private PurchaseListService purService;
	
	@Resource
	private GoodsDao goodsDao;
	
	@RequestMapping("/queryPurchaseListPage.do")
	public String queryPurchaseListPage(){
		return "/WEB-INF/purchase/queryPurchaseList.jsp";
	}
	
	@RequestMapping("/modifyGoodsSupplier.do")
	@ResponseBody
	public ShopsResult modifyGoodsSupplier(String supplierUnique,String goodsIds) {
		try {
			return purService.modifyGoodsSupplier(supplierUnique, goodsIds);
		}catch (Exception e) {
			ShopsResult sr = new ShopsResult(0,"更新失败");
			return sr;
		}
	}
	/**
	 调拨单记录列表页面
	 * @return
	 */
	@RequestMapping("/queryAllorationListPage.do")
	public String  queryAllorationListPage(){
		return "/WEB-INF/goods/allorationList.jsp";
	}
	/**
	跳到新增调拨单页面
	 * @return
	 */
	@RequestMapping("/addAllorationPage.do")
	public String  addAllorationPage(){
		return "/WEB-INF/goods/addAlloration.jsp";
	}
	/**
	 * 查询调拨单列表
	 * @param supplier_unique
	 * @param uniques
	 * @return
	 */
	@ResponseBody
	@RequestMapping("/queryAllorationList.do")
	public PurResult queryAllorationList(HttpServletRequest request,
			@RequestParam(value="page",defaultValue="1")Integer page,
			@RequestParam(value="limit",defaultValue="15")Integer limit){		
		PurResult result = new PurResult();
		try {
			Map<String, Object> params = HandleMessyCode.handle(ServletsUtil.getParameters(request));
			params.put("limit", limit);
			params.put("page", page);
			result=purService.queryAllorationList(params,request);
		} catch (Exception e) {
			result.setStatus(0);
			result.setMsg("查询失败!");	
			e.printStackTrace();
		}	
		return result;
	}
	
	/**
	 * 调拨单导出excel
	 */
	@RequestMapping("/exportAllorationExcel.do")
	public void exportAllorationExcel(HttpServletRequest request, HttpServletResponse response) throws Exception{
		Map<String, Object> params = HandleMessyCode.handle(ServletsUtil.getParameters(request));
		List<Map<String,Object>> list=purService.getAllorationList(params);	
		String[] titles=new String[] {
				"调拨单编号","调拨日期","调出仓库","调入仓库","调拨数量","调拨金额","操作人"
		};
		loadDiaoBoXLS(list,"调拨商品",request,response,titles);
	}
	
	/**
	 * 查询入库详情页面
	 * @param purchase_list_unique
	 * @return
	 */
	@RequestMapping("/queryStorageDetailPage.do")
	public String queryStorageDetailPage(
			@RequestParam(value="purchase_list_unique",required=true)String purchase_list_unique,HttpServletRequest request){
		request.setAttribute("purchase_list_unique", purchase_list_unique);
		return "/WEB-INF/goods/allorationDetail.jsp";
	}
	/**
	 * 打印入库详情页面
	 * @param purchase_list_unique
	 * @return
	 */
	@RequestMapping("/queryStorageDetailPrintPage.do")
	public String queryStorageDetailPrintPage(
			@RequestParam(value="purchase_list_unique",required=true)String purchase_list_unique,HttpServletRequest request){
		request.setAttribute("purchase_list_unique", purchase_list_unique);
		return "/WEB-INF/goods/allorationDetailPrint.jsp";
	}
	/**
	 * 查询订单详情
	 * @param supplier_unique
	 * @param purchase_list_unique
	 * @return
	 */
	@RequestMapping("/queryAllorationDetail.do")
	@ResponseBody
	public PurResult queryStorageDetail(
			@RequestParam(value="purchase_list_unique",required=true)String purchase_list_unique){
		return purService.queryAllorationDetail(purchase_list_unique);
	}
	@RequestMapping("/queryOrderDetailPage.do")
	public String queryOrderDetailPage(String shop_unique, String purchase_list_unique,HttpServletRequest request){
		request.setAttribute("shop_unique", shop_unique);
		request.setAttribute("purchase_list_unique", purchase_list_unique);
		return "/WEB-INF/purchase/purchaseDetail.jsp";
	}
	@RequestMapping("/queryPurchaseDetailPrintPage.do")
	public String queryPurchaseDetailPrintPage(String shop_unique, String purchase_list_unique,HttpServletRequest request){
		request.setAttribute("shop_unique", shop_unique);
		request.setAttribute("purchase_list_unique", purchase_list_unique);
		return "/WEB-INF/purchase/purchaseDetailPrint.jsp";
	}
	
	/**
	 * 进货订单查询
	 * @param shop_unique
	 * @param orderMessage
	 * @param startTime
	 * @param endTime
	 * @param order
	 * @param orderType
	 * @param pageNum
	 * @param pageSize
	 * @param receipt_status
	 * @param paystatus
	 * @return
	 */
	@RequestMapping("queryPurLists.do")
	@ResponseBody
	public ShopsResult queryPurLists(
			@RequestParam(value="shop_unique",required=true)String shop_unique,
			String orderMessage,
			Timestamp startTime,
			Timestamp endTime,
			@RequestParam(value="orderName" ,defaultValue="sale_list_datetime")String orderName, 
			@RequestParam(value="orderType",defaultValue="desc")String orderType,
			@RequestParam(value="pageNum",defaultValue="1")Integer pageNum, 
			@RequestParam(value="pageSize",defaultValue="8")Integer pageSize,
			Integer receipt_status,
			Integer paystatus){
		return purService.queryPurLists(shop_unique, orderMessage, startTime, endTime, orderName, orderType, pageNum, pageSize, receipt_status, paystatus);
	}
	@RequestMapping("queryGoodsByPage.do")
	@ResponseBody
	public PurResult queryGoodsByPage(
			@RequestParam(value="managerUnique",required=true)String managerUnique,
			@RequestParam(value="goodsMessage" ,defaultValue="")String goodsMessage, 
			@RequestParam(value="goods_kind_unique",defaultValue="-1")Long goods_kind_unique, 
			@RequestParam(value="goods_kind_parunique",defaultValue="-1")Long goods_kind_parunique,
			@RequestParam(value="pageNum",defaultValue="1")Integer pageNum, 
			@RequestParam(value="pageSize",defaultValue="8")Integer pageSize){
		return purService.queryGoodsByPage(managerUnique, goodsMessage, goods_kind_unique, goods_kind_parunique, pageNum, pageSize);
	}

	@RemoteLog(title = "新增调拨", businessType = BusinessType.INSERT)
	@RequestMapping("submitAllorationStorage.do")
	@ResponseBody
	public PurResult submitAllorationStorage(
			@RequestParam(value="shop_unique",required=true)String shop_unique,
			@RequestParam(value="detailJson" ,defaultValue="")String detailJson, 
			@RequestParam(value="purchase_list_remark",defaultValue="")String purchase_list_remark, 
			@RequestParam(value="user_id",defaultValue="")String user_id,
			@RequestParam(value="user_name",defaultValue="")String user_name,
			@RequestParam(value="storehouse_id_in",defaultValue="-1")String storehouse_id_in)
	{
		PurResult result = new PurResult();
		try {
			result=purService.addAllorationStorage(shop_unique, detailJson,purchase_list_remark,storehouse_id_in,user_id,user_name);
			System.err.println(JSON.toJSONString(result));
		} catch (Exception e) {
			result.setStatus(0);
			result.setMsg("查询失败!");
			e.printStackTrace();
		}		
		return result;
	}
	/**
	 * 订单详情查询
	 * @param shop_unique
	 * @param purchase_list_unique
	 * @return
	 */
	@RequestMapping("queryOrderDetail.do")
	@ResponseBody
	public ShopsResult queryOrderDetail(
			@RequestParam(value="shop_unique",required=true)String shop_unique,String purchase_list_unique){
		return purService.queryOrderDetail(shop_unique, purchase_list_unique);
	}
	
	/**
	 * 更新购物车商品
	 * @param shop_unique 店铺编号
	 * @param goods_barcode 商品条码
	 * @param count 变换数量
	 * @return
	 */
	@RequestMapping("/updateCartGoodsCount.do")
	@ResponseBody
	public ShopsResult updateCartGoodsCount(
			@RequestParam(value="shop_unique",required=true)String shop_unique,String goods_barcode,Integer count,String supplier_unique){
		return purService.updateCartGoodsCount(shop_unique, goods_barcode, count,supplier_unique);
	}
	
	/**
	 * 供货商购物车订单详情
	 * @param shop_unique
	 * @return
	 */
	@RequestMapping("/queryPurCartGoods.do")
	@ResponseBody
	public ShopsResult queryPurCartGoods(
			@RequestParam(value="shop_unique",required=true)String shop_unique){
		return purService.queryPurCartGoods(shop_unique);
	}
	
	/**
	 * 更新购物车商品数量
	 * @param shop_unique 店铺编号
	 * @param purchase_list_parunique 购物车编号
	 * @param goods_barcode 商品条码
	 * @param count 修改后的商品数量
	 * @return
	 */
	@RequestMapping("/modifyCartDetail.do")
	@ResponseBody
	public ShopsResult modifyCartDetail(
			@RequestParam(value="shop_unique",required=true)String shop_unique,String purchase_list_parunique,String goods_barcode,Double count){
		return purService.modifyCartDetail(shop_unique, purchase_list_parunique, goods_barcode, count);
	}
	
	/**
	 * 移除购物车商品
	 * @param shop_unique 供货商编号
	 * @param purchase_list_parunique 购物车编号
	 * @param goods_barcode 商品条码
	 * @return
	 */
	@RequestMapping("/deleteFromCart.do")
	@ResponseBody
	public ShopsResult deleteFromCart(
			@RequestParam(value="shop_unique",required=true)String shop_unique,String purchase_list_parunique,String goods_barcode){
		return purService.deleteFromCart(shop_unique, purchase_list_parunique, goods_barcode);
	}
	
	/**
	 * @param shop_unique
	 * @param goodsBarcodes
	 * @param purchase_list_parunique
	 * @return
	 */
	@RequestMapping("/toSubmitCart.do")
	@ResponseBody
	public ShopsResult toSubmitCart(
			@RequestParam(value="shop_unique",required=true)String shop_unique,String[] goodsBarcodes,String purchase_list_parunique){
		return purService.toSubmitCart(shop_unique, goodsBarcodes, purchase_list_parunique);
	}
	
	@ResponseBody
	@RequestMapping("/submitGoods.do")
	/**
	 * 提交采购订单
	 * @param request
	 * @param shop_unique
	 * @param goodsBarcodes
	 * @param uniques
	 * @return
	 */
	public ShopsResult submitGoods( 
			@RequestParam(value="shop_unique",required=true)String shop_unique,String[] goodsBarcodes,String[] uniques,String purchase_list_parunique){
		return purService.submitGoods(shop_unique,goodsBarcodes,uniques,purchase_list_parunique);
	}
	
	/**
	 * 快速采购库存不足的商品
	 * @param shop_unique
	 * @param stockType
	 * @return
	 */
	@RequestMapping("/easyPurchase.do")
	@ResponseBody
	public ShopsResult easyPurchase(
			@RequestParam(value="shop_unique",required=true)String shop_unique,
			@RequestParam(value="stockType",required=true,defaultValue="1")Integer stockType){
		return purService.easyPurchase(shop_unique, stockType);
	}
	/**
	 * 下载进货订单excel表
	 * @param map
	 * @param shopUnique
	 * @return
	 */
	@RequestMapping("/purListExcel.do")
	@ResponseBody
	public void purListExcel(
			@RequestParam(value="shopUnique",required=true)String shopUnique,
			String orderMessage,
			Timestamp startTime,
			Timestamp endTime,
			Integer payStatus,
			Integer receiptStatus,
			HttpServletRequest request,
			HttpServletResponse response
			){
		Map<String,Object> map=new HashMap<String, Object>();
		map.put("shopUnique", shopUnique);
		map.put("startTime", startTime);
		map.put("endTime", endTime);
		if(payStatus!=-1){
			map.put("payStatus", payStatus);
		}
		if(receiptStatus!=-1){
			map.put("receiptStatus", receiptStatus);
		}
		if(orderMessage!=null&&!"".equals(orderMessage)){
			map.put("orderMessage", "%"+orderMessage+"%");
		}
		List<PurListMain> data= purService.purListExcel(map, shopUnique, request);
		loadOutClaimXLS(data,"进货记录Excel",request,response);
	}
	
	public void loadDiaoBoXLS(List<Map<String, Object>> caseVoList, final String string,HttpServletRequest request,HttpServletResponse response,final String[] titles){
		try {
			response.setContentType(
					"application/x-msdownload; charset=UTF-8");// 告知浏览器文件类型
			String agent = request.getHeader("user-agent");
			if (agent.contains("Firefox")) {
				response.setHeader(
						"Content-Disposition",
						"attachment;filename="
								+ new String((string + ".xls")
										.getBytes("GB2312"), "ISO-8859-1"));// 设置文件头
			} else {
				response.setHeader(
						"Content-Disposition",
						"attachment;filename="
								+ URLEncoder.encode(string + ".xls", "UTF-8"));// 设置文件头
			}
			OutputStream os = response.getOutputStream();
			LoadOutObjectXLSUtil<Map<String, Object>> objectXLS = new LoadOutObjectXLSUtil<Map<String, Object>>();
			objectXLS.generateXLS(os, caseVoList, new XLSCallBack<Map<String, Object>>() {
				public Object[] getValues(Map<String, Object> tt) {
					String str = "";
					
					String purchase_list_unique = tt.get("purchase_list_unique")+"";
					if (StringUtil.blank(purchase_list_unique)) {
						purchase_list_unique = str;
					}
					String purchase_list_date =  tt.get("purchase_list_date")+"";
					if (StringUtil.blank(purchase_list_date)) {
						purchase_list_date = str;
					}
					String storehouse_name_out =tt.get("storehouse_name_out")+"";
					if (StringUtil.blank(storehouse_name_out)) {
						storehouse_name_out = str;
					}
					String storehouse_name_in = tt.get("storehouse_name_in")+"";
					if (StringUtil.blank(storehouse_name_in)) {
						storehouse_name_in = str;
					}
					String good_count = tt.get("good_count")+"";
					if (StringUtil.blank(good_count)) {
						good_count = str;
					}
					String good_amt = tt.get("good_amt")+"";
					if (StringUtil.blank(good_amt)) {
						good_amt = str;
					}
					String staff_name = tt.get("staff_name")+"";
					if (StringUtil.blank(staff_name)) {
						staff_name = str;
					}					
					
					return new String[] {purchase_list_unique,purchase_list_date,storehouse_name_out,
							storehouse_name_in,good_count,good_amt,staff_name
					};
				}

				public String getTitle() {
					return string;
				}

				public int[] getColumnsWidth() {
					return new int[] { 
										  80 * 80, 80 * 80,80 * 80,80 * 80,
										  80 * 80, 80 * 80,80 * 80,80 * 80,
										  80 * 80,80 * 80,80 * 80};
				}

				public String[] getColumnsName() {
					return titles;
				}

				public int[][] MergedRegion() {
					// TODO Auto-generated method stub
					return null;
				}

				public List<Object[]> getheaderValue() {
					// TODO Auto-generated method stub
					return null;
				}

			});
			os.flush();
		} catch (IOException e) {
			e.printStackTrace();
		}
	}
	
	public void loadOutClaimXLS(List<PurListMain> caseVoList, final String string,HttpServletRequest request,HttpServletResponse response) {
		try {
			response.setContentType(
					"application/x-msdownload; charset=UTF-8");// 告知浏览器文件类型
			String agent = request.getHeader("user-agent");
			if (agent.contains("Firefox")) {
				response.setHeader(
						"Content-Disposition",
						"attachment;filename="
								+ new String((string + ".xls")
										.getBytes("GB2312"), "ISO-8859-1"));// 设置文件头
			} else {
				response.setHeader(
						"Content-Disposition",
						"attachment;filename="
								+ URLEncoder.encode(string + ".xls", "UTF-8"));// 设置文件头
			}
			OutputStream os = response.getOutputStream();
			LoadOutObjectXLSUtil<PurListMain> objectXLS = new LoadOutObjectXLSUtil<PurListMain>();
			objectXLS.generateXLS(os, caseVoList, new XLSCallBack<PurListMain>() {
				public Object[] getValues(PurListMain tt) {
					String str = "";
					
					String purchase_list_unique ="";
						purchase_list_unique=tt.getPurListUnique().toString();
					String supplierName = tt.getSupplierName();
					if (StringUtil.blank(supplierName)) {
						supplierName = str;
					}
					String purListDate = tt.getPurListDate();
					if (StringUtil.blank(purListDate)) {
						purListDate = str;
					}
					String purListTotal = tt.getPurListTotal().toString();
					if (StringUtil.blank(purListTotal)) {
						purListTotal = str;
					}
					String purListSum = tt.getPurListSum().toString();
					if (StringUtil.blank(purListSum)) {
						purListSum = str;
					}
					String payStatus = tt.getPayStatus();
					if (StringUtil.blank(payStatus)) {
						payStatus = str;
					}
					String receiptStatus = tt.getReceiptStatus();
					if (StringUtil.blank(receiptStatus)) {
						receiptStatus = str;
					}
					String payMethod = tt.getPayMethod();
					if (StringUtil.blank(payMethod)) {
						payMethod = str;
					}
					
					return new String[] {purchase_list_unique,supplierName,purListDate,purListTotal,
										 purListSum,payStatus,receiptStatus,payMethod};
				}

				public String getTitle() {
					return string;
				}

				public int[] getColumnsWidth() {
					return new int[] { 
										  80 * 80, 80 * 80,80 * 80,80 * 80,
										  80 * 80, 80 * 80,80 * 80,80 * 80};
				}

				public String[] getColumnsName() {
					return new String[] { "订单编号","供货商名称","下单时间","订单总金额",
										  "商品总数量","支付状态","处理状态","付款方式"	};
				}

				public int[][] MergedRegion() {
					// TODO Auto-generated method stub
					return null;
				}

				public List<Object[]> getheaderValue() {
					// TODO Auto-generated method stub
					return null;
				}

			});
			os.flush();
		} catch (IOException e) {
			e.printStackTrace();
		}
	}
	
	
	/**
	 * @param map
	 * @param shopUnique
	 * @return
	 */
	@RequestMapping("/printPage.do")
	public String printPage(
			@RequestParam(value="shopUnique",required=true)String shopUnique,
			String orderMessage,
			Timestamp startTime,
			Timestamp endTime,
			Integer payStatus,
			Integer receiptStatus,
			HttpServletRequest request){
		request.setAttribute("shopUnique", shopUnique);
		request.setAttribute("startTime", startTime);
		request.setAttribute("endTime", endTime);
		request.setAttribute("orderMessage", orderMessage);
		request.setAttribute("payStatus", payStatus);
		request.setAttribute("receiptStatus", receiptStatus);
		return "/html/purchase/printPurchase.jsp";
	}
	/**
	 * 打印PDF
	 * @param map
	 * @param shopUnique
	 * @return
	 */
	@RequestMapping("/printPurList.do")
	@ResponseBody
	public ShopsResult printPurList(
			@RequestParam(value="shopUnique",required=true)String shopUnique,
			String orderMessage,
			Timestamp startTime,
			Timestamp endTime,
			Integer payStatus,
			Integer receiptStatus,
			HttpServletRequest request){
		Map<String,Object> map=new HashMap<String, Object>();
		map.put("shopUnique", shopUnique);
		map.put("startTime", startTime);
		map.put("endTime", endTime);
		if(payStatus!=-1){
			map.put("payStatus", payStatus);
		}
		if(receiptStatus!=-1){
			map.put("receiptStatus", receiptStatus);
		}
		return purService.printPurList(map, shopUnique, request);
	}
	
	@RequestMapping("/goodsManagerPage.do")
	public String goodsManagerPage(HttpServletRequest request){
		return "/WEB-INF/goods/goodList.jsp";
	}
	@RequestMapping("/goodsManagerPage_wj.do")
	public String goodsManagerPage_wj(HttpServletRequest request){
		return "/WEB-INF/goods/goodList_wj.jsp";
	}
	/**
	 * 因全球精选添加的商品列表
	 * @param request
	 * @return
	 */
	@RequestMapping("/goodsManagerPtPage.do")
	public String goodsManagerPtPage(HttpServletRequest request){
		return "/WEB-INF/goods/goodListPt.jsp";
	}
	@RequestMapping("/goodsStockRecordPage.do")
	public String goodsStockRecordPage(HttpServletRequest request){
		return "/WEB-INF/goods/goodsStockRecord.jsp";
	}
	
	@RequestMapping("/printRecordPage.do")
	public String printRecordPage(
			@RequestParam(value="shopUnique",required=true)String shopUnique,
			String goodsMessage,
			Timestamp startTime,
			Timestamp endTime,
			String parUnique,
			String kindUnique,
			String stockType,
			String stockResource,
			HttpServletRequest request){
		request.setAttribute("shopUnique", shopUnique);
		request.setAttribute("startTime", startTime);
		request.setAttribute("endTime", endTime);
		request.setAttribute("goodsMessage", goodsMessage);
		request.setAttribute("parUnique", parUnique);
		request.setAttribute("kindUnique", kindUnique);
		request.setAttribute("stockType", stockType);
		request.setAttribute("stockResource", stockResource);
		return "/html/goods/printRecord.jsp";
	}
	
	@RequestMapping("/printRecordList.do")
	@ResponseBody
	public ShopsResult printRecordList(
			@RequestParam(value="shopUnique",required=true)String shopUnique,
			String goodsMessage,
			Timestamp startTime,
			Timestamp endTime,
			String parUnique,
			String kindUnique,
			String stockType,
			String stockResource,
			HttpServletRequest request){
		Map<String,Object> map=new HashMap<String, Object>();
		map.put("shopUnique", shopUnique);
		map.put("startTime", startTime);
		map.put("endTime", endTime);
		
		if(!stockResource.equals("-1")){
			map.put("stockResource", stockResource);
		}
		if(!stockType.equals("-1")){
			map.put("stockType", stockType);
		}
		if(!parUnique.equals("-1")){
			map.put("parUnique", parUnique);
		}
		if(!kindUnique.equals("-1")){
			map.put("kindUnique", kindUnique);
		}
		
		if(goodsMessage!=null&&!"".equals(goodsMessage)){
			map.put("goodsMessage", "%"+goodsMessage+"%");
		}
		
		List<GoodsInfo> data = goodsDao.ExcelGoodsRecord(map);
		ShopsResult sr = new ShopsResult();
		sr.setData(data);
		sr.setStatus(1);
		sr.setMsg("查询成功！");
		return sr;
	}
	
	
	/**
	 * #############################     商品结构优化        #########################################################
	 */
	
	/**
	 * 跳转到商品优化界面
	 */
	@RequestMapping("/toGoodsTopPage.do")
	public String  toGoodsTopPage(){
		return "/WEB-INF/goods/goodsTopList.jsp";
	}
	
	/**
	 * 跳转到一键进货
	 */
	@RequestMapping("/toGoodTopOrder.do")
	public String  toGoodTopOrder(){
		return "/WEB-INF/goods/goodsTopOrder.jsp";
	}
	
	/**
	 * 跳转到平台-商品优化订单
	 */
	@RequestMapping("/toGoodsOrder.do")
	public String  toGoodsOrder(){
		return "/WEB-INF/goods/topOrderList.jsp";
	}
	
	/**
	 * 商品优化-本地热销
	 */
	@ResponseBody
	@RequestMapping("/getGoodsTopList.do")
	public PurResult getGoodsTopList(HttpServletRequest request,
			@RequestParam(value="page1",defaultValue="1")Integer page1,
			@RequestParam(value="limit1",defaultValue="15")Integer limit1){		
		PurResult result = new PurResult();
		try {
			Map<String, Object> params = HandleMessyCode.handle(ServletsUtil.getParameters(request));
			params.put("limit", limit1);
			params.put("page", (page1-1)*limit1);
			result=purService.getGoodsTopList(params);
		} catch (Exception e) {
			result.setStatus(0);
			result.setMsg("查询失败!");	
			e.printStackTrace();
		}	
		return result;
	}
	
	/**
	 * 商品优化-周边热销
	 */
	@ResponseBody
	@RequestMapping("/getGoodsNearbyList.do")
	public PurResult getGoodsNearbyList(HttpServletRequest request,
			@RequestParam(value="page1",defaultValue="1")Integer page1,
			@RequestParam(value="limit1",defaultValue="15")Integer limit1){		
		PurResult result = new PurResult();
		try {
			Map<String, Object> params = HandleMessyCode.handle(ServletsUtil.getParameters(request));
			params.put("limit", limit1);
			params.put("page", (page1-1)*limit1);
			result=purService.getGoodsNearbyList(params);
		} catch (Exception e) {
			result.setStatus(0);
			result.setMsg("查询失败!");	
			e.printStackTrace();
		}	
		return result;
	}
	
	/**
	 * 商品汰换列表
	 */
	@ResponseBody
	@RequestMapping("/getGoodsListNoSale.do")
	public PurResult getGoodsListNoSale(HttpServletRequest request,
			@RequestParam(value="page",defaultValue="1")Integer page,
			@RequestParam(value="limit",defaultValue="15")Integer limit){		
		PurResult result = new PurResult();
		try {
			Map<String, Object> params = HandleMessyCode.handle(ServletsUtil.getParameters(request));
			params.put("limit", limit);
			params.put("page", (page-1)*limit);
			result=purService.getGoodsListNoSale(params);
		} catch (Exception e) {
			result.setStatus(0);
			result.setMsg("查询失败!");	
			e.printStackTrace();
		}	
		return result;
	}
	
	/**
	 * 商品优化订单
	 */
	@ResponseBody
	@RequestMapping("/getGoodsTopOrderList.do")
	public PurResult getGoodsTopOrderList(HttpServletRequest request,
			@RequestParam(value="page",defaultValue="1")Integer page,
			@RequestParam(value="limit",defaultValue="15")Integer limit){		
		PurResult result = new PurResult();
		try {
			Map<String, Object> params = HandleMessyCode.handle(ServletsUtil.getParameters(request));
			params.put("limit", limit);
			params.put("page", (page-1)*limit);
			result=purService.getGoodsTopOrderList(params);
		} catch (Exception e) {
			result.setStatus(0);
			result.setMsg("查询失败!");	
			e.printStackTrace();
		}	
		return result;
	}
	
	
	/**
	 * 暂不需要
	 */
	@RequestMapping("addNoNeedGoods.do")
	@ResponseBody
	public PurResult addNoNeedGoods(HttpServletRequest request)
	{
		PurResult result = new PurResult();
		try {
			Map<String, Object> params = HandleMessyCode.handle(ServletsUtil.getParameters(request));
			result=purService.addNoNeedGoods(params);
		} catch (Exception e) {
			result.setStatus(0);
			result.setMsg("操作失败!");
			e.printStackTrace();
		}		
		return result;
	}
	
	@RequestMapping("addGoodsTopOrder.do")
	@ResponseBody
	public PurResult addGoodsTopOrder(HttpServletRequest request)
	{
		PurResult result = new PurResult();
		try {
			Map<String, Object> params = HandleMessyCode.handle(ServletsUtil.getParameters(request));
			result=purService.insertGoods_top_order(params);
		} catch (Exception e) {
			result.setStatus(0);
			result.setMsg("操作失败!");
			e.printStackTrace();
		}		
		return result;
	}
}
