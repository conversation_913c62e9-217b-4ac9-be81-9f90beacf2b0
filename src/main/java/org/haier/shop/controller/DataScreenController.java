package org.haier.shop.controller;

import java.util.HashMap;
import java.util.Map;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;

import org.haier.shop.service.DataScreenService;
import org.haier.shop.util.ShopsResult;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

@Controller
@RequestMapping("/data")
@CrossOrigin
public class DataScreenController {
	@Resource
	private DataScreenService dataService;
	
	@RequestMapping("/queryOnlineCount.do")
	@ResponseBody
	public ShopsResult queryOnlineCount() {
		try {
			return dataService.queryOnlineCount();
		}catch (Exception e) {
			return new ShopsResult(0, "查询失败！");
		}
	}
	
	@RequestMapping("/queryExpressCount.do")
	@ResponseBody
	public ShopsResult queryExpressCount() {
		try {
			return dataService.queryExpressCount();
		}catch (Exception e) {
			return new ShopsResult(0, "查询失败！");
		}
	}
	/**
	 * 活跃店铺信息查询益农版
	 * @return
	 */
	@RequestMapping("/getActiveShopsMessageYN.do")
	@ResponseBody
	public ShopsResult getActiveShopsMessageYN() {
		return dataService.getActiveShopsMessageYN();
	}
	
	@RequestMapping("/newShopsCountYN.do")
	@ResponseBody
	public ShopsResult newShopsCountYN() {
		return dataService.newShopsCountYN();
	}
	
	@RequestMapping("/top5GoodsMessage.do")
	@ResponseBody
	public ShopsResult top5GoodsMessage(){
		return dataService.top5GoodsMessage();
	}
	
	/**
	 * 各种支付方式占比
	 * @return
	 */
	@RequestMapping("/payTypeMessage.do")
	@ResponseBody
	public ShopsResult payTypeMessage(){
		return dataService.payTypeMessage();
	}
	
	/**
	 * 订单达成率查询
	 * @return
	 */
	@RequestMapping("/proportionOfOrder.do")
	@ResponseBody
	public ShopsResult proportionOfOrder(){
		return dataService.proportionOfOrder();
	}
	
	/**
	 * 今天昨天交易额
	 */
	@RequestMapping("/getYestodayOrder.do")
	@ResponseBody
	public ShopsResult getYestodayOrder(){
		return dataService.getYestodayOrder();
	}
	
	/**
	 * 用户服务评价
	* @return（展示方法参数和返回值）
	 */
	@RequestMapping("/queryEvaluateList.do")
	@ResponseBody
	public ShopsResult queryEvaluateList(){
		return dataService.queryEvaluateList();
	}
	
	/**
	 * 查询指定时间内每一秒的订单数量和订单总金额
	 * @return
	 */
	@RequestMapping("/getSaleListTotalMessageBySecond.do")
	@ResponseBody
	public ShopsResult getSaleListTotalMessageBySecond(){
		return dataService.getSaleListTotalMessageBySecond();
	}
	
	/**
	 * 相比昨日销售营业额对比
	 * @return
	 */
	@RequestMapping("/onLineSaleComparsionYesterday.do")
	@ResponseBody
	public ShopsResult onLineSaleComparsionYesterday(){
		return dataService.onLineSaleComparsionYesterday();
	}
	
	
	/**
	 * 销量前五十商品名称查询
	 * @return
	 */
	@RequestMapping("/top50characterCloud.do")
	@ResponseBody
	public ShopsResult top50characterCloud(){
		return dataService.top50characterCloud();
	}
	
	/**
	 * 系统当前时间
	 * @return
	 */
	@RequestMapping("/selectNow.do")
	@ResponseBody
	public ShopsResult selectNow(){
		return dataService.selectNow();
	}
	
	@RequestMapping("/getActiveShopsMessage.do")
	@ResponseBody
	public ShopsResult getActiveShopsMessage(
			String goods
			){
		return dataService.getActiveShopsMessage();
	}
	
	/**
	 * 当日流水及订单量
	 * @return
	 */
	@RequestMapping("/getDailyTurnover.do")
	@ResponseBody
	public ShopsResult getDailyTurnover(){
		return dataService.getDailyTurnover();
	}
	
	/**
	 * 本周新增店铺数量
	 */
	@RequestMapping("/newShopsCount.do")
	@ResponseBody
	public ShopsResult newShopsCount(){
		return dataService.newShopsCount();
	}
	
	/**
	 * 今日订单分时统计额及昨日订单分时统计额
	 * @return
	 */
	@RequestMapping("/orderTotalByHours.do")
	@ResponseBody
	public ShopsResult orderTotalByHours(Double num){
		return dataService.orderTotalByHours(num );
	}
	
	/**
	 * 最新订单信息
	 * @return
	 */
	@RequestMapping("/lastestOrder.do")
	@ResponseBody
	public ShopsResult lastestOrder(){
		return dataService.lastestOrder();
	}
	@RequestMapping("/queryYN.do")
	@ResponseBody
	public ShopsResult queryYN(){
		return dataService.queryYN();
	}
	/**
	 * 销量前五的店铺销售状况
	 * @return
	 */
	@RequestMapping("/top5ShopsList.do")
	@ResponseBody
	public ShopsResult top5ShopsList(){
		return dataService.top5ShopsList();
	}
	
	/**
	 * 商品分类销量
	 * @return
	 */
	@RequestMapping("/kindSaleRatio.do")
	@ResponseBody
	public ShopsResult kindSaleRatio(){
		return dataService.kindSaleRatio();
	}
	
	/**
	 * 商品销售各区间数量 
	 */
	@RequestMapping("/goodSalePriceCount.do")
	@ResponseBody
	public ShopsResult goodSalePriceCount(){
		return dataService.goodSalePriceCount();
	}
	
	@RequestMapping("/getOrderList.do")
	@ResponseBody
	public ShopsResult getOrderList(){
		return dataService.getOrderList();
	}
	
	/**shop_phone
	 * 店铺信息
	 */
	@RequestMapping("/getShopList.do")
	@ResponseBody
	public ShopsResult getShopList(){
		return dataService.getShopList();
	}
	
	/**
	 * 骑手订单信息
	 */
	@RequestMapping("/getRiderList.do")
	@ResponseBody
	public ShopsResult getRiderList(HttpServletRequest request){
		System.out.println(request.getRemoteAddr());
		System.out.println(request.getRequestURL());
		return dataService.getRiderList();
	}
	
	/**
	 * 近一月销售额
	 * @return
	 */
	@RequestMapping("/lastMonthSaleTotal.do")
	@ResponseBody
	public ShopsResult lastMonthSaleTotal(
			@RequestParam()String startTime,
			@RequestParam()String endTime
			){
		Map<String,Object> map=new HashMap<String, Object>();
		map.put("startTime", startTime);
		map.put("endTime", endTime);
		System.out.println(map);
		return dataService.lastMonthSaleTotal(map);
	}
	
	/**
	 * 近一月销售额
	 * @return
	 */
	@RequestMapping("/lastMonthSaleTotalYN.do")
	@ResponseBody
	public ShopsResult lastMonthSaleTotalYN(
			@RequestParam()String startTime,
			@RequestParam()String endTime
			){
		Map<String,Object> map=new HashMap<String, Object>();
		map.put("startTime", startTime);
		map.put("endTime", endTime);
		System.out.println(map);
		return dataService.lastMonthSaleTotalYN(map);
	}

	/**
	 * 线上采购额昨日同时段对比查询
	 * @return
	 */
	@RequestMapping("/onLinePurComparsionYesterday.do")
	@ResponseBody
	public ShopsResult onLinePurComparsionYesterday(){
		return  dataService.onLinePurComparsionYesterday();
	}
	
	/**
	 * 60S内采购订单数量统计
	 * @return
	 */
	@RequestMapping("/getPurListTotalMessageBySecond.do")
	@ResponseBody
	public ShopsResult getPurListTotalMessageBySecond(){
		return dataService.getPurListTotalMessageBySecond();
	}
	
	/**
	 * 根据选择的省份，查询其下地级市列表
	 * @param map
	 * @return
	 */
	@RequestMapping("/getCitiesInProvince.do")
	@ResponseBody
	public ShopsResult getCitiesInProvince(
			@RequestParam(value="name",required=true)String name
			){
		Map<String,Object> map=new HashMap<String, Object>();
		map.put("name", "%"+name+"%");
		return dataService.getCitiesInProvince(map);
	}
	
	@RequestMapping("/managerTest.do")
	@ResponseBody
	public ShopsResult managerTest(
			@RequestParam(value="shopUnique",required=true)String shopUnique
			){
		ShopsResult sr=new ShopsResult();
		if(shopUnique.equals("8302016134121")){
			sr.setStatus(1);
		}else{
			sr.setStatus(2);
		}
		return sr;
	}
	
	/**
	 * 今日销量前三的商品信息查询
	 * @param map
	 * @return
	 */
	@RequestMapping("/queryTop3GrossProfit.do")
	@ResponseBody
	public ShopsResult queryTop3GrossProfit(){
		Map<String,Object> map=new HashMap<String, Object>();
		return dataService.queryTop3GrossProfit(map);
	}
	

	/**
	 * 各类型的店铺数量查询
	 */
	@RequestMapping("/queryShopCountByType.do")
	@ResponseBody
	public ShopsResult queryShopCountByType(){
		return dataService.queryShopCountByType();
	}
	
	@RequestMapping("/mianmiShops.do")
	@ResponseBody
	public ShopsResult mianmiShops(){
		ShopsResult ns=new ShopsResult();
		ns.setStatus(1);
		ns.setMsg("查询成功！");
		Map<String,Double> ma=new HashMap<String, Double>();
		ma.put("mianmi", 32.5);
		ma.put("percent", 1.4);
		ns.setData(ma);
		return ns;
	}
	
	@RequestMapping("/cusOrder.do")
	@ResponseBody
	public ShopsResult cusOrder(){
		ShopsResult ns=new ShopsResult();
		ns.setStatus(1);
		ns.setMsg("查询成功！");
		Map<String,Double> ma=new HashMap<String, Double>();
		ma.put("cusOrder", 38.8);
		ma.put("percent", -0.2);
		ns.setData(ma);
		return ns;
	}
	
	
	@RequestMapping("/updateShopMsg.do")
	@ResponseBody
	public ShopsResult updateShopMsg(){
		return dataService.updateShopMsg();
	}
	
	/**
	 * 获取各区县的站点数量
	 * @return
	 */
	@ResponseBody
	@RequestMapping("/queryYNshopByArea.do")
	public ShopsResult queryYNshopByArea(
			@RequestParam(defaultValue="371321")String areaDictNum) {
		return dataService.queryYNshopByArea(areaDictNum);
	}
}

