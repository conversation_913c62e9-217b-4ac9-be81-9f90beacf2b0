package org.haier.shop.controller;

import java.util.List;
import java.util.Map;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;

import org.haier.shop.service.FeedBackService;
import org.haier.shop.util.PurResult;
import org.haier.shop.util.ShopsResult;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;


@Controller
@RequestMapping("/feedBack")
public class FeedBackController {
	@Resource
	private FeedBackService feedBackService;
	
	@RequestMapping("/queryFeedBackListPage.do")
	public String queryFeedBackListPage(){
		return "/WEB-INF/manager/queryFeedBackList.jsp";
	}
	@RequestMapping("/queryFeedBackPhoneListPage.do")
	public String queryFeedBackPhoneListPage(){
		return "/WEB-INF/manager/queryFeedBackPhoneList.jsp";
	}
	@RequestMapping("/addFeedBackPhonePage.do")
	public String addFeedBackPhonePage(){
		return "/WEB-INF/manager/addFeedBackPhone.jsp";
	}
	@RequestMapping("/queryGoodsKindImageListPage.do")
	public String queryGoodsKindImageListPage(){
		return "/WEB-INF/manager/queryGoodsKindImageList.jsp";
	}
	@RequestMapping("/queryFeedBackDetailPage.do")
	public String queryFeedBackDetailPage(String feed_back_id,Model model){
		//获取意见反馈详情
		Map<String ,Object> feeback = feedBackService.queryFeedBackDetail(feed_back_id);
		//获取意见反馈图片列表
		List<Map<String ,Object>> imgList = feedBackService.queryFeedBackImageList(feed_back_id);
		model.addAttribute("feeback", feeback);
		model.addAttribute("imgList", imgList);
		return "/WEB-INF/manager/feedBackDetail.jsp";
	}
	
	@RequestMapping("/editFeedBackPhonePage.do")
	public String editFeedBackPhonePage(String feed_back_phone_id,HttpServletRequest request){
		Map<String,Object> map=feedBackService.getFeedBackPhoneInfo(feed_back_phone_id);
		request.setAttribute("feed_back_phone_id", map.get("feed_back_phone_id"));
		request.setAttribute("feed_back_phone", map.get("feed_back_phone"));
		request.setAttribute("feed_back_name", map.get("feed_back_name"));
		request.setAttribute("feed_back_type", map.get("feed_back_type"));
		request.setAttribute("feed_back_flag", map.get("feed_back_flag"));
		return "/WEB-INF/manager/editFeedBackPhone.jsp";
	}
	@RequestMapping("/editGoodsKindImagePage.do")
	public String editGoodsKindImagePage(String goods_kind_unique,HttpServletRequest request){
		Map<String,Object> map=feedBackService.queryGoodsKindImageDetail(goods_kind_unique);
		request.setAttribute("goods_kind_unique", map.get("goods_kind_unique"));
		request.setAttribute("goods_kind_name", map.get("goods_kind_name"));
		request.setAttribute("goods_kind_hui_image", map.get("goods_kind_hui_image"));
		request.setAttribute("goods_kind_image", map.get("goods_kind_image"));
		return "/WEB-INF/manager/editGoodsKindImage.jsp";
	}
	
	/**
	 * 更新分类图片信息
	 * @param request
	 * @param shop_unique
	 * @param shop_name
	 * @param shop_phone
	 * @param shop_address_detail
	 * @param manager_pwd
	 * @param shop_remark
	 * @return
	 */
	@RequestMapping("/editGoodsKindImage.do")
	@ResponseBody
	public ShopsResult editGoodsKindImage(HttpServletRequest request,String goods_kind_unique
			){
		return feedBackService.editGoodsKindImage(request, goods_kind_unique);
	}
	
	/**
	 * 处理
	 * @param map
	 * @return
	 */
	@RequestMapping("/saveChuLi.do")
	@ResponseBody
	public PurResult saveChuLi(
			@RequestParam(value="feed_back_id",required=true)String feed_back_id,
			@RequestParam(value="feed_back_remark",required=true)String feed_back_remark
			){
		return feedBackService.saveChuLi(feed_back_id,feed_back_remark);
	}
	/**
	 * 查询意见反馈列表
	 * @param map
	 * @return
	 */
	@RequestMapping("/queryFeedBackList.do")
	@ResponseBody
	public PurResult queryFeedBackList(
			@RequestParam(value="orderMessage",required=false)String orderMessage,
			@RequestParam(value="startTime",required=false)String startTime,
			@RequestParam(value="endTime",required=false)String endTime,
			@RequestParam(value="feed_back_source",required=false)String feed_back_source,
			@RequestParam(value="feed_back_type",required=false)String feed_back_type,
			@RequestParam(value="feed_back_status",required=false)String feed_back_status,
			@RequestParam(value = "page", defaultValue = "0") int page,
			@RequestParam(value = "limit", defaultValue = "8") int pageSize
			){
		return feedBackService.queryFeedBackList(orderMessage,startTime,endTime,feed_back_source,
				feed_back_type,feed_back_status,page,pageSize);
	}
	/**
	 * 标记反馈
	 * @param map
	 * @return
	 */
	@RequestMapping("/targetFeedBack.do")
	@ResponseBody
	public PurResult targetFeedBack(
			@RequestParam(value="feed_back_id",required=true)Integer feed_back_id,
			@RequestParam(value="feed_back_target_status",required=true)Integer feed_back_target_status
			){
		return feedBackService.targetFeedBack(feed_back_id,feed_back_target_status);
	}
	
	/**
	 * 查询短信通知手机号列表 
	 * @param map
	 * @return
	 */
	@RequestMapping("/queryFeedBackPhoneList.do")
	@ResponseBody
	public PurResult queryFeedBackPhoneList(
			@RequestParam(value="orderMessage",required=false)String orderMessage,
			@RequestParam(value = "page", defaultValue = "0") int page,
			@RequestParam(value = "limit", defaultValue = "8") int pageSize
			){
		return feedBackService.queryFeedBackPhoneList(orderMessage,page,pageSize);
	}
	/**
	 * 添加短信手机号 
	 * @param map
	 * @return
	 */
	@RequestMapping("/addFeedBackPhone.do")
	@ResponseBody
	public PurResult addFeedBackPhone(
			@RequestParam(value="feed_back_name",required=false)String feed_back_name,
			@RequestParam(value="feed_back_phone",required=false)String feed_back_phone,
			@RequestParam(value="feed_back_type",required=false)String feed_back_type,
			@RequestParam(value="feed_back_flag",required=false)String feed_back_flag
			
			){
		return feedBackService.addFeedBackPhone(feed_back_name,feed_back_phone,feed_back_type,feed_back_flag);
	}
	/**
	 * 修改短信手机号 
	 * @param map
	 * @return
	 */
	@RequestMapping("/editFeedBackPhone.do")
	@ResponseBody
	public PurResult editFeedBackPhone(
			@RequestParam(value="feed_back_phone_id",required=false)String feed_back_phone_id,
			@RequestParam(value="feed_back_name",required=false)String feed_back_name,
			@RequestParam(value="feed_back_phone",required=false)String feed_back_phone,
			@RequestParam(value="feed_back_type",required=false)String feed_back_type,
			@RequestParam(value="feed_back_flag",required=false)String feed_back_flag
			
			){
		return feedBackService.editFeedBackPhone(feed_back_phone_id,feed_back_name,feed_back_phone,feed_back_type,feed_back_flag);
	}
	/**
	 * 删除短信手机号 
	 * @param map
	 * @return
	 */
	@RequestMapping("/deleteFeedBackPhone.do")
	@ResponseBody
	public PurResult deleteFeedBackPhone(
			@RequestParam(value="feed_back_phone_id",required=false)String feed_back_phone_id
			){
		return feedBackService.deleteFeedBackPhone(feed_back_phone_id);
	}
	//店铺资质列表
	@RequestMapping("/queryShopQualificationListPage.do")
	public String queryShopQualificationListPage(){
		return "/WEB-INF/manager/queryShopQualificationList.jsp";
	}
	
	//店铺资质详情页面
	@RequestMapping("/queryShopQualificationDetailPage.do")
	public String queryShopQualificationDetailPage(String shop_qualification_id,HttpServletRequest request){
		request.setAttribute("shop_qualification_id", shop_qualification_id);
		return "/WEB-INF/manager/queryShopQualificationDetail.jsp";
	}
	
	/**
	 * 查询店铺资质列表
	 * @param map
	 * @return
	 */
	@RequestMapping("/queryShopQualificationList.do")
	@ResponseBody
	public PurResult queryShopQualificationList(
			@RequestParam(value="orderMessage",required=false)String orderMessage,
			Integer pageNum,
			Integer pageSize
			
			){
		return feedBackService.queryShopQualificationList(orderMessage,pageNum,pageSize);
	}
	
	/**
	 * 查询店铺资质详情
	 * @param map
	 * @return
	 */
	@RequestMapping("/queryShopQualificationDetail.do")
	@ResponseBody
	public PurResult queryShopQualificationDetail(
			@RequestParam(value="shop_qualification_id",required=true)String shop_qualification_id
			){
		return feedBackService.queryShopQualificationDetail(shop_qualification_id);
	}
	
	//店铺审核页面
	@RequestMapping("/queryShopExamineListPage.do")
	public String queryShopExamineListPage(){
			return "/WEB-INF/manager/queryShopExamineList.jsp";
	}
	
	//站点审核页面
	@RequestMapping("/siteAuditList.do")
	public String siteAuditList(){
			return "/WEB-INF/manager/siteAuditList.jsp";
	}
	/**
	 * 查询店铺审核列表
	 * @param map
	 * @return
	 */
	@RequestMapping("/queryShopExamineList.do")
	@ResponseBody
	public PurResult queryShopExamineList(
			@RequestParam(value="orderMessage",required=false)String orderMessage,
			@RequestParam(value="examinestatus",required=false)String examinestatus,
			String area_dict_num,String shop_type,
			@RequestParam(value = "page", defaultValue = "0") int page,
			@RequestParam(value = "limit", defaultValue = "8") int pageSize
			){
		return feedBackService.queryShopExamineList(orderMessage,examinestatus,page,pageSize,area_dict_num,shop_type);
	}
	//店铺审核详情页面
	@RequestMapping("/queryShopDetailPage.do")
	public String queryShopDetailPage(String shop_unique,String type,HttpServletRequest request){
			request.setAttribute("shop_unique", shop_unique);
			request.setAttribute("type", type);
			return "/WEB-INF/manager/queryShopExamineDetail.jsp";
	}
	/**
	 * 查询店铺审核详情
	 * @param shop_unique
	 * @return
	 */
	@RequestMapping("/queryShopExamineDetail.do")
	@ResponseBody
	public PurResult queryShopExamineDetail(
			String shop_unique,String type
			){
		return feedBackService.queryShopExamineDetail(shop_unique,type);
	}
	/**
	 * 保存审核结果
	 * @param shop_unique
	 * @return
	 */
	@RequestMapping("/saveShopExamine.do")
	@ResponseBody
	public PurResult saveShopExamine(
			String shop_unique,
			String type,
			String examinestatus,
			String examinestatus_reason,
			String pay_info_id,
			String examine_status,
			String userPhone,
			HttpServletRequest request
			){
		return feedBackService.saveShopExamine(shop_unique,type,examinestatus,examinestatus_reason,pay_info_id,examine_status,userPhone, request);
	}
	
	/**
	 * 查询分类图片列表
	 * @param map
	 * @return
	 */
	@RequestMapping("/queryGoodsKindImageList.do")
	@ResponseBody
	public PurResult queryGoodsKindImageList(
			@RequestParam(value="orderMessage",required=false)String orderMessage,
			@RequestParam(value = "page", defaultValue = "0") int page,
			@RequestParam(value = "limit", defaultValue = "8") int pageSize
			){
		return feedBackService.queryGoodsKindImageList(orderMessage,page,pageSize);
	}
}
