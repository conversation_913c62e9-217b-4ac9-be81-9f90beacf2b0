package org.haier.shop.controller;

import java.net.InetAddress;
import java.net.UnknownHostException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;

import org.haier.shop.service.BusinessService;
import org.haier.shop.util.PurResult;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.multipart.MultipartFile;

import jxl.Sheet;
import jxl.Workbook;

@RequestMapping("/business")
@Controller
public class BusinessController {
	
	@Resource
	private BusinessService businessService;
	
	/**
	 * 跳转列表页面
	 * @return
	 */
	@RequestMapping("/businessListPage.do")
	public String noticeListPage(){
		return "/WEB-INF/business/businessList.jsp";
	}
	
	/**
	 * 查询列表
	 * @return
	 */
	@RequestMapping("/businessList.do")
	@ResponseBody
	public PurResult businessList(
			String business_type,
			@RequestParam(value = "page", defaultValue = "1") int page,
			@RequestParam(value = "limit", defaultValue = "8") int pageSize){
		Map<String,Object> params = new HashMap<String, Object>();
		params.put("business_type", business_type);
		params.put("pageSize", pageSize);
		params.put("startNum", (page-1)*pageSize);
		return businessService.queryBusinessList(params);
	}
	
	
	/**
	 * 跳转添加页面
	 * @return
	 */
	@RequestMapping("/addBusinessPage.do")
	public String addBusinessPage(){
		return "/WEB-INF/business/addBusiness.jsp";
	}
	
	/**
	 * 添加
	 * @return
	 */
	@RequestMapping("/addBusiness.do")
	@ResponseBody
	public PurResult addBusiness(HttpServletRequest request){
		return businessService.addBusiness(request);
	}
	
	/**
	 * 跳转修改页面
	 * @return
	 */
	@RequestMapping("/updateBusinessPage.do")
	public String updateBusinessPage(String id,Model model){
		//获取详情
		Map<String ,Object> businessDetail = businessService.queryBusinessDetail(id);
		model.addAttribute("businessDetail", businessDetail);
		return "/WEB-INF/business/updateBusiness.jsp";
	}
	
	/**
	 * 修改
	 * @return
	 */
	@RequestMapping("/updateBusiness.do")
	@ResponseBody
	public PurResult updateBusiness(HttpServletRequest request){
		return businessService.updateBusiness(request);
	}
	
	/**
	 * 删除
	 * @return
	 */
	@RequestMapping("/deleteBusiness.do")
	@ResponseBody
	public PurResult deleteBusiness(String id){
		return businessService.deleteBusiness(id);
	}
	
	/**
	 * 跳转详情页面
	 * @return
	 */
	@RequestMapping("/businessDetailPage.do")
	public String businessDetailPage(String id,Model model){
		//获取详情
		Map<String ,Object> businessDetail = businessService.queryBusinessDetail(id);
		model.addAttribute("businessDetail", businessDetail);
		return "/WEB-INF/business/businessDetail.jsp";
	}
	
	/**
	 * 联通新卡用户红包导入
	 */
	@RequestMapping("unicomUserImport.do")
	@ResponseBody
	public PurResult unicomUserImport(@RequestParam MultipartFile file,HttpServletRequest request){
		PurResult result = new PurResult();
		try {
			List<Map<String ,Object>> list = new ArrayList<Map<String ,Object>>();
			
			System.out.println(file.getContentType());
			
			//创建Excel，读取文件内容
			Workbook workbook = Workbook.getWorkbook(file.getInputStream());
	 
			//获取第一个工作表
			Sheet sheet = workbook.getSheet(0);
			
			//循环插入数据
			for(int i=1; i<sheet.getRows(); i++){
				Map<String ,Object> params = new HashMap<>();
					
				String person_name = sheet.getCell(0, i).getContents();//能人姓名
				if(person_name!=null){
					params.put("person_name", person_name);
				}
				
				String person_phone = sheet.getCell(2, i).getContents();//能人手机号
				if(person_phone!=null){
					params.put("person_phone", person_phone);
				}
				
				String cus_phone = sheet.getCell(3, i).getContents();//手机号
				if(cus_phone!=null){
					params.put("cus_phone", cus_phone);
				}
					
				String referees_num = sheet.getCell(1, i).getContents();;//能人编号
				if(referees_num!=null){
					params.put("referees_num", referees_num);
				}
					
				String business_money = sheet.getCell(4, i).getContents();;//套餐金额
				if(business_money!=null){
					params.put("business_money", business_money);
				}
				
				String net_time = sheet.getCell(6, i).getContents();;//入网时间
				if(net_time!=null){
					params.put("net_time", net_time);
				}
				params.put("business_type", "1");//联通
				list.add(params);
			}
			result = businessService.unicomUserImport(list);
		} catch (Exception e) {
			e.printStackTrace();
			result.setStatus(0);
			result.setMsg("异常");
		}
		return result;
	}
	
	/**
	 * 跳转能人列表页面
	 * @return
	 */
	@RequestMapping("/redPersonListPage.do")
	public String redPersonListPage(){
		return "/WEB-INF/business/redPersonList.jsp";
	}
	
	/**
	 * 跳转红包订单统计
	 * @return
	 */
	@RequestMapping("/toRedOrderStatistics.do")
	public String toRedOrderStatistics(Model model){
		model.addAttribute("redOrder", businessService.queryRedOrderStatistics(null).get(0));
		return "/WEB-INF/business/redOrderStatistics.jsp";
	}
	
	/**
	 * 跳转到总平台-待办事项
	 */
	@RequestMapping("/toPlatformMsg.do")
	public String toPlatformMsg(Model model){		

		return "/WEB-INF/business/platformMsg.jsp";
	}
	
	/**
	 * 统计页面
	 */
	@RequestMapping("/toStatistics.do")
	public String toStatistics(Model model){		
		//model.addAttribute("redOrder", businessService.queryRedOrderStatistics(null));
		return "/WEB-INF/business/toStatistics.jsp";
	}
	
	/**
	 * 查询能人列表
	 * @return
	 */
	@RequestMapping("/redPersonList.do")
	@ResponseBody
	public PurResult redPersonList(
			String person_info,
			@RequestParam(value = "field", defaultValue = "create_times")String field,
			@RequestParam(value = "order", defaultValue = "desc")String order,
			@RequestParam(value = "page", defaultValue = "1") int page,
			@RequestParam(value = "limit", defaultValue = "8") int pageSize){
		Map<String,Object> params = new HashMap<String, Object>();
		params.put("person_info", person_info);
		params.put("field", field);
		params.put("order", order);
		params.put("pageSize", pageSize);
		params.put("startNum", (page-1)*pageSize);
		
		return businessService.queryRedPersonList(params);
	}
	
	/**
	 * 订单使用红包统计列表
	 * @return
	 */
	@RequestMapping("/redOrderStatisticsList.do")
	@ResponseBody
	public PurResult redOrderStatisticsList(
			String person_info,
			String shop_info,
			String orderStatus,
			@RequestParam(value = "field", defaultValue = "sale_list_datetime")String field,
			@RequestParam(value = "order", defaultValue = "desc")String order,
			@RequestParam(value="startTime",defaultValue="")String startTime,
			@RequestParam(value="endTime",defaultValue="")String endTime,
			@RequestParam(value = "page", defaultValue = "1") int page,
			@RequestParam(value = "limit", defaultValue = "15") int pageSize){
		Map<String,Object> params = new HashMap<String, Object>();
		params.put("person_info", person_info);
		params.put("shop_info", shop_info);
		params.put("orderStatus", orderStatus);
		params.put("startTime", startTime);
		params.put("endTime", endTime);
		params.put("field", field);
		params.put("order", order);
		params.put("pageSize", pageSize);
		params.put("startNum", (page-1)*pageSize);
		
		return businessService.queryRedOrderList(params);
	}
	
	/**
	 * 跳转能人佣金列表页面
	 * @return
	 */
	@RequestMapping("/redPersonCommissionListPage.do")
	public String redPersonCommissionListPage(String staff_num,Model model){
		model.addAttribute("staff_num", staff_num);
		return "/WEB-INF/business/redPersonCommissionList.jsp";
	}
	
	/**
	 * 查询能人佣金列表
	 * @return
	 */
	@RequestMapping("/redPersonCommissionList.do")
	@ResponseBody
	public PurResult redPersonCommissionList(
			String staff_num,
			String cus_phone,
			String commission_type,
			@RequestParam(value = "page", defaultValue = "1") int page,
			@RequestParam(value = "limit", defaultValue = "8") int pageSize){
		Map<String,Object> params = new HashMap<String, Object>();
		params.put("staff_num", staff_num);
		params.put("cus_phone", cus_phone);
		params.put("commission_type", commission_type);
		params.put("pageSize", pageSize);
		params.put("startNum", (page-1)*pageSize);
		return businessService.queryRedPersonCommissionList(params);
	}
	
	/**
	 * 跳转能人提现列表页面
	 * @return
	 */
	@RequestMapping("/redPersonWidthListPage.do")
	public String redPersonWidthListPage(String staff_num,Model model){
		model.addAttribute("staff_num", staff_num);
		return "/WEB-INF/business/redPersonWidthList.jsp";
	}
	
	/**
	 * 查询能人提现列表
	 * @return
	 */
	@RequestMapping("/redPersonWidthList.do")
	@ResponseBody
	public PurResult redPersonWidthList(
			String staff_num,
			String withd_status,
			String staff_info,
			@RequestParam(value = "page", defaultValue = "1") int page,
			@RequestParam(value = "limit", defaultValue = "8") int pageSize){
		Map<String,Object> params = new HashMap<String, Object>();
		params.put("staff_num", staff_num);
		params.put("withd_status", withd_status);
		params.put("staff_info", staff_info);
		params.put("pageSize", pageSize);
		params.put("startNum", (page-1)*pageSize);
		return businessService.queryRedPersonWidthList(params);
	}
	
	/**
	 * 跳转能人提现列表页面
	 * @return
	 */
	@RequestMapping("/redPersonWidthOperationListPage.do")
	public String redPersonWidthOperationListPage(){
		return "/WEB-INF/business/redPersonWidthOperationList.jsp";
	}
	
	/**
	 * 能人提现确认打款--微信企业付款给用户
	 * @return
	 */
	@RequestMapping("/confirmPayment.do")
	@ResponseBody
	public PurResult confirmPayment(HttpServletRequest request,String withd_id){
		return businessService.weixinTransfers(withd_id,getIpAddr(request));
	}

	/**
	 * 获取当前网络ip
	 * @param request
	 * @return
	 */
	public String getIpAddr(HttpServletRequest request) {
		String ipAddress = request.getHeader("x-forwarded-for");
		if (ipAddress == null || ipAddress.length() == 0 || "unknown".equalsIgnoreCase(ipAddress)) {
			ipAddress = request.getHeader("Proxy-Client-IP");
		}
		if (ipAddress == null || ipAddress.length() == 0 || "unknown".equalsIgnoreCase(ipAddress)) {
			ipAddress = request.getHeader("WL-Proxy-Client-IP");
		}
		if (ipAddress == null || ipAddress.length() == 0 || "unknown".equalsIgnoreCase(ipAddress)) {
			ipAddress = request.getRemoteAddr();
			if (ipAddress.equals("127.0.0.1") || ipAddress.equals("0:0:0:0:0:0:0:1")) {
				// 根据网卡取本机配置的IP
				InetAddress inet = null;
				try {
					inet = InetAddress.getLocalHost();
				} catch (UnknownHostException e) {
					e.printStackTrace();
				}
				ipAddress = inet.getHostAddress();
			}
		}
		// 对于通过多个代理的情况，第一个IP为客户端真实IP,多个IP按照','分割
		if (ipAddress != null && ipAddress.length() > 15) { // "***.***.***.***".length()
															// = 15
			if (ipAddress.indexOf(",") > 0) {
				ipAddress = ipAddress.substring(0, ipAddress.indexOf(","));
			}
		}
		return ipAddress;
	}
	
	/**
	 * 联通能人佣金导入
	 */
	@RequestMapping("unicomPersonCommissionImport.do")
	@ResponseBody
	public PurResult unicomPersonCommissionImport(@RequestParam MultipartFile file,HttpServletRequest request){
		PurResult result = new PurResult();
		try {
			List<Map<String ,Object>> list = new ArrayList<Map<String ,Object>>();
			
			System.out.println(file.getContentType());
			
			//创建Excel，读取文件内容
			Workbook workbook = Workbook.getWorkbook(file.getInputStream());
	 
			//获取第一个工作表
			Sheet sheet = workbook.getSheet(0);
			
			//循环插入数据
			for(int i=1; i<sheet.getRows(); i++){
				Map<String ,Object> params = new HashMap<>();
					
				String person_name = sheet.getCell(0, i).getContents();//能人姓名
				if(person_name!=null){
					params.put("person_name", person_name);
				}
				
				String person_phone = sheet.getCell(2, i).getContents();//能人手机号
				if(person_phone!=null){
					params.put("person_phone", person_phone);
				}
				
				String cus_phone = sheet.getCell(3, i).getContents();//手机号
				if(cus_phone!=null){
					params.put("cus_phone", cus_phone);
				}
					
				String referees_num = sheet.getCell(1, i).getContents();;//能人编号
				if(referees_num!=null){
					params.put("referees_num", referees_num);
				}
					
				String business_money = sheet.getCell(4, i).getContents();;//套餐金额
				if(business_money!=null){
					params.put("business_money", business_money);
				}
				
				String out_account = sheet.getCell(5, i).getContents();;//用户出账金额
				if(out_account!=null){
					params.put("out_account", out_account);
				}
				
				params.put("business_type", "1");//联通
				list.add(params);
			}
			result = businessService.unicomPersonCommissionImport(list);
		} catch (Exception e) {
			e.printStackTrace();
			result.setStatus(0);
			result.setMsg("异常");
		}
		return result;
	}
	
	/**
	 * 跳转红包发放记录列表页面
	 * @return
	 */
	@RequestMapping("/redPacketPage.do")
	public String redPacketPage(String id,Model model){
		model.addAttribute("business_id", id);
		return "/WEB-INF/business/redPacketList.jsp";
	}
	
	/**
	 * 查询红包发放记录列表
	 * @return
	 */
	@RequestMapping("/redPacketList.do")
	@ResponseBody
	public PurResult redPacketList(
			String business_id,
			String cus_phone,
			String red_status,
			@RequestParam(value = "field", defaultValue = "provide_time") String field,
			@RequestParam(value = "order", defaultValue = "desc") String order,
			@RequestParam(value = "page", defaultValue = "1") int page,
			@RequestParam(value = "limit", defaultValue = "8") int pageSize){
		Map<String,Object> params = new HashMap<String, Object>();
		params.put("business_id", business_id);
		params.put("cus_phone", cus_phone);
		params.put("red_status", red_status);
		params.put("field", field);
		params.put("order", order);
		params.put("pageSize", pageSize);
		params.put("startNum", (page-1)*pageSize);
		return businessService.queryRedPacketList(params);
	}
	
	/**
	 * 总平台-待办事项
	 */
	@RequestMapping("/queryPlatformMsg.do")
	@ResponseBody
	public PurResult queryPlatformMsg(
			String orderStatus,
			@RequestParam(value="startTime",defaultValue="")String startTime,
			@RequestParam(value="endTime",defaultValue="")String endTime,
			@RequestParam(value = "page", defaultValue = "1") int page,
			@RequestParam(value = "limit", defaultValue = "15") int pageSize){
		Map<String,Object> params = new HashMap<String, Object>();
		params.put("orderStatus", orderStatus);
		params.put("startTime", startTime);
		params.put("endTime", endTime);
		params.put("pageSize", pageSize);
		params.put("startNum", (page-1)*pageSize);
		
		return businessService.queryPlatformMsg(params);
	}
	
	/**
	 * 红包订单统计查询
	 */
	@RequestMapping("/getStatistics.do")
	@ResponseBody
	public PurResult getStatistics(
			String orderStatus,
			@RequestParam(value="startTime",defaultValue="")String startTime,
			@RequestParam(value="endTime",defaultValue="")String endTime){
		Map<String,Object> params = new HashMap<String, Object>();
		params.put("startTime", startTime);
		params.put("endTime", endTime);
		
		return businessService.getStatistics(params);
	}
}
