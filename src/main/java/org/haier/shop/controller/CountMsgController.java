package org.haier.shop.controller;

import java.util.HashMap;
import java.util.Map;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;

import org.apache.shiro.SecurityUtils;
import org.apache.shiro.subject.Subject;
import org.haier.shop.service.CountMsgService;
import org.haier.shop.util.PurResult;
import org.haier.shop.util.ShopsResult;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

@Controller
@RequestMapping("/countMsg")
public class CountMsgController {
	@Resource
	private CountMsgService countMsgService;
	
	/**
	 * 
	 * @param shopUnique 店铺编号
	 * @param startTime 开始时间
	 * @param endTime 结束时间
	 * @param goods_big_class1
	 * @param goods_small_class1
	 * @param supplierUnique1
	 * @param goodsMessage1
	 * @param goods_big_class2
	 * @param goods_small_class2
	 * @param supplierUnique2
	 * @param goodsMessage2
	 * @return
	 */
	@RequestMapping("/queryCountStatisticsSameGoods.do")
	@ResponseBody
	public PurResult queryCountStatisticsSameGoods(String shopUnique,String startTime,String endTime
			,Integer goods_big_class1,Integer goods_small_class1,String supplierUnique1,String goodsMessage1
			,Integer goods_big_class2,Integer goods_small_class2,String supplierUnique2,String goodsMessage2
			) {
		return countMsgService.queryCountStatisticsSameGoods(shopUnique, startTime, endTime, goods_big_class1, goods_small_class1, 
				supplierUnique1, goodsMessage1, goods_big_class2, goods_small_class2, supplierUnique2, goodsMessage2);
	}
	@RequestMapping("/statisticsSameGoods.do")
	public String statisticsSameGoods() {
		return "/WEB-INF/countMessage/statisticsSameGoods.jsp";
	}
	
	/**
	 * 
	 * @param shopUnique
	 * @param startTime1
	 * @param endTime1
	 * @param startTime2
	 * @param endTime2
	 * @param goods_big_class
	 * @param good_small_class
	 * @param supplierUnique
	 * @param goodsMessage
	 * @return
	 */
	@RequestMapping("/queryCountStatisticsSameTime.do")
	@ResponseBody
	public PurResult queryCountStatisticsSameTime(String shopUnique,String startTime1,String endTime1,String startTime2,String endTime2
			,Integer goods_big_class,Integer goods_small_class,String supplierUnique,String goodsMessage
			) {
		return countMsgService.queryCountStatisticsSameTime(shopUnique, startTime1, endTime1, startTime2, endTime2,
				goods_big_class, goods_small_class, supplierUnique, goodsMessage);
	}
	//统计信息页面
	@RequestMapping("/countMsgPage.do")
	public String countMsgPage(){
		
//		return "/WEB-INF/countMessage/countMsgPage.jsp";
		Subject subject = SecurityUtils.getSubject();
		String roleType = subject.getSession().getAttribute("roleType").toString();
		if(roleType.equals("1")){
			return "/WEB-INF/countMessage/countMsgPage.jsp";
		}else if(roleType.equals("2")){
			return "/WEB-INF/countMessage/countMsgPage.jsp";
		}else {
			return "/WEB-INF/countMessage/countMsgPage.jsp";
		}
	}
	//统计信息页面-宁宇
	@RequestMapping("/countMsgPageNY.do")
	public String countMsgPageNY(){

		return "/WEB-INF/countMessage/countMsgPageNY.jsp";
		
	}
	//统计信息页面-小象
	@RequestMapping("/countMsgPageXiaoxiang.do")
	public String countMsgPageXiaoxiang(){

		return "/WEB-INF/countMessage/countMsgPageXiaoxiang.jsp";
		
	}
	@RequestMapping("/statisticsSameTime.do")
	public String statisticsSameTime() {
		return "/WEB-INF/countMessage/statisticsSameTime.jsp";
	}
	
	@RequestMapping("/goodsSaleStatistics.do")
	public String goodsSaleStatistics(){
		return "/WEB-INF/countMessage/goodsSaleStatistics.jsp";
	}
	
	//查询营业额
	@RequestMapping("/queryTurnover.do")
	@ResponseBody
	public PurResult queryTurnover(String shop_unique,String type){
		return countMsgService.queryTurnover(shop_unique,type);
	}
	//查询订单信息
	@RequestMapping("/queryOrderMsg.do")
	@ResponseBody
	public PurResult queryOrderMsg(String shop_unique,String type){
		return countMsgService.queryOrderMsg(shop_unique,type);
	}
	
	//支付方式占比
	@RequestMapping("/queryPayProportion.do")
	@ResponseBody
	public PurResult queryPayProportion(String shop_unique,String type){
		return countMsgService.queryPayProportion(shop_unique,type);
	}
	//分类销量占比
	@RequestMapping("/queryKindSaleProportion.do")
	@ResponseBody
	public PurResult queryKindSaleProportion(String shop_unique,String type){
		return countMsgService.queryKindSaleProportion(shop_unique,type);
	}
	//查询销售走势
	@RequestMapping("/querySaleTrend.do")
	@ResponseBody
	public PurResult querySaleTrend(String shop_unique,String type,String startDate,String endDate){
		return countMsgService.querySaleTrend(shop_unique,type,startDate,endDate);
	}
	//查询超市进货top5
	@RequestMapping("/queryShopPurchaseTop5.do")
	@ResponseBody
	public PurResult queryShopPurchaseTop5(String shop_unique,String type){
		return countMsgService.queryShopPurchaseTop5(shop_unique,type);
	}
	//查询商品销量top5
	@RequestMapping("/queryGoodsSaleTop5.do")
	@ResponseBody
	public PurResult queryGoodsSaleTop5(String shop_unique,String type){
		return countMsgService.queryGoodsSaleTop5(shop_unique,type);
	}
	//查询业务员销售额top5
	@RequestMapping("/querySalesmanTop5.do")
	@ResponseBody
	public PurResult querySalesmanTop5(String shop_unique,String type){
		return countMsgService.querySalesmanTop5(shop_unique,type);
	}
	
	/**
	 * 今日订单分时统计额及昨日订单分时统计额
	 * @return
	 */
	@RequestMapping("/orderTotalByHours.do")
	@ResponseBody
	public ShopsResult orderTotalByHours(String shop_unique){
		return countMsgService.orderTotalByHours(shop_unique);
	}
	
	/**
	 * 百货豆统计
	 * @param map
	 * @return
	 */
	@RequestMapping("/queryShopStatisticsMsgPage.do")
	@ResponseBody
	public ShopsResult queryShopStatisticsMsgPage(
		@RequestParam(value="shopUnique")String shopUnique,
		@RequestParam(value="queryType",defaultValue="1")Integer queryType
			){
		Map<String,Object> map=new HashMap<String,Object>();
		map.put("shopUnique", shopUnique);
		if(queryType==1){//当日查询
			map.put("startDay", 0);			
			map.put("endDay", -1);
		}else if(queryType==2){
			map.put("startDay", 1);
			map.put("endDay", 0);
		}else if(queryType==3){//最近一周
			map.put("startDay", 6);
			map.put("endDay", -1);
		}else if(queryType==4){
			map.put("startDay", 29);
			map.put("endDay", -1);
		}
		
		return countMsgService.queryShopStatisticsMsgPage(map);
	}
	
	
	/**
	 * 积分统计
	 * @param shopUnique
	 * @param queryType
	 * @return
	 */
	@RequestMapping("/queryShopStatisticsMsgPagePoints.do")
	@ResponseBody
	public ShopsResult queryShopStatisticsMsgPagePoints(
		@RequestParam(value="shopUnique")String shopUnique,
		@RequestParam(value="queryType",defaultValue="1")Integer queryType
			){
		Map<String,Object> map=new HashMap<String,Object>();
		map.put("shopUnique", shopUnique);
		if(queryType==1){//当日查询
			map.put("startDay", 0);			
			map.put("endDay", -1);
		}else if(queryType==2){
			map.put("startDay", 1);
			map.put("endDay", 0);
		}else if(queryType==3){//最近一周
			map.put("startDay", 6);
			map.put("endDay", -1);
		}else if(queryType==4){
			map.put("startDay", 29);
			map.put("endDay", -1);
		}
		return countMsgService.queryShopStatisticsMsgPagePoints(map);
	}
	
	@RequestMapping("/queryBeansAccountByDays.do")
	@ResponseBody
	public ShopsResult queryBeansAccountByDays(
			@RequestParam(value="shopUnique")String shopUnique,
			@RequestParam(value="queryType",defaultValue="3")Integer queryType
			){
		Map<String,Object> map=new HashMap<String,Object>();
		map.put("shopUnique", shopUnique);
//		if(queryType==1){//当日查询
//			map.put("startDay", 1);	
//		}else if(queryType==2){
//			map.put("startDay", 2);
//			map.put("endDay", 0);
//		}else 
		if(queryType==3){//最近一周
			map.put("startDay", 1);
			map.put("unitStart", "WEEK");
		}else if(queryType==4){
			map.put("startDay", 1);
			map.put("unitStart", "MONTH");
		}
		return countMsgService.queryBeansAccountByDays(map);
	}
	
	@RequestMapping("/pointsStatisticsByDay.do")
	@ResponseBody
	public ShopsResult pointsStatisticsByDay(
			@RequestParam(value="shopUnique")String shopUnique,
			@RequestParam(value="queryType",defaultValue="3")Integer queryType
			){
		Map<String,Object> map=new HashMap<String,Object>();
		map.put("shopUnique", shopUnique);
//		if(queryType==1){//当日查询
//			map.put("startDay", 1);	
//		}else if(queryType==2){
//			map.put("startDay", 2);
//			map.put("endDay", 0);
//		}else 
		if(queryType==3){//最近一周
			map.put("startDay", 1);
			map.put("unitStart", "WEEK");
		}else if(queryType==4){
			map.put("startDay", 1);
			map.put("unitStart", "MONTH");
		}
		//
		return countMsgService.pointsStatisticsByDay(map);
	}
	

	/**
	 * 公共优惠券店内销售统计
	 * @param map
	 * @return
	 */
	@RequestMapping("/queryCouponPublicStatistics.do")
	@ResponseBody
	public ShopsResult queryCouponPublicStatistics(
			@RequestParam(value="shopUnique")String shopUnique,
			@RequestParam(value="queryType",defaultValue="3")Integer queryType){
		Map<String,Object> map=new HashMap<String,Object>();
		map.put("shopUnique", shopUnique);
		if(queryType==1){//当日查询
			map.put("startDay", 1);	
			map.put("unitStart", "DAY");
		}else if(queryType==3){//最近一周
			map.put("startDay", 1);
			map.put("unitStart", "WEEK");
		}else if(queryType==4){
			map.put("startDay", 1);
			map.put("unitStart", "MONTH");
		}
		
		map.put("shopUniquePub", "*************");
		return countMsgService.queryCouponPublicStatistics(map);
	}

	/**
	 * 公共优惠券店内销售统计
	 * @param map
	 * @return
	 */
	@RequestMapping("/queryPubCouponMsg.do")
	@ResponseBody
	public ShopsResult queryPubCouponMsg(
			@RequestParam(value="shopUnique")String shopUnique,
			@RequestParam(value="queryType",defaultValue="3")Integer queryType){
		Map<String,Object> map=new HashMap<String,Object>();
		map.put("shopUnique", shopUnique);
		if(queryType==3){//最近一周
			map.put("startDay", 1);
			map.put("unitStart", "WEEK");
		}else if(queryType==4){
			map.put("startDay", 1);
			map.put("unitStart", "MONTH");
		}
		map.put("shopUniquePub", "*************");
		return countMsgService.queryPubCouponMsg(map);
	}
	
	/**
	 * 本店优惠券发放及使用情况
	 * @param map
	 * @return
	 */
	@RequestMapping("/ourShopCouponMsg.do")
	@ResponseBody
	public ShopsResult ourShopCouponMsg(@RequestParam(value="shopUnique")String shopUnique,
			@RequestParam(value="queryType",defaultValue="3")Integer queryType){
		Map<String,Object> map=new HashMap<String,Object>();
		map.put("shopUnique", shopUnique);
		if(queryType==1){
			map.put("startDay", 1);
			map.put("unitStart", "DAY");
		}else if(queryType==3){//最近一周
			map.put("startDay", 1);
			map.put("unitStart", "WEEK");
		}else if(queryType==4){
			map.put("startDay", 1);
			map.put("unitStart", "MONTH");
		}
		map.put("pubShopUnique", "*************");
		return countMsgService.ourShopCouponMsg(map);
	}
	@RequestMapping("/ourCouponUseMsgByDay.do")
	@ResponseBody
	public ShopsResult ourCouponUseMsgByDay(
			@RequestParam(value="shopUnique")String shopUnique,
			@RequestParam(value="queryType",defaultValue="3")Integer queryType){
		Map<String,Object> map=new HashMap<String,Object>();
		map.put("shopUnique", shopUnique);
		if(queryType==3){//最近一周
			map.put("startDay", 1);
			map.put("unitStart", "WEEK");
		}else if(queryType==4){
			map.put("startDay", 1);
			map.put("unitStart", "MONTH");
		}
		map.put("pubShopUnique", "*************");
		return countMsgService.ourCouponUseMsgByDay(map);
	}
	
	@RequestMapping("/accountStatistics.do")
	@ResponseBody
	public ShopsResult accountStatistics(@RequestParam(value="shopUnique")String shopUnique,
			@RequestParam(value="queryType",defaultValue="3")Integer queryType){
		Map<String,Object> map=new HashMap<String,Object>();
		map.put("shopUnique", shopUnique);
		if(queryType==1){
			map.put("startDay", 1);
			map.put("unitStart", "DAY");
		}else if(queryType==3){//最近一周
			map.put("startDay", 1);
			map.put("unitStart", "WEEK");
		}else if(queryType==4){
			map.put("startDay", 1);
			map.put("unitStart", "MONTH");
		}
		map.put("pubShopUnique", "*************");
		return countMsgService.accountStatistics(map);
	}

	@RequestMapping("/queryTakeCashList.do")
	@ResponseBody
	public ShopsResult queryTakeCashList(
			@RequestParam(value="shopUnique",required=true)String shopUnique,
			@RequestParam(value="pageNum",defaultValue="1")Integer pageNum,
			@RequestParam(value="pageSize",defaultValue="8")Integer pageSize
			){
		Map<String,Object> map=new HashMap<String,Object>();
		map.put("shopUnique", shopUnique);
		map.put("startNum", (pageNum-1)*pageSize);
		map.put("pageSize", pageSize);
		return countMsgService.queryTakeCashList(map);
	}
	
	@RequestMapping("/orderNetWork.do")
	public String orderNetWork(){
		return "/WEB-INF/countMessage/countDetail/orderNetWork.jsp";
	}
	
	/**
	 * 订单收入详情界面及分页
	 * @param map
	 * @return
	 */
	@RequestMapping("/queryOrderListNetWork.do")
	@ResponseBody
	public ShopsResult queryOrderListNetWork(
			@RequestParam(value="shopUnique",required=true)String shopUnique,
			@RequestParam(value="pageNum",defaultValue="1")Integer pageNum,
			@RequestParam(value="pageSize",defaultValue="8")Integer pageSize,
			String startTime,
			String endTime,
			Integer saleType,
			Integer deliveryType,
			Integer couponType,
			Integer checkBeans,
			Integer checkPoints
			){
		Map<String,Object> map=new HashMap<String,Object>();
		map.put("shopUnique", shopUnique);
		map.put("startNum", (pageNum-1)*pageSize);
		map.put("pageNum", pageNum);
		map.put("pageSize", pageSize);
		map.put("startTime", startTime);
		map.put("endTime", endTime);
		map.put("saleType", saleType);
		map.put("deliveryType", deliveryType);
		map.put("couponType", couponType);
		map.put("checkBeans", checkBeans);
		map.put("checkPoints", checkPoints);
		map.put("pubShopUnique", "*************");
		map.put("order", "saleListDatetime");
		map.put("orderType", "DESC");
		return countMsgService.queryOrderListNetWork(map);
	}
}
