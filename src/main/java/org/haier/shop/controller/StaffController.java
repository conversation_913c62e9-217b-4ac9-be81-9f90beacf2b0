package org.haier.shop.controller;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import cn.hutool.core.util.ObjectUtil;
import org.haier.log.annotation.RemoteLog;
import org.haier.log.enums.BusinessType;
import org.haier.shop.entity.SysRole;
import org.haier.shop.params.shopStaff.ShopIoBoundInspectEditParams;
import org.haier.shop.result.shop.ShopConfigQueryResult;
import org.haier.shop.service.ShopService;
import org.haier.shop.service.StaffService;
import org.haier.shop.service.SysRoleService;
import org.haier.shop.test.QRCodeUtil;
import org.haier.shop.util.PurResult;
import org.haier.shop.util.ShopsResult;
import org.haier.shop.util.ShopsUtil;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;


@Controller
@RequestMapping("/shopsStaff")
public class StaffController {
	@Resource
	private StaffService staffService;
	
	@Resource
	private SysRoleService roleService;
	
	@Resource
	private ShopService shopService;
	
	/**
	 * 下载员工二维码
	 * @param staffId
	 * @param shopUnique
	 * @param request
	 * @return
	 */
	@RequestMapping("/downLoadStaffCode.do")
	@ResponseBody
	public ShopsResult downLoadStaffCode(String staffId,String shopUnique,String staffName,HttpServletRequest request) {
		ShopsResult sr = new ShopsResult(1,"成功!");
		
//		String filePath = request.getServletContext().getRealPath("");// 获取项目所在的绝对路径
		String filePath = "/mnt/myData/tomcat/tomcat6/webapps/image/staff/";//文件存储绝对路径
		
		String fileName = shopUnique + staffId + ".jpg";
		
		String text = "http://test170.buyhoo.cc/service?params={\"shopUnique\":" + shopUnique + ",\"staffId\":" + staffId + "}";
		try {
			QRCodeUtil.encode(text, "/mnt/myData/tomcat/tomcat6/webapps/image/staff/test.jpg", filePath ,fileName , staffName, true);
		}catch (Exception e) {
			e.printStackTrace();
		}
		
		Map<String,Object> map = new HashMap<String,Object>();
		map.put("downLoadUrl", "http://test170.buyhoo.cc/image/staff/" + fileName);
		map.put("downLoadName", fileName);
		sr.setData(map);
		
		
		return sr;
	}
	//员工列表页面
	@RequestMapping("/queryStaffListPage.do")
	public String queryStaffListPage(){
		return "/WEB-INF/manager/queryStaffList.jsp";
	}
	//新增员工页面
	@RequestMapping("/addStaffPage.do")
	public String addStaffPage(Model model){
		return "/WEB-INF/manager/addStaff.jsp";
	}
	//查看员工页面
	@RequestMapping("/detailStaffPage.do")
	public String detailStaffPage(Integer staffId,HttpServletRequest request,Model model){
		//查询员工信息
		Map<String, Object> result= staffService.getStaffById(staffId);
		request.setAttribute("staff", result);
			
		return "/WEB-INF/manager/detailStaff.jsp";
	}
	//修改员工页面
	@RequestMapping("/editStaffPage.do")
	public String editStaffPage(Integer staffId,HttpServletRequest request,Model model){
		//查询员工信息
		Map<String, Object> result= staffService.getStaffById(staffId);
		request.setAttribute("staff", result);
		//查询该店铺角色列表
		Map<String ,Object> params = new HashMap<String, Object>();
		params.put("shop_unique", result.get("shop_unique"));
		List<SysRole> roleList = roleService.quertRoleList(params);
		model.addAttribute("roleList", roleList);
		return "/WEB-INF/manager/editStaff.jsp";
	}

	@RequestMapping("/ioBoundInspect.do")
	public String ioBoundInspect(String shopUnique, String shopName, Model model) {
		model.addAttribute("shopUnique", shopUnique);
		model.addAttribute("shopName", shopName);
		ShopConfigQueryResult shopConfigQueryResult = staffService.queryShopConfig(shopUnique);
		if (ObjectUtil.isNotEmpty(shopConfigQueryResult)) {
			model.addAttribute("ioBoundInspect", shopConfigQueryResult.getIsIoboundInspect());
		} else {
			model.addAttribute("ioBoundInspect", 0);
		}
		return "/WEB-INF/manager/ioBoundInspect.jsp";
	}

	   //修改起的信息页面
	@RequestMapping("/editMyStaffPage.do")
	public String editMyStaffPage(Integer staffId,HttpServletRequest request,Model model){
		//查询员工信息
		Map<String, Object> result= staffService.getStaffById(staffId);
		request.setAttribute("staff", result);
		//查询该店铺角色列表
		Map<String ,Object> params = new HashMap<String, Object>();
		params.put("shop_unique", result.get("shop_unique"));
		List<SysRole> roleList = roleService.quertRoleList(params);
		model.addAttribute("roleList", roleList);
		return "/WEB-INF/manager/editMyStaff.jsp";
	}
	
	//员工业绩页面
	@RequestMapping("/queryStaffAchievePage.do")
	public String queryStaffAchievePage(){
		return "/WEB-INF/manager/queryStaffAchieve.jsp";
	}
	//分店列表页面
	@RequestMapping("/queryBranchShopListPage.do")
	public String queryBranchShopListPage(){
		return "/WEB-INF/manager/queryBranchShopList.jsp";
	}
	//新增分店页面
	@RequestMapping("/addShopPage.do")
	public String addShopPage(){
		return "/WEB-INF/manager/addShop.jsp";
	}
	//修改分店页面
	@RequestMapping("/editShopPage.do")
	public String editShopPage(Long shopUnique,HttpServletRequest request){
		//查询店铺信息
		Map<String, Object> result= staffService.queryShopInfoByShopUnique(shopUnique);
		request.setAttribute("shop", result);
		
		//查询分店的营业时间信息
		
		List<Map<String ,Object>> shopHoursList = shopService.getShopHoursList(shopUnique+"");
		request.setAttribute("shopHoursList", shopHoursList);
		return "/WEB-INF/manager/editShop.jsp";
	}
	/**
	 * 管理员登录
	 * @param map
	 * @return
	 */
	@RequestMapping("/staffLoginByAccountPwd.do")
	@ResponseBody
	public ShopsResult staffLoginByAccountPwd(
				HttpServletRequest request,
				HttpServletResponse response,
				@RequestParam(value="staffAccount",required=true)String staffAccount,
				@RequestParam(value="staffPwd",required=true)String staffPwd
			){
		Map<String,Object> map=new HashMap<String, Object>();
		map.put("staffAccount", staffAccount);
		map.put("staffPwd", ShopsUtil.string2MD5(staffPwd));
		return staffService.staffLoginByAccountPwd(request,map);
	}
	/**
	 * 更新管理员信息后，查询管理员更新后的信息
	 * @param staffAccount
	 * @return
	 */
	@RequestMapping("/staffNewMessage.do")
	@ResponseBody
	public ShopsResult staffNewMessage(
				@RequestParam(value="staffAccount",required=true)Long staffAccount,
				HttpServletRequest request
			){
		Map<String,Object> map=new HashMap<String, Object>();
		map.put("staffAccount", staffAccount);
		return staffService.staffLoginByAccountPwd(request,map);
	}
	/**
	 * 
	 * 查询管理员旗下所有店铺
	 * @param map
	 * @return
	 */
	@RequestMapping("/queryShopManager.do")
	@ResponseBody
	public ShopsResult queryShopManager(
			@RequestParam(value="managerUnique",required=true)String managerUnique
			){
		Map<String,Object> map =new HashMap<String, Object>();
		map.put("managerUnique", managerUnique);
		return staffService.queryShopManager(map);
	}
	/**
	 * 修改员工权限
	 * @param map
	 * @return
	 */
	@RequestMapping("/modifyStaffPower.do")
	@ResponseBody
	public ShopsResult modifyStaffPower(
			@RequestParam(value="staffId",required=true) Integer staffId,
			@RequestParam(value="powerPrice",required=true) Integer powerPrice,
			@RequestParam(value="powerCount",required=true) Integer powerCount,
			@RequestParam(value="powerSupplier",required=true) Integer powerSupplier,
			@RequestParam(value="powerKind",required=true) Integer powerKind,
			@RequestParam(value="powerInPrice",required=true) Integer powerInPrice,
			@RequestParam(value="powerName",required=true) Integer powerName,
			@RequestParam(value="powerDelete",required=true) Integer powerDelete,
			@RequestParam(value="powerPur",required=true) Integer powerPur,
			@RequestParam(value="powerAdd",required=true) Integer powerAdd,
			@RequestParam(value="powerRecharge",required=true)Integer powerRecharge
			){
		Map<String,Object> map=new HashMap<String, Object>();
		map.put("staffId", staffId);
		map.put("powerPrice", powerPrice);
		map.put("powerCount", powerCount);
		map.put("powerKind", powerKind);
		map.put("powerSupplier", powerSupplier);
		map.put("powerInPrice", powerInPrice);
		map.put("powerName", powerName);
		map.put("powerDelete", powerDelete);
		map.put("powerPur", powerPur);
		map.put("powerAdd", powerAdd);
		map.put("powerRecharge", powerRecharge);
		System.out.println(map);
		return staffService.modifyStaffPower(map);
	}
	
	/**
	 * 根据店铺名获取相关管理员信息
	 * @param map
	 * @return
	 */
	@RequestMapping("/queryShopManagers.do")
	@ResponseBody
	public ShopsResult queryShopManagers(
				@RequestParam(value="shopUnique",required=true)Long shopUnique
			){
		Map<String,Object> map=new HashMap<String, Object>();
		map.put("shopUnique", shopUnique);
		return staffService.queryShopManagers(map);
	}
	

	/**
	 * 根据员工编号，修改相应权限
	 * @param map
	 * @return
	 */
	@RequestMapping("/queryManagerPower.do")
	@ResponseBody
	public ShopsResult queryManagerPower(
			@RequestParam(value="staffId",required=true)Integer staffId){
		Map<String,Object> map=new HashMap<String, Object>();
		map.put("staffId", staffId);
		return staffService.queryManagerPower(map);
	}
	
	/**
	 * 会员信息页数查询
	 * @param map
	 * @return
	 */
	@RequestMapping("/queryStaffsPages.do")
	@ResponseBody
	public ShopsResult queryStaffsPages(
			@RequestParam(value="managerUnique",required=true) Long managerUnique,
			@RequestParam(value="pageSize",defaultValue="25")int pageSize,
			String staffMessage
			){
		Map<String,Object> map=new HashMap<String, Object>();
		map.put("managerUnique", managerUnique);
		map.put("pageSize", pageSize);
		if(null!=staffMessage){
			map.put("staffMessage", "%"+staffMessage+"%");
		}
		return staffService.queryStaffsPages(map);
	}
	
	
	/**
	 * 员工信息分页查询
	 * @param map
	 * @return
	 */
	@RequestMapping("/queryStaffByPage.do")
	@ResponseBody
	public PurResult queryStaffByPage(
			@RequestParam(value="managerUnique")Long managerUnique,
			@RequestParam(value="shopUnique")String shopUnique,
			@RequestParam(value="shopClass")Integer shopClass,
			@RequestParam(value = "page", defaultValue = "0") int page,
			@RequestParam(value = "limit", defaultValue = "8") int pageSize,
			String staffMessage,
			String staffShops
			){
		Map<String,Object> map=new HashMap<String, Object>();
		if(shopClass == 1){
			map.put("managerUnique", managerUnique);
		}else{
			map.put("shopUnique", shopUnique);
		}
		map.put("pageNum", pageSize);
		map.put("pageSize", pageSize);
		map.put("startNum", (page-1)*pageSize);
		if(null!=staffMessage && !staffMessage.equals("")){
			map.put("staffMessage", "%"+staffMessage+"%");
		}
		map.put("staffShops",staffShops);
		return staffService.queryStaffByPage(map);
	}
	
	/**
	 * 查询员工信息详情
	 * @param map
	 * @return
	 */
	@RequestMapping("/queryStaffDetailMessage.do")
	@ResponseBody
	public ShopsResult queryStaffDetailMessage(
			@RequestParam(value="staffId",required=true)Integer staffId
			){
		Map<String,Object> map=new HashMap<String, Object>();
		map.put("staffId", staffId);
		return staffService.queryStaffDetailMessage(map);
	}
	
	
	/**
	 * 更新员工信息
	 * @param map
	 * @return
	 */
	@RemoteLog(title="修改员工信息", businessType = BusinessType.UPDATE)
	@RequestMapping("/updateStaffBaseMessage.do")
	@ResponseBody
	public ShopsResult updateStaffBaseMessage(
			HttpServletRequest request,
			@RequestParam(value="shopUnique",required=true)String shopUnique,
			@RequestParam(value="staffId",required=true)Integer staffId,
			@RequestParam(value="staffName",required=true)String staffName,
			@RequestParam(value="staffPhone",required=true)String staffPhone,
			String staffAccount,
			String staffAccount_old,
			String staffPwd,
			String county,
			@RequestParam(value="staffPosition",required=true)Integer staffPosition,
//			@RequestParam(value="powerPrice",required=true)Integer powerPrice,
//			@RequestParam(value="powerCount",required=true)Integer powerCount,
//			@RequestParam(value="powerSupplier",required=true)Integer powerSupplier,
//			@RequestParam(value="powerKind",required=true)Integer powerKind,
//			@RequestParam(value="powerInPrice",required=true)Integer powerInPrice,
//			@RequestParam(value="powerName",required=true)Integer powerName,
//			@RequestParam(value="powerDelete",required=true)Integer powerDelete,	
//			@RequestParam(value="powerPur",required=true)Integer powerPur,
//			@RequestParam(value="powerAdd",required=true)Integer powerAdd,
//			@RequestParam(value="powerRecharge",required=true)Integer powerRecharge,
//			@RequestParam(value="bean",required=true)Integer bean,
			String nShopUnique,
			String role_code
			){
		Map<String,Object> map=new HashMap<String, Object>();
		map.put("shopUnique", shopUnique);
		map.put("staffName", staffName);
		map.put("staffPhone", staffPhone);
		map.put("staffAccount", staffAccount);
		map.put("county", county);
//		map.put("staffBirthday", staffBirthday);
		map.put("nShopUnique", nShopUnique);
		if(null!=staffPwd&&!staffPwd.trim().equals("")){
			map.put("staffPwd", ShopsUtil.string2MD5(staffPwd));//需加密
			map.put("staffPwdOk", staffPwd);
		}
		map.put("staffPosition", staffPosition);
		map.put("staffId", staffId);
//		map.put("powerPrice", powerPrice);
//		map.put("powerCount", powerCount);
//		map.put("powerSupplier", powerSupplier);
//		map.put("powerKind", powerKind);
//		map.put("powerInPrice", powerInPrice);
//		map.put("powerName", powerName);
//		map.put("powerPur", powerPur);
//		map.put("powerAdd", powerAdd);
//		map.put("powerDelete", powerDelete);
//		map.put("powerRecharge", powerRecharge);
//		map.put("bean", bean);
//		return staffService.updateStaffBaseMessage(map, request, shopUnique, staffId);
		return staffService.updateStaffBaseMessage(map,role_code,staffAccount_old);
	}
	
	/**
	 * 添加新的员工信息
	 * @return
	 */
	@RemoteLog(title="添加员工", businessType = BusinessType.INSERT)
	@RequestMapping("/addNewStaff.do")
	@ResponseBody
	public ShopsResult addNewStaff(
			HttpServletRequest request,
			@RequestParam(value="shopUnique",required=true)Long shopUnique,
			@RequestParam(value="staffAccount",required=true)String staffAccount,
			@RequestParam(value="staffPwd",defaultValue="123456")String staffPwd,
			@RequestParam(value="staffName",required=true)String staffName,
			String staffPhone,
			String county,
			String managerUnique,
			@RequestParam(value="staffPosition",required=true)Integer  staffPosition,
//			@RequestParam(value="powerPrice",required=true)Integer powerPrice,
//			@RequestParam(value="powerCount",required=true)Integer powerCount,
//			@RequestParam(value="powerSupplier",required=true)Integer powerSupplier,
//			@RequestParam(value="powerKind",required=true)Integer powerKind,
//			@RequestParam(value="powerInPrice",required=true)Integer powerInPrice,
//			@RequestParam(value="powerName",required=true)Integer powerName,
//			@RequestParam(value="powerDelete",required=true)Integer powerDelete,
//			@RequestParam(value="powerPur",required=true)Integer powerPur,
//			@RequestParam(value="powerAdd",required=true)Integer powerAdd,
//			@RequestParam(value="powerRecharge",required=true)Integer powerRecharge
			String role_code
			){
//		return staffService.addNewStaff(request,shopUnique, staffAccount, staffPwd, staffName, staffPhone, staffPosition, 
//				 powerPrice, powerCount, powerSupplier, powerKind, powerInPrice, powerName, powerDelete, powerPur, powerAdd,powerRecharge);
		return staffService.addNewStaff(request,shopUnique, staffAccount, staffPwd, staffName, staffPhone, staffPosition, 
				 role_code,county,managerUnique);
	}
	
	/**
	 * 查询同管理员的店铺信息列表
	 * @param map
	 * @return
	 */
	@RequestMapping("/queryAllShopsByPage.do")
	@ResponseBody
	public PurResult queryAllShopsByPage(
			@RequestParam(value="shopMessage",defaultValue="")String shopMessage,
			@RequestParam(value="managerUnique",required=true)Long managerUnique,
			@RequestParam(value = "page", defaultValue = "1") int page,
			@RequestParam(value = "limit", defaultValue = "15") int pageSize
			){
		Map<String,Object> map=new HashMap<String, Object>();
		map.put("managerUnique", managerUnique);
		map.put("pageSize", pageSize);
		map.put("startNum", (page-1)*pageSize);
		if(shopMessage!=null&&!"".equals(shopMessage)){
			map.put("shopMessage", "%"+shopMessage+"%");
		}
		return staffService.queryAllShopsByPage(map);
	}
	
	/**
	 * 员工业绩查询
	 * @param map
	 * @return
	 */
	@RequestMapping("/queryGoodsSaleByStaffPage.do")
	@ResponseBody
	public PurResult queryGoodsSaleByStaffPage(
			@RequestParam(value="startTime")String startTime,
			@RequestParam(value="endTime")String endTime,
			Integer staffId,
			String goodsMessage,
			String managerUnique,
			Long shopUnique,
			@RequestParam(value = "page", defaultValue = "0") int page,
			@RequestParam(value = "limit", defaultValue = "8") int pageSize
			){
		Map<String,Object> map=new HashMap<String, Object>();
		map.put("shopUnique", shopUnique);
		map.put("startTime", startTime);
		map.put("endTime", endTime);
		map.put("pageSize", pageSize);
		
		if(staffId!=null && staffId!=-1){
			map.put("staffId", staffId);
		}
		if(null!=goodsMessage&&!goodsMessage.equals("")){
			map.put("goodsMessage", "%"+goodsMessage+"%");
		}
		map.put("startNum", (page-1)*pageSize);
		return staffService.queryGoodsSaleByStaffPage(map);
	}
	
	//地图选点页面
	@RequestMapping("/selectMap.do")
	public String selectMap(){
		return "/WEB-INF/manager/selectMap.jsp";
	}
	
	//地图选点页面
	@RequestMapping("/selectMapApp.do")
	public String selectMapApp(){
		return "/WEB-INF/manager/selectMapApp.jsp";
	}
	
	/**
	 * 删除员工
	 * @param staffId 员工id
	 * @return
	 */
	@RemoteLog(title="删除员工", businessType = BusinessType.DELETE)
	@RequestMapping("/deleteStaff.do")
	@ResponseBody
	public PurResult deleteStaff(String staffId){
		return staffService.deleteStaff(staffId);
	}
	
	/**
	 * 查询该店铺角色列表
	 * @param shop_unique 店铺编码
	 * @return
	 */
	@RequestMapping("/queryRoleListByShop.do")
	@ResponseBody
	public PurResult queryRoleListByShop(String shop_unique){
		PurResult result = new PurResult();
		try {
			Map<String ,Object> params = new HashMap<String, Object>();
			params.put("shop_unique", shop_unique);
			List<SysRole> roleList = roleService.quertRoleList(params);
			result.setData(roleList);
	    	result.setStatus(1);
			result.setMsg("成功");
		}catch (Exception e) {
			e.printStackTrace();
			result.setStatus(0);
			result.setMsg("异常");
		}
		return result;
	}

	/**
	 * 设置库存管理方式
	 *
	 * @return
	 */
	@PostMapping("/editIoBoundInspect.do")
	@ResponseBody
	public ShopsResult editIoBoundInspect(@Validated ShopIoBoundInspectEditParams params){
		return staffService.editIoBoundInspect(params);
	}
}
