package org.haier.shop.controller;

import java.util.HashMap;
import java.util.Map;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;

import org.apache.shiro.SecurityUtils;
import org.apache.shiro.session.Session;
import org.apache.shiro.subject.Subject;
import org.haier.log.annotation.RemoteLog;
import org.haier.log.enums.BusinessType;
import org.haier.meituan.util.MUtil;
import org.haier.shop.entity.ShopCourier;
import org.haier.shop.entity.Staff;
import org.haier.shop.service.DeliveryService;
import org.haier.shop.service.ShopService;
import org.haier.shop.service.SysAgreementService;
import org.haier.shop.util.PurResult;
import org.haier.shop.util.ShopsResult;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

@Controller
@RequestMapping("/delivery")
public class DeliveryController {
	
	@Resource
	private DeliveryService deliveryService;
	
	@Resource
	private ShopService shopService;
	
	@Resource
	private SysAgreementService sysAgreementService;
	
	/**
	 * 查询配送信息
	 */
	@RequestMapping("/queryShopDelivery.do")
	public String queryShopDelivery(Model model){
		Subject subject = SecurityUtils.getSubject();
		Session session = subject.getSession();
		Staff staff = (Staff) session.getAttribute("staff");
		String shop_unique = String.valueOf(staff.getShop_unique());
		
		//微信小程序开通状态：0:不显示 1:显示 2:审核中 3:审核失败
		Integer show_buy_status = 0;
		String show_buy_fail_reason = "";
		//获取商家信息
		ShopsResult result = shopService.queryShopMessage(shop_unique);
		if(result.getStatus() == 0){
			Map<String,Object> shopInfo = (Map<String, Object>) result.getData();
			show_buy_status = Integer.parseInt(MUtil.strObject(shopInfo.get("show_buy_status")));
			show_buy_fail_reason = MUtil.strObject(shopInfo.get("show_buy_fail_reason"));
		}
		if(show_buy_status == 0){
			//获取微信小程序开通协议
			Map<String ,Object> agreement = sysAgreementService.querySysAgreement("wechat");
			model.addAttribute("agreement", agreement);
			
			return "/WEB-INF/wechatApplet/protocol.jsp";
		}else if(show_buy_status == 1) {
			//获取店铺配送信息
			Map<String ,Object> deliveryInfo = deliveryService.queryShopDelivery(shop_unique);
			model.addAttribute("deliveryInfo", deliveryInfo);
			
			return "/WEB-INF/delivery/shopDelivery.jsp";
		}else{
			model.addAttribute("show_buy_status", show_buy_status);
			model.addAttribute("show_buy_fail_reason", show_buy_fail_reason);
			return "/WEB-INF/wechatApplet/showBuyStatus.jsp";
		}
	}
	
	/**
	 * 修改配送信息
	 * @param shop_unique 店铺唯一标示
	 * @param distribution_scope 配送范围，单位米
	 * @param delivery_type 配送方式：0自配送 1美团配送 2蜂鸟配送
	 * @param take_fee 起送费（元）
	 * @param take_free_price 免配送费价格（元）
	 * @param take_estimate_time 配送预计时长（分钟）
	 * @param shop_take_price 商家自配送配送费
	 * @param is_order_taking 是否自动接单：0是 1否
	 * @param subsidy_delivery_price 每单补贴配送费
	 * @return
	 */
	@RemoteLog(title = "修改配送信息", businessType = BusinessType.UPDATE)
	@RequestMapping("/updateShopDelivery.do")
	@ResponseBody
	public ShopsResult updateShopDelivery(
			String shop_unique,String distribution_scope,String delivery_type,
			String take_fee,String take_free_price,String take_estimate_time,
			String shop_take_price,String is_order_taking,String subsidy_delivery_price){
		ShopsResult shopsResult = new ShopsResult();
		try {
			Map<String ,Object> params = new HashMap<String, Object>();
			params.put("shop_unique", shop_unique);
			params.put("distribution_scope", distribution_scope);
			params.put("delivery_type", delivery_type);
			params.put("take_fee", take_fee);
			params.put("take_free_price", take_free_price);
			params.put("take_estimate_time", take_estimate_time);
			params.put("shop_take_price", shop_take_price);
			params.put("is_order_taking", is_order_taking);
			params.put("subsidy_delivery_price", subsidy_delivery_price);
			shopsResult = deliveryService.updateShopDelivery(params);
		} catch (Exception e) {
			shopsResult.setStatus(0);
			shopsResult.setMsg("异常："+e.getMessage());
		}
		return shopsResult;
	}
	
	/**
	 * 跳转店铺骑手列表页面
	 */
	@RequestMapping("/getShopCourierList.do")
	public String getShopCourierList(String type,Model model){
		Subject subject = SecurityUtils.getSubject();
		Session session = subject.getSession();
		Staff staff = (Staff) session.getAttribute("staff");
		String shop_unique = String.valueOf(staff.getShop_unique());
		
		//微信小程序开通状态：0:不显示 1:显示 2:审核中 3:审核失败
		Integer show_buy_status = 0;
		String show_buy_fail_reason = "";
		//获取商家信息
		ShopsResult result = shopService.queryShopMessage(shop_unique);
		if(result.getStatus() == 0){
			Map<String,Object> shopInfo = (Map<String, Object>) result.getData();
			show_buy_status = Integer.parseInt(MUtil.strObject(shopInfo.get("show_buy_status")));
			show_buy_fail_reason = MUtil.strObject(shopInfo.get("show_buy_fail_reason"));
		}
		if(show_buy_status == 0){
			//获取微信小程序开通协议
			Map<String ,Object> agreement = sysAgreementService.querySysAgreement("wechat");
			model.addAttribute("agreement", agreement);
			
			return "/WEB-INF/wechatApplet/protocol.jsp";
		}else if(show_buy_status == 1) {
			model.addAttribute("type", type);
			return "/WEB-INF/courier/courierList.jsp";
		}else{
			model.addAttribute("show_buy_status", show_buy_status);
			model.addAttribute("show_buy_fail_reason", show_buy_fail_reason);
			return "/WEB-INF/wechatApplet/showBuyStatus.jsp";
		}
	}
	
	/**
	 * 店铺骑手列表分页查询
	 * @param map
	 * @return
	 */
	@RequestMapping("/quertShopCourierList.do")
	@ResponseBody
	public PurResult quertShopCourierList(
			@RequestParam(value = "page", defaultValue = "0") int page,
			@RequestParam(value = "limit", defaultValue = "8") int pageSize,
			ShopCourier shopCourier){
		PurResult result = new PurResult();
		try {
			shopCourier.setPageSize(pageSize);
			shopCourier.setStartNum((page-1)*pageSize);
			
			result = deliveryService.getShopCourierList(shopCourier);
		} catch (Exception e) {
			e.printStackTrace();
			result.setStatus(0);
			result.setMsg("异常："+e.getMessage());
		}
		
		return result;
	}
	
	/**
	 * 添加店铺骑手页面
	 */
	@RequestMapping("/addShopCourierPage.do")
	public String addShopCourierPage(HttpServletRequest request, Model model){
		return "/WEB-INF/courier/addShopCourier.jsp";
	}
	
	/**
	 * 添加店铺关联一刻钟骑手页面
	 */
	@RequestMapping("/addShopRider.do")
	public String addShopRider(HttpServletRequest request, Model model){
		return "/WEB-INF/courier/addShopRider.jsp";
	}
	
	/**
	 * 添加店铺骑手
	 * @param shop_unique 店铺唯一标示
	 * @param courier_name 配送员姓名
	 * @param courier_phone 配送员电话
	 * @return
	 */
	@RemoteLog(title = "添加店铺骑手", businessType = BusinessType.INSERT)
	@RequestMapping("/addShopCourier.do")
	@ResponseBody
	public ShopsResult addShopCourier(ShopCourier shopCourier){
		ShopsResult shopsResult = new ShopsResult();
		try {
			shopsResult = deliveryService.addShopCourier(shopCourier);
		} catch (Exception e) {
			shopsResult.setStatus(0);
			shopsResult.setMsg("异常："+e.getMessage());
		}
		return shopsResult;
	}
	
	/**
	 * 修改店铺骑手页面
	 */
	@RemoteLog(title = "修改店铺骑手", businessType = BusinessType.UPDATE)
	@RequestMapping("/updateShopCourierPage.do")
	public String updateShopCourierPage(String courier_id, Model model){
		//获取店铺骑手信息
		ShopCourier shopCourier = deliveryService.getShopCourier(courier_id);
		
		model.addAttribute("shopCourier", shopCourier);
		
		return "/WEB-INF/courier/updateShopCourier.jsp";
	}
	
	/**
	 * 添加店铺骑手
	 * @param id 配送员id
	 * @param courier_name 配送员姓名
	 * @param courier_phone 配送员电话
	 * @return
	 */
	@RequestMapping("/updateShopCourier.do")
	@ResponseBody
	public ShopsResult updateShopCourier(ShopCourier shopCourier){
		ShopsResult shopsResult = new ShopsResult();
		try {
			shopsResult = deliveryService.updateShopCourier(shopCourier);
		} catch (Exception e) {
			shopsResult.setStatus(0);
			shopsResult.setMsg("异常："+e.getMessage());
		}
		return shopsResult;
	}
	
	/**
	 * 删除店铺骑手
	 * @param courier_id 配送员id
	 * @return
	 */
	@RemoteLog(title = "删除店铺骑手", businessType = BusinessType.DELETE)
	@RequestMapping("/deleteShopCourier.do")
	@ResponseBody
	public ShopsResult deleteShopCourier(String courier_id){
		ShopsResult shopsResult = new ShopsResult();
		try {
			shopsResult = deliveryService.deleteShopCourier(courier_id);
		} catch (Exception e) {
			shopsResult.setStatus(0);
			shopsResult.setMsg("异常："+e.getMessage());
		}
		return shopsResult;
	}
	
	/**
	 * 跳转特殊商品列表页面
	 */
	@RequestMapping("/getShopSpecialList.do")
	public String getShopSpecialList(){
		return "/WEB-INF/delivery/specialGoods.jsp";
	}
	
	/**
	 * 店铺特殊商品列表分页查询
	 * @param shop_unique 店铺标示
	 * @param search_message 搜索关键字
	 * @return
	 */
	@RequestMapping("/queryShopSpecialList.do")
	@ResponseBody
	public PurResult queryShopSpecialList(
			@RequestParam(value = "page", defaultValue = "0") int page,
			@RequestParam(value = "limit", defaultValue = "8") int pageSize,
			String shop_unique ,String search_message){
		PurResult result = new PurResult();
		try {
			Map<String ,Object> params = new HashMap<String, Object>();
			params.put("shop_unique", shop_unique);
			params.put("search_message", search_message);
			params.put("pageSize", pageSize);
			params.put("startNum", (page-1)*pageSize);
			
			result = deliveryService.getShopSpecialList(params);
		} catch (Exception e) {
			e.printStackTrace();
			result.setStatus(0);
			result.setMsg("异常："+e.getMessage());
		}
		
		return result;
	}
	
	/**
	 * 删除店铺特殊商品
	 * @param goods_id 商品id
	 * @return
	 */
	@RequestMapping("/deleteShopSpecial.do")
	@ResponseBody
	public ShopsResult deleteShopSpecial(String goods_id){
		ShopsResult shopsResult = new ShopsResult();
		try {
			shopsResult = deliveryService.deleteShopSpecial(goods_id);
		} catch (Exception e) {
			shopsResult.setStatus(0);
			shopsResult.setMsg("异常："+e.getMessage());
		}
		return shopsResult;
	}
	
	/**
	 * 跳转添加特殊商品页面
	 */
	@RequestMapping("/addShopSpecialPage.do")
	public String addShopSpecialPage(){
		return "/WEB-INF/delivery/addSpecialGoods.jsp";
	}
	
	/**
	 * 店铺未加入特殊商品列表分页查询
	 * @param shop_unique 店铺标示
	 * @param search_message 搜索关键字
	 * @return
	 */
	@RequestMapping("/getShopGoodsList.do")
	@ResponseBody
	public PurResult getShopGoodsList(
			@RequestParam(value = "page", defaultValue = "0") int page,
			@RequestParam(value = "limit", defaultValue = "8") int pageSize,
			String shop_unique ,String search_message){
		PurResult result = new PurResult();
		try {
			Map<String ,Object> params = new HashMap<String, Object>();
			params.put("shop_unique", shop_unique);
			params.put("search_message", search_message);
			params.put("pageSize", pageSize);
			params.put("startNum", (page-1)*pageSize);
			
			result = deliveryService.getShopGoodsList(params);
		} catch (Exception e) {
			e.printStackTrace();
			result.setStatus(0);
			result.setMsg("异常："+e.getMessage());
		}
		
		return result;
	}
	
	/**
	 * 添加店铺特殊商品
	 * @param goods_infos 特殊商品信息：goods_id:is_one_delivery:single_delivery_fee,goods_id:is_one_delivery:single_delivery_fee...
	 * @return
	 */
	@RequestMapping("/addShopSpecial.do")
	@ResponseBody
	public ShopsResult addShopSpecial(String goods_infos){
		ShopsResult shopsResult = new ShopsResult();
		try {
			shopsResult = deliveryService.addShopSpecial(goods_infos);
		} catch (Exception e) {
			shopsResult.setStatus(0);
			shopsResult.setMsg("异常："+e.getMessage());
		}
		return shopsResult;
	}	
}
