package org.haier.shop.controller;

import javax.annotation.Resource;

import org.haier.shop.service.ShopCouponCashCountService;
import org.haier.shop.util.PurResult;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

/**
 * 超市提现统计查询
 * <AUTHOR>
 *
 */
@Controller
@RequestMapping("/shopCouponCashCount")
public class ShopCouponCashCountController {
	@Resource
	private ShopCouponCashCountService shopCouponCashCountService;
	
	//查询店铺余额,已提现金额,待处理金额
	@RequestMapping("/queryShopCashBalanceInfo.do")
	@ResponseBody
	public PurResult queryShopCashBalanceInfo(String shop_unique){
			return shopCouponCashCountService.queryShopCashBalanceInfo(shop_unique);
	}
	//查询店铺线上订单相关信息
	@RequestMapping("/queryShopOrderCountInfo.do")
	@ResponseBody
	public PurResult queryShopOrderCountInfo(String shop_unique,String type){
		return shopCouponCashCountService.queryShopOrderCountInfo(shop_unique,type);
	}
	//查询提现记录页面
	@RequestMapping("/queryShopCashLogPage.do")
	public String queryFeedBackListPage(){
		return "/WEB-INF/manager/queryShopCashLogPage.jsp";
	}
	//查询提现记录接口
	@RequestMapping("/queryShopCashLog.do")
	@ResponseBody
	public PurResult queryShopCashLog(String shop_unique,String startDate,String endDate,String cash_mode,String handle_status,
			@RequestParam(value="page",defaultValue="1")Integer page,
			@RequestParam(value="limit",defaultValue="8")Integer limit){
		return shopCouponCashCountService.queryShopCashLog(shop_unique,startDate,endDate,cash_mode,handle_status,page,limit);
	}
	//查询线上订单记录页面
	@RequestMapping("/queryOnlineOrderListPage.do")
	public String queryOnlineOrderListPage(){
//		System.out.println("queryOnlineOrderListPage.do");
		return "/WEB-INF/manager/queryOnlineOrderListPage.jsp";
	}
	
	//查询线上订单记录接口
	@RequestMapping("/queryOnlineOrderList.do")
	@ResponseBody
	public PurResult queryOnlineOrderList(
			String shopUnique,
			String startTime,
			String endTime,
			@RequestParam(value="page",defaultValue="1")Integer page,
			@RequestParam(value="limit",defaultValue="8")Integer limit,
			Integer saleListHandleState
			){
		
		return shopCouponCashCountService.queryOnlineOrderList(shopUnique,startTime,endTime,page,limit,saleListHandleState);
	}
	
	
	
	
}
