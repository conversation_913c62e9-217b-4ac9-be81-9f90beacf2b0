package org.haier.shop.controller;

import java.util.HashMap;
import java.util.Map;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.haier.meituan.util.MUtil;
import org.haier.shop.service.ExpressService;
import org.haier.shop.util.HandleMessyCode;
import org.haier.shop.util.PurResult;
import org.haier.shop.util.ServletsUtil;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

@RequestMapping("/express")
@Controller
public class ExpressController {
	
	private static final Logger logger = LoggerFactory.getLogger(ExpressController.class);
	
	@Resource
	private ExpressService expressService;
	
	@RequestMapping(value = "/expressListPage.do")
    public String expressListPage(){
    	logger.info("后台管理-快递公司信息列表页面");
        return "/WEB-INF/manager/queryExpressList.jsp";
    }
    
	
	@RequestMapping(value = "/addPage.do")
    public String addPage(){
    	logger.info("后台管理-添加快递信息页面");
        return "/WEB-INF/manager/addExpress.jsp";
    }
    
    @RequestMapping(value="/queryList.do")
	@ResponseBody
	public PurResult queryList(
			HttpServletRequest request,
			@RequestParam(value = "page", defaultValue = "0") int page,
			@RequestParam(value = "limit", defaultValue = "8") int pageSize){
    	Map<String, Object> params = HandleMessyCode.handle(ServletsUtil.getParameters(request));
		params.put("limit", pageSize);
		params.put("page", (page-1)*pageSize);
		
		PurResult result = new PurResult();
		try {
			result = expressService.getExpressList(params);
		} catch (Exception e) {
			logger.error("列表查询异常：",e);
			e.printStackTrace();
			result.setStatus(0);
			result.setMsg("异常");
		}
		return result;
	}
    
    @RequestMapping(value = "/addExpress.do")
    @ResponseBody
    public PurResult addExpress(String express_name,String staff_id,HttpServletRequest request){
    	logger.info("后台管理-添加快递信息");
    	PurResult result = new PurResult();
    	try {
    		Map<String, Object> params = new HashMap<String, Object>();
    		params.put("express_name", express_name);
    		params.put("staff_id", staff_id);
    		result = expressService.addExpress(params, request);
		} catch (Exception e) {
			logger.info("后台管理-添加快递信息异常："+e.getMessage());
			result.setStatus(0);
			result.setMsg("异常");
		}
        return result;
    }
    
    @RequestMapping(value = "/getExpress.do")
    public String getExpress(String express_id,Model model){
    	logger.info("后台管理-获取快递信息详情");
    	try {
    		Map<String, Object> express = expressService.getExpress(express_id);
    		model.addAttribute("express", express);
		} catch (Exception e) {
			return "/error";
		}
    	return "/WEB-INF/manager/editExpress.jsp";
    }
    
    @RequestMapping(value = "/updateExpress.do")
    @ResponseBody
    public PurResult updateExpress(String express_name,String express_id,String staff_id,HttpServletRequest request){
    	logger.info("后台管理-修改快递信息");
    	PurResult result = new PurResult();
    	try {
    		Map<String, Object> params = new HashMap<String, Object>();
    		params.put("express_name", express_name);
    		params.put("express_id", express_id);
    		params.put("staff_id", staff_id);
    		result = expressService.updateExpress(params, request);
		} catch (Exception e) {
			logger.info("后台管理-修改快递信息异常："+e.getMessage());
			result.setStatus(0);
			result.setMsg("异常");
		}
        return result;
    }
    
    @RequestMapping(value = "/deleteExpress.do")
    @ResponseBody
    public PurResult deleteExpress(String express_id){
    	logger.info("后台管理-删除快递信息");
    	PurResult result = new PurResult();
    	try {	
    		result = expressService.deleteExpress(express_id);
		} catch (Exception e) {
			logger.info("后台管理-删除快递信息异常："+e.getMessage());
			result.setStatus(0);
			result.setMsg("异常");
		}
        return result;
    }
    
    @RequestMapping(value = "/queryExpressPriceListPage.do")
    public String queryExpressPriceListPage(){
    	logger.info("后台管理-快递公司信息列表页面");
        return "/WEB-INF/express/queryExpressPriceList.jsp";
    }
    
    /**
	 * 查询快递费用地区列表
	 * @return
	 */
	@RequestMapping("/queryExpressPriceList.do")
	@ResponseBody
	public PurResult queryDictList(HttpServletRequest request,
			@RequestParam(value="page",defaultValue="1")Integer page,
			@RequestParam(value="limit",defaultValue="15")Integer limit){		
		PurResult result = new PurResult();
		try {
			Map<String, Object> params = HandleMessyCode.handle(ServletsUtil.getParameters(request));
			String area_dict_content = MUtil.strObject(params.get("area_dict_content"));
			System.out.println("---------"+area_dict_content);
			if(area_dict_content != null && !area_dict_content.equals("")){
				params.put("area_dict_content", "%"+area_dict_content+"%");
			}else{
				params.remove("area_dict_content");
			}
			params.put("limit", limit);
			params.put("page", (page-1)*limit);
			result=expressService.queryExpressPriceList(params);
		} catch (Exception e) {
			result.setStatus(0);
			result.setData(null);
			result.setMsg("查询失败!");
			e.printStackTrace();
		}		
		return result;
	}
	
	 @RequestMapping(value = "/addExpressPricePage.do")
	 public String addExpressPricePage(){
		 return "/WEB-INF/express/addExpressPrice.jsp";
     }
	 //查询一级区域
	 @RequestMapping(value = "/queryParentAddress.do")
	 @ResponseBody
	 public PurResult queryParentAddress(){
		PurResult result = new PurResult();
		try {	
			result = expressService.queryParentAddress();
		} catch (Exception e) {
			result.setStatus(0);
			result.setMsg("异常");
		}
	    return result;
	 }
	 //添加快递费用
	 @RequestMapping(value = "/addExpressPrice.do")
	 @ResponseBody
	 public PurResult addExpressPrice(String address_list,String first_price,String count_price){
		 PurResult result = new PurResult();
		 try {	
			 result = expressService.addExpressPrice(address_list,first_price,count_price);
		 } catch (Exception e) {
			 result.setStatus(0);
			 result.setMsg("异常");
		 }
		 return result;
	 }
	 
	 /**
	 * 修改快递费用页面
	 * @return
	 */
	@RequestMapping("/queryEditExpressPricePage.do")
	public String queryEditExpressPricePage(String express_price_id,HttpServletRequest request){
		request.setAttribute("express_price_id", express_price_id);
		return "/WEB-INF/express/editExpressPrice.jsp";
	}
	//查询快递费用详情
    @RequestMapping(value = "/queryExpressPriceDetail.do")
    @ResponseBody
    public PurResult queryExpressPriceDetail(String express_price_id){
   	PurResult result = new PurResult();
   	try {	
   		result = expressService.queryExpressPriceDetail(express_price_id);
   	} catch (Exception e) {
   		result.setStatus(0);
   		result.setMsg("异常");
   	}
       return result;
    }
    
	 //修改快递费用
	 @RequestMapping(value = "/editExpressPrice.do")
	 @ResponseBody
	 public PurResult editExpressPrice(String address_list,String first_price,String count_price,String express_price_id){
		 PurResult result = new PurResult();
		 try {	
			 result = expressService.editExpressPrice(address_list,first_price,count_price,express_price_id);
		 } catch (Exception e) {
			 result.setStatus(0);
			 result.setMsg("异常");
		 }
		 return result;
	 }
	 /**
      * 删除地区
      * @return
      */
     @RequestMapping("/deleteExpressPrice.do")
     @ResponseBody
     public PurResult deleteExpressPrice(
     		String express_price_id
     		){		
     	PurResult result = new PurResult();
     	try {
     		result=expressService.deleteExpressPrice(express_price_id);
     	} catch (Exception e) {
     		result.setStatus(0);
     		result.setData(null);
     		result.setMsg("查询失败!");
     		e.printStackTrace();
     	}		
     	return result;
     }
}
