package org.haier.shop.controller;

import java.util.HashMap;
import java.util.Map;

import javax.annotation.Resource;

import org.haier.shop.service.ShopFunctionService;
import org.haier.shop.util.ShopsResult;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

@Controller
@RequestMapping("/shopFunction")
public class ShopFunctionController {
	
	@Resource
	private ShopFunctionService funService;
	
	@RequestMapping("/getShopsSet.do")
	@ResponseBody
	public ShopsResult queryShopFunction(
			@RequestParam(value="shopUnique",required=true)Long shopUnique
			){
		Map<String,Object> map=new HashMap<String, Object>();
		map.put("shopUnique", shopUnique);
		return funService.queryShopFunction(map);
	}
}
