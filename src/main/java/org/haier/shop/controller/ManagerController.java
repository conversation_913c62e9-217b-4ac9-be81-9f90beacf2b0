package org.haier.shop.controller;

import net.sf.json.JSONObject;
import org.apache.shiro.SecurityUtils;
import org.apache.shiro.subject.Subject;
import org.haier.shop.controller.config.RemoteConfig;
import org.haier.shop.dao.BusinessDao;
import org.haier.shop.service.ManagerService;
import org.haier.shop.service.ShopService;
import org.haier.shop.util.HttpGetUtil;
import org.haier.shop.util.PurResult;
import org.haier.shop.util.ShopsResult;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.HttpSession;
import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.util.HashMap;
import java.util.Map;
@Controller
@RequestMapping("/manager")
public class ManagerController {
	private static final Logger logger = LoggerFactory.getLogger(ManagerController.class);
	@Resource
	private ManagerService manService;
	
	@Resource
	private ShopService shopService;
	
	@Resource
	private BusinessDao businessDao;

	@RequestMapping("/mainPage.do")
	public String mainPage(Model model){
//		//是否允许自采购:0 不允许 1允许
//		Integer isSelfPurchase = 1;
//		//获取商家信息
//		Map<String ,Object> params = new HashMap<String, Object>();
//		params.put("shop_unique", shop_unique);
//		ShopsResult result = shopService.queryShopMessage(shop_unique);
//		if(result.getStatus() == 0){
//			Map<String,Object> shopInfo = (Map<String, Object>) result.getData();
//			Integer shop_class = Integer.parseInt(MUtil.strObject(shopInfo.get("shop_class")));//店铺分类：0普通商家；1：连锁；2加盟
//			String is_other_purchase = MUtil.strObject(shopInfo.get("is_other_purchase"));//是否允许像其他供货商采购：0不允许 1允许
//			if(shop_class != 0 && is_other_purchase.equals("0")){//连锁加盟并且不允许向其他供货商采购
//				isSelfPurchase = 0;
//			}
//		}
//		model.addAttribute("isSelfPurchase", isSelfPurchase);
//		return "/WEB-INF/main.jsp";
		Subject subject = SecurityUtils.getSubject();
        try {
            if (!subject.isAuthenticated()) {  
            	return "redirect:/login";  
	        }
        }catch(Exception e){
        	logger.error("加载首页异常："+e);
	    }
        model.addAttribute("msgCount", businessDao.queryPlatformMsgCount(null));

		System.out.println("初始化AI数据" + RemoteConfig.AIURL);
		model.addAttribute("aiUrl", RemoteConfig.AIURL);
        return "/WEB-INF/main.jsp";
	}
	
	@RequestMapping("/toYinongMainPage.do")
	public String toYinongMainPage(Model model){
		Subject subject = SecurityUtils.getSubject();
        try {
            if (!subject.isAuthenticated()) {  
            	return "redirect:/login";  
	        }
        }catch(Exception e){
        	logger.error("加载首页异常："+e);
	    }
        return "/WEB-INF/yinongMain.jsp";
	}
	//退出登录
	@RequestMapping("/loginout.do")
	public String loginout(HttpServletRequest request,HttpServletResponse response){
		HttpSession session = request.getSession();
		session.setAttribute("shop_unique","" );
		return "/WEB-INF/login_new.jsp";	
	}
	
	/**
	 * 注册新帐户
	 * @param manager_account
	 * @param manager_pwd
	 * @param manager_phone
	 * @param manager_name
	 * @return
	 */
	@RequestMapping("register.do")
	@ResponseBody
	public ShopsResult addNewManager(String manager_account,String manager_pwd,String manager_phone,String manager_name){
		return manService.addNewManager(manager_account, manager_pwd, manager_phone, manager_name);
	}
	
	
	
	/**
	 * 会员信息详情查询
	 * @param map
	 * @return
	 */
	@RequestMapping("/queryCusDetail.do")
	@ResponseBody
	public ShopsResult queryCusDetail(
			@RequestParam(value="cusId")Integer cusId,
			@RequestParam(value="shopUnique")Long shopUnique
			){
		Map<String,Object> map=new HashMap<String, Object>();
		map.put("cusId", cusId);
		map.put("shopUnique", shopUnique);
		return manService.queryCusDetail(map);
	}
	
	/**
	 * 天气
	 */
	@RequestMapping("/getWeather.do")
	@ResponseBody
	public PurResult getWeather(
			String city
			){
		String result = null;
		PurResult result2=new PurResult();
		try {
			result = HttpGetUtil.sendGet("https://way.jd.com/jisuapi/weather", "city="+URLEncoder.encode(city,"UTF-8")+"&appkey=e442991d65698f5bbddc2e9b9881e1a1");
		} catch (UnsupportedEncodingException e) {
			e.printStackTrace();
		}
		JSONObject jsonObject = JSONObject.fromObject(result);
		JSONObject personList = (JSONObject) jsonObject.get("result");
		if(personList!=null){
			JSONObject str = (JSONObject) personList.get("result");
			String date = str.getString("date");
			String week = str.getString("week");
			String weather = str.getString("weather");
			String templow = str.getString("templow");
			String temphigh = str.getString("temphigh");
			Map<String, Object> map=new HashMap<String, Object>();
			map.put("date", date);
			map.put("week", week);
			map.put("weather", weather);
			map.put("templow", templow);
			map.put("temphigh", temphigh);
			result2.setData(map);
		}else{
			result2.setData(new HashMap<String, Object>());
			
		}
		return result2;
	}
	
	@RequestMapping("/bankManager.do")
	public String bankManager(){
		return "/WEB-INF/manager/bankManager.jsp";
	}
	
	@RequestMapping("/promptManager.do")
	public String promptManager(){
		return "/WEB-INF/manager/promptManager.jsp"				;
	}
}
