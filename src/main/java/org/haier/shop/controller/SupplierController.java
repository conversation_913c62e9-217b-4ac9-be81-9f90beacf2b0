package org.haier.shop.controller;

import java.math.BigDecimal;
import java.util.HashMap;
import java.util.Map;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;

import org.apache.shiro.SecurityUtils;
import org.apache.shiro.session.Session;
import org.apache.shiro.subject.Subject;
import org.haier.log.annotation.RemoteLog;
import org.haier.log.enums.BusinessType;
import org.haier.shop.entity.Staff;
import org.haier.shop.service.SupplierService;
import org.haier.shop.util.PurResult;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import com.alibaba.fastjson.JSONArray;

@Controller
@RequestMapping("/html/supplier")
public class SupplierController {
	@Resource
	private SupplierService supplierService;
	
	
	@RequestMapping("/goodsSaleStockDetail.do")
	public String goodsSaleStockDetail() {
		return "/WEB-INF/purchase/goodsSaleStockDetail.jsp";
	}
	@RequestMapping("/supGoodsSaleList.do")
	public String supGoodsSaleList() {
		return "/WEB-INF/purchase/supGoodsSaleList.jsp";
	}
	@RequestMapping("/querySupplierListPage.do")
	public String querySupplierListPage(){
		return "/WEB-INF/purchase/querySupplierList.jsp";
	}
	
	@RequestMapping("/addSupplierPage.do")
	public String addSupplierPage(){
		return "/WEB-INF/purchase/addSupplier.jsp";
	}
	
	@RequestMapping("/addStoragePage.do")
	public String addStoragePage(){
		return "/WEB-INF/purchase/addStorage.jsp";
	}
	@RequestMapping("/supGoodsList.do")
	public String supGoodsList(String supplierUnique,String supplierName,String supplierType,Model model){
		model.addAttribute("supplierUnique", supplierUnique);
		model.addAttribute("supplierName", supplierName);
		model.addAttribute("supplierType", supplierType);
		return "/WEB-INF/purchase/supGoodsList.jsp";
	}
	
	@RequestMapping("/editSupplierPage.do")
	public String editSupplierPage(Integer supplier_id,HttpServletRequest request){
		Map<String, Object> result=supplierService.getSupplierById(supplier_id);
		request.setAttribute("shop", result);
		return "/WEB-INF/purchase/editSupplier.jsp";
	}
	
	
	@RequestMapping("/deleteAllGoodsSupplier.do")
	@ResponseBody
	public PurResult deleteAllGoodsSupplier(
			@RequestParam(value="shopUnique")String shopUnique,
			@RequestParam(value="supplierUnique")String supplierUnique) {
		try {
			return supplierService.deleteAllGoodsSupplier(shopUnique, supplierUnique);
		}catch (Exception e) {
			PurResult pr = new PurResult();
			return pr;
		}
	}
	
	@RequestMapping("/querySupplierList.do")
	@ResponseBody
	public PurResult querySupplierList(
			@RequestParam(value="shop_unique",required=true)String shop_unique,
			@RequestParam(value = "page", defaultValue = "1") int page,
			@RequestParam(value = "limit", defaultValue = "8") int pageSize,
			String supMsg
			){
		return supplierService.querySupplierList(shop_unique, page, pageSize,supMsg);
	}
	@RequestMapping("/getMapCenter.do")
	@ResponseBody
	public PurResult getMapCenter(
			@RequestParam(value="shop_unique",required=true)String shop_unique){
		return supplierService.getMapCenter(shop_unique);
	}
	
	/**
	 * 添加供货商
	 * @param shop_unique 店铺编号
	 * @param shop_name 店铺名称
	 * @param shop_address_detail 详细地址
	 * @param shop_phone 联系方式
	 * @param manager_name 管理员名称
	 * @param shop_latitude 纬度
	 * @param shop_longitude 经度
	 * @param province 省
	 * @param city 市
	 * @param district 区
	 * @param supplier_kind_id 分类
	 * @param settlementBank 结算银行
	 * @param settlementName 持卡人姓名
	 * @param settlementCard 卡号
	 * @return
	 */
	@RemoteLog(title = "添加供货商", businessType = BusinessType.INSERT)
	@RequestMapping("/saveSupplier.do")
	@ResponseBody
	public PurResult saveSupplier(
			@RequestParam(value="shop_unique",required=true)String shop_unique,
			@RequestParam(value="shop_name",required=true)String shop_name,
			@RequestParam(value="shop_address_detail",required=true)String shop_address_detail,
			@RequestParam(value="shop_phone",required=true)String shop_phone,
			@RequestParam(value="manager_name",required=true)String manager_name,
			@RequestParam(value="shop_latitude",required=true)BigDecimal shop_latitude,
			@RequestParam(value="shop_longitude",required=true)BigDecimal shop_longitude,
			@RequestParam(value="province",required=true)String province,
			@RequestParam(value="city",required=true)String city,
			@RequestParam(value="district",required=true)String district,
			@RequestParam(value="supplier_kind_id",required=true)String supplier_kind_id,
			String supplierUnique,
			String settlementBank,
			String settlementName,
			String settlementCard,
			String supplierRemark
			
			){
		return supplierService.saveSupplier(shop_unique,shop_name,shop_address_detail,shop_phone
				,manager_name,shop_latitude,shop_longitude,province,city,district,supplier_kind_id,
				settlementBank,settlementName,settlementCard,supplierRemark,supplierUnique);
	}
	//修改商家
	@RemoteLog(title = "修改供货商", businessType = BusinessType.UPDATE)
	@RequestMapping("/editSupplier.do")
	@ResponseBody
	public PurResult editSupplier(
			@RequestParam(value="supplier_unique",required=true)String supplier_unique,
			@RequestParam(value="shop_name",required=true)String shop_name,
			@RequestParam(value="shop_address_detail",required=true)String shop_address_detail,
			@RequestParam(value="shop_phone",required=true)String shop_phone,
			@RequestParam(value="manager_name",required=true)String manager_name,
			@RequestParam(value="shop_latitude",required=true)BigDecimal shop_latitude,
			@RequestParam(value="shop_longitude",required=true)BigDecimal shop_longitude,
			@RequestParam(value="province",required=true)String province,
			@RequestParam(value="city",required=true)String city,
			@RequestParam(value="district",required=true)String district,
			@RequestParam(value="shop_unique",required=true)String shop_unique,
			@RequestParam(value="supplier_kind_id",required=true)String supplier_kind_id,
			String settlementBank,
			String settlementName,
			String settlementCard,
			String supplierRemark
			){
		return supplierService.editSupplier(supplier_unique,shop_name,shop_address_detail,shop_phone
				,manager_name,shop_latitude,shop_longitude,province,city,district,shop_unique,supplier_kind_id,
				settlementBank,settlementName,settlementCard,supplierRemark);
	}
	//删除
	@RemoteLog(title = "删除供货商", businessType = BusinessType.DELETE)
	@RequestMapping("/deleteP.do")
	@ResponseBody
	public PurResult deleteP(
			@RequestParam(value="supplier_id",required=true)Integer supplier_id
			){
		return supplierService.deleteP(supplier_id);
	}
	//查询供货商根据地区
	@RequestMapping("/querySupplierByContries.do")
	@ResponseBody
	public PurResult querySupplierByContries(
			@RequestParam(value="area_dict_num",required=true)String area_dict_num){
		return supplierService.querySupplierByContries(area_dict_num);
	}
	//查询供货商根据名字
	@RequestMapping("/querySupplierByName.do")
	@ResponseBody
	public PurResult querySupplierByName(
			@RequestParam(value="searchShopName",required=true)String searchShopName){
		return supplierService.querySupplierByName(searchShopName);
	}
	/**
	 * 提交订单
	 * @param supplier_unique
	 * @param uniques
	 * @return
	 */
	@ResponseBody
	@RequestMapping("/submitOrder.do")
	public PurResult submitOrder(
			String supplier_unique,
			String shop_unique,
			Integer purchase_list_sum,
			BigDecimal purchase_list_total,
			String detailJson,
			HttpServletRequest request
			){
		//从session获取shop_unique
		Subject subject = SecurityUtils.getSubject();
		Session session = subject.getSession();
		 //获取用户信息
		Staff staff = (Staff) session.getAttribute("staff");
		String login_shop_unique = staff.getShop_unique().toString();
		return supplierService.submitOrder( supplier_unique,shop_unique,purchase_list_sum,purchase_list_total,detailJson,login_shop_unique);
	}
	
	/**
	 * 供应商供应商品界面-分页查询
	 * @param map
	 * @return
	 */
	@RequestMapping("/querySupGoodsByPage.do")
	@ResponseBody
	public PurResult querySupGoodsByPage(
		@RequestParam(value="shopUnique",required=true)String shopUnique,
		@RequestParam(value="supplierUnique",required=true)String supplierUnique,
		@RequestParam(value = "page", defaultValue = "1") int page,
		@RequestParam(value = "limit", defaultValue = "8") int pageSize,
		String goodsMsg
			){
		Map<String,Object> map=new HashMap<String,Object>();
		map.put("shopUnique", shopUnique);
		map.put("supplierUnique", supplierUnique);
		map.put("pageSize", pageSize);
		map.put("startNum", (page-1)*pageSize);
		if(null!=goodsMsg&&!goodsMsg.trim().equals("")){
			map.put("goodsMsg", "%"+goodsMsg+"%");
		}
		return supplierService.querySupGoodsByPage(map);
	}
	
	/**
	 * 供应商供应商品界面-分页查询
	 * @param map
	 * @return
	 */
	@RequestMapping("/querySupGoodsPages.do")
	@ResponseBody
	public PurResult querySupGoodsPages(@RequestParam(value="shopUnique",required=true)String shopUnique,
			@RequestParam(value="supplierUnique",required=true)String supplierUnique,
			@RequestParam(value="pageSize",defaultValue="8")Integer pageSize,
			String goodsMsg){
		Map<String,Object> map=new HashMap<String,Object>();
		map.put("shopUnique", shopUnique);
		map.put("supplierUnique", supplierUnique);
		map.put("pageSize", pageSize);
		if(null!=goodsMsg&&!goodsMsg.trim().equals("")){
			map.put("goodsMsg", "%"+goodsMsg+"%");
		}
		return supplierService.querySupGoodsPages(map);
	}
	
	/**
	 * 清除店内商品的供货商信息
	 * @param map
	 * @return
	 */
	@RequestMapping("/clearSupGoodsMsgPage.do")
	@ResponseBody
	public PurResult clearSupGoodsMsgPage(
			String shopUnique,
			String supplierUnique,
			String goodsList
			){
		Map<String,Object> map=new HashMap<String,Object>();
		map.put("shopUnique", shopUnique);
		map.put("supplierUnique", supplierUnique);
		JSONArray list=JSONArray.parseArray(goodsList);
		System.out.println(list.size());
		if(list.size()>0){
			map.put("list", list);
		}
		return supplierService.clearSupGoodsMsgPage(map);
	}
	
	/**
	 * 商品进价信息修改
	 * @param goodsBarcode
	 * @param shopUnique
	 * @param goodsInPrice
	 * @return
	 */
	@RequestMapping("saveGoodsInPrice.do")
	@ResponseBody
	public PurResult saveGoodsInPrice(
			String goodsBarcode,
			String shopUnique,
			Double goodsInPrice
			){
		Map<String,Object> map=new HashMap<String,Object>();
		map.put("shopUnique", shopUnique);
		map.put("goodsBarcode", goodsBarcode);
		map.put("goodsInPrice", goodsInPrice);
		return supplierService.saveGoodsInPrice(map);
	}
	/**
	 * 供货商分类查询页面
	 * @return
	 */
	@RequestMapping("/querySupplierKindListPage.do")
	public String querySupplierKindListPage(){
		return "/WEB-INF/purchase/querySupplierKindList.jsp";
	}
	
	@RequestMapping("/querySupplierKindList.do")
	@ResponseBody
	public PurResult querySupplierKindList(
			@RequestParam(value="shop_unique",required=true)String shop_unique,
			@RequestParam(value = "page", defaultValue = "1") int page,
			@RequestParam(value = "limit", defaultValue = "8") int pageSize,
			String supMsg
			){
		return supplierService.querySupplierKindList(shop_unique, page, pageSize,supMsg);
	}
	@RequestMapping("/addSupplierKindPage.do")
	public String addSupplierKindPage(){
		return "/WEB-INF/purchase/addSupplierKind.jsp";
	}
	
	//添加供货商分类
	@RemoteLog(title = "添加供货商分类", businessType = BusinessType.INSERT)
	@RequestMapping("/saveSupplierKind.do")
	@ResponseBody
	public PurResult saveSupplierKind(
			@RequestParam(value="shop_unique",required=true)String shop_unique,
			@RequestParam(value="supplier_kind_name",required=true)String supplier_kind_name
			){
		return supplierService.saveSupplierKind(shop_unique,supplier_kind_name);
	}
	//删除供货商分类
	@RemoteLog(title = "删除供货商分类", businessType = BusinessType.DELETE)
	@RequestMapping("/deleteSupplierKind.do")
	@ResponseBody
	public PurResult deleteSupplierKind(
			@RequestParam(value="id",required=true)Integer id
			){
		return supplierService.deleteSupplierKind(id);
	}
	@RemoteLog(title = "修改供货商分类", businessType = BusinessType.UPDATE)
	@RequestMapping("/editSupplierKindPage.do")
	public String editSupplierKindPage(Integer id,HttpServletRequest request){
		Map<String, Object> result=supplierService.getSupplierKindById(id);
		request.setAttribute("shop", result);
		return "/WEB-INF/purchase/editSupplierKind.jsp";
	}
	
	//修改供货商分类
	@RequestMapping("/editSupplierKind.do")
	@ResponseBody
	public PurResult editSupplierKind(
			@RequestParam(value="id",required=true)String id,
			@RequestParam(value="supplier_kind_name",required=true)String supplier_kind_name
			){
		return supplierService.editSupplierKind(id,supplier_kind_name);
	}
	//查询供货商分类
	@RequestMapping("/querySupplierKindByShopUnique.do")
	@ResponseBody
	public PurResult querySupplierKindByShopUnique(
			@RequestParam(value="shop_unique",required=true)String shop_unique
			){
		return supplierService.querySupplierKindByShopUnique(shop_unique);
	}
}
