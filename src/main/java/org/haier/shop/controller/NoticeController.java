package org.haier.shop.controller;

import java.util.HashMap;
import java.util.Map;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;

import org.haier.shop.service.NoticeService;
import org.haier.shop.util.PurResult;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

@RequestMapping("/notice")
@Controller
public class NoticeController {
	
	@Resource
	private NoticeService noticeService;
	
	/**
	 * 跳转列表页面
	 * @return
	 */
	@RequestMapping("/noticeListPage.do")
	public String noticeListPage(){
		return "/WEB-INF/notice/noticeList.jsp";
	}
	
	/**
	 * 查询列表
	 * @return
	 */
	@RequestMapping("/noticeList.do")
	@ResponseBody
	public PurResult noticeList(
			String notice_type,
			String start_date,
			String end_date,
			@RequestParam(value = "page", defaultValue = "1") int page,
			@RequestParam(value = "limit", defaultValue = "8") int pageSize){
		Map<String,Object> params = new HashMap<String, Object>();
		params.put("notice_type", notice_type);
		params.put("start_date", start_date);
		params.put("end_date", end_date);
		params.put("pageSize", pageSize);
		params.put("startNum", (page-1)*pageSize);
		return noticeService.queryNoticeList(params);
	}
	
	
	/**
	 * 跳转添加页面
	 * @return
	 */
	@RequestMapping("/addNoticePage.do")
	public String addNoticePage(){
		return "/WEB-INF/notice/addNotice.jsp";
	}
	
	/**
	 * 添加
	 * @return
	 */
	@RequestMapping("/addNotice.do")
	@ResponseBody
	public PurResult addNotice(HttpServletRequest request){
		return noticeService.addNotice(request);
	}
	
	/**
	 * 跳转修改页面
	 * @return
	 */
	@RequestMapping("/updateNoticePage.do")
	public String updateNoticePage(String notice_id,Model model){
		//获取详情
		Map<String ,Object> noticeDetail = noticeService.queryNoticeDetail(notice_id);
		model.addAttribute("noticeDetail", noticeDetail);
		return "/WEB-INF/notice/updateNotice.jsp";
	}
	
	/**
	 * 修改
	 * @return
	 */
	@RequestMapping("/updateNotice.do")
	@ResponseBody
	public PurResult updateNotice(HttpServletRequest request){
		return noticeService.updateNotice(request);
	}
	
	/**
	 * 发布
	 * @return
	 */
	@RequestMapping("/releaseNotice.do")
	@ResponseBody
	public PurResult releaseNotice(String notice_id,String notice_type){
		return noticeService.releaseNotice(notice_id,notice_type);
	}
	
	/**
	 * 撤回
	 * @return
	 */
	@RequestMapping("/withdrawNotice.do")
	@ResponseBody
	public PurResult withdrawNotice(String notice_id){
		return noticeService.withdrawNotice(notice_id);
	}
	
	/**
	 * 删除
	 * @return
	 */
	@RequestMapping("/deleteNotice.do")
	@ResponseBody
	public PurResult deleteNotice(String notice_id){
		return noticeService.deleteNotice(notice_id);
	}
	
	/**
	 * 跳转详情页面
	 * @return
	 */
	@RequestMapping("/noticeDetailPage.do")
	public String noticeDetailPage(String notice_id,Model model){
		//获取详情
		Map<String ,Object> noticeDetail = noticeService.queryNoticeDetail(notice_id);
		model.addAttribute("noticeDetail", noticeDetail);
		return "/WEB-INF/notice/noticeDetail.jsp";
	}
	
}
