package org.haier.shop.controller;

import java.net.InetAddress;
import java.net.UnknownHostException;
import java.util.HashMap;
import java.util.Hashtable;
import java.util.List;
import java.util.Map;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.haier.shop.service.ShopSoftService;
import org.haier.shop.util.HandleMessyCode;
import org.haier.shop.util.PurResult;
import org.haier.shop.util.ServletsUtil;
import org.haier.shop.util.wxPay.QRUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import com.google.zxing.BarcodeFormat;
import com.google.zxing.EncodeHintType;
import com.google.zxing.MultiFormatWriter;
import com.google.zxing.common.BitMatrix;

@Controller
@RequestMapping("/shopSoft")
public class ShopSoftController{
    private static final Logger logger = LoggerFactory.getLogger(ShopSoftController.class);
    
    @Autowired
    private ShopSoftService shopSoftService;
    
    @RequestMapping(value = "/shopSoftPage.do")
    public String shopSoftPage(Model model){
    	logger.info("跳转店铺软件管理页面");
        return "/WEB-INF/device/ShopSoftList.jsp";
    }
    
    @RequestMapping(value="/shopSoftList.do")
	@ResponseBody
	public PurResult shopSoftList(
			HttpServletRequest request,
			@RequestParam(value = "page", defaultValue = "1") int page,
			@RequestParam(value = "limit", defaultValue = "8") int pageSize){
    	Map<String, Object> params = HandleMessyCode.handle(ServletsUtil.getParameters(request));
		params.put("pageSize", pageSize);
		params.put("startNum", (page-1)*pageSize);
		
		PurResult result = new PurResult();
		try {
			List<Map<String ,Object>> list = shopSoftService.queryShopSoftList(params);
	    	Integer count = shopSoftService.queryShopSoftListCount(params);
	    	result.setStatus(1);
			result.setMsg("成功");
			result.setCount(count);
			result.setData(list);
		} catch (Exception e) {
			logger.error("列表查询异常：",e);
			e.printStackTrace();
			result.setStatus(0);
			result.setMsg("异常");
		}
		return result;
	}
    
    @RequestMapping(value = "/shopSoftProfitPage.do")
    public String shopSoftProfitPage(Model model){
    	logger.info("跳转店铺软件购买记录列表页面");
        return "/WEB-INF/device/ShopSoftProfitList.jsp";
    }
    
    @RequestMapping(value="/shopSoftProfitList.do")
	@ResponseBody
	public PurResult shopSoftProfitList(
			HttpServletRequest request,
			@RequestParam(value = "page", defaultValue = "1") int page,
			@RequestParam(value = "limit", defaultValue = "8") int pageSize){
    	Map<String, Object> params = HandleMessyCode.handle(ServletsUtil.getParameters(request));
    	params.put("pageSize", pageSize);
		params.put("startNum", (page-1)*pageSize);
		
		PurResult result = new PurResult();
		try {
			List<Map<String ,Object>> list = shopSoftService.queryShopSoftProfitList(params);
	    	Integer count = shopSoftService.queryShopSoftProfitListCount(params);
	    	result.setStatus(1);
			result.setMsg("成功");
			result.setCount(count);
			result.setData(list);
		} catch (Exception e) {
			logger.error("列表查询异常：",e);
			e.printStackTrace();
			result.setStatus(0);
			result.setMsg("异常");
		}
		return result;
	}
    
    @RequestMapping(value = "/deleteShopSoftProfit.do")
    @ResponseBody
    public PurResult deleteShopSoftProfit(String id){
    	logger.info("删除店铺软件购买记录");
    	PurResult result = new PurResult();
    	try {	
    		shopSoftService.deleteShopSoftProfit(id);
    		result.setStatus(1);
    		result.setMsg("成功");
		} catch (Exception e) {
			logger.error("删除店铺软件购买记录异常：",e);
			result.setStatus(0);
			result.setMsg("异常");
		}
        return result;
    }
    
    @RequestMapping(value = "/renewPage.do")
    public String renewPage(String cdkey_code,String device_name,String device_no,Model model){
    	logger.info("跳转软件续费页面");
    	//获取软件设置列表
    	List<Map<String ,Object>> softSettingList = shopSoftService.querySysSoftSettingList();
    	model.addAttribute("softSettingList", softSettingList);
    	model.addAttribute("cdkey_code", cdkey_code);
    	model.addAttribute("device_name", device_name);
    	model.addAttribute("device_no", device_no);
        return "/WEB-INF/device/ShopSoftRenew.jsp";
    }
    
    /**
	 * 微信支付-生成二维码
	 * @param cdkey_code 激活码
	 * @param shop_unique 店铺编号
	 * @param device_no 设备编号
	 * @param setting_code 软件设置code
	 * @param bug_type 购买类型：1购买 2续费
	 * @param request
	 * @param response
	 */
	@RequestMapping("/qrcode.do")
	@ResponseBody
	public void qrcode(HttpServletRequest request,HttpServletResponse response) {
		try {
			Map<String, Object> params = HandleMessyCode.handle(ServletsUtil.getParameters(request));
	        String spbill_create_ip = getIpAddr(request);
	        params.put("spbill_create_ip", spbill_create_ip);
//	        params.put("profit_no", "SP"+System.currentTimeMillis());
	        String text = shopSoftService.weixinPay(params);
	        
	        int width = 300; 
	        int height = 300; 
	        //二维码的图片格式 
	        String format = "gif"; 
			Hashtable hints = new Hashtable(); 
	        //内容所使用编码 
	        hints.put(EncodeHintType.CHARACTER_SET, "utf-8");
	        BitMatrix bitMatrix = new MultiFormatWriter().encode(text, BarcodeFormat.QR_CODE, width, height, hints);
			QRUtil.writeToStream(bitMatrix, format, response.getOutputStream());
			
		} catch (Exception e) {
			e.printStackTrace();
		}
	}
	
	/**
	 * 支付宝支付
	 * @param cdkey_code 激活码
	 * @param shop_unique 店铺编号
	 * @param device_no 设备编号
	 * @param setting_code 软件设置code
	 * @param bug_type 购买类型：1购买 2续费
	 * @param request
	 * @param response
	 * @throws Exception
	 */
	@RequestMapping("/zhifubaoPay.do")
	public void zhifubaoPay(HttpServletRequest request,HttpServletResponse response) throws Exception {
		try {
			Map<String, Object> params = HandleMessyCode.handle(ServletsUtil.getParameters(request));
	        String spbill_create_ip = getIpAddr(request);
	        params.put("spbill_create_ip", spbill_create_ip);
//	        params.put("profit_no", "SP"+System.currentTimeMillis());
	        String text = shopSoftService.aliPay(params);
	        
	        int width = 300; 
	        int height = 300; 
	        //二维码的图片格式 
	        String format = "gif"; 
			Hashtable hints = new Hashtable(); 
	        //内容所使用编码 
	        hints.put(EncodeHintType.CHARACTER_SET, "utf-8");
	        BitMatrix bitMatrix = new MultiFormatWriter().encode(text, BarcodeFormat.QR_CODE, width, height, hints);
			QRUtil.writeToStream(bitMatrix, format, response.getOutputStream());
		}catch (Exception e) {
			e.printStackTrace();
		}
	}

	@RequestMapping(value = "/isPaySuccess.do")
    @ResponseBody
    public PurResult isPaySuccess(String profit_no,String shop_unique){
    	logger.info("验证是否支付成功");
    	PurResult result = new PurResult();
    	try {	
    		Map<String,Object> params = new HashMap();
    		params.put("profit_no", profit_no);
    		params.put("shop_unique", shop_unique);
    		result = shopSoftService.isPaySuccess(params);
		} catch (Exception e) {
			logger.error("验证是否支付成功异常：",e);
			result.setStatus(0);
			result.setMsg("异常");
		}
        return result;
    }
	
	/**
	 * 获取当前网络ip
	 * <AUTHOR>
	 * @param request
	 * @return
	 */
	public String getIpAddr(HttpServletRequest request) {
		String ipAddress = request.getHeader("x-forwarded-for");
		if (ipAddress == null || ipAddress.length() == 0 || "unknown".equalsIgnoreCase(ipAddress)) {
			ipAddress = request.getHeader("Proxy-Client-IP");
		}
		if (ipAddress == null || ipAddress.length() == 0 || "unknown".equalsIgnoreCase(ipAddress)) {
			ipAddress = request.getHeader("WL-Proxy-Client-IP");
		}
		if (ipAddress == null || ipAddress.length() == 0 || "unknown".equalsIgnoreCase(ipAddress)) {
			ipAddress = request.getRemoteAddr();
			if (ipAddress.equals("127.0.0.1") || ipAddress.equals("0:0:0:0:0:0:0:1")) {
				// 根据网卡取本机配置的IP
				InetAddress inet = null;
				try {
					inet = InetAddress.getLocalHost();
				} catch (UnknownHostException e) {
					e.printStackTrace();
				}
				ipAddress = inet.getHostAddress();
			}
		}
		// 对于通过多个代理的情况，第一个IP为客户端真实IP,多个IP按照','分割
		if (ipAddress != null && ipAddress.length() > 15) { // "***.***.***.***".length()
															// = 15
			if (ipAddress.indexOf(",") > 0) {
				ipAddress = ipAddress.substring(0, ipAddress.indexOf(","));
			}
		}
		return ipAddress;
	}
}