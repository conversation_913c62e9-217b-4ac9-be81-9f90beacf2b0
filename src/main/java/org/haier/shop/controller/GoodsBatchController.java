package org.haier.shop.controller;

import org.haier.shop.params.goodsBatch.GoodsBatchChooseParams;
import org.haier.shop.params.goodsBatch.GoodsBatchChooseViewParams;
import org.haier.shop.params.goodsBatch.GoodsBatchExportParams;
import org.haier.shop.params.goodsBatch.GoodsBatchQueryParams;
import org.haier.shop.service.GoodsBatchService;
import org.haier.shop.util.PurResult;
import org.springframework.stereotype.Controller;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.math.BigDecimal;

/**
* 商品入库批次表
* @ClassName GoodsBatch
* <AUTHOR> 
* @Date 2024-04-28
**/
@Controller
@RequestMapping("/html/goodsBatch")
public class GoodsBatchController {
    @Resource
    private GoodsBatchService goodsBatchService;
    @RequestMapping("/goodsBatchPage.do")
    public String goodBatchPage(HttpServletRequest request){
        return "/WEB-INF/goodsBatch/goodsBatchPage.jsp";
    }

    /**
     * 入库批次列表
     * @param params
     * @return
     */
    @GetMapping("/listPage.do")
    @ResponseBody
    public PurResult page(@Validated GoodsBatchQueryParams params) {
        return goodsBatchService.selectPage(params);
    }


    /**
     * 批次导出
     * @param params
     * @return
     */
    @GetMapping("/exportToExcel.do")
    public void exportToExcel(@Validated GoodsBatchExportParams params, HttpServletRequest request, HttpServletResponse response){
        goodsBatchService.exportToExcel(params, request, response);
    }

    /**
     * 批次选择页面
     * @param shopUnique
     * @param goodsName
     * @param goodsBarcode
     * @param outCount
     * @param request
     * @return
     */
    @GetMapping("/goodsBatchChoosePage.do")
    public String goodsBatchChoosePage(String shopUnique, String goodsName, String goodsBarcode, BigDecimal outCount, String goodsId, String listUnique, HttpServletRequest request) {
        request.setAttribute("shopUnique", shopUnique);
        request.setAttribute("goodsName", goodsName);
        request.setAttribute("goodsBarcode", goodsBarcode);
        request.setAttribute("outCount", outCount);
        request.setAttribute("goodsId", goodsId);
        request.setAttribute("listUnique", listUnique);
        return "/WEB-INF/goodsBatch/goodsBatchChoose.jsp";
    }

    /**
     * 入库批次选择列表
     * @param params
     * @return
     */
    @PostMapping("/batchSelectList.do")
    @ResponseBody
    public PurResult batchSelectList(@RequestBody @Validated GoodsBatchChooseParams params) {
        return goodsBatchService.batchSelectList(params);
    }

    /**
     * 批次查看页面
     * @param shopUnique
     * @param goodsName
     * @param goodsBarcode
     * @param outCount
     * @param auditStatus
     * @param request
     * @return
     */
    @GetMapping("/goodsBatchChoosePageView.do")
    public String goodsBatchChoosePageView(String shopUnique, String goodsName, String goodsBarcode, BigDecimal outCount, String goodsId, String listUnique, Integer auditStatus, HttpServletRequest request) {
        request.setAttribute("shopUnique", shopUnique);
        request.setAttribute("goodsName", goodsName);
        request.setAttribute("goodsBarcode", goodsBarcode);
        request.setAttribute("outCount", outCount);
        request.setAttribute("goodsId", goodsId);
        request.setAttribute("listUnique", listUnique);
        request.setAttribute("auditStatus", auditStatus);
        return "/WEB-INF/goodsBatch/goodsBatchChooseView.jsp";
    }


    /**
     * 批次选择预览列表
     * @param params
     * @return
     */
    @GetMapping("/batchSelectListView.do")
    @ResponseBody
    public PurResult batchSelectListView(@Validated GoodsBatchChooseViewParams params) {
        return goodsBatchService.batchSelectListView(params);
    }


    /**
     * 退货批次选择页面
     * @param shopUnique
     * @param goodsName
     * @param goodsBarcode
     * @param request
     * @return
     */
    @GetMapping("/retGoodsBatchChoosePage.do")
    public String retGoodsBatchChoosePage(String shopUnique, String goodsName, String goodsBarcode, String goodsId, HttpServletRequest request) {
        request.setAttribute("shopUnique", shopUnique);
        request.setAttribute("goodsName", goodsName);
        request.setAttribute("goodsBarcode", goodsBarcode);
        request.setAttribute("goodsId", goodsId);
        return "/WEB-INF/goodsBatch/retGoodsBatchChoose.jsp";
    }
}