package org.haier.shop.controller;


import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.haier.shop.entity.beans.Beans;
import org.haier.shop.service.LogisticsService;
import org.haier.shop.util.ShopsResult;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

/**
 * 物流统计
 * <AUTHOR>
 */
@Controller
@RequestMapping("/logistics")
public class LogisticsController extends Beans{
	@Resource
	private LogisticsService logisticsService;
	
	/**
	 * 男女比例 平均年龄
	 */
	@RequestMapping(value="/querySexList.do")  
	@ResponseBody
	public ShopsResult querySexList(HttpServletRequest request,HttpServletResponse response) {
		ShopsResult sr=new ShopsResult();
		try {
			sr=logisticsService.getSexList();
		}catch (Exception e) {
			sr.setStatus(0);
			sr.setMsg("操作失败");
			e.printStackTrace();
		}
		return sr;
	}
	
	/**
	 * 配送里程和配送时间
	 */
	@RequestMapping(value="/getPeiSongList.do")  
	@ResponseBody
	public ShopsResult getPeiSongList(HttpServletRequest request,HttpServletResponse response) {
		ShopsResult sr=new ShopsResult();
		try {
			sr=logisticsService.getPeiSongList();
		}catch (Exception e) {
			sr.setStatus(0);
			sr.setMsg("操作失败");
			e.printStackTrace();
		}
		return sr;
	}
	
	/**
	 * 一刻钟配送详情
	 */
	@RequestMapping(value="/getOneMinute.do")  
	@ResponseBody
	public ShopsResult getOneMinute(HttpServletRequest request,HttpServletResponse response) {
		ShopsResult sr=new ShopsResult();
		try {
			sr=logisticsService.getOneMinute();
		}catch (Exception e) {
			sr.setStatus(0);
			sr.setMsg("操作失败");
			e.printStackTrace();
		}
		return sr;
	}

	/**
	 * 配送方式占比
	 */
	@RequestMapping(value="/getPeiSongTypeList.do")  
	@ResponseBody
	public ShopsResult getPeiSongTypeList(HttpServletRequest request,HttpServletResponse response) {
		ShopsResult sr=new ShopsResult();
		try {
			sr=logisticsService.getPeiSongTypeList();
		}catch (Exception e) {
			sr.setStatus(0);
			sr.setMsg("操作失败");
			e.printStackTrace();
		}
		return sr;
	}
	
	/**
	 * 近7天  配送订单量统计
	 */
	@RequestMapping(value="/getOrderList.do")  
	@ResponseBody
	public ShopsResult getOrderList(HttpServletRequest request,HttpServletResponse response) {
		ShopsResult sr=new ShopsResult();
		try {
			sr=logisticsService.getOrderList();
		}catch (Exception e) {
			sr.setStatus(0);
			sr.setMsg("操作失败");
			e.printStackTrace();
		}
		return sr;
	}
	
	/**
	 * 月均活跃度
	 */
	@RequestMapping(value="/getMonthActivity.do")  
	@ResponseBody
	public ShopsResult getMonthActivity(HttpServletRequest request,HttpServletResponse response) {
		ShopsResult sr=new ShopsResult();
		try {
			sr=logisticsService.getMonthActivity();
		}catch (Exception e) {
			sr.setStatus(0);
			sr.setMsg("操作失败");
			e.printStackTrace();
		}
		return sr;
	}

}
