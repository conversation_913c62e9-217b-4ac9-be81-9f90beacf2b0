package org.haier.shop.controller;

import java.util.Map;

import javax.annotation.Resource;

import org.haier.shop.service.OilService;
import org.haier.shop.util.ShopsResult;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;


/**
 * 加油站相关功能管理
 * <AUTHOR>
 *
 */
@RequestMapping("/oil")
@Controller
public class OilController {
	
	@Resource
	private OilService OilService;
	
	/**
	 * 
	 * @param id 修改时上传ID
	 * @param shopUnique 店铺编号
	 * @param disName 名称
	 * @param startTime 活动开始时间
	 * @param endTime 活动结束时间
	 * @param payAmount 需要支付的金额
	 * @param discountMoney 给顾客优惠的金额
	 * @param activeState 1、正常；2、停用；3、删除
	 * @return
	 */
	@RequestMapping("/addNewShopDis.do")
	@ResponseBody
	public ShopsResult addNewShopDis(Integer id,String shopUnique,String disName,String startTime,
			String endTime,Double payAmount,Double discountMoney,Integer activeState) {
		return OilService.addNewShopDis(id, shopUnique, disName, startTime, endTime, payAmount, discountMoney, activeState);
	}
	
	@RequestMapping("/toAddNewDisManager.do")
	public String toAddNewDisManager() {
		return "/WEB-INF/oil/toAddNewDisManager.jsp";
	}
	
	@RequestMapping("/toShopDisManager.do")
	public String toShopDisManager() {
		return "/WEB-INF/oil/shopDisManager.jsp";
	}
	/**
	 * 查询店铺的优惠配置信息
	 * @param shopUnique
	 * @param page
	 * @param pageSize
	 * @return
	 */
	@RequestMapping("/queryShopDisList.do")
	@ResponseBody
	public ShopsResult queryShopDisList(String shopUnique,Integer page,Integer limit,String startTime,String endTime) {
		return OilService.queryShopDisList(shopUnique, page, limit,startTime,endTime);
	}
	/**
	 * 添加新的油枪油品信息
	 * @param parName 名称
	 * @param parValue 值
	 * @param stationPrice 油站价格
	 * @param interPrice 国标价
	 * @param parType 类型：1、油枪；2、油品（油品必须标注价格）
	 * @param id 当前ID信息，如果不为空，则为更新
	 * @param delFlag 1、删除；否则不操作
	 * @return
	 */
	@RequestMapping("/addNewMsg.do")
	@ResponseBody
	public ShopsResult addNewMsg(String parName,String parValue,Double stationPrice,Double interPrice,Integer id,Integer parType,Integer delFlag) {
		return OilService.addNewMsg(parName, parValue, stationPrice, interPrice, parType, id, delFlag);
	}
	/**
	 * 查询油枪，油号信息
	 * @param parType 信息类型：1、油枪；2、油品
	 * @param delFlag
	 * @return
	 */
	@RequestMapping("/queryOilMsgList.do")
	@ResponseBody
	public ShopsResult queryOilMsgList(Integer parType,Integer delFlag,Integer id) {
		return OilService.queryOilMsgList(parType, delFlag,id);
	}
	
	@RequestMapping("/toAddNewOilMsg.do")
	public String toAddNewOilMsg() {
		return "/WEB-INF/oil/toAddNewOilMsg.jsp";
	}
	
	@RequestMapping("/toAddNewGunMsg.do")
	public String toAddNewGunMsg() {
		return "/WEB-INF/oil/toAddNewGunMsg.jsp";
	}
	
	@RequestMapping("/toEditOilMsg.do")
	public String toEditOilMsg(Model model,Integer id) {
		Map<String,Object> oilMap = OilService.queryOrderMsgById(id,2);
		oilMap.put("parType", 2);
		model.addAttribute("oilMsg", oilMap);
		return "/WEB-INF/oil/toEditOilMsg.jsp";
	}
	
	@RequestMapping("/ollGunManager.do")
	public String ollGunManager() {
		return "/WEB-INF/oil/ollGunManager.jsp";
	}
	
	@RequestMapping("/oilMsgManager.do")
	public String oilMsgManager() {
		return "/WEB-INF/oil/oilMsgManager.jsp";
	}
	
	@RequestMapping("/oilGunManager.do")
	public String oilGunManager() {
		return "/WEB-INF/oil/oilGunManager.jsp";
	}
	
	@RequestMapping("/toEditOilGun.do")
	public String toEditOilGun(Model model,Integer id) {
		Map<String,Object> oilMap = OilService.queryOrderMsgById(id,1);
		model.addAttribute("oilMsg", oilMap);
		return "/WEB-INF/oil/toEditOilGun.jsp";
	}
}
