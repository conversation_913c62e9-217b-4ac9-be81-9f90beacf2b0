package org.haier.shop.controller;

import java.util.HashMap;
import java.util.Map;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;

import org.haier.shop.service.InfoPushService;
import org.haier.shop.util.PurResult;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

@RequestMapping("/infoPush")
@Controller
public class InfoPushController {
	
	@Resource
	private InfoPushService infoPushService;
	
	/**
	 * 跳转列表页面
	 * @return
	 */
	@RequestMapping("/infoListPage.do")
	public String infoListPage(){
		return "/WEB-INF/info/infoList.jsp";
	}
	
	/**
	 * 查询列表
	 * @return
	 */
	@RequestMapping("/infoList.do")
	@ResponseBody
	public PurResult infoList(
			String status,
			@RequestParam(value = "page", defaultValue = "1") int page,
			@RequestParam(value = "limit", defaultValue = "8") int pageSize){
		Map<String,Object> params = new HashMap<String, Object>();
		params.put("status", status);
		params.put("pageSize", pageSize);
		params.put("startNum", (page-1)*pageSize);
		return infoPushService.queryInfoList(params);
	}
	
	
	/**
	 * 跳转添加页面
	 * @return
	 */
	@RequestMapping("/addInfoPage.do")
	public String addAdPage(){
		return "/WEB-INF/info/addInfo.jsp";
	}
	
	/**
	 * 添加
	 * @return
	 */
	@RequestMapping("/addInfo.do")
	@ResponseBody
	public PurResult addInfo(HttpServletRequest request){
		return infoPushService.addInfo(request);
	}
	
	/**
	 * 跳转修改页面
	 * @return
	 */
	@RequestMapping("/updateInfoPage.do")
	public String updateInfoPage(String info_id,Model model){
		//获取详情
		Map<String ,Object> infoDetail = infoPushService.queryInfoDetail(info_id);
		model.addAttribute("infoDetail", infoDetail);
		return "/WEB-INF/info/updateInfo.jsp";
	}
	
	/**
	 * 修改
	 * @return
	 */
	@RequestMapping("/updateInfo.do")
	@ResponseBody
	public PurResult updateInfo(HttpServletRequest request){
		return infoPushService.updateInfo(request);
	}
	
	/**
	 * 发送消息推送
	 * @return
	 */
	@RequestMapping("/sendInfo.do")
	@ResponseBody
	public PurResult sendInfo(String info_id){
		return infoPushService.sendInfo(info_id);
	}
	
	/**
	 * 删除
	 * @return
	 */
	@RequestMapping("/deleteInfo.do")
	@ResponseBody
	public PurResult deleteInfo(String info_id){
		return infoPushService.deleteInfo(info_id);
	}
	
}
