package org.haier.shop.controller;

import java.io.UnsupportedEncodingException;
import java.util.HashMap;
import java.util.Map;

import javax.annotation.Resource;

import org.haier.shop.service.BranchStoreService;
import org.haier.shop.util.PurResult;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

/**
 */
@Controller
@RequestMapping("/branchStore")
public class BranchStoreController {
	
	@Resource
	private BranchStoreService branchStoreService;
	
	/**
	 * 总店查询分店列表
	 * @param company_code
	 * @param shop_name 
	 * @param startNum
	 * @param pageSize
	 * @return
	 * @throws UnsupportedEncodingException 
	 */
	@RequestMapping("getBranchStoreList.do")
	@ResponseBody
	public PurResult getBranchStoreList(
			@RequestParam(value="company_code",required=true)String company_code,
			String shop_name,
			@RequestParam(value="pageIndex",defaultValue="0")Integer pageIndex, 
			@RequestParam(value="pageSize",defaultValue="8")Integer pageSize
			) throws UnsupportedEncodingException{
		Map<String ,Object> params = new HashMap<String, Object>();
		params.put("company_code", company_code);
		params.put("startNum", pageIndex);
		params.put("pageSize", pageSize);
		if(shop_name != null && !shop_name.equals("")){
			params.put("shop_name", new String(shop_name.getBytes("ISO-8859-1"), "UTF-8"));
		}
		return branchStoreService.getBranchStoreList(params);
	}
	
	/**
	 * 查询分店详情
	 * @param shop_unique 店铺唯一编号
	 * @return
	 */
	@RequestMapping("getBranchStore.do")
	@ResponseBody
	public PurResult getBranchStore(String shop_unique){
		return branchStoreService.getBranchStore(shop_unique);
	}
	
	/**
	 * 删除分店信息，逻辑删除
	 * @param shop_unique 店铺唯一编号
	 * @return
	 */
	@RequestMapping("deleteBranchStore.do")
	@ResponseBody
	public PurResult deleteBranchStore(String shop_unique){
		return branchStoreService.deleteBranchStore(shop_unique);
	}
}
