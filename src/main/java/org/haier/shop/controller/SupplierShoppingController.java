package org.haier.shop.controller;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alipay.api.AlipayApiException;
import com.alipay.api.internal.util.AlipaySignature;
import com.aliyuncs.http.HttpResponse;
import com.google.zxing.BarcodeFormat;
import com.google.zxing.EncodeHintType;
import com.google.zxing.MultiFormatWriter;
import com.google.zxing.common.BitMatrix;
import com.haier.util.HelibaoPayUtil;
import com.haier.util.bean.ReturnVo;
import com.haier.vo.AppCreateOrderResponseVo;
import com.haier.vo.AppCreateOrderVo;
import com.haier.vo.NotifyResponseVo;
import org.apache.log4j.Logger;
import org.apache.shiro.SecurityUtils;
import org.apache.shiro.session.Session;
import org.apache.shiro.subject.Subject;
import org.haier.log.util.IpUtils;
import org.haier.shop.config.SysConfig;
import org.haier.shop.controller.config.SwiftpassConfig;
import org.haier.shop.dao.SupplierShoppingDao;
import org.haier.shop.dao2.ShoppingDao;
import org.haier.shop.entity.Staff;
import org.haier.shop.entity.shop.ShopList;
import org.haier.shop.entity.wj.WJGoods;
import org.haier.shop.redis.RedisCache;
import org.haier.shop.service.LoanMoneyService;
import org.haier.shop.service.LoanService;
import org.haier.shop.service.SupplierShoppingService;
import org.haier.shop.thread.SortThread;
import org.haier.shop.util.*;
import org.haier.shop.util.wxPay.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.HttpSession;
import java.io.*;
import java.math.BigDecimal;
import java.net.URLDecoder;
import java.net.URLEncoder;
import java.text.ParseException;
import java.util.*;
import java.util.concurrent.TimeUnit;

@Controller
@RequestMapping("/shopping")
public class SupplierShoppingController {

	private static Logger logger = Logger.getLogger(SupplierShoppingController.class);
	@Autowired
	private SupplierShoppingService supplierShoppingService;	
	@Resource
	private LoanService loanService;
	@Resource
	private SupplierShoppingDao supShoppingDao;
	@Resource
	private ShoppingDao shoppingDao;
	@Resource
	private LoanMoneyService loanMoneyService;
	
	@Resource
    private RedisTemplate<String, Object> redisTemplate;
	@Autowired 
	private RedisCache rc;
	
	
	
	@RequestMapping("/toChooseLoanTermLimit.do")
	public String toChooseLoanTermLimit(HttpServletRequest request,Double loan_amt) {
		request.setAttribute("loanMoney", loan_amt);
		request.setAttribute("loanList", loanService.querySxRuleMsg(null));
		return "/WEB-INF/supplierShopping/chooseLoanTermLimit.jsp";
	}
	
	/**
	 * 此接口用于shopUpdate项目的支付回调，如需要修改，请配合shopUpdate项目完成
	 * @param request
	 * @return
	 */
	@RequestMapping("/shopUpdateAuthPayCallBack.do")
	@ResponseBody
	public String shopUpdateAuthPayCallBack(String orderNo,String payAmt,HttpServletRequest request) {
		String respString = "success";
		try {			
			System.out.println("接收到的信息" + orderNo + "===" + payAmt);
			Map<String, Object> paramsMap = new HashMap<String, Object>();
			paramsMap.put("pay_code", orderNo);
			paramsMap.put("order_code", orderNo.split("sub")[0]);
			paramsMap.put("main_order_no", orderNo.split("sub")[0]);
			paramsMap.put("pay_date", new Date());
			paramsMap.put("pay_status","2");
			paramsMap.put("order_status","1" );
			paramsMap.put("pay_type","3" );
			paramsMap.put("pay_mode", 6);
			
			System.out.println("更新订单信息，发送订单通知");
			
			Integer c = supplierShoppingService.updateShoppingOrder(paramsMap);
			System.out.println("更新订单数量" + c);
			supplierShoppingService.sendMsg(orderNo);
		}catch (Exception e) {
		  	e.printStackTrace();
		}
		return respString;
	}
	
	
	/**
	 * 支付成功后
	 * 1、更新订单状态
	 * 2、查询是否有未生效的赊销订单信息
	 * 3、如果有未生效赊销订单信息
	 * 3.1、赊销订单信息生效；
	 * 3.2、修改店铺待还赊销额；
	 * 3.3、
	 * @param notifyResponseVo
	 * @return
	 */
	@RequestMapping("/callBackHelibaoPay.do")
	@ResponseBody
	public String callBackHelibaoPay(NotifyResponseVo notifyResponseVo,HttpServletRequest request,HttpResponse response) {
		
		System.out.println("合利宝支付回调结果===" + notifyResponseVo.getRt4_status());
		
		 // 商户根据根据支付结果做业务处理
	   	 if("SUCCESS".equals(notifyResponseVo.getRt4_status())){
			Map<String, Object> paramsMap = new HashMap<String, Object>();
			paramsMap.put("out_trade_no", notifyResponseVo.getRt2_orderId());
			paramsMap.put("total_fee", Double.valueOf(MUtil.strObject(notifyResponseVo.getRt5_orderAmount())));
			paramsMap.put("time_end", notifyResponseVo.getRt7_timestamp());
			
			//支付成功，更新订单信息
			//支付成功
			Map<String, Object> params=new HashMap<String, Object>();
			String orderNo = notifyResponseVo.getRt2_orderId();
			params.put("pay_code", orderNo);
			orderNo = orderNo.substring(0,orderNo.indexOf("sub"));
			params.put("order_code", orderNo);
			params.put("main_order_no", orderNo);
			params.put("pay_date",new Date());
			params.put("pay_status","2");
			params.put("order_status","1" );
			params.put("pay_type","3" );
			params.put("pay_mode", 6);
			
			supplierShoppingService.updateShoppingOrder(params);
			supplierShoppingService.sendMsg(orderNo);
			supplierShoppingService.addPromotionUnionpay(orderNo);//添加银联促销
	   	 }
		return "success";
	}
	
	//合利宝支付，调用支付宝小程序后，回调返回
	@RequestMapping("/callBackHelibaoAliPay.do")
	@ResponseBody
	public String callBackHelibaoAliPay(NotifyResponseVo notifyResponseVo,HttpServletRequest request) {
		System.out.println("合利宝支付宝小程序回调");
		System.out.println(notifyResponseVo);
		Map<String,String[]> pars = request.getParameterMap();
		Set<String> keySet = pars.keySet();
		System.out.println("返回的参数数量" + keySet.size());
		for(String s : keySet) {
			System.out.println(s + "===" + request.getParameter(s));
		}
		 // 商户根据根据支付结果做业务处理
		try {
			if("SUCCESS".equals(notifyResponseVo.getRt4_status())){
				Map<String, Object> paramsMap = new HashMap<String, Object>();
				paramsMap.put("out_trade_no", notifyResponseVo.getRt2_orderId());
				paramsMap.put("total_fee", Double.valueOf(MUtil.strObject(notifyResponseVo.getRt5_orderAmount())));
				paramsMap.put("time_end", notifyResponseVo.getRt7_timestamp());
				paramsMap.put("mch_id", notifyResponseVo.getRt1_customerNumber());
				paramsMap.put("server_type", 6);
				
				//支付成功，更新订单信息
				//支付成功
				Map<String, Object> params=new HashMap<String, Object>();
				String orderNo = notifyResponseVo.getRt2_orderId();
				params.put("pay_code", orderNo);
				params.put("order_code", orderNo);
				params.put("main_order_no", orderNo);
				params.put("pay_date",new Date());
				params.put("pay_status","2");
				params.put("order_status","1" );
				params.put("pay_type","3" );
				params.put("pay_mode", 6);
				
				supplierShoppingService.updateShoppingOrder(params);
				supplierShoppingService.sendMsg(orderNo);
				
				//修改完订单信息后，创建分拣订单信息，并请求分拣排序功能
				try {
					SortThread thread = new SortThread(SysConfig.SORTINGURL, notifyResponseVo.getRt2_orderId());
					thread.start();
				}catch (Exception e) {
					e.printStackTrace();
				}
			}
			return "success";
		}catch (Exception e) {
			e.printStackTrace();
			System.out.println("订单处理失败");
			return "fail";
		}
	}
	
	
	@RequestMapping("/callBackHelibaoPayYN.do")
	@ResponseBody
	public String callBackHelibaoPayYN(NotifyResponseVo notifyResponseVo) {
		//支付成功
		System.out.println("益农商城支付回调"+notifyResponseVo.getRt4_status());
		try {
		// 商户根据根据支付结果做业务处理
		if("SUCCESS".equals(notifyResponseVo.getRt4_status())){
			Map<String, Object> paramsMap = new HashMap<String, Object>();
			paramsMap.put("out_trade_no", notifyResponseVo.getRt2_orderId());
			paramsMap.put("total_fee", Double.valueOf(MUtil.strObject(notifyResponseVo.getRt5_orderAmount())));
			paramsMap.put("time_end", notifyResponseVo.getRt7_timestamp());
			
			//支付成功，更新订单信息
			//支付成功
			Map<String, Object> params=new HashMap<String, Object>();
			String orderNo = notifyResponseVo.getRt2_orderId();
			orderNo = orderNo.substring(0,orderNo.indexOf("SUB"));
			params.put("order_code", orderNo);
			params.put("main_order_no", orderNo);
			params.put("pay_date",new Date());
			params.put("pay_status","2");
			params.put("order_status","1" );
			params.put("pay_type","3" );
			params.put("pay_mode", 6);
			System.out.println(params.toString());
			supplierShoppingService.updateShoppingOrderYN(params);
		}
		} catch (Exception e) {
			e.printStackTrace();
		}
		return "success";
	}
	
	@RequestMapping("/callBackHelibaoPayReturnMoney.do")
	@ResponseBody
	public String callBackHelibaoPayReturnMoney(NotifyResponseVo notifyResponseVo) {
		System.out.println("回调通知结果");
		System.out.println(notifyResponseVo);
		//支付成功
		// 商户根据根据支付结果做业务处理
		if("SUCCESS".equals(notifyResponseVo.getRt4_status())){
			Map<String, Object> paramsMap = new HashMap<String, Object>();
			paramsMap.put("out_trade_no", notifyResponseVo.getRt2_orderId());
			paramsMap.put("total_fee", Double.valueOf(MUtil.strObject(notifyResponseVo.getRt5_orderAmount())));
			paramsMap.put("time_end", notifyResponseVo.getRt7_timestamp());
			
			loanMoneyService.updateReturnMoney(notifyResponseVo.getRt2_orderId());
		}
		return "success";
	}
	
	/**
	 * 生成6位随机数
	 * @return
	 */
	public static String randomCode() {
        StringBuilder str = new StringBuilder();
        Random random = new Random();
        for (int i = 0; i < 6; i++) {
            str.append(random.nextInt(10));
        }
        return str.toString();
    }
	
	@RequestMapping("/generateCodeHLB.do")
	@ResponseBody
	public ShopsResult generateCodeHLB(String orderNo,String payType,String orderAmount,HttpServletRequest request) {
		ShopsResult sr = new ShopsResult();
		sr.setStatus(0);
		String codeUrl = "";
		if(payType.equals(SysConfig.WXPAY) || payType.equals(SysConfig.ALIPAY)) {
			sr.setStatus(1);
			codeUrl = SysConfig.HELIBAOWXPAYURL + orderAmount+"&orderNo="+orderNo;
		}else {
			AppCreateOrderVo vo = new AppCreateOrderVo();
			//随机产生新的订单号，防止更换支付方式
			
			vo.setP2_orderId(orderNo);
			vo.setP3_customerNumber(SysConfig.MCHID);
			vo.setP4_payType("SCAN");
//			vo.setP5_orderAmount("0.02");
			vo.setP5_orderAmount(orderAmount);
			vo.setP7_authcode("1");
			vo.setP8_appType(payType);
			vo.setP9_notifyUrl(SysConfig.HELIBAONOTIFYURL);
			vo.setP10_successToUrl("");
			vo.setP11_orderIp(IpUtils.getHostIp());
			vo.setP12_goodsName("金圈平台");
			
			vo.setSign(SysConfig.MCHKEY);
			ReturnVo rs = HelibaoPayUtil.swipe(vo, SysConfig.MCHKEY, null);
			System.out.println("轻轻揭过"+rs);
			if(rs.getType() != null && rs.getType().equals("1")) {
				//下单成功
				AppCreateOrderResponseVo  appvo = (AppCreateOrderResponseVo)rs.getData();
				codeUrl = appvo.getRt8_qrcode();
				sr.setStatus(1);
			}
		}
		
		if(!codeUrl.equals("")) {
			//获取当前项目的绝对路径，生成图片
			String filePath = this.getClass().getClassLoader().getResource("../../").getPath();
			System.out.println(filePath);
			try {
				
				int width = 300; 
				int height = 300; 
				//二维码的图片格式 
				String format = "jpg"; 
				Hashtable hints = new Hashtable(); 
				//内容所使用编码 
				hints.put(EncodeHintType.CHARACTER_SET, "utf-8"); 
				BitMatrix bitMatrix = new MultiFormatWriter().encode(codeUrl, BarcodeFormat.QR_CODE, width, height, hints); 
				//生成二维码 
				File tempFile = new File(filePath);
				filePath = tempFile.getParent();
				System.out.println(filePath);
				File outputFile = new File(filePath + File.separatorChar +"payimages"+File.separatorChar +orderNo + "." + format);
				System.out.println(outputFile.exists());
				if(!outputFile.exists()) {
					outputFile.createNewFile();
				}
				if(IPGet.ExtranetIP.equals("")) {
					IPGet.getIp();
				}
				if(SysConfig.FORMAL.equals(SysConfig.REDISTEST)) {
					sr.setData(SysConfig.HELIBAOIMGURL + orderNo + "." + format);
				}else if(SysConfig.TEST.equals(SysConfig.REDISTEST)) {
					sr.setData(SysConfig.HELIBAOIMGURL + orderNo+"."+format);
				}else{
					sr.setData(request.getScheme()+"://"+request.getServerName()+":"+request.getServerPort()+"/payimages/"+orderNo+"."+format);
				}
				System.out.println(sr.getData());
				sr.setCord(orderNo);
//				sr.setData(outputFile.getAbsolutePath());
				QRUtil.writeToFile(bitMatrix, format, outputFile); 
			}catch (Exception e) {
				sr.setStatus(0);
				e.printStackTrace();
			}
		}
		return sr;
	}
	@RequestMapping("/generateCodeHLBRetrunMoney.do")
	@ResponseBody
	public ShopsResult generateCodeHLBRetrunMoney(String orderNo,String payType,String orderAmount,String principal_money) throws Exception {
		Subject subject = SecurityUtils.getSubject();
		Session session = subject.getSession();
		Staff staff = (Staff) session.getAttribute("staff");
		String shop_unique = String.valueOf(staff.getShop_unique());
		//保存待支付订单
		Map<String,Object> params=new HashMap<>();
		params.put("sale_list_unique", orderNo);
		if(payType.equals(SysConfig.WXPAY)) {
			params.put("pay_type", 2);
		}else{
			params.put("pay_type", 1);
		}
		params.put("return_money", orderAmount);
		params.put("type", 2);
		params.put("pay_status", 1);
		params.put("shop_unique", shop_unique);
		params.put("principal_money", principal_money);
		loanMoneyService.saveReturnMoney(params);
		ShopsResult sr = new ShopsResult();
		sr.setStatus(0);
		String codeUrl = "";
		if(payType.equals(SysConfig.WXPAY)) {
			codeUrl= weixinPay( orderAmount, orderNo,PayConfigUtil.RETURN_MONEY_NOTIFY_URL);
			sr.setStatus(1);
		}else {
			AppCreateOrderVo vo = new AppCreateOrderVo();
			//随机产生新的订单号，防止更换支付方式
			
			vo.setP2_orderId(orderNo);
			vo.setP3_customerNumber(SysConfig.MCHID);
			vo.setP4_payType("SCAN");
//			vo.setP5_orderAmount("0.02");
			vo.setP5_orderAmount(orderAmount);
			vo.setP7_authcode("1");
			vo.setP8_appType(payType);
			vo.setP9_notifyUrl(SysConfig.HELIBAONOTIFYURL_RETURN_MONEY);
			vo.setP10_successToUrl("");
			vo.setP11_orderIp(IpUtils.getHostIp());
			vo.setP12_goodsName("金圈平台");
			
			vo.setSign(SysConfig.MCHKEY);
			logger.debug("请求合利宝信息");
			ReturnVo rs = HelibaoPayUtil.swipe(vo, SysConfig.MCHKEY, null);
			System.out.println("轻轻揭过"+rs);
			if(rs.getType() != null && rs.getType().equals("1")) {
				//下单成功
				AppCreateOrderResponseVo  appvo = (AppCreateOrderResponseVo)rs.getData();
				codeUrl = appvo.getRt8_qrcode();
				sr.setStatus(1);
			}
		}
		
		if(!codeUrl.equals("")) {
			//获取当前项目的绝对路径，生成图片
			String filePath = this.getClass().getClassLoader().getResource("../../").getPath();
			System.out.println(filePath);
			try {
				
				int width = 300; 
				int height = 300; 
				//二维码的图片格式 
				String format = "jpg"; 
				Hashtable hints = new Hashtable(); 
				//内容所使用编码 
				hints.put(EncodeHintType.CHARACTER_SET, "utf-8"); 
				BitMatrix bitMatrix = new MultiFormatWriter().encode(codeUrl, BarcodeFormat.QR_CODE, width, height, hints); 
				//生成二维码 
				File tempFile = new File(filePath);
				filePath = tempFile.getParent();
				System.out.println(filePath);
				File tf = new File(filePath + File.separatorChar +"images"+File.separatorChar);
				if(!tf.exists()) {
					tf.mkdirs();
				}
				File outputFile = new File(filePath + File.separatorChar +"payimages"+File.separatorChar +orderNo + "." + format);
				System.out.println(outputFile.exists());
				if(!outputFile.exists()) {
					outputFile.createNewFile();
				}
				if(IPGet.ExtranetIP.equals("")) {
					IPGet.getIp();
				}
//				if(IPGet.ExtranetIP.equals(AddOrderTask.TESTSERVERIP)) {
//					sr.setData("http://test170.buyhoo.cc/images/"+orderNo + "." + format);
//				}else if(IPGet.ExtranetIP.equals(AddOrderTask.MAINSERVERIP)) {
//					sr.setData("http://shop.buyhoo.cc/images/"+orderNo+"."+format);
//				}else if(IPGet.ExtranetIP.equals(AddOrderTask.SERVERIP)) {
//					sr.setData("http://buyhoo.cc/images/"+orderNo+"."+format);
//				}else{
//					sr.setData("http://127.0.0.1:8080/images/"+orderNo+"."+format);
//				}
//				System.out.println(sr.getData());
				sr.setCord(orderNo);
//				sr.setData(outputFile.getAbsolutePath());
				QRUtil.writeToFile(bitMatrix, format, outputFile); 
				
				//将图片上传到文件服务器
				//存到文件服务器-start
				SFTPUtil sftp = new SFTPUtil(FTPConfig.username, FTPConfig.password, FTPConfig.host, FTPConfig.port);   
		        sftp.login();
		        
				//获取本地文件流，
		        InputStream is = new FileInputStream(filePath + File.separatorChar +"payimages"+File.separatorChar +orderNo + "." + format);
		        try {
					sftp.upload(FTPConfig.pay_code_path, orderNo + "." + format, is); 
				} catch (Exception e) {
					e.printStackTrace();
				}
				sr.setData("http://file.buyhoo.cc/image/payCode/" + orderNo + "." + format);
			}catch (Exception e) {
				sr.setStatus(0);
				e.printStackTrace();
			}
		}
		return sr;
	}
	@RequestMapping("/generateCodeHLBRetrunMoneyYN.do")
	@ResponseBody
	public ShopsResult generateCodeHLBRetrunMoneyYN(String orderNo,String payType,String orderAmount) throws Exception {
		if(orderNo.indexOf("SUB")!=-1){
		}else{
			orderNo = orderNo+"SUB"+randomCode();
		}
		ShopsResult sr = new ShopsResult();
		sr.setStatus(0);
		String codeUrl = "";
		if(payType.equals(SysConfig.WXPAY)) {
			codeUrl= weixinPay( orderAmount, orderNo,PayConfigUtil.YN_NOTIFY_URL);
			sr.setStatus(1);
		}else {
			AppCreateOrderVo vo = new AppCreateOrderVo();
			//随机产生新的订单号，防止更换支付方式
			
			vo.setP2_orderId(orderNo);
			vo.setP3_customerNumber(SysConfig.MCHID);
			vo.setP4_payType("SCAN");
//			vo.setP5_orderAmount("0.02");
			vo.setP5_orderAmount(orderAmount);
			vo.setP7_authcode("1");
			vo.setP8_appType(payType);
			vo.setP9_notifyUrl(SysConfig.HELIBAONOTIFYURL_YN);
			vo.setP10_successToUrl("");
			vo.setP11_orderIp(IpUtils.getHostIp());
			vo.setP12_goodsName("金圈平台");
			
			vo.setSign(SysConfig.MCHKEY);
			ReturnVo rs = HelibaoPayUtil.swipe(vo, SysConfig.MCHKEY, null);
			System.out.println("轻轻揭过"+rs);
			if(rs.getType() != null && rs.getType().equals("1")) {
				//下单成功
				AppCreateOrderResponseVo  appvo = (AppCreateOrderResponseVo)rs.getData();
				codeUrl = appvo.getRt8_qrcode();
				sr.setStatus(1);
			}
		}
		
		if(!codeUrl.equals("")) {
			//获取当前项目的绝对路径，生成图片
			String filePath = this.getClass().getClassLoader().getResource("../../").getPath();
			System.out.println(filePath);
			try {
				
				int width = 300; 
				int height = 300; 
				//二维码的图片格式 
				String format = "jpg"; 
				Hashtable hints = new Hashtable(); 
				//内容所使用编码 
				hints.put(EncodeHintType.CHARACTER_SET, "utf-8"); 
				BitMatrix bitMatrix = new MultiFormatWriter().encode(codeUrl, BarcodeFormat.QR_CODE, width, height, hints); 
				//生成二维码 
				File tempFile = new File(filePath);
				filePath = tempFile.getParent();
				System.out.println(filePath);
				File outputFile = new File(filePath + File.separatorChar +"images"+File.separatorChar +orderNo + "." + format);
				System.out.println(outputFile.exists());
				if(!outputFile.exists()) {
					outputFile.createNewFile();
				}
				if(IPGet.ExtranetIP.equals("")) {
					IPGet.getIp();
				}
				if(IPGet.ExtranetIP.equals(AddOrderTask.IP12)) {
					sr.setData("http://test170.buyhoo.cc/images/"+orderNo + "." + format);
				}else if(IPGet.ExtranetIP.equals(AddOrderTask.IP18)) {
					sr.setData("http://buyhoo.cc/images/"+orderNo+"."+format);
				}else{
					sr.setData("http://127.0.0.1:8080/images/"+orderNo+"."+format);
				}
				System.out.println(sr.getData());
				sr.setCord(orderNo);
//				sr.setData(outputFile.getAbsolutePath());
				QRUtil.writeToFile(bitMatrix, format, outputFile); 
			}catch (Exception e) {
				sr.setStatus(0);
				e.printStackTrace();
			}
		}
		return sr;
	}
	
	public String weixinPay(String payMoeny,String out_trade_no,String url) throws Exception {
        // 账号信息 
        String appid = PayConfigUtil.APP_ID;  // appid  
        String mch_id = PayConfigUtil.MCH_ID; // 商业号  
        String key = PayConfigUtil.API_KEY; // key  
        
        String currTime = PayToolUtil.getCurrTime();  
        String strTime = currTime.substring(8, currTime.length());  
        String strRandom = PayToolUtil.buildRandom(4) + "";  
        String nonce_str = strTime + strRandom;  
        // 回调接口   
        String notify_url = url;
        String trade_type = "NATIVE";
         
        
        SortedMap<Object,Object> packageParams = new TreeMap<Object,Object>();  
        packageParams.put("appid", appid);  
        packageParams.put("mch_id", mch_id);  
        packageParams.put("nonce_str", nonce_str);  
        packageParams.put("body", "金圈百货");  //（调整为自己的名称）
        packageParams.put("out_trade_no", out_trade_no); 
        String finalmoney= String.format("%.2f", Double.valueOf(payMoeny));//微信请求金额单位为分
		finalmoney = finalmoney.replace(".", "");
		int total_fee= Integer.parseInt(finalmoney);
        packageParams.put("total_fee", total_fee+""); //价格的单位为分  
        packageParams.put("spbill_create_ip", "127.0.0.1");  
        packageParams.put("notify_url", notify_url);  
        packageParams.put("trade_type", trade_type);  
  
        String sign = PayToolUtil.createSign("UTF-8", packageParams,key);  
        packageParams.put("sign", sign);
          
        String requestXML = PayToolUtil.getRequestXml(packageParams);  
        System.out.println(requestXML);  
   
        String resXml = HttpUtil.postData(PayConfigUtil.UFDODER_URL, requestXML);  
        System.out.println(resXml);
  
        @SuppressWarnings("rawtypes")
		Map map = XMLUtil4jdom.doXMLParse(resXml);  
        String urlCode = (String) map.get("code_url");  
        System.out.println(map.toString());
        System.out.println(urlCode);
        return urlCode;
	}
	
	@RequestMapping("/toPayTypeSelect.do")
	public String toPayTypeSelect(String orderNo , Double actual_amt,HttpServletRequest request,HttpServletResponse response,String principal_money) {
		request.setAttribute("orderNo", orderNo);
		request.setAttribute("orderAmount", actual_amt);
		request.setAttribute("principal_money", principal_money);
		return "/WEB-INF/supplierShopping/payTypeSelect.jsp";
	}
	
	@RequestMapping("/modifySupOrderList.do")
	@ResponseBody
	public ShopsResult modifySupOrderList(String order_code,Double realAmt,String goodsDetail) {
		Map<String,Object> map = new HashMap<String,Object>();
		map.put("order_code", order_code);
		map.put("real_pay_money", realAmt);
		
		List<Map<String,Object>> list = new ArrayList<Map<String,Object>>();
		String[] details = goodsDetail.split(";");
		for(String d : details ) {
			Map<String,Object> tempMap = new HashMap<String,Object>();
			String[] strs = d.split(":");
			tempMap.put("orderdetail_id", strs[0]);
			tempMap.put("real_price", strs[1]);
			list.add(tempMap);
		}
		try {
			return supplierShoppingService.modifySupOrderList(map, list);
		}catch (MyException e) {
			e.printStackTrace();
			return new ShopsResult(0, e.getMsg());
		}catch (Exception e) {
			e.printStackTrace();
			return new ShopsResult(0,"更新失败");
		}
		
	}
	
	/**
	 * 查看订单详情列表，用于学校项目修改支付金额
	 * @param global_sub_id
	 * @return
	 */
	@RequestMapping("/toShopOrderDetail.do")
	public String toShopOrderDetail(String global_sub_id) {
		return "/WEB-INF/supplierShopping/shopQrderSettlementDetail.jsp";
	}
	
	/**
	 * 查询学校订单详情
	 * @return
	 */
	@RequestMapping("/queryShopOrderDetail.do")
	@ResponseBody
	public ShopsResult queryShopOrderDetail(String orderCode,String main_order_no,String company_code) {
		try {
			return supplierShoppingService.queryShopOrderDetail(orderCode, main_order_no, company_code);
		}catch (Exception e) {
			e.printStackTrace();
			ShopsResult sr = new ShopsResult(1, "查询失败");
			return sr;
		}
	}
	
	@RequestMapping("/downLoadSupOrderExcel.do")
	@ResponseBody
	public void downLoadSupOrderExcel(
			String shopUnique,
			String order_status,
			String oper_id,
			String order_no,
			String startTime,
			String endTime,
			String customer_code,
			HttpServletRequest request,
			HttpServletResponse response
			){
		Map<String,Object> map=new HashMap<String, Object>();
		map.put("shop_unique", shopUnique);
		map.put("order_status", order_status);
		map.put("oper_id", oper_id);
		map.put("order_no", order_no);
		map.put("startTime", startTime);
		map.put("endTime", endTime);
		map.put("customer_code", customer_code);
		List<ShopList> list = supplierShoppingService.downLoadSupOrderExcel(map);
		loadGoodsInfoExcel(list,"订单信息",request,response);
	}
	
	public void loadGoodsInfoExcel(List<ShopList> caseVoList, final String string,HttpServletRequest request,HttpServletResponse response) {
		try {
			response.setContentType(
					"application/x-msdownload; charset=UTF-8");// 告知浏览器文件类型
			String agent = request.getHeader("user-agent");
			if (agent.contains("Firefox")) {
				response.setHeader(
						"Content-Disposition",
						"attachment;filename="
								+ new String((string + ".xls")
										.getBytes("GB2312"), "ISO-8859-1"));// 设置文件头
			} else {
				response.setHeader(
						"Content-Disposition",
						"attachment;filename="
								+ URLEncoder.encode(string + ".xls", "UTF-8"));// 设置文件头
			}
			OutputStream os = response.getOutputStream();
			LoadOutObjectXLSUtil<ShopList> objectXLS = new LoadOutObjectXLSUtil<ShopList>();
			objectXLS.generateXLS(os, caseVoList, new XLSCallBack<ShopList>() {
				
				@Override
				public Object[] getValues(ShopList tt) {
					String str = "";
					String order_code = tt.getOrder_code();
					if (StringUtil.blank(order_code)) {
						order_code = str;
					}
					
					String company_name = tt.getCompany_name();
					if (StringUtil.blank(company_name)) {
						company_name = str;
					}
					
					String order_remarks = tt.getOrder_remarks();
					if (StringUtil.blank(order_remarks)) {
						order_remarks = str;
					}
					
					String create_date = tt.getCreate_date();
					if (StringUtil.blank(create_date)) {
						create_date = str;
					}
					
					String orderStatus = tt.getOrderStatus();
					if (StringUtil.blank(orderStatus)) {
						orderStatus = str;
					}
					
					String collect_name = tt.getCollect_name();
					
					String collect_phone = tt.getCollect_phone();
					
					String pay_date = tt.getPay_date();
					
					String order_money = tt.getOrder_money().toString();
					
					String real_pay_money = tt.getReal_pay_money().toString();
					
					String receipt_date = tt.getReceipt_date();
					
					String duty_ren = tt.getDuty_ren();
					
					String service_phone = tt.getService_phone();
					
					return new String[] {order_code,orderStatus,create_date,order_money,real_pay_money,pay_date,
							receipt_date,company_name,duty_ren,service_phone,collect_name,collect_phone};
				}

				@Override
				public String getTitle() {
					return string;
				}

				@Override
				public int[] getColumnsWidth() {
					return new int[] { 
										  80 * 80, 80 * 80,80 * 80,80 * 80,80 * 80,80 * 80,80 * 80,
										  80 * 80, 80 * 80,80 * 80,80 * 80,80 * 80};
				}

				@Override
				public String[] getColumnsName() {
					return new String[] { "订单编号","订单状态","下单日期","供货商品金额","结算金额",
										  "结算日期","收货日期","供货商名称","供货商负责人","负责人电话","收件人","收件人电话"};
				}

				@Override
				public int[][] MergedRegion() {
					// TODO Auto-generated method stub
					return null;
				}

				@Override
				public List<Object[]> getheaderValue() {
					// TODO Auto-generated method stub
					return null;
				}

			});
			os.flush();
		} catch (IOException e) {
			e.printStackTrace();
		}
	}
	/**
	 * 取消待支付的订单
	 * @param shopUnique
	 * @param orderCode
	 * @return
	 */
	@RequestMapping("/canOrderSettlement.do")
	@ResponseBody
	public ShopsResult canOrderSettlement(String shopUnique,String orderCode) {
		try {
			return supplierShoppingService.canOrderSettlement(shopUnique, orderCode);
		}catch (Exception e) {
			e.printStackTrace();
			return new ShopsResult(0, "订单取消失败！");
		}
	}
	
	@RequestMapping("/toOrderSettlementDetail.do")
	public String toOrderSettlementDetail() {
		return "/WEB-INF/supplierShopping/orderSettlementDetail.jsp";
	}
	
	@RequestMapping("/queryOrderSettlementDetail.do")
	@ResponseBody
	public ShopsResult queryOrderSettlementDetail(String order_code) {
		try {
			return supplierShoppingService.queryOrderSettlementDetail(order_code);
		}catch (Exception e) {
			return new ShopsResult(0, "查询失败");
		}
	}
	
	@RequestMapping("/orderSettlementCode.do")
	@ResponseBody
	public ShopsResult orderSettlementCode(HttpServletRequest request,String out_trade_no,String shop_unique,Integer total_fee) {
		try {
			return supplierShoppingService.orderSettlementCode(request, out_trade_no, shop_unique, total_fee);
		}catch (Exception e) {
			return new ShopsResult(0,"创建失败!");
		}
	}
	/**
	 * 创建主订单和子订单信息
	 * @param shopUnique
	 * @param orderList
	 * @param totalMoney
	 * @return
	 */
	@RequestMapping("/addNewOrderSettlement.do")
	@ResponseBody
	public ShopsResult addNewOrderSettlement(String shopUnique,String orderList,Double totalMoney) {
		try {
			return supplierShoppingService.addNewOrderSettlement(shopUnique, orderList, totalMoney);
		}catch (Exception e) {
			e.printStackTrace();
			return new ShopsResult(0, "保存失败");
		}
	}
	
	/**
	 * 查询指定店铺，指定供货商，指定时间内的未结算订单信息
	 * @param shopUnique
	 * @param company_code
	 * @param startTime
	 * @param endTime
	 * @return
	 */
	@RequestMapping("/querySupOrderListMsg.do")
	@ResponseBody
	public ShopsResult querySupOrderListMsg(String shopUnique ,String company_code,String startTime,String endTime) {
		try {
			return supplierShoppingService.querySupOrderListMsg(shopUnique, company_code, startTime, endTime);
		}catch (Exception e) {
			e.printStackTrace();
			return new ShopsResult(0,"查询失败!");
		}
	}
	
	/**
	 * 打开新增结算界面
	 * @return
	 */
	@RequestMapping("/toAddOrderSettlement.do")
	public String toAddOrderSettlement() {
		return "/WEB-INF/supplierShopping/addOrderSettlement.jsp";
	}
	
	/**
	 * 查询商家结算记录
	 * @param shopUnique
	 * @param company_code
	 * @param pay_status
	 * @param limit
	 * @return
	 */
	@RequestMapping("/queryOrderSettlementList.do")
	@ResponseBody
	public ShopsResult queryOrderSettlementList(String shopUnique,String company_code,Integer pay_status,Integer limit,Integer page,String startTime ,String endTime) {
		try {
			return supplierShoppingService.queryOrderSettlementList(shopUnique, company_code, pay_status, page, limit,startTime,endTime);
		}catch (Exception e) {
			e.printStackTrace();
			return new ShopsResult(1, "查询失败!");
		}
	}
	/**
	 * 查询指定商家的所有为结算供货订单的
	 * @param shopUnique
	 * @return
	 */
	@RequestMapping("/queryOrderSupplierMsg.do")
	@ResponseBody
	public ShopsResult queryOrderSupplierMsg(String shopUnique) {
		try {
			return supplierShoppingService.queryOrderSupplierMsg(shopUnique);
		}catch (Exception e) {
			e.printStackTrace();
			return new ShopsResult(0, "查询失败!");
		}
	}
	
	@RequestMapping("/modifyShopGold.do")
	@ResponseBody
	public ShopsResult modifyShopGold(String shopUnique,String goldGrantId) {
		try {
			return supplierShoppingService.modifyShopGold(shopUnique, goldGrantId);
		}catch (Exception e) {
			return new ShopsResult(0, "领取失败");
		}
	}
	
	@RequestMapping("/queryShopJQB.do")
	@ResponseBody
	public ShopsResult queryShopJQB(String shopUnique) {
		try {
			return supplierShoppingService.queryShopJQB(shopUnique);
		}catch (Exception e) {
			return new ShopsResult(0,"查询失败");
		}
	}
	
	/**
	 * 跳转订单结算界面
	 * @return
	 */
	@RequestMapping("/toOrderSettlement.do")
	public String toOrderSettlement() {
		return "/WEB-INF/supplierShopping/orderSettlement.jsp";
	}
	
	//跳转到-供货商商城页面
	@RequestMapping("/toSupplierShopping.do")
	public String toSupplierGlobal(Model model,HttpSession session)   {		
		try
		{
			
		
		Staff sa= (Staff)session.getAttribute("staff");
		model.addAttribute("goodKindList", supplierShoppingService.getGoodskindList(sa.getArea_dict_num(),null));//商品分类	
		if(sa!=null&&sa.getShop_type()==9)
		{
			model.addAttribute("goodKindList", supplierShoppingService.getGoodskindList_food(sa.getArea_dict_num(),null));//商品分类	
		}
		}catch(Exception e)
		{
			e.printStackTrace();
		}
		
		return "/WEB-INF/supplierShopping/supplierShopping.jsp";
	}
	
	@RequestMapping("/toSupplierGlobalWJ.do")
	public String toSupplierGlobalWJ(Model model,HttpSession session) {
		Staff sa= (Staff)session.getAttribute("staff");
		model.addAttribute("goodKindList", supplierShoppingService.getWJGoodskindList(sa.getArea_dict_num()));//商品分类	
		return "/WEB-INF/supplierShopping/supplierShoppingWJ.jsp";
	}
	
	@RequestMapping("/toWJGoodsDetail.do")
	public String toWJGoodsDetail(Model model,String goods_id) {
		Map<String,Object> params=new HashMap<String, Object>();
		params.put("goods_id", goods_id);		
		WJGoods g = supplierShoppingService.getWJGoodsSpecList(params);
		model.addAttribute("goods_detail", supplierShoppingService.getGoodDetail(params));//商品id
		model.addAttribute("getGoodSpecList", g);//商品id
		model.addAttribute("goods",g.getList().get(0));
		System.out.println(g.getList().get(0));
		model.addAttribute("goods_id", goods_id);//商品id
		return "/WEB-INF/supplierShopping/WJgoodsDetail.jsp";
	}
	
	//跳转到-商品详情页面
	@RequestMapping("/toGoodDetail.do")
	public String toGoodDetail(Model model,String goods_id){
		Map<String,Object> params=new HashMap<String, Object>();
		
		Object o = rc.getObject("initial_delivery_fee");
		Double fee = 0.0;
		if(null == o) {
			fee = shoppingDao.querySystemConfig();
		}else {
			fee = Double.parseDouble(o.toString());
		}
		
		
		params.put("goods_id", goods_id);	
		List<Map<String,Object>> list = supplierShoppingService.getGoodSpecList(params);
		String company_code = null;
		if(null != list && !list.isEmpty()) {
			company_code = list.get(0).get("company_code").toString();
		}
		model.addAttribute("company_code",company_code);
		model.addAttribute("getGoodSpecList", supplierShoppingService.getGoodSpecList(params));//商品id
		model.addAttribute("goods_detail", supplierShoppingService.getGoodDetail(params));//商品id
		
		model.addAttribute("goods_bind_detail", supplierShoppingService.getGoodBindDetail(params));//捆绑商品信息
		params.put("yRecord", "0");
		params.put("notre", "2");
		params.put("overdue_status", '1');
		model.addAttribute("coupon", supplierShoppingService.getCoupon(params));//优惠券
		model.addAttribute("fullgift", supplierShoppingService.getFullgift(params));//满赠
		model.addAttribute("goods_id", goods_id);//商品id

		//临沭学校
		Subject subject = SecurityUtils.getSubject();
		Session session = subject.getSession();
		Staff staff = (Staff) session.getAttribute("staff");
		Integer shopType = staff.getShop_type();
		if(shopType == 9) {
			return "/WEB-INF/supplierShopping/goodsDetailLS.jsp";
		}else {
			model.addAttribute("initial_delivery_fee",fee);
			return "/WEB-INF/supplierShopping/goodsDetail.jsp";
		}

	}
	
	//跳转到-捆绑商品详情页面
		@RequestMapping("/toGoodDetailBinding.do")
		public String toGoodDetailBinding(Model model,String goods_id){
			Map<String,Object> params=new HashMap<String, Object>();
			params.put("goods_id", goods_id);	
			model.addAttribute("getGoodSpecList", supplierShoppingService.getGoodSpecList(params));//商品id
			model.addAttribute("goods_detail", supplierShoppingService.getGoodDetail(params));//商品id
			model.addAttribute("goods_id", goods_id);//商品id
			return "/WEB-INF/supplierShopping/goodsDetailBinding.jsp";
		}
	
	//购物车
	@RequestMapping("/toShoppingCart.do")
	public String toShoppingCart(Model model,String shop_unique,String area_dict_num,Integer shopType){
		Map<String,Object> params=new HashMap<String, Object>();
		params.put("shop_unique", shop_unique);	
		params.put("area_dict_num", area_dict_num);	
		//model.addAttribute("shopping_cart_list", supplierShoppingService.queryShoppingCart(params));
		model.addAttribute("loanMoney",loanService.queryShopLoanMoney(shop_unique, null));
		model.addAttribute("shopping_cart_list", supplierShoppingService.queryShoppingCart2(shop_unique,area_dict_num,shopType));
		
		Object o = rc.getObject("initial_delivery_fee");
		Double fee = 0.0;
		if(null == o) {
			fee = shoppingDao.querySystemConfig();
		}else {
			fee = Double.parseDouble(o.toString());
		}
		model.addAttribute("initial_delivery_fee",fee);

		if( null == shopType || shopType != 8) {
			return "/WEB-INF/supplierShopping/shoppingCart.jsp";
		}else {
			return "/WEB-INF/supplierShopping/WJshoppingCart.jsp";
		}
	}
	
	
	
	//供货商城-结算页面
	@RequestMapping("/toSettlementPage.do")
	public String toSettlementPage(Model model,String ids,String shop_unique,String area_dict_num,Integer shopType){
		String []id=ids.split(",");			
		
		Object o = rc.getObject("initial_delivery_fee");
		Double fee = 0.0;
		if(null == o) {
			fee = shoppingDao.querySystemConfig();
		}else {
			fee = Double.parseDouble(o.toString());
		}
		
		if(null == shopType || shopType != 8) {
			model.addAttribute("settle", supplierShoppingService.querySettlementList(id,shop_unique,area_dict_num));	
			model.addAttribute("ids", ids);	
			model.addAttribute("initial_delivery_fee",fee);
			return "/WEB-INF/supplierShopping/settlementPage.jsp";
		}else {
			model.addAttribute("settle", supplierShoppingService.querySettlementListWJ(id,shop_unique));	
			model.addAttribute("ids", ids);	
			return "/WEB-INF/supplierShopping/WJsettlementPage.jsp";
		}
	}
	//供货商城-结算页面
	@RequestMapping("/toSettlementPageYN.do")
	public String toSettlementPageYN(Model model,String ids,String shop_unique,String area_dict_num,Integer shopType){
		String []id=ids.split(",");			
		model.addAttribute("settle", supplierShoppingService.querySettlementListYN(id,shop_unique,area_dict_num));	
		model.addAttribute("ids", ids);	
		return "/WEB-INF/supplierShopping/settlementPageYN.jsp";
	}
	
	//供货商城-立即购买
	@RequestMapping("/toBuyNow.do")
	public String toBuyNow(Model model,String good_id,String spec_id,String spec_name,String good_count,String 	goods_name,String goods_img,String deduct_amt,String price,String company_name,String stock_count,String company_code) throws UnsupportedEncodingException{
		model.addAttribute("good_id", good_id);
		model.addAttribute("spec_id", spec_id);
		model.addAttribute("spec_name", URLDecoder.decode(spec_name, "UTF-8"));
		model.addAttribute("good_count", good_count);
		model.addAttribute("goods_name", URLDecoder.decode(goods_name, "UTF-8"));
		model.addAttribute("goods_img", goods_img);
		model.addAttribute("deduct_amt", deduct_amt);
		model.addAttribute("price", price);
		model.addAttribute("company_name", URLDecoder.decode(company_name, "UTF-8"));
		model.addAttribute("stock_count", stock_count);
		model.addAttribute("company_code", company_code);
		
		Subject subject = SecurityUtils.getSubject();
		Session session = subject.getSession();
		Staff staff = (Staff) session.getAttribute("staff");
		String shop_unique = String.valueOf(staff.getShop_unique());
		
		deduct_amt = (deduct_amt == null || deduct_amt.trim().equals("")) ? "0.00" : deduct_amt;
		
		BigDecimal sum_price=new BigDecimal(good_count).multiply(new BigDecimal(price));
		
		//抵扣金额
		BigDecimal deduct_amt_all=new BigDecimal(good_count).multiply(new BigDecimal(deduct_amt));
		model.addAttribute("sum_price", sum_price);//合计
		
		Map<String,Object> params=new HashMap<String, Object>();
		params.put("shop_unique", shop_unique);
		Map<String,Object> goldMap=supplierShoppingService.queryGoldByShop(params);//店铺金圈币可用数量		
		BigDecimal jqb_count=new BigDecimal("0");//店铺金圈币可用数量 
		if(goldMap!=null) {
			jqb_count=new BigDecimal(goldMap.get("jqb_count").toString());
		}
		BigDecimal jqb_max_count=jqb_count.compareTo(deduct_amt_all)>=0?deduct_amt_all:jqb_count;//本次可抵扣最多金圈币
		
		//配送费
		params.put("company_code", company_code);
		params.put("area_dict_num", staff.getArea_dict_num());
		Map<String ,Object> deliveryPrice= shoppingDao.getDeliveryPrice(params);
		BigDecimal delivery_price=new BigDecimal("0");
		BigDecimal actual_delivery_price = new BigDecimal(0);
		if(null != deliveryPrice) {
			model.addAttribute("free_delivery_price", deliveryPrice.get("free_delivery_price").toString());//订单免配送费价格
			if("2".equals(deliveryPrice.get("delivery_price_type").toString())) {//按订单配送				
				delivery_price=new BigDecimal(deliveryPrice.get("delivery_price").toString());				
			}
			if("1".equals(deliveryPrice.get("delivery_price_type").toString())) {//按件数
				params.put("goods_id", good_id);
				Map<String ,Object> goodDeliveryPrice= shoppingDao.getGoodDeliveryPrice(params);
				if(goodDeliveryPrice!=null) {
					delivery_price=new BigDecimal(goodDeliveryPrice.get("delivery_price").toString()).multiply(new BigDecimal(good_count));	
				}						
			}
			actual_delivery_price=sum_price.compareTo(new BigDecimal(deliveryPrice.get("free_delivery_price").toString()))>=0?new BigDecimal("0"):delivery_price;//实际配送费
		}else {
			model.addAttribute("free_delivery_price", 1000000);//订单免配送费价格
		}
		model.addAttribute("jqb_count", jqb_count);
		model.addAttribute("actual_delivery_price", actual_delivery_price);
		model.addAttribute("jqb_max_count", jqb_max_count);
		model.addAttribute("delivery_price", jqb_count);
		model.addAttribute("should_amt_all", sum_price.subtract(jqb_max_count).add(actual_delivery_price));//应付款金额
		return "/WEB-INF/supplierShopping/buyNow.jsp";
	}
	
	//供货商城-订单详情
	@RequestMapping("/toOrderDetail.do")
	public String toOrderDetail(Model model,String main_order_no,String order_status,Integer shopType,String shop_unique){
		model.addAttribute("settle", supplierShoppingService.queryOrderDetail(main_order_no,shopType,shop_unique));	
		model.addAttribute("order_status", order_status);//订单状态
		model.addAttribute("main_order_no", main_order_no);//主订单编号
		if(null == shopType || shopType != 8) {
			return "/WEB-INF/supplierShopping/orderDetail.jsp";
		}else {
			return "/WEB-INF/supplierShopping/WJorderDetail.jsp";
		}
		
	}
	@RequestMapping("/toOrderDetailYN.do")
	public String toOrderDetailYN(Model model,String main_order_no,String order_status,Integer shopType,String shop_unique,String company_code){
		model.addAttribute("settle", supplierShoppingService.queryOrderDetailYN(main_order_no,shopType,shop_unique,company_code));	
		model.addAttribute("order_status", order_status);//订单状态
		model.addAttribute("main_order_no", main_order_no);//主订单编号
		model.addAttribute("company_code", company_code);//主订单编号
		return "/WEB-INF/supplierShopping/orderDetailYN.jsp";
		
	}
	@RequestMapping("/toOrderDetailYNSup.do")
	public String toOrderDetailYNSup(Model model,String main_order_no,String order_status,Integer shopType,String shop_unique,String company_code){
		model.addAttribute("settle", supplierShoppingService.queryOrderDetailYNSup(main_order_no,shopType,shop_unique,company_code));	
		model.addAttribute("order_status", order_status);//订单状态
		model.addAttribute("main_order_no", main_order_no);//主订单编号
		model.addAttribute("company_code", company_code);//主订单编号
		return "/WEB-INF/supplierShopping/orderDetailYNSup.jsp";
		
	}
	
	//跳转到-金圈币列表
	@RequestMapping("/toGoldList.do")
	public String toGoldList(Model model){		
		Subject subject = SecurityUtils.getSubject();
		Session session = subject.getSession();
		Staff staff = (Staff) session.getAttribute("staff");
		String shop_unique = String.valueOf(staff.getShop_unique());
		Map<String,Object> params=new HashMap<String, Object>();
		params.put("shop_unique", shop_unique);
		params=supplierShoppingService.queryGoldByShop(params);
		if(params==null)
		{
			params=new HashMap<String, Object>();
			params.put("rule_order_num", 0);	
			params.put("rule_order_num2", 0);	
			params.put("rule_order_num3", 0);	
			params.put("valid_order_count2", 0);	
			params.put("gold_num", 0);	
			params.put("gold_num2", 0);	
			params.put("gold_num3", 0);	
			params.put("jqb_count", 0);	
			params.put("jqb_use_count", 0);	
			params.put("jqb_give_count", 0);	
		}
		
		model.addAttribute("shop_gold", params);	
		return "/WEB-INF/supplierShopping/goldList.jsp";
	}
	
	//跳转到-我的订单
	@RequestMapping("/toMyOrder.do")
	public String toMyOrder(Model model){	
		model.addAttribute("confirmReceiptUrl", SysConfig.CONFIRMRECEIPTURL);
		return "/WEB-INF/supplierShopping/myOrderList.jsp";
	}
	//跳转到-我的订单
	@RequestMapping("/toMyOrderYN.do")
	public String toMyOrderYN(Model model){		
		return "/WEB-INF/supplierShopping/myOrderListYN.jsp";
	}
	
	//跳转到-付款结算页面
	@RequestMapping("/toPayResult.do")
	public String toPayResult(Model model,String out_trade_no,String total_fee,String code_img_url){		
		model.addAttribute("out_trade_no", out_trade_no);
		model.addAttribute("total_fee", total_fee);
		model.addAttribute("code_img_url", code_img_url);
		
		
		return "/WEB-INF/supplierShopping/payResult.jsp";
	}
	
	//跳转到-付款结算页面
	@RequestMapping("/toPayResult2.do")
	public String toPayResult2(Model model,String orderNo,String orderAmount,String code_img_url,String payType){		
		if(!payType.equals(SysConfig.WXPAY)) {
			model.addAttribute("orderNo", orderNo+"sub"+randomCode());
		}else {
			model.addAttribute("orderNo", orderNo);
		}
		model.addAttribute("orderAmount", orderAmount);
		model.addAttribute("payType",payType);
		
		return "/WEB-INF/supplierShopping/payResult2.jsp";
	}
	//跳转到-付款结算页面
	@RequestMapping("/toPayResult3.do")
	public String toPayResult3(Model model,String orderNo,String orderAmount,String code_img_url,String payType,String principal_money){		
		model.addAttribute("orderNo", orderNo+"SUB"+randomCode());
		model.addAttribute("orderAmount", orderAmount);
		model.addAttribute("payType",payType);
		model.addAttribute("principal_money",principal_money);
		
		return "/WEB-INF/supplierShopping/payResult3.jsp";
	}
	//跳转到-付款结算页面
	@RequestMapping("/toPayResultYN.do")
	public String toPayResultYN(Model model,String orderNo,String orderAmount,String code_img_url,String payType){		
		model.addAttribute("orderNo", orderNo+"SUB"+randomCode());
		model.addAttribute("orderAmount", orderAmount);
		model.addAttribute("payType",payType);
		model.addAttribute("out_trade_no",orderNo);
		
		return "/WEB-INF/supplierShopping/payResultYN.jsp";
	}
		
	/**
	 * 供货商城-提交订单
	 */
	@RequestMapping("/saveOrder.do")
	@ResponseBody
	public PurResult saveOrder(String shop_unique,String oper_id,String gold_amt,String order_amt,String actual_amt,
			String loan_amt,String delivery_price,String ids,String order_remarks,String []delivery_fee,Integer shopType,
			Integer sxRuleId,String recordIds,String giftIds,String giftCouponIds,String AdminCouponId){		
		PurResult result = new PurResult();
		try {
			
			String []id=ids.split(",");	
			String []order_remark=order_remarks.split(",");
			
			String []recordId = null;
			if(!recordIds.equals("")) {
				recordId = recordIds.split(",");
			}
			String []giftId = null;
			if(!giftIds.equals("")) {
				giftId = giftIds.split(",");
			}
			String []giftCouponId = null;
			if(!giftCouponIds.equals("")) {
				giftCouponId = giftCouponIds.split(",");
			}
			System.out.println(AdminCouponId);
			if(null == shopType || shopType != 8) {
//				result = supplierShoppingService.saveOrder(id,shop_unique,oper_id,gold_amt,order_amt,actual_amt,delivery_price,order_remark,delivery_fee,loan_amt,sxRuleId);
				result = supplierShoppingService.saveOrder(id,shop_unique,oper_id,gold_amt,order_amt,actual_amt,
						delivery_price,order_remark,delivery_fee,loan_amt,sxRuleId,recordId,giftId,giftCouponId,AdminCouponId);
			}else {
				result = supplierShoppingService.saveOrderWJ(id, shop_unique, oper_id, gold_amt, order_amt, actual_amt, delivery_price, order_remark, delivery_fee);
			}
		} catch (Exception e) {
			result.setStatus(0);
			result.setMsg("提交订单失败!");	
			e.printStackTrace();
		}
		return result;
	}
	/**
	 * 供货商城-提交订单
	 */
	@RequestMapping("/saveOrderYN.do")
	@ResponseBody
	public PurResult saveOrderYN(String shop_unique,String oper_id,String gold_amt,String order_amt,String actual_amt,
			String delivery_price,String ids,String order_remarks,String []delivery_fee,Integer shopType){		
		PurResult result = new PurResult();
		try {
			String []id=ids.split(",");	
			String []order_remark=order_remarks.split(",");	
			result = supplierShoppingService.saveOrderYN(id,shop_unique,oper_id,gold_amt,order_amt,actual_amt,delivery_price,order_remark,delivery_fee);
			
		} catch (Exception e) {
			result.setStatus(0);
			result.setMsg("提交订单失败!");	
			e.printStackTrace();
		}
		return result;
	}
	/**
	 * 供货商城-提交订单
	 * @throws UnsupportedEncodingException 
	 */
	@RequestMapping(value ="/saveOrderYNPC.do")
	@ResponseBody
	public PurResult saveOrderYN(String shop_unique,
			String gold_amt,String order_amt,String actual_amt,
			String delivery_price,String ids,String order_remarks,String delivery_fees,Integer shopType,
			@RequestParam(value = "oper_id",required=true)String oper_id,
			HttpServletRequest request) throws UnsupportedEncodingException{
		PurResult result = new PurResult();
		try {
			Map<String, Object> params =ServletsUtil.getParameters(request);
			System.out.println(params.get("oper_id") +">>>>>>>>>>>>>>222");
			System.out.println("备注"+order_remarks);
			String []id=ids.split(",");	
			String []order_remark=order_remarks.split(",");	
			String []delivery_fee=delivery_fees.split(",");	
			result = supplierShoppingService.saveOrderYN(id,shop_unique,params.get("oper_id").toString(),gold_amt,order_amt,actual_amt,delivery_price,order_remark,delivery_fee);
			
		} catch (Exception e) {
			result.setStatus(0);
			result.setMsg("提交订单失败!");	
			e.printStackTrace();
		}
		return result;
	}
	
	/**
	 * 取消dingd
	 */
	@RequestMapping("/cancelOrder.do")
	@ResponseBody
	public PurResult cancelOrder(String main_order_no){		
		PurResult result = new PurResult();
		try {			
			result = supplierShoppingService.cancelOrder(main_order_no);
		} catch (Exception e) {
			result.setStatus(0);
			result.setMsg("操作失败!");	
			e.printStackTrace();
		}
		return result;
	}
	
	/**
	 * 立即购买
	 */
	@RequestMapping("/buyNowOrder.do")
	@ResponseBody
	public PurResult buyNowOrder(String shop_unique,String oper_id,String gold_amt,String order_amt,String actual_amt,String actual_delivery_price,
			String good_id,String order_remark,String company_code,String spec_id,String spec_name,String good_count,String loan_amt,
			String loan_count,String recordIds,String giftIds,String giftCouponIds,String AdminCouponId){		
		PurResult result = new PurResult();
		try {						
			result=supplierShoppingService.buyNowOrder(good_id,shop_unique,oper_id,gold_amt,order_amt,actual_amt,actual_delivery_price,order_remark,company_code,spec_id,spec_name,good_count,loan_amt,loan_count,recordIds,giftIds,giftCouponIds,AdminCouponId);
		} catch (Exception e) {
			result.setStatus(0);
			result.setMsg("提交订单失败!");	
			e.printStackTrace();
		}
		return result;
	}
	
	@RequestMapping("/getGoodListWJ.do")
	@ResponseBody
	public PurResult getGoodListWJ(HttpServletRequest request,
			@RequestParam(value="sort_collection",defaultValue="desc")String sort_collection,
			@RequestParam(value="page",defaultValue="1")Integer page,
			@RequestParam(value="limit",defaultValue="20")Integer limit,
			String sort_type,
			String sort_name,
			Integer shop_type) {
		PurResult result = new PurResult();
		try {
			Map<String, Object> params = HandleMessyCode.handle(ServletsUtil.getParameters(request));
			if(null != sort_name && !sort_name.equals("")) {
				params.put("sort_type", sort_type);
				params.put("sort_name", sort_name);
				if("sort".equals(params.get("sort_name")))
				{
					if("desc".equals(params.get("sort_type"))){
						params.put("sort_type","asc");
					}else {
						params.put("sort_type","desc");
					}
				}
			}
			params.put("limit", limit);
			params.put("page", (page-1)*limit);
			result=supplierShoppingService.getGoodListWJ(params);
		} catch (Exception e) {
			result.setStatus(0);
			result.setMsg("查询失败!");	
			e.printStackTrace();
		}
		return result;
	}
	
	/**
	 * 使用缓存-new
	 * @return
	 */
	public  PurResult useCache(String id){
		PurResult sr=new PurResult(1,"操作成功");
		System.out.println("缓存key======="+id);
		Object d1=redisTemplate.opsForValue().get(id);
		if(null == d1 || d1.toString().equals("")) {
			return null;
		}
		//
		System.out.println("缓存信息" + d1.toString());
		if(d1.toString().startsWith("[")) {
			sr.setData(JSON.parseArray(d1.toString()));
		}else {
			JSONObject o = JSON.parseObject(d1.toString());
			sr.setData(o);
		}
		return null;
	}
	public JSONObject useObject(String id) {
		PurResult sr=new PurResult(1,"操作成功");
		System.out.println("缓存key======="+id);
		Object d1=redisTemplate.opsForValue().get(id);
		if(null == d1 || d1.toString().equals("")) {
			return null;
		}
		//
		System.out.println("缓存信息" + d1.toString());
		JSONObject o = JSON.parseObject(d1.toString());
		sr.setData(o);
		return o;
	}
	
	public JSONArray useObjectArray(String id) {
		System.out.println("缓存key======="+id);
		Object d1=redisTemplate.opsForValue().get(id);
		if(null == d1 || d1.toString().equals("")) {
			return null;
		}
		//
		System.out.println("缓存信息" + d1.toString());
		JSONArray o = JSON.parseArray(d1.toString());
		return o;
	}
	
	public  void SetCache(String id,Object value){
		redisTemplate.opsForValue().set(id, value.toString());
		redisTemplate.expire(id, 300, TimeUnit.SECONDS);
	}
	/**
	 * 供货商城-商品列表
	 */
	@ResponseBody
	@RequestMapping("/getGoodList.do")
	public PurResult getGoodList(HttpServletRequest request,
			@RequestParam(value="sort_collection",defaultValue="desc")String sort_collection,
			@RequestParam(value="page",defaultValue="1")Integer page,
			@RequestParam(value="limit",defaultValue="20")Integer limit,
			Integer shop_type
			){		
		PurResult result = new PurResult();
		try {
			Map<String, Object> params = HandleMessyCode.handle(ServletsUtil.getParameters(request));
			if(params.get("sort_collection")!=null&&!"".equals(params.get("sort_collection"))){
				if("desc".equals(params.get("sort_collection"))){
					params.put("sort_collection","asc");
				}else{
					params.put("sort_collection","desc");
				}
			}
			String id = "";
			Set<String> keys = params.keySet();
			for(String key:keys) {
				id += params.get(key) == null ? "":params.get(key).toString();
			}
			
			PurResult temp = useCache(id);
			if(null != temp) {
				return temp;
			}
			
			params.put("limit", limit);
			params.put("page", (page-1)*limit);
			params.put("shop_type", shop_type);
			result=supplierShoppingService.getGoodList(params);
			JSONObject OO = (JSONObject)JSON.toJSON(result);
			SetCache(id, OO.getJSONArray("data"));
		} catch (Exception e) {
			result.setStatus(0);
			result.setMsg("查询失败!");	
			e.printStackTrace();
		}	
		return result;
	}
	
	/**
	 * 金圈币使用详情
	 */
	@ResponseBody
	@RequestMapping("/queryGoldUserList.do")
	public PurResult queryGoldUserList(HttpServletRequest request,
			@RequestParam(value="page",defaultValue="1")Integer page,
			@RequestParam(value="limit",defaultValue="15")Integer limit){		
		PurResult result = new PurResult();
		try {
			Map<String, Object> params = HandleMessyCode.handle(ServletsUtil.getParameters(request));
			params.put("limit", limit);
			params.put("page", (page-1)*limit);
			result=supplierShoppingService.queryGoldUserList(params);
		} catch (Exception e) {
			result.setStatus(0);
			result.setMsg("查询失败!");	
			e.printStackTrace();
		}	
		return result;
	}
	
	
	/**
	 * 金圈币发放详情
	 */
	@ResponseBody
	@RequestMapping("/queryGoldRewardList.do")
	public PurResult queryGoldRewardList(HttpServletRequest request,
			@RequestParam(value="page",defaultValue="1")Integer page,
			@RequestParam(value="limit",defaultValue="15")Integer limit){		
		PurResult result = new PurResult();
		try {
			Map<String, Object> params = HandleMessyCode.handle(ServletsUtil.getParameters(request));
			params.put("limit", limit);
			params.put("page", (page-1)*limit);
			result=supplierShoppingService.queryGoldRewardList(params);
		} catch (Exception e) {
			result.setStatus(0);
			result.setMsg("查询失败!");	
			e.printStackTrace();
		}	
		return result;
	}
	
	/**
	 * 我的订单
	 */
	@ResponseBody
	@RequestMapping("/getMyOrderList.do")
	public PurResult getMyOrderList(HttpServletRequest request,
			@RequestParam(value="page",defaultValue="1")Integer page,
			@RequestParam(value="limit",defaultValue="15")Integer limit){		
		PurResult result = new PurResult();
		try {
			Map<String, Object> params = HandleMessyCode.handle(ServletsUtil.getParameters(request));
			params.put("limit", limit);
			params.put("page", (page-1)*limit);
			result=supplierShoppingService.getMyOrderList(params);
		} catch (Exception e) {
			result.setStatus(0);
			result.setMsg("查询失败!");	
			e.printStackTrace();
		}	
		return result;
	}
	
	
	/**
	 * 加入购物车
	 */
	@RequestMapping("/insertShoppingCart.do")
	@ResponseBody
	public PurResult insertShoppingCart(HttpServletRequest request){
		PurResult rs = new PurResult();
		try {
			Map<String, Object> params = ServletsUtil.getParameters(request);
			rs=supplierShoppingService.insertShoppingCart(params);
		} catch (Exception e) {
			rs.setStatus(0);
			rs.setMsg("操作失败!");
			e.printStackTrace();
		}		
		return rs;
	}
	
	@RequestMapping("/updateShoppingLoanCart.do")
	@ResponseBody
	public PurResult updateShoppingLoanCart(Integer loan_count,Integer id,@RequestParam(name="type",defaultValue="2")Integer type) {
		return supplierShoppingService.updateShoppingLoanCart(id,loan_count,type); 
	}
	
	/**
	 * 修改购物车商品数量
	 */
	@RequestMapping("/updateShoppingCart.do")
	@ResponseBody
	public PurResult updateShoppingCart(HttpServletRequest request){
		PurResult rs = new PurResult();
		try {
			Map<String, Object> params = ServletsUtil.getParameters(request);
			rs=supplierShoppingService.updateShoppingCart(params);
		} catch (Exception e) {
			rs.setStatus(0);
			rs.setMsg("操作失败!");
			e.printStackTrace();
		}		
		return rs;
	}
	/**
	 * 修改购物车商品数量
	 */
	@RequestMapping("/updateShoppingCartYN.do")
	@ResponseBody
	public PurResult updateShoppingCartYN(HttpServletRequest request){
		PurResult rs = new PurResult();
		try {
			Map<String, Object> params = ServletsUtil.getParameters(request);
			rs=supplierShoppingService.updateShoppingCartYN(params);
		} catch (Exception e) {
			rs.setStatus(0);
			rs.setMsg("操作失败!");
			e.printStackTrace();
		}		
		return rs;
	}
	
	/**
	 * 购物车
	 */
	@RequestMapping("/deleteShoppingCart.do")
	@ResponseBody
	public PurResult deleteShoppingCart(HttpServletRequest request){
		PurResult rs = new PurResult();
		try {
			Map<String, Object> params = ServletsUtil.getParameters(request);
			rs=supplierShoppingService.deleteShoppingCart(params);
		} catch (Exception e) {
			rs.setStatus(0);
			rs.setMsg("操作失败!");
			e.printStackTrace();
		}		
		return rs;
	}
	
	/**
	 * 购物车捆绑删除
	 */
//	@RequestMapping("/deleteBindShoppingCart.do")
//	@ResponseBody
//	public PurResult deleteBindShoppingCart(HttpServletRequest request){
//		PurResult rs = new PurResult();
//		try {
//			Map<String, Object> params = ServletsUtil.getParameters(request);
//			rs=supplierShoppingService.deleteBindShoppingCart(params);
//		} catch (Exception e) {
//			rs.setStatus(0);
//			rs.setMsg("操作失败!");
//			e.printStackTrace();
//		}		
//		return rs;
//	}
	
	/**
	 * 购物车
	 */
	@RequestMapping("/deleteShoppingCartYN.do")
	@ResponseBody
	public PurResult deleteShoppingCartYN(HttpServletRequest request){
		PurResult rs = new PurResult();
		try {
			Map<String, Object> params = ServletsUtil.getParameters(request);
			rs=supplierShoppingService.deleteShoppingCartYN(params);
		} catch (Exception e) {
			rs.setStatus(0);
			rs.setMsg("操作失败!");
			e.printStackTrace();
		}		
		return rs;
	}
	
	
	/**
	 * 禁用  删除
	 */
	@RequestMapping("/deleteShoppingCartMore.do")
	@ResponseBody
	public PurResult deleteShoppingCartMore(HttpServletRequest request,String ids){
		PurResult rs = new PurResult();
		try {
			String []id=ids.split(",");
			rs=supplierShoppingService.deleteShoppingCartMore(id);
		} catch (Exception e) {
			rs.setStatus(0);
			rs.setMsg("操作失败!");
			e.printStackTrace();
		}		
		return rs;
	}
	/**
	 * 禁用  删除
	 */
	@RequestMapping("/deleteShoppingCartMoreYN.do")
	@ResponseBody
	public PurResult deleteShoppingCartMoreYN(HttpServletRequest request,String ids){
		PurResult rs = new PurResult();
		try {
			String []id=ids.split(",");
			rs=supplierShoppingService.deleteShoppingCartMoreYN(id);
		} catch (Exception e) {
			rs.setStatus(0);
			rs.setMsg("操作失败!");
			e.printStackTrace();
		}		
		return rs;
	}
	
	/**
	 * 初始化扫码请求，通过该请求生成二维码来进行扫码支付。
	 */
	@RequestMapping("/generateCode.do")
	@ResponseBody
	public PurResult generateCode(HttpServletRequest request,String out_trade_no,String shop_unique,int total_fee,Integer shopType){	
		PurResult result= new PurResult();
		try {			
			System.out.println("供货商城-生成付款码：out_trade_no="+out_trade_no+";shop_unique="+shop_unique+";total_fee="+total_fee);								
			result=supplierShoppingService.generateCode(request,out_trade_no,shop_unique,total_fee,shopType);			
		} catch (Exception e) {
			e.printStackTrace();			
			result.setStatus(0);
			result.setMsg("支付失败");			
			return result;
		}
		return result;
	}
	
	/**
	 * 供货商城-扫码支付回调
	 */
	@RequestMapping(value = "/orderSettlementCallBack.do" ,method = RequestMethod.POST)
	@ResponseBody
	public synchronized String orderSettlementCallBack(HttpServletRequest request,HttpServletResponse response) throws ParseException{
		String respString = "fail";
		try {			 
			 String xmlString = XMLUtils.parseRequst(request);
			 System.out.println("供货商城-扫码支付回调===="+xmlString);	       
			 Map<String,String> map = XmlUtilsYu.toMap(xmlString.getBytes(), "utf-8");
			 if(map.containsKey("sign")){
                if(!SignUtils.checkParam(map, SwiftpassConfig.key)){
                    respString = "fail";
                }else{
                	if ("0".equals(map.get("status")) && "0".equals(map.get("result_code"))) {
                		if("0".equals(map.get("pay_result"))) {
                			//支付成功
                			Map<String, Object> params=new HashMap<String, Object>();
                			params.put("main_order_no", map.get("out_trade_no"));
                			params.put("order_code", map.get("out_trade_no"));
                			params.put("pay_date",new Date());
                			params.put("pay_status","2");
                			params.put("order_status","4" );
                			params.put("pay_type","3" );
                			
                			//1、货到付款；2、支付宝；3、微信;5、银联                			
                			if("pay.alipay.jspay".equals(map.get("trade_type"))) {
                				params.put("pay_mode", "2");
                			}
                			if("pay.weixin.jspay".equals(map.get("trade_type"))) {
                				params.put("pay_mode","3" );
                			}
                			if("pay.unionpay.native".equals(map.get("trade_type"))) {
                				params.put("pay_mode", "5");
                			}
                			//修改主订单及各个子定的支付支付状态
                			supplierShoppingService.orderSettlementCallBack(params);
                			respString = "success";
                		}
                	}                    
                }
            }
		}catch (Exception e) {
		  	e.printStackTrace();
		}
		return respString;
	}
	
	
	/**
	 * 供货商城-扫码支付回调
	 */
	@RequestMapping(value = "/scanCodeCallBackWJ.do" ,method = RequestMethod.POST)
	@ResponseBody
	public synchronized String scanCodeCallBackWJ(HttpServletRequest request,HttpServletResponse response) throws ParseException{
		String respString = "fail";
		try {			 
			 String xmlString = XMLUtils.parseRequst(request);
			 System.out.println("五金供货商城-扫码支付回调===="+xmlString);	       
			 Map<String,String> map = XmlUtilsYu.toMap(xmlString.getBytes(), "utf-8");
			 System.out.println("五金供货商城回调结果" + map);
			 if(map.containsKey("sign")){
                if(!SignUtils.checkParam(map, SwiftpassConfig.key)){
                    respString = "fail";
                }else{
                	if ("0".equals(map.get("status")) && "0".equals(map.get("result_code"))) {
                		if("0".equals(map.get("pay_result"))) {
                			//支付成功
                			Map<String, Object> params=new HashMap<String, Object>();
                			params.put("order_code", map.get("out_trade_no"));
                			params.put("main_order_no", map.get("out_trade_no"));
                			params.put("pay_date",new Date());
                			params.put("pay_status","2");
                			params.put("order_status","1" );
                			params.put("pay_type","3" );
                			//params.put("pay_money", new BigDecimal(map.get("total_fee").toString()).divide(new BigDecimal("100")).setScale(2,BigDecimal.ROUND_HALF_UP).doubleValue());
                			//1、货到付款；2、支付宝；3、微信;5、银联                			
                			if("pay.alipay.jspay".equals(map.get("trade_type"))) {
                				params.put("pay_mode", "2");
                			}
                			if("pay.weixin.jspay".equals(map.get("trade_type"))) {
                				params.put("pay_mode","3" );
                			}
                			if("pay.unionpay.native".equals(map.get("trade_type"))) {
                				params.put("pay_mode", "5");
                			}
                			
                			Integer c = supplierShoppingService.updateShoppingOrder(params);
                			System.out.println("结算订单数量" + c);
//                			supplierShoppingService.sendMsg(map.get("out_trade_no").toString());
//                			supplierShoppingService.addPromotionUnionpay(map.get("out_trade_no").toString());//添加银联促销
                			respString = "success";
                		}
                	}                    
                }
            }
		}catch (Exception e) {
		  	e.printStackTrace();
		}
		return respString;
	}
	
	/**
	 * 供货商城-扫码支付回调
	 */
	@RequestMapping(value = "/scanCodeCallBack.do" ,method = RequestMethod.POST)
	@ResponseBody
	public synchronized String scanCodeCallBack(HttpServletRequest request,HttpServletResponse response) throws ParseException{
		String respString = "fail";
		try {			 
			 String xmlString = XMLUtils.parseRequst(request);
			 System.out.println("供货商城-扫码支付回调===="+xmlString);	       
			 Map<String,String> map = XmlUtilsYu.toMap(xmlString.getBytes(), "utf-8");
			 if(map.containsKey("sign")){
                if(!SignUtils.checkParam(map, SwiftpassConfig.key)){
                    respString = "fail";
                }else{
                	if ("0".equals(map.get("status")) && "0".equals(map.get("result_code"))) {
                		if("0".equals(map.get("pay_result"))) {
                			//支付成功
                			Map<String, Object> params=new HashMap<String, Object>();
                			params.put("order_code", map.get("out_trade_no"));
                			params.put("main_order_no", map.get("out_trade_no"));
                			params.put("pay_date",new Date());
                			params.put("pay_status","2");
                			params.put("order_status","1" );
                			params.put("pay_type","3" );
                			params.put("pay_code",map.get("out_trade_no"));
                			
                			//1、货到付款；2、支付宝；3、微信;5、银联                			
                			if("pay.alipay.jspay".equals(map.get("trade_type"))) {
                				params.put("pay_mode", "2");
                			}
                			if("pay.weixin.jspay".equals(map.get("trade_type"))) {
                				params.put("pay_mode","3" );
                			}
                			if("pay.unionpay.native".equals(map.get("trade_type"))) {
                				params.put("pay_mode", "5");
                			}
                			supplierShoppingService.updateShoppingOrder(params);
                			supplierShoppingService.sendMsg(map.get("out_trade_no").toString());
                			supplierShoppingService.addPromotionUnionpay(map.get("out_trade_no").toString());//添加银联促销
                			respString = "success";
                		}
                	}                    
                }
            }
		}catch (Exception e) {
		  	e.printStackTrace();
		}
		return respString;
	}
	
	/**
	 * 查询订单支付详情
	 * @param orderNo
	 * @return
	 */
	public PurResult queryOrderHLB(String orderNo) {
		PurResult pr = new PurResult();
		
		
		return pr;
	}
	
	/**
	 * 订单查询
	 */
	@RequestMapping("/queryOrderYT.do")
	@ResponseBody
	public PurResult queryOrderYT(String out_trade_no) throws AlipayApiException, IOException {
		PurResult result= new PurResult();		
		
		//查询数据库订单信息并返回
		out_trade_no = out_trade_no.split("sub")[0];
		result = supplierShoppingService.queryMainOrderPayStatus(out_trade_no);
		
//		String mch_id=SwiftpassConfig.mch_id;
//		String mch_key=SwiftpassConfig.key;
//		
//		SortedMap<String,String> paramMap = new TreeMap<String,String>();        
//		paramMap.put("service", "unified.trade.query");
//		paramMap.put("mch_id", mch_id);
//		paramMap.put("out_trade_no", out_trade_no);
//		paramMap.put("nonce_str", String.valueOf(new Date().getTime()));
//        Map<String, String> tem = SignUtils.paraFilter(paramMap);
//        StringBuilder buf = new StringBuilder((tem.size() + 1) * 10);
//        SignUtils.buildPayParams(buf, tem, false);
//        String preStr = buf.toString();
//        String sign = MD5.sign(preStr, "&key=" + mch_key, "utf-8");
//        paramMap.put("sign", sign);
//        String reqUrl = SwiftpassConfig.req_url;
//        System.out.println("reqParams:" + XMLUtils.parseXML(paramMap));
//        CloseableHttpResponse response = null;
//        CloseableHttpClient client = null;
//        String res = null;
//        Map<String, String> resultMap = null;
//        try {
//            HttpPost httpPost = new HttpPost(reqUrl);
//            StringEntity entityParams = new StringEntity(XMLUtils.parseXML(paramMap), "utf-8");
//            httpPost.setEntity(entityParams);
//            httpPost.setHeader("Content-Type", "text/xml;utf-8");
//            client = HttpClients.createDefault();
//            response = client.execute(httpPost);
//            if (response != null && response.getEntity() != null) {
//                resultMap = SignUtils.toMap(EntityUtils.toByteArray(response.getEntity()), "utf-8");
//                res = XMLUtils.toXml(resultMap);
//                if(!SignUtils.checkParam(resultMap, mch_key)){
//                    res = "验证签名不通过";
//                }else{
//                    if("0".equals(resultMap.get("status"))){
//                    	
//                    	result.setStatus(1);
//                		result.setData(resultMap);
//                    }else{
//                    	result.setStatus(0);
//                		result.setData(resultMap);
//                    }
//                }
//            } else {
//            	result.setStatus(0);
//        		result.setData(resultMap);
//            }
//        } catch (Exception e) {
//        	e.printStackTrace();
//        	result.setStatus(0);
//    		result.setData(resultMap);
//        } finally {
//            if (response != null) {
//                response.close();
//            }
//            if (client != null) {
//                client.close();
//            }
//        }
        return result;
	}
	
	/**
	 * 查询可用金圈币
	 */
	@RequestMapping("/getShopGold.do")
	@ResponseBody
	public PurResult getShopGold(String shop_unique){		
		PurResult result = new PurResult(1,"查询成功！");
		try {	
			Map<String,Object> params=new HashMap<String, Object>();
			params.put("shop_unique", shop_unique);
			Map<String ,Object> data=supplierShoppingService.queryGoldByShop1(params);
			if(data == null || data.isEmpty()) {
				result.setMsg("店铺没有可用金圈币!");
				result.setData(new HashMap<String,Object>());
			}else {
				result.setStatus(1);
				result.setData(data);
			}			
		} catch (Exception e) {
			result.setStatus(0);
			result.setMsg("操作失败!");	
			e.printStackTrace();
		}
		return result;
	}
	
	/**
	 * 查看店铺信息
	 */
	@RequestMapping("/queryShopMessage.do")
	@ResponseBody
	public PurResult queryShopMessage(String shop_unique){
		PurResult result = new PurResult();
		try {	
			Map<String,Object> params=new HashMap<String, Object>();
			params.put("shop_unique", shop_unique);
			Map<String ,Object> data=supplierShoppingService.queryShopMessage(params);
			if(data.isEmpty()||data==null) {
				result.setStatus(0);
				result.setMsg("店铺不存在!");	
			}else {
				result.setData(data);
				result.setStatus(1);
			}			
		} catch (Exception e) {
			result.setStatus(0);
			result.setMsg("操作失败!");	
			e.printStackTrace();
		}
		return result;
	}
	
	/**
	 * 修改店铺金圈币
	 */
	@RequestMapping("/updateShopGold.do")
	@ResponseBody
	public PurResult updateShopGold(String shop_unique,String gold_amt) {
		PurResult result = new PurResult();
		try {	
			Map<String,Object> params=new HashMap<String, Object>();
			params.put("shop_unique", shop_unique);
			params.put("gold_amt", gold_amt);
			supShoppingDao.updateShopGold(params);
			result.setData("修改金圈币成功！");
			result.setStatus(1);			
		} catch (Exception e) {
			result.setStatus(0);
			result.setMsg("操作失败!");	
			e.printStackTrace();
		}
		return result;
	}
	
	/**
	 * 商品分类地址
	 */
	@RequestMapping("/getGoodsKindList.do")
	@ResponseBody
	public PurResult getGoodsKindList(String area_dict_num,String shopUnique) {
		PurResult result = new PurResult();
		try {	
			List<Map<String ,Object>> data=supplierShoppingService.getGoodskindList(area_dict_num,shopUnique);
			result.setData(data);
			result.setStatus(1);			
		} catch (Exception e) {
			result.setStatus(0);
			result.setMsg("操作失败!");	
			e.printStackTrace();
		}
		return result;
	}
	
	/**
	 * 增加店铺金圈币
	 */
	@RequestMapping("/addShopGold.do")
	@ResponseBody
	public PurResult addShopGold(String shop_unique,String gold_amt) {		
		PurResult result = new PurResult();
		try {	
			Map<String,Object> params=new HashMap<String, Object>();
			params.put("shop_unique", shop_unique);
			params.put("gold_amt", gold_amt);
			supShoppingDao.addShopGold(params);
			result.setData("增加金圈币成功！");
			result.setStatus(1);			
		} catch (Exception e) {
			result.setStatus(0);
			result.setMsg("操作失败!");	
			e.printStackTrace();
		}
		return result;
	}
	
	@RequestMapping("/twxPayCallBack.do")
	@ResponseBody
	public PurResult twxPayCallBack(String saleListUnique) {
		//支付成功
		Map<String, Object> params=new HashMap<String, Object>();
		params.put("main_order_no", saleListUnique);
		params.put("pay_date",new Date());
		params.put("pay_status","2");
		params.put("order_status","1" );
		//1、货到付款；2、支付宝；3、微信;5、银联                			
		params.put("pay_mode","3" );
		params.put("pay_type","1" );
		supplierShoppingService.updateShoppingOrder(params);
		return new PurResult(1,"");
	}
	
	/**
	 * 供货商城-微信支付回调
	 */
	@RequestMapping(value = "/wxPayCallBack.do" ,method = RequestMethod.POST)
	@ResponseBody
	public synchronized String wxPayCallBack(HttpServletRequest request,HttpServletResponse response) throws ParseException{
		String respString = "fail";
		try {			 
			 String xmlString = XMLUtils.parseRequst(request);
			 //String xmlString ="<xml><appid><![CDATA[wx38618456447b4af2]]></appid><bank_type><![CDATA[OTHERS]]></bank_type><cash_fee><![CDATA[1]]></cash_fee><fee_type><![CDATA[CNY]]></fee_type><is_subscribe><![CDATA[N]]></is_subscribe><mch_id><![CDATA[**********]]></mch_id><nonce_str><![CDATA[KKklE5JWj01ilBMKZVfQl12t65vP99vx]]></nonce_str><openid><![CDATA[oYxKd5uD05N6xAEGxJT4klRf4cuA]]></openid><out_trade_no><![CDATA[DD202006120959529388]]></out_trade_no><result_code><![CDATA[SUCCESS]]></result_code><return_code><![CDATA[SUCCESS]]></return_code><sign><![CDATA[F01373E38501B7074C37E411BF5324A2]]></sign><time_end><![CDATA[**************]]></time_end><total_fee>1</total_fee><trade_type><![CDATA[APP]]></trade_type><transaction_id><![CDATA[4200000602202006120999859581]]></transaction_id></xml>";
			 System.out.println("供货商城-微信支付回调===="+xmlString);	       
			 Map<String,String> map = XmlUtilsYu.toMap(xmlString.getBytes(), "utf-8");
			 if(map.containsKey("sign")){
                if(!SignUtils.checkParam(map, "yingxiangli123shangjiabaihuoabcd")){
                    respString = "fail";
                }else{
                	if ("SUCCESS".equals(map.get("result_code"))) {
                			//支付成功
                			Map<String, Object> params=new HashMap<String, Object>();
                			params.put("main_order_no", map.get("out_trade_no"));
                			Map<String,Object> order=shoppingDao.queryOrderByOrderNo(params);
                			if("0".equals(order.get("order_status").toString())) {
                				params.put("pay_date",new Date());
                    			params.put("pay_status","2");
                    			params.put("order_status","1" );
                    			//1、货到付款；2、支付宝；3、微信;5、银联                			
                    			params.put("pay_mode","3" );
                    			params.put("pay_type","1" );
                    			supplierShoppingService.updateShoppingOrder(params);
                    			supplierShoppingService.sendMsg(map.get("out_trade_no").toString());
                    			supplierShoppingService.addPromotionUnionpay(map.get("out_trade_no").toString());//添加银联促销
                    			//supplierShoppingService.startOrderLoanMsg(map.get("out_trade_no").toString());
                    			respString = "success";
                			}                			                			
                	}                    
                }
            }
		}catch (Exception e) {
		  	e.printStackTrace();
		}
		return respString;
	}
	//供货商称app小程序合利宝支付回调
	@RequestMapping(value = "/purchaseHelibaoCallBack.do")
	@ResponseBody
	public synchronized String purchaseHelibaoCallBack(NotifyResponseVo notifyResponseVo) throws ParseException{
		System.out.println("供货商称app小程序合利宝支付回调");
		System.out.println(notifyResponseVo.getRt4_status());
		System.out.println(notifyResponseVo.getRt1_customerNumber());
		System.out.println(notifyResponseVo.getRt2_orderId());

		String urlPathParam = "orderCode=" + notifyResponseVo.getRt2_orderId();

		String res = HttpUtil.postData(SysConfig.sendJGPushToApp, urlPathParam);

		System.out.println("订单推送结果" + res);

		try {
             // 商户根据根据支付结果做业务处理
        	 if("SUCCESS".equals(notifyResponseVo.getRt4_status())){
        		//支付成功
     			Map<String, Object> params=new HashMap<String, Object>();
     			params.put("main_order_no", notifyResponseVo.getRt2_orderId());
     			params.put("server_type", 6);
     			params.put("mch_id", notifyResponseVo.getRt1_customerNumber());
     			Map<String,Object> order=shoppingDao.queryOrderByOrderNo(params);
     			if("0".equals(order.get("order_status").toString())) {
     				params.put("pay_date",new Date());
         			params.put("pay_status","2");
         			params.put("order_status","1" );
         			//1、货到付款；2、支付宝；3、微信;5、银联                			
         			params.put("pay_mode","3" );
         			params.put("pay_type","1" );
        			params.put("pay_code",notifyResponseVo.getRt2_orderId() );
         			supplierShoppingService.updateShoppingOrder(params);
         			supplierShoppingService.sendMsg(notifyResponseVo.getRt2_orderId());
         			supplierShoppingService.addPromotionUnionpay(notifyResponseVo.getRt2_orderId());//添加银联促销

					//修改完订单信息后，创建分拣订单信息，并请求分拣排序功能
					try {
						SortThread thread = new SortThread(SysConfig.SORTINGURL, notifyResponseVo.getRt2_orderId());
						thread.start();
					}catch (Exception e) {
						e.printStackTrace();
					}
     			}
        	 }
             return "success";// 反馈处理结果
    	}catch(Exception e){
    		e.printStackTrace();
            return "fail 系统内部错误" + e.getMessage();// 反馈处理结果
    	}
	}

	/**
	 * 供货商城-支付宝支付回调
	 * @throws Exception 
	 */
	@RequestMapping(value = "/aliPayCallBack.do" ,method = RequestMethod.POST)
	@ResponseBody
	public synchronized String aliPayCallBack(HttpServletRequest request,HttpServletResponse response) throws ParseException, Exception{
		Map<String,String> params = new HashMap<String,String>();		    
		Map<String,String[]> requestParams = request.getParameterMap();
		System.out.println("供货商城-支付宝支付回调===="+requestParams);
		for (Iterator<String> iter = requestParams.keySet().iterator(); iter.hasNext();) {
			String name = iter.next();
			String[] values = requestParams.get(name);
			String valueStr = "";
			for (int i = 0; i < values.length; i++) {
				valueStr = (i == values.length - 1) ? valueStr + values[i]
						: valueStr + values[i] + ",";
			}
			//乱码解决，这段代码在出现乱码时使用
//			valueStr = new String(valueStr.getBytes("ISO-8859-1"), "utf-8");
			System.out.println("返回参数名："+name+",值："+valueStr);
			params.put(name, valueStr);
		}
		boolean signVerified = AlipaySignature.rsaCheckV1(params, AlipayPayUtil.ALIPAY_PUBLIC_KEY, "UTF-8", AlipayConfig.sign_type); //调用SDK验证签名
		System.out.println("签名结果："+signVerified);
		//——请在这里编写您的程序（以下代码仅作参考）——
		
		/* 实际验证过程建议商户务必添加以下校验：
		1、需要验证该通知数据中的out_trade_no是否为商户系统中创建的订单号，
		2、判断total_amount是否确实为该订单的实际金额（即商户订单创建时的金额），
		3、校验通知中的seller_id（或者seller_email) 是否为out_trade_no这笔单据的对应的操作方（有的时候，一个商户可能有多个seller_id/seller_email）
		4、验证app_id是否为该商户本身。
		*/
		if(signVerified) {//验证成功
			//商户订单号
			String out_trade_no = new String(request.getParameter("out_trade_no").getBytes("ISO-8859-1"),"UTF-8");
		
			//支付宝交易号
			//String trade_no = new String(request.getParameter("trade_no").getBytes("ISO-8859-1"),"UTF-8");
		
			//交易状态
			String trade_status = new String(request.getParameter("trade_status").getBytes("ISO-8859-1"),"UTF-8");
			
			if(trade_status.equals("TRADE_FINISHED")){
				//判断该笔订单是否在商户网站中已经做过处理
				//如果没有做过处理，根据订单号（out_trade_no）在商户网站的订单系统中查到该笔订单的详细，并执行商户的业务程序
				//如果有做过处理，不执行商户的业务程序
				Map<String, Object> paramsMap=new HashMap<String, Object>();
    			paramsMap.put("main_order_no", out_trade_no);
    			Map<String,Object> order=shoppingDao.queryOrderByOrderNo(paramsMap);
    			if("0".equals(order.get("order_status").toString())) {
    				paramsMap.put("pay_date",new Date());
        			paramsMap.put("pay_status","2");
        			paramsMap.put("order_status","1" );
        			//1、货到付款；2、支付宝；3、微信;5、银联                			
        			paramsMap.put("pay_mode","2" );
        			params.put("pay_type","2" );
        			supplierShoppingService.updateShoppingOrder(paramsMap);
        			supplierShoppingService.sendMsg(out_trade_no);
        			supplierShoppingService.addPromotionUnionpay(out_trade_no);//添加银联促销
        			supplierShoppingService.startOrderLoanMsg(out_trade_no);
    			} 	
				//注意：
    			
			}else if (trade_status.equals("TRADE_SUCCESS")){
				//判断该笔订单是否在商户网站中已经做过处理
				//如果没有做过处理，根据订单号（out_trade_no）在商户网站的订单系统中查到该笔订单的详细，并执行商户的业务程序
				//如果有做过处理，不执行商户的业务程序
    			Map<String, Object> paramsMap=new HashMap<String, Object>();
    			paramsMap.put("main_order_no", out_trade_no);
    			Map<String,Object> order=shoppingDao.queryOrderByOrderNo(paramsMap);
    			if("0".equals(order.get("order_status").toString())) {
    				paramsMap.put("pay_date",new Date());
        			paramsMap.put("pay_status","2");
        			paramsMap.put("order_status","1" );
        			//1、货到付款；2、支付宝；3、微信;5、银联                			
        			paramsMap.put("pay_mode","2" );
        			params.put("pay_type","2" );
        			supplierShoppingService.updateShoppingOrder(paramsMap);
        			supplierShoppingService.sendMsg(out_trade_no);
        			supplierShoppingService.addPromotionUnionpay(out_trade_no);//添加银联促销
        			supplierShoppingService.startOrderLoanMsg(out_trade_no);
    			}    			
				//注意：
				//付款完成后，支付宝系统发送该交易状态通知
			}
			
			System.out.println("success");
			return "success";
		}else {//验证失败
			System.out.println("fail");
			return "fail";
			//调试用，写文本函数记录程序运行情况是否正常
			//String sWord = AlipaySignature.getSignCheckContentV1(params);
			//AlipayConfig.logResult(sWord);
		}
	}
	
	//查询供货商
	@RequestMapping("/querySupplierList2.do")
	@ResponseBody
	public PurResult querySupplierList2(String area_dict_num,String shop_type){
		PurResult pr = new PurResult();
		String supid = "querySupplierList2" + area_dict_num + shop_type+"supplier_list";
		String brandid = "querySupplierList2" + area_dict_num + shop_type+"brand_list";
		JSONArray j1 = useObjectArray(supid);
		JSONArray j2 = useObjectArray(brandid);
		if(null != j1 && null != j2) {
			pr.setData(j1);
			pr.setCord(j2);
			return pr;
		}
		pr = supplierShoppingService.querySupplierList2(area_dict_num, shop_type);
		return pr;
	}
	@RequestMapping("/querySupplierListYN.do")
	@ResponseBody
	public PurResult querySupplierListYN(String area_dict_num,String shop_type)
	{
		return supplierShoppingService.querySupplierListYN(area_dict_num,shop_type);
	}
	//申请退款
	@RequestMapping("/applyRefund.do")
	@ResponseBody
	public PurResult applyRefund(String order_code,String company_code
			){
		return supplierShoppingService.updateApplyRefund(order_code,company_code);
	}
	//取消退款
	@RequestMapping("/cancelApplyRefund.do")
	@ResponseBody
	public PurResult cancelApplyRefund(String order_code
			){
		return supplierShoppingService.cancelApplyRefund(order_code);
	}
	
	@RequestMapping("/toYNSupplierShopping.do")
	public String toYNSupplierShopping(Model model,HttpSession session) {
		Staff sa= (Staff)session.getAttribute("staff");
		model.addAttribute("goodKindList", supplierShoppingService.queryGoodskindListByYN(sa.getArea_dict_num(),null));//商品分类	
		
		return "/WEB-INF/supplierShopping/supplierShoppingYN.jsp";
	}
	
	/**
	 * 供货商城-商品列表
	 */
	@ResponseBody
	@RequestMapping("/getGoodListYN.do")
	public PurResult getGoodListYN(HttpServletRequest request,
			@RequestParam(value="sort_collection",defaultValue="desc")String sort_collection,
			@RequestParam(value="page",defaultValue="1")Integer page,
			@RequestParam(value="limit",defaultValue="20")Integer limit,
			Integer shop_type
			){		
		PurResult result = new PurResult();
		try {
			Map<String, Object> params = HandleMessyCode.handle(ServletsUtil.getParameters(request));
			if(params.get("sort_collection")!=null&&!"".equals(params.get("sort_collection"))){
				if("desc".equals(params.get("sort_collection"))){
					params.put("sort_collection","asc");
				}else{
					params.put("sort_collection","desc");
				}
			}
			params.put("limit", limit);
			params.put("page", (page-1)*limit);
			params.put("shop_type", shop_type);
			result=supplierShoppingService.getGoodListYN(params);
		} catch (Exception e) {
			result.setStatus(0);
			result.setMsg("查询失败!");	
			e.printStackTrace();
		}	
		return result;
	}
	
	//跳转到-商品详情页面
	@RequestMapping("/toGoodDetailYN.do")
	public String toGoodDetailYN(Model model,String goods_id){
		Map<String,Object> params=new HashMap<String, Object>();
		params.put("goods_id", goods_id);	
		model.addAttribute("getGoodSpecList", supplierShoppingService.getGoodSpecList(params));//商品id
		model.addAttribute("goods_detail", supplierShoppingService.getGoodDetail(params));//商品id
		model.addAttribute("goods_id", goods_id);//商品id
		return "/WEB-INF/supplierShopping/goodsDetailYN.jsp";
	}
	
	/**
	 * 加入购物车
	 */
	@RequestMapping("/insertShoppingCartYN.do")
	@ResponseBody
	public PurResult insertShoppingCartYN(HttpServletRequest request){
		PurResult rs = new PurResult();
		try {
			Map<String, Object> params = ServletsUtil.getParameters(request);
			
			System.out.println("商品信息" );
			Set<String> keys = params.keySet();
			for(String s : keys) {
				System.out.println(s + "===" + params.get(s));
			}
			rs=supplierShoppingService.insertShoppingCartYN(params);
		} catch (Exception e) {
			rs.setStatus(0);
			rs.setMsg("操作失败!");
			e.printStackTrace();
		}		
		return rs;
	}
	
	//购物车
	@RequestMapping("/toShoppingCartYN.do")
	public String toShoppingCartYN(Model model,String shop_unique,String area_dict_num,Integer shopType){
		Map<String,Object> params=new HashMap<String, Object>();
		params.put("shop_unique", shop_unique);	
		params.put("area_dict_num", area_dict_num);	
		model.addAttribute("shopping_cart_list", supplierShoppingService.queryShoppingCartYN(shop_unique,area_dict_num,shopType));
		return "/WEB-INF/supplierShopping/shoppingCartYN.jsp";
	}
	@RequestMapping("/queryYNOrderStatus.do")
	@ResponseBody
	public PurResult queryYNOrderStatus(String out_trade_no) throws AlipayApiException, IOException {
		Map<String,Object> params=new HashMap<String, Object>();
		params.put("order_code", out_trade_no);
        return supplierShoppingService.queryYNOrderStatus(params);
	}
	
	/**
	 * 我的订单
	 */
	@ResponseBody
	@RequestMapping("/getMyOrderListYN.do")
	public PurResult getMyOrderListYN(HttpServletRequest request,
			@RequestParam(value="page",defaultValue="1")Integer page,
			@RequestParam(value="limit",defaultValue="15")Integer limit){		
		PurResult result = new PurResult();
		try {
			Map<String, Object> params = HandleMessyCode.handle(ServletsUtil.getParameters(request));
			params.put("limit", limit);
			params.put("page", (page-1)*limit);
			result=supplierShoppingService.getMyOrderListYN(params);
		} catch (Exception e) {
			result.setStatus(0);
			result.setMsg("查询失败!");	
			e.printStackTrace();
		}	
		return result;
	}

	@ResponseBody
	@RequestMapping("/sendYNOrder.do")
	public PurResult sendYNOrder(HttpServletRequest request
			){		
		PurResult result = new PurResult();
		try {
			Map<String, Object> params = HandleMessyCode.handle(ServletsUtil.getParameters(request));
			result=supplierShoppingService.sendYNOrder(params);
		} catch (Exception e) {
			result.setStatus(0);
			result.setMsg("查询失败!");	
			e.printStackTrace();
		}	
		return result;
	}
	@ResponseBody
	@RequestMapping("/confirmYNOrder.do")
	public PurResult confirmYNOrder(HttpServletRequest request
			){		
		PurResult result = new PurResult();
		try {
			Map<String, Object> params = HandleMessyCode.handle(ServletsUtil.getParameters(request));
			result=supplierShoppingService.confirmYNOrder(params);
		} catch (Exception e) {
			result.setStatus(0);
			result.setMsg("查询失败!");	
			e.printStackTrace();
		}	
		return result;
	}
	@RequestMapping("/cancelOrderYN.do")
	@ResponseBody
	public PurResult cancelOrderYN(String main_order_no){		
		PurResult result = new PurResult();
		try {			
			result = supplierShoppingService.cancelOrderYN(main_order_no);
		} catch (Exception e) {
			result.setStatus(0);
			result.setMsg("操作失败!");	
			e.printStackTrace();
		}
		return result;
	}
	
	/**
	 * 限购判断
	 */
	@RequestMapping("/cycleFrequency.do")
	@ResponseBody
	public PurResult cycleFrequency(HttpServletRequest request){
		PurResult rs = new PurResult();
		try {
			Map<String, Object> params = ServletsUtil.getParameters(request);
			rs=supplierShoppingService.cycleFrequency(params);
		} catch (Exception e) {
			rs.setStatus(0);
			rs.setMsg("操作失败!");
			e.printStackTrace();
		}		
		return rs;
	}
	
	
	//供货商城-立即购买
		@RequestMapping("/toBuyNowTWO.do")
		public String toBuyNowTWO(Model model,String good_id,String spec_id,String spec_name,String good_count,String 	goods_name,String goods_img,String deduct_amt,String price,String company_name,String stock_count,String company_code,String cycle,String frequency) 
				throws UnsupportedEncodingException{
			String cycleTwo = URLDecoder.decode(cycle, "UTF-8");
			Subject subject = SecurityUtils.getSubject();
			Session session = subject.getSession();
			Staff staff = (Staff) session.getAttribute("staff");
			String shop_unique = String.valueOf(staff.getShop_unique());
			String area_dict_num = String.valueOf(staff.getArea_dict_num());
			
			Object o = rc.getObject("initial_delivery_fee");
			Double fee = 0.0;
			if(null == o) {
				fee = shoppingDao.querySystemConfig();
			}else {
				fee = Double.parseDouble(o.toString());
			}
			
			model.addAttribute("initial_delivery_fee",fee);
			
			Map<String,Object> mp = new HashMap<String,Object>();
			mp.put("good_id", good_id);
		    List<Map<String,Object>> spol = shoppingDao.getBindingGoodsList(mp);
//		    System.out.println(spol);
		    //存在捆绑商品，否则执行原操作
		    if(spol.isEmpty()) {
		    	model.addAttribute("good_id", good_id);
				model.addAttribute("spec_id", spec_id);
				model.addAttribute("spec_name", URLDecoder.decode(spec_name, "UTF-8"));
				model.addAttribute("good_count", good_count);
				model.addAttribute("goods_name", URLDecoder.decode(goods_name, "UTF-8"));
				model.addAttribute("goods_img", goods_img);
				model.addAttribute("deduct_amt", deduct_amt);
				model.addAttribute("price", price);
				model.addAttribute("company_name", URLDecoder.decode(company_name, "UTF-8"));
				model.addAttribute("stock_count", stock_count);
				model.addAttribute("company_code", company_code);
				
				//判断限购周期与限购次数
				if(!cycleTwo.equals("") && !frequency.equals("")) {
					Map<String,Object> oc = new HashMap<String,Object>();
					oc.put("good_id", good_id);
			    	oc.put("shop_unique", shop_unique);
			    	oc.put("cycle", cycleTwo);
			    	oc.put("frequency", frequency);
					PurResult result1 = supplierShoppingService.cycleFrequency(oc);
					if(result1.getStatus() == 0) {
						model.addAttribute("msgg", URLDecoder.decode(goods_name, "UTF-8")+"立即购买失败，该产品"+cycleTwo+"限购"+frequency+"次！");
						return "/WEB-INF/supplierShopping/buyNowTWOErr.jsp";
					}
				}
				
				deduct_amt = (deduct_amt == null || deduct_amt.trim().equals("")) ? "0.00" : deduct_amt;
				
				BigDecimal sum_price=new BigDecimal(good_count).multiply(new BigDecimal(price));
				
				//供应商优惠券
				Map<String, Object> reMap = new HashMap<String,Object>();
				reMap.put("company_code", company_code);
				reMap.put("shop_unique", shop_unique);
				reMap.put("usage_status", "0");
				reMap.put("overdue_status", "1");
				reMap.put("yRecord", "1");
				reMap.put("notre", "0");
				List<Map<String, Object>> recCoupon = supplierShoppingService.getCoupon(reMap);
				if(recCoupon.size() == 0) {
					model.addAttribute("coupon_amount", 0);//优惠金额
				}else {
					List<Map<String, Object>> Maxcoupon = new ArrayList<Map<String,Object>>();
					for(Map<String, Object> co:recCoupon) {
						BigDecimal meetAmount = new BigDecimal(co.get("meet_amount").toString());
						if(sum_price.compareTo(meetAmount) >= 0) {
							Maxcoupon.add(co);
						}
					}
					//遍历获取符合优惠券需求的最大一条记录
					if(Maxcoupon.size() != 0) {
						Map<String, Object> maxcou = Maxcoupon.get(0);
						BigDecimal MaxcouAmount = new BigDecimal(Maxcoupon.get(0).get("coupon_amount").toString());
						for(int i=0;i<Maxcoupon.size();i++) {
							BigDecimal couAmount = new BigDecimal(Maxcoupon.get(i).get("coupon_amount").toString());
							if(couAmount.compareTo(MaxcouAmount) >= 0) {
								MaxcouAmount = couAmount;
								maxcou = Maxcoupon.get(i);
							}
						}
						model.addAttribute("coupon_amount", new BigDecimal(maxcou.get("coupon_amount").toString()));//优惠金额
						model.addAttribute("coupon_id", maxcou.get("coupon_id").toString());//优惠id
						model.addAttribute("record_id", maxcou.get("record_id").toString());//领取id
					}else {
						model.addAttribute("coupon_amount", 0);//优惠金额
					}
				}
				
				//跨店优惠券
				reMap.put("company_code", "GS371300001");
				reMap.put("usage_status", "0");
				reMap.put("overdue_status", "1");
				reMap.put("yRecord", "0");
				reMap.put("notre", "0");
				List<Map<String, Object>> adCoupon = supplierShoppingService.getCoupon(reMap);
				if(adCoupon.size() == 0) {
					model.addAttribute("admin_coupon", 0);//优惠金额
				}else {
					List<Map<String, Object>> MaxcouponAll = new ArrayList<Map<String,Object>>();
					for(Map<String, Object> co:adCoupon) {
						//筛选出未用过的平台优惠券
						Map<String, Object> reusMap = new HashMap<String,Object>();
						reusMap.put("shop_unique", shop_unique);
						reusMap.put("coupon_id", co.get("coupon_id").toString());
						reusMap.put("company_code", "GS371300001");
						List<Map<String, Object>> recUsCoupon = shoppingDao.getUsedCoupon(reusMap);
						if(recUsCoupon.size() == 0) {
							//未使用过
							BigDecimal meetAmount = new BigDecimal(co.get("meet_amount").toString());
							int limit_quantity_type = Integer.parseInt(co.get("limit_quantity_type").toString());
							int total_surplus = Integer.parseInt(co.get("total_surplus").toString());
							if(sum_price.compareTo(meetAmount) >= 0) {
								//判断限制数量的优惠券
								if(limit_quantity_type == 2) {
									//剩余数量不为零的
									if(total_surplus > 0) {
										MaxcouponAll.add(co);
									}
								}else {
									//不限制数量的
									MaxcouponAll.add(co);
								}
							}
						}
						
					}
					//遍历获取符合优惠券需求的最大一条记录
					if(MaxcouponAll.size() != 0) {
						Map<String, Object> maxcouAll = MaxcouponAll.get(0);
						BigDecimal MaxcouAmountAll = new BigDecimal(MaxcouponAll.get(0).get("coupon_amount").toString());
						for(int i=0;i<MaxcouponAll.size();i++) {
							BigDecimal couAmount = new BigDecimal(MaxcouponAll.get(i).get("coupon_amount").toString());
							if(couAmount.compareTo(MaxcouAmountAll) >= 0) {
								MaxcouAmountAll = couAmount;
								maxcouAll = MaxcouponAll.get(i);
							}
						}
						model.addAttribute("admin_coupon", new BigDecimal(maxcouAll.get("coupon_amount").toString()));//优惠金额
						model.addAttribute("AdminCouponId", maxcouAll.get("coupon_id").toString());//优惠id
						System.out.println(maxcouAll.get("coupon_id").toString());
						//model.addAttribute("AdminRecordId", maxcouAll.get("record_id").toString());//领取id
					}else {
						model.addAttribute("admin_coupon", 0);//优惠金额
					}
				}
				
				
				//满赠
				Map<String, Object> fullMap = new HashMap<String,Object>();
				fullMap.put("company_code", company_code);
				List<Map<String, Object>> fullgift = supplierShoppingService.getFullgift(fullMap);
				List<Map<String, Object>> allFullgift = new ArrayList<Map<String,Object>>();
				//获取所有符合的满赠
				for(Map<String, Object> gift:fullgift) {
					String meet_amount = gift.get("meet_amount").toString();
					BigDecimal meetAmount = new BigDecimal(meet_amount);
					if(sum_price.compareTo(meetAmount) >= 0) {
						allFullgift.add(gift);
					}
				}
				//遍历获取符合满赠需求的最大一条记录
				Map<String, Object> maxGift = new HashMap<String,Object>();
				if(allFullgift.size() != 0) {
					BigDecimal MaxAmount = new BigDecimal(allFullgift.get(0).get("meet_amount").toString());
					for(int i=0;i<allFullgift.size();i++) {
						BigDecimal meetAmount = new BigDecimal(allFullgift.get(i).get("meet_amount").toString());
						if(meetAmount.compareTo(MaxAmount) >= 0) {
							MaxAmount = meetAmount;
							maxGift = allFullgift.get(i);
						}
					}
				}
				List<Map<String, Object>> maxFullgift = new ArrayList<Map<String,Object>>();
				maxFullgift.add(maxGift);
				model.addAttribute("fullgiftList", maxFullgift);//满赠
				
				
				//平台满赠
				Map<String, Object> comfullMap = new HashMap<String,Object>();
				comfullMap.put("company_code", "GS371300001");
				List<Map<String, Object>> comfullgift = supplierShoppingService.getFullgift(comfullMap);
				List<Map<String, Object>> comallFullgift = new ArrayList<Map<String,Object>>();
				//获取所有符合的满赠
				for(Map<String, Object> comgift:comfullgift) {
					BigDecimal meetAmount = new BigDecimal(comgift.get("meet_amount").toString());
					if(sum_price.compareTo(meetAmount) >= 0) {
						comallFullgift.add(comgift);
					}
				}
				//遍历获取符合满赠需求的最大一条记录
				Map<String, Object> commaxGift = new HashMap<String,Object>();
				if(comallFullgift.size() != 0) {
					BigDecimal comMaxAmount = new BigDecimal(comallFullgift.get(0).get("meet_amount").toString());
					for(int i=0;i<comallFullgift.size();i++) {
						BigDecimal meetAmount = new BigDecimal(comallFullgift.get(i).get("meet_amount").toString());
						if(meetAmount.compareTo(comMaxAmount) >= 0) {
							comMaxAmount = meetAmount;
							commaxGift = comallFullgift.get(i);
						}
					}
				}
				List<Map<String, Object>> commaxFullgift = new ArrayList<Map<String,Object>>();
				commaxFullgift.add(commaxGift);
				model.addAttribute("comfullgiftList", commaxFullgift);//满赠
				
				//抵扣金额
				BigDecimal deduct_amt_all=new BigDecimal(good_count).multiply(new BigDecimal(deduct_amt));
				model.addAttribute("sum_price", sum_price);//合计
				
				Map<String,Object> params=new HashMap<String, Object>();
				params.put("shop_unique", shop_unique);
				Map<String,Object> goldMap=supplierShoppingService.queryGoldByShop(params);//店铺金圈币可用数量		
				BigDecimal jqb_count=new BigDecimal("0");//店铺金圈币可用数量 
				if(goldMap!=null) {
					jqb_count=new BigDecimal(goldMap.get("jqb_count").toString());
				}
				BigDecimal jqb_max_count=jqb_count.compareTo(deduct_amt_all)>=0?deduct_amt_all:jqb_count;//本次可抵扣最多金圈币
				
				//配送费
				params.put("company_code", company_code);
				params.put("area_dict_num", staff.getArea_dict_num());
				Map<String ,Object> deliveryPrice= shoppingDao.getDeliveryPrice(params);
				model.addAttribute("free_delivery_price", deliveryPrice.get("free_delivery_price").toString());//订单免配送费价格
				BigDecimal delivery_price=new BigDecimal("0");
				if("2".equals(deliveryPrice.get("delivery_price_type").toString())) {//按订单配送				
					delivery_price=new BigDecimal(deliveryPrice.get("delivery_price").toString());				
				}
				if("1".equals(deliveryPrice.get("delivery_price_type").toString())) {//按件数
					params.put("goods_id", good_id);
					Map<String ,Object> goodDeliveryPrice= shoppingDao.getGoodDeliveryPrice(params);
					if(goodDeliveryPrice!=null) {
						delivery_price=new BigDecimal(goodDeliveryPrice.get("delivery_price").toString()).multiply(new BigDecimal(good_count));	
					}						
				}
				BigDecimal actual_delivery_price=sum_price.compareTo(new BigDecimal(deliveryPrice.get("free_delivery_price").toString()))>=0?new BigDecimal("0"):delivery_price;//实际配送费
				model.addAttribute("jqb_count", jqb_count);
				model.addAttribute("actual_delivery_price", actual_delivery_price);
				model.addAttribute("jqb_max_count", jqb_max_count);
				model.addAttribute("delivery_price", jqb_count);
				model.addAttribute("should_amt_all", sum_price.subtract(jqb_max_count).add(actual_delivery_price));//应付款金额
				return "/WEB-INF/supplierShopping/buyNow2.jsp";
		    }else {
		    	String[] goods_ids;
		    	String goods_idss = "";
		    	for(Map<String,Object> map1 : spol) {
			    	String gId = map1.get("good_id").toString();
			    	String gName = map1.get("goods_name").toString();
			    	String cycle1 = map1.get("cycle").toString();
			    	String frequency1 = map1.get("frequency").toString();
			    	String quotaNumber = map1.get("quotaNumber").toString();
			    	Integer good_count1 = Integer.valueOf(map1.get("good_count").toString());
			    	Integer auto_fxiaoshou = Integer.valueOf(map1.get("auto_fxiaoshou").toString());
			    	if(gId.equals(good_id)) {
			    		model.addAttribute("good_id", good_id);
						model.addAttribute("spec_id", spec_id);
						model.addAttribute("spec_name", URLDecoder.decode(spec_name, "UTF-8"));
						model.addAttribute("good_count", good_count);
						model.addAttribute("goods_name", URLDecoder.decode(goods_name, "UTF-8"));
						model.addAttribute("goods_img", goods_img);
						model.addAttribute("deduct_amt", deduct_amt);
						model.addAttribute("price", price);
						model.addAttribute("company_name", URLDecoder.decode(company_name, "UTF-8"));
						model.addAttribute("stock_count", stock_count);
						model.addAttribute("company_code", company_code);
						
						//判断限购周期与限购次数
						if(!cycleTwo.equals("") && !frequency.equals("")) {
							Map<String,Object> oc = new HashMap<String,Object>();
							oc.put("good_id", good_id);
					    	oc.put("shop_unique", shop_unique);
					    	oc.put("cycle", cycleTwo);
					    	oc.put("frequency", frequency);
							PurResult result1 = supplierShoppingService.cycleFrequency(oc);
							if(result1.getStatus() == 0) {
								model.addAttribute("msgg", URLDecoder.decode(goods_name, "UTF-8") + "立即购买失败，该产品"+cycleTwo+"限购"+frequency+"次！");
								return "/WEB-INF/supplierShopping/buyNowTWOErr.jsp";
								//return "购买失败，该产品"+cycleTwo+"限购"+frequency+"次！";
							}
						}
						
						deduct_amt = (deduct_amt == null || deduct_amt.trim().equals("")) ? "0.00" : deduct_amt;
						
						BigDecimal sum_price=new BigDecimal(good_count).multiply(new BigDecimal(price));
						
						//抵扣金额
						BigDecimal deduct_amt_all=new BigDecimal(good_count).multiply(new BigDecimal(deduct_amt));
						model.addAttribute("sum_price", sum_price);//合计
						
						Map<String,Object> params=new HashMap<String, Object>();
						params.put("shop_unique", shop_unique);
						Map<String,Object> goldMap=supplierShoppingService.queryGoldByShop(params);//店铺金圈币可用数量		
						BigDecimal jqb_count=new BigDecimal("0");//店铺金圈币可用数量 
						if(goldMap!=null) {
							jqb_count=new BigDecimal(goldMap.get("jqb_count").toString());
						}
						BigDecimal jqb_max_count=jqb_count.compareTo(deduct_amt_all)>=0?deduct_amt_all:jqb_count;//本次可抵扣最多金圈币
						
						//配送费
						params.put("company_code", company_code);
						params.put("area_dict_num", staff.getArea_dict_num());
						Map<String ,Object> deliveryPrice= shoppingDao.getDeliveryPrice(params);
						model.addAttribute("free_delivery_price", deliveryPrice.get("free_delivery_price").toString());//订单免配送费价格
						BigDecimal delivery_price=new BigDecimal("0");
						if("2".equals(deliveryPrice.get("delivery_price_type").toString())) {//按订单配送				
							delivery_price=new BigDecimal(deliveryPrice.get("delivery_price").toString());				
						}
						if("1".equals(deliveryPrice.get("delivery_price_type").toString())) {//按件数
							params.put("goods_id", good_id);
							Map<String ,Object> goodDeliveryPrice= shoppingDao.getGoodDeliveryPrice(params);
							if(goodDeliveryPrice!=null) {
								delivery_price=new BigDecimal(goodDeliveryPrice.get("delivery_price").toString()).multiply(new BigDecimal(good_count));	
							}						
						}
						BigDecimal actual_delivery_price=sum_price.compareTo(new BigDecimal(deliveryPrice.get("free_delivery_price").toString()))>=0?new BigDecimal("0"):delivery_price;//实际配送费
						model.addAttribute("jqb_count", jqb_count);
						model.addAttribute("actual_delivery_price", actual_delivery_price);
						model.addAttribute("jqb_max_count", jqb_max_count);
						model.addAttribute("delivery_price", jqb_count);
						model.addAttribute("should_amt_all", sum_price.subtract(jqb_max_count).add(actual_delivery_price));//应付款金额
			    	}else {
			    		//商品id集合
				    	goods_idss += map1.get("good_id").toString()+",";
				    	
				    	//规格库存
				    	List<Map<String ,Object>> spList =  supplierShoppingService.getGoodSpecList(map1);
				    	
				    	for(Map<String,Object> m:spList) {
				    		Integer stockCount = (Integer) m.get("available_stock_count");
				    		if(auto_fxiaoshou != 1) {
				    			if(good_count1 > stockCount) {
				    				model.addAttribute("msgg", gName+"购买数量不能超过库存数量！");
									return "/WEB-INF/supplierShopping/buyNowTWOErr.jsp";
									//return gName+"购买数量不能超过库存数量！";
					    		}
				    		}
				    	}
				    	
				    	//判断限购数量
				    	if(good_count1 > (Integer.parseInt(quotaNumber))) {
				    		model.addAttribute("msgg", gName+"购买数量不能超过限购数量！");
							return "/WEB-INF/supplierShopping/buyNowTWOErr.jsp";
				    	}
				    	
				    	//判断限购周期与限购次数
						if(!cycle1.equals("") && !frequency1.equals("")) {
							Map<String,Object> oc = new HashMap<String,Object>();
							oc.put("good_id", gId);
					    	oc.put("shop_unique", shop_unique);
					    	oc.put("cycle", cycle1);
					    	oc.put("frequency", frequency1);
							PurResult result1 = supplierShoppingService.cycleFrequency(oc);
							//System.out.println(result1);
							if(result1.getStatus() == 0) {
								model.addAttribute("msgg", gName + "立即购买失败，该产品"+cycle1+"限购"+frequency1+"次！");
								return "/WEB-INF/supplierShopping/buyNowTWOErr.jsp";
								//return "购买失败，该产品"+cycle1+"限购"+frequency1+"次！";
							}
						}
				    }
			    	
			    	}
		    	goods_ids = goods_idss.split(",");
		    	model.addAttribute("settle", supplierShoppingService.querySettlementBindingList(goods_ids,shop_unique,area_dict_num));	
		    	goods_idss = goods_idss + good_id;
				model.addAttribute("ids", goods_idss);	
				return "/WEB-INF/supplierShopping/buyNowTWO.jsp";
		    	
		    }
			
		}
		
		/**
		 * 供货商城-提交捆绑订单
		 */
		@RequestMapping("/saveOrderBinding.do")
		@ResponseBody
		public PurResult saveOrderBinding(String shop_unique,String oper_id,String gold_amt,String order_amt,String actual_amt,
				String good_id,String spec_id,String spec_name,String good_count,
				String delivery_price,String ids,String order_remarks,String []delivery_fee,Integer shopType,Integer sxRuleId){		
			PurResult result = new PurResult();
			try {
				String []id=ids.split(",");	
				String []order_remark=order_remarks.split(",");	
				result = supplierShoppingService.saveOrderBinding(id,shop_unique,oper_id,gold_amt,order_amt,actual_amt,good_id,spec_id,spec_name,good_count,delivery_price,order_remark,delivery_fee,sxRuleId);
			} catch (Exception e) {
				result.setStatus(0);
				result.setMsg("提交订单失败!");	
				e.printStackTrace();
			}
			return result;
		}
		
		//跳转到-领取优惠券
		@RequestMapping("/myCoupon.do")
		public String myCoupon(Model model,String shop_unique){
			Map<String,Object> params=new HashMap<String, Object>();
			params.put("usage_status", '0');
			params.put("shop_unique", shop_unique);
			params.put("yRecord", "1");
			params.put("notre", "0");
			params.put("overdue_status", "1");
			params.put("allCou", "1");
			model.addAttribute("coupon", supplierShoppingService.getCoupon(params));//优惠券
			return "/WEB-INF/supplierShopping/coupon.jsp";
				}
		
		//跳转到-领取优惠券
		@RequestMapping("/coupon.do")
		public String coupon(Model model,String company_code,String notre){
			Map<String,Object> params=new HashMap<String, Object>();
			
			Subject subject = SecurityUtils.getSubject();
			Session session = subject.getSession();
			Staff staff = (Staff) session.getAttribute("staff");
			String shop_unique = String.valueOf(staff.getShop_unique());
			//System.out.println(shop_unique);
			params.put("company_code", company_code);
			params.put("yRecord", "-1");
			params.put("shop_unique", shop_unique);
			params.put("notre", notre);
			params.put("overdue_status", "1");
			model.addAttribute("coupon", supplierShoppingService.getCoupon(params));//优惠券
			model.addAttribute("company_code", company_code);
			return "/WEB-INF/supplierShopping/coupon.jsp";
		}
		
		//领取优惠券
		@RequestMapping("/record.do")
		@ResponseBody
		public PurResult record(HttpServletRequest request){
			PurResult rs = new PurResult();
			try {
				Map<String, Object> params = ServletsUtil.getParameters(request);
				Subject subject = SecurityUtils.getSubject();
				Session session = subject.getSession();
				Staff staff = (Staff) session.getAttribute("staff");
				String shop_unique = String.valueOf(staff.getShop_unique());
				params.put("shop_unique", shop_unique);
				rs=supplierShoppingService.record(params);
			} catch (Exception e) {
				rs.setStatus(0);
				rs.setMsg("操作失败!");
				e.printStackTrace();
			}		
			return rs;
		}
		
		//领取优惠券
		@RequestMapping("/recordAll.do")
		@ResponseBody
		public PurResult recordAll(HttpServletRequest request){
			PurResult rs = new PurResult();
			try {
				Map<String, Object> params = ServletsUtil.getParameters(request);
				Subject subject = SecurityUtils.getSubject();
				Session session = subject.getSession();
				Staff staff = (Staff) session.getAttribute("staff");
				String shop_unique = String.valueOf(staff.getShop_unique());
				params.put("shopUnique", shop_unique);
				rs=supplierShoppingService.recordAll(params);
			} catch (Exception e) {
				rs.setStatus(0);
				rs.setMsg("操作失败!");
				e.printStackTrace();
			}		
			return rs;
		}		
		
		
		//跳转到-选择优惠券
		@RequestMapping("/shopCoupon.do")
		public String shopCoupon(Model model,String company_code,String coupon_id,String record_id,String should_amt_all,String sum_amt_all,String loan_amt){
			Map<String,Object> params=new HashMap<String, Object>();
					
			Subject subject = SecurityUtils.getSubject();
			Session session = subject.getSession();
			Staff staff = (Staff) session.getAttribute("staff");
			String shop_unique = String.valueOf(staff.getShop_unique());
			
			params.put("company_code", company_code);
			params.put("shop_unique", shop_unique);
			params.put("overdue_status", "1");
			params.put("usage_status", "0");
			if(company_code.equals("GS371300001")) {
				params.put("yRecord", "0");
			}else {
				params.put("yRecord", "1");
			}
			params.put("notre", "0");
			model.addAttribute("coupon", supplierShoppingService.getCoupon(params));//已领取优惠券
			model.addAttribute("record_id", record_id);
			model.addAttribute("coupon_id", coupon_id);
			model.addAttribute("company_code", company_code);
			model.addAttribute("should_amt_all", should_amt_all);
			model.addAttribute("sum_amt_all", sum_amt_all);
			model.addAttribute("loan_amt", loan_amt);
			return "/WEB-INF/supplierShopping/shopCoupon.jsp";
		}
		
	
}
