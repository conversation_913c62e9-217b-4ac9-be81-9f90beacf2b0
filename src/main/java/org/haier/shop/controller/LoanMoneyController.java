package org.haier.shop.controller;

import java.io.BufferedOutputStream;
import java.io.BufferedReader;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.util.HashMap;
import java.util.Iterator;
import java.util.Map;
import java.util.SortedMap;
import java.util.TreeMap;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.apache.shiro.SecurityUtils;
import org.apache.shiro.session.Session;
import org.apache.shiro.subject.Subject;
import org.haier.shop.entity.Staff;
import org.haier.shop.service.LoanMoneyService;
import org.haier.shop.util.PurResult;
import org.haier.shop.util.ShopsResult;
import org.haier.shop.util.wxPay.PayConfigUtil;
import org.haier.shop.util.wxPay.PayToolUtil;
import org.haier.shop.util.wxPay.XMLUtil4jdom;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;



@Controller
@RequestMapping("/loanMoney")
public class LoanMoneyController {
	@Resource
	private LoanMoneyService loanService;
	
	@RequestMapping("/loanManagerPage.do")
	public String loanManagerPage(HttpServletRequest request){
		Subject subject = SecurityUtils.getSubject();
		Session session = subject.getSession();
		Staff staff = (Staff) session.getAttribute("staff");
		String shop_unique = String.valueOf(staff.getShop_unique());
		Map<String,Object> map=loanService.queryIsOpenLoan(shop_unique);
		
		if(map==null){
			request.setAttribute("audit_status", -1);
			//跳到开通审核页面
			return "/WEB-INF/loanMoney/loan_protocol.jsp";
		}else if("4".equals(map.get("audit_status").toString())){
			//审核不通过重新上传
			request.setAttribute("data", map);
			return "/WEB-INF/loanMoney/updateOpenLoan.jsp";
			
		}else if("2".equals(map.get("audit_status").toString())||"3".equals(map.get("audit_status").toString())){
			//审核通过
			//查询提前还款的金额
			Map<String,Object> advanceMoney=loanService.queryAdvanceMoney(shop_unique); 
			//查询基本的赊销规则，如果需要提前还款，计算提前还款金额
			map.put("advance_money", advanceMoney == null ? 0 : advanceMoney.get("advance_money") );
			map.put("principal_money", advanceMoney == null ? 0 : advanceMoney.get("principal_money"));
			map.put("wyj", advanceMoney == null ? 0 : advanceMoney.get("wyj"));
			
			map.put("not_return_money", advanceMoney == null ? 0 : advanceMoney.get("notReturnMoney"));
			request.setAttribute("data", map);
			return "/WEB-INF/loanMoney/shopLoan.jsp";
			
		}
		request.setAttribute("data", map);
		return "/WEB-INF/loanMoney/loanInfo.jsp";
	}
	
	
	@RequestMapping("/openLoanPage.do")
	public String openLoanPage(HttpServletRequest request){
		//查询是否开通赊销
		return "/WEB-INF/loanMoney/openLoanPage.jsp";
	}
	//还款明细
	@RequestMapping("/loanReturnPage.do")
	public String loanReturnPage(HttpServletRequest request){
		//查询是否开通赊销
		return "/WEB-INF/loanMoney/loanReturnPage.jsp";
	}
	//借款明细
	@RequestMapping("/loanListPage.do")
	public String loanListPage(HttpServletRequest request){
		//查询是否开通赊销
		return "/WEB-INF/loanMoney/loanListPage.jsp";
	}
	
	 @RequestMapping("/addOpenLoan.do")
	 @ResponseBody
	 public ShopsResult  addOpenLoan(
				HttpServletRequest request,
				@RequestParam(value="shop_unique",required=true)String shop_unique,
				String id
				
				) throws Exception{
			Map<String,Object> map=new HashMap<String, Object>();
			map.put("shop_unique", shop_unique);
			map.put("id", id);
			
		return loanService.addOpenLoan(map, request);
	}
	 @RequestMapping("/toPayTypeSelect.do")
	 public String toPayTypeSelect(String shop_unique , Double actual_amt,HttpServletRequest request,HttpServletResponse response,String principal_money) {
			request.setAttribute("orderNo", "HK_"+System.nanoTime());
			request.setAttribute("orderAmount", actual_amt);
			request.setAttribute("principal_money", principal_money);
			return "/WEB-INF/supplierShopping/payTypeSelect.jsp";
	 }
	 
	 @RequestMapping("/queryOrderStatus.do")
	 @ResponseBody
	 public ShopsResult  queryOrderStatus(
				String out_trade_no){
			Map<String,Object> map=new HashMap<String, Object>();
			map.put("sale_list_unique", out_trade_no);
			
		return loanService.queryOrderStatus(map);
	}
	 
	 @SuppressWarnings({ "unchecked", "rawtypes" })
	 @RequestMapping(value = "weixinNotify.do", produces = "application/json;charset=UTF-8")
	 @ResponseBody
	 public void weixinNotify(HttpServletRequest request, HttpServletResponse response) throws  Exception{
	        //读取参数 
//			System.out.println("回调成功");
	        InputStream inputStream ;  
	        StringBuffer sb = new StringBuffer();  
	        inputStream = request.getInputStream();  
	        String s ;  
	        BufferedReader in = new BufferedReader(new InputStreamReader(inputStream, "UTF-8"));  
	        while ((s = in.readLine()) != null){  
	            sb.append(s);
	        }
	        in.close();
	        inputStream.close();
	  
	        //解析xml成map  
	        Map<String, String> m = new HashMap<String, String>();  
	        m = XMLUtil4jdom.doXMLParse(sb.toString());  
	        
	        //过滤空 设置 TreeMap  
	        SortedMap<Object,Object> packageParams = new TreeMap<Object,Object>();        
			Iterator it = m.keySet().iterator();  
	        while (it.hasNext()) {  
	            String parameter = (String) it.next();
	            String parameterValue = m.get(parameter);
	            
	            String v = "";  
	            if(null != parameterValue) {
	                v = parameterValue.trim();  
	            }  
	            packageParams.put(parameter, v);  
	        }  
	          
	        // 账号信息  
	        String key = PayConfigUtil.API_KEY; //key  
	  
	        //判断签名是否正确  
	        if(PayToolUtil.isTenpaySign("UTF-8", packageParams,key)) {  
	            //------------------------------  
	            //处理业务开始  
	            //------------------------------  
	            String resXml = "";  
	            if("SUCCESS".equals((String)packageParams.get("result_code"))){  
	                // 这里是支付成功  
	                //////////执行自己的业务逻辑////////////////  
	                String out_trade_no = (String)packageParams.get("out_trade_no");  
	                
	                String total_fee = (String)packageParams.get("total_fee");  
	                
	                
	               loanService.updateReturnMoney(out_trade_no);
	               
	                System.out.println("还款支付成功"+out_trade_no+"金额"+total_fee);  
	                //通知微信.异步确认成功.必写.不然会一直通知后台.八次之后就认为交易失败了.  
	                resXml = "<xml>" + "<return_code><![CDATA[SUCCESS]]></return_code>"  
	                        + "<return_msg><![CDATA[OK]]></return_msg>" + "</xml> ";  
	                  
	            } else {
	                resXml = "<xml>" + "<return_code><![CDATA[FAIL]]></return_code>"  
	                        + "<return_msg><![CDATA[报文为空]]></return_msg>" + "</xml> ";  
	                
	            }
	            //------------------------------  
	            //处理业务完毕  
	            //------------------------------  
	            BufferedOutputStream out = new BufferedOutputStream(response.getOutputStream());  
	            out.write(resXml.getBytes());  
	            out.flush();  
	            out.close();  
	        } else{  
	        	System.out.println("通知签名验证失败");  
	        }
	    }
	 
	 /**
	 * 查询还款明细列表
	 * @param map
	 * @return
	 */
	@RequestMapping("/queryLoanReturnList.do")
	@ResponseBody
	public PurResult queryLoanReturnList(
			@RequestParam(value="shopMessage",defaultValue="")String shopMessage,
			@RequestParam(value="startTime",defaultValue="")String startTime,
			@RequestParam(value="endTime",defaultValue="")String endTime,
			@RequestParam(value="managerUnique")Long managerUnique,
			@RequestParam(value = "page", defaultValue = "1") int page,
			@RequestParam(value = "limit", defaultValue = "15") int pageSize
			){
		Map<String,Object> map=new HashMap<String, Object>();
		map.put("managerUnique", managerUnique);
		map.put("endTime", endTime);
		map.put("startTime", startTime);
		map.put("pageSize", pageSize);
		map.put("startNum", (page-1)*pageSize);
		if(shopMessage!=null&&!"".equals(shopMessage)){
			map.put("shopMessage", "%"+shopMessage+"%");
		}
		System.out.println(map.toString());
		return loanService.queryLoanReturnList(map);
	}
	/**
	 * 查询借款明细列表
	 * @param map
	 * @return
	 */
	@RequestMapping("/queryLoanList.do")
	@ResponseBody
	public PurResult queryLoanList(
			@RequestParam(value="shopMessage",defaultValue="")String shopMessage,
			@RequestParam(value="startTime",defaultValue="")String startTime,
			@RequestParam(value="endTime",defaultValue="")String endTime,
			@RequestParam(value="managerUnique")Long managerUnique,
			@RequestParam(value = "page", defaultValue = "1") int page,
			@RequestParam(value = "limit", defaultValue = "15") int pageSize,
			@RequestParam(value = "valid_type", defaultValue = "1") int valid_type
			){
		Map<String,Object> map=new HashMap<String, Object>();
		map.put("managerUnique", managerUnique);
		map.put("endTime", endTime);
		map.put("startTime", startTime);
		map.put("pageSize", pageSize);
		map.put("startNum", (page-1)*pageSize);
		map.put("valid_type", valid_type);
		if(shopMessage!=null&&!"".equals(shopMessage)){
			map.put("shopMessage", "%"+shopMessage+"%");
		}
		System.out.println(map.toString());
		return loanService.queryLoanList(map);
	}
		
}
