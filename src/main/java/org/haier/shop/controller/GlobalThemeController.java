package org.haier.shop.controller;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.servlet.http.HttpServletRequest;

import org.haier.shop.entity.globalSelect.GlobalDetailVO;
import org.haier.shop.service.GlobalThemeService;
import org.haier.shop.service.GoodsService;
import org.haier.shop.util.PurResult;
import org.haier.shop.util.ServletsUtil;
import org.haier.shop.util.ShopsResult;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

@Controller
@RequestMapping("/global")
public class GlobalThemeController {

	@Autowired
	private GlobalThemeService globalService;
	@Autowired
	private GoodsService goodsService;
	
	//跳转到-供货商全球精选页面
	@RequestMapping("/toSupplierGlobal.do")
	public String toSupplierGlobal(){
		return "/WEB-INF/global/supplierGlobalList.jsp";
	}
	
	//跳转到-总平台全球精选页面
	@RequestMapping("/toPlatformGlobal.do")
	public String toPlatformGlobal(){
		return "/WEB-INF/global/platformGlobalList.jsp";
	}
	
	//跳转到-全球精选上下架页面
	@RequestMapping("/toGlobalGoodShelf.do")
	public String toGlobalGoodShelf(){
		return "/WEB-INF/global/globalGoodShelf.jsp";
	}
	//跳转到-全球精选-第一书记页面
	@RequestMapping("/toGlobalSecretary.do")
	public String toGlobalSecretary(){
		return "/WEB-INF/global/globalSecretary.jsp";
	}	
	//跳转到-全球精选-第一书记页面-新增界面
	@RequestMapping("/addSeretaryPage.do")
	public String addSeretaryPage(){
		return "/WEB-INF/global/addSeretary.jsp";
	}
	//跳转到-全球精选-第一书记页面-编辑
	@RequestMapping("/editSecretaryPage.do")
	
	
	public String editSecretaryPage(Model model,String id){
		
		Map<String ,Object> data = globalService.querySecretary(id); 
		model.addAttribute("id", id);
		model.addAttribute("data", data);
		return "/WEB-INF/global/editSecretary.jsp";
	}
	/**
	 * 跳转到驳回页面
	 */
	@RequestMapping("/toAuditNo.do")
	public String toAuditNo(){
		return "/WEB-INF/global/auditNo.jsp";
	}
	
	//跳转到上架页面
	@RequestMapping("/toGoodShelf.do")
	public String toGoodShelf(){
		return "/WEB-INF/global/upperShelf.jsp";
	}
	
	//跳转到-全球精选订单列表页面
	@RequestMapping("/toGlobalOrderList.do")
	public String toGlobalOrderList(){
		return "/WEB-INF/global/orderList.jsp";
	}
	//跳转到-全球精选订单列表页面
	@RequestMapping("/toGlobalOrderPtList.do")
	public String toGlobalOrderPtList(){
		return "/WEB-INF/global/ptOrderList.jsp";
	}
	
	//跳转到-订单详情
	@RequestMapping("/toOrderDetail.do")
	public String toOrderDetail(Model model,String global_sub_id){
		model.addAttribute("global_sub_id", global_sub_id);
		return "/WEB-INF/global/orderDetail.jsp";
	}
	//跳转到-平台全球精选商品上下架详情
	@RequestMapping("/toOnShelf.do")
	public String toOnShelf(Model model,String barCode,String id,String lower_price){
		model.addAttribute("barCode", barCode);
		model.addAttribute("id", id);
		model.addAttribute("lower_price", lower_price);
		return "/WEB-INF/global/globalGoodShelfPt.jsp";
	}
	/**
	 * 
	 */
	@RequestMapping("/toExpress.do")
	public String toExpress(){
		return "/WEB-INF/global/toExpress.jsp";
	}
	
	/**
	 * 供货商全球精选列表
	 */
	@RequestMapping("/queryGlobalSupplierList.do")
	@ResponseBody
	public PurResult queryGlobalSupplierList(
			@RequestParam(value="content",defaultValue="")String content,
			@RequestParam(value="cuxiao_status",defaultValue="")String cuxiao_status,
			@RequestParam(value="audit_status",defaultValue="")String audit_status,
			@RequestParam(value="shop_unique",defaultValue="")Long shop_unique,
			@RequestParam(value = "page", defaultValue = "1") int page,
			@RequestParam(value = "limit", defaultValue = "15") int pageSize
			){
		Map<String,Object> map=new HashMap<String, Object>();
		map.put("shop_unique", shop_unique);
		map.put("pageSize", pageSize);
		map.put("startNum", (page-1)*pageSize);
		map.put("content", content);
		map.put("cuxiao_status", cuxiao_status);
		map.put("audit_status", audit_status);
		return globalService.queryGlobalSupplierList(map);
	}
	
	/**
	 * 全球精选-订单页面
	 */
	@RequestMapping("/queryGlobalOrderList.do")
	@ResponseBody
	public PurResult queryGlobalOrderList(
			@RequestParam(value="content",defaultValue="")String content,
			@RequestParam(value="handle_status",defaultValue="")String handle_status,
			@RequestParam(value="shop_unique",defaultValue="")Long shop_unique,
			@RequestParam(value = "page", defaultValue = "1") int page,
			@RequestParam(value = "limit", defaultValue = "15") int pageSize
			){
		Map<String,Object> map=new HashMap<String, Object>();
		map.put("shop_unique", shop_unique);
		map.put("pageSize", pageSize);
		map.put("startNum", (page-1)*pageSize);
		map.put("content", content);
		map.put("handle_status", handle_status);
		return globalService.queryGlobalOrderList(map);
	}
	
	
	//跳转到-供货商全球精选-新增页面
	@RequestMapping("/addGlobalSelect.do")
	public String addGlobalSelect(Model model){
		Map<String,Object> params=new HashMap<String,Object>();
    	params.put("id", null);
    	//获取有商家的区域列表
    	List<Map<String ,Object>> areaList = globalService.getAreaList(params); 
    	//获取有商家的城市列表
    	List<Map<String ,Object>> cityList = globalService.getCityList(areaList);
    	//获取有商家的省份列表
    	List<Map<String ,Object>> provinceList = globalService.getProvinceList(cityList);
		//获取所有审核通过店铺列表
		List<Map<String ,Object>> shopList = goodsService.getAllShopList();
		model.addAttribute("shopList", shopList);
    	model.addAttribute("areaList", areaList);
    	model.addAttribute("cityList", cityList);
    	model.addAttribute("provinceList", provinceList);
    	return "/WEB-INF/global/addGlobalSelect.jsp";
	}
	

	
	/**
	 * 禁用  删除
	 */
	@RequestMapping("/updateGlobalThemeStatus.do")
	@ResponseBody
	public PurResult updateGlobalThemeStatus(HttpServletRequest request){
		PurResult rs = new PurResult();
		try {
			Map<String, Object> params = ServletsUtil.getParameters(request);
			rs=globalService.updateGlobalThemeStatus(params);
		} catch (Exception e) {
			rs.setStatus(0);
			rs.setMsg("操作失败!");
			e.printStackTrace();
		}		
		return rs;
	}
	
	/**
	 * 主题审核
	 */
	@RequestMapping("/auditGlobalTheme.do")
	@ResponseBody
	public PurResult auditGlobalTheme(HttpServletRequest request){
		PurResult rs = new PurResult();
		try {
			Map<String, Object> params = ServletsUtil.getParameters(request);
			rs=globalService.insertThemeAudit(params);
		} catch (Exception e) {
			rs.setStatus(0);
			rs.setMsg("操作失败!");
			e.printStackTrace();
		}		
		return rs;
	}
	
	/**
	 * 上架
	 */
	@RequestMapping("/upperShelfGlobal.do")
	@ResponseBody
	public PurResult upperShelfGlobal(HttpServletRequest request){
		PurResult rs = new PurResult();
		try {
			Map<String, Object> params = ServletsUtil.getParameters(request);
			rs=globalService.upperShelfGlobal(params);
		} catch (Exception e) {
			rs.setStatus(0);
			rs.setMsg("操作失败!");
			e.printStackTrace();
		}		
		return rs;
	}
	
	/**
	 * 下架
	 */
	@RequestMapping("/updateGoodShelfStatus.do")
	@ResponseBody
	public PurResult updateGoodShelfStatus(HttpServletRequest request){
		PurResult rs = new PurResult();
		try {
			Map<String, Object> params = ServletsUtil.getParameters(request);
			rs=globalService.updateGoodShelfStatus(params);
		} catch (Exception e) {
			rs.setStatus(0);
			rs.setMsg("操作失败!");
			e.printStackTrace();
		}		
		return rs;
	}
	
	/**
	 * 全球精选商品上下架
	 */
	@RequestMapping("/queryGlobalGoodShelfList.do")
	@ResponseBody
	public PurResult queryGlobalGoodShelfList(
			@RequestParam(value="content",defaultValue="")String content,
			@RequestParam(value="shelf_status",defaultValue="")String shelf_status,
			@RequestParam(value="shop_unique",defaultValue="")Long shop_unique,
			@RequestParam(value="area_dict_num",defaultValue="")String area_dict_num,
			@RequestParam(value = "page", defaultValue = "1") int page,
			@RequestParam(value = "limit", defaultValue = "15") int pageSize
			){
		Map<String,Object> map=new HashMap<String, Object>();
		map.put("shop_unique", shop_unique);
		map.put("area_dict_num", area_dict_num);
		map.put("pageSize", pageSize);
		map.put("startNum", (page-1)*pageSize);
		map.put("content", content);
		map.put("shelf_status", shelf_status);
		return globalService.queryGlobalGoodShelfList(map);
	}
	/**
	 * 添加全家精选
	 */

	@RequestMapping("/addGlobalTheme.do")
	@ResponseBody
	public PurResult addGlobalTheme(HttpServletRequest request){
		PurResult rs = new PurResult();
		try {
			Map<String, Object> params = ServletsUtil.getParameters(request);
			
			
			rs=globalService.addGlobalTheme(params);
		} catch (Exception e) {
			rs.setStatus(0);
			rs.setMsg("操作失败!");
			e.printStackTrace();
		}		
		return rs;
	}
	
	/**
	 * 跳转到全球精选详情界面
	 */
	@RequestMapping("/toGlobalThemeDetail.do")
	public String toGlobalThemeDetail(Model model,String id){
		Map<String,Object> params=new HashMap<String,Object>();
    	params.put("id", id);
    	//获取有商家的区域列表
    	List<Map<String ,Object>> areaList = globalService.getAreaList(params); 
    	//获取有商家的城市列表
    	List<Map<String ,Object>> cityList = globalService.getCityList(areaList);
    	//获取有商家的省份列表
    	List<Map<String ,Object>> provinceList = globalService.getProvinceList(cityList); 
    	
    	for(Map<String ,Object> city:cityList)
    	{
    		int num=0;
    		for(Map<String ,Object> area:areaList)
        	{
    			if(area.get("city_code").equals(city.get("city_code")))
    			{
    				
    				if(area.get("flag").equals("false"))
    				{
    					num=num+1;
    				}
    			}
        		
        	}
    		if(num==0)
    		{
    			city.put("flag", "true");
    		}else
    		{
    			city.put("flag", "false");
    		}
    		
    	}
    	
    	
    	GlobalDetailVO data=globalService.queryGlobalThemeDetail(params);
    	model.addAttribute("areaList", areaList);
    	model.addAttribute("cityList", cityList);
    	model.addAttribute("provinceList", provinceList);
    	model.addAttribute("id", id);
    	model.addAttribute("data", data);
    	
		return "/WEB-INF/global/globalSelectDetail.jsp";
	}
	/**
	 * 全球精选详情
	 */
	@RequestMapping("/queryGlobalThemeDetail.do")
	@ResponseBody
	public PurResult queryGlobalThemeDetail(HttpServletRequest request)
	{
		PurResult rs=new PurResult();
		try {
			Map<String,Object> params=ServletsUtil.getParameters(request);
			
			GlobalDetailVO data=globalService.queryGlobalThemeDetail(params);
			rs.setData(data);
			rs.setStatus(1);
		}catch(Exception e)
		{
			rs.setStatus(0);
			rs.setMsg("操作失败！");
			e.printStackTrace();
		}
		return rs;
	}
	
	/**
	 * 发货
	 */
	@RequestMapping("/editExpressInfo.do")
	@ResponseBody
	public PurResult editExpressInfo(HttpServletRequest request){
		PurResult rs = new PurResult();
		try {
			Map<String, Object> params = ServletsUtil.getParameters(request);
			rs=globalService.editExpressInfo(params);
		} catch (Exception e) {
			rs.setStatus(0);
			rs.setMsg("操作失败!");
			e.printStackTrace();
		}		
		return rs;
	}
	
	@RequestMapping("/getGlobalOrderDetail.do")
	@ResponseBody
	public PurResult getGlobalOrderDetail(@RequestParam(value="global_sub_id",defaultValue="")String global_sub_id){
		Map<String,Object> map=new HashMap<String, Object>();
		map.put("global_sub_id", global_sub_id);
		return globalService.getGlobalOrderDetail(map);
	}
	/**
	  * 总平图全球精选-商品商品上下架详情
	 * @param request
	 * @return
	 */
	@RequestMapping("/queryPTGGShelfList.do")
	@ResponseBody
	public PurResult queryPTGGShelfList(HttpServletRequest request,@RequestParam(value = "page", defaultValue = "1") int page,
			@RequestParam(value = "limit", defaultValue = "15") int pageSize){
		PurResult rs = new PurResult();
		try {
			
			Map<String, Object> params = ServletsUtil.getParameters(request);
			params.put("pageSize", pageSize);
			params.put("startNum", (page-1)*pageSize);
			rs=globalService.queryPTGGShelfList(params);
		} catch (Exception e) {
			rs.setStatus(0);
			rs.setMsg("操作失败!");
			e.printStackTrace();
		}		
		return rs;
	}
	/**
	 * 平台上架
	 */
	@RequestMapping("/upperShelfGlobal2.do")
	@ResponseBody
	public PurResult upperShelfGlobal2(HttpServletRequest request){
		PurResult rs = new PurResult();
		try {
			Map<String, Object> params = ServletsUtil.getParameters(request);
			rs=globalService.upperShelfGlobal2(params);
		} catch (Exception e) {
			rs.setStatus(0);
			rs.setMsg("操作失败!");
			e.printStackTrace();
		}		
		return rs;
	}
	/**
	 * 平台批量上下架
	 */
	@RequestMapping("/updateGoodShelf.do")
	@ResponseBody
	public PurResult updateGoodShelf(HttpServletRequest request){
		PurResult rs = new PurResult();
		try {
			Map<String, Object> params = ServletsUtil.getParameters(request);
			rs=globalService.updateGoodShelf(params);
		} catch (Exception e) {
			rs.setStatus(0);
			rs.setMsg("操作失败!");
			e.printStackTrace();
		}		
		return rs;
	}
	
	/**
	 * 全球精选-订单页面
	 */
	@RequestMapping("/queryGlobalSecretaryList.do")
	@ResponseBody
	public PurResult queryGlobalSecretaryList(
			@RequestParam(value="shop_unique",defaultValue="")Long shop_unique,
			@RequestParam(value="search_message",defaultValue="")String search_message,
			@RequestParam(value = "page", defaultValue = "1") int page,
			@RequestParam(value = "limit", defaultValue = "15") int pageSize
			){
		Map<String,Object> map=new HashMap<String, Object>();
		map.put("shop_unique", shop_unique);
		map.put("pageSize", pageSize);
		map.put("search_message", search_message);
		map.put("startNum", (page-1)*pageSize);
		return globalService.queryGlobalSecretaryList(map);
	}
	
	/**
	 * 全球精选-订单页面
	 */
	@RequestMapping("/queryGlobalSecretary.do")
	@ResponseBody
	public PurResult queryGlobalSecretary(){
		Map<String,Object> map=new HashMap<String, Object>();
		return globalService.queryGlobalSecretaryList(map);
	}
	
	/**
	  * 新增第一书记
	 */
	@RequestMapping("/addSecretary.do")
	@ResponseBody
	public PurResult addSecretary(HttpServletRequest request){
		PurResult rs = new PurResult();
		try {
			Map<String, Object> params = ServletsUtil.getParameters(request);
			rs=globalService.addSecretary(params);
		} catch (Exception e) {
			rs.setStatus(0);
			rs.setMsg("操作失败!");
			e.printStackTrace();
		}		
		return rs;
	}
	/**
	 *   删除
	 */
	@RequestMapping("/deleteGlobalSecretary.do")
	@ResponseBody
	public PurResult deleteGlobalSecretary(HttpServletRequest request){
		PurResult rs = new PurResult();
		try {
			Map<String, Object> params = ServletsUtil.getParameters(request);
			rs=globalService.deleteGlobalSecretary(params);
		} catch (Exception e) {
			rs.setStatus(0);
			rs.setMsg("操作失败!");
			e.printStackTrace();
		}		
		return rs;
	}
	/**
	 * 修改
	 */
	@RequestMapping("/updateSecretary.do")
	@ResponseBody
	public PurResult updateSecretary(HttpServletRequest request){
		PurResult rs = new PurResult();
		try {
			Map<String, Object> params = ServletsUtil.getParameters(request);
			rs=globalService.updateSecretary(params);
		} catch (Exception e) {
			rs.setStatus(0);
			rs.setMsg("操作失败!");
			e.printStackTrace();
		}		
		return rs;
	}
	/**
	  * 地区查询
	 * @return
	 */
	@RequestMapping("/queryGoodsKinds.do")
	@ResponseBody
	public ShopsResult queryGoodsKinds(String shop_unique,String area_dict_parent_num){
		return globalService.queryGoodsKinds(shop_unique, area_dict_parent_num);
	}
}
