package org.haier.shop.controller;

import java.net.InetAddress;
import java.net.UnknownHostException;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;

import org.apache.shiro.SecurityUtils;
import org.apache.shiro.session.Session;
import org.apache.shiro.subject.Subject;
import org.haier.log.annotation.RemoteLog;
import org.haier.log.enums.BusinessType;
import org.haier.shop.entity.Staff;
import org.haier.shop.service.DisService;
import org.haier.shop.service.ShopService;
import org.haier.shop.util.HandleMessyCode;
import org.haier.shop.util.PurResult;
import org.haier.shop.util.ServletsUtil;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.w3c.dom.css.ElementCSSInlineStyle;

@RequestMapping("/dis")
@Controller
public class DisController {
	
	@Resource
	private ShopService shopService;
	
	@Resource
	private DisService disService;
	
	/**
	 * 跳转开通分销列表
	 * @return
	 */
	@RequestMapping("/openDisPage.do")
	public String openDisPage(){
		return "/WEB-INF/dis/openDisList.jsp";
	}
	
	/**
	 * 查询小程序审核列表
	 * @return
	 */
	@RequestMapping("/openDisList.do")
	@ResponseBody
	public PurResult openDisList(
			String is_dis,
			String shopMessage,
			@RequestParam(value = "page", defaultValue = "1") int page,
			@RequestParam(value = "limit", defaultValue = "8") int pageSize){
		Map<String,Object> map=new HashMap<String, Object>();
		map.put("is_dis", is_dis);
		map.put("shopMessage", shopMessage);
		map.put("pageSize", pageSize);
//		map.put("startNum", ((page-1)%44)*pageSize);//shopTest
		map.put("startNum", (page-1)*pageSize);
		return shopService.queryWechatExamineList(map);
	}
	
	/**
	 * 商家开通/关闭分销
	 * @param shop_unique 店铺唯一编码
	 * @param is_dis 是否开启分销：1不开启 2开启
	 * @return
	 */
	@RequestMapping("/updateShopIsDis.do")
	@ResponseBody
	public PurResult updateShopIsDis(@RequestParam(value="shop_unique",required=true)Long shop_unique,String is_dis){
		Map<String,Object> params = new HashMap<String, Object>();
		params.put("shop_unique", shop_unique);
		params.put("is_dis", is_dis);
		return shopService.updateShopIsDis(params);
	}
	
	/**
	 * 跳转商家分销商等级列表页面
	 * @return
	 */
	@RequestMapping("/toShopDisLevelListPage.do")
	public String toShopDisLevelListPage(){
		return "/WEB-INF/dis/shopDisLevelList.jsp";
	}
	
	/**
	 * 查询商家分销商等级列表
	 * @return
	 */
	@RequestMapping("/shopDisLevelList.do")
	@ResponseBody
	public PurResult shopDisLevelList(String shop_unique){
		Map<String,Object> params = new HashMap<String, Object>();
		params.put("shop_unique", shop_unique);
		return shopService.queryShopDisLevelList(params);
	}
	
	/**
	 * 跳转商家分销商等级详情页面
	 * @return
	 */
	@RequestMapping("/shopDisLevelDetailPage.do")
	public String shopDisLevelDetailPage(String dis_level_id,Model model){
		Map<String ,Object> disLevle = shopService.queryShopDisLevel(dis_level_id);
		model.addAttribute("disLevle", disLevle);
		return "/WEB-INF/dis/shopDisLevelEdit.jsp";
	}
	
	/**
	 * 跳转选择商品页面
	 * @return
	 */
	@RequestMapping("/choseGoods.do")
	public String choseGoods(String id,Model model){
		model.addAttribute("id", id);
		return "/WEB-INF/dis/choseGoods.jsp";
	}
	/**
	 * 跳转选择复选框商品页面
	 * @return
	 */
	@RequestMapping("/choseGoodsCheckbox.do")
	public String choseGoodsCheckbox(Model model,int type){
		model.addAttribute("type", type);
		return "/WEB-INF/dis/choseGoodsCheckbox.jsp";
	}
	/**
	 * 跳转选择商品分类页面
	 * @return
	 */
	@RequestMapping("/choseGoodsKind.do")
	public String choseGoodsKind(Model model,int type){
		model.addAttribute("type", type);
		return "/WEB-INF/dis/choseGoodsKindCheckbox.jsp";
	}
	
	/**
	 * 修改商家分销商等级
	 * @return
	 */
	@RequestMapping("/updateDisLevel.do")
	@ResponseBody
	public PurResult updateDisLevel(HttpServletRequest request){
		Map<String, Object> params = HandleMessyCode.handle(ServletsUtil.getParameters(request));
		return shopService.updateDisLevel(params);
	}
	
	/**
	 * 跳转分销商管理列表
	 * @return
	 */
	@RequestMapping("/shopDisListPage.do")
	public String shopDisListPage(Model model){
		Subject subject = SecurityUtils.getSubject();
		Session session = subject.getSession();
		Staff staff = (Staff) session.getAttribute("staff");
		String shop_unique = String.valueOf(staff.getShop_unique());
		//获取分销商等级列表
		Map<String,Object> params = new HashMap<String, Object>();
		params.put("shop_unique", shop_unique);
		List<Map<String ,Object>> levelList = shopService.getDisLevelList(params);
		model.addAttribute("levelList", levelList);
		//查询团长的数量
		int parent_count = shopService.queryDifferentDisLevelCount(params);
		model.addAttribute("parent_count", parent_count);
		return "/WEB-INF/dis/shopDisList.jsp";
	}
	
	/**
	 * 查询团长列表
	 * @return
	 */
	@RequestMapping("/shopDisList.do")
	@ResponseBody
	public PurResult shopDisList(
			String shop_unique,
			String disMessage,
			String status,
			String start_time,
			String end_time,
			String teamNumber,
			@RequestParam(value = "page", defaultValue = "1") int page,
			@RequestParam(value = "limit", defaultValue = "8") int pageSize){
		Map<String,Object> map = new HashMap<String, Object>();
		map.put("shop_unique", shop_unique);
		map.put("disMessage", disMessage);
		map.put("status", status);
		map.put("start_time", start_time);
		map.put("end_time", end_time);
		map.put("teamNumber", teamNumber);
		map.put("pageSize", pageSize);
		map.put("startNum", (page-1)*pageSize);
		return disService.shopDisList(map);
	}
	
	/**
	 * 跳转新增分销商页面
	 * @return
	 */
	@RequestMapping("/addShopDisPage.do")
	public String addShopDisPage(String shop_unique,Model model){
		//获取分销商等级列表
		Map<String,Object> params = new HashMap<String, Object>();
		params.put("shop_unique", shop_unique);
		List<Map<String ,Object>> levelList = shopService.getDisLevelList(params);
		model.addAttribute("levelList", levelList);
		return "/WEB-INF/dis/addShopDis.jsp";
	}
	
	/**
	 * 查询会员列表
	 * @return
	 */
	@RequestMapping("/queryCustomerList.do")
	@ResponseBody
	public PurResult queryCustomerList(String cus_message){
		Map<String,Object> params = new HashMap<String, Object>();
		params.put("cus_message", cus_message);
		return disService.queryCustomerList(params);
	}
	
	/**
	 * 新增分销商
	 * @param cus_uniques 会员编码，多个以逗号隔开
	 * @param shop_unique 店铺编号
	 * @param dis_level_id 分销商等级id
	 * @return
	 */
	@RemoteLog(title = "新增团长", businessType = BusinessType.INSERT)
	@RequestMapping("/addDisRelation.do")
	@ResponseBody
	public PurResult addDisRelation(String cus_uniques,String shop_unique,String dis_level_id){
		return disService.addDisRelation(cus_uniques, shop_unique, dis_level_id);
	}
	
	/**
	 * 跳转编辑分销商页面
	 * @return
	 */
	@RequestMapping("/editShopDisPage.do")
	public String editShopDisPage(String shop_unique,String dis_relation_id,Model model){
		//获取分销商等级列表
		Map<String,Object> params = new HashMap<String, Object>();
		params.put("shop_unique", shop_unique);
		List<Map<String ,Object>> levelList = shopService.getDisLevelList(params);
		model.addAttribute("levelList", levelList);
		//获取分销商详情
		Map<String ,Object> shopDis = disService.queryDisRelation(dis_relation_id);
		model.addAttribute("shopDis", shopDis);
		return "/WEB-INF/dis/editShopDis.jsp";
	}
	
	/**
	 * 编辑分销商
	 * @param dis_relation_id 分销商id
	 * @param dis_level_id 分销商等级id
	 * @param status 状态：1正常 2取消资质
	 * @return
	 */
	@RemoteLog(title = "修改团长信息", businessType = BusinessType.UPDATE)
	@RequestMapping("/updateDisRelation.do")
	@ResponseBody
	public PurResult updateDisRelation(String dis_relation_id,String dis_level_id,String status,String one_cus_unique){
		Map<String,Object> params = new HashMap<String, Object>();
		params.put("dis_relation_id", dis_relation_id);
		params.put("dis_level_id", dis_level_id);
		if(one_cus_unique!=null&&!"".equals(one_cus_unique)){
			params.put("one_cus_unique", one_cus_unique);
		}
		params.put("status", status);
		return disService.updateDisRelation(params);
	}
	
	/**
	 * 跳转下级会员列表
	 * @return
	 */
	@RequestMapping("/shopDisLowerListPage.do")
	public String shopDisLowerListPage(
			String shop_unique,
			String cus_unique,
			Model model){
		model.addAttribute("shop_unique", shop_unique);
		model.addAttribute("cus_unique", cus_unique);
		return "/WEB-INF/dis/shopDisLowerList.jsp";
	}
	
	/**
	 * 查询分销商下级会员列表
	 * @return
	 */
	@RequestMapping("/shopDisLowerList.do")
	@ResponseBody
	public PurResult shopDisLowerList(
			String shop_unique,
			String cus_unique,
			String cus_type,
			String disMessage,
			String start_time,
			String end_time,
			@RequestParam(value = "page", defaultValue = "1") int page,
			@RequestParam(value = "limit", defaultValue = "8") int pageSize){
		Map<String,Object> map = new HashMap<String, Object>();
		map.put("shop_unique", shop_unique);
		map.put("cus_unique", cus_unique);
		map.put("cus_type", cus_type);
		map.put("disMessage", disMessage);
		map.put("start_time", start_time);
		map.put("end_time", end_time);
		map.put("pageSize", pageSize);
		map.put("startNum", (page-1)*pageSize);
		return disService.shopDisLowerList(map);
	}
	
	/**
	 * 跳转分销商详情页面
	 * @return
	 */
	@RequestMapping("/shopDisDetailPage.do")
	public String shopDisDetailPage(String dis_relation_id,Model model){
		//获取分销商详情
		Map<String ,Object> shopDis = disService.queryDisRelation(dis_relation_id);
		model.addAttribute("shopDis", shopDis);
		return "/WEB-INF/dis/shopDisDetail.jsp";
	}
	
	/**
	 * 分销商详情页面统计
	 * @return
	 */
	@RequestMapping("/shopDisDetailStatistics.do")
	@ResponseBody
	public PurResult shopDisDetailStatistics(
			String shop_unique,
			String cus_unique,
			String start_time,
			String end_time){
		Map<String,Object> map = new HashMap<String, Object>();
		map.put("shop_unique", shop_unique);
		map.put("cus_unique", cus_unique);
		map.put("start_time", start_time);
		map.put("end_time", end_time);
		return disService.shopDisDetailStatistics(map);
	}
	
	/**
	 * 跳转分销商账单明细列表
	 * @return
	 */
	@RequestMapping("/disTradingListPage.do")
	public String disTradingListPage(
			String shop_unique,
			String cus_unique,
			Model model){
		model.addAttribute("shop_unique", shop_unique);
		model.addAttribute("cus_unique", cus_unique);
		return "/WEB-INF/dis/disTradingList.jsp";
	}
	
	/**
	 * 查询分销商账单明细列表
	 * @return
	 */
	@RequestMapping("/disTradingList.do")
	@ResponseBody
	public PurResult disTradingList(
			String shop_unique,
			String cus_unique,
			String trading_flow,
			String trading_type,
			String start_time,
			String end_time,
			@RequestParam(value = "page", defaultValue = "1") int page,
			@RequestParam(value = "limit", defaultValue = "8") int pageSize){
		Map<String,Object> map = new HashMap<String, Object>();
		map.put("shop_unique", shop_unique);
		map.put("cus_unique", cus_unique);
		map.put("trading_flow", trading_flow);
		map.put("trading_type", trading_type);
		map.put("start_time", start_time);
		map.put("end_time", end_time);
		map.put("pageSize", pageSize);
		map.put("startNum", (page-1)*pageSize);
		return disService.disTradingList(map);
	}
	
	/**
	 * 跳转店铺佣金明细列表
	 * @return
	 */
	@RequestMapping("/commissionPage.do")
	public String commissionPage(Model model){
		//获取店铺佣金累计信息
		Subject subject = SecurityUtils.getSubject();
		Session session = subject.getSession();
		Staff staff = (Staff) session.getAttribute("staff");
		String shop_unique = String.valueOf(staff.getShop_unique());
		Map<String ,Object> commissionInfo = disService.queryCommissionInfo(shop_unique);
		model.addAttribute("commissionInfo", commissionInfo);
		return "/WEB-INF/dis/commissionList.jsp";
	}
	
	/**
	 * 查询店铺佣金明细列表
	 * @return
	 */
	@RequestMapping("/commissionList.do")
	@ResponseBody
	public PurResult commissionList(
			String shop_unique,
			String disMessage,
			String start_time,
			String end_time,
			@RequestParam(value = "page", defaultValue = "1") int page,
			@RequestParam(value = "limit", defaultValue = "8") int pageSize){
		Map<String,Object> map = new HashMap<String, Object>();
		map.put("shop_unique", shop_unique);
		map.put("disMessage", disMessage);
		map.put("start_time", start_time);
		map.put("end_time", end_time);
		map.put("pageSize", pageSize);
		map.put("startNum", (page-1)*pageSize);
		return disService.commissionList(map);
	}
	
	/**
	 * 跳转拓客记录列表
	 * @return
	 */
	@RequestMapping("/cusRelationListPage.do")
	public String cusRelationListPage(Model model){
	
		return "/WEB-INF/dis/cusRelationList.jsp";
	}
	/**
	 * 查询拓客列表
	 * @return
	 */
	@RequestMapping("/queryCusRelationList.do")
	@ResponseBody
	public PurResult queryCusRelationList(
			String shop_unique,
			String disMessage,
			String cusMessage,
			String start_time,
			String end_time,
			@RequestParam(value = "page", defaultValue = "1") int page,
			@RequestParam(value = "limit", defaultValue = "8") int pageSize){
		Map<String,Object> map = new HashMap<String, Object>();
		map.put("shop_unique", shop_unique);
		map.put("disMessage", disMessage);
		map.put("cusMessage", cusMessage);
		map.put("start_time", start_time);
		map.put("end_time", end_time);
		map.put("pageSize", pageSize);
		map.put("startNum", (page-1)*pageSize);
		return disService.queryCusRelationList(map);
	}
	/**
	 * 查询店铺佣金统计
	 * @return
	 */
	@RequestMapping("/commissionStatistics.do")
	@ResponseBody
	public PurResult commissionStatistics(
			String shop_unique,
			String start_time,
			String end_time){
		Map<String,Object> map = new HashMap<String, Object>();
		map.put("shop_unique", shop_unique);
		map.put("start_time", start_time);
		map.put("end_time", end_time);
		return disService.commissionStatistics(map);
	}
	
	/**
	 * 跳转分销商订单列表
	 * @return
	 */
	@RequestMapping("/disOrderPage.do")
	public String disOrderPage(){
		return "/WEB-INF/dis/disOrderList.jsp";
	}
	
	/**
	 * 查询店铺分销订单列表
	 * @return
	 */
	@RequestMapping("/disOrderList.do")
	@ResponseBody
	public PurResult disOrderList(
			String shop_unique,
			String order_message,
			String sale_list_handlestate,
			String start_time,
			String end_time,
			@RequestParam(value = "page", defaultValue = "1") int page,
			@RequestParam(value = "limit", defaultValue = "8") int pageSize){
		Map<String,Object> map = new HashMap<String, Object>();
		map.put("shop_unique", shop_unique);
		map.put("order_message", order_message);
		map.put("sale_list_handlestate", sale_list_handlestate);
		map.put("start_time", start_time);
		map.put("end_time", end_time);
		map.put("pageSize", pageSize);
		map.put("startNum", (page-1)*pageSize);
		return disService.disOrderList(map);
	}
	
	/**
	 * 跳转分销商订单详情
	 * @return
	 */
	@RequestMapping("/disOrderDetailPage.do")
	public String disOrderDetailPage(String sale_list_unique,Model model){
		//获取分销订单详情
		Map<String ,Object> disOrderDetail = disService.queryDisOrderDetail(sale_list_unique);
		model.addAttribute("disOrderDetail", disOrderDetail);
		return "/WEB-INF/dis/disOrderDetail.jsp";
	}
	
	/**
	 * 跳转分销商提现列表
	 * @return
	 */
	@RequestMapping("/disWithdListPage.do")
	public String disWithdListPage(){
		return "/WEB-INF/dis/disWithdList.jsp";
	}
	
	/**
	 * 查询分销商提现列表
	 * @return
	 */
	@RequestMapping("/disWithdList.do")
	@ResponseBody
	public PurResult disWithdList(
			String cusMessage,
			String tradingStatus,
			@RequestParam(value = "page", defaultValue = "1") int page,
			@RequestParam(value = "limit", defaultValue = "8") int pageSize){
		Map<String,Object> params = new HashMap<String, Object>();
		params.put("cusMessage", cusMessage);
		params.put("tradingStatus", tradingStatus);
		params.put("pageSize", pageSize);
		params.put("startNum", (page-1)*pageSize);
		return disService.disWithdList(params);
	}
	
	/**
	 * 分销商提现确认打款--微信企业付款给用户
	 * @return
	 */
	@RequestMapping("/confirmPayment.do")
	@ResponseBody
	public PurResult confirmPayment(
			HttpServletRequest request,
			String dis_trading_id,
			String trading_amount,
			String trading_flow,
			String openid,
			String shop_unique,
			String cus_unique
			){
		Map<String,Object> params = new HashMap<String, Object>();
		params.put("trading_amount", trading_amount);
		params.put("trading_flow", trading_flow);
		params.put("openid", openid);
		params.put("spbill_create_ip", getIpAddr(request));
		params.put("dis_trading_id", dis_trading_id);
		params.put("shop_unique", shop_unique);
		params.put("cus_unique", cus_unique);
		return disService.weixinTransfers(params);
	}

	/**
	 * 获取当前网络ip
	 * <AUTHOR>
	 * @param request
	 * @return
	 */
	public String getIpAddr(HttpServletRequest request) {
		String ipAddress = request.getHeader("x-forwarded-for");
		if (ipAddress == null || ipAddress.length() == 0 || "unknown".equalsIgnoreCase(ipAddress)) {
			ipAddress = request.getHeader("Proxy-Client-IP");
		}
		if (ipAddress == null || ipAddress.length() == 0 || "unknown".equalsIgnoreCase(ipAddress)) {
			ipAddress = request.getHeader("WL-Proxy-Client-IP");
		}
		if (ipAddress == null || ipAddress.length() == 0 || "unknown".equalsIgnoreCase(ipAddress)) {
			ipAddress = request.getRemoteAddr();
			if (ipAddress.equals("127.0.0.1") || ipAddress.equals("0:0:0:0:0:0:0:1")) {
				// 根据网卡取本机配置的IP
				InetAddress inet = null;
				try {
					inet = InetAddress.getLocalHost();
				} catch (UnknownHostException e) {
					e.printStackTrace();
				}
				ipAddress = inet.getHostAddress();
			}
		}
		// 对于通过多个代理的情况，第一个IP为客户端真实IP,多个IP按照','分割
		if (ipAddress != null && ipAddress.length() > 15) { // "***.***.***.***".length()
															// = 15
			if (ipAddress.indexOf(",") > 0) {
				ipAddress = ipAddress.substring(0, ipAddress.indexOf(","));
			}
		}
		return ipAddress;
	}
	
	/**
	 * 查询分销商下级会员列表
	 * @return
	 */
	@RequestMapping("/queryShopDisChildCusList.do")
	@ResponseBody
	public PurResult queryShopDisChildCusList(
			String shop_unique,
			String cus_unique,
			String cus_type,
			@RequestParam(value = "page", defaultValue = "1") int page,
			@RequestParam(value = "limit", defaultValue = "8") int pageSize){
		Map<String,Object> map = new HashMap<String, Object>();
		map.put("shop_unique", shop_unique);
		map.put("cus_unique", cus_unique);
		map.put("cus_type", cus_type);
		map.put("pageSize", pageSize);
		map.put("startNum", (page-1)*pageSize);
		return disService.queryShopDisChildCusList(map);
	}
	/**
	 * 查询分销商佣金收益列表
	 * @return
	 */
	@RequestMapping("/queryShopDisTradingAmountList.do")
	@ResponseBody
	public PurResult queryShopDisTradingAmountList(
			String shop_unique,
			String cus_unique,
			String cus_type,
			@RequestParam(value = "page", defaultValue = "1") int page,
			@RequestParam(value = "limit", defaultValue = "8") int pageSize){
		Map<String,Object> map = new HashMap<String, Object>();
		map.put("shop_unique", shop_unique);
		map.put("cus_unique", cus_unique);
		map.put("cus_type", cus_type);
		map.put("pageSize", pageSize);
		map.put("startNum", (page-1)*pageSize);
		if("1".equals(cus_type)){
			return disService.queryShopDisTradingAmountList(map);
		}else{
			return disService.queryShopDisCashAmountList(map);
		}
	}
	/**
	 * 跳转分销商品列表
	 * @return
	 */
	@RequestMapping("/disGoodsListPage.do")
	public String disGoodsListPage(Model model){
		return "/WEB-INF/dis/disGoodsList.jsp";
	}
	/**
	 * 查询分销商品列表
	 * @return
	 */
	@RequestMapping("/queryDisGoodsList.do")
	@ResponseBody
	public PurResult queryDisGoodsList(
			String shop_unique,
			String disMessage,
			String dis_level_id,
			@RequestParam(value = "page", defaultValue = "1") int page,
			@RequestParam(value = "limit", defaultValue = "8") int pageSize){
		Map<String,Object> map = new HashMap<String, Object>();
		map.put("shop_unique", shop_unique);
		if(disMessage!=null&&!"".equals(disMessage.trim())){
			map.put("disMessage","%"+disMessage+"%");
		}
		if(dis_level_id!=null&&!"".equals(dis_level_id.trim())){
			map.put("dis_level_id","%"+dis_level_id+"%");
		}
		map.put("pageSize", pageSize);
		map.put("startNum", (page-1)*pageSize);
		return disService.queryDisGoodsList(map);
	}
	/**
	 * 跳转设置佣金页面
	 * @return
	 */
	@RequestMapping("/addSetCommissionRatioPage.do")
	public String addSetCommissionRatioPage(Model model){
		Subject subject = SecurityUtils.getSubject();
		Session session = subject.getSession();
		Staff staff = (Staff) session.getAttribute("staff");
		String shop_unique = String.valueOf(staff.getShop_unique());
		Map<String,Object> params = new HashMap<String, Object>();
		params.put("shop_unique", shop_unique);
		Map<String ,Object> shop_config = shopService.querySetCommissionRatio(params);
		model.addAttribute("shop_config", shop_config);
		return "/WEB-INF/dis/addSetCommissionRatio.jsp";
	}
	/**
	 * 修改佣金设置
	 * @param cus_uniques 会员编码，多个以逗号隔开
	 * @param shop_unique 店铺编号
	 * @param dis_level_id 分销商等级id
	 * @return
	 */
	@RemoteLog(title = "修改佣金设置", businessType = BusinessType.UPDATE)
	@RequestMapping("/addSetCommissionRatio.do")
	@ResponseBody
	public PurResult addSetCommissionRatio(Double commission_ratio,String shop_unique){
		return disService.addSetCommissionRatio(commission_ratio, shop_unique);
	}
}
