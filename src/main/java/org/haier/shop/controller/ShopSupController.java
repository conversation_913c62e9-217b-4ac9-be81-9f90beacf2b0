package org.haier.shop.controller;

import org.haier.shop.service.supplier.ShopSupService;
import org.haier.shop.util.PurResult;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Controller
@RequestMapping("/html/shopSup")
public class ShopSupController {
    @Resource
    private ShopSupService shopSupService;

    /**
     * 根据店铺编码查询供货商列表信息
     *
     * @param shopUnique (shopUnique：店铺编号)
     * @return
     */
    @RequestMapping(method = RequestMethod.POST, value = "/querySupplierList.do")
    @ResponseBody
    public PurResult querySupplierList(String shopUnique) {
        Map<String, Object> params = new HashMap<>();
        params.put("shopUnique", shopUnique);
        return shopSupService.querySupplierList(params);
    }

    /**
     * 商品编辑页面添加供货商
     *
     * @param shopUnique：店铺编号
     * @param goodsBarcode：商品条码
     * @param supplierUnique：供货商编号
     * @param createId：操作人ID
     * @param createBy：操作人姓名
     * @return
     */
    @RequestMapping(method = RequestMethod.POST, value = "/addSupGood.do")
    @ResponseBody
    public PurResult addSupGood(String shopUnique, String goodsBarcode, String supplierUnique, String createId, String createBy) {
        Map<String, Object> params = new HashMap<>();
        params.put("shopUnique", shopUnique);
        params.put("goodsBarcode", goodsBarcode);
        params.put("supplierUnique", supplierUnique);
        params.put("createId", createId);
        params.put("createBy", createBy);
        List<String> bindGoodList = new ArrayList<>();
        return shopSupService.addSupGood(params,bindGoodList);
    }

    /**
     * 商品列表页面多选商品批量更换供货商
     *
     * @param shopUnique：店铺编号
     * @param goodsList：商品条码列表
     * @param supplierUnique：供货商编号
     * @param createId：操作人ID
     * @param createBy：操作人姓名
     * @return
     */
    @RequestMapping(method = RequestMethod.POST, value = "/batchAddSupGoods.do")
    @ResponseBody
    public PurResult batchAddSupGoods(String shopUnique, String[] goodsList, String supplierUnique, String createId, String createBy) {
        Map<String, Object> params = new HashMap<>();
        params.put("shopUnique", shopUnique);
        params.put("goodsList", goodsList);
        params.put("supplierUnique", supplierUnique);
        params.put("createId", createId);
        params.put("createBy", createBy);
        return shopSupService.batchAddSupGoods(params);
    }
}
