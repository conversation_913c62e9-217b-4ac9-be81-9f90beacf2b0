package org.haier.shop.controller;

import javax.annotation.Resource;

import org.haier.shop.service.AreaDictService;
import org.haier.shop.util.ShopsResult;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

@Controller
@RequestMapping("/area")
public class AreaDictController {
	@Resource
	private AreaDictService areaDictService;
	
	/**
	 * 查询省市区县信息
	 * @param area_dict_parent_num
	 * @return
	 */
	@ResponseBody
	@RequestMapping("/queryArea.do")
	public ShopsResult queryArea(String area_dict_parent_num){
		return areaDictService.queryArea(area_dict_parent_num);
	}
}
