package org.haier.shop.controller;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;

import org.haier.shop.entity.SqlPc;
import org.haier.shop.service.UpdateService;
import org.haier.shop.util.ShopsResult;
import org.haier.shop.util.UpdateUtil;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

@Controller
@RequestMapping("/update")
public class UpdateController {
	@Resource
	private UpdateService updateService;
	
	@RequestMapping("/queryUpdatePageCount.do")
	@ResponseBody
	public ShopsResult queryUpdatePageCount(
			@RequestParam(value="pageSize",defaultValue="20")Integer pageSize,
			@RequestParam(value="handleStatus",defaultValue="0")Integer handleStatus,
			String shopMessage
			){
		Map<String,Object> map=new HashMap<String, Object>();
		map.put("pageSize", pageSize);
		map.put("examinestatus", UpdateUtil.EXAMINESTATUS);
		map.put("useType", UpdateUtil.OPERATEUSETYPE);
		map.put("operateHandleStatus", UpdateUtil.OPERATEHANDLESTATUS);
		map.put("handleStatus", handleStatus);
		
		if(null!=shopMessage&&!shopMessage.equals("")){
			map.put("shopMessage", "%"+shopMessage+"%");
		}
		return updateService.queryUpdatePageCount(map);
	}

	
	@RequestMapping("/queryUpdateMessageByPage.do")
	@ResponseBody
	public ShopsResult queryUpdateMessageByPage(
			@RequestParam(value="pageSize",defaultValue="20")Integer pageSize,
			@RequestParam(value="handleStatus",defaultValue="-1")Integer handleStatus,
			String shopMessage,
			@RequestParam(value="pageNum",defaultValue="1")Integer pageNum
			){
		Map<String,Object> map=new HashMap<String, Object>();
		map.put("pageSize", pageSize);
		map.put("examinestatus", UpdateUtil.EXAMINESTATUS);
		map.put("useType", UpdateUtil.OPERATEUSETYPE);
		map.put("startNum", (pageNum-1)*pageSize);
		if(null!=handleStatus&&handleStatus!=-1){
			map.put("handleStatus", handleStatus);
		}
		if(null!=shopMessage&&!shopMessage.equals("")){
			map.put("shopMessage", "%"+shopMessage+"%");
		}
		return updateService.queryUpdateMessageByPage(map);
	}
	
	/**
	 * 删除已有操作指令，添加新的操作指令
	 * @return
	 */
	@RequestMapping("/operatingMachine.do")
	@ResponseBody
	public ShopsResult operatingMachine(
			Integer operateType,
			@RequestParam(value="shops")String shops,
			Integer handleStatus
			){
		Map<String,Object> map=new HashMap<String, Object>();
		map.put("operateType", operateType);
		map.put("handleStatus", handleStatus);
		List<Map<String,Object>> list=new ArrayList<Map<String,Object>>();
		String[] shop=shops.split(";;");
		for(String str:shop){
			Map<String,Object> m=new HashMap<String, Object>();
			if(str!=null&&!"".equals(str)){
				m.put("shopUnique", str.split(";")[0]);
				m.put("macId", str.split(";")[1]);
				System.out.println(m);
				list.add(m);
			}
		}
		
		map.put("list", list);
		map.put("operateHandleStatus", UpdateUtil.OPERATEHANDLESTATUS);
		map.put("operateCancelHandleStatus", UpdateUtil.OPERATECANCELHANDLESTATUS);
		System.out.println("命令启动"+map);
		return updateService.operatingMachine(map,operateType);
	}
	
	/**
	 * 设置新版本
	 * @return
	 */
	@RequestMapping("setNewEdition.do")
	@ResponseBody
	public ShopsResult setNewEdition(){
		return updateService.setNewEdition();
	}
	
	/**
	 * 所有店铺的版本信息
	 * @return
	 */
	@RequestMapping("/queryVersionNumberList.do")
	@ResponseBody
	public ShopsResult queryVersionNumberList(){
		return updateService.queryVersionNumberList();
	}
	@RequestMapping("/addSqlPage.do")
	public String addNewSqlHere(){
		return "/WEB-INF/shop/addSqlPage.jsp";
	}
	
	/**
	 * 保存SQL信息
	 * @param sqlPc
	 * @return
	 */
	@RequestMapping("/saveSqlCmd.do")
	@ResponseBody
	public ShopsResult saveSqlCmd(SqlPc sqlPc){
		return updateService.saveSqlCmd(sqlPc);
	}
}
