package org.haier.shop.controller;

import com.alibaba.fastjson.JSON;
import org.apache.shiro.SecurityUtils;
import org.apache.shiro.authc.ExcessiveAttemptsException;
import org.apache.shiro.authc.IncorrectCredentialsException;
import org.apache.shiro.authc.UnknownAccountException;
import org.apache.shiro.authc.UsernamePasswordToken;
import org.apache.shiro.authz.UnauthorizedException;
import org.apache.shiro.authz.annotation.RequiresGuest;
import org.apache.shiro.session.Session;
import org.apache.shiro.subject.Subject;
import org.haier.shop.dao.ShopsConfigDao;
import org.haier.shop.entity.ShopsConfig;
import org.haier.shop.entity.Staff;
import org.haier.shop.util.MD5Utils;
import org.haier.shop.util.PurResult;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

@RestController
@RequestMapping("/aiLogin")
public class AiLoginController {


    @Resource
    private RedisTemplate<String, Object> redisTemplate;

    @Resource
    private ShopsConfigDao shopsConfigDao;


    /**
     * 登录
     *
     * @param staff
     * @param model
     * @return
     */
    @PostMapping(value = "/loginMain.do")
    public PurResult login(Staff staff, Model model) {
        PurResult result = new PurResult();
        Map<String, Object> jsonResult = new HashMap<String, Object>();
        Subject subject = SecurityUtils.getSubject();
        UsernamePasswordToken usernamePasswordToken = new UsernamePasswordToken(staff.getStaff_account(), MD5Utils.getPwd(staff.getStaff_pwd()));
        try {

            subject.login(usernamePasswordToken);
            Session session = subject.getSession();
            //获取用户信息
            staff = (Staff) subject.getPrincipal();
            ShopsConfig shopsConfig = shopsConfigDao.selectByShopUnique(String.valueOf(staff.getShop_unique()));
            session.setAttribute("goods_in_price_type", shopsConfig.getGoodsInPriceType());
            session.setAttribute("staff", staff);
            session.setAttribute("staffJSON", JSON.toJSONString(staff));
            session.setAttribute("login_shop_class", staff.getShop_class());
            session.setAttribute("login_shop_unique", staff.getShop_unique());

            jsonResult.put("shopUnique", shopsConfig.getShopUnique());
            jsonResult.put("staffId", staff.getStaff_id());
            result.setData(jsonResult);
            result.setStatus(PurResult.SUCCESS);
            result.setMsg("登录成功");
        } catch (IncorrectCredentialsException e) {
            result.setStatus(PurResult.FAIL);
            result.setMsg("密码错误");
        } catch (ExcessiveAttemptsException e) {
            result.setStatus(PurResult.FAIL);
            result.setMsg("失败次数过多");
        } catch (UnknownAccountException e) {
            result.setStatus(PurResult.FAIL);
            result.setMsg("账号不存在");
        } catch (UnauthorizedException e) {
            result.setStatus(PurResult.FAIL);
            result.setMsg("您没有得到相应的授权");
        } catch (Exception e) {
            result.setStatus(PurResult.FAIL);
            result.setMsg("登录异常");
        }

        return result;
    }

    @GetMapping("/checkLogin.do")
    public PurResult checkLogin(@RequestParam String phone) {
        Subject subject = SecurityUtils.getSubject();

        // 方式1：通过Shiro的Session验证（需确保Session未禁用）
        if (subject.isAuthenticated()) {
            Staff staff = (Staff) subject.getPrincipal();
            if (phone.equals(staff.getStaff_account())) {
                return PurResult.ok();
            }
        }

        // 方式2：直接查询Shiro的内存Session（更可靠）
        Session session = subject.getSession(false);
        if (session != null) {
            Staff staff = (Staff) session.getAttribute("staff");
            if (staff != null && phone.equals(staff.getStaff_account())) {
                return PurResult.ok();
            }
        }

        return PurResult.fail("登录失效");
    }


    /**
     * 登录
     *
     * @return
     */
    @PostMapping(value = "/loginOut.do")
    @RequiresGuest
    public PurResult logout() {
        Subject subject = SecurityUtils.getSubject();
        subject.logout();
        return PurResult.ok("退出成功");
    }
}