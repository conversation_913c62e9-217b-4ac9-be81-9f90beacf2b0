package org.haier.shop.controller;

import java.io.IOException;
import java.io.InputStream;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;

import org.apache.http.client.ClientProtocolException;
import org.apache.shiro.SecurityUtils;
import org.apache.shiro.session.Session;
import org.apache.shiro.subject.Subject;
import org.haier.shop.entity.Staff;
import org.haier.shop.entity.beans.PageData;
import org.haier.shop.entity.globalSelect.FarmDetailVO;
import org.haier.shop.redis.RedisCache;
import org.haier.shop.service.FarmProductService;
import org.haier.shop.service.GlobalThemeService;
import org.haier.shop.service.GoodsService;
import org.haier.shop.service.WeishiProductService;
import org.haier.shop.util.AccessToken;
import org.haier.shop.util.FTPConfig;
import org.haier.shop.util.MUtil;
import org.haier.shop.util.PicSaveUtil;
import org.haier.shop.util.PurResult;
import org.haier.shop.util.SFTPUtil;
import org.haier.shop.util.ServletsUtil;
import org.haier.shop.util.ShopsResult;
import org.haier.shop.util.ShopsUtil;
import org.haier.shop.util.UUIDUtil;
import org.haier.shop.util.WeiXinUtil;
import org.haier.shop.util.weishi.ProductInfo;
import org.haier.shop.util.wxPay.PayConfigUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.multipart.MultipartFile;

import com.alibaba.fastjson.JSON;

import net.sf.json.JSONArray;
import net.sf.json.JSONObject;
import net.sf.json.JsonConfig;

/**
 * 腾讯微视频
 * <AUTHOR>
 *
 *2022年5月17日
 */
@Controller
@RequestMapping("/weishi")
public class WeiShiPingController {

	@Autowired
	private GlobalThemeService globalService;
	@Autowired
	private GoodsService goodsService;
	@Resource
	private FarmProductService farmService;
	@Resource
	private WeishiProductService weishiProductService;
	@Autowired
	private RedisCache rc;
	
	//跳转到-产品管理
	@RequestMapping("/toProductsPage.do")
	public String toProductsPage(){
		return "/WEB-INF/farm/wechatProducts.jsp";
	}
	//跳转到-订单管理
	@RequestMapping("/toOrderPage.do")
	public String toOrderPage(){
		return "/WEB-INF/farm/wechatOrder.jsp";
	}
	//跳转到-平台审核
	@RequestMapping("/toWechatProducts_pt.do")
	public String wechatProducts_pt(){
		return "/WEB-INF/farm/wechatProducts_pt.jsp";
	}
	/**
	 * 微视频商品列表
	 */
	@RequestMapping("/queryWechatGoodsList.do")
	@ResponseBody
	public PurResult queryGlobalSupplierList(

			@RequestParam(value="status",defaultValue="-1")int status,
			@RequestParam(value = "page", defaultValue = "1") int page,
			@RequestParam(value = "limit", defaultValue = "10") int pageSize,
			String kind_id
			){
		PurResult result=new PurResult();
		try {
			
		//获取token
		Object resmap=rc.getObject("yikezhong_access_token");
		String access_token;
		if(resmap == null) {
				AccessToken accessToken;
				 accessToken = WeiXinUtil.getAccessToken();
				  access_token=accessToken.getToken();
//				Integer expiresIn=accessToken.getExpiresIn();
				Object o = access_token;
				rc.putObject("yikezhong_access_token", o, 100);
		}else
		{
			access_token=resmap.toString();
		}
		Map<String,Object> map=new HashMap<String, Object>();
		if(status!=-1)
		{
			map.put("status", status);
		}

		map.put("page", page);
		map.put("page_size", pageSize);

		//
		JSONObject resultData=WeiXinUtil.doPostStr(PayConfigUtil.WXP_SUP_LIST+"?access_token="+access_token,JSON.toJSONString(map));
		
		if(resultData.get("errcode").equals(0))
		{
			result.setStatus(1);
			result.setData(resultData.get("spus"));
			result.setCount((int)resultData.get("total_num"));
		}
		
		} catch (ClientProtocolException e) {
			e.printStackTrace();
			result.setStatus(0);
			result.setMsg(e.getMessage());
		} catch (IOException e) {
			e.printStackTrace();
			result.setStatus(0);
			result.setMsg(e.getMessage());
		}
		
		return result;
	}
	
	/**
	 * 微视频订单列表
	 */
	@RequestMapping("/queryWechatOrderList.do")
	@ResponseBody
	public PurResult queryWechatOrderList(

			@RequestParam(value="status",defaultValue="-1")int status,
			@RequestParam(value = "page", defaultValue = "1") int page,
			@RequestParam(value = "limit", defaultValue = "10") int pageSize,
			@RequestParam(value = "startTime", defaultValue = "") String startTime,
			@RequestParam(value = "endTime", defaultValue = "") String endTime,
			String kind_id
			){
		PurResult result=new PurResult();
		try {
			
		//获取token
		Object resmap=rc.getObject("yikezhong_access_token");
		String access_token;
		if(resmap == null) {
				AccessToken accessToken;
				 accessToken = WeiXinUtil.getAccessToken();
				  access_token=accessToken.getToken();
//				Integer expiresIn=accessToken.getExpiresIn();
				Object o = access_token;
				rc.putObject("yikezhong_access_token", o, 100);
		}else
		{
			access_token=resmap.toString();
		}
		Map<String,Object> map=new HashMap<String, Object>();
		if(status!=-1)
		{
			map.put("sort_order", status);
		}
		map.put("page", page);
		map.put("page_size", pageSize);
		map.put("start_create_time", startTime);
		map.put("end_create_time", endTime);

		//
		JSONObject resultData=WeiXinUtil.doPostStr(PayConfigUtil.WXP_ORDER_LIST+"?access_token="+access_token,JSON.toJSONString(map));
		
		if(resultData.get("errcode").equals(0))
		{
			result.setStatus(1);
			result.setData(resultData.get("orders"));
			result.setCount((int)resultData.get("total_num"));
		}
		
		} catch (ClientProtocolException e) {
			e.printStackTrace();
			result.setStatus(0);
			result.setMsg(e.getMessage());
		} catch (IOException e) {
			e.printStackTrace();
			result.setStatus(0);
			result.setMsg(e.getMessage());
		}
		
		return result;
	}
	//跳转到-产品添加
	@RequestMapping("/toAddWechatProducts.do")
	public String toAddProducts(Model model){
		Map<String,Object> params=new HashMap<String,Object>();
    	params.put("id", null);
    	//获取有商家的区域列表
    	List<Map<String ,Object>> areaList = globalService.getFarmAreaList(params); 
    	//获取有商家的城市列表
    	List<Map<String ,Object>> cityList = globalService.getCityList(areaList);
    	//获取有商家的省份列表
    	List<Map<String ,Object>> provinceList = globalService.getProvinceList(cityList);
		//获取所有审核通过店铺列表
		List<Map<String ,Object>> shopList = goodsService.getAllShopList();
		model.addAttribute("shopList", shopList);
    	model.addAttribute("areaList", areaList);
    	model.addAttribute("cityList", cityList);
    	model.addAttribute("provinceList", provinceList);
		return "/WEB-INF/farm/wechatAddProducts.jsp";
	}
	
	/**
	 * 添加 微视频商品产品
	 */

	@RequestMapping("/addProduct.do")
	@ResponseBody
	public PurResult addProduct(HttpServletRequest request){
		PurResult rs = new PurResult();
		try {
			Map<String, Object> params = ServletsUtil.getParameters(request);
			System.out.println(params);
			Subject subject = SecurityUtils.getSubject();
			Session session = subject.getSession();
			Staff staff = (Staff) session.getAttribute("staff");
			if(staff.getShop_type()==4)
			{
				params.put("audit_status", 1);
			}else
			{
				params.put("audit_status", 0);
			}
			params.put("secretary_id",-1);
			
			//生产条码
			String barcode ="N"+System.currentTimeMillis()/ 1000+(int)((Math.random()*9+1)*1000); //10位数的时间戳+4位随机数
			params.put("barcode", barcode);

			//上传商品图片
			SFTPUtil sftp = new SFTPUtil(FTPConfig.username, FTPConfig.password, FTPConfig.host, FTPConfig.port);   
	        sftp.login(); 
	        String goods_image=uploadWeishiFile(request,sftp,"goodsPicturePath1",params);
			if(goods_image!=null)
			{
				params.put("goods_image", goods_image);
			}
			String detail_image=uploadWeishiFile(request,sftp,"goodsPicturePath2",params);
			if(detail_image!=null)
			{
				params.put("detail_image", detail_image);
			}
			String goods_video=uploadWeishiFile(request,sftp,"goodsPicturePathVideo",params);
			if(detail_image!=null)
			{
				params.put("goods_video", goods_video);
			}

			JSONArray peopleJson=JSONArray.fromObject(params.get("peopleJson"));
			@SuppressWarnings("unchecked")
			List<PageData> peopleList = JSONArray.toList(peopleJson, new PageData(), new JsonConfig());//农户
			
			for(PageData p:peopleList)
			{
				String aptitudes_imgages=uploadWeishiFile(request,sftp,p.get("aptitudes_imgages").toString(),params);

				params.put("aptitudes_imgages", aptitudes_imgages);
	
				String aptitudes_imgages2=uploadWeishiFile(request,sftp,p.get("aptitudes_imgages2").toString(),params);
		
				params.put("aptitudes_imgages2", aptitudes_imgages2);
	
				String aptitudes_imgages3=uploadWeishiFile(request,sftp,p.get("aptitudes_imgages3").toString(),params);

				params.put("aptitudes_imgages3", aptitudes_imgages3);

			}
	        sftp.logout(); 
			rs=weishiProductService.addProduct(params,request,peopleList);
		} catch (Exception e) {
			rs.setStatus(0);
			rs.setMsg("操作失败!");
			e.printStackTrace();
		}		
		return rs;
	}
	
	/**
	 * 跳转到详情界面
	 */
	@RequestMapping("/toDetail.do")
	public String toFarmDetail(Model model,String id){

		Map<String,Object> params=new HashMap<String,Object>();
    	params.put("id", id);
    	params.put("farm_product_id", id);
    	//获取有商家的区域列表
    	List<Map<String ,Object>> areaList = globalService.getFarmAreaList(params); 
    	//获取有商家的城市列表
    	List<Map<String ,Object>> cityList = globalService.getCityList(areaList);
    	//获取有商家的省份列表
    	List<Map<String ,Object>> provinceList = globalService.getProvinceList(cityList); 
    	
    	//审核记录
    	List<Map<String ,Object>> auditList = globalService.getFarmAuditList(params); 
    	for(Map<String ,Object> city:cityList)
    	{
    		int num=0;
    		for(Map<String ,Object> area:areaList)
        	{
    			if(area.get("city_code").equals(city.get("city_code")))
    			{
    				
    				if(area.get("flag").equals("false"))
    				{
    					num=num+1;
    				}
    			}
        		
        	}
    		if(num==0)
    		{
    			city.put("flag", "true");
    		}else
    		{
    			city.put("flag", "false");
    		}
    		
    	}
    	
    	
    	FarmDetailVO data=globalService.queryFarmDetail(params);
		//获取token
		String access_token=getAccess_token();
		
		weishiProductService.queryAuditProduct(data,params,access_token);
		
		
    	model.addAttribute("areaList", areaList);
    	model.addAttribute("cityList", cityList);
    	model.addAttribute("provinceList", provinceList);
    	model.addAttribute("auditList", auditList);
    	model.addAttribute("id", id);
    	model.addAttribute("data", data);
			
		return "/WEB-INF/farm/weishiDetail_pt.jsp";
	}
	

	/**
	 * 主题审核
	 */
	@RequestMapping("/auditProduct.do")
	@ResponseBody
	public PurResult auditFarmProduct(HttpServletRequest request){
		PurResult rs = new PurResult();
		try {
			//获取token
			Object resmap=rc.getObject("yikezhong_access_token");
			String access_token;
			if(resmap == null) {
					AccessToken accessToken;
					 accessToken = WeiXinUtil.getAccessToken();
					  access_token=accessToken.getToken();
//					Integer expiresIn=accessToken.getExpiresIn();
					Object o = access_token;
					rc.putObject("yikezhong_access_token", o, 100);
			}else
			{
				access_token=resmap.toString();
			}
			Map<String, Object> params = ServletsUtil.getParameters(request);
			rs=weishiProductService.auditProduct(params,access_token);
		} catch (Exception e) {
			rs.setStatus(0);
			rs.setMsg("操作失败!");
			e.printStackTrace();
		}		
		return rs;
	}
	
	/**
	 * 产品列表
	 */
	@RequestMapping("/queryProductsList.do")
	@ResponseBody
	public PurResult queryGlobalSupplierList(
			@RequestParam(value="content",defaultValue="")String content,
			@RequestParam(value="start_time",defaultValue="")String start_time,
			@RequestParam(value="end_time",defaultValue="")String end_time,
			@RequestParam(value="audit_status",defaultValue="-1")int audit_status,
			@RequestParam(value="shelf_status",defaultValue="-1")int shelf_status,
			@RequestParam(value="sup_shelf_status",defaultValue="-1")int sup_shelf_status,
			@RequestParam(value="shop_unique",defaultValue="")Long shop_unique,
			@RequestParam(value="shop_unique2",defaultValue="")String shop_unique2,
			@RequestParam(value="active_status",defaultValue="-1")int active_status,
			@RequestParam(value = "page", defaultValue = "1") int page,
			@RequestParam(value = "limit", defaultValue = "10") int pageSize,
			@RequestParam(value = "is_wechat", defaultValue = "1") int is_wechat,
			String kind_id
			){

		Map<String,Object> map=new HashMap<String, Object>();
		map.put("shop_unique", shop_unique);
		map.put("shop_unique2", shop_unique2);
		map.put("pageSize", pageSize);
		map.put("startNum", (page-1)*pageSize);
		map.put("content", content);
		map.put("start_time", start_time);
		map.put("end_time", end_time);
		map.put("audit_status", audit_status);
		map.put("shelf_status", shelf_status);
		map.put("sup_shelf_status", sup_shelf_status);
		map.put("active_status", active_status);
		map.put("is_wechat", is_wechat);
		String temp_str="";   
	    Date dt = new Date();   
	    SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");   
	    temp_str=sdf.format(dt);   
		
		map.put("temp_str", temp_str);
		if(kind_id!=null&&!"".equals(kind_id)){
			map.put("kind_id", kind_id);
		}
		return weishiProductService.queryProductsList(map);
	}
	/**
	 * 跳转到 编辑商品页面
	 */
	@RequestMapping("/toEditProducts.do")
	public String toEditProducts(Model model,String id){
		Map<String,Object> params=new HashMap<String,Object>();
    	params.put("id", id);
    	params.put("farm_product_id", id);
    	//获取有商家的区域列表
    	List<Map<String ,Object>> areaList = globalService.getFarmAreaList(params); 
    	//获取有商家的城市列表
    	List<Map<String ,Object>> cityList = globalService.getCityList(areaList);
    	//获取有商家的省份列表
    	List<Map<String ,Object>> provinceList = globalService.getProvinceList(cityList); 
    	//审核记录
    	List<Map<String ,Object>> auditList = globalService.getFarmAuditList(params); 
    	for(Map<String ,Object> city:cityList)
    	{
    		int num=0;
    		for(Map<String ,Object> area:areaList)
        	{
    			if(area.get("city_code").equals(city.get("city_code")))
    			{
    				
    				if(area.get("flag").equals("false"))
    				{
    					num=num+1;
    				}
    			}
        		
        	}
    		if(num==0)
    		{
    			city.put("flag", "true");
    		}else
    		{
    			city.put("flag", "false");
    		}
    		
    	}
    	
    	
    	FarmDetailVO data=globalService.queryFarmDetail(params);
    	model.addAttribute("areaList", areaList);
    	model.addAttribute("cityList", cityList);
    	model.addAttribute("provinceList", provinceList);
    	model.addAttribute("auditList", auditList);
    	model.addAttribute("id", id);
    	model.addAttribute("data", data);
    	
		return "/WEB-INF/farm/wechatEditProducts.jsp";
	}
	public String getAccess_token()
	{
		Object resmap=rc.getObject("yikezhong_access_token");
		String access_token=null;
		 try {
				if(resmap == null) {
						AccessToken accessToken;
						
							accessToken = WeiXinUtil.getAccessToken();
						
						  access_token=accessToken.getToken();
//						Integer expiresIn=accessToken.getExpiresIn();
						Object o = access_token;
						rc.putObject("yikezhong_access_token", o, 100);
				}else
				{
					access_token=resmap.toString();
				}
		 } catch (ClientProtocolException e) {
				// TODO Auto-generated catch block
				e.printStackTrace();
			} catch (IOException e) {
				// TODO Auto-generated catch block
				e.printStackTrace();
			}
		 
		 return access_token;
	}
	
	/**
	 * 修改产品
	 */

	@RequestMapping("/updateProduct.do")
	@ResponseBody
	public PurResult updateProduct(HttpServletRequest request){
		PurResult rs = new PurResult();
		try {
			Map<String, Object> params = ServletsUtil.getParameters(request);
			Subject subject = SecurityUtils.getSubject();
			Session session = subject.getSession();
			Staff staff = (Staff) session.getAttribute("staff");
			if(staff.getShop_type()==4)
			{
				
				params.put("audit_status", 1);
			}else
			{
				params.put("audit_status", 0);
			}
			SFTPUtil sftp = new SFTPUtil(FTPConfig.username, FTPConfig.password, FTPConfig.host, FTPConfig.port);   
	        sftp.login();
			//微视商城图片
			String goods_image=uploadWeishiFile(request,sftp,"goodsPicturePath1",params);
			if(goods_image!=null)
			{
				params.put("goods_image", goods_image);
			}
			String detail_image=uploadWeishiFile(request,sftp,"goodsPicturePath2",params);
			if(detail_image!=null)
			{
				params.put("detail_image", detail_image);
			}
			String goods_video=uploadWeishiFile(request,sftp,"goodsPicturePathVideo",params);
			if(detail_image!=null)
			{
				params.put("goods_video", goods_video);
			}
				
			JSONArray peopleJson=JSONArray.fromObject(params.get("peopleJson"));
			@SuppressWarnings("unchecked")
			List<PageData> peopleList = JSONArray.toList(peopleJson, new PageData(), new JsonConfig());//农户
			for(PageData p:peopleList)
			{
				String aptitudes_imgages=uploadWeishiFile(request,sftp,p.get("aptitudes_imgages").toString(),params);
				if(aptitudes_imgages!=null)
				{
					params.put("aptitudes_imgages", aptitudes_imgages);
				}
				String aptitudes_imgages2=uploadWeishiFile(request,sftp,p.get("aptitudes_imgages2").toString(),params);
				if(aptitudes_imgages2!=null)
				{
					params.put("aptitudes_imgages2", aptitudes_imgages2);
				}
				String aptitudes_imgages3=uploadWeishiFile(request,sftp,p.get("aptitudes_imgages3").toString(),params);
				if(aptitudes_imgages3!=null)
				{
					params.put("aptitudes_imgages3", aptitudes_imgages3);
				}
			}
			sftp.logout(); 
			rs=weishiProductService.updateWeishiProduct(params,request);
		} catch (Exception e) {
			rs.setStatus(0);
			rs.setMsg("操作失败!");
			e.printStackTrace();
		}		
		return rs;
	}
	/**
	 * 
	 * @param request
	 * @param string
	 * @return
	 */
	private String uploadWeishiFile(HttpServletRequest request,SFTPUtil sftp, String string,Map<String, Object> params) {
		String url=null;
		try {
		MultipartFile file=null;
		//上传商品图片
		 
		file=ShopsUtil.testMulRequest(request, string);
		if(file!=null){
			InputStream is= file.getInputStream();
			 
			String orName=file.getOriginalFilename();//获取文件原名称
			String lastName=orName.substring(orName.lastIndexOf("."));
			String newName=UUIDUtil.getUUID32()+lastName;
			
			
			String filePathDetail="/"+"image"+"/"+params.get("shop_unique");
	        boolean flag=sftp.upload(FTPConfig.goods_path+"/"+params.get("shop_unique"), newName, is);   
			if(flag){
				
				Map<String,String> map=new HashMap<String, String>();
				map.put("resp_type", "1");
				map.put("upload_type", "1");
				map.put("img_url", "https://file.buyhoo.cc"+filePathDetail+"/"+newName);
				System.out.println(map.get("img_url"));
				
				String result=PicSaveUtil.formUpload(PayConfigUtil.WXP_IMG_UPDATE+"?access_token="+getAccess_token(),map,null,null);
				JSONObject resultData=JSONObject.fromObject(result);
				System.out.println(result);
				if(resultData.get("errcode").equals(0))
				{
					 JSONObject data2 = JSONObject.fromObject(resultData.get("img_info"));
					url=data2.get("temp_img_url").toString();
				}
				
			}
		}
		} catch (Exception e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		}  
		return url;
	}

	/**
	 * 上下架
	 */
	@RequestMapping("/updateStatus.do")
	@ResponseBody
	public PurResult updateFarmStatus(
			@RequestParam(value="id",defaultValue="")int id,
			@RequestParam(value="shelf_status",defaultValue="")int shelf_status,
			@RequestParam(value="good_status",defaultValue="1")int good_status,
			@RequestParam(value="barcode",defaultValue="")String barcode
			){
		PurResult rs = new PurResult();
		try {
			//获取token
			String access_token=getAccess_token();
		rs=weishiProductService.updateStatus(id,shelf_status,good_status,barcode,access_token);
		} catch (Exception e) {
			rs.setStatus(0);
			rs.setMsg("操作失败!");
			e.printStackTrace();
		}
		return rs;
	}
	/**
	 * 订单详情
	 * @param sup_order_unique
	 * @param request
	 * @return
	 */
	@RequestMapping("/queryOrderDetailPage.do")
	public String queryFarmOrderDetailPage(String out_order_id,HttpServletRequest request){
		request.setAttribute("out_order_id", out_order_id);
		return "/WEB-INF/farm/weishiOrderDetail.jsp";
	}
	/**
	 * 查询订单详情
	 * @param shop_unique
	 * @param sale_list_unique
	 * @return
	 */
	@RequestMapping("/queryOrderDetail.do")
	@ResponseBody
	public ShopsResult queryOrderDetail(
			@RequestParam(value="out_order_id")String out_order_id){
		return weishiProductService.queryOrderDetail(out_order_id);
	}
	/**
	 *发货
	 * @param sup_order_unique
	 * @param request
	 * @return
	 */
	@RequestMapping("/queryOrderDeliveryPage.do")
	public String queryOrderDeliveryPage(String out_order_id,Long order_id,String openid,HttpServletRequest request){
		request.setAttribute("out_order_id", out_order_id);
		request.setAttribute("order_id", order_id);
		request.setAttribute("openid", openid);
		return "/WEB-INF/farm/weishiDelivery.jsp";
	}
	/**
	 *发货
	 * @param sup_order_unique
	 * @param request
	 * @return
	 */
	@RequestMapping("/addOrderDelivery.do")
	@ResponseBody
	public PurResult addOrderDelivery(String out_order_id,Long order_id,String delivery_id,String waybill_id,String openid,HttpServletRequest request){
		PurResult rs = new PurResult();
		try {
			//查询订单详情
			Map<String,Object> map=new HashMap<String, Object>();
			map.put("out_order_id", out_order_id);
			map.put("openid", openid);
		
			JSONObject resultData;
		
			resultData = WeiXinUtil.doPostStr(PayConfigUtil.WXP_ORDER_GET+"?access_token="+getAccess_token(),JSON.toJSONString(map));
			System.out.println("access_token="+getAccess_token());
			if(resultData.get("errcode").equals(0))
			{
				 Map<String, Object> order=MUtil.jsonToMap(resultData.get("order").toString());
				JSONObject data2 = JSONObject.fromObject(order.get("order_detail"));
//				OrderDetail orderDetail =(OrderDetail) JSONObject.toBean(data2,OrderDetail.class);
				@SuppressWarnings("unchecked")
				List<ProductInfo> info = JSONArray.toList(data2.getJSONArray("product_infos"), new ProductInfo(), new JsonConfig());//多规格
				rs=weishiProductService.addOrderDelivery(out_order_id,order_id,delivery_id,waybill_id,openid,getAccess_token(),info,request);
			}
			
			
		} catch (Exception e) {
			rs.setStatus(0);
			rs.setMsg("操作失败!");
			e.printStackTrace();
		}		
		return rs;
	}
	/**
	  *收货
	 * @param sup_order_unique
	 * @param request
	 * @return
	 */
	@RequestMapping("/deliveryRecieve.do")
	@ResponseBody
	public PurResult deliveryRecieve(String out_order_id,String openid,HttpServletRequest request){
		PurResult rs = new PurResult();
		try {
			//查询订单详情
			Map<String,Object> map=new HashMap<String, Object>();
			map.put("out_order_id", out_order_id);
			map.put("openid", openid);

		    rs=weishiProductService.deliveryRecieve(map,getAccess_token());
			
			
		} catch (Exception e) {
			rs.setStatus(0);
			rs.setMsg("操作失败!");
			e.printStackTrace();
		}		
		return rs;
	}
}
