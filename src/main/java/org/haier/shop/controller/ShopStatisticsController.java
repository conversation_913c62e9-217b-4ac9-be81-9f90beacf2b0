package org.haier.shop.controller;

import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.io.OutputStream;
import java.net.URLEncoder;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.haier.shop.entity.MianMiDetail;
import org.haier.shop.entity.MianMiMain;
import org.haier.shop.service.ShopStatisticsService;
import org.haier.shop.util.ExcelUtil;
import org.haier.shop.util.HandleMessyCode;
import org.haier.shop.util.PurResult;
import org.haier.shop.util.ServletsUtil;
import org.haier.shop.util.ShopsResult;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
@Controller
@RequestMapping("/statistics")
public class ShopStatisticsController {
	@Resource
	private ShopStatisticsService service;
	
	/**
	 * 根据日期查询各种支付方式的支付金额
	 * @param map
	 * @return
	 */
	@RequestMapping("/queryPayMethodByDay.do")
	@ResponseBody
	public PurResult queryPayMethodByDay(String startTime,String endTime){
		PurResult result = new PurResult();
		try {
			Map<String,Object> params=new HashMap<String, Object>();
			params.put("startTime", startTime);
			params.put("endTime", endTime);
			result=service.queryPayMethodByDay(params);
		} catch (Exception e) {
			result.setStatus(0);
			result.setMsg("查询失败!");
			e.printStackTrace();
		}		
		return result;
	}
	
	@RequestMapping("/toPayMethodStatistics.do")
	public String toPayMethodStatistics(){
		return "/WEB-INF/shop/payMethodStatistics.jsp";
	}
	
	@RequestMapping("/StatisticsMsgPage.do")
	public String StatisticsMsgPage(){
		return "/WEB-INF/beans/StatisticstMsgPage.jsp";
	}
	@RequestMapping("/toPayMethodDetail.do")
	public String payMethodDetail(){
		return "/WEB-INF/shop/payMethodDetail.jsp";
	}
	
	@RequestMapping("/payMethodDetailPages.do")
	@ResponseBody
	public ShopsResult payMethodDetailPages(
			String shopMessage,
			String startTime,
			String endTime,
			@RequestParam(value="pageNum",defaultValue="1")Integer pageNum,
			@RequestParam(value="pageSize",defaultValue="8")Integer pageSize,
			@RequestParam(value="payCode",defaultValue="9")Integer payCode
			){
		Map<String,Object> map=new HashMap<String,Object>();
		if(null!=shopMessage&&!shopMessage.trim().equals("")){
			map.put("shopMessage", "%"+shopMessage+"%");
		}
		map.put("startTime", startTime);
		map.put("endTime", endTime);
		map.put("pageSize", pageSize);
		map.put("payCode", payCode);
		map.put("startNum", (pageNum-1)*pageSize);
		return service.payMethodDetailPages(map);
	}
	
	@RequestMapping("/payMethodDetail.do")
	@ResponseBody
	public ShopsResult payMethodDetail(
			String shopMessage,
			String startTime,
			String endTime,
			@RequestParam(value="pageNum",defaultValue="1")Integer pageNum,
			@RequestParam(value="pageSize",defaultValue="8")Integer pageSize,
			@RequestParam(value="payCode",defaultValue="9")Integer payCode,
			@RequestParam(value="order",defaultValue="1")Integer order,
			@RequestParam(value="orderType",defaultValue="1")Integer orderType
			){
		Map<String,Object> map=new HashMap<String,Object>();
		if(null!=shopMessage&&!shopMessage.trim().equals("")){
			map.put("shopMessage", "%"+shopMessage+"%");
		}
		map.put("startTime", startTime);
		map.put("endTime", endTime);
		map.put("pageSize", pageSize);
		map.put("payCode", payCode);
		map.put("startNum", (pageNum-1)*pageSize);
		if(order==1){
			map.put("order", "payMoney");
		}
		if(orderType==1){
			map.put("orderType", "DESC");
		}
		return service.payMethodDetail(map);
	}
	
	
	/**
	 * 跳转免密支付详情界面
	 * @return
	 */
	@RequestMapping("/toMianmiDetail.do")
	public String toMianmiDetail(){
		return "/WEB-INF/shop/mianmiDetail.jsp";
	}
	
	/**
	 * 查询已开通免密支付的店铺数量
	 * @param map
	 * @return
	 */
	@RequestMapping("/mianmiDetailShopsListPages.do")
	@ResponseBody
	public ShopsResult mianmiDetailShopsListPages(
			@RequestParam(value="pageSize",defaultValue="8")Integer pageSize,
			String startTime,
			String endTime,
			String shopMessage,
			Integer mianmiStatus
			){
		Map<String,Object> map=new HashMap<String,Object>();
		map.put("startTime", startTime);
		map.put("endTime", endTime);
		map.put("pageSize", pageSize);
		map.put("mianmiStatus", mianmiStatus);
		if(null!=shopMessage&&!shopMessage.trim().equals("")){
			map.put("shopMessage", "%"+shopMessage+"%");
		}
		return service.mianmiDetailShopsListPages(map);
	}
	
	/**
	 * 查询符合条件的店铺信息
	 * @param map
	 * @return
	 */
	@RequestMapping("/mianmiDetailShopsList.do")
	@ResponseBody
	public ShopsResult mianmiDetailShopsList(
			String startTime,
			String endTime,
			String shopMessage,
			Integer mianmiStatus,
			@RequestParam(value="pageSize",defaultValue="8")Integer pageSize,
			@RequestParam(value="order",defaultValue="1")Integer order,
			@RequestParam(value="orderType",defaultValue="1")Integer orderType,
			@RequestParam(value="pageNum",defaultValue="1")Integer pageNum
			){
		Map<String,Object> map=new HashMap<String,Object>();
		map.put("startTime", startTime);
		map.put("endTime", endTime);
		map.put("pageSize", pageSize);
		map.put("mianmiStatus", mianmiStatus);
		if(null!=shopMessage&&!shopMessage.trim().equals("")){
			map.put("shopMessage", "%"+shopMessage+"%");
		}
		map.put("startNum", (pageNum-1)*pageSize);
		if(order==1){
			map.put("order", "mianmiApplyDatetime");
		}
		if(orderType==1){
			map.put("orderType", "DESC");
		}
		return service.mianmiDetailShopsList(map);
	}
	
	
	@RequestMapping("/toOneMonthTrend.do")
	public String toOneMonthTrend(){
		return "/WEB-INF/shop/toOneMonthTrend.jsp";
	}
	
	@RequestMapping("/toShopSaleListDetail.do")
	public String toShopSaleListDetail(){
		return "/WEB-INF/shop/shopSaleListDetail.jsp";
	}
	
	/**
	 * 店铺销售订单列表页面
	 * 周期内查询店铺订单信息列表统计
	 * @param map
	 * @return
	 */
	@RequestMapping("getShopListPageMsg.do")
	@ResponseBody
	public ShopsResult getShopListPageMsg(String shopUnique,String startTime,String endTime,Integer pageSize){
		Map<String,Object> map=new HashMap<String,Object>();
		map.put("startTime", startTime);
		map.put("endTime", endTime);
		map.put("shopUnique", shopUnique);
		map.put("pageSize", pageSize);
		return service.getShopListPageMsg(map);
	}
	
	
	/**
	 * 店铺销售列表查询
	 */
	@RequestMapping("getShopListByPage.do")
	@ResponseBody
	public ShopsResult getShopListByPage(String shopUnique,String startTime,String endTime,Integer pageSize,Integer pageNum){
		Map<String,Object> map=new HashMap<String,Object>();
		map.put("startTime", startTime);
		map.put("endTime", endTime);
		map.put("shopUnique", shopUnique);
		map.put("pageSize", pageSize);
		map.put("startNum", (pageNum-1)*pageSize);
		return service.getShopListByPage(map);
	}
	
	/**
	 * 获取订单详情
	 * @param map
	 * @return
	 */
	@RequestMapping("/showListDetail.do")
	@ResponseBody
	public ShopsResult showListDetail(String saleListUnique){
		Map<String,Object> map=new HashMap<String,Object>();
		map.put("saleListUnique", saleListUnique);
		return service.showListDetail(map);
	}
	
	/**
	 * 更新店铺免密支付状态
	 * @param shopUnique
	 * @param mianmiStatus
	 * @param mianmiRemarks
	 * @return
	 */
	@ResponseBody
	@RequestMapping("/updateShopMianmiStatus.do")
	public ShopsResult updateShopMianmiStatus(
			String shopUnique,
			Integer mianmiStatus,
			String mianmiRemarks
			){
		Map<String,Object> map=new HashMap<String,Object>();
		map.put("shopUnique", shopUnique);
		map.put("mianmiStatus", mianmiStatus);
		map.put("mianmiRemarks", mianmiRemarks);
		System.out.println(map);
		return service.updateShopMianmiStatus(map);
	}
	
//	/**
//	 * 免密支付EXCEL下载
//	 * @param map
//	 * @return
//	 */
//	@RequestMapping("/downExcel.do")
//	@ResponseBody
//	public ShopsResult downExcel(String startTime,String endTime){
//		Map<String,Object> map=new HashMap<String,Object>()
//		map.put("startTime", startTime);
//		map.put("endTime", endTime);
//		//根据获取的数据，返回EXCEL表
//		
//		return service.downExcel(map);
//	}
	@RequestMapping("/downExcelForDown.do")
	public void downExcel(String startTime,String endTime,Integer type,
			HttpServletRequest request,HttpServletResponse response
			){
		System.out.println(startTime);
		System.out.println(endTime);
		System.out.println(type);
		response.reset();
		response.setHeader("Connection", "close");
		response.setHeader("Content-Type", "application/octet-stream");
		Map<String,Object> map=new HashMap<String,Object>();
		map.put("startTime", startTime);
		map.put("endTime", endTime);
		map.put("type", type);
		List<MianMiMain> list=service.downExcel(map);
		@SuppressWarnings("deprecation")
		String filePath=request.getRealPath("/");
		File file=new File(filePath+File.separator+"mianmi.xls");
		if(list==null||list.isEmpty()){
		}else{
			List<MianMiDetail> dl=list.get(0).getList();
			List<String> tl=new ArrayList<>();
			tl.add("日期/店铺");
			for(int i=0;i<dl.size();i++){
				tl.add(dl.get(i).getDate());
			}
			ExcelUtil.buildExcel(list, file, tl,type);
		}
		
		OutputStream os=null;
		FileInputStream fis =null;
		int bytesRead = 0;
		byte[] buffer = new byte[8192];
		try {
			if(!file.exists()){
				file.createNewFile();
			}
			response.setHeader("Content-Disposition", "attachment;filename="+URLEncoder.encode("mianmi.xls", "UTF-8"));
			fis=new FileInputStream(file);
			os=response.getOutputStream();
			while ((bytesRead = fis.read(buffer, 0, 8192)) != -1) {
				os.write(buffer, 0, bytesRead);
			};
			os.flush();
		} catch (IOException e) {
			e.printStackTrace();
		}finally{
			try {
				if(os!=null){
					os.close();
				}
				if(fis!=null){
					fis.close();
				}
			} catch (IOException e) {
				e.printStackTrace();
			}
		}
	}
}
