package org.haier.shop.controller;

import org.haier.shop.util.SmsMessageUtil;

import net.sf.json.JSONObject;

public class SmsThread implements Runnable {
	// 手机号
	public String phone;
	// 短信内容
	public String smsContent;
	// 短信模板
	public String templateNo;

	public SmsThread(String phone, String smsContent, String templateNo) {
		super();
		this.phone = phone;
		this.smsContent = smsContent;
		this.templateNo = templateNo;
	}

	public void run() {
		// 发送短信
		SmsMessageUtil.send(phone, smsContent, templateNo);
	}

	public static void main(String[] args) {
		JSONObject jo = new JSONObject();
		jo.put("revenue", Double.toString(2.02));
		jo.put("serviceFee", 3.24);
		jo.put("income", 22);

		SmsThread smsThread = new SmsThread("18520569996", jo.toString(), "SMS_173406054");
		Thread t = new Thread(smsThread);
		t.start();
	}
}
