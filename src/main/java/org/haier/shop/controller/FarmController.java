package org.haier.shop.controller;

import java.io.File;
import java.io.InputStream;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.UUID;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.apache.shiro.SecurityUtils;
import org.apache.shiro.session.Session;
import org.apache.shiro.subject.Subject;
import org.haier.shop.entity.Staff;
import org.haier.shop.entity.beans.PageData;
import org.haier.shop.entity.globalSelect.FarmDetailVO;
import org.haier.shop.entity.globalSelect.FarmKind;
import org.haier.shop.service.FarmProductService;
import org.haier.shop.service.GlobalThemeService;
import org.haier.shop.service.GoodsService;
import org.haier.shop.util.AreaResult;
import org.haier.shop.util.FTPConfig;
import org.haier.shop.util.ImageBinary;
import org.haier.shop.util.PicSaveUtil;
import org.haier.shop.util.PurResult;
import org.haier.shop.util.SFTPUtil;
import org.haier.shop.util.ServletsUtil;
import org.haier.shop.util.ShopsResult;
import org.haier.shop.util.ShopsUtil;
import org.haier.shop.util.UUIDUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.multipart.MultipartFile;

import cn.hutool.http.HttpResponse;
import net.sf.json.JSONArray;
import net.sf.json.JsonConfig;

@Controller
@RequestMapping("/farm")
public class FarmController {

	@Autowired
	private GlobalThemeService globalService;
	@Autowired
	private GoodsService goodsService;
	@Resource
	private FarmProductService farmService;
	
	//跳转到-益农资讯
	@RequestMapping("/toFarmMessage.do")
	public String toFarmMessage(){
		return "/WEB-INF/yinong/farmMessage.jsp";
	}
	/**
	 * 获取农业资讯
	 * @return
	 */
	@RequestMapping("/queryFarmInformation.do")
	@ResponseBody
	public PurResult queryFarmInformation(HttpServletRequest request) {
		try {
			return farmService.queryFarmInformation(request);
		}catch (Exception e) {
			e.printStackTrace();
			PurResult result = new PurResult(0,"操作失败！");
			return result;
		}
	}
	/**
	  *新增农业资讯
	 * @param request
	 * @return
	 */
	@RequestMapping("/addFarmInformation.do")
	@ResponseBody
	public PurResult addFarmInformation(HttpServletRequest request){
		PurResult rs = new PurResult();
		try {
			Map<String, Object> params = ServletsUtil.getParameters(request);
	
			rs=farmService.addFarmInformation(params);
		} catch (Exception e) {
			rs.setStatus(0);
			rs.setMsg("操作失败!");
			e.printStackTrace();
		}		
		return rs;
	}
	/**
	  *新增农业图片
	 * @param request
	 * @return
	 */
	@RequestMapping("/addFarmInformationImage.do")
	@ResponseBody
	public PurResult addFarmInformationImage(MultipartFile file,HttpServletRequest request){
		PurResult rs = new PurResult();
		
		try {
			String host=request.getRequestURL().substring(0, request.getRequestURL().indexOf("/shop"));
        	System.out.println("访问路径"+host);
			String orName=file.getOriginalFilename();
			String lastName=orName.substring(orName.lastIndexOf("."));
            String filePath = this.getClass().getClassLoader().getResource("../../").getPath();
			filePath=filePath.substring(0, filePath.length()-request.getContextPath().length());
			String shop_dir = filePath + File.separator + "image" + File.separator + "farm"+ File.separator;
			File dir=new File(shop_dir);
			if(!dir.exists()){
				dir.mkdirs();
			}
			PicSaveUtil.handleFileUpId(file, request, shop_dir, orName);
			rs.setData(host+File.separator + "image" + File.separator + "farm"+ File.separator+orName);
			rs.setMsg(orName);
			rs.setStatus(1);
		} catch (Exception e) {
			rs.setStatus(0);
			rs.setMsg("操作失败!");
			e.printStackTrace();
		}		
		return rs;
	}
	//跳转到-供需大厅
	@RequestMapping("/toFarmHall.do")
	public String toFarmHall(){
		return "/WEB-INF/yinong/farmHall.jsp";
	}
	/**
	 * 
	 * @return
	 */
	@RequestMapping("/addNewFarmKind.do")
	@ResponseBody
	public PurResult addNewFarmKind(HttpServletRequest request,HttpServletResponse response,
			Integer id,String kindName,Integer parentId,Integer kindStatus
			) {
		try {
			FarmKind farmKind = new FarmKind();
			farmKind.setId(id);
			farmKind.setKindName(kindName);
			farmKind.setParentId(parentId);
			farmKind.setKindStatus(kindStatus == null?"1":kindStatus+"");
			return farmService.addNewFarmKind(farmKind,request,response);
		}catch (Exception e) {
			e.printStackTrace();
			PurResult result = new PurResult(0,"操作失败！");
			return result;
		}
	}
	@RequestMapping("/editFarmKind.do")
	public String editFarmKind() {
		return "/WEB-INF/farm/editFarmKind.jsp";
	}
	@RequestMapping("/setAddMoney.do")
	public String setAddMoney(HttpServletRequest request) {
		Map<String,Object> map= globalService.querySetAddMoney();
		request.setAttribute("data", map);
		return "/WEB-INF/farm/setAddMoney.jsp";
	}
	/**
	 * 查询全部分类信息
	 * @return
	 */
	@RequestMapping("/queryFarmKind.do")
	@ResponseBody
	public PurResult queryFarmKind(
			Integer page,
			Integer limit,
			String kindMsg,
			Integer id,
			Integer validType
			) {
		try {
			Map<String,Object> map = new HashMap<String,Object>();
			if(null != page && null != limit) {
				map.put("startNum", (page-1)*limit);
				map.put("pageSize", limit);
			}
			if(validType!=null&&validType==-1){
				map.put("parentId", -1);
			}
			if(null != kindMsg && !kindMsg.equals("")) {
				map.put("kindMsg", "%"+kindMsg+"%");
			}
			map.put("id", id);
			return farmService.queryFarmKind(map);
		}catch (Exception e) {
			e.printStackTrace();
			PurResult pr = new PurResult(0, "查询失败");
			return pr;
		}
	}
	//跳转到-农产品管理
	@RequestMapping("/toProductsPage.do")
	public String toProductsPage(){
		return "/WEB-INF/farm/products.jsp";
	}
	//跳转到-农产品管理
	@RequestMapping("/toProductsPagePlatform.do")
	public String toProductsPagePlatform(){
		return "/WEB-INF/farm/products_platform.jsp";
	}
	//跳转到-农产品添加
	@RequestMapping("/toAddProducts.do")
	public String toAddProducts(Model model){
		Map<String,Object> params=new HashMap<String,Object>();
    	params.put("id", null);
    	//获取有商家的区域列表
    	List<Map<String ,Object>> areaList = globalService.getFarmAreaList(params); 
    	//获取有商家的城市列表
    	List<Map<String ,Object>> cityList = globalService.getCityList(areaList);
    	//获取有商家的省份列表
    	List<Map<String ,Object>> provinceList = globalService.getProvinceList(cityList);
		//获取所有审核通过店铺列表
		List<Map<String ,Object>> shopList = goodsService.getAllShopList();
		model.addAttribute("shopList", shopList);
    	model.addAttribute("areaList", areaList);
    	model.addAttribute("cityList", cityList);
    	model.addAttribute("provinceList", provinceList);
		return "/WEB-INF/farm/addProducts.jsp";
	}

	/**
	 * 添加农产品
	 */

	@RequestMapping("/addProduct.do")
	@ResponseBody
	public PurResult addProduct(HttpServletRequest request){
		PurResult rs = new PurResult();
		try {
			Map<String, Object> params = ServletsUtil.getParameters(request);
			System.out.println(params);
			Subject subject = SecurityUtils.getSubject();
			Session session = subject.getSession();
			Staff staff = (Staff) session.getAttribute("staff");
			if(staff.getShop_type()==4)
			{
				params.put("audit_status", 1);
			}else
			{
				params.put("audit_status", 0);
			}
			//生产条码
			String barcode ="N"+System.currentTimeMillis()/ 1000+(int)((Math.random()*9+1)*1000); //10位数的时间戳+4位随机数
			params.put("barcode", barcode);
			JSONArray peopleJson=JSONArray.fromObject(params.get("peopleJson"));
			List<PageData> peopleList = JSONArray.toList(peopleJson, new PageData(), new JsonConfig());//农户
			MultipartFile file=null;
			//上传商品图片
			SFTPUtil sftp = new SFTPUtil(FTPConfig.username, FTPConfig.password, FTPConfig.host, FTPConfig.port);   
	        sftp.login(); 
			file=ShopsUtil.testMulRequest(request, "goodsPicturePath1");
			if(file!=null){
				InputStream is = file.getInputStream();   
				String orName=file.getOriginalFilename();//获取文件原名称
				String lastName=orName.substring(orName.lastIndexOf("."));
				String newName=UUIDUtil.getUUID32()+lastName;
				String filePathDetail="/"+"image"+"/"+params.get("shop_unique");
				String filePath = this.getClass().getClassLoader().getResource("../../").getPath();
				filePath=filePath.substring(0, filePath.length()-request.getContextPath().length())+filePathDetail;
				ShopsUtil.savePicture(file, filePath, newName,"2");
		        boolean flag=sftp.upload(FTPConfig.goods_path+"/"+params.get("shop_unique"), newName, is);   
				if(flag){
					params.put("goods_image", filePathDetail+"/"+newName);
				}
			}
			
			MultipartFile videofile=null;
			//上传商品视频
	        videofile=ShopsUtil.testMulRequest(request, "goodsPicturePathVideo");
			if(videofile!=null){
				InputStream is = videofile.getInputStream();   
				String orName=videofile.getOriginalFilename();//获取文件原名称
				String lastName=orName.substring(orName.lastIndexOf("."));
				String newName=UUIDUtil.getUUID32()+lastName;
				String filePathDetail="/"+"image"+"/"+params.get("shop_unique");
				String filePath = this.getClass().getClassLoader().getResource("../../").getPath();
				filePath=filePath.substring(0, filePath.length()-request.getContextPath().length())+filePathDetail;
//				ShopsUtil.savePicture(videofile, filePath, newName,"2");
		        boolean flag=sftp.upload(FTPConfig.goods_path+"/"+params.get("shop_unique"), newName, is);   
				if(flag){
					params.put("goods_video", filePathDetail+"/"+newName);
				}
			}
			
			int num=0;
			for(PageData p:peopleList)
			{
				MultipartFile file1=null;
				file1=ShopsUtil.testMulRequest(request, p.get("aptitudes_imgages").toString());
				if(file1!=null){
					String orName=file1.getOriginalFilename();//获取文件原名称
					String lastName=orName.substring(orName.lastIndexOf("."));
					String newName=UUIDUtil.getUUID32()+lastName;
					String filePathDetail="/"+"image"+"/"+params.get("shop_unique");
					String filePath = this.getClass().getClassLoader().getResource("../../").getPath();
					filePath=filePath.substring(0, filePath.length()-request.getContextPath().length())+filePathDetail;
					ShopsUtil.savePicture(file1, filePath, newName,"2");
			        InputStream is = file1.getInputStream();   
			        boolean flag=sftp.upload(FTPConfig.goods_path+"/"+params.get("shop_unique"), newName, is);   
					if(flag){
						p.put("aptitudes_imgages", filePathDetail+"/"+newName);
					}else
					{
						p.put("aptitudes_imgages", null);
					}
				}else
				{
					p.put("aptitudes_imgages", null);
				}
				MultipartFile file2=null;
				file2=ShopsUtil.testMulRequest(request, p.get("aptitudes_imgages2").toString());
				if(file2!=null){
					String orName=file2.getOriginalFilename();//获取文件原名称
					String lastName=orName.substring(orName.lastIndexOf("."));
					String newName=UUIDUtil.getUUID32()+lastName;
					String filePathDetail="/"+"image"+"/"+params.get("shop_unique");
					String filePath = this.getClass().getClassLoader().getResource("../../").getPath();
					filePath=filePath.substring(0, filePath.length()-request.getContextPath().length())+filePathDetail;
					ShopsUtil.savePicture(file2, filePath, newName,"2");
					InputStream is = file2.getInputStream();   
			        boolean flag=sftp.upload(FTPConfig.goods_path+"/"+params.get("shop_unique"), newName, is);
					if(flag){
						p.put("aptitudes_imgages2", filePathDetail+"/"+newName);
					}else
					{
						p.put("aptitudes_imgages2", null);
					}
				}else
				{
					p.put("aptitudes_imgages2", null);
				}
				
				MultipartFile file3=null;
				file3=ShopsUtil.testMulRequest(request,p.get("aptitudes_imgages3").toString());
				if(file3!=null){
					String orName=file3.getOriginalFilename();//获取文件原名称
					String lastName=orName.substring(orName.lastIndexOf("."));
					String newName=UUIDUtil.getUUID32()+lastName;
					String filePathDetail="/"+"image"+"/"+params.get("shop_unique");
					String filePath = this.getClass().getClassLoader().getResource("../../").getPath();
					filePath=filePath.substring(0, filePath.length()-request.getContextPath().length())+filePathDetail;
					ShopsUtil.savePicture(file3, filePath, newName,"2");
					InputStream is = file3.getInputStream();   
			        boolean flag=sftp.upload(FTPConfig.goods_path+"/"+params.get("shop_unique"), newName, is);
					if(flag){
						p.put("aptitudes_imgages3", filePathDetail+File.separator+newName);
					}else
					{
						p.put("aptitudes_imgages3", null);
					}
				}else
				{
					p.put("aptitudes_imgages3", null);
				}
			}
	        sftp.logout(); 
			rs=globalService.addProduct(params,request,peopleList);
		} catch (Exception e) {
			rs.setStatus(0);
			rs.setMsg("操作失败!");
			e.printStackTrace();
		}		
		return rs;
	}
	/**
	 * 修改农产品
	 */

	@RequestMapping("/updateProduct.do")
	@ResponseBody
	public PurResult updateProduct(HttpServletRequest request){
		PurResult rs = new PurResult();
		try {
			Map<String, Object> params = ServletsUtil.getParameters(request);
			Subject subject = SecurityUtils.getSubject();
			Session session = subject.getSession();
			Staff staff = (Staff) session.getAttribute("staff");
			if(staff.getShop_type()==4)
			{
				
				params.put("audit_status", 1);
			}else
			{
				params.put("audit_status", 0);
			}
			rs=globalService.updateProduct(params,request);
		} catch (Exception e) {
			rs.setStatus(0);
			rs.setMsg("操作失败!");
			e.printStackTrace();
		}		
		return rs;
	}
	/**
	 * 添加农产品_对外接口
	 */

	@SuppressWarnings("unchecked")
	@RequestMapping("/addProduct_base.do")
	@ResponseBody
	public PurResult addProduct_base(
			
			@RequestParam(value = "goods_name",required=true)String goods_name,
			HttpServletRequest request){
		PurResult rs = new PurResult();
		try {
			Map<String, Object> params = ServletsUtil.getParameters(request);
			System.out.println(params.toString() +">>>>>>>>>>>>>>22233");
			System.out.println(goods_name +">>>>>>>>>>>>>>22233");

			JSONArray peopleJson=JSONArray.fromObject(params.get("peopleJson"));
			List<PageData> peopleList = JSONArray.toList(peopleJson, new PageData(), new JsonConfig());//农户
			SFTPUtil sftp = new SFTPUtil(FTPConfig.username, FTPConfig.password, FTPConfig.host, FTPConfig.port);   
	        sftp.login();
			//保存农产品图片
			if(params.containsKey("goodsPicturePath1")&&params.get("goodsPicturePath1")!=null)
			{
				Map<String,Object> map=new HashMap<String,Object>();
				String mysqlPath=ImageBinary.mysqlPath("jpg",params.get("shop_unique").toString());
				map.put("imagePath", mysqlPath);
				map.put("imageMsg", params.get("goodsPicturePath1"));
				map.put("imgFormat", "jpg");
				String path=ImageBinary.savePicture2(sftp,map, request,params.get("shop_unique").toString());
				params.put("goods_image", path);
			}

			//保存农户资质图片
			
			for(PageData p:peopleList)
			{
	
					if(p.containsKey("aptitudes_imgages")&&p.get("aptitudes_imgages")!=null&&p.get("aptitudes_imgages")!="")
					{
						Map<String,Object> map=new HashMap<String,Object>();
						String mysqlPath=ImageBinary.mysqlPath("jpg",params.get("shop_unique").toString());
						map.put("imagePath", mysqlPath);
						map.put("imageMsg", p.get("aptitudes_imgages"));
						map.put("imgFormat", "jpg");
						String path=ImageBinary.savePicture2(sftp,map, request,params.get("shop_unique").toString());
						p.put("aptitudes_imgages", path);
					}else
					{
						p.put("aptitudes_imgages", null);
					}
					
					if(p.containsKey("aptitudes_imgages2")&&p.get("aptitudes_imgages2")!=null&&p.get("aptitudes_imgages2")!="")
					{
						Map<String,Object> map=new HashMap<String,Object>();
						String mysqlPath=ImageBinary.mysqlPath("jpg",params.get("shop_unique").toString());
						map.put("imagePath", mysqlPath);
						map.put("imageMsg", p.get("aptitudes_imgages2"));
						map.put("imgFormat", "jpg");
						String path=ImageBinary.savePicture2(sftp,map, request,params.get("shop_unique").toString());
						p.put("aptitudes_imgages2", path);
					}else
					{
						p.put("aptitudes_imgages2", null);
					}
					
					if(p.containsKey("aptitudes_imgages3")&&p.get("aptitudes_imgages3")!=null&&p.get("aptitudes_imgages3")!="")
					{
						Map<String,Object> map=new HashMap<String,Object>();
						String mysqlPath=ImageBinary.mysqlPath("jpg",params.get("shop_unique").toString());
						map.put("imagePath", mysqlPath);
						map.put("imageMsg", p.get("aptitudes_imgages3"));
						map.put("imgFormat", "jpg");
						String path=ImageBinary.savePicture2(sftp,map, request,params.get("shop_unique").toString());
						p.put("aptitudes_imgages3", path);
					}else
					{
						p.put("aptitudes_imgages3", null);
					}

			}
			sftp.logout(); 
			rs=globalService.addProduct2(params,request,peopleList);
		} catch (Exception e) {
			rs.setStatus(0);
			rs.setMsg("操作失败!");
			e.printStackTrace();
		}		
		return rs;
	}
	@SuppressWarnings("unchecked")
	@RequestMapping("/updateProduct_base.do")
	@ResponseBody
	public PurResult updateProduct_base(
			
			@RequestParam(value = "goods_name",required=true)String goods_name,
			HttpServletRequest request){
		PurResult rs = new PurResult();
		try {
			Map<String, Object> params = ServletsUtil.getParameters(request);
			JSONArray peopleJson=JSONArray.fromObject(params.get("peopleJson"));
			List<PageData> peopleList = JSONArray.toList(peopleJson, new PageData(), new JsonConfig());//农户
			SFTPUtil sftp = new SFTPUtil(FTPConfig.username, FTPConfig.password, FTPConfig.host, FTPConfig.port);   
			sftp.login();
			//保存农产品图片
			if(params.containsKey("goodsPicturePath1")&&params.get("goodsPicturePath1")!=null)
			{
				Map<String,Object> map=new HashMap<String,Object>();
				String mysqlPath=ImageBinary.mysqlPath("jpg",params.get("shop_unique").toString());
				map.put("imagePath", mysqlPath);
				map.put("imageMsg", params.get("goodsPicturePath1"));
				map.put("imgFormat", "jpg");
				String path=ImageBinary.savePicture2(sftp,map, request,params.get("shop_unique").toString());
				params.put("goods_image", path);
			}
			
			//保存农户资质图片
			
			for(PageData p:peopleList)
			{
				
				if(p.containsKey("aptitudes_imgages")&&p.get("aptitudes_imgages")!=null&&p.get("aptitudes_imgages")!="")
				{
					Map<String,Object> map=new HashMap<String,Object>();
					String mysqlPath=ImageBinary.mysqlPath("jpg",params.get("shop_unique").toString());
					map.put("imagePath", mysqlPath);
					map.put("imageMsg", p.get("aptitudes_imgages"));
					map.put("imgFormat", "jpg");
					String path=ImageBinary.savePicture2(sftp,map, request,params.get("shop_unique").toString());
					p.put("aptitudes_imgages", path);
				}else
				{
					p.put("aptitudes_imgages", null);
				}
				
				if(p.containsKey("aptitudes_imgages2")&&p.get("aptitudes_imgages2")!=null&&p.get("aptitudes_imgages2")!="")
				{
					Map<String,Object> map=new HashMap<String,Object>();
					String mysqlPath=ImageBinary.mysqlPath("jpg",params.get("shop_unique").toString());
					map.put("imagePath", mysqlPath);
					map.put("imageMsg", p.get("aptitudes_imgages2"));
					map.put("imgFormat", "jpg");
					String path=ImageBinary.savePicture2(sftp,map, request,params.get("shop_unique").toString());
					p.put("aptitudes_imgages2", path);
				}else
				{
					p.put("aptitudes_imgages2", null);
				}
				
				if(p.containsKey("aptitudes_imgages3")&&p.get("aptitudes_imgages3")!=null&&p.get("aptitudes_imgages3")!="")
				{
					Map<String,Object> map=new HashMap<String,Object>();
					String mysqlPath=ImageBinary.mysqlPath("jpg",params.get("shop_unique").toString());
					map.put("imagePath", mysqlPath);
					map.put("imageMsg", p.get("aptitudes_imgages3"));
					map.put("imgFormat", "jpg");
					String path=ImageBinary.savePicture2(sftp,map, request,params.get("shop_unique").toString());
					p.put("aptitudes_imgages3", path);
				}else
				{
					p.put("aptitudes_imgages3", null);
				}
				
			}
			sftp.logout(); 
			rs=globalService.updateProduct2(params,request,peopleList);
		} catch (Exception e) {
			rs.setStatus(0);
			rs.setMsg("操作失败!");
			e.printStackTrace();
		}		
		return rs;
	}
	//跳转到-平台全球精选商品上下架详情
	@RequestMapping("/toOnShelf.do")
	public String toOnShelf(Model model,String barCode,String id,String lower_price){
		model.addAttribute("barCode", barCode);
		model.addAttribute("id", id);
		model.addAttribute("lower_price", lower_price);
		return "/WEB-INF/global/globalGoodShelfPt.jsp";
	}

	
	/**
	 * 农产品列表F
	 */
	@RequestMapping("/queryProductsList.do")
	@ResponseBody
	public PurResult queryGlobalSupplierList(
			@RequestParam(value="content",defaultValue="")String content,
			@RequestParam(value="start_time",defaultValue="")String start_time,
			@RequestParam(value="end_time",defaultValue="")String end_time,
			@RequestParam(value="audit_status",defaultValue="-1")int audit_status,
			@RequestParam(value="shelf_status",defaultValue="-1")int shelf_status,
			@RequestParam(value="sup_shelf_status",defaultValue="-1")int sup_shelf_status,
			@RequestParam(value="shop_unique",defaultValue="")Long shop_unique,
			@RequestParam(value="shop_unique2",defaultValue="")String shop_unique2,
			@RequestParam(value="active_status",defaultValue="-1")int active_status,
			@RequestParam(value = "page", defaultValue = "1") int page,
			@RequestParam(value = "limit", defaultValue = "15") int pageSize,
			@RequestParam(value = "is_wechat", defaultValue = "1") int is_wechat,
			String kind_id
			){

		Map<String,Object> map=new HashMap<String, Object>();
		map.put("shop_unique", shop_unique);
		map.put("shop_unique2", shop_unique2);
		map.put("pageSize", pageSize);
		map.put("startNum", (page-1)*pageSize);
		map.put("content", content);
		map.put("start_time", start_time);
		map.put("end_time", end_time);
		map.put("audit_status", audit_status);
		map.put("shelf_status", shelf_status);
		map.put("sup_shelf_status", sup_shelf_status);
		map.put("active_status", active_status);
		map.put("is_wechat", is_wechat);
		String temp_str="";   
	    Date dt = new Date();   
	    SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");   
	    temp_str=sdf.format(dt);   
		
		map.put("temp_str", temp_str);
		if(kind_id!=null&&!"".equals(kind_id)){
			map.put("kind_id", kind_id);
		}
		return globalService.queryProductsList(map);
	}
	
	/**
	 * 第一书记，省市区编码
	 */
	@RequestMapping("/queryAreaList.do")
	@ResponseBody
	public AreaResult queryGlobalSupplierList(@RequestParam(value="type",defaultValue="")String type
			){
		AreaResult result = new AreaResult();
		try {
			Map<String,Object> params=new HashMap<String,Object>();
			params.put("id", null);
			//获取有商家的区域列表
	    	List<Map<String ,Object>> areaList = globalService.getFarmAreaList(params); 
	    	//获取有商家的城市列表
	    	List<Map<String ,Object>> cityList = globalService.getCityList(areaList);
	    	//获取有商家的省份列表
	    	List<Map<String ,Object>> provinceList = globalService.getProvinceList(cityList);
			//获取所有审核通过店铺列表
//			List<Map<String ,Object>> shopList = goodsService.getAllShopList();
			
			List<Map<String ,Object>> secretaryList = globalService.querySecretaryList();
			
	    	result.setAreaList(areaList);
	    	result.setCityList(cityList);
	    	result.setProvinceList(provinceList);
	    	result.setSecretaryList(secretaryList);
	    	//result.setShopList(shopList);
	    	result.setStatus(1);
			result.setMsg("成功");
			
		} catch (Exception e) {
			e.printStackTrace();
			result.setStatus(0);
			result.setMsg("异常");
		}
		return result;
	}
	
	
	/**
	 * 跳转到农产品详情界面
	 */
	@RequestMapping("/toFarmDetail.do")
	public String toFarmDetail(Model model,String id){
		Map<String,Object> params=new HashMap<String,Object>();
    	params.put("id", id);
    	params.put("farm_product_id", id);
    	//获取有商家的区域列表
    	List<Map<String ,Object>> areaList = globalService.getFarmAreaList(params); 
    	//获取有商家的城市列表
    	List<Map<String ,Object>> cityList = globalService.getCityList(areaList);
    	//获取有商家的省份列表
    	List<Map<String ,Object>> provinceList = globalService.getProvinceList(cityList); 
    	
    	//审核记录
    	List<Map<String ,Object>> auditList = globalService.getFarmAuditList(params); 
    	for(Map<String ,Object> city:cityList)
    	{
    		int num=0;
    		for(Map<String ,Object> area:areaList)
        	{
    			if(area.get("city_code").equals(city.get("city_code")))
    			{
    				
    				if(area.get("flag").equals("false"))
    				{
    					num=num+1;
    				}
    			}
        		
        	}
    		if(num==0)
    		{
    			city.put("flag", "true");
    		}else
    		{
    			city.put("flag", "false");
    		}
    		
    	}
    	
    	
    	FarmDetailVO data=globalService.queryFarmDetail(params);
    	model.addAttribute("areaList", areaList);
    	model.addAttribute("cityList", cityList);
    	model.addAttribute("provinceList", provinceList);
    	model.addAttribute("auditList", auditList);
    	model.addAttribute("id", id);
    	model.addAttribute("data", data);
    	
		return "/WEB-INF/farm/farmDetail.jsp";
	}
	/**
	 * 跳转到农产品详情界面
	 */
	@RequestMapping("/toEditProducts.do")
	public String toEditProducts(Model model,String id){
		Map<String,Object> params=new HashMap<String,Object>();
    	params.put("id", id);
    	//获取有商家的区域列表
    	List<Map<String ,Object>> areaList = globalService.getFarmAreaList(params); 
    	//获取有商家的城市列表
    	List<Map<String ,Object>> cityList = globalService.getCityList(areaList);
    	//获取有商家的省份列表
    	List<Map<String ,Object>> provinceList = globalService.getProvinceList(cityList); 
    	
    	for(Map<String ,Object> city:cityList)
    	{
    		int num=0;
    		for(Map<String ,Object> area:areaList)
        	{
    			if(area.get("city_code").equals(city.get("city_code")))
    			{
    				
    				if(area.get("flag").equals("false"))
    				{
    					num=num+1;
    				}
    			}
        		
        	}
    		if(num==0)
    		{
    			city.put("flag", "true");
    		}else
    		{
    			city.put("flag", "false");
    		}
    		
    	}
    	
    	
    	FarmDetailVO data=globalService.queryFarmDetail(params);
    	model.addAttribute("areaList", areaList);
    	model.addAttribute("cityList", cityList);
    	model.addAttribute("provinceList", provinceList);
    	model.addAttribute("id", id);
    	model.addAttribute("data", data);
    	
		return "/WEB-INF/farm/editProducts.jsp";
	}

	/**
	 * 农产品列表
	 */
	@RequestMapping("/queryProductsDetail.do")
	@ResponseBody
	public PurResult queryProductsDetail(@RequestParam(value="id",defaultValue="")int id){
		PurResult rs = new PurResult();
		Map<String,Object> params=new HashMap<String,Object>();;
		params.put("id", id);
		FarmDetailVO data=globalService.queryFarmDetail(params);
		rs.setStatus(1);
		rs.setData(data);
		return rs;
	}

	
	/**
	 * 农产品 上下架
	 */
	@RequestMapping("/updateFarmStatus.do")
	@ResponseBody
	public PurResult updateFarmStatus(
			@RequestParam(value="id",defaultValue="")int id,
			@RequestParam(value="shelf_status",defaultValue="")int shelf_status,
			@RequestParam(value="good_status",defaultValue="1")int good_status
			){
		PurResult rs = new PurResult();
		try {

		rs=globalService.updateFarmStatus(id,shelf_status,good_status);
		} catch (Exception e) {
			rs.setStatus(0);
			rs.setMsg("操作失败!");
			e.printStackTrace();
		}
		return rs;
	}
	/**
	 * 农产品 p批量上下架
	 */
	@RequestMapping("/updateAllFarmStatus.do")
	@ResponseBody
	public PurResult updateFarmStatus(
			@RequestParam(value="shelf_status",defaultValue="")int shelf_status,
			@RequestParam(value="data",defaultValue="")String data,
			@RequestParam(value="good_status",defaultValue="1")int good_status
			){
		PurResult rs = new PurResult();
		try {

		rs=globalService.updateAllFarmStatus(shelf_status,data,good_status);
		} catch (Exception e) {
			rs.setStatus(0);
			rs.setMsg("操作失败!");
			e.printStackTrace();
		}
		return rs;
	}
	
	
	/**
	 * 商品分类查询
	 * @param shop_unique 店铺编号
	 * @param goods_kind_parunique 商品分类父类编号
	 * @return
	 */
	@RequestMapping("/queryfarmKinds.do")
	@ResponseBody
	public ShopsResult queryfarmKinds(String goods_kind_parunique,
			@RequestParam(value="kindStatus",defaultValue="1")Integer kindStatus,
			@RequestParam(value="kind_type",defaultValue="1")Integer kind_type
			){
		return globalService.queryfarmKinds( goods_kind_parunique,kindStatus,kind_type);
	}
	
	@RequestMapping("/updateAddMoney.do")
	@ResponseBody
	public ShopsResult updateAddMoney(
			String add_money_quan,
			String add_money_sup
			){
		return globalService.updateAddMoney( add_money_quan,add_money_sup);
	}
	
	@RequestMapping("/toSupOrderList.do")
	public String toSupOrderList() {
		return "/WEB-INF/farm/supOrderList.jsp";
	}
}
