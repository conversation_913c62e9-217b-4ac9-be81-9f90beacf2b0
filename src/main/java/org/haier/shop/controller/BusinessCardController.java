package org.haier.shop.controller;


import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import cn.hutool.http.HttpUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import jxl.common.Logger;
import org.apache.shiro.SecurityUtils;
import org.apache.shiro.session.Session;
import org.apache.shiro.subject.Subject;
import org.haier.shop.config.JQVersionTwoConfig;
import org.haier.shop.entity.Staff;
import org.haier.shop.params.card.*;
import org.haier.shop.params.card.jqtwo.AddCardParams;
import org.haier.shop.params.card.jqtwo.DeleteCardParams;
import org.haier.shop.params.card.jqtwo.EditCardParams;
import org.haier.shop.params.card.jqtwo.ListParams;
import org.haier.shop.result.card.GetBusinessCardDetail;
import org.haier.shop.result.card.GetInfoParams;
import org.haier.shop.result.card.Personnel;
import org.haier.shop.result.card.UploadFileResult;
import org.haier.shop.result.common.PageSearchResult;
import org.haier.shop.service.BusinessCardService;
import org.haier.shop.util.common.Result;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpSession;
import java.io.File;
import java.io.IOException;
import java.util.ArrayList;
import java.util.List;



/**
 * 企业名片管理
 */
@RequestMapping("/businessCard")
@Controller
public class BusinessCardController {

    @Resource
    private BusinessCardService businessCardService;

    private static Logger logger = Logger.getLogger(BusinessCardController.class);


    /**
     * 打开企业名片页面
     * @param request
     * @return
     */
    @RequestMapping("/toCardPage.do")
    private String toCardPage( HttpServletRequest request) {
        Subject subject = SecurityUtils.getSubject();
        Session session = subject.getSession();
        Staff staff = (Staff) session.getAttribute("staff");
        String shopUnique = staff.getShop_unique().toString();
        String shopName = staff.getShop_name();
        session.setAttribute("shopUnique", shopUnique);
        session.setAttribute("shopName", shopName);
        session.setAttribute("userId", staff.getStaff_id());
        session.setAttribute("userType", staff.getStaff_position() == 3 ? "1":"2");
        return "/WEB-INF/commonPage.jsp";
    }

    /**
     * 打开企业名片页面
     * @param request
     * @return
     */
    @RequestMapping("/toPlatCardPage.do")
    private String toPlatCardPage( HttpServletRequest request) {
        Subject subject = SecurityUtils.getSubject();
        Session session = subject.getSession();
        Staff staff = (Staff) session.getAttribute("staff");
        String shopUnique = staff.getShop_unique().toString();
        String shopName = staff.getShop_name();
        session.setAttribute("shopUnique", shopUnique);
        session.setAttribute("shopName", shopName);
        session.setAttribute("userId", staff.getStaff_id());
        session.setAttribute("userType", staff.getStaff_position() == 3 ? "1":"2");
        return "/WEB-INF/commonPlatPage.jsp";
    }

    /**
     * 上传文件
     * @return
     */
    @RequestMapping("/uploadFile.do")
    @ResponseBody
    public Result<UploadFileResult> uploadFile(@RequestParam(value="file",required=true) MultipartFile file){

        HttpRequest request = HttpUtil.createPost(JQVersionTwoConfig.uploadFile);
        System.out.println(JQVersionTwoConfig.uploadFile);
        request.header("Content-Type", "multipart/form-data");
        try {
            File f = convertMultipartFileToFile(file);
            request.form("file", f);
        } catch (IOException e) {
            logger.info("文件转换格式异常");
        }
        HttpResponse response = request.execute();
        System.out.println(response.body());
        String res = response.body();
        JSONObject jo = JSONObject.parseObject(res);
        if (jo.containsKey("code") && jo.getString("code").equals(JQVersionTwoConfig.SUCCESSCODE)) {
            JSONObject data = jo.getJSONObject("data");
            UploadFileResult result = new UploadFileResult();
            result.setFileName(data.getString("filename"));
            result.setUrl(data.getString("url"));
            return Result.success(result);
        }
        return Result.fail(jo.getString("msg"));
    }


    private static File convertMultipartFileToFile(MultipartFile multipartFile) throws IOException {
        String originName = multipartFile.getOriginalFilename();
        File file = File.createTempFile("temp", originName.substring(originName.lastIndexOf(".")));
        multipartFile.transferTo(file);
        return file;
    }

    /**
     * 获取名片列表
     * @param params
     * @return
     */
    @RequestMapping("/getBusinessCardList.do")
    @ResponseBody
    public Result<PageSearchResult<GetBusinessCardDetail>> getBusinessCardList(@RequestBody GetBusinessCardParams params){
        //整理需要上传的参数
        ListParams listParams=new ListParams();

        listParams.setPageNum(params.getPage());
        listParams.setPageSize(params.getLimit());
        listParams.setShopId(params.getShopUnique());
        listParams.setStartTime(params.getStartTime());
        listParams.setEndTime(params.getEndTime());
        listParams.setUserName(params.getUserInfo());
        listParams.setShopName(params.getCompanyInfo());
        listParams.setUserId(params.getUserId());
        listParams.setUserType(params.getUserType());
        logger.info("请求的路径===={}" + JQVersionTwoConfig.getCardList);
        logger.info("---获取名片列表接口参数：" + JSON.toJSONString(listParams));
        String res = HttpUtil.post(JQVersionTwoConfig.getCardList, JSON.toJSONString(listParams));
        logger.info("---获取名片列表接口返回值：" + res);

        JSONObject jo = JSONObject.parseObject(res);
        if (jo.containsKey("code") && jo.getString("code").equals(JQVersionTwoConfig.SUCCESSCODE)) {
            JSONObject data = jo.getJSONObject("data");
            //查询成功
            PageSearchResult result = new PageSearchResult();
            result.setPageNum(params.getPage());
            result.setPageSize(params.getLimit());
            result.setTotalCount(data.getInteger("total"));
            List<Object> rows = new ArrayList<>();
            JSONArray rowsJson = data.getJSONArray("rows");
            for (int i = 0; i < rowsJson.size(); i++) {
                JSONObject row = rowsJson.getJSONObject(i);
                GetBusinessCardDetail detail = new GetBusinessCardDetail();
                BeanUtil.copyProperties(row, detail);
                rows.add(detail);
            }
            result.setRows(rows);
            return Result.success(result);
        }

        return Result.fail(jo.getString("msg"));
    }

    /**
     * 获取名片详情
     * @return
     */
    @RequestMapping("/getBusinessCardDetail.do")
    @ResponseBody
    public Result<GetBusinessCardDetail> getBusinessCardDetail(@RequestBody GetBusinessCardDetailParams params){
        GetInfoParams getInfoParams=new GetInfoParams();

        BeanUtil.copyProperties(params,getInfoParams);
        logger.info("请求的路径===={}" + JQVersionTwoConfig.getCardDetail);
        logger.info("---获取名片详情接口参数：" + JSON.toJSONString(getInfoParams));
        String res = HttpUtil.post(JQVersionTwoConfig.getCardDetail, JSON.toJSONString(getInfoParams));
        logger.info("---获取名片详情接口返回值：" + res);
        JSONObject jo = JSONObject.parseObject(res);
        if (jo.containsKey("code") && jo.getString("code").equals("200")) {
            JSONObject data = jo.getJSONObject("data");
            GetBusinessCardDetail detail = new GetBusinessCardDetail();
            BeanUtil.copyProperties(data, detail);

            JSONArray personalPicFileList = data.getJSONArray("personalPicFileList");
            List<String> personalPicFiles = new ArrayList<>();
            if (personalPicFileList != null) {
                for (int i = 0; i < personalPicFileList.size(); i++) {
                    personalPicFiles.add(personalPicFileList.getString(i));
                }
            }
            detail.setPersonalPicFiles(personalPicFiles);

            JSONArray companyPicFileList = data.getJSONArray("companyPicFileList");
            List<String> companyPicFiles= new ArrayList<>();
            if (companyPicFileList != null) {
                for (int i = 0; i < companyPicFileList.size(); i++) {
                    companyPicFiles.add(companyPicFileList.getString(i));
                }
            }
            detail.setCompanyPicFiles(companyPicFiles);

            //查询本店的员工列表和团长列表
            List<Personnel> personnelList=businessCardService.queryShopStaffList(data.getString("shopId"),params.getUserId());

            for (Personnel personnel : personnelList) {
                if (personnel.getUserId().compareTo(detail.getUserId()) == 0) {
                    detail.setStaffName(personnel.getUserName());
                    break;
                }
            }

            return Result.success(detail);
        }

        return Result.fail(jo.getString("msg"));
    }

    /**
     * 新增名片
     * @return
     */
    @RequestMapping("/addBusinessCard.do")
    @ResponseBody
    public Result<Void> addBusinessCard(@RequestBody AddBusinessCardParams params){
        AddCardParams addCardParams=new AddCardParams();
        BeanUtil.copyProperties(params,addCardParams);
        addCardParams.setShopId(params.getShopUnique());
        if (null != params.getMobile() && ObjectUtil.isNull(params.getWeChat())) {
            addCardParams.setWeChat(params.getMobile());
        }
        addCardParams.setPersonalPicFileList(params.getPersonalPicFiles());
        addCardParams.setCompanyPicFileList(params.getCompanyPicFiles());
        logger.info("请求的路径===={}" + JQVersionTwoConfig.addCard);
        logger.info("---新增名片接口参数：" + JSON.toJSONString(addCardParams));
        String res = HttpUtil.post(JQVersionTwoConfig.addCard, JSON.toJSONString(addCardParams));
        logger.info("---新增名片接口返回值：" + res);
        JSONObject jo = JSONObject.parseObject(res);
        if (jo.containsKey("code") && jo.getString("code").equals("200")) {
            return Result.success();
        }

        return Result.fail(jo.getString("msg"));
    }

    /**
     * 更新名片信息
     * @param params
     * @return
     */
    @RequestMapping("/updateBusinessCard.do")
    @ResponseBody
    public Result<Void> updateBusinessCard(@RequestBody UpdateBusinessCardParams params){
        EditCardParams editCardParams=new EditCardParams();
        BeanUtil.copyProperties(params,editCardParams);
        editCardParams.setShopId(params.getShopUnique());
        if (ObjectUtil.isNotNull(params.getMobile() ) && ObjectUtil.isNull(params.getWeChat())) {
            editCardParams.setWeChat(params.getMobile());
        }
        editCardParams.setPersonalPicFileList(params.getPersonalPicFiles());
        editCardParams.setCompanyPicFileList(params.getCompanyPicFiles());
        logger.info("请求的路径===={}" + JQVersionTwoConfig.updateCard);
        logger.info("---更新名片接口参数：" + JSON.toJSONString(editCardParams));
        String res = HttpUtil.post(JQVersionTwoConfig.updateCard, JSON.toJSONString(editCardParams));
        logger.info("---更新名片接口返回值：" + res);
        JSONObject jo = JSONObject.parseObject(res);
        if (jo.containsKey("code") && jo.getString("code").equals("200")) {
            return Result.success();
        }

        return Result.fail(jo.getString("msg"));
    }

    /**
     * 删除名称，暂时只允许删除自己的名片
     * @param params
     * @return
     */
    @RequestMapping("/deleteBusinessCard.do")
    @ResponseBody
    public Result deleteBusinessCard(@RequestBody DeleteBusinessCardParams params){
        DeleteCardParams deleteCardParams=new DeleteCardParams();
        deleteCardParams.setIds(params.getIds());
        logger.info("请求的路径===={}" + JQVersionTwoConfig.deleteCard);
        logger.info("---删除名片接口参数：" + JSON.toJSONString(deleteCardParams));
        String res = HttpUtil.post(JQVersionTwoConfig.deleteCard, JSON.toJSONString(deleteCardParams));
        logger.info("---删除名片接口返回值：" + res);
        JSONObject jo = JSONObject.parseObject(res);
        if (jo.containsKey("code") && jo.getString("code").equals("200")) {
            return Result.success();
        }
        return Result.fail(jo.getString("msg"));
    }

    /**
     * 获取可新增名片人员列表，不分页
     * @return
     */
    @RequestMapping("/getPersonnelList.do")
    @ResponseBody
    public Result<PageSearchResult<Personnel>> getPersonnelList(@RequestBody GetPersonnelListParams params){
        //查询本店的员工列表和团长列表
        List<Personnel> personnelList=businessCardService.queryShopStaffList(params.getShopUnique(),null);
        List<Personnel> perResList = new ArrayList<>();

        //需要将已添加的过滤掉

        ListParams listParams=new ListParams();

        listParams.setPageNum(1);
        listParams.setPageSize(10000);
        listParams.setShopId(Long.parseLong(params.getShopUnique()));
        logger.info("---获取可新增名片人员列表接口参数：" + JSON.toJSONString(listParams));
        String res = HttpUtil.post(JQVersionTwoConfig.getCardList, JSON.toJSONString(listParams));
        logger.info("---获取可新增名片人员列表接口返回值：" + res);
        JSONObject jo = JSONObject.parseObject(res);
        if (jo.containsKey("code") && jo.getString("code").equals("200")) {
            JSONObject data = jo.getJSONObject("data");
            JSONArray rowsJson = data.getJSONArray("rows");
            for (int i = 0; i < personnelList.size(); i++) {
                boolean flag = false;
                for (int j = 0; j < rowsJson.size(); j++) {
                    JSONObject row = rowsJson.getJSONObject(j);
                    if (personnelList.get(i).getUserId().compareTo(row.getLong("userId")) == 0){
                        //已有名片，不添加
                        flag = true;
                        break;
                    }
                }
                if (flag) {
                    continue;
                }
                perResList.add(personnelList.get(i));
            }
        }

        List<Object> rows=new ArrayList<>();
        for (Personnel personnel:perResList){
            rows.add(personnel);
        }
        PageSearchResult result=new PageSearchResult();
        result.setRows(rows);
        result.setTotalCount(perResList.size());
        result.setPageNum(1);
        result.setPageSize(perResList.size());
        return Result.success(result);
    }
}
