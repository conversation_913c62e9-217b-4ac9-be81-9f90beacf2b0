package org.haier.shop.controller;

import java.util.Map;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;

import org.haier.meituan.util.MUtil;
import org.haier.shop.service.DictService;
import org.haier.shop.util.HandleMessyCode;
import org.haier.shop.util.PurResult;
import org.haier.shop.util.ServletsUtil;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

@Controller
@RequestMapping("/dict")
public class DictController {
	@Resource
	private DictService dictService;
	
	
	
	/**
	 * 查询地区管理列表页面
	 * @return
	 */
	@RequestMapping("/queryDictListPage.do")
	public String queryDictListPage(){
		return "/WEB-INF/dict/queryDictList.jsp";
	}
	
	@RequestMapping("/queryFarmKindList.do")
	public String queryFarmKindList() {
		return "/WEB-INF/farm/queryFarmKind.jsp";
	}
	/**
	 * 查询地区管理列表
	 * @return
	 */
	@RequestMapping("/queryDictList.do")
	@ResponseBody
	public PurResult queryDictList(HttpServletRequest request,
			@RequestParam(value="page",defaultValue="1")Integer page,
			@RequestParam(value="limit",defaultValue="15")Integer limit){		
		PurResult result = new PurResult();
		try {
			Map<String, Object> params = HandleMessyCode.handle(ServletsUtil.getParameters(request));
			String area_dict_content = MUtil.strObject(params.get("area_dict_content"));
			System.out.println("---------"+area_dict_content);
			if(area_dict_content != null && !area_dict_content.equals("")){
				params.put("area_dict_content", "%"+area_dict_content+"%");
			}else{
				params.remove("area_dict_content");
			}
			params.put("limit", limit);
			params.put("page", (page-1)*limit);
			result=dictService.queryDictList(params);
		} catch (Exception e) {
			result.setStatus(0);
			result.setData(null);
			result.setMsg("查询失败!");
			e.printStackTrace();
		}		
		return result;
	}
	/**
	 * 添加地区页面
	 * @return
	 */
	@RequestMapping("/addDictPage.do")
	public String addDictPage(){
		return "/WEB-INF/dict/addDict.jsp";
	}
	
	/**
	 * 查询上级地区
	 * @return
	 */
	@RequestMapping("/queryParentDictList.do")
	@ResponseBody
	public PurResult queryParentDictList(HttpServletRequest request
		){		
		PurResult result = new PurResult();
		try {
			Map<String, Object> params = HandleMessyCode.handle(ServletsUtil.getParameters(request));
			result=dictService.queryParentDictList(params);
		} catch (Exception e) {
			result.setStatus(0);
			result.setData(null);
			result.setMsg("查询失败!");
			e.printStackTrace();
		}		
		return result;
	}
	/**
	 * 添加地区
	 * @return
	 */
	@RequestMapping("/addDict.do")
	@ResponseBody
	public PurResult addDict(
			String area_dict_parent_num,
			String area_dict_content,
			String sort
			){		
		PurResult result = new PurResult();
		try {
			result=dictService.addDict(area_dict_parent_num,area_dict_content,sort);
		} catch (Exception e) {
			result.setStatus(0);
			result.setData(null);
			result.setMsg("查询失败!");
			e.printStackTrace();
		}		
		return result;
	}
	/**
	 * 删除地区
	 * @return
	 */
	@RequestMapping("/deleteDict.do")
	@ResponseBody
	public PurResult deleteDict(
			String area_dict_id
			){		
		PurResult result = new PurResult();
		try {
			result=dictService.deleteDict(area_dict_id);
		} catch (Exception e) {
			result.setStatus(0);
			result.setData(null);
			result.setMsg("查询失败!");
			e.printStackTrace();
		}		
		return result;
	}
	
	/**
	 * 修改地区页面
	 * @return
	 */
	@RequestMapping("/queryEditDictPage.do")
	public String queryEditDictPage(String area_dict_id,HttpServletRequest request){
		request.setAttribute("area_dict_id", area_dict_id);
		return "/WEB-INF/dict/editDict.jsp";
	}
	
	/**
	 * 查询地区详情
	 * @return
	 */
	@RequestMapping("/queryDictById.do")
	@ResponseBody
	public PurResult queryDictById(
			String area_dict_id
			){		
		PurResult result = new PurResult();
		try {
			result=dictService.queryDictById(area_dict_id);
		} catch (Exception e) {
			result.setStatus(0);
			result.setData(null);
			result.setMsg("查询失败!");
			e.printStackTrace();
		}		
		return result;
	}
	/**
	 * 修改地区
	 * @return
	 */
	@RequestMapping("/editDict.do")
	@ResponseBody
	public PurResult editDict(
			String area_dict_parent_num,
			String area_dict_content,
			String sort,
			String area_dict_id
			){		
		PurResult result = new PurResult();
		try {
			result=dictService.editDict(area_dict_parent_num,area_dict_content,sort,area_dict_id);
		} catch (Exception e) {
			result.setStatus(0);
			result.setData(null);
			result.setMsg("查询失败!");
			e.printStackTrace();
		}		
		return result;
	}
	
}
