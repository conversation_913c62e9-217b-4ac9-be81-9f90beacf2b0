package org.haier.shop.controller;

import net.sf.json.JSONObject;
import org.apache.commons.lang3.StringUtils;
import org.haier.shop.service.MonitorInfoService;
import org.haier.shop.service.UnicomAbleService;
import org.haier.shop.util.*;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@RequestMapping("/unicomMonitor")
@Controller
public class UnicomMonitorInfoController {
	@Resource
	private MonitorInfoService monitorInfoService;
	//跳转联通能人列表页面
	@RequestMapping(value = "/toListPage.do")
    public String toAbleListPage(){
        return "/WEB-INF/unicom/monitor/list.jsp";
    }
    
	//监控设备列表
    @RequestMapping(value="/queryUnicomMonitorList.do")
	@ResponseBody
	public PurResult queryUnicomMonitorList(
			HttpServletRequest request,
			@RequestParam(value = "page", defaultValue = "0") int page,
			@RequestParam(value = "limit", defaultValue = "8") int pageSize){
    	Map<String, Object> params = HandleMessyCode.handle(ServletsUtil.getParameters(request));
		params.put("pageSize", pageSize);
		params.put("startNum", (page-1)*pageSize);
		
		PurResult result = new PurResult();
		try {
			result = monitorInfoService.queryUnicomMonitorList(params);
		} catch (Exception e) {
			e.printStackTrace();
			result.setStatus(0);
			result.setMsg("异常");
		}
		return result;
	}
    
    //跳转 添加页面
  	@RequestMapping(value = "/toAddPage.do")
      public String toAddAblePage(){
		return "/WEB-INF/unicom/monitor/add.jsp";
      }
      
  	//添加监控设备信息
    @RequestMapping(value="/addMonitorInfo.do")
  	@ResponseBody
  	public PurResult addMonitorInfo(HttpServletRequest request){
      	Map<String, Object> params = HandleMessyCode.handle(ServletsUtil.getParameters(request));
  		PurResult result = new PurResult();
		  if(StringUtils.isEmpty(params.get("serial_number").toString())){
			  result.setStatus(0);
			  result.setMsg("设备序列号不能为空");
			  return result;
		  }
  		try {
  			result = monitorInfoService.addMonitorInfo(params);
  		} catch (Exception e) {
  			e.printStackTrace();
  			result.setStatus(0);
  			result.setMsg("异常");
  		}
  		return result;
  	}
      
    //跳转联通能人修改页面
  	@RequestMapping(value = "/toUpdatePage.do")
    public String toUpdateAblePage(String monitor_info_id,Model model){
  		//查询设备详细信息
  		Map<String ,Object> params = new HashMap<String ,Object>();
  		params.put("monitor_info_id", monitor_info_id);
  		Map<String ,Object> detail = monitorInfoService.selectMonitorInfoByMonitorInfoId(Long.parseLong(monitor_info_id));
  		model.addAttribute("ableDetail", detail);
        return "/WEB-INF/unicom/monitor/update.jsp";
    }

	@RequestMapping(value = "/stop.do")
	@ResponseBody
	public PurResult stopOrStart(String monitor_info_id,String state){
		//查询设备详细信息
		Map<String ,Object> params = new HashMap<String ,Object>();
		params.put("monitor_info_id", monitor_info_id);
		params.put("state",state);
		PurResult result = new PurResult();
		try {
			result = monitorInfoService.updateMonitorInfo(params);
		} catch (Exception e) {
			e.printStackTrace();
			result.setStatus(0);
			result.setMsg("异常");
		}
		return result;
	}
      
  	//修改监控设备信息
    @RequestMapping(value="/updateMonitorInfo.do")
  	@ResponseBody
  	public PurResult updateMonitorInfo(HttpServletRequest request){
      	Map<String, Object> params = HandleMessyCode.handle(ServletsUtil.getParameters(request));
  		PurResult result = new PurResult();
  		try {
  			result = monitorInfoService.updateMonitorInfo(params);
  		} catch (Exception e) {
  			e.printStackTrace();
  			result.setStatus(0);
  			result.setMsg("异常");
  		}
  		return result;
  	}

	@RequestMapping(value="/lookUrl.do")
	@ResponseBody
	public PurResult lookUrl(String serial_number,String protocol){
		PurResult result = new PurResult();
		//获取 accessToken
		Map<String,Object> paramMap1 =new HashMap<>();
		paramMap1.put("appKey", Load.YSAPPID);
		paramMap1.put("appSecret",Load.YSSECRET);
		try {
		JSONObject resToken = HttpsUtil.doPostJson("https://open.ys7.com/api/lapp/token/get", paramMap1);
		if(!isSuccess(resToken,result)){
			return  result;
		}
		JSONObject resTokenData = resToken.getJSONObject("data");
		String accessToken = resTokenData.getString("accessToken");
		//获取URL
		Map<String,Object> paramMap2 =new HashMap<>();
		paramMap2.put("deviceSerial",serial_number); //直播源，例如427734222，均采用英文符号，限制50个
		paramMap2.put("accessToken",accessToken); //授权过程获取的access_token
		paramMap2.put("channelNo",1); //通道号,，非必选，默认为1
		paramMap2.put("protocol",protocol);//流播放协议，1-ezopen、2-hls、3-rtmp、4-flv，默认为1
		paramMap2.put("quality",2); //视频清晰度，1-高清（主码流）、2-流畅（子码流）
		JSONObject resUrljson = HttpsUtil.doPostJson("https://open.ys7.com/api/lapp/v2/live/address/get", paramMap2);
		if(!isSuccess(resUrljson,result)){
			return  result;
		}
		JSONObject resUrlData = resUrljson.getJSONObject("data");
		String returnUrl = resUrlData.getString("url");
		result.setStatus(1);
		result.setMsg("成功！");
		result.setData(returnUrl);
		} catch (Exception e) {
			e.printStackTrace();
			result.setStatus(0);
			result.setMsg("异常！！！请核对【设备序列号】");
		}
	    return  result;
	}

	@RequestMapping("/getSixMonitor.do")
	@ResponseBody
	public PurResult getSixMonitor() {
		PurResult pr = new PurResult(1,"查询成功!");
		//获取可用的6个摄像头，并展示在页面上
		List<Map<String,Object>> list = monitorInfoService.showSixMonitor();
		
		//获取 accessToken
		Map<String,Object> paramMap1 =new HashMap<>();
		paramMap1.put("appKey", Load.YSAPPID);
		paramMap1.put("appSecret",Load.YSSECRET);
		
		List<Map<String,String>> resList = new ArrayList<Map<String,String>>();
		try {
			JSONObject resToken = HttpsUtil.doPostJson("https://open.ys7.com/api/lapp/token/get", paramMap1);
			JSONObject resTokenData = resToken.getJSONObject("data");
			String accessToken = resTokenData.getString("accessToken");
			//获取URL
			for(Integer i = 0; i < list.size(); i++) {
				String serial_number = list.get(i).get("serial_number").toString();
				
				Map<String ,String> retuenMap =new HashMap<>();
				
				Map<String,Object> paramMap2 =new HashMap<>();
				paramMap2.put("deviceSerial",serial_number); //直播源，例如427734222，均采用英文符号，限制50个
				paramMap2.put("accessToken",accessToken); //授权过程获取的access_token
				paramMap2.put("channelNo",1); //通道号,，非必选，默认为1
				paramMap2.put("protocol",1);//流播放协议，1-ezopen、2-hls、3-rtmp、4-flv，默认为1
				paramMap2.put("quality",2); //视频清晰度，1-高清（主码流）、2-流畅（子码流）
				JSONObject resUrljson = HttpsUtil.doPostJson("https://open.ys7.com/api/lapp/v2/live/address/get", paramMap2);
				JSONObject resUrlData = resUrljson.getJSONObject("data");
				String returnUrl = resUrlData.getString("url");
				retuenMap.put("url",returnUrl);
				retuenMap.put("accessToken",accessToken);
				
				resList.add(retuenMap);
			}
		} catch (Exception e) {
			e.printStackTrace();
		}
		
		pr.setData(resList);
		
		return pr;
	}
	
	@RequestMapping("/showSixMonitor.do")
	public String showSixMonitor(Model model) {
		
		
		return "/WEB-INF/unicom/monitor/liveSix.jsp";
	}

	@RequestMapping(value="/toLivePage.do")
	public String live(String serial_number,String protocol,Model model){
		//获取 accessToken
		Map<String,Object> paramMap1 =new HashMap<>();
		paramMap1.put("appKey", Load.YSAPPID);
		paramMap1.put("appSecret",Load.YSSECRET);
		Map<String ,String> retuenMap =new HashMap<>();
		try {
			JSONObject resToken = HttpsUtil.doPostJson("https://open.ys7.com/api/lapp/token/get", paramMap1);
			JSONObject resTokenData = resToken.getJSONObject("data");
			String accessToken = resTokenData.getString("accessToken");
			//获取URL
			Map<String,Object> paramMap2 =new HashMap<>();
			paramMap2.put("deviceSerial",serial_number); //直播源，例如427734222，均采用英文符号，限制50个
			paramMap2.put("accessToken",accessToken); //授权过程获取的access_token
			paramMap2.put("channelNo",1); //通道号,，非必选，默认为1
			paramMap2.put("protocol",protocol);//流播放协议，1-ezopen、2-hls、3-rtmp、4-flv，默认为1
			paramMap2.put("quality",2); //视频清晰度，1-高清（主码流）、2-流畅（子码流）
			JSONObject resUrljson = HttpsUtil.doPostJson("https://open.ys7.com/api/lapp/v2/live/address/get", paramMap2);
			JSONObject resUrlData = resUrljson.getJSONObject("data");
			System.out.println(resUrlData);
			String returnUrl = resUrlData.getString("url");
			retuenMap.put("url",returnUrl);
			retuenMap.put("accessToken",accessToken);
		} catch (Exception e) {
			e.printStackTrace();
			return "/error";
		}
		model.addAttribute("laveData", retuenMap);
		return "/WEB-INF/unicom/monitor/live.jsp";
	}


	public Boolean isSuccess(JSONObject json,PurResult result){
		String code = json.getString("code");
		if(!code.equals("200")){
			result.setStatus(0);
			result.setMsg("异常【"+json.getString("msg")+"】");
			if(code.equals("20018")){
				result.setMsg("该用户不拥有该设备,请核对【设备序列号】");
			}
			return false;
		}
		return true;
	}
      


}
