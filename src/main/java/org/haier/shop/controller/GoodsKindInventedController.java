package org.haier.shop.controller;

import java.util.List;
import java.util.Map;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;

import org.haier.log.annotation.RemoteLog;
import org.haier.log.enums.BusinessType;
import org.haier.shop.service.GoodsKindInventedService;
import org.haier.shop.util.ShopsResult;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.servlet.ModelAndView;

import com.alibaba.fastjson.JSONArray;

/**
* @author: 作者:王恩龙
* @version: 2023年3月29日 下午5:40:03
*
*/

@Controller
@RequestMapping("/goodsKindInvented")
public class GoodsKindInventedController {

	@Resource
	private GoodsKindInventedService inventedService;
	
	/**
	 * 批量更新虚拟分类排序
	 * @param list<Map<String,Object>> goodsKindInventedId：虚拟分类ID;kindSort:虚拟分类排序
	 * @return
	 */
	@RequestMapping("/modifyGoodsKindInventedSort.do")
	@ResponseBody
	public ShopsResult modifyGoodsKindInventedSort(String list) {
		JSONArray ja = JSONArray.parseArray(list);
		return inventedService.modifyGoodsKindInventedSort(ja);
	}
	
	/**
	 * 删除指定虚拟分类下的商品或删除指定ID的虚拟分类下商品
	 * @param goods_kind_invented_id
	 * @param goods_kind_invented_goods_id
	 * @return
	 */
	@RequestMapping("/deleteGoodsKindInventedGoods.do")
	@ResponseBody
	public ShopsResult deleteGoodsKindInventedGoods(Integer goods_kind_invented_id , Integer goods_kind_invented_goods_id) {
		return inventedService.deleteGoodsKindInventedGoods(goods_kind_invented_id, goods_kind_invented_goods_id);
	}
	/**
	 * 查询店铺商品信息，排除指定虚拟分类
	 * @param shop_unique 店铺编号
	 * @param goods_kind_invented_id 虚拟分类ID
	 * @param page 页码
	 * @param limit 单页查询数量
	 * @param search_name 输入框搜索内容
	 * @return
	 */
	@RequestMapping("/queryGoodsList.do")
	@ResponseBody
	public ShopsResult queryGoodsList(String shop_unique, Integer goods_kind_invented_id, Integer page ,Integer limit, String search_name,String barcodeList,String parUnique,String kindUnique) {
		return inventedService.queryGoodsList(shop_unique, goods_kind_invented_id, page, limit, search_name,barcodeList,parUnique,kindUnique);
	}
	/**
	 * 跳转添加商品页面
	 * @return
	 */
	@RequestMapping("/toAddGoodsMsgPage.do")
	public String toAddGoodsMsgPage() {
		return "/WEB-INF/goods/addGoodsMsgPage.jsp";
	}
	/**
	 * 添加或更新虚拟分类信息
	 * @param goods_kind_invented_id
	 * @param goods_kind_unique
	 * @param goods_kind_name
	 * @param goods_list
	 * @param shop_unique
	 * @param valid_type
	 * @return
	 */
	@RemoteLog(title="添加或更新虚拟分类信息", businessType = BusinessType.UPDATE)
	@RequestMapping("/addGoodsKindInvented.do")
	@ResponseBody
	public ShopsResult addGoodsKindInvented(Integer goods_kind_invented_id,Integer goods_kind_unique,String goods_kind_name,String goods_list,String shop_unique,Integer valid_type) {
		return inventedService.addGoodsKindInvented(goods_kind_invented_id, goods_kind_unique, goods_kind_name, goods_list, shop_unique,valid_type);
	}
	/**
	 * 查询虚拟分类下的商品列表信息
	 * @param goods_kind_invented_id 虚拟分类ID
	 * @param goods_kind_unique 虚拟分类编号
	 * @return
	 */
	@RequestMapping("/queryGoodsKindInventedGoodsList.do")
	@ResponseBody
	public ShopsResult queryGoodsKindInventedGoodsList(Integer goods_kind_invented_id,String goods_kind_unique,Integer page,Integer limit) {
		return inventedService.queryGoodsKindInventedGoodsList(goods_kind_invented_id, goods_kind_unique,page,limit);
	}
	
	@RequestMapping("/goodsKindInventedPage.do")
	public String goodsKindInventedPage() {
		return "/WEB-INF/goods/goodsKindInventedPage.jsp";
	}
	
	@RequestMapping("/goodsKindInventedDetail.do")
	public String goodsKindInventedDetail(Integer goods_kind_invented_id ,String goods_kind_unique,String goods_kind_name,ModelAndView modelAndView) {
		modelAndView.addObject("goods_kind_invented_id", goods_kind_invented_id);
		modelAndView.addObject("goods_kind_unique",goods_kind_unique);
		modelAndView.addObject("goods_kind_name", goods_kind_name);
		return "/WEB-INF/goods/goodsKindInventedDetail.jsp";
	}
	
	/**
	 * 查询店铺内虚拟分类信息
	 * @param shop_unique 店铺编号
	 * @param page 页码
	 * @param pageSize 单页查询数量
	 * @return
	 */
	@RequestMapping("/queryGoodsKindInventedList.do")
	@ResponseBody
	public ShopsResult queryGoodsKindInventedList(String shop_unique ,
			String search_name,
			@RequestParam(value="page", defaultValue="1") Integer page, 
			@RequestParam(value="limit", defaultValue="10")Integer pageSize) {
		return inventedService.queryGoodsKindInventedList(shop_unique,search_name, page, pageSize);
	}
}
