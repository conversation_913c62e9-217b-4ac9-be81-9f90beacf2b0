package org.haier.shop.controller;

import java.io.File;
import java.io.FileInputStream;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;
import javax.servlet.http.HttpServlet;
import javax.servlet.http.HttpServletRequest;

import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.haier.customer.entity.Shops;
import org.haier.shop.service.UtilService;
import org.haier.shop.util.HandleMessyCode;
import org.haier.shop.util.PurResult;
import org.haier.shop.util.ServletsUtil;
import org.haier.shop.util.ShopsResult;
import org.haier.shop.util.ShopsUtil;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

@Controller
@RequestMapping("/util")
public class UtilController {
	@Resource
	private UtilService utilService;
	
	@RequestMapping("/getRequestMsg.do")
	@ResponseBody
	public ShopsResult getRequestMsg(HttpServletRequest request) {
		ShopsResult sr = new ShopsResult();
		
		Map<String,Object> map = new HashMap<String,Object>();
		map.put("getSchema", request.getScheme());
		map.put("getServerName", request.getServerName());
		map.put("getServerPort", request.getServerPort());
		map.put("getServletPath", request.getServletPath());
		map.put("getContextPath", request.getContextPath());
		map.put("getServletContext().getContextPath()", request.getServletContext().getContextPath());
		map.put("", "");
		map.put("", "");
		map.put("", "");
		map.put("", "");
		map.put("", "");
		map.put("", "");
		sr.setData(map);
		
		return sr;
	}
	
	@RequestMapping("/toPayImageList.do")
	public String toPayImageList() {
		return "/WEB-INF/util/toQueryPayImage.jsp";
	}
	/**
	 * 查询支付文件列表
	 * @param startTime
	 * @param endTime
	 * @param page
	 * @param limit
	 * @return
	 */
	@RequestMapping("/queryPayimageList.do")
	@ResponseBody
	public ShopsResult queryPayimageList(String startTime , String endTime , Integer page, Integer limit) {
		return utilService.queryPayimageList(startTime, endTime, page, limit);
	}
	
	@RequestMapping("/uploadImage.do")
	@ResponseBody
	public ShopsResult uploadImage(HttpServletRequest request) {
		return utilService.uploadImage(request);
	}
	
	@RequestMapping("/toUploadImage.do")
	public String toUploadImage() {
		return "/WEB-INF/util/uploadImage.jsp";
	}
	
	@RequestMapping("/addNewShopYN.do")
	@ResponseBody
	public ShopsResult addNewShopYN() {
		return utilService.addNewShopYN();
	}
	
	@RequestMapping("/updateKindUnqualified.do")
	@ResponseBody
	public ShopsResult 	updateKindUnqualified(){
		return utilService.updateKindUnqualified();
	}
	
	@RequestMapping("/addGoodsGoods.do")
	@ResponseBody
	public ShopsResult addGoodsGoods(
			@RequestParam(value="shopUnique")String shopUnique){
		return utilService.addGoodsGoods(shopUnique);
	}
	
	@RequestMapping("/updateForeign.do")
	@ResponseBody
	public ShopsResult updateForeign(String shopUnique){
		return utilService.updateForeign(shopUnique);
	}
	@RequestMapping("/queryNoUseGoods.do")
	@ResponseBody
	public ShopsResult queryNoUseGoods(
			@RequestParam(value="shopUnique")String shopUnique){
		return utilService.queryNoUseGoods(shopUnique);
	}
	
	@RequestMapping("/copyGoodsToNewShop.do")
	@ResponseBody
	public ShopsResult copyGoodsToNewShop(
			@RequestParam(value="newShopUnique")String newShopUnique,
			@RequestParam(value="shopUnique")String shopUnique
			){
		Map<String,Object> map=new HashMap<String, Object>();
		map.put("shopUnique", shopUnique);
		map.put("newShopUnique", newShopUnique);
		return utilService.copyGoodsToNewShop(map);
	}
	
	/**
	 * 更新用于更新PC收银端软件的新消息
	 * @return
	 */
	@RequestMapping("/updatePcFile.do")
	@ResponseBody
	public ShopsResult updatePcFile(){
		return utilService.updatePcFile();
	}
	
	@RequestMapping("/updatePcFileTest.do")
	@ResponseBody
	public ShopsResult updatePcFileTest(){
		return utilService.updatePcFileTest();
	}
	/**
	 * 历史版本信息查询
	 * @return
	 */
	@RequestMapping("/queryHistoricalVersion.do")
	@ResponseBody
	public ShopsResult queryHistoricalVersion(){
		return utilService.queryHistoricalVersion();
	}
	
	/**
	 * 店铺总数量查询
	 * @return
	 */
	@RequestMapping("/queryShopsCount.do")
	@ResponseBody	
	public ShopsResult queryShopsCount(
			String shopsMessage,
			@RequestParam(value="pageSize",defaultValue="8")Integer pageSize,
			Integer handleStatus,
			String xversionNumber
			){
		Map<String,Object> map=new HashMap<String, Object>();
		map.put("shopsMessage", shopsMessage);
		map.put("pageSize", pageSize);
		map.put("handleStatus", handleStatus);
		map.put("xversionNumber", xversionNumber);
		return utilService.queryShopsCount(map);
	}
	
	/**
	 * 升级管理
	 */
	@RequestMapping("/queryShopsMessage.do")
	@ResponseBody
	public PurResult queryShopsMessage(HttpServletRequest request,
			@RequestParam(value="page",defaultValue="1")Integer page,
			@RequestParam(value="limit",defaultValue="15")Integer limit){		
		PurResult result = new PurResult();
		try {
			Map<String, Object> params = HandleMessyCode.handle(ServletsUtil.getParameters(request));
			params.put("limit", limit);
//			params.put("page", ((page-1)%32)*limit);//shopTest
			params.put("page", (page-1)*limit);
			result=utilService.queryShopsMessage(params);
		} catch (Exception e) {
			result.setStatus(0);
			result.setMsg("查询失败!");
			e.printStackTrace();
		}		
		return result;
	}

	/**
	 * 批量设置更新状态
	 * @param ids
	 * @return
	 */
	@RequestMapping("/modifyShopsVersion.do")
	@ResponseBody
	public ShopsResult modifyShopsVersion(
			String ids,
			@RequestParam(value="handleStatus",defaultValue="1")Integer handleStatus){
		Map<String,Object> map=new HashMap<String, Object>();
		map.put("handleStatus", handleStatus);
		String[] idArr=ids.split(";");
		List<String> idList=new ArrayList<String>();
		for(String a:idArr){
			idList.add(a);
		}
		map.put("ids", idList);
		System.out.println(map);
		return utilService.modifyShopsVersion(map);
	}
	
	@RequestMapping("/addTitle.do")
	@ResponseBody
	public ShopsResult test(){
		return utilService.test();
	}
	
	@RequestMapping("/modifyGoodsPic.do")
	@ResponseBody
	public ShopsResult modifyGoodsPic(String shopUnique){
		return utilService.modifyGoodsPic(shopUnique);
	}
	
	@RequestMapping("/addNewGoodsAll.do")
	@ResponseBody
	public ShopsResult addNewGoodsAll(){
		return utilService.addNewGoodsAll();
	}
	
	@RequestMapping("/modifyNullList.do")
	@ResponseBody
	public ShopsResult modifyNullList(){
		return utilService.modifyNullList();
	}
	
	@RequestMapping("/deleteListDetail.do")
	@ResponseBody
	public ShopsResult deleteListDetail(){
		return utilService.deleteListDetail();
	}
	
	/**
	 * 添加自定义分类98001
	 * @return
	 */
	@RequestMapping("/addSelfKind.do")
	@ResponseBody
	public ShopsResult addSelfKind(){
		return utilService.addSelfKind();
	}
	
	/**
	 * 更新会员的会员别名
	 * @param shopUnique
	 * @return
	 */
	@RequestMapping("/updateCusAlias.do")
	@ResponseBody
	public ShopsResult updateCusAlias(String shopUnique){
		Map<String,Object> map = new HashMap<String,Object>();
		map.put("shopUnique", shopUnique);
		return utilService.updateCusAlias(map);
	}
	
	@RequestMapping("/clearPic.do")
	@ResponseBody
	public ShopsResult clearPic(String shopUnique) {
		ShopsUtil.clearPic(shopUnique);
		return new ShopsResult(1,"成功!");
	}
	
	/**
	 * 根据文件批量注册店铺信息
	 * @return
	 */
	public ShopsResult registerShopsList() {
		try {
			/*
			 * 1、读取文件信息；
			 */
			String filePath = "";
			String sheetName = "shops";
			
			/*
			 * 以下信息需要向service层传参
			 */
			String shop_name = null;
			String manager_account = null;
			String manager_pwd = null;
			String shop_address_detail =null;
			String shop_phone = null;
			Integer examinestatus = 4;
			//店铺分类：0普通商家；1：连锁；2加盟 3系统平台 4普通商家分店 5代理商店铺  
			Integer shop_class = 0;
			
			String company_code = null;
			Integer is_other_purchase = null;
			String area_dict_num;
			String shop_latitude;
			String shop_longitude;
			String province = null;
			String city =null;
			String district = null;
			//店铺类型:1、便利店；2、水果店；3、母婴店；4、益农中心站；5、益农标准站、简易站、专业站；0：其他
			Integer shop_type = 1;
			
			
			File file = new File(filePath);
			if(!file.exists()) {
				return new ShopsResult(1,"文件信息不存在");
			}
			String[] fileNames=file.getName().split("\\.");
			if(!fileNames[fileNames.length-1].equals(".xls")) {
				return new ShopsResult(1,"文件格式不对");
			}
			
			Workbook w=null;
			FileInputStream fis=null;
			fis = new FileInputStream(file);
			w=new HSSFWorkbook(fis);
			
			
			//获取sheet文件内容
			Sheet sheet=w.getSheet(sheetName);
			if(null == sheet) {
				return new ShopsResult(1, "表格信息不存在");
			}
			
			int rs=sheet.getFirstRowNum();//第一行标题行
			int re=sheet.getLastRowNum();//最后一行
			if(re < rs) {
				return new ShopsResult(1,"无数据");
			}
			//获取列区域范围
			int ls=sheet.getRow(0).getFirstCellNum();
			int le=sheet.getRow(0).getLastCellNum();
			
			if(le < ls) {
				return new ShopsResult(1,"无数据");
			}
			
			//获取文件标题内容
			Row bt=sheet.getRow(0);
			//从第一个开始
			rs = rs++;
			
			
			
			
			
			
			return new ShopsResult();
		}catch (Exception e) {
			return null;
		}
	}
}
