package org.haier.shop.controller;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONUtil;
import org.haier.log.annotation.RemoteLog;
import org.haier.log.enums.BusinessType;
import org.haier.shop.entity.publicEntity.PageSearch;
import org.haier.shop.params.SetSubAccountParams;
import org.haier.shop.service.GoodsService;
import org.haier.shop.service.PayTypeService;
import org.haier.shop.util.ShopsResult;
import org.haier.shop.util.UtilForJAVA;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

@Controller
@RequestMapping("/payType")
public class PayTypeController {

	private static final Logger log = LoggerFactory.getLogger(PayTypeController.class);
	@Resource
	private PayTypeService payTypeService;
	
	@Resource
	private GoodsService goodsService;
	
	@RequestMapping("/downloadYiTongShopMsgData.do")
	public void downloadYiTongShopMsgData(
			String shopMsg,Integer examineStatus,String startTime,String endTime
			,HttpServletRequest request,HttpServletResponse response
			) {
		//获取需要下载的信息
		payTypeService.downloadYiTongShopMsgData(shopMsg, examineStatus, startTime, endTime, request, response);
	}
	/**
	 * 修改支付状态
	 * @param id
	 * @param status
	 * @param examineStatus
	 * @return
	 */
	@RequestMapping("/modifyShopPayMsg.do")
	@ResponseBody
	public ShopsResult modifyShopPayMsg(Integer id, Integer examineStatus , String examineRemarks) {
		try {
			return payTypeService.modifyShopPayMsg(id, examineStatus, examineRemarks);
		} catch (Exception e) {
			return new ShopsResult(0, "修改失败！");
		}
	}
	/**
	 * 获取店铺支付信息提交审核的所有审核状态
	 * @return
	 */
	@RequestMapping("/queryShopPayMsgExamineStatus.do")
	@ResponseBody
	public ShopsResult queryShopPayMsgExamineStatus() {
		try {
			return payTypeService.queryShopPayMsgExamineStatus();
		}catch (Exception e) {
			return new ShopsResult(0, "查询失败！");
		}
	}

	/**
	 * 分页查询已有的审核资料信息
	 * @param shopUnique
	 * @return
	 */
	@RequestMapping("/queryShopPayMsgByPage.do")
	@ResponseBody
	public ShopsResult queryShopPayMsgByPage(
			String shopMsg,
			String startTime,
			String endTime,
			Integer examinStatus,
			Integer page,
			Integer limit
			) {
		try {
			Map<String,Object> map = new HashMap<String,Object>();
			PageSearch pageSearch = new PageSearch();
			if(null != shopMsg && !shopMsg.equals("")) {
				map.put("shopMsg", "%"+shopMsg+"%");
			}
			map.put("startTime", startTime);
			map.put("endTime", endTime);
			map.put("examinStatus", examinStatus);
			
			pageSearch.setPageNum(page);
			pageSearch.setPageSize(limit);
			return payTypeService.queryShopPayMsgByPage(map, pageSearch);
		}catch (Exception e) {
			e.printStackTrace();
			return new ShopsResult(0, "查询失败");
		}
	}
	
	/**
	 * 更新店铺的支付信息
	 * @param id
	 * @param mchId
	 * @param mchKey
	 * @param defaultType
	 * @param shopUnique
	 * @param payType
	 * @return
	 */
	@RemoteLog(title = "新增店铺支付信息", businessType = BusinessType.INSERT)
	@RequestMapping("/saveShopPayType.do")
	@ResponseBody
	public ShopsResult saveShopPayType(
			String id,
			String mchId,
			String mchKey,
			String defaultType,
			@RequestParam(required=true)String shopUnique,
			@RequestParam(value="payType" ,defaultValue = "3")String payType) {
		try {
			return payTypeService.saveShopPayType(id, mchId, mchKey, defaultType, shopUnique, payType);
		} catch (Exception e) {
			return new ShopsResult(0, "保存失败！");
		}
	}
	/**
	 * 查询当前易通支付信息
	 * @return
	 */
	@ResponseBody
	@RequestMapping("/queryShopYiTongPayMsg.do")
	public ShopsResult queryShopYiTongPayMsg(String shopUnique) {
		try {
			return payTypeService.queryShopYiTongPayMsg(shopUnique);
		}catch (Exception e) {
			e.printStackTrace();
			return new ShopsResult(0, "查询失败！");
		}
	}
	/**
	 * 获取当前店铺的有效支付信息
	 * @param shopUnique
	 * @return
	 */
	@RequestMapping("/getShopPayMsg.do")
	@ResponseBody
	public ShopsResult getShopPayMsg(String shopUnique) {
		try {
			return payTypeService.getShopPayMsg(shopUnique);
		}catch (Exception e) {
			e.printStackTrace();
			return new ShopsResult(0,"查询失败！");
		}
	}

	@RemoteLog(title = "支付方式上传资质", businessType = BusinessType.UPDATE)
	@RequestMapping("/saveShopPayMsg.do")
	@ResponseBody
	public ShopsResult saveShopPayMsg(
			HttpServletRequest request,
			String shopUnique,
			String legalPerson,
			String userPhone,
			Integer id,
			String shopAddress,
			String email,
			String subBranch
			) {
		try {
			return payTypeService.addNewShopPayMsg(request, shopUnique, legalPerson, userPhone,id,shopAddress,email,subBranch);
		}catch (Exception e) {
			e.printStackTrace();
			return new ShopsResult(0, "保存失败！");
		}
	}
	
	@RequestMapping("/shopPayMsgList.do")
	public String shopPayMsgList() {
		return "/WEB-INF/sys/payType/shopPayMsgList.jsp";
	}
	/**
	 * 跳转平台支付管理页面
	 * @return
	 */
	@RequestMapping("/payType.do")
	public String payType() {
		return "/WEB-INF/sys/payType/payType.jsp";
	}
	
	@RequestMapping("/toEditPayData.do")
	public String editPayData() {
		return "/WEB-INF/sys/payType/editPayData.jsp";
	}
	
	@RequestMapping("/toEditPayType.do")
	public String toEditPayType() {
		return "/WEB-INF/sys/payType/editPayType.jsp";
	}
	/**
	 * 跳转店铺支付管理页面
	 * @return
	 */
	@RequestMapping("/payTypeManager.do")
	public String payTypeManager() {
		return "/WEB-INF/sys/payType/payTypeManager.jsp";
	}
	
	/**
	 * 查询店铺内所有的支付方式
	 * @param map
	 * @return
	 */
	@RequestMapping("/getPayType.do")
	@ResponseBody
	public ShopsResult getPayType(String shopUnique) {
		try {
			Map<String,Object> map = new HashMap<String, Object>();
			map.put("shopUnique", shopUnique);
			return payTypeService.getPayType(map);
		} catch (Exception e) {
			e.printStackTrace();
			ShopsResult sr = new ShopsResult(0,"查询失败！");
			return sr;
		}
	}
	
	/**
	 * 更新店铺的支付信息
	 * @param map
	 * @return
	 */
	@RemoteLog(title = "修改店铺支付信息", businessType = BusinessType.UPDATE)
	@RequestMapping("/modiyfPayType.do")
	@ResponseBody
	public ShopsResult modiyfPayType(
			@RequestParam(value="shopUnique",required=true)String shopUnique
			,Integer defaultType
			,Integer appDefaultType
			,Integer validType
			,Integer appValidType
			,Double rate
			,Double appRate
			,String otherSet
			,String mchId
			,String mchKey
			,Integer id
			,HttpServletRequest request
			,HttpServletResponse response
			) {
		try {
			Map<String,Object> map = UtilForJAVA.getParameters(request);
			return payTypeService.modiyfPayType(map);
		} catch (Exception e) {
			e.printStackTrace();
			ShopsResult sr = new ShopsResult(0,"失败！");
			return sr;
		}
	}
	

	/**
	 * 查询平台所有支付方式
	 * @param map
	 * @return
	 */
	@RequestMapping("/queryPlatPayType.do")
	@ResponseBody
	public ShopsResult queryPlatPayType(HttpServletRequest request,
			HttpServletResponse response
			) {
		try {
			Map<String,Object> map = UtilForJAVA.getParameters(request);
			return payTypeService.queryPlatPayType(map);
		} catch (Exception e) {
			e.printStackTrace();
			ShopsResult sr = new ShopsResult(0,"查询失败！");
			return sr;
		}
	}
	
	/**
	 * 修改支付方式的信息
	 * @param request
	 * @param response
	 * @param id
	 * @return
	 */
	@RequestMapping("/modifyPlatPayType.do")
	@ResponseBody
	public ShopsResult modifyPlatPayType(
			HttpServletRequest request
			,HttpServletResponse response
			,@RequestParam(required=true)Integer id
			) {
		try {
			Map<String,Object> map = UtilForJAVA.getParameters(request);
			return payTypeService.modifyPlatPayType(map);
		} catch (Exception e) {
			e.printStackTrace();
			ShopsResult sr = new ShopsResult(0,"更新失败！");
			return sr;
		}
	}
	
	/**
	 * 跳转该支付方式所有店铺列表
	 * @return
	 */
	@RequestMapping("/payTypeShopListPage.do")
	public String payTypeShopListPage(String pay_type,String pay_type_name,Model model) {
		model.addAttribute("pay_type", pay_type);
		model.addAttribute("pay_type_name", pay_type_name);
		return "/WEB-INF/sys/payType/payTypeShopList.jsp";
	}
	
	/**
	 * 查询该支付方式所有店铺列表
	 * @param pay_type 支付方式
	 * @param shop_message 店铺名称/店铺编号
	 * @return
	 */
	@RequestMapping("/queryPayTypeShopList.do")
	@ResponseBody
	public ShopsResult queryPayTypeShopList(HttpServletRequest request,
			HttpServletResponse response
			) {
		try {
			Map<String,Object> map = UtilForJAVA.getParameters(request);
			return payTypeService.queryPayTypeShopList(map);
		} catch (Exception e) {
			e.printStackTrace();
			ShopsResult sr = new ShopsResult(0,"查询失败！");
			return sr;
		}
	}
	
	/**
	 * 跳转添加店铺支付方式页面
	 * @return
	 */
	@RequestMapping("/addShopPayTypePage.do")
	public String addShopPayTypePage(String pay_type,String pay_type_name,Model model) {
		model.addAttribute("pay_type", pay_type);
		model.addAttribute("pay_type_name", pay_type_name);
		//获取所有店铺列表
		List<Map<String ,Object>> shopList = goodsService.getAllShopList();
		model.addAttribute("shopList", shopList);
		return "/WEB-INF/sys/payType/addShopPayType.jsp";
	}
	
	/**
	 * 添加店铺支付方式
	 * @return
	 */
	@RequestMapping("/addShopPayType.do")
	@ResponseBody
	public ShopsResult addShopPayType(HttpServletRequest request,
			HttpServletResponse response
			) {
		try {
			Map<String,Object> map = UtilForJAVA.getParameters(request);
			return payTypeService.addShopPayType(map);
		} catch (Exception e) {
			e.printStackTrace();
			ShopsResult sr = new ShopsResult(0,"失败！");
			return sr;
		}
	}
	
	/**
	 * 跳转修改店铺支付方式页面
	 * @return
	 */
	@RequestMapping("/updateShopPayTypePage.do")
	public String updateShopPayTypePage(String shop_pay_type_id,String pay_type_name,String shop_name,Model model) {
		//获取店铺支付方式详情
		Map<String ,Object> shopPayType = payTypeService.queryShopPayType(shop_pay_type_id);
		model.addAttribute("shopPayType", shopPayType);
		model.addAttribute("pay_type_name", pay_type_name);
		model.addAttribute("shop_name", shop_name);
		return "/WEB-INF/sys/payType/updateShopPayType.jsp";
	}
	
	/**
	 * 跳转该店铺所有支付方式列表
	 * @return
	 */
	@RequestMapping("/queryShopPayTypeListPage.do")
	public String queryShopPayTypeListPage(String shop_unique,Model model) {
		model.addAttribute("shop_unique", shop_unique);
		return "/WEB-INF/sys/payType/shopPayTypeList.jsp";
	}

	/**
	 * 设置子账簿
	 * @return
	 */
	@RequestMapping("/setSubAccount.do")
	@ResponseBody
	public String setSubAccount(@RequestBody SetSubAccountParams params) {
		log.info("设置子账簿入参:{}", JSONUtil.toJsonStr(params));
		if(ObjectUtil.hasEmpty(params,params.getSubAccount(),params.getShopUnique())) {
			return "error";
		}
		payTypeService.setSubAccount(params);
		return "success";
	}
}
