package org.haier.shop.controller;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.servlet.http.HttpServletRequest;

import org.haier.shop.entity.SysPermission;
import org.haier.shop.service.SysMenuService;
import org.haier.shop.util.PurResult;
import org.haier.shop.util.ServletsUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

@Controller
@RequestMapping("/sys/menu")
public class SysMenuController {
    private static final Logger logger = LoggerFactory.getLogger(SysMenuController.class);
    
    @Autowired
    private SysMenuService menuService;
    
    @RequestMapping(value = "/getMenuList.do")
    public String getMenuList(Model model){
    	logger.info("后台管理-获取菜单列表");
        return "/WEB-INF/sys/permission/permissionList.jsp";
    }
    
    @RequestMapping(value = "/add.do")
    public String add(){
    	logger.info("后台管理-打开添加菜单页面");
        return "/WEB-INF/sys/permission/addPermission.jsp";
    }
    
    @RequestMapping(value="/queryList.do")
	@ResponseBody
	public PurResult queryList(
			HttpServletRequest request,
			@RequestParam(value = "page", defaultValue = "0") int page,
			@RequestParam(value = "limit", defaultValue = "8") int pageSize){
    	Map<String, Object> params = ServletsUtil.getParameters(request);
		params.put("limit", pageSize);
		params.put("page", (page-1)*pageSize);
		
		PurResult result = new PurResult();
		try {
			List<SysPermission> list = menuService.quertMenuList(params);
	    	Integer count = menuService.quertMenuListCount(params);
	    	result.setStatus(1);
			result.setMsg("成功");
			result.setCount(count);
			result.setData(list);
		} catch (Exception e) {
			logger.error("列表查询异常：",e);
			result.setStatus(0);
			result.setMsg("异常");
		}
		return result;
	}
    @RequestMapping(value="/queryList2.do")
	@ResponseBody
	public PurResult queryList2(HttpServletRequest request){
    	Map<String, Object> params = ServletsUtil.getParameters(request);
		
		PurResult result = new PurResult();
		try {
			List<SysPermission> list = menuService.quertMenuList2(params);
	    	result.setStatus(1);
			result.setMsg("成功");
			result.setData(list);
		} catch (Exception e) {
			logger.error("列表查询异常：",e);
			result.setStatus(0);
			result.setMsg("异常");
		}
		return result;
	}
    @RequestMapping(value = "/quertMenuList.do")
    @ResponseBody
    public PurResult quertMenuList(String version,String terminal,String type){
    	logger.info("后台管理-获取一级菜单列表");
    	PurResult result = new PurResult();
    	try {
    		//获取所有一级菜单列表
    		Map<String ,Object> params = new HashMap<String, Object>();
    		params.put("level", "1");
    		params.put("version", version);
    		params.put("terminal", terminal);
    		params.put("type", type);
        	List<SysPermission> classOneMenu = menuService.quertMenuList(params);
        	
        	result.setData(classOneMenu);
        	result.setStatus(1);
			result.setMsg("成功");
		} catch (Exception e) {
			logger.info("后台管理-获取一级菜单列表异常："+e.getMessage());
			result.setStatus(0);
			result.setMsg("异常");
		}
        return result;
    }
    
    @RequestMapping(value = "/addMenu.do")
    @ResponseBody
    public PurResult addMenu(SysPermission permission){
    	logger.info("后台管理-添加菜单");
    	PurResult result = new PurResult();
    	try {
    		menuService.insert(permission);
    		result.setStatus(1);
    		result.setMsg("成功");
		} catch (Exception e) {
			logger.info("后台管理-添加菜单异常："+e.getMessage());
			result.setStatus(0);
			result.setMsg("异常");
		}
        return result;
    }
    
    @RequestMapping(value = "/getMenu.do")
    public String getMenu(String id,Model model){
    	logger.info("后台管理-获取菜单详情");
    	try {
    		SysPermission permission = new SysPermission();
    		permission.setId(Integer.parseInt(id));
    		permission = menuService.getMenu(permission);
    		model.addAttribute("permission",permission);
		} catch (Exception e) {
			logger.info("后台管理-获取菜单详情异常："+e.getMessage());
			return "/error";
		}
		return "/WEB-INF/sys/permission/updatePermission.jsp";
    }
    
    @RequestMapping(value = "/updateMenu.do")
    @ResponseBody
    public PurResult updateMenu(SysPermission permission){
    	logger.info("后台管理-修改菜单");
    	PurResult result = new PurResult();
    	try {
			menuService.update(permission);
			result.setStatus(1);
			result.setMsg("成功");
		} catch (Exception e) {
			logger.info("后台管理-修改菜单异常："+e.getMessage());
			result.setStatus(0);
			result.setMsg("异常");
		}
        return result;
    }
    
    @RequestMapping(value = "/deleteMenu.do")
    @ResponseBody
    public PurResult deleteMenu(String code){
    	logger.info("后台管理-删除菜单");
    	PurResult result = new PurResult();
    	try {
    		Map<String ,Object> params = new HashMap<String, Object>();
    		params.put("parent_code", code);
    		List<SysPermission> list = menuService.quertMenuList(params);
    		if(list.size()>0){
    			result.setStatus(2);
    			result.setMsg("请先删除子集菜单");
    			return result;
    		}
    		int count = menuService.getRoleCountByMenuCode(code);
    		if(count>0){
    			result.setStatus(3);
    			result.setMsg("请先取消角色授权");
    			return result;
    		}
    		menuService.delete(code);
    		result.setStatus(1);
			result.setMsg("成功");
		} catch (Exception e) {
			logger.info("后台管理-删除菜单异常："+e.getMessage());
			result.setStatus(0);
			result.setMsg("异常");
		}
        return result;
    }
}