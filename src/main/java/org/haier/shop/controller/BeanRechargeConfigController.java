package org.haier.shop.controller;

import java.util.HashMap;
import java.util.Map;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.haier.shop.service.BeanRechargeConfigService;
import org.haier.shop.util.HandleMessyCode;
import org.haier.shop.util.PurResult;
import org.haier.shop.util.ServletsUtil;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

@RequestMapping("/beanRechange")
@Controller
public class BeanRechargeConfigController {
	
	private static final Logger logger = LoggerFactory.getLogger(BeanRechargeConfigController.class);
	
	@Resource
	private BeanRechargeConfigService beanRechargeConfigService;
	
	@RequestMapping(value = "/listPage.do")
    public String listPage(){
    	logger.info("后台管理-百货豆购买配置");
        return "/WEB-INF/manager/beanRechangeList.jsp";
    }
	
	@RequestMapping(value = "/addPage.do")
    public String addPage(){
    	logger.info("后台管理-百货豆购买配置页面");
        return "/WEB-INF/manager/addBeanRechange.jsp";
    }
    
    @RequestMapping(value="/queryList.do")
	@ResponseBody
	public PurResult queryList(
			HttpServletRequest request,
			@RequestParam(value = "page", defaultValue = "0") int page,
			@RequestParam(value = "limit", defaultValue = "8") int pageSize){
    	Map<String, Object> params = HandleMessyCode.handle(ServletsUtil.getParameters(request));
		params.put("limit", pageSize);
		params.put("page", (page-1)*pageSize);
		
		PurResult result = new PurResult();
		try {
			result = beanRechargeConfigService.getList(params);
		} catch (Exception e) {
			logger.error("列表查询异常：",e);
			e.printStackTrace();
			result.setStatus(0);
			result.setMsg("异常");
		}
		return result;
	}
    
    @RequestMapping(value = "/addBeanRechange.do")
    @ResponseBody
    public PurResult addBeanRechange(String money,String give_beans){
    	logger.info("后台管理-添加百货豆购买配置");
    	PurResult result = new PurResult();
    	try {
    		Map<String, Object> params = new HashMap<String, Object>();
    		params.put("money", money);
    		params.put("give_beans", give_beans);
    		result = beanRechargeConfigService.add(params);
		} catch (Exception e) {
			logger.info("后台管理-添加百货豆购买配置异常："+e.getMessage());
			result.setStatus(0);
			result.setMsg("异常");
		}
        return result;
    }
    
    @RequestMapping(value = "/getBeanRechange.do")
    public String getBeanRechange(String beans_recharge_config_id,Model model){
    	logger.info("后台管理-获取百货豆购买配置详情");
    	try {
    		Map<String, Object> beanRechange = beanRechargeConfigService.getInfo(beans_recharge_config_id);
    		model.addAttribute("beanRechange", beanRechange);
		} catch (Exception e) {
			return "/error";
		}
    	return "/WEB-INF/manager/editBeanRechange.jsp";
    }
    
    @RequestMapping(value = "/updateBeanRechange.do")
    @ResponseBody
    public PurResult updateBeanRechange(String money,String give_beans,String beans_recharge_config_id){
    	logger.info("后台管理-修改百货豆购买配置");
    	PurResult result = new PurResult();
    	try {
    		Map<String, Object> params = new HashMap<String, Object>();
    		params.put("money", money);
    		params.put("give_beans", give_beans);
    		params.put("beans_recharge_config_id", beans_recharge_config_id);
    		result = beanRechargeConfigService.update(params);
		} catch (Exception e) {
			logger.info("后台管理-修改百货豆购买配置异常："+e.getMessage());
			result.setStatus(0);
			result.setMsg("异常");
		}
        return result;
    }
    
    @RequestMapping(value = "/deleteBeanRechange.do")
    @ResponseBody
    public PurResult deleteBeanRechange(String beans_recharge_config_id){
    	logger.info("后台管理-删除百货豆购买配置");
    	PurResult result = new PurResult();
    	try {	
    		result = beanRechargeConfigService.delete(beans_recharge_config_id);
		} catch (Exception e) {
			logger.info("后台管理-删除百货豆购买配置异常："+e.getMessage());
			result.setStatus(0);
			result.setMsg("异常");
		}
        return result;
    }
}
