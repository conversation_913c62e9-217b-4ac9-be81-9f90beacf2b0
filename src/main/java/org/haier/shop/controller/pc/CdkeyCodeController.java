package org.haier.shop.controller.pc;

import java.net.InetAddress;
import java.net.UnknownHostException;
import java.util.HashMap;
import java.util.Hashtable;
import java.util.List;
import java.util.Map;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.haier.meituan.util.MUtil;
import org.haier.shop.service.ShopDeviceService;
import org.haier.shop.service.ShopSoftService;
import org.haier.shop.util.HandleMessyCode;
import org.haier.shop.util.PurResult;
import org.haier.shop.util.ServletsUtil;
import org.haier.shop.util.wxPay.QRUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import com.google.zxing.BarcodeFormat;
import com.google.zxing.EncodeHintType;
import com.google.zxing.MultiFormatWriter;
import com.google.zxing.common.BitMatrix;

/**
 * 收银端激活码相关
 */

@Controller
@RequestMapping("/pc/cdkeyCode")
public class CdkeyCodeController {
	
	@Autowired
    private ShopDeviceService shopDeviceService;
	
	@Autowired
	private ShopSoftService shopSoftService;
	
	/**
	 * 获取设备激活码信息
	 * @param shop_unique 店铺编号
	 * @param device_no 设备编号
	 * @param edition 软件版本号
	 * @return
	 */
	@RequestMapping("/getShopDeviceCdkeyInfo.do")
	@ResponseBody
	public PurResult getShopDeviceCdkeyInfo(String shop_unique,String device_no,String edition){
		PurResult result = new PurResult();
		try { 
			Map<String ,Object> params = new HashMap<String, Object>();
			params.put("shop_unique", shop_unique);
			params.put("device_no", device_no);
			params.put("edition", edition);
			result = shopDeviceService.getShopDeviceCdkeyInfo(params);
		} catch (Exception e) {
			result.setStatus(0);
			result.setMsg("异常");
		}
		return result;
	}
	
	/**
	 * 设备激活码激活
	 * @param shop_unique 店铺编号
	 * @param device_no 设备编号
	 * @param cdkey_code 激活码
	 * @return
	 */
	@RequestMapping("/cdkeyActivation.do")
	@ResponseBody
	public PurResult cdkeyActivation(String shop_unique,String device_no,String cdkey_code){
		PurResult result = new PurResult();
		try { 
			result = shopDeviceService.cdkeyActivation(shop_unique, device_no, cdkey_code);
		} catch (Exception e) {
			result.setStatus(0);
			result.setMsg("异常");
		}
		return result;
	}
	
	/**
	 * 获取软件激活码设备列表
	 * @return
	 */
	@RequestMapping("/getSoftSettingList.do")
	@ResponseBody
	public PurResult getSoftSettingList(){
		PurResult result = new PurResult();
		try { 
			//获取软件设置列表
	    	List<Map<String ,Object>> softSettingList = shopSoftService.querySysSoftSettingList();
	    	result.setData(softSettingList);
	    	result.setStatus(1);
			result.setMsg("成功");
		} catch (Exception e) {
			result.setStatus(0);
			result.setMsg("异常");
		}
		return result;
	}
	
	/**
	 * 微信支付-生成二维码
	 * @param profit_no 支付记录订单号：SP+时间戳
	 * @param shop_unique 店铺编号
	 * @param device_no 设备编号
	 * @param setting_code 软件设置code
	 * @param bug_type 购买类型：1购买 2续费
	 * @param cdkey_code 激活码，购买类型为2时，必填
	 * @param request
	 * @param response
	 */
	@RequestMapping("/qrcode.do")
	@ResponseBody
	public void qrcode(HttpServletRequest request,HttpServletResponse response) {
		try {
			Map<String, Object> params = HandleMessyCode.handle(ServletsUtil.getParameters(request));
	        String spbill_create_ip = getIpAddr(request);
	        params.put("spbill_create_ip", spbill_create_ip);
	        
	        Integer bug_type = Integer.parseInt(MUtil.strObject(params.get("bug_type"))); 
	        if(bug_type == 1) {//购买，生成激活码
	        	String cdkey_code = shopSoftService.getCdkeyCode(10);
	        	params.put("cdkey_code", cdkey_code);
	        }
	        
	        String text = shopSoftService.weixinPay(params);
	        
	        int width = 300; 
	        int height = 300; 
	        //二维码的图片格式 
	        String format = "gif"; 
			Hashtable hints = new Hashtable(); 
	        //内容所使用编码 
	        hints.put(EncodeHintType.CHARACTER_SET, "utf-8");
	        BitMatrix bitMatrix = new MultiFormatWriter().encode(text, BarcodeFormat.QR_CODE, width, height, hints);
			QRUtil.writeToStream(bitMatrix, format, response.getOutputStream());
			
		} catch (Exception e) {
			e.printStackTrace();
		}
	}
	
	/**
	 * 支付宝支付
	 * @param profit_no 支付记录订单号：SP+时间戳
	 * @param shop_unique 店铺编号
	 * @param device_no 设备编号
	 * @param setting_code 软件设置code
	 * @param bug_type 购买类型：1购买 2续费
	 * @param cdkey_code 激活码，购买类型为2时，必填
	 * @param request
	 * @param response
	 * @throws Exception
	 */
	@RequestMapping("/zhifubaoPay.do")
	public void goBuyBeanPage2(HttpServletRequest request,HttpServletResponse response) throws Exception {
		try {
			Map<String, Object> params = HandleMessyCode.handle(ServletsUtil.getParameters(request));
	        String spbill_create_ip = getIpAddr(request);
	        params.put("spbill_create_ip", spbill_create_ip);
	        Integer bug_type = Integer.parseInt(MUtil.strObject(params.get("bug_type"))); 
	        if(bug_type == 1) {//购买，生成激活码
	        	String cdkey_code = shopSoftService.getCdkeyCode(10);
	        	params.put("cdkey_code", cdkey_code);
	        }
	        String text = shopSoftService.aliPay(params);
	        
	        int width = 300; 
	        int height = 300; 
	        //二维码的图片格式 
	        String format = "gif"; 
			Hashtable hints = new Hashtable(); 
	        //内容所使用编码 
	        hints.put(EncodeHintType.CHARACTER_SET, "utf-8");
	        BitMatrix bitMatrix = new MultiFormatWriter().encode(text, BarcodeFormat.QR_CODE, width, height, hints);
			QRUtil.writeToStream(bitMatrix, format, response.getOutputStream());
		}catch (Exception e) {
			e.printStackTrace();
		}
	}
	
	/**
	 * 验证是否支付成功
	 * @param profit_no 支付记录订单号：SP+时间戳
	 * @param shop_unique 店铺编号
	 */
	@RequestMapping("/isPaySuccess.do")
	@ResponseBody
	public PurResult isPaySuccess(String profit_no,String shop_unique){
		PurResult result = new PurResult();
		try { 
			Map<String ,Object> params = new HashMap<String ,Object>();
			params.put("profit_no", profit_no);
			params.put("shop_unique", shop_unique);
			result = shopSoftService.isPaySuccess(params);
		} catch (Exception e) {
			result.setStatus(0);
			result.setMsg("异常");
		}
		return result;
	}

	/**
	 * 获取当前网络ip
	 * <AUTHOR>
	 * @param request
	 * @return
	 */
	public String getIpAddr(HttpServletRequest request) {
		String ipAddress = request.getHeader("x-forwarded-for");
		if (ipAddress == null || ipAddress.length() == 0 || "unknown".equalsIgnoreCase(ipAddress)) {
			ipAddress = request.getHeader("Proxy-Client-IP");
		}
		if (ipAddress == null || ipAddress.length() == 0 || "unknown".equalsIgnoreCase(ipAddress)) {
			ipAddress = request.getHeader("WL-Proxy-Client-IP");
		}
		if (ipAddress == null || ipAddress.length() == 0 || "unknown".equalsIgnoreCase(ipAddress)) {
			ipAddress = request.getRemoteAddr();
			if (ipAddress.equals("127.0.0.1") || ipAddress.equals("0:0:0:0:0:0:0:1")) {
				// 根据网卡取本机配置的IP
				InetAddress inet = null;
				try {
					inet = InetAddress.getLocalHost();
				} catch (UnknownHostException e) {
					e.printStackTrace();
				}
				ipAddress = inet.getHostAddress();
			}
		}
		// 对于通过多个代理的情况，第一个IP为客户端真实IP,多个IP按照','分割
		if (ipAddress != null && ipAddress.length() > 15) { // "***.***.***.***".length()
															// = 15
			if (ipAddress.indexOf(",") > 0) {
				ipAddress = ipAddress.substring(0, ipAddress.indexOf(","));
			}
		}
		return ipAddress;
	}
	
}
