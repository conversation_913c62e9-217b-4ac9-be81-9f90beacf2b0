package org.haier.shop.controller;

import java.io.BufferedInputStream;
import java.io.BufferedOutputStream;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.net.URL;
import java.net.URLEncoder;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.UUID;
import java.util.zip.ZipEntry;
import java.util.zip.ZipOutputStream;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.apache.commons.io.FileUtils;
import org.apache.shiro.SecurityUtils;
import org.apache.shiro.session.Session;
import org.apache.shiro.subject.Subject;
import org.haier.shop.entity.Staff;
import org.haier.shop.service.LTService;
import org.haier.shop.util.HandleMessyCode;
import org.haier.shop.util.LoadOutObjectXLSUtil;
import org.haier.shop.util.PurResult;
import org.haier.shop.util.ServletsUtil;
import org.haier.shop.util.StringUtil;
import org.haier.shop.util.XLSCallBack;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;



@Controller
@RequestMapping("/lt")
public class LTController {
	@Resource
	private LTService ltService;
	
	@RequestMapping("/cusListPage.do")
	public String cusListPage(HttpServletRequest request){
		
		return "/WEB-INF/lt/cusListPage.jsp";
	}
	@RequestMapping("/tpyListPage.do")
	public String tpyListPage(HttpServletRequest request){
		Subject subject = SecurityUtils.getSubject();
		Session session = subject.getSession();
		Staff staff = (Staff) session.getAttribute("staff");
		String staff_id = String.valueOf(staff.getStaff_id());
		Map<String,Object> data= ltService.queryStaffAreaCode(staff_id);
		request.setAttribute("staff_county", data);
		return "/WEB-INF/lt/tpyList.jsp";
	}
	@RequestMapping("/refusePage.do")
	public String refusePage(HttpServletRequest request,String id){
		request.setAttribute("id", id);
		return "/WEB-INF/lt/refuse.jsp";
	}
	@RequestMapping("/teamDetailPage.do")
	public String teamDetailPage(HttpServletRequest request,String id){
		request.setAttribute("id", id);
		return "/WEB-INF/lt/team_detail.jsp";
	}
	
	@RequestMapping("/yjDetailPage.do")
	public String yjDetailPage(HttpServletRequest request,String id){
		//查询佣金总金额和余额
		Map<String,Object> data= ltService.queryUserYJAllAndBalance(id);
		request.setAttribute("id", id);
		request.setAttribute("data", data);
		return "/WEB-INF/lt/yj_detail.jsp";
	}
	@RequestMapping("/rechargePage.do")
	public String rechargePage(HttpServletRequest request,String id,String balance_withdrawal){
		request.setAttribute("id", id);
		request.setAttribute("balance_withdrawal", balance_withdrawal);
		return "/WEB-INF/lt/recharge.jsp";
	}
	@RequestMapping("/cusDetailPage.do")
	public String cusDetailPage(HttpServletRequest request,String id){
		request.setAttribute("id", id);
		Map<String,Object> data= ltService.queryCusDetail(id);
		request.setAttribute("data", data);
		return "/WEB-INF/lt/cusDetail.jsp";
	}
	@RequestMapping("/yjMoneyListPage.do")
	public String yjMoneyDetailPage(HttpServletRequest request){
		return "/WEB-INF/lt/yjMoneyList.jsp";
	}
	@RequestMapping("/financeListPage.do")
	public String financeListPage(HttpServletRequest request){
		return "/WEB-INF/lt/financeList.jsp";
	}
	@RequestMapping("/returnMoneyConfigPage.do")
	public String returnMoneyConfigPage(HttpServletRequest request){
		return "/WEB-INF/lt/set_return_money.jsp";
	}
	@RequestMapping("/cashListPage.do")
	public String cashListPage(HttpServletRequest request){
		return "/WEB-INF/lt/cashListPage.jsp";
	}
	
	/**
	 * 普通会员
	 * @param map
	 * @return
	 */
	@RequestMapping("/queryCusList.do")
	@ResponseBody
	public PurResult queryCusList(
			@RequestParam(value="shopMessage",defaultValue="")String shopMessage,
			@RequestParam(value="business_status",defaultValue="")String business_status,
			@RequestParam(value="managerUnique")Long managerUnique,
			@RequestParam(value = "page", defaultValue = "1") int page,
			@RequestParam(value = "limit", defaultValue = "15") int pageSize
			){
		Map<String,Object> map=new HashMap<String, Object>();
		map.put("managerUnique", managerUnique);
		map.put("pageSize", pageSize);
		map.put("startNum", (page-1)*pageSize);
		if(shopMessage!=null&&!"".equals(shopMessage)){
			map.put("shopMessage", "%"+shopMessage+"%");
		}
		if(business_status!=null&&!"".equals(business_status)){
			map.put("business_status", business_status);
		}
		System.out.println(map.toString());
		return ltService.queryCusList(map);
	}
	@RequestMapping("/queryYjMoneyList.do")
	@ResponseBody
	public PurResult queryYjMoneyList(
			@RequestParam(value="shopMessage",defaultValue="")String shopMessage,
			@RequestParam(value="managerUnique")Long managerUnique,
			@RequestParam(value = "page", defaultValue = "1") int page,
			@RequestParam(value = "limit", defaultValue = "15") int pageSize
			){
		Map<String,Object> map=new HashMap<String, Object>();
		map.put("managerUnique", managerUnique);
		map.put("pageSize", pageSize);
		map.put("startNum", (page-1)*pageSize);
		if(shopMessage!=null&&!"".equals(shopMessage)){
			map.put("shopMessage", "%"+shopMessage+"%");
		}
		System.out.println(map.toString());
		return ltService.queryYjMoneyList(map);
	}
		
	@RequestMapping("/updateCusStatus.do")
	@ResponseBody
	public PurResult updateCusStatus(
			String commissioner_id
			){
		Map<String,Object> map=new HashMap<String, Object>();
		map.put("commissioner_id", commissioner_id);
		return ltService.updateCusStatus(map);
	}
	@RequestMapping("/updateTpyInfo.do")
	@ResponseBody
	public PurResult updateTpyInfo(
			String id,
			String examine_remarks,
			String examine_status
			){
		Map<String,Object> map=new HashMap<String, Object>();
		map.put("id", id);
		map.put("examine_status", examine_status);
		map.put("examine_remarks", examine_remarks);
		return ltService.updateTpyInfo(map);
	}
	@RequestMapping("/updateReturnMoneyConfig.do")
	@ResponseBody
	public PurResult updateReturnMoneyConfig(
			String detailJson,
			HttpServletRequest request

			){
		return ltService.updateReturnMoneyConfig(detailJson,request);
	}
	@RequestMapping("/deleteReturnMoneyConfigList.do")
	@ResponseBody
	public PurResult deleteReturnMoneyConfigList(
			String id
			
			){
		return ltService.deleteReturnMoneyConfigList(id);
	}
	
	@RequestMapping("/saveRecharge.do")
	@ResponseBody
	public PurResult saveRecharge(
			String id,
			Double money,
			String remark
			){
		Map<String,Object> map=new HashMap<String, Object>();
		map.put("id", id);
		map.put("remark", remark);
		map.put("money", money);
		if(money>0){
			map.put("operate_type", 1);
		}else{
			map.put("operate_type", 2);
		}
		return ltService.saveRecharge(map);
	}
	
	/**
	 * 特派员
	 * @param map
	 * @return
	 */
	@RequestMapping("/queryTpyList.do")
	@ResponseBody
	public PurResult queryTpyList(
			@RequestParam(value="order_message",defaultValue="")String order_message,
			@RequestParam(value="examine_status",defaultValue="")String examine_status,
			@RequestParam(value="staff_id")String staff_id,
			String county,
			@RequestParam(value = "page", defaultValue = "1") int page,
			@RequestParam(value = "limit", defaultValue = "15") int pageSize
			){
		Map<String,Object> map=new HashMap<String, Object>();
		map.put("staff_id", staff_id);
		map.put("pageSize", pageSize);
		map.put("startNum", (page-1)*pageSize);
		if(order_message!=null&&!"".equals(order_message)){
			map.put("shopMessage", "%"+order_message+"%");
		}
		if(examine_status!=null&&!"".equals(examine_status)){
			map.put("examine_status", examine_status);
		}
		if(county!=null&&!"".equals(county)){
			map.put("county", county);
		}
		System.out.println(map.toString());
		return ltService.queryTpyList(map);
	}
	
	/**
	 * 团队明细
	 * @param map
	 * @return
	 */
	@RequestMapping("/queryTeamList.do")
	@ResponseBody
	public PurResult queryTeamList(
			@RequestParam(value="id",defaultValue="")String id,
			@RequestParam(value = "page", defaultValue = "1") int page,
			@RequestParam(value = "limit", defaultValue = "15") int pageSize
			){
		Map<String,Object> map=new HashMap<String, Object>();
		map.put("id", id);
		map.put("pageSize", pageSize);
		map.put("startNum", (page-1)*pageSize);
		System.out.println(map.toString());
		return ltService.queryTeamList(map);
	}
	/**
	 * 佣金明细
	 * @param map
	 * @return
	 */
	@RequestMapping("/queryYJDetailList.do")
	@ResponseBody
	public PurResult queryYJDetailList(
			@RequestParam(value="id",defaultValue="")String id,
			@RequestParam(value = "page", defaultValue = "1") int page,
			@RequestParam(value = "limit", defaultValue = "15") int pageSize
			){
		Map<String,Object> map=new HashMap<String, Object>();
		map.put("id", id);
		map.put("pageSize", pageSize);
		map.put("startNum", (page-1)*pageSize);
		System.out.println(map.toString());
		return ltService.queryYJDetailList(map);
	}
	@RequestMapping("/queryReturnMoneyConfigList.do")
	@ResponseBody
	public PurResult queryReturnMoneyConfigList(
			){
		return ltService.queryReturnMoneyConfigList();
	}
	//财务明细
	@RequestMapping("/queryFinanceList.do")
	@ResponseBody
	public PurResult queryFinanceList(
			@RequestParam(value="shopMessage",defaultValue="")String shopMessage,
			@RequestParam(value="balance_type",defaultValue="")String balance_type,
			@RequestParam(value="start_time",defaultValue="")String start_time,
			@RequestParam(value="end_time",defaultValue="")String end_time,
			@RequestParam(value="managerUnique")Long managerUnique,
			@RequestParam(value = "page", defaultValue = "1") int page,
			@RequestParam(value = "limit", defaultValue = "15") int pageSize
			){
		Map<String,Object> map=new HashMap<String, Object>();
		map.put("managerUnique", managerUnique);
		map.put("pageSize", pageSize);
		map.put("startNum", (page-1)*pageSize);
		if(shopMessage!=null&&!"".equals(shopMessage)){
			map.put("shopMessage", "%"+shopMessage+"%");
		}
		if(balance_type!=null&&!"".equals(balance_type)){
			map.put("balance_type", balance_type);
		}
		if(start_time!=null&&!"".equals(start_time)){
			map.put("start_time", start_time);
		}
		if(end_time!=null&&!"".equals(end_time)){
			map.put("end_time", end_time);
		}
		System.out.println(map.toString());
		return ltService.queryFinanceList(map);
	}
	//提现管理
	@RequestMapping("/queryCashList.do")
	@ResponseBody
	public PurResult queryCashList(
			@RequestParam(value="shopMessage",defaultValue="")String shopMessage,
			@RequestParam(value="cash_status",defaultValue="")String cash_status,
			@RequestParam(value="start_time",defaultValue="")String start_time,
			@RequestParam(value="end_time",defaultValue="")String end_time,
			@RequestParam(value="managerUnique")Long managerUnique,
			@RequestParam(value = "page", defaultValue = "1") int page,
			@RequestParam(value = "limit", defaultValue = "15") int pageSize
			){
		Map<String,Object> map=new HashMap<String, Object>();
		map.put("managerUnique", managerUnique);
		map.put("pageSize", pageSize);
		map.put("startNum", (page-1)*pageSize);
		if(shopMessage!=null&&!"".equals(shopMessage)){
			map.put("shopMessage", "%"+shopMessage+"%");
		}
		if(cash_status!=null&&!"".equals(cash_status)){
			map.put("cash_status", cash_status);
		}
		if(start_time!=null&&!"".equals(start_time)){
			map.put("start_time", start_time);
		}
		if(end_time!=null&&!"".equals(end_time)){
			map.put("end_time", end_time);
		}
		System.out.println(map.toString());
		return ltService.queryCashList(map);
	}
	
	@RequestMapping("/updateCashStatus.do")
	@ResponseBody
	public PurResult updateCashStatus(
			String id,
			String cash_status,
			String list_unique
			){
		Map<String,Object> map=new HashMap<String, Object>();
		map.put("id", id);
		map.put("cash_status", cash_status);
		map.put("list_unique", list_unique);
		return ltService.updateCashStatus(map);
	}
	
	/**
	 * 提现导出excel
	 */
	@RequestMapping("/cashExcel.do")
	public void cashExcel(HttpServletRequest request, HttpServletResponse response) throws Exception{
		Map<String, Object> params = HandleMessyCode.handle(ServletsUtil.getParameters(request));
		List<Map<String,Object>> list=ltService.queryCashListExcel(params);	
		String[] titles=new String[] {
				"会员名称","姓名","手机号","提现账户","提现金额","手续费","到账金额",
				"状态","日期"
		};
		loadDiaoBoXLS(list,"提现记录",request,response,titles);
	}
	
	public void loadDiaoBoXLS(List<Map<String, Object>> caseVoList, final String string,HttpServletRequest request,HttpServletResponse response,final String[] titles){
		try {
			response.setContentType(
					"application/x-msdownload; charset=UTF-8");// 告知浏览器文件类型
			String agent = request.getHeader("user-agent");
			if (agent.contains("Firefox")) {
				response.setHeader(
						"Content-Disposition",
						"attachment;filename="
								+ new String((string + ".xls")
										.getBytes("GB2312"), "ISO-8859-1"));// 设置文件头
			} else {
				response.setHeader(
						"Content-Disposition",
						"attachment;filename="
								+ URLEncoder.encode(string + ".xls", "UTF-8"));// 设置文件头
			}
			OutputStream os = response.getOutputStream();
			LoadOutObjectXLSUtil<Map<String, Object>> objectXLS = new LoadOutObjectXLSUtil<Map<String, Object>>();
			objectXLS.generateXLS(os, caseVoList, new XLSCallBack<Map<String, Object>>() {
				public Object[] getValues(Map<String, Object> tt) {
					String str = "";
					
					String cus_name = tt.get("cus_name")+"";
					if (StringUtil.blank(cus_name)) {
						cus_name = str;
					}
					String name =  tt.get("name")+"";
					if (StringUtil.blank(name)) {
						name = str;
					}
					String cus_phone =tt.get("cus_phone")+"";
					if (StringUtil.blank(cus_phone)) {
						cus_phone = str;
					}
					String bank = tt.get("bank")+"";
					if (StringUtil.blank(bank)) {
						bank = str;
					}
					String cash_money = tt.get("cash_money")+"";
					if (StringUtil.blank(cash_money)) {
						cash_money = str;
					}
					String cash_service = tt.get("cash_service")+"";
					if (StringUtil.blank(cash_service)) {
						cash_service = str;
					}
					String to_money = tt.get("to_money")+"";
					if (StringUtil.blank(to_money)) {
						to_money = str;
					}					
					String cash_status_str = tt.get("cash_status_str")+"";
					if (StringUtil.blank(cash_status_str)) {
						cash_status_str = str;
					}					
					String create_date = tt.get("create_date")+"";
					if (StringUtil.blank(create_date)) {
						create_date = str;
					}					
							
					
					return new String[] {cus_name,name,cus_phone,
							bank,cash_money,cash_service,to_money,cash_status_str,create_date
					};
				}

				public String getTitle() {
					return string;
				}

				public int[] getColumnsWidth() {
					return new int[] { 
										  80 * 80, 80 * 80,80 * 80,80 * 80,
										  80 * 80, 80 * 80,80 * 80,80 * 80,
										  80 * 80,80 * 80,80 * 80,80 * 80,80 * 80};
				}

				public String[] getColumnsName() {
					return titles;
				}

				public int[][] MergedRegion() {
					// TODO Auto-generated method stub
					return null;
				}

				public List<Object[]> getheaderValue() {
					// TODO Auto-generated method stub
					return null;
				}

			});
			os.flush();
		} catch (IOException e) {
			e.printStackTrace();
		}
	}
	/**
	 * 特派员导出excel
	 */
	@RequestMapping("/tpyListExcel.do")
	public void tpyListExcel(HttpServletRequest request, HttpServletResponse response) throws Exception{
		Map<String, Object> params = HandleMessyCode.handle(ServletsUtil.getParameters(request));
		List<Map<String,Object>> list=ltService.queryTpyListExcel(params);	
		String[] titles=new String[] {
				"会员名称","姓名","手机号","上级特派员","余额","佣金","区域"
		};
		loadTpyListXLS(list,"特派员",request,response,titles);
	}
	
	public void loadTpyListXLS(List<Map<String, Object>> caseVoList, final String string,HttpServletRequest request,HttpServletResponse response,final String[] titles){
		try {
			response.setContentType(
					"application/x-msdownload; charset=UTF-8");// 告知浏览器文件类型
			String agent = request.getHeader("user-agent");
			if (agent.contains("Firefox")) {
				response.setHeader(
						"Content-Disposition",
						"attachment;filename="
								+ new String((string + ".xls")
										.getBytes("GB2312"), "ISO-8859-1"));// 设置文件头
			} else {
				response.setHeader(
						"Content-Disposition",
						"attachment;filename="
								+ URLEncoder.encode(string + ".xls", "UTF-8"));// 设置文件头
			}
			OutputStream os = response.getOutputStream();
			LoadOutObjectXLSUtil<Map<String, Object>> objectXLS = new LoadOutObjectXLSUtil<Map<String, Object>>();
			objectXLS.generateXLS(os, caseVoList, new XLSCallBack<Map<String, Object>>() {
				public Object[] getValues(Map<String, Object> tt) {
					String str = "";
					
					String cus_name = tt.get("cus_name")+"";
					if (StringUtil.blank(cus_name)) {
						cus_name = str;
					}
					String name =  tt.get("name")+"";
					if (StringUtil.blank(name)) {
						name = str;
					}
					String cus_phone =tt.get("cus_phone")+"";
					if (StringUtil.blank(cus_phone)) {
						cus_phone = str;
					}
					String parent_cus = tt.get("parent_cus")+"";
					if (StringUtil.blank(parent_cus)) {
						parent_cus = str;
					}
					String balance_consumption = tt.get("balance_consumption")+"";
					if (StringUtil.blank(balance_consumption)) {
						balance_consumption = str;
					}
					String balance_withdrawal = tt.get("balance_withdrawal")+"";
					if (StringUtil.blank(balance_withdrawal)) {
						balance_withdrawal = str;
					}
					String area_dict_content = tt.get("area_dict_content")+"";
					if (StringUtil.blank(area_dict_content)) {
						area_dict_content = str;
					}					
								
					
					
					return new String[] {cus_name,name,cus_phone,
							parent_cus,balance_consumption,balance_withdrawal,area_dict_content
					};
				}
				
				public String getTitle() {
					return string;
				}
				
				public int[] getColumnsWidth() {
					return new int[] { 
							80 * 80, 80 * 80,80 * 80,80 * 80,
							80 * 80, 80 * 80,80 * 80,80 * 80,
							80 * 80,80 * 80,80 * 80};
				}
				
				public String[] getColumnsName() {
					return titles;
				}
				
				public int[][] MergedRegion() {
					// TODO Auto-generated method stub
					return null;
				}
				
				public List<Object[]> getheaderValue() {
					// TODO Auto-generated method stub
					return null;
				}
				
			});
			os.flush();
		} catch (IOException e) {
			e.printStackTrace();
		}
	}
	
	/**
	 * 下载
	 * @return 
	 */
	@RequestMapping("/downloadInfo.do")
	public HttpServletResponse downloadInfo(HttpServletRequest request, HttpServletResponse response) throws Exception{
		Map<String, Object> params = HandleMessyCode.handle(ServletsUtil.getParameters(request));
		List<Map<String,Object>> list=ltService.queryTpyImageList(params);	
		List<File> file_list=new ArrayList<>();
		String filePath = this.getClass().getClassLoader().getResource("../../").getPath();
		filePath=filePath.substring(0, filePath.length()-request.getContextPath().length());
		for (Map<String, Object> map : list) {
			String file_url=map.get("file_sign_url").toString();
        	String fileName=filePath+"/"+UUID.randomUUID()+".jpg";
        	File file=new File(fileName);
        	URL url=new URL(file_url);
        	FileUtils.copyURLToFile(url, file);
        	file_list.add(file);
		}
        //打包凭证.zip与EXCEL一起打包
        try {
            String zipFilenameA = filePath+"/"+"tempFileA.zip" ;
            File fileA = new File(zipFilenameA);
            if (!fileA.exists()){  
            	System.out.println(111);
                fileA.createNewFile();   
            }
            response.reset();
            //response.getWriter()
            //创建文件输出流
            FileOutputStream fousa = new FileOutputStream(fileA);   
            ZipOutputStream zipOutA = new ZipOutputStream(fousa);
            zipFile(file_list, zipOutA);
            zipOutA.close();
            fousa.close();
            for (File file : file_list) {
            	file.delete();
			}
            return downloadZip(fileA,response);
        }catch (Exception e) {
                e.printStackTrace();
            }
        return response ;
    }
		
	
	 public static void zipFile (List files,ZipOutputStream outputStream) {
	        int size = files.size();
	        for(int i = 0; i < size; i++) {
	            File file = (File) files.get(i);
	            zipFile(file, outputStream);
	        }
	    }
	    /**  
	     * 根据输入的文件与输出流对文件进行打包
	     * @param File
	     * @param org.apache.tools.zip.ZipOutputStream
	     */
	    public static void zipFile(File inputFile,  ZipOutputStream ouputStream) {
	        try {
	            if(inputFile.exists()) {
	                if (inputFile.isFile()) {
	                    FileInputStream IN = new FileInputStream(inputFile);
	                    BufferedInputStream bins = new BufferedInputStream(IN, 512);
	                    ZipEntry entry = new ZipEntry(inputFile.getName());
	                    ouputStream.putNextEntry(entry);
	                    // 向压缩文件中输出数据   
	                    int nNumber;
	                    byte[] buffer = new byte[512];
	                    while ((nNumber = bins.read(buffer)) != -1) {
	                        ouputStream.write(buffer, 0, nNumber);
	                    }
	                    // 关闭创建的流对象   
	                    bins.close();
	                    IN.close();
	                } else {
	                    try {
	                        File[] files = inputFile.listFiles();
	                        for (int i = 0; i < files.length; i++) {
	                            zipFile(files[i], ouputStream);
	                        }
	                    } catch (Exception e) {
	                        e.printStackTrace();
	                    }
	                }
	            }
	        } catch (Exception e) {
	            e.printStackTrace();
	        }
	    }
	    public static HttpServletResponse downloadZip(File file,HttpServletResponse response) {
	        if(file.exists() == false){  
	            System.out.println("待压缩的文件目录："+file+"不存在.");  
	        }else{
	            try {
	            // 以流的形式下载文件。
	            InputStream fis = new BufferedInputStream(new FileInputStream(file.getPath()));
	            byte[] buffer = new byte[fis.available()];
	            fis.read(buffer);
	            fis.close();
	            // 清空response
	            response.reset();
	    
	            OutputStream toClient = new BufferedOutputStream(response.getOutputStream());
	            response.setContentType("application/octet-stream");
	    
	            //如果输出的是中文名的文件，在此处就要用URLEncoder.encode方法进行处理
	            response.setHeader("Content-Disposition", "attachment;filename="
	                    + new String(file.getName().getBytes("GB2312"), "ISO8859-1"));
	            toClient.write(buffer);
	            toClient.flush();
	            toClient.close();
	            } catch (IOException ex) {
	            ex.printStackTrace();
	            }finally{
	                 try {
	                        File f = new File(file.getPath());
	                        f.delete();
	                    } catch (Exception e) {
	                        e.printStackTrace();
	                    }
	            }
	        }
	        return response;
	    }
	    
	
}
