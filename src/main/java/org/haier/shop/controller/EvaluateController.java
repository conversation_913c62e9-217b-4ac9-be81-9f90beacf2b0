package org.haier.shop.controller;

import java.sql.Timestamp;
import java.util.HashMap;
import java.util.Map;

import javax.annotation.Resource;

import org.haier.shop.service.EvaluateService;
import org.haier.shop.util.ShopsResult;
import org.haier.shop.util.ShopsUtil;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

@Controller
@RequestMapping("/html/evaluate")
public class EvaluateController {
	@Resource
	private EvaluateService evalService;
	
	
	/**
	 * 查询店铺近期的商品评价
	 * @param map
	 * @return
	 */
	@RequestMapping("/evaluateListPagesQuery.do")
	@ResponseBody
	public ShopsResult evaluateListPagesQuery(
			@RequestParam(value="shopUnique",required=true)Long shopUnique,
			String goodsMessage,
			Long saleListUnique,
			String cusName,
			String startTime,
			String endTime,
			String groupUnique,
			String kindUnique,
			@RequestParam(value="evaluateStatus",defaultValue="-1")Integer evaluateStatus,
			@RequestParam(value="pageSize",defaultValue="20")Integer pageSize
			){
		ShopsResult sr=new ShopsResult();
		Map<String,Object> map=new HashMap<String, Object>();
		map.put("shopUnique", shopUnique);
		if(null!=goodsMessage){
			map.put("goodsMessage", "%"+goodsMessage+"%");
		}
		map.put("saleListUnique", saleListUnique);
		if(!"".equals(cusName)){
			map.put("cusName", cusName);
		}
		
		Timestamp startDate=null;
		if(null!=startTime&&!"".equals(startTime)){
			startDate=ShopsUtil.stringToTimestamp(startTime);
			if(null==startDate){
				sr.setStatus(2);
				sr.setMsg("时间参数格式错误");
				return sr;
			}
		}
		Timestamp endDate=null;
		if(null!=endTime&&!"".equals(endTime)){
			endDate=ShopsUtil.stringToTimestamp(endTime);
			if(null==endDate){
				sr.setStatus(2);
				sr.setMsg("时间参数格式错误");
				return sr;
			}
		}
		map.put("startTime", startDate);
		map.put("endTime", endDate);
		if("-1".equals(groupUnique)){
			groupUnique=null;
		}
		if("-1".equals(kindUnique)){
			kindUnique=null;
		}
		map.put("groupUnique", groupUnique);
		map.put("kindUnique", kindUnique);
		if(-1==evaluateStatus){
			evaluateStatus=null;
		}
		map.put("evaluateStatus", evaluateStatus);
		map.put("pageSize", pageSize);
		return evalService.evaluateListPagesQuery(map);
	}
	
	/**
	 * @param map
	 * @return
	 */
	@RequestMapping("/evaluateListQuery.do")
	@ResponseBody
	public ShopsResult evaluateListQuery(
			@RequestParam(value="shopUnique",required=true)Long shopUnique,
			String goodsMessage,
			Long saleListUnique,
			String cusName,
			String startTime,
			String endTime,
			String groupUnique,
			String kindUnique,
			@RequestParam(value="evaluateStatus",defaultValue="-1")Integer evaluateStatus,
			@RequestParam(value="pageSize",defaultValue="20")Integer pageSize,
			@RequestParam(value="pageNum",defaultValue="1")Integer pageNum,
			@RequestParam(value="order",defaultValue="1")Integer order,
			@RequestParam(value="orderType",defaultValue="1")Integer orderType
			){
		ShopsResult sr=new ShopsResult();
		Map<String,Object> map=new HashMap<String, Object>();
		map.put("shopUnique", shopUnique);
		if(null!=goodsMessage){
			map.put("goodsMessage", "%"+goodsMessage+"%");
		}
		map.put("saleListUnique", saleListUnique);
		if(!"".equals(cusName)){
			map.put("cusName", cusName);
		}
		Timestamp startDate=null;
		if(null!=startTime&&!"".equals(startTime)){
			startDate=ShopsUtil.stringToTimestamp(startTime);
			if(null==startDate){
				sr.setStatus(2);
				sr.setMsg("时间参数格式错误");
				return sr;
			}
		}
		Timestamp endDate=null;
		if(null!=endTime&&!"".equals(endTime)){
			endDate=ShopsUtil.stringToTimestamp(endTime);
			if(null==endDate){
				sr.setStatus(2);
				sr.setMsg("时间参数格式错误");
				return sr;
			}
		}
		map.put("startTime", startDate);
		map.put("endTime", endDate);
		if("-1".equals(groupUnique)){
			groupUnique=null;
		}
		if("-1".equals(kindUnique)){
			kindUnique=null;
		}
		if(-1==evaluateStatus){
			evaluateStatus=null;
		}
		map.put("groupUnique", groupUnique);
		map.put("kindUnique", kindUnique);
		if(null!=evaluateStatus&&-1==evaluateStatus){
			evaluateStatus=null;
		}
		map.put("evaluateStatus", evaluateStatus);
		map.put("pageSize", pageSize);
		map.put("startNum", (pageNum-1)*pageSize);
		
		if(1==order){
			map.put("order", "saleListUnique");
		}else if(2==order){
			map.put("order", "goodsName");
		}else if(3==order){
			map.put("order", "goodsBarcode");
		}else if(4==order){
			map.put("order", "evaluateDate");
		}else if(5==order){
			map.put("order", "evaluatePoint");
		}else if(6==order){
			map.put("order", "evaluateScore");
		}
		
		if(1==orderType){
			map.put("orderType", "ASC");
		}
		if(2==orderType){
			map.put("orderType", "DESC");
		}
		return evalService.evaluateListQuery(map);
	}
	
	/**
	 * 查询评论详情
	 * @param map
	 * @return
	 */
	@RequestMapping("/queryEvaluateDetail.do")
	@ResponseBody
	public ShopsResult queryEvaluateDetail(
			@RequestParam(value="evaluateId",required=true)Integer evaluateId){
		Map<String,Object> map=new HashMap<String, Object>();
		map.put("evaluateId", evaluateId);
		return evalService.queryEvaluateDetail(map);
	}
	
	/**
	 * 删除管理员评论内容
	 * @param map
	 * @return
	 */
	@RequestMapping("/deleteManagerEvaluate.do")
	@ResponseBody
	public ShopsResult deleteManagerEvaluate(
			@RequestParam(value="detailId",required=true)Integer detailId){
		Map<String,Object> map=new HashMap<String, Object>();
		map.put("detailId", detailId);
		map.put("evaluateStatus", 2);
		return evalService.deleteManagerEvaluate(map);
	}
	
	/**
	 * 添加订单评论回复
	 * @param map
	 * @return
	 */
	@RequestMapping("/responeEvaluate.do")
	@ResponseBody
	public ShopsResult responeEvaluate(
			@RequestParam(required=true,value="evaluateContent")String evaluateContent,
			@RequestParam(value="evaluateId",required=true)Integer evaluateId,
			@RequestParam(value="userType",required=true)Integer userType,
			@RequestParam(value="evaluateUserId",required=true)Integer evaluateUserId){
		Map<String,Object> map=new HashMap<String, Object>();
		
		map.put("evaluateContent", evaluateContent);
		map.put("evaluateId", evaluateId);
		map.put("userType", userType);
		map.put("evaluateUserId", evaluateUserId);
		map.put("evaluateStatus", 2);
		return evalService.responeEvaluate(map);
	}
}
