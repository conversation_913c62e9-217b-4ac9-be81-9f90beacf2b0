package org.haier.shop.controller;

import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.net.HttpURLConnection;
import java.net.URL;
import java.net.URLConnection;
import java.net.URLEncoder;
import java.sql.Timestamp;
import java.text.DateFormat;
import java.text.DecimalFormat;
import java.text.SimpleDateFormat;
import java.util.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONUtil;
import net.sf.json.JSONObject;
import org.haier.log.annotation.RemoteLog;
import org.haier.log.enums.BusinessType;
import org.haier.meituan.util.MUtil;
import org.haier.shop.entity.GoodsInfo;
import org.haier.shop.entity.ShopStockDetail;
import org.haier.shop.entity.ShopsConfig;
import org.haier.shop.entity.Staff;
import org.haier.shop.enums.GoodsInPriceTypeEnums;
import org.haier.shop.params.goods.InStockParam;
import org.haier.shop.params.goods.OutStockParam;
import org.haier.shop.params.stock.ShopOutStockAuditParam;
import org.haier.shop.params.stock.ShopStockDetailQueryParam;
import org.haier.shop.result.stock.ShopStockDetailVO;
import org.haier.shop.service.*;
import org.haier.shop.util.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.util.ObjectUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;

import com.jcraft.jsch.SftpException;


@Controller
@RequestMapping("/html/goods")
public class GoodsController {
//	private Logger log = Logger.getLogger(GoodsController.class);
	@Resource
	private GoodsService goodsService;
	
	@Resource
	private ShopService shopService;
	
	@Resource
	private StockService stockService;
	@Resource
	private ShopStockService shopStockService;
	@Resource
	private ShopsConfigService shopsConfigService;
	
	@RequestMapping("/importGoodsKindMsg.do")
	@ResponseBody
	public ShopsResult importGoodsKindMsg(String goodsMsg,String shopUnique) {
		return goodsService.importGoodsKindMsg(goodsMsg, shopUnique);
	}
	@RequestMapping("/queryGoodsKindMsg.do")
	@ResponseBody
	 public ShopsResult queryGoodsKindMsg(String goodsMsg,String shopUnique) {
		 return goodsService.queryGoodsKindMsg(goodsMsg, shopUnique);
	 }
	/**
	 * 批量修改商品价格，库存信息
	 * @param shopUnique
	 * @param goodsMsg
	 * @return
	 */
	@RequestMapping("/importGoodsBaseMsg.do")
	@ResponseBody
	public ShopsResult importGoodsBaseMsg(String shopUnique, String goodsMsg, HttpServletRequest request) {
		return goodsService.importGoodsBaseMsg(shopUnique, goodsMsg, request);
	}
	
	
	/**
     * 查询商品的基础信息
     * @param goodsMsg 商品基本信息
     * @param shopUnique
     * @return
     */
	@RequestMapping("/queryGoodsBaseMsg.do")
	@ResponseBody
    public ShopsResult queryGoodsBaseMsg(String goodsMsg,String shopUnique) {
    	return goodsService.queryGoodsBaseMsg(goodsMsg, shopUnique);
    }
	
	/**
	 * 添加云端商品
	 * @param goods_barcode 商品条码
	 * @param goods_name 商品名称
	 * @param goods_in_price 商品进价
	 * @param goods_sale_price 商品售价
	 * @param goods_cus_price 商品会员价
	 * @param goods_count 库存数量
	 * @param goods_standard 商品规格
	 * @param goods_unit 销售单位
	 * @param goods_picturepath 图片
	 * @param request 
	 * @return
	 */
	@RequestMapping("/addNewGoodsByCloud.do")
	@ResponseBody
	public ShopsResult addNewGoodsByCloud(String goods_barcode,String goods_name,Double goods_in_price,Double goods_sale_price,Double goods_cus_price,
			Double goods_count,String goods_standard,String goods_unit,String goods_picturepath,HttpServletRequest request,Integer addIndex) {
		return goodsService.addNewGoodsByCloud(goods_barcode, goods_name, goods_in_price, goods_sale_price, goods_cus_price, 
				goods_count, goods_standard, goods_unit, goods_picturepath, request,addIndex);
	}
	@RequestMapping("/queryBaseGoods.do")
	@ResponseBody
	public ShopsResult queryBaseGoods(Integer page,Integer limit,String goodsMsg, String goodsList, String shop_unique) {
		try {
			return goodsService.queryBaseGoods(page, limit, goodsMsg, goodsList,shop_unique);
		}catch (Exception e) {
			ShopsResult sr = new ShopsResult(1,"查询失败!");
			return sr;
		}
	}
	
	/**
	 * 跳转选择商品页面
	 * @return
	 */
	@RequestMapping("/toChooseBaseGoods.do")
	public String toChooseBaseGoods() {
		return "/WEB-INF/goods/toChooseBaseGoods.jsp";
	}
	
	/**
	 * 跳转添加商品页面
	 * @return
	 */
	@RequestMapping("/toAddBaseGoods.do")
	public String toAddBaseGoods() {
		return "/WEB-INF/goods/toAddBaseGoods.jsp";
	}
	/**
	 * 删除首页商品信息
	 * @param shopUnique
	 * @param goodsBarcode
	 * @return
	 */
	@RemoteLog(title = "删除首页商品信息", businessType = BusinessType.DELETE)
	@RequestMapping("/deleteGoodsIndex.do")
	@ResponseBody
	public ShopsResult deleteGoodsIndex(String shopUnique, String goodsBarcode) {
		return goodsService.deleteGoodsIndex(shopUnique, goodsBarcode);
	}
	/**
	 * 修改商品列表
	 * @param shopUnique
	 * @param goodsBarcodes
	 * @return
	 */
	@RemoteLog(title = "修改首页推荐", businessType = BusinessType.UPDATE)
	@RequestMapping("/addNewGoodsIndex.do")
	@ResponseBody
	public ShopsResult addNewGoodsIndex(String shopUnique,String goodsBarcodes) {
		return goodsService.addNewGoodsIndex(shopUnique, goodsBarcodes);
	}
	
	@RequestMapping("/toGoodsFristSort.do")
	public String toGoodsFristSort() {
		return "/WEB-INF/goods/goodsFristSort.jsp";
	}
	
	
	/**
	 * 查询小程序首页显示的商品信息
	 * @param shopUnique
	 * @return
	 */
	@RequestMapping("/queryGoodsIndexSort.do")
	@ResponseBody
	public ShopsResult queryGoodsIndexSort(String shopUnique) {
		return goodsService.queryGoodsIndexSort(shopUnique);
	}
	/**
	 * 油号详情管理
	 * @param goodsBarcode
	 * @return
	 */
	@RequestMapping("/toEditOilGoods.do")
	public String toEditOilGoods(String goodsBarcode,HttpServletRequest request,Model model) {

		String shopUnique= ((Staff)request.getSession().getAttribute("staff")).getShop_unique() + "";
		//查询可用的油枪和油号
		List<Map<String,Object>> oilList = goodsService.queryUseAbleOilList(2,shopUnique,1);
		model.addAttribute("oilList", oilList);
		
		//查询现有油品的信息
		Map<String,Object> oilMap = goodsService.getOilGoodsDetail(goodsBarcode,shopUnique);
		model.addAttribute("oilMap",oilMap);
		
		return "/WEB-INF/goods/toEditOilGoods.jsp";
	}
	/**
	 * 新增或添加商品信息
	 * @param shopUnique 店铺编号
	 * @param gunNum 油枪编号
	 * @param oilNum 油号
	 * @param goodsSalePrice 商品售价
	 * @param goodsInPrice 商品进价
	 * @param goodsCount 商品库存
	 * @param goodsId 商品ID
	 * @return
	 */
	@RequestMapping("/addNewOilMsg.do")
	@ResponseBody
	public ShopsResult addNewOilMsg(String shopUnique,Integer gunNum,String gunName,Integer oilNum,String oilName,
			Double goodsSalePrice,Double goodsInPrice,Double goodsCount,Integer goodsId) {
		return goodsService.addNewOilMsg(shopUnique, gunNum, gunName, oilNum, oilName, goodsSalePrice, goodsInPrice, goodsCount, goodsId);
	}
	
	@RequestMapping("/toAddOilGoods.do")
	public String toAddOilGoods(Model model,HttpServletRequest request) {
		
		String shopUnique= ((Staff)request.getSession().getAttribute("staff")).getShop_unique() + "";
		//查询可用的油枪和油号
		List<Map<String,Object>> gunList = goodsService.queryUseAbleOilList(1,shopUnique,2);
		List<Map<String,Object>> oilList = goodsService.queryUseAbleOilList(2, shopUnique,2);
		
		model.addAttribute("gunList", gunList);
		model.addAttribute("oilList", oilList);
		
		return "/WEB-INF/goods/addOilGoods.jsp";
	}
	
	
	/**
	 * 查询油品信息列表
	 * @return
	 */
	@RequestMapping("/queryOilGoodsList.do")
	@ResponseBody
	public ShopsResult queryOilGoodsList(HttpServletRequest request) {
		String shopUnique= ((Staff)request.getSession().getAttribute("staff")).getShop_unique() + "";
		return goodsService.queryOilGoodsList(shopUnique);
	}
	
	@RequestMapping("/oilGoodsManager.do")
	public String goodsKindPage(Model model,HttpServletRequest request) {
		return "/WEB-INF/goods/oilGoodsManager.jsp";
	}
	
	/**
	 * 查询摄像头列表
	 * @param shopUnique 摄像头所属的店铺编号
	 * @return
	 */
	@RequestMapping("/queryMonitorList.do")
	@ResponseBody
	public ShopsResult queryMonitorList(String shopUnique){
		ShopsResult sr = new ShopsResult(1, "查询成功!");
		try {
			List<Map<String,Object>> list = goodsService.queryMonitorList(shopUnique,null);
			sr.setData(list);
		}catch (Exception e) {
			e.printStackTrace();
			sr.setStatus(0);
			sr.setMsg("系统错误!");
		}
		
		return sr;
	}
	
	@ResponseBody
	@RequestMapping("/uploadImg.do")
	public PurResult uploadImg(MultipartFile[] file,String shop_unique,HttpServletRequest request) {
		if(null == file || file.length == 0) {
			return new PurResult(0, "请至少上传一张图片！");
		}
		return goodsService.uploadImg(file, shop_unique, request);
	}
	
	//打开图片上传界面
	@RequestMapping("/openGoodsPicImport.do")
	public String openGoodsPicImport() {
		return "/WEB-INF/goods/goodsPicImport.jsp";
	}
	

	/**
	 * 查询店铺的自营供货商信息
	 * @param shopUnique
	 * @return
	 */
	@RequestMapping("/getGoodsSupplierMsg.do")
	@ResponseBody
	public PurResult getGoodsSupplierMsg(String shopUnique) {
		
		return goodsService.getGoodsSupplierMsg(shopUnique);
	}
		
	/**
	 * 商品列表-yll
	 */
	@RequestMapping("/getGoodList.do")
	@ResponseBody
	public PurResult getGoodList(HttpServletRequest request,
			@RequestParam(value="page",defaultValue="1")Integer page,
			@RequestParam(value="limit",defaultValue="15")Integer limit,
			String field,
			String order
			){		
		Long t1=Calendar.getInstance().getTimeInMillis();
		PurResult result = new PurResult();
		try {
//			Map<String,String[]> m = request.getParameterMap();
			Map<String, Object> params = HandleMessyCode.handle(ServletsUtil.getParameters(request));
			String goodsMessage = MUtil.strObject(params.get("goodsMessage"));
			if(goodsMessage != null && !goodsMessage.equals("")){
				params.put("goodsMessage", "%"+goodsMessage+"%");
			}
			params.put("limit", limit);
			params.put("page", (page-1)*limit);
			params.put("field",field);
			params.put("order",order);
			result=goodsService.getGoodList(params);
			Long t2 = Calendar.getInstance().getTimeInMillis();
			System.out.println("耗时"+(t2-t1));
		} catch (Exception e) {
			result.setStatus(0);
			result.setMsg("查询失败!");
			e.printStackTrace();
		}		
		
		return result;
	}
	
	/**
	 * 跳转到商品新增页面
	 */
	@RequestMapping(value = "/toImportGood.do")
	public String toImportGood() {
		return "/WEB-INF/goods/importGood.jsp";
	}
	
	/**
	 * 跳转到商品新增页面
	 */
	@RequestMapping(value = "/toImportGoodBase.do")
	public String toImportGoodBase() {
		return "/WEB-INF/goods/importGoodBase.jsp";
	}
	/**
	 * 跳转到商品新增页面
	 */
	@RequestMapping(value = "/toImportGoodKind.do")
	public String toImportGoodKind() {
		return "/WEB-INF/goods/importGoodKind.jsp";
	}
	
	@RequestMapping("/toImportGoodsKind.do")
	public String toImportGoodsKind() {
		return "/WEB-INF/goods/importGoodsKind.jsp";
	}
	
	
	/**
	 * 商品详情查询
	 * @param shop_unique 店铺编号
	 * @param goods_barcode 商品条码
	 * @return
	 */
	@RequestMapping("queryGoodsDetail.do")
	@ResponseBody
	public ShopsResult queryGoodsDetail(
			@RequestParam(value="shop_unique",required=true)String shop_unique,
			@RequestParam(value="goods_barcode",required=true)String goods_barcode){
		return goodsService.queryGoodsDetail(shop_unique, goods_barcode);
	}
	
	/**
	 * 更新商品信息
	 * @param shop_unique 商铺编号
	 * @param goods_barcode 商品条码
	 * @param goods_name 商品名称
	 * @param goods_kind_unique 商品分类编号
	 * @param goods_brand 商品品牌
	 * @param goods_in_price 商品进价
	 * @param goods_sale_price 商品售价
	 * @param goods_life 商品保质期
	 * @param goods_points 会员积分
	 * @param goods_count 商品库存量
	 * @param goods_sold 销售数量
	 * @param goods_standard 商品规格
	 * @param default_supplier_unique 默认供货商编号
	 * @return
	 */
	@RequestMapping("/updateGoodsMessage.do")
	@ResponseBody
	public ShopsResult updateGoodsMessage(String shop_unique,String goods_barcode,String goods_name,String goods_kind_unique,
			String goods_brand,Double goods_in_price,Double  goods_sale_price,String goods_unit,Integer goods_life,Integer goods_points,Double goods_count,
			Double goods_sold,String goods_standard,String default_supplier_unique,Double goods_cus_price,Double goods_web_sale_price, String staff_id, HttpServletRequest request){
		System.out.println("后台商品信息更新：：：：："+shop_unique+":::::::"+goods_barcode+"::::::"+goods_kind_unique+"::::"+goods_sale_price);
		return goodsService.updateGoodsMessage(shop_unique, goods_barcode, goods_name, goods_kind_unique
				, goods_brand, goods_in_price, goods_sale_price,goods_unit, goods_life, goods_points,
				goods_count, goods_sold, goods_standard, default_supplier_unique,goods_cus_price,goods_web_sale_price, staff_id, request);
	}
	/**
	 * 商品供应商查询
	 * 此方法只能查询店铺所在区县的供应商中提供该商品的供应商
	 * @param shopUnique 店铺编号
	 * @param goodsBarcode 商品条码
	 * @return
	 */
	@RequestMapping("/queryGoodsSupplier.do")
	@ResponseBody
	public ShopsResult queryGoodsSupplier(
			@RequestParam(value="shopUnique",required=true)String shopUnique,
			@RequestParam(value="goodsBarcode",required=true)String goodsBarcode){
		return goodsService.queryGoodsSupplier(shopUnique, goodsBarcode);
	}
	/**
	 * 更新图片信息
	 * @param shop_unique 供货商编号
	 * @param request 图片请求
	 * @param goods_barcode 商品条码
	 * @return
	 */
	@RequestMapping("/updateGoodsImage.do")
	@ResponseBody
	public ShopsResult updateGoodsImage(
			@RequestParam(value="shop_unique",required=true)String shop_unique,
			HttpServletRequest request,
			@RequestParam(value="goods_barcode",required=true)String goods_barcode,
			String goods_name,String goods_kind_unique,
			String goods_brand,
			Double goods_in_price,
			Double  goods_sale_price,
			Integer goods_life,
			Integer goods_points,
			Integer goods_count,
			Integer goods_sold,
			String goods_standard,
			String default_supplier_unique,
			String goods_address,
			String goods_remarks,
			String goods_unit,
			String sup_goods_barcode
			){
		System.out.println("后台商品信息更新：：：：："+shop_unique+":::::::"+goods_barcode+"::::::"+goods_kind_unique+":::::"+goods_sale_price);
		return goodsService.updateGoodsImage(shop_unique, request, goods_barcode,goods_name ,goods_kind_unique, 
				goods_brand,goods_in_price,goods_sale_price,goods_life,goods_points,goods_count,goods_sold,goods_standard,default_supplier_unique,goods_address,goods_remarks,goods_unit,sup_goods_barcode);
	}
	
	/**
	 * 添加新商品
	 * @param shop_unique 商铺编号
	 * @param goods_barcode 商品条码
	 * @param goods_name 商品名称
	 * @param goods_kind_unique 商品分类编号
	 * @param goods_brand 商品品牌
	 * @param goods_in_price 商品进价
	 * @param goods_sale_price 商品售价
	 * @param goods_life 商品保质期
	 * @param goods_points 会员积分
	 * @param goods_count 商品库存量
	 * @param goods_sold 销售数量
	 * @param goods_standard 商品规格
	 * @param default_supplier_unique 默认供货商编号
	 * @return
	 */
	@RequestMapping("/addNewGoods.do")
	@ResponseBody
	public ShopsResult addNewGoods(
			@RequestParam(value="shop_unique",required=true)String shop_unique,
			@RequestParam(value="goods_barcode",required=true)String goods_barcode,String goods_name,String goods_kind_unique,
			String goods_brand,Double goods_in_price,Double  goods_sale_price,
			Integer goods_life,Integer goods_points,Integer goods_count,Integer goods_sold,String goods_standard,
			String default_supplier_unique,String goods_address,String goods_remarks){
		return goodsService.addNewGoods(shop_unique, goods_barcode, goods_name, goods_kind_unique, goods_brand, goods_in_price,
				goods_sale_price, goods_life, goods_points, goods_count, goods_sold, goods_standard, default_supplier_unique,goods_address,goods_remarks);
	}
	
	@ResponseBody
	@RequestMapping("/goodsTest.do")
	public ShopsResult goodsTest(){
		return goodsService.goodsTest();
	}
	
	@RequestMapping("/selectName.do")
	@ResponseBody
	public ShopsResult selectName(Integer pageNum){
		return goodsService.selectName(pageNum);
	}
	
	@RequestMapping("search.do")
	@ResponseBody
	public ShopsResult selectGoods(Integer pageNum){
		return goodsService.selectGoods(pageNum);
	}
	
	/**
	 * 商品信息查询，用于饼状图制作
	 * @param shop_unique
	 * @param days
	 * @return
	 */
	@RequestMapping("/goodsCount.do")
	@ResponseBody
	public ShopsResult goodsCount(
			@RequestParam(value="shop_unique",required=true)String shop_unique,
			@RequestParam(value="days",defaultValue="30")Integer days){
		System.out.println("/html/goods/goodsCount.do");
		return goodsService.goodsCount(shop_unique, days);
	}
	
	/**
	 * 商品信息查询，用于饼状图金额制作
	 * @param shop_unique
	 * @param days
	 * @return
	 */
	@RequestMapping("/goodsAmount.do")
	@ResponseBody
	public ShopsResult goodsAmount(
			@RequestParam(value="shop_unique",required=true)String shop_unique,
			@RequestParam(value="days",defaultValue="30")Integer days){
		System.out.println("/goodsAmount.do");
		return goodsService.goodsAmount(shop_unique, days);
	}
	
	/**
	 * 商品信息查询页数查询
	 * @param shop_unique
	 * @param goodsMessage
	 * @param goods_kind_unique
	 * @param goods_kind_parunique
	 * @param stockType
	 * @return
	 */
	@RequestMapping("/queryGoodsPages.do")
	@ResponseBody
	public ShopsResult queryGoodsPages(
			@RequestParam(value="shop_unique",required=true)String shop_unique,
			String goodsMessage,
			String goods_kind_unique,
			String goods_kind_parunique,
			Integer stockType,
			@RequestParam(value="pageSize",defaultValue="25")Integer pageSize){
		System.out.println("/html/goods/queryGoodsPages.do");
		return goodsService.queryGoodsPages(shop_unique, goodsMessage, goods_kind_unique, goods_kind_parunique, stockType,pageSize);
	}
	
	@RequestMapping("/bindGoodsAddSearch.do")
	@ResponseBody
	public ShopsResult bindGoodsAddSearch(
			@RequestParam(value="shopUnique",required=true)Long shopUnique,
			String goodsMessage,
			String groupUnique,
			String kindUnique
			){
		Map<String,Object> map=new HashMap<String, Object>();
		if(!"-1".equals(groupUnique)){
			map.put("groupUnique", groupUnique);
		}
		if(!"-1".equals(kindUnique)){
			map.put("kindUnique", kindUnique);
		}
		map.put("shopUnique", shopUnique);
		if(null!=goodsMessage){
			map.put("goodsMessage","%"+goodsMessage+"%");
		}
		return goodsService.bindGoodsAddSearch(map);
	}
	

	/**
	 * 促销商品页数查询
	 * @param shopUnique
	 * @return
	 */
	@RequestMapping("/promotionGoodsPages.do")
	@ResponseBody
	public ShopsResult promotionGoodsPages(
			@RequestParam(value="shopUnique",required=true)Long shopUnique,
			String goodsMessage,
			String groupUnique,
			String kindUnique,
			Integer promotionType,
			@RequestParam(value="pageSize",defaultValue="25")Integer pageSize
			){
		Map<String,Object> map=new HashMap<String, Object>();
		map.put("shopUnique", shopUnique);
		map.put("promotionType", promotionType);
		if(null!=goodsMessage){
			map.put("goodsMessage", "%"+goodsMessage+"%");
		}
		if(!"-1".equals(groupUnique)){
			map.put("groupUnique", groupUnique);
		}
		if(!"-1".equals(kindUnique)){
			map.put("kindUnique", kindUnique);
		}
		map.put("pageSize", pageSize);
		return goodsService.promotionGoodsPages(map);
	}
	

	/**
	 * 
	 * 商品信息查询
	 * @param shopUnique
	 * @return
	 */
	@RequestMapping("/promotionGoodsSearchByPage.do")
	@ResponseBody
	public ShopsResult promotionGoodsSearchByPage(
			@RequestParam(value="shopUnique",required=true)Long shopUnique,
			String goodsMessage,
			String groupUnique,
			String kindUnique,
			Integer promotionType,
			@RequestParam(value="pageSize",defaultValue="25")Integer pageSize,
			@RequestParam(value="pageNum",defaultValue="1")Integer pageNum,
			@RequestParam(value="order",defaultValue="1")Integer order,
			@RequestParam(value="orderType",defaultValue="1")Integer orderType
			){
		Map<String,Object> map=new HashMap<String, Object>();
		map.put("shopUnique", shopUnique);
		map.put("promotionType", promotionType);
		if(null!=goodsMessage){
			map.put("goodsMessage", "%"+goodsMessage+"%");
		}
		if(!"-1".equals(groupUnique)){
			map.put("groupUnique", groupUnique);
		}
		if(!"-1".equals(kindUnique)){
			map.put("kindUnique", kindUnique);
		}
		map.put("pageSize", pageSize);
		
		map.put("startNum", (pageNum-1)*pageSize);
		if(order==1){
			map.put("order", "goodsBarcode");
		}
		if(order==2){
			map.put("order", "goodsName");
		}
		if(order==3){
			map.put("order", "goodsUnit");
		}
		if(order==4){
			map.put("order", "promotionCount");
		}
		if(order==5){
			map.put("order", "goodsInPrice");
		}
		if(order==6){
			map.put("order", "goodsSalePrice");
		}
		if(order==7){
			map.put("order", "goodsDiscount");
		}
		if(order==8){
			map.put("order", "proPrice");
		}
		if(orderType==1){
			map.put("orderType", "ASC");
		}
		if(orderType==2){
			map.put("orderType", "DESC");
		}
		return goodsService.promotionGoodsSearchByPage(map);
	}
	
	/**
	 * 取消促销活动（将促销数量改为0，并将折扣改为1）
	 * @param shopUnique
	 * @return
	 */
	@RequestMapping("/cancelPromotion.do")
	@ResponseBody
	public ShopsResult cancelPromotion(
			@RequestParam(value="shopUnique",required=true)Long shopUnique,
			@RequestParam(value="goodsBarcode",required=true)String goodsBarcode,
			@RequestParam(value="goodsDiscount",defaultValue="1.00")Double goodsDiscount,
			@RequestParam(value="promotionCount",defaultValue="0")Double promotionCount
		
			){
		Map<String,Object> map=new HashMap<String, Object>();
		map.put("shopUnique", shopUnique);
		map.put("goodsBarcode", goodsBarcode);
		map.put("promotionCount", promotionCount);
		map.put("goodsDiscount", goodsDiscount);
		return goodsService.cancelPromotion(map);
	}
	
	/**
	 * 商品详情查询
	 * @param shopUnique
	 * @return
	 */
	@RequestMapping("/goodsDetail.do")
	@ResponseBody
	public ShopsResult goodsDetail(
			@RequestParam(value="shopUnique",required=true)String shopUnique,
			@RequestParam(value="goodsBarcode",required=true)String goodsBarcode){
		Map<String,Object> map=new HashMap<String, Object>();
		map.put("goodsBarcode", goodsBarcode);
		map.put("shopUnique", shopUnique);
		return goodsService.goodsDetail(map);
	}
	
	
	/**
	 * 查询商品供货商供应商品信息
	 * @param shopUnique
	 * @return
	 */
	@RequestMapping("/querySupplierGoods.do")
	@ResponseBody
	public ShopsResult querySupplierGoods(
			@RequestParam(value="supplierUnique",required=true)Long supplierUnique,
			@RequestParam(value="goodsBarcode",required=true)String goodsBarcode,
			@RequestParam(value="shopUnique",required=true)String shopUnique
			){
		Map<String,Object> map=new HashMap<String, Object>();
		map.put("supplierUnique", supplierUnique);
		map.put("goodsBarcode", goodsBarcode);
		map.put("shopUnique", shopUnique);
		return goodsService.querySupplierGoods(map);
	}
	
	/**
	 * 更新商品信息（新版，切换为多个规格的商品信息）
	 * @param shopUnique 店铺编号
	 * @param foreignKey 包装外键关键词
	 * @param kindUnique 商品分类
	 * @param goodsBarcode 基础商品信息商品条码
	 * @param goodsCount 商品库存量（以最小单位计算）
	 * @param goodsBrand 商品品牌
	 * @param supplierUnique 供货商编号
	 * @param supGoodsBarcode 选择的进货商品条码
	 * @param goodsMessage 商品信息数组
	 * @param request 图片存放
	 * @return
	 */
	
	@RequestMapping("/toUpdateGoodsMessage.do")
	@ResponseBody
	public ShopsResult updateListGoodsMessage(
			@RequestParam(value="shopUnique",required=true)Long shopUnique,
			@RequestParam(value="foreignKey",required=true)Long foreignKey,
			@RequestParam(value="kindUnique",required=true)String kindUnique,
			@RequestParam(value="goodsCount",required=true)Double goodsCount,
			@RequestParam(value="goodsBarcode",required=true)String goodsBarcode,
			Integer goodsLife,
			String goodsAddress,
			String goodsBrand,
			Long supplierUnique,
			String supGoodsBarcode,
			String goodsRemarks,
			@RequestParam(value="goodsMessage",required=true)String goodsMessage,
			HttpServletRequest request
			){
		Map<String,Object> bmap=new HashMap<String, Object>();
		bmap.put("shopUnique", shopUnique);
		bmap.put("goodsBrand", goodsBrand);
		bmap.put("foreignKey", foreignKey);
		bmap.put("supplierUnique", supplierUnique);
		if(kindUnique.equals("-1")){
			kindUnique=null;
		}
		bmap.put("kindUnique", kindUnique);
		bmap.put("goodsAddress", goodsAddress);
		bmap.put("goodsLife", goodsLife);
		bmap.put("goodsRemarks", goodsRemarks);
		if("-1".equals(supGoodsBarcode)){
			supGoodsBarcode=null;
		}
		if(supplierUnique == -1){
			supplierUnique=null;
		}
		bmap.put("supGoodsBarcode", supGoodsBarcode);
		System.out.println("后台商品信息更新:::::::"+shopUnique+":::::::"+goodsBarcode+"::::::"+kindUnique+":::::::"+goodsMessage);
		return goodsService.updateListGoodsMessage(bmap, goodsMessage, request,goodsCount,goodsBarcode,shopUnique,foreignKey);
	}
	
	
	@RequestMapping("/aaa.do")
	@ResponseBody
	public ShopsResult  aaa(){
		return goodsService.aaa();
	}
	
	/**
	 * 商品销售分类页数
	 * @return
	 */
	@RequestMapping("/queryPagesForRanking.do")
	@ResponseBody
	public ShopsResult queryPagesForRanking(
			@RequestParam(value="shopUnique",required=true)Long shopUnique,
			String startTime,
			String endTime,
			@RequestParam(value="pageSize",defaultValue="25")Integer pageSize,
			String goodsMessage,
			String parUnique,
			String kindUnique
			){
		Map<String,Object> map=new HashMap<String, Object>();
		map.put("shopUnique", shopUnique);
		if(null!=startTime){
			map.put("startTime", startTime+" 00:00:00");
		}
		if(null!=endTime){
			map.put("endTime", endTime+" 23:59:59");
		}
		if(null!=goodsMessage){
			map.put("goodsMessage", "%"+goodsMessage+"%");
		}
		if(null!=parUnique&&!parUnique.equals("0")&&!parUnique.equals("-1")){
			map.put("parUnique", parUnique);
		}
		if(null!=kindUnique&&!kindUnique.equals("0")&&!kindUnique.equals("-1")){
			map.put("kindUnique", kindUnique);
		}
		map.put("pageSize", pageSize);
		return goodsService.queryPagesForRanking(map);
	}
	
	/**
	 * 商品日销量：
	 * @param shopUnique
	 * @return
	 */
	@RequestMapping("/queryGoodsRanking.do")
	@ResponseBody
	public ShopsResult queryGoodsRanking(
			@RequestParam(value="shopUnique",required=true)Long shopUnique,
			String startTime,
			String endTime,
			@RequestParam(value="pageSize",defaultValue="25")Integer pageSize,
			@RequestParam(value="pageNum",defaultValue="1")Integer pageNum,
			String goodsMessage,
			String parUnique,
			String kindUnique,
			Integer order,
			Integer orderType
			){
		Map<String,Object> map=new HashMap<String, Object>();
		map.put("shopUnique", shopUnique);
		if(null!=startTime){
			map.put("startTime", startTime+" 00:00:00");
		}
		if(null!=endTime){
			map.put("endTime", endTime+" 23:59:59");
		}
		if(null!=goodsMessage&&!goodsMessage.equals("")){
			map.put("goodsMessage", "%"+goodsMessage+"%");
		}
		if(null!=parUnique&&!parUnique.equals("0")&&!parUnique.equals("-1")){
			map.put("parUnique", parUnique);
		}
		if(null!=kindUnique&&!kindUnique.equals("0")&&!kindUnique.equals("-1")){
			map.put("kindUnique", kindUnique);
		}
		map.put("pageSize", pageSize);
		map.put("startNum", (pageNum-1)*pageSize);
		if(order==1){
			map.put("order", "goodsName");
		}
		if(order==2){
			map.put("order", "goodsBarcode");
		}
		if(order==3){
			map.put("order", "saleTotal");
		}
		if(order==4){
			map.put("order", "saleCount");
		}
		if(orderType==1){
			map.put("orderType", "ASC");
		}
		if(orderType==2){
			map.put("orderType", "DESC");
		}
		return goodsService.queryGoodsRanking(map);
	}

	/**
	 * 商品日销量界面：商品销售详情
	 * @param shopUnique
	 * @return
	 */
	@RequestMapping("/queryRankingDetail.do")
	@ResponseBody
	public ShopsResult queryRankingDetail(
			@RequestParam(value="shopUnique",required=true)Long shopUnique,
			@RequestParam(value="goodsBarcode",required=true)String goodsBarcode,
			Integer order,
			Integer orderType
			){
		Map<String,Object> map=new HashMap<String, Object>();
		map.put("shopUnique", shopUnique);
		map.put("goodsBarcode", goodsBarcode);
		if(order==1){
			map.put("order", "saleListUnique");
		}
		if(order==2){
			map.put("order", "detailCount");
		}
		if(order==3){
			map.put("order", "detailPrice");
		}
		if(order==4){
			map.put("order", "saleListTime");
		}
		if(order==5){
			map.put("order", "subTotal");
		}
		if(orderType==1){
			map.put("orderType", "DESC");
		}
		if(orderType==2){
			map.put("orderType", "ASC");
		}
		return goodsService.queryRankingDetail(map);
	}
	
	/**
	 * 出入库记录：页数查询
	 * @param shopUnique
	 * @return
	 */
	@RequestMapping("/queryStockRecordPages.do")
	@ResponseBody
	public ShopsResult queryStockRecordPages(
			@RequestParam(value="shopUnique")Long shopUnique,
			String startTime,
			String endTime,
			String parUnique,
			String kindUnique,
			String goodsMessage,
			Integer stockType,
			Integer stockResource,
			@RequestParam(value="pageSize",defaultValue="25")Integer pageSize
			){
		Map<String,Object> map=new HashMap<String, Object>();
		map.put("shopUnique", shopUnique);
		map.put("startTime", startTime);
		if(endTime!=null){
			map.put("endTime", endTime+" 23:59:59");
		}
		if(!parUnique.equals("-1")){
			map.put("parUnique", parUnique);
		}
		if(!kindUnique.equals("-1")){
			map.put("kindUnique", kindUnique);
		}
		if(null==stockType||stockType==-1){
			stockType=null;
		}
		map.put("stockType", stockType);
		if(null==stockResource||stockResource==-1){
			stockResource=null;
		}
		map.put("stockResource", stockResource);
		if(null==goodsMessage||goodsMessage.equals("")){
		}else{
			map.put("goodsMessage","%"+goodsMessage+"%");
		}
		map.put("pageSize", pageSize);
		return goodsService.queryStockRecordPages(map);
	}
	
	/**
	 * 商品出入库记录查询
	 * @param shopUnique
	 * @return
	 */
	@RequestMapping("/queryGoodsRecordByPage.do")
	@ResponseBody
	public PurResult queryGoodsRecordByPage(
			@RequestParam(value="shopUnique")Long shopUnique,
			String startTime,
			String endTime,
			String parUnique,
			String kindUnique,
			String goodsMessage,
			Integer stockType,
			Integer stockResource,
			@RequestParam(value="pageSize",defaultValue="8")Integer pageSize,
			@RequestParam(value="pageNum",defaultValue="1")Integer pageNum,
			@RequestParam(value="order",defaultValue="1")Integer order,
			@RequestParam(value="orderType",defaultValue="1")Integer orderType
			){
		Map<String,Object> map=new HashMap<String, Object>();
		map.put("shopUnique", shopUnique);
		map.put("startTime", startTime);
		if(endTime!=null){
			map.put("endTime", endTime+" 23:59:59");
		}
		if(!parUnique.equals("-1")){
			map.put("parUnique", parUnique);
		}
		if(!kindUnique.equals("-1")){
			map.put("kindUnique", kindUnique);
		}
		if(null==stockType||stockType==-1){
			stockType=null;
		}
		map.put("stockType", stockType);
		if(null==stockResource||stockResource==-1){
			stockResource=null;
		}
		map.put("stockResource", stockResource);
		if(null==goodsMessage||goodsMessage.equals("")){
		}else{
			map.put("goodsMessage","%"+goodsMessage+"%");
		}
		map.put("pageSize", pageSize);
		map.put("startNum", (pageNum-1)*pageSize);
		if(order==1){
			map.put("order", "goodsName");
		}
		if(order==2){
			map.put("order", "goodsBarcode");
		}
		if(order==3){
			map.put("order", "goodsCount");
		}
		if(order==4){
			map.put("order", "stockCount");
		}
		if(order==5){
			map.put("order", "stockTime");
		}
		if(order==6){
			map.put("order", "listUnique");
		}
		if(orderType==1){
			map.put("orderType", "ASC");
		}
		if(orderType==2){
			map.put("orderType", "DESC");
		}
		return goodsService.queryGoodsRecordByPage(map);
	}
	
	@RequestMapping("/EmpRecordExcel.do")
	@ResponseBody
	public void EmpRecordExcel(
			@RequestParam(value="shopUnique",required=true)String shopUnique,
			String goodsMessage,
			Timestamp startTime,
			Timestamp endTime,
			String parUnique,
			String kindUnique,
			String stockType,
			String stockResource,
			String order,
			String orderType,
			HttpServletRequest request,
			HttpServletResponse response
			){
		String startTime_str = "";  
		String endTime_str = "";  
        DateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");  
        try {  
        	startTime_str = sdf.format(startTime);
        	endTime_str = sdf.format(endTime);
        } catch (Exception e) {  
            startTime_str=null;
        }  
		Map<String,Object> map=new HashMap<String, Object>();
		map.put("shopUnique", shopUnique);
		map.put("startTime", startTime_str);
		map.put("endTime", endTime_str);
		if(!stockResource.equals("-1")){
			map.put("stockResource", stockResource);
		}
		if(!stockType.equals("-1")){
			map.put("stockType", stockType);
		}
		if(!parUnique.equals("-1")){
			map.put("parUnique", parUnique);
		}
		if(!kindUnique.equals("-1")){
			map.put("kindUnique", kindUnique);
		}
		
		if(goodsMessage!=null&&!"".equals(goodsMessage)){
			map.put("goodsMessage", "%"+goodsMessage+"%");
		}
		List<GoodsInfo> data= goodsService.ExcelGoodsRecord(map);
		loadRecordExcel(data,"出入库记录Excel",request,response);
	}
	
	public void loadRecordExcel(List<GoodsInfo> caseVoList, final String string,HttpServletRequest request,HttpServletResponse response) {
		try {
			response.setContentType(
					"application/x-msdownload; charset=UTF-8");// 告知浏览器文件类型
			String agent = request.getHeader("user-agent");
			if (agent.contains("Firefox")) {
				response.setHeader(
						"Content-Disposition",
						"attachment;filename="
								+ new String((string + ".xls")
										.getBytes("GB2312"), "ISO-8859-1"));// 设置文件头
			} else {
				response.setHeader(
						"Content-Disposition",
						"attachment;filename="
								+ URLEncoder.encode(string + ".xls", "UTF-8"));// 设置文件头
			}
			OutputStream os = response.getOutputStream();
			LoadOutObjectXLSUtil<GoodsInfo> objectXLS = new LoadOutObjectXLSUtil<GoodsInfo>();
			objectXLS.generateXLS(os, caseVoList, new XLSCallBack<GoodsInfo>() {
				public Object[] getValues(GoodsInfo tt) {
					String str = "";
					
					String goodsBarcode ="";
					goodsBarcode=tt.getGoodsBarcode().toString();
					String goodsName = tt.getGoodsName();
					if (StringUtil.blank(goodsName)) {
						goodsName = str;
					}
					String goodsCount = tt.getGoodsCount().toString();
					if (StringUtil.blank(goodsCount)) {
						goodsCount = str;
					}
					String stockCount = tt.getStockCount().toString();
					if (StringUtil.blank(stockCount)) {
						stockCount = str;
					}
					String stockTime = tt.getStockTime().toString();
					if (StringUtil.blank(stockTime)) {
						stockTime = str;
					}
					String listUnique = tt.getListUnique();
					if (StringUtil.blank(listUnique)) {
						listUnique = str;
					}
					String stockType = tt.getStockType();
					if (StringUtil.blank(stockType)) {
						stockType = str;
					}
					String stockResource = tt.getStockResource();
					if (StringUtil.blank(stockResource)) {
						stockResource = str;
					}
					
					return new String[] {goodsBarcode,goodsName,goodsCount,stockCount,
							listUnique,stockType,stockResource,stockTime};
				}

				public String getTitle() {
					return string;
				}

				public int[] getColumnsWidth() {
					return new int[] { 
										  80 * 80, 80 * 80,80 * 80,80 * 80,
										  80 * 80, 80 * 80,80 * 80,80 * 80};
				}

				public String[] getColumnsName() {
					return new String[] { "商品条码","商品名称","商品数量","库存数量",
										  "订单编号","出入库类型","操作类型","操作日期"	};
				}

				public int[][] MergedRegion() {
					// TODO Auto-generated method stub
					return null;
				}
				public List<Object[]> getheaderValue() {
					// TODO Auto-generated method stub
					return null;
				}

			});
			os.flush();
		} catch (IOException e) {
			e.printStackTrace();
		}
	}
	/**
	 * 打印PDF
	 * @param managerUnique
	 * @param orderNo
	 * @return
	 */
	@RequestMapping("/printAlloration.do")
	public String printAlloration(
			@RequestParam(value="managerUnique",required=true)String managerUnique,
			String orderNo,
			String startTime,
			String endTime,
			Long storehouse_outList,
			Long storehouse_inList,
			HttpServletRequest request){
		request.setAttribute("managerUnique", managerUnique);
		request.setAttribute("startTime", startTime);
		request.setAttribute("endTime", endTime);
		request.setAttribute("storehouse_outList", storehouse_outList);
		request.setAttribute("storehouse_inList", storehouse_inList);
		request.setAttribute("orderNo", orderNo);
		return "/html/goods/printAlloration.jsp";
	}
	
	@RequestMapping("/printAllorationList.do")
	@ResponseBody
	public ShopsResult printAllorationList(
			@RequestParam(value="managerUnique",required=true)String managerUnique,
			String orderNo,
			String startTime,
			String endTime,
			Long storehouse_outList,
			Long storehouse_inList,
			HttpServletRequest request){
		Map<String,Object> map=new HashMap<String, Object>();
		map.put("managerUnique", managerUnique);
		map.put("startTime", startTime);
		map.put("endTime", endTime);
		if(storehouse_outList!=null && storehouse_outList != -1){
			map.put("storehouse_outList", storehouse_outList);
		}
		if(storehouse_inList!=null && storehouse_inList != -1){
			map.put("storehouse_inList", storehouse_inList);
		}
		
		if(orderNo!=null&&!"".equals(orderNo)){
			map.put("orderNo", "%"+orderNo+"%");
		}
		return goodsService.printAllorationList(map);
	}
	@RequestMapping("/EmpAllorationExcel.do")
	@ResponseBody
	public void EmpAllorationExcel(
			@RequestParam(value="managerUnique",required=true)String managerUnique,
			String orderNo,
			String startTime,
			String endTime,
			Long storehouse_outList,
			Long storehouse_inList,
			HttpServletRequest request,
			HttpServletResponse response
	){
		
		Map<String,Object> map=new HashMap<String, Object>();
		map.put("managerUnique", managerUnique);
		map.put("startTime", startTime);
		map.put("endTime", endTime);
		if(storehouse_outList!=null && storehouse_outList != -1){
			map.put("storehouse_outList", storehouse_outList);
		}
		if(storehouse_inList!=null && storehouse_inList != -1){
			map.put("storehouse_inList", storehouse_inList);
		}
		
		if(orderNo!=null&&!"".equals(orderNo)){
			map.put("orderNo", "%"+orderNo+"%");
		}
		List<GoodsInfo> data= goodsService.ExcelGoodsAlloration(map);
		loadAllorationExcel(data,"商品调拨记录Excel",request,response);
	}
	
	public void loadAllorationExcel(List<GoodsInfo> caseVoList, final String string,HttpServletRequest request,HttpServletResponse response) {
		try {
			response.setContentType(
					"application/x-msdownload; charset=UTF-8");// 告知浏览器文件类型
			String agent = request.getHeader("user-agent");
			if (agent.contains("Firefox")) {
				response.setHeader(
						"Content-Disposition",
						"attachment;filename="
								+ new String((string + ".xls")
										.getBytes("GB2312"), "ISO-8859-1"));// 设置文件头
			} else {
				response.setHeader(
						"Content-Disposition",
						"attachment;filename="
								+ URLEncoder.encode(string + ".xls", "UTF-8"));// 设置文件头
			}
			OutputStream os = response.getOutputStream();
			LoadOutObjectXLSUtil<GoodsInfo> objectXLS = new LoadOutObjectXLSUtil<GoodsInfo>();
			objectXLS.generateXLS(os, caseVoList, new XLSCallBack<GoodsInfo>() {
				public Object[] getValues(GoodsInfo tt) {
					String str = "";
					
					String purchase_list_unique ="";
					purchase_list_unique=tt.getPurchase_list_unique().toString();
					
					String storehouse_name_out = tt.getStorehouse_name_out();
					if (StringUtil.blank(storehouse_name_out)) {
						storehouse_name_out = str;
					}
					String storehouse_name_in = tt.getStorehouse_name_in().toString();
					if (StringUtil.blank(storehouse_name_in)) {
						storehouse_name_in = str;
					}
					String purchase_list_date = tt.getPurchase_list_date().toString();
					if (StringUtil.blank(purchase_list_date)) {
						purchase_list_date = str;
					}
					
					return new String[] {purchase_list_unique,storehouse_name_out,storehouse_name_in,purchase_list_date};
				}

				public String getTitle() {
					return string;
				}

				public int[] getColumnsWidth() {
					return new int[] { 
										  80 * 80, 80 * 80,80 * 80,80 * 80};
				}

				public String[] getColumnsName() {
					return new String[] { "调拨单编号"," 调出仓库","调入仓库","调拨日期"};
				}

				public int[][] MergedRegion() {
					// TODO Auto-generated method stub
					return null;
				}

				public List<Object[]> getheaderValue() {
					// TODO Auto-generated method stub
					return null;
				}

			});
			os.flush();
		} catch (IOException e) {
			e.printStackTrace();
		}
	}
	
	@RequestMapping("/EmpGoodsExcel.do")
	@ResponseBody
	public void EmpGoodsExcel(
			String shopUnique,
			String goodsMessage,
			String parUnique,
			String kindUnique,
			HttpServletRequest request,
			HttpServletResponse response
			){
		Map<String,Object> map=new HashMap<String, Object>();
		map.put("shop_unique", shopUnique);
		if(!parUnique.equals("-1")){
			map.put("parUnique", parUnique);
		}
		if(!kindUnique.equals("-1")){
			map.put("kindUnique", kindUnique);
		}
		
		if(goodsMessage!=null&&!"".equals(goodsMessage)){
			map.put("goodsMessage", "%"+goodsMessage+"%");
		}
		List<GoodsInfo> data= goodsService.ExcelGoodsInfo(map);
		loadGoodsInfoExcel(data,"商品信息Excel",request,response);
	}
	public void loadGoodsInfoExcel(List<GoodsInfo> caseVoList, final String string,HttpServletRequest request,HttpServletResponse response) {
		try {
			response.setContentType(
					"application/x-msdownload; charset=UTF-8");// 告知浏览器文件类型
			String agent = request.getHeader("user-agent");
			if (agent.contains("Firefox")) {
				response.setHeader(
						"Content-Disposition",
						"attachment;filename="
								+ new String((string + ".xls")
										.getBytes("GB2312"), "ISO-8859-1"));// 设置文件头
			} else {
				response.setHeader(
						"Content-Disposition",
						"attachment;filename="
								+ URLEncoder.encode(string + ".xls", "UTF-8"));// 设置文件头
			}
			OutputStream os = response.getOutputStream();
			LoadOutObjectXLSUtil<GoodsInfo> objectXLS = new LoadOutObjectXLSUtil<GoodsInfo>();
			objectXLS.generateXLS(os, caseVoList, new XLSCallBack<GoodsInfo>() {
				public Object[] getValues(GoodsInfo tt) {
					String str = "";
					String goodsBarcode ="";
					goodsBarcode=tt.getGoodsBarcode().toString();
					String goodsKindNameFirst = tt.getGoodsKindNameFirst();
					if (StringUtil.blank(goodsKindNameFirst)) {
						goodsKindNameFirst = str;
					}
					String goodsKindNameSecond = tt.getGoodsKindNameSecond();
					if (StringUtil.blank(goodsKindNameSecond)) {
						goodsKindNameSecond = str;
					}
					String goodsName = tt.getGoodsName();
					if (StringUtil.blank(goodsName)) {
						goodsName = str;
					}
					String goodsCount = tt.getGoodsCount().toString();
					if (StringUtil.blank(goodsCount)) {
						goodsCount = str;
					}
					
					String goods_sold = tt.getGoods_sold().toString();
					if (StringUtil.blank(goods_sold)) {
						goods_sold = str;
					}
					String goods_unit = tt.getGoods_unit().toString();
					if (StringUtil.blank(goods_unit)) {
						goods_unit = str;
					}
					String goods_in_price = tt.getGoods_in_price();
					if (StringUtil.blank(goods_in_price)) {
						goods_in_price = str;
					}
					String goods_sale_price = tt.getGoods_sale_price();
					if (StringUtil.blank(goods_sale_price)) {
						goods_sale_price = str;
					}
					String goods_cus_price = tt.getGoods_cus_price();
					if (StringUtil.blank(goods_cus_price)) {
						goods_cus_price = str;
					}
					String shop_name = tt.getShop_name();
					if (StringUtil.blank(shop_name)) {
						shop_name = str;
					}
					
					return new String[] {goodsBarcode, goodsKindNameFirst, goodsKindNameSecond, goodsName,goods_unit,goods_sold,goods_in_price,
							goods_sale_price,goods_cus_price,goodsCount,shop_name};
				}

				public String getTitle() {
					return string;
				}

				public int[] getColumnsWidth() {
					return new int[] { 
										  80 * 80, 80 * 80, 80 * 80, 80 * 80,80 * 80,80 * 80,
										  80 * 80, 80 * 80,80 * 80,80 * 80,80 * 80};
				}

				public String[] getColumnsName() {
					return new String[] { "商品条码","商品大类","商品小类","商品名称","单位","销量",
										  "进货价","销售价","会员价","库存量","所属店铺"	};
				}

				public int[][] MergedRegion() {
					// TODO Auto-generated method stub
					return null;
				}

				public List<Object[]> getheaderValue() {
					// TODO Auto-generated method stub
					return null;
				}

			});
			os.flush();
		} catch (IOException e) {
			e.printStackTrace();
		}
	}
	
	@RequestMapping("/goodsCostQuery.do")
	@ResponseBody
	public ShopsResult goodsCostQuery(
			Long shopUnique,
			String goodsBarcode,
			String datetimeStart,
			String datetimeEnd
			){
		Map<String,Object> map=new HashMap<String, Object>();
		map.put("shopUnique", shopUnique);
		map.put("goodsBarcode", goodsBarcode);
		map.put("datetimeStart", datetimeStart);
		map.put("datetimeEnd", datetimeEnd);
		return goodsService.goodsCostQuery(map);
	}
	
	
	/**
	 * 删除店铺的重复商品并保留最大ID的商品信息
	 * @param shopUnique
	 * @return
	 */
	@RequestMapping("/deleteSameGoods.do")
	@ResponseBody
	public ShopsResult deleteSameGoods(
			@RequestParam(value="shopUnique",required=true)String shopUnique){
		return goodsService.deleteSameGoods(shopUnique);
	}

	@RequestMapping("/queryGoodsSaleMessagePages.do")
	@ResponseBody
	public ShopsResult queryGoodsSaleMessagePages(
			String goodsMessage,
			String startTime,
			String endTime,
			@RequestParam(value="pageSize",defaultValue="25")Integer pageSize,
			String groupUnique,
			String kindUnique
			){
		Map<String,Object> map=new HashMap<String, Object>();
		map.put("startTime", startTime);
		map.put("endTime", endTime);
		if(null!=kindUnique&&!kindUnique.equals("-1")){
			map.put("kindUnique", kindUnique);
		}else if(null==groupUnique||groupUnique.equals("-1")){
		}else{
			map.put("groupUnique", groupUnique);
		}
		map.put("pageSize", pageSize);
		if(null==goodsMessage||goodsMessage.equals("")){
		}else{
			map.put("goodsMessage", "%"+goodsMessage+"%");
		}
		return goodsService.queryGoodsSaleMessagePages(map);
	}
	/**
	 * 商品销量排行界面；分页查询
	 * @param goodsMessage
	 * @return
	 */
	@RequestMapping("/queryGoodsByPage.do")
	@ResponseBody
	public ShopsResult queryGoodsByPage(
			String goodsMessage,
			String startTime,
			String endTime,
			@RequestParam(value="pageSize",defaultValue="25")Integer pageSize,
			String groupUnique,
			String kindUnique,
			@RequestParam(value="pageNum",defaultValue="1")Integer pageNum,
			@RequestParam(value="order",defaultValue="1")Integer order,
			@RequestParam(value="orderType",defaultValue="1")Integer orderType
			){
		Map<String,Object> map=new HashMap<String, Object>();
		map.put("startTime", startTime);
		map.put("endTime", endTime);
		if(null!=kindUnique&&!kindUnique.equals("-1")){
			map.put("kindUnique", kindUnique);
		}else if(null==groupUnique||groupUnique.equals("-1")){
		}else{
			map.put("groupUnique", groupUnique);
		}
		map.put("pageSize", pageSize);
		if(null==goodsMessage||goodsMessage.equals("")){
		}else{
			map.put("goodsMessage", "%"+goodsMessage+"%");
		}
		map.put("startNum", (pageNum-1)*pageSize);
		
		if(order==1){
			map.put("order", "goodsBarcode");
		}
		if(order==2){
			map.put("order", "goodsName");
		}
		if(order==3){
			map.put("order", "saleCount");
		}
		if(order==4){
			map.put("order", "averPrice");
		}
		if(order==5){
			map.put("order", "saleSum");
		}
		if(order==6){
			map.put("order", "listCount");
		}
		if(order==7){
			map.put("order", "shopCount");
		}
		if(orderType==1){
			map.put("orderType", "DESC");
		}
		if(orderType==2){
			map.put("orderType", "ASC");
		}
		
		return goodsService.queryGoodsByPage(map);
	}
	
	/**
	 * 销售商品的店铺数量查询
	 * @return
	 */
	@RequestMapping("/queryGoodsLateralSalePages.do")
	@ResponseBody
	public ShopsResult queryGoodsLateralSalePages(
			@RequestParam(value="startTime")String startTime,
			@RequestParam(value="endTime")String endTime,
			@RequestParam(value="goodsBarcode",required=true)String goodsBarcode,
			@RequestParam(value="pageSize")Integer pageSize
			){
		Map<String,Object> map=new HashMap<String, Object>();
		map.put("startTime", startTime);
		map.put("endTime", endTime);
		map.put("goodsBarcode", goodsBarcode);
		return goodsService.queryGoodsLateralSalePages(map);
	}
	/**
	 * 分页查询店铺某商品销售信息
	 * @param goodsBarcode
	 * @return
	 */
	@RequestMapping("/queryGoodsLateralSaleByPage.do")
	@ResponseBody
	public ShopsResult queryGoodsLateralSaleByPage(
			String startTime,
			String endTime,
			@RequestParam(value="pageNum",defaultValue="1")Integer pageNum,
			@RequestParam(value="pageSize",defaultValue="25")Integer pageSize,
			@RequestParam(value="order",defaultValue="1")Integer order,
			@RequestParam(defaultValue="1")Integer orderType,
			@RequestParam(value="goodsBarcode")String goodsBarcode
			){
		Map<String,Object> map=new HashMap<String, Object>();
		map.put("startTime", startTime);
		map.put("endTime", endTime);
		map.put("startNum", (pageNum-1)*pageSize);
		map.put("pageSize", pageSize);
		map.put("goodsBarcode", goodsBarcode);
		if(order==1){
			map.put("order", "shopName");
		}
		if(order==2){
			map.put("order", "saleCount");
		}
		if(order==3){
			map.put("order", "saleSum");
		}
		if(order==4){
			map.put("order", "averPrice");
		}
		if(order==5){
			map.put("order", "listCount");
		}
		if(orderType==1){
			map.put("orderType", "DESC");
		}
		if(orderType==2){
			map.put("orderType", "ASC");
		}
		return goodsService.queryGoodsLateralSaleByPage(map);
	}
	
	/**
	 * 商品销量纵向比较
	 * @param startTime
	 * @param endTime
	 * @return
	 */
	@RequestMapping("/queryGoodsProtraitPages.do")
	@ResponseBody
	public ShopsResult queryGoodsProtraitPages(
			@RequestParam(value="startTime") String startTime,
			@RequestParam(value="endTime")String endTime,
			@RequestParam(value="pageSize",defaultValue="25")Integer pageSize
			){
		Map<String,Object> map=new HashMap<String, Object>();
		map.put("startTime", startTime);
		map.put("endTime", endTime);
		map.put("pageSize", pageSize);
		return goodsService.queryGoodsProtraitPages(map);
	}
	
	@RequestMapping("/queryGoodsProtraitByPage.do")
	@ResponseBody
	public ShopsResult queryGoodsProtraitByPage(
			@RequestParam(value="startTime") String startTime,
			@RequestParam(value="endTime")String endTime,
			@RequestParam(value="pageSize",defaultValue="25")Integer pageSize,
			@RequestParam(value="goodsBarcode")String goodsBarcode,
			@RequestParam(value="shopUnique")String shopUnique,
			@RequestParam(value="pageNum",defaultValue="1")Integer pageNum,
			@RequestParam(value="order",defaultValue="1")Integer order,
			@RequestParam(value="orderType",defaultValue="1")Integer orderType
			){
		Map<String,Object> map=new HashMap<String, Object>();
		map.put("startTime", startTime);
		map.put("endTime", endTime);
		map.put("pageSize", pageSize);
		map.put("startNum", (pageNum-1)*pageSize);
		map.put("goodsBarcode", goodsBarcode);
		map.put("shopUnique", shopUnique);
		if(order==1){
			map.put("order", "datelist");
		}
		if(order==2){
			map.put("order", "saleCount");
		}
		if(order==3){
			map.put("order", "averPrice");
		}
		if(order==4){
			map.put("order", "saleSum");
		}
		if(order==5){
			map.put("order", "listCount");
		}
		if(orderType==1){
			map.put("orderType", "DESC");
		}
		if(orderType==2){
			map.put("orderType", "ASC");
		}
		return goodsService.queryGoodsProtraitByPage(map);
	}
	@RequestMapping("/addGoodsPage.do")
	public String addGoodsPage(String flag,String goods_barcode,String shop_unique,Model model){
		model.addAttribute("flag", flag);
		model.addAttribute("goods_barcode", goods_barcode);
		model.addAttribute("shop_unique", shop_unique);
		return "/WEB-INF/goods/addGoods1.jsp";
	}
	
	@RequestMapping("/addGoodsPage_wj.do")
	public String addGoodsPage_wj(String flag,String goods_barcode,Model model){
		model.addAttribute("flag", flag);
		model.addAttribute("goods_barcode", goods_barcode);
		return "/WEB-INF/goods/addGoods_wj.jsp";
	}
	
	@RequestMapping("/goodsDetailPage.do")
	public String goodsDetailPage(String goods_id,String goods_picturepath,String goods_name,String goods_standard,Model model){
		Map<String ,Object> params = new HashMap<String, Object>();
		params.put("goods_id", goods_id);
		//获取商品详情信息
		params.put("type", 2);
		List<Map<String ,Object>> goodsDetails = goodsService.getGoodsDetails(params);
		//获取商品轮播图列表
		params.put("type", 1);
		List<Map<String ,Object>> carouselImgList = goodsService.getGoodsDetails(params);
		//获取视频列表
		params.put("type", 3);
		List<Map<String,Object>> vedio = goodsService.getGoodsDetails(params);
		//获取商品详情
		params.put("type",4);
		List<Map<String, Object>> gDetail = goodsService.getGoodsDetails(params);
		if (!ObjectUtils.isEmpty(gDetail)){ //如果已经存在4了，则把2清空
			goodsDetails = new ArrayList<>();
		}

		List<Map<String,Object>> monitorList = goodsService.queryMonitorList(null,goods_id);
		model.addAttribute("monitorList", monitorList);
		model.addAttribute("goodsDetails", goodsDetails);
		model.addAttribute("carouselImgList", carouselImgList);
		model.addAttribute("goods_picturepath", goods_picturepath);
		model.addAttribute("goods_name", goods_name);
		model.addAttribute("goods_standard", goods_standard);
		model.addAttribute("goods_id", goods_id);
		model.addAttribute("details_vedio_url", (vedio == null || vedio.isEmpty()) ? null : vedio.get(0).get("details_img_url"));
		model.addAttribute("content", ObjectUtils.isEmpty(gDetail) ? "" : gDetail.get(0).get("details_img_url"));
		return "/WEB-INF/goods/goodsDetails.jsp";
	}
	
	/**
	 * 输入条码后，获取云库商品或本店商品
	 * @param shop_unique
	 * @return
	 */
	@RequestMapping("/getCloudMessage.do")
	@ResponseBody
	public PurResult getCloudMessage(
			@RequestParam(value="shop_unique",required=true)Long shop_unique,
			@RequestParam(value="goodsBarcode",required=true)String goodsBarcode
			){
		Map<String,Object> map=new HashMap<String, Object>();
		map.put("shop_unique", shop_unique);
		map.put("goodsBarcode", goodsBarcode);
		return goodsService.getCloudMessage(map);
	}
	/**
	 * 将商品信息保存，并更新云库商品
	 * 此处为保证所有商品的foreignKey一致，将所有同条码的商品更新为相同
	 * 此处先更新本店铺的商品信息，若更新不成功，则将新商品添加，否则仅更新
	 * @param sameType:售价和网购价是否同步
	 * @return
	 */
	@RemoteLog(title="新增或修改商品信息", businessType = BusinessType.UPDATE)
	@RequestMapping("/saveGoodsMessage.do")
	@ResponseBody
	public PurResult saveGoodsMessage(
			@RequestParam(value="shop_unique",required=true)String shop_unique,
			@RequestParam(value="kindUnique",required=true)String kindUnique,
			@RequestParam(value="goodsMessage",required=true)String goodsMessage,
			String operateType,
			String goodsBrand,
			String goodsRemarks,
			String foreignKey,
			HttpServletRequest request,
			@RequestParam(value="goodsGround",defaultValue="1")Integer goodsGround,//上架状态（默认上架）,
			String userAgent,
			String ip,
			String goods_barcode,
			String outStockCount,
			String autoGoodsJson,
			String goodsDelete,
			String staff_id,
			String defaultSupplierUnique,
			@RequestParam(value="sameType",defaultValue="1")Integer sameType,
			Integer goodsChengType
			){
		String manager_unique= ((Staff)request.getSession().getAttribute("staff")).getManager_unique();
		System.out.println("用户管理编号"+manager_unique);
		return goodsService.saveGoodsMessage(operateType, shop_unique, goodsBrand, kindUnique, goodsMessage, goodsRemarks, foreignKey,
				goodsGround,request,ip,userAgent,goods_barcode,outStockCount,autoGoodsJson,goodsDelete,staff_id,sameType,goodsChengType,defaultSupplierUnique,manager_unique);
	}
	@RequestMapping("/saveGoodsMessage_wj.do")
	@ResponseBody
	public PurResult saveGoodsMessage_wj(
			@RequestParam(value="shop_unique",required=true)String shop_unique,
			@RequestParam(value="kindUnique",required=true)String kindUnique,
			@RequestParam(value="goodsMessage",required=true)String goodsMessage,
			String goodsBrand,
			String goodsRemarks,
			String foreignKey,
			HttpServletRequest request,
			@RequestParam(value="goodsGround",defaultValue="1")Integer goodsGround,//上架状态（默认上架）,
			String userAgent,
			String ip,
			String goods_barcode,
			String outStockCount,
			String autoGoodsJson,
			String goodsDelete,
			String staff_id,
			String defaultSupplierUnique,
			@RequestParam(value="sameType",defaultValue="1")Integer sameType,
			Integer goodsChengType
			){
		String manager_unique= ((Staff)request.getSession().getAttribute("staff")).getManager_unique();
		System.out.println("用户管理编号"+manager_unique);
		return goodsService.saveGoodsMessage_wj(shop_unique, goodsBrand, kindUnique, goodsMessage, goodsRemarks, foreignKey,
				goodsGround,request,ip,userAgent,goods_barcode,outStockCount,autoGoodsJson,goodsDelete,staff_id,sameType,goodsChengType,defaultSupplierUnique,manager_unique);
	}
	@RequestMapping("/getGoodsBaseMessage.do")
	@ResponseBody
	public PurResult getGoodsBaseMessage(
			@RequestParam(value="supplierUnique",required=true)Long supplierUnique,
			String goodsMessage,
			String kindUnique,
			String groupUnique
			){
		Map<String,Object> map=new HashMap<String, Object>();
		map.put("supplierUnique", supplierUnique);
		if(goodsMessage!=null){
			map.put("goodsMessage", "%"+goodsMessage+"%");
		}
		if(!kindUnique.equals("-1")){
			map.put("kindUnique", kindUnique);
		}
		if(!groupUnique.equals("-1")){
			map.put("groupUnique", groupUnique);
		}
		return goodsService.getGoodsBaseMessage(map);
	}
	@RequestMapping("/goodsKindPage.do")
	public String goodsKindPage(HttpServletRequest request){
		return "/WEB-INF/goods/goodsKind.jsp";
	}
	@RequestMapping("/goodsBinding.do")
	public String goodsBinding(HttpServletRequest request){
		return "/WEB-INF/goods/goodsBinding.jsp";
	}
	
	/**
	 * 商品销售统计界面查询
	 * @param page
	 * @return
	 */
	@RequestMapping("/queryGoodsSaleStatistics.do")
	@ResponseBody
	public PurResult queryGoodsSaleStatistics(HttpServletRequest request,
			@RequestParam(value="page",defaultValue="1")Integer page,
			@RequestParam(value="limit",defaultValue="15")Integer limit){		
		PurResult result = new PurResult();
		try {
			Map<String, Object> params = HandleMessyCode.handle(ServletsUtil.getParameters(request));
			params.put("limit", limit);
			params.put("page", (page - 1)*limit);
			result=goodsService.queryGoodsSaleStatistics(params);
		} catch (Exception e) {
			result.setStatus(0);
			result.setData(null);
			result.setMsg("查询失败!");
			e.printStackTrace();
		}		
		return result;
	}
	
	/**
	 * 商品销售明细页面
	 * @param goods_barcode 商品编码
	 * @param shop_unique 店铺编码
	 * @param startTime 开始时间
	 * @param endTime 结束时间
	 * @return
	 */
	@RequestMapping("/queryGoodsSaleDetailPage.do")
	public String queryGoodsSaleDetail(
			String goods_barcode,
			String shop_unique,
			String startTime,
			String endTime,
			String saleCount,
			String saleSum,
			String purSum,
			String grossProfit,
			Model model
			){		
		model.addAttribute("goods_barcode", goods_barcode);
		model.addAttribute("shop_unique", shop_unique);
		model.addAttribute("startTime", startTime);
		model.addAttribute("endTime", endTime);
		model.addAttribute("saleCount", saleCount);
		model.addAttribute("saleSum", saleSum);
		model.addAttribute("purSum", purSum);
		model.addAttribute("grossProfit", grossProfit);
		return "/WEB-INF/countMessage/goodsSaleDetailList.jsp";
	}
	/**
	 * 商品列
	 */
	@RequestMapping("/getGoodPrintList.do")
	@ResponseBody
	public PurResult getGoodPrintList(HttpServletRequest request,
			@RequestParam(value="page",defaultValue="1")Integer page,
			@RequestParam(value="limit",defaultValue="20")Integer limit,
			String field,
			String order
			){		
		PurResult result = new PurResult();
		try {
//			Map<String,String[]> m = request.getParameterMap();
			Map<String, Object> params = HandleMessyCode.handle(ServletsUtil.getParameters(request));
			String goodsMessage = MUtil.strObject(params.get("goodsMessage"));
			if(goodsMessage != null && !goodsMessage.equals("")){
				params.put("goodsMessage", "%"+goodsMessage+"%");
			}
			params.put("limit", limit);
			params.put("page", (page-1)*limit);
			params.put("field",field);
			params.put("order",order);
			result=goodsService.getGoodPrintList(params);
		} catch (Exception e) {
			result.setStatus(0);
			result.setMsg("查询失败!");
			e.printStackTrace();
		}		
		
		return result;
	}
	/**
	 * 商品销售明细
	 * @param goods_barcode 商品编码
	 * @param shop_unique 店铺编码
	 * @param startTime 开始时间
	 * @param endTime 结束时间
	 * @return
	 */
	@RequestMapping("/queryGoodsSaleDetail.do")
	@ResponseBody
	public PurResult queryGoodsSaleDetail(
			String goods_barcode,
			String shop_unique,
			String startTime,
			String endTime,
			@RequestParam(value="page",defaultValue="1")Integer page,
			@RequestParam(value="limit",defaultValue="15")Integer limit){		
		PurResult result = new PurResult();
		try {
			Map<String, Object> params = new HashMap<String, Object>();
			params.put("goods_barcode", goods_barcode);
			params.put("shop_unique", shop_unique);
			params.put("startTime", startTime);
			params.put("endTime", endTime);
			params.put("limit", limit);
			params.put("page", (page-1)*limit);
			result = goodsService.queryGoodsSaleDetail(params);
		} catch (Exception e) {
			result.setStatus(0);
			result.setMsg("查询失败!");
			e.printStackTrace();
		}		
		return result;
	}
	
	@RequestMapping("/downloadSaleStatisticsExcel.do")
	@ResponseBody
	public void downloadSaleStatisticsExcel(
			HttpServletRequest request,
			HttpServletResponse response,
			@RequestParam(value="shopUnique")String shopUnique,
			String goodsMessage,
			String groupUnique,
			String kindUnique,
			String startTime,
			String endTime,
			@RequestParam(value="order",defaultValue="1")Integer order,
			@RequestParam(value="orderType",defaultValue="1")Integer orderType){
		Map<String,Object> map=new HashMap<String, Object>();
		map.put("shopUnique", shopUnique);
		map.put("groupUnique", groupUnique);
		map.put("kindUnique", kindUnique);
		map.put("startTime", startTime);
		map.put("endTime", endTime);
		if(null!=goodsMessage&&!goodsMessage.equals("")){
			map.put("goodsMessage", "%"+goodsMessage+"%");
		}
		if(order==1){
			map.put("order", "saleCount");
		}else if(order==2){
			map.put("order", "saleSum");
		}else if(order==3){
			map.put("order", "purSum");
		}else if(order==4){
			map.put("order", "grossProfit");
		}
		if(orderType==1){
			map.put("orderType", "DESC");
		}else{
			map.put("orderType", "ASC");
		}
		map.put("receiptStatus", "6");
		map.put("purchase_list_status", "1");
		List<Map<String,Object>> data=goodsService.downloadSaleStatisticsExcel(map);
		loadOutClaimXLS(data, "商品周期销售记录", request, response);
	}
	
	public void loadOutClaimXLS(List<Map<String, Object>> caseVoList, final String string,HttpServletRequest request,HttpServletResponse response) {
		try {
			response.setContentType(
					"application/x-msdownload; charset=UTF-8");// 告知浏览器文件类型
			String agent = request.getHeader("user-agent");
			if (agent.contains("Firefox")) {
				response.setHeader(
						"Content-Disposition",
						"attachment;filename="
								+ new String((string + ".xls")
										.getBytes("GB2312"), "ISO-8859-1"));// 设置文件头
			} else {
				response.setHeader(
						"Content-Disposition",
						"attachment;filename="
								+ URLEncoder.encode(string + ".xls", "UTF-8"));// 设置文件头
			}
			OutputStream os = response.getOutputStream();
			LoadOutObjectXLSUtil<Map<String, Object>> objectXLS = new LoadOutObjectXLSUtil<Map<String, Object>>();
			objectXLS.generateXLS(os, caseVoList, new XLSCallBack<Map<String, Object>>() {
				public Object[] getValues(Map<String, Object> tt) {
					String str = "";
					
					String goodsBarcode=String.valueOf( tt.get("goodsBarcode"));
					String goodsName = (String) tt.get("goodsName");
					if (StringUtil.blank(goodsName)) {
						goodsName = str;
					}
					String sGoodsCount = tt.get("sGoodsCount")+"";
					if (StringUtil.blank(sGoodsCount)) {
						sGoodsCount = str;
					}
					String eGoodsCount =  tt.get("eGoodsCount")+"";
					if (StringUtil.blank(eGoodsCount)) {
						eGoodsCount = str;
					}
					String saleCount =tt.get("saleCount")+"";
					if (StringUtil.blank(saleCount)) {
						saleCount = str;
					}
					String saleSum = tt.get("saleSum")+"";
					if (StringUtil.blank(saleSum)) {
						saleSum = str;
					}
					String purSum = tt.get("purSum")+"";
					if (StringUtil.blank(purSum)) {
						purSum = str;
					}
					String grossProfit = tt.get("grossProfit")+"";
					if (StringUtil.blank(grossProfit)) {
						grossProfit = str;
					}
					String averRatio = tt.get("averRatio")+"";
					if (StringUtil.blank(averRatio)) {
						averRatio = str;
					}
					String purCount = tt.get("purCount")+"";
					if (StringUtil.blank(purCount)) {
						purCount = str;
					}
					String purTotal =  tt.get("purTotal")+"";
					if (StringUtil.blank(purTotal)) {
						purTotal = str;
					}
					
					return new String[] {
							goodsBarcode,goodsName,sGoodsCount,eGoodsCount,saleCount,saleSum,purSum,
							grossProfit,averRatio,purCount,purTotal
					};
				}

				public String getTitle() {
					return string;
				}

				public int[] getColumnsWidth() {
					return new int[] { 
										  80 * 80, 80 * 80,80 * 80,80 * 80,
										  80 * 80, 80 * 80,80 * 80,80 * 80,
										  80 * 80,80 * 80,80 * 80};
				}

				public String[] getColumnsName() {
					return new String[] { "商品条码","商品名称","期初库存","期末库存","销售总额",
							"销售数量","销售成本","毛利润","平均毛利率","进货数量","进货总金额"};
				}

				public int[][] MergedRegion() {
					// TODO Auto-generated method stub
					return null;
				}

				public List<Object[]> getheaderValue() {
					// TODO Auto-generated method stub
					return null;
				}

			});
			os.flush();
		} catch (IOException e) {
			e.printStackTrace();
		}
	}
	
	/**
	 * 查询商品详情
	 * @return
	 */
	@RequestMapping("/queryGoodsStatisticsDetail.do")
	@ResponseBody
	public PurResult queryGoodsStatisticsDetail(HttpServletRequest request,
			@RequestParam(value="page",defaultValue="1")Integer page,
			@RequestParam(value="limit",defaultValue="15")Integer limit){		
		PurResult result = new PurResult();
		try {
			Map<String, Object> params = HandleMessyCode.handle(ServletsUtil.getParameters(request));
			params.put("limit", limit);
			params.put("page", (page-1)*limit);
			result=goodsService.queryGoodsStatisticsDetail(params);
		} catch (Exception e) {
			result.setStatus(0);
			result.setData(null);
			result.setMsg("查询失败!");
			e.printStackTrace();
		}		
		return result;
	}
	
	@RequestMapping("/downloadSaleStatisticsDetailExcel.do")
	public void downloadSaleStatisticsDetailExcel(
			@RequestParam(value="shopUnique")String shopUnique,
			@RequestParam(value="goodsMessage")String goodsBarcode,
			String startTime,
			String endTime,
			Integer stockType,
			Integer stockResource,
			Integer stockOrigin,
			HttpServletRequest request,
			HttpServletResponse response
			){
		Map<String,Object> map=new HashMap<String, Object>();
		map.put("shopUnique", shopUnique);
		map.put("goodsBarcode", goodsBarcode);
		map.put("startTime", startTime);
		map.put("endTime", endTime);
		map.put("stockType", stockType);
		map.put("stockResource", stockResource);
		map.put("stockOrigin", stockOrigin);
		List<Map<String,Object>> data = goodsService.downloadSaleStatisticsDetailExcel(map);
		Map<String,Object> data2=goodsService.downloadSaleStatisticsCount(map);
		if(data!=null)
		{
			data2.put("listUnique", "统计：");
			data.add(data2);
		}
		String[] titles={
				"单号",
				"商品大类",
				"商品小类",
				"商品名称",
				"商品条码",
				"操作时间",
				"出入库",
				"操作类型",
				"操作前数量",
				"数量",
				"操作后数量",
				"金额",
				"审核状态",
				"审核人",
				"审核时间",
				"最后修改人",
				"最后修改时间",
				"操作人",
				"操作平台"			
				};
		loadOutClaimDetailXLS(data,"商品出入库详情",request,response,1,titles);
	}
	@RequestMapping("/downloadSingleGoodsExcel.do")
	public void downloadSingleGoodsExcel(
			@RequestParam(value="shopUnique")String shopUnique,
			@RequestParam(value="goodsMessage")String goodsBarcode,
			String startTime,
			String endTime,
			Integer stockType,
			Integer stockResource,
			Integer stockOrigin,
			HttpServletRequest request,
			HttpServletResponse response
			){
		Map<String,Object> map=new HashMap<String, Object>();
		map.put("shopUnique", shopUnique);
		map.put("goodsBarcode", goodsBarcode);
		map.put("startTime", startTime);
		map.put("endTime", endTime);
		map.put("stockType", stockType);
		map.put("stockResource", stockResource);
		map.put("stockOrigin", stockOrigin);
		List<Map<String,Object>> data=goodsService.downloadSingleGoodsExcel(map);
		String[] titles={
				"单号",
				"商品大类",
				"商品小类",
				"商品名称",
				"商品条码",
				"出入库",
				"数量",
				"操作人"		
		};
		loadOutClaimSingleGoodsDetailXLS(data,"单品统计出入库详情",request,response,1,titles);
	}
	
	public void loadOutClaimSingleGoodsDetailXLS(List<Map<String, Object>> caseVoList, final String string,HttpServletRequest request,HttpServletResponse response,final Integer type,final String[] titles){
		try {
			response.setContentType(
					"application/x-msdownload; charset=UTF-8");// 告知浏览器文件类型
			String agent = request.getHeader("user-agent");
			if (agent.contains("Firefox")) {
				response.setHeader(
						"Content-Disposition",
						"attachment;filename="
								+ new String((string + ".xls")
										.getBytes("GB2312"), "ISO-8859-1"));// 设置文件头
			} else {
				response.setHeader(
						"Content-Disposition",
						"attachment;filename="
								+ URLEncoder.encode(string + ".xls", "UTF-8"));// 设置文件头
			}
			OutputStream os = response.getOutputStream();
			LoadOutObjectXLSUtil<Map<String, Object>> objectXLS = new LoadOutObjectXLSUtil<Map<String, Object>>();
			objectXLS.generateXLS(os, caseVoList, new XLSCallBack<Map<String, Object>>() {
				public Object[] getValues(Map<String, Object> tt) {
					
					String str = "";
					String listUnique="";
					if(tt.get("goodsBarcode")!=null){
						listUnique=tt.get("listUnique").toString();
					}
					String goodsBarcode="";
					if(tt.get("goodsBarcode")!=null){
						goodsBarcode=tt.get("goodsBarcode").toString();
						
					}
					String goodsKindNameFirst = tt.getOrDefault("goodsKindNameFirst", StrUtil.EMPTY).toString();
					String goodsKindNameSecond = tt.getOrDefault("goodsKindNameSecond", StrUtil.EMPTY).toString();
					String goodsName ="";
					if(tt.get("goodsName")!=null){
						goodsName=(String)tt.get("goodsName");
						if (StringUtil.blank(goodsName)) {
							goodsName = str;
						}
					}
					
					String goodsCount="0";
					if(null!=tt.get("goodsCount")){
						goodsCount=tt.get("goodsCount").toString();
						if (StringUtil.blank(goodsCount)) {
							goodsCount = str;
						}
					}
					
					String stockType ="";
					if(null!=tt.get("stockType")){
						stockType=tt.get("stockType").toString();
						if (StringUtil.blank(stockType)) {
							stockType = str;
						}
					}
					
					String staffName = "";
					if(null!=tt.get("staffName")){
						staffName=tt.get("staffName").toString();
						if (StringUtil.blank(staffName)) {
							staffName = str;
						}
					}
					
					if(type==1){
						return new String[] {
								listUnique, goodsKindNameFirst, goodsKindNameSecond, goodsName,goodsBarcode,stockType,goodsCount,
								staffName
						};
					}else{
						return new String[]{
								
						};
					}
				}

				public String getTitle() {
					return string;
				}

				public int[] getColumnsWidth() {
					int[] result=null;
					if(type==1){
						result=new int[]{ 80 * 80, 80 * 80, 80 * 80, 80 * 80, 80 * 80,80 * 80,80 * 80,
								80 * 80};
					}else if(type==2){
						result=new int[]{ 80 * 80, 80 * 80,80 * 80,80 * 80,
								80 * 80, 80 * 80,80 * 80};
					}else{
						result=new int[]{ 80 * 80, 80 * 80,80 * 80,80 * 80,
								80 * 80, 80 * 80};
					}
					
					return result;
				}

				public String[] getColumnsName() {
					return titles;
				}

				public int[][] MergedRegion() {
					// TODO Auto-generated method stub
					return null;
				}

				public List<Object[]> getheaderValue() {
					// TODO Auto-generated method stub
					return null;
				}

			});
			os.flush();
		} catch (IOException e) {
			e.printStackTrace();
		}
	}
	public void loadOutClaimDetailXLS(List<Map<String, Object>> caseVoList, final String string,HttpServletRequest request,HttpServletResponse response,final Integer type,final String[] titles){
		try {
			response.setContentType(
					"application/x-msdownload; charset=UTF-8");// 告知浏览器文件类型
			String agent = request.getHeader("user-agent");
			if (agent.contains("Firefox")) {
				response.setHeader(
						"Content-Disposition",
						"attachment;filename="
								+ new String((string + ".xls")
										.getBytes("GB2312"), "ISO-8859-1"));// 设置文件头
			} else {
				response.setHeader(
						"Content-Disposition",
						"attachment;filename="
								+ URLEncoder.encode(string + ".xls", "UTF-8"));// 设置文件头
			}
			OutputStream os = response.getOutputStream();
			LoadOutObjectXLSUtil<Map<String, Object>> objectXLS = new LoadOutObjectXLSUtil<Map<String, Object>>();
			objectXLS.generateXLS(os, caseVoList, new XLSCallBack<Map<String, Object>>() {
				public Object[] getValues(Map<String, Object> tt) {
					
					String str = "";
					String goodsBarcode="";
					String shopName="";
					if(tt.get("shopName")!=null){
						shopName=tt.get("shopName").toString();
						
					}
					if(tt.get("goodsBarcode")!=null){
						goodsBarcode=tt.get("goodsBarcode").toString();
						
					}
					String goodsKindNameFirst = tt.getOrDefault("goodsKindNameFirst", StrUtil.EMPTY).toString();
					String goodsKindNameSecond = tt.getOrDefault("goodsKindNameSecond", StrUtil.EMPTY).toString();
					String goodsName ="";
					if(tt.get("goodsName")!=null){
						goodsName=(String)tt.get("goodsName");
						if (StringUtil.blank(goodsName)) {
							goodsName = str;
						}
					}
					
					String stockTime= "";
					if(null!=tt.get("stockTime")){
						stockTime=String.valueOf( tt.get("stockTime"));
						if (StringUtil.blank(stockTime)) {
							stockTime = str;
						}
					}
					String goodsCount="0";
					if(null!=tt.get("goodsCount")){
						goodsCount=tt.get("goodsCount").toString();
						if (StringUtil.blank(goodsCount)) {
							goodsCount = str;
						}
					}
					String stockCount = "";
					if(null!=tt.get("stockCount")){
						stockCount =  tt.get("stockCount").toString();
						if (StringUtil.blank(stockCount)) {
							stockCount = str;
						}
					}
					
					String stockTotal = "";
					if(null!=tt.get("stockTotal")){
						stockTotal=tt.get("stockTotal").toString();
						if (StringUtil.blank(stockTotal)) {
							stockTotal = str;
						}
						
					}
					String beforeCount ="";
					if(null!=tt.get("beforeCount")){
						beforeCount=tt.get("beforeCount").toString();
						if (StringUtil.blank(beforeCount)) {
							beforeCount = str;
						}
					}
					
					String stockType ="";
					if(null!=tt.get("stockType")){
						stockType=tt.get("stockType").toString();
						if (StringUtil.blank(stockType)) {
							stockType = str;
						}
					}
					String stockResource ="";
					if(null!=tt.get("stockResource")){
						stockResource=tt.get("stockResource").toString();
						if (StringUtil.blank(stockResource)) {
							stockResource = str;
						}
					}
					
					String stockResourceCode ="";
					if(null!=tt.get("stockResourceCode")){
						stockResourceCode=tt.get("stockResourceCode").toString();
						if (StringUtil.blank(stockResourceCode)) {
							stockResourceCode = str;
						}
					}
					String stockOrigin ="";
					if(null!=tt.get("stockOrigin")){
						stockOrigin = tt.get("stockOrigin").toString();
						if (StringUtil.blank(stockOrigin)) {
							stockOrigin = str;
						}
					}
					String staffName = "";
					if(null!=tt.get("staffName")){
						staffName=tt.get("staffName").toString();
						if (StringUtil.blank(staffName)) {
							staffName = str;
						}
					}
					String listUnique ="";
					if(null!=tt.get("listUnique")){
						listUnique=tt.get("listUnique")+"";
						if (StringUtil.blank(listUnique)) {
							listUnique = str;
						}
					}
					String saleSum="",saleCount="",purSum="",grossProfit="",giftCount="",goodsKindParname="",goodsKindName="",auditStatus="";
					if(null!=tt.get("giftCount")){
						saleSum=tt.get("giftCount").toString();
						if(StringUtil.blank(giftCount)){
							giftCount=str;
						}
					}
					if(null!=tt.get("saleSum")){
						saleSum=tt.get("saleSum").toString();
						if(StringUtil.blank(saleSum)){
							saleSum=str;
						}
					}
					if(null!=tt.get("saleCount")){
						saleCount=tt.get("saleCount").toString();
						if(StringUtil.blank(saleCount)){
							saleCount=str;
						}
					}
					if(null!=tt.get("purSum")){
						purSum=tt.get("purSum").toString();
						if(StringUtil.blank(purSum)){
							purSum=str;
						}
					}
					if(null!=tt.get("grossProfit")){
						grossProfit=tt.get("grossProfit").toString();
						if(StringUtil.blank(grossProfit)){
							grossProfit=str;
						}
					}
					if(null!=tt.get("goodsKindParname")){
						goodsKindParname=tt.get("goodsKindParname").toString();
						if(StringUtil.blank(goodsKindParname)){
							goodsKindParname=str;
						}
					}
					if(null!=tt.get("goodsKindName")){
						goodsKindName=tt.get("goodsKindName").toString();
						if(StringUtil.blank(goodsKindName)){
							goodsKindName=str;
						}
					}
					if(null!=tt.get("auditStatus")){
						auditStatus=tt.get("auditStatus").toString();
						if(StringUtil.blank(auditStatus)){
							auditStatus=str;
						}
					}
					String auditName ="";
					if(tt.get("auditName")!=null){
						auditName=(String)tt.get("auditName");
						if (StringUtil.blank(auditName)) {
							auditName = str;
						}
					}
					String auditTime ="";
					if(tt.get("auditTime")!=null){
						auditTime=(String)tt.get("auditTime");
						if (StringUtil.blank(auditTime)) {
							auditTime = str;
						}
					}
					String updateName ="";
					if(tt.get("updateName")!=null){
						updateName=(String)tt.get("updateName");
						if (StringUtil.blank(updateName)) {
							updateName = str;
						}
					}
					String updateTime ="";
					if(tt.get("updateTime")!=null){
						updateTime=(String)tt.get("updateTime");
						if (StringUtil.blank(updateTime)) {
							updateTime = str;
						}
					}
					if(type==1){
						return new String[] {
								listUnique, goodsKindNameFirst, goodsKindNameSecond, goodsName,goodsBarcode,stockTime,stockType,stockResource,beforeCount,goodsCount,stockCount,stockTotal,
								auditStatus,auditName, auditTime, updateName, updateTime, staffName,stockOrigin
						};
					}else if(type==2){
						return new String[]{
								goodsBarcode,goodsName,saleCount,giftCount,saleSum,purSum,grossProfit,goodsCount
						};
					}else if(type==3){
						return new String[]{
								shopName,goodsBarcode,goodsName,saleSum,saleCount,purSum,grossProfit
						};
					}else{
						return new String[]{
								
						};
					}
				}
				
				public String getTitle() {
					return string;
				}
				
				public int[] getColumnsWidth() {
					int[] result=null;
					if(type==1){
						result=new int[]{80 * 80, 80 * 80, 80 * 80, 80 * 80, 80 * 80,80 * 80,80 * 80,
								80 * 80, 80 * 80,80 * 80,80 * 80,
								80 * 80,80 * 80,80 * 80};
					}else if(type==2){
						result=new int[]{ 80 * 80, 80 * 80,
								80 * 80, 80 * 80,80 * 80,80 * 80,80 * 80,80 * 80};
					}else if(type==3){//宁宇全部
						result=new int[]{  80 * 80,80 * 80, 80 * 80,80 * 80,80 * 80,
								80 * 80, 80 * 80};
					}else{
						result=new int[]{ 80 * 80, 80 * 80,80 * 80,80 * 80,
								80 * 80, 80 * 80};
					}
					
					return result;
				}
				
				public String[] getColumnsName() {
					return titles;
				}
				
				public int[][] MergedRegion() {
					// TODO Auto-generated method stub
					return null;
				}
				
				public List<Object[]> getheaderValue() {
					// TODO Auto-generated method stub
					return null;
				}
				
			});
			os.flush();
		} catch (IOException e) {
			e.printStackTrace();
		}
	}
	
	/**
	 * 商品出入库明细界面商品信息查询
	 * @param shopUnique
	 * @return
	 */
	@RequestMapping("/queryGoodsMessageForStatisticsDetail.do")
	@ResponseBody
	public ShopsResult queryGoodsMessageForStatisticsDetail(
			@RequestParam(value="shopUnique",required=true)String shopUnique
			,@RequestParam(value="goodsMessage",required=true)String goodsMessage
			){
		Map<String,Object> map=new HashMap<String, Object>();
		map.put("shopUnique", shopUnique);
		if(null!=goodsMessage&&!goodsMessage.equals("")){
			goodsMessage="%"+goodsMessage+"%";
		}
		map.put("goodsMessage", goodsMessage);
		return goodsService.queryGoodsMessageForStatisticsDetail(map);
	}
	
	@RequestMapping("/goodsInvertory.do")
	public String goodsInvertory(){
		return "/WEB-INF/goods/goodsInventory.jsp";
	}
	
	/**
	 * 统计销售数据并下载
	 * @param shopUnique
	 * @return
	 */
	@RequestMapping("/downloadGoodsSaleStatisticsExcel.do")
	public void downloadGoodsSaleStatisticsExcel(
			HttpServletRequest request,
			HttpServletResponse response,
			@RequestParam(value="shopUnique")String shopUnique,
			String goodsMessage,
			String groupUnique,
			String kindUnique,
			String profit,
			String startTime,
			String endTime,
			@RequestParam(value="order",defaultValue="1")Integer order,
			@RequestParam(value="orderType",defaultValue="1")Integer orderType){
		Map<String,Object> map=new HashMap<String, Object>();
		map.put("shopUnique", shopUnique);
		map.put("groupUnique", groupUnique);
		map.put("kindUnique", kindUnique);
		map.put("profit", profit);
		map.put("startTime", startTime);
		map.put("endTime", endTime);
		if(null!=goodsMessage&&!goodsMessage.equals("")){
			map.put("goodsMessage", "%"+goodsMessage+"%");
		}
		if(order==1){
			map.put("order", "saleCount");
		}else if(order==2){
			map.put("order", "saleSum");
		}else if(order==3){
			map.put("order", "purSum");
		}else if(order==4){
			map.put("order", "grossProfit");
		}
		if(orderType==1){
			map.put("orderType", "DESC");
		}else{
			map.put("orderType", "ASC");
		}
		map.put("receiptStatus", "6");
		map.put("purchase_list_status", "1");
		
		List<Map<String,Object>> list=goodsService.queryGoodsSaleStatisticsExcel(map);

		Map<String,Object> m = goodsService.goodsSaleStatistics(map);
		
		if(null!=list&&list.size()>0){
			Map<String, Object> lastData=new HashMap<String, Object>();
			DecimalFormat df = new DecimalFormat("0.0000");
			String grossProfit=df.format(m.get("grossProfit"));
			Float rofit = Float.valueOf(grossProfit);
			lastData.put("goodsBarcode", "统计：");
			lastData.put("saleCount", m.get("saleCount"));
			lastData.put("saleSum", m.get("saleSum"));
			lastData.put("purSum", m.get("purSum"));
			lastData.put("grossProfit", rofit*100+"%");
			list.add(lastData);
		}
		
		String[] titles=new String[] {
				"商品条码","商品名称","销售数量","赠送数量","销售金额","销售成本","销售毛利润","当前库存"
		};
		loadOutClaimDetailXLS(list,"商品销售详情",request,response,2,titles);
	}
	
	/**
	 * 统计销售数据并下载-宁宇-全部
	 * @param goodsMessage
	 * @return
	 */
	@RequestMapping("/downloadGoodsSaleStatisticsExcel_NYALL.do")
	public void downloadGoodsSaleStatisticsExcel_NYALL(
			HttpServletRequest request,
			HttpServletResponse response,
			String goodsMessage,
			String startTime,
			String endTime,
			@RequestParam(value="order",defaultValue="1")Integer order,
			@RequestParam(value="orderType",defaultValue="1")Integer orderType){
		Map<String,Object> map=new HashMap<String, Object>();
		map.put("startTime", startTime);
		map.put("endTime", endTime);
		if(null!=goodsMessage&&!goodsMessage.equals("")){
			map.put("goodsMessage", "%"+goodsMessage+"%");
		}
		if(order==1){
			map.put("order", "saleCount");
		}else if(order==2){
			map.put("order", "saleSum");
		}else if(order==3){
			map.put("order", "purSum");
		}else if(order==4){
			map.put("order", "grossProfit");
		}
		if(orderType==1){
			map.put("orderType", "DESC");
		}else{
			map.put("orderType", "ASC");
		}
		map.put("receiptStatus", "6");
		map.put("purchase_list_status", "1");
		List<Map<String,Object>> list=goodsService.downloadGoodsSaleStatisticsExcel_NYALL(map);
		String[] titles=new String[] {
				"店铺","商品条码","商品名称","销售总额","销售总量","销售成本","销售毛利"
		};
		loadOutClaimDetailXLS(list,"商品销售详情",request,response,3,titles);
	}
	@RequestMapping("/goodsDelete.do")
	public String goodsDelete(){
		return "/WEB-INF/goods/goodsDelete.jsp";
	}

	/**
	 * 删除商品信息界面，查询分页数量
	 * @param goodsMsg
	 * @return
	 */
	@RequestMapping("/toQueryGoodsPage.do")
	@ResponseBody
	public ShopsResult toQueryGoodsPage(
			String goodsMsg,
			String goodsKindParunique,
			String goodsKindUnique,
			String shopUnique,
			Integer pageSize
			){
		Map<String,Object> map=new HashMap<String,Object>();
		map.put("shopUnique", shopUnique);
		map.put("pageSize", pageSize);
		map.put("goodsKindUnique", goodsKindUnique);
		map.put("goodsKindParunique", goodsKindParunique);
		if(null!=goodsMsg&&!goodsMsg.trim().replace(" ", "").equals("")){
			map.put("goodsMsg", "%"+goodsMsg+"%");
		}
		return goodsService.toQueryGoodsPage(map);
	}
	
	/**
	 * 获取商家是否开启自动补货设置
	 * @param shop_unique 商家唯一标示
	 * @return
	 */
	@RequestMapping("/getAutoPurchase.do")
	@ResponseBody
	public PurResult getAutoPurchase(String shop_unique){
		PurResult result = goodsService.getAutoPurchase(shop_unique);
		return result;
	}
	/**
	 * 删除商品信息，并修改同步信息至删除
	 * @param goodsBarcodes
	 * @return
	 */
	@RemoteLog(title="删除商品信息", businessType = BusinessType.DELETE)
	@RequestMapping("/deleteGoodsList.do")
	@ResponseBody
	public ShopsResult deleteGoodsList(
			String goodsBarcodes,
			String shopUnique,
			Integer staffId,
			Integer equipmentType,
			String macId
			){
		Map<String,Object> map=new HashMap<String,Object>();
		map.put("shopUnique", shopUnique);
		map.put("staffId", staffId);
		map.put("equipmentType", equipmentType);
		map.put("macId", macId);
		String[] barcodes=goodsBarcodes.split(";");
		map.put("barcodes", barcodes);
		return goodsService.deleteGoodsList(map,barcodes);
	}
	
	/**
	 * 删除油品
	 * @param goodsBarcode 商品条码 
	 * @param shopUnique 店铺编号
	 * @param staffId 员工编号
	 * @param equipmentType 设备类型：1、；2、；3、浏览器
	 * @param macId 设备编号
	 * @param gunNum 油枪号
	 * @param oilNum 油号
	 * @return
	 */
	@RequestMapping("/deleteOilGoods.do")
	@ResponseBody
 	public ShopsResult deleteOilGoods(String goodsBarcode,String shopUnique,Integer staffId,Integer equipmentType,String macId,String gunNum,String oilNum) {
		return goodsService.deleteOilGoods(goodsBarcode, shopUnique, staffId, equipmentType, macId, gunNum, oilNum);
	}

	@RemoteLog(title="删除查询商品信息", businessType = BusinessType.DELETE)
	@RequestMapping("/deleteGoodsSearch.do")
	@ResponseBody
	public ShopsResult deleteGoodsSearch(
			String goodsMsg,
			String shopUnique,
			Integer staffId,
			Integer equipmentType,
			String macId,
			String goodsKindUnique,
			String goodsKindParunique
			){
		Map<String,Object> map=new HashMap<String,Object>();
		map.put("shopUnique", shopUnique);
		map.put("goodsKindUnique", goodsKindUnique);
		map.put("goodsKindParunique", goodsKindParunique);
		if(goodsMsg!=null&&!goodsMsg.replace(" ", "").trim().equals("")){
			map.put("goodsMsg", "%"+goodsMsg+"%");
		}
		map.put("staffId", staffId);
		map.put("equipmentType", equipmentType);
		map.put("macId", macId);
		System.out.println(map);
		return goodsService.deleteGoodsSearch(map);
	}
	
	
	
	/**
	 * 开启、关闭自动补货
	 * @param shop_unique 商家唯一标示
	 * @param auto_purchase 0 关闭自动补货 1开启自动补货
	 * @return
	 */
	@RequestMapping("/updateAutoPurchase.do")
	@ResponseBody
	public PurResult updateAutoPurchase(String shop_unique,String auto_purchase){
		Map<String,Object> params=new HashMap<String, Object>();
		params.put("auto_purchase", auto_purchase);
		params.put("shop_unique", shop_unique);
		
		PurResult result = goodsService.updateAutoPurchase(params);
		return result;
	}
	
	@RequestMapping("/yancaoInsert.do")
	@ResponseBody
	public ShopsResult yancaoInsert(
			
			@RequestParam(value="shopUnique")String shopUnique){
		Map<String,Object> map=new HashMap<String,Object>();
		map.put("shopUnique", shopUnique);
		return goodsService.yancaoInsert(map);
	}
	
	@RequestMapping("/queryGoodsNewCountForShop.do")
	@ResponseBody
	public ShopsResult queryGoodsNewCountForShop(
			String shop_unique,
			String startTime,
			String endTime
			){
		Map<String,Object> map=new HashMap<String,Object>();
		map.put("startTime", startTime);
		map.put("endTime", endTime);
		map.put("shop_unique", shop_unique);
		return goodsService.queryGoodsNewCountForShop(map);
	}
	
	@RequestMapping("/shopNewGoods.do")
	public String shopNewGoods(Model model){
		//获取所有审核通过店铺列表
		List<Map<String ,Object>> shopList = goodsService.getAllShopList();
		model.addAttribute("shopList", shopList);
		return "/WEB-INF/goods/newShopGoodsCount.jsp";
	}
	
	/**
     * 创建新的条码
     * @param goodsBarcode
     * @param barcodes
     * @return
     */
	@RequestMapping("/queryGoodsBarcodeSameForeignkey.do")
	@ResponseBody
    public ShopsResult queryGoodsBarcodeSameForeignkey(
    		@RequestParam(value="goodsBarcode")String goodsBarcode,
    		String barcodes,
    		@RequestParam(value="shopUnique")String shopUnique){
    	try {
    		return goodsService.queryGoodsBarcodeSameForeignkey(goodsBarcode, barcodes,shopUnique);
		} catch (Exception e) {
			e.printStackTrace();
			return new ShopsResult(0, "系统错误");
		}
    }
	
	/**
	 * 将商品信息保存，并更新云库商品
	 * 此处为保证所有商品的foreignKey一致，将所有同条码的商品更新为相同
	 * 此处先更新本店铺的商品信息，若更新不成功，则将新商品添加，否则仅更新
	 * @param shop_unique
	 * @return
	 */
	@RequestMapping("/saveGoodsMessageNew.do")
	@ResponseBody
	public PurResult saveGoodsMessageNew(
			@RequestParam(value="shop_unique",required=true)String shop_unique,
			@RequestParam(value="kindUnique",required=true)String kindUnique,
			@RequestParam(value="goodsMessage",required=true)String goodsMessage,
			String operateType,
			String goodsBrand,
			String goodsRemarks,
			String foreignKey,
			HttpServletRequest request,
			@RequestParam(value="goodsGround",defaultValue="1")Integer goodsGround,//上架状态（默认上架）,
			String userAgent,
			String ip,
			String goods_barcode,
			String outStockCount,
			String autoGoodsJson,
			String goodsDelete,
			String staff_id,
			Integer sameType,
			Integer goodsChengType,
			String defultSupplierUnique
			){
		String manager_unique= ((Staff)request.getSession().getAttribute("staff")).getManager_unique();

		return goodsService.saveGoodsMessage(operateType, shop_unique, goodsBrand, kindUnique, goodsMessage, goodsRemarks, foreignKey,
				goodsGround,request,ip,userAgent,goods_barcode,outStockCount,autoGoodsJson,goodsDelete,staff_id,sameType,goodsChengType,defultSupplierUnique,manager_unique);
	}
	
    /**
     * 获取商品详情
     * @param shopUnique
     * @return
     */
	@RequestMapping("/getGoodsDetail.do")
	@ResponseBody
    public ShopsResult getGoodsDetail(String shopUnique,String goodsBarcode){
    	try {
    		Map<String,Object> map = new HashMap<String,Object>();
    		map.put("shopUnique", shopUnique);
    		map.put("goodsBarcode", goodsBarcode);
			return goodsService.getGoodsDetail(map);
		} catch (MyException e) {
//			e.printStackTrace();
			return new ShopsResult(e.getStatus(),e.getMsg());
		}
    }
	
	/**
     * 更新商品图片
     * @param shop_unique 店铺编码
     * @param goods_barcode 商品编码
     * @param file 商品图片文件
     * @return
     */
	@RequestMapping("/updateGoodsImg.do")
	@ResponseBody
    public PurResult updateGoodsImg(String shop_unique,String goods_barcode,@RequestParam(value="fileupload",required=true)MultipartFile file,HttpServletRequest request){
    	return goodsService.updateGoodsImg(shop_unique, goods_barcode,file,request);
    }
	
	@RequestMapping("/updateGoodsDictImg.do")
	@ResponseBody
	public ShopsResult updateGoodsDictImg(String barcode,@RequestParam(value="fileupload",required=true)MultipartFile file,HttpServletRequest request){
		return goodsService.updateGoodsDictImg(barcode,file,request);
	}
	/**
     * 更新商品详细信息
     * @param monitor_info_id 商品id
     * @return
     */
	@RemoteLog(title="更新商品详细信息", businessType = BusinessType.UPDATE)
	@RequestMapping("/updateGoodsDetails.do")
	@ResponseBody
    public PurResult updateGoodsDetails(HttpServletRequest request,String monitor_info_id){
		MultipartFile file1 = null;
		MultipartFile file2 = null;
		MultipartFile file3 = null;
		MultipartFile file4 = null;
		if(request instanceof MultipartHttpServletRequest){
			MultipartHttpServletRequest multipartHttpServletRequest =(MultipartHttpServletRequest)request;
			Map<String, MultipartFile> mp = multipartHttpServletRequest.getFileMap();
			file1 = mp.get("carousel_img1");
			file2 = mp.get("carousel_img2");
			file3 = mp.get("carousel_img3");
			file4 = mp.get("vedioInput");
		}
    	return goodsService.updateGoodsDetails(file1,file2,file3,file4,request,monitor_info_id);
    }
	
	/**
     * 批量更新商品库存
     * @return
     */
	@RemoteLog(title="批量更新商品库存", businessType = BusinessType.UPDATE)
	@RequestMapping("/batchUpdateCount.do")
	@ResponseBody
    public PurResult batchUpdateCount(HttpServletRequest request){
    	return goodsService.batchUpdateCount(request);
    }
	
	//跳转到打印价签选择商品页面
	@RequestMapping("/printPriceTag.do")
	public String printPriceTag(){
		return "/WEB-INF/goods/printPriceTag.jsp";
	}
	/**
	 * 商品出入库明细
	 * @return
	 */
	@RequestMapping("/goodsSaleStatisticsDetail.do")
	public String goodsSaleStatisticsDetail(String goodsBarcode,String goodsName,Model model){
		model.addAttribute("goodsBarcode", goodsBarcode);
		model.addAttribute("goodsName", goodsName);
		return "/WEB-INF/countMessage/goodsSaleStatisticsDetail.jsp";
	}

	/**
	 * 新增入库单
	 * @return
	 */
	@RequestMapping("/addIntoStockPage.do")
	public String addIntoStockPage(Model model){
		model.addAttribute("listUnique", UniqueUtils.createListUnique());
		return "/WEB-INF/countMessage/intoStockAdd.jsp";
	}
	/**
	 * 新增入库单
	 * @return
	 */
	@RemoteLog(title="新增入库单", businessType = BusinessType.INSERT)
	@PostMapping("/addIntoStock.do")
	@ResponseBody
	public PurResult addIntoStock(@RequestBody @Validated InStockParam inStockParam){
		return stockService.addIntoStock(inStockParam);
	}

	/**
	 * 修改入库单
	 * @return
	 */
	@RemoteLog(title="修改入库单", businessType = BusinessType.UPDATE)
	@PostMapping("/editIntoStock.do")
	@ResponseBody
	public PurResult editIntoStock(@RequestBody @Validated InStockParam inStockParam){
		return stockService.editIntoStock(inStockParam);
	}
	/**
	 * 查询出入库分页记录
	 * @return
	 */
	@GetMapping("/queryGoodsStatisticsDetail2.do")
	@ResponseBody
	public PurResult queryGoodsStatisticsDetail2(@Validated ShopStockDetailQueryParam params){
		PurResult result = new PurResult();
		try {
			result = stockService.listPage(params);
		} catch (Exception e) {
			result.setStatus(0);
			result.setData(null);
			result.setMsg("查询失败!");
			e.printStackTrace();
		}		
		return result;
	}
	/**
	 * 删除商品信息，并修改同步信息至删除
	 * @param list_unique
	 * @return
	 */
	@RemoteLog(title="删除入库(出库)单", businessType = BusinessType.DELETE)
	@RequestMapping("/deleteStock.do")
	@ResponseBody
	public ShopsResult deleteStock(
			String list_unique,
			String shop_unique
			){
		Map<String,Object> map=new HashMap<String,Object>();
		map.put("list_unique", list_unique);
		map.put("shop_unique", shop_unique);
		return stockService.deleteStock(map);
	}

	/**
	 * 审核
	 * @param list_unique
	 * @return
	 */
	@RemoteLog(title="审核入库单", businessType = BusinessType.UPDATE)
	@RequestMapping("/addAuditStock.do")
	@ResponseBody
	public ShopsResult addAuditStock(
			String list_unique,
			String shop_unique,
			String audit_status,
			String stock_type_code,
			String staff_id,
			String audit_content,
			HttpServletRequest request
	){
		return stockService.addAuditStock(list_unique,shop_unique,audit_status,stock_type_code, staff_id, audit_content, request);
	}

	/**
	 * 入库单详情页面
	 * @return
	 */
	@GetMapping("/queryStockDetailPage.do")
	public String queryStockDetailPage(Long shopStockDetailId, Integer stockType, HttpServletRequest request){
		ShopStockDetail shopStockDetail = shopStockService.selectById(shopStockDetailId);
		if (ObjectUtil.isNotNull(shopStockDetail)) {
			ShopStockDetailVO vo = BeanUtil.toBean(shopStockDetail, ShopStockDetailVO.class);
			if (ObjectUtil.isNotNull(vo.getStockTime())) {
				vo.setStockTimeStr(DateUtil.formatDateTime(vo.getStockTime()));
			}
			vo.setGoodsInPriceType(GoodsInPriceTypeEnums.CUSTOM.getCode());
			ShopsConfig shopsConfig = shopsConfigService.selectByShopUnique(shopStockDetail.getShopUnique());
			if (ObjectUtil.isNotNull(shopsConfig) && ObjectUtil.isNotNull(shopsConfig.getGoodsInPriceType())) {
				vo.setGoodsInPriceType(shopsConfig.getGoodsInPriceType());
			}
			request.setAttribute("shopStockDetail", vo);
		}
		if(ObjectUtil.equals(Integer.valueOf(1), stockType)){
			return "/WEB-INF/countMessage/intoStockDetail.jsp";
		}else{
			return "/WEB-INF/countMessage/outStockDetail.jsp";
		}
	}
	/**
	 * 查询出入库详情
	 * @param list_unique
	 * @return
	 */
	@RequestMapping("/queryStockDetail.do")
	@ResponseBody
	public ShopsResult queryStockDetail(String list_unique, String shop_unique, Integer stock_type, Integer goodsInPriceType){
		Map<String,Object> map=new HashMap<String,Object>();
		map.put("list_unique", list_unique);
		map.put("shop_unique", shop_unique);
		map.put("stock_type", stock_type);
		map.put("goodsInPriceType", goodsInPriceType);
		return stockService.queryStockDetail(map);
	}

	/**
	 * 新增出库单
	 * @return
	 */
	@RequestMapping("/addOutStockPage.do")
	public String addOutStockPage(Model model, String shopUnique){
		model.addAttribute("listUnique", UniqueUtils.createListUnique());
		ShopsConfig shopsConfig = shopsConfigService.selectByShopUnique(shopUnique);
		if (ObjectUtil.isNotNull(shopsConfig) && ObjectUtil.isNotNull(shopsConfig.getGoodsInPriceType())) {
			model.addAttribute("goodsInPriceType", shopsConfig.getGoodsInPriceType());
		}
		return "/WEB-INF/countMessage/outStockAdd.jsp";
	}
	/**
	 * 新增出库单
	 * @return
	 */
	@RemoteLog(title="新增出库单", businessType = BusinessType.INSERT)
	@PostMapping("/addOutStock.do")
	@ResponseBody
	public PurResult addOutStock(@RequestBody @Validated OutStockParam outStockParam) {
		return stockService.addOutStock(outStockParam);
	}

	/**
	 * 修改出单
	 * @return
	 */
	@RemoteLog(title="修改出库单", businessType = BusinessType.UPDATE)
	@PostMapping("/editOutStock.do")
	@ResponseBody
	public PurResult editOutStock(@RequestBody @Validated OutStockParam outStockParam){
		return stockService.editOutStock(outStockParam);
	}

	/**
	 * 出库审核
	 * @param auditParam
	 * @return
	 */
	@RemoteLog(title="审核出库单", businessType = BusinessType.UPDATE)
	@PostMapping("/outAuditStock.do")
	@ResponseBody
	public PurResult outAuditStock(@RequestBody @Validated ShopOutStockAuditParam auditParam){
		return stockService.outAuditStock(auditParam);
	}
	/**
	 * 修改详情页面
	 * @return
	 */
	@RequestMapping("/queryStockDetailEditPage.do")
	public String queryStockDetailEditPage(Long shopStockDetailId, String stock_type_code, HttpServletRequest request){
		ShopStockDetail shopStockDetail = shopStockService.selectById(shopStockDetailId);
		if (ObjectUtil.isNotNull(shopStockDetail)) {
			ShopStockDetailVO vo = BeanUtil.toBean(shopStockDetail, ShopStockDetailVO.class);
			if (ObjectUtil.isNotNull(vo.getStockTime())) {
				vo.setStockTimeStr(DateUtil.formatDateTime(vo.getStockTime()));
			}
			vo.setGoodsInPriceType(GoodsInPriceTypeEnums.CUSTOM.getCode());
			ShopsConfig shopsConfig = shopsConfigService.selectByShopUnique(shopStockDetail.getShopUnique());
			if (ObjectUtil.isNotNull(shopsConfig) && ObjectUtil.isNotNull(shopsConfig.getGoodsInPriceType())) {
				vo.setGoodsInPriceType(shopsConfig.getGoodsInPriceType());
			}
			request.setAttribute("shopStockDetail", vo);
		}
		if("1".equals(stock_type_code)){
			return "/WEB-INF/countMessage/intoStockEdit.jsp";
		}else{
			return "/WEB-INF/countMessage/outStockEdit.jsp";
		}
	}

	/**
	 * 查询商品调价列表页面
	 * @return
	 */
	@RequestMapping("/queryGoodsModifyPriceListPage.do")
	public String queryGoodsModifyPriceListPage(){
		return "/WEB-INF/goods/modify_price/queryGoodsModifyPriceList.jsp";
	}
	/**
	 * 查询商品调价列表
	 * @return
	 */
	@RequestMapping("/queryGoodsModifyPriceList.do")
	@ResponseBody
	public PurResult queryGoodsModifyPriceList(HttpServletRequest request,
			@RequestParam(value="page",defaultValue="1")Integer page,
			@RequestParam(value="limit",defaultValue="15")Integer limit){		
		PurResult result = new PurResult();
		try {
			Map<String, Object> params = HandleMessyCode.handle(ServletsUtil.getParameters(request));
			params.put("limit", limit);
			params.put("page", (page-1)*limit);
			result=stockService.queryGoodsModifyPriceList(params);
		} catch (Exception e) {
			result.setStatus(0);
			result.setData(null);
			result.setMsg("查询失败!");
			e.printStackTrace();
		}		
		return result;
	}
	
	/**
	 * 调拨单导出excel
	 */
	@RequestMapping("/exportModifyPriceExcel.do")
	public void exportModifyPriceExcel(HttpServletRequest request, HttpServletResponse response) throws Exception{
		Map<String, Object> params = HandleMessyCode.handle(ServletsUtil.getParameters(request));
		List<Map<String,Object>> list=stockService.getGoodsModifyPriceList(params);	
		String[] titles=new String[] {
				"单号","时间","提交人","审核状态","审核人"
		};
		loadModifyPriceXLS(list,"调拨商品",request,response,titles);
	}
	
	public void loadModifyPriceXLS(List<Map<String, Object>> caseVoList, final String string,HttpServletRequest request,HttpServletResponse response,final String[] titles){
		try {
			response.setContentType(
					"application/x-msdownload; charset=UTF-8");// 告知浏览器文件类型
			String agent = request.getHeader("user-agent");
			if (agent.contains("Firefox")) {
				response.setHeader(
						"Content-Disposition",
						"attachment;filename="
								+ new String((string + ".xls")
										.getBytes("GB2312"), "ISO-8859-1"));// 设置文件头
			} else {
				response.setHeader(
						"Content-Disposition",
						"attachment;filename="
								+ URLEncoder.encode(string + ".xls", "UTF-8"));// 设置文件头
			}
			OutputStream os = response.getOutputStream();
			LoadOutObjectXLSUtil<Map<String, Object>> objectXLS = new LoadOutObjectXLSUtil<Map<String, Object>>();
			objectXLS.generateXLS(os, caseVoList, new XLSCallBack<Map<String, Object>>() {
				public Object[] getValues(Map<String, Object> tt) {
					String str = "";
					
					String list_unique = tt.get("list_unique")+"";
					if (StringUtil.blank(list_unique)) {
						list_unique = str;
					}
					String create_time =  tt.get("create_time")+"";
					if (StringUtil.blank(create_time)) {
						create_time = str;
					}
					String staff_name =tt.get("staff_name")+"";
					if (StringUtil.blank(staff_name)) {
						staff_name = str;
					}
					String audit_status = tt.get("audit_status")+"";
					if (StringUtil.blank(audit_status)) {
						audit_status = str;
					}
					String audit_staff_name = tt.get("audit_staff_name")+"";
					if (StringUtil.blank(audit_staff_name)) {
						audit_staff_name = str;
					}					
					
					return new String[] {list_unique,create_time,staff_name,
							audit_status,audit_staff_name
					};
				}

				public String getTitle() {
					return string;
				}

				public int[] getColumnsWidth() {
					return new int[] { 
										  80 * 80, 80 * 80,80 * 80,80 * 80,
										  80 * 80, 80 * 80,80 * 80,80 * 80,
										  80 * 80,80 * 80,80 * 80};
				}

				public String[] getColumnsName() {
					return titles;
				}

				public int[][] MergedRegion() {
					// TODO Auto-generated method stub
					return null;
				}

				public List<Object[]> getheaderValue() {
					// TODO Auto-generated method stub
					return null;
				}

			});
			os.flush();
		} catch (IOException e) {
			e.printStackTrace();
		}
	}
	
	
	/**
	 * 添加商品调价页面
	 * @return
	 */
	@RequestMapping("/addGoodsModifyPage.do")
	public String addGoodsModifyPage(){
		return "/WEB-INF/goods/modify_price/addGoodsModify.jsp";
	}
	/**
	 * 新增商品调价
	 * @return
	 */
	@RemoteLog(title="新增商品调价", businessType = BusinessType.INSERT)
	@RequestMapping("/addGoodsModify.do")
	@ResponseBody
	public PurResult addGoodsModify(
			String shop_unique,
			String shop_list,
			String detailJson,
			String user_id
			){
		return stockService.addGoodsModify(shop_list,detailJson,user_id,shop_unique);
	}
	
	/**
	 * 删除商品调价信息
	 * @param list_unique
	 * @return
	 */
	@RemoteLog(title="删除商品调价信息", businessType = BusinessType.DELETE)
	@RequestMapping("/deleteGoodsModify.do")
	@ResponseBody
	public ShopsResult deleteGoodsModify(
			String list_unique,
			String shop_unique
			){
		Map<String,Object> map=new HashMap<String,Object>();
		map.put("list_unique", list_unique);
		map.put("shop_unique", shop_unique);
		return stockService.deleteGoodsModify(map);
	}
	/**
	 * 商品调价详情页面
	 * @return
	 */
	@RequestMapping("/queryGoodsModifyDetailPage.do")
	public String queryGoodsModifyDetailPage(
			String list_unique,
			HttpServletRequest request
			){
		request.setAttribute("list_unique", list_unique);
		return "/WEB-INF/goods/modify_price/goodsModifyDetail.jsp";
	}
	/**
	 * 查询商品调价详情
	 * @param list_unique
	 * @return
	 */
	@RequestMapping("/queryGoodsModifyDetail.do")
	@ResponseBody
	public ShopsResult queryGoodsModifyDetail(
			String list_unique
			){
		Map<String,Object> map=new HashMap<String,Object>();
		map.put("list_unique", list_unique);
		return stockService.queryGoodsModifyDetail(map);
	}
	
	/**
	 * 审核
	 * @param list_unique
	 * @return
	 */
	@RemoteLog(title="审核商品调价", businessType = BusinessType.UPDATE)
	@RequestMapping("/addAuditGoodsModify.do")
	@ResponseBody
	public ShopsResult addAuditGoodsModify(
			String list_unique,
			String audit_status,
			String user_id,
			HttpServletRequest request
			){
		return stockService.addAuditGoodsModify(list_unique,audit_status,user_id, request);
	}
	
	/**
	 * 修改商品调价页面
	 * @return
	 */
	@RequestMapping("/queryGoodsModifyEditPage.do")
	public String queryGoodsModifyEditPage(
			String list_unique,
			HttpServletRequest request
			){
		request.setAttribute("list_unique", list_unique);
		return "/WEB-INF/goods/modify_price/editGoodsModify.jsp";
	}
	
	/**
	 * 修改商品调价
	 * @return
	 */
	@RemoteLog(title="修改商品调价", businessType = BusinessType.UPDATE)
	@RequestMapping("/updateGoodsModify.do")
	@ResponseBody
	public PurResult updateGoodsModify(
			String shop_unique,
			String shop_list,
			String detailJson,
			String user_id,
			String list_unique
			){
		return stockService.updateGoodsModify(shop_list,detailJson,user_id,shop_unique,list_unique);
	}
	/**
	 * 查询商品调价进价列表页面
	 * @return
	 */
	@RequestMapping("/queryGoodsModifyInPriceListPage.do")
	public String queryGoodsModifyInPriceListPage(){
		return "/WEB-INF/goods/modify_price/queryGoodsModifyInPriceList.jsp";
	}
	/**
	 * 查询商品调价列表
	 * @return
	 */
	@RequestMapping("/queryGoodsModifyInPriceList.do")
	@ResponseBody
	public PurResult queryGoodsModifyInPriceList(HttpServletRequest request,
			@RequestParam(value="page",defaultValue="1")Integer page,
			@RequestParam(value="limit",defaultValue="15")Integer limit){		
		PurResult result = new PurResult();
		try {
			Map<String, Object> params = HandleMessyCode.handle(ServletsUtil.getParameters(request));
			params.put("limit", limit);
			params.put("page", (page-1)*limit);
			result=stockService.queryGoodsModifyInPriceList(params);
		} catch (Exception e) {
			result.setStatus(0);
			result.setData(null);
			result.setMsg("查询失败!");
			e.printStackTrace();
		}		
		return result;
	}
	/**
	 * 添加商品调价页面
	 * @return
	 */
	@RequestMapping("/addGoodsModifyInPricePage.do")
	public String addGoodsModifyInPricePage(){
		return "/WEB-INF/goods/modify_price/addGoodsModifyInPrice.jsp";
	}
	/**
	 * 新增商品调价
	 * @return
	 */
	@RemoteLog(title="新增商品进价调价", businessType = BusinessType.INSERT)
	@RequestMapping("/addGoodsModifyInPrice.do")
	@ResponseBody
	public PurResult addGoodsModifyInPrice(
			String shop_unique,
			String shop_list,
			String detailJson,
			String user_id
			){
		return stockService.addGoodsModifyInPrice(shop_list,detailJson,user_id,shop_unique);
	}
	

	/**
	 * 商品调价详情页面
	 * @return
	 */
	@RequestMapping("/queryGoodsModifyInPriceDetailPage.do")
	public String queryGoodsModifyInPriceDetailPage(
			String list_unique,
			HttpServletRequest request
			){
		request.setAttribute("list_unique", list_unique);
		return "/WEB-INF/goods/modify_price/goodsModifyInPriceDetail.jsp";
	}
	
	/**
	 * 审核进价调价
	 * @param list_unique
	 * @return
	 */
	@RemoteLog(title="审核商品进价调价", businessType = BusinessType.UPDATE)
	@RequestMapping("/addAuditGoodsModifyInPrice.do")
	@ResponseBody
	public ShopsResult addAuditGoodsModifyInPrice(
			String list_unique,
			String audit_status,
			String user_id,
			HttpServletRequest request
			){
		return stockService.addAuditGoodsModifyInPrice(list_unique,audit_status,user_id, request);
	}
	
	/**
	 * 修改商品调价页面
	 * @return
	 */
	@RequestMapping("/queryGoodsModifyInPriceEditPage.do")
	public String queryGoodsModifyInPriceEditPage(
			String list_unique,
			HttpServletRequest request
			){
		request.setAttribute("list_unique", list_unique);
		return "/WEB-INF/goods/modify_price/editGoodsModifyInPrice.jsp";
	}
	
	/**
	 * 修改商品调价
	 * @return
	 */
	@RemoteLog(title="修改商品进价调价", businessType = BusinessType.UPDATE)
	@RequestMapping("/updateGoodsModifyInPrice.do")
	@ResponseBody
	public PurResult updateGoodsModifyInPrice(
			String shop_unique,
			String shop_list,
			String detailJson,
			String user_id,
			String list_unique
			){
		return stockService.updateGoodsModifyInPrice(shop_list,detailJson,user_id,shop_unique,list_unique);
	}
	/**
	  爬取商品图片
	 */
	@RequestMapping("querySperGoods.do")
	@ResponseBody
	public PurResult querySperGoods(String goods_id,String goods_name,String goods_barcode) {
		return goodsService.querySperGoods( goods_id, goods_name, goods_barcode);
	}
    /**
     * 爬取商品图片替换商家图片
     */
    @SuppressWarnings({ "rawtypes", "unlikely-arg-type" })
	@RequestMapping(value="/updatePhoto", method=RequestMethod.POST)
    @ResponseBody
    public PurResult updatePhoto(HttpServletRequest request,String url,String goods_id,String shop_unique){
    	PurResult pr=new PurResult(100001,"操作成功!");
		try {
				Map<String, Object> params =new HashMap<String,Object>();
				
				HttpURLConnection httpCon = null;
		        URLConnection con = null;
		        URL urlObj=null;
		        InputStream in =null;
		        
		        UUID uuid = UUID.randomUUID();
				
		        if(url.startsWith("http")){
                    urlObj = new URL(url);
                    con = urlObj.openConnection();
                    httpCon =(HttpURLConnection) con;
                    in = httpCon.getInputStream();
                    String filePathDetail = File.separator + "image"+ File.separator + shop_unique;
                    String ngoods=uuid.toString()+".jpg";
                    ftpUpload("/"+shop_unique, ngoods,in); //文件服务器存图片
                    in.close();
  	
         
                    params.put("url", filePathDetail+File.separator+ngoods);
    				params.put("goods_id", goods_id);
    				goodsService.updatePhoto(params);
    				pr.setData(filePathDetail+File.separator+ngoods);
                    
                }else
                {
                	pr.setStatus(200001);
        			pr.setMsg("替换失败！");
                }
				
			
		} catch (Exception e) {
			 e.printStackTrace();
			pr.setStatus(200001);
			pr.setMsg("替换失败！");
		}
		return pr;
	}

	/**
	 * 商品列表-yll
	 */
	@RequestMapping("/getGoodBatchList.do")
	@ResponseBody
	public ShopsResult getGoodBatchList(HttpServletRequest request,
								 @RequestParam(value="page",defaultValue="1")Integer page,
								 @RequestParam(value="limit",defaultValue="15")Integer limit,
								 String field,
								 String order
	){
		Long t1=Calendar.getInstance().getTimeInMillis();
		ShopsResult result = new ShopsResult();
		try {
//			Map<String,String[]> m = request.getParameterMap();
			Map<String, Object> params = HandleMessyCode.handle(ServletsUtil.getParameters(request));
			String goodsMessage = MUtil.strObject(params.get("goodsMessage"));
			if(goodsMessage != null && !goodsMessage.equals("")){
				params.put("goodsMessage", "%"+goodsMessage+"%");
			}
			params.put("limit", limit);
			params.put("page", (page-1)*limit);
			params.put("field",field);
			params.put("order",order);
			result=goodsService.getGoodBatchList(params);
			Long t2 = Calendar.getInstance().getTimeInMillis();
			System.out.println("耗时"+(t2-t1));
		} catch (Exception e) {
			result.setStatus(0);
			result.setMsg("查询失败!");
			e.printStackTrace();
		}

		return result;
	}
    public boolean ftpUpload(String directory, String sftpFileName, InputStream input)
    {
    	
    	try {
    		
    		SFTPUtil sftp = new SFTPUtil(FTPConfig.username, FTPConfig.password, FTPConfig.host, FTPConfig.port);   
    		sftp.login(); 
			 sftp.upload(FTPConfig.goods_path+directory, sftpFileName, input);
		} catch (SftpException e) {
			e.printStackTrace();
			return false;
		}
    	return true;
    }
}
