package org.haier.shop.controller;

import java.util.HashMap;
import java.util.Map;

import javax.annotation.Resource;

import org.haier.log.annotation.RemoteLog;
import org.haier.log.enums.BusinessType;
import org.haier.shop.service.GoodsBindingService;
import org.haier.shop.util.PurResult;
import org.haier.shop.util.ShopsResult;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

@Controller
@RequestMapping("/goodsBinding")
public class GoodsBindingController {
	@Resource
	private GoodsBindingService bindService;
	
	@RequestMapping("/addBindingPage.do")
	public String addBindingPage(){
		return "/WEB-INF/goods/addBinding.jsp";
	}
	
	@RequestMapping("/queryShopsBinding.do")
	@ResponseBody
	public PurResult queryShopsBinding(Long shopUnique,Integer useType,String goodsMessage,@RequestParam(value = "page", defaultValue = "1") int page,
			@RequestParam(value = "limit", defaultValue = "8") int pageSize){
		Map<String,Object> map=new HashMap<String, Object>();
		map.put("shopUnique", shopUnique);
		map.put("useType", useType);
		if(null != goodsMessage && !goodsMessage.equals("")){
			map.put("goodsMessage", "%"+goodsMessage+"%");
		}
		map.put("pageSize", pageSize);
		map.put("startNum", (page-1)*pageSize);
		return bindService.queryShopsBinding(map);
	}
	
	/**
	 * 删除已有的商品捆绑关系
	 * @param bindingUnique
	 * @return
	 */
	@RemoteLog(title = "删除已有的商品捆绑关系", businessType = BusinessType.DELETE)
	@RequestMapping("/deleteBindingGoods.do")
	@ResponseBody
	public ShopsResult deleteBindingGoods(Long bindingUnique,Long shopUnique){
		Map<String,Object> map=new HashMap<String, Object>();
		map.put("bindingUnique", bindingUnique);
		map.put("shopUnique", shopUnique);
		return  bindService.deleteBindingGoods(map);
	}
	
	
	/**
	 * 修改商品捆绑消息
	 * @param map
	 * @return
	 */
	@RemoteLog(title = "启停用商品捆绑关系", businessType = BusinessType.UPDATE)
	@RequestMapping("/modifyBinding.do")
	@ResponseBody
	public ShopsResult modifyBinding(Long shopUnique,Long bindingUnique,Integer useType){
		Map<String,Object> map=new HashMap<String, Object>();
		map.put("shopUnique", shopUnique);
		map.put("bindingUnique", bindingUnique);
		map.put("useType", useType);
		return bindService.modifyBinding(map);
	}
	
	/**
	 * 添加捆绑关系
	 * @param goodsBarcodes
	 * @param goodsCounts
	 * @param shopUnique
	 * @param bindingTotal
	 * @return
	 */
	@RemoteLog(title = "添加商品捆绑关系", businessType = BusinessType.INSERT)
	@RequestMapping("/newBindingGoods.do")
	@ResponseBody
	public ShopsResult newBindingGoods(
			@RequestParam(value="goodsBarcodes",required=true)String goodsBarcodes,
			@RequestParam(value="goodsCounts",required=true)String goodsCounts,
			@RequestParam(value="shopUnique",required=true)Long shopUnique,
			@RequestParam(value="bindingTotal",required=true)Double bindingTotal
			){
		return bindService.newBindingGoods(goodsBarcodes, goodsCounts, shopUnique,bindingTotal);
	}
}
