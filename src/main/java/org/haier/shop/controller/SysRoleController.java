package org.haier.shop.controller;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.apache.shiro.SecurityUtils;
import org.apache.shiro.subject.Subject;
import org.haier.log.annotation.RemoteLog;
import org.haier.log.enums.BusinessType;
import org.haier.shop.entity.Staff;
import org.haier.shop.entity.SysAction;
import org.haier.shop.entity.SysPermission;
import org.haier.shop.entity.SysRole;
import org.haier.shop.service.SysActionService;
import org.haier.shop.service.SysMenuService;
import org.haier.shop.service.SysRoleService;
import org.haier.shop.util.HandleMessyCode;
import org.haier.shop.util.PurResult;
import org.haier.shop.util.ServletsUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

@Controller
@RequestMapping("/sys/role")
public class SysRoleController{
    private static final Logger logger = LoggerFactory.getLogger(SysRoleController.class);
    
    @Autowired
    private SysRoleService roleService;
    
    @Autowired
    private SysMenuService menuService;
    
    @Autowired
    private SysActionService actionService;

    @RequestMapping(value = "/getRoleList.do")
    public String getRoleList(Model model){
    	logger.info("后台管理-获取总系统角色列表");
        return "/WEB-INF/sys/role/roleList.jsp";
    }
    
    @RequestMapping(value = "/addPage.do")
    public String addPage(){
    	logger.info("后台管理-打开添加页面");
        return "/WEB-INF/sys/role/addRole.jsp";
    }
    
    @RequestMapping(value="/queryList.do")
	@ResponseBody
	public PurResult queryList(
			HttpServletRequest request,
			@RequestParam(value = "page", defaultValue = "1") int page,
			@RequestParam(value = "limit", defaultValue = "8") int pageSize){
    	Map<String, Object> params = HandleMessyCode.handle(ServletsUtil.getParameters(request));
		params.put("limit", pageSize);
		params.put("page", (page-1)*pageSize);
		
		PurResult result = new PurResult();
		Subject subject = SecurityUtils.getSubject();
		Staff staff = (Staff) subject.getPrincipal();
		try {
			if(staff.getShop_class() == 1 && staff.getShop_type() == 6) {
				params.put("manager_unique", staff.getManager_unique());
				params.remove("shop_unique");
			}
			List<SysRole> list = roleService.quertRoleList(params);
	    	Integer count = roleService.quertRoleListCount(params);
	    	result.setStatus(1);
			result.setMsg("成功");
			result.setCount(count);
			result.setData(list);			
		} catch (Exception e) {
			logger.error("列表查询异常：",e);
			e.printStackTrace();
			result.setStatus(0);
			result.setMsg("异常");
		}
		return result;
	}

	@RemoteLog(title = "添加角色", businessType = BusinessType.INSERT)
    @RequestMapping(value = "/addRole.do")
    @ResponseBody
    public PurResult addRole(SysRole role){
    	logger.info("后台管理-添加角色");
    	PurResult result = new PurResult();
    	try {
    		SysRole sysRole = roleService.getRole(role);
    		if(sysRole != null){
    			result.setStatus(0);
    			result.setMsg("角色名称已存在");
    			return result;
    		}
    		roleService.insert(role);
    		result.setStatus(1);
    		result.setMsg("成功");
		} catch (Exception e) {
			logger.info("后台管理-添加角色异常："+e.getMessage());
			result.setStatus(0);
			result.setMsg("异常");
		}
        return result;
    }
    
    @RequestMapping(value = "/getRole.do")
    public String getRole(String code,Model model){
    	logger.info("后台管理-获取角色详情");
    	try {
    		SysRole role = new SysRole();
    		role.setCode(code);
    		role = roleService.getRole(role);
    		model.addAttribute("role", role);
		} catch (Exception e) {
			return "/error";
		}
    	return "/WEB-INF/sys/role/updateRole.jsp";
    }

	@RemoteLog(title = "修改角色", businessType = BusinessType.UPDATE)
    @RequestMapping(value = "/updateRole.do")
    @ResponseBody
    public PurResult updateRole(SysRole role){
    	logger.info("后台管理-修改角色");
    	PurResult result = new PurResult();
    	try {
    		SysRole params = new SysRole();
    		params.setName(role.getName());
    		params.setShop_unique(role.getShop_unique());
    		SysRole sysRole = roleService.getRole(params);
    		if(sysRole != null && !sysRole.getCode().equals(role.getCode())){
    			result.setStatus(0);
    			result.setMsg("角色名称已存在");
    			return result;
    		}
			roleService.update(role);
			result.setStatus(1);
			result.setMsg("成功");
		} catch (Exception e) {
			logger.info("后台管理-修改角色异常："+e.getMessage());
			result.setStatus(0);
			result.setMsg("异常");
		}
        return result;
    }

	@RemoteLog(title = "删除角色", businessType = BusinessType.DELETE)
    @RequestMapping(value = "/deleteRole.do")
    @ResponseBody
    public PurResult deleteRole(String code){
    	logger.info("后台管理-删除角色");
    	PurResult result = new PurResult();
    	try {	
    			Map<String ,Object> params = new HashMap<String, Object>();
    			params.put("role_code", code);
    		    int count = roleService.getStaffByRoleCodeCount(params);
    		    if(count>0){
    				result.setStatus(2);
    				result.setMsg("请先删除该角色下用户");
    				return result;
    		    }
    			roleService.delete(code);
    			result.setStatus(1);
    			result.setMsg("成功");
		} catch (Exception e) {
			logger.info("后台管理-删除角色异常："+e.getMessage());
			result.setStatus(0);
			result.setMsg("异常");
		}
        return result;
    }
    
    @RequestMapping("/auth.do")
	public String auth(Model model,String code,String type,String shop_unique){
		try {
			model.addAttribute("code", code);
			model.addAttribute("shop_unique", shop_unique);
		} catch (Exception e) {
			logger.error(e.getMessage());
		}
		if("1".equals(type)) {
			return "/WEB-INF/sys/role/roleAuth1.jsp";
		}
		return "/WEB-INF/sys/role/roleAuth.jsp";
	}
    
	/**       
	* 角色授权列表  
	* @param request
	* @param response
	* @param model
	* @return
	*/
	@RequestMapping(value="/authList.do", method=RequestMethod.GET)
	@ResponseBody
	public PurResult authList(String code,Model model,String shop_unique){
		PurResult result = new PurResult();
		try {
			//获取当前登录用户信息
			Subject subject = SecurityUtils.getSubject();
			Staff staff = (Staff) subject.getPrincipal();
	    
			Integer staff_position = staff.getStaff_position();
			if(staff_position == 3){//店主
				
			}
			String version = "";
			Integer type = staff.getShop_type();;
			if(staff.getShop_class() == 0){
				version = "ordinary"; 
			}else if(staff.getShop_class() == 1){
				version = "chain";
			}else if(staff.getShop_class() == 2){
				version = "join";
			}else if(staff.getShop_class() == 3){
				version = "admin";
				type = null;
			}
			Map<String ,Object> params = new HashMap<String, Object>();
   			params.put("version", version);
   			params.put("type", type);
   			
   			//获取一级菜单列表
    		params.put("level", "1");
        	List<SysPermission> perms = menuService.quertMenuList(params);
        	
			//获取角色关联的菜单列表
        	Map<String ,Object> params1 = new HashMap<String, Object>();
        	params1.put("role_code", code);
        	params1.put("shop_unique", shop_unique);
			List<SysPermission> rolePerms = roleService.getMenuListByRoleCode(params1);
			//获取角色关联的操作权限列表
			List<SysAction> roleActiontList = roleService.getActionListByRoleCode(params1);
        	
			//存储json结构菜单
   			List<Map<String,Object>> permsList = new ArrayList<Map<String,Object>>();
   			for(int i=0;i<perms.size();i++) {
   				Map<String,Object> permsMap = new HashMap<String,Object>();
   				if(perms.get(i).getTerminal() != null && !perms.get(i).getTerminal().equals("")){
   					permsMap.put("name", perms.get(i).getName()+"（"+perms.get(i).getTerminal()+"）");
				}else{
					permsMap.put("name", perms.get(i).getName());
				}
   				permsMap.put("value", "menu,"+perms.get(i).getCode());
   				permsMap.put("checked", false);
   				for(int m=0;m<rolePerms.size();m++) {
   					if(rolePerms.get(m).getCode().equals(perms.get(i).getCode())) {
   						permsMap.put("checked", true);
   					}
   				}
   				
   				//获取该一级菜单的二级菜单列表
	        	params.put("level", "2");
	        	params.put("parent_code", perms.get(i).getCode());
				List<SysPermission> levelPerms = menuService.quertMenuList(params);
				if(levelPerms.size() == 0){//没有二级菜单
					List<Map<String,Object>> actionListMap = new ArrayList<Map<String,Object>>();
					//获取该菜单的所有操作权限
					params.put("menu_code", perms.get(i).getCode());
					List<SysAction> actionList = actionService.quertList(params);
	   				for(int n=0;n<actionList.size();n++) {
		   	   			Map<String,Object> actionMap = new HashMap<String,Object>();
		   	   			actionMap.put("name", actionList.get(n).getAction_name());
		   	   			actionMap.put("value", "action,"+actionList.get(n).getCode());
		   	   			actionMap.put("checked", false);
		   	   			for(int m=0;m<roleActiontList.size();m++) {
			   				if(roleActiontList.get(m).getCode().equals(actionList.get(n).getCode())) {
			   					actionMap.put("checked", true);
			   				}
		   				}
		   	   			actionListMap.add(actionMap);
	   				}
	   				if(actionListMap.size()>0){
	   					permsMap.put("list", actionListMap);
	   				}
				}else{
					List<Map<String,Object>> levelPermsListMap = new ArrayList<Map<String,Object>>();
	   				for(int j=0;j<levelPerms.size();j++) {
	   					if(perms.get(i).getCode().equals(levelPerms.get(j).getParent_code())) {
		   	   				Map<String,Object> levelPermsMap = new HashMap<String,Object>();
			   	   			if(levelPerms.get(j).getTerminal() != null && !levelPerms.get(j).getTerminal().equals("")){
			   	   				levelPermsMap.put("name", levelPerms.get(j).getName()+"（"+levelPerms.get(j).getTerminal()+"）");
							}else{
								levelPermsMap.put("name", levelPerms.get(j).getName());
							}
		   	   			    levelPermsMap.put("value", "menu,"+levelPerms.get(j).getCode());
		   	   			    levelPermsMap.put("checked", false);
		   	   			    for(int m=0;m<rolePerms.size();m++) {
		   					   if(rolePerms.get(m).getCode().equals(levelPerms.get(j).getCode())) {
		   						levelPermsMap.put("checked", true);
		   					   }
		   				    }
			   	   			List<Map<String,Object>> actionListMap = new ArrayList<Map<String,Object>>();
			   	   			//获取该菜单的所有操作权限
							params.put("menu_code", levelPerms.get(j).getCode());
							List<SysAction> actionList = actionService.quertList(params);
			   				for(int n=0;n<actionList.size();n++) {
				   	   			Map<String,Object> actionMap = new HashMap<String,Object>();
				   	   			actionMap.put("name", actionList.get(n).getAction_name());
				   	   			actionMap.put("value", "action,"+actionList.get(n).getCode());
				   	   			actionMap.put("checked", false);
				   	   			for(int m=0;m<roleActiontList.size();m++) {
					   				if(roleActiontList.get(m).getCode().equals(actionList.get(n).getCode())) {
					   					actionMap.put("checked", true);
					   				}
				   				}
				   	   			actionListMap.add(actionMap);
			   				}
			   				if(actionListMap.size()>0){
			   					levelPermsMap.put("list", actionListMap);
			   				}
		   	   			    levelPermsListMap.add(levelPermsMap);
	   					}
	   				}
	   				if(levelPermsListMap.size()>0){
	   					permsMap.put("list", levelPermsListMap);
	   				}
				}
   				permsList.add(permsMap);
   			}
			
        	result.setStatus(1);
			result.setMsg("成功");
			result.setData(permsList);
		} catch (Exception e) {
			logger.error("数据查询异常：",e);
			result.setStatus(0);
			result.setMsg("异常");
		}
		return result;
	}
	
	/**       
	* 保存角色授权信息
	* @param request
	* @param response
	* @param entity
	* @return
	*/
	@RemoteLog(title = "保存角色授权信息", businessType = BusinessType.UPDATE)
	@RequestMapping(value = "/saveAuth.do", method = { RequestMethod.GET, RequestMethod.POST })
	@ResponseBody
	public PurResult saveAuth(HttpServletRequest request,HttpServletResponse response) {
		logger.info("后台管理-保存角色授权信息");
		PurResult result = new PurResult();
 		try{
 			Map<String, Object> params = HandleMessyCode.handle(ServletsUtil.getParameters(request));
 			
 	 		roleService.sitesRoleAuth(params);
 	 		result.setStatus(1);
			result.setMsg("成功");
 		}catch(Exception e){
 			logger.info("后台管理-角色授权异常："+e.getMessage());
 			e.printStackTrace();
 			result.setStatus(0);
			result.setMsg("异常");
 		}
        return result;
	}
	
	/**
	 * 给分店角色 -总店授权
	 */
	@RequestMapping(value = "/saveAuth1.do", method = { RequestMethod.GET, RequestMethod.POST })
	@ResponseBody
	public PurResult saveAuth1(HttpServletRequest request,HttpServletResponse response) {
		logger.info("后台管理-保存角色授权信息");
		PurResult result = new PurResult();
 		try{
 			Map<String, Object> params = HandleMessyCode.handle(ServletsUtil.getParameters(request));
 			
 	 		roleService.sitesRoleAuth1(params);
 	 		result.setStatus(1);
			result.setMsg("成功");
 		}catch(Exception e){
 			logger.info("后台管理-角色授权异常："+e.getMessage());
 			e.printStackTrace();
 			result.setStatus(0);
			result.setMsg("异常");
 		}
        return result;
	}
    
}