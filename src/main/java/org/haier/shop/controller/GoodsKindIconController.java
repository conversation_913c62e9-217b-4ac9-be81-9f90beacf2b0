package org.haier.shop.controller;

import org.haier.shop.service.GoodsKindIconService;
import org.haier.shop.util.MyException;
import org.haier.shop.util.PurResult;
import org.haier.shop.util.ShopsResult;
import org.haier.shop.util.UtilForJAVA;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.Map;


@Controller
@RequestMapping("/html/goods")
public class GoodsKindIconController {
    @Resource
    private GoodsKindIconService goodsKindIconService;

    @ResponseBody
    @RequestMapping("/addGoodsKindIcon.do")
    public ShopsResult addGoodsKindIcon(@RequestParam(value="icon",required=true)MultipartFile file,
                                        @RequestParam(value="shop_type",required=true)Integer shop_type,
                                        @RequestParam(value="icon_type",required=true)Integer icon_type, String shop_unique,
                                        @RequestParam(value="create_user",required=true)String create_user, HttpServletRequest request){
        try {
            if (null == file) {
                return new ShopsResult(0, "请至少上传一张图片！");
            }
            return goodsKindIconService.addGoodsKindIcon(file, shop_type, icon_type, shop_unique,create_user, request);
        } catch (MyException e) {
            return new ShopsResult(e.status, e.msg);
        }

    }

    /**
     * 查询图标库
     * @param icon_type 图标类型
     * @param shop_unique 店铺名称
     * @return
     * @throws MyException
     */
    @RequestMapping("/queryGoodsKindIconByIconType.do")
    @ResponseBody
    public ShopsResult queryGoodsKindIconByIconType(@RequestParam(value="icon_type",required=true)Integer icon_type, String shop_unique,HttpServletRequest request){
        try {
            return goodsKindIconService.queryListByIconType(icon_type,shop_unique);
        } catch (MyException e) {
            return new ShopsResult(e.status, e.msg);
        }
    }

    @RequestMapping("/addNewGoodsKindIcon.do")
    public String addNewGoodsKindIcon(HttpServletRequest request,Integer kindType){
        //根据分类类新，将分类的父级分类信息查出
        return "/WEB-INF/goods/addNewGoodsKindIcon.jsp";
    }

    /**
     * 选择图标页面
     * @return
     */
    @RequestMapping("/chooseGoodsKindIconPage.do")
    public String chooseIconPage() {
        return "/WEB-INF/goods/chooseGoodsKindIcon.jsp";
    }
}
