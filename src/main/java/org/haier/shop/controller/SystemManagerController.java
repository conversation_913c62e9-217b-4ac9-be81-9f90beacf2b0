package org.haier.shop.controller;

import java.io.File;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;

import org.haier.shop.config.SysConfig;
import org.haier.shop.entity.log.LogFile;
import org.haier.shop.redis.RedisCache;
import org.haier.shop.service.SystemManagerService;
import org.haier.shop.util.AddOrderTask;
import org.haier.shop.util.Load;
import org.haier.shop.util.ShopsResult;
import org.haier.shop.util.sftp.ScheduleSingLeTon;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.multipart.MultipartFile;

@Controller
@RequestMapping("/systemManager")
public class SystemManagerController {
	@Resource
	private SystemManagerService sysService;

	ScheduleSingLeTon insteans = ScheduleSingLeTon.getInsteans();
	
	@Resource
	private RedisCache redis;

	@RequestMapping("/toTimeSelect.do")
	public String toTimeSelect(String shopUnique,String macId, HttpServletRequest request,String shopName){
		request.setAttribute("shopUnique", shopUnique);
		request.setAttribute("shopName", shopName);
		request.setAttribute("macId", macId);
		return "/WEB-INF/sys/log/timeSelect.jsp";
	}

	/**
	 * 获取日志文件列表
	 * @param shopUnique
	 * @param macId
	 * @return
	 */
	@RequestMapping("/queryLogList.do")
	@ResponseBody
	public ShopsResult queryLogList(String shopUnique, String macId, HttpServletRequest request){
		ShopsResult shopsResult = new ShopsResult(1,"");

		if(null == shopUnique || null == macId){
			return shopsResult;
		}

		macId = macId.replace(":", "").toUpperCase();

		//获取文件信息
		String filePath = SysConfig.FILEPATH + shopUnique + File.separator + macId;
		String visitPath = request.getScheme() + "://" + request.getServerName() + File.separator + "pcLog" + File.separator + shopUnique + File.separator + macId + File.separator;

		File floder = new File(filePath);

		System.out.println("当前文件路径" + filePath);
		System.out.println("当前文件路径" + visitPath) ;

		Long startTime = System.currentTimeMillis() - 24 * 3600 * 10000;
		System.out.println("开始时间" + startTime);

		List<LogFile> fileList = new ArrayList<LogFile>();

		System.out.println(floder.exists());
		System.out.println(floder.isDirectory());
		//文件夹
		if(floder.exists() && floder.isDirectory()){
			//获取所有的文件，并筛选一个月以内的文件
			File[] files = floder.listFiles();
			System.out.println("当前文件夹文件数量" + files.length);
			for(File file : files){
				if(file.isFile()){
					Long modifyTime = file.lastModified();
					System.out.println("当前文件修改时间" + modifyTime);
					System.out.println(startTime < modifyTime);
					if(modifyTime > startTime){
						LogFile logFile = new LogFile();
						logFile.setFileName(file.getName());
						logFile.setFileSize(file.length());
						logFile.setFilePath(visitPath + file.getName());

						fileList.add(logFile);
					}
				}
			}
		}
		shopsResult.setData(fileList);

		return  shopsResult;
	}
	@RequestMapping("/toLogList.do")
	public String toLogList(String shopUnique, String macId){
		return "/WEB-INF/sys/log/logList.jsp";
	}

	@RequestMapping("/sendUpdloadCmd.do")
	@ResponseBody
	public ShopsResult sendUpdloadCmd(String shopUnique,String macId, Integer dayCount,String startTime, String endTime){
		return sysService.sendUpdloadCmd(shopUnique, macId, dayCount, startTime, endTime);
	}
	/**
	 * 查询店铺信息
	 * @param page 页码
	 * @param limit 单页查询数量
	 * @param shopMsg 搜索框信息
	 * @return
	 */
	@RequestMapping("/queryShopMacList.do")
	@ResponseBody
	public ShopsResult queryShopMacList(Integer page,Integer limit,String shopMsg){
		return sysService.queryShopMacList(page,limit,shopMsg);
	}

	@RequestMapping("/toLogPage.do")
	public String toLogPage(){
		return "/WEB-INF/sys/log/shopList.jsp";
	}
	/**
	 * 上传文件测试
	 * @param request
	 * @param file
	 * @return
	 */@RequestMapping("/uploadTest.do")
	@ResponseBody
	public ShopsResult uploadTest(HttpServletRequest request,MultipartFile file ,String id, String value){
		return sysService.uploadTest(request,file,id,value);
	}
	/**
	 * 上传APP升级文件
	 * @param appId 1、APP商城；2、百货商家端；3、供货商（金圈云商）；4、
	 * @param appType
	 * @param updateVersion
	 * @param updateDes
	 * @param updateInstall
	 * @param appName
	 * @param updateLog
	 * @param request
	 * @param file
	 * @param code
	 * @return
	 */
	@RequestMapping("/uploadApp.do")
	@ResponseBody
	public ShopsResult uploadApp(Integer appId, Integer appType, String updateVersion,String updateDes,
			Integer updateInstall,String appName,String updateLog,HttpServletRequest request,MultipartFile file,Integer code) {
		//需要防止段时间内重复提交
		String id = "" + appId + appType + updateVersion;
		Object o = redis.getObject("" + appId + appType + updateVersion);
		if(null == o) {
			redis.putObject(id, id);
		}else {
			ShopsResult sr = new ShopsResult(0, "文件已经提交，请勿重复提交");
			return sr;
		}
		
		return sysService.uploadApp(appId, appType, updateVersion, updateDes, updateInstall, appName, updateLog, request, file,code);
	}
	@RequestMapping("/queryAppUploadDetail.do")
	@ResponseBody
	public ShopsResult queryAppUploadDetail(String app_update_id ) {


		return sysService.queryAppUploadDetail(app_update_id);
	}
	/*
	 * 获取上传进度
	 * */
	@RequestMapping("/getprogressMonitor.do")
	@ResponseBody
	public ScheduleSingLeTon getprogressMonitor(){
		return  insteans;
	}
	@RequestMapping("/uploadAppDetail.do")
	public String toUploadFile(String app_update_id) {

		return "/WEB-INF/sys/update/toUploadDetail.jsp";
	}
	@RequestMapping("/createMd5.do")
	@ResponseBody
	public ShopsResult createMd5(Integer id, String path) {
		//需要防止段时间内重复提交
		String data = "" + id + path;
		Object o = redis.getObject("" + id  + path);
		if(null == o) {
			redis.putObject(data, data,10);
		}else {
			ShopsResult sr = new ShopsResult(0, "请勿重复提交");
			return sr;
		}

		return sysService.createMd5(id, path);
	}
	@RequestMapping("/appPacketUnzip.do")
	@ResponseBody
	public ShopsResult appPacketUnzip(Integer id, Integer appType, String url) {
		//需要防止段时间内重复提交
		String data = "" + id + appType + url;
		Object o = redis.getObject("" + id + appType + url);
		if(null == o) {
			redis.putObject(data, data,60);
		}else {
			ShopsResult sr = new ShopsResult(0, "请勿重复提交");
			return sr;
		}

		return sysService.appPacketUnzip(id, appType, url);
	}
	@RequestMapping("/uploadAppPacket.do")
	@ResponseBody
	public ShopsResult uploadAppPacket(HttpServletRequest request,MultipartFile file) {

		return sysService.uploadAppPacket(request,file);
	}

	@RequestMapping("/toUploadFile.do")
	public String toUploadFile() {
		return "/WEB-INF/sys/update/toUploadFile.jsp";
	}
	
	@RequestMapping("/toUploadProjectFile.do")
	public String toUploadProjectFile() {
		return "/WEB-INF/sys/update/toUploadProjectFile.jsp";
	}
	
	@RequestMapping("/toProjectUpdateRrecord.do")
	public String toProjectUpdateRrecord() {
		return "/WEB-INF/sys/update/toProjectUpdateRrecord.jsp";
	}
	
	@RequestMapping("/toAppUpdateRecord.do")
	public String toAppUpdateRecord(Integer appId,Integer appType) {
		return "/WEB-INF/sys/update/toAppUpdateRecord.jsp";
	}
	/**
	 * 分页查询APP升级记录
	 * @param page 页码
	 * @param limit 单页查询数量
	 * @param project_id 项目ID
	 * @param appId app类型：1、APP商城；2、APP商家端；3、供货商；4、物流端；5、PC客户端；6、一刻钟到家小程序；7、云商系统
	 * @param appType
	 * @param startTime
	 * @param endTime
	 * @return
	 */
	@RequestMapping("/queryAppVersionList.do")
	@ResponseBody
	public ShopsResult queryAppVersionList(Integer page, Integer limit,Integer project_id, Integer appId, Integer appType, String startTime, String endTime) {
		return sysService.queryAppVersionList(page, limit,project_id, appId, appType, startTime, endTime);
	}
	
	@RequestMapping("/queryShopsUsesMsg.do")
	@ResponseBody
	public ShopsResult queryShopsUserMsg(){
		return sysService.queryShopsUserMsg();
	}
	
	/**
	 * 周期内免密使用情况
	 * @return
	 */
	@RequestMapping("/mianmiStatisQuery.do")
	@ResponseBody
	public ShopsResult mianmiStatisQuery(String startTime,String endTime){
		Map<String,Object> map=new HashMap<String,Object>();
		map.put("startTime", startTime);
		map.put("endTime", endTime);
		map.put("mianmiPayCode", Load.MIANMIPAYCODE);
		map.put("mianmiStatus", Load.MIANMISTATUS);
		return sysService.mianmiStatisQuery(map);
	}

	/**
	 * 免密支付走势图数据列表查询
	 * @param startTime
	 * @return
	 */
	@RequestMapping("/mianmiStatisticsPic.do")
	@ResponseBody
	public ShopsResult mianmiStatisticsPic(
			String startTime,
			String endTime
			){
		Map<String,Object> map=new HashMap<String,Object>();
		map.put("startTime", startTime);
		map.put("endTime", endTime);
		map.put("mianmiPayCode", Load.MIANMIPAYCODE);
		map.put("mianmiStatus", Load.MIANMISTATUS);
		return sysService.mianmiStatisticsPic(map);
	}
}
