package org.haier.shop.controller;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import org.haier.shop.service.CustomerRefundsService;
import org.haier.shop.util.ExcelUtil;
import org.haier.shop.util.PurResult;
import org.haier.shop.util.TitleForExcel;
import org.haier.shop.vo.CustomerRefundsVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.io.OutputStream;
import java.net.URLEncoder;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * @Description 退费记录
 * @ClassName CustomerRefundsController
 * <AUTHOR>
 * @Date 2024/4/15 10:27
 **/
@Controller
@RequestMapping("/html/customerRefunds")
public class CustomerRefundsController {

    @Autowired
    private CustomerRefundsService customerRefundsService;

    /**
     * 退费记录页面
     *
     * @return
     */
    @RequestMapping("/toPageList.do")
    public String toPageList() {
        return "/WEB-INF/customerRefunds/customerRefundsPageList.jsp";
    }

    /**
     * 分页查询退费记录
     * @param request
     * @param shopUnique 店铺编码
     * @param pageNum 页码
     * @param pageSize 返回记录数量
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @param cusMessage 会员信息
     * @return
     */
    @RequestMapping("/queryPageList.do")
    @ResponseBody
    public PurResult queryDictList(HttpServletRequest request,
                                   @RequestParam(value="shopUnique")String shopUnique,
                                   @RequestParam(value = "page", defaultValue = "1") int pageNum,
                                   @RequestParam(value = "limit", defaultValue = "8") int pageSize,
                                   String startTime,
                                   String endTime,
                                   String cusMessage) {
        PurResult result = new PurResult();
        try {
            Map<String, Object> paramsMap = new HashMap<>();
            paramsMap.put("shopUnique", shopUnique);
            paramsMap.put("pageNum", (pageNum-1)*pageSize);
            paramsMap.put("pageSize", pageSize);
            paramsMap.put("startTime", startTime);
            paramsMap.put("endTime", endTime);
            paramsMap.put("cusMessage", cusMessage);
            //只查询充值成功
            result = customerRefundsService.queryCustomerRefundsPage(paramsMap);
        }catch (Exception e) {
            e.printStackTrace();
            result.setStatus(0);
            result.setMsg("查询失败");
        }
        return result;
    }

    /**
     * 导出Excel
     * @param request
     * @param shopUnique 店铺编码
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @param cusMessage 会员信息
     */
    @RequestMapping("/exportExcel.do")
    public void exportExcel(HttpServletRequest request, HttpServletResponse response,
                            @RequestParam(value="shopUnique")String shopUnique,
                            String startTime,
                            String endTime,
                            String cusMessage) {
        response.reset();
        response.setHeader("Connection", "close");
        response.setHeader("Content-Type", "application/octet-stream");

        try {
            Map<String, Object> paramsMap = new HashMap<>();
            paramsMap.put("shopUnique", shopUnique);
            paramsMap.put("startTime", startTime);
            paramsMap.put("endTime", endTime);
            paramsMap.put("cusMessage", cusMessage);
            List<CustomerRefundsVO> list = customerRefundsService.queryCustomerRefundsList(paramsMap);
            String filePath=request.getRealPath("/");
            File file=new File(filePath+File.separator+"cusTomer"+shopUnique+".xls");
            if(CollectionUtil.isNotEmpty(list)){
                List<Map<String, Object>> dataList = list.stream().map(v -> {
                    Map<String, Object> m = new HashMap<>();
                    BeanUtil.copyProperties(v, m);
                    return m;
                }).collect(Collectors.toList());
                List<TitleForExcel> tl=new ArrayList<>();//标题
                TitleForExcel t1=new TitleForExcel("cusUnique","会员编号");
                TitleForExcel t2=new TitleForExcel("cusName","会员名称");
                TitleForExcel t3=new TitleForExcel("cusPhone","联系方式");
                TitleForExcel t4=new TitleForExcel("refundsMoney","退费金额");
                TitleForExcel t5=new TitleForExcel("createTime","退费时间");
                TitleForExcel t6=new TitleForExcel("staffName","操作人");
                tl.add(t1);
                tl.add(t2);
                tl.add(t3);
                tl.add(t4);
                tl.add(t5);
                tl.add(t6);
                ExcelUtil.ExcelForListMap(dataList, file, tl);

                OutputStream os=null;
                FileInputStream fis =null;
                int bytesRead = 0;
                byte[] buffer = new byte[8192];
                try {
                    if(!file.exists()){
                        file.createNewFile();
                    }

                    response.setContentType("application/x-msdownload; charset=UTF-8");// 告知浏览器文件类型
                    String agent = request.getHeader("user-agent");
                    if (agent.contains("Firefox")) {
                        response.setHeader(
                                "Content-Disposition",
                                "attachment;filename="
                                        + new String(("退费记录.xls")
                                        .getBytes("GB2312"), "ISO-8859-1"));// 设置文件头
                    } else {
                        response.setHeader(
                                "Content-Disposition",
                                "attachment;filename="
                                        + URLEncoder.encode("退费记录.xls", "UTF-8"));// 设置文件头
                    }

                    fis=new FileInputStream(file);
                    os=response.getOutputStream();
                    while ((bytesRead = fis.read(buffer, 0, 8192)) != -1) {
                        os.write(buffer, 0, bytesRead);
                    };
                    os.flush();
                } catch (IOException e) {
                    e.printStackTrace();
                }finally{
                    try {
                        if(os!=null){
                            os.close();
                        }
                        if(fis!=null){
                            fis.close();
                        }
                    } catch (IOException e) {
                        e.printStackTrace();
                    }
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }
}
