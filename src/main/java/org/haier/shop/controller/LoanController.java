package org.haier.shop.controller;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;

import org.haier.shop.service.LoanService;
import org.haier.shop.util.ServletsUtil;
import org.haier.shop.util.ShopsResult;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;


@RequestMapping("/loan")
@Controller
public class LoanController {
	@Resource
	private LoanService loanService;
	
	/**
	 * 千万额度编辑界面
	 * @return
	 */
	@RequestMapping("/toEditQuota.do")
	public String toEditQuota() {
		return "/WEB-INF/loan/editQuota.jsp";
	}
	
	/**
	 * 查询店铺
	 * @param shop_unique
	 * @return
	 */
	@RequestMapping("/queryLoanShopDetail.do")
	@ResponseBody
	public ShopsResult queryLoanShopDetail(String shop_unique) {
		return loanService.queryLoanShopDetail(shop_unique);
	}
	
	/**
	 * 借款停用启用界面
	 * @return
	 */
	@RequestMapping("/toStopLoan.do")
	public String toStopLoan() {
		return "/WEB-INF/loan/stopLoan.jsp";
	}
	/**
	 * 查询店铺的贷款简报
	 * @return
	 */
	@RequestMapping("/queryShopLoanList.do")
	@ResponseBody
	public ShopsResult queryShopLoanList(String searchMsg,Integer page,Integer limit,Integer auditStatus) {
		
		return loanService.queryShopLoanList(searchMsg, page, limit, auditStatus);
	}
	/**
	 * 查询店铺赊销订单
	 * @return
	 */
	@RequestMapping("/queryLoanOrderList.do")
	@ResponseBody
	public ShopsResult queryLoanOrderList(String searchMsg,Integer page,Integer limit,Integer auditStatus,String num) {
		
		return loanService.queryLoanOrderList(searchMsg, page, limit, auditStatus,num);
	}
	/**
	 * 查询超期还款店铺
	 * @return
	 */
	@RequestMapping("/queryWindControl.do")
	@ResponseBody
	public ShopsResult queryWindControl(String searchMsg,Integer page,Integer limit) {
		
		return loanService.queryWindControl(searchMsg, page, limit);
	}
	/**
	 * 查询超期还款店铺-订单详情-订单
	 * @return
	 */
	@RequestMapping("/queryWindControlDetail.do")
	@ResponseBody
	public ShopsResult queryWindControlDetail(String shop_unique,Integer page,Integer limit) {
		
		return loanService.queryWindControlDetail(shop_unique, page, limit);
	}
	/**
	 * 查询超期还款店铺-订单详情-天数
	 * @return
	 */
	@RequestMapping("/queryWindControlDetail2.do")
	@ResponseBody
	public ShopsResult queryWindControlDetail2(String shop_unique,Integer page,Integer limit) {
		
		return loanService.queryWindControlDetail2(shop_unique, page, limit);
	}
	
	/**
	 * 查询超期还款店铺-订单详情-天数
	 * @return
	 */
	@RequestMapping("/queryWindControlDetail3.do")
	@ResponseBody
	public ShopsResult queryWindControlDetail3(String order_no,Integer page,Integer limit) {
		
		return loanService.queryWindControlDetail3(order_no, page, limit);
	}
	/**
	 * 查询借款规则
	 * @param sxRuleId
	 * @return
	 */
	@RequestMapping("/querySxRuleMsg.do")
	@ResponseBody
	public ShopsResult querySxRuleMsg(Integer sxRuleId) {
		ShopsResult sr = new ShopsResult(1, "查询成功！");
		sr.setData(loanService.querySxRuleMsg(sxRuleId));
		return sr;
	}
	
	@RequestMapping("/toShowImg.do")
	public String toShowImg(String url,HttpServletRequest request) {
		request.setAttribute("url", url);
		return "/WEB-INF/loan/showImg.jsp";
	}
	
	@RequestMapping("/toShopLoanList.do")
	public String toShopLoanList() {
		return "/WEB-INF/loan/shopLoanList.jsp";
	}
	/**
	 * 查询店铺赊销订单
	 * @return
	 */
	@RequestMapping("/toLoanOrder.do")
	public String toLoanOrder(Model model) {
		//获取所有审核通过店铺列表
		List<Map<String ,Object>> shopList = loanService.queryShopLoanList2();
		model.addAttribute("shopList", shopList);
		return "/WEB-INF/loan/shopLoanOrderList.jsp";
	}
	
	@RequestMapping("/toWindControl.do")
	public String toWindControl() {
		return "/WEB-INF/loan/windControl.jsp";
	}	
	/**
	 * 更新店铺审核信息
	 * @param shop_loan_id 店铺申请ID
	 * @param audit_status 修改后的状态
	 * @param loan_money 审批的额度
	 * @param remarks 备注信息，给管理人员看
	 * @param refuse_reason 审核批注，给客户看
	 * @return
	 */
	@RequestMapping("/updateShopLoanMsg.do")
	@ResponseBody
	public ShopsResult updateShopLoanMsg(
			Integer shop_loan_id,Integer audit_status,String loan_money,String remarks,String refuse_reason,Integer new_loan_money
			) {
		Map<String,Object> map = new HashMap<String,Object>();
		map.put("refuse_reason", refuse_reason);
		map.put("remarks", remarks);
		map.put("shop_loan_id", shop_loan_id);
		map.put("audit_status", audit_status);
		map.put("loan_money", loan_money);
		map.put("new_loan_money", new_loan_money);
		return loanService.updateShopLoanMsg(shop_loan_id,audit_status,loan_money,remarks,refuse_reason,new_loan_money);
	}
	
	@RequestMapping("/toShopLoanApplyDetail.do")
	public String toShopLoanApplyDetail() {
		return "/WEB-INF/loan/shopLoanApplyDetail.jsp";
	}
	
	@RequestMapping("/toShopLoanDetail.do")
	public String toShopLoanDetail() {
		return "/WEB-INF/loan/shopLoanDetail.jsp";
	}
	
	@RequestMapping("/toWindControlDetail.do")
	public String toWindControlDetail() {
		return "/WEB-INF/loan/windControlDetail.jsp";
	}
	
	@RequestMapping("/toWindControlDetail2.do")
	public String toWindControlDetail2() {
		return "/WEB-INF/loan/windControlDetail2.jsp";
	}
	
	@RequestMapping("/toWindControlDetail3.do")
	public String toWindControlDetail3() {
		return "/WEB-INF/loan/windControlDetail3.jsp";
	}
	
	@RequestMapping("/toShopApplyList.do")
	public String toShopApplyList() {
		return "/WEB-INF/loan/shopApplyList.jsp";
	}
	/**
	 * @param page 当前查询页数
	 * @param pageSize 每页查询的店铺数量
	 */
	@RequestMapping("/queryLoanShopList.do")
	@ResponseBody
	public ShopsResult queryLoanShopList(Integer page,Integer limit,@RequestParam(value = "audit_status",defaultValue = "1")Integer audit_status) {
		return loanService.queryLoanShopList(page, limit,audit_status);
	}
	/**
	 * 赊销规则配置
	 * @return
	 */
	@RequestMapping("/loanPolicySet.do")
	public String loanPolicySet() {
		return "/WEB-INF/loan/loanPolicySet.jsp";
	}
	
	/**
	 * 更新店铺审核信息
	 * @return
	 */
	@RequestMapping("/queryRules.do")
	@ResponseBody
	public ShopsResult queryRules() {
		return loanService.queryRules();
	}
	/**
	 * 修改赊销规则
	 * @return
	 */
	@RequestMapping("/updateRules.do")
	@ResponseBody
	public ShopsResult updateRules(HttpServletRequest request) {
		Map<String, Object> params = ServletsUtil.getParameters(request);
		return loanService.updateRules(params);
	}
}
