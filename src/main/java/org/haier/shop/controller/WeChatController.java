package org.haier.shop.controller;

import java.io.File;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;
import javax.imageio.stream.FileImageInputStream;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.apache.shiro.SecurityUtils;
import org.apache.shiro.session.Session;
import org.apache.shiro.subject.Subject;
import org.haier.meituan.util.MUtil;
import org.haier.shop.dao.UtilDao;
import org.haier.shop.entity.Staff;
import org.haier.shop.service.ExpressService;
import org.haier.shop.service.ShopService;
import org.haier.shop.service.StaffService;
import org.haier.shop.service.SysAgreementService;
import org.haier.shop.util.Load;
import org.haier.shop.util.PurResult;
import org.haier.shop.util.ShopsResult;
import org.haier.shop.util.UtilForRequest;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import net.sf.json.JSONObject;

@RequestMapping("/wechat")
@Controller
public class WeChatController {
	
	@Resource
	private ShopService shopService;
	
	@Resource
	private SysAgreementService sysAgreementService;
	
	@Resource
	private StaffService staffService;
	
	@Resource
	private ExpressService expressService;
	@Resource
	private UtilDao utilDao;
	
	@RequestMapping("/checkShareMsg.do")
	@ResponseBody
	public ShopsResult checkShareMsg(
			String staffPhone
			,@RequestParam(value="shopUnique",required=true)String shopUnique) {
		try {
			return shopService.checkShareMsg(staffPhone,shopUnique);
		}catch (Exception e) {
			e.printStackTrace();
			ShopsResult sr = new ShopsResult(0,"查询失败");
			return sr;
		}
	}
	
	/**
	 * 跳转微信小程序开通协议页面
	 * @return
	 */
	@RequestMapping("/applyOpen.do")
	public String applyOpen(Model model){
		Subject subject = SecurityUtils.getSubject();
		Session session = subject.getSession();
		Staff staff = (Staff) session.getAttribute("staff");
		String shop_unique = String.valueOf(staff.getShop_unique());
		
		//微信小程序开通状态：0:不显示 1:显示 2:审核中 3:审核失败
		Integer show_buy_status = 0;
		String show_buy_fail_reason = "";
		//获取商家信息
		ShopsResult result = shopService.queryShopMessage(shop_unique);
		if(result.getStatus() == 0){
			@SuppressWarnings("unchecked")
			Map<String,Object> shopInfo = (Map<String, Object>) result.getData();
			show_buy_status = Integer.parseInt(MUtil.strObject(shopInfo.get("show_buy_status")));
			show_buy_fail_reason = MUtil.strObject(shopInfo.get("show_buy_fail_reason"));
		}
		if(show_buy_status == 0){
			//获取微信小程序开通协议
			Map<String ,Object> agreement = sysAgreementService.querySysAgreement("wechat");
			model.addAttribute("agreement", agreement);
			
			return "/WEB-INF/wechatApplet/protocol.jsp";
		}
		
		model.addAttribute("show_buy_status", show_buy_status);
		model.addAttribute("show_buy_fail_reason", show_buy_fail_reason);
		return "/WEB-INF/wechatApplet/showBuyStatus.jsp";
	}
	
	/**
	 * 商家申请开通小程序
	 * @param shop_unique 店铺唯一编码
	 * @return
	 */
	@RequestMapping("/updateShowBuyStatus.do")
	public String updateShowBuyStatus(@RequestParam(value="shop_unique",required=true)Long shop_unique,Model model){
		Map<String,Object> params = new HashMap<String, Object>();
		params.put("shop_unique", shop_unique);
		params.put("show_buy_status", 2);
		shopService.updateShowBuyStatus(params);
		//微信小程序开通状态：0:不显示 1:显示 2:审核中 3:审核失败
		Integer show_buy_status = 0;
		String show_buy_fail_reason = "";
		//获取商家信息
		ShopsResult result = shopService.queryShopMessage(String.valueOf(shop_unique));
		if(result.getStatus() == 0){
			@SuppressWarnings("unchecked")
			Map<String,Object> shopInfo = (Map<String, Object>) result.getData();
			show_buy_status = Integer.parseInt(MUtil.strObject(shopInfo.get("show_buy_status")));
			show_buy_fail_reason = MUtil.strObject(shopInfo.get("show_buy_fail_reason"));
		}
		
		model.addAttribute("show_buy_status", show_buy_status);
		model.addAttribute("show_buy_fail_reason", show_buy_fail_reason);
		return "/WEB-INF/wechatApplet/showBuyStatus.jsp";
	}
	
	/**
	 * 跳转小程序开通审核页面
	 * @return
	 */
	@RequestMapping("/wechatExaminePage.do")
	public String wechatExaminePage(){
		return "/WEB-INF/wechatApplet/wechatExamineList.jsp";
	}
	
	/**
	 * 查询小程序审核列表
	 * @return
	 */
	@RequestMapping("/queryWechatExamineList.do")
	@ResponseBody
	public PurResult queryWechatExamineList(
			String show_buy_status,
			String shopMessage,
			@RequestParam(value = "page", defaultValue = "1") int page,
			@RequestParam(value = "limit", defaultValue = "8") int pageSize){
		Map<String,Object> map=new HashMap<String, Object>();
		map.put("show_buy_status", show_buy_status);
		map.put("shopMessage", shopMessage);
		map.put("pageSize", pageSize);
		map.put("startNum", (page-1)*pageSize);
		return shopService.queryWechatExamineList(map);
	}
	
	/**
	 * 审核详情
	 * @param shop_unique 店铺唯一编码
	 * @return
	 */
	@RequestMapping("/wechatExamineDetail.do")
	public String wechatExamineDetail(@RequestParam(value="shop_unique",required=true)Long shop_unique,Model model){
		Map<String,Object> shopInfo = new HashMap<String, Object>();
		//获取商家信息
		ShopsResult result = shopService.queryShopMessage(String.valueOf(shop_unique));
		if(result.getStatus() == 0){
			shopInfo = (Map<String, Object>) result.getData();
		}
		model.addAttribute("shop_unique", shop_unique);
		model.addAttribute("shopInfo", shopInfo);
		return "/WEB-INF/wechatApplet/wechatExamineDetail.jsp";
	}
	
	/**
	 * 商家开通小程序审核
	 * @param shop_unique 店铺唯一编码
	 * @param show_buy_status 微信小程序开通状态：1:显示  3:审核失败
	 * @param show_buy_fail_reason 审核失败原因
	 * @return
	 */
	@RequestMapping("/examineWechat.do")
	@ResponseBody
	public PurResult examineWechat(@RequestParam(value="shop_unique",required=true)Long shop_unique,String show_buy_status,String show_buy_fail_reason){
		Map<String,Object> params = new HashMap<String, Object>();
		params.put("shop_unique", shop_unique);
		params.put("show_buy_status", show_buy_status);
		params.put("show_buy_fail_reason", show_buy_fail_reason);
		if(show_buy_status != null && show_buy_status.equals("1")){//审核通过
			params.put("beans_agreement", 1);//开通百货豆
		}
		return shopService.updateShowBuyStatus(params);
	}
	
	/**
	 * 跳转店铺信息页面
	 * @return
	 */
	@RequestMapping("/shopInfo.do")
	public String shopInfo(Model model){
		Subject subject = SecurityUtils.getSubject();
		Session session = subject.getSession();
		Staff staff = (Staff) session.getAttribute("staff");
		String shop_unique = String.valueOf(staff.getShop_unique());
		
		//微信小程序开通状态：0:不显示 1:显示 2:审核中 3:审核失败
		Integer show_buy_status = 0;
		String show_buy_fail_reason = "";
		//获取商家信息
		ShopsResult result = shopService.queryShopMessage(shop_unique);
		if(result.getStatus() == 0){
			Map<String,Object> shopInfo = (Map<String, Object>) result.getData();
			show_buy_status = Integer.parseInt(MUtil.strObject(shopInfo.get("show_buy_status")));
			show_buy_fail_reason = MUtil.strObject(shopInfo.get("show_buy_fail_reason"));
		}
		if(show_buy_status == 0){
			//获取微信小程序开通协议
			Map<String ,Object> agreement = sysAgreementService.querySysAgreement("wechat");
			model.addAttribute("agreement", agreement);
			
			return "/WEB-INF/wechatApplet/protocol.jsp";
		}else if(show_buy_status == 1) {
			//查询店铺信息
			Map<String, Object> shopInfo = staffService.queryShopInfoByShopUnique(Long.valueOf(shop_unique));
			model.addAttribute("shop", shopInfo);
			//获取所有快递公司信息
			List<Map<String ,Object>> expressList = expressService.getExpressList();
			model.addAttribute("expressList", expressList);
			//获取当前店铺关联快递公司
			List<Map<String ,Object>> shopExpressList = shopService.getShopExpressRelationList(shop_unique);
			model.addAttribute("shopExpressList", shopExpressList);
			//获取当前店铺营业时间列表
			List<Map<String ,Object>> shopHoursList = shopService.getShopHoursList(shop_unique);
			model.addAttribute("shopHoursList", shopHoursList);
			return "/WEB-INF/wechatApplet/shopInfo.jsp";
		}else{
			model.addAttribute("show_buy_status", show_buy_status);
			model.addAttribute("show_buy_fail_reason", show_buy_fail_reason);
			return "/WEB-INF/wechatApplet/showBuyStatus.jsp";
		}
	}
	
	/**
	 * 下载微信小程序二维码信息
	 * @param width 二维码宽度
	 * @param shopUnique 店铺编号
	 * @param shareId 分享人cusUnique
	 * @param shopType 店铺类型
	 */
	@RequestMapping("/createCode.do")
	public void createCode(String shopUnique,Integer width,String shareId, HttpServletRequest request,
			HttpServletResponse response,Integer shopType){
		if(null == shopUnique || null == width){
			return;
		}
		String absPath = request.getServletContext().getRealPath("");
		absPath = new File(absPath).getParent();
		absPath = absPath + File.separator+"smallProgramCode"+File.separator;
		File f = new File(absPath);
		if(!f.exists()){
			f.mkdirs();
		}
		absPath = absPath+shopUnique+"-"+width+".jpg";
		//查询店铺小程序码类型
		
		try{
			String params = "";
			Integer smallType = utilDao.getShopType(shopUnique);
			if(smallType == null || smallType == 1) {
				params = "grant_type=client_credential&appid="+Load.APPID+"&secret="+Load.SECRET;
			}else if(smallType == 2) {
				params = "grant_type=client_credential&appid="+Load.WOAPPID+"&secret="+Load.WOSECRET;
			}else if(smallType == 4) {
				params = "grant_type=client_credential&appid="+Load.MHJAPPID+"&secret="+Load.MHJSECRET;
			}
			String access_token = JSONObject.fromObject(UtilForRequest.doGet("https://api.weixin.qq.com/cgi-bin/token", params)).getString("access_token");
		
			String json;
			if(shopType != null && shopType == 8){
				json = "{\"path\":\"pages/bindingStores/bindingStores?shareId="+shareId+"&shopUnique="+shopUnique+"\",\"width\":"+width+"}";
			}else {
				json = "{\"path\":\"pages/index/index?shareId="+shareId+"&shop_unique="+shopUnique+"\",\"width\":"+width+"}";
			}
			
			System.out.println(json.toString());
			byte[] data = UtilForRequest.post("https://api.weixin.qq.com/wxa/getwxacode?access_token="+access_token, json);
			if(data.length<100){
				System.out.println(data);
			}
			response.setHeader("Content-Type","application/octet-stream");
			response.setHeader("Content-Disposition","attachment;filename=code.png");
			response.getOutputStream().write(data);;
			response.getOutputStream().flush();
			response.getOutputStream().close();
			
		}catch(Exception e){
			e.printStackTrace();
		}
		
	}
	
	/**
	 * 下载微信小程序支付页面二维码信息
	 */
	@RequestMapping("/createCode1.do")
	public void createCode1(String shopUnique,HttpServletRequest request,HttpServletResponse response){
		if(null == shopUnique){
			return;
		}
		String absPath = request.getServletContext().getRealPath("");
		absPath = new File(absPath).getParent();
		absPath = absPath + File.separator+"smallProgramCode"+File.separator;
		File f = new File(absPath);
		if(!f.exists()){
			f.mkdirs();
		}
		absPath = absPath+shopUnique+"-1000.jpg";
		//查询店铺小程序码类型
		
		try{
			String params = "";
			Integer smallType = utilDao.getShopType(shopUnique);
			if(smallType == null || smallType == 1) {
				params="grant_type=client_credential&appid="+Load.APPID+"&secret="+Load.SECRET;
			}else if(smallType == 2) {
				params = "grant_type=client_credential&appid="+Load.WOAPPID+"&secret="+Load.WOSECRET;
			}
			String access_token = JSONObject.fromObject(UtilForRequest.doGet("https://api.weixin.qq.com/cgi-bin/token", params)).getString("access_token");
		
			String json = "{\"path\":\"pages/confirm-order/confirm-order?shopUnique="+shopUnique+"\",\"width\":1000}";

			
//			json = "{\"path\":\"pages/selfServiceSuperMarket/main/mainPage?shareId="+shareId+"&shop_unique="+shopUnique+"\",\"width\":"+width+"}";
//			json = "{\"path\":\"pages/productDetails/productDetails?shareId="+"1562901534802"+"&shopId="+"1578733747161"+"&goodsBarcode=99000002\",\"width\":1200}";
			System.out.println(json.toString());
			byte[] data = UtilForRequest.post("https://api.weixin.qq.com/wxa/getwxacode?access_token="+access_token, json);
			if(data.length<100){
				System.out.println(data);
			}
			response.setHeader("Content-Type","application/octet-stream");
			response.setHeader("Content-Disposition","attachment;filename=code.png");
			response.getOutputStream().write(data);;
			response.getOutputStream().flush();
			response.getOutputStream().close();
			
		}catch(Exception e){
			e.printStackTrace();
		}
		
	}
	
	/**
	 * 下载微信小程序充值页面二维码信息
	 */
	@RequestMapping("/createCode2.do")
	public void createCode2(String shopUnique, HttpServletRequest request,HttpServletResponse response){
		if(null == shopUnique){
			return;
		}
		String absPath = request.getServletContext().getRealPath("");
		absPath = new File(absPath).getParent();
		absPath = absPath + File.separator+"smallProgramCode"+File.separator;
		File f = new File(absPath);
		if(!f.exists()){
			f.mkdirs();
		}
		absPath = absPath+shopUnique+"-1000.jpg";
		//查询店铺小程序码类型
		
		try{
			String params = "";
			Integer smallType = utilDao.getShopType(shopUnique);
			if(smallType == null || smallType == 1) {
				params="grant_type=client_credential&appid="+Load.APPID+"&secret="+Load.SECRET;
			}else if(smallType == 2) {
				params = "grant_type=client_credential&appid="+Load.WOAPPID+"&secret="+Load.WOSECRET;
			}
			String access_token = JSONObject.fromObject(UtilForRequest.doGet("https://api.weixin.qq.com/cgi-bin/token", params)).getString("access_token");
		
			String json = "{\"path\":\"pages/recharge/recharge?shopUnique="+shopUnique+"\",\"width\":1000}";

			
//			json = "{\"path\":\"pages/selfServiceSuperMarket/main/mainPage?shareId="+shareId+"&shop_unique="+shopUnique+"\",\"width\":"+width+"}";
//			json = "{\"path\":\"pages/productDetails/productDetails?shareId="+"1562901534802"+"&shopId="+"1578733747161"+"&goodsBarcode=99000002\",\"width\":1200}";
			System.out.println(json.toString());
			byte[] data = UtilForRequest.post("https://api.weixin.qq.com/wxa/getwxacode?access_token="+access_token, json);
			if(data.length<100){
				System.out.println(data);
			}
			response.setHeader("Content-Type","application/octet-stream");
			response.setHeader("Content-Disposition","attachment;filename=code.png");
			response.getOutputStream().write(data);;
			response.getOutputStream().flush();
			response.getOutputStream().close();
			
		}catch(Exception e){
			e.printStackTrace();
		}
		
	}
	
	public static boolean rebackPic(File file ,HttpServletResponse response){
		if(file == null || response == null){
			return false;
		}
		try{
			FileImageInputStream  fs = new FileImageInputStream (file);
			int streamLength = (int)fs.length();
			byte[] image = new byte[streamLength ];
			fs.read(image,0,streamLength );
			fs.close();
			
			response.setHeader("Content-Type","application/octet-stream");
			response.setHeader("Content-Disposition","attachment;filename=code.jpg");
			response.getOutputStream().write(image);;
			response.getOutputStream().flush();
			response.getOutputStream().close();
		}catch(Exception e){
			e.printStackTrace();
			return false;
		}
		
		return true;
	}
	
	/**
	 * 获取商家开通小程序状态
	 * @param shop_unique 店铺唯一编码
	 * @return
	 */
	@RequestMapping("/getShopShowBuyStatus.do")
	@ResponseBody
	public PurResult getShopShowBuyStatus(@RequestParam(required=true,value="shop_unique")String shop_unique){
		PurResult result = new PurResult();
		try {
			//获取商家信息
			ShopsResult shopResult = shopService.queryShopMessage(shop_unique);
			if(shopResult.getStatus() == 0){
				Map<String,Object> shopInfo = (Map<String, Object>) shopResult.getData();
				//微信小程序开通状态：0:不显示 1:显示 2:审核中 3:审核失败
				Integer show_buy_status = Integer.parseInt(MUtil.strObject(shopInfo.get("show_buy_status")));
				if(show_buy_status == 1) {//如果已开通，查询对应绑定会员信息
					result.setCord(shopInfo);
				}
				result.setData(show_buy_status);
				result.setStatus(1);
				result.setMsg("成功");
			}
		} catch (Exception e) {
			e.printStackTrace();
			result.setStatus(0);
			result.setMsg("异常");
		}
		
		return result;
	}
}
