package org.haier.shop.controller;

import java.io.File;
import java.io.InputStream;
import java.util.HashMap;
import java.util.Map;
import java.util.UUID;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;

import org.haier.shop.service.SysAgreementService;
import org.haier.shop.util.FTPConfig;
import org.haier.shop.util.PicSaveUtil;
import org.haier.shop.util.PurResult;
import org.haier.shop.util.SFTPUtil;
import org.haier.shop.util.ServletsUtil;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.multipart.MultipartFile;

@Controller
@RequestMapping("/agreement")
public class SysAgreementController {
	
	@Resource
	private SysAgreementService sysAgreementService;
	
	//跳转协议管理页面
	@RequestMapping("management.do")
	public String management(Model model){
		//获取百货豆开通协议
		Map<String ,Object> beans = sysAgreementService.querySysAgreement("beans");
		model.addAttribute("beans", beans);
		//获取微信小程序开通协议
		Map<String ,Object> wechat = sysAgreementService.querySysAgreement("wechat");
		model.addAttribute("wechat", wechat);
		return "/WEB-INF/sys/agreement/agreement.jsp";
	}
	
	/**
	 * 小程序协议管理
	 */
	@RequestMapping("toWechatAgreement.do")
	public String toWechatAgreement(Model model){
		//获取百货豆开通协议
		Map<String ,Object> beans1 = sysAgreementService.querySysAgreement("card_recharge");
		model.addAttribute("beans1", beans1);
		//获取微信小程序开通协议
		Map<String ,Object> beans2 = sysAgreementService.querySysAgreement("bean_recharge");
		model.addAttribute("beans2", beans2);
		Map<String ,Object> beans3 = sysAgreementService.querySysAgreement("density_free");
		model.addAttribute("beans3", beans3);
		return "/WEB-INF/sys/agreement/wechatAgreement.jsp";
	}
	
	/**
	 * 储蓄卡充值说明
	 */
	@RequestMapping("toRechargeExplain.do")
	public String toRechargeExplain(Model model){
		Map<String ,Object> map = sysAgreementService.querySysAgreement("card_recharge");
		model.addAttribute("beans", map);
		return "/WEB-INF/sys/agreement/card_recharge_explain.jsp";
	}
	
	/**
	 * 百货豆充值说明
	 */
	@RequestMapping("toBeanExplain.do")
	public String toBeanExplain(Model model){
		Map<String ,Object> map = sysAgreementService.querySysAgreement("bean_recharge");
		model.addAttribute("beans", map);
		return "/WEB-INF/sys/agreement/bean_recharge_explain.jsp";
	}
	
	/**
	 * 修改协议
	 * @param code 协议code：beans百货豆开通协议，wechat 微信小程序开通协议
	 * @param name 协议名称
	 * @param content 协议内容
	 * @return
	 */
	@RequestMapping("/updateSysAgreement.do")
	@ResponseBody
	public PurResult updateSysAgreement(String code,String name,String content){
		PurResult result = new PurResult();
		try {
			Map<String,Object> params = new HashMap<String, Object>();
			params.put("code", code);
			params.put("name", name);
			params.put("content", content);
			sysAgreementService.updateSysAgreement(params);
			
			result.setStatus(1);
			result.setMsg("成功");
		} catch (Exception e) {
			e.printStackTrace();
			result.setStatus(0);
			result.setMsg("异常");
		}
		
		return result;
	}
	
	/**
	 * 跳转故障处理
	 */
	@RequestMapping("faultHanding.do")
	public String faultHanding(Model model,HttpServletRequest request,String type){
		model.addAttribute("type", type);
		return "/WEB-INF/fault/handling.jsp";
	}
	
	/**
	 * 获取故障处理列表
	 * @return
	 */
	@RequestMapping("queryfaultHanding.do")
	@ResponseBody
	public PurResult queryfaultHanding(HttpServletRequest request) {
		try {
			return sysAgreementService.queryfaultHanding(request);
		}catch (Exception e) {
			e.printStackTrace();
			PurResult result = new PurResult(0,"操作失败！");
			return result;
		}
	}
	/**
	  *新增故障处理
	 * @param request
	 * @return
	 */
	@RequestMapping("addFaultHanding.do")
	@ResponseBody
	public PurResult addFaultHanding(HttpServletRequest request){
		PurResult rs = new PurResult();
		try {
			Map<String, Object> params = ServletsUtil.getParameters(request);
	
			rs=sysAgreementService.addFaultHanding(params);
		} catch (Exception e) {
			rs.setStatus(0);
			rs.setMsg("操作失败!");
			e.printStackTrace();
		}		
		return rs;
	}
	/**
	  *新增故障处理
	 * @param request
	 * @return
	 */
	@RequestMapping("addFaultHandingImage.do")
	@ResponseBody
	public PurResult addFarmInformationImage(MultipartFile file,HttpServletRequest request){
		PurResult rs = new PurResult(0,"操作失败!");
		
		try {
			String host="http://file.buyhoo.cc";
			boolean flag=false;
			if(null!=file){
				//存到文件服务器-start
				SFTPUtil sftp = new SFTPUtil(FTPConfig.username, FTPConfig.password, FTPConfig.host, FTPConfig.port);   
		        sftp.login();
				InputStream is;
				
				is = file.getInputStream();
				String orName=file.getOriginalFilename();
				String lastName=orName.substring(orName.lastIndexOf("."));//".jpg"
				String ngoods=UUID.randomUUID().toString()+lastName;
				flag=sftp.upload(FTPConfig.goods_path+"/faultHanding", ngoods, is);   
				rs.setData(host+File.separator + "image" + File.separator + "faultHanding"+ File.separator+ngoods);
				if(flag)
				{
					rs.setMsg(ngoods);
					rs.setStatus(1);
				}
				
			}
			
		} catch (Exception e) {
			e.printStackTrace();
		}		
		return rs;
	}
}
