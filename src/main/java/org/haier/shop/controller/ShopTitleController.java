package org.haier.shop.controller;

import org.haier.shop.service.ShopTitleService;
import org.haier.shop.util.ShopsResult;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;

@Controller
@RequestMapping("/shopTitle")
public class ShopTitleController {

    @Autowired
    private ShopTitleService shopTitleService;

    @RequestMapping("/toShopTitlePage.do")
    public String toShopTitlePage() {
        return "/WEB-INF/sys/shopTitle/shopTitlePage.jsp";
    }

    /**
     * 查询店铺的标题信息
     *
     * @return
     */
    @RequestMapping("/queryShopTitleList.do")
    @ResponseBody
    public ShopsResult queryShopTitleList(Integer page, Integer limit, Long shopUnique, Integer validType,String titleName) {
        return shopTitleService.queryShopTitleList(page, limit, shopUnique, validType,titleName);
    }

    @RequestMapping("/toAddNewShopTitlePage.do")
    public String addNewShopTitle() {
        return "/WEB-INF/sys/shopTitle/addShopTitlePage.jsp";
    }

    @RequestMapping("/modifyShopTitlePage.do")
    public String modifyShopTitlePage() {
        return "/WEB-INF/sys/shopTitle/modifyShopTitlePage.jsp";
    }

    @RequestMapping("/authorizeShopTitlePage.do")
    public String authorizeShopTitlePage() {
        return "/WEB-INF/sys/shopTitle/authorizeShopTitlePage.jsp";
    }

    @RequestMapping(method = RequestMethod.POST, value = "/addShopTitle.do")
    @ResponseBody
    public ShopsResult addShopTitle(String titleName, MultipartFile file, Integer modularType, HttpServletRequest request) {
        return shopTitleService.addShopTitle(titleName, file, modularType, request);
    }

    @RequestMapping("/queryShopTitleInfo.do")
    @ResponseBody
    public ShopsResult queryShopTitleInfo(Long id, String shopMessage, Integer page, Integer limit) {
        return shopTitleService.queryShopTitleInfo(id, shopMessage, page, limit);
    }

    @RequestMapping(method = RequestMethod.POST, value = "/deleteShopTitle.do")
    @ResponseBody
    public ShopsResult deleteShopTitle(Long id) {
        return shopTitleService.deleteShopTitle(id);
    }

    @RequestMapping(method = RequestMethod.POST, value = "/authorizeShopTitle.do")
    @ResponseBody
    public ShopsResult authorizeShopTitle(@RequestParam Long id, @RequestParam String shopTitleStr) {
        return shopTitleService.authorizeShopTitle(id, shopTitleStr);
    }

    @RequestMapping(method = RequestMethod.POST, value = "/modifyShopTitle.do")
    @ResponseBody
    public ShopsResult modifyShopTitle(Long id, String titleName, MultipartFile file, Integer modularType, HttpServletRequest request) {
        return shopTitleService.modifyShopTitle(id, titleName, file, modularType, request);
    }

}