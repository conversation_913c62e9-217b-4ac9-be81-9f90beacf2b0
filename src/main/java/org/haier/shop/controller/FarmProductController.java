package org.haier.shop.controller;

import java.io.IOException;
import java.io.OutputStream;
import java.net.URLEncoder;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.HttpSession;

import org.haier.shop.entity.globalSelect.GlobalDetailVO;
import org.haier.shop.service.FarmProductService;
import org.haier.shop.util.HandleMessyCode;
import org.haier.shop.util.LoadOutObjectXLSUtil;
import org.haier.shop.util.PurResult;
import org.haier.shop.util.ServletsUtil;
import org.haier.shop.util.ShopsResult;
import org.haier.shop.util.StringUtil;
import org.haier.shop.util.XLSCallBack;
import org.haier.shop.util.excel.inpl.ExcelReader;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.multipart.MultipartFile;

import com.alibaba.fastjson.JSONObject;


@Controller
@RequestMapping("/farmProduct")
public class FarmProductController {

	@Autowired
	private FarmProductService farmService;
	
	
	//跳转到-供货商全球精选页面
	@RequestMapping("/toSupplierGlobal.do")
	public String toSupplierGlobal(){
		return "/WEB-INF/global/supplierGlobalList.jsp";
	}
	
	//中心站-农产品审核
	@RequestMapping("/farmProductAudit.do")
	public String farmProductAudit(){
		return "/WEB-INF/yinong/farmProductAudit.jsp";
	}
	
	//跳转到-农产品上架
	@RequestMapping("/toFarmProductShelf.do")
	public String toFarmProductShelf(){
		return "/WEB-INF/yinong/farmProductShelf.jsp";
	}
	//跳转到-全球精选-第一书记页面
	@RequestMapping("/toGlobalSecretary.do")
	public String toGlobalSecretary(){
		return "/WEB-INF/global/globalSecretary.jsp";
	}	
	//跳转到-全球精选-第一书记页面-新增界面
	@RequestMapping("/addSeretaryPage.do")
	public String addSeretaryPage(){
		return "/WEB-INF/global/addSeretary.jsp";
	}
	//跳转到-全球精选-第一书记页面-编辑
	@RequestMapping("/editSecretaryPage.do")
	
	
	public String editSecretaryPage(Model model,String id){
		
		Map<String ,Object> data = farmService.querySecretary(id); 
		model.addAttribute("id", id);
		model.addAttribute("data", data);
		return "/WEB-INF/global/editSecretary.jsp";
	}
	/**
	 * 跳转到驳回页面
	 */
	@RequestMapping("/toAuditNo.do")
	public String toAuditNo(){
		return "/WEB-INF/yinong/auditNo.jsp";
	}
	
	//跳转到上架页面
	@RequestMapping("/toGoodShelf.do")
	public String toGoodShelf(Model model,String farm_products_id){
		Map<String,Object> map=new HashMap<String, Object>();
		map.put("farm_products_id", farm_products_id);
		model.addAttribute("good_spec_list", farmService.getFarmProductSpecList(map));
		model.addAttribute("farm_products_id", farm_products_id);
		return "/WEB-INF/yinong/upperShelf.jsp";
	}
	
	@RequestMapping("/shelfDetail.do")
	public String shelfDetail(Model model,String shelf_id){
		Map<String,Object> map=new HashMap<String, Object>();
		map.put("shelf_id", shelf_id);
		model.addAttribute("good_spec_list", farmService.getFarmProductShelfDetail(map));
		return "/WEB-INF/yinong/shelfDetail.jsp";
	}
	
	//跳转到-全球精选订单列表页面
	@RequestMapping("/toFarmOrderList.do")
	public String toFarmOrderList(){
		return "/WEB-INF/yinong/orderList.jsp";
	}
	//跳转到-全球精选订单列表页面
	@RequestMapping("/toGlobalOrderPtList.do")
	public String toGlobalOrderPtList(){
		return "/WEB-INF/global/ptOrderList.jsp";
	}
	
	//跳转到-订单详情
	@RequestMapping("/toOrderDetail.do")
	public String toOrderDetail(Model model,String global_sub_id){
		model.addAttribute("global_sub_id", global_sub_id);
		return "/WEB-INF/yinong/orderDetail.jsp";
	}

	/**
	 * 
	 */
	@RequestMapping("/toExpress.do")
	public String toExpress(){
		return "/WEB-INF/yinong/toExpress.jsp";
	}
	
	/**
	 * 农产品审核列表
	 */
	@RequestMapping("/queryFarmProductAuditList.do")
	@ResponseBody
	public PurResult queryFarmProductAuditList(
			@RequestParam(value="content",defaultValue="")String content,
			@RequestParam(value="audit_status",defaultValue="")String audit_status,
			@RequestParam(value = "page", defaultValue = "1") int page,
			@RequestParam(value = "limit", defaultValue = "15") int pageSize,String area_dict_num
			){
		Map<String,Object> map=new HashMap<String, Object>();
		map.put("pageSize", pageSize);
		map.put("startNum", (page-1)*pageSize);
		map.put("content", content);
		map.put("audit_status", audit_status);
		map.put("area_dict_num", area_dict_num);
		return farmService.queryFarmProductAuditList(map);
	}
	
	/**
	 * 全球精选-订单页面
	 */
	@RequestMapping("/queryFarmOrderList.do")
	@ResponseBody
	public PurResult queryFarmOrderList(
			@RequestParam(value="content",defaultValue="")String content,
			@RequestParam(value="startTime",defaultValue="")String startTime,
			@RequestParam(value="endTime",defaultValue="")String endTime,
			@RequestParam(value="handle_status",defaultValue="")String handle_status,
			@RequestParam(value="shop_unique",defaultValue="")Long shop_unique,
			@RequestParam(value = "page", defaultValue = "1") int page,
			@RequestParam(value = "limit", defaultValue = "15") int pageSize
			){
		Map<String,Object> map=new HashMap<String, Object>();
		map.put("shop_unique", shop_unique);
		map.put("pageSize", pageSize);
		map.put("startNum", (page-1)*pageSize);
		map.put("startTime", startTime);
		map.put("endTime", endTime);
		map.put("content", content);
		map.put("handle_status", handle_status);
		return farmService.queryFarmOrderList(map);
	}
	
	

	

	
	/**
	 * 删除
	 */
	@RequestMapping("/deleteFarmProductShelf.do")
	@ResponseBody
	public PurResult deleteFarmProductShelf(HttpServletRequest request){
		PurResult rs = new PurResult();
		try {
			Map<String, Object> params = ServletsUtil.getParameters(request);			
			rs=farmService.deleteFarmProductShelf(params);
		} catch (Exception e) {
			rs.setStatus(0);
			rs.setMsg("操作失败!");
			e.printStackTrace();
		}		
		return rs;
	}
	
	/**
	 * 主题审核
	 */
	@RequestMapping("/auditFarmProduct.do")
	@ResponseBody
	public PurResult auditFarmProduct(HttpServletRequest request){
		PurResult rs = new PurResult();
		try {
			Map<String, Object> params = ServletsUtil.getParameters(request);
			rs=farmService.auditFarmProduct(params);
		} catch (Exception e) {
			rs.setStatus(0);
			rs.setMsg("操作失败!");
			e.printStackTrace();
		}		
		return rs;
	}
	
	@RequestMapping("/auditFarmProductNo.do")
	@ResponseBody
	public PurResult auditFarmProductNo(HttpServletRequest request){
		PurResult rs = new PurResult();
		try {
			Map<String, Object> params = ServletsUtil.getParameters(request);
			rs=farmService.auditFarmProductNo(params);
		} catch (Exception e) {
			rs.setStatus(0);
			rs.setMsg("操作失败!");
			e.printStackTrace();
		}		
		return rs;
	}
	
	/**
	 * 农产品上架
	 */
	@RequestMapping("/upperShelfProduct.do")
	@ResponseBody
	public PurResult upperShelfGlobal(HttpServletRequest request){
		PurResult rs = new PurResult();
		try {
			Map<String, Object> params = ServletsUtil.getParameters(request);
			rs=farmService.upperShelfProduct(params);
		} catch (Exception e) {
			rs.setStatus(0);
			rs.setMsg("操作失败!");
			e.printStackTrace();
		}		
		return rs;
	}
	
	/**
	 * 下架
	 */
	@RequestMapping("/updateGoodShelfStatus.do")
	@ResponseBody
	public PurResult updateGoodShelfStatus(HttpServletRequest request){
		PurResult rs = new PurResult();
		try {
			Map<String, Object> params = ServletsUtil.getParameters(request);
			rs=farmService.updateGoodShelfStatus(params);
		} catch (Exception e) {
			rs.setStatus(0);
			rs.setMsg("操作失败!");
			e.printStackTrace();
		}		
		return rs;
	}
	
	/**
	 * 全球精选商品上下架
	 */
	@RequestMapping("/queryFarmProductShelfList.do")
	@ResponseBody
	public PurResult queryFarmProductShelfList(
			@RequestParam(value="content",defaultValue="")String content,
			@RequestParam(value="shelf_status",defaultValue="")String shelf_status,
			@RequestParam(value="shop_unique",defaultValue="")Long shop_unique,
			@RequestParam(value="area_dict_num",defaultValue="")String area_dict_num,
			@RequestParam(value = "page", defaultValue = "1") int page,
			@RequestParam(value = "limit", defaultValue = "15") int pageSize
			){
		Map<String,Object> map=new HashMap<String, Object>();
		map.put("shop_unique", shop_unique);
		map.put("area_dict_num", area_dict_num);
		map.put("pageSize", pageSize);
		map.put("startNum", (page-1)*pageSize);
		map.put("content", content);
		map.put("shelf_status", shelf_status);
		return farmService.queryFarmProductShelfList(map);
	}

	
	/**
	 * 跳转到全球精选详情界面
	 */
	@RequestMapping("/toGlobalThemeDetail.do")
	public String toGlobalThemeDetail(Model model,String id){
		Map<String,Object> params=new HashMap<String,Object>();
    	params.put("id", id);
    	//获取有商家的区域列表
    	List<Map<String ,Object>> areaList = farmService.getAreaList(params); 
    	//获取有商家的城市列表
    	List<Map<String ,Object>> cityList = farmService.getCityList(areaList);
    	//获取有商家的省份列表
    	List<Map<String ,Object>> provinceList = farmService.getProvinceList(cityList); 
    	
    	for(Map<String ,Object> city:cityList)
    	{
    		int num=0;
    		for(Map<String ,Object> area:areaList)
        	{
    			if(area.get("city_code").equals(city.get("city_code")))
    			{
    				
    				if(area.get("flag").equals("false"))
    				{
    					num=num+1;
    				}
    			}
        		
        	}
    		if(num==0)
    		{
    			city.put("flag", "true");
    		}else
    		{
    			city.put("flag", "false");
    		}
    		
    	}
    	
    	
    	GlobalDetailVO data=farmService.queryGlobalThemeDetail(params);
    	model.addAttribute("areaList", areaList);
    	model.addAttribute("cityList", cityList);
    	model.addAttribute("provinceList", provinceList);
    	model.addAttribute("id", id);
    	model.addAttribute("data", data);
    	
		return "/WEB-INF/global/globalSelectDetail.jsp";
	}
	/**
	 * 全球精选详情
	 */
	@RequestMapping("/queryGlobalThemeDetail.do")
	@ResponseBody
	public PurResult queryGlobalThemeDetail(HttpServletRequest request)
	{
		PurResult rs=new PurResult();
		try {
			Map<String,Object> params=ServletsUtil.getParameters(request);
			
			GlobalDetailVO data=farmService.queryGlobalThemeDetail(params);
			rs.setData(data);
			rs.setStatus(1);
		}catch(Exception e)
		{
			rs.setStatus(0);
			rs.setMsg("操作失败！");
			e.printStackTrace();
		}
		return rs;
	}
	
	/**
	 * 发货
	 */
	@RequestMapping("/editExpressInfo.do")
	@ResponseBody
	public PurResult editExpressInfo(HttpServletRequest request){
		PurResult rs = new PurResult();
		try {
			Map<String, Object> params = ServletsUtil.getParameters(request);
			rs=farmService.editExpressInfo(params);
		} catch (Exception e) {
			rs.setStatus(0);
			rs.setMsg("操作失败!");
			e.printStackTrace();
		}		
		return rs;
	}
	
	@RequestMapping("/getOrderDetail.do")
	@ResponseBody
	public PurResult getOrderDetail(@RequestParam(value="global_sub_id",defaultValue="")String global_sub_id){
		Map<String,Object> map=new HashMap<String, Object>();
		map.put("global_sub_id", global_sub_id);
		return farmService.getOrderDetail(map);
	}
	/**
	  * 总平图全球精选-商品商品上下架详情
	 * @param request
	 * @return
	 */
	@RequestMapping("/queryPTGGShelfList.do")
	@ResponseBody
	public PurResult queryPTGGShelfList(HttpServletRequest request,@RequestParam(value = "page", defaultValue = "1") int page,
			@RequestParam(value = "limit", defaultValue = "15") int pageSize){
		PurResult rs = new PurResult();
		try {
			
			Map<String, Object> params = ServletsUtil.getParameters(request);
			params.put("pageSize", pageSize);
			params.put("startNum", (page-1)*pageSize);
			rs=farmService.queryPTGGShelfList(params);
		} catch (Exception e) {
			rs.setStatus(0);
			rs.setMsg("操作失败!");
			e.printStackTrace();
		}		
		return rs;
	}
	/**
	 * 平台上架
	 */
	@RequestMapping("/upperShelfGlobal2.do")
	@ResponseBody
	public PurResult upperShelfGlobal2(HttpServletRequest request){
		PurResult rs = new PurResult();
		try {
			Map<String, Object> params = ServletsUtil.getParameters(request);
			rs=farmService.upperShelfGlobal2(params);
		} catch (Exception e) {
			rs.setStatus(0);
			rs.setMsg("操作失败!");
			e.printStackTrace();
		}		
		return rs;
	}
	/**
	 * 平台批量上下架
	 */
	@RequestMapping("/updateGoodShelf.do")
	@ResponseBody
	public PurResult updateGoodShelf(HttpServletRequest request){
		PurResult rs = new PurResult();
		try {
			Map<String, Object> params = ServletsUtil.getParameters(request);
			rs=farmService.updateGoodShelf(params);
		} catch (Exception e) {
			rs.setStatus(0);
			rs.setMsg("操作失败!");
			e.printStackTrace();
		}		
		return rs;
	}
	
	/**
	 * 全球精选-订单页面
	 */
	@RequestMapping("/queryGlobalSecretaryList.do")
	@ResponseBody
	public PurResult queryGlobalSecretaryList(
			@RequestParam(value="shop_unique",defaultValue="")Long shop_unique,
			@RequestParam(value="search_message",defaultValue="")String search_message,
			@RequestParam(value = "page", defaultValue = "1") int page,
			@RequestParam(value = "limit", defaultValue = "15") int pageSize
			){
		Map<String,Object> map=new HashMap<String, Object>();
		map.put("shop_unique", shop_unique);
		map.put("pageSize", pageSize);
		map.put("search_message", search_message);
		map.put("startNum", (page-1)*pageSize);
		return farmService.queryGlobalSecretaryList(map);
	}
	/**
	  * 新增第一书记
	 */
	@RequestMapping("/addSecretary.do")
	@ResponseBody
	public PurResult addSecretary(HttpServletRequest request){
		PurResult rs = new PurResult();
		try {
			Map<String, Object> params = ServletsUtil.getParameters(request);
			rs=farmService.addSecretary(params);
		} catch (Exception e) {
			rs.setStatus(0);
			rs.setMsg("操作失败!");
			e.printStackTrace();
		}		
		return rs;
	}
	/**
	 *   删除
	 */
	@RequestMapping("/deleteGlobalSecretary.do")
	@ResponseBody
	public PurResult deleteGlobalSecretary(HttpServletRequest request){
		PurResult rs = new PurResult();
		try {
			Map<String, Object> params = ServletsUtil.getParameters(request);
			rs=farmService.deleteGlobalSecretary(params);
		} catch (Exception e) {
			rs.setStatus(0);
			rs.setMsg("操作失败!");
			e.printStackTrace();
		}		
		return rs;
	}
	/**
	 * 修改
	 */
	@RequestMapping("/updateSecretary.do")
	@ResponseBody
	public PurResult updateSecretary(HttpServletRequest request){
		PurResult rs = new PurResult();
		try {
			Map<String, Object> params = ServletsUtil.getParameters(request);
			rs=farmService.updateSecretary(params);
		} catch (Exception e) {
			rs.setStatus(0);
			rs.setMsg("操作失败!");
			e.printStackTrace();
		}		
		return rs;
	}
	/**
	  * 地区查询
	 * @return
	 */
	@RequestMapping("/queryGoodsKinds.do")
	@ResponseBody
	public ShopsResult queryGoodsKinds(String shop_unique,String area_dict_parent_num){
		return farmService.queryGoodsKinds(shop_unique, area_dict_parent_num);
	}
	
	/**
	 * 待发货订单-导出excel
	 */
	@RequestMapping("/exportOrderExcel.do")
	public void exportOrderExcel(HttpServletRequest request, HttpServletResponse response) throws Exception{
		Map<String, Object> params = HandleMessyCode.handle(ServletsUtil.getParameters(request));
		List<Map<String,Object>> list=farmService.queryFarmOrderExcel(params);	
		String[] titles=new String[] {
				"订单id","订单编号","收货人","收货地址","联系电话","备注","商品名称","商品规格","数量","配送方式","快递单号","快递公司","代卖店铺","实付金额","店铺利润"
		};
		loadOrderXLS(list,"待发货订单",request,response,titles);
	}
	
	public void loadOrderXLS(List<Map<String, Object>> caseVoList,final String str,HttpServletRequest request,HttpServletResponse response,final String[] titles){
		try {
			response.setContentType(
					"application/x-msdownload; charset=UTF-8");// 告知浏览器文件类型
			String agent = request.getHeader("user-agent");
			if (agent.contains("Firefox")) {
				response.setHeader(
						"Content-Disposition",
						"attachment;filename="
								+ new String((str + ".xls")
										.getBytes("GB2312"), "ISO-8859-1"));// 设置文件头
			} else {
				response.setHeader(
						"Content-Disposition",
						"attachment;filename="
								+ URLEncoder.encode(str + ".xls", "UTF-8"));// 设置文件头
			}
			OutputStream os = response.getOutputStream();
			LoadOutObjectXLSUtil<Map<String, Object>> objectXLS = new LoadOutObjectXLSUtil<Map<String, Object>>();
			objectXLS.generateXLS(os, caseVoList, new XLSCallBack<Map<String, Object>>() {
				public Object[] getValues(Map<String, Object> tt) {
					String str = "";
					
					String sup_order_unique = tt.get("sup_order_unique")+"";
					if (StringUtil.blank(sup_order_unique)) {
						sup_order_unique = str;
					}
					String global_sub_id = tt.get("global_sub_id")+"";
					if (StringUtil.blank(global_sub_id)) {
						global_sub_id = str;
					}
					String addressee =  tt.get("addressee")+"";
					if (StringUtil.blank(addressee)) {
						addressee = str;
					}
					String address =tt.get("address")+"";
					if (StringUtil.blank(address)) {
						address = str;
					}
					String phone = tt.get("phone")+"";
					if (StringUtil.blank(phone)) {
						phone = str;
					}
					String sale_list_remark = tt.get("sale_list_remark")+"";
					if (StringUtil.blank(sale_list_remark)) {
						sale_list_remark = str;
					}
					String goods_name = tt.get("goods_name")+"";
					if (StringUtil.blank(goods_name)) {
						goods_name = str;
					}
					String spec_name = tt.get("spec_name")+"";
					if (StringUtil.blank(spec_name)) {
						spec_name = str;
					}					
					String detail_count = tt.get("detail_count")+"";
					if (StringUtil.blank(detail_count)) {
						detail_count = str;
					}
					String express_type = tt.get("express_type")+"";
					if (StringUtil.blank(express_type)) {
						express_type = str;
					}
					String logistics_unique = tt.get("logistics_unique")+"";
					if (StringUtil.blank(logistics_unique)) {
						logistics_unique = str;
					}
					String compay_name = tt.get("compay_name")+"";
					if (StringUtil.blank(compay_name)) {
						compay_name = str;
					}
					String shop_name = tt.get("shop_name")+"";
					if (StringUtil.blank(shop_name)) {
						shop_name = str;
					}
					String actual_amt = tt.get("actual_amt")+"";
					if (StringUtil.blank(actual_amt)) {
						actual_amt = str;
					}
					String profit_amt = tt.get("profit_amt")+"";
					if (StringUtil.blank(profit_amt)) {
						profit_amt = str;
					}
					return new String[] {global_sub_id,sup_order_unique,addressee,address,phone,sale_list_remark,goods_name,
							spec_name,detail_count,express_type,logistics_unique,compay_name,shop_name,actual_amt,profit_amt
					};
				}

				public String getTitle() {
					return str;
				}

				public int[] getColumnsWidth() {
					return new int[] { 
										  80 * 80, 80 * 80,80 * 80,80 * 80,
										  80 * 80, 80 * 80,80 * 80,80 * 80,80 * 80,
										  80 * 80,80 * 80,80 * 80,80 * 80,80 * 80,80 * 80};
				}

				public String[] getColumnsName() {
					return titles;
				}

				public int[][] MergedRegion() {
					// TODO Auto-generated method stub
					return null;
				}

				public List<Object[]> getheaderValue() {
					// TODO Auto-generated method stub
					return null;
				}

			});
			os.flush();
		} catch (IOException e) {
			e.printStackTrace();
		}
	}
	
	/**
	 * 益农订单导入-快递批量发货
	 */
	@RequestMapping("/importOrderExcel.do")
	@ResponseBody
	public PurResult importOrderExcel(HttpServletRequest request,HttpSession session, HttpServletResponse response,@RequestParam MultipartFile file) {
		PurResult rs = new PurResult();
		try {
			ExcelReader reader = new ExcelReader(file.getInputStream());
			List<String[]> list=reader.getAllData(0);
			//检验导入的数据是否合法
			if(checkOrderExcel(list,rs)) {
				//数据保存到数据库
				farmService.batchUpdateFarmLogistics(list);
				rs.setStatus(1);
				rs.setMsg("导入成功");
			}
			
		} catch (IOException e) {
			rs.setStatus(0);
			rs.setMsg("导入失败!");
			e.printStackTrace();
		}				
        return rs;
    }
	
	public boolean checkOrderExcel(List<String[]> list,PurResult result) throws IOException{
		//校验是否是模板文件
		if(!"订单id".equals(list.get(0)[0].toString().trim())||
				!"订单编号".equals(list.get(0)[1].toString().trim())||
				!"收货人".equals(list.get(0)[2].toString().trim())||
				!"收货地址".equals(list.get(0)[3].toString().trim())||
				!"联系电话".equals(list.get(0)[4].toString().trim())||
				!"备注".equals(list.get(0)[5].toString().trim())||
				!"商品名称".equals(list.get(0)[6].toString().trim())||
				!"商品规格".equals(list.get(0)[7].toString().trim())||
				!"数量".equals(list.get(0)[8].toString().trim())||
				!"配送方式".equals(list.get(0)[9].toString().trim())||
				!"快递单号".equals(list.get(0)[10].toString().trim())||
				!"快递公司".equals(list.get(0)[11].toString().trim())||
				!"代卖店铺".equals(list.get(0)[12].toString().trim())||
				!"实付金额".equals(list.get(0)[13].toString().trim())||
				!"店铺利润".equals(list.get(0)[14].toString().trim())){
			result.setStatus(0);
			result.setMsg("请您按照模板规范导入文件！");
			return false;
		}
		int num=0;
		for(int i=1;i<list.size();i++){
			String []tem=list.get(i);
			for(int j=0;j<14;j++){
				if(j==0||j==10||j==11){
					if(tem[j]==null||tem[j]==""){
						num++;
					}
				}				
			}
		}
		if(num>0){
			result.setStatus(0);
			result.setMsg("你导入的excle文件,订单id、快递单号、快递公司存在空数据,请您检查后重新导入！");
			return false;
		}			
		result.setCount(list.size()-1);;
		return true;
	}
}
