package org.haier.shop.controller.validate;

import org.haier.shop.params.restockPlan.*;
import org.haier.shop.service.supplier.ShopsRestockPlanService;
import org.haier.shop.util.ShopsResult;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.annotation.Resource;
/**
 * 补货单
 * <AUTHOR>
 * @Date 2023/9/11 10:03
 **/
@Controller
@RequestMapping("/html/restockPlan")
public class ShopsRestockPlanController {

	@Resource
	private ShopsRestockPlanService srpService;

	/**
	 * 添加补货计划
	 * @param params
	 * @return
	 */
	@RequestMapping("/addRestockPlan.do")
	@ResponseBody
	public ShopsResult addRestockPlan(@RequestBody AddRestockPlanParams params){
		return srpService.addRestockPlan(params);
	}

	/**
	 * 删除补货计划
	 * @param params
	 * @return
	 */
	@RequestMapping("/deleteRestockPlan.do")
	@ResponseBody
	public ShopsResult deleteRestockPlan(@RequestBody DeleteRestockPlanParams params){
		return srpService.deleteRestockPlan(params);
	}

	/**
	 * 查询补货计划列表信息
	 * @param params
	 * @return
	 */
	@RequestMapping("/queryRestockPlanList.do")
	@ResponseBody
	public ShopsResult queryRestockPlanList(@RequestBody RestockPlanListParams params){
		return srpService.queryRestockPlanList(params);
	}

	/**
	 * 根据补货计划ID查询商品详细信息
	 * @param params
	 * @return
	 */
	@RequestMapping("/queryGoodsListByPlanId.do")
	@ResponseBody
	public ShopsResult queryGoodsListByPlanId(@RequestBody RestockPlanGoodsParams params){
		return srpService.queryGoodsListByPlanId(params);
	}

	/**
	 * 补货计划中添加商品
	 * @param params
	 * @return
	 */
	@RequestMapping( method = RequestMethod.POST,value = "/addRestockPlanGoods.do")
	@ResponseBody
	public ShopsResult addRestockPlanGoods(@RequestBody RestockPlanGoodsAddParams params){
		return srpService.addRestockPlanGoods(params);
	}

	/**
	 * 更新补货计划状态
	 * @param params
	 * @return
	 */
	@RequestMapping("/updatePlanStatus.do")
	@ResponseBody
	public ShopsResult updatePlanStatus(@RequestBody UpdateStatusParams params){
		return srpService.updatePlanStatus(params);
	}

	/**
	 * 查询某补货计划下供货商信息分组信息
	 * @param params
	 * @return
	 */
	@RequestMapping("/getPresentListByPlanId.do")
	@ResponseBody
	public ShopsResult getPresentListByPlanId(@RequestBody QueryGoodsListGroupBySupplierParams params){
		return srpService.getPresentListByPlanId(params);
	}

	/**
	 * 更换供货商
	 * @param params
	 * @return
	 */
	@RequestMapping("/updateSupplier.do")
	@ResponseBody
	public ShopsResult updateSupplier(@RequestBody RestockPlanGoodsUpdateSupplierParams params){
		return srpService.updateSupplier(params);
	}

	/**
	 * 查询店铺绑定的供货商信息
	 * @param params
	 * @return
	 */
	@RequestMapping("/getGoodsSupplierMsg.do")
	@ResponseBody
	public ShopsResult getGoodsSupplierMsg(@RequestBody GetGoodsSupplierMsgParams params) {
		return srpService.getGoodsSupplierMsg(params);
	}

	/**
	 * 修改或删除补货计划下商品信息
	 * @param params
	 * @return
	 */
	@RequestMapping("/modifyRestockPlanGoods.do")
	@ResponseBody
	public ShopsResult modifyRestockPlanGoods(@RequestBody ModifyGoodsParams params){
		return srpService.modifyRestockPlanGoods(params);
	}

	/**
	 * 根据供货商ID获取商品分组信息
	 * @param params
	 * @return
	 */
	@RequestMapping("/getGoodsListBySupplierId.do")
	@ResponseBody
	public ShopsResult queryGoodsListBySupplierId(@RequestBody QueryGoodsBySupplierParams params){
		return srpService.queryGoodsListBySupplierId(params);
	}

	/**
	 * 查询商品详细信息
	 * @param params
	 * @return
	 */
	@RequestMapping("/queryGoodsDetail.do")
	@ResponseBody
	public ShopsResult queryGoodsDetail(@RequestBody QueryGoodsDetailParams params){
		return srpService.queryGoodsDetail(params);
	}

	/**
	 * 修改补货计划下供货商备注信息
	 * @param params
	 * @return
	 */
	@RequestMapping("/updateRestockPlanSupplier.do")
	@ResponseBody
	public ShopsResult updateRestockPlanSupplier(@RequestBody RestockPlanSupplierUpdateParams params){
		return srpService.updateRestockPlanSupplier(params);
	}

	/**
	 * 再次补货
	 * @param params
	 * @return
	 */
	@RequestMapping("/restockAgain.do")
	@ResponseBody
	public ShopsResult restockAgain(@RequestBody DeleteRestockPlanParams params){
		return srpService.restockAgain(params);
	}
}
