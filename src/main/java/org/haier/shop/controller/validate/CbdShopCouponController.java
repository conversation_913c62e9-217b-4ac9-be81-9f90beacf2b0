package org.haier.shop.controller.validate;

import org.haier.shop.params.cbd.CbdShopCouponListParams;
import org.haier.shop.service.cbdShopCoupon.CbdShopCouponService;
import org.haier.shop.util.ShopsResult;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;

/**
 * 商圈消费券
 *
 * <AUTHOR>
 * @Date 2023年9月16日10:14:06
 **/
@Controller
@RequestMapping("/html/cbd")
public class CbdShopCouponController {
    @Autowired
    private CbdShopCouponService cbdShopCouponService;

    /**
     * 查询商圈消费券核销列表
     *
     * @param params
     * @return
     */
    @RequestMapping(method = RequestMethod.POST, value = "/queryCbdShopCouponListWithPage.do")
    @ResponseBody
    public ShopsResult queryCouponRecordListWithPage(CbdShopCouponListParams params) {
        return cbdShopCouponService.queryCouponRecordListWithPage(params);
    }

}
