package org.haier.shop.controller.validate;

import org.haier.shop.params.shopSupplier.*;
import org.haier.shop.service.supplier.ShopSupplierService;
import org.haier.shop.util.ShopsResult;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;

/**
 * 供应商
 *
 * <AUTHOR>
 * @Date 2023年9月16日10:14:06
 **/
@Controller
@RequestMapping("/html/shopSupplier")
public class ShopSupplierController {
    @Autowired
    private ShopSupplierService shopSupService;

    /**
     * 添加供应商分类信息
     *
     * @param params
     * @return
     */
    @RequestMapping(method = RequestMethod.POST, value = "/addSupKind.do")
    @ResponseBody
    public ShopsResult addSupKind(@RequestBody SupKindAddParams params) {
        return shopSupService.addSupKind(params);
    }

    /**
     * 修改或删除供应商分类信息
     *
     * @param params
     * @return
     */
    @RequestMapping(method = RequestMethod.POST, value = "/modifySupKind.do")
    @ResponseBody
    public ShopsResult modifySupKind(@RequestBody SupKindModifyParams params) {
        return shopSupService.modifySupKind(params);
    }

    /**
     * 更新供货商分类排序
     * @param params
     * @return
     */
    @RequestMapping(method = RequestMethod.POST, value = "/updateSupKindSort.do")
    @ResponseBody
    public ShopsResult updateSupKindSort(@RequestBody SupKindSortUpdateParams params){
        return shopSupService.updateSupKindSort(params);
    }

    /**
     * 查询供货商分类信息列表
     *
     * @param params
     * @return
     */
    @RequestMapping(method = RequestMethod.POST, value = "/querySupKindList.do")
    @ResponseBody
    public ShopsResult querySupKindList(@RequestBody ShopUniqueParams params) {
        return shopSupService.querySupKindList(params);
    }

    /**
     * 添加供应商信息
     *
     * @param params
     * @return
     */
    @RequestMapping(method = RequestMethod.POST, value = "/addsupInfo.do")
    @ResponseBody
    public ShopsResult addSupInfo(@RequestBody SupInfoAddParams params) {
        return shopSupService.addSupInfo(params);
    }

    /**
     * 修改供应商信息
     *
     * @param params
     * @return
     */
    @RequestMapping(method = RequestMethod.POST, value = "/updateSupInfo.do")
    @ResponseBody
    public ShopsResult updateSupInfo(@RequestBody SupInfoUpdateParams params) {
        return shopSupService.updateSupInfo(params);
    }

    /**
     * 删除供应商信息
     *
     * @param params
     * @return
     */
    @RequestMapping(method = RequestMethod.POST, value = "/deleteSupInfo.do")
    @ResponseBody
    public ShopsResult deleteSupInfo(@RequestBody SupplierIdParams params) {
        return shopSupService.deleteSupInfo(params);
    }

    /**
     * 查询供应商列表信息
     *
     * @param params
     * @return
     */
    @RequestMapping(method = RequestMethod.POST, value = "/querySupList.do")
    @ResponseBody
    public ShopsResult querySupList(@RequestBody QuerySupListParams params) {
        return shopSupService.querySupList(params);
    }

    /**
     * 查询供应商详细信息-业务信息
     *
     * @param params
     * @return
     */
    @RequestMapping(method = RequestMethod.POST, value = "/querySupBusinessInfo.do")
    @ResponseBody
    public ShopsResult querySupBusinessInfo(@RequestBody SupplierUniqueParams params) {
        return shopSupService.querySupBusinessInfo(params);
    }

    /**
     * 查询供应商详细信息-购销订单信息
     *
     * @param params
     * @return
     */
    @RequestMapping(method = RequestMethod.POST, value = "/querySupBillInfo.do")
    @ResponseBody
    public ShopsResult querySupBillInfo(@RequestBody QueryBillListParams params) {
        return shopSupService.querySupBillInfo(params);
    }

    /**
     * 查询供应商详细信息-付款记录信息
     *
     * @param params
     * @return
     */
    @RequestMapping(method = RequestMethod.POST, value = "/querySupPaymentInfo.do")
    @ResponseBody
    public ShopsResult querySupPaymentInfo(@RequestBody SupplierUniqueParams params) {
        return shopSupService.querySupPaymentInfo(params);
    }

    /**
     * 查询供应商给店铺所供商品列表（包括未建档和已建档）
     *
     * @param params
     * @return
     */
    @RequestMapping(method = RequestMethod.POST, value = "/querySupRecordGoodList.do")
    @ResponseBody
    public ShopsResult querySupRecordGoodList(@RequestBody QueryRecordGoodsListParams params) {
        return shopSupService.querySupRecordGoodList(params);
    }

    /**
     * 查询店铺与供应商还款信息
     *
     * @param params
     * @return
     */
    @RequestMapping(method = RequestMethod.POST, value = "/queryQepaymentInfo.do")
    @ResponseBody
    public ShopsResult queryQepaymentInfo(@RequestBody QueryRepayHisInfoParams params) {
        return shopSupService.queryQepaymentInfo(params);
    }

    /**
     * 查询店铺与供应商未还款购销单
     */
    @RequestMapping(method = RequestMethod.POST, value = "/queryUnpaidBillList.do")
    @ResponseBody
    public ShopsResult queryUnpaidBillList(@RequestBody SupplierUniqueParams params) {
        return shopSupService.queryUnpaidBillList(params);
    }

    /**
     * 多选购销单进行还款
     */
    @RequestMapping(method = RequestMethod.POST, value = "/repaymentBills.do")
    @ResponseBody
    public ShopsResult repaymentBills(@RequestBody RepaymentBillsParams params) {
        return shopSupService.repaymentBills(params);
    }

    /**
     * 建档并更新商品信息
     * @param params
     * @return
     */
    @RequestMapping(method = RequestMethod.POST, value = "/updateSupRecordGood.do")
    @ResponseBody
    public ShopsResult updateSupRecordGood(@RequestBody SupRecordGoodsUpdateParams params){
        return shopSupService.updateSupRecordGood(params);
    }
    /**
     * 绑定供货商
     * @param params
     * @return
     */
    @RequestMapping(method = RequestMethod.POST, value = "/bindSupplier.do")
    @ResponseBody
    public ShopsResult bindSupplier(@RequestBody BindSupParams params){
        return shopSupService.bindSupplier(params);
    }
    /**
     * 查询供货商申请列表信息
     * @param params
     * @return
     */
    @RequestMapping(method = RequestMethod.POST, value = "/querySupExamineList.do")
    @ResponseBody
    public ShopsResult querySupExamineList(@RequestBody QuerySupListParams params){
        return shopSupService.querySupExamineList(params);
    }
    /**
     * 根据已建档商品ID和店铺编码删除已建档商品信息
     * @param params
     * @return
     */
    @RequestMapping(method = RequestMethod.POST, value = "/deleteShopSupGoodsEntity.do")
    @ResponseBody
    public ShopsResult deleteShopSupGoodsEntity(@RequestBody ShopSupGoodsDeleteParams params) {
        return shopSupService.deleteShopSupGoodsEntity(params);
    }
}
