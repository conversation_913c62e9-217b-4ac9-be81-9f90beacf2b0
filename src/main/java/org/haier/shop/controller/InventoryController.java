package org.haier.shop.controller;

import java.io.IOException;
import java.io.OutputStream;
import java.net.URLEncoder;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.haier.shop.service.InventoryService;
import org.haier.shop.util.HandleMessyCode;
import org.haier.shop.util.LoadOutObjectXLSUtil;
import org.haier.shop.util.PurResult;
import org.haier.shop.util.ServletsUtil;
import org.haier.shop.util.StringUtil;
import org.haier.shop.util.XLSCallBack;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

@Controller
@RequestMapping("/inven")
public class InventoryController {
	@Resource
	private InventoryService invenService;

	
	@RequestMapping("/queryInventoryRecord.do")
	@ResponseBody
	public PurResult queryInventoryRecord(HttpServletRequest request,
			@RequestParam(value="page",defaultValue="1")Integer page,
			@RequestParam(value="limit",defaultValue="15")Integer limit){
		PurResult result = new PurResult();
		try {
			Map<String, Object> params = HandleMessyCode.handle(ServletsUtil.getParameters(request));
			params.put("limit", limit);
			params.put("page", (page-1)*limit);
			result=invenService.queryInventoryRecord(params);
		} catch (Exception e) {
			result.setStatus(0);
			result.setMsg("查询失败!");
			e.printStackTrace();
		}		
		return result;
	}
	
	@RequestMapping("/inventoryDetail.do")
	public String inventoryDetail(String id,Model model){
		model.addAttribute("id",id);
		return "/WEB-INF/goods/goodsInventoryDetail.jsp";
	}
	
	
	/**
	 * 盘点订单详情
	 * @param map
	 * @return
	 */
	@RequestMapping("/queryInventoryDetail.do")
	@ResponseBody
	public PurResult queryInventoryDetail(
			@RequestParam(value="shopUnique")String shopUnique,
			@RequestParam(value="id")String id
			){
		Map<String,Object> map=new HashMap<String, Object>();
		map.put("shopUnique", shopUnique);
		map.put("id",id);
		return invenService.queryInventoryDetail(map);
	}
	
	/**
	 * 盘库记录导出excel
	 */
	@RequestMapping("/exportInventoryExcel.do")
	public void exportInventoryExcel(HttpServletRequest request, HttpServletResponse response) throws Exception{
		Map<String, Object> params = HandleMessyCode.handle(ServletsUtil.getParameters(request));
		List<Map<String,Object>> list=invenService.queryInventoryList(params);	
		String[] titles=new String[] {
				"盘单号","盘点日期","盘点总数","盘亏数","盘亏金额","操作人"
		};
		loadPanKuXLS(list,"盘库记录",request,response,titles);
	}
	
	public void loadPanKuXLS(List<Map<String, Object>> caseVoList, final String string,HttpServletRequest request,HttpServletResponse response,final String[] titles){
		try {
			response.setContentType(
					"application/x-msdownload; charset=UTF-8");// 告知浏览器文件类型
			String agent = request.getHeader("user-agent");
			if (agent.contains("Firefox")) {
				response.setHeader(
						"Content-Disposition",
						"attachment;filename="
								+ new String((string + ".xls")
										.getBytes("GB2312"), "ISO-8859-1"));// 设置文件头
			} else {
				response.setHeader(
						"Content-Disposition",
						"attachment;filename="
								+ URLEncoder.encode(string + ".xls", "UTF-8"));// 设置文件头
			}
			OutputStream os = response.getOutputStream();
			LoadOutObjectXLSUtil<Map<String, Object>> objectXLS = new LoadOutObjectXLSUtil<Map<String, Object>>();
			objectXLS.generateXLS(os, caseVoList, new XLSCallBack<Map<String, Object>>() {
				public Object[] getValues(Map<String, Object> tt) {
					String str = "";
					
					String id = tt.get("id")+"";
					if (StringUtil.blank(id)) {
						id = str;
					}
					String startTime =  tt.get("startTime")+"";
					if (StringUtil.blank(startTime)) {
						startTime = str;
					}
					String inventoryAcount =tt.get("inventoryAcount")+"";
					if (StringUtil.blank(inventoryAcount)) {
						inventoryAcount = str;
					}
					String invenCount = tt.get("invenCount")+"";
					if (StringUtil.blank(invenCount)) {
						invenCount = str;
					}
					String invenAmount = tt.get("invenAmount")+"";
					if (StringUtil.blank(invenAmount)) {
						invenAmount = str;
					}
					String staffName = tt.get("staffName")+"";
					if (StringUtil.blank(staffName)) {
						staffName = str;
					}					
					
					return new String[] {id,startTime,inventoryAcount,invenCount,invenAmount,staffName
					};
				}

				public String getTitle() {
					return string;
				}

				public int[] getColumnsWidth() {
					return new int[] { 
										  80 * 80, 80 * 80,80 * 80,80 * 80,
										  80 * 80, 80 * 80,80 * 80,80 * 80,
										  80 * 80,80 * 80,80 * 80};
				}

				public String[] getColumnsName() {
					return titles;
				}

				public int[][] MergedRegion() {
					// TODO Auto-generated method stub
					return null;
				}

				public List<Object[]> getheaderValue() {
					// TODO Auto-generated method stub
					return null;
				}

			});
			os.flush();
		} catch (IOException e) {
			e.printStackTrace();
		}
	}
}
