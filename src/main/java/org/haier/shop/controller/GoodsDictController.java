package org.haier.shop.controller;

import java.util.Map;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;

import org.haier.shop.service.GoodsDictService;
import org.haier.shop.util.HandleMessyCode;
import org.haier.shop.util.PurResult;
import org.haier.shop.util.ServletsUtil;
import org.haier.shop.util.ShopsResult;
import org.haier.shop.util.UtilForJAVA;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

@Controller
@RequestMapping("/html/goods")
public class GoodsDictController {
	@Resource
	private GoodsDictService dictService;
	
	/**
	 * 
	 * 商品公共信息查询
	 * @param goods_barcode 商品条码
	 * @param shop_unique 供货商编号
	 * @return
	 */
	@RequestMapping("/queryBaseGoodsMessage.do")
	@ResponseBody
	public ShopsResult queryBaseGoodsMessage(
			@RequestParam(value="goodsBarcode",required=true)String goodsBarcode,
			@RequestParam(value="shopUnique",required=true)String shopUnique){
		return dictService.queryBaseGoodsMessage(goodsBarcode, shopUnique);
	}
	
	/**
	 * 跳转到
	 */
	@RequestMapping("/toGoodsDictList.do")
	public String toGoodsDictList(){
		return "/WEB-INF/goods/goodsDictAudit.jsp";
	}
	
	/**
	 * 跳转到编辑图片页面
	 */
	@RequestMapping("/toEditImg.do")
	public String toEditImg(){
		return "/WEB-INF/goods/editImg.jsp";
	}
	
	/**
	 * 修改商品大库图片
	 */
	@RequestMapping("/updateGoodsDictImgOld.do")
	@ResponseBody
	public PurResult updateGoodsDictImg(
			String  goods_barcode,
			String  goods_picturepath,
			HttpServletRequest request
			){
		return dictService.updateGoodsDictImg(goods_barcode,goods_picturepath,request);
	}
	
	
	/**
	 * 大库-商品列表查询
	 */
	@RequestMapping("/getGoodsDictList.do")
	@ResponseBody
	public PurResult getGoodsDictList(HttpServletRequest request,
			@RequestParam(value="page",defaultValue="1")Integer page,
			@RequestParam(value="limit",defaultValue="15")Integer limit
			,String goodsMessage
			){		
		PurResult result = new PurResult();
		try {
			Map<String, Object> params = HandleMessyCode.handle(ServletsUtil.getParameters(request));
			params.put("limit", limit);
			params.put("page", (page-1)*limit);
			params.put("goodsMessage",goodsMessage);
			result=dictService.getGoodsDictList(params);
		} catch (Exception e) {
			result.setStatus(0);
			result.setMsg("查询失败!");
			e.printStackTrace();
		}		
		return result;
	}
	
	@RequestMapping("/updateGoodsDict.do")
	@ResponseBody
	public ShopsResult updateGoodsDict(HttpServletRequest request){
		try {
			Map<String,Object> map = UtilForJAVA.addParameter(request);
			return dictService.updateGoodsDictMsg(map);
		} catch (Exception e) {
			return new ShopsResult(0, "系统错误！");
		}
	}
}
