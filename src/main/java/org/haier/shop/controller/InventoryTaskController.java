package org.haier.shop.controller;

import org.haier.shop.service.InventoryTaskService;
import org.haier.shop.util.HandleMessyCode;
import org.haier.shop.util.PurResult;
import org.haier.shop.util.ServletsUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Map;

/**
 * 盘库单
 */
@Controller
@RequestMapping("/inventoryTask")
public class InventoryTaskController {

    @Autowired
    private InventoryTaskService inventoryTaskService;

    /**
     * 盘库列表跳转
     * @return
     */
    @RequestMapping("/forwardInventoryTask.do")
    public String forwardInventoryTask(){
        return "/WEB-INF/goods/inventoryTask.jsp";
    }

    /**
     * 盘库列表
     */
    @RequestMapping("/taskList")
    @ResponseBody
    public PurResult taskList(HttpServletRequest request,
                              @RequestParam(value="page",defaultValue="1")Integer page,
                              @RequestParam(value="limit",defaultValue="15")Integer limit) {
        PurResult result = new PurResult();
        try {
            Map<String, Object> params = HandleMessyCode.handle(ServletsUtil.getParameters(request));
            params.put("limit", limit);
            params.put("page", page);
            result = inventoryTaskService.taskList(params,request);
        } catch (Exception e) {
            result.setStatus(0);
            result.setMsg("查询失败!");
            e.printStackTrace();
        }
        return result;
    }

    /**
     * 盘库详情跳转
     * @return
     */
    @RequestMapping("/forwardInventoryTaskDetail.do")
    public String forwardInventoryTaskDetail(Long taskId,Model model){
        model.addAttribute("taskId",taskId);
        return "/WEB-INF/goods/inventoryTaskDetail.jsp";
    }

    /**
     * 盘库单预览
     * @return
     */
    @RequestMapping("/taskPreview.do")
    @ResponseBody
    public PurResult taskPreview(HttpServletRequest request){
        try {
            return inventoryTaskService.taskPreview(request);
        }catch (Exception e){
            return PurResult.fail("查询失败");
        }
    }

    /**
     * 单个商品明细-商品盘点跳转
     * @return
     */
    @RequestMapping("/forwardTaskGoodsDetail.do")
    public String forwardTaskGoodsDetail(Long taskId,String goodsBarcode, Model model){
        model.addAttribute("taskId",taskId);
        model.addAttribute("goodsBarcode",goodsBarcode);
        return "/WEB-INF/goods/taskGoodsDetail.jsp";
    }

    /**
     * 单个商品明细-商品盘点跳转
     * @return
     */
    @RequestMapping("/taskGoodsDetail.do")
    @ResponseBody
    public PurResult taskGoodsDetail(HttpServletRequest request){
        try {
            return inventoryTaskService.taskGoodsDetail(request);
        }catch (Exception e){
            return PurResult.fail("查询失败");
        }
    }


    /**
     * 调拨单导出excel
     */
    @RequestMapping("/exportInventoryExcel.do")
    public void exportAllorationExcel(HttpServletRequest request, HttpServletResponse response) throws Exception{
        inventoryTaskService.exportInventoryExcel(request,response);
    }

}
