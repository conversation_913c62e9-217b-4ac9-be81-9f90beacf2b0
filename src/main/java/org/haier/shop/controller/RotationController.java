package org.haier.shop.controller;

import java.io.File;
import java.io.FileInputStream;
import java.io.InputStream;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;

import org.haier.shop.service.RotationService;
import org.haier.shop.util.FTPConfig;
import org.haier.shop.util.SFTPUtil;
import org.haier.shop.util.ShopsResult;
import org.haier.shop.util.ShopsUtil;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.multipart.MultipartFile;


@Controller
@RequestMapping("/rotation")
public class RotationController {
	@Resource
	private RotationService rotationService;
	
	
	@RequestMapping("/toAddRotatiol.do")
	public String toAddRotatiol(Model model) {
		List<Map<String,Object>> brandList = rotationService.queryBrandList();
		model.addAttribute("brandList",brandList);
		return "/WEB-INF/rotation/rotationAdd.jsp";
	}
	
	@RequestMapping("/deleteRotationImg.do")
	@ResponseBody
	public ShopsResult deleteRotationImg(Integer img_id,Integer valid_status) {
		try {
			return rotationService.addNewRotationImg(null, null, img_id, null, null, valid_status);
		}catch (Exception e) {
			e.printStackTrace();
			ShopsResult sr = new ShopsResult();
			return sr;
		}
	}
	
	@RequestMapping("/addNewRotationImg.do")
	@ResponseBody
	public ShopsResult addNewRotationImg(HttpServletRequest request,@RequestParam(value="fileupload",required=true)MultipartFile file,
			Integer img_id,Integer brand_id,Integer img_type,String remarks,Integer valid_status) {
		ShopsResult sr = new ShopsResult(1,"创建成功!");
		try {
			//获取图片，保存，并返回访问地址
			if(file!=null){
				String orName = file.getOriginalFilename();//获取文件原名称
				String lastName = orName.substring(orName.lastIndexOf("."));
				lastName.replace(".JPG", ".jpg");
				lastName.replace(".JPEG", ".jpeg");
				lastName.replace(".PNG", ".png");
				String newName = "rotation" + (img_id == null ? "" : img_id) +Math.round(Math.random()*100)+lastName;
				String filePathDetail=File.separator+"image"+File.separator+"rotation";
				String filePath=request.getSession().getServletContext().getRealPath(File.separator);
				filePath=filePath.substring(0, filePath.length()-request.getContextPath().length())+filePathDetail;
				SFTPUtil sftp = new SFTPUtil(FTPConfig.username, FTPConfig.password, FTPConfig.host, FTPConfig.port);   
		        sftp.login();
		        
		        ShopsUtil.savePicture(file, filePath, newName,"2");//图片保存本地
		        
		        InputStream is;
		        is=new FileInputStream(new File(filePath+File.separator+newName));
				sftp.upload(FTPConfig.goods_path+"/"+"rotation", newName, is);   
				String img_url = "https://file.buyhoo.cc/image/rotation/" + newName;
				sr.setData("https://file.buyhoo.cc/image/rotation/" + newName);
				
				rotationService.addNewRotationImg(img_url, brand_id, img_id, img_type, remarks, valid_status);
			}
			
		}catch (Exception e) {
			e.printStackTrace();
			sr.setStatus(0);
			sr.setMsg("创建失败!");
		}
		return sr;
	}
	
	@RequestMapping("/toRotatiolMagager.do")
	public String toRotatiolMagager(Model model) {
		List<Map<String,Object>> brandList = rotationService.queryBrandList();
		model.addAttribute("brandList",brandList);
		return "/WEB-INF/rotation/rotationManager.jsp";
	}
	
	/**
	 * 查询轮播图信息
	 * @param brandId 品牌ID
	 * @param imgType 图片类型
	 * @param page 页码
	 * @param limit 单页查询数量
	 * @return
	 */
	@RequestMapping("/queryRotationList.do")
	@ResponseBody
	public ShopsResult queryRotationList(Integer brandId,Integer imgType,Integer page,Integer limit) {
		try {
			return rotationService.queryRotationList(brandId, imgType, page, limit);
		}catch (Exception e) {
			e.printStackTrace();
			ShopsResult sr = new ShopsResult(0,"查询失败");
			return sr;
		}
	}
}
