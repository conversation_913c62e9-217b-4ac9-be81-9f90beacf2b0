package org.haier.shop.controller;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import org.haier.customer.entity.ShopResult;
import org.haier.meituan.util.MUtil;
import org.haier.shop.entity.SaleListDetail;
import org.haier.shop.entity.SaleListMain;
import org.haier.shop.service.SaleListService;
import org.haier.shop.util.*;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.*;
import java.math.BigDecimal;
import java.net.URLDecoder;
import java.net.URLEncoder;
import java.sql.Timestamp;
import java.util.*;
import java.util.stream.Collectors;

@Controller
@RequestMapping("/html/order")
public class SalelistController {
	@Resource
	private SaleListService saleService;
	//---------------------统计图
	@RequestMapping("/queryXXsale.do")
	@ResponseBody
	public ShopsResult queryXXsale(String shop_unique,String create_time,String end_time,Integer page,
			Integer limit,String query_type){
		Map<String,Object> map=new HashMap<String,Object>();
		if(null != page && null != limit) {
			map.put("startNum", (page-1)*limit);
			map.put("pageSize", limit);
		}
		map.put("shop_unique", shop_unique);
		map.put("start_time", create_time);
		map.put("end_time", end_time);
		map.put("query_type", query_type);
		return saleService.queryXXsale(map);
	}
	
	@RequestMapping("/queryTimeSale.do")
	@ResponseBody
	public ShopsResult queryTimeSale(String shop_unique,String create_time,String end_time){
		Map<String,Object> map=new HashMap<String,Object>();

		map.put("shop_unique", shop_unique);
		map.put("start_time", create_time);
		map.put("end_time", end_time);
		return saleService.queryTimeSale(map);
	}
	
	
	// --------------------统计图---------------------

	/**
	 * 1、查询各分店的营业信息
	 * @param manager_unique
	 * @param page
	 * @param limit
	 * @param startTime
	 * @param endTime
	 * @return
	 */
	@RequestMapping("/statisticsForShopByPage.do")
	@ResponseBody
	public ShopsResult statisticsForShopByPage(String manager_unique,Integer page,Integer limit,
			String startTime,String endTime,String shopList,String field,String order) {
		return saleService.statisticsForShopByPage(manager_unique, page, limit, startTime, endTime,shopList,field,order);
	}
	/**
	 * 这个接口仅用于将银河湾备用库存转移到实际库存
	 * @return
	 */
	@RequestMapping("/updateGoodsList.do")
	@ResponseBody
	public ShopsResult updateGoodsList() {
		return saleService.updateGoodsList();
	}
	
	@RequestMapping("/statisticsForShop.do")
	public String statisticsForShop() {
		return "/WEB-INF/sale/statisticsForShop.jsp";
	}
	
	@RequestMapping("/queryReturnDetail.do")
	@ResponseBody
	public ShopsResult queryReturnDetail(String retListUnique) {
		return saleService.queryReturnDetail(retListUnique);
	}
	
	@RequestMapping("/toQueryReturnDetail.do")
	public String toQueryReturnDetail() {
		return "/WEB-INF/sale/toQueryReturnDetail.jsp";
	}
	/**
	 * 去输入拒绝退款原因
	 * @return
	 */
	@RequestMapping("/toInputRefuseReason.do")
	public String toInputRefuseReason() {
		return "/WEB-INF/sale/toInputRefuseReason.jsp";
	}
	
	/**
	 * 修改退款订单状态
	 * @param retListUnique 退款单号
	 * @param retListHandlestate 退款审核状态
	 * @param retListRemarks 如果拒绝退款，需要填写拒绝原因
	 * @param staffId 操作员工ID
	 * @param macId 操作设备的macId或浏览器型号
	 * @return
	 */
	@RequestMapping("/modifyReturnMsg.do")
	@ResponseBody
	public ShopsResult modifyReturnMsg(String retListUnique, Integer retListHandleState, String retListRemarks, String staffId, String macId) {
		return saleService.modifyReturnMsg(retListUnique, retListHandleState, retListRemarks, staffId, macId);
	}
	
	/**
	 * 查询退款订单的申请信息
	 * @param shopUnique 店铺编号
	 * @param limit 页码
	 * @param pageSize 单页查询数量
	 * @param startTime 开始查询时间
	 * @param endTime 结束查询时间
	 * @param retListHandleState 订单退款申请状态：1、待处理；2、处理中；3、已退款；4、已驳回
	 * @return
	 */
	@RequestMapping("/queryRetLists.do")
	@ResponseBody
	public ShopsResult queryRetLists(String shopUnique, Integer page, Integer limit, String startTime, String endTime, Integer retListHandleState) {
		return saleService.queryRetLists(shopUnique, page, limit, startTime, endTime, retListHandleState);
	}
	
	@RequestMapping("/toQueryReturnList.do")
	public String toQueryReturnList() {
		return "/WEB-INF/sale/toQueryReturnList.jsp";
	}
	
	@RequestMapping("/querySaleListNetPage.do")
	public String querySaleListNetPage() {
		return "/WEB-INF/sale/querySaleListNet.jsp";
	}
	
	@RequestMapping("/querySaleListsPage.do")
	public String querySaleListsPage(HttpServletRequest request,String sale_list_handlestate){
		request.setAttribute("sale_list_handlestate", sale_list_handlestate);
		return "/WEB-INF/sale/querySaleList.jsp";
	}
	@RequestMapping("/querySelectPeiSongPage.do")
	public String querySaleListsPage(HttpServletRequest request,String sale_list_unique,String shop_unique){
		System.out.println(sale_list_unique);
		request.setAttribute("sale_list_unique", sale_list_unique);
		request.setAttribute("shop_unique", shop_unique);
		return "/WEB-INF/sale/querySelectPeiSongPage.jsp";
	}
	
	@RequestMapping("/queryFarmListsAllPage.do")
	public String queryFarmListsAllPage(){
		return "/WEB-INF/sale/queryFarmListsAll.jsp";
	}
	
	@RequestMapping("/queryFarmOrderDetailPage.do")
	public String queryFarmOrderDetailPage(String sup_order_unique, String secretary_id,HttpServletRequest request){
		request.setAttribute("sup_order_unique", sup_order_unique);
		request.setAttribute("secretary_id", secretary_id);
		return "/WEB-INF/sale/farmOrderDetail.jsp";
	}
	
	/**
	 * 分店店铺销售统计
	 */
	@RequestMapping("/queryShopOrderPage.do")
	public String queryShopOrderPage(){
		return "/WEB-INF/sale/shopOrderList.jsp";
	}
	@RequestMapping("/querySaleListsPageNY.do")
	public String querySaleListsPageNY(){
		return "/WEB-INF/sale/querySaleListNY.jsp";
	}
	
	@RequestMapping("/queryOrderDetailPage.do")
	public String queryOrderDetailPage(String shop_unique, String sale_list_unique,HttpServletRequest request,Integer loanStatus){
		request.setAttribute("shop_unique", shop_unique);
		request.setAttribute("sale_list_unique", sale_list_unique);
		request.setAttribute("loanStatus", loanStatus);
		return "/WEB-INF/sale/orderDetail.jsp";
	}
	
	@RequestMapping("/queryOrderDetailPageJY.do")
	public String queryOrderDetailPageJY(String shop_unique, String sale_list_unique,HttpServletRequest request,Integer loanStatus){
		request.setAttribute("shop_unique", shop_unique);
		request.setAttribute("sale_list_unique", sale_list_unique);
		request.setAttribute("loanStatus", loanStatus);
		return "/WEB-INF/sale/orderDetailJY.jsp";
	}
	
	@RequestMapping("/queryOrderDetailPrintPage.do")
	public String queryOrderDetailPrintPage(String shop_unique, String sale_list_unique,HttpServletRequest request){
		request.setAttribute("shop_unique", shop_unique);
		request.setAttribute("sale_list_unique", sale_list_unique);
		return "/WEB-INF/sale/orderDetailPrint.jsp";
	}
	/**
	 * 网单查询，去掉了充值统计和会员续费统计功能
	 * @param shop_unique
	 * @param orderMessage
	 * @param sale_list_handlestate
	 * @param startTime
	 * @param endTime
	 * @param sale_list_state
	 * @param orderName
	 * @param orderType
	 * @param pageNum
	 * @param pageSize
	 * @param staffId
	 * @param goodsMessage
	 * @param goods_kind_parunique
	 * @param goods_kind_unique
	 * @param cusType
	 * @param paymentMethod
	 * @return
	 */
	@RequestMapping("/querySaleListsNet.do")
	@ResponseBody
	public PurResult querySaleListsN(
			@RequestParam(value="shop_unique")String shop_unique, 
			String orderMessage, 
			Integer sale_list_handlestate,
			String startTime,
			String endTime, 
			Integer sale_list_state, 
			@RequestParam(value="orderName" ,defaultValue="sale_list_datetime")String orderName, 
			@RequestParam(value="orderType",defaultValue="desc")String orderType,
			@RequestParam(value = "page", defaultValue = "1") int page,
			@RequestParam(value = "limit", defaultValue = "8") int pageSize,
			String staffId,
			String goodsMessage,
			String goods_kind_parunique,
			String goods_kind_unique,
			Integer cusType,
			Integer paymentMethod
			){
		return saleService.querySaleListsNet(shop_unique, orderMessage, sale_list_handlestate, startTime, endTime, sale_list_state, orderName, orderType, page, pageSize,staffId,
				goodsMessage,goods_kind_parunique,goods_kind_unique,cusType,paymentMethod);
	}
	
	@RequestMapping("/querySaleLists.do")
	@ResponseBody
	public PurResult querySaleLists(
			@RequestParam(value="shop_unique")String shop_unique, 
			String orderMessage, 
			Integer sale_list_handlestate,
			String startTime,
			String endTime, 
			Integer sale_list_state, 
			@RequestParam(value="orderName" ,defaultValue="sale_list_datetime")String orderName, 
			@RequestParam(value="orderType",defaultValue="desc")String orderType,
			@RequestParam(value = "page", defaultValue = "1") int page,
			@RequestParam(value = "limit", defaultValue = "8") int pageSize,
			String staffId,
			String goodsMessage,
			String goods_kind_parunique,
			String goods_kind_unique,
			Integer cusType,
			Integer paymentMethod
			){
		return saleService.querySaleLists(shop_unique, orderMessage, sale_list_handlestate, startTime, endTime, sale_list_state, orderName, orderType, page, pageSize,staffId,
				goodsMessage,goods_kind_parunique,goods_kind_unique,cusType,paymentMethod);
	}
	@RequestMapping("/querySelectPeiSong.do")
	@ResponseBody
	public PurResult querySelectPeiSong(
			@RequestParam(value="shop_unique")String shop_unique
			){
		return saleService.querySelectPeiSong(shop_unique);
	}
	/**
	 * 宁宇销售订单
	 * @param shop_unique
	 * @param orderMessage
	 * @param sale_list_handlestate
	 * @param startTime
	 * @param endTime
	 * @param sale_list_state
	 * @param orderName
	 * @param orderType
	 * @param page
	 * @param pageSize
	 * @param staffId
	 * @param goodsMessage
	 * @param goods_kind_parunique
	 * @param goods_kind_unique
	 * @param cusType
	 * @param paymentMethod
	 * @return
	 */
	@RequestMapping("/querySaleNYLists.do")
	@ResponseBody
	public PurResult querySaleNYLists(
			@RequestParam(value="shop_unique")String shop_unique, 
			String orderMessage, 
			Integer sale_list_handlestate,
			String startTime,
			String endTime, 
			Integer sale_list_state, 
			@RequestParam(value="orderName" ,defaultValue="sale_list_datetime")String orderName, 
			@RequestParam(value="orderType",defaultValue="desc")String orderType,
			@RequestParam(value = "page", defaultValue = "1") int page,
			@RequestParam(value = "limit", defaultValue = "8") int pageSize,
			String staffId,
			String goodsMessage,
			String goods_kind_parunique,
			String goods_kind_unique,
			Integer cusType,
			Integer paymentMethod,
			Integer sourceType
			){
		return saleService.querySaleNYLists(shop_unique, orderMessage, sale_list_handlestate, startTime, endTime, sale_list_state, orderName, orderType, page, pageSize,staffId,
				goodsMessage,goods_kind_parunique,goods_kind_unique,cusType,paymentMethod, sourceType);
	}
	
	/**
	 * 宁宇订单查询
	 * @param shop_unique
	 * @param startTime
	 * @param endTime
	 * @param page
	 * @param pageSize
	 * @return
	 */
	@RequestMapping("/querySaleListsNY.do")
	@ResponseBody
	public PurResult querySaleListsNY(
			@RequestParam(value="shop_unique")String shop_unique, 
			String startTime,
			String endTime, 
			@RequestParam(value = "page", defaultValue = "1") int page,
			@RequestParam(value = "limit", defaultValue = "8") int pageSize
			){
		Map<String,Object> map = new HashMap<String,Object>();
		map.put("shop_unique", shop_unique);
		map.put("startNum", (page-1)*pageSize);
		map.put("pageSize", pageSize);
		map.put("startTime", startTime);
		map.put("endTime", endTime);
		return saleService.querySaleListsNY(map);
	}
	/**
	 * 员工统计-导出excel
	 */
	@RequestMapping("/exportEmployeeStatistic.do")
	public void exportEmployeeStatistic(HttpServletRequest request, HttpServletResponse response,@RequestParam(value="shop_unique")String shop_unique, 
			String orderMessage, 
			Integer sale_list_handlestate,
			String startTime,
			String endTime, 
			Integer sale_list_state, 
			@RequestParam(value="orderName" ,defaultValue="sale_list_datetime")String orderName, 
			@RequestParam(value="orderType",defaultValue="desc")String orderType,
			@RequestParam(value = "page", defaultValue = "1") int page,
			@RequestParam(value = "limit", defaultValue = "8") int pageSize,
			Integer staffId,
			String goodsMessage,
			String goods_kind_parunique,
			String goods_kind_unique) throws Exception{
		List<Map<String,Object>> list=saleService.querySaleLists2(shop_unique, orderMessage, sale_list_handlestate, startTime, endTime, sale_list_state, orderName, orderType, staffId,
				goodsMessage,goods_kind_parunique,goods_kind_unique);	
		String[] titles=new String[] {
				"订单编号","下单日期","收货人","订单金额","订单状态","付款状态","收银员","提成"
		};
		loadEmployeeXLS(list,"调拨商品",request,response,titles);
	}
	
	public void loadEmployeeXLS(List<Map<String, Object>> caseVoList, final String string,HttpServletRequest request,HttpServletResponse response,final String[] titles){
		try {
			response.setContentType(
					"application/x-msdownload; charset=UTF-8");// 告知浏览器文件类型
			String agent = request.getHeader("user-agent");
			if (agent.contains("Firefox")) {
				response.setHeader(
						"Content-Disposition",
						"attachment;filename="
								+ new String((string + ".xls")
										.getBytes("GB2312"), "ISO-8859-1"));// 设置文件头
			} else {
				response.setHeader(
						"Content-Disposition",
						"attachment;filename="
								+ URLEncoder.encode(string + ".xls", "UTF-8"));// 设置文件头
			}
			OutputStream os = response.getOutputStream();
			LoadOutObjectXLSUtil<Map<String, Object>> objectXLS = new LoadOutObjectXLSUtil<Map<String, Object>>();
			objectXLS.generateXLS(os, caseVoList, new XLSCallBack<Map<String, Object>>() {
				public Object[] getValues(Map<String, Object> tt) {
					String str = "";
					
					String sale_list_unique = tt.get("sale_list_unique")+"";
					if (StringUtil.blank(sale_list_unique)) {
						sale_list_unique = str;
					}
					String time =  tt.get("time")+"";
					if (StringUtil.blank(time)) {
						time = str;
					}
					String sale_list_name =tt.get("sale_list_name")+"";
					if (StringUtil.blank(sale_list_name)) {
						sale_list_name = str;
					}
					String sale_list_total = tt.get("sale_list_total")+"";
					if (StringUtil.blank(sale_list_total)) {
						sale_list_total = str;
					}
					String sale_list_handlestate = tt.get("sale_list_handlestate")+"";
					if (StringUtil.blank(sale_list_handlestate)) {
						sale_list_handlestate = str;
					}
					String sale_list_state = tt.get("sale_list_state")+"";
					if (StringUtil.blank(sale_list_state)) {
						sale_list_state = str;
					}
					String staffName = tt.get("staffName")+"";
					if (StringUtil.blank(staffName)) {
						staffName = str;
					}
					String commission_sum = tt.get("commission_sum")+"";
					if (StringUtil.blank(commission_sum)) {
						commission_sum = str;
					}
					
					return new String[] {sale_list_unique,time,sale_list_name,
							sale_list_total,sale_list_handlestate,sale_list_state,staffName,commission_sum
					};
				}

				public String getTitle() {
					return string;
				}

				public int[] getColumnsWidth() {
					return new int[] { 
										  80 * 80, 80 * 80,80 * 80,80 * 80,
										  80 * 80, 80 * 80,80 * 80,80 * 80,
										  80 * 80,80 * 80,80 * 80};
				}

				public String[] getColumnsName() {
					return titles;
				}

				public int[][] MergedRegion() {
					// TODO Auto-generated method stub
					return null;
				}

				public List<Object[]> getheaderValue() {
					// TODO Auto-generated method stub
					return null;
				}

			});
			os.flush();
		} catch (IOException e) {
			e.printStackTrace();
		}
	}
	
	/**
	 * 分店销售统计
	 */
	@RequestMapping("/queryShopOrderList.do")
	@ResponseBody
	public PurResult queryShopOrderList(
			@RequestParam(value="manager_unique")String manager_unique, 
			String startTime,
			String endTime, 
			@RequestParam(value = "page", defaultValue = "1") int page,
			@RequestParam(value = "limit", defaultValue = "15") int pageSize
			){
		return saleService.queryShopOrderList(manager_unique, startTime, endTime,page, pageSize);
	}

	/**
	 * 查询订单详情
	 * @param shop_unique
	 * @param sale_list_unique
	 * @return
	 */
	@RequestMapping("/queryOrderDetail.do")
	@ResponseBody
	public ShopsResult queryOrderDetail(
			@RequestParam(value="shop_unique")String shop_unique,
			@RequestParam(value="sale_list_unique")String sale_list_unique,Integer loanStatus){
		return saleService.queryOrderDetail(shop_unique, sale_list_unique,loanStatus);
	}
	
	/**
	 * 查询订单详情
	 * @param shop_unique
	 * @param sale_list_unique
	 * @return
	 */
	@RequestMapping("/queryOrderDetailJY.do")
	@ResponseBody
	public ShopsResult queryOrderDetailJY(
			@RequestParam(value="shop_unique")String shop_unique,
			@RequestParam(value="sale_list_unique")String sale_list_unique,Integer loanStatus){
		return saleService.queryOrderDetailJY(shop_unique, sale_list_unique,loanStatus);
	}
	
	
	/**
	 * 查询订单详情
	 * @param shop_unique
	 * @param sale_list_unique
	 * @return
	 */
	@RequestMapping("/queryFarmOrderDetail.do")
	@ResponseBody
	public ShopsResult queryFarmOrderDetail(
			@RequestParam(value="secretary_id")String secretary_id,
			@RequestParam(value="sup_order_unique")String sup_order_unique){
		return saleService.queryFarmOrderDetail(secretary_id, sup_order_unique);
	}
	
	/**
	 * 更新订单处理状态
	 * @param shop_unique
	 * @param sale_list_unique
	 * @param sale_list_handlestate
	 * @param sale_list_state
	 * @return
	 */
	@RequestMapping("/updateSaleList.do")
	@ResponseBody
	public ShopsResult updateSaleList(String shop_unique,String sale_list_unique,Integer sale_list_handlestate,Integer sale_list_state){
		return saleService.updateSaleList(shop_unique, sale_list_unique, sale_list_handlestate, sale_list_state);
	}
	
	@RequestMapping("/updateSaleListStatus.do")
	@ResponseBody
	public ShopsResult updateSaleListStatus(String sale_list_unique){
		return saleService.updateSaleListStatus(sale_list_unique);
	}
	
	/**
	 * 主界面查询基本信息
	 * @param shop_unique
	 * @return
	 */
	@ResponseBody
	@RequestMapping("/baseMessage.do")
	public ShopsResult baseMessage(String shop_unique){
		return saleService.baseMessage(shop_unique);
	}
	
	/**
	 * 店铺营运信息查询
	 * @param map
	 * @return
	 */
	@RequestMapping("/salesTurnoverStatistics.do")
	@ResponseBody
	public ShopsResult salesTurnoverStatistics(
			@RequestParam(value="shopUnique",required=true)Long shopUnique,
			String startTime,
			String endTime
			){
		Map<String,Object> map=new HashMap<String, Object>();
		map.put("shopUnique", shopUnique);
		map.put("startTime", startTime);
		map.put("endTime", endTime);
		return saleService.salesTurnoverStatistics(map);
	}
	
	
	/**
	 * 查询各月内，各分类销售情况汇总
	 * @param map
	 * @return
	 */
	@RequestMapping("/typeSaleByTime.do")
	@ResponseBody
	public ShopResult typeSaleByTime(
			@RequestParam(value="shopUnique",required=true)Long shopUnique,
			@RequestParam(value="startTime",required=true)String startTime,
			@RequestParam(value="endTime",required=true)String endTime
			){
		Map<String,Object> map=new HashMap<String, Object>();
		map.put("shopUnique", shopUnique);
		map.put("startTime", startTime);
		map.put("endTime", endTime);
		return saleService.typeSaleByTime(map);
	}
	
	
	/**
	 * 各分数的订单数量统计
	 * @param map
	 * @return
	 */
	@RequestMapping("/saleListEvaluateQuery.do")
	@ResponseBody
	public ShopsResult saleListEvaluateQuery(
			@RequestParam(value="days",required=true,defaultValue="1")Integer days,
			@RequestParam(value="shopUnique",required=true)Long shopUnique){
		Map<String,Object> map=new HashMap<String, Object>();
		map.put("shopUnique", shopUnique);
		map.put("days", days);
		return saleService.saleListEvaluateQuery(map);
	}

	/**
	 * 用户活跃度查询
	 * @param map
	 * @return
	 * 大于起始时间当天，小于等于截至日期当天
	 */
	@RequestMapping("/cusActivityQuery.do")
	@ResponseBody
	public ShopsResult cusActivityQuery(
			@RequestParam(value="shopUnique",required=true)Long shopUnique,
			@RequestParam(value="startTime",required=true)Timestamp startTime,
			@RequestParam(value="endTime",required=true)Timestamp endTime
			){
		Map<String,Object> map=new HashMap<String, Object>();
		map.put("shopUnique", shopUnique);
		map.put("startTime", startTime);
		map.put("endTime", endTime);
		return saleService.cusActivityQuery(map);
	}
	
	/**
	 * 订单信息数量查询
	 * @param map
	 * @return
	 */
	@RequestMapping("/newOrdersCount.do")
	@ResponseBody
	public ShopsResult newOrdersCount(
			@RequestParam(value="shopUnique",required=true)Long shopUnique,
			@RequestParam(value="saleListHandlestate",defaultValue="2")Integer saleListHandlestate
			){
		Map<String,Object> map=new HashMap<String, Object>();
		map.put("shopUnique", shopUnique);
		map.put("saleListHandlestate", saleListHandlestate);
		return saleService.newOrdersCount(map);
	}
	
	/**
	 * 订单详情EXCEL表生成并返回下载地址
	 * @param map
	 * @return
	 */
	@ResponseBody
	@RequestMapping("/saleListExcel.do")
	public void saleListExcel(
			HttpServletRequest request,
			HttpServletResponse response,
			@RequestParam(value="shopUnique",required=true)Long shopUnique,
			String startTime,
			String endTime,
			String orderMessage,
			Integer saleListHandlestate,
			Integer saleListPayment,
			String goodsMessage,
			String goods_kind_parunique,
			String goods_kind_unique
			){
		Map<String,Object> map=new HashMap<String, Object>();
		map.put("shopUnique", shopUnique);
		map.put("shop_unique", shopUnique);
		map.put("startTime", startTime);
		map.put("endTime", endTime);
		if(saleListHandlestate!=null&&saleListHandlestate>0){
			map.put("saleListHnadleState", saleListHandlestate);
		}
		if(saleListPayment!=null&&saleListPayment>0){
			map.put("saleListPayment", saleListPayment);
		}
		if(orderMessage!=null&&!"".equals(orderMessage)){
			map.put("orderMessage", "%"+orderMessage+"%");
		}
		if(null!=goodsMessage&&!"".equals(goodsMessage)){
			map.put("goodsMessage", "%"+goodsMessage+"%");
		}
		if(null!=goods_kind_parunique && !"-1".equals(goods_kind_parunique) && !"".equals(goods_kind_parunique)){
			map.put("goods_kind_parunique", goods_kind_parunique);
		}
		if(null!=goods_kind_unique && !"-1".equals(goods_kind_unique) && !"".equals(goods_kind_unique)){
			map.put("goods_kind_unique", goods_kind_unique);
		}
		
		List<Map<String, Object>> data =saleService.saleListExcel(map, request, shopUnique);
		//查询统计信息
		Map<String, Object> countMap=saleService.queryOrderTotal(map);
		Map<String, Object> newCountMap=new HashMap<String, Object>();
		if(null==data||data.isEmpty()){
			newCountMap.put("sale_list_unique", "订单总数量:"+0);
			newCountMap.put("time", "订单总金额:"+0);
			newCountMap.put("cus_name", "订单总原价:"+0);
			newCountMap.put("cusPhone", "订单总利润:"+0);
			newCountMap.put("sale_list_total2", "订单总提成:"+0);
		}else{
			newCountMap.put("sale_list_unique", "订单总数量:"+data.size());
			BigDecimal saleTotal = new BigDecimal(countMap.getOrDefault("saleTotal", 0).toString());
			BigDecimal retListTotalMoney = new BigDecimal(countMap.getOrDefault("retListTotalMoney", 0).toString());
			newCountMap.put("time", "订单总金额:"+ NumberUtil.sub(saleTotal, retListTotalMoney));
			newCountMap.put("cus_name", "订单总原价:"+countMap.get("purTotal"));
			BigDecimal retListOriginTotal = new BigDecimal(countMap.getOrDefault("retListOriginTotal", 0).toString());
			BigDecimal purTotal = new BigDecimal(countMap.getOrDefault("purTotal", 0).toString());
			newCountMap.put("cusPhone", "订单总利润:"+ NumberUtil.sub(saleTotal, retListOriginTotal, purTotal));
			newCountMap.put("sale_list_total2", "订单总提成:"+countMap.get("commission_sum"));
		}
		data.add(newCountMap);
		loadOutClaimXLS(data,"销售记录Excel",request,response);
	}
	
	public void loadOutClaimXLS(List<Map<String, Object>> caseVoList, final String string,HttpServletRequest request,HttpServletResponse response) {
		try {
			response.setContentType(
					"application/x-msdownload; charset=UTF-8");// 告知浏览器文件类型
			String agent = request.getHeader("user-agent");
			if (agent.contains("Firefox")) {
				response.setHeader(
						"Content-Disposition",
						"attachment;filename="
								+ new String((string + ".xls")
										.getBytes("GB2312"), "ISO-8859-1"));// 设置文件头
			} else {
				response.setHeader(
						"Content-Disposition",
						"attachment;filename="
								+ URLEncoder.encode(string + ".xls", "UTF-8"));// 设置文件头
			}
			OutputStream os = response.getOutputStream();
			LoadOutObjectXLSUtil<Map<String, Object>> objectXLS = new LoadOutObjectXLSUtil<Map<String, Object>>();
			objectXLS.generateXLS(os, caseVoList, new XLSCallBack<Map<String, Object>>() {
				public Object[] getValues(Map<String, Object> tt) {
					String str = "";
					
					String purchase_list_unique ="";
						purchase_list_unique=String.valueOf( tt.get("sale_list_unique"));
					String purchase_list_date = (String) tt.get("time");
					if (StringUtil.blank(purchase_list_date)) {
						purchase_list_date = str;
					}
					String cusName = (String) tt.get("cusName");
					if (StringUtil.blank(cusName)) {
						cusName = str;
					}
					String cusPhone = (String) tt.get("cusPhone");
					if (StringUtil.blank(cusPhone)) {
						cusPhone = str;
					}
					
					String cusAddress = (String) tt.get("cusAddress");
					if(StringUtil.blank(cusAddress)) {
						cusAddress = str;
					}
					
					String saleListTotal = String.valueOf(tt.get("sale_list_total2"));
					if (StringUtil.blank(saleListTotal)) {
						saleListTotal = str;
					}
					String saleListActuallyReceived = String.valueOf(tt.get("sale_list_total"));
					if (StringUtil.blank(saleListActuallyReceived)) {
						saleListActuallyReceived = str;
					}
					String saleListCount = String.valueOf(tt.get("sale_list_totalCount"));
					if (StringUtil.blank(saleListCount)) {
						saleListCount = str;
					}
					String payType = String.valueOf(tt.get("sale_list_payment"));
					if (StringUtil.blank(payType)) {
						payType = str;
					}
					String payMethod=String.valueOf(tt.get("payMethod"));
					if (StringUtil.blank(payMethod)) {
						payMethod = str;
					}
					String payMoney=String.valueOf(tt.get("pay_money"));
					if (StringUtil.blank(payMoney)) {
						payMoney = str;
					}
					String payStatus = String.valueOf(tt.get("sale_list_state"));
					if (StringUtil.blank(payStatus)) {
						payStatus = str;
					}
					
					String sale_list_cashier = MUtil.strObject(tt.get("sale_list_cashier"));
					String staffName = "";
					if(sale_list_cashier != null && !sale_list_cashier.equals("0")){
						staffName = (String) tt.get("staffName");
						if (StringUtil.blank(staffName)) {
							staffName = str;
						}
					}
					
					String commission_sum = String.valueOf(tt.get("commission_sum"));
					if (StringUtil.blank(commission_sum)) {
						commission_sum = str;
					}
					
					String source_type = String.valueOf(tt.get("source_type"));
					if (StringUtil.blank(source_type)) {
						source_type = str;
					}
					return new String[] {purchase_list_unique,purchase_list_date,cusName,cusPhone,cusAddress,saleListTotal,
										saleListActuallyReceived,saleListCount,payMethod,payMoney,payStatus,staffName,source_type,commission_sum};
				}

				public String getTitle() {
					return string;
				}

				public int[] getColumnsWidth() {
					return new int[] { 
										  80 * 80, 80 * 80,80 * 80,80 * 80,80 * 80,
										  80 * 80, 80 * 80,80 * 80,80 * 80,80 * 80,
										  80 * 80,80 * 80,80 * 80,80 *80};
				}

				public String[] getColumnsName() {
					return new String[] { "订单编号","下单日期","收货人","联系方式","收货地址",
										  "订单原价","订单金额","商品总数","支付方式","支付金额",
										  "付款状态","收银员","终端类型","提成"};
				}

				public int[][] MergedRegion() {
					// TODO Auto-generated method stub
					return null;
				}

				public List<Object[]> getheaderValue() {
					// TODO Auto-generated method stub
					return null;
				}

			});
			os.flush();
		} catch (IOException e) {
			e.printStackTrace();
		}
	}
	
	
	/**
	 * 宁宇分店时间段会员消费详情EXCEL表生成并返回下载地址
	 * @param map
	 * @return
	 */
	@ResponseBody
	@RequestMapping("/saleListNYExcel.do")
	public void saleListNYExcel(HttpServletRequest request,HttpServletResponse response,
			String shop_name,String sale_date
			){
		Map<String,Object> map=new HashMap<String, Object>();
		map.put("shop_name", shop_name);
		map.put("start_date", sale_date.split("~")[0].trim());
		map.put("end_date", sale_date.split("~")[1].trim()+" 23:59:59");
		
		List<Map<String, Object>> data =saleService.saleListNYExcel(map);

		loadOutNYClaimXLS(data,"宁宇"+sale_date.split("~")[0].trim()+"-"+sale_date.split("~")[1].trim(),request,response);
	}
	
	public void loadOutNYClaimXLS(List<Map<String, Object>> caseVoList, final String string,HttpServletRequest request,HttpServletResponse response) {
		try {
			response.setContentType("application/x-msdownload; charset=UTF-8");// 告知浏览器文件类型
			String agent = request.getHeader("user-agent");
			if (agent.contains("Firefox")) {
				response.setHeader("Content-Disposition","attachment;filename="
								+ new String((string + ".xls")
										.getBytes("GB2312"), "ISO-8859-1"));// 设置文件头
			} else {
				response.setHeader(
						"Content-Disposition",
						"attachment;filename="+ URLEncoder.encode(string + ".xls", "UTF-8"));// 设置文件头
			}
			OutputStream os = response.getOutputStream();
			LoadOutObjectXLSUtil<Map<String, Object>> objectXLS = new LoadOutObjectXLSUtil<Map<String, Object>>();
			objectXLS.generateXLS(os, caseVoList, new XLSCallBack<Map<String, Object>>() {
				public Object[] getValues(Map<String, Object> tt) {
					String str = "";
					
					String shop_unique = String.valueOf(tt.get("shop_unique"));
					if (StringUtil.blank(shop_unique)) {
						shop_unique = str;
					}
					String shop_name = (String) tt.get("shop_name");
					if (StringUtil.blank(shop_name)) {
						shop_name = str;
					}
					String shop_phone = String.valueOf(tt.get("shop_phone"));
					if (StringUtil.blank(shop_phone)) {
						shop_phone = str;
					}
					String card_deduction = String.valueOf(tt.get("card_deduction"));
					if (StringUtil.blank(card_deduction)) {
						card_deduction = str;
					}
					String pay_money = String.valueOf(tt.get("pay_money"));
					if (StringUtil.blank(pay_money)) {
						pay_money = str;
					}
					
					return new String[] {shop_unique,shop_name,shop_phone,card_deduction,pay_money};
				}

				public String getTitle() {
					return string;
				}

				public int[] getColumnsWidth() {
					return new int[] { 
										  80 * 80, 80 * 80,80 * 80,80 * 80,80 * 80,80 * 80, };
				}

				public String[] getColumnsName() {
					return new String[] { "店铺编号","店铺名称","联系方式","储值卡抵扣金额","总抵扣金额"	};
				}

				public int[][] MergedRegion() {
					// TODO Auto-generated method stub
					return null;
				}

				public List<Object[]> getheaderValue() {
					// TODO Auto-generated method stub
					return null;
				}

			});
			os.flush();
		} catch (IOException e) {
			e.printStackTrace();
		}
	}
	
	/**
	 * 宁宇分店时间段会员消费详情EXCEL表生成并返回下载地址-店铺订单详情
	 * @param map
	 * @return
	 */
	@ResponseBody
	@RequestMapping("/saleListDetailYNExcel.do")
	public void saleListDetailYNExcel(HttpServletRequest request,HttpServletResponse response,
			String shop_unique,String startTime,String endTime){
		Map<String,Object> map=new HashMap<String, Object>();
		map.put("shop_unique", shop_unique);
		map.put("startTime",startTime);
		map.put("endTime",endTime+" 23:59:59");

		 List<Map<String, Object>> data =saleService.saleListDetailYNExcel(map);
		loadOutDetailNYClaimXLS(data,"宁宇"+shop_unique+startTime+"-"+endTime,request,response);
	}
	
	public void loadOutDetailNYClaimXLS(List<Map<String, Object>> caseVoList, final String string,HttpServletRequest request,HttpServletResponse response) {
		try {
			response.setContentType("application/x-msdownload; charset=UTF-8");// 告知浏览器文件类型
			String agent = request.getHeader("user-agent");
			if (agent.contains("Firefox")) {
				response.setHeader("Content-Disposition","attachment;filename="
								+ new String((string + ".xls")
										.getBytes("GB2312"), "ISO-8859-1"));// 设置文件头
			} else {
				response.setHeader(
						"Content-Disposition",
						"attachment;filename="+ URLEncoder.encode(string + ".xls", "UTF-8"));// 设置文件头
			}
			OutputStream os = response.getOutputStream();
			LoadOutObjectXLSUtil<Map<String, Object>> objectXLS = new LoadOutObjectXLSUtil<Map<String, Object>>();
			objectXLS.generateXLS(os, caseVoList, new XLSCallBack<Map<String, Object>>() {
				public Object[] getValues(Map<String, Object> tt) {
					String str = "";
					
					String sale_list_unique = String.valueOf(tt.get("sale_list_unique"));
					if (StringUtil.blank(sale_list_unique)) {
						sale_list_unique = str;
					}
					String shop_name = (String) tt.get("shop_name");
					if (StringUtil.blank(shop_name)) {
						shop_name = str;
					}
					String cus_id = String.valueOf(tt.get("cus_id"));
					if (StringUtil.blank(cus_id)) {
						cus_id = str;
					}
					String cus_phone = String.valueOf(tt.get("cus_phone"));
					if (StringUtil.blank(cus_phone)) {
						cus_phone = str;
					}
					String card_deduction =String.valueOf(tt.get("card_deduction")); 
					if (StringUtil.blank(card_deduction)) {
						card_deduction = str;
					}
					String pay_money =String.valueOf(tt.get("pay_money")); 
					if (StringUtil.blank(pay_money)) {
						pay_money = str;
					}

					String staffName = "";
					staffName = String.valueOf(tt.get("staffName"));
					if(staffName != null && !staffName.equals("")){
						staffName = (String) tt.get("staffName");
						if (StringUtil.blank(staffName)) {
							staffName = str;
						}
					}
					String time = String.valueOf(tt.get("time"));
					if (StringUtil.blank(time)) {
						time = str;
					}
					
					return new String[] {sale_list_unique,shop_name,cus_id,cus_phone,card_deduction,pay_money,staffName,time};
				}

				public String getTitle() {
					return string;
				}

				public int[] getColumnsWidth() {
					return new int[] { 
										  80 * 80, 80 * 80,80 * 80,80 * 80,80 * 80,80 * 80,80 * 80,80 * 80 };
				}

				public String[] getColumnsName() {
					return new String[] { "订单编号","店铺名称","会员编号","顾客手机号","储值卡抵扣金额","总抵扣金额","收银员","消费日期"	};
				}

				public int[][] MergedRegion() {
					// TODO Auto-generated method stub
					return null;
				}

				public List<Object[]> getheaderValue() {
					// TODO Auto-generated method stub
					return null;
				}

			});
			os.flush();
		} catch (IOException e) {
			e.printStackTrace();
		}
	}
	
	@RequestMapping("/printPage.do")
	public String printPage(
			HttpServletRequest request,
			@RequestParam(value="shopUnique",required=true)Long shopUnique,
			Timestamp startTime,
			Timestamp endTime,
			String orderMessage,
			Integer saleListHandlestate,
			Integer saleListPayment,
			String goodsMessage,
			String goods_kind_parunique,
			String goods_kind_unique,
			String cusType
			){
		request.setAttribute("shopUnique", shopUnique);
		request.setAttribute("startTime", startTime);
		request.setAttribute("endTime", endTime);
		request.setAttribute("orderMessage", orderMessage);
		request.setAttribute("saleListHandlestate", saleListHandlestate);
		request.setAttribute("saleListPayment", saleListPayment);
		request.setAttribute("goodsMessage", goodsMessage);
		request.setAttribute("goods_kind_parunique", goods_kind_parunique);
		request.setAttribute("goods_kind_unique", goods_kind_unique);
		request.setAttribute("cusType", cusType);
		return "/WEB-INF/sale/printOrder.jsp";
	}
	/**
	 * @param map
	 * @return
	 */
	@RequestMapping("/printOrderList.do")
	@ResponseBody
	public ShopsResult printOrderList(
			HttpServletRequest request,
			@RequestParam(value="shopUnique",required=true)Long shopUnique,
			String startTime,
			String endTime,
			String orderMessage,
			Integer saleListHandlestate,
			Integer saleListPayment,
			String goodsMessage,
			String goods_kind_parunique,
			String goods_kind_unique,
			String cusType
			){
		Map<String,Object> map=new HashMap<String, Object>();
		map.put("shopUnique", shopUnique);
		map.put("shop_unique", shopUnique);
		map.put("startTime", startTime);
		map.put("endTime", endTime);
		map.put("cusType", cusType);
		if(saleListHandlestate!=null&&saleListHandlestate>0){
			map.put("saleListHnadleState", saleListHandlestate);
		}
		if(saleListPayment!=null&&saleListPayment>0){
			map.put("saleListPayment", saleListPayment);
		}
		if(orderMessage!=null&&!"".equals(orderMessage)){
			map.put("orderMessage", "%"+orderMessage+"%");
		}
		if(null!=goodsMessage&&!"".equals(goodsMessage)){
			try {
				map.put("goodsMessage", "%"+URLDecoder.decode(goodsMessage,"UTF-8")+"%");
			} catch (UnsupportedEncodingException e) {
				e.printStackTrace();
			}
		}
		if(null!=goods_kind_parunique && !"-1".equals(goods_kind_parunique) && !"".equals(goods_kind_parunique)){
			map.put("goods_kind_parunique", goods_kind_parunique);
		}
		if(null!=goods_kind_unique && !"-1".equals(goods_kind_unique) && !"".equals(goods_kind_unique)){
			map.put("goods_kind_unique", goods_kind_unique);
		}
		return saleService.printOrderList(map, request, shopUnique);
	}
	
	
	@RequestMapping("/printFarmPage.do")
	public String printFarmPage(
			HttpServletRequest request,
			Timestamp startTime,
			Timestamp endTime,
			String orderMessage,
			Integer handle_status,
			Integer pay_status
			){
		request.setAttribute("startTime", startTime);
		request.setAttribute("endTime", endTime);
		request.setAttribute("orderMessage", orderMessage);
		request.setAttribute("handle_status", handle_status);
		request.setAttribute("pay_status", pay_status);
		return "/WEB-INF/sale/printFarmOrder.jsp";
	}
	
	
	/**
	 * @param map
	 * @return
	 */
	@RequestMapping("/printFarmOrderList.do")
	@ResponseBody
	public ShopsResult printFarmOrderList(
			HttpServletRequest request,
			String startTime,
			String endTime,
			String orderMessage,
			Integer handle_status,
			Integer pay_status
			){
		Map<String,Object> map=new HashMap<String, Object>();
		map.put("startTime", startTime);
		map.put("endTime", endTime);
		if(handle_status!=null&&handle_status>0){
			map.put("handle_status", handle_status);
		}
		if(pay_status!=null&&pay_status>0){
			map.put("pay_status", pay_status);
		}
		if(orderMessage!=null&&!"".equals(orderMessage)){
			map.put("orderMessage", "%"+orderMessage+"%");
		}
		return saleService.printFarmOrderList(map, request);
	}
	
	
	/**
	 * 订票订单总览界面：页数查询
	 * @param map
	 * @return
	 */
	@RequestMapping("/queryListPages.do")
	@ResponseBody
	public ShopsResult queryListPages(
			@RequestParam(value="pageSize",defaultValue="30")Integer pageSize,
			@RequestParam(value="shopUnique")Long shopUnique,
			@RequestParam(value="startTime",required=true)String startTime,
			@RequestParam(value="endTime",required=true)String endTime,
			@RequestParam(value="handleStatus",required=true)Integer handleStatus,
			@RequestParam(value="listType",required=true)Integer listType			
			){
		Map<String,Object> map=new HashMap<String, Object>();
		map.put("pageSize", pageSize);
		map.put("shopUnique", shopUnique);
		map.put("startTime", startTime);
		map.put("endTime", endTime);
		
		if(listType!=-1){
			map.put("listType", listType);
		}
		if(handleStatus!=-1){
			map.put("handleStatus", handleStatus);
		}
		return saleService.queryListPages(map);
	}
	/**
	 * 店铺订单总览界面：分页查询订单
	 * @param map
	 * @return
	 */
	@RequestMapping("/queryListByPage.do")
	@ResponseBody
	public ShopsResult queryListByPage(
			@RequestParam(value="pageSize",defaultValue="30")Integer pageSize,
			@RequestParam(value="shopUnique")Long shopUnique,
			@RequestParam(value="startTime",required=true)String startTime,
			@RequestParam(value="endTime",required=true)String endTime,
			@RequestParam(value="handleStatus",required=true)Integer handleStatus,
			@RequestParam(value="listType",required=true)Integer listType,
			@RequestParam(value="pageNum",required=true)Integer pageNum,
			Integer order,
			Integer orderType
			){
		Map<String,Object> map=new HashMap<String, Object>();
		map.put("pageSize", pageSize);
		map.put("shopUnique", shopUnique);
		map.put("startTime", startTime);
		map.put("endTime", endTime);
		if(listType!=-1){
			map.put("listType", listType);
		}
		if(handleStatus!=-1){
			map.put("handleStatus", handleStatus);
		}
		if(order==1){
			map.put("order", "saleListUnique");
		}
		if(order==2){
			map.put("order", "actuallyReceived");
		}
		if(order==3){
			map.put("order", "listTime");
		}
		if(order==4){
			map.put("order", "listType");
		}
		if(order==5){
			map.put("order", "handleStatus");
		}
		if(order==6){
			map.put("order", "listCount");
		}
		if(orderType==1){
			map.put("orderType", "DESC");
		}
		
		if(orderType==2){
			map.put("orderType", "ASC");
		}
		map.put("startNum", (pageNum-1)*pageSize);
		return saleService.queryListByPage(map);
	}
	
	/**
	 * 订单详情查询
	 * @param map
	 * @return
	 */
	@RequestMapping("/queryListDetail.do")
	@ResponseBody
	public ShopsResult queryListDetail(@RequestParam(value="saleListUnique",required=true)String saleListUnique){
		Map<String,Object> map=new HashMap<String, Object>();
		map.put("saleListUnique", saleListUnique);
		return saleService.queryListDetail(map);
	}
	
	@RequestMapping("/toImportSaleList.do")
	public String toImportSaleList(){
		return "/WEB-INF/sale/importSaleList.jsp";
	}
	
	/**
	 * 下载订单（包含详情）
	 */
	@RequestMapping("/saleListExcelDetail.do")
	public void saleListExcelDetail(
			@RequestParam(value="shopUnique",required=true)Long shopUnique,
			String startTime,
			String endTime,
			String orderMessage,
			Integer saleListHandlestate,
			Integer saleListPayment,
			Integer saleType,
			String goodsMessage,
			String goods_kind_parunique,
			String goods_kind_unique,
			Integer staff_id,
			String cusType,
			HttpServletRequest request,
			HttpServletResponse response
			){
		response.reset();
		response.setHeader("Connection", "close");
		response.setHeader("Content-Type", "application/octet-stream");
		Map<String,Object> map=new HashMap<String,Object>();
		map.put("startTime", startTime);
		map.put("endTime", endTime);
		map.put("shopUnique", shopUnique);
		map.put("shop_unique", shopUnique);
		map.put("cusType", cusType);
		map.put("saleType", saleType);

		if(orderMessage!=null&&!"".equals(orderMessage)){
			map.put("orderMessage", "%"+orderMessage+"%");
		}
		if(null!=goodsMessage&&!"".equals(goodsMessage)){
			map.put("goodsMessage", "%"+goodsMessage+"%");
		}
		map.put("saleListHandlestate", saleListHandlestate);
		map.put("sale_list_handlestate", saleListHandlestate);
		map.put("saleListPayment", saleListPayment);
		if(null!=goods_kind_parunique && !"-1".equals(goods_kind_parunique) && !"".equals(goods_kind_parunique)){
			map.put("goods_kind_parunique", goods_kind_parunique);
		}
		if(null!=goods_kind_unique && !"-1".equals(goods_kind_unique) && !"".equals(goods_kind_unique)){
			map.put("goods_kind_unique", goods_kind_unique);
		}
		map.put("staffId", staff_id);
		List<Map<String,Object>> list=saleService.saleListExcelDetail(map);
		List<SaleListMain> detailList = saleService.saleListExcelDetailClass(map);
		System.out.println(detailList);
		System.out.println("记录"+list.size());
		@SuppressWarnings("deprecation")
		String filePath=request.getRealPath("/");
		System.out.println(filePath);
		File file=new File(filePath+File.separator+"销售统计（详）.xls");
		List<TitleForExcel> tl=new ArrayList<>();
		if(list==null||list.isEmpty()){
		}else{
			Map<String, Object> goodsParams = new HashMap<>();
			goodsParams.put("shopUnique", shopUnique);
			goodsParams.put("goodsBarcodeSet", list.stream().filter(v -> ObjectUtil.isNotEmpty(v.get("goodsBarcode"))).map(v -> v.get("goodsBarcode")).collect(Collectors.toSet()));
			List<Map<String, Object>> goodsDetailList = saleService.selectGoodsDetailInfo(goodsParams);
			if (CollectionUtil.isNotEmpty(goodsDetailList)) {
				Map<String, Map<String, Object>> goodsBarcodeMap = new HashMap<>();
				goodsDetailList.stream().forEach(m -> {
					goodsBarcodeMap.put(m.get("goods_barcode").toString(), m);
				});
				for (SaleListMain sm : detailList) {
					sm.getListDetail().forEach(v -> {
						Map<String, Object> gbMap = goodsBarcodeMap.get(v.getGoodsBarcode());
						if (ObjectUtil.isNotNull(gbMap)) {
							v.setGoodsPurprice(Double.parseDouble(gbMap.getOrDefault("goods_in_price", 0).toString()));
							v.setFirstKindName(gbMap.getOrDefault("firstKindName", StrUtil.EMPTY).toString());
							v.setSecondKindName(gbMap.getOrDefault("secondKindName", StrUtil.EMPTY).toString());
							v.setStockPrice((BigDecimal) gbMap.getOrDefault("stock_price", BigDecimal.ZERO));
						}
					});
				}
			}
//			"订单编号","下单日期","顾客姓名","联系方式",
//			  "应收金额","实收金额","商品总数","支付方式","支付金额",
//			  "支付状态","收银员","提成"
			TitleForExcel t1=new TitleForExcel();
			t1.setName("saleListUnique");
			t1.setValue("订单编号");
			tl.add(t1);
			TitleForExcel t2=new TitleForExcel();
			t2.setName("saleListDatetime");
			t2.setValue("下单日期");
			tl.add(t2);
			TitleForExcel t3=new TitleForExcel();
			t3.setName("cusName");
			t3.setValue("收货人");
			tl.add(t3);
			TitleForExcel t4=new TitleForExcel();
			t4.setName("cusPhone");
			t4.setValue("联系方式");
			tl.add(t4);
			TitleForExcel t5 = new TitleForExcel();
			t5.setName("saleListAddress");
			t5.setValue("配送地址");
			tl.add(t5);
			TitleForExcel t6=new TitleForExcel();
			t6.setName("saleListTotal");
			t6.setValue("订单原价");
			tl.add(t6);
			TitleForExcel t7=new TitleForExcel();
			t7.setName("saleListActuallyReceived");
			t7.setValue("订单金额");
			tl.add(t7);

			TitleForExcel t11=new TitleForExcel();
			t11.setName("firstKindName");
			t11.setValue("商品大类");
			tl.add(t11);

			TitleForExcel t12=new TitleForExcel();
			t12.setName("secondKindName");
			t12.setValue("商品小类");
			tl.add(t12);

			TitleForExcel t13=new TitleForExcel();
			t13.setName("goodsBarcode");
			t13.setValue("商品条码");
			tl.add(t13);
			

			TitleForExcel t14=new TitleForExcel();
			t14.setName("goodsName");
			t14.setValue("商品名称");
			tl.add(t14);
			
			TitleForExcel t15=new TitleForExcel();
			t15.setName("goodsSalePrice");
			t15.setValue("销售单价");
			tl.add(t15);
			
			TitleForExcel t16=new TitleForExcel();
			t16.setName("goodsCount");
			t16.setValue("销售数量");
			tl.add(t16);

			TitleForExcel t17=new TitleForExcel();
			t17.setName("goodsPurprice");
			t17.setValue("库存均价");
			tl.add(t17);

			TitleForExcel t18=new TitleForExcel();
			t18.setName("stockPrice");
			t18.setValue("最近入库价");
			tl.add(t18);

			ExcelUtil.ExcelForListObject(detailList, file, tl);
		}
//		
		OutputStream os=null;
		FileInputStream fis =null;
		int bytesRead = 0;
		byte[] buffer = new byte[8192];
		try {
			if(!file.exists()){
				file.createNewFile();
			}
			response.setContentType("application/x-msdownload; charset=UTF-8");// 告知浏览器文件类型
			String agent = request.getHeader("user-agent");
			if (agent.contains("Firefox")) {
				response.setHeader(
						"Content-Disposition",
						"attachment;filename="
								+ new String(("销售统计（详）.xls")
										.getBytes("GB2312"), "ISO-8859-1"));// 设置文件头
			} else {
				response.setHeader(
						"Content-Disposition",
						"attachment;filename="
								+ URLEncoder.encode("销售统计（详）.xls", "UTF-8"));// 设置文件头
			}
			fis=new FileInputStream(file);
			os=response.getOutputStream();
			while ((bytesRead = fis.read(buffer, 0, 8192)) != -1) {
				os.write(buffer, 0, bytesRead);
			};
			os.flush();
		} catch (IOException e) {
			e.printStackTrace();
		}finally{
			try {
				if(os!=null){
					os.close();
				}
				if(fis!=null){
					fis.close();
				}
			} catch (IOException e) {
				e.printStackTrace();
			}
		}
	}
	
	/**
	 * 评价列表
	 */
	@RequestMapping("/getEvaluateList.do")
	@ResponseBody
	public PurResult getEvaluateList(HttpServletRequest request,
			@RequestParam(value="orderName" ,defaultValue="create_time")String orderName, 
			@RequestParam(value="orderType",defaultValue="desc")String orderType,
			@RequestParam(value="page",defaultValue="1")Integer page,
			@RequestParam(value="limit",defaultValue="15")Integer limit
			){		
		PurResult result = new PurResult();
		try {
			Map<String, Object> params = HandleMessyCode.handle(ServletsUtil.getParameters(request));
			params.put("limit", limit);
			params.put("page", (page-1)*limit);
			params.put("orderName", orderName);
			params.put("orderType", orderType);
			result=saleService.getEvaluateList(params);
		} catch (Exception e) {
			result.setStatus(0);
			result.setMsg("查询失败!");
			e.printStackTrace();
		}		
		
		return result;
	}
	
	/**
	 * 跳转到评价页面
	 */
	@RequestMapping("/toEvaluatePage.do")
	public String toEvaluatePage(){
		return "/WEB-INF/sale/evaluateList.jsp";
	}
	
	/**
	 * 跳转到评论回复页面
	 */
	@RequestMapping("/toReplyPage.do")
	public String toReplyPage(String shop_unique, String sale_list_unique,String evaluate_id,Model model){
		model.addAttribute("shop_unique", shop_unique);
		model.addAttribute("sale_list_unique", sale_list_unique);
		Map<String, Object> params=new HashMap<String, Object>();
		params.put("evaluate_id", evaluate_id);
		List<Map<String, Object>> imgList=saleService.getEvaluateImage(params);
		model.addAttribute("imgList", imgList);
		return "/WEB-INF/sale/evaluateDetail.jsp";
	}
	
	@RequestMapping("/updateEvaluate.do")
	@ResponseBody
	public ShopsResult updateEvaluate(HttpServletRequest request){
		Map<String, Object> params = HandleMessyCode.handle(ServletsUtil.getParameters(request));
		return saleService.updateEvaluate(params);
	}
	
	@RequestMapping("/staffSaleStatis.do")
	public String staffSaleStatis() {
		return "/WEB-INF/sale/staffSaleStatis.jsp";
	}
	
	/*
	 * 
	 * 农产品订单
	 */
	@RequestMapping("/queryFarmListsAll.do")
	@ResponseBody
	public PurResult querySaleListsAll(
//			@RequestParam(value="shop_unique")String shop_unique, 
			String orderMessage, 
			Integer handle_status,
			String startTime,
			String endTime, 
			Integer secretary_id, 
			Integer pay_status,
			@RequestParam(value="orderName" ,defaultValue="sale_list_datetime")String orderName, 
			@RequestParam(value="orderType",defaultValue="desc")String orderType,
			@RequestParam(value = "page", defaultValue = "1") int page,
			@RequestParam(value = "limit", defaultValue = "8") int pageSize,
			Integer staffId,
			String goodsMessage,
			String goods_kind_parunique,
			String goods_kind_unique,
			Integer cusType,
			Integer paymentMethod
			){
		return saleService.queryFarmListsAll(orderMessage, handle_status, startTime, endTime, secretary_id, pay_status, orderName, orderType, page, pageSize,staffId,
				goodsMessage,goods_kind_parunique,goods_kind_unique,cusType,paymentMethod);
	}
	
	
	/**
	 * 农产品订单详情EXCEL表生成并返回下载地址
	 * @param map
	 * @return
	 */
	@ResponseBody
	@RequestMapping("/farmListExcel.do")
	public void farmListExcel(
			HttpServletRequest request,
			HttpServletResponse response,
			String startTime,
			String endTime,
			String orderMessage,
			Integer handle_status,
			Integer pay_status
			){
		Map<String,Object> map=new HashMap<String, Object>();
		map.put("startTime", startTime);
		map.put("endTime", endTime);
		if(handle_status!=null&&handle_status>0){
			map.put("handle_status", handle_status);
		}
		if(pay_status!=null&&pay_status>0){
			map.put("pay_status", pay_status);
		}
		if(orderMessage!=null&&!"".equals(orderMessage)){
			map.put("orderMessage", "%"+orderMessage+"%");
		}
		
		List<Map<String, Object>> data =saleService.farmListExcel(map, request);
		//查询统计信息
		Map<String, Object> countMap=saleService.queryFarmOrderDetailSum(map);
		Map<String, Object> newCountMap=new HashMap<String, Object>();
		if(null==data||data.isEmpty()){
			newCountMap.put("sup_order_unique", "订单总数量:"+0);
			newCountMap.put("sale_list_datetime", "订单总金额:"+0);
			newCountMap.put("sale_list_name", "订单总原价:"+0);
		}else{
			newCountMap.put("sup_order_unique", "订单总数量:"+data.size());
			newCountMap.put("sale_list_datetime", "订单总金额:"+countMap.get("saleTotal"));
			newCountMap.put("sale_list_name", "订单总原价:"+countMap.get("purTotal"));
		}
		data.add(newCountMap);
		loadFarmOutClaimXLS(data,"销售记录Excel",request,response);
	}
	
	public void loadFarmOutClaimXLS(List<Map<String, Object>> caseVoList, final String string,HttpServletRequest request,HttpServletResponse response) {
		try {
			response.setContentType(
					"application/x-msdownload; charset=UTF-8");// 告知浏览器文件类型
			String agent = request.getHeader("user-agent");
			if (agent.contains("Firefox")) {
				response.setHeader(
						"Content-Disposition",
						"attachment;filename="
								+ new String((string + ".xls")
										.getBytes("GB2312"), "ISO-8859-1"));// 设置文件头
			} else {
				response.setHeader(
						"Content-Disposition",
						"attachment;filename="
								+ URLEncoder.encode(string + ".xls", "UTF-8"));// 设置文件头
			}
			OutputStream os = response.getOutputStream();
			LoadOutObjectXLSUtil<Map<String, Object>> objectXLS = new LoadOutObjectXLSUtil<Map<String, Object>>();
			objectXLS.generateXLS(os, caseVoList, new XLSCallBack<Map<String, Object>>() {
				public Object[] getValues(Map<String, Object> tt) {
					String str = "";
					
					String purchase_list_unique ="";
						purchase_list_unique=String.valueOf( tt.get("sup_order_unique"));
					String purchase_list_date = (String) tt.get("sale_list_datetime");
					if (StringUtil.blank(purchase_list_date)) {
						purchase_list_date = str;
					}
					String cusName = (String) tt.get("sale_list_name");
					if (StringUtil.blank(cusName)) {
						cusName = str;
					}
					String cusPhone = (String) tt.get("sale_list_phone");
					if (StringUtil.blank(cusPhone)) {
						cusPhone = str;
					}
					String saleListTotal = String.valueOf(tt.get("sub_list_total"));
					if (StringUtil.blank(saleListTotal)) {
						saleListTotal = str;
					}
					String saleListActuallyReceived = String.valueOf(tt.get("sub_list_total"));
					if (StringUtil.blank(saleListActuallyReceived)) {
						saleListActuallyReceived = str;
					}
					String saleListCount = String.valueOf(tt.get("detail_count"));
					if (StringUtil.blank(saleListCount)) {
						saleListCount = str;
					}
					String payType = String.valueOf(tt.get("sale_list_payment"));
					if (StringUtil.blank(payType)) {
						payType = str;
					}
					String payMethod=String.valueOf(tt.get("pay_method"));
					if (StringUtil.blank(payMethod)) {
						payMethod = str;
					}
					String payMoney=String.valueOf(tt.get("pay_money"));
					if (StringUtil.blank(payMoney)) {
						payMoney = str;
					}
					String payStatus = String.valueOf(tt.get("pay_status"));
					if (StringUtil.blank(payStatus)) {
						payStatus = str;
					}
					return new String[] {purchase_list_unique,purchase_list_date,cusName,cusPhone,saleListTotal,
										saleListActuallyReceived,saleListCount,payMethod,payMoney,payStatus};
				}

				public String getTitle() {
					return string;
				}

				public int[] getColumnsWidth() {
					return new int[] { 
										  80 * 80, 80 * 80,80 * 80,80 * 80,
										  80 * 80, 80 * 80,80 * 80,80 * 80,80 * 80,
										  80 * 80,80 * 80,80 * 80};
				}

				public String[] getColumnsName() {
					return new String[] { "订单编号","下单日期","顾客姓名","联系方式",
										  "应收金额","实收金额","商品总数","支付方式","支付金额",
										  "支付状态"};
				}

				public int[][] MergedRegion() {
					// TODO Auto-generated method stub
					return null;
				}

				public List<Object[]> getheaderValue() {
					// TODO Auto-generated method stub
					return null;
				}

			});
			os.flush();
		} catch (IOException e) {
			e.printStackTrace();
		}
	}
	
	
	/**
	 * 农产品订单详情EXCEL表生成并返回下载地址
	 * @param map
	 * @return
	 */
	@ResponseBody
	@RequestMapping("/farmListExcelDetail.do")
	public void farmListExcelDetail(
			HttpServletRequest request,
			HttpServletResponse response,
			String startTime,
			String endTime,
			String orderMessage,
			Integer handle_status,
			Integer pay_status
			){
		Map<String,Object> map=new HashMap<String, Object>();
		map.put("startTime", startTime);
		map.put("endTime", endTime);
		if(handle_status!=null&&handle_status>0){
			map.put("handle_status", handle_status);
		}
		if(pay_status!=null&&pay_status>0){
			map.put("pay_status", pay_status);
		}
		if(orderMessage!=null&&!"".equals(orderMessage)){
			map.put("orderMessage", "%"+orderMessage+"%");
		}
		
		List<Map<String, Object>> data =saleService.farmListExcel(map, request);
		//查询统计信息
		Map<String, Object> countMap=saleService.queryFarmOrderDetailSum(map);
		Map<String, Object> newCountMap=new HashMap<String, Object>();
		if(null==data||data.isEmpty()){
			newCountMap.put("sup_order_unique", "订单总数量:"+0);
			newCountMap.put("sale_list_datetime", "订单总金额:"+0);
			newCountMap.put("sale_list_name", "订单总原价:"+0);
		}else{
			newCountMap.put("sup_order_unique", "订单总数量:"+data.size());
			newCountMap.put("sale_list_datetime", "订单总金额:"+countMap.get("saleTotal"));
			newCountMap.put("sale_list_name", "订单总原价:"+countMap.get("purTotal"));
		}
		data.add(newCountMap);
		loadFarmDOutClaimXLS(data,"销售记录详情Excel",request,response);
	}
	
	public void loadFarmDOutClaimXLS(List<Map<String, Object>> caseVoList, final String string,HttpServletRequest request,HttpServletResponse response) {
		try {
			response.setContentType(
					"application/x-msdownload; charset=UTF-8");// 告知浏览器文件类型
			String agent = request.getHeader("user-agent");
			if (agent.contains("Firefox")) {
				response.setHeader(
						"Content-Disposition",
						"attachment;filename="
								+ new String((string + ".xls")
										.getBytes("GB2312"), "ISO-8859-1"));// 设置文件头
			} else {
				response.setHeader(
						"Content-Disposition",
						"attachment;filename="
								+ URLEncoder.encode(string + ".xls", "UTF-8"));// 设置文件头
			}
			OutputStream os = response.getOutputStream();
			LoadOutObjectXLSUtil<Map<String, Object>> objectXLS = new LoadOutObjectXLSUtil<Map<String, Object>>();
			objectXLS.generateXLS(os, caseVoList, new XLSCallBack<Map<String, Object>>() {
				public Object[] getValues(Map<String, Object> tt) {
					String str = "";
					
					String purchase_list_unique ="";
						purchase_list_unique=String.valueOf( tt.get("sup_order_unique"));
					String purchase_list_date = (String) tt.get("sale_list_datetime");
					if (StringUtil.blank(purchase_list_date)) {
						purchase_list_date = str;
					}
					String cusName = (String) tt.get("sale_list_name");
					if (StringUtil.blank(cusName)) {
						cusName = str;
					}
					String cusPhone = (String) tt.get("sale_list_phone");
					if (StringUtil.blank(cusPhone)) {
						cusPhone = str;
					}
					String saleListTotal = String.valueOf(tt.get("sub_list_total"));
					if (StringUtil.blank(saleListTotal)) {
						saleListTotal = str;
					}
					String saleListActuallyReceived = String.valueOf(tt.get("sub_list_total"));
					if (StringUtil.blank(saleListActuallyReceived)) {
						saleListActuallyReceived = str;
					}
					String saleListCount = String.valueOf(tt.get("detail_count"));
					if (StringUtil.blank(saleListCount)) {
						saleListCount = str;
					}
					String payType = String.valueOf(tt.get("sale_list_payment"));
					if (StringUtil.blank(payType)) {
						payType = str;
					}
					String payMethod=String.valueOf(tt.get("pay_method"));
					if (StringUtil.blank(payMethod)) {
						payMethod = str;
					}
					String payMoney=String.valueOf(tt.get("pay_money"));
					if (StringUtil.blank(payMoney)) {
						payMoney = str;
					}
					String payStatus = String.valueOf(tt.get("pay_status"));
					if (StringUtil.blank(payStatus)) {
						payStatus = str;
					}
					String goods_barcode = String.valueOf(tt.get("goods_barcode"));
					if (StringUtil.blank(goods_barcode)) {
						goods_barcode = str;
					}
					String goods_name = String.valueOf(tt.get("goods_name"));
					if (StringUtil.blank(goods_name)) {
						goods_name = str;
					}
					String secretary_name = String.valueOf(tt.get("secretary_name"));
					if (StringUtil.blank(secretary_name)) {
						secretary_name = str;
					}
					return new String[] {purchase_list_unique,purchase_list_date,cusName,cusPhone,goods_barcode,goods_name,secretary_name,saleListTotal,
										saleListActuallyReceived,saleListCount,payMethod,payMoney,payStatus};
				}

				public String getTitle() {
					return string;
				}

				public int[] getColumnsWidth() {
					return new int[] { 
										  80 * 80, 80 * 80,80 * 80,80 * 80,
										  80 * 80, 80 * 80,80 * 80,80 * 80,80 * 80,
										  80 * 80,80 * 80,80 * 80,80 * 80};
				}

				public String[] getColumnsName() {
					return new String[] { "订单编号","下单日期","顾客姓名","联系方式","商品条码","商品名称","第一书记",
										  "应收金额","实收金额","商品总数","支付方式","支付金额",
										  "支付状态"};
				}

				public int[][] MergedRegion() {
					// TODO Auto-generated method stub
					return null;
				}

				public List<Object[]> getheaderValue() {
					// TODO Auto-generated method stub
					return null;
				}

			});
			os.flush();
		} catch (IOException e) {
			e.printStackTrace();
		}
	}
	
}
