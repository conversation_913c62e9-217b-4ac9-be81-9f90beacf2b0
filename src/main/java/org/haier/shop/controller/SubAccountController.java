package org.haier.shop.controller;

import org.haier.shop.params.SubAccountAddOrderParam;
import org.haier.shop.service.SubAccountServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

/**
 * @ClassName SubAccountController
 * @Description 子账簿controller
 * <AUTHOR>
 * @Date 2025/3/29 15:30
 * @Version 1.0
 */

@RequestMapping("/subAccount")
@Controller
public class SubAccountController {

    @Autowired
    private SubAccountServiceImpl subAccountService;

    @RequestMapping("/addOrder.do")
    @ResponseBody
    public String addOrder(@RequestBody SubAccountAddOrderParam param) {
      return   subAccountService.addOrder(param);
    }



}