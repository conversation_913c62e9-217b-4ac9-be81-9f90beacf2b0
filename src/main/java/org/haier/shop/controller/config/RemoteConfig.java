package org.haier.shop.controller.config;

import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.io.Serializable;

@Component
public class RemoteConfig implements Serializable {

    private static final long serialVersionUID = 1L;

    private String aiUrl;

    public static String AIURL;

    @PostConstruct
    public void init() {
        System.out.println("初始化AI数据" + this.aiUrl);
        AIURL = this.aiUrl;
    }

    public String getAiUrl() {
        return aiUrl;
    }
    public void setAiUrl(String aiUrl) {
        this.aiUrl = aiUrl;
    }
}
