package org.haier.shop.controller.config;

import java.io.IOException;
import java.io.InputStream;
import java.util.Properties;


/**
 * <一句话功能简述>
 * <功能详细描述>配置信息
 * 
 * <AUTHOR>
 * @version  [版本号, 2014-8-29]
 * @see  [相关类/方法]
 * @since  [产品/模块版本]
 */
public class SwiftpassConfig {
    
    /**
     * 交易密钥
     */
    public static String key ;
    
    /**
     * 商户号
     */
    public static String mch_id;
    
    /**
     * 请求url
     */
    public static String req_url;
    
    /**
     * 通知url
     */
    public static String notify_url;
    public static String notify_url_WJ;
    
    public static String ORDERSETTLEMENT;
    
    static{
        Properties prop = new Properties();   
        InputStream in = SwiftpassConfig.class.getResourceAsStream("/config.properties");   
        try {   
            prop.load(in);   
            key = prop.getProperty("key").trim();   
            mch_id = prop.getProperty("mch_id").trim();   
            req_url = prop.getProperty("req_url").trim();   
            notify_url = prop.getProperty("notify_url").trim();
            notify_url_WJ = prop.getProperty("notify_url_WJ").trim();
            ORDERSETTLEMENT = prop.getProperty("ORDERSETTLEMENT");
        } catch (IOException e) {   
            e.printStackTrace();   
        } 
    }
}
