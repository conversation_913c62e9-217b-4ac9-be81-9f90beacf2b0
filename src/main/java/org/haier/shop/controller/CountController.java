package org.haier.shop.controller;

import java.sql.Timestamp;

import javax.annotation.Resource;

import org.haier.shop.service.CountService;
import org.haier.shop.util.ShopsResult;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

@RequestMapping("/count")
@Controller
public class CountController {
	@Resource
	private CountService conService;
	
	/**
	 * 各区域的销售统计
	 * @param startTime
	 * @param endTime
	 * @return
	 */
	@RequestMapping("/statisByArea.do")
	@ResponseBody
	public ShopsResult statisByArea(Timestamp startTime,Timestamp endTime){
		return conService.cusCountByActArea(startTime, endTime);
	}
	
	/**
	 * 各时间段内活跃用户数量
	 * @param startTime
	 * @param endTime
	 * @return
	 */
	@RequestMapping("/cusCountByActive.do")
	@ResponseBody
	public ShopsResult cusCountByActive(Timestamp startTime,Timestamp endTime){
		return conService.cusCountByActive(startTime, endTime);
	}
}
