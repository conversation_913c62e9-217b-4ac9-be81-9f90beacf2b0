package org.haier.shop.controller;

import java.util.List;
import java.util.Map;

import javax.servlet.http.HttpServletRequest;

import org.haier.shop.entity.SysAction;
import org.haier.shop.service.SysActionService;
import org.haier.shop.util.HandleMessyCode;
import org.haier.shop.util.PurResult;
import org.haier.shop.util.ServletsUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

@Controller
@RequestMapping("/sys/action")
public class SysActionController {
    private static final Logger logger = LoggerFactory.getLogger(SysActionController.class);
    
    @Autowired
    private SysActionService service;
    
    @RequestMapping(value = "/getActionList.do")
    public String getActionList(String code,Model model){
    	logger.info("后台管理-获取菜单操作列表");
    	model.addAttribute("code", code);
        return "/WEB-INF/sys/action/actionList.jsp";
    }
    
    @RequestMapping(value = "/addpage.do")
    public String addpage(String code,Model model){
    	logger.info("后台管理-打开添加菜单操作页面");
    	model.addAttribute("code", code);
        return "/WEB-INF/sys/action/addAction.jsp";
    }
    
    @RequestMapping(value="/queryList.do")
	@ResponseBody
	public PurResult queryList(
			HttpServletRequest request,
			@RequestParam(value = "page", defaultValue = "0") int page,
			@RequestParam(value = "limit", defaultValue = "8") int pageSize){
    	Map<String, Object> params = HandleMessyCode.handle(ServletsUtil.getParameters(request));
		params.put("limit", pageSize);
		params.put("page", (page-1)*pageSize);
		
		PurResult result = new PurResult();
		try {
			List<SysAction> list = service.quertList(params);
	    	Integer count = service.quertListCount(params);
	    	result.setStatus(1);
			result.setMsg("成功");
			result.setCount(count);
			result.setData(list);
		} catch (Exception e) {
			logger.error("列表查询异常：",e);
			result.setStatus(0);
			result.setMsg("异常");
		}
		return result;
	}
    
    @RequestMapping(value = "/add.do")
    @ResponseBody
    public PurResult add(SysAction action){
    	logger.info("后台管理-添加菜单操作");
    	PurResult result = new PurResult();
    	try {
    		SysAction sysAction = service.getAction(action);
    		if(sysAction != null){
    			result.setStatus(2);
    			result.setMsg("操作action已存在");
    	        return result;
    		}
    		service.insert(action);
    		result.setStatus(1);
			result.setMsg("成功");
		} catch (Exception e) {
			logger.info("后台管理-添加菜单操作异常："+e.getMessage());
			result.setStatus(0);
			result.setMsg("异常");
		}
        return result;
    }
    
    @RequestMapping(value = "/getAction.do")
    public String getAction(String code,Model model){
    	logger.info("后台管理-获取菜单操作详情");
    	try {
    		SysAction action = new SysAction();
    		action.setCode(code);
    		action = service.getAction(action);
    		model.addAttribute("action",action);
		} catch (Exception e) {
			logger.info("后台管理-获取菜单操作详情异常："+e.getMessage());
			return "/error";
		}
		return "/WEB-INF/sys/action/updateAction.jsp";
    }
    
    @RequestMapping(value = "/update.do")
    @ResponseBody
    public PurResult update(SysAction action){
    	logger.info("后台管理-修改菜单操作");
    	PurResult result = new PurResult();
    	try {
    		SysAction param = new SysAction();
    		param.setCode(action.getCode());
    		SysAction sysAction = service.getAction(param);
    		if(!action.getAction_param().equals(sysAction.getAction_param())){
    			SysAction param1 = new SysAction();
    			param1.setAction_param(action.getAction_param());
    			SysAction result1 = service.getAction(param1);
    			if(result1 != null){
    				result.setStatus(2);
        			result.setMsg("操作action已存在");
        	        return result;
        		}
    		}
			service.update(action);
			result.setStatus(1);
			result.setMsg("成功");
		} catch (Exception e) {
			logger.info("后台管理-修改菜单操作异常："+e.getMessage());
			result.setStatus(0);
			result.setMsg("异常");
		}
        return result;
    }
    
    @RequestMapping(value = "/delete.do")
    @ResponseBody
    public PurResult delete(String code){
    	logger.info("后台管理-删除菜单操作");
    	PurResult result = new PurResult();
    	try {
    		service.delete(code);
    		result.setStatus(1);
			result.setMsg("成功");
		} catch (Exception e) {
			logger.info("后台管理-删除菜单操作异常："+e.getMessage());
			result.setStatus(0);
			result.setMsg("异常");
		}
        return result;
    }
}