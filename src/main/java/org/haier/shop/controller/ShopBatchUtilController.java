package org.haier.shop.controller;

import cn.hutool.extra.qrcode.QrCodeUtil;
import org.haier.shop.entity.ShopsEntity;
import org.haier.shop.params.shop.ShopBatchPayCodeParams;
import org.haier.shop.params.shop.ShopBatchRegisterParams;
import org.haier.shop.service.ShopBatchUtilService;
import org.haier.shop.service.ShopService;
import org.haier.shop.util.ShopsResult;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.ResourceLoader;
import org.springframework.http.MediaType;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.imageio.ImageIO;
import javax.servlet.http.HttpServletRequest;
import java.awt.*;
import java.awt.image.BufferedImage;
import java.io.File;
import java.util.List;

/**
 * @Description 店铺批量工具
 * @ClassName ShopBatchUtilController
 * <AUTHOR>
 * @Date 2024/6/12 16:19
 **/
@RestController
@RequestMapping("/shopsBatch")
public class ShopBatchUtilController {

    @Resource
    private ShopBatchUtilService shopBatchUtilService;
    @Resource
    private ShopService shopService;
    @Autowired
    private ResourceLoader resourceLoader;

    /**
     * 批量注册店铺
     * @param request
     * @return
     */
    @RequestMapping(value = "/register.do", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    @ResponseBody
    public ShopsResult shopBatchRegister(HttpServletRequest request, @Validated ShopBatchRegisterParams params) {
        return shopBatchUtilService.shopBatchRegister(request, params);
    }

    /**
     * 批量生产支付码
     * @param request
     * @return
     */
    @GetMapping("/batchPayCode.do")
    @ResponseBody
    public ShopsResult test(HttpServletRequest request, @Validated ShopBatchPayCodeParams params) {
        List<ShopsEntity> list = shopService.createBatchShopPayCode(params.getStartShopId(), params.getEndShopId());
        for (ShopsEntity entity: list) {
            createShopPayCode(request, String.valueOf(entity.getShopUnique()), entity.getShopName(), 800);
        }
        return ShopsResult.ok(list.size());
    }

    private void createShopPayCode(HttpServletRequest request, String shopUnique, String shopName, Integer imageSize) {
        try {
            //生成文件并返回进度
            String absPath = this.getClass().getClassLoader().getResource("../../").getPath();
            String filePathDetail = File.separator + "file" + File.separator;

            System.out.println(absPath + filePathDetail);
            String contextPath = request.getContextPath();
            System.out.println(contextPath);

            //网页支付通用吗
//    		String url = request.getScheme() + "://" + request.getServerName() + "/shopUpdate/wechat/payForShop.do?shopUnique=" + shopUnique;
            //小程序支付通用码
            String url = "https://buyhoo.cc/buyhooPay?type=aggregatedPay&shopUnique=" + shopUnique + "&shopName=" + shopName;
            //二维码的图片格式
            String format = "jpg";
            File outputFile = new File(absPath + filePathDetail + shopUnique + "-" + imageSize + "." + format);


            File filePath = new File(absPath + filePathDetail);
            if(!filePath.exists()) {
                filePath.mkdirs();
            }
            outputFile.createNewFile();


            // 添加字体的属性设置
            Font font = new Font("创客贴金刚体", Font.BOLD, 100);
            try {
                org.springframework.core.io.Resource resource = resourceLoader.getResource("classpath:qrcode_background.png");
                File backGroundImageUrl = resource.getFile();
                //加载背景图片(也就是模板图)
                BufferedImage backGroundImage = ImageIO.read(backGroundImageUrl);
                //加载二维码图片(也就是需要合成到模板图上的图片)
                BufferedImage imageCode = QrCodeUtil.generate(url, 750, 750);
                //把背景图片当做为模板
                Graphics2D graphics = backGroundImage.createGraphics();
                //在模板上绘制图象(需要绘图的图片,左边距,上边距,图片宽度,图片高度,图像观察者)同一个模板一般是不会变的
                graphics.drawImage(imageCode, 141, 422, 750, 750, null);
                //设置字体
                graphics.setFont(font);
                //设置颜色
                graphics.setColor(Color.WHITE);
                //获取字体度量(字体度量是指对于指定字号的某种字体，在度量方面的各种属性)
                FontMetrics fontMetrics = graphics.getFontMetrics(font);
                //获取字体度量的宽度
                int textWidth = fontMetrics.stringWidth(shopName);
                //左边距=(模板图宽度-文字宽度)/2
                int widthX = (backGroundImage.getWidth() - textWidth) / 2;
                //g.drawString(title, 820, 2850);
                //绘制文字(内容，左边距,上边距)，同一个模板上边距一般也是不变的
                graphics.drawString(shopName, widthX, 150);
                //完成模板修改
                graphics.dispose();
                //获取新文件的地址
                //生成新的合成过的用户二维码并写入新图片,指定类型为png
                ImageIO.write(backGroundImage, "png", outputFile);
            } catch (Exception e) {
                e.printStackTrace();
            }

        }catch (Exception e) {
            e.printStackTrace();
        }
    }

}
