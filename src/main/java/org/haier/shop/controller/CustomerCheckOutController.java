package org.haier.shop.controller;

import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.OutputStream;
import java.math.BigDecimal;
import java.net.URLEncoder;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.apache.log4j.Logger;
import org.apache.poi.hssf.usermodel.HSSFSheet;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.Row;
import org.apache.shiro.SecurityUtils;
import org.apache.shiro.session.Session;
import org.apache.shiro.subject.Subject;
import org.haier.log.annotation.RemoteLog;
import org.haier.log.enums.BusinessType;
import org.haier.shop.config.WxPushConfig;
import org.haier.shop.entity.ShopsConfig;
import org.haier.shop.entity.Staff;
import org.haier.shop.entity.ny.CustomerSummaryByGroup;
import org.haier.shop.entity.ny.NingyuLottery;
import org.haier.shop.params.customer.RechargeForNYCusDetailParams;
import org.haier.shop.params.customer.RechargeForNYCusParams;
import org.haier.shop.redis.RedisCache;
import org.haier.shop.result.customer.SetMemberDaySetParams;
import org.haier.shop.service.CustomerCheckOutService;
import org.haier.shop.service.ManagerService;
import org.haier.shop.service.ShopsConfigService;
import org.haier.shop.util.ExcelUtil;
import org.haier.shop.util.HandleMessyCode;
import org.haier.shop.util.MyException;
import org.haier.shop.util.PurResult;
import org.haier.shop.util.ServletsUtil;
import org.haier.shop.util.ShopsResult;
import org.haier.shop.util.ShopsUtil;
import org.haier.shop.util.TitleForExcel;
import org.haier.shop.util.UtilForJAVA;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;


@Controller
@RequestMapping("/html/customer")
public class CustomerCheckOutController {
	@Resource
	private CustomerCheckOutService cusService;
	private static Logger log=Logger.getLogger(CustomerCheckOutController.class);
	
	@Resource
	private ManagerService manService;
	
	@Autowired
	private RedisCache rc;

	@Resource
	private ShopsConfigService  shopConfigService;


	/**
	 * 创建或者更新会员日设置
	 * @param params
	 * @return
	 */
	@RequestMapping("/setMemberDaySet.do")
	@ResponseBody
	public PurResult setMemberDaySet(@RequestBody SetMemberDaySetParams params) {
		return shopConfigService.setMemberDaySet(params);
	}

	/**
	 * 查询当前的店铺设置信息，包含会员日信息设置
	 * @return
	 */
	@RequestMapping("/queryShopsConfig.do")
	@ResponseBody
	public PurResult queryShopsConfig() {
		Subject subject = SecurityUtils.getSubject();
		Session session = subject.getSession();
		Staff staff = (Staff) session.getAttribute("staff");
		return PurResult.ok(shopConfigService.selectByShopUnique(staff.getShop_unique().toString()));
	}


	@RequestMapping("/memberDaySetPage.do")
	public String memberDaySetPage() {
		//跳转会员日设置界面
		return "/WEB-INF/customer/memberDaySetPage.jsp";
	}

	/**
	 * 宁宇充值
	 * @param list
	 * @return
	 */
	@RequestMapping("/rechargeForNYCus.do")
	@ResponseBody
	public PurResult rechargeForNYCus(@RequestBody List<RechargeForNYCusDetailParams> list) {
		return cusService.rechargeForNYCus(list);
	}

	/**
	 * 出现异常情况，解锁用。
	 * @return
	 */
	@RequestMapping("/lockRecharge.do")
	@ResponseBody
	public PurResult unRechargeLock() {
		WxPushConfig.isRecharge.set(false);
		return PurResult.ok();
	}

	@RequestMapping("/rechargeForNYCusN.do")
	@ResponseBody
	public PurResult rechargeForNYCusList(@RequestBody RechargeForNYCusParams params) {
		return PurResult.ok(params);
	}

	/**
	 * 跳转到宁宇专用的充值界面
	 * @return
	 */
	@RequestMapping("/toNYRechargePage.do")
	public String toNYRechargePage() {
		return "/WEB-INF/customer/customerNYRecharge.jsp";
	}
	
	/**
	 * 1、如果退款状态为驳回，将余额增加回账户；
	 * 2、如果退款状态为成功
	 * 2.1、现金退款，直接修改订单状态即可
	 * 2.2、使用小程序退款；
	 * 2.2.1、查询是否有小程序支付的列表，如果没有，返回提示要求使用现金退款；
	 * 2.2.2、查询是否有小程序支付的列表，如果有，比较充值的金额之和是否比当前的金额要大，如果大，提示用现金退款；
	 * 2.2.3、如果是小程序多笔退款，没退一笔，记录一次日志，防止部分退款成功；
	 * @param rechargeId 充值列表ID
	 * @param rechargeMethod 退款方式；
	 * @param rechargeStatus 退款状态；
	 * @param shopUnique 店铺编号
	 * @param remarks 驳回退款时的备注信息；
	 * @return
	 */
	@RequestMapping("/submitRefundMsg.do")
	@ResponseBody
	public PurResult submitRefundMsg(String rechargeId,Integer rechargeMethod,Integer rechargeStatus,String remarks,String shopUnique) {
		return cusService.submitRefundMsg(rechargeId, rechargeMethod, rechargeStatus, remarks,shopUnique);
	}
	
	@RequestMapping("/companyStaff.do")
	public String companyStaff() {
		return "/WEB-INF/customer/companyStaff.jsp";
	}
	
	@RequestMapping("/toCusRefundSingle.do")
	public String toCusRefundSingle() {
		return "/WEB-INF/customer/cusRefundPageSingle.jsp";
	}
	
	/**
	 * 
	 * @param shopUnique 店铺编号
	 * @param cusUnique 会员编号
	 * @param startTime 开始查询时间
	 * @param endTime 结束查询时间
	 * @param page 页码
	 * @param limit 限制数量
	 * @return
	 */
	@RequestMapping("/queryCusRefundList.do")
	@ResponseBody
	public PurResult queryCusRefundList(
			Long shopUnique,String cusUnique,
			String startTime,String endTime,
			@RequestParam(value="page",defaultValue="1")Integer page,
			@RequestParam(value="limit",defaultValue="15")Integer limit,HttpServletRequest request) {
		try {
			
			Map<String, Object> map = HandleMessyCode.handle(ServletsUtil.getParameters(request));
			map.put("pageSize", limit);
			map.put("startNum", (page-1)*limit);
			//只查询充值成功
			return cusService.queryCusRefundList(map);
		}catch (Exception e) {
			e.printStackTrace();
			PurResult pr = new PurResult(0, "查询失败");
			return pr;
		}
	}
	
	@RequestMapping("/toCusRefundPageList.do")
	public String toCusRefundPage() {
		Subject subject = SecurityUtils.getSubject();
		Session session = subject.getSession();
		Staff staff = (Staff) session.getAttribute("staff");
		
		//远见餐厅
		if(staff.getShop_type() == 12) {
			return "/WEB-INF/customer/cusRefundPageList.jsp";
		}else {
			return "/WEB-INF/customer/cusRefundPageList.jsp";
		}
	}
	
	/**
	 * 修改会员是否可提现
	 * @param shopUnique 店铺编号
	 * @param cusUnique 会员编号
	 * @param sameType 1：不可提现；2：可提现；
	 * @return
	 */
	@RequestMapping("/modifyCusMsg.do")
	@ResponseBody
	public PurResult modifyCusMsg(String shopUnique,String cusUnique,Integer sameType) {
		return cusService.modifyCusMsg(shopUnique, cusUnique, sameType);
	}
	
	@RequestMapping("/rechargeYJ.do")
	@ResponseBody
	/**
	 * 会员充值
	 * @param shopUnique 店铺编号
	 * @param cusUnique 会员编号
	 * @param rechargeType 充值类型：1、普通充值；2、换卡充值；3、补贴充值；4、因错退还；5、退卡区县；6、支付宝充值；7、微信充值
	 * @param rechargeMoney 充值金额
	 * @return
	 */
	public PurResult rechargeYJ(String shopUnique,String cusUnique,Integer rechargeType,Double rechargeMoney) {
		return cusService.rechargeYJ(shopUnique, cusUnique, rechargeType, rechargeMoney);
	}
	
	@RequestMapping("/toRechargePage.do")
	public String toRechargePage() {
		return "/WEB-INF/customer/toRechargePage.jsp";
	}
	
	@RequestMapping("/customerSummaryCYByGroup.do")
	@ResponseBody
	public PurResult customerSummaryCYByGroup(String shopUnique,Integer groupType,String startTime,String endTime,Integer page,Integer limit) {
		Map<String,Object> map = new HashMap<String,Object>();
		map.put("shopUnique", shopUnique);
		map.put("startTime", startTime);
		map.put("endTime", endTime);
		map.put("startNum", (page-1)*limit);
		map.put("pageSize", limit);
		return cusService.customerSummaryCYByGroup(map);
	}
	/**
	 * 查询文件加载进度
	 * @return
	 */
	@RequestMapping("/downLoadSpeedOfProgress.do")
	@ResponseBody
	public PurResult downLoadSpeedOfProgress() {
		PurResult pr = new PurResult(1,"查询成功!");
		
		Object speed = rc.getObject("downLoadSpeedOfProgress");
		
		if(null == speed) {
			pr.setStatus(0);
			pr.setMsg("文件已过期，请重新兑换");
		}else {
			pr.setData(speed);
		}
		
		return pr;
	}
	
	@RequestMapping("/createFile.do")
	@ResponseBody
	public PurResult createFile(HttpServletRequest request,HttpServletResponse response,String shopUnique,
			Integer type,Double min_points,Double min_money,String cus_msg) {
		PurResult pr = new PurResult(1,"查询成功!");
		//获取服务器所在路径
		String absPath = this.getClass().getClassLoader().getResource("../../").getPath();
		System.out.println(absPath);
		String filePath = "ningyu" + System.currentTimeMillis() + ".xls";
		
		List<Map<String,Object>> list = cusService.downLoadRedeenPointsDetail(shopUnique,
				type,min_points,min_money,cus_msg,"/shop/" + filePath);
		
		try {
			//创建文件
			@SuppressWarnings("resource")
			HSSFWorkbook wb = new HSSFWorkbook();
			HSSFSheet sheet = wb.createSheet("积分转换对账单");
			Integer rc = 0;
			
			Row rs = sheet.createRow(rc);
			rc++;
			for(Integer j = 0; j<15 ;j++) {
				Cell cell = rs.createCell(j);
				switch (j) {
				case 0:
					cell.setCellValue("会员编号");
					break;
				case 1:
					cell.setCellValue("联系方式");
					break;
				case 2:
					cell.setCellValue("会员姓名");
					break;
				case 3:
					cell.setCellValue("累计充值");
					break;
				case 4:
					cell.setCellValue("累计赠额");
					break;
				case 5:
					cell.setCellValue("当前余额");
					break;
				case 6:
					cell.setCellValue("当前赠额");
					break;
				case 7:
					cell.setCellValue("已用积分");
					break;
				case 8:
					cell.setCellValue("剩余积分");
					break;
				case 9:
					cell.setCellValue("转换后累计充值");
					break;
				case 10:
					cell.setCellValue("转换后累计赠额");
					break;
				case 11:
					cell.setCellValue("转换后余额");
					break;
				case 12:
					cell.setCellValue("转换后赠额");
					break;
				case 13:
					cell.setCellValue("转换后已用积分");
					break;
				case 14:
					cell.setCellValue("转换后剩余积分");
					break;
				default:
					break;
				}
			}
			
			for(Integer i = 0; i < list.size(); i++) {
				Row row = sheet.createRow(rc);
				rc ++;
				for(Integer j = 0; j<15 ;j++) {
					Cell cell = row.createCell(j);
					switch (j) {
					case 0:
						cell.setCellValue(list.get(i).get("cusUnique").toString());
						break;
					case 1:
						cell.setCellValue(list.get(i).get("cusPhone").toString());
						break;
					case 2:
						cell.setCellValue(list.get(i).get("cusName").toString());
						break;
					case 3:
						cell.setCellValue(list.get(i).get("cusAmount").toString());
						break;
					case 4:
						cell.setCellValue(list.get(i).get("giveAmount").toString());
						break;
					case 5:
						cell.setCellValue(list.get(i).get("cusBalance").toString());
						break;
					case 6:
						cell.setCellValue(list.get(i).get("cusRebate").toString());
						break;
					case 7:
						cell.setCellValue(list.get(i).get("cusUsePoints").toString());
						break;
					case 8:
						cell.setCellValue(list.get(i).get("cusPoints").toString());
						break;
					case 9:
						cell.setCellValue(list.get(i).get("ncusAmount").toString());
						break;
					case 10:
						cell.setCellValue(list.get(i).get("ngiveAmount").toString());
						break;
					case 11:
						cell.setCellValue(list.get(i).get("ncusBalance").toString());
						break;
					case 12:
						cell.setCellValue(list.get(i).get("ncusRebate").toString());
						break;
					case 13:
						cell.setCellValue(list.get(i).get("ncusUsePoints").toString());
						break;
					case 14:
						cell.setCellValue(list.get(i).get("ncusPoints").toString());
						break;
					default:
						break;
					}
				}
			}
			
			//将写入的数据保存
			File f = new File(absPath + File.separator + filePath);
			if(!f.exists()) {
				f.createNewFile();
			}
			FileOutputStream output=new FileOutputStream(absPath + File.separator + filePath);
			wb.write(output);
			output.flush();
			output.close();
			
		}catch (Exception e) {
			pr.setStatus(0);
			pr.setMsg("操作失败");
		}
		
		return pr;
	}
	
	/**
	 * 
	 * @param type 1:预览；2：修改数据并下载
	 * @param request 
	 * @param response
	 * @param shopUnique 店铺编号
	 */
	@SuppressWarnings("resource")
	@RequestMapping("/downLoadRedeenPointsDetail.do")
	public void downLoadRedeenPointsDetail(HttpServletRequest request,HttpServletResponse response,String shopUnique,
			Integer type,Double min_points,Double min_money,String cus_msg) {
		List<Map<String,Object>> list = cusService.downLoadRedeenPointsDetail(shopUnique,
				type,min_points,min_money,cus_msg,null);
		
		@SuppressWarnings("deprecation")
		String filePath=request.getRealPath("/");
		File file=new File(filePath+File.separator+"cusTomer"+shopUnique + System.currentTimeMillis() +".xls");
		try {
			if(null == list || list.isEmpty()) {
				//没有满足的条件，返回空文件
				if(!file.exists()){
					file.createNewFile();
				}
				OutputStream os=null;
				FileInputStream fis =null;
				response.setContentType("application/x-msdownload; charset=UTF-8");// 告知浏览器文件类型
				String agent = request.getHeader("user-agent");
				if (agent.contains("Firefox")) {
					response.setHeader(
							"Content-Disposition",
							"attachment;filename="
									+ new String(("会员信息.xls")
											.getBytes("GB2312"), "ISO-8859-1"));// 设置文件头
				} else {
					response.setHeader(
							"Content-Disposition",
							"attachment;filename="
									+ URLEncoder.encode("会员信息.xls", "UTF-8"));// 设置文件头
				}
				
				fis=new FileInputStream(file);
				os=response.getOutputStream();
				byte[] buffer = new byte[8192];
				int bytesRead = 0;
				while ((bytesRead = fis.read(buffer, 0, 8192)) != -1) {
					os.write(buffer, 0, bytesRead);
				};
				os.flush();
				
			}
		}catch (Exception e) {
			return;
		}
		try {
			List<TitleForExcel> tl=new ArrayList<>();//标题
			TitleForExcel t1=new TitleForExcel("cusUnique","会员编号");
			TitleForExcel t2=new TitleForExcel("cusName","会员名称");
			TitleForExcel t3=new TitleForExcel("cusPhone","联系方式");
			TitleForExcel t4=new TitleForExcel("cusAmount","累计充值");
			TitleForExcel t5=new TitleForExcel("giveAmount","累计赠送");
			TitleForExcel t6=new TitleForExcel("cusBalance","剩余充值金额");
			TitleForExcel t7=new TitleForExcel("cusRebate","剩余赠送金额");
			TitleForExcel t8=new TitleForExcel("cusUsePoints","已用积分");
			TitleForExcel t9=new TitleForExcel("cusPoints","剩余积分");
			
			TitleForExcel t10=new TitleForExcel("ncusAmount","转换后累计充值");
			TitleForExcel t11=new TitleForExcel("ngiveAmount","转换后累计赠额");
			TitleForExcel t12=new TitleForExcel("ncusBalance","转换后剩余充值金额");
			TitleForExcel t13=new TitleForExcel("ncusRebate","转换后剩余赠送金额");
			TitleForExcel t14=new TitleForExcel("ncusUsePoints","转换后累计使用积分");
			TitleForExcel t15=new TitleForExcel("ncusPoints","转换后剩余积分");
			
			tl.add(t1);tl.add(t2);tl.add(t3);tl.add(t4);tl.add(t5);
			tl.add(t6);tl.add(t7);tl.add(t8);tl.add(t9);tl.add(t10);
			tl.add(t11);tl.add(t12);tl.add(t13);tl.add(t14);tl.add(t15);
			ExcelUtil.ExcelForListMap(list, file, tl);
			OutputStream os=null;
			FileInputStream fis =null;
			int bytesRead = 0;
			byte[] buffer = new byte[8192];
			try {
				if(!file.exists()){
					file.createNewFile();
				}
				
				System.out.println(file.getAbsolutePath());
				
				
				//直接返回不靠谱，建议先保存，
				
				response.setContentType("application/x-msdownload; charset=UTF-8");// 告知浏览器文件类型
				String agent = request.getHeader("user-agent");
				if (agent.contains("Firefox")) {
					response.setHeader(
							"Content-Disposition",
							"attachment;filename="
									+ new String(("会员信息.xls")
											.getBytes("GB2312"), "ISO-8859-1"));// 设置文件头
				} else {
					response.setHeader(
							"Content-Disposition",
							"attachment;filename="
									+ URLEncoder.encode("会员信息.xls", "UTF-8"));// 设置文件头
				}
				
				fis=new FileInputStream(file);
				os=response.getOutputStream();
				while ((bytesRead = fis.read(buffer, 0, 8192)) != -1) {
					os.write(buffer, 0, bytesRead);
				};
				os.flush();
			} catch (IOException e) {
				e.printStackTrace();
			}finally{
				try {
					if(os!=null){
						os.close();
					}
					if(fis!=null){
						fis.close();
					}
				} catch (IOException e) {
					e.printStackTrace();
				}
			}
		}catch (Exception e) {
		}
	}
	
	@RequestMapping("/redeenPoints.do")
	public String redeenPoints(String shopUnique,Model model) {
		model.addAttribute("shopUnique",shopUnique);
		return "/WEB-INF/customer/redeenPoints.jsp";
	}
	
	/**
	 * 查询指定日期的平台充值信息
	 * @param datetime
	 * @return
	 */
	@RequestMapping("/queryOnlinePlatRechargeListDetail.do")
	@ResponseBody
	public PurResult queryOnlinePlatRechargeListDetail(String datetime) {
		return manService.queryOnlinePlatRechargeListDetail(datetime);
	}
	
	@RequestMapping("/queryOnlinePlatRechargeList.do")
	public String queryOnlinePlatRechargeList(String rechargeDate) {
		return "/WEB-INF/customer/queryOnlinePlatRechargeList.jsp";
	}
	/**
	 * 查询平台为线上会员充值的记录
	 * @param startTime
	 * @param endTime
	 * @return
	 */
	@RequestMapping("/queryOnlineCusRechargeList.do")
	@ResponseBody
	public PurResult queryOnlineCusRechargeList(String startTime,String endTime) {
		return manService.queryOnlineCusRechargeList(startTime, endTime);
	}
	
	@RequestMapping("/toRechargeRecord.do")
	public String toRechargeRecord() {
		return "/WEB-INF/customer/platRechargeRecord.jsp";
	}
	
	@RequestMapping("/rechargeForCus.do")
	@ResponseBody
	public PurResult rechargeForCus(String cusAccounts) {
		return manService.rechargeForCus(cusAccounts);
	}
	
	@RequestMapping("/queryOnlineCusMsgByCusAccount.do")
	@ResponseBody
	public PurResult queryOnlineCusMsgByCusAccount(String cusAccounts) {
		return manService.queryOnlineCusMsgByCusAccount(cusAccounts);
	}
	
	/**
	 * 更新充值配置信息
	 * @param lottery
	 * @return
	 */
	@RequestMapping("/modifyLotterySet.do")
	@ResponseBody
	public ShopsResult modifyLotterySet(@RequestBody()NingyuLottery lottery) {
		return cusService.modifyLotterySet(lottery);
	}
	
	@RequestMapping("/queryNYLotteryMsg.do")
	@ResponseBody
	public ShopsResult queryNYLotteryMsg() {
		return cusService.queryNYLotteryMsg();
	}
	
	@RequestMapping("/toLotterySet.do")
	public String toLotterySet(HttpServletRequest request) {
		request.setAttribute("lotterySet", cusService.getLotterySet());
		return "/WEB-INF/customer/lotterySet.jsp";
	}
	
	@RequestMapping("/downLoadCustomerSummaryList.do")
	public void downLoadCustomerSummaryList(String dshopUnique,Integer dgroupType,String dstartTime,String dendTime,HttpServletRequest request,HttpServletResponse response) {
		Map<String,Object> map = new HashMap<String,Object>();
		map.put("shopUnique", dshopUnique);
		map.put("startTime", dstartTime);
		map.put("endTime", dendTime);
		map.put("groupType", dgroupType);
		
		List<CustomerSummaryByGroup> list = cusService.downLoadCustomerSummaryList(map, dgroupType);
		if(null == list || list.isEmpty()) {
			return ;
		}
		String fileName = "储值卡消费汇总.xls";
		//非空完成下载
		@SuppressWarnings("deprecation")
		String filePath=request.getRealPath("/");
		System.out.println(filePath);
		File file=new File(filePath+File.separator+ fileName);
		List<TitleForExcel> tl=new ArrayList<>();
		TitleForExcel t1 = new TitleForExcel("name", "店铺名称");
		if(dgroupType == 1) {
			t1.setValue("店铺名称");
		}else if(dgroupType == 2) {
			t1.setValue("日期");
		}
		TitleForExcel t2 = new TitleForExcel("rechargeMoney", "充值金额");
		TitleForExcel t3 = new TitleForExcel("giveMoney", "充值赠额");
		TitleForExcel t4 = new TitleForExcel("rechargeTotal", "充值总额");
		TitleForExcel t5 = new TitleForExcel("consumptionMoney", "主账户消费金额");
		TitleForExcel t6 = new TitleForExcel("consumptionGiveMoney", "赠送账号消费金额");
		TitleForExcel t7 = new TitleForExcel("consumptionTotal", "消费合计");
		TitleForExcel t8 = new TitleForExcel("refundsMoney", "主账户退卡金额");
		TitleForExcel t9 = new TitleForExcel("refundsGiveMoney", "赠送账户退卡金额");
		TitleForExcel t10 = new TitleForExcel("refundsTotal", "退卡总额");
		TitleForExcel t11 = new TitleForExcel("allTotal", "储值汇总");
		tl.add(t1);
		tl.add(t2);
		tl.add(t3);
		tl.add(t4);
		tl.add(t5);
		tl.add(t6);
		tl.add(t7);
		tl.add(t8);
		tl.add(t9);
		tl.add(t10);
		tl.add(t11);
		
		ExcelUtil.ExcelForListObject(list, file, tl);
		OutputStream os=null;
		FileInputStream fis =null;
		int bytesRead = 0;
		byte[] buffer = new byte[8192];
		
		try {
			if(!file.exists()){
				file.createNewFile();
			}
			response.setContentType("application/x-msdownload; charset=UTF-8");// 告知浏览器文件类型
			String agent = request.getHeader("user-agent");
			if (agent.contains("Firefox")) {
				response.setHeader(
						"Content-Disposition",
						"attachment;filename="
								+ new String((fileName)
										.getBytes("GB2312"), "ISO-8859-1"));// 设置文件头
			} else {
				response.setHeader(
						"Content-Disposition",
						"attachment;filename="
								+ URLEncoder.encode(fileName, "UTF-8"));// 设置文件头
			}
			fis=new FileInputStream(file);
			os=response.getOutputStream();
			while ((bytesRead = fis.read(buffer, 0, 8192)) != -1) {
				os.write(buffer, 0, bytesRead);
			};
			os.flush();
		} catch (IOException e) {
			e.printStackTrace();
		}finally{
			try {
				if(os!=null){
					os.close();
				}
				if(fis!=null){
					fis.close();
				}
			} catch (IOException e) {
				e.printStackTrace();
			}
		}
	}
	@RequestMapping("/downloadNYRechargeLogExcel.do")
	public void downloadNYRechargeLogExcel(
			String startTime,
			String endTime,
			String search_name,
			String rechargeType,
			String shopUnique,
			HttpServletRequest request,HttpServletResponse response) {
		Map<String,Object> map = new HashMap<String,Object>();
		map.put("startTime", startTime);
		map.put("endTime", endTime);
		map.put("search_name", search_name);
		map.put("rechargeType", rechargeType);
		map.put("shopUnique", shopUnique);
		map.put("rechargeStatus", 1);
		
		List<Map<String,Object>> list = cusService.downloadNYRechargeLogExcel(map);
		if(null == list || list.isEmpty()) {
			return ;
		}
		String fileName = "会员充值记录.xls";
		//非空完成下载
		@SuppressWarnings("deprecation")
		String filePath=request.getRealPath("/");
		System.out.println(filePath);
		File file=new File(filePath+File.separator+ fileName);
		List<TitleForExcel> tl=new ArrayList<>();
		
		TitleForExcel t1 = new TitleForExcel("rechargeDatetime", "充值时间");
		TitleForExcel t2 = new TitleForExcel("recharge_money", "充值金额");
		TitleForExcel t3 = new TitleForExcel("give_money", "赠送金额");
		TitleForExcel t4 = new TitleForExcel("rechargeMethod", "充值方式");
		TitleForExcel t5 = new TitleForExcel("data_type", "充值类型");
		TitleForExcel t6 = new TitleForExcel("cus_name", "会员名称");
		TitleForExcel t7 = new TitleForExcel("cus_phone", "会员手机号");
		TitleForExcel t8 = new TitleForExcel("shop_name", "充值店铺");
		TitleForExcel t9 = new TitleForExcel("remarks", "备注信息");
		tl.add(t1);
		tl.add(t2);
		tl.add(t3);
		tl.add(t4);
		tl.add(t5);
		tl.add(t6);
		tl.add(t7);
		tl.add(t8);
		tl.add(t9);
		
		ExcelUtil.ExcelForListMap(list, file, tl);
		OutputStream os=null;
		FileInputStream fis =null;
		int bytesRead = 0;
		byte[] buffer = new byte[8192];
		
		try {
			if(!file.exists()){
				file.createNewFile();
			}
			response.setContentType("application/x-msdownload; charset=UTF-8");// 告知浏览器文件类型
			String agent = request.getHeader("user-agent");
			if (agent.contains("Firefox")) {
				response.setHeader(
						"Content-Disposition",
						"attachment;filename="
								+ new String((fileName)
										.getBytes("GB2312"), "ISO-8859-1"));// 设置文件头
			} else {
				response.setHeader(
						"Content-Disposition",
						"attachment;filename="
								+ URLEncoder.encode(fileName, "UTF-8"));// 设置文件头
			}
			fis=new FileInputStream(file);
			os=response.getOutputStream();
			while ((bytesRead = fis.read(buffer, 0, 8192)) != -1) {
				os.write(buffer, 0, bytesRead);
			};
			os.flush();
		} catch (IOException e) {
			e.printStackTrace();
		}finally{
			try {
				if(os!=null){
					os.close();
				}
				if(fis!=null){
					fis.close();
				}
			} catch (IOException e) {
				e.printStackTrace();
			}
		}
	}
	@RequestMapping("/downloadNYCardSaleExcel.do")
	public void downloadNYCardSaleExcel(
			String startTime,
			String endTime,
			String search_name,
			String shopUnique,
			HttpServletRequest request,HttpServletResponse response) {
		Map<String,Object> map = new HashMap<String,Object>();
		map.put("startTime", startTime);
		map.put("endTime", endTime);
		map.put("search_name", search_name);
		map.put("shopUnique", shopUnique);
		
		List<Map<String,Object>> list = (List<Map<String, Object>>) cusService.queryCusConsumptionList(map).getData();
		if(null == list || list.isEmpty()) {
			return ;
		}
		String fileName = "储值卡消费统计.xls";
		//非空完成下载
		@SuppressWarnings("deprecation")
		String filePath=request.getRealPath("/");
		System.out.println(filePath);
		File file=new File(filePath+File.separator+ fileName);
		List<TitleForExcel> tl=new ArrayList<>();
		
		TitleForExcel t1 = new TitleForExcel("saleListDate", "下单时间");
		TitleForExcel t2 = new TitleForExcel("cusUnique", "卡号");
		TitleForExcel t3 = new TitleForExcel("cusName", "姓名");
		TitleForExcel t5 = new TitleForExcel("saleTotal", "订单金额");
		TitleForExcel t6 = new TitleForExcel("recharge_money", "本金");
		TitleForExcel t7 = new TitleForExcel("give_money", "赠额");
		TitleForExcel t8 = new TitleForExcel("cusPhone", "手机号");
		TitleForExcel t9 = new TitleForExcel("payMoney", "储值卡支付金额");
		TitleForExcel t10 = new TitleForExcel("shop_name", "店铺名称");
		tl.add(t1);
		tl.add(t2);
		tl.add(t3);
		tl.add(t5);
		tl.add(t6);
		tl.add(t7);
		tl.add(t8);
		tl.add(t9);
		tl.add(t10);
		
		ExcelUtil.ExcelForListMap(list, file, tl);
		OutputStream os=null;
		FileInputStream fis =null;
		int bytesRead = 0;
		byte[] buffer = new byte[8192];
		
		try {
			if(!file.exists()){
				file.createNewFile();
			}
			response.setContentType("application/x-msdownload; charset=UTF-8");// 告知浏览器文件类型
			String agent = request.getHeader("user-agent");
			if (agent.contains("Firefox")) {
				response.setHeader(
						"Content-Disposition",
						"attachment;filename="
								+ new String((fileName)
										.getBytes("GB2312"), "ISO-8859-1"));// 设置文件头
			} else {
				response.setHeader(
						"Content-Disposition",
						"attachment;filename="
								+ URLEncoder.encode(fileName, "UTF-8"));// 设置文件头
			}
			fis=new FileInputStream(file);
			os=response.getOutputStream();
			while ((bytesRead = fis.read(buffer, 0, 8192)) != -1) {
				os.write(buffer, 0, bytesRead);
			};
			os.flush();
		} catch (IOException e) {
			e.printStackTrace();
		}finally{
			try {
				if(os!=null){
					os.close();
				}
				if(fis!=null){
					fis.close();
				}
			} catch (IOException e) {
				e.printStackTrace();
			}
		}
	}
	@RequestMapping("/downloadHistoryCusTotalExcel.do")
	public void downloadHistoryCusTotalExcel(
			String startTime,
			String endTime,
			HttpServletRequest request,HttpServletResponse response) {
		Map<String,Object> map = new HashMap<String,Object>();
		map.put("startTime", startTime);
		map.put("endTime", endTime);
		
		List<Map<String,Object>> list =  cusService.downloadHistoryCusTotalExcel(map);
		if(null == list || list.isEmpty()) {
			return ;
		}
		String fileName = "历史会员总额.xls";
		//非空完成下载
		@SuppressWarnings("deprecation")
		String filePath=request.getRealPath("/");
		System.out.println(filePath);
		File file=new File(filePath+File.separator+ fileName);
		List<TitleForExcel> tl=new ArrayList<>();
		
		TitleForExcel t2 = new TitleForExcel("date", "统计日期");
		TitleForExcel t3 = new TitleForExcel("balance", "本金余额");
		TitleForExcel t5 = new TitleForExcel("giveBalance", "赠金余额");
		TitleForExcel t6 = new TitleForExcel("count", "会员数量");
		tl.add(t2);
		tl.add(t3);
		tl.add(t5);
		tl.add(t6);
		
		ExcelUtil.ExcelForListMap(list, file, tl);
		OutputStream os=null;
		FileInputStream fis =null;
		int bytesRead = 0;
		byte[] buffer = new byte[8192];
		
		try {
			if(!file.exists()){
				file.createNewFile();
			}
			response.setContentType("application/x-msdownload; charset=UTF-8");// 告知浏览器文件类型
			String agent = request.getHeader("user-agent");
			if (agent.contains("Firefox")) {
				response.setHeader(
						"Content-Disposition",
						"attachment;filename="
								+ new String((fileName)
										.getBytes("GB2312"), "ISO-8859-1"));// 设置文件头
			} else {
				response.setHeader(
						"Content-Disposition",
						"attachment;filename="
								+ URLEncoder.encode(fileName, "UTF-8"));// 设置文件头
			}
			fis=new FileInputStream(file);
			os=response.getOutputStream();
			while ((bytesRead = fis.read(buffer, 0, 8192)) != -1) {
				os.write(buffer, 0, bytesRead);
			};
			os.flush();
		} catch (IOException e) {
			e.printStackTrace();
		}finally{
			try {
				if(os!=null){
					os.close();
				}
				if(fis!=null){
					fis.close();
				}
			} catch (IOException e) {
				e.printStackTrace();
			}
		}
	}
	
	@RequestMapping("/customerSummaryByGroup.do")
	@ResponseBody
	public PurResult customerSummaryByGroup(String shopUnique,Integer groupType,String startTime,String endTime,Integer page,Integer limit) {
		Map<String,Object> map = new HashMap<String,Object>();
		map.put("shopUnique", shopUnique);
		map.put("startTime", startTime);
		map.put("endTime", endTime);
		map.put("startNum", (page-1)*limit);
		map.put("pageSize", limit);
		map.put("groupType", groupType);
		return cusService.customerSummaryByGroup(map, groupType);
	}
	
	@RequestMapping("/customerSummary.do")
	public String customerSummary() {
		return "/WEB-INF/customer/customerSummary.jsp";
	}
	
	@RequestMapping("/cusSummaryCY.do")
	public String cusSummaryCY() {
		return "/WEB-INF/customer/cusSummaryCY.jsp";
	}
	
	@RequestMapping("/queryCusRechargeStaticByShop.do")
	@ResponseBody
	public PurResult queryCusRechargeStaticByShop(String startTime,String endTime,Integer page,Integer limit,String shopUnique) {
		try {
			return manService.queryCusRechargeStaticByShop(startTime, endTime, page, limit,shopUnique);
		}catch (Exception e) {
			return new PurResult(1, "查询失败！");
		}
	}
	
	@RequestMapping("/queryCusRechargeStatic.do")
	@ResponseBody
	public PurResult queryCusRechargeStatic(
			String shopUnique,String startTime,String endTime,String shopMsg
			) {
		try {
			return manService.queryCusRechargeStatic(shopUnique,startTime, endTime, shopMsg);
		}catch (Exception e) {
			e.printStackTrace();
			return new PurResult(0, "查询失败");
		}
	}
	/**
	 * 
	 * @param shopUnique 店铺编号
	 * @param cusMessage 会员信息，手机好，名称或会员编号
	 * @param startTime 开始查询时间
	 * @param endTime 截至查询时间
	 * @param cusStatus 会员状态：-1、全部；0、审核不通过；1、审核通过；2、待审核
	 * @param page 页码
	 * @param limit 每页查询数量
	 * @return
	 */
	@RequestMapping("/queryWJCusCheckOut.do")
	@ResponseBody
	public PurResult queryWJCusCheckOut(String shopUnique,String cusMessage,String startTime,String endTime,
			Integer cusStatus,Integer page,Integer limit) {
		try {
			return cusService.queryWJCusCheckOut(shopUnique, cusMessage, startTime, endTime, cusStatus, page, limit);
		}catch (Exception e) {
			e.printStackTrace();
			return new PurResult(0, "查询失败！");
		}
	}
	@RequestMapping("/toCustomerWJ.do")
	public String toCustomerWJ() {
		return "/WEB-INF/customer/customer-wj.jsp";
	}
	
	/**
	 * 查询会员的月消费额
	 * @param map
	 * @return
	 */
	@RequestMapping("/queryCusSaleMsgByMonth.do")
	@ResponseBody
	public PurResult queryCusSaleMsgByMonth(String search_msg,Integer page,Integer limit,Integer pay_type,String shopUnique,String startTime) {
		try {
			Map<String,Object> map = new HashMap<String,Object>();
			map.put("search_msg", search_msg);
			map.put("startNum", (page-1)*limit);
			map.put("pageSize", limit);
			map.put("shopUnique", shopUnique);
			map.put("startTime", startTime);
			map.put("payType", pay_type);
			return cusService.queryCusSaleMsgByMonth(map);
		}catch (Exception e) {
			e.printStackTrace();
			return new PurResult(0, "查询失败");
		}
	}
	
	@RequestMapping("/queryCusSaleMsgByMonthExcel.do")
	public void queryCusSaleMsgByMonthExcel(String search_msg,Integer pay_type,String shopUnique,String startTime,HttpServletRequest request,HttpServletResponse response) {
		response.reset();
		response.setHeader("Connection", "close");
		response.setHeader("Content-Type", "application/octet-stream");
		try {
			Map<String,Object> map = new HashMap<String,Object>();
			map.put("search_msg", search_msg);
			map.put("shopUnique", shopUnique);
			map.put("startTime", startTime);
			map.put("payType", pay_type);
			
			List<Map<String,Object>> list = cusService.queryCusSaleMsgByMonthExcel(map);
			
			@SuppressWarnings("deprecation")
			String filePath=request.getRealPath("/");
			File file=new File(filePath+File.separator+"cusTomer"+shopUnique+".xls");
			
			if(null != list) {
				List<TitleForExcel> tl=new ArrayList<>();//标题
				TitleForExcel t1=new TitleForExcel("cus_unique","会员编号");
				TitleForExcel t2=new TitleForExcel("allAmounts","消费金额");
				TitleForExcel t3=new TitleForExcel("cus_name","会员名称");
				TitleForExcel t4=new TitleForExcel("cus_phone","联系方式");
				TitleForExcel t5=new TitleForExcel("cus_balance","账户余额");
				tl.add(t1);tl.add(t2);tl.add(t3);tl.add(t4);tl.add(t5);
				
				ExcelUtil.ExcelForListMap(list, file, tl);
				
				OutputStream os=null;
				FileInputStream fis =null;
				int bytesRead = 0;
				byte[] buffer = new byte[8192];
				try {
					if(!file.exists()){
						file.createNewFile();
					}
					
					response.setContentType("application/x-msdownload; charset=UTF-8");// 告知浏览器文件类型
					String agent = request.getHeader("user-agent");
					if (agent.contains("Firefox")) {
						response.setHeader(
								"Content-Disposition",
								"attachment;filename="
										+ new String(("会员消费月信息.xls")
												.getBytes("GB2312"), "ISO-8859-1"));// 设置文件头
					} else {
						response.setHeader(
								"Content-Disposition",
								"attachment;filename="
										+ URLEncoder.encode("会员消费月信息.xls", "UTF-8"));// 设置文件头
					}
					
					fis=new FileInputStream(file);
					os=response.getOutputStream();
					while ((bytesRead = fis.read(buffer, 0, 8192)) != -1) {
						os.write(buffer, 0, bytesRead);
					};
					os.flush();
				} catch (IOException e) {
					e.printStackTrace();
				}finally{
					try {
						if(os!=null){
							os.close();
						}
						if(fis!=null){
							fis.close();
						}
					} catch (IOException e) {
						e.printStackTrace();
					}
				}
			}
		}catch (Exception e) {
			e.printStackTrace();
		}
		
	}
	
	@RequestMapping("/customerRenewList.do")
	public String customerRenewList() {
		return "/WEB-INF/customer/customerRenewList.jsp";
	}	
	/**
	 * 会员信息分页查询
	 * @param map
	 * @return
	 */
	@RequestMapping("/queryCusCheckOut.do")
	@ResponseBody
	public PurResult queryCusCheckOut(
			@RequestParam(value="shop_unique",required=true)Long shop_unique,
			String cusMessage,
			String cusType,
			String order,
			String orderType,
			@RequestParam(value = "page", defaultValue = "0") int page,
			@RequestParam(value = "limit", defaultValue = "8") int pageSize,
			Integer shopType,
			String cus_level_id,
			Integer sameType
			){
		Map<String,Object> map=new HashMap<String, Object>();
		map.put("shop_unique", shop_unique);
		map.put("pageSize", pageSize);
		map.put("startNum", (page-1)*pageSize);
		map.put("cus_type", cusType);
		map.put("sameType", sameType);
		if(order != null && !order.equals("") && orderType != null && !orderType.equals("")){
			map.put("order", order);
			map.put("orderType", orderType);
		}
		if(cus_level_id != null && !cus_level_id.equals("")){
			map.put("cus_level_id", cus_level_id);
		}
		if(null!=cusMessage && !cusMessage.equals("")){
			map.put("cusMessage", "%"+cusMessage+"%");
		}
		return cusService.queryCusCheckOut(map,shopType);
	}
	/**
	 * 线上会员信息分页查询
	 * @param map
	 * @return
	 */
	@RequestMapping("/queryCusOnline.do")
	@ResponseBody
	public PurResult queryCusOnline(HttpServletRequest request,
			@RequestParam(value="shop_unique",required=true)Long shop_unique,
			String cusMessage,
			String cusType,
			String order,
			String orderType,
			@RequestParam(value = "page", defaultValue = "0") int page,
			@RequestParam(value = "limit", defaultValue = "8") int pageSize
			){
		//Map<String,Object> map=new HashMap<String, Object>();
		Map<String, Object> map = ServletsUtil.getParameters(request);
		map.put("shop_unique", shop_unique);
		map.put("pageSize", pageSize);
		map.put("startNum", (page-1)*pageSize);
		map.put("cus_type", cusType);
		if(order != null && !order.equals("") && orderType != null && !orderType.equals("")){
			map.put("order", order);
			map.put("orderType", orderType);
		}
		if(null!=cusMessage && !cusMessage.equals("")){
			map.put("cusMessage", "%"+cusMessage+"%");
		}
		return cusService.queryCusOnline(map);
	}
	/**
	 * 线上会员信息分页查询
	 * @param map
	 * @return
	 */
	@RequestMapping("/queryCusOnlinePt.do")
	@ResponseBody
	public PurResult queryCusOnlinePt(HttpServletRequest request,
			@RequestParam(value="shop_unique",required=true)Long shop_unique,
			String cusMessage,
			String cusType,
			String order,
			String orderType,
			@RequestParam(value = "page", defaultValue = "0") int page,
			@RequestParam(value = "limit", defaultValue = "8") int pageSize
			){
		//Map<String,Object> map=new HashMap<String, Object>();
		Map<String, Object> map = ServletsUtil.getParameters(request);
		map.put("shop_unique", shop_unique);
		map.put("pageSize", pageSize);
		map.put("startNum", (page-1)*pageSize);
		map.put("cus_type", cusType);
		if(order != null && !order.equals("") && orderType != null && !orderType.equals("")){
			map.put("order", order);
			map.put("orderType", orderType);
		}
		if(null!=cusMessage && !cusMessage.equals("")){
			map.put("cusMessage", "%"+cusMessage+"%");
		}
		return cusService.queryCusOnlinePt(map);
	}
	/**
	 * 会员详情查询
	 * @param map
	 * @return
	 */
	@RequestMapping("/cusDetailMessageQuery.do")
	@ResponseBody
	public ShopsResult cusDetailMessageQuery(
			@RequestParam(value="cusId",required=true)Integer cusId
			){
		Map<String,Object> map=new HashMap<String, Object>();
		map.put("cusId", cusId);
		return cusService.cusDetailMessageQuery(map);
	}
	
	
	/**
	 * 编辑会员页面
	 */
	@RequestMapping("/editCustomerPage.do")
	public String editCustomerPage(Integer cusId,Long shopUnique,Model model){ 		
		Map<String,Object> map=new HashMap<String, Object>();
		map.put("cusId", cusId);
		map.put("shopUnique", shopUnique);
		ShopsResult result = manService.queryCusDetail(map);
		model.addAttribute("customer", result.getData());	
		model.addAttribute("cusId", cusId);	
		model.addAttribute("shopUnique", shopUnique);	
		return "/WEB-INF/customer/editCustomer.jsp";
	}

	/**
	 * 会员详情更新
	 * @param request
	 * @param shopUnique
	 * @param cusId
	 * @param cusUnique
	 * @param cusName
	 * @param levelId
	 * @param cusPhone
	 * @param cusWeixin
	 * @param cusQQ
	 * @param cusEmail
	 * @param cusBirthday
	 * @param cusSex
	 * @param cusOccupation
	 * @param cusHeadPath
	 * @param cusAddress
	 * @param cusPwd
	 * @param cusType
	 * @param oldCusType
	 * @param validityStart 有效期开始日期
	 * @param validityEnd 有效期结束日期
	 * @param powerCredit 赊销权限：0、未授权；1、已授权
	 * @param creditLimit 赊销授权额度
	 * @return
	 */
	@RemoteLog(title = "修改会员", businessType = BusinessType.UPDATE)
	@RequestMapping("/saveCusMessage.do")
	@ResponseBody
	public ShopsResult saveCusMessage(
			HttpServletRequest request,
			@RequestParam(value="shopUnique",required=true)String shopUnique,
			@RequestParam(value="cusId",required=true)Integer cusId,
			@RequestParam(value="cusUnique",required=true)String cusUnique,
			@RequestParam(value="cusName",required=true)String cusName,
			@RequestParam(value="levelId")Integer levelId,
			String cusPhone,
			String cusWeixin,
			String cusQQ,
			String cusEmail,
			String cusBirthday,
			Integer cusSex,
			String cusOccupation,
			String cusHeadPath,
			String cusAddress,
			String cusPwd,
			String cusType,
			String oldCusType,
			String validityStart,
			String validityEnd,
			Integer powerCredit,
			BigDecimal creditLimit
			){
		Map<String,Object> map=new HashMap<String, Object>();
		map.put("cusId", cusId);
		map.put("cusUnique", cusUnique);
		map.put("cusName", cusName);
		map.put("cusPhone", cusPhone);
		map.put("cusPhone", cusPhone);
		map.put("cusQQ", cusQQ);
		map.put("cusWeixin", cusWeixin);
		map.put("cusEmail", cusEmail);
		map.put("cusBirthday", cusBirthday);
		map.put("cusSex", cusSex);
		map.put("cusOccupation", cusOccupation);
		map.put("cusAddress", cusAddress);
		map.put("cusHeadPath", cusHeadPath);
		map.put("levelId", levelId);
		map.put("shopUnique", shopUnique);
		map.put("validityStart", validityStart);
		map.put("validityEnd", validityEnd);
		if(null!=cusPwd&&!cusPwd.equals("")){
			map.put("cusPwd", ShopsUtil.string2MD5(cusPwd));
		}
		map.put("powerCredit" , powerCredit);
		map.put("creditLimit", creditLimit);
		return cusService.saveCusMessage(map,request,shopUnique,cusType,oldCusType);
	}
	
	/**
	 * 会员等级
	 */
	@RequestMapping("/getMemberLevel.do")
	@ResponseBody
	public PurResult getMemberLevel(
			@RequestParam(value="shopUnique",required=true)Long shopUnique,
			@RequestParam(value = "page", defaultValue = "0") int page,
			@RequestParam(value = "limit", defaultValue = "8") int pageSize
			){
		Map<String,Object> map=new HashMap<String, Object>();
		map.put("shopUnique", shopUnique);
		map.put("pageSize", pageSize);
		map.put("startNum", (page-1)*pageSize);

		return cusService.getMemberLevel(map);
	}
	
	/**
	 * 会员等级-修改折扣
	 */
	@RequestMapping("/editMemberLevel.do")
	public String editMemberLevel(String cus_level_id,Model model){ 		
		Map<String,Object> map=new HashMap<String, Object>();
		map.put("cus_level_id", cus_level_id);
		model.addAttribute("memberLevel", cusService.getMemberLevelById(map));		
		return "/WEB-INF/customer/editMemberLevel.jsp";
	}
	
	/**
	 * 会员充值记录页面
	 */
	@RequestMapping("/queryRechargeRecordPage.do")
	public String queryRechargeRecordPage(int type,String cus_unique,Model model){ 		
		model.addAttribute("cus_unique", cus_unique);
		if(type==2) {
			return "/WEB-INF/customer/rechargeRecordNy.jsp";
		}
		return "/WEB-INF/customer/rechargeRecord.jsp";
	}
	
	/**
	 * 会员购买记录页面
	 */
	@RequestMapping("/queryBuyRecordPage.do")
	public String queryBuyRecordPage(String cus_unique,Model model){ 		
		model.addAttribute("cus_unique", cus_unique);		
		return "/WEB-INF/customer/buyRecord.jsp";
	}
	/**
	 * 会员积分记录页面
	 */
	@RequestMapping("/queryCusPointHistoryPage.do")
	public String queryCusPointHistoryPage(String cus_unique,Model model){ 		
		model.addAttribute("cus_unique", cus_unique);		
		return "/WEB-INF/customer/queryCusPointHistoryPage.jsp";
	}
	/**
	 * 会员积分清零页面
	 */
	@RequestMapping("/pointClearPage.do")
	public String pointClearPage(Model model,String shop_unique){ 
		//查询配置详情
		Map<String,Object> map=cusService.queryPointClear(shop_unique);
		model.addAttribute("shop", map);		
		return "/WEB-INF/customer/pointClearPage.jsp";
	}
	
	/**
	 * 会员充值记录查询
	 * @param map
	 * @return
	 */
	@RequestMapping("/queryRechargeRecord.do")
	@ResponseBody
	public PurResult queryRechargeRecord(
				@RequestParam(value="cusUnique",required=true)String cusUnique,
				@RequestParam(value="shopUnique",required=true)String shopUnique,
				@RequestParam(value = "page", defaultValue = "0") int page,
				@RequestParam(value = "limit", defaultValue = "8") int pageSize,
				@RequestParam(value="cusType",defaultValue="1")Integer cusType
			){
		Map<String,Object> map=new HashMap<String, Object>();
		map.put("cusUnique", cusUnique);
		map.put("shopUnique", shopUnique);
		map.put("startNum", (page-1)*pageSize);
		map.put("cusType", cusType);
		map.put("pageSize", pageSize);
		return cusService.queryRechargeRecord(map);
	}
	
	/**
	 * 会员购买记录
	 */
	@RequestMapping("/queryBuyRecord.do")
	@ResponseBody
	public PurResult queryBuyRecord(
				@RequestParam(value="cusUnique",required=true)String cusUnique,
				@RequestParam(value="shopUnique",required=true)String shopUnique,
				@RequestParam(value = "page", defaultValue = "0") int page,
				@RequestParam(value = "limit", defaultValue = "8") int pageSize,
				@RequestParam(value="cusType",defaultValue="1")Integer cusType
			){
		Map<String,Object> map=new HashMap<String, Object>();
		map.put("cusUnique", cusUnique);
		map.put("shopUnique", shopUnique);
		map.put("startNum", (page-1)*pageSize);
		map.put("cusType", cusType);
		map.put("pageSize", pageSize);
		return cusService.queryBuyRecord(map);
	}
	/**
	 * 会员积分记录
	 */
	@RequestMapping("/queryCusPointHistory.do")
	@ResponseBody
	public PurResult queryCusPointHistory(
			@RequestParam(value="cusUnique",required=true)String cusUnique,
			@RequestParam(value="shopUnique",required=true)String shopUnique,
			@RequestParam(value = "page", defaultValue = "0") int page,
			@RequestParam(value = "limit", defaultValue = "8") int pageSize
			){
		Map<String,Object> map=new HashMap<String, Object>();
		map.put("cusUnique", cusUnique);
		map.put("shopUnique", shopUnique);
		map.put("startNum", (page-1)*pageSize);
		map.put("pageSize", pageSize);
		return cusService.queryCusPointHistory(map);
	}
	
	/**
	 * 退款记录
	 */
	@RequestMapping("/queryBackRecord.do")
	@ResponseBody
	public PurResult queryBackRecord(
				@RequestParam(value="cus_id",required=true)String cus_id,
				@RequestParam(value = "page", defaultValue = "0") int page,
				@RequestParam(value = "limit", defaultValue = "8") int pageSize
			){
		Map<String,Object> map=new HashMap<String, Object>();
		map.put("cus_id", cus_id);
		map.put("startNum", (page-1)*pageSize);
		map.put("pageSize", pageSize);
		return cusService.queryBackRecord(map);
	}
	
	
	/**
	 * 平台会员详情
	 * @param map
	 * @return
	 */
	@RequestMapping("/platformCustomerDetail.do")
	public String platformCustomerDetail(String cus_unique,Model model){
		//获取平台会员信息
		Map<String ,Object> customer = cusService.platformCustomerDetail(cus_unique);
		model.addAttribute("customer", customer);
		return "/WEB-INF/platform/platformCustomerDetail.jsp";
	}
	
	/**
	 * 平台会员交易记录页面
	 * @param map
	 * @return
	 */
	@RequestMapping("/platformCustomerTransRecordListPage.do")
	public String platformCustomerTransRecordListPage(String cus_unique,Model model){
		model.addAttribute("cus_unique", cus_unique);
		return "/WEB-INF/platform/platformCustomerTransRecordList.jsp";
	}
	
	/**
	 * 平台会员交易记录查询
	 * @param cus_unique
	 * @param money_type 货币类型 1:储值卡 2:百货豆 3:积分
	 * @param sale_type 1:储值卡充值 2:储值卡消费 3：百货豆消费 4：百货豆兑换 5：积分抵扣 6：积分线上消费 7：积分门店消费 8:百货豆充值
	 * @return
	 */
	@RequestMapping("/platformCustomerTransRecordList.do")
	@ResponseBody
	public PurResult platformCustomerTransRecordList(
			@RequestParam(value = "page", defaultValue = "0") int page,
			@RequestParam(value = "limit", defaultValue = "8") int pageSize,
			String cus_unique,
			String money_type,
			String sale_type
			){
		Map<String,Object> map=new HashMap<String, Object>();
		map.put("cus_unique", cus_unique);
		map.put("money_type", money_type);
		map.put("sale_type", sale_type);
		map.put("startNum", (page-1)*pageSize);
		map.put("pageSize", pageSize);
		return cusService.queryRechargeRecordPlatform(map);
	}

	/**
	 * 会员消费记录页面
	 */
	@RequestMapping("/queryComsumptionRecordPage.do")
	public String queryComsumptionRecordPage(int type,String cus_unique,Model model){ 		
		model.addAttribute("cus_unique", cus_unique);
		if(type==2) {
			return "/WEB-INF/customer/comsumptionRecordNy.jsp";
		}
		return "/WEB-INF/customer/comsumptionRecord.jsp";
	}
	/**
	 * 线上会员消费记录页面
	 */
	@RequestMapping("/queryComsumptionRecordPage2.do")
	public String queryComsumptionRecordPage2(String cus_unique,Model model){ 		
		model.addAttribute("cus_unique", cus_unique);		
		return "/WEB-INF/customer/comsumptionRecord2.jsp";
	}	
	/**
	 * 会员消费记录查询
	 * @param map
	 * @return
	 */
	@RequestMapping("/queryComsumptionRecord.do")
	@ResponseBody
	public PurResult queryComsumptionRecord(
			@RequestParam(value="cusUnique",required=true)String cusUnique,
			@RequestParam(value="shopUnique",required=true)String shopUnique,
			@RequestParam(value = "page", defaultValue = "0") int page,
			@RequestParam(value = "limit", defaultValue = "8") int pageSize,
			@RequestParam(value="cusType",defaultValue="3")Integer cusType
			){
		Map<String,Object> map=new HashMap<String, Object>();
		map.put("cusUnique", cusUnique);
		map.put("shopUnique", shopUnique);
		map.put("cusType", cusType);
		map.put("pageSize", pageSize);
		map.put("startNum", (page-1)*pageSize);
		return cusService.queryComsumptionRecord(map);
	}
	/**
	 * 平台会员消费记录查询
	 * @param map
	 * @return
	 */
	@RequestMapping("/queryComsumptionRecord2.do")
	@ResponseBody
	public PurResult queryComsumptionRecord2(
			@RequestParam(value="cusUnique",required=true)String cusUnique,
			@RequestParam(value="shopUnique",required=true)String shopUnique,
			@RequestParam(value = "page", defaultValue = "0") int page,
			@RequestParam(value = "limit", defaultValue = "8") int pageSize,
			@RequestParam(value="cusType",defaultValue="3")Integer cusType
			){
		Map<String,Object> map=new HashMap<String, Object>();
		map.put("cusUnique", cusUnique);
		map.put("shopUnique", shopUnique);
		map.put("cusType", cusType);
		map.put("pageSize", pageSize);
		map.put("startNum", (page-1)*pageSize);
		return cusService.queryComsumptionRecord2(map);
	}
	
	/**
	 * 新增会员
	 */
	@RequestMapping("/addNewCusPage.do")
	public String addNewCusPage(Model model){
		return "/WEB-INF/customer/addCustomer.jsp";
	}
	/**
	 * 会员活生命周期
	 */
	@RequestMapping("/cusLifeCycle.do")
	public String cusLifeCycle(Model model){
		
		Map<String,Object> map=cusService.queryCusLifeCycle(1);
		model.addAttribute("data", map);	
		return "/WEB-INF/customer/cusLifeCycle.jsp";
	}
	/**
	 * 修改生命周期配置
	 */
	@RequestMapping("/updateCusLifeCycle.do")
	@ResponseBody
	public ShopsResult updateCusLifeCycle(HttpServletRequest request,HttpServletResponse response){
		try {
			Map<String,Object> map=UtilForJAVA.addParameter(request);
			return cusService.updateCusLifeCycle(map);
		} catch (MyException e) {
			return new ShopsResult(e.status, e.msg);
		}
	}
	/**
	 * 会员活跃度
	 */
	@RequestMapping("/cusActivity.do")
	public String cusActivity(Model model){
		Map<String,Object> map=cusService.queryCusLifeCycle(2);
		model.addAttribute("data", map);	
		return "/WEB-INF/customer/cusActivity.jsp";
	}
	/**
	 * 会员忠诚度
	 */
	@RequestMapping("/cusLoyalty.do")
	public String cusLoyalty(Model model){
		Map<String,Object> map=cusService.queryCusLifeCycle(3);
		model.addAttribute("data", map);	
		return "/WEB-INF/customer/cusLoyalty.jsp";
	}
	@RemoteLog(title="新增会员", businessType = BusinessType.INSERT)
	@RequestMapping("/addNewCus.do")
	@ResponseBody
	public ShopsResult  addNewCus(
			HttpServletRequest request,
			@RequestParam(value="shopUnique",required=true)String shopUnique,
			@RequestParam(value="cusUnique",required=true)String cusUnique,
			@RequestParam(value="cusName",required=true)String cusName,
			@RequestParam(value="cusType",required=true)String cusType,
			@RequestParam(value="levelId")Integer levelId,
			String cusPhone,
			String cusWeixin,
			String cusQQ,
			String cusEmail,
			String cusBirthday,
			Integer cusSex,
			String cusOccupation,
			String cusAddress,
			String cusPwd,
			String validityStart,
			String validityEnd
			){
		Map<String,Object> map=new HashMap<String, Object>();
		map.put("shopUnique", shopUnique);
		map.put("cusUnique", cusUnique);
		map.put("cusName", cusName);
		map.put("cusPhone", cusPhone);
		map.put("cusPhone", cusPhone);
		map.put("cusQQ", cusQQ);
		map.put("cusWeixin", cusWeixin);
		map.put("cusEmail", cusEmail);
		map.put("cusBirthday", cusBirthday);
		map.put("cusSex", cusSex);
		map.put("cusOccupation", cusOccupation);
		map.put("cusAddress", cusAddress);
		map.put("cusType", cusType);
		map.put("levelId", levelId);
		map.put("validityStart", validityStart);
		map.put("validityEnd", validityEnd);
		if(null!=cusPwd&&!cusPwd.equals("")){
			map.put("cusPwd", ShopsUtil.string2MD5(cusPwd));
		}
		
		System.out.println("会员信息为：：："+map);
		return cusService.addNewCus(map,request,shopUnique);
	}
	
	
	/**
	 * 会员充值
	 * @param map
	 * @return
	 */
	@RequestMapping("/cusRecharge.do")
	@ResponseBody
	public ShopsResult cusRecharge(
			@RequestParam(value="cusId",required=true)Integer cusId,
			@RequestParam(value="cusAmount")Double cusAmount,
			@RequestParam(value="cusType",defaultValue="1")Integer cusType//默认为充值
			){
		Map<String,Object> map=new HashMap<String, Object>();
		map.put("cusId", cusId);
		map.put("cusAmount", cusAmount);
		map.put("cusType", cusType);
		return cusService.cusRecharge(map);
	}
	
	/**
	 * 取现
	 * @param map
	 * @return
	 */
	@RequestMapping("/sureTakeNow.do")
	@ResponseBody
	public ShopsResult sureTakeNow(
			@RequestParam(value="cusId")Integer cusId,
			@RequestParam(value="takeNowMoney")Double takeNowMoney,
			@RequestParam(value="cusType",defaultValue="2")Integer cusType
			){
		Map<String,Object> map =new HashMap<String, Object>();
		map.put("cusId", cusId);
		map.put("cusAmount", takeNowMoney);
		map.put("cusType", cusType);
		return cusService.sureTakeNow(map, takeNowMoney);
	}

	@RemoteLog(title="会员等级修改", businessType = BusinessType.UPDATE)
	@RequestMapping("/updateMemberLevel.do")
	@ResponseBody
	public ShopsResult updateMemberLevel(
			@RequestParam(value="cus_level_id")String cus_level_id,
			@RequestParam(value="cus_level_discount")String cus_level_discount,
			@RequestParam(value="cus_level_points")String cus_level_points
			){
		Map<String,Object> map =new HashMap<String, Object>();
		map.put("cus_level_id", cus_level_id);
		map.put("cus_level_discount", cus_level_discount);
		map.put("cus_level_points", cus_level_points);
		return cusService.updateMemberLevel(map);
	}
	
	/**
	 * 管理员所有店铺员工查询
	 * @return
	 */
	@RequestMapping("/queryManagerStaffs.do")
	@ResponseBody
	public ShopsResult queryManagerStaffs(
			String managerUnique,
			String shopUnique
			){
		Map<String,Object> map =new HashMap<String, Object>();
		map.put("managerUnique", managerUnique);
		map.put("shopUnique", shopUnique);
		log.info("请求路径为：html/customer/queryManagerStaffs.do");
		log.info("接受的参数信息为："+map);
//		if(null==managerUnique){
//			ShopsResult sr=new ShopsResult();
//			sr.setStatus(2);
//			sr.setMsg("不是管理员");
//			return sr;
//		}
		return cusService.queryManagerStaffs(map);
	}
	
	/**
	 * 会员等级信息查询
	 * @param map
	 * @return
	 */
	@RequestMapping("/queryCusLevel.do")
	@ResponseBody
	public ShopsResult queryCusLevel(
			@RequestParam(value="shopUnique")Long shopUnique
			){
		Map<String,Object> map =new HashMap<String, Object>();
		map.put("shopUnique", shopUnique);
		return cusService.queryCusLevel(map);
	}
	
	@RequestMapping("/customer.do")
	public String customer(HttpServletRequest request){
		Subject subject = SecurityUtils.getSubject();
		Session session = subject.getSession();
		Staff staff = (Staff) session.getAttribute("staff");
		if(staff.getShop_type()==6)
		{
			return "/WEB-INF/customer/customer-ny.jsp";
		}else if(staff.getShop_type()==7) {
			return "/WEB-INF/customer/customer-subny.jsp";
		}
		return "/WEB-INF/customer/customer.jsp";
	}
	@RequestMapping("/customerOnline.do")
	public String customerOnline(HttpServletRequest request){
		return "/WEB-INF/customer/customerOnline.jsp";
	}
	
	@RequestMapping("/toMemberLevel.do")
	public String toMemberLevel(HttpServletRequest request){
		return "/WEB-INF/customer/shopMemberLevel.jsp";
	}
	
	/**
	 * 删除会员信息
	 * @param map
	 * @return
	 */
	@RemoteLog(title="启用(停用)会员", businessType = BusinessType.DELETE)
	@RequestMapping("/deleteCustomer.do")
	@ResponseBody
	public ShopsResult deleteCustomer(
			String shopUnique,
			HttpServletRequest request,
			String cusUniques,
			String cus_status,
			String cus_available
			){
		ShopsUtil.recordLog(log, request);
		Map<String,Object> map=new HashMap<String,Object>();
		map.put("shopUnique", shopUnique);
		String[] uniques=cusUniques.split(";");
		map.put("uniques", uniques);
		map.put("cus_status", cus_status == null ? "0":cus_status);
		map.put("cus_available", cus_available == null ? "2":cus_available);
		return cusService.deleteCustomer(map);
	}
	
	/**
	 * 会员信息导出
	 */
	@RequestMapping("/downCusExcel.do")
	public void downCusExcel(String shopUnique,Integer sameType,HttpServletRequest request,HttpServletResponse response){
		ShopsUtil.recordLog(log, request);
		response.reset();
		response.setHeader("Connection", "close");
		response.setHeader("Content-Type", "application/octet-stream");
		try {
			List<Map<String ,Object>> list=cusService.downCusExcel(shopUnique,sameType == null ? -1 : sameType);
//			System.out.println(list);
			@SuppressWarnings("deprecation")
			String filePath=request.getRealPath("/");
			File file=new File(filePath+File.separator+"cusTomer"+shopUnique+".xls");
			if(null==list||list.isEmpty()){
			}else{
				List<TitleForExcel> tl=new ArrayList<>();//标题
				TitleForExcel t1=new TitleForExcel("cusUnique","会员编号");
				TitleForExcel t2=new TitleForExcel("cusName","会员名称");
				TitleForExcel t3=new TitleForExcel("cusSex","性别");
				TitleForExcel t4=new TitleForExcel("cusPhone","联系方式");
				TitleForExcel t5=new TitleForExcel("cusTotal","消费总金额");
				TitleForExcel t6=new TitleForExcel("cusRegeditDate","注册日期");
				TitleForExcel t7=new TitleForExcel("cusBirthday","生日");
				TitleForExcel t8=new TitleForExcel("cusCount","消费次数");
				TitleForExcel t9=new TitleForExcel("totalPoints","累计积分");
				TitleForExcel t10=new TitleForExcel("cusUsePoints","已用积分");
				TitleForExcel t11=new TitleForExcel("cusPoints","剩余积分");
				TitleForExcel t12=new TitleForExcel("cusAmount","充值总额");
				TitleForExcel t13=new TitleForExcel("cusUse","已用金额");
				TitleForExcel t14=new TitleForExcel("cusBalance","剩余金额");
				tl.add(t1);tl.add(t2);tl.add(t3);tl.add(t4);tl.add(t5);
				tl.add(t6);tl.add(t7);tl.add(t8);tl.add(t9);tl.add(t10);
				tl.add(t11);tl.add(t12);tl.add(t13);tl.add(t14);
				ExcelUtil.ExcelForListMap(list, file, tl);
				
				OutputStream os=null;
				FileInputStream fis =null;
				int bytesRead = 0;
				byte[] buffer = new byte[8192];
				try {
					if(!file.exists()){
						file.createNewFile();
					}
					
					response.setContentType("application/x-msdownload; charset=UTF-8");// 告知浏览器文件类型
					String agent = request.getHeader("user-agent");
					if (agent.contains("Firefox")) {
						response.setHeader(
								"Content-Disposition",
								"attachment;filename="
										+ new String(("会员信息.xls")
												.getBytes("GB2312"), "ISO-8859-1"));// 设置文件头
					} else {
						response.setHeader(
								"Content-Disposition",
								"attachment;filename="
										+ URLEncoder.encode("会员信息.xls", "UTF-8"));// 设置文件头
					}
					
					fis=new FileInputStream(file);
					os=response.getOutputStream();
					while ((bytesRead = fis.read(buffer, 0, 8192)) != -1) {
						os.write(buffer, 0, bytesRead);
					};
					os.flush();
				} catch (IOException e) {
					e.printStackTrace();
				}finally{
					try {
						if(os!=null){
							os.close();
						}
						if(fis!=null){
							fis.close();
						}
					} catch (IOException e) {
						e.printStackTrace();
					}
				}
			}
		} catch (Exception e) {
			e.printStackTrace();
		}
	}

	/**
	 * 会员信息导出
	 */
	@RequestMapping("/downCusCheckoutExcel.do")
	public void downCusCheckoutExcel(@RequestParam(value="shopUnique",required=true)String shopUnique,
							 String cusMessage,
							 String cusType,
							 String order,
							 String orderType,
							 Integer shopType,
							 String cus_level_id,
							 Integer sameType,HttpServletRequest request,HttpServletResponse response){
		ShopsUtil.recordLog(log, request);
		response.reset();
		response.setHeader("Connection", "close");
		response.setHeader("Content-Type", "application/octet-stream");
		try {
			Map<String,Object> map=new HashMap<String, Object>();
			map.put("shop_unique", shopUnique);
			map.put("pageSize", 100000000);
			map.put("startNum", 0);
			map.put("cus_type", cusType);
			map.put("sameType", sameType);
			if(order != null && !order.equals("") && orderType != null && !orderType.equals("")){
				map.put("order", order);
				map.put("orderType", orderType);
			}
			if(cus_level_id != null && !cus_level_id.equals("")){
				map.put("cus_level_id", cus_level_id);
			}
			if(null!=cusMessage && !cusMessage.equals("")){
				map.put("cusMessage", "%"+cusMessage+"%");
			}
			PurResult purResult = cusService.queryCusCheckOut(map,shopType);
			List<Map<String ,Object>> list= (List<Map<String, Object>>) purResult.getData();
//			System.out.println(list);
			@SuppressWarnings("deprecation")
			String filePath=request.getRealPath("/");
			File file=new File(filePath+File.separator+"cusTomer"+shopUnique+".xls");
			if(null==list||list.isEmpty()){
			}else{
				List<TitleForExcel> tl=new ArrayList<>();//标题
				TitleForExcel t1=new TitleForExcel("cusUnique","会员编号");
				TitleForExcel t2=new TitleForExcel("cusName","会员名称");
				TitleForExcel t3=new TitleForExcel("cusSex","性别");
				TitleForExcel t4=new TitleForExcel("cusPhone","联系方式");
				TitleForExcel t5=new TitleForExcel("cusTotal","消费总金额");
				TitleForExcel t6=new TitleForExcel("cusRegeditDate","注册日期");
				TitleForExcel t7=new TitleForExcel("cusBirthday","生日");
				TitleForExcel t8=new TitleForExcel("cusCount","消费次数");
				TitleForExcel t9=new TitleForExcel("totalPoints","累计积分");
				TitleForExcel t10=new TitleForExcel("cusUsePoints","已用积分");
				TitleForExcel t11=new TitleForExcel("cusPoints","剩余积分");
				TitleForExcel t12=new TitleForExcel("cusAmount","充值总额");
				TitleForExcel t13=new TitleForExcel("cusUse","已用金额");
				TitleForExcel t14=new TitleForExcel("cusBalance","剩余金额");
				tl.add(t1);tl.add(t2);tl.add(t3);tl.add(t4);tl.add(t5);
				tl.add(t6);tl.add(t7);tl.add(t8);tl.add(t9);tl.add(t10);
				tl.add(t11);tl.add(t12);tl.add(t13);tl.add(t14);
				ExcelUtil.ExcelForListMap(list, file, tl);

				OutputStream os=null;
				FileInputStream fis =null;
				int bytesRead = 0;
				byte[] buffer = new byte[8192];
				try {
					if(!file.exists()){
						file.createNewFile();
					}

					response.setContentType("application/x-msdownload; charset=UTF-8");// 告知浏览器文件类型
					String agent = request.getHeader("user-agent");
					if (agent.contains("Firefox")) {
						response.setHeader(
								"Content-Disposition",
								"attachment;filename="
										+ new String(("会员信息.xls")
										.getBytes("GB2312"), "ISO-8859-1"));// 设置文件头
					} else {
						response.setHeader(
								"Content-Disposition",
								"attachment;filename="
										+ URLEncoder.encode("会员信息.xls", "UTF-8"));// 设置文件头
					}

					fis=new FileInputStream(file);
					os=response.getOutputStream();
					while ((bytesRead = fis.read(buffer, 0, 8192)) != -1) {
						os.write(buffer, 0, bytesRead);
					};
					os.flush();
				} catch (IOException e) {
					e.printStackTrace();
				}finally{
					try {
						if(os!=null){
							os.close();
						}
						if(fis!=null){
							fis.close();
						}
					} catch (IOException e) {
						e.printStackTrace();
					}
				}
			}
		} catch (Exception e) {
			e.printStackTrace();
		}
	}

	/**
	 * 查询会员续费页面
	 */
	@RequestMapping("/queryCusRenewListPage.do")
	public String queryCusRenewListPage(HttpServletRequest request){
		return "/WEB-INF/customer/queryCusRenewList.jsp";
	}
	
	@RequestMapping("/queryCusConsumption.do")
	public String queryCusConsumption(HttpServletRequest request) {
		return "/WEB-INF/customer/queryCusConsumption.jsp";
	}
	
	@RequestMapping("/queryCusRecharge.do")
	public String queryCusRecharge() {
		Subject subject = SecurityUtils.getSubject();
		Session session = subject.getSession();
		Staff staff = (Staff) session.getAttribute("staff");
		if(staff.getShop_type() == 12) {
			return "/WEB-INF/customer/queryCusRechargeYJ.jsp";
		}
			
		return "/WEB-INF/customer/queryCusRecharge.jsp";
	}
	
	@RequestMapping("/monthSaleStatistics.do")
	public String monthSaleStatistics() {
		return "/WEB-INF/customer/monthSaleStatistics.jsp";
	}
	/**
	 * 
	 * @param request
	 * @param page
	 * @param limit
	 * @return
	 */
	@RequestMapping("/queryCusConsumptionList.do")
	@ResponseBody
	public PurResult queryCusConsumptionList(
			HttpServletRequest request,
			@RequestParam(value="page",defaultValue="1")Integer page,
			@RequestParam(value="limit",defaultValue="15")Integer limit
			) {
		PurResult result = new PurResult();
		try {
			Map<String, Object> params = HandleMessyCode.handle(ServletsUtil.getParameters(request));
			params.put("limit", limit);
			params.put("page", (page-1)*limit);
			if(params.get("search_name")!=null&&!"".equals(params.get("search_name"))){
				params.put("search_name", "%"+params.get("search_name")+"%");
			}
			result=cusService.queryCusConsumptionList(params);
		} catch (Exception e) {
			result.setStatus(0);
			result.setData(null);
			result.setMsg("查询失败!");
			e.printStackTrace();
		}		
		return result;
	}
	
	@RequestMapping("/queryCusRechargeList.do")
	@ResponseBody
	public PurResult queryCusRechargeList(
			HttpServletRequest request,
			@RequestParam(value="page",defaultValue="1")Integer page,
			@RequestParam(value="limit",defaultValue="15")Integer limit
			) {
		PurResult pr = new PurResult(0, "查询失败!");
		try {
			Map<String, Object> map = HandleMessyCode.handle(ServletsUtil.getParameters(request));
			map.put("pageSize", limit);
			map.put("startNum", (page-1)*limit);
			//只查询充值成功
			map.put("rechargeStatus", 1);
			return cusService.queryCusRechargeList(map);
		}catch (Exception e) {
			e.printStackTrace();
			return pr;
		}
	}
	
	/**
	 * 查询会员续费分页记录
	 * @return
	 */
	@RequestMapping("/queryCusRenewList.do")
	@ResponseBody
	public PurResult queryCusRenewList(HttpServletRequest request,
			@RequestParam(value="page",defaultValue="1")Integer page,
			@RequestParam(value="limit",defaultValue="15")Integer limit){		
		PurResult result = new PurResult();
		try {
			Map<String, Object> params = HandleMessyCode.handle(ServletsUtil.getParameters(request));
			params.put("limit", limit);
			params.put("page", (page-1)*limit);
			if(params.get("search_name")!=null&&!"".equals(params.get("search_name"))){
				params.put("search_name", "%"+params.get("search_name")+"%");
			}
			result=cusService.queryCusRenewList(params);
		} catch (Exception e) {
			result.setStatus(0);
			result.setData(null);
			result.setMsg("查询失败!");
			e.printStackTrace();
		}		
		return result;
	}
	
	@RequestMapping("/customerNy.do")
	public String customerNy(HttpServletRequest request){
		return "/WEB-INF/customer/customer-ny.jsp";
	}
	
	/**
	 * 会员退款记录页面
	 */
	@RequestMapping("/queryBackRecordPage.do")
	public String queryBackRecordPage(String cus_id,Model model){ 		
		model.addAttribute("cus_id", cus_id);		
		return "/WEB-INF/customer/backRecord.jsp";
	}
	
	
	/**
	 * 会员信息导出-宁宇
	 */
	@RequestMapping("/downCusExcelNy.do")
	public void downCusExcelNy(String shopUnique,HttpServletRequest request,HttpServletResponse response){
		ShopsUtil.recordLog(log, request);
		response.reset();
		response.setHeader("Connection", "close");
		response.setHeader("Content-Type", "application/octet-stream");
		try {
			List<Map<String ,Object>> list=cusService.downCusExcel(shopUnique,-1);
			System.out.println(list.size());
			@SuppressWarnings("deprecation")
			String filePath=request.getRealPath("/");
			File file=new File(filePath+File.separator+"cusTomer"+shopUnique+".xls");
			if(null==list||list.isEmpty()){
			}else{
				List<TitleForExcel> tl=new ArrayList<>();//标题
				TitleForExcel t1=new TitleForExcel("cusUnique","会员编号");
				TitleForExcel t2=new TitleForExcel("cusName","会员名称");
				TitleForExcel t3=new TitleForExcel("cusSex","性别");
				TitleForExcel t4=new TitleForExcel("cusPhone","联系方式");
				TitleForExcel t5=new TitleForExcel("cusAmount","累计充值");
				TitleForExcel t6=new TitleForExcel("give_amount","累计赠送");
				TitleForExcel t13=new TitleForExcel("used_rechage","已用充值金额");
				TitleForExcel t7=new TitleForExcel("used_give","已用赠送金额");
				TitleForExcel t14=new TitleForExcel("cusBalance","剩余充值金额");
				TitleForExcel t12=new TitleForExcel("cus_rebate","剩余赠送金额");
				TitleForExcel t8=new TitleForExcel("cusCount","消费次数");
				TitleForExcel t9=new TitleForExcel("totalPoints","累计积分");
				TitleForExcel t10=new TitleForExcel("cusUsePoints","已用积分");
				TitleForExcel t11=new TitleForExcel("cusPoints","剩余积分");
		
				tl.add(t1);tl.add(t2);tl.add(t3);tl.add(t4);tl.add(t5);
				tl.add(t6);tl.add(t7);tl.add(t8);tl.add(t9);tl.add(t10);
				tl.add(t11);tl.add(t12);tl.add(t13);tl.add(t14);
				ExcelUtil.ExcelForListMap(list, file, tl);
				OutputStream os=null;
				FileInputStream fis =null;
				int bytesRead = 0;
				byte[] buffer = new byte[8192];
				try {
					if(!file.exists()){
						file.createNewFile();
					}
					
					response.setContentType("application/x-msdownload; charset=UTF-8");// 告知浏览器文件类型
					String agent = request.getHeader("user-agent");
					if (agent.contains("Firefox")) {
						response.setHeader(
								"Content-Disposition",
								"attachment;filename="
										+ new String(("会员信息.xls")
												.getBytes("GB2312"), "ISO-8859-1"));// 设置文件头
					} else {
						response.setHeader(
								"Content-Disposition",
								"attachment;filename="
										+ URLEncoder.encode("会员信息.xls", "UTF-8"));// 设置文件头
					}
					
					fis=new FileInputStream(file);
					os=response.getOutputStream();
					while ((bytesRead = fis.read(buffer, 0, 8192)) != -1) {
						os.write(buffer, 0, bytesRead);
					};
					os.flush();
				} catch (IOException e) {
					e.printStackTrace();
				}finally{
					try {
						if(os!=null){
							os.close();
						}
						if(fis!=null){
							fis.close();
						}
					} catch (IOException e) {
						e.printStackTrace();
					}
				}
			}
		} catch (Exception e) {
			e.printStackTrace();
		}
	}
	/**
	 *五金审核会员
	 */
	@RequestMapping("/auditWJCusCheckOut.do")
	@ResponseBody
	public ShopsResult auditWJCusCheckOut(HttpServletRequest request,HttpServletResponse response){
		try {
			Map<String,Object> map=UtilForJAVA.addParameter(request);
			return cusService.auditWJCusCheckOut(map);
		} catch (MyException e) {
			return new ShopsResult(e.status, e.msg);
		}
	}
	/**
	  *五金会员禁用
	 */
	@RequestMapping("/updateWJCusCheckOut.do")
	@ResponseBody
	public ShopsResult updateWJCusCheckOut(HttpServletRequest request,HttpServletResponse response){
		try {
			Map<String,Object> map=UtilForJAVA.addParameter(request);
			return cusService.updateWJCusCheckOut(map);
		} catch (MyException e) {
			return new ShopsResult(e.status, e.msg);
		}
	}

	@RequestMapping("/queryMemberLevelCusCount.do")
	public String queryMemberLevelCusCount(String cus_level_id,Model model){ 		
		model.addAttribute("cus_level_id", cus_level_id);		
		return "/WEB-INF/customer/customer_levelCount.jsp";
	}

	@RemoteLog(title="修改积分清零规则", businessType = BusinessType.UPDATE)
	@RequestMapping("/updatePointClearConfig.do")
	@ResponseBody
	public ShopsResult updatePointClearConfig(HttpServletRequest request,HttpServletResponse response){
		try {
			Map<String,Object> map=UtilForJAVA.addParameter(request);
			return cusService.updatePointClearConfig(map);
		} catch (MyException e) {
			return new ShopsResult(e.status, e.msg);
		}
	}
	
}

	
