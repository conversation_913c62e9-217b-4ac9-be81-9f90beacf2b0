package org.haier.shop.controller;

import java.io.UnsupportedEncodingException;
import java.util.HashMap;
import java.util.Map;
import java.util.logging.Logger;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;

import org.haier.meituan.util.MUtil;
import org.haier.meituanpeisong.service.PeisongOrderService;
import org.haier.shop.util.ServletsUtil;
import org.haier.shop.util.ShopsResult;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

@Controller
@RequestMapping("/peisong")
public class PeisongController {
	
	private static final Logger logger = Logger.getGlobal();

	@Resource
	private PeisongOrderService peisongOrderService;
	
	/**
	 * 配送订单创建
	 * @param sale_list_unique 订单编号,不能为空
	 * @param delivery_type 配送方式，0：自配送 1：美团配送 2:一刻钟配送，不能为空
	 * @param goods_weight 订单商品重量（kg），不能为空
	 * @param shop_courier_id 自配送商家快递员id，可为空，自配送时不能为空
	 * @param courier_name 配送员姓名，可为空，自配送时不能为空
	 * @param courier_phone 配送员电话，可为空，自配送时不能为空
	 * @param sale_list_cashier 收银员id
	 * 
	 * @param return_price 退还差价
	 * @param goodsList 核实订单商品信息，json字符串
	 * 		 [
	 * 			{
	 * 				goods_barcode 商品编码
	 * 				goods_name 商品名称
	 * 				goods_count 商品数量
	 * 				goods_price 商品单价
	 * 				goods_subtotal 商品价格小计
	 * 			}
	 * 		]
	 * @return
	 */
	@RequestMapping("/createOrder.do")
	@ResponseBody
	public ShopsResult createOrder(HttpServletRequest request){
		Map<String ,Object> params = ServletsUtil.getParameters(request);
		logger.info("配送订单创建，params="+params);
		String sale_list_unique = MUtil.strObject(params.get("sale_list_unique"));
		String delivery_type = MUtil.strObject(params.get("delivery_type"));
		String goods_weight = MUtil.strObject(params.get("goods_weight"));
		String shop_courier_id = MUtil.strObject(params.get("shop_courier_id"));
		String courier_name = MUtil.strObject(params.get("courier_name"));
		try {
			courier_name = new String(courier_name.getBytes("ISO-8859-1"), "UTF-8");
		} catch (UnsupportedEncodingException e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		}
		String courier_phone = MUtil.strObject(params.get("courier_phone"));
		String sale_list_cashier = MUtil.strObject(params.get("sale_list_cashier"));
		ShopsResult shopsResult = new ShopsResult();
		
		if(delivery_type.equals("0")){//商家自配送
			shopsResult = peisongOrderService.shopSelf(sale_list_unique, goods_weight,shop_courier_id,courier_name,courier_phone,sale_list_cashier);
		}else if(delivery_type.equals("1")){//美团配送
			shopsResult = peisongOrderService.createOrder(sale_list_unique, goods_weight,sale_list_cashier);
		}else if(delivery_type.equals("2")){//一刻钟配送
			shopsResult = peisongOrderService.createDelivery(sale_list_unique,sale_list_cashier,0);
		}
		//订单核实信息
		String return_price = MUtil.strObject(params.get("return_price"));
		String goodsList = MUtil.strObject(params.get("goodsList"));
		try {
			goodsList = new String(goodsList.getBytes("ISO-8859-1"), "UTF-8");
		} catch (UnsupportedEncodingException e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		}
		if(return_price != null && !return_price.equals("") && goodsList != null && !goodsList.equals("")) {
			Map<String ,Object> verifyParams = new HashMap<String, Object>();
			verifyParams.put("sale_list_unique", sale_list_unique);
			verifyParams.put("return_price", return_price);
			verifyParams.put("verify_staff_id", sale_list_cashier);
			verifyParams.put("goodsList", goodsList);
			peisongOrderService.verifyOrderNew(verifyParams);
		}
		
		return shopsResult;
	}
	
	/**
	 * 取消配送单
	 * @param sale_list_unique 订单编号
	 * @param delivery_type 配送方式，0：自配送 1：美团配送 2:一刻钟配送，不能为空
	 * @param cancel_reason 详细取消原因
	 * @return
	 */
	@RequestMapping("/deleteOrder.do")
	@ResponseBody
	public ShopsResult deleteOrder(HttpServletRequest request){
		Map<String, Object> params = ServletsUtil.getParameters(request);
		logger.info("取消配送单，params="+params);
		String sale_list_unique = MUtil.strObject(params.get("sale_list_unique"));
		String delivery_type = MUtil.strObject(params.get("delivery_type"));
		String cancel_reason = MUtil.strObject(params.get("cancel_reason"));
		
		ShopsResult shopsResult = new ShopsResult();
		if(delivery_type.equals("0")){//商家自配送
			shopsResult = peisongOrderService.cancelShopDelivery(sale_list_unique);
		}else if(delivery_type.equals("1")){//美团配送
			shopsResult = peisongOrderService.deleteOrder(sale_list_unique, cancel_reason);
		}else if(delivery_type.equals("2")){//一刻钟配送
			shopsResult = peisongOrderService.cancelYkzDelivery(sale_list_unique);
		}
		
		return shopsResult;
	}
	
	/**
	 *订单核实
	 * @param sale_list_unique 订单编号
	 * @param return_price 退还差价
	 * @param verify_staff_id 核单员工id
	 * @param goodsList 核实订单商品信息，json字符串
	 * 		 [
	 * 			{
	 * 				goods_barcode 商品编码
	 * 				goods_name 商品名称
	 * 				goods_count 商品数量
	 * 				goods_price 商品单价
	 * 				goods_subtotal 商品价格小计
	 * 			}
	 * 		]
	 * @return
	 */
	@RequestMapping("/verifyOrder.do")
	@ResponseBody
	public ShopsResult verifyOrder(HttpServletRequest request){
		Map<String, Object> params = ServletsUtil.getParameters(request);
		logger.info("订单核实，params="+params);
		
		ShopsResult shopsResult = new ShopsResult();
		shopsResult = peisongOrderService.verifyOrder(params);
		
		return shopsResult;
	}
	
//	/**
//	 * 评价骑手
//	 * @param sale_list_unique 订单编号
//	 * @param score 评分（5分制）
//	 * @param comment_content 评价内容
//	 * @return
//	 */
//	@RequestMapping("/evaluate.do")
//	@ResponseBody
//	public ShopsResult evaluate(String sale_list_unique,int score,String comment_content){
//		logger.info("美团配送平台，评价骑手");
//		
//		ShopsResult shopsResult = peisongOrderService.evaluate(sale_list_unique,score,comment_content);
//		
//		return shopsResult;
//	}
//	
}
