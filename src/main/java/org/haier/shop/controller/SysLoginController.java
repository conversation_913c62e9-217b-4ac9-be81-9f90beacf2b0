package org.haier.shop.controller;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;

import org.apache.shiro.SecurityUtils;
import org.apache.shiro.authc.DisabledAccountException;
import org.apache.shiro.authc.ExcessiveAttemptsException;
import org.apache.shiro.authc.ExpiredCredentialsException;
import org.apache.shiro.authc.IncorrectCredentialsException;
import org.apache.shiro.authc.LockedAccountException;
import org.apache.shiro.authc.UnknownAccountException;
import org.apache.shiro.authc.UsernamePasswordToken;
import org.apache.shiro.authz.UnauthorizedException;
import org.apache.shiro.session.Session;
import org.apache.shiro.subject.Subject;
import org.haier.meituan.util.MUtil;
import org.haier.shop.dao.ShopsConfigDao;
import org.haier.shop.entity.ShopsConfig;
import org.haier.shop.entity.Staff;
import org.haier.shop.entity.SysPermission;
import org.haier.shop.service.BeanService;
import org.haier.shop.service.ShopService;
import org.haier.shop.service.StaffService;
import org.haier.shop.service.SysMenuService;
import org.haier.shop.service.SysRoleService;
import org.haier.shop.util.MD5Utils;
import org.haier.shop.util.StringUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import com.alibaba.fastjson.JSON;

@Controller
public class SysLoginController {
    private static final Logger logger = LoggerFactory.getLogger(SysLoginController.class);
    
    @Autowired
    private SysRoleService roleService;
    
    @Autowired
    private SysMenuService menuService;
    
    @Autowired
    private StaffService staffService;
    
    @Autowired
    private ShopService shopService;
    
    @Autowired
    private BeanService beanService;
    
    @Resource
    private RedisTemplate<String, Object> redisTemplate;

	@Resource
	private ShopsConfigDao shopsConfigDao;
    
    @RequestMapping("/toShopListForLog.do")
    public String toShopListForLog() {
    	return "/WEB-INF/shop/shopListForLog.jsp";
    }
    
    @RequestMapping("/loginYn.do")
    public String loginYn() {
    	return "/WEB-INF/login_yiNong.jsp";
    }

	@GetMapping(value = "/loginMain.do")
	public String loginMain(String roleType){
		if(StringUtil.isBlank(roleType)){
			return "/WEB-INF/login/loginMain.jsp";
		}
		return  "/WEB-INF/login/deng"+roleType+".jsp";
	}
    @PostMapping(value = "/loginMain.do")
    public String login(Staff staff, boolean rememberMe,Model model){
		String roleType = staff.getRoleType();
    	Subject subject = SecurityUtils.getSubject();
        if (subject.isAuthenticated()) {  
        	return "redirect:/manager/mainPage.do";
        }
    	if(staff.getStaff_account() == null || staff.getStaff_account().equals("")){
    		staff = (Staff) subject.getPrincipal();
        	System.out.println(staff);
        	if(staff!=null){
        		Map<String, Object> shops=(Map<String, Object>) shopService.queryShopMessage(staff.getShop_unique().toString()).getData();
        		if(shops != null) {
        			Integer shop_type = Integer.parseInt(MUtil.strObject(shops.get("shop_type")));
            		if(shop_type==4||shop_type==5){
            	    	return "redirect:/loginYn.do";
            		}else{
                		return  "/WEB-INF/login/deng"+roleType+".jsp";
            		}
        		}
        	}
    		return  "/WEB-INF/login/deng"+roleType+".jsp";
    	}
    	String msg = "";
    	
        UsernamePasswordToken usernamePasswordToken = new UsernamePasswordToken(staff.getStaff_account(),MD5Utils.getPwd(staff.getStaff_pwd()));
        usernamePasswordToken.setRememberMe(rememberMe);
        try {
            if (!subject.isAuthenticated()) { 
	        	subject.login(usernamePasswordToken);
	        	Session session = subject.getSession();
            	
            	//获取用户信息
	        	staff = (Staff) subject.getPrincipal();
				ShopsConfig shopsConfig = shopsConfigDao.selectByShopUnique(String.valueOf(staff.getShop_unique()));
				session.setAttribute("goods_in_price_type", shopsConfig.getGoodsInPriceType());
            	session.setAttribute("staff", staff);
				session.setAttribute("roleType", roleType);
            	session.setAttribute("staffJSON", JSON.toJSONString(staff));
            	session.setAttribute("login_shop_class", staff.getShop_class());
            	session.setAttribute("login_shop_unique", staff.getShop_unique());
            	Integer staff_position = staff.getStaff_position();
            	String version = "";
            	String terminal = "web";
				Integer type = staff.getShop_type();
				
				Object c = redisTemplate.opsForValue().get("shopLogin");
				if(null == c) {
//				redisTemplate.opsForValue().set("shopLogin", "ddd");
				}else {
					if(staff.getShop_type() == 9) {
						model.addAttribute("message", "该账户已过期");
						return  "/WEB-INF/login/deng"+roleType+".jsp";
					}
				}
				/**
				 * 集团后台1---3 系统平台
				 * 连锁总店 ---1 连锁
				 * 连锁店  ---1 连锁
				 * 直营店  ---其他
				 */
				Map<String,String> roleTypeNameMap = new HashMap<>();
				roleTypeNameMap.put("1","集团后台");
				roleTypeNameMap.put("2","连锁总店");
				roleTypeNameMap.put("3","连锁店");
				roleTypeNameMap.put("4","直营店");

				if(roleType.equals("1")){
					if(staff.getShop_class() != 3){
						subject.logout();
						model.addAttribute("message", "该账号不属于【"+roleTypeNameMap.get(roleType)+"】,请核实账号身份！");
						return  "/WEB-INF/login/deng"+roleType+".jsp";
					}
				}else if(roleType.equals("2") || roleType.equals("3")){
					if(staff.getShop_class() != 1){
						subject.logout();
						model.addAttribute("message", "该账号不属于【"+roleTypeNameMap.get(roleType)+"】,请核实账号身份！");
						return  "/WEB-INF/login/deng"+roleType+".jsp";
					}
				}else if( roleType.equals("4")){
					if(staff.getShop_class() == 3 ||staff.getShop_class() == 1 ){
						subject.logout();
						model.addAttribute("message", "该账号不属于【"+roleTypeNameMap.get(roleType)+"】,请核实账号身份！");
						return  "/WEB-INF/login/deng"+roleType+".jsp";
					}
				}else {
					subject.logout();
					model.addAttribute("message", "该账号登录权限有误，请刷新页面！");
					return  "/WEB-INF/login/deng"+roleType+".jsp";
				}
				switch (staff.getShop_class()) {
				case 0:
					version = "ordinary";
					break;
				case 1:
					version = "chain";
					break;
				case 2:
					version = "join";
					break;
				case 3:
					version = "admin";
					terminal = null;
					type = null;
					break;
				case 4:
					version = "branchStore";
					break;
				case 5:
					version = "agency";
					break;
				default:
					version = "ordinary";
					break;
				}
				
				Map<String ,Object> params = new HashMap<String, Object>();
	   			params.put("version", version);
	   			params.put("type", type);
	   			params.put("terminal", terminal);
	   			params.put("seq", "1");//根据seq排序
	   			List<SysPermission> listLevel1 = new ArrayList<SysPermission>();
	   			List<SysPermission> listLevel2 = new ArrayList<SysPermission>();
    			if(staff_position == 3){//店主
    	   			//获取一级菜单列表
    	    		params.put("level", "1");
    	        	listLevel1 = menuService.quertMenuList(params);
    	        	//获取二级菜单列表
    	        	params.put("level", "2");
    	        	listLevel2 = menuService.quertMenuList(params);
    			}else{
    	        	params.put("role_code", MUtil.strObject(roleService.getRoleByStaffId(staff.getStaff_id()).get("role_code")));
    	        	//获取角色关联一级菜单列表
    	    		params.put("level", "1");
    	    		params.put("shop_unique", String.valueOf(staff.getShop_unique()));
    	        	listLevel1 = roleService.getMenuListByRoleCode(params);
    	        	//获取角色关联二级菜单列表
    	        	params.put("level", "2");
    	        	listLevel2 = roleService.getMenuListByRoleCode(params);
    			}
    			if(listLevel1.size()<=0||listLevel2.size()<=0){
    		        model.addAttribute("message", "您没有任何权限无法登陆");  
    		        return  "/WEB-INF/login/deng"+roleType+".jsp";
    			}
    			session.setAttribute("listLevel1", listLevel1);
    			session.setAttribute("listLevel2", listLevel2);
    			if(staff.getShop_class() == 8) {
    				return "redirect:/manager/toYinongMainPage.do";
    			}
            	return "redirect:/manager/mainPage.do";
	        }
        } catch (IncorrectCredentialsException e) {  
	        msg = "登录密码错误";  
	        model.addAttribute("message", msg);  
	        logger.info(msg);
	    } catch (ExcessiveAttemptsException e) {  
	        msg = "登录失败次数过多";  
	        model.addAttribute("message", msg);  
	        logger.info(msg); 
	    } catch (LockedAccountException e) {  
	        msg = "帐号已被锁定";  
	        model.addAttribute("message", msg);  
	        logger.info(msg);
	    } catch (DisabledAccountException e) {  
	    	//2，已提交申请；3，审核未通过；5：已撤回
	    	staff = staffService.getStaffByAccount(staff.getStaff_account());
	    	if(staff.getExaminestatus() == 2){
	    		msg = "店铺未审核，请耐心等待审核！";  
	    	}else if(staff.getExaminestatus() == 3){
	    		msg = "店铺审核未通过，请联系客服！";  
	    	}if(staff.getExaminestatus() == 5){
	    		msg = "店铺账号已回收";  
	    	}
	        model.addAttribute("message", msg);   
	        logger.info(msg);
	    } catch (ExpiredCredentialsException e) {  
	        msg = "帐号已过期";  
	        model.addAttribute("message", msg);  
	        logger.info(msg);
	    } catch (UnknownAccountException e) {  
	        msg = "帐号不存在";  
	        model.addAttribute("message", msg);  
	        logger.info(msg);
	    } catch (UnauthorizedException e) {  
	        msg = "您没有得到相应的授权！" + e.getMessage();  
	        model.addAttribute("message", msg);  
	        logger.info(msg);
	    }catch(Exception e){
	    	e.printStackTrace();
	    	logger.error("登录异常："+e);
	    }
        model.addAttribute("staff_account", staff.getStaff_account());
        return  "/WEB-INF/login/deng"+roleType+".jsp";
    }
    
    @RequestMapping(value = "/register.do")
    public String register(){
    	return "/WEB-INF/register.jsp";
    }
    
    @RequestMapping(value = "/registerApp.do")
    public String registerApp(String code,String agencyCode,Model model){
    	model.addAttribute("code",code);
    	model.addAttribute("agencyCode",agencyCode);
    	return "/WEB-INF/registerApp.jsp";
    }
    
    @RequestMapping("/registerYn.do")
    public String registerYn() {
    	return "/WEB-INF/registerYn.jsp";
    }
    
    @RequestMapping(value = "/getSession.do")
    @ResponseBody
    public String getSession(){
    	logger.info("获取session值");
    	Map<String ,Object> jsonresult = new HashMap<String ,Object>();
    	try {
    		Subject subject = SecurityUtils.getSubject();
    		Session session = subject.getSession();
        	//获取用户信息
    		Staff staff = (Staff) session.getAttribute("staff");
    		jsonresult.put("staff",staff);
        	jsonresult.put("code", "200");//成功
        	jsonresult.put("error", "成功");
		} catch (Exception e) {
			logger.info("获取session值："+e.getMessage());
			jsonresult.put("code", "100");
			jsonresult.put("error", "失败");
		}
        return JSON.toJSONString(jsonresult);
    }
    
    @RequestMapping(value = "/updateSession.do")
    @ResponseBody
    public String updateSession(Long shop_unique,Integer shop_class,String login_shop_class,String login_shop_unique){
    	logger.info("修改session值");
    	Map<String ,Object> jsonresult = new HashMap<String ,Object>();
    	try {
    		Subject subject = SecurityUtils.getSubject();
    		Session session = subject.getSession();
        	//获取用户信息
    		Staff staff = (Staff) session.getAttribute("staff");
    		staff.setShop_unique(shop_unique);
    		staff.setShop_class(shop_class);
    		//获取店铺小程序开通状态
    		Map<String ,Object> shopInfo = (Map<String, Object>) shopService.queryShopMessage(String.valueOf(shop_unique)).getData();
    		staff.setShow_buy_status(Integer.parseInt(MUtil.strObject(shopInfo.get("show_buy_status"))));
    		session.setAttribute("staff", staff);
    		if("4".equals(login_shop_class)&&"0".equals(String.valueOf(shop_class))) {
    			//分店查看总店菜单
    			Map<String ,Object> params = new HashMap<String, Object>();
	   			params.put("version", "ordinary");
//	   			params.put("type", type);
	   			params.put("terminal", "web");
	   			params.put("seq", "1");//根据seq排序
    			List<SysPermission> listLevel1 = new ArrayList<SysPermission>();
	   			List<SysPermission> listLevel2 = new ArrayList<SysPermission>();
    			
	        	params.put("role_code", MUtil.strObject(roleService.getRoleByStaffId(staff.getStaff_id()).get("role_code")));
	        	//获取角色关联一级菜单列表
	    		params.put("level", "1");
    	    	params.put("shop_unique", login_shop_unique+"yll");
	        	listLevel1 = roleService.getMenuListByRoleCode(params);
	        	//获取角色关联二级菜单列表
	        	params.put("level", "2");
	        	listLevel2 = roleService.getMenuListByRoleCode(params);
	        	
    			session.setAttribute("listLevel1", listLevel1);
    			session.setAttribute("listLevel2", listLevel2);
    		}
    		if("4".equals(login_shop_class)&&"4".equals(String.valueOf(shop_class))) {
    			//分店查看总店菜单
    			Map<String ,Object> params = new HashMap<String, Object>();
	   			params.put("version", "ordinary");
//	   			params.put("type", type);
	   			params.put("terminal", "web");
	   			params.put("seq", "1");//根据seq排序
    			List<SysPermission> listLevel1 = new ArrayList<SysPermission>();
	   			List<SysPermission> listLevel2 = new ArrayList<SysPermission>();
    			
	        	params.put("role_code", MUtil.strObject(roleService.getRoleByStaffId(staff.getStaff_id()).get("role_code")));
	        	//获取角色关联一级菜单列表
	    		params.put("level", "1");
    	    	params.put("shop_unique", login_shop_unique);
	        	listLevel1 = roleService.getMenuListByRoleCode(params);
	        	//获取角色关联二级菜单列表
	        	params.put("level", "2");
	        	listLevel2 = roleService.getMenuListByRoleCode(params);
	        	
    			session.setAttribute("listLevel1", listLevel1);
    			session.setAttribute("listLevel2", listLevel2);
    		}    		    		    		
    		
        	jsonresult.put("code", "200");//成功
        	jsonresult.put("error", "成功");
		} catch (Exception e) {
			logger.info("修改session值："+e.getMessage());
			jsonresult.put("code", "100");
			jsonresult.put("error", "失败");
		}
    	//System.out.println(JSON.toJSONString(jsonresult));
        return JSON.toJSONString(jsonresult);
    }
    
//    @RequestMapping(value = "/index")
//    public String index(Model model,String lang){
//        Subject subject = SecurityUtils.getSubject();
//        try {
//            if (!subject.isAuthenticated()) {  
//            	return "redirect:/login";  
//	        }
//            model.addAttribute("lang", lang);
//        }catch(Exception e){
//        	logger.error("加载首页异常："+e);
//	    }
//        return "/index";
//    }
    

    @RequestMapping("/perfectInfoPage.do")
    public String perfectInfoPage(Model model) throws Exception {
    	//查询所有银行列表
    	List<Map<String,Object>> cardList = beanService.queryBankName();
    	model.addAttribute("cardList", cardList);
    	return "/WEB-INF/perfectInfo.jsp";
    }
    
    @RequestMapping("/perfectInfoPageApp.do")
    public String perfectInfoPageApp(Model model) throws Exception {
    	//查询所有银行列表
    	List<Map<String,Object>> cardList = beanService.queryBankName();
    	model.addAttribute("cardList", cardList);
    	return "/WEB-INF/perfectInfoApp.jsp";
    }
    
    @RequestMapping("/registerSuccess.do")
    public String registerSuccess() {
    	return "/WEB-INF/registerSuccess.jsp";
    }
}