package org.haier.shop.controller;

import java.util.HashMap;
import java.util.Map;

import javax.annotation.Resource;

import org.haier.shop.service.CountMsgNingyuService;
import org.haier.shop.util.PurResult;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

@Controller
@RequestMapping("/countMsgNingYu")
public class CountMsgNingYuController {
	@Resource
	private CountMsgNingyuService countMsgNingyuService;
	

	
	//店铺销量柱状图
	@RequestMapping("/queryNYsale.do")
	@ResponseBody
	public PurResult queryNYsale(String shop_unique,String create_time,String end_time,Integer page,
			Integer limit,String query_type){
		Map<String,Object> map=new HashMap<String,Object>();
		if(null != page && null != limit) {
			map.put("startNum", (page-1)*limit);
			map.put("pageSize", limit);
		}
		map.put("start_time", create_time);
		map.put("end_time", end_time);
		map.put("query_type", query_type);
		return countMsgNingyuService.queryNYsale(map);
	}
	//店铺菜品饼图
	@RequestMapping("/queryNYBar.do")
	@ResponseBody
		public PurResult queryNYBar(String shop_unique,String create_time,String end_time,String query_type){
			Map<String,Object> map=new HashMap<String,Object>();
			map.put("shop_unique", shop_unique);
			map.put("start_time", create_time);
			map.put("end_time", end_time);
			map.put("query_type", query_type);
			return countMsgNingyuService.queryNYBar(map);
		}
}
