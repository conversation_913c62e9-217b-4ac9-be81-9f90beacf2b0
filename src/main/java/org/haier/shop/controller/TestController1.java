package org.haier.shop.controller;


import java.util.Map;

import javax.servlet.http.HttpServletRequest;

import org.haier.shop.util.ShopsResult;
import org.haier.shop.util.UtilForFace;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;

import net.sf.json.JSONObject;

@Controller
public class TestController1 {
	
	@RequestMapping("/test.do")
	@ResponseBody
	public ShopsResult test(
			String api_secret,
			String api_key,
			String faceset_token,
			String face_tokens,
			String display_name,String outer_id,String tags,String user_data
			){
		ShopsResult sr=new ShopsResult();
		JSONObject object=UtilForFace.createFaceMap(display_name,outer_id,tags,user_data);
		sr.setData(object);
		return sr;
	}
	
	@RequestMapping("/dataScreen.do")
	public String dataScreen(){
		return "/WEB-INF/dataScreen2.jsp";
	}
	
	@RequestMapping("/dataScreenYN.do")
	public String dataScreenYN() {
		return "/WEB-INF/dataScreenYN.jsp";
	}
	
	@RequestMapping("/map.do")
	public String map() {
		return "/WEB-INF/map.jsp";
	}
 
	@RequestMapping("/test11.do")
	@ResponseBody
	public ShopsResult test1(HttpServletRequest request){
		ShopsResult sr=new ShopsResult();
		MultipartFile file=null;
		if( request instanceof MultipartHttpServletRequest){
			MultipartHttpServletRequest multipartHttpServletRequest =(MultipartHttpServletRequest)request;
			Map<String, MultipartFile> mp = multipartHttpServletRequest.getFileMap();
			file = mp.get("picture");
		}
		if(file==null){
			sr.setStatus(2);
			return sr;
		}
		JSONObject object=UtilForFace.detectImg(null);
		
		sr.setStatus(1);
		sr.setMsg("测试成功！");
		sr.setData(object);
		return sr;
	}
	
	@RequestMapping("/test1.do")
	@ResponseBody
	public ShopsResult test1(Integer flagiii){
		System.out.println("test1.do");
		if(null!=flagiii){
			System.out.println(flagiii);
		}
		return new ShopsResult(1,"成功！");
	}
	@RequestMapping("/test2.do")
	@ResponseBody
	public ShopsResult test2(Integer flagiii){
		System.out.println("test21.do");
		if(null!=flagiii){
			System.out.println(flagiii);
		}
		return new ShopsResult(1,"成功！");
	}
	
	@RequestMapping("/test3.do")
	@ResponseBody
	public ShopsResult test3(Integer flagiii){
		if(null!=flagiii){
			System.out.println(flagiii);
		}
		System.out.println("test3.do");
		return new ShopsResult(1,"成功！");
	}
}
