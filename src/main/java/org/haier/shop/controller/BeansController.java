package org.haier.shop.controller;


import io.goeasy.GoEasy;

import java.io.BufferedOutputStream;
import java.io.BufferedReader;
import java.io.FileInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.io.PrintWriter;
import java.io.UnsupportedEncodingException;
import java.math.BigDecimal;
import java.math.MathContext;
import java.net.InetAddress;
import java.net.UnknownHostException;
import java.security.KeyStore;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.Hashtable;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.SortedMap;
import java.util.TreeMap;

import javax.annotation.Resource;
import javax.net.ssl.SSLContext;
import javax.servlet.ServletException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import net.sf.json.JSONObject;

import org.apache.http.HttpEntity;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.conn.ssl.SSLConnectionSocketFactory;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.shiro.SecurityUtils;
import org.apache.shiro.session.Session;
import org.apache.shiro.subject.Subject;
import org.dom4j.Document;
import org.dom4j.Element;
import org.dom4j.io.SAXReader;
import org.haier.customer.entity.OrderPayResultVO;
import org.haier.customer.entity.OrderPaySendVO;
import org.haier.customer.entity.Shops;
import org.haier.log.annotation.RemoteLog;
import org.haier.log.enums.BusinessType;
import org.haier.meituan.util.MUtil;
import org.haier.shop.dao.BeansDao;
import org.haier.shop.entity.BuyBeansVO;
import org.haier.shop.entity.ShopBeansVO;
import org.haier.shop.entity.Staff;
import org.haier.shop.entity.TiCashVO;
import org.haier.shop.entity.beans.Beans;
import org.haier.shop.entity.beans.PageData;
import org.haier.shop.service.BeanService;
import org.haier.shop.service.LoanMoneyService;
import org.haier.shop.service.ShopService;
import org.haier.shop.service.SupplierShoppingService;
import org.haier.shop.service.SysAgreementService;
import org.haier.shop.util.AlipayConfig;
import org.haier.shop.util.BeansRule;
import org.haier.shop.util.Page;
import org.haier.shop.util.PageQuery;
import org.haier.shop.util.PurResult;
import org.haier.shop.util.ShopsResult;
import org.haier.shop.util.WeChatPayAPI;
import org.haier.shop.util.wxPay.PayConfigUtil;
import org.haier.shop.util.wxPay.PayToolUtil;
import org.haier.shop.util.wxPay.QRUtil;
import org.haier.shop.util.wxPay.XMLUtil4jdom;
import org.jdom.JDOMException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.servlet.ModelAndView;

import com.alibaba.fastjson.JSON;
import com.alipay.api.AlipayApiException;
import com.alipay.api.AlipayClient;
import com.alipay.api.DefaultAlipayClient;
import com.alipay.api.internal.util.AlipaySignature;
import com.alipay.api.internal.util.codec.Base64;
import com.alipay.api.request.AlipayTradePagePayRequest;
import com.google.zxing.BarcodeFormat;
import com.google.zxing.EncodeHintType;
import com.google.zxing.MultiFormatWriter;
import com.google.zxing.WriterException;
import com.google.zxing.common.BitMatrix;

/**
 * 百货豆业务持久层
 * <AUTHOR>
 */
@RequestMapping("/beans")
@Controller
@SuppressWarnings({ "unused" })

public class BeansController extends Beans{
	@Resource
	private BeanService beanService;
	@Resource
	private BeansDao beansDao;
	
	@Resource
	private SysAgreementService sysAgreementService;
	
	@Resource
	private ShopService shopService;
	
	
	@Resource
	private LoanMoneyService loanService;
	
	@Autowired
	private SupplierShoppingService supplierShoppingService;
	@Autowired
	HttpServletRequest request;
	
	
	@RequestMapping("/queryBankNameList.do")
	@ResponseBody
	public ShopsResult queryBankNameList() {
		ShopsResult sr = new ShopsResult(1,"查询成功!");
		try {
			sr.setData(beanService.queryBankName());
		} catch (Exception e) {
			e.printStackTrace();
		}
		return sr;
	}
	
	/**
	 * 商家-百货豆管理页面B01
	 * <AUTHOR>
	 * @param page
	 * @return
	 * @throws Exception
	 */
	@RequestMapping("/querybeans.do")
	public ModelAndView querybeans(Page page,String shopUnique) throws Exception{
		ModelAndView mv = new ModelAndView();
		PageData pd = new PageData();
		pd = this.getPageData();
		//Long shop_unique=(Long) this.getRequest().getSession().getAttribute("shop_unique");
		if(shopUnique!=null)
		{
			pd.put("shop_unique", shopUnique);
			page.setPd(pd);
			mv.addObject("pd", pd);
			
			if(shopUnique.equals("*************"))
			{
				PageData beansDetail = beanService.getPtBeans(shopUnique);	//店铺百货豆信息
				mv.addObject("beans", beansDetail);
				mv.setViewName("/WEB-INF/beans/queryBeanMain.jsp");
			}
			else 
			{
				int beans_agreement=beanService.getAgreement(shopUnique);
				if(beans_agreement==1)
				{
					PageData beansDetail = beanService.getBeans(shopUnique);	//店铺百货豆信息
					mv.addObject("beans", beansDetail);
					mv.setViewName("/WEB-INF/beans/queryBeans.jsp");
				}else
				{
					//获取百货豆开通协议
					Map<String ,Object> agreement = sysAgreementService.querySysAgreement("beans");
					mv.addObject("agreement", agreement);
					mv.setViewName("/WEB-INF/beans/protocol.jsp");
				}
					

			}
			
		}

		return mv;
		
	}
	
	/**
	 * 商家-开通百货豆协议
	 * <AUTHOR>
	 * @param page
	 * @return
	 * @throws Exception
	 */
	@RequestMapping("/updateProtocol.do")
	public ModelAndView updateProtocol() throws Exception{
		ModelAndView mv = new ModelAndView();
		PageData pd = new PageData();
		pd = this.getPageData();
		//开通百货豆协议
		int beans_agreement=beanService.updateProtocol(pd);
		
				
				if(beans_agreement>0)
				{	//查询平台配置的抵扣规则
					
					PageData beansDetail = beanService.getBeans(pd.getString("shopUnique"));	//店铺百货豆信息
					mv.addObject("beans", beansDetail);
					mv.setViewName("forward:/WEB-INF/beans/queryBeans.jsp");
					return mv;
				}
		return mv;
		
	}
	
	/**
	 * 商家-百货豆管理-内嵌页面1-历史记录
	 * <AUTHOR>
	 * @param page
	 * @return
	 * @throws Exception
	 */
	@RequestMapping("/getMyLi1.do")
	public ModelAndView getMyLi1(Page page,String shop_unique) throws Exception{
		ModelAndView mv = new ModelAndView();
		PageData pd = new PageData();
		pd = this.getPageData();
		if(shop_unique!=null)
		{
			mv.setViewName("/WEB-INF/beans/myLi1.jsp");
			
		}else
		{
			mv.setViewName("/WEB-INF/login_new.jsp");
		}
		
		pd.put("shop_unique", shop_unique);
		page.setPd(pd);
		int count=beanService.getTransactionListCount(page);//记录条数
		page.setTotalResult(count);
		int orderPages=beanService.queryCusCount(pd);
		mv.addObject("pd", pd);
		mv.addObject("orderPages",orderPages);
		return mv;
		
	}
	/**
	 * 商家-百货豆管理-内嵌页面2-领取规则
	 * <AUTHOR>
	 * @param page
	 * @return
	 * @throws Exception
	 */
	@RequestMapping("/getMyLi2.do")
	public ModelAndView getMyLi2(Page page,String shop_unique) throws Exception{
		ModelAndView mv = new ModelAndView();
		PageData pd = new PageData();
		pd = this.getPageData();
		if(shop_unique!=null)
		{
			mv.setViewName("/WEB-INF/beans/myLi2.jsp");
			pd.put("shop_unique", shop_unique);
			page.setPd(pd);
			BeansRule beansRule=beanService.queryBenasRule(pd);//店铺百货豆领取规则
			if(beansRule==null)
			{
				beansRule=new BeansRule();
			}
			mv.addObject("rule", beansRule);
			mv.addObject("pd", pd);
			
		}else
		{
			mv.setViewName("/WEB-INF/login_new.jsp");
		}

		return mv;
		
	}
	/**
	 * 商家-百货豆管理-内嵌页面2-提现
	 * <AUTHOR>
	 * @return
	 * @throws Exception
	 */
	@RequestMapping("/getMyLi3.do")
	public ModelAndView getMyLi3(String shop_unique) throws Exception{
		
		
		ModelAndView mv = this.getModelAndView();
		PageData pd = new PageData();
		if(shop_unique!=null)
		{
			ShopBeansVO beanDetai=beanService.getShopBeans(shop_unique);
			List<Map<String,Object>> cardList=beanService.queryBankCard(shop_unique);
			if(beanDetai!=null)
			{
				long a=beanDetai.getBeans_old_count()*(100-beanDetai.getPt_tax())/100;
				long moneyTop=a/100;
				
				if(moneyTop<beanDetai.getS_moeny())
				{
					moneyTop=0;
				}else if(moneyTop>beanDetai.getM_moeny())
				{
					moneyTop=beanDetai.getM_moeny();
				}
				
				pd = this.getPageData();
				int tx_dtae=beanDetai.getTx_dtae();
				int tx_times=beanDetai.getTx_times();
				//查询最近tx_dtae日的交易次数
				int tixianCount=beanService.queryTransactionCount(shop_unique,tx_dtae);
				int tiXianCount=tx_times-tixianCount;
				if(tiXianCount<0)
				{
					tiXianCount=0;
				}
				mv.addObject("tiXianCount", tiXianCount);
				mv.addObject("moneyTop", moneyTop);
				
			}else
			{
				mv.addObject("tiXianCount", 0);
				mv.addObject("moneyTop", 0);
			}
			
			mv.addObject("cardList", cardList);
			pd.put("shop_unique", shop_unique);

			mv.addObject("beanDetai", beanDetai);

			mv.addObject("pd", pd);
	
			mv.setViewName("/WEB-INF/beans/myLi3.jsp");
		}else
		{
			mv.setViewName("../manager/loginout.do");
		}

		return mv;
	}
	
	@RequestMapping("/queryBeansConfig.do")
	@ResponseBody
	public ShopsResult queryBeansConfig(String shop_unique)throws Exception{
		
		ShopsResult result=new ShopsResult();
		Map<String,Object> data=new HashMap<>();
		if(shop_unique!=null)
		{
			ShopBeansVO beanDetai=beanService.getShopBeans(shop_unique);
			List<Map<String,Object>> cardList=beanService.queryBankCard(shop_unique);
			if(beanDetai!=null)
			{
				long a=beanDetai.getBeans_old_count()*(100-beanDetai.getPt_tax())/100;
				long moneyTop=a/100;
				
				if(moneyTop<beanDetai.getS_moeny())
				{
					moneyTop=0;
				}else if(moneyTop>beanDetai.getM_moeny())
				{
					moneyTop=beanDetai.getM_moeny();
				}
				
				int tx_dtae=beanDetai.getTx_dtae();
				int tx_times=beanDetai.getTx_times();
				//查询最近tx_dtae日的交易次数
				int tixianCount=beanService.queryTransactionCount(shop_unique,tx_dtae);
				int tiXianCount=tx_times-tixianCount;
				if(tiXianCount<0)
				{
					tiXianCount=0;
				}
				data.put("tiXianCount", tiXianCount);
				data.put("moneyTop", moneyTop);
				
			}else
			{
				data.put("tiXianCount", 0);
				data.put("moneyTop", 0);
			}
			
			data.put("beanDetai", beanDetai);

			result.setData(data);
			result.setStatus(1);
		}
		return result;
	}
	
	
	/**
	 * 百货豆交易记录-分页
	 * <AUTHOR>
	 * @param page
	 * @return
	 * @throws Exception
	 */
	@RequestMapping("/page/getTransactionList.do")
	@ResponseBody
	public String getTransactionList(Page page) throws Exception{
		ModelAndView mv = new ModelAndView();
		PageData pd = new PageData();
		pd = this.getPageData();
		
		int currentPage=page.getCurrentPage();
		//Long shop_unique=(Long) this.getRequest().getSession().getAttribute("shop_unique");
		//pd.put("shop_unique", shop_unique);
		page.setPd(pd);
		int count=beanService.getTransactionListCount(page);
		page.setTotalResult(count);
		int startNum=Integer.parseInt(pd.getString("currentPage"));
		int pageSize=Integer.parseInt(pd.getString("showCount"));
		
		if(count%pageSize==0)
			page.setTotalPage(count/pageSize);
		else
		{
			page.setTotalPage(count/pageSize+1);
		}
		
		page.setCurrentPage(startNum*pageSize);
		page.setShowCount(pageSize);
		List<PageData> payList=beanService.getTransactionList(page);  //店铺交易记录列表
		
		mv.addObject("page",page);
		mv.addObject("pd", pd);
		mv.addObject("payList", payList);
		mv.addObject("status","0");
		
		return JSON.toJSONString(mv);
	}
	
	/**
	 * 分页查询店铺交易记录-购买记录
	 * <AUTHOR>
	 * @return
	 */
	@RequestMapping("/page/queryTransactionList.do")
	@ResponseBody
	public ShopsResult queryTransactionList(PageQuery page){
		
		//String shop_unique=this.getRequest().getSession().getAttribute("shop_unique").toString();
		//page.setShopUnique(shop_unique);
		//System.out.println("当前查询讯息！"+page);
		return beanService.queryTransactionList(page);
	};
	/**
	 * 分页查询店铺交易记录-提现记录
	 * <AUTHOR>
	 * @return
	 */
	@RequestMapping("/page/queryTransactionList2.do")
	@ResponseBody
	public PurResult queryTransactionList2(PageQuery pageQuery,
			@RequestParam(value = "page", defaultValue = "1") int page,
			@RequestParam(value = "limit", defaultValue = "8") int pageSize){
		pageQuery.setCurrentPage(page);
		pageQuery.setShowCount(pageSize);
		return beanService.queryTransactionList2(pageQuery);
	}
	/**
	 * 分页查询店铺交易记录-提现记录
	 * <AUTHOR>
	 * @return
	 */
	@RequestMapping("/page/queryShopBeansPromation.do")
	@ResponseBody
	public PurResult queryShopBeansPromation(PageQuery pageQuery,
			@RequestParam(value = "page", defaultValue = "1") int page,
			@RequestParam(value = "limit", defaultValue = "8") int pageSize){
		pageQuery.setCurrentPage(page);
		pageQuery.setShowCount(pageSize);
		return beanService.queryShopBeansPromation(pageQuery);
	}
	/**
	 * 微信支付-test
	 * <AUTHOR>
	 * @return
	 * @throws ServletException
	 * @throws IOException
	 */
	@RequestMapping("/weixin/pay.do")
	public ModelAndView  weixinPay()throws ServletException, IOException 
	{  	
		ModelAndView mv = this.getModelAndView();
		PageData pd = new PageData();
		pd = this.getPageData();
		
		OrderPaySendVO orderPaySendVO = new OrderPaySendVO();
		orderPaySendVO.setAttach("");//附加数据
		orderPaySendVO.setBody("百货豆");//商品描述
		orderPaySendVO.setDetail(JSONObject.fromObject(pd).toString());//商品详情
		orderPaySendVO.setNotifyUrl("https://www.lvgreen.top:8833/shop/beans/index.jsp");
		orderPaySendVO.setOutTradeNo("123456");//商户订单号
		orderPaySendVO.setSpbillCreateIp(getIpAddr(request));//终端IP
		orderPaySendVO.setTotalFee(0.01f);//总金额
		orderPaySendVO.setOpenid("");//用户标识
		orderPaySendVO.setFee_type("CYN");//用户标识
		OrderPayResultVO orderPayResultVO = WeChatPayAPI.getInstance().orderPay(orderPaySendVO);
		
		
		mv.addObject("pd", pd);
		mv.addObject("orderPayResultVO", orderPayResultVO);
		mv.setViewName("system/buttonrights/buttonrights_list");
		
		return mv;
	}
	
	/**
	 * 分页查询会员交易记录3-发放记录
	 * <AUTHOR>
	 * @param pageQuery
	 * @return
	 */
	
	@ResponseBody
	@RequestMapping("/pages/queryOrderListByPage.do")
	public ShopsResult queryOrderListByPage(PageQuery pageQuery){
		return beanService.queryOrderListByPage(pageQuery);
	}
	
	/**
	 * 分页查询会员交易记录4-抵扣记录
	 * <AUTHOR>
	 * @param pageQuery
	 * @return
	 */
	
	@ResponseBody
	@RequestMapping("/pages/queryOrderListByPage2.do")
	public PurResult queryOrderListByPage2(PageQuery pageQuery,
			@RequestParam(value = "page", defaultValue = "1") int page,
			@RequestParam(value = "limit", defaultValue = "8") int pageSize){
		pageQuery.setCurrentPage(page);
		pageQuery.setShowCount(pageSize);
		return beanService.queryOrderListByPage2(pageQuery);
	}
	
	/**
	 * 获取当前网络ip
	 * <AUTHOR>
	 * @param request
	 * @return
	 */
	public static String getIpAddr(HttpServletRequest request) {
		String ipAddress = request.getHeader("x-forwarded-for");
		if (ipAddress == null || ipAddress.length() == 0 || "unknown".equalsIgnoreCase(ipAddress)) {
			ipAddress = request.getHeader("Proxy-Client-IP");
		}
		if (ipAddress == null || ipAddress.length() == 0 || "unknown".equalsIgnoreCase(ipAddress)) {
			ipAddress = request.getHeader("WL-Proxy-Client-IP");
		}
		if (ipAddress == null || ipAddress.length() == 0 || "unknown".equalsIgnoreCase(ipAddress)) {
			ipAddress = request.getRemoteAddr();
			if (ipAddress.equals("127.0.0.1") || ipAddress.equals("0:0:0:0:0:0:0:1")) {
				// 根据网卡取本机配置的IP
				InetAddress inet = null;
				try {
					inet = InetAddress.getLocalHost();
				} catch (UnknownHostException e) {
					e.printStackTrace();
				}
				ipAddress = inet.getHostAddress();
			}
		}
		// 对于通过多个代理的情况，第一个IP为客户端真实IP,多个IP按照','分割
		if (ipAddress != null && ipAddress.length() > 15) { // "***.***.***.***".length()
															// = 15
			if (ipAddress.indexOf(",") > 0) {
				ipAddress = ipAddress.substring(0, ipAddress.indexOf(","));
			}
		}
		return ipAddress;
	}
	
	/**
	 * 微信支付-生产二维码
	 * <AUTHOR>
	 * @param request
	 * @param response
	 * @param modelMap
	 */
	@RequestMapping("/qrcode.do")
	@ResponseBody
	@SuppressWarnings({ "rawtypes", "unchecked" })
	public void qrcode(HttpServletRequest request, HttpServletResponse response,ModelMap modelMap) {
		try {
			String data = request.getParameter("pass");
			byte[] decode = java.util.Base64.getDecoder().decode(data.getBytes("utf-8"));
			String json=new String(decode);
			JSONObject json_test = JSONObject.fromObject(json); 
			BuyBeansVO cashVO=(BuyBeansVO)JSONObject.toBean(json_test, BuyBeansVO.class);
			
	        String productId = cashVO.getProductId();
	        String beans_old_count=cashVO.getBeans_old_count();
	        String payMoeny=cashVO.getPayMoeny();
	        String out_trade_no=cashVO.getOut_trade_no();
	        String shop_unique=cashVO.getShop_unique();
	        String spbill_create_ip=getIpAddr(request);
	       
	        
	        String text = beanService.weixinPay(shop_unique, productId,spbill_create_ip,beans_old_count,payMoeny,out_trade_no); 
	        
	        int width = 300; 
	        int height = 300; 
	        //二维码的图片格式 
	        String format = "gif"; 
	        
			Hashtable hints = new Hashtable(); 
	        //内容所使用编码 
	        hints.put(EncodeHintType.CHARACTER_SET, "utf-8");
	        BitMatrix bitMatrix;
			try {
				bitMatrix = new MultiFormatWriter().encode(text, BarcodeFormat.QR_CODE, width, height, hints);
				QRUtil.writeToStream(bitMatrix, format, response.getOutputStream());
			} catch (WriterException e) {
				e.printStackTrace();
			}
			
		} catch (Exception e) {
		}
	}
	/**
	 * 
	 * <AUTHOR>
	 * @return
	 * @throws Exception
	 */
	@ResponseBody
	@RequestMapping("/hadPay.do")
	public ShopsResult hadPay() throws Exception{
		ShopsResult sr=new ShopsResult(1,"成功！");
		try {
			PageData pd = new PageData();
			pd = this.getPageData();
			
			String out_trade_no=pd.getString("out_trade_no");
//			System.out.println(pd);
			PageData order = beanService.findOneByTradeCode(out_trade_no);
			//简单的业务逻辑：在微信的回调接口里面，已经定义了，回调返回成功的话，那么 _PAY_RESULT 不为空
			if(order != null&&order.getInt("receive_state") == 1){
				
			}else
			{
				sr.setStatus(0);
				sr.setMsg("支付失败");
			}
		} catch (Exception e) {
		}
		return sr;
	}
	 

	/**
	 * 微信平台发起的回调方法，
	 * 调用我们这个系统的这个方法接口，将扫描支付的处理结果告知我们系统
	 * <AUTHOR>
	 * @throws JDOMException
	 * @throws Exception
	 */
	@SuppressWarnings({ "unchecked", "rawtypes" })
	@RequestMapping(value = "weixinNotify.do", produces = "application/json;charset=UTF-8")
	@ResponseBody
	public void weixinNotify(HttpServletRequest request, HttpServletResponse response) throws  Exception{
        //读取参数 
//		System.out.println("回调成功");
        InputStream inputStream ;  
        StringBuffer sb = new StringBuffer();  
        inputStream = request.getInputStream();  
        String s ;  
        BufferedReader in = new BufferedReader(new InputStreamReader(inputStream, "UTF-8"));  
        while ((s = in.readLine()) != null){  
            sb.append(s);
        }
        in.close();
        inputStream.close();
  
        //解析xml成map  
        Map<String, String> m = new HashMap<String, String>();  
        m = XMLUtil4jdom.doXMLParse(sb.toString());  
        
        //过滤空 设置 TreeMap  
        SortedMap<Object,Object> packageParams = new TreeMap<Object,Object>();        
		Iterator it = m.keySet().iterator();  
        while (it.hasNext()) {  
            String parameter = (String) it.next();
            String parameterValue = m.get(parameter);
            
            String v = "";  
            if(null != parameterValue) {
                v = parameterValue.trim();  
            }  
            packageParams.put(parameter, v);  
        }  
          
        // 账号信息  
        String key = PayConfigUtil.API_KEY; //key  
  
        //判断签名是否正确  
        if(PayToolUtil.isTenpaySign("UTF-8", packageParams,key)) {  
            //------------------------------  
            //处理业务开始  
            //------------------------------  
            String resXml = "";  
            GoEasy goeasy=new GoEasy("BC-93cd649e09404a8e9427fd540fdf5acf",null);
            if("SUCCESS".equals((String)packageParams.get("result_code"))){  
                // 这里是支付成功  
                //////////执行自己的业务逻辑////////////////  
                String mch_id = (String)packageParams.get("mch_id");  
                String openid = (String)packageParams.get("openid");  
                String is_subscribe = (String)packageParams.get("is_subscribe");  
                String out_trade_no = (String)packageParams.get("out_trade_no");  
                
                String total_fee = (String)packageParams.get("total_fee");  
                String shop_unique =out_trade_no.substring(0,13);
                
                //////////执行自己的业务逻辑//////////////// 
                //暂时使用最简单的业务逻辑来处理：只是将业务处理结果保存到session中
                //（根据自己的实际业务逻辑来调整，很多时候，我们会操作业务表，将返回成功的状态保留下来）
                request.getSession().setAttribute("_PAY_RESULT", "OK");
                
                PageData order = beanService.findOneByTradeCode(out_trade_no);
                if(order != null&&order.getInt("receive_state") != 1)
                {
                	 
                	goeasy.publish(shop_unique,"支付成功，订单号："+out_trade_no);
                	order.put("receive_state", 1);
					beanService.payOrder(order);
                }
//                System.out.println("支付成功");  
                //通知微信.异步确认成功.必写.不然会一直通知后台.八次之后就认为交易失败了.  
                resXml = "<xml>" + "<return_code><![CDATA[SUCCESS]]></return_code>"  
                        + "<return_msg><![CDATA[OK]]></return_msg>" + "</xml> ";  
                  
            } else {
                resXml = "<xml>" + "<return_code><![CDATA[FAIL]]></return_code>"  
                        + "<return_msg><![CDATA[报文为空]]></return_msg>" + "</xml> ";  
                
            }
            //------------------------------  
            //处理业务完毕  
            //------------------------------  
            BufferedOutputStream out = new BufferedOutputStream(response.getOutputStream());  
            out.write(resXml.getBytes());  
            out.flush();  
            out.close();  
        } else{  
        	System.out.println("通知签名验证失败");  
        }
    }
	/**
	 * 微信平台发起的回调方法，
	 * 调用我们这个系统的这个方法接口，将扫描支付的处理结果告知我们系统
	 * <AUTHOR>
	 * @throws JDOMException
	 * @throws Exception
	 */
	@SuppressWarnings({ "unchecked", "rawtypes" })
	@RequestMapping(value = "weixinNotifyReturnMoney.do", produces = "application/json;charset=UTF-8")
	@ResponseBody
	public void weixinNotifyReturnMoney(HttpServletRequest request, HttpServletResponse response) throws  Exception{
		//读取参数 
//		System.out.println("回调成功");
		InputStream inputStream ;  
		StringBuffer sb = new StringBuffer();  
		inputStream = request.getInputStream();  
		String s ;  
		BufferedReader in = new BufferedReader(new InputStreamReader(inputStream, "UTF-8"));  
		while ((s = in.readLine()) != null){  
			sb.append(s);
		}
		in.close();
		inputStream.close();
		
		//解析xml成map  
		Map<String, String> m = new HashMap<String, String>();  
		m = XMLUtil4jdom.doXMLParse(sb.toString());  
		
		//过滤空 设置 TreeMap  
		SortedMap<Object,Object> packageParams = new TreeMap<Object,Object>();        
		Iterator it = m.keySet().iterator();  
		while (it.hasNext()) {  
			String parameter = (String) it.next();
			String parameterValue = m.get(parameter);
			
			String v = "";  
			if(null != parameterValue) {
				v = parameterValue.trim();  
			}  
			packageParams.put(parameter, v);  
		}  
		
		// 账号信息  
		String key = PayConfigUtil.API_KEY; //key  
		
		//判断签名是否正确  
			//------------------------------  
			//处理业务开始  
			//------------------------------  
			String resXml = "";  
			if("SUCCESS".equals((String)packageParams.get("result_code"))){  
				// 这里是支付成功  
				//////////执行自己的业务逻辑////////////////  
				String mch_id = (String)packageParams.get("mch_id");  
				String openid = (String)packageParams.get("openid");  
				String is_subscribe = (String)packageParams.get("is_subscribe");  
				String out_trade_no = (String)packageParams.get("out_trade_no");  
				
				String total_fee = (String)packageParams.get("total_fee");  
				String shop_unique =out_trade_no.substring(0,13);
				
				//////////执行自己的业务逻辑//////////////// 
				//暂时使用最简单的业务逻辑来处理：只是将业务处理结果保存到session中
				//（根据自己的实际业务逻辑来调整，很多时候，我们会操作业务表，将返回成功的状态保留下来）
				request.getSession().setAttribute("_PAY_RESULT", "OK");
				
				loanService.updateReturnMoney(out_trade_no);
		
//                System.out.println("支付成功");  
				//通知微信.异步确认成功.必写.不然会一直通知后台.八次之后就认为交易失败了.  
				resXml = "<xml>" + "<return_code><![CDATA[SUCCESS]]></return_code>"  
						+ "<return_msg><![CDATA[OK]]></return_msg>" + "</xml> ";  
				
			} else {
				resXml = "<xml>" + "<return_code><![CDATA[FAIL]]></return_code>"  
						+ "<return_msg><![CDATA[报文为空]]></return_msg>" + "</xml> ";  
				
			}
			//------------------------------  
			//处理业务完毕  
			//------------------------------  
			BufferedOutputStream out = new BufferedOutputStream(response.getOutputStream());  
			out.write(resXml.getBytes());  
			out.flush();  
			out.close();  
		
	}
	/**
	 * 微信平台发起的回调方法，
	 * 调用我们这个系统的这个方法接口，将扫描支付的处理结果告知我们系统
	 * <AUTHOR>
	 * @throws JDOMException
	 * @throws Exception
	 */
	@SuppressWarnings({ "unchecked", "rawtypes" })
	@RequestMapping(value = "weixinNotifyReturnMoneyYN.do", produces = "application/json;charset=UTF-8")
	@ResponseBody
	public void weixinNotifyReturnMoneyYN(HttpServletRequest request, HttpServletResponse response) throws  Exception{
		//读取参数 
		System.out.println("助农商城微信支付回调");
		InputStream inputStream ;  
		StringBuffer sb = new StringBuffer();  
		inputStream = request.getInputStream();  
		String s ;  
		BufferedReader in = new BufferedReader(new InputStreamReader(inputStream, "UTF-8"));  
		while ((s = in.readLine()) != null){  
			sb.append(s);
		}
		in.close();
		inputStream.close();
		
		//解析xml成map  
		Map<String, String> m = new HashMap<String, String>();  
		m = XMLUtil4jdom.doXMLParse(sb.toString());  
		
		//过滤空 设置 TreeMap  
		SortedMap<Object,Object> packageParams = new TreeMap<Object,Object>();        
		Iterator it = m.keySet().iterator();  
		while (it.hasNext()) {  
			String parameter = (String) it.next();
			String parameterValue = m.get(parameter);
			
			String v = "";  
			if(null != parameterValue) {
				v = parameterValue.trim();  
			}  
			packageParams.put(parameter, v);  
		}  
		
		// 账号信息  
		String key = PayConfigUtil.API_KEY; //key  
		System.out.println("回调参数"+packageParams.toString());
		//判断签名是否正确  
			//------------------------------  
			//处理业务开始  
			//------------------------------  
			String resXml = "";  
			if("SUCCESS".equals((String)packageParams.get("result_code"))){  
				// 这里是支付成功  
				//////////执行自己的业务逻辑////////////////  
				String mch_id = (String)packageParams.get("mch_id");  
				String openid = (String)packageParams.get("openid");  
				String is_subscribe = (String)packageParams.get("is_subscribe");  
				String out_trade_no = (String)packageParams.get("out_trade_no");  
				
				String total_fee = (String)packageParams.get("total_fee");  
				String shop_unique =out_trade_no.substring(0,13);
				
				//////////执行自己的业务逻辑//////////////// 
				//暂时使用最简单的业务逻辑来处理：只是将业务处理结果保存到session中
				//（根据自己的实际业务逻辑来调整，很多时候，我们会操作业务表，将返回成功的状态保留下来）
				request.getSession().setAttribute("_PAY_RESULT", "OK");
				
				//支付成功，更新订单信息
				//支付成功
				Map<String, Object> params=new HashMap<String, Object>();
				String orderNo = out_trade_no;
				if(orderNo.indexOf("SUB")!=-1){
					orderNo = orderNo.substring(0,orderNo.indexOf("SUB"));
				}
				params.put("order_code", orderNo);
				params.put("main_order_no", orderNo);
				params.put("pay_date",new Date());
				params.put("pay_status","2");
				params.put("order_status","1" );
				params.put("pay_type","3" );
				params.put("pay_mode", 6);
				System.out.println(params.toString());
				supplierShoppingService.updateShoppingOrderYN(params);
				
//                System.out.println("支付成功");  
				//通知微信.异步确认成功.必写.不然会一直通知后台.八次之后就认为交易失败了.  
				resXml = "<xml>" + "<return_code><![CDATA[SUCCESS]]></return_code>"  
						+ "<return_msg><![CDATA[OK]]></return_msg>" + "</xml> ";  
				
			} else {
				resXml = "<xml>" + "<return_code><![CDATA[FAIL]]></return_code>"  
						+ "<return_msg><![CDATA[报文为空]]></return_msg>" + "</xml> ";  
				
			}
			//------------------------------  
			//处理业务完毕  
			//------------------------------  
			BufferedOutputStream out = new BufferedOutputStream(response.getOutputStream());  
			out.write(resXml.getBytes());  
			out.flush();  
			out.close();  
		
	}
	
	/**
	 * 商家-增-百货豆获取规则记录
	 * <AUTHOR>
	 * @return
	 * @throws Exception
	 */
	@RequestMapping("/addRule.do")
	@ResponseBody
	public ShopsResult addBeansRule() throws Exception{
			PageData pd = new PageData();
			pd = this.getPageData();
			return beanService.addBenasRule(pd);
		}

	/**
	 * 商家-删-百货豆获取规则记录
	 * <AUTHOR>
	 * @return
	 * @throws Exception
	 */
	@RequestMapping("/deleteRule.do")
	@ResponseBody
	public ShopsResult deleteBeansRule() throws Exception{
			PageData pd = new PageData();
			pd = this.getPageData();
			return beanService.deleteBeansRule(pd);
		}
	/**
	 * 商家-改-百货豆获取规则记录
	 * <AUTHOR>
	 * @return
	 * @throws Exception
	 */
	@RequestMapping("/updateRule.do")
	@ResponseBody
	public ShopsResult updateBeansRule() throws Exception{
			PageData pd = new PageData();
			pd = this.getPageData();
			return beanService.updateBeansRule(pd);
		}
	/**
	 * 购买百货豆
	 * <AUTHOR>
	 * @return
	 * @throws Exception
	 */
	@RequestMapping("/buy.do")
	@ResponseBody
	public ShopsResult buyBeans() throws Exception {
		
		return null;
	}
	
	/**
	 * 跳转到百货豆提现页面
	 * <AUTHOR>
	 * @return
	 * @throws Exception
	 */
	@RequestMapping("/getCash")
	@ResponseBody
	public ModelAndView getCash() throws Exception {
		
		ModelAndView mv = this.getModelAndView();
		PageData pd = new PageData();
		pd = this.getPageData();
		mv.addObject("pd", pd);
		mv.setViewName("../WEB-INF/beans/getCash.jsp");
		return mv;
	}
	/**
	 * 百货豆提现
	 * <AUTHOR>
	 * @return
	 * @throws Exception
	 */
	@RequestMapping("/addCash.do")
	@ResponseBody
	public ShopsResult addCash() throws Exception{
		PageData pd = new PageData();
		pd = this.getPageData();
		return beanService.addCash(pd);
	}
	/**
	 * 修改抵扣规则
	 * <AUTHOR>
	 * @return
	 * @throws Exception
	 */
	@RemoteLog(title = "修改百货豆规则", businessType = BusinessType.UPDATE)
	@RequestMapping("/updateDiKou.do")
	@ResponseBody
	public ShopsResult updateDikou() throws Exception{
		PageData pd = new PageData();
		pd = this.getPageData();
		return beanService.updateDikou(pd);
	}
	/**
	 * 支付宝支付
	 * <AUTHOR>
	 * @param request
	 * @param response
	 * @throws Exception
	 */
	@RequestMapping("/zhifubaoPay.do")
	public void goBuyBeanPage2(HttpServletRequest request, HttpServletResponse response) throws Exception {
		try {
		PageData pd = new PageData();
		pd = this.getPageData();
		//String shop_unique= this.getRequest().getSession().getAttribute("shop_unique").toString();
		
		String data = request.getParameter("pass");
		byte[] decode = java.util.Base64.getDecoder().decode(data.getBytes("utf-8"));
		String json=new String(decode);
		JSONObject json_test = JSONObject.fromObject(json); 
		BuyBeansVO cashVO=(BuyBeansVO)JSONObject.toBean(json_test, BuyBeansVO.class);
		
        String payMoeny=cashVO.getPayMoeny();
        String out_trade_no=cashVO.getOut_trade_no();
        String shop_unique=cashVO.getShop_unique();

		//订单名称，必填
        String subject="百货豆";
        String body=shop_unique;
        //添加订单
        pd.put("shop_unique", shop_unique);
        pd.put("pay_type", 0);//支付类型
        beanService.addBeanOrder(pd);
       
        AlipayClient alipayClient = new DefaultAlipayClient(AlipayConfig.gatewayUrl, AlipayConfig.app_id, AlipayConfig.merchant_private_key, "json", AlipayConfig.charset, AlipayConfig.alipay_public_key, AlipayConfig.sign_type);
    	//设置请求参数
    	AlipayTradePagePayRequest alipayRequest = new AlipayTradePagePayRequest();
    	alipayRequest.setReturnUrl(AlipayConfig.return_url);
    	alipayRequest.setNotifyUrl(AlipayConfig.notify_url);
    	alipayRequest.setBizContent("{\"out_trade_no\":\""+ out_trade_no +"\"," 
    			+ "\"total_amount\":\""+ payMoeny +"\"," 
    			+ "\"subject\":\""+ subject +"\"," 
    			+ "\"body\":\""+ body +"\"," 
    			+ "\"product_code\":\"FAST_INSTANT_TRADE_PAY\"}");
    	String result = alipayClient.pageExecute(alipayRequest).getBody();
		response.setHeader("Content-Type", "text/html; charset=UTF-8");
        response.setCharacterEncoding("UTF-8");  
    	PrintWriter out = response.getWriter();
    	out.println(result);
    	//输出
		}catch (Exception e) {
		}
		}
	/**
     * 回调路径return_url
     * <AUTHOR>
     * @param request
     * @param response
	 * @throws Exception 
     */
    @SuppressWarnings("rawtypes")
	@RequestMapping("return_url.do")
    public ModelAndView returnUrl(HttpServletRequest request, HttpServletResponse response) throws Exception{
    	ModelAndView mv = this.getModelAndView();
        //获取支付宝POST过来反馈信息
        Map<String,String> params = new HashMap<String,String>();
        Map requestParams = request.getParameterMap();
        for (Iterator iter = requestParams.keySet().iterator(); iter.hasNext();) {
            String name = (String) iter.next();
            String[] values = (String[]) requestParams.get(name);
            String valueStr = "";
            for (int i = 0; i < values.length; i++) {
                valueStr = (i == values.length - 1) ? valueStr + values[i]
                            : valueStr + values[i] + ",";
            }
            //乱码解决，这段代码在出现乱码时使用。
            //valueStr = new String(valueStr.getBytes("ISO-8859-1"), "utf-8");
            params.put(name, valueStr);
        }
        //GoEasy第三方推送                  
        GoEasy goeasy=new GoEasy("BC-93cd649e09404a8e9427fd540fdf5acf", null);
        PageData pd = new PageData();
		pd = this.getPageData();
        boolean signVerified = AlipaySignature.rsaCheckV1(params, AlipayConfig.alipay_public_key, AlipayConfig.charset,AlipayConfig.sign_type);
        
        if(signVerified) {
            //商户订单号
            String out_trade_no = new String(request.getParameter("out_trade_no").getBytes("ISO-8859-1"),"UTF-8");
            //支付宝交易号
            String trade_no = new String(request.getParameter("trade_no").getBytes("ISO-8859-1"),"UTF-8");
            //付款金额
            String total_amount = new String(request.getParameter("total_amount").getBytes("ISO-8859-1"),"UTF-8");
            
            String shop_unique =out_trade_no.substring(0,13);
            System.out.println("------------------------"+shop_unique);

            PageData order = beanService.findOneByTradeCode(out_trade_no);
            if(order == null){
                signVerified = false;
                mv.addObject("signVerified", signVerified); 
                mv.addObject("msg","商户订单号不存在");
            }else{
/*                if(!order.getString("payMoney").equals(total_amount)){
                    signVerified = false;
                    mv.addObject("msg", "付款金额不对");
                }*/

                if(order.getInt("receive_state") == 1){//判断当前订单是否已处理，避免重复处理
                	mv.addObject("msg", "无需重复处理");
                }else{
                    	order.put("receive_state", 1);
						beanService.payOrder(order);
						
						mv.addObject("msg", "成功支付");
						goeasy.publish(shop_unique,"支付成功,订单号:"+out_trade_no);
                }

            }
        }else{
            mv.addObject("msg", "验签失败");
            goeasy.publish("百货豆", "支付异常，请联系管理员");
            
        }
        
        
		
		
        mv.addObject("signVerified", signVerified);
       // mv.setViewName("../manager/mainPage.do");
        
        //mv.setViewName("querybeans.do");
        mv.setViewName("/WEB-INF/beans/payResult.jsp");
        return mv;
    }	
    /**
     * 跳转到百货豆提现页面
     * <AUTHOR>
     * @return
     * @throws Exception
     */
	@RequestMapping("/goBuyBeansPage.do")
	public ModelAndView goBuyBeansPage() throws Exception {
		
		ModelAndView mv = this.getModelAndView();
		PageData pd = new PageData();
		pd = this.getPageData();
		String data = request.getParameter("pass");
		byte[] decode = java.util.Base64.getDecoder().decode(data.getBytes("utf-8"));
		String beans_old_count=new String(decode);
		pd.put("beans_old_count", beans_old_count);
		mv.addObject("pd", pd);
		mv.setViewName("/WEB-INF/beans/buyBeans.jsp");
		return mv;
	}
	
    /**
     * 跳转到百货豆规则页面
     * <AUTHOR>
     * @return
     * @throws Exception
     */
	@RequestMapping("/goMakeRulePage.do")
	public ModelAndView goMakeRulePage(Page page,String shop_unique) throws Exception {
		
		ModelAndView mv = new ModelAndView();
		PageData pd = new PageData();
		pd = this.getPageData();
		if(shop_unique!=null)
		{
			mv.setViewName("/WEB-INF/beans/makeRule.jsp");
			pd.put("shop_unique", shop_unique);
			page.setPd(pd);
			//BeansRule beansRule=beanService.queryBenasRule(pd);//店铺百货豆领取规则
			PageData dikouRule=beanService.queryBeansDiKu(pd);
			
			mv.addObject("rule", dikouRule);
			mv.addObject("pd", pd);
			
		}else
		{
			mv.setViewName("/WEB-INF/login_new.jsp");
		}
		
		
		return mv;
	}
	/**
	 * 跳转到百货豆提现页面
	 * <AUTHOR>
	 * @return
	 * @throws Exception
	 */
	@RequestMapping("/zfbPay.do")
	public ModelAndView zfbPay() throws Exception {
		
		ModelAndView mv = this.getModelAndView();
		PageData pd = new PageData();
		pd = this.getPageData();
		mv.addObject("pd", pd);
		mv.setViewName("/WEB-INF/beans/zfbPay.jsp");
		return mv;
	} 
	/**
	 * 
	 * <AUTHOR>
	 * @return
	 * @throws Exception
	 */
	@RequestMapping("/queryPayBeans.do")
	@ResponseBody
	public ShopsResult queryPayBeans() throws Exception{
		PageData pd = new PageData();
		pd = this.getPageData();
		return beanService.queryPayBeans(pd);
	}
	/**
	 * 平台-百货豆管理-内嵌页面3-提现处理
	 * <AUTHOR>
	 * @return
	 * @throws Exception
	 */
	@RequestMapping("/getPtLi3.do")
	public String getPtLi3() throws Exception{
		return "/WEB-INF/beans/ptLi3.jsp";
	}
	/**
	 *  平台-百货豆管理-内嵌页面2-提现规则
	 * <AUTHOR>
	 * @return
	 * @throws Exception
	 */
	@RequestMapping("/getPtLi2.do")
	public ModelAndView getPtLi2(String shop_unique) throws Exception{
		
		ModelAndView mv = this.getModelAndView();
		PageData pd = new PageData();
		if(shop_unique!=null)
		{
			ShopBeansVO beanDetai=beanService.getShopBeans(shop_unique);
			mv.addObject("beanDetai", beanDetai);
			mv.setViewName("/WEB-INF/beans/ptLi2.jsp");
		}else
		{
			mv.setViewName("../manager/loginout.do");
		}

		return mv;
	}
	/**
	 * 平台-百货豆管理-内嵌页面1-历史记录
	 * <AUTHOR>
	 * @param page
	 * @return
	 * @throws Exception
	 */
	@RequestMapping("/getPtLi1.do")
	public ModelAndView getPtLi1(Page page,String shop_unique) throws Exception{
		ModelAndView mv = this.getModelAndView();
		PageData pd = new PageData();
		pd = this.getPageData();
		pd.put("shop_unique", shop_unique);
		page.setPd(pd);
		int count=beanService.getTransactionListCount(page);//记录条数
		page.setTotalResult(count);
		
		
		int orderPages=beanService.queryCusCount(pd);
		mv.addObject("orderPages", orderPages);
		mv.setViewName("/WEB-INF/beans/ptLi1.jsp");
		return mv;
	}
	/**
	 * 跳转添加银行卡页面
	 * <AUTHOR>
	 * @return
	 * @throws Exception
	 */
	@RequestMapping("/addCard.do")
	public String addCard(Model model){
		Subject subject = SecurityUtils.getSubject();
		Session session = subject.getSession();
		Staff staff = (Staff) session.getAttribute("staff");
		String shop_unique = String.valueOf(staff.getShop_unique());
		
		//微信小程序开通状态：0:不显示 1:显示 2:审核中 3:审核失败
		Integer show_buy_status = 0;
		String show_buy_fail_reason = "";
		//获取商家信息
		ShopsResult result = shopService.queryShopMessage(shop_unique);
		if(result.getStatus() == 0){
			Map<String,Object> shopInfo = (Map<String, Object>) result.getData();
			show_buy_status = Integer.parseInt(MUtil.strObject(shopInfo.get("show_buy_status")));
			show_buy_fail_reason = MUtil.strObject(shopInfo.get("show_buy_fail_reason"));
		}
		if(show_buy_status == 0){
			//获取微信小程序开通协议
			Map<String ,Object> agreement = sysAgreementService.querySysAgreement("wechat");
			model.addAttribute("agreement", agreement);
			
			return "/WEB-INF/wechatApplet/protocol.jsp";
		}else if(show_buy_status == 1) {
			return "/WEB-INF/beans/addCard.jsp";
		}else{
			model.addAttribute("show_buy_status", show_buy_status);
			model.addAttribute("show_buy_fail_reason", show_buy_fail_reason);
			return "/WEB-INF/wechatApplet/showBuyStatus.jsp";
		}
	}
	/**
	 * 平台待处理提现查询
	 * <AUTHOR>
	 * @return
	 * @throws Exception
	 */
	@RequestMapping("/ptLi3.do")
	@ResponseBody
	public ShopsResult queryPtLi3(
			@RequestParam(value="shopUnique")String shopUnique,
			@RequestParam(value="pageNum",defaultValue="1")Integer pageNum,
			@RequestParam(value="pageSize",defaultValue="10")Integer pageSize,
			String goodsMessage,
			String startTime,
			String endTime,
			@RequestParam(value="order",defaultValue="1")Integer order,
			@RequestParam(value="orderType",defaultValue="1")Integer orderType
			) throws Exception{
		Map<String,Object> map=new HashMap<String, Object>();
		if(shopUnique.equals("*************"))
		{
			map.put("shopUnique", null);
		}else
		{
			map.put("shopUnique", shopUnique);
		}
		
		map.put("startNum", (pageNum-1)*pageSize);
		map.put("pageSize", pageSize);
		map.put("startTime", startTime);
		map.put("endTime", endTime);
		if(null!=goodsMessage&&!goodsMessage.equals("")){
			map.put("goodsMessage", "%"+goodsMessage+"%");
		}
		if(order==1){
			map.put("order", "receive_time");
		}else if(order==2){
			map.put("order", "receive_count");
		}else if(order==3){
			map.put("order", "payMoney");
		}
		if(orderType==1){
			map.put("orderType", "DESC");
		}else{
			map.put("orderType", "ASC");
		}
		map.put("receive_state", 0);
		map.put("exchange_type", 2);
		
		return beanService.queryPtLi3(map);
	}
	
	/**
	 * 查商家银行卡
	 * <AUTHOR>
	 * @return
	 * @throws Exception
	 */
	
	@RequestMapping("/queryCard.do")
	@ResponseBody
	public ShopsResult queryCard(@RequestParam(value="shopUnique")String shopUnique) throws Exception{
		PageData pd = new PageData();
		pd = this.getPageData();
		return beanService.queryCard(shopUnique);
	}
	/**
	 * 
	 * <AUTHOR>
	 * @return
	 * @throws Exception
	 */
	@RequestMapping("/goAddCard.do")
	public ModelAndView goAddCard() throws Exception{
		ModelAndView mv = this.getModelAndView();
		PageData pd = new PageData();
		
		List<Map<String,Object>> cardList=beanService.queryBankName();
		pd = this.getPageData();
		mv.addObject("pd", pd);
		mv.addObject("cardList", cardList);
		mv.setViewName("/WEB-INF/beans/updateCard.jsp");
		return mv;
	}
	/**
	 * 新增银行卡
	 * <AUTHOR>
	 * @return
	 * @throws Exception
	 */
	@RemoteLog(title = "新增银行卡", businessType = BusinessType.INSERT)
	@RequestMapping("/addBankCard.do")
	@ResponseBody
	public ShopsResult addBankCard() throws Exception{
		PageData pd = new PageData();
		pd = this.getPageData();
		return beanService.addbankCard(pd);
	}
	/**
	 * 修改银行卡
	 * <AUTHOR>
	 * @return
	 * @throws Exception
	 */
	@RemoteLog(title = "修改银行卡", businessType = BusinessType.UPDATE)
	@RequestMapping("/updateBankCard.do")
	@ResponseBody
	public ShopsResult updateBankCard() throws Exception{
		PageData pd = new PageData();
 		pd = this.getPageData();
		return beanService.updateBankCard(pd);
	}	
	/**、
	 * 删除银行卡
	 * <AUTHOR>
	 * @return
	 * @throws Exception
	 */
	@RemoteLog(title = "删除银行卡", businessType = BusinessType.DELETE)
	@RequestMapping("/deleteBankCard.do")
	@ResponseBody
	public ShopsResult deleteBankCard() throws Exception{
		PageData pd = new PageData();
 		pd = this.getPageData();
		return beanService.deleteBankCard(pd);
	}
	/**
	 * 商家百货豆-新增提现
	 * <AUTHOR>
	 * @return
	 * @throws Exception
	 */
	@RequestMapping("/addCardRecord.do")
	@ResponseBody
	public ShopsResult addCardRecord(HttpServletRequest request, HttpServletResponse response) throws Exception {

		String data = request.getParameter("pass");
		byte[] decode = java.util.Base64.getDecoder().decode(data.getBytes("utf-8"));
		String json=new String(decode);
		JSONObject json_test = JSONObject.fromObject(json); 
		TiCashVO cashVO=(TiCashVO)JSONObject.toBean(json_test, TiCashVO.class);
		String order_id = "tx" + System.currentTimeMillis(); //订单号\
		cashVO.setOrder_id(order_id);
		
		return beanService.addCardRecord(cashVO);
	}	
	/**
	 * 平台-百货豆-去提现处理界面
	 * <AUTHOR>
	 * @return
	 * @throws Exception
	 */
	@RequestMapping("/goProcessCash.do")
	public ModelAndView goProcessCash() throws Exception{
		ModelAndView mv = this.getModelAndView();
		PageData pd = new PageData();
		pd = this.getPageData();
		PageData cashDetail=beanService.queryCashDetail(pd);
		
		if(cashDetail!=null)
		{
			List<PageData> historyList=beanService.queryCashList(cashDetail);
			mv.addObject("historyList", historyList);
			
		}else
		{
			mv.addObject("historyList", null);
		}
		
		
		mv.addObject("pd", pd);
		mv.addObject("cd", cashDetail);
		mv.setViewName("/WEB-INF/beans/ptLi3_cash.jsp");
		return mv;
	}
	/**
	 * 平台-去提现处理界面
	 * <AUTHOR>
	 * @return
	 * @throws Exception
	 */
	@RequestMapping("/goPtProcessCash.do")
	public ModelAndView goPtProcessCash() throws Exception{
		ModelAndView mv = this.getModelAndView();
		PageData pd = new PageData();
		pd = this.getPageData();
		PageData cashDetail=beanService.queryPtCashDetail(pd);
		
		mv.addObject("pd", pd);
		mv.addObject("cd", cashDetail);
		mv.setViewName("/WEB-INF/beans/drawCashDetail.jsp");
		return mv;
	}
	/**
	 * <AUTHOR>
	 * @return
	 * @throws Exception
	 */
	@RequestMapping("/updateCashOrder.do")
	@ResponseBody
	public ShopsResult updateCashOrder() throws Exception{
		PageData pd = new PageData();
 		pd = this.getPageData();
		return beanService.updateCashOrder(pd);
	}
	/**
	 * 平台修改规则
	 * <AUTHOR>
	 * @return
	 * @throws Exception
	 */
	@RequestMapping("/updatePtRule.do")
	@ResponseBody
	public ShopsResult updatePtRule() throws Exception{
		PageData pd = new PageData();
 		pd = this.getPageData();
		return beanService.updatePtRule(pd);
	}
	/**
	 * 平台提现驳回
	 *<AUTHOR>
	 * @return
	 * @throws Exception
	 */
	@RequestMapping("/updateBoHuiCash.do")
	@ResponseBody
	public ShopsResult updateBoHuiCash() throws Exception{
		PageData pd = new PageData();
 		pd = this.getPageData();
		return beanService.updateBoHuiCash(pd);
	}
	/**
	 * 去线上订单统计-提现页面
	 * <AUTHOR>
	 * @return
	 * @throws Exception
	 */
	@RequestMapping("/onlineStatistics.do")
	public String goOnlinePage(Model model){
		Subject subject = SecurityUtils.getSubject();
		Session session = subject.getSession();
		Staff staff = (Staff) session.getAttribute("staff");
		String shop_unique = String.valueOf(staff.getShop_unique());
		
		//微信小程序开通状态：0:不显示 1:显示 2:审核中 3:审核失败
		Integer show_buy_status = 0;
		String show_buy_fail_reason = "";
		//获取商家信息
		ShopsResult result = shopService.queryShopMessage(shop_unique);
		if(result.getStatus() == 0){
			Map<String,Object> shopInfo = (Map<String, Object>) result.getData();
			show_buy_status = Integer.parseInt(MUtil.strObject(shopInfo.get("show_buy_status")));
			show_buy_fail_reason = MUtil.strObject(shopInfo.get("show_buy_fail_reason"));
		}
		if(show_buy_status == 0){
			//获取微信小程序开通协议
			Map<String ,Object> agreement = sysAgreementService.querySysAgreement("wechat");
			model.addAttribute("agreement", agreement);
			
			return "/WEB-INF/wechatApplet/protocol.jsp";
		}else if(show_buy_status == 1) {
			return "/WEB-INF/beans/onlineStatistics.jsp";
		}else{
			model.addAttribute("show_buy_status", show_buy_status);
			model.addAttribute("show_buy_fail_reason", show_buy_fail_reason);
			return "/WEB-INF/wechatApplet/showBuyStatus.jsp";
		}
	}
	/**
	 * 商家-提现页面
	 * <AUTHOR>
	 * @param shop_unique
	 * @return
	 * @throws Exception
	 */
	@RequestMapping("/goDrawCashPage.do")
	public ModelAndView goDrawCashPage(@RequestParam(value="shop_unique",required=true) String shop_unique) throws Exception{
		
		ModelAndView mv = this.getModelAndView();
		ShopBeansVO beanDetai=beanService.getShopBeans(shop_unique);
		PageData pd = new PageData();
 		pd = this.getPageData();
			List<Map<String,Object>> cardList=beanService.queryBankCard(shop_unique);
			int tx_dtae=beanDetai.getTx_dtae();
			int tx_times=beanDetai.getTx_times();
			//查询最近tx_dtae日的交易次数
			int tixianCount=beanService.queryTransactionCountPt(shop_unique,tx_dtae);
			int tiXianCount=tx_times-tixianCount;
			if(tiXianCount<0)
			{
				tiXianCount=0;
			}
			int moneyTop= beanDetai.getShop_balance().intValue();
			pd.put("shop_unique", shop_unique);
			mv.addObject("pd", pd);
			mv.addObject("moneyTop", moneyTop);//可提现金额
			mv.addObject("tiXianCount", tiXianCount);//可提现次数
			mv.addObject("beanDetai", beanDetai);//提现规则
			mv.addObject("cardList", cardList);//银行卡列表
			mv.setViewName("/WEB-INF/beans/drawCash.jsp");

		return mv;		
		
	}
	/**
	 * 平台-提现处理页面
	 * @return
	 * @throws Exception
	 */
	@RequestMapping("/goDrawCashHandle.do")
	public String goDrawCashHandle() throws Exception{
		return "/WEB-INF/beans/drawCashHandle.jsp";
	}
	
	@RequestMapping("/queryDrawCashList.do")
	@ResponseBody
	public PurResult queryDrawCashList(
			@RequestParam(value="shopUnique")String shopUnique,
			@RequestParam(value = "page", defaultValue = "1") int page,
			@RequestParam(value = "limit", defaultValue = "8") int pageSize,
			String goodsMessage,
			String startTime,
			String endTime,
			@RequestParam(value="order",defaultValue="1")Integer order,
			@RequestParam(value="orderType",defaultValue="1")Integer orderType
			) throws Exception{
		Map<String,Object> map=new HashMap<String, Object>();
		if(shopUnique.equals("*************"))
		{
			map.put("shopUnique", null);
		}else
		{
			map.put("shopUnique", shopUnique);
		}
		
		map.put("startNum", (page-1)*pageSize);
		map.put("pageSize", pageSize);
		map.put("startTime", startTime);
		map.put("endTime", endTime);
		if(null!=goodsMessage&&!goodsMessage.equals("")){
			map.put("goodsMessage", "%"+goodsMessage+"%");
		}
		if(order==1){
			map.put("order", "apply_datetime");
		}else if(order==2){
			map.put("order", "take_money");
		}
		if(orderType==1){
			map.put("orderType", "DESC");
		}else{
			map.put("orderType", "ASC");
		}
		map.put("handle_status", 1);
		
		return beanService.queryDrawCashList(map);
	}
	/**
	 * 提现处理-驳回和已处理
	 * <AUTHOR>
	 * @return
	 * @throws Exception
	 */
	@RequestMapping("/updateDrawCash.do")
	@ResponseBody
	public ShopsResult updateDrawCash() throws Exception{
		PageData pd = new PageData();
 		pd = this.getPageData();
		return beanService.updateDrawCash(pd);
	}
	
	/**
	 * 线上订单-提现
	 * <AUTHOR>
	 * @return
	 * @throws Exception
	 */
	@RequestMapping("/addDrawCash.do")
	@ResponseBody
	public ShopsResult addDrawCash() throws Exception{
		PageData pd = new PageData();
 		pd = this.getPageData();
		return beanService.addDrawCash(pd);
	}
	
	/**
	 * 查询店铺的百货豆赠送情况
	 * @param pageQuery
	 * @return
	 */
	@RequestMapping("/queryShopsBeansListByPage.do")
	@ResponseBody
	public PurResult queryShopsBeansListByPage(PageQuery pageQuery,
			@RequestParam(value = "page", defaultValue = "1") int page,
			@RequestParam(value = "limit", defaultValue = "8") int pageSize){
		pageQuery.setCurrentPage(page);
		pageQuery.setShowCount(pageSize);
		return beanService.queryShopsBeansListByPage(pageQuery);
	}
	
	/**
	 * 店铺-添加百货豆商品促销
	 * <AUTHOR>
	 * @return
	 * @throws Exception
	 */
	@RequestMapping("/addShopBeanPromation.do")
	@ResponseBody
	public ShopsResult addShopBeanPromation(String shopUnique,String goods_barcode,String shop_bean_give) {
		ShopsResult result=new ShopsResult();
		try {
			PageData pd = new PageData();
	 		pd.put("shopUnique", shopUnique);
	 		pd.put("goods_barcode", goods_barcode);
	 		pd.put("shop_bean_give", shop_bean_give);
	 		result=beanService.addShopBeanPromation(pd);

		} catch (Exception e) {
			e.printStackTrace();
			result.setStatus(0);
			result.setMsg("异常");
		}
			
			return result;
		}
/**
 * 店铺-修改百货豆商品促销-启动状态
 * <AUTHOR>
 * @return
 * @throws Exception
 */
@RequestMapping("/updatePromationStatus.do")
@ResponseBody
public ShopsResult updateShopBeanPromationStatus(String shopUnique,String goods_barcode,String valid) {
	ShopsResult result=new ShopsResult();
	try {
		PageData pd = new PageData();
 		pd.put("shopUnique", shopUnique);
 		pd.put("goods_barcode", goods_barcode);
 		pd.put("valid", valid);
 		result=beanService.updateShopBeanPromationStatus(pd);

	} catch (Exception e) {
		e.printStackTrace();
		result.setStatus(0);
		result.setMsg("异常");
	}
		return result;
	}
}
