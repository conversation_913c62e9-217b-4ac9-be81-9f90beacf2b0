package org.haier.shop.controller;

import javax.annotation.Resource;

import org.haier.shop.service.DataSearchService;
import org.haier.shop.util.ShopsResult;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

@RequestMapping("/dataSearch")
@Controller
public class DataSearchController {
	@Resource
	private DataSearchService dataSearchService;
	
	/**
	 * 大屏左上角，到店人数数量统计
	 * @return
	 */
	@RequestMapping("/peopleArrivingSearch.do")
	@ResponseBody
	public ShopsResult peopleArrivingSearch(){
		return dataSearchService.peopleArrivingSearch();
	};
	
	/**
	 * 在线店铺数量统计
	 * @return
	 */
	@RequestMapping("/shopOnLineStatis.do")
	@ResponseBody
	public ShopsResult shopOnLineStatis(){
		return dataSearchService.shopOnLineStatis();
	};
	

	/**
	 * 明星店铺统计
	 * @return
	 */
	@RequestMapping("/theBestSallerShop.do")
	@ResponseBody
	public ShopsResult theBestSallerShop(){
		return dataSearchService.theBestSallerShop();
	};
	
	/**
	 * 各阶段支付金额统计
	 * @return
	 */
	@RequestMapping("/getListIncomeStage.do")
	@ResponseBody
	public ShopsResult getListIncomeStage(){
		return dataSearchService.getListIncomeStage();
	}
	

	/**
	 * 近一月商品价格走势图
	 * @return
	 */
	@RequestMapping("/goodsPriceTrend.do")
	@ResponseBody
	public ShopsResult goodsPriceTrend(){
		return dataSearchService.goodsPriceTrend();
	}
	
	/**
	 * 查询某个商品三个月内的售价浮动情况
	 * @return
	 */
	@RequestMapping("/getPriceFloat.do")
	@ResponseBody
	public ShopsResult getPriceFloat(){
		return dataSearchService.getPriceFloat();
	}
	
	/**
	 * 查询某商品近一月的售价浮动情况
	 * @return
	 */
	@RequestMapping("/getPriceDiff.do")
	@ResponseBody
	public ShopsResult getPriceDiff(
			@RequestParam(value="goodsType",defaultValue="1")Integer goodsType){
		return dataSearchService.getPriceDiff(goodsType);
	}
	
	@RequestMapping("/queryTotalStatistics.do")
	@ResponseBody
	public ShopsResult queryTotalStatistics(){
		return dataSearchService.queryTotalStatistics();
	}
	
	/**
	 * 小程序实时在线用户量
	 * @return
	 */
	@RequestMapping("/queryOnlineUserCount.do")
	@ResponseBody
	public ShopsResult queryOnlineUserCount(){
		return dataSearchService.queryOnlineUserCount();
	}
	
	@RequestMapping("/queryOnlineUserCountNew.do")
	@ResponseBody
	public ShopsResult queryOnlineUserCountNew(){
		return dataSearchService.queryOnlineUserCountNew();
	}
	
	/**
	 * 线上线下订单量对比
	 * @return
	 */
	@RequestMapping("/queryLineCount.do")
	@ResponseBody
	public ShopsResult queryLineCount(){
		return dataSearchService.queryLineCount();
	}
	
	/**
	 * 更新店铺名单
	 * @return
	 */
	@RequestMapping("/updateShopName.do")
	@ResponseBody
	public ShopsResult updateShopName(){
		return dataSearchService.updateShopName();
	}
}
