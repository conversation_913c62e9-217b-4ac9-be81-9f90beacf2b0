package org.haier.shop.controller;

import java.util.HashMap;
import java.util.Map;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.haier.log.annotation.RemoteLog;
import org.haier.log.enums.BusinessType;
import org.haier.shop.service.GoodsKindService;
import org.haier.shop.util.MyException;
import org.haier.shop.util.PurResult;
import org.haier.shop.util.ShopsResult;
import org.haier.shop.util.UtilForJAVA;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

@Controller
@RequestMapping("/html/goods")
public class GoodsKindController {
	@Resource
	private GoodsKindService kindService;
	
	/**
	 * 商品分类查询
	 * @param shop_unique 店铺编号
	 * @param goods_kind_parunique 商品分类父类编号
	 * @return
	 */
	@RequestMapping("/queryGoodsKinds.do")
	@ResponseBody
	public ShopsResult queryGoodsKinds(String shop_unique,String goods_kind_parunique){
		return kindService.queryGoodsKinds(shop_unique, goods_kind_parunique);
	}
	
	/**
	 * 商品分类查询（包含子类）
	 * @return
	 */
	@RequestMapping("/queryGoodsGroups.do")
	@ResponseBody
	public ShopsResult queryGoodsGroups(String shop_unique){
		return kindService.queryGoodsGroups(shop_unique);
	}
	
	@RequestMapping("/modifyGoodsKinds.do")
	@ResponseBody
	/**
	 * 商品分类信息更新
	 * @param shop_unique
	 * @param goodsKindsUniques
	 * @return
	 */
	public ShopsResult modifyGoodsKinds(String shop_unique,String[] goodsKindsUniques){
		return kindService.modifyGoodsKinds(shop_unique, goodsKindsUniques);
	};
	
	/**
	 * 添加新的商品分类申请
	 * @param map
	 * @return
	 */
	@RequestMapping("/newGoodsKind.do")
	@ResponseBody
	public ShopsResult sureNewKind(
			@RequestParam(value="shopUnique",required=true)Long shopUnique,
			@RequestParam(value="groupName",required=true)String groupName,
			String groupUnique,
			@RequestParam(value="kindName",required=true)String kindName
			){
		Map<String,Object> map=new HashMap<String, Object>();
		map.put("shopUnique", shopUnique);
		map.put("groupName", groupName);
		map.put("groupUnique",groupUnique);
		map.put("kindName", kindName);
		return kindService.sureNewKind(map);
	}
	/**
	 * 添加商品分类
	 * @return
	 */
	@RequestMapping("/addGoodsKinds.do")
	@ResponseBody
	public PurResult addGoodsKinds(
			@RequestParam(value="shop_unique",required=true)String shop_unique,
			@RequestParam(value="goods_kind_unique",required=true)String goods_kind_unique
			){
		return kindService.addGoodsKinds(shop_unique,goods_kind_unique);
	}
	/**
	 * 删除商品分类
	 * @return
	 */
	@RequestMapping("/deleteGoodsKind.do")
	@ResponseBody
	public PurResult deleteGoodsKind(
			@RequestParam(value="shop_unique",required=true)String shop_unique,
			@RequestParam(value="goods_kind_unique",required=true)String goods_kind_unique
			){
		return kindService.deleteGoodsKind(shop_unique,goods_kind_unique);
	}
	
	/**
	 * 查询所有分类
	 * @return
	 */
	@RequestMapping("/queryAllGoodsKinds.do")
	@ResponseBody
	public PurResult queryAllGoodsKinds(
			@RequestParam(value="shop_unique",required=true)String shop_unique){
		return kindService.queryAllGoodsKinds(shop_unique);
	}
	
	/**
	 * 查询店内所有一级二级商品分类
	 * @return
	 */
	@RequestMapping("/queryAllKindsInShops.do")
	@ResponseBody
	public ShopsResult queryAllKindsInShops(String shopUnique){
		Map<String,Object> map=new HashMap<String, Object>();
		map.put("shopUnique", shopUnique);
		return kindService.queryAllKindsInShops(map);
	}
	
	/**
	 * 查询商品分类信息
	 * @param map
	 * @return
	 * @throws MyException
	 */
	@RequestMapping("/queryGoodsKindsWithGoodsCount.do")
	@ResponseBody
	public ShopsResult queryGoodsKindsWithGoodsCount(HttpServletRequest request,HttpServletResponse response){
		try {
			Map<String,Object> map=UtilForJAVA.addParameter(request);
			return kindService.queryGoodsKindsWithGoodsCount(map);
		} catch (MyException e) {
			return new ShopsResult(e.status, e.msg);
		}
	}
	
	/**
	 * 跳转商品分类详情页
	 * @param request
	 * @param response
	 * @return
	 */
	@RequestMapping("/goodsKindDetail.do")
	public String goodsKindDetail(HttpServletRequest request,HttpServletResponse response){
			return "/WEB-INF/goods/updateGoodsKind.jsp";
	}
	
	/**
	 * 商品分类详情修改
	 * @param request
	 * @param response
	 * @return
	 */
	@RemoteLog(title="修改商品分类信息", businessType=BusinessType.UPDATE)
	@RequestMapping("/modifyGoodsKindMsg.do")
	@ResponseBody
	public ShopsResult modifyGoodsKindMsg(HttpServletRequest request,HttpServletResponse response){
		try {
			Map<String,Object> map=UtilForJAVA.addParameter(request);
			return kindService.modifyGoodsKindMsg(map);
		} catch (MyException e) {
			return new ShopsResult(e.status, e.msg);
		}
	}

	@RemoteLog(title="停用商品分类信息", businessType=BusinessType.DELETE)
	@RequestMapping("/deleteGoodsKinds.do")
	@ResponseBody
	public ShopsResult deleteGoodsKinds(HttpServletRequest request,HttpServletResponse response){
		try {
			Map<String,Object> map=UtilForJAVA.addParameter(request);
			return kindService.deleteGoodsKind(map);
		} catch (MyException e) {
			return new ShopsResult(e.status, e.msg);
		}
	}
	
	@RequestMapping("/addNewGoodsKind.do")
	public String addNewGoodsKind(HttpServletRequest request,Integer kindType){
		//根据分类类新，将分类的父级分类信息查出
		
		return "/WEB-INF/goods/addNewGoodsKind.jsp";
	}
	
	/**
	 * 查询店铺所有一级分类 
	 * @param map
	 * @return
	 * @throws MyException
	 */
	@RequestMapping("/queryAllGroupMsgByType.do")
	@ResponseBody
	public ShopsResult queryAllGroupMsgByType(HttpServletRequest request,HttpServletResponse response){
		try {
			Map<String,Object> map=UtilForJAVA.addParameter(request);
			return kindService.queryAllGroupMsgByType(map);
		} catch (MyException e) {
			return new ShopsResult(e.status, e.msg);
		}
	}
	
	/**
	 * 添加新的商品分类信息
	 * @param ShopUnique
	 * @param kindName
	 * @param groupUnique
	 * @return
	 * @throws MyException 
	 */
	@RemoteLog(title="添加商品分类", businessType = BusinessType.INSERT)
	@RequestMapping("/addNewGoodsKindLay.do")
	@ResponseBody
	public ShopsResult addNewGoodsKindLay(HttpServletRequest request,HttpServletResponse response) throws MyException{
		try {
			System.out.println("擦额");
			Map<String,Object> map=UtilForJAVA.addParameter(request);
			ShopsResult sr=new ShopsResult(0,"添加失败！");
			if(map.get("shopUnique")==null||map.get("shopUnique").toString().trim().equals("")){
				sr.setMsg("店铺编号未上传或为空");
				return sr;
			}
			if(map.get("kindName")==null||map.get("kindName").toString().trim().equals("")){
				sr.setMsg("分类名称未上传或为空");
				return sr;
			}
			if(map.get("groupUnique")==null||map.get("groupUnique").toString().trim().equals("")){
				sr.setMsg("父类编号未上传或为空");
				return sr;
			}
			if(map.get("groupUnique").toString().equals("0") && (map.get("iconId")==null||map.get("iconId").toString().trim().equals(""))){
				sr.setMsg("请选择分类图标");
				return sr;
			}
			map.put("kindType", 2);//自定义分类
			return kindService.addNewGoodsKindLay(map);
		} catch (MyException e) {
			return new ShopsResult(e.status, e.msg);
		}
	}
	
	/**
	 * 修改商品分类信息
	 * @param shopUnique
	 * @param kindType
	 * @return
	 */
	@RequestMapping("/useCustomeKind.do")
	@ResponseBody
	public ShopsResult useCustomeKind(String shopUnique,Integer kindType) {
		return kindService.useCustomeKind(shopUnique, kindType);
	}
	
	/**
	 * 获取当前店铺的分类使用信息
	 * @param shopUnique
	 * @return
	 */
	@RequestMapping("/getShopNowKindType.do")
	@ResponseBody
	public ShopsResult getShopNowKindType(String shopUnique) {
		return kindService.getShopNowKindType(shopUnique);
	}
}
