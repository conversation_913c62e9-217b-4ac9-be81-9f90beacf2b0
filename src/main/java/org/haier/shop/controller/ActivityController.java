package org.haier.shop.controller;

import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.net.HttpURLConnection;
import java.net.URL;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.UUID;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;

import cn.hutool.core.util.RandomUtil;
import net.sf.json.JSONObject;
import org.apache.commons.lang3.StringUtils;
import org.apache.ibatis.annotations.Param;
import org.apache.shiro.SecurityUtils;
import org.apache.shiro.session.Session;
import org.apache.shiro.subject.Subject;
import org.haier.log.annotation.RemoteLog;
import org.haier.log.enums.BusinessType;
import org.haier.meituan.util.MUtil;
import org.haier.shop.entity.Staff;
import org.haier.shop.entity.beans.PageData;
import org.haier.shop.service.ActivityService;
import org.haier.shop.service.ShopService;
import org.haier.shop.service.SysAgreementService;
import org.haier.shop.util.*;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.util.FileCopyUtils;
import org.springframework.util.ObjectUtils;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.multipart.MultipartFile;

import com.alibaba.fastjson.JSON;

import net.sf.json.JSONArray;
import net.sf.json.JsonConfig;


@Controller
@RequestMapping("/activity")
public class ActivityController {
	@Resource
	private ActivityService activityService;
	
	@Resource
	private ShopService shopService;
	
	@Resource
	private SysAgreementService sysAgreementService;
	
	
	@RequestMapping("/deleteRechargeOffline.do")
	@ResponseBody
	public PurResult deleteRechargeOffline(Long recharge_config_id) {
		return activityService.deleteRechargeOffline(recharge_config_id);
	}
	/**
	 * 根据ID查询充值详情
	 * @param recharge_config_id
	 * @return
	 */
	@RequestMapping("/queryOilRechargeConfigDetail.do")
	@ResponseBody
	public PurResult queryOilRechargeConfigDetail(Long recharge_config_id) {
		return activityService.queryOilRechargeConfigDetail(recharge_config_id);
	}
	
	@RequestMapping("/addOilSubCouponChoose.do")
	public String addOilSubCouponChoose(HttpServletRequest request) {
		return "/WEB-INF/manager/addOilSubCouponChoose.jsp";
	}
	
	@RequestMapping("/addOilSubGoodsChoose.do")
	public String addOilSubGoodsChoose() {
		return "/WEB-INF/manager/addOilSubGoodsChoose.jsp";
	}
	
	@RequestMapping("/queryOilRechargeConfigList.do")
	@ResponseBody
	public PurResult queryOilRechargeConfigList(String shopUnique,String rechargeMsg,Integer deleteStatus,
			String startTime,String endTime,Double minMoney,Double maxMoney,Integer page,Integer limit) {
		Map<String,Object> map = new HashMap<String,Object>();
		map.put("shopUnique", shopUnique);
		map.put("rechargeMsg", rechargeMsg);
		map.put("startTime", startTime);
		map.put("endTime", endTime);
		map.put("minMoney", minMoney);
		map.put("maxMoney", maxMoney);
		if(null != limit && null != page) {
			map.put("startNum", (page-1)*limit);
			map.put("pageSize", limit);
		}
		return activityService.queryOilRechargeConfigList(map);
	}
	
	@RequestMapping("/downLoadCode.do")
	public String downLoadCode(String shop_unique) {
		return "/WEB-INF/promotion/downLoadCode.jsp";
	}
	
	@RequestMapping("promotionChoseGoodsPage.do")
	public String promotionChoseGoodsPage(String type ,Model model,String storehouse_out_id,String is_online,String shop_unique,String page){
		model.addAttribute("type", type);
		model.addAttribute("storehouse_out_id", storehouse_out_id);
		model.addAttribute("is_online", is_online);
		model.addAttribute("shop_unique", shop_unique);
		model.addAttribute("page", page);
		return "/WEB-INF/promotion/choseGoods.jsp";
	}
	/**
	 * 商品促销
	 * @param type
	 * @param model
	 * @param storehouse_out_id
	 * @param is_online
	 * @param shop_unique
	 * @return
	 */
	@RequestMapping("promotionChoseGoodsPage2.do")
	public String promotionChoseGoodsPage2(String type ,Model model,String is_online,String shop_unique){
		model.addAttribute("type", type);
		model.addAttribute("is_online", is_online);
		model.addAttribute("shop_unique", shop_unique);
		return "/WEB-INF/promotion/choseGoods3.jsp";
	}
	
	@RequestMapping("promotionChoseGoodsGiftPage.do")
	public String promotionChoseGoodsGiftPage(String value,Model model,String is_online){
		model.addAttribute("value", value);
		model.addAttribute("is_online", is_online);
		return "/WEB-INF/promotion/choseGoodsGift.jsp";
	}
	@RequestMapping("stockChoseGoodsPage.do")
	public String stockChoseGoodsPage(String type ,Model model,String storehouse_out_id,String is_online, Integer goodsInPriceType){
		model.addAttribute("type", type);
		model.addAttribute("storehouse_out_id", storehouse_out_id);
		model.addAttribute("is_online", is_online);
		model.addAttribute("goodsInPriceType", goodsInPriceType);
		return "/WEB-INF/countMessage/stockChoseGoods.jsp";
	}
	
	
	@RequestMapping("/choseGoodsCount.do")
	public String choseGoodsCount() {
		return "/WEB-INF/promotion/closeGoodsCount.jsp";
	}
	
	//采购主页
	@RequestMapping("/purchaseIndexPage.do")
	public String purchaseIndexPage(String shop_unique,Model model){
		//是否允许自采购:0 不允许 1允许
		Integer isSelfPurchase = 1;
		if(null == shop_unique || shop_unique.equals("")) {
			Subject subject = SecurityUtils.getSubject();
			Session session = subject.getSession();
			Staff staff = (Staff) session.getAttribute("staff");
			shop_unique = String.valueOf(staff.getShop_unique());
		}
		//获取商家信息
		Map<String ,Object> params = new HashMap<String, Object>();
		params.put("shop_unique", shop_unique);
		ShopsResult result = shopService.queryShopMessage(shop_unique);
		if(result.getStatus() == 0){
			Map<String,Object> shopInfo = (Map<String, Object>) result.getData();
			Integer shop_class = Integer.parseInt(MUtil.strObject(shopInfo.get("shop_class")));//店铺分类：0普通商家；1：连锁；2加盟
			String is_other_purchase = MUtil.strObject(shopInfo.get("is_other_purchase"));//是否允许像其他供货商采购：0不允许 1允许
			if(shop_class != 0 && is_other_purchase.equals("0")){//连锁加盟并且不允许向其他供货商采购
				isSelfPurchase = 0;
			}
		}
		model.addAttribute("isSelfPurchase", isSelfPurchase);
		return "/WEB-INF/purchase/purchaseIndex.jsp";
	}
	
	//自采购页面
	@RequestMapping("/selfPurchasePage.do")
	public String selfPurchasePage(){
		return "/WEB-INF/purchase/selfPurchase.jsp";
	}
	
	//pc收银促销列表页面
	@RequestMapping("/queryPromotionListPage.do")
	public String queryPromotionListPage(){
		return "/WEB-INF/promotion/queryPromotionListPage.jsp";
	}
	
	//微信小程序促销列表页面
	@RequestMapping("/queryWeChatPromotionListPage.do")
	public String queryWeChatPromotionListPage(Model model){
		Subject subject = SecurityUtils.getSubject();
		Session session = subject.getSession();
		Staff staff = (Staff) session.getAttribute("staff");
		String shop_unique = String.valueOf(staff.getShop_unique());
		
		//微信小程序开通状态：0:不显示 1:显示 2:审核中 3:审核失败
		Integer show_buy_status = 0;
		String show_buy_fail_reason = "";
		//获取商家信息
		ShopsResult result = shopService.queryShopMessage(shop_unique);
		if(result.getStatus() == 0){
			Map<String,Object> shopInfo = (Map<String, Object>) result.getData();
			show_buy_status = Integer.parseInt(MUtil.strObject(shopInfo.get("show_buy_status")));
			show_buy_fail_reason = MUtil.strObject(shopInfo.get("show_buy_fail_reason"));
		}
		if(show_buy_status == 0){
			//获取微信小程序开通协议
			Map<String ,Object> agreement = sysAgreementService.querySysAgreement("wechat");
			model.addAttribute("agreement", agreement);
			
			return "/WEB-INF/wechatApplet/protocol.jsp";
		}else if(show_buy_status == 1) {
			return "/WEB-INF/wechatApplet/queryPromotionListPage.jsp";
		}else{
			model.addAttribute("show_buy_status", show_buy_status);
			model.addAttribute("show_buy_fail_reason", show_buy_fail_reason);
			return "/WEB-INF/wechatApplet/showBuyStatus.jsp";
		}
	}
	
	//微信小程序添加限时抢购页面
	@RequestMapping("/addWeChatSingleGoodsPromotionPage.do")
	public String addWeChatSingleGoodsPromotionPage(String activity_range,Model model){
		model.addAttribute("activity_range", activity_range);
		return "/WEB-INF/wechatApplet/addSingleGoodsPromotion.jsp";
	}
		
	//微信小程序促销列表页面
	@RequestMapping("/editWechatPromotionActivity.do")
	public String editWechatPromotionActivity(String promotion_activity_id,String type,HttpServletRequest request){
		request.setAttribute("promotion_activity_id", promotion_activity_id);
		if("1".equals(type)){
			//商品折扣
			return "/WEB-INF/wechatApplet/editGoodsPromotion.jsp";
		}else if("2".equals(type)){
			//商品满赠
			return "/WEB-INF/wechatApplet/editGoodsPromotionGift.jsp";
		}else if("3".equals(type)){
			//订单活动
			return "/WEB-INF/wechatApplet/editOrderMarkdown.jsp";
		}else if("4".equals(type)){
			//单品促销
			return "/WEB-INF/wechatApplet/editSingleGoodsPromotion.jsp";
		}
		return "/WEB-INF/wechatApplet/addOrderMarkdown.jsp";
	}
	
	
	@RequestMapping("/addGoodsPromotionPage.do")
	public String addGoodsPromotionPage(){
		return "/WEB-INF/promotion/addGoodsPromotion.jsp";
	}
	@RequestMapping("/addSingleGoodsPromotionPage.do")
	public String addSingleGoodsPromotionPage(String activity_range,Model model){
		model.addAttribute("activity_range", activity_range);
		return "/WEB-INF/promotion/addSingleGoodsPromotion.jsp";
	}
	@RequestMapping("/addGoodsPromotionGiftPage.do")
	public String addGoodsPromotionGiftPage(){
		return "/WEB-INF/promotion/addGoodsPromotionGift.jsp";
	}
	@RequestMapping("/addOrderMarkdownPage.do")
	public String addOrderMarkdownPage(){
		return "/WEB-INF/promotion/addOrderMarkdown.jsp";
	}
	@RequestMapping("/queryShopCouponListPage.do")
	public String queryShopCouponListPage(){
		Subject subject = SecurityUtils.getSubject();
		Session session = subject.getSession();
		Staff staff = (Staff) session.getAttribute("staff");
		Integer shop_type = staff.getShop_type();
		
		if(shop_type == 11) {
			return "/WEB-INF/promotion/queryShopCouponListOil.jsp";
		}
		
		return "/WEB-INF/promotion/queryShopCouponList.jsp";
	}
	@RequestMapping("/queryCbdShopCouponListWithPage.do")
	public String queryCbdShopCouponListWithPage(){
		return "/WEB-INF/cbd/queryCbdShopCouponListWithPage.jsp";
	}
	@RequestMapping("/recordTakingPage.do")
	public String recordTakingPage(String shop_coupon_id,Model model){
		model.addAttribute("shop_coupon_id", shop_coupon_id);
		return "/WEB-INF/promotion/queryCouponRecord.jsp";
	}
	@RequestMapping("/addShopCouponPage.do")
	public String addShopCouponPage(Model model){
		Subject subject = SecurityUtils.getSubject();
		Session session = subject.getSession();
		Staff staff = (Staff) session.getAttribute("staff");
		Integer shop_type = staff.getShop_type();
		
		if(shop_type == 11) {
			//获取所有该加油站的店铺
			List<Map<String,Object>> shopList = activityService.selectShopsList(staff.getShop_unique() + "");
			model.addAttribute("shopList", shopList);
			return "/WEB-INF/promotion/addShopCouponOil.jsp";
		}
		return "/WEB-INF/promotion/addShopCoupon.jsp";
	}
	@RequestMapping("/addSingleShopCouponPage.do")
	public String addSingleShopCouponPage(){
		return "/WEB-INF/promotion/addSingleShopCoupon.jsp";
	}
	@RequestMapping("/editPromotionActivity.do")
	public String editPromotionActivity(String promotion_activity_id,String type,HttpServletRequest request){
		request.setAttribute("promotion_activity_id", promotion_activity_id);
		if("1".equals(type)){
			//商品折扣
			return "/WEB-INF/promotion/editGoodsPromotion.jsp";
		}else if("2".equals(type)){
			//商品满赠
			return "/WEB-INF/promotion/editGoodsPromotionGift.jsp";
		}else if("3".equals(type)){
			//订单活动
			return "/WEB-INF/promotion/editOrderMarkdown.jsp";
		}else if("4".equals(type)){
			//单品促销
			return "/WEB-INF/promotion/editSingleGoodsPromotion.jsp";
		}
		return "/WEB-INF/promotion/addOrderMarkdown.jsp";
	}
	
	/**
	 * 查询促销列表
	 * @param map
	 * @return
	 */
	@RequestMapping("/queryPromotionList.do")
	@ResponseBody
	public PurResult queryPromotionList(
			@RequestParam(value="shop_unique",required=true)Long shop_unique,
			String type,
			String time_status,
			String status,
			@RequestParam(value="activity_range",required=true)String activity_range,
			@RequestParam(value = "page", defaultValue = "1") int page,
			@RequestParam(value = "limit", defaultValue = "8") int pageSize){
		Map<String,Object> map=new HashMap<String, Object>();
		map.put("shop_unique", shop_unique);
		map.put("pageSize", pageSize);
		map.put("startNum", (page-1)*pageSize);
		map.put("type", type);
		map.put("activity_range", activity_range);
		map.put("time_status", time_status);
		map.put("status", status);
		return activityService.queryPromotionList(map);
	}
	/**
	 * 商品列表查询
	 * @param supplier_unique
	 * @param goodsMessage
	 * @param goods_kind_parunique
	 * @param goods_kind_unique
	 * @param pageSize
	 * @return
	 */
	@RequestMapping("queryGoodsByPage.do")
	@ResponseBody
	public PurResult queryGoodsByPage(
			@RequestParam(value="shop_unique",required=true)String shop_unique,
			String goodsMessage,
			String goods_kind_parunique,
			String goods_kind_unique,
			@RequestParam(value = "page", defaultValue = "1") int page,
			@RequestParam(value = "limit", defaultValue = "8") int pageSize
			){
		return activityService.queryGoodsByPage(shop_unique,goodsMessage,goods_kind_parunique,goods_kind_unique,page,pageSize);
	}
	
	@RequestMapping("choseGoodsPage.do")
	public String choseGoodsPage(String type ,Model model){
		model.addAttribute("type", type);
		return "/WEB-INF/manager/choseGoods.jsp";
	}
	
	/**
	 * 商品列表查询
	 * @param shop_unique
	 * @param goodsMessage
	 * @param goods_kind_parunique
	 * @param goods_kind_unique
	 * @param pageSize
	 * @param is_online 线上线下：1线下pc收银 2线上小程序
	 * @return
	 */
	@RequestMapping("queryGoodsByPage1.do")
	@ResponseBody
	public PurResult queryGoodsByPage1(
			@RequestParam(value="shop_unique",required=true)String shop_unique,
			String goodsMessage,
			String goods_kind_parunique,
			String goods_kind_unique,
			@RequestParam(value = "page", defaultValue = "1") int page,
			@RequestParam(value = "limit", defaultValue = "8") int pageSize,
			Integer is_online,
			String supplierUnique
			){
		return activityService.queryGoodsByPage1(shop_unique,goodsMessage,goods_kind_parunique,goods_kind_unique,page,pageSize,is_online,supplierUnique);
	}
	/**
	 * 查询积分提成列表
	 * @param supplier_unique
	 * @param goodsMessage
	 * @param goods_kind_parunique
	 * @param goods_kind_unique
	 * @param pageSize
	 * @return
	 */
	@RequestMapping("queryPointAndCommissionByPage.do")
	@ResponseBody
	public PurResult queryPointAndCommissionByPage(
			@RequestParam(value="shop_unique",required=true)String shop_unique,
			String goodsMessage,
			String goods_kind_parunique,
			String goods_kind_unique,
			@RequestParam(value = "page", defaultValue = "1") int page,
			@RequestParam(value = "limit", defaultValue = "8") int pageSize,
			Integer type
			){
		System.out.println("alloration/queryGoodsByPage.do");
		if(type==2){
			return activityService.queryGoodsByPage(shop_unique,goodsMessage,goods_kind_parunique,goods_kind_unique,page,pageSize);
		}else{
			return activityService.queryGoodsKindPointsAndCommissionByPage(shop_unique,goodsMessage,goods_kind_parunique,goods_kind_unique,page,pageSize);
		}
	}
	/**
	 * 提交商品打折活动
	 * @param supplier_unique
	 * @param uniques
	 * @return
	 */
	@RemoteLog(title="新增商品打折活动", businessType = BusinessType.INSERT)
	@ResponseBody
	@RequestMapping("/submitSupplierStorageOrder.do")
	public PurResult submitSupplierStorageOrder(
			String shop_unique,
			String promotion_activity_name,
			String detailJson,
			String startDate,
			String endDate,
			String order_activity
			){
		return activityService.submitSupplierStorageOrder( shop_unique,promotion_activity_name,startDate,endDate,order_activity,detailJson);
	}
	/**
	 * 提交商品赠品
	 * @param supplier_unique
	 * @param uniques
	 * @return
	 */
	@RemoteLog(title="新增商品赠品", businessType = BusinessType.INSERT)
	@ResponseBody
	@RequestMapping("/submitGoodsGift.do")
	public PurResult submitGoodsGift(
			String shop_unique,
			String promotion_activity_name,
			String detailJson,
			String startDate,
			String endDate,
			String order_activity
			){
		return activityService.submitGoodsGift( shop_unique,promotion_activity_name,startDate,endDate,order_activity,detailJson);
	}
	/**
	 * 提交订单满减
	 * @param supplier_unique
	 * @param uniques
	 * @return
	 */
	@RemoteLog(title="新增订单促销", businessType = BusinessType.INSERT)
	@ResponseBody
	@RequestMapping("/addOrderMarkdown.do")
	public PurResult addOrderMarkdown(
			String shop_unique,
			String promotion_activity_name,
			String startDate,
			String endDate,
			String meet_price1,
			String discount_price1,
			String meet_price2,
			String discount_price2,
			String meet_price3,
			String discount_price3,
			String goods_id1,
			String goods_id2,
			String goods_id3,
			String gift_count1,
			String gift_count2,
			String gift_count3
			){
		return activityService.addOrderMarkdown( shop_unique,promotion_activity_name,startDate,endDate,meet_price1,discount_price1,meet_price2,discount_price2,
				meet_price3,discount_price3,goods_id1,goods_id2,goods_id3,gift_count1,gift_count2,gift_count3);
	}
	//删除活动
	@RemoteLog(title="删除促销活动", businessType = BusinessType.DELETE)
	@RequestMapping("/deleteActivity.do")
	@ResponseBody
	public PurResult deleteActivity(@RequestParam(value="promotion_activity_id",required=true)Integer promotion_activity_id){
		Map<String,Object> map=new HashMap<String, Object>();
		map.put("promotion_activity_id", promotion_activity_id);
		return activityService.deleteActivity(map);
	}
	
	//修改活动状态
	@RemoteLog(title="启停用促销活动", businessType = BusinessType.UPDATE)
	@RequestMapping("/updateActivityStatus.do")
	@ResponseBody
	public PurResult updateActivityStatus(@RequestParam(value="promotion_activity_id",required=true)Integer promotion_activity_id,String status){
		Map<String,Object> map = new HashMap<String, Object>();
		map.put("promotion_activity_id", promotion_activity_id);
		map.put("status", status);
		return activityService.updateActivityStatus(map);
	}
	
	/**
	 * 查询商品折扣详情
	 * @param map
	 * @return
	 */
	@RequestMapping("/queryGoodsMarkdownDetail.do")
	@ResponseBody
	public PurResult queryGoodsMarkdownDetail(
			@RequestParam(value="promotion_activity_id",required=true)Integer promotion_activity_id
			){
		Map<String,Object> map=new HashMap<String, Object>();
		map.put("promotion_activity_id", promotion_activity_id);
		return activityService.queryGoodsMarkdownDetail(map);  
	}
	/**
	 * 查询单品促销详情
	 * @param map
	 * @return
	 */
	@RequestMapping("/querySingleGoodsPromotionDetail.do")
	@ResponseBody
	public PurResult querySingleGoodsPromotionDetail(
			@RequestParam(value="promotion_activity_id",required=true)Integer promotion_activity_id
			){
		Map<String,Object> map=new HashMap<String, Object>();
		map.put("promotion_activity_id", promotion_activity_id);
		return activityService.querySingleGoodsPromotionDetail(map);  
	}
	
	/**
	 * 线上管理-查询限时抢购详情
	 * @param map
	 * @return
	 */
	@RequestMapping("/queryFlashSaleDetail.do")
	@ResponseBody
	public PurResult queryFlashSaleDetail(
			@RequestParam(value="promotion_activity_id",required=true)Integer promotion_activity_id
			){
		Map<String,Object> map=new HashMap<String, Object>();
		map.put("promotion_activity_id", promotion_activity_id);
		return activityService.queryFlashSaleDetail(map);  
	}
	
	/**
	 * 查询商品赠品详情
	 * @param map
	 * @return
	 */
	@RequestMapping("/queryGoodsGiftDetail.do")
	@ResponseBody
	public PurResult queryGoodsGiftDetail(
			@RequestParam(value="promotion_activity_id",required=true)Integer promotion_activity_id
			){
		Map<String,Object> map=new HashMap<String, Object>();
		map.put("promotion_activity_id", promotion_activity_id);
		return activityService.queryGoodsGiftDetail(map);  
	}
	/**
	 * 查询订单活动赠品详情
	 * @param map
	 * @return
	 */
	@RequestMapping("/queryOrderMarkdownDetail.do")
	@ResponseBody
	public PurResult queryOrderMarkdownDetail(
			@RequestParam(value="promotion_activity_id",required=true)Integer promotion_activity_id
			){
		Map<String,Object> map=new HashMap<String, Object>();
		map.put("promotion_activity_id", promotion_activity_id);
		return activityService.queryOrderMarkdownDetail(map);  
	}
	//积分管理页面
	@RequestMapping("/pointsManagerPage.do")
	public String pointsManagerPage(){
		return "/WEB-INF/manager/pointsManagerPage.jsp";
	}
	/**
	 * 提交商品分类积分配置
	 * @return
	 */
	@RemoteLog(title="新增(修改)商品分类积分设置", businessType = BusinessType.UPDATE)
	@ResponseBody
	@RequestMapping("/submitGoodsKindPoint.do")
	public PurResult submitGoodsKindPoint(
			String shop_unique,
			String goods_kind_parunique,
			String goods_kind_child,
			String goods_kind_points_type,
			String goods_kind_points_unit,
			String goods_kind_points_unit_val
			){
		return activityService.submitGoodsKindPoint( shop_unique,goods_kind_parunique,goods_kind_child,goods_kind_points_type,goods_kind_points_unit,goods_kind_points_unit_val);
	}
	/**
	 * 提交商品积分配置
	 */
	@ResponseBody
	@RequestMapping("/submitGoodsPoint.do")
	public PurResult submitGoodsPoint(
			String shop_unique,
			String detailJson
			){
		return activityService.submitGoodsPoint( shop_unique,detailJson);
	}
	//提成管理页面
	@RequestMapping("/commissionManagerPage.do")
	public String commissionManagerPage(){
			return "/WEB-INF/manager/commissionManagerPage.jsp";
	}
	//积分配置查询页面
	@RequestMapping("/queryPointsSetList.do")
	public String queryPointsSetList(){
		return "/WEB-INF/manager/queryPointsSetList.jsp";
	}
	//提交商品分类提成配置
	@RemoteLog(title="新增(修改)商品分类提成设置", businessType = BusinessType.UPDATE)
	@ResponseBody
	@RequestMapping("/submitGoodsKindCommission.do")
	public PurResult submitGoodsKindCommission(
			String shop_unique,
			String goods_kind_parunique,
			String goods_kind_child,
			String goods_kind_commission_type,
			String goods_kind_commission_unit,
			String goods_kind_commission_unit_val
			){
		return activityService.submitGoodsKindCommission( shop_unique,goods_kind_parunique,goods_kind_child,goods_kind_commission_type,goods_kind_commission_unit,goods_kind_commission_unit_val);
	}
	//提交商品提成配置
	@ResponseBody
	@RequestMapping("/submitGoodsCommission.do")
	public PurResult submitGoodsCommission(
			String shop_unique,
			String detailJson
			){
		return activityService.submitGoodsCommission( shop_unique,detailJson);
	}
	
	/**
	 * 提交单品促销
	 * @return
	 */
	@RemoteLog(title="新增单品促销", businessType = BusinessType.INSERT)
	@ResponseBody
	@RequestMapping("/submitSingleGoodsPromotion.do")
	public PurResult submitSingleGoodsPromotion(
			String shop_unique,
			String promotion_activity_name,
			String detailJson,
			String startDate,
			String endDate,
			String activity_range
			){
		return activityService.submitSingleGoodsPromotion( shop_unique,promotion_activity_name,startDate,endDate,detailJson,activity_range);
	}
	
	/**
	 * 线上管理-提交限时抢购
	 * @return
	 */
	@RemoteLog(title="新增限时抢购", businessType = BusinessType.INSERT)
	@ResponseBody
	@RequestMapping("/submitFlashSale.do")
	public PurResult submitFlashSale(
			String shop_unique,
			String promotion_activity_name,
			String detailJson,
			String startDate,
			String endDate,
			String activity_range
			){
		return activityService.submitFlashSale( shop_unique,promotion_activity_name,startDate,endDate,detailJson,activity_range);
	}
	/**
	 * 线上管理-提交限时抢购-编辑
	 * @return
	 */
	@RemoteLog(title="修改限时抢购", businessType = BusinessType.UPDATE)
	@ResponseBody
	@RequestMapping("/updateFlashSale.do")
	public PurResult updateFlashSale(
			String promotion_activity_id,
			String promotion_activity_name,
			String detailJson,
			String startDate,
			String endDate,
			String shop_unique, 
			String activity_range
			){
		return activityService.updateFlashSale( promotion_activity_id,shop_unique,promotion_activity_name,startDate,endDate,detailJson,activity_range);
	}	
	/**
	 * 查询优惠券列表
	 * @param map
	 * @return
	 */
	@RequestMapping("/queryShopCouponList.do")
	@ResponseBody
	public PurResult queryShopCouponList(
			@RequestParam(value="shop_unique",required=false)String shop_unique,
			@RequestParam(value="user_status",required=false)String user_status,
			String give_status,
			@RequestParam(value = "page", defaultValue = "0") int page,
			@RequestParam(value = "limit", defaultValue = "8") int pageSize
			){
		return activityService.queryShopCouponList(shop_unique,user_status,give_status,page,pageSize);
	}
	
	/**
	 * 新增优惠券
	 * @param shop_unique 店铺编号
	 * @param start_time 开始时间
	 * @param end_time 结束时间
	 * @param meet_amount 满足金额
	 * @param coupon_amount 优惠金额
	 * @param type 1：全品类 2：仅限非折扣商品
	 * @param is_time 是否分时段优惠券：1否 1是
	 * @param is_daily 分时段优惠券是否可每天使用：1否 2是
	 * @param daily_num 分时段优惠券每天使用次数
	 * @param times 时段集合，以逗号隔开
	 * @param is_auto_grant 是否自动发放：1否 2是
	 * @param is_grant_num 是否限制发放次数：-1 不发放 1发放
	 * @param grant_num 发放次数
	 * @param exclusive_type 专享优惠券类型：1联通专享
	 * @param designated_shop_unique 指定可使用优惠券的店铺
	 * @param coupon_name 优惠券名称
	 * @param is_online 是否线上优惠券1、线上；2、线下
	 * @return
	 */
	@RemoteLog(title="新增优惠券", businessType = BusinessType.INSERT)
	@ResponseBody
	@RequestMapping("/addShopCoupon.do")
	public PurResult addShopCoupon(
			String shop_unique,
			String start_time,
			String end_time,
			String meet_amount,
			String coupon_amount,
			String type,
			String is_time,
			String is_daily,
			String daily_num,
			String times,
			String is_auto_grant,
			String is_grant_num,
			String grant_num,
			String exclusive_type,
			String days,
			Integer is_online,
			String coupon_name,
			String designated_shop_unique,
			Integer coupon_type,
			String rule_description
			){
		System.out.println(days);
		return activityService.addShopCoupon(shop_unique,start_time,end_time,
				meet_amount,coupon_amount,type,is_time,is_daily,daily_num,times,is_auto_grant,is_grant_num,
				grant_num,exclusive_type,days,is_online,coupon_name,designated_shop_unique,coupon_type,rule_description);
	}
	
	//跳转修改优惠券页面
	@RemoteLog(title="修改优惠券", businessType = BusinessType.UPDATE)
	@RequestMapping("/updateShopCouponPage.do")
	public String updateShopCouponPage(String shop_coupon_id,Model model){
		//获取优惠券信息
		Map<String ,Object> shopCoupon = activityService.getShopCoupon(shop_coupon_id);
		model.addAttribute("shopCoupon", shopCoupon);
		
		Subject subject = SecurityUtils.getSubject();
		Session session = subject.getSession();
		Staff staff = (Staff) session.getAttribute("staff");
		Integer shop_type = staff.getShop_type();
		
		if(shop_type == 11) {
			//获取所有该加油站的店铺
			List<Map<String,Object>> shopList = activityService.selectShopsList(staff.getShop_unique() + "");
			model.addAttribute("shopList", shopList);
		}
		return "/WEB-INF/promotion/updateShopCoupon.jsp";
	}
	@RequestMapping("/updateSingleShopCouponPage.do")
	public String updateSingleShopCouponPage(String shop_coupon_id,Model model){
		//获取优惠券信息
		Map<String ,Object> shopCoupon = activityService.getShopCoupon(shop_coupon_id);
		model.addAttribute("shopCoupon", shopCoupon);
		return "/WEB-INF/promotion/updateSingleShopCoupon.jsp";
	}
	
	/**
	 * 修改优惠券
	 * @param shop_coupon_id 优惠券id
	 * @param start_time 开始时间
	 * @param end_time 结束时间
	 * @param meet_amount 满足金额
	 * @param coupon_amount 优惠金额
	 * @param type 1：全品类 2：仅限非折扣商品
	 * @param is_time 是否分时段优惠券：1否 1是
	 * @param is_daily 分时段优惠券是否可每天使用：1否 2是
	 * @param daily_num 分时段优惠券每天使用次数
	 * @param times 时段集合，以逗号隔开
	 * @param is_auto_grant 是否自动发放：1否 2是
	 * @param old_is_auto_grant
	 * @param is_grant_num 是否限制发放次数：-1 不发放 1发放
	 * @param grant_num 发放次数
	 * @param exclusive_type 专享优惠券类型：1联通专享
	 * @return
	 */
	@ResponseBody
	@RequestMapping("/updateShopCoupon.do")
	public PurResult updateShopCoupon(
			String shop_coupon_id,
			String start_time,
			String end_time,
			String meet_amount,
			String coupon_amount,
			String type,
			String is_time,
			String is_daily,
			String daily_num,
			String times,
			String is_auto_grant,
			String old_is_auto_grant,
			String is_grant_num,
			String grant_num,
			String coupon_name,
			String exclusive_type,
			String days,
			String rule_description
			){
		return activityService.updateShopCoupon(shop_coupon_id,start_time,end_time,
				meet_amount,coupon_amount,type,
				is_time,is_daily,daily_num,
				times,is_auto_grant,old_is_auto_grant,
				is_grant_num,grant_num,exclusive_type,coupon_name,days,rule_description);
	}
	
	/**
	 * 删除优惠券
	 * @param supplier_unique
	 * @param uniques
	 * @return
	 */
	@RemoteLog(title="删除优惠券", businessType = BusinessType.DELETE)
	@ResponseBody
	@RequestMapping("/deleteShopCoupon.do")
	public PurResult deleteShopCoupon(
			String shop_coupon_id
			){
		return activityService.deleteShopCoupon( shop_coupon_id);
	}
	/**
	 * 删除充值配置
	 * @param supplier_unique
	 * @param uniques
	 * @return
	 */
	@ResponseBody
	@RequestMapping("/deleteRechargeConfig.do")
	public PurResult deleteRechargeConfig(
			String platform_recharge_config_id,String shop_coupon_id
			){
		return activityService.deleteRechargeConfig( platform_recharge_config_id,shop_coupon_id);
	}
	
	/**
	 * 充值列表页面
	 */
	@RequestMapping("/queryRechargeConfigListPage.do")
	public String queryRechargeConfigListPage(){
		return "/WEB-INF/platform/queryRechargeConfigList.jsp";
	}
	/**
	 * 新增充值
	 */
	@RequestMapping("/addRechargeConfigPage.do")
	public String addRechargeConfigPage(){
		return "/WEB-INF/platform/addRechargeConfig.jsp";
	}
	
	/**
	 * 编辑充值配置
	 * @param platform_recharge_config_id
	 * @return
	 */
	@RequestMapping("/editRechargeConfigPage.do")
	public String editRechargeConfigPage(String platform_recharge_config_id,Model model){
		//获取配置详情
		Map<String ,Object> config = activityService.getRechargeConfig(platform_recharge_config_id);
		model.addAttribute("config", config);
		return "/WEB-INF/platform/editRechargeConfig.jsp";
	}
	/**
	 * 查询充值列表
	 * @param map
	 * @return
	 */
	@RequestMapping("/queryRechargeConfigList.do")
	@ResponseBody
	public PurResult queryRechargeConfigList(
			@RequestParam(value = "page", defaultValue = "0") int page,
			@RequestParam(value = "limit", defaultValue = "8") int pageSize
			){
		return activityService.queryRechargeConfigList(page,pageSize);
	}
	/**
	 * 新增充值
	 * @param supplier_unique
	 * @param uniques
	 * @return
	 */
	@ResponseBody
	@RequestMapping("/addRechargeConfig.do")
	public PurResult addRechargeConfig(HttpServletRequest request){
		Map<String, Object> params = HandleMessyCode.handle(ServletsUtil.getParameters(request));
		return activityService.addRechargeConfig(params);
	}
	
	/**
	 * 修改充值
	 * @return
	 */
	@ResponseBody
	@RequestMapping("/updateRechargeConfig.do")
	public PurResult updateRechargeConfig(HttpServletRequest request){
		Map<String, Object> params = HandleMessyCode.handle(ServletsUtil.getParameters(request));
		return activityService.updateRechargeConfig(params);
	}
	/**
	 * 积分抵扣页面
	 */
	@RequestMapping("/setPointUsePage.do")
	public String setPointUsePage(){
		return "/WEB-INF/promotion/setPointUse.jsp";
	}
	
	/**
	 * 积分抵扣
	 * @param supplier_unique
	 * @param uniques
	 * @return
	 */
	@RemoteLog(title="积分抵扣设置", businessType = BusinessType.UPDATE)
	@ResponseBody
	@RequestMapping("/setPointUse.do")
	public PurResult setPointUse(
			String shop_unique,
			String points_val,
			String money,
			String use_top,
			Double use_money
			){
		return activityService.setPointUse( shop_unique,points_val,money,use_top,use_money);
	}
	/**
	 * 查询积分抵扣
	 * @param supplier_unique
	 * @param uniques
	 * @return
	 */
	@ResponseBody
	@RequestMapping("/querySetPointUse.do")
	public PurResult querySetPointUse(
			String shop_unique
			){
		return activityService.querySetPointUse( shop_unique);
	}
	
	/**
	 * 分类主题页面
	 */
	@RequestMapping("/queryClassThemeListPage.do")
	public String queryClassThemeListPage(){
		return "/WEB-INF/platform/queryClassThemeList.jsp";
	}
	
	/**
	 * 引导页图片
	 */
	@RequestMapping("/toBootImg.do")
	public String toBootImg(){
		return "/WEB-INF/platform/bootImg.jsp";
	}
	
	@RequestMapping("/editBootImg.do")
	public String editBootImg(HttpServletRequest request){
		return "/WEB-INF/platform/editBootImg.jsp";
	}
	
	/**
	 * 修改分类主题页面
	 */
	@RequestMapping("/editClassThemePage.do")
	public String editClassThemePage(String class_theme_id,HttpServletRequest request){
		Map<String, Object> map= activityService.queryClassThemeById(class_theme_id);
		request.setAttribute("class_theme_id", map.get("class_theme_id"));
		request.setAttribute("class_theme_image", map.get("class_theme_image"));
		request.setAttribute("class_theme_name", map.get("class_theme_name"));
		return "/WEB-INF/platform/editClassTheme.jsp";
	}
	
	/**
	 * 查询分类主题列表
	 * @param map
	 * @return
	 */
	@RequestMapping("/queryClassThemeList.do")
	@ResponseBody
	public PurResult queryClassThemeList(
			@RequestParam(value = "page", defaultValue = "1") int page,
			@RequestParam(value = "limit", defaultValue = "8") int pageSize
			){
		return activityService.queryClassThemeList(page,pageSize);
	}
	
	/**
	 * 引导页图片
	 */
	@RequestMapping("/queryBootImg.do")
	@ResponseBody
	public PurResult queryBootImg(){
		return activityService.queryBootImg();
	}
	/**
	 * 修改分类主题
	 * @param map
	 * @return
	 */
	@RequestMapping("/editClassTheme.do")
	@ResponseBody
	public PurResult editClassTheme(
			String  class_theme_id,
			String  class_theme_name,
			HttpServletRequest request
			){
		return activityService.editClassTheme(class_theme_id,class_theme_name,request);
	}
	
	@RequestMapping("/uploadImg.do")
	@ResponseBody
	public PurResult uploadImg(HttpServletRequest request){
		return activityService.uploadImg(request);
	}
	
	@RequestMapping("/uploadGoodsDictImg.do")
	@ResponseBody
	public PurResult uploadGoodsDictImg(HttpServletRequest request){
		return activityService.uploadGoodsDictImg(request);
	}
	
	/**
	 * 修改引导页图片
	 */
	@RequestMapping("/updateBootImg.do")
	@ResponseBody
	public PurResult updateBootImg(
			String  id,
			String  time,
			String  url,
			HttpServletRequest request
			){
		return activityService.updateBootImg(id,time,url,request);
	}
	
	/**
	 * 主题活动商品页面
	 */
	@RequestMapping("/queryClassThemeGoodsListPage.do")
	public String queryClassThemeGoodsListPage(){
		return "/WEB-INF/promotion/queryClassThemeGoodsListPage.jsp";
	}
	
	/**
	 * 查询主题活动有的商品
	 * @param map
	 * @return
	 */
	@RequestMapping("/queryClassThemeGoodsList.do")
	@ResponseBody
	public PurResult queryClassThemeGoodsList(
			@RequestParam(value="class_theme_id",required=true)String class_theme_id,
			@RequestParam(value="shop_unique",required=true)String shop_unique
			){
		Map<String,Object> map=new HashMap<String, Object>();
		map.put("class_theme_id", class_theme_id);
		map.put("shop_unique", shop_unique);
		return activityService.queryClassThemeGoodsList(map);  
	}
	/**
	 * 查询主题活动有的商品
	 * @param map
	 * @return
	 */
	@RequestMapping("/deleteClassThemeGoodsByGoodsBarcode.do")
	@ResponseBody
	public PurResult deleteClassThemeGoodsByGoodsBarcode(
			@RequestParam(value="goods_id",required=true)String goods_id,
			@RequestParam(value="shop_unique",required=true)String shop_unique
			){
		Map<String,Object> map=new HashMap<String, Object>();
		map.put("goods_id", goods_id);
		map.put("shop_unique", shop_unique);
		return activityService.deleteClassThemeGoodsByGoodsBarcode(map);  
	}
	/**
	 * 添加主题活动商品
	 * @param map
	 * @return
	 */
	@RequestMapping("/addClassThemeGoodsList.do")
	@ResponseBody
	public PurResult addClassThemeGoodsList(
			@RequestParam(value="class_theme_id",required=true)String class_theme_id,
			@RequestParam(value="goods_id",required=true)String goods_id,
			@RequestParam(value="shop_unique",required=true)String shop_unique
			){
		return activityService.addClassThemeGoodsList(class_theme_id,goods_id,shop_unique);  
	}
	
	/**
	 * 打开界面，前端上传excel表格，并获取会员信息
	 * @return
	 */
	@RequestMapping("/toCustomerRecharge.do")
	public String toCustomerRecharge() {
		return "/WEB-INF/customer/customerOnlineRecharge.jsp";
	}
	
	//平台会员页面
	@RequestMapping("/queryPlatformCusListPage.do")
	public String queryPlatformCusListPage(){
		return "/WEB-INF/platform/queryPlatformCusList.jsp";
	}
	//平台消费画像页面
	@RequestMapping("/queryCusConsumePage.do")
	public String queryCusConsumePage(){
		return "/WEB-INF/customer/customerOnlinePt.jsp";
	}
	//平台会员返利配置页面
	@RequestMapping("/queryPlatformCusReward.do")
	public String queryPlatformCusReward(){
		return "/WEB-INF/platform/queryPlatformCusReward.jsp";
	}	
	/**
	  * 平台会员返利列表
	 * @param map
	 * @return
	 */
	@RequestMapping("/queryRewardList.do")
	@ResponseBody
	public PurResult queryRewardList(
			String shop_name,
			String valid,
			String reward_type,
			@RequestParam(value = "page", defaultValue = "1") int page,
			@RequestParam(value = "limit", defaultValue = "10") int pageSize,
			String field,
			String order
			){
		Map<String,Object> map=new HashMap<String, Object>();
		map.put("pageSize", pageSize);
		map.put("startNum", (page-1)*pageSize);
		map.put("shop_name", shop_name);
		map.put("valid", valid);
		map.put("reward_type", reward_type);
		map.put("field",field);
		map.put("order",order);
		return activityService.queryRewardList(map);
	}
	/**
	 * 查询平台会员列表
	 * @param map
	 * @return
	 */
	@RequestMapping("/queryPlatformCusList.do")
	@ResponseBody
	public PurResult queryPlatformCusList(
			String  cusMessage,
			@RequestParam(value = "page", defaultValue = "0") int page,
			@RequestParam(value = "limit", defaultValue = "8") int pageSize
			){
		return activityService.queryPlatformCusList(cusMessage,page,pageSize);
	}
	/**
	 * 添加会员返利店铺
	 * @param map
	 * @return
	 */
	@RequestMapping("/addReward.do")
	@ResponseBody
	public PurResult addReward(@RequestParam(value="data",required=true)String data){
		
		JSONArray jsonArray=JSONArray.fromObject(data);//多包装
		@SuppressWarnings("unchecked")
		List<PageData> data2 = JSONArray.toList(jsonArray, new PageData(), new JsonConfig());
		return activityService.addReward(data2);  
	}
	/**
	 * 修改会员返利店铺
	 * @param map
	 * @return
	 */
	@RequestMapping("/updateReward2.do")
	@ResponseBody
	public PurResult updateReward2(HttpServletRequest request){
		
		Map<String, Object> params = HandleMessyCode.handle(ServletsUtil.getParameters(request));
		return activityService.edtitReward(params);  
	}
	/**
	 * 删除返利规则
	 * @param map
	 * @return
	 */
	@RequestMapping("/deleteReward.do")
	@ResponseBody
	public PurResult deleteReward(@RequestParam(value="id",required=true)String id){
		
		return activityService.deleteReward(id);  
	}
	/**
	 * 修改返利规则
	 * @param map
	 * @return
	 */
	@RequestMapping("/updateReward.do")
	@ResponseBody
	public PurResult updateReward(@RequestParam(value="id",required=true)String id,
			@RequestParam(value="valid",required=true)int valid){
		
		return activityService.updateReward(id,valid);  
	}
	/**
	 * 查询优惠券领取记录
	 * @param map
	 * @return
	 */
	@RequestMapping("/queryCouponRecord.do")
	@ResponseBody
	public PurResult queryCouponRecord(String use_status,
			@RequestParam(value="shop_coupon_id",required=true)String shop_coupon_id,
			@RequestParam(value = "page", defaultValue = "1") int page,
			@RequestParam(value = "limit", defaultValue = "8") int pageSize){
		Map<String,Object> map=new HashMap<String, Object>();
		map.put("pageSize", pageSize);
		map.put("use_status", use_status);
		map.put("startNum", (page-1)*pageSize);
		map.put("shop_coupon_id", shop_coupon_id);
		return activityService.queryCouponRecord(map);
	}
	
	/**
	 * 查询商品信息
	 * @param map
	 * @return
	 */
	@RequestMapping("/queryGoodsMsgList.do")
	@ResponseBody
	public ShopsResult queryGoodsMsgList(HttpServletRequest request) {
		Map<String,Object> map = HandleMessyCode.handle(ServletsUtil.getParameters(request));
		return activityService.queryGoodsMsgList(map);
	}
	
	@RequestMapping("queryGoodsKind.do")
	@ResponseBody
	public PurResult queryGoodsKind(
			@RequestParam(value="shop_unique",required=true)String shop_unique,
			String goodsMessage,
			@RequestParam(value = "page", defaultValue = "1") int page,
			@RequestParam(value = "limit", defaultValue = "8") int pageSize
			){
		return activityService.queryGoodsKind(shop_unique,goodsMessage,page,pageSize);
	}
	
	//益农电视管理
	@RequestMapping("/queryEshowTV.do")
	public String queryEshowTV(){
		return "/WEB-INF/platform/queryEshowTV.jsp";
	}	
	/**
	  * 益农电视管理列表
	 * @param map
	 * @return
	 */
	@RequestMapping("/queryEshowTVList.do")
	@ResponseBody
	public PurResult queryRewardList(
			String shop_name,
			String valid,
			String reward_type,
			@RequestParam(value = "page", defaultValue = "1") int page,
			@RequestParam(value = "limit", defaultValue = "10") int pageSize){
		Map<String,Object> map=new HashMap<String, Object>();
		map.put("pageSize", pageSize);
		map.put("startNum", (page-1)*pageSize);
		map.put("shop_name", shop_name);
		return activityService.queryEshowTVList(map);
	}
	
	/**
	 * 
	 * 云闪付活动20200701
	 */
	@RequestMapping("/queryCoudflashPay.do")
	public String queryCoudflashPay(){
		return "/WEB-INF/platform/queryCoudflashPay.jsp";
	}	
	/**
	  * 查询云闪付活动商品
	 * @param map
	 * @return
	 */
	@RequestMapping("/queryCoudflashGoods.do")
	@ResponseBody
	public PurResult queryCoudflashGoods(
			String time_status,
			String status,
			@RequestParam(value = "page", defaultValue = "1") int page,
			@RequestParam(value = "limit", defaultValue = "10") int pageSize){
		Map<String,Object> map=new HashMap<String, Object>();
		map.put("pageSize", pageSize);
		map.put("startNum", (page-1)*pageSize);
		map.put("time_status", time_status);
		map.put("status", status);
		return activityService.queryCoudflashGoods(map);
	}
	/**
	 * 云闪付
	 * @return
	 */
	@RequestMapping("/addCoudflashGood.do")
	public String addCoudflashGood(){
		return "/WEB-INF/platform/addCoudflashGood.jsp";
	}
	/**
	 * 云闪付
	 * @return
	 */
	@RequestMapping("/chooseCoudflashGood.do")
	public String chooseCoudflashGood(){
		return "/WEB-INF/platform/chooseCoudflashGood.jsp";
	}
	/**
	 * 提交商品打折活动
	 * @param supplier_unique
	 * @param uniques
	 * @return
	 */
	@ResponseBody
	@RequestMapping("/submitCoudflashGoods.do")
	public PurResult submitCoudflashGoods(
			String detailJson,
			String startDate,
			String endDate,
			String status
			){
		return activityService.submitCoudflashGoods( startDate,endDate,status,detailJson);
	}
	/**
	 * 修改活动状态
	 * @param supplier_unique
	 * @param uniques
	 * @return
	 */
	@ResponseBody
	@RequestMapping("/updateYSFStatus.do")
	public PurResult updateYSFStatus(String id,String status){
		return activityService.updateYSFStatus( id,status);
	}
	
	@RequestMapping("foodManagerListPage.do")
	public String foodManagerListPage(int type,HttpServletRequest request){
		request.setAttribute("type", type);
		return "/WEB-INF/foodManager/foodManagerList.jsp";
	}
	@RequestMapping("addFoodManagerPage.do")
	public String addFoodManagerPage(int type,HttpServletRequest request){
		request.setAttribute("type", type);
		return "/WEB-INF/foodManager/addFoodManagerPage.jsp";
	}
	@RequestMapping("editFoodManagerPage.do")
	public String editFoodManagerPage(int type,HttpServletRequest request,String id){
		request.setAttribute("type", type);
		request.setAttribute("id", id);
		Map<String,Object> map=new HashMap<String, Object>();
		map.put("id", id);
		Map<String,Object> data= activityService.queryFoodManager(map);
		request.setAttribute("data", data);
		return "/WEB-INF/foodManager/editFoodManagerPage.jsp";
	}
	@RequestMapping("editNewsManagerPage.do")
	public String editNewsManagerPage(HttpServletRequest request,String id){
		request.setAttribute("id", id);
		Map<String,Object> map=new HashMap<String, Object>();
		map.put("id", id);
		Map<String,Object> data= activityService.queryNewsManager(map);
		request.setAttribute("data", data);
		return "/WEB-INF/news/editNewsManagerPage.jsp";
	}
	@RequestMapping("editReportConfigPage.do")
	public String editReportConfigPage(HttpServletRequest request,String id){
		request.setAttribute("id", id);
		Map<String,Object> map=new HashMap<String, Object>();
		map.put("id", id);
		Map<String,Object> data= activityService.queryReportConfig(map);
		request.setAttribute("data", data);
		return "/WEB-INF/reportConfig/editReportConfigPage.jsp";
	}
	
	/**
	 * 查询同管理员的店铺信息列表
	 * @param map
	 * @return
	 */
	@RequestMapping("/queryFoodManagerList.do")
	@ResponseBody
	public PurResult queryFoodManagerList(
			@RequestParam(value="type",defaultValue="1")String type,
			@RequestParam(value="shopMessage",defaultValue="")String shopMessage,
			@RequestParam(value="managerUnique",required=true)Long managerUnique,
			@RequestParam(value = "page", defaultValue = "1") int page,
			@RequestParam(value = "limit", defaultValue = "15") int pageSize
			){
		Map<String,Object> map=new HashMap<String, Object>();
		map.put("type", type);
		map.put("managerUnique", managerUnique);
		map.put("pageSize", pageSize);
		map.put("startNum", (page-1)*pageSize);
		if(shopMessage!=null&&!"".equals(shopMessage)){
			map.put("shopMessage", "%"+shopMessage+"%");
		}
		return activityService.queryFoodManagerList(map);
	}
	
	//上传文件
    @ResponseBody
    @RequestMapping("/uploadFile.do")
    public String uploadFile(HttpServletRequest request, @Param("file") MultipartFile file) throws IOException {
        System.out.println("上传图片");
    	Map<String, Object> result = new HashMap<>();
        Map<String, Object> map2 = new HashMap<>();
        if (file != null) {
        	InputStream is = file.getInputStream();
        	String filePath = this.getClass().getClassLoader().getResource("../../").getPath();
			filePath=filePath.substring(0, filePath.length()-request.getContextPath().length());
            System.out.println(filePath.toString());
            try {
                String substring = file.getOriginalFilename();
                // 图片的路径+文件名称
                String fileName = "/static/upload/" + substring;
                System.out.println(fileName);
                // 图片的在服务器上面的物理路径
                File destFile = new File(filePath, fileName);
                // 生成upload目录
                File parentFile = destFile.getParentFile();
                if (!parentFile.exists()) {
                    parentFile.mkdirs();// 自动生成upload目录
                }
                // 把上传的临时图片，复制到当前项目的webapp路径
                FileCopyUtils.copy(file.getInputStream(), new FileOutputStream(destFile));
                SFTPUtil sftp = new SFTPUtil(FTPConfig.username, FTPConfig.password, FTPConfig.host, FTPConfig.port);   
    	        sftp.login(); 
    	        boolean flag=sftp.upload(FTPConfig.goods_path+"/"+"8302016134121", substring, is);   
    			String shop_picture_path="image" + "/" + "8302016134121"+"/"+substring;
    			sftp.logout(); 
                
                map2 = new HashMap<>();
                result.put("name", file.getOriginalFilename());// 新的文件名
                result.put("originalName", file.getOriginalFilename());// 原始文件名
                result.put("size", file.getSize());
                result.put("state", "SUCCESS");
                result.put("url", shop_picture_path);// 展示图片的请求url
                System.out.println(shop_picture_path);
            /*    
                map.put("code", 0);//0表示成功，1失败
                map.put("msg", "上传成功");//提示消息
                map.put("data", map2);
                map2.put("src", fileName);//图片url
                map2.put("url", fileName);//图片url
                map2.put("title", substring);//图片名称，这个会显示在输入框里
*/            } catch (Exception e) {
                e.printStackTrace();
            }
        }
            String jStr = JSON.toJSONString(result);
            System.out.println(jStr);
            return   jStr;              

    }

	/**
	 * 上传并且压缩图片
	 */
	@ResponseBody
	@RequestMapping("/uploadFileCompress.do")
	public String uploadFileCompress(HttpServletRequest request, @Param("file") MultipartFile file) throws IOException {
		String shop_unique = request.getParameter("shop_unique");
		if (file.isEmpty() || ObjectUtils.isEmpty(shop_unique)){
			return "{}";
		}
		JSONObject result = new JSONObject();
		try {
			String[] oriName = file.getOriginalFilename().split("\\.");
			File originFile = ImageZipUtils.MultipartFileToFile(file);
			byte[] bytes = ImageZipUtils.file2byte(originFile);
			byte[] compressImg = ImageZipUtils.compressPicForScale(bytes, 150, 1, false, 0, 0);
			String newFileName = StringUtils.join(System.currentTimeMillis(),RandomUtil.randomNumbers(6),".",oriName[1]);
			SFTPUtil sftp = new SFTPUtil(FTPConfig.username, FTPConfig.password, FTPConfig.host, FTPConfig.port);
			sftp.login( );
			sftp.upload(FTPConfig.goods_path+"/"+shop_unique+"/", newFileName, ImageZipUtils.byte2Input(compressImg));

			result.put("name", newFileName);// 新的文件名
			result.put("originalName", file.getOriginalFilename());// 原始文件名
			result.put("size", file.getSize());
			result.put("state", "SUCCESS");
			result.put("url", StringUtils.join("/image/",shop_unique,"/",newFileName));// 展示图片的请求url
		}catch (Exception e){
		    e.printStackTrace();
		}
		return result.toString();
	}

    @RequestMapping(value="upload")
    @ResponseBody
    public Map<String,Object> upload(HttpServletRequest req,String url){
    	System.out.println(url);
        Map<String,Object> result = new HashMap<String, Object>();
        String filePath = this.getClass().getClassLoader().getResource("../../").getPath();
		filePath=filePath.substring(0, filePath.length()-req.getContextPath().length());
        System.out.println(filePath.toString());
        
        
        try {
        	String fileName=UUID.randomUUID()+".jpg";
        	URL imageUrl = new URL(url);
        	InputStream is = new URL(imageUrl.toString()).openStream();

        	SFTPUtil sftp = new SFTPUtil(FTPConfig.username, FTPConfig.password, FTPConfig.host, FTPConfig.port);   
	        sftp.login(); 
	        boolean flag=sftp.upload(FTPConfig.goods_path+"/"+"8302016134121", fileName, is);   
			String shop_picture_path="https://file.buyhoo.cc/image" + "/" + "8302016134121"+"/"+fileName;
			System.out.println(shop_picture_path);
			sftp.logout(); 
        	
            result.put("state", 1);// UEDITOR的规则:不为SUCCESS则显示state的内容
            result.put("url", shop_picture_path);
            result.put("title", shop_picture_path);
            result.put("original", shop_picture_path);
        }
        catch (Exception e) {
            System.out.println(e.getMessage());
            result.put("state", "文件上传失败!");
            result.put("url","");
            result.put("title", "");
            result.put("original", "");
            System.out.println("文件 上传失败!");
        }
        
        return result;
    }
    
    public static void downLoadFromUrl(String urlStr, String fileName, String savePath) {
		try {
			System.out.println(urlStr);
			URL url = new URL(urlStr);
			HttpURLConnection conn = (HttpURLConnection) url.openConnection();
			// 设置超时间为3秒
			conn.setConnectTimeout(3 * 1000);
			// 防止屏蔽程序抓取而返回403错误
			conn.setRequestProperty("User-Agent", "Mozilla/4.0 (compatible; MSIE 5.0; Windows NT; DigExt)");
			// 得到输入流
			InputStream inputStream = conn.getInputStream();
			
			byte[] buffer = new byte[1024];
			int len = 0;
			ByteArrayOutputStream bos = new ByteArrayOutputStream();
			while ((len = inputStream.read(buffer)) != -1) {
				bos.write(buffer, 0, len);
			}
			bos.close();
			 
			// 获取自己数组
			byte[] getData = bos.toByteArray();
 
			// 文件保存位置
			File saveDir = new File(savePath);
			if (!saveDir.exists()) {
				saveDir.mkdir();
			}
			File file = new File(saveDir + File.separator + fileName);
			FileOutputStream fos = new FileOutputStream(file);
			fos.write(getData);
			if (fos != null) {
				fos.close();
			}
			if (inputStream != null) {
				inputStream.close();
			}
			System.out.println(fileName+ ":" + url + " download success");
		} catch (Exception e) {
			e.printStackTrace();
			System.err.println(urlStr);
		}
	}

    
    
    @RequestMapping("/addFoodManager.do")
	@ResponseBody
	public ShopsResult  addFoodManager(
			HttpServletRequest request,
			@RequestParam(value="shop_unique",required=true)String shop_unique,
			String type,
			String title,
			String remark,
			String content,
			String id
			
			) throws Exception{
		Map<String,Object> map=new HashMap<String, Object>();
		map.put("shop_unique", shop_unique);
		map.put("title", title);
		map.put("remark", remark);
		map.put("content", content);
		map.put("type", type);
		map.put("id", id);
		
		return activityService.addFoodManager(map, request);
	}
    @RequestMapping("/deleteFoodManager.do")
    @ResponseBody
    public ShopsResult  deleteFoodManager(
    		String id
    		
    		) throws Exception{
    	Map<String,Object> map=new HashMap<String, Object>();
    	map.put("id", id);
    	
    	return activityService.deleteFoodManager(map);
    }
    @RequestMapping("/deleteNewsManager.do")
    @ResponseBody
    public ShopsResult  deleteNewsManager(
    		String id
    		
    		) throws Exception{
    	Map<String,Object> map=new HashMap<String, Object>();
    	map.put("id", id);
    	
    	return activityService.deleteNewsManager(map);
    }
    @RequestMapping("/deleteReportConfig.do")
    @ResponseBody
    public ShopsResult  deleteReportConfig(
    		String id
    		
    		) throws Exception{
    	Map<String,Object> map=new HashMap<String, Object>();
    	map.put("id", id);
    	
    	return activityService.deleteReportConfig(map);
    }
    
    @RequestMapping("queryNewsManagerPage.do")
	public String queryNewsManagerPage(HttpServletRequest request){
		return "/WEB-INF/news/newsManagerList.jsp";
	}
    @RequestMapping("queryReportConfigPage.do")
    public String queryReportConfigPage(HttpServletRequest request){
    	return "/WEB-INF/reportConfig/queryReportConfigPage.jsp";
    }
    
    /**
	 * 查询新闻列表
	 * @param map
	 * @return
	 */
	@RequestMapping("/queryNewsManagerList.do")
	@ResponseBody
	public PurResult queryNewsManagerList(
			@RequestParam(value="shopMessage",defaultValue="")String shopMessage,
			@RequestParam(value = "page", defaultValue = "1") int page,
			@RequestParam(value = "limit", defaultValue = "15") int pageSize
			){
		Map<String,Object> map=new HashMap<String, Object>();
		map.put("pageSize", pageSize);
		map.put("startNum", (page-1)*pageSize);
		if(shopMessage!=null&&!"".equals(shopMessage)){
			map.put("shopMessage", "%"+shopMessage+"%");
		}
		return activityService.queryNewsManagerList(map);
	}
	@RequestMapping("/addNewsManagerPage.do")
	public String addNewsManagerPage(HttpServletRequest request){
		return "/WEB-INF/news/addNewsManagerPage.jsp";
	}
	@RequestMapping("/addReportConfigPage.do")
	public String addReportConfigPage(HttpServletRequest request){
		return "/WEB-INF/reportConfig/addReportConfigPage.jsp";
	}
	
	@RequestMapping("/newsIndex.do")
	public String newsIndex(HttpServletRequest request){
		return "/WEB-INF/news_index.jsp";
	}
	@RequestMapping("/lookNewsManagerListPage.do")
	public String lookNewsManagerListPage(HttpServletRequest request){
		return "/WEB-INF/supplierShopping/lookNewsList.jsp";
	}
	@RequestMapping("/lookNewsDetailPage.do")
	public String lookNewsDetailPage(HttpServletRequest request,String id){
		Map<String,Object> map=new HashMap<String, Object>();
		map.put("id", id);
		Map<String,Object> data= activityService.queryNewsManager(map);
		request.setAttribute("data", data);
		return "/WEB-INF/lookNewsDetail.jsp";
	}
	
	@RequestMapping("/addNewsManager.do")
	@ResponseBody
	public ShopsResult addNewsManager(
				HttpServletRequest request,
				String title,
				String startDate,
				String text_desc,
				String content,
				String id
				
				) throws Exception{
			Map<String,Object> map=new HashMap<String, Object>();
			map.put("startDate", startDate);
			map.put("title", title);
			map.put("text_desc", text_desc);
			map.put("content", content.replaceAll("&amp;", "&"));
			map.put("id", id);
			
			return activityService.addNewsManager(map, request);
		}

	/**
	 * 查询到期列表
	 * @param map
	 * @return
	 */
	@RequestMapping("/queryReortConfigList.do")
	@ResponseBody
	public PurResult queryReortConfigList(
			@RequestParam(value="shopMessage",defaultValue="")String shopMessage,
			@RequestParam(value = "page", defaultValue = "1") int page,
			@RequestParam(value = "limit", defaultValue = "15") int pageSize
			){
		Map<String,Object> map=new HashMap<String, Object>();
		map.put("pageSize", pageSize);
		map.put("startNum", (page-1)*pageSize);
		if(shopMessage!=null&&!"".equals(shopMessage)){
			map.put("shopMessage", "%"+shopMessage+"%");
		}
		return activityService.queryReortConfigList(map);
	}
	
	@RequestMapping("/addReportConfig.do")
	@ResponseBody
	public ShopsResult addReportConfig(
				HttpServletRequest request,
				String name,
				String startDate,
				String endDate,
				String phone,
				String ip,
				String id
				
				) throws Exception{
			Map<String,Object> map=new HashMap<String, Object>();
			map.put("startDate", startDate);
			map.put("endDate", endDate);
			map.put("name", name);
			map.put("phone", phone);
			map.put("ip", ip);
			map.put("id", id);
			
			return activityService.addReportConfig(map, request);
		}
	
	@ResponseBody
	@RequestMapping("/addSingleShopCoupon.do")
	public PurResult addSingleShopCoupon(
			String shop_unique,
			String start_time,
			String end_time,
			String coupon_name,
			String goods_barcode,
			String goods_name,
			String goods_in_price,
			String goods_price,
			String count,
			String exclusive_type
			){
		return activityService.addSingleShopCoupon(shop_unique,start_time,end_time,
				coupon_name,goods_barcode,goods_name,
				goods_in_price,goods_price,count,
				exclusive_type);
	}
	@ResponseBody
	@RequestMapping("/updateSingleShopCoupon.do")
	public PurResult updateSingleShopCoupon(
			String shop_unique,
			String start_time,
			String end_time,
			String coupon_name,
			String goods_barcode,
			String goods_name,
			String goods_in_price,
			String goods_price,
			String count,
			String shop_coupon_id,
			String id,
			String exclusive_type
			){
		return activityService.updateSingleShopCoupon(shop_unique,start_time,end_time,
				coupon_name,goods_barcode,goods_name,
				goods_in_price,goods_price,count,
				exclusive_type,shop_coupon_id,id);
	}
	
}
