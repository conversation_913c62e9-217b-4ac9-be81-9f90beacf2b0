package org.haier.shop.controller;

import java.util.HashMap;
import java.util.Map;

import javax.annotation.Resource;
import org.haier.shop.service.Test2Service;
import org.haier.shop.util.IPGet;
import org.haier.shop.util.ShopsResult;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

@Controller
@RequestMapping("/test2")
public class Test2Controller {
	@Resource
	private Test2Service test2Service;
	
	
	@RequestMapping("/getNetworkInterfaces.do")
	@ResponseBody
	public ShopsResult getNetworkInterfaces() {
		ShopsResult sr = new ShopsResult();
		IPGet.getIp();
		sr.setData(IPGet.ExtranetIP+"========"+IPGet.IntranetIP);;
		return sr;
	}
	
	@RequestMapping("/goodsFORpage.do")
	@ResponseBody
	public ShopsResult goodsFORpage(
				@RequestParam(value="shopUnique")String shopUnique,
				String goodsMessage,
				Integer pageSize,
				String groupUnique,
				String kindUnique,
				Integer stockType
			){
		Map<String,Object> map=new HashMap<String, Object>();
		map.put("shopUnique", shopUnique);
		if(null!=goodsMessage){
			map.put("goodsMessage", "%"+goodsMessage+"%");
		}
		map.put("pageSize", pageSize);
		map.put("groupUnique", groupUnique);
		map.put("kindUnique", kindUnique);
		map.put("stockType", stockType);
		return test2Service.goodsFORpage(map);
	}
	
	@RequestMapping("/goodsFORpagea.do")
	@ResponseBody
	public ShopsResult goodsFORpagea(
				@RequestParam(value="shopUnique")String shopUnique,
				String goodsMessage,
				Integer pageSize,
				String groupUnique,
				String kindUnique,
				Integer stockType
			){
		Map<String,Object> map=new HashMap<String, Object>();
		map.put("shopUnique", shopUnique);
		if(null!=goodsMessage){
			map.put("goodsMessage", "%"+goodsMessage+"%");
		}
		map.put("pageSize", pageSize);
		map.put("groupUnique", groupUnique);
		map.put("kindUnique", kindUnique);
		map.put("stockType", stockType);
		return test2Service.goodsFORpagea(map);
	}
	
	@RequestMapping("/queryShopsList.do")
	@ResponseBody
	public ShopsResult queryShopsList(){
		return test2Service.queryShopsList();
	}
	
	@RequestMapping("/testEcharts.do")
	public String testEcharts(){
		return "/WEB-INF/testEcharts.jsp";
	}
}
