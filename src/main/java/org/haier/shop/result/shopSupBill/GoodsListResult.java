package org.haier.shop.result.shopSupBill;

import java.math.BigDecimal;

public class GoodsListResult {
    private String goodsBarcode;
    private String goodsPicturepath;
    private BigDecimal purchaseGoodsCount;

    public String getGoodsBarcode() {
        return goodsBarcode;
    }

    public void setGoodsBarcode(String goodsBarcode) {
        this.goodsBarcode = goodsBarcode;
    }

    public String getGoodsPicturepath() {
        return goodsPicturepath;
    }

    public void setGoodsPicturepath(String goodsPicturepath) {
        this.goodsPicturepath = goodsPicturepath;
    }

    public BigDecimal getPurchaseGoodsCount() {
        return purchaseGoodsCount;
    }

    public void setPurchaseGoodsCount(BigDecimal purchaseGoodsCount) {
        this.purchaseGoodsCount = purchaseGoodsCount;
    }
}
