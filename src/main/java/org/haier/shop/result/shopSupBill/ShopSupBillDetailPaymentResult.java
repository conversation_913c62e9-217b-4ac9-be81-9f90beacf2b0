package org.haier.shop.result.shopSupBill;

import java.math.BigDecimal;
import java.util.List;

public class ShopSupBillDetailPaymentResult {
    private Long paymentId;

    private List<String> voucherPicturepath;
    private BigDecimal paymentMoney;
    private String paymentRemark;
    public Long getPaymentId() {
        return paymentId;
    }

    public void setPaymentId(Long paymentId) {
        this.paymentId = paymentId;
    }

    public List<String> getVoucherPicturepath() {
        return voucherPicturepath;
    }

    public void setVoucherPicturepath(List<String> voucherPicturepath) {
        this.voucherPicturepath = voucherPicturepath;
    }

    public BigDecimal getPaymentMoney() {
        return paymentMoney;
    }

    public void setPaymentMoney(BigDecimal paymentMoney) {
        this.paymentMoney = paymentMoney;
    }

    public String getPaymentRemark() {
        return paymentRemark;
    }

    public void setPaymentRemark(String paymentRemark) {
        this.paymentRemark = paymentRemark;
    }
}
