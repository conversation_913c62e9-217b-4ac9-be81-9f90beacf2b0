package org.haier.shop.result.shopSupBill;

import java.math.BigDecimal;

public class ShopSupBillDetailBatchResult {
    private Integer goodsCategory;//商品种类
    private BigDecimal purchaseCost;//采购成本
    private BigDecimal amountPayable;//应结金额
    private BigDecimal settlePref;//结算优惠
    private BigDecimal settledAmount;//已结金额
    private BigDecimal outstandingAmount;//待结金额
    public Integer getGoodsCategory() {
        return goodsCategory;
    }

    public void setGoodsCategory(Integer goodsCategory) {
        this.goodsCategory = goodsCategory;
    }

    public BigDecimal getPurchaseCost() {
        return purchaseCost;
    }

    public void setPurchaseCost(BigDecimal purchaseCost) {
        this.purchaseCost = purchaseCost;
    }

    public BigDecimal getAmountPayable() {
        return amountPayable;
    }

    public void setAmountPayable(BigDecimal amountPayable) {
        this.amountPayable = amountPayable;
    }

    public BigDecimal getSettlePref() {
        return settlePref;
    }

    public void setSettlePref(BigDecimal settlePref) {
        this.settlePref = settlePref;
    }

    public BigDecimal getSettledAmount() {
        return settledAmount;
    }

    public void setSettledAmount(BigDecimal settledAmount) {
        this.settledAmount = settledAmount;
    }

    public BigDecimal getOutstandingAmount() {
        return outstandingAmount;
    }

    public void setOutstandingAmount(BigDecimal outstandingAmount) {
        this.outstandingAmount = outstandingAmount;
    }
}
