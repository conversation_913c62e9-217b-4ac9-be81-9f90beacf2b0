package org.haier.shop.result.shopSupBill;

import java.math.BigDecimal;

public class GoodsDetailResult extends GoodsListResult {
    private Long detailId;
    private String goodsName;
    private BigDecimal tradeGoodsCount;
    private BigDecimal purchasePrice;
    private BigDecimal salePrice;
    private BigDecimal totalPrice;
    private String billUnit;
    private String restockUnit;
    private Integer goodsStatus;
    private Integer numberStatus;
    private BigDecimal retailPriceNow;
    private BigDecimal netPriceNow;
    private BigDecimal memberPriceNow;
    private BigDecimal retailPrice;
    private BigDecimal netPrice;
    private BigDecimal memberPrice;
    private BigDecimal receiptsReceivedCount;

    public Long getDetailId() {
        return detailId;
    }

    public void setDetailId(Long detailId) {
        this.detailId = detailId;
    }

    public String getGoodsName() {
        return goodsName;
    }

    public void setGoodsName(String goodsName) {
        this.goodsName = goodsName;
    }

    public BigDecimal getTradeGoodsCount() {
        return tradeGoodsCount;
    }

    public void setTradeGoodsCount(BigDecimal tradeGoodsCount) {
        this.tradeGoodsCount = tradeGoodsCount;
    }

    public BigDecimal getPurchasePrice() {
        return purchasePrice;
    }

    public void setPurchasePrice(BigDecimal purchasePrice) {
        this.purchasePrice = purchasePrice;
    }

    public BigDecimal getSalePrice() {
        return salePrice;
    }

    public void setSalePrice(BigDecimal salePrice) {
        this.salePrice = salePrice;
    }

    public BigDecimal getTotalPrice() {
        return totalPrice;
    }

    public void setTotalPrice(BigDecimal totalPrice) {
        this.totalPrice = totalPrice;
    }

    public Integer getGoodsStatus() {
        return goodsStatus;
    }

    public void setGoodsStatus(Integer goodsStatus) {
        this.goodsStatus = goodsStatus;
    }

    public Integer getNumberStatus() {
        return numberStatus;
    }

    public void setNumberStatus(Integer numberStatus) {
        this.numberStatus = numberStatus;
    }

    public BigDecimal getRetailPriceNow() {
        return retailPriceNow;
    }

    public void setRetailPriceNow(BigDecimal retailPriceNow) {
        this.retailPriceNow = retailPriceNow;
    }

    public BigDecimal getNetPriceNow() {
        return netPriceNow;
    }

    public void setNetPriceNow(BigDecimal netPriceNow) {
        this.netPriceNow = netPriceNow;
    }

    public BigDecimal getMemberPriceNow() {
        return memberPriceNow;
    }

    public void setMemberPriceNow(BigDecimal memberPriceNow) {
        this.memberPriceNow = memberPriceNow;
    }

    public BigDecimal getRetailPrice() {
        return retailPrice;
    }

    public void setRetailPrice(BigDecimal retailPrice) {
        this.retailPrice = retailPrice;
    }

    public BigDecimal getNetPrice() {
        return netPrice;
    }

    public void setNetPrice(BigDecimal netPrice) {
        this.netPrice = netPrice;
    }

    public BigDecimal getMemberPrice() {
        return memberPrice;
    }

    public void setMemberPrice(BigDecimal memberPrice) {
        this.memberPrice = memberPrice;
    }

    public BigDecimal getReceiptsReceivedCount() {
        return receiptsReceivedCount;
    }

    public void setReceiptsReceivedCount(BigDecimal receiptsReceivedCount) {
        this.receiptsReceivedCount = receiptsReceivedCount;
    }

    public String getBillUnit() {
        return billUnit;
    }

    public void setBillUnit(String billUnit) {
        this.billUnit = billUnit;
    }

    public String getRestockUnit() {
        return restockUnit;
    }

    public void setRestockUnit(String restockUnit) {
        this.restockUnit = restockUnit;
    }
}
