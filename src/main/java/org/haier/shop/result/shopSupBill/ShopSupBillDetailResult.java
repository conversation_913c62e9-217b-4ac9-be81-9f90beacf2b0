package org.haier.shop.result.shopSupBill;

import java.util.List;

public class ShopSupBillDetailResult{
    private Long id;
    private Integer status;
    private String remark;
    /*private BigDecimal totalPrice;*/
    private ShopSupBillDetailOrderResult orderInfo;
    private ShopSupBillDetailBatchResult batchInfo;
    private ShopSupBillDetailPaymentResult paymentInfo;
    private List<GoodsDetailResult> goodsList;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }
/*    public BigDecimal getTotalPrice() {
        return totalPrice;
    }

    public void setTotalPrice(BigDecimal totalPrice) {
        this.totalPrice = totalPrice;
    }*/

    public List<GoodsDetailResult> getGoodsList() {
        return goodsList;
    }

    public void setGoodsList(List<GoodsDetailResult> goodsList) {
        this.goodsList = goodsList;
    }

    public ShopSupBillDetailOrderResult getOrderInfo() {
        return orderInfo;
    }

    public void setOrderInfo(ShopSupBillDetailOrderResult orderInfo) {
        this.orderInfo = orderInfo;
    }

    public ShopSupBillDetailBatchResult getBatchInfo() {
        return batchInfo;
    }

    public void setBatchInfo(ShopSupBillDetailBatchResult batchInfo) {
        this.batchInfo = batchInfo;
    }

    public ShopSupBillDetailPaymentResult getPaymentInfo() {
        return paymentInfo;
    }

    public void setPaymentInfo(ShopSupBillDetailPaymentResult paymentInfo) {
        this.paymentInfo = paymentInfo;
    }
}
