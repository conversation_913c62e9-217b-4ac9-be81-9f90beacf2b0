package org.haier.shop.result.speech;

import java.io.Serializable;

public class QuerySpeechListByParamResult implements Serializable {
    private Long id;
    //店铺编号
    private Long shopUnique;
    //店铺名称
    private String shopName;
    //完成时间
    private String completeTime;
    //申请编号
    private String applyNo;
    //手机识别的文本
    private String speechTextPhone;
    //上传的文件保存路径
    private String speechPath;
    //系统识别的文本
    private String speechTextSys;
    //申请的设备类型
    private String appType;
    //命令类型
    private String cmdType;
    //操作的页面
    private String pageIndex;
    //解析的参数
    private String param;
    //系统识别的指令
    private String cmdSys;
    //系统错误提示
    private String errorMsgSys;
    //识别进度
    private Integer progress;
    //指令明文
    private String cmdRemarks;
    //创建时间
    private String createTime;
    //更新时间
    private String updateTime;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getShopUnique() {
        return shopUnique;
    }

    public void setShopUnique(Long shopUnique) {
        this.shopUnique = shopUnique;
    }

    public String getShopName() {
        return shopName;
    }

    public void setShopName(String shopName) {
        this.shopName = shopName;
    }

    public String getCompleteTime() {
        return completeTime;
    }

    public void setCompleteTime(String completeTime) {
        this.completeTime = completeTime;
    }

    public String getApplyNo() {
        return applyNo;
    }

    public void setApplyNo(String applyNo) {
        this.applyNo = applyNo;
    }

    public String getSpeechTextPhone() {
        return speechTextPhone;
    }

    public void setSpeechTextPhone(String speechTextPhone) {
        this.speechTextPhone = speechTextPhone;
    }

    public String getSpeechPath() {
        return speechPath;
    }

    public void setSpeechPath(String speechPath) {
        this.speechPath = speechPath;
    }

    public String getSpeechTextSys() {
        return speechTextSys;
    }

    public void setSpeechTextSys(String speechTextSys) {
        this.speechTextSys = speechTextSys;
    }

    public String getAppType() {
        return appType;
    }

    public void setAppType(String appType) {
        this.appType = appType;
    }

    public String getCmdType() {
        return cmdType;
    }

    public void setCmdType(String cmdType) {
        this.cmdType = cmdType;
    }

    public String getPageIndex() {
        return pageIndex;
    }

    public void setPageIndex(String pageIndex) {
        this.pageIndex = pageIndex;
    }

    public String getParam() {
        return param;
    }

    public void setParam(String param) {
        this.param = param;
    }

    public String getCmdSys() {
        return cmdSys;
    }

    public void setCmdSys(String cmdSys) {
        this.cmdSys = cmdSys;
    }

    public String getErrorMsgSys() {
        return errorMsgSys;
    }

    public void setErrorMsgSys(String errorMsgSys) {
        this.errorMsgSys = errorMsgSys;
    }

    public Integer getProgress() {
        return progress;
    }

    public void setProgress(Integer progress) {
        this.progress = progress;
    }

    public String getCmdRemarks() {
        return cmdRemarks;
    }

    public void setCmdRemarks(String cmdRemarks) {
        this.cmdRemarks = cmdRemarks;
    }

    public String getCreateTime() {
        return createTime;
    }

    public void setCreateTime(String createTime) {
        this.createTime = createTime;
    }

    public String getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(String updateTime) {
        this.updateTime = updateTime;
    }
}
