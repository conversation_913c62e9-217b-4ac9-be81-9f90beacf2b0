package org.haier.shop.result.restockPlan;

import java.math.BigDecimal;
import java.util.List;

public class RestockPlanSupplierResult{
    private Long supplierId;
    private String supplierName;
    private String supplierPhone;
    private String shopUnique;
    private String restockNo;
    private String supplierAddress;
    private String companyLeagl;
    private BigDecimal goodsAllTotal;//预计总价
    private Integer goodsTypeCount;//商品种类
    private String remark;
    private List<RestockPlanSupGoodsResult> goodsList;

    public Long getSupplierId() {
        return supplierId;
    }

    public void setSupplierId(Long supplierId) {
        this.supplierId = supplierId;
    }

    public String getSupplierName() {
        return supplierName;
    }

    public void setSupplierName(String supplierName) {
        this.supplierName = supplierName;
    }

    public String getSupplierPhone() {
        return supplierPhone;
    }

    public void setSupplierPhone(String supplierPhone) {
        this.supplierPhone = supplierPhone;
    }

    public String getShopUnique() {
        return shopUnique;
    }

    public void setShopUnique(String shopUnique) {
        this.shopUnique = shopUnique;
    }

    public String getRestockNo() {
        return restockNo;
    }

    public void setRestockNo(String restockNo) {
        this.restockNo = restockNo;
    }

    public String getSupplierAddress() {
        return supplierAddress;
    }

    public void setSupplierAddress(String supplierAddress) {
        this.supplierAddress = supplierAddress;
    }

    public String getCompanyLeagl() {
        return companyLeagl;
    }

    public void setCompanyLeagl(String companyLeagl) {
        this.companyLeagl = companyLeagl;
    }

    public BigDecimal getGoodsAllTotal() {
        return goodsAllTotal;
    }

    public void setGoodsAllTotal(BigDecimal goodsAllTotal) {
        this.goodsAllTotal = goodsAllTotal;
    }

    public Integer getGoodsTypeCount() {
        return goodsTypeCount;
    }

    public void setGoodsTypeCount(Integer goodsTypeCount) {
        this.goodsTypeCount = goodsTypeCount;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public List<RestockPlanSupGoodsResult> getGoodsList() {
        return goodsList;
    }

    public void setGoodsList(List<RestockPlanSupGoodsResult> goodsList) {
        this.goodsList = goodsList;
    }
}
