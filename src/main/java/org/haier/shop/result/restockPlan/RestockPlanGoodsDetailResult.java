package org.haier.shop.result.restockPlan;

import java.math.BigDecimal;

public class RestockPlanGoodsDetailResult {
    private String goodsBarcode;
    private String supplierUnique;
    private String supplierName;
    private String goodsName;
    private Integer purchaseType;
    private Integer goodsChengType;
    private BigDecimal goodsCount;
    private BigDecimal outStockCount;
    private String goodsUnit;
    private BigDecimal count3;
    private BigDecimal count7;
    private BigDecimal bestCount;
    private String lastCreateTime;
    private BigDecimal lastGoodsCount;
    private BigDecimal lastGoodsInPrice;

    public String getGoodsBarcode() {
        return goodsBarcode;
    }

    public void setGoodsBarcode(String goodsBarcode) {
        this.goodsBarcode = goodsBarcode;
    }

    public String getSupplierUnique() {
        return supplierUnique;
    }

    public void setSupplierUnique(String supplierUnique) {
        this.supplierUnique = supplierUnique;
    }

    public Integer getPurchaseType() {
        return purchaseType;
    }

    public void setPurchaseType(Integer purchaseType) {
        this.purchaseType = purchaseType;
    }

    public String getSupplierName() {
        return supplierName;
    }

    public void setSupplierName(String supplierName) {
        this.supplierName = supplierName;
    }

    public String getGoodsName() {
        return goodsName;
    }

    public void setGoodsName(String goodsName) {
        this.goodsName = goodsName;
    }

    public Integer getGoodsChengType() {
        return goodsChengType;
    }

    public void setGoodsChengType(Integer goodsChengType) {
        this.goodsChengType = goodsChengType;
    }

    public BigDecimal getGoodsCount() {
        return goodsCount;
    }

    public void setGoodsCount(BigDecimal goodsCount) {
        this.goodsCount = goodsCount;
    }

    public BigDecimal getOutStockCount() {
        return outStockCount;
    }

    public void setOutStockCount(BigDecimal outStockCount) {
        this.outStockCount = outStockCount;
    }

    public String getGoodsUnit() {
        return goodsUnit;
    }

    public void setGoodsUnit(String goodsUnit) {
        this.goodsUnit = goodsUnit;
    }

    public BigDecimal getCount3() {
        return count3;
    }

    public void setCount3(BigDecimal count3) {
        this.count3 = count3;
    }

    public BigDecimal getCount7() {
        return count7;
    }

    public void setCount7(BigDecimal count7) {
        this.count7 = count7;
    }

    public BigDecimal getBestCount() {
        return bestCount;
    }

    public void setBestCount(BigDecimal bestCount) {
        this.bestCount = bestCount;
    }

    public String getLastCreateTime() {
        return lastCreateTime;
    }

    public void setLastCreateTime(String lastCreateTime) {
        this.lastCreateTime = lastCreateTime;
    }

    public BigDecimal getLastGoodsCount() {
        return lastGoodsCount;
    }

    public void setLastGoodsCount(BigDecimal lastGoodsCount) {
        this.lastGoodsCount = lastGoodsCount;
    }

    public BigDecimal getLastGoodsInPrice() {
        return lastGoodsInPrice;
    }

    public void setLastGoodsInPrice(BigDecimal lastGoodsInPrice) {
        this.lastGoodsInPrice = lastGoodsInPrice;
    }
}
