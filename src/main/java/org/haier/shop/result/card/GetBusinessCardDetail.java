package org.haier.shop.result.card;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;

import java.io.Serializable;
import java.util.List;

public class GetBusinessCardDetail implements Serializable {
    public static final long serialVersionUID = 1L;
    //ID
    @JsonSerialize(using = ToStringSerializer.class)
    private Long id;
    //用户ID
    private Long userId;
    //用户名称
    private String userName;
    //原用户的名称
    private String staffName;
    //头像
    private String icon;
    //客户类型:1、店长；2、店员；3、团长
    private Integer userType;
    //邮箱
    private String email;
    //手机号
    private String mobile;
    //职位
    private String position;
    //公司名称
    private String companyName;
    //公司地址
    private String companyAddress;
    //公司介绍
    private String companyIntroduce;
    //个人介绍
    private String personalIntroduce;
    //用户类型：1、企业用户；2、个人用户
    private Integer cardType;
    //店铺名称
    private String shopName;
    //创建时间
    private String createTime;
    //默认用户：1、默认；0、非默认
    private Integer defaultCard;

    //图片列表
    private List<String> personalPicFiles;
    //企业图片列表
    private List<String> companyPicFiles;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getUserId() {
        return userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }

    public String getUserName() {
        return userName;
    }

    public void setUserName(String userName) {
        this.userName = userName;
    }

    public String getStaffName() {
        return staffName;
    }

    public void setStaffName(String staffName) {
        this.staffName = staffName;
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public String getPosition() {
        return position;
    }

    public void setPosition(String position) {
        this.position = position;
    }

    public String getCompanyName() {
        return companyName;
    }

    public void setCompanyName(String companyName) {
        this.companyName = companyName;
    }

    public String getCompanyAddress() {
        return companyAddress;
    }

    public void setCompanyAddress(String companyAddress) {
        this.companyAddress = companyAddress;
    }

    public String getCompanyIntroduce() {
        return companyIntroduce;
    }

    public void setCompanyIntroduce(String companyIntroduce) {
        this.companyIntroduce = companyIntroduce;
    }

    public String getPersonalIntroduce() {
        return personalIntroduce;
    }

    public void setPersonalIntroduce(String personalIntroduce) {
        this.personalIntroduce = personalIntroduce;
    }

    public Integer getCardType() {
        return cardType;
    }

    public void setCardType(Integer cardType) {
        this.cardType = cardType;
    }

    public String getShopName() {
        return shopName;
    }

    public void setShopName(String shopName) {
        this.shopName = shopName;
    }

    public String getCreateTime() {
        return createTime;
    }

    public void setCreateTime(String createTime) {
        this.createTime = createTime;
    }

    public Integer getDefaultCard() {
        return defaultCard;
    }

    public void setDefaultCard(Integer defaultCard) {
        this.defaultCard = defaultCard;
    }

    public String getMobile() {
        return mobile;
    }

    public void setMobile(String mobile) {
        this.mobile = mobile;
    }

    public Integer getUserType() {
        return userType;
    }

    public void setUserType(Integer userType) {
        this.userType = userType;
    }

    public List<String> getPersonalPicFiles() {
        return personalPicFiles;
    }

    public void setPersonalPicFiles(List<String> personalPicFiles) {
        this.personalPicFiles = personalPicFiles;
    }

    public List<String> getCompanyPicFiles() {
        return companyPicFiles;
    }

    public void setCompanyPicFiles(List<String> companyPicFiles) {
        this.companyPicFiles = companyPicFiles;
    }

    public String getIcon() {
        return icon;
    }

    public void setIcon(String icon) {
        this.icon = icon;
    }
}
