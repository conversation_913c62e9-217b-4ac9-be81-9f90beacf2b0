package org.haier.shop.result.cbd;

import com.fasterxml.jackson.annotation.JsonFormat;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * @ClassName CbdShopCouponDtoResult
 * <AUTHOR>
 * @Date 2024/2/22 9:52
 */
public class CbdShopCouponDtoResult implements Serializable {
    /**
     * 消费券名称
     */
    private String couponName;
    /**
     * 券类型
     */
    private String typeDesc;
    /**
     * 面值
     */
    private BigDecimal parValue;
    /**
     * 券码
     */
    private String couponCode;
    /**
     * 核销金额
     */
    private BigDecimal couponActualityMoney;
    /**
     * 核销时间
     */
    @JsonFormat(pattern = "yyyy年MM月dd日HH:mm:ss", timezone = "GMT+8")
    private Date useTime;
    /**
     * 结算状态
     */
    private String settlementStatusDesc;
    /**
     * 结算时间
     */
    @JsonFormat(pattern = "yyyy年MM月dd日HH:mm:ss", timezone = "GMT+8")
    private Date settlementTime;
    /**
     * 到账户名
     */
    private String legalPerson;
    /**
     * 到账账户
     */
    private String bankNo;

    public String getLegalPerson() {
        return legalPerson;
    }

    public void setLegalPerson(String legalPerson) {
        this.legalPerson = legalPerson;
    }

    public String getBankNo() {
        return bankNo;
    }

    public void setBankNo(String bankNo) {
        this.bankNo = bankNo;
    }

    public String getCouponName() {
        return couponName;
    }

    public void setCouponName(String couponName) {
        this.couponName = couponName;
    }

    public String getTypeDesc() {
        return typeDesc;
    }

    public void setTypeDesc(String typeDesc) {
        this.typeDesc = typeDesc;
    }

    public BigDecimal getParValue() {
        return parValue;
    }

    public void setParValue(BigDecimal parValue) {
        this.parValue = parValue;
    }

    public String getCouponCode() {
        return couponCode;
    }

    public void setCouponCode(String couponCode) {
        this.couponCode = couponCode;
    }

    public BigDecimal getCouponActualityMoney() {
        return couponActualityMoney;
    }

    public void setCouponActualityMoney(BigDecimal couponActualityMoney) {
        this.couponActualityMoney = couponActualityMoney;
    }

    public Date getUseTime() {
        return useTime;
    }

    public void setUseTime(Date useTime) {
        this.useTime = useTime;
    }

    public String getSettlementStatusDesc() {
        return settlementStatusDesc;
    }

    public void setSettlementStatusDesc(String settlementStatusDesc) {
        this.settlementStatusDesc = settlementStatusDesc;
    }

    public Date getSettlementTime() {
        return settlementTime;
    }

    public void setSettlementTime(Date settlementTime) {
        this.settlementTime = settlementTime;
    }
}
