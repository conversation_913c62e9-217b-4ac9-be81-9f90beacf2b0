package org.haier.shop.result.cbd;

import com.fasterxml.jackson.annotation.JsonFormat;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * @ClassName CbdShopCouponCountResult
 * <AUTHOR>
 * @Date 2024/2/22 9:52
 */
public class CbdShopCouponCountResult implements Serializable {
    /**
     * 已核销数量
     */
    private Long useCount;
    /**
     * 已核销金额
     */
    private BigDecimal useAmount;
    /**
     * 已结算金额
     */
    private  Long settlementCount;
    /**
     * 已结算数量
     */
    private BigDecimal settlementAmount;
    /**
     * 未结算数量
     */
    private Long unSettlementCount;
    /**
     * 未结算金额
     */
    private BigDecimal unSettlementAmount;

    public Long getUseCount() {
        return useCount;
    }

    public void setUseCount(Long useCount) {
        this.useCount = useCount;
    }

    public BigDecimal getUseAmount() {
        return useAmount;
    }

    public void setUseAmount(BigDecimal useAmount) {
        this.useAmount = useAmount;
    }

    public Long getSettlementCount() {
        return settlementCount;
    }

    public void setSettlementCount(Long settlementCount) {
        this.settlementCount = settlementCount;
    }

    public BigDecimal getSettlementAmount() {
        return settlementAmount;
    }

    public void setSettlementAmount(BigDecimal settlementAmount) {
        this.settlementAmount = settlementAmount;
    }

    public Long getUnSettlementCount() {
        return unSettlementCount;
    }

    public void setUnSettlementCount(Long unSettlementCount) {
        this.unSettlementCount = unSettlementCount;
    }

    public BigDecimal getUnSettlementAmount() {
        return unSettlementAmount;
    }

    public void setUnSettlementAmount(BigDecimal unSettlementAmount) {
        this.unSettlementAmount = unSettlementAmount;
    }

    public CbdShopCouponCountResult(Long useCount, BigDecimal useAmount, Long settlementCount, BigDecimal settlementAmount, Long unSettlementCount, BigDecimal unSettlementAmount) {
        this.useCount = useCount;
        this.useAmount = useAmount;
        this.settlementCount = settlementCount;
        this.settlementAmount = settlementAmount;
        this.unSettlementCount = unSettlementCount;
        this.unSettlementAmount = unSettlementAmount;
    }
}
