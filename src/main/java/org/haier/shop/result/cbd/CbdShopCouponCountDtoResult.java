package org.haier.shop.result.cbd;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * @ClassName CbdShopCouponCountResult
 * <AUTHOR>
 * @Date 2024/2/22 9:52
 */
public class CbdShopCouponCountDtoResult implements Serializable {
    /**
     * 消费券ID
     */
    private Long shopCouponId;
    /**
     * 核销金额
     */
    private BigDecimal couponActualityMoney;
    /**
     * 结算状态
     */
    private  Integer settlementStatus;

    public Long getShopCouponId() {
        return shopCouponId;
    }

    public void setShopCouponId(Long shopCouponId) {
        this.shopCouponId = shopCouponId;
    }

    public BigDecimal getCouponActualityMoney() {
        return couponActualityMoney;
    }

    public void setCouponActualityMoney(BigDecimal couponActualityMoney) {
        this.couponActualityMoney = couponActualityMoney;
    }

    public Integer getSettlementStatus() {
        return settlementStatus;
    }

    public void setSettlementStatus(Integer settlementStatus) {
        this.settlementStatus = settlementStatus;
    }
}
