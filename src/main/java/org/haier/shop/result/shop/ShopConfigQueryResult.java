package org.haier.shop.result.shop;

import java.io.Serializable;

/**
 * @ClassName ShopConfigQueryResult
 * <AUTHOR>
 * @Date 2024/4/25 16:34
 */

public class ShopConfigQueryResult implements Serializable {
    private Long id;
    private Long shopUnique;
    private Integer goodsInPriceType;
    private Integer isIoboundInspect;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getShopUnique() {
        return shopUnique;
    }

    public void setShopUnique(Long shopUnique) {
        this.shopUnique = shopUnique;
    }

    public Integer getGoodsInPriceType() {
        return goodsInPriceType;
    }

    public void setGoodsInPriceType(Integer goodsInPriceType) {
        this.goodsInPriceType = goodsInPriceType;
    }

    public Integer getIsIoboundInspect() {
        return isIoboundInspect;
    }

    public void setIsIoboundInspect(Integer isIoboundInspect) {
        this.isIoboundInspect = isIoboundInspect;
    }
}
