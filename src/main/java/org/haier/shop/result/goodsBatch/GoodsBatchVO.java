package org.haier.shop.result.goodsBatch;

import com.fasterxml.jackson.annotation.JsonFormat;

import java.math.BigDecimal;
import java.util.Date;

/**
 * @Description 批次返回值
 * @ClassName GoodsBatchVO
 * <AUTHOR>
 * @Date 2024/4/30 9:43
 **/
public class GoodsBatchVO {

    private Long goodsBatchId;

    /**
     * 店铺编码
     */
    private Long shopUnique;

    /**
     * 批次号
     */
    private String batchUnique;

    /**
     * 入库单号
     */
    private String stockListUnique;

    /**
     * 商品大类
     */
    private String goodsKindNameFirst;

    /**
     * 商品小类
     */
    private String goodsKindNameSecond;

    /**
     * 商品名称
     */
    private String goodsName;

    /**
     * 商品条码
     */
    private String goodsBarcode;

    /**
     * 商品进价
     */
    private BigDecimal goodsInPrice;

    /**
     * 商品剩余数量
     */
    private BigDecimal goodsCount;

    /**
     * 商品入库数量
     */
    private BigDecimal goodsInCount;

    /**
     * 商品生产日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date goodsProd;

    /**
     * 商品过期日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date goodsExp;

    /**
     * 商品保质天数
     */
    private Integer goodsLife;

    /**
     * 临期状态:0-正常，1-临期，2-过期
     */
    private Integer expStatus;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;

    public Long getGoodsBatchId() {
        return goodsBatchId;
    }

    public void setGoodsBatchId(Long goodsBatchId) {
        this.goodsBatchId = goodsBatchId;
    }

    public Long getShopUnique() {
        return shopUnique;
    }

    public void setShopUnique(Long shopUnique) {
        this.shopUnique = shopUnique;
    }

    public String getBatchUnique() {
        return batchUnique;
    }

    public void setBatchUnique(String batchUnique) {
        this.batchUnique = batchUnique;
    }

    public String getStockListUnique() {
        return stockListUnique;
    }

    public void setStockListUnique(String stockListUnique) {
        this.stockListUnique = stockListUnique;
    }

    public String getGoodsKindNameFirst() {
        return goodsKindNameFirst;
    }

    public void setGoodsKindNameFirst(String goodsKindNameFirst) {
        this.goodsKindNameFirst = goodsKindNameFirst;
    }

    public String getGoodsKindNameSecond() {
        return goodsKindNameSecond;
    }

    public void setGoodsKindNameSecond(String goodsKindNameSecond) {
        this.goodsKindNameSecond = goodsKindNameSecond;
    }

    public String getGoodsName() {
        return goodsName;
    }

    public void setGoodsName(String goodsName) {
        this.goodsName = goodsName;
    }

    public String getGoodsBarcode() {
        return goodsBarcode;
    }

    public void setGoodsBarcode(String goodsBarcode) {
        this.goodsBarcode = goodsBarcode;
    }

    public BigDecimal getGoodsInPrice() {
        return goodsInPrice;
    }

    public void setGoodsInPrice(BigDecimal goodsInPrice) {
        this.goodsInPrice = goodsInPrice;
    }

    public BigDecimal getGoodsCount() {
        return goodsCount;
    }

    public void setGoodsCount(BigDecimal goodsCount) {
        this.goodsCount = goodsCount;
    }

    public BigDecimal getGoodsInCount() {
        return goodsInCount;
    }

    public void setGoodsInCount(BigDecimal goodsInCount) {
        this.goodsInCount = goodsInCount;
    }

    public Date getGoodsProd() {
        return goodsProd;
    }

    public void setGoodsProd(Date goodsProd) {
        this.goodsProd = goodsProd;
    }

    public Date getGoodsExp() {
        return goodsExp;
    }

    public void setGoodsExp(Date goodsExp) {
        this.goodsExp = goodsExp;
    }

    public Integer getGoodsLife() {
        return goodsLife;
    }

    public void setGoodsLife(Integer goodsLife) {
        this.goodsLife = goodsLife;
    }

    public Integer getExpStatus() {
        return expStatus;
    }

    public void setExpStatus(Integer expStatus) {
        this.expStatus = expStatus;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }
}
