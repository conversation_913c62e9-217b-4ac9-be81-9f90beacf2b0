package org.haier.shop.result.goodsBatch;

import org.haier.shop.entity.GoodsBatch;
import java.math.BigDecimal;

/**
 * 商品入库批次表
 * <AUTHOR> 
 * @ClassName GoodsBatch
 * @Date 2024-04-28
 **/

public class GoodsBatchChooseVO extends GoodsBatch {

	/**
	 * 选择出库数量
	 */
	private BigDecimal outCount;

	private BigDecimal goodsContain;

	public BigDecimal getGoodsContain() {
		return goodsContain;
	}

	public void setGoodsContain(BigDecimal goodsContain) {
		this.goodsContain = goodsContain;
	}

	public BigDecimal getOutCount() {
		return outCount;
	}

	public void setOutCount(BigDecimal outCount) {
		this.outCount = outCount;
	}
}