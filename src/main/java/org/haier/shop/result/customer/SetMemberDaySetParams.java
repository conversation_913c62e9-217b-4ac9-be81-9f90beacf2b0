package org.haier.shop.result.customer;

import java.io.Serializable;
import java.math.BigDecimal;

public class SetMemberDaySetParams implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 所属店铺编号
     */
    private String shopUnique;
    /**
     * 是否开启会员日功能：1、开启；0、关闭
     */
    private Integer memberDay;
    /**
     * 会员日类型：1、每周；2、每月；3、每年
     */
    private Integer memberDayType;

    /**
     * 会员日值：1、0-6；2、0-30；3：03-25（具体日期）
     */
    private String memberDayValue;

    /**
     * 会员日积分倍数，1：代表原数值
     */
    private BigDecimal memberPointMultiple;


    public String getShopUnique() {
        return shopUnique;
    }

    public void setShopUnique(String shopUnique) {
        this.shopUnique = shopUnique;
    }

    public Integer getMemberDay() {
        return memberDay;
    }

    public void setMemberDay(Integer memberDay) {
        this.memberDay = memberDay;
    }

    public Integer getMemberDayType() {
        return memberDayType;
    }

    public void setMemberDayType(Integer memberDayType) {
        this.memberDayType = memberDayType;
    }

    public String getMemberDayValue() {
        return memberDayValue;
    }

    public void setMemberDayValue(String memberDayValue) {
        this.memberDayValue = memberDayValue;
    }

    public BigDecimal getMemberPointMultiple() {
        return memberPointMultiple;
    }

    public void setMemberPointMultiple(BigDecimal memberPointMultiple) {
        this.memberPointMultiple = memberPointMultiple;
    }
}
