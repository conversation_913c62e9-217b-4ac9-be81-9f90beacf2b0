package org.haier.shop.result.customer;

import java.io.Serializable;
import java.math.BigDecimal;

public class QueryCusRechargeStaticResult implements Serializable {
    //充值金额
    private BigDecimal rechargeMoney;
    //赠送金额
    private BigDecimal giveMoney;
    //充值人数
    private Integer count;

    public BigDecimal getRechargeMoney() {
        return rechargeMoney;
    }

    public void setRechargeMoney(BigDecimal rechargeMoney) {
        this.rechargeMoney = rechargeMoney;
    }

    public BigDecimal getGiveMoney() {
        return giveMoney;
    }

    public void setGiveMoney(BigDecimal giveMoney) {
        this.giveMoney = giveMoney;
    }

    public Integer getCount() {
        return count;
    }

    public void setCount(Integer count) {
        this.count = count;
    }
}
