package org.haier.shop.result.customer;

import java.io.Serializable;
import java.math.BigDecimal;

public class QueryNYCusDetailResult implements Serializable {
    //会员ID
    private Integer cus_id;
    //会员编号
    private String cus_unique;
    //会员昵称
    private String cus_name;
    //会员微信号
    private String cus_weixin;
    //当前会员积分余额
    private BigDecimal cus_points;
    //当前会员余额
    private BigDecimal cus_balance;
    //会员赠送余额
    private BigDecimal cus_rebate;
    //会员类型
    private String cus_type;

    public Integer getCus_id() {
        return cus_id;
    }

    public void setCus_id(Integer cus_id) {
        this.cus_id = cus_id;
    }

    public String getCus_unique() {
        return cus_unique;
    }

    public void setCus_unique(String cus_unique) {
        this.cus_unique = cus_unique;
    }

    public String getCus_name() {
        return cus_name;
    }

    public void setCus_name(String cus_name) {
        this.cus_name = cus_name;
    }

    public String getCus_weixin() {
        return cus_weixin;
    }

    public void setCus_weixin(String cus_weixin) {
        this.cus_weixin = cus_weixin;
    }

    public BigDecimal getCus_points() {
        return cus_points;
    }

    public void setCus_points(BigDecimal cus_points) {
        this.cus_points = cus_points;
    }

    public BigDecimal getCus_balance() {
        return cus_balance;
    }

    public void setCus_balance(BigDecimal cus_balance) {
        this.cus_balance = cus_balance;
    }

    public String getCus_type() {
        return cus_type;
    }

    public void setCus_type(String cus_type) {
        this.cus_type = cus_type;
    }

    public BigDecimal getCus_rebate() {
        return cus_rebate;
    }

    public void setCus_rebate(BigDecimal cus_rebate) {
        this.cus_rebate = cus_rebate;
    }
}
