package org.haier.shop.result.dingtalk;


import java.io.Serializable;

public class OApiRobotSendResponseResult implements Serializable {
    /**
     * 错误码
     * 错误码（errorcode） 错误码描述（errmsg） 解决方案
     * 400013 群已被解散 请向其他群发消息
     * 400101 access_token不存在 请确认access_token拼写是否正确
     * 400102 机器人已停用 请联系管理员启用机器人
     * 400105 不支持的消息类型 请使用文档中支持的消息类型
     * 400106 机器人不存在 请确认机器人是否在群中
     * 410100 发送速度太快而限流 请降低发送速度
     * 430101 含有不安全的外链 请确认发送的内容合法
     * 430102 含有不合适的文本 请确认发送的内容合法
     * 430103 含有不合适的图片 请确认发送的内容合法
     * 430104 含有不合适的内容 请确认发送的内容合法
     * 310000 keywords not in content 消息内容中不包含任何关键词
     * 310000 invalid timestamp timestamp 无效
     * 310000 sign not match 签名不匹配
     * 310000 ip X.X.X.X not in whitelist IP地址不在白名单
     */
    private Long errcode;
    /**
     * 错误码描述
     */
    private String errmsg;

    public Long getErrcode() {
        return errcode;
    }

    public void setErrcode(Long errcode) {
        this.errcode = errcode;
    }

    public String getErrmsg() {
        return errmsg;
    }

    public void setErrmsg(String errmsg) {
        this.errmsg = errmsg;
    }
}
