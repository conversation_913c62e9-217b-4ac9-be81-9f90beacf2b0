package org.haier.shop.result.shopSupplier;

import java.math.BigDecimal;

public class ShopSupplierRecordGoodsResult {
    /**
     * 商品ID
     */
    private Long id;
    /**
     * 商品条码
     */
    private String goodsBarcode;
    /**
     *商品名称
     */
    private String goodsName;
    /**
     *商品图片路径
     */
    private String goodsImageUrl;
    /**
     *商品售价-未建档
     */
    private BigDecimal goodsSalePriceUndoc;
    /**
     *商品进价-未建档
     */
    private BigDecimal goodsInPriceUndoc;
    /**
     *称重商品类型：0、按件；1、按重量
     */
    private Integer goodsChengType;
    /**
     *商品别名
     */
    private String goodsAlias;
    /**
     *保存天数
     */
    private Integer expirationDate;
    /**
     *商品生产地
     */
    private String goodsAddress;
    /**
     *商品规格
     */
    private String goodsStandard;
    /**
     *商品单位
     */
    private String goodsUnit;
    /**
     *生产日期
     */
    private String goodsProduceDate;
    /**
     *库存
     */
    private BigDecimal goodsCount;
    /**
     *商品售价
     */
    private BigDecimal goodsSalePrice;
    /**
     *商品进价
     */
    private BigDecimal goodsInPrice;
    /**
     *月销量
     */
    private BigDecimal monthlySales;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getGoodsBarcode() {
        return goodsBarcode;
    }

    public void setGoodsBarcode(String goodsBarcode) {
        this.goodsBarcode = goodsBarcode;
    }

    public String getGoodsName() {
        return goodsName;
    }

    public void setGoodsName(String goodsName) {
        this.goodsName = goodsName;
    }

    public String getGoodsImageUrl() {
        return goodsImageUrl;
    }

    public void setGoodsImageUrl(String goodsImageUrl) {
        this.goodsImageUrl = goodsImageUrl;
    }

    public BigDecimal getGoodsSalePriceUndoc() {
        return goodsSalePriceUndoc;
    }

    public void setGoodsSalePriceUndoc(BigDecimal goodsSalePriceUndoc) {
        this.goodsSalePriceUndoc = goodsSalePriceUndoc;
    }

    public BigDecimal getGoodsInPriceUndoc() {
        return goodsInPriceUndoc;
    }

    public void setGoodsInPriceUndoc(BigDecimal goodsInPriceUndoc) {
        this.goodsInPriceUndoc = goodsInPriceUndoc;
    }

    public Integer getGoodsChengType() {
        return goodsChengType;
    }

    public void setGoodsChengType(Integer goodsChengType) {
        this.goodsChengType = goodsChengType;
    }

    public String getGoodsAlias() {
        return goodsAlias;
    }

    public void setGoodsAlias(String goodsAlias) {
        this.goodsAlias = goodsAlias;
    }

    public Integer getExpirationDate() {
        return expirationDate;
    }

    public void setExpirationDate(Integer expirationDate) {
        this.expirationDate = expirationDate;
    }

    public String getGoodsAddress() {
        return goodsAddress;
    }

    public void setGoodsAddress(String goodsAddress) {
        this.goodsAddress = goodsAddress;
    }

    public String getGoodsStandard() {
        return goodsStandard;
    }

    public void setGoodsStandard(String goodsStandard) {
        this.goodsStandard = goodsStandard;
    }

    public String getGoodsUnit() {
        return goodsUnit;
    }

    public void setGoodsUnit(String goodsUnit) {
        this.goodsUnit = goodsUnit;
    }

    public String getGoodsProduceDate() {
        return goodsProduceDate;
    }

    public void setGoodsProduceDate(String goodsProduceDate) {
        this.goodsProduceDate = goodsProduceDate;
    }

    public BigDecimal getGoodsCount() {
        return goodsCount;
    }

    public void setGoodsCount(BigDecimal goodsCount) {
        this.goodsCount = goodsCount;
    }

    public BigDecimal getGoodsSalePrice() {
        return goodsSalePrice;
    }

    public void setGoodsSalePrice(BigDecimal goodsSalePrice) {
        this.goodsSalePrice = goodsSalePrice;
    }

    public BigDecimal getGoodsInPrice() {
        return goodsInPrice;
    }

    public void setGoodsInPrice(BigDecimal goodsInPrice) {
        this.goodsInPrice = goodsInPrice;
    }

    public BigDecimal getMonthlySales() {
        return monthlySales;
    }

    public void setMonthlySales(BigDecimal monthlySales) {
        this.monthlySales = monthlySales;
    }
}
