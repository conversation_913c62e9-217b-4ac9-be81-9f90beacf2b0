package org.haier.shop.result.shopSupplier;

import java.math.BigDecimal;

public class ShopSupplierListResult {
    /**
     * 供货商ID
     */
    private Long id;
    /**
     * 供货商编号
     */
    private String supplierUnique;
    /**
     * 供货商名称
     */
    private String supplierName;
    /**
     * 联系人
     */
    private String contacts;
    /**
     * 联系方式
     */
    private String contactMobile;
    /**
     * 是否绑定
     */
    private Integer bindFlag;
    /**
     * 是否停用
     */
    private Integer enableStatus;
    /**
     * 采购类型
     */
    private Integer purchaseType;
    /**
     * 分类
     */
    private String supplierKindUnique;
    /**
     * 分类名称
     */
    private String supplierKindName;
    /**
     * 地址
     */
    private String address;
    /**
     * 订单数量
     */
    private BigDecimal orderCount;
    /**
     * 欠款金额
     */
    private BigDecimal debts;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getSupplierUnique() {
        return supplierUnique;
    }

    public void setSupplierUnique(String supplierUnique) {
        this.supplierUnique = supplierUnique;
    }

    public String getSupplierName() {
        return supplierName;
    }

    public void setSupplierName(String supplierName) {
        this.supplierName = supplierName;
    }

    public String getContacts() {
        return contacts;
    }

    public void setContacts(String contacts) {
        this.contacts = contacts;
    }

    public String getContactMobile() {
        return contactMobile;
    }

    public void setContactMobile(String contactMobile) {
        this.contactMobile = contactMobile;
    }

    public Integer getPurchaseType() {
        return purchaseType;
    }

    public void setPurchaseType(Integer purchaseType) {
        this.purchaseType = purchaseType;
    }

    public String getSupplierKindUnique() {
        return supplierKindUnique;
    }

    public void setSupplierKindUnique(String supplierKindUnique) {
        this.supplierKindUnique = supplierKindUnique;
    }

    public String getSupplierKindName() {
        return supplierKindName;
    }

    public void setSupplierKindName(String supplierKindName) {
        this.supplierKindName = supplierKindName;
    }

    public String getAddress() {
        return address;
    }

    public void setAddress(String address) {
        this.address = address;
    }

    public Integer getBindFlag() {
        return bindFlag;
    }

    public void setBindFlag(Integer bindFlag) {
        this.bindFlag = bindFlag;
    }

    public Integer getEnableStatus() {
        return enableStatus;
    }

    public void setEnableStatus(Integer enableStatus) {
        this.enableStatus = enableStatus;
    }

    public BigDecimal getOrderCount() {
        return orderCount;
    }

    public void setOrderCount(BigDecimal orderCount) {
        this.orderCount = orderCount;
    }

    public BigDecimal getDebts() {
        return debts;
    }

    public void setDebts(BigDecimal debts) {
        this.debts = debts;
    }
}
