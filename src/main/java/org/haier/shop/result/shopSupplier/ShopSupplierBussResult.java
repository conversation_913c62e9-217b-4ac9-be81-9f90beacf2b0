package org.haier.shop.result.shopSupplier;

import java.math.BigDecimal;

public class ShopSupplierBussResult {
    /**
     * 供货商编号
     */
    private String supplierUnique;
    /**
     * 供货商名称
     */
    private String supplierName;
    /**
     * 所供商品种类
     */
    private Integer goodsTypeCount;
    /**
     *采购总额
     */
    private BigDecimal purchaseAmount;
    /**
     *已结金额
     */
    private BigDecimal settledAmount;
    /**
     *待结金额
     */
    private BigDecimal outstandingAmount;
    /**
     *购销单数量
     */
    private Long billCount;

    public String getSupplierUnique() {
        return supplierUnique;
    }

    public void setSupplierUnique(String supplierUnique) {
        this.supplierUnique = supplierUnique;
    }

    public String getSupplierName() {
        return supplierName;
    }

    public void setSupplierName(String supplierName) {
        this.supplierName = supplierName;
    }

    public Integer getGoodsTypeCount() {
        return goodsTypeCount;
    }

    public void setGoodsTypeCount(Integer goodsTypeCount) {
        this.goodsTypeCount = goodsTypeCount;
    }

    public BigDecimal getPurchaseAmount() {
        return purchaseAmount;
    }

    public void setPurchaseAmount(BigDecimal purchaseAmount) {
        this.purchaseAmount = purchaseAmount;
    }

    public BigDecimal getSettledAmount() {
        return settledAmount;
    }

    public void setSettledAmount(BigDecimal settledAmount) {
        this.settledAmount = settledAmount;
    }

    public BigDecimal getOutstandingAmount() {
        return outstandingAmount;
    }

    public void setOutstandingAmount(BigDecimal outstandingAmount) {
        this.outstandingAmount = outstandingAmount;
    }

    public Long getBillCount() {
        return billCount;
    }

    public void setBillCount(Long billCount) {
        this.billCount = billCount;
    }
}
