package org.haier.shop.result.shopSupplier;

import java.math.BigDecimal;

public class ShopSupplierPayResult {
    /**
     * 付款记录ID
     */
    private Long paymentId;
    /**
     * 付款人ID
     */
    private Long createId;
    /**
     * 付款人姓名
     */
    private String createBy;
    /**
     * 付款时间
     */
    private String createTime;
    /**
     * 状态
     */
    private Integer status;
    /**
     * 付款金额
     */
    private BigDecimal paymentMoney;
    /**
     * 备注
     */
    private String remark;

    public Long getPaymentId() {
        return paymentId;
    }

    public void setPaymentId(Long paymentId) {
        this.paymentId = paymentId;
    }

    public Long getCreateId() {
        return createId;
    }

    public void setCreateId(Long createId) {
        this.createId = createId;
    }

    public String getCreateBy() {
        return createBy;
    }

    public void setCreateBy(String createBy) {
        this.createBy = createBy;
    }

    public String getCreateTime() {
        return createTime;
    }

    public void setCreateTime(String createTime) {
        this.createTime = createTime;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public BigDecimal getPaymentMoney() {
        return paymentMoney;
    }

    public void setPaymentMoney(BigDecimal paymentMoney) {
        this.paymentMoney = paymentMoney;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }
}
