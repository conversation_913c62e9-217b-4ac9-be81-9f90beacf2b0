package org.haier.shop.result.shopSupplier;

import java.math.BigDecimal;
import java.util.List;

public class ShopSupplierPayDetailResult {
    /**
     * 还款人ID
     */
    private Long createId;
    /**
     * 还款人姓名
     */
    private String createBy;
    /**
     * 还款时间
     */
    private String createTime;
    /**
     *还款金额
     */
    private BigDecimal paymentMoney;
    /**
     *还款备注
     */
    private String remark;
    /**
     *还款凭证
     */
    private List<String> voucherPicturePath;
    /**
     *还款订单
     */
    private List<ShopSupplierBillResult> billList;

    public Long getCreateId() {
        return createId;
    }

    public void setCreateId(Long createId) {
        this.createId = createId;
    }

    public String getCreateBy() {
        return createBy;
    }

    public void setCreateBy(String createBy) {
        this.createBy = createBy;
    }

    public String getCreateTime() {
        return createTime;
    }

    public void setCreateTime(String createTime) {
        this.createTime = createTime;
    }

    public BigDecimal getPaymentMoney() {
        return paymentMoney;
    }

    public void setPaymentMoney(BigDecimal paymentMoney) {
        this.paymentMoney = paymentMoney;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public List<String> getVoucherPicturePath() {
        return voucherPicturePath;
    }

    public void setVoucherPicturePath(List<String> voucherPicturePath) {
        this.voucherPicturePath = voucherPicturePath;
    }

    public List<ShopSupplierBillResult> getBillList() {
        return billList;
    }

    public void setBillList(List<ShopSupplierBillResult> billList) {
        this.billList = billList;
    }
}
