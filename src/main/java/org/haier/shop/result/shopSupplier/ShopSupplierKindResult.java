package org.haier.shop.result.shopSupplier;

import java.util.List;

public class ShopSupplierKindResult {
    private String supplierKindUnique;
    private String supplierKindName;
    private List<ShopSupplierKindSubListResult> subKindList;

    public String getSupplierKindUnique() {
        return supplierKindUnique;
    }

    public void setSupplierKindUnique(String supplierKindUnique) {
        this.supplierKindUnique = supplierKindUnique;
    }

    public String getSupplierKindName() {
        return supplierKindName;
    }

    public void setSupplierKindName(String supplierKindName) {
        this.supplierKindName = supplierKindName;
    }

    public List<ShopSupplierKindSubListResult> getSubKindList() {
        return subKindList;
    }

    public void setSubKindList(List<ShopSupplierKindSubListResult> subKindList) {
        this.subKindList = subKindList;
    }
}
