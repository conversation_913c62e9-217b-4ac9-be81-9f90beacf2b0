package org.haier.shop.result.common;

import java.io.Serializable;
import java.util.List;

/**
 * 分页查询的公共返回形式
 */
public class PageSearchResult<T> implements Serializable {
    public static final long serialVersionUID = 1L;

    private Integer totalCount;
    private Integer pageNum;
    private Integer pageSize;
    private List<T> rows;

    public Integer getTotalCount() {
        return totalCount;
    }

    public void setTotalCount(Integer totalCount) {
        this.totalCount = totalCount;
    }

    public Integer getPageNum() {
        return pageNum;
    }

    public void setPageNum(Integer pageNum) {
        this.pageNum = pageNum;
    }

    public Integer getPageSize() {
        return pageSize;
    }

    public void setPageSize(Integer pageSize) {
        this.pageSize = pageSize;
    }

    public List<T> getRows() {
        return rows;
    }

    public void setRows(List<T> rows) {
        this.rows = rows;
    }
}
