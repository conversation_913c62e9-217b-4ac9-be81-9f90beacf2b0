package org.haier.shop.result.stock;

import com.fasterxml.jackson.annotation.JsonFormat;
import org.haier.shop.entity.ShopStockDetail;

import java.math.BigDecimal;
import java.util.Date;

/**
 * @Description 库存管理返回值
 * @ClassName ShopStockDetailVO
 * <AUTHOR>
 * @Date 2024/5/1 8:24
 **/
public class ShopStockDetailVO extends ShopStockDetail {

    /**
     * 操作员工
     */
    private String staffName;

    /**
     * 出入库时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date stockTime;

    /**
     * 审批人
     */
    private String auditName;

    /**
     * 修改人
     */
    private String updateName;

    /**
     * 修改时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date updateTime;

    private String stockTimeStr;

    /**
     * 出入库方式
     */
    private Integer goodsInPriceType;

    public String getStaffName() {
        return staffName;
    }

    public void setStaffName(String staffName) {
        this.staffName = staffName;
    }

    @Override
    public Date getStockTime() {
        return stockTime;
    }

    @Override
    public void setStockTime(Date stockTime) {
        this.stockTime = stockTime;
    }

    public String getAuditName() {
        return auditName;
    }

    public void setAuditName(String auditName) {
        this.auditName = auditName;
    }

    public String getUpdateName() {
        return updateName;
    }

    public void setUpdateName(String updateName) {
        this.updateName = updateName;
    }

    @Override
    public Date getUpdateTime() {
        return updateTime;
    }

    @Override
    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public String getStockTimeStr() {
        return stockTimeStr;
    }

    public void setStockTimeStr(String stockTimeStr) {
        this.stockTimeStr = stockTimeStr;
    }

    public Integer getGoodsInPriceType() {
        return goodsInPriceType;
    }

    public void setGoodsInPriceType(Integer goodsInPriceType) {
        this.goodsInPriceType = goodsInPriceType;
    }
}
