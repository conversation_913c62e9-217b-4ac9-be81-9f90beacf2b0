package org.haier.shop.entity;

import org.haier.shop.entity.beans.BaseModel;


@SuppressWarnings("serial")
public class SysRolePermission extends BaseModel{
	
	
	private Integer id;
	private String role_code;//角色编码
	private String permission_code;//权限编码
	private String shop_unique;
	public Integer getId() {
		return id;
	}
	public void setId(Integer id) {
		this.id = id;
	}
	public String getRole_code() {
		return role_code;
	}
	public void setRole_code(String role_code) {
		this.role_code = role_code;
	}
	public String getPermission_code() {
		return permission_code;
	}
	public void setPermission_code(String permission_code) {
		this.permission_code = permission_code;
	}
	public String getShop_unique() {
		return shop_unique;
	}
	public void setShop_unique(String shop_unique) {
		this.shop_unique = shop_unique;
	}
}
