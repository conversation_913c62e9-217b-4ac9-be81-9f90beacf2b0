package org.haier.shop.entity.gold;

import java.io.Serializable;

public class GoldGrantDetail implements Serializable{
	
	/**
	 * 
	 */
	private static final long serialVersionUID = 1L;
	private Integer id;//
	private Integer grant_id;//金圈币发放编号
	private String shop_unique;//店铺编号
	private Integer receive_status;//0：已过期；1：新发放未领取；2：已领取
	private String receive_code;//发放状态明文
	private String receipt_time;//领取时间
	public Integer getId() {
		return id;
	}
	public void setId(Integer id) {
		this.id = id;
	}
	public Integer getGrant_id() {
		return grant_id;
	}
	public void setGrant_id(Integer grant_id) {
		this.grant_id = grant_id;
	}
	public String getShop_unique() {
		return shop_unique;
	}
	public void setShop_unique(String shop_unique) {
		this.shop_unique = shop_unique;
	}
	public Integer getReceive_status() {
		return receive_status;
	}
	public void setReceive_status(Integer receive_status) {
		this.receive_status = receive_status;
	}
	public String getReceive_code() {
		return receive_code;
	}
	public void setReceive_code(String receive_code) {
		this.receive_code = receive_code;
	}
	public String getReceipt_time() {
		return receipt_time;
	}
	public void setReceipt_time(String receipt_time) {
		this.receipt_time = receipt_time;
	}
}
