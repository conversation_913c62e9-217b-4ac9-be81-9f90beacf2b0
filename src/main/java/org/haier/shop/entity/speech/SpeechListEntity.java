package org.haier.shop.entity.speech;

import java.io.Serializable;

/**
 * speech_list实体类
 */
public class SpeechListEntity extends BaseEntity implements Serializable {
    //店铺编号
    private Long shopUnique;
    //申请编号
    private String applyNo;
    //完成时间
    private String completeTime;
    //前端上传的文本信息
    private String speechTextPhone;
    //语音文件保留地址，https形式
    private String speechPath;
    //系统识别的语音文本
    private String speechTextSys;
    //app类型:001:(百货商家端超市版);002:(百货商家端餐饮版);003:(百货商端农贸版);004:(金圈云商);005:(金圈云商供应商版);006:(一刻钟到家APP);
    private String appType;
    //命令类型:0001(打开);0002(关闭);
    private String cmdType;
    //页面索引:具体见附录对照
    private String pageIndex;
    //识别到的参数信息,jsonstr格式，具体参数需要根据具体pageIndex判定
    private String params;
    //系统识别的命令
    private String cmdSys;
    //系统识别到的错误
    private String errorMsgSys;
    //创建时间
    private String createTime;
    //进度，由此字段判断是否识别成功。1、新受理；2、处理中；3、处理成功；4、处理失败。
    private Integer progress;

    public String getCompleteTime() {
        return completeTime;
    }

    public void setCompleteTime(String completeTime) {
        this.completeTime = completeTime;
    }

    public Long getShopUnique() {
        return shopUnique;
    }

    public void setShopUnique(Long shopUnique) {
        this.shopUnique = shopUnique;
    }

    public String getApplyNo() {
        return applyNo;
    }

    public void setApplyNo(String applyNo) {
        this.applyNo = applyNo;
    }

    public String getSpeechTextPhone() {
        return speechTextPhone;
    }

    public void setSpeechTextPhone(String speechTextPhone) {
        this.speechTextPhone = speechTextPhone;
    }

    public String getSpeechPath() {
        return speechPath;
    }

    public void setSpeechPath(String speechPath) {
        this.speechPath = speechPath;
    }

    public String getSpeechTextSys() {
        return speechTextSys;
    }

    public void setSpeechTextSys(String speechTextSys) {
        this.speechTextSys = speechTextSys;
    }

    public String getAppType() {
        return appType;
    }

    public void setAppType(String appType) {
        this.appType = appType;
    }

    public String getCmdType() {
        return cmdType;
    }

    public void setCmdType(String cmdType) {
        this.cmdType = cmdType;
    }

    public String getPageIndex() {
        return pageIndex;
    }

    public void setPageIndex(String pageIndex) {
        this.pageIndex = pageIndex;
    }

    public String getParams() {
        return params;
    }

    public void setParams(String params) {
        this.params = params;
    }

    public String getCmdSys() {
        return cmdSys;
    }

    public void setCmdSys(String cmdSys) {
        this.cmdSys = cmdSys;
    }

    public void setErrorMsgSys(String errorMsgSys) {
        this.errorMsgSys = errorMsgSys;
    }

    public String getCreateTime() {
        return createTime;
    }

    public void setCreateTime(String createTime) {
        this.createTime = createTime;
    }

    public Integer getProgress() {
        return progress;
    }

    public void setProgress(Integer progress) {
        this.progress = progress;
    }

    public String getErrorMsgSys() {
        return errorMsgSys;
    }
}
