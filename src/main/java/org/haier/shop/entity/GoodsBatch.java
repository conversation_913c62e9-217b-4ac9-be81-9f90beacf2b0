package org.haier.shop.entity;

import com.fasterxml.jackson.annotation.JsonFormat;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 商品入库批次表
 * <AUTHOR> 
 * @ClassName GoodsBatch
 * @Date 2024-04-28
 **/

public class GoodsBatch {
	private Long goodsBatchId;

	/**
	* 店铺编码
	*/
	private Long shopUnique;

	/**
	* 批次号
	*/
	private String batchUnique;

	/**
	* 入库单号
	*/
	private String stockListUnique;

	/**
	* 商品条码
	*/
	private String goodsBarcode;

	/**
	* 商品进价
	*/
	private BigDecimal goodsInPrice;

	/**
	* 商品剩余数量
	*/
	private BigDecimal goodsCount;

	/**
	* 商品入库数量
	*/
	private BigDecimal goodsInCount;

	/**
	* 商品生产日期
	*/
	@JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
	private Date goodsProd;

	/**
	* 商品过期日期
	*/
	@JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
	private Date goodsExp;

	/**
	* 商品保质天数
	*/
	private Integer goodsLife;

	/**
	* 创建时间
	*/
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
	private Date createTime;

	/**
	* 创建人ID
	*/
	private Long createId;

	/**
	* 修改时间
	*/
	private Date updateTime;

	/**
	* 修改人ID
	*/
	private Long updateId;

	/**
	 * 入库商品条码
	 */
	private String sourceBarcode;

	/**
	 * 商品包含子商品数量
	 */
	private BigDecimal goodsContain;

	public String getSourceBarcode() {
		return sourceBarcode;
	}

	public void setSourceBarcode(String sourceBarcode) {
		this.sourceBarcode = sourceBarcode;
	}

	public BigDecimal getGoodsContain() {
		return goodsContain;
	}

	public void setGoodsContain(BigDecimal goodsContain) {
		this.goodsContain = goodsContain;
	}

	public Long getGoodsBatchId() {
		return goodsBatchId;
	}

	public void setGoodsBatchId(Long goodsBatchId) {
		this.goodsBatchId = goodsBatchId;
	}

	public Long getShopUnique() {
		return shopUnique;
	}

	public void setShopUnique(Long shopUnique) {
		this.shopUnique = shopUnique;
	}

	public String getBatchUnique() {
		return batchUnique;
	}

	public void setBatchUnique(String batchUnique) {
		this.batchUnique = batchUnique;
	}

	public String getStockListUnique() {
		return stockListUnique;
	}

	public void setStockListUnique(String stockListUnique) {
		this.stockListUnique = stockListUnique;
	}

	public String getGoodsBarcode() {
		return goodsBarcode;
	}

	public void setGoodsBarcode(String goodsBarcode) {
		this.goodsBarcode = goodsBarcode;
	}

	public BigDecimal getGoodsInPrice() {
		return goodsInPrice;
	}

	public void setGoodsInPrice(BigDecimal goodsInPrice) {
		this.goodsInPrice = goodsInPrice;
	}

	public BigDecimal getGoodsCount() {
		return goodsCount;
	}

	public void setGoodsCount(BigDecimal goodsCount) {
		this.goodsCount = goodsCount;
	}

	public BigDecimal getGoodsInCount() {
		return goodsInCount;
	}

	public void setGoodsInCount(BigDecimal goodsInCount) {
		this.goodsInCount = goodsInCount;
	}

	public Date getGoodsProd() {
		return goodsProd;
	}

	public void setGoodsProd(Date goodsProd) {
		this.goodsProd = goodsProd;
	}

	public Date getGoodsExp() {
		return goodsExp;
	}

	public void setGoodsExp(Date goodsExp) {
		this.goodsExp = goodsExp;
	}

	public Integer getGoodsLife() {
		return goodsLife;
	}

	public void setGoodsLife(Integer goodsLife) {
		this.goodsLife = goodsLife;
	}

	public Date getCreateTime() {
		return createTime;
	}

	public void setCreateTime(Date createTime) {
		this.createTime = createTime;
	}

	public Long getCreateId() {
		return createId;
	}

	public void setCreateId(Long createId) {
		this.createId = createId;
	}

	public Date getUpdateTime() {
		return updateTime;
	}

	public void setUpdateTime(Date updateTime) {
		this.updateTime = updateTime;
	}

	public Long getUpdateId() {
		return updateId;
	}

	public void setUpdateId(Long updateId) {
		this.updateId = updateId;
	}
}