package org.haier.shop.entity;

import java.util.Date;

/**
 * 员工表
 * <AUTHOR> 
 * @ClassName ShopStaff
 * @Date 2024-05-01
 **/

public class ShopStaff {
	private Long staffId;

	/**
	* 收银员登录账号
	*/
	private String staffAccount;

	/**
	* 用户登录密码
	*/
	private String staffPwd;

	/**
	* 密码明文
	*/
	private String pwdOk;

	/**
	* 超级管理员编号：为空字符串则表示不是超级管理员
	*/
	private String managerUnique;

	/**
	* 用户所属店铺编号
	*/
	private Long shopUnique;

	/**
	* 员工生日
	*/
	private Date staffBirthday;

	/**
	* 员工手机号
	*/
	private String staffPhone;

	/**
	* 职位类型：1、收银员；2、送货员；3、店主；4其他待开发(此字段弃用，值为3和1，店主和其他)
	*/
	private Integer staffPosition;

	/**
	* 是否有权利抹零：1、有；2、没有
	*/
	private Integer staffNoChange;

	/**
	* 是否有权利修改商品信息：1、有；2、没有
	*/
	private Integer staffModifyGoods;

	/**
	* 同步类型：1、已同步；2、未同步
	*/
	private Integer sameType;

	/**
	* 员工名称
	*/
	private String staffName;

	/**
	* 头像路径
	*/
	private String staffProtrait;

	/**
	* 百度推送CHANNELID
	*/
	private String channelId;

	/**
	* 手机类型：0、未登录或电脑登录；1、ANDRIOD；2：IOS；
	*/
	private Integer phoneType;

	/**
	* 手机型号
	*/
	private String phoneModel;

	/**
	* 系统版本号
	*/
	private String systemVersion;

	/**
	* 百货豆配置和提现处理权限：1有0没有
	*/
	private Integer bean;

	/**
	* 区域编码
	*/
	private String county;

	/**
	* 0：收银机会员充值界面显示普通现金充值；1、显示自定义充值设置界面
	*/
	private Integer staffRechargeCash;

	public Long getStaffId() {
		return staffId;
	}

	public void setStaffId(Long staffId) {
		this.staffId = staffId;
	}

	public String getStaffAccount() {
		return staffAccount;
	}

	public void setStaffAccount(String staffAccount) {
		this.staffAccount = staffAccount;
	}

	public String getStaffPwd() {
		return staffPwd;
	}

	public void setStaffPwd(String staffPwd) {
		this.staffPwd = staffPwd;
	}

	public String getPwdOk() {
		return pwdOk;
	}

	public void setPwdOk(String pwdOk) {
		this.pwdOk = pwdOk;
	}

	public String getManagerUnique() {
		return managerUnique;
	}

	public void setManagerUnique(String managerUnique) {
		this.managerUnique = managerUnique;
	}

	public Long getShopUnique() {
		return shopUnique;
	}

	public void setShopUnique(Long shopUnique) {
		this.shopUnique = shopUnique;
	}

	public Date getStaffBirthday() {
		return staffBirthday;
	}

	public void setStaffBirthday(Date staffBirthday) {
		this.staffBirthday = staffBirthday;
	}

	public String getStaffPhone() {
		return staffPhone;
	}

	public void setStaffPhone(String staffPhone) {
		this.staffPhone = staffPhone;
	}

	public Integer getStaffPosition() {
		return staffPosition;
	}

	public void setStaffPosition(Integer staffPosition) {
		this.staffPosition = staffPosition;
	}

	public Integer getStaffNoChange() {
		return staffNoChange;
	}

	public void setStaffNoChange(Integer staffNoChange) {
		this.staffNoChange = staffNoChange;
	}

	public Integer getStaffModifyGoods() {
		return staffModifyGoods;
	}

	public void setStaffModifyGoods(Integer staffModifyGoods) {
		this.staffModifyGoods = staffModifyGoods;
	}

	public Integer getSameType() {
		return sameType;
	}

	public void setSameType(Integer sameType) {
		this.sameType = sameType;
	}

	public String getStaffName() {
		return staffName;
	}

	public void setStaffName(String staffName) {
		this.staffName = staffName;
	}

	public String getStaffProtrait() {
		return staffProtrait;
	}

	public void setStaffProtrait(String staffProtrait) {
		this.staffProtrait = staffProtrait;
	}

	public String getChannelId() {
		return channelId;
	}

	public void setChannelId(String channelId) {
		this.channelId = channelId;
	}

	public Integer getPhoneType() {
		return phoneType;
	}

	public void setPhoneType(Integer phoneType) {
		this.phoneType = phoneType;
	}

	public String getPhoneModel() {
		return phoneModel;
	}

	public void setPhoneModel(String phoneModel) {
		this.phoneModel = phoneModel;
	}

	public String getSystemVersion() {
		return systemVersion;
	}

	public void setSystemVersion(String systemVersion) {
		this.systemVersion = systemVersion;
	}

	public Integer getBean() {
		return bean;
	}

	public void setBean(Integer bean) {
		this.bean = bean;
	}

	public String getCounty() {
		return county;
	}

	public void setCounty(String county) {
		this.county = county;
	}

	public Integer getStaffRechargeCash() {
		return staffRechargeCash;
	}

	public void setStaffRechargeCash(Integer staffRechargeCash) {
		this.staffRechargeCash = staffRechargeCash;
	}
}