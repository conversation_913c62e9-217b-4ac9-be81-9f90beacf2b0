package org.haier.shop.entity;

import java.util.List;

public class GoodsGroups {
		//大类ID
		private String goodsKindId;
		//所属店铺编号
		private String shopUnique;
		//大类编号
		private String goodsKindUnique;
		//大类父类编号
		private String goodsKindParunique;
		//大类名称
		private String goodsKindName;
		//大类序号
		private String goodsKindOrder;
		//大类图片路径
		private String goodsKindPicture;
		//子类商品分类
		private List<GoodsKinds> goodsKinds;
		public String getGoodsKindId() {
			return goodsKindId;
		}
		public void setGoodsKindId(String goodsKindId) {
			this.goodsKindId = goodsKindId;
		}
		public String getShopUnique() {
			return shopUnique;
		}
		public void setShopUnique(String shopUnique) {
			this.shopUnique = shopUnique;
		}
		public String getGoodsKindUnique() {
			return goodsKindUnique;
		}
		public void setGoodsKindUnique(String goodsKindUnique) {
			this.goodsKindUnique = goodsKindUnique;
		}
		public String getGoodsKindParunique() {
			return goodsKindParunique;
		}
		public void setGoodsKindParunique(String goodsKindParunique) {
			this.goodsKindParunique = goodsKindParunique;
		}
		public String getGoodsKindName() {
			return goodsKindName;
		}
		public void setGoodsKindName(String goodsKindName) {
			this.goodsKindName = goodsKindName;
		}
		public String getGoodsKindOrder() {
			return goodsKindOrder;
		}
		public void setGoodsKindOrder(String goodsKindOrder) {
			this.goodsKindOrder = goodsKindOrder;
		}
		public String getGoodsKindPicture() {
			return goodsKindPicture;
		}
		public void setGoodsKindPicture(String goodsKindPicture) {
			this.goodsKindPicture = goodsKindPicture;
		}
		public List<GoodsKinds> getGoodsKinds() {
			return goodsKinds;
		}
		public void setGoodsKinds(List<GoodsKinds> goodsKinds) {
			this.goodsKinds = goodsKinds;
		}
}
