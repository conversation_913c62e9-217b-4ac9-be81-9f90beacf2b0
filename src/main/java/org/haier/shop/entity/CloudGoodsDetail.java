package org.haier.shop.entity;

import java.io.Serializable;

public class CloudGoodsDetail implements Serializable{
	/**
	 * 
	 */
	private static final long serialVersionUID = 1L;
	//商品条码
	private String goodsBarcode;
	//商品名称
	private String goodsName;
	//商品进价
	private Double goodsInPrice;
	//商品售价
	private Double goodsSalePrice;
	//网购价
	private Double goodsWebSalePrice;
	//商品图片
	private String goodsPicturePath;
	//商品包含最小单位数量
	private Double goodsContain;
	//计价单位
	private String goodsUnit;
	//商品规格
	private String goodsStandard;
	//商品排序
	private Integer orderSort;
	//商品会员价
	private Double goodsCusPrice;
	//商品库存
	private Double goodsCount;
	//最小采购量：起定量
	private Double minSaleCount;
	//每次采购的增加数量
	private Double incrementCount;
	
	public Double getGoodsCount() {
		return goodsCount;
	}
	public void setGoodsCount(Double goodsCount) {
		this.goodsCount = goodsCount;
	}
	public Integer getOrderSort() {
		return orderSort;
	}
	public void setOrderSort(Integer orderSort) {
		this.orderSort = orderSort;
	}
	public Double getGoodsWebSalePrice() {
		return goodsWebSalePrice;
	}
	public void setGoodsWebSalePrice(Double goodsWebSalePrice) {
		this.goodsWebSalePrice = goodsWebSalePrice;
	}
	public String getGoodsStandard() {
		return goodsStandard;
	}
	public void setGoodsStandard(String goodsStandard) {
		this.goodsStandard = goodsStandard;
	}
	public String getGoodsBarcode() {
		return goodsBarcode;
	}
	public void setGoodsBarcode(String goodsBarcode) {
		this.goodsBarcode = goodsBarcode;
	}
	public String getGoodsName() {
		return goodsName;
	}
	public void setGoodsName(String goodsName) {
		this.goodsName = goodsName;
	}
	public Double getGoodsInPrice() {
		return goodsInPrice;
	}
	public void setGoodsInPrice(Double goodsInPrice) {
		this.goodsInPrice = goodsInPrice;
	}
	public Double getGoodsSalePrice() {
		return goodsSalePrice;
	}
	public void setGoodsSalePrice(Double goodsSalePrice) {
		this.goodsSalePrice = goodsSalePrice;
	}
	public Double getGoodsContain() {
		return goodsContain;
	}
	public void setGoodsContain(Double goodsContain) {
		this.goodsContain = goodsContain;
	}
	public String getGoodsUnit() {
		return goodsUnit;
	}
	public void setGoodsUnit(String goodsUnit) {
		this.goodsUnit = goodsUnit;
	}
	public String getGoodsPicturePath() {
		return goodsPicturePath;
	}
	public void setGoodsPicturePath(String goodsPicturePath) {
		this.goodsPicturePath = goodsPicturePath;
	}
	public Double getGoodsCusPrice() {
		return goodsCusPrice;
	}
	public void setGoodsCusPrice(Double goodsCusPrice) {
		this.goodsCusPrice = goodsCusPrice;
	}
	public Double getMinSaleCount() {
		return minSaleCount;
	}
	public void setMinSaleCount(Double minSaleCount) {
		this.minSaleCount = minSaleCount;
	}
	public Double getIncrementCount() {
		return incrementCount;
	}
	public void setIncrementCount(Double incrementCount) {
		this.incrementCount = incrementCount;
	}
	@Override
	public String toString() {
		return "CloudGoodsDetail [goodsBarcode=" + goodsBarcode + ", goodsName=" + goodsName + ", goodsInPrice="
				+ goodsInPrice + ", goodsSalePrice=" + goodsSalePrice + ", goodsPicturePath=" + goodsPicturePath
				+ ", goodsContain=" + goodsContain + ", goodsUnit=" + goodsUnit + "]";
	}
}
