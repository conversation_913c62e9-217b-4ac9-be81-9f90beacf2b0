package org.haier.shop.entity;

import java.io.Serializable;
import java.math.BigDecimal;

public class CustomerReverseRechargeEntity implements Serializable {
    //ID
    private Long id;
    //销售订单号
    private String saleListUnique;
    //退款订单号
    private String retListUnique;
    //退款状态：1、退款中；2、退款成功；3、退款失败
    private Integer applyStatus;
    //退款申请金额
    private BigDecimal applyMoney;
    //备注信息
    private String remarks;
    //创建时间
    private String createDatetime;
    //退款成功时间
    private String successDatetime;


    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getSaleListUnique() {
        return saleListUnique;
    }

    public void setSaleListUnique(String saleListUnique) {
        this.saleListUnique = saleListUnique;
    }

    public String getRetListUnique() {
        return retListUnique;
    }

    public void setRetListUnique(String retListUnique) {
        this.retListUnique = retListUnique;
    }

    public Integer getApplyStatus() {
        return applyStatus;
    }

    public void setApplyStatus(Integer applyStatus) {
        this.applyStatus = applyStatus;
    }

    public BigDecimal getApplyMoney() {
        return applyMoney;
    }

    public void setApplyMoney(BigDecimal applyMoney) {
        this.applyMoney = applyMoney;
    }

    public String getCreateDatetime() {
        return createDatetime;
    }

    public void setCreateDatetime(String createDatetime) {
        this.createDatetime = createDatetime;
    }

    public String getSuccessDatetime() {
        return successDatetime;
    }

    public void setSuccessDatetime(String successDatetime) {
        this.successDatetime = successDatetime;
    }

    public String getRemarks() {
        return remarks;
    }

    public void setRemarks(String remarks) {
        this.remarks = remarks;
    }
}
