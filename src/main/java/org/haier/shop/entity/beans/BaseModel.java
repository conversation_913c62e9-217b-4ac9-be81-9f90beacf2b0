package org.haier.shop.entity.beans;

import java.io.Serializable;

public class BaseModel implements Serializable {

	private static final long serialVersionUID = 1L;

    private Integer page = Integer.valueOf(1);
	private Integer rows = Integer.valueOf(10);
	private Integer offset;//开始位置
	public Integer getPage() {
	    return this.page;
	  }

	  public void setPage(Object page) {
		  this.page = Integer.valueOf(Integer.parseInt(page.toString()));
	  }

	  public Integer getRows()
	  {
	    return this.rows;
	  }

	  public void setRows(Object rows) {
		  this.rows = Integer.valueOf(Integer.parseInt(rows.toString()));
	  }

	  public Integer getOffset()
	  {
	    if ((this.page == null) || (this.rows == null))
	      return Integer.valueOf(0);
	    this.offset = Integer.valueOf((this.page.intValue() - 1) * this.rows.intValue());
	    if (this.offset.intValue() < 0)
	      return Integer.valueOf(0);
	    return this.offset;
	  }

	  public void setOffset(Integer offset) {
	    if (offset != null)
	      if (offset.intValue() < 0)
	        this.offset = Integer.valueOf(0);
	      else
	        this.offset = offset;
	  }
}
