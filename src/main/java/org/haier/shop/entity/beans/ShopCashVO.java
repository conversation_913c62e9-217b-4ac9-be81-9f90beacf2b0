/**
 * 
 */
package org.haier.shop.entity.beans;

/**
 * <AUTHOR>
 *	商家线上订单余额提现实体类
 */
public class ShopCashVO {
	
	private String shop_unique;
	//提现金额
	private double take_money=0.00;
	//提现前账号余额
	private double shop_balance=0.00;
	//提现的账户类型
	private Integer card_type;
	//卡号
	private Integer card_id;
	//持卡人
	private String bank_name;
	//卡号
	private String bank_card;
	//单号
	private String order_id;
	//申请人员
	private Integer staff_id;
	
	public String getShop_unique() {
		return shop_unique;
	}
	public void setShop_unique(String shop_unique) {
		this.shop_unique = shop_unique;
	}
	public double getTake_money() {
		return take_money;
	}
	public void setTake_money(double take_money) {
		this.take_money = take_money;
	}
	public double getShop_balance() {
		return shop_balance;
	}
	public void setShop_balance(double shop_balance) {
		this.shop_balance = shop_balance;
	}
	public Integer getCard_type() {
		return card_type;
	}
	public void setCard_type(Integer card_type) {
		this.card_type = card_type;
	}
	public Integer getCard_id() {
		return card_id;
	}
	public void setCard_id(Integer card_id) {
		this.card_id = card_id;
	}
	public String getBank_name() {
		return bank_name;
	}
	public void setBank_name(String bank_name) {
		this.bank_name = bank_name;
	}
	public String getBank_card() {
		return bank_card;
	}
	public void setBank_card(String bank_card) {
		this.bank_card = bank_card;
	}
	public String getOrder_id() {
		return order_id;
	}
	public void setOrder_id(String order_id) {
		this.order_id = order_id;
	}
	public Integer getStaff_id() {
		return staff_id;
	}
	public void setStaff_id(Integer staff_id) {
		this.staff_id = staff_id;
	}
	
}
