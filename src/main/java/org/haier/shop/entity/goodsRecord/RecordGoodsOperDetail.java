package org.haier.shop.entity.goodsRecord;

public class RecordGoodsOperDetail {
    /**
     * 记录详情ID
     */
    private Long id;
    /**
     * 记录ID
     */
    private Long recordGoodsOperId;
    /**
     * 修改的字段
     */
    private Integer goodsOperClass;
    /**
     * 修改前的值(null值返回空字符串）
     */
    private String valueSource;
    /**
     * 修改后的值
     */
    private String valueNew;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getRecordGoodsOperId() {
        return recordGoodsOperId;
    }

    public void setRecordGoodsOperId(Long recordGoodsOperId) {
        this.recordGoodsOperId = recordGoodsOperId;
    }

    public Integer getGoodsOperClass() {
        return goodsOperClass;
    }

    public void setGoodsOperClass(Integer goodsOperClass) {
        this.goodsOperClass = goodsOperClass;
    }

    public String getValueSource() {
        return valueSource;
    }

    public void setValueSource(String valueSource) {
        this.valueSource = valueSource;
    }

    public String getValueNew() {
        return valueNew;
    }

    public void setValueNew(String valueNew) {
        this.valueNew = valueNew;
    }
}
