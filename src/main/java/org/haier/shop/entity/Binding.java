package org.haier.shop.entity;

import java.util.List;

/**
 * 商品捆绑关系查询封装结果
 * <AUTHOR>
 */
public class Binding {
	//捆绑关系编号
	private Integer bindingId;
	//店铺编号
	private Long shopUnique;
	//绑定关系
	private Long bindingUnique;
	//使用状态:1、在用；2、禁用
	private Integer useTypeCode;
	//使用状态
	private String useType;
	//创建时间
	private String establishDatetime;
	//禁用时间
	private String disableDatetime;
	//商品详情
	private List<BindGoods> listDetail;
	//捆绑销售商品总价
	private Double bindingTotal;
	
	public Integer getBindingId() {
		return bindingId;
	}
	public void setBindingId(Integer bindingId) {
		this.bindingId = bindingId;
	}
	public Long getShopUnique() {
		return shopUnique;
	}
	public void setShopUnique(Long shopUnique) {
		this.shopUnique = shopUnique;
	}
	public Long getBindingUnique() {
		return bindingUnique;
	}
	public void setBindingUnique(Long bindingUnique) {
		this.bindingUnique = bindingUnique;
	}
	public Integer getUseTypeCode() {
		return useTypeCode;
	}
	public void setUseTypeCode(Integer useTypeCode) {
		this.useTypeCode = useTypeCode;
	}
	public String getUseType() {
		return useType;
	}
	public void setUseType(String useType) {
		this.useType = useType;
	}
	public String getEstablishDatetime() {
		return establishDatetime;
	}
	public void setEstablishDatetime(String establishDatetime) {
		this.establishDatetime = establishDatetime;
	}
	public String getDisableDatetime() {
		return disableDatetime;
	}
	public void setDisableDatetime(String disableDatetime) {
		this.disableDatetime = disableDatetime;
	}
	public List<BindGoods> getListDetail() {
		return listDetail;
	}
	public void setListDetail(List<BindGoods> listDetail) {
		this.listDetail = listDetail;
	}
	public Double getBindingTotal() {
		return bindingTotal;
	}
	public void setBindingTotal(Double bindingTotal) {
		this.bindingTotal = bindingTotal;
	}
	
}
