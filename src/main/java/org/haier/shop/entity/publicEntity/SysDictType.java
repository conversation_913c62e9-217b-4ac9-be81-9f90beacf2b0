package org.haier.shop.entity.publicEntity;

import java.util.List;

/**
* @author: 作者:王恩龙
* @version: 2023年6月14日 下午2:53:35
*
*/
public class SysDictType {
	//字典编号
	private Long dict_id;
	//字典名称
	private String dict_name;
	//字典类型，关联字典的键值对
	private String dict_type;
	//字典状态：0、正常；1、停用
	private String status;
	//备注信息
	private String remark;
	//键值对
	private List<SysDictData> list;
	public Long getDict_id() {
		return dict_id;
	}
	public void setDict_id(Long dict_id) {
		this.dict_id = dict_id;
	}
	public String getDict_name() {
		return dict_name;
	}
	public void setDict_name(String dict_name) {
		this.dict_name = dict_name;
	}
	public String getDict_type() {
		return dict_type;
	}
	public void setDict_type(String dict_type) {
		this.dict_type = dict_type;
	}
	public String getStatus() {
		return status;
	}
	public void setStatus(String status) {
		this.status = status;
	}
	public String getRemark() {
		return remark;
	}
	public void setRemark(String remark) {
		this.remark = remark;
	}
	public List<SysDictData> getList() {
		return list;
	}
	public void setList(List<SysDictData> list) {
		this.list = list;
	}
}
