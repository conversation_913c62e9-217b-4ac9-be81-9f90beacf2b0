package org.haier.shop.entity.publicEntity;
/**
* @author: 作者:王恩龙
* @version: 2023年6月14日 下午2:53:23
*
*/
public class SysDictData {
	//字典编码
	private Long dict_code;
	//字典排序
	private Integer dict_sort;
	//字典标签值
	private String dict_label;
	//字典键值，数据库存储的值
	private String dict_value;
	//字典类型，该字典的用途
	private String dict_type;
	//是否默认;Y默认；N非默认
	private String is_default;
	//状态;0正常；1、停用
	private String status;
	//备注信息
	private String remark;
	public Long getDict_code() {
		return dict_code;
	}
	public void setDict_code(Long dict_code) {
		this.dict_code = dict_code;
	}
	public Integer getDict_sort() {
		return dict_sort;
	}
	public void setDict_sort(Integer dict_sort) {
		this.dict_sort = dict_sort;
	}
	public String getDict_label() {
		return dict_label;
	}
	public void setDict_label(String dict_label) {
		this.dict_label = dict_label;
	}
	public String getDict_value() {
		return dict_value;
	}
	public void setDict_value(String dict_value) {
		this.dict_value = dict_value;
	}
	public String getDict_type() {
		return dict_type;
	}
	public void setDict_type(String dict_type) {
		this.dict_type = dict_type;
	}
	public String getIs_default() {
		return is_default;
	}
	public void setIs_default(String is_default) {
		this.is_default = is_default;
	}
	public String getStatus() {
		return status;
	}
	public void setStatus(String status) {
		this.status = status;
	}
	public String getRemark() {
		return remark;
	}
	public void setRemark(String remark) {
		this.remark = remark;
	}
}
