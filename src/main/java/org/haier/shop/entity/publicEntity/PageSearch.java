package org.haier.shop.entity.publicEntity;

import java.io.Serializable;

public class PageSearch implements Serializable{
	
	private static final long serialVersionUID = 1L;
	private Integer pageNum;
	private Integer pageSize;
	public Integer getPageNum() {
		return pageNum;
	}
	public void setPageNum(Integer pageNum) {
		this.pageNum = pageNum;
	}
	public Integer getPageSize() {
		return pageSize;
	}
	public void setPageSize(Integer pageSize) {
		this.pageSize = pageSize;
	}
	/**
	 * 获取搜索的开始数
	 * @return
	 */
	public Integer getStartNum() {
		System.out.println(this.pageNum);
		System.out.println(this.pageSize);
		return (this.pageNum-1)*this.pageSize;
	}
}
