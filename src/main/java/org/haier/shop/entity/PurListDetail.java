package org.haier.shop.entity;

public class PurListDetail {
	//商品详情
	private String goodsBarcode;
	//商品名称
	private String goodsName;
	//进货价
	private Double goodsPrice;
	//进货数量
	private Double goodsCount;
	//小计
	private Double subtotal;
	
	public String getGoodsBarcode() {
		return goodsBarcode;
	}
	public void setGoodsBarcode(String goodsBarcode) {
		this.goodsBarcode = goodsBarcode;
	}
	public String getGoodsName() {
		return goodsName;
	}
	public void setGoodsName(String goodsName) {
		this.goodsName = goodsName;
	}
	public Double getGoodsPrice() {
		return goodsPrice;
	}
	public void setGoodsPrice(Double goodsPrice) {
		this.goodsPrice = goodsPrice;
	}
	public Double getGoodsCount() {
		return goodsCount;
	}
	public void setGoodsCount(Double goodsCount) {
		this.goodsCount = goodsCount;
	}
	
	public Double getSubtotal() {
		return subtotal;
	}
	public void setSubtotal(Double subtotal) {
		this.subtotal = subtotal;
	}
	@Override
	public String toString() {
		return "PurListDetail [goodsBarcode=" + goodsBarcode + ", goodsName=" + goodsName + ", goodsPrice=" + goodsPrice
				+ ", goodsCount=" + goodsCount + "]";
	}
}
