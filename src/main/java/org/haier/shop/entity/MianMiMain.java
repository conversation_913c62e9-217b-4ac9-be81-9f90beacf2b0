package org.haier.shop.entity;

import java.util.List;

public class MianMiMain {
	//店铺编号
	private Long shopUnique;
	//店铺名称
	private String shopName;
	//免密详情
	private List<MianMiDetail> list;
	public Long getShopUnique() {
		return shopUnique;
	}
	public void setShopUnique(Long shopUnique) {
		this.shopUnique = shopUnique;
	}
	public String getShopName() {
		return shopName;
	}
	public void setShopName(String shopName) {
		this.shopName = shopName;
	}
	public List<MianMiDetail> getList() {
		return list;
	}
	public void setList(List<MianMiDetail> list) {
		this.list = list;
	}
	@Override
	public String toString() {
		return "MianMiMain [shopUnique=" + shopUnique + ", shopName=" + shopName + ", list=" + list + "]";
	}
}
