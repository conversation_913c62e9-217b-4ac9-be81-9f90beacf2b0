package org.haier.shop.entity.supplier;

/**
 * @description 补货计划供货商
 */
public class RestockPlanSupplierEntity {
    /**
     * ID
     */
    private Long shopRestockplanPresentId;
    /**
     * 店铺编号
     */
    private Long shopUnique;
    /**
     * 补货单id
     */
    private Long shopRestockplanId;

    /**
     * 供应商编号
     */
    private String supplierUnique;
    /**
     * 备注
     */
    private String remark;
    /**
     * 操作人
     */
    private String createUser;
    /**
     * 操作时间
     */
    private String createTime;

    public Long getShopRestockplanPresentId() {
        return shopRestockplanPresentId;
    }

    public void setShopRestockplanPresentId(Long shopRestockplanPresentId) {
        this.shopRestockplanPresentId = shopRestockplanPresentId;
    }

    public Long getShopUnique() {
        return shopUnique;
    }

    public void setShopUnique(Long shopUnique) {
        this.shopUnique = shopUnique;
    }

    public Long getShopRestockplanId() {
        return shopRestockplanId;
    }

    public void setShopRestockplanId(Long shopRestockplanId) {
        this.shopRestockplanId = shopRestockplanId;
    }

    public String getSupplierUnique() {
        return supplierUnique;
    }

    public void setSupplierUnique(String supplierUnique) {
        this.supplierUnique = supplierUnique;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public String getCreateUser() {
        return createUser;
    }

    public void setCreateUser(String createUser) {
        this.createUser = createUser;
    }

    public String getCreateTime() {
        return createTime;
    }

    public void setCreateTime(String createTime) {
        this.createTime = createTime;
    }
}