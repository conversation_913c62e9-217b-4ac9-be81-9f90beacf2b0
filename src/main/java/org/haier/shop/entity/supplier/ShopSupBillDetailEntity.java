package org.haier.shop.entity.supplier;

import java.math.BigDecimal;

public class ShopSupBillDetailEntity {
    private Long detailId;
    private String billId;
    private String orderNo;
    private String goodsName;
    private String goodsBarcode;//商品条码
    private String goodsUnit;//配送单位
    private BigDecimal goodsCount;//配送数量
    private BigDecimal goodsPurchasePrice;//配送价
    private BigDecimal goodsPurchaseCount;//补货单商品数量
    private String goodsPurchaseUnit;//补货单商品单位
    private BigDecimal goodsActualCount;//实际入库数量
    private BigDecimal goodsSalePrice;//建议售价
    private BigDecimal goodsSalePriceStorage;//入库商品售价
    private BigDecimal goodsWebSalePriceStorage;//入库网单价
    private BigDecimal goodsCusPriceStorage;//入库会员价
    private Integer goodsLife;//保质期
    private String goodsProduceDate;//生产日期
    private BigDecimal subtotalMoney;//小计金额
    private Integer status;//是否缺货
    private Integer checkStatus;//是否核实
    private Long createId;
    private String createBy;
    private String createTime;
    private Long modifyId;
    private String modifyBy;
    private String modifyTime;
    private Integer delFlag;
    private String goodsImageUrl;//商品图片

    public Long getDetailId() {
        return detailId;
    }

    public void setDetailId(Long detailId) {
        this.detailId = detailId;
    }

    public String getBillId() {
        return billId;
    }

    public void setBillId(String billId) {
        this.billId = billId;
    }

    public String getOrderNo() {
        return orderNo;
    }

    public void setOrderNo(String orderNo) {
        this.orderNo = orderNo;
    }

    public String getGoodsName() {
        return goodsName;
    }

    public void setGoodsName(String goodsName) {
        this.goodsName = goodsName;
    }

    public String getGoodsBarcode() {
        return goodsBarcode;
    }

    public void setGoodsBarcode(String goodsBarcode) {
        this.goodsBarcode = goodsBarcode;
    }

    public String getGoodsUnit() {
        return goodsUnit;
    }

    public void setGoodsUnit(String goodsUnit) {
        this.goodsUnit = goodsUnit;
    }

    public BigDecimal getGoodsCount() {
        return goodsCount;
    }

    public void setGoodsCount(BigDecimal goodsCount) {
        this.goodsCount = goodsCount;
    }

    public BigDecimal getGoodsPurchasePrice() {
        return goodsPurchasePrice;
    }

    public void setGoodsPurchasePrice(BigDecimal goodsPurchasePrice) {
        this.goodsPurchasePrice = goodsPurchasePrice;
    }

    public BigDecimal getGoodsPurchaseCount() {
        return goodsPurchaseCount;
    }

    public void setGoodsPurchaseCount(BigDecimal goodsPurchaseCount) {
        this.goodsPurchaseCount = goodsPurchaseCount;
    }

    public String getGoodsPurchaseUnit() {
        return goodsPurchaseUnit;
    }

    public void setGoodsPurchaseUnit(String goodsPurchaseUnit) {
        this.goodsPurchaseUnit = goodsPurchaseUnit;
    }

    public BigDecimal getGoodsActualCount() {
        return goodsActualCount;
    }

    public void setGoodsActualCount(BigDecimal goodsActualCount) {
        this.goodsActualCount = goodsActualCount;
    }

    public BigDecimal getGoodsSalePrice() {
        return goodsSalePrice;
    }

    public void setGoodsSalePrice(BigDecimal goodsSalePrice) {
        this.goodsSalePrice = goodsSalePrice;
    }

    public BigDecimal getGoodsSalePriceStorage() {
        return goodsSalePriceStorage;
    }

    public void setGoodsSalePriceStorage(BigDecimal goodsSalePriceStorage) {
        this.goodsSalePriceStorage = goodsSalePriceStorage;
    }

    public BigDecimal getGoodsWebSalePriceStorage() {
        return goodsWebSalePriceStorage;
    }

    public void setGoodsWebSalePriceStorage(BigDecimal goodsWebSalePriceStorage) {
        this.goodsWebSalePriceStorage = goodsWebSalePriceStorage;
    }

    public BigDecimal getGoodsCusPriceStorage() {
        return goodsCusPriceStorage;
    }

    public void setGoodsCusPriceStorage(BigDecimal goodsCusPriceStorage) {
        this.goodsCusPriceStorage = goodsCusPriceStorage;
    }

    public Integer getGoodsLife() {
        return goodsLife;
    }

    public void setGoodsLife(Integer goodsLife) {
        this.goodsLife = goodsLife;
    }

    public String getGoodsProduceDate() {
        return goodsProduceDate;
    }

    public void setGoodsProduceDate(String goodsProduceDate) {
        this.goodsProduceDate = goodsProduceDate;
    }

    public BigDecimal getSubtotalMoney() {
        return subtotalMoney;
    }

    public void setSubtotalMoney(BigDecimal subtotalMoney) {
        this.subtotalMoney = subtotalMoney;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public Integer getCheckStatus() {
        return checkStatus;
    }

    public void setCheckStatus(Integer checkStatus) {
        this.checkStatus = checkStatus;
    }

    public Long getCreateId() {
        return createId;
    }

    public void setCreateId(Long createId) {
        this.createId = createId;
    }

    public String getCreateBy() {
        return createBy;
    }

    public void setCreateBy(String createBy) {
        this.createBy = createBy;
    }

    public String getCreateTime() {
        return createTime;
    }

    public void setCreateTime(String createTime) {
        this.createTime = createTime;
    }

    public String getModifyTime() {
        return modifyTime;
    }

    public void setModifyTime(String modifyTime) {
        this.modifyTime = modifyTime;
    }

    public Long getModifyId() {
        return modifyId;
    }

    public void setModifyId(Long modifyId) {
        this.modifyId = modifyId;
    }

    public String getModifyBy() {
        return modifyBy;
    }

    public void setModifyBy(String modifyBy) {
        this.modifyBy = modifyBy;
    }

    public Integer getDelFlag() {
        return delFlag;
    }

    public void setDelFlag(Integer delFlag) {
        this.delFlag = delFlag;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;

        ShopSupBillDetailEntity that = (ShopSupBillDetailEntity) o;

        if (detailId != that.detailId) return false;
        if (billId != null ? !billId.equals(that.billId) : that.billId != null) return false;
        if (orderNo != null ? !orderNo.equals(that.orderNo) : that.orderNo != null)
            return false;
        if (goodsPurchasePrice != null ? !goodsPurchasePrice.equals(that.goodsPurchasePrice) : that.goodsPurchasePrice != null)
            return false;
        if (goodsActualCount != null ? !goodsActualCount.equals(that.goodsActualCount) : that.goodsActualCount != null)
            return false;
        if (goodsSalePrice != null ? !goodsSalePrice.equals(that.goodsSalePrice) : that.goodsSalePrice != null)
            return false;
        if (goodsLife != null ? !goodsLife.equals(that.goodsLife) : that.goodsLife != null) return false;
        if (goodsProduceDate != null ? !goodsProduceDate.equals(that.goodsProduceDate) : that.goodsProduceDate != null)
            return false;
        if (subtotalMoney != null ? !subtotalMoney.equals(that.subtotalMoney) : that.subtotalMoney != null)
            return false;
        if (status != null ? !status.equals(that.status) : that.status != null) return false;
        if (checkStatus != null ? !checkStatus.equals(that.checkStatus) : that.checkStatus != null) return false;
        if (createId != null ? !createId.equals(that.createId) : that.createId != null) return false;
        if (createBy != null ? !createBy.equals(that.createBy) : that.createBy != null) return false;
        if (createTime != null ? !createTime.equals(that.createTime) : that.createTime != null) return false;
        if (modifyId != null ? !modifyId.equals(that.modifyId) : that.modifyId != null) return false;
        if (modifyBy != null ? !modifyBy.equals(that.modifyBy) : that.modifyBy != null) return false;
        if (modifyTime != null ? !modifyTime.equals(that.modifyTime) : that.modifyTime != null) return false;
        if (delFlag != null ? !delFlag.equals(that.delFlag) : that.delFlag != null) return false;

        return true;
    }

    @Override
    public int hashCode() {
        int result = (int) (detailId ^ (detailId >>> 32));
        result = 31 * result + (billId != null ? billId.hashCode() : 0);
        result = 31 * result + (orderNo != null ? orderNo.hashCode() : 0);
        result = 31 * result + (goodsPurchasePrice != null ? goodsPurchasePrice.hashCode() : 0);
        result = 31 * result + (goodsActualCount != null ? goodsActualCount.hashCode() : 0);
        result = 31 * result + (goodsSalePrice != null ? goodsSalePrice.hashCode() : 0);
        result = 31 * result + (goodsLife != null ? goodsLife.hashCode() : 0);
        result = 31 * result + (goodsProduceDate != null ? goodsProduceDate.hashCode() : 0);
        result = 31 * result + (subtotalMoney != null ? subtotalMoney.hashCode() : 0);
        result = 31 * result + (status != null ? status.hashCode() : 0);
        result = 31 * result + (checkStatus != null ? checkStatus.hashCode() : 0);
        result = 31 * result + (createId != null ? createId.hashCode() : 0);
        result = 31 * result + (createBy != null ? createBy.hashCode() : 0);
        result = 31 * result + (createTime != null ? createTime.hashCode() : 0);
        result = 31 * result + (modifyId != null ? modifyId.hashCode() : 0);
        result = 31 * result + (modifyBy != null ? modifyBy.hashCode() : 0);
        result = 31 * result + (modifyTime != null ? modifyTime.hashCode() : 0);
        result = 31 * result + (delFlag != null ? delFlag.hashCode() : 0);
        return result;
    }

    public String getGoodsImageUrl() {
        return goodsImageUrl;
    }

    public void setGoodsImageUrl(String goodsImageUrl) {
        this.goodsImageUrl = goodsImageUrl;
    }
}
