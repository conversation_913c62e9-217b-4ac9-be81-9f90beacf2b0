package org.haier.shop.entity.supplier;

import java.math.BigDecimal;

/**
 * @description 补货计划
 */
public class RestockPlanGoodsEntity {
    /**
    * id
    */
    private Long shopRestockplanGoodsId;

    /**
    * 店铺编号
    */
    private Long shopUnique;

    /**
     * 补货单id
     */
    private Long shopRestockplanId;

    /**
     * 供应商ID
     */
    private Long shopRestockplanPresentId;
    /**
     * 商品条码
     */
    private String goodsBarcode;
    /**
     * 商品数量
     */
    private BigDecimal goodsCount;
    /**
     * 商品进价
     */
    private BigDecimal goodsInPrice;

    public Long getShopRestockplanGoodsId() {
        return shopRestockplanGoodsId;
    }

    public void setShopRestockplanGoodsId(Long shopRestockplanGoodsId) {
        this.shopRestockplanGoodsId = shopRestockplanGoodsId;
    }

    public Long getShopRestockplanId() {
        return shopRestockplanId;
    }

    public void setShopRestockplanId(Long shopRestockplanId) {
        this.shopRestockplanId = shopRestockplanId;
    }

    public Long getShopUnique() {
        return shopUnique;
    }

    public void setShopUnique(Long shopUnique) {
        this.shopUnique = shopUnique;
    }

    public Long getShopRestockplanPresentId() {
        return shopRestockplanPresentId;
    }

    public void setShopRestockplanPresentId(Long shopRestockplanPresentId) {
        this.shopRestockplanPresentId = shopRestockplanPresentId;
    }

    public String getGoodsBarcode() {
        return goodsBarcode;
    }

    public void setGoodsBarcode(String goodsBarcode) {
        this.goodsBarcode = goodsBarcode;
    }

    public BigDecimal getGoodsCount() {
        return goodsCount;
    }

    public void setGoodsCount(BigDecimal goodsCount) {
        this.goodsCount = goodsCount;
    }

    public BigDecimal getGoodsInPrice() {
        return goodsInPrice;
    }

    public void setGoodsInPrice(BigDecimal goodsInPrice) {
        this.goodsInPrice = goodsInPrice;
    }
}