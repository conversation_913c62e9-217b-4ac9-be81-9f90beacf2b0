package org.haier.shop.entity;

import java.io.Serializable;

/**
 * 
 */
public class SqlPc implements Serializable{
	/**
	 * 
	 */
	private static final long serialVersionUID = 1L;
	private String shopUnique;
	private String datetime;
	private String sqlContext;
	private Long staffId;
	private Integer validType;
	private String broname;
	private String useIp;
	public String getShopUnique() {
		return shopUnique;
	}
	public void setShopUnique(String shopUnique) {
		this.shopUnique = shopUnique;
	}
	public String getDatetime() {
		return datetime;
	}
	public void setDatetime(String datetime) {
		this.datetime = datetime;
	}
	public String getSqlContext() {
		return sqlContext;
	}
	public void setSqlContext(String sqlContext) {
		this.sqlContext = sqlContext;
	}
	public Long getStaffId() {
		return staffId;
	}
	public void setStaffId(Long staffId) {
		this.staffId = staffId;
	}
	public Integer getValidType() {
		return validType;
	}
	public void setValidType(Integer validType) {
		this.validType = validType;
	}
	public String getBroname() {
		return broname;
	}
	public void setBroname(String broname) {
		this.broname = broname;
	}
	public String getUseIp() {
		return useIp;
	}
	public void setUseIp(String useIp) {
		this.useIp = useIp;
	}
	@Override
	public String toString() {
		return "SqlPc [shopUnique=" + shopUnique + ", datetime=" + datetime + ", sqlContext=" + sqlContext
				+ ", staffId=" + staffId + ", validType=" + validType + ", broname=" + broname + ", useIp=" + useIp
				+ "]";
	}
}
