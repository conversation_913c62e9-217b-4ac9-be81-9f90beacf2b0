package org.haier.shop.entity;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 退费记录
 * <AUTHOR> 
 * @ClassName CustomerRefunds
 * @Date 2024-04-15
 **/

public class CustomerRefunds implements Serializable {
	private static final long serialVersionUID = 6434735313274673207L;
	/**
	 * 退款记录表id
	 */
	private Integer id;

	/**
	* 会员id
	*/
	private Integer cusId;

	/**
	* 退款金额
	*/
	private BigDecimal refundsMoney;

	/**
	* 退款方式 1:现金 2:微信 3:支付宝 4：银行卡 5.其他
	*/
	private Integer refundsMethod;

	/**
	* 会员退款收款账户
	*/
	private String cusAccount;

	/**
	* 店铺id
	*/
	private Long shopUnique;

	/**
	* 操作ID
	*/
	private Long refundsCashier;

	/**
	* 核单员工id
	*/
	private Integer verifyStaffId;

	/**
	* 退款时间
	*/
	private Date createTime;

	/**
	* 备注
	*/
	private String remarks;

	/**
	* 退款赠额金额
	*/
	private BigDecimal refundsGivemoney;

	public Integer getId() {
		return id;
	}

	public void setId(Integer id) {
		this.id = id;
	}

	public Integer getCusId() {
		return cusId;
	}

	public void setCusId(Integer cusId) {
		this.cusId = cusId;
	}

	public BigDecimal getRefundsMoney() {
		return refundsMoney;
	}

	public void setRefundsMoney(BigDecimal refundsMoney) {
		this.refundsMoney = refundsMoney;
	}

	public Integer getRefundsMethod() {
		return refundsMethod;
	}

	public void setRefundsMethod(Integer refundsMethod) {
		this.refundsMethod = refundsMethod;
	}

	public String getCusAccount() {
		return cusAccount;
	}

	public void setCusAccount(String cusAccount) {
		this.cusAccount = cusAccount;
	}

	public Long getShopUnique() {
		return shopUnique;
	}

	public void setShopUnique(Long shopUnique) {
		this.shopUnique = shopUnique;
	}

	public Long getRefundsCashier() {
		return refundsCashier;
	}

	public void setRefundsCashier(Long refundsCashier) {
		this.refundsCashier = refundsCashier;
	}

	public Integer getVerifyStaffId() {
		return verifyStaffId;
	}

	public void setVerifyStaffId(Integer verifyStaffId) {
		this.verifyStaffId = verifyStaffId;
	}

	public Date getCreateTime() {
		return createTime;
	}

	public void setCreateTime(Date createTime) {
		this.createTime = createTime;
	}

	public String getRemarks() {
		return remarks;
	}

	public void setRemarks(String remarks) {
		this.remarks = remarks;
	}

	public BigDecimal getRefundsGivemoney() {
		return refundsGivemoney;
	}

	public void setRefundsGivemoney(BigDecimal refundsGivemoney) {
		this.refundsGivemoney = refundsGivemoney;
	}
}