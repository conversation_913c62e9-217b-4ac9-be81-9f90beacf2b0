package org.haier.shop.entity;

public class GoodsPacking {
	private Long goodsId;
	//商品编号
	private String goodsBarcode;
	//商品名称
	private String goodsName;
	//商品规格
	private String goodsStandard;
	//商品包装
	private String goodsUnit;
	//商品进价
	private Double goodsInPrice;
	//商品售价
	private Double goodsSalePrice;
	//商品规格关联关键字
	private String foreign_key;
	//包含销售商品最小单位数量
	private Integer containCount;
	//商品促销状态
	private Double goodsPromotion;
	 //商品折扣
	private Double goodsDiscount;
	//商品图片路径
	private String goodsPicturepath;
	//商品会员价
	private Double goodsCusPrice;
	//商品上架，下架状态
	private Integer shelfState;
	
	public Long getGoodsId() {
		return goodsId;
	}
	public void setGoodsId(Long goodsId) {
		this.goodsId = goodsId;
	}
	public String getGoodsBarcode() {
		return goodsBarcode;
	}
	public void setGoodsBarcode(String goodsBarcode) {
		this.goodsBarcode = goodsBarcode;
	}
	public String getGoodsName() {
		return goodsName;
	}
	public void setGoodsName(String goodsName) {
		this.goodsName = goodsName;
	}
	public String getGoodsStandard() {
		return goodsStandard;
	}
	public void setGoodsStandard(String goodsStandard) {
		this.goodsStandard = goodsStandard;
	}
	public String getGoodsUnit() {
		return goodsUnit;
	}
	public void setGoodsUnit(String goodsUnit) {
		this.goodsUnit = goodsUnit;
	}
	public Double getGoodsInPrice() {
		return goodsInPrice;
	}
	public void setGoodsInPrice(Double goodsInPrice) {
		this.goodsInPrice = goodsInPrice;
	}
	public Double getGoodsSalePrice() {
		return goodsSalePrice;
	}
	public void setGoodsSalePrice(Double goodsSalePrice) {
		this.goodsSalePrice = goodsSalePrice;
	}
	public String getForeign_key() {
		return foreign_key;
	}
	public void setForeign_key(String foreign_key) {
		this.foreign_key = foreign_key;
	}
	public Integer getContainCount() {
		return containCount;
	}
	public void setContainCount(Integer containCount) {
		this.containCount = containCount;
	}
	public Double getGoodsPromotion() {
		return goodsPromotion;
	}
	public void setGoodsPromotion(Double goodsPromotion) {
		this.goodsPromotion = goodsPromotion;
	}
	public Double getGoodsDiscount() {
		return goodsDiscount;
	}
	public void setGoodsDiscount(Double goodsDiscount) {
		this.goodsDiscount = goodsDiscount;
	}
	public String getGoodsPicturepath() {
		return goodsPicturepath;
	}
	public void setGoodsPicturepath(String goodsPicturepath) {
		this.goodsPicturepath = goodsPicturepath;
	}
	public Double getGoodsCusPrice() {
		return goodsCusPrice;
	}
	public void setGoodsCusPrice(Double goodsCusPrice) {
		this.goodsCusPrice = goodsCusPrice;
	}
	public Integer getShelfState() {
		return shelfState;
	}
	public void setShelfState(Integer shelfState) {
		this.shelfState = shelfState;
	}
	
	
}
