/**
 * 
 */
package org.haier.shop.entity;

/**
 * <AUTHOR>
 *
 */
public class TiCashVO {
	
	
	private String  beans_old_count;
	private String  payMoney;
	private String  moneyTop;
	private String  card_id;
	private String  shop_unique;
	private String  s_moeny;
	private String  m_moeny;
	private String  pt_tax;
	private String  tx_dtae;
	private String  tx_times;
	private String  s_cl_date;
	private String  m_cl_date;
	private String  receive_count;
	private String order_id;
	private String shop_balance;
	private double realMoney;
	private Integer give_count;
	
	public Integer getGive_count() {
		return give_count;
	}
	public void setGive_count(Integer give_count) {
		this.give_count = give_count;
	}
	public String getShop_balance() {
		return shop_balance;
	}
	public void setShop_balance(String shop_balance) {
		this.shop_balance = shop_balance;
	}
	public double getRealMoney() {
		return realMoney;
	}
	public void setRealMoney(double realMoney) {
		this.realMoney = realMoney;
	}
	public String getOrder_id() {
		return order_id;
	}
	public void setOrder_id(String order_id) {
		this.order_id = order_id;
	}
	public String getBeans_old_count() {
		return beans_old_count;
	}
	public void setBeans_old_count(String beans_old_count) {
		this.beans_old_count = beans_old_count;
	}
	public String getPayMoney() {
		return payMoney;
	}
	public void setPayMoney(String payMoney) {
		this.payMoney = payMoney;
	}
	public String getMoneyTop() {
		return moneyTop;
	}
	public void setMoneyTop(String moneyTop) {
		this.moneyTop = moneyTop;
	}
	public String getCard_id() {
		return card_id;
	}
	public void setCard_id(String card_id) {
		this.card_id = card_id;
	}
	public String getShop_unique() {
		return shop_unique;
	}
	public void setShop_unique(String shop_unique) {
		this.shop_unique = shop_unique;
	}
	public String getS_moeny() {
		return s_moeny;
	}
	public void setS_moeny(String s_moeny) {
		this.s_moeny = s_moeny;
	}
	public String getM_moeny() {
		return m_moeny;
	}
	public void setM_moeny(String m_moeny) {
		this.m_moeny = m_moeny;
	}
	public String getPt_tax() {
		return pt_tax;
	}
	public void setPt_tax(String pt_tax) {
		this.pt_tax = pt_tax;
	}
	public String getTx_dtae() {
		return tx_dtae;
	}
	public void setTx_dtae(String tx_dtae) {
		this.tx_dtae = tx_dtae;
	}
	public String getTx_times() {
		return tx_times;
	}
	public void setTx_times(String tx_times) {
		this.tx_times = tx_times;
	}
	public String getS_cl_date() {
		return s_cl_date;
	}
	public void setS_cl_date(String s_cl_date) {
		this.s_cl_date = s_cl_date;
	}
	public String getM_cl_date() {
		return m_cl_date;
	}
	public void setM_cl_date(String m_cl_date) {
		this.m_cl_date = m_cl_date;
	}
	public String getReceive_count() {
		return receive_count;
	}
	public void setReceive_count(String receive_count) {
		this.receive_count = receive_count;
	}
	@Override
	public String toString() {
		return "TiCashVO [beans_old_count=" + beans_old_count + ", payMoney=" + payMoney + ", moneyTop=" + moneyTop
				+ ", card_id=" + card_id + ", shop_unique=" + shop_unique + ", s_moeny=" + s_moeny + ", m_moeny="
				+ m_moeny + ", pt_tax=" + pt_tax + ", tx_dtae=" + tx_dtae + ", tx_times=" + tx_times + ", s_cl_date="
				+ s_cl_date + ", m_cl_date=" + m_cl_date + ", receive_count=" + receive_count + ", order_id=" + order_id
				+ ", shop_balance=" + shop_balance + ", realMoney=" + realMoney + "]";
	}



	
}
