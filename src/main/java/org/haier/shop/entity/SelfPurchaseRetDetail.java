package org.haier.shop.entity;

import java.math.BigDecimal;

/**
 * 退货订单商品明细
 * <AUTHOR> 
 * @ClassName SelfPurchaseRetDetail
 * @Date 2024-05-20
 **/
public class SelfPurchaseRetDetail {
	private Integer id;

	/**
	* 订单详情ID，采用这个值的原因是为了防止赠品导致goods_barcode重复
	*/
	private Long selfPurchaseDetailId;

	/**
	* 进货订单号
	*/
	private String selfPurchaseUnique;

	/**
	* 退货订单号
	*/
	private String selfPurchaseRetUnique;

	/**
	* 退货商品名称
	*/
	private String goodsName;

	/**
	* 退货商品编码
	*/
	private String goodsBarcode;

	/**
	* 退货商品退款时单价，可与进货时价格不同，但退款小计累计不应超过进货单价
	*/
	private BigDecimal goodsInPrice;

	/**
	* 退货数量，同一个订单累计退货数量不应超过进货数量
	*/
	private BigDecimal goodsCount;

	/**
	* 退货时商品单位，同进货
	*/
	private String unitName;

	/**
	* 是否赠品：1、非赠品；2、赠品
	*/
	private Integer giftType;

	public Integer getId() {
		return id;
	}

	public void setId(Integer id) {
		this.id = id;
	}

	public Long getSelfPurchaseDetailId() {
		return selfPurchaseDetailId;
	}

	public void setSelfPurchaseDetailId(Long selfPurchaseDetailId) {
		this.selfPurchaseDetailId = selfPurchaseDetailId;
	}

	public String getSelfPurchaseUnique() {
		return selfPurchaseUnique;
	}

	public void setSelfPurchaseUnique(String selfPurchaseUnique) {
		this.selfPurchaseUnique = selfPurchaseUnique;
	}

	public String getSelfPurchaseRetUnique() {
		return selfPurchaseRetUnique;
	}

	public void setSelfPurchaseRetUnique(String selfPurchaseRetUnique) {
		this.selfPurchaseRetUnique = selfPurchaseRetUnique;
	}

	public String getGoodsName() {
		return goodsName;
	}

	public void setGoodsName(String goodsName) {
		this.goodsName = goodsName;
	}

	public String getGoodsBarcode() {
		return goodsBarcode;
	}

	public void setGoodsBarcode(String goodsBarcode) {
		this.goodsBarcode = goodsBarcode;
	}

	public BigDecimal getGoodsInPrice() {
		return goodsInPrice;
	}

	public void setGoodsInPrice(BigDecimal goodsInPrice) {
		this.goodsInPrice = goodsInPrice;
	}

	public BigDecimal getGoodsCount() {
		return goodsCount;
	}

	public void setGoodsCount(BigDecimal goodsCount) {
		this.goodsCount = goodsCount;
	}

	public String getUnitName() {
		return unitName;
	}

	public void setUnitName(String unitName) {
		this.unitName = unitName;
	}

	public Integer getGiftType() {
		return giftType;
	}

	public void setGiftType(Integer giftType) {
		this.giftType = giftType;
	}
}