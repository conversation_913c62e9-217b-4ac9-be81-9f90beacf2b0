package org.haier.shop.entity.ret;

public class ReturnDetail {
	private Integer retListDetailId;
	private String saleListUnique;
	private String goodsBarcode;
	private String goodsName;
	private Double retListDetailCount;
	private Double retListDetailPrice;
	private Integer handleWay;
	private Double retListOriginPrice;
	private String retListUnique;
	private Integer rsaleListDetailId;
	private String imagePath;
	private String goodsStandard;
	private String goodsUnit;
	private Double saleListDetailCount;
	
	
	
	public Double getSaleListDetailCount() {
		return saleListDetailCount;
	}
	public void setSaleListDetailCount(Double saleListDetailCount) {
		this.saleListDetailCount = saleListDetailCount;
	}
	public String getGoodsStandard() {
		return goodsStandard;
	}
	public void setGoodsStandard(String goodsStandard) {
		this.goodsStandard = goodsStandard;
	}
	public String getGoodsUnit() {
		return goodsUnit;
	}
	public void setGoodsUnit(String goodsUnit) {
		this.goodsUnit = goodsUnit;
	}
	public String getImagePath() {
		return imagePath;
	}
	public void setImagePath(String imagePath) {
		this.imagePath = imagePath;
	}
	public Integer getRetListDetailId() {
		return retListDetailId;
	}
	public void setRetListDetailId(Integer retListDetailId) {
		this.retListDetailId = retListDetailId;
	}
	public String getSaleListUnique() {
		return saleListUnique;
	}
	public void setSaleListUnique(String saleListUnique) {
		this.saleListUnique = saleListUnique;
	}
	public String getGoodsBarcode() {
		return goodsBarcode;
	}
	public void setGoodsBarcode(String goodsBarcode) {
		this.goodsBarcode = goodsBarcode;
	}
	public String getGoodsName() {
		return goodsName;
	}
	public void setGoodsName(String goodsName) {
		this.goodsName = goodsName;
	}
	public Double getRetListDetailCount() {
		return retListDetailCount;
	}
	public void setRetListDetailCount(Double retListDetailCount) {
		this.retListDetailCount = retListDetailCount;
	}
	public Double getRetListDetailPrice() {
		return retListDetailPrice;
	}
	public void setRetListDetailPrice(Double retListDetailPrice) {
		this.retListDetailPrice = retListDetailPrice;
	}
	public Integer getHandleWay() {
		return handleWay;
	}
	public void setHandleWay(Integer handleWay) {
		this.handleWay = handleWay;
	}
	public Double getRetListOriginPrice() {
		return retListOriginPrice;
	}
	public void setRetListOriginPrice(Double retListOriginPrice) {
		this.retListOriginPrice = retListOriginPrice;
	}
	public String getRetListUnique() {
		return retListUnique;
	}
	public void setRetListUnique(String retListUnique) {
		this.retListUnique = retListUnique;
	}
	public Integer getRsaleListDetailId() {
		return rsaleListDetailId;
	}
	public void setRsaleListDetailId(Integer rsaleListDetailId) {
		this.rsaleListDetailId = rsaleListDetailId;
	}
}
