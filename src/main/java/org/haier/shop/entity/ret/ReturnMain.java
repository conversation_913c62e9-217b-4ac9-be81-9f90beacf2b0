package org.haier.shop.entity.ret;

import java.util.List;

public class ReturnMain {
	private Integer id;
	private String saleListUnique;
	private String shopUnique;
	private String retListDatetime;
	private Double retListTotal;
	private Double retListCount;
	private Integer retListState;
	private String retListStateMsg;
	private Integer retListHandlestate;
	private String retListHandlestateMsg;
	private String retListRemarks;
	private Integer staffId;
	private String macId;
	private Integer retOrigin;
	private String retOriginMsg;
	private Integer retMoneyType;
	private String retMoneyTypeMsg;
	private Double retListTotalMoney;
	private String retBackDatetime;
	private String retListUnique;
	private Integer retListBean;
	private String retListReason;
	private String retPayMsg;
	private String cusUnique;
	private Integer saleListHandlestate;
	private Double retListDelfee;
	private Double saleListTotal;
	private Double saleListDelfee;
	
	
	private List<ReturnDetail> detailList;
	private List<ReturnPayDetail> payDetailList;
	
	
	public Double getSaleListTotal() {
		return saleListTotal;
	}
	public void setSaleListTotal(Double saleListTotal) {
		this.saleListTotal = saleListTotal;
	}
	public Double getSaleListDelfee() {
		return saleListDelfee;
	}
	public void setSaleListDelfee(Double saleListDelfee) {
		this.saleListDelfee = saleListDelfee;
	}
	public Double getRetListDelfee() {
		return retListDelfee;
	}
	public void setRetListDelfee(Double retListDelfee) {
		this.retListDelfee = retListDelfee;
	}
	public Integer getSaleListHandlestate() {
		return saleListHandlestate;
	}
	public void setSaleListHandlestate(Integer saleListHandlestate) {
		this.saleListHandlestate = saleListHandlestate;
	}
	public String getCusUnique() {
		return cusUnique;
	}
	public void setCusUnique(String cusUnique) {
		this.cusUnique = cusUnique;
	}
	public Integer getId() {
		return id;
	}
	public void setId(Integer id) {
		this.id = id;
	}
	public String getSaleListUnique() {
		return saleListUnique;
	}
	public void setSaleListUnique(String saleListUnique) {
		this.saleListUnique = saleListUnique;
	}
	public String getShopUnique() {
		return shopUnique;
	}
	public void setShopUnique(String shopUnique) {
		this.shopUnique = shopUnique;
	}
	public String getRetListDatetime() {
		return retListDatetime;
	}
	public void setRetListDatetime(String retListDatetime) {
		this.retListDatetime = retListDatetime;
	}
	public Double getRetListTotal() {
		return retListTotal;
	}
	public void setRetListTotal(Double retListTotal) {
		this.retListTotal = retListTotal;
	}
	public Double getRetListCount() {
		return retListCount;
	}
	public void setRetListCount(Double retListCount) {
		this.retListCount = retListCount;
	}
	public Integer getRetListState() {
		return retListState;
	}
	public void setRetListState(Integer retListState) {
		this.retListState = retListState;
	}
	public String getRetListStateMsg() {
		return retListStateMsg;
	}
	public void setRetListStateMsg(String retListStateMsg) {
		this.retListStateMsg = retListStateMsg;
	}
	public Integer getRetListHandlestate() {
		return retListHandlestate;
	}
	public void setRetListHandlestate(Integer retListHandlestate) {
		this.retListHandlestate = retListHandlestate;
	}
	public String getRetListHandlestateMsg() {
		return retListHandlestateMsg;
	}
	public void setRetListHandlestateMsg(String retListHandlestateMsg) {
		this.retListHandlestateMsg = retListHandlestateMsg;
	}
	public String getRetListRemarks() {
		return retListRemarks;
	}
	public void setRetListRemarks(String retListRemarks) {
		this.retListRemarks = retListRemarks;
	}
	public Integer getStaffId() {
		return staffId;
	}
	public void setStaffId(Integer staffId) {
		this.staffId = staffId;
	}
	public String getMacId() {
		return macId;
	}
	public void setMacId(String macId) {
		this.macId = macId;
	}
	public List<ReturnDetail> getDetailList() {
		return detailList;
	}
	public void setDetailList(List<ReturnDetail> detailList) {
		this.detailList = detailList;
	}
	public List<ReturnPayDetail> getPayDetailList() {
		return payDetailList;
	}
	public void setPayDetailList(List<ReturnPayDetail> payDetailList) {
		this.payDetailList = payDetailList;
	}
	public Integer getRetOrigin() {
		return retOrigin;
	}
	public void setRetOrigin(Integer retOrigin) {
		this.retOrigin = retOrigin;
	}
	public String getRetOriginMsg() {
		return retOriginMsg;
	}
	public void setRetOriginMsg(String retOriginMsg) {
		this.retOriginMsg = retOriginMsg;
	}
	public Integer getRetMoneyType() {
		return retMoneyType;
	}
	public void setRetMoneyType(Integer retMoneyType) {
		this.retMoneyType = retMoneyType;
	}
	public String getRetMoneyTypeMsg() {
		return retMoneyTypeMsg;
	}
	public void setRetMoneyTypeMsg(String retMoneyTypeMsg) {
		this.retMoneyTypeMsg = retMoneyTypeMsg;
	}
	public Double getRetListTotalMoney() {
		return retListTotalMoney;
	}
	public void setRetListTotalMoney(Double retListTotalMoney) {
		this.retListTotalMoney = retListTotalMoney;
	}
	public String getRetBackDatetime() {
		return retBackDatetime;
	}
	public void setRetBackDatetime(String retBackDatetime) {
		this.retBackDatetime = retBackDatetime;
	}
	public String getRetListUnique() {
		return retListUnique;
	}
	public void setRetListUnique(String retListUnique) {
		this.retListUnique = retListUnique;
	}
	public Integer getRetListBean() {
		return retListBean;
	}
	public void setRetListBean(Integer retListBean) {
		this.retListBean = retListBean;
	}
	public String getRetListReason() {
		return retListReason;
	}
	public void setRetListReason(String retListReason) {
		this.retListReason = retListReason;
	}
	public String getRetPayMsg() {
		return retPayMsg;
	}
	public void setRetPayMsg(String retPayMsg) {
		this.retPayMsg = retPayMsg;
	}
}
