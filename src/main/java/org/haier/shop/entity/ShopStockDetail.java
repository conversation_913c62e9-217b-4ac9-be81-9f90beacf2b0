package org.haier.shop.entity;

import com.fasterxml.jackson.annotation.JsonFormat;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 库存主表
 * <AUTHOR> 
 * @ClassName ShopStockDetail
 * @Date 2024-05-01
 **/

public class ShopStockDetail {
	private Integer shopStockDetailId;

	/**
	* 店铺编号
	*/
	private String shopUnique;

	/**
	* 订单编号
	*/
	private String listUnique;

	/**
	* 来源单号
	*/
	private String sourceUnique;

	/**
	* 供货商编号
	*/
	private String supplierUnique;

	/**
	* 1:报损 2:退货 3:换货 4:初始化
	*/
	private Integer stockKind;

	/**
	* 0:未审核 1:审核通过 2:审核不通过
	*/
	private Integer auditStatus;

	/**
	* 审核人ID
	*/
	private Long auditId;

	/**
	* 审核时间
	*/
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
	private Date auditTime;

	/**
	* 审核内容
	*/
	private String auditContent;

	/**
	* 备注信息
	*/
	private String stockRemarks;

	/**
	* 出入库类型：1、入库；2、出库；
	*/
	private Integer stockType;

	/**
	* 1：手动出入库；2：销售订单出入库，3：进货订单出入库（出库为退货）；4：盘库
5:网上订单出库;6：寄存；7、云商采购;8:调拨;9、退货
	*/
	private Integer stockResource;

	/**
	* 操作来源：1、手机；2、PC端；3、web网页端；
4、小程序
	*/
	private Integer stockOrigin;

	/**
	* 操作员工编号
	*/
	private Long staffId;

	/**
	* 出入库时间
	*/
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
	private Date stockTime;

	/**
	* 总数量
	*/
	private BigDecimal totalCount;

	/**
	* 总金额
	*/
	private BigDecimal totalAmount;

	/**
	* 修改人ID
	*/
	private Long updateId;

	/**
	* 修改时间
	*/
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
	private Date updateTime;

	public Integer getShopStockDetailId() {
		return shopStockDetailId;
	}

	public void setShopStockDetailId(Integer shopStockDetailId) {
		this.shopStockDetailId = shopStockDetailId;
	}

	public String getShopUnique() {
		return shopUnique;
	}

	public void setShopUnique(String shopUnique) {
		this.shopUnique = shopUnique;
	}

	public String getListUnique() {
		return listUnique;
	}

	public void setListUnique(String listUnique) {
		this.listUnique = listUnique;
	}

	public String getSourceUnique() {
		return sourceUnique;
	}

	public void setSourceUnique(String sourceUnique) {
		this.sourceUnique = sourceUnique;
	}

	public String getSupplierUnique() {
		return supplierUnique;
	}

	public void setSupplierUnique(String supplierUnique) {
		this.supplierUnique = supplierUnique;
	}

	public Integer getStockKind() {
		return stockKind;
	}

	public void setStockKind(Integer stockKind) {
		this.stockKind = stockKind;
	}

	public Integer getAuditStatus() {
		return auditStatus;
	}

	public void setAuditStatus(Integer auditStatus) {
		this.auditStatus = auditStatus;
	}

	public Long getAuditId() {
		return auditId;
	}

	public void setAuditId(Long auditId) {
		this.auditId = auditId;
	}

	public Date getAuditTime() {
		return auditTime;
	}

	public void setAuditTime(Date auditTime) {
		this.auditTime = auditTime;
	}

	public String getAuditContent() {
		return auditContent;
	}

	public void setAuditContent(String auditContent) {
		this.auditContent = auditContent;
	}

	public String getStockRemarks() {
		return stockRemarks;
	}

	public void setStockRemarks(String stockRemarks) {
		this.stockRemarks = stockRemarks;
	}

	public Integer getStockType() {
		return stockType;
	}

	public void setStockType(Integer stockType) {
		this.stockType = stockType;
	}

	public Integer getStockResource() {
		return stockResource;
	}

	public void setStockResource(Integer stockResource) {
		this.stockResource = stockResource;
	}

	public Integer getStockOrigin() {
		return stockOrigin;
	}

	public void setStockOrigin(Integer stockOrigin) {
		this.stockOrigin = stockOrigin;
	}

	public Long getStaffId() {
		return staffId;
	}

	public void setStaffId(Long staffId) {
		this.staffId = staffId;
	}

	public Date getStockTime() {
		return stockTime;
	}

	public void setStockTime(Date stockTime) {
		this.stockTime = stockTime;
	}

	public BigDecimal getTotalCount() {
		return totalCount;
	}

	public void setTotalCount(BigDecimal totalCount) {
		this.totalCount = totalCount;
	}

	public BigDecimal getTotalAmount() {
		return totalAmount;
	}

	public void setTotalAmount(BigDecimal totalAmount) {
		this.totalAmount = totalAmount;
	}

	public Long getUpdateId() {
		return updateId;
	}

	public void setUpdateId(Long updateId) {
		this.updateId = updateId;
	}

	public Date getUpdateTime() {
		return updateTime;
	}

	public void setUpdateTime(Date updateTime) {
		this.updateTime = updateTime;
	}
}