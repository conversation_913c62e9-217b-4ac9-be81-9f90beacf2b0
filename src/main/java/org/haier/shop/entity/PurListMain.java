package org.haier.shop.entity;

import java.util.List;

public class PurListMain {
	//订单编号
	private Long purListUnique;
	//供货商名称
	private String supplierName;
	//下单时间
	private String purListDate;
	//订单总金额
	private Double purListTotal;
	//订单商品数量
	private Double purListSum;
	//订单支付状态
	private String payStatus;
	//订单处理状态
	private String receiptStatus;
	//订单支付方式
	private String payMethod;
	//订单商品详情
	private List<PurListDetail> listDetail;
	public Long getPurListUnique() {
		return purListUnique;
	}
	public void setPurListUnique(Long purListUnique) {
		this.purListUnique = purListUnique;
	}
	public String getSupplierName() {
		return supplierName;
	}
	public void setSupplierName(String supplierName) {
		this.supplierName = supplierName;
	}
	public String getPurListDate() {
		return purListDate;
	}
	public void setPurListDate(String purListDate) {
		this.purListDate = purListDate;
	}
	public Double getPurListTotal() {
		return purListTotal;
	}
	public void setPurListTotal(Double purListTotal) {
		this.purListTotal = purListTotal;
	}
	public Double getPurListSum() {
		return purListSum;
	}
	public void setPurListSum(Double purListSum) {
		this.purListSum = purListSum;
	}
	public String getPayStatus() {
		return payStatus;
	}
	public void setPayStatus(String payStatus) {
		this.payStatus = payStatus;
	}
	public String getReceiptStatus() {
		return receiptStatus;
	}
	public void setReceiptStatus(String receiptStatus) {
		this.receiptStatus = receiptStatus;
	}
	public String getPayMethod() {
		return payMethod;
	}
	public void setPayMethod(String payMethod) {
		this.payMethod = payMethod;
	}
	public List<PurListDetail> getListDetail() {
		return listDetail;
	}
	public void setListDetail(List<PurListDetail> listDetail) {
		this.listDetail = listDetail;
	}
	@Override
	public String toString() {
		return "PurListMain [purListUnique=" + purListUnique + ", supplierName=" + supplierName + ", purListDate="
				+ purListDate + ", purListTotal=" + purListTotal + ", purListSum=" + purListSum + ", payStatus="
				+ payStatus + ", receiptStatus=" + receiptStatus + ", payMethod=" + payMethod + ", listDetail="
				+ listDetail + "]";
	}
}
