package org.haier.shop.entity.dataScreen;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

public class AreaYN implements Serializable{
	
	/**
	 * 
	 */
	private static final long serialVersionUID = 1L;
	private String content;//区县名称
	private Integer cusCount;//县区内小程序注册量
	private List<ShopYN> list = new ArrayList<ShopYN>();
	public String getContent() {
		return content;
	}
	public void setContent(String content) {
		this.content = content;
	}
	public List<ShopYN> getList() {
		return list;
	}
	public void setList(List<ShopYN> list) {
		this.list = list;
	}
	public Integer getCusCount() {
		return cusCount;
	}
	public void setCusCount(Integer cusCount) {
		this.cusCount = cusCount;
	}
}
