package org.haier.shop.entity.dataScreen;

import java.io.Serializable;

public class ShopYN implements Serializable{
	
	/**
	 * 
	 */
	private static final long serialVersionUID = 1L;
	private String shopType;//店铺类型简易站，标准站，专业站，中心站
	private String typeName;//站点名称
	private Integer shopCount;//站点数量；
	public String getShopType() {
		return shopType;
	}
	public void setShopType(String shopType) {
		this.shopType = shopType;
	}
	public Integer getShopCount() {
		return shopCount;
	}
	public void setShopCount(Integer shopCount) {
		this.shopCount = shopCount;
	}
	public String getTypeName() {
		return typeName;
	}
	public void setTypeName(String typeName) {
		this.typeName = typeName;
	}
}
