package org.haier.shop.entity;
import java.util.List;
public class BaseGoods {
	//商品ID
	private int goodsId;
	//商品库存数量
	private Double goodsCount;
	//最小单位商品编码
	private String baseBarcode;
	//商品图片路径
	private String goodsPicturepath;
	//商品分类（一级分类名称）
	private String groupsName;
	//商品一级分类编号
	private String groupsUnique;
	//商品保质期
	private Integer goodsLife;
	//商品二级分类名称
	private String kindName;
	//商品二级分类编号
	private String kindUnique;
	//基础商品基本单位
	private String goodsUnit;
	//商品品牌
	private String goodsBrand;
	//商品产地
	private String goodsAddress;
	//商品备注信息
	private String goodsRemarks;
	//商品销量
	private Double  goodsSold;
	//商品供货商编号
	private Long supplierUnique;
	//供货商名称
	private String supplierName;
	//供货价
	private Double goodsPrice;
	//供货商供货条码
	private String supGoodsBarcode;
	//上架状态
	private Integer tableType;
	//商品规格条码
	private List<GoodsPacking> listDetail;
	//包装外键
	private String foreignKey;
	//缺货提醒类型
	private Integer 	out_stock_remind_type;
	//最大库存量
	private Integer unsalable_count;
	//提醒库存量
	private Integer out_stock_waring_count;
	//自动进货库存量
	private Integer out_stock_count;
	
	
	public Integer getOut_stock_remind_type() {
		return out_stock_remind_type;
	}
	public void setOut_stock_remind_type(Integer out_stock_remind_type) {
		this.out_stock_remind_type = out_stock_remind_type;
	}
	public Integer getUnsalable_count() {
		return unsalable_count;
	}
	public void setUnsalable_count(Integer unsalable_count) {
		this.unsalable_count = unsalable_count;
	}
	public Integer getOut_stock_waring_count() {
		return out_stock_waring_count;
	}
	public void setOut_stock_waring_count(Integer out_stock_waring_count) {
		this.out_stock_waring_count = out_stock_waring_count;
	}
	public Integer getOut_stock_count() {
		return out_stock_count;
	}
	public void setOut_stock_count(Integer out_stock_count) {
		this.out_stock_count = out_stock_count;
	}
	public Double getGoodsCount() {
		return goodsCount;
	}
	public void setGoodsCount(Double goodsCount) {
		this.goodsCount = goodsCount;
	}
	public String getGroupsName() {
		return groupsName;
	}
	public void setGroupsName(String groupsName) {
		this.groupsName = groupsName;
	}
	public String getGroupsUnique() {
		return groupsUnique;
	}
	public void setGroupsUnique(String groupsUnique) {
		this.groupsUnique = groupsUnique;
	}
	public String getKindName() {
		return kindName;
	}
	public void setKindName(String kindName) {
		this.kindName = kindName;
	}
	public String getKindUnique() {
		return kindUnique;
	}
	public void setKindUnique(String kindUnique) {
		this.kindUnique = kindUnique;
	}
	public String getGoodsBrand() {
		return goodsBrand;
	}
	public void setGoodsBrand(String goodsBrand) {
		this.goodsBrand = goodsBrand;
	}
	public String getGoodsAddress() {
		return goodsAddress;
	}
	public void setGoodsAddress(String goodsAddress) {
		this.goodsAddress = goodsAddress;
	}
	public String getGoodsRemarks() {
		return goodsRemarks;
	}
	public void setGoodsRemarks(String goodsRemarks) {
		this.goodsRemarks = goodsRemarks;
	}
	public Double getGoodsSold() {
		return goodsSold;
	}
	public void setGoodsSold(Double goodsSold) {
		this.goodsSold = goodsSold;
	}
	public Long getSupplierUnique() {
		return supplierUnique;
	}
	public void setSupplierUnique(Long supplierUnique) {
		this.supplierUnique = supplierUnique;
	}
	public String getSupplierName() {
		return supplierName;
	}
	public void setSupplierName(String supplierName) {
		this.supplierName = supplierName;
	}
	public Double getGoodsPrice() {
		return goodsPrice;
	}
	public void setGoodsPrice(Double goodsPrice) {
		this.goodsPrice = goodsPrice;
	}
	public String getSupGoodsBarcode() {
		return supGoodsBarcode;
	}
	public void setSupGoodsBarcode(String supGoodsBarcode) {
		this.supGoodsBarcode = supGoodsBarcode;
	}
	public List<GoodsPacking> getListDetail() {
		return listDetail;
	}
	public void setListDetail(List<GoodsPacking> listDetail) {
		this.listDetail = listDetail;
	}
	public String getGoodsPicturepath() {
		return goodsPicturepath;
	}
	public void setGoodsPicturepath(String goodsPicturepath) {
		this.goodsPicturepath = goodsPicturepath;
	}
	public int getGoodsId() {
		return goodsId;
	}
	public void setGoodsId(int goodsId) {
		this.goodsId = goodsId;
	}
	public String getForeignKey() {
		return foreignKey;
	}
	public void setForeignKey(String foreignKey) {
		this.foreignKey = foreignKey;
	}
	public String getBaseBarcode() {
		return baseBarcode;
	}
	public void setBaseBarcode(String baseBarcode) {
		this.baseBarcode = baseBarcode;
	}
	public String getGoodsUnit() {
		return goodsUnit;
	}
	public void setGoodsUnit(String goodsUnit) {
		this.goodsUnit = goodsUnit;
	}
	public Integer getGoodsLife() {
		return goodsLife;
	}
	public Integer getTableType() {
		return tableType;
	}
	public void setTableType(Integer tableType) {
		this.tableType = tableType;
	}
	public void setGoodsLife(Integer goodsLife) {
		this.goodsLife = goodsLife;
	}
	@Override
	public String toString() {
		return "BaseGoods [goodsId=" + goodsId + ", goodsCount=" + goodsCount + ", baseBarcode=" + baseBarcode
				+ ", goodsPicturepath=" + goodsPicturepath + ", groupsName=" + groupsName + ", groupsUnique="
				+ groupsUnique + ", goodsLife=" + goodsLife + ", kindName=" + kindName + ", kindUnique=" + kindUnique
				+ ", goodsUnit=" + goodsUnit + ", goodsBrand=" + goodsBrand + ", goodsAddress=" + goodsAddress
				+ ", goodsRemarks=" + goodsRemarks + ", goodsSold=" + goodsSold + ", supplierUnique=" + supplierUnique
				+ ", supplierName=" + supplierName + ", goodsPrice=" + goodsPrice + ", supGoodsBarcode="
				+ supGoodsBarcode + ", listDetail=" + listDetail + ", foreignKey=" + foreignKey + "]";
	}
}
