package org.haier.shop.entity;
import java.io.Serializable;
import java.util.List;

import org.haier.shop.entity.beans.PageData;
/**
 * 金圈币
 * <AUTHOR>
 */
public class GoldVO implements Serializable{
	
	private static final long serialVersionUID = 2596954780945079591L;
	private String id;
	private String shop_unique;
	private String shop_name;
	private String device_num;
	private String jqb_count;
	private String jqb_use_count;
	private String 	jqb_give_count;
	private String create_time;
	private String order_type;
	private List<PageData> deviceList;

	public String getOrder_type() {
		return order_type;
	}

	public void setOrder_type(String order_type) {
		this.order_type = order_type;
	}

	public String getId() {
		return id;
	}

	public void setId(String id) {
		this.id = id;
	}

	public String getShop_unique() {
		return shop_unique;
	}

	public void setShop_unique(String shop_unique) {
		this.shop_unique = shop_unique;
	}

	public String getShop_name() {
		return shop_name;
	}

	public void setShop_name(String shop_name) {
		this.shop_name = shop_name;
	}

	public String getDevice_num() {
		return device_num;
	}

	public void setDevice_num(String device_num) {
		this.device_num = device_num;
	}

	public String getJqb_count() {
		return jqb_count;
	}

	public void setJqb_count(String jqb_count) {
		this.jqb_count = jqb_count;
	}

	public String getJqb_use_count() {
		return jqb_use_count;
	}

	public void setJqb_use_count(String jqb_use_count) {
		this.jqb_use_count = jqb_use_count;
	}

	public String getJqb_give_count() {
		return jqb_give_count;
	}

	public void setJqb_give_count(String jqb_give_count) {
		this.jqb_give_count = jqb_give_count;
	}

	public List<PageData> getDeviceList() {
		return deviceList;
	}

	public void setDeviceList(List<PageData> deviceList) {
		this.deviceList = deviceList;
	}

	public String getCreate_time() {
		return create_time;
	}

	public void setCreate_time(String create_time) {
		this.create_time = create_time;
	}

	

	
}
