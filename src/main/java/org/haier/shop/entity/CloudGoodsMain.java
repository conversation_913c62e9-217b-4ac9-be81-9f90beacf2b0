package org.haier.shop.entity;

import java.util.List;
import java.util.Map;

/**
 * 商品信息
 * <AUTHOR>
 */
public class CloudGoodsMain {
	//商品外键
	private String foreignKey;
	//小类信息
	private String kindUnique;
	//商品分类
	private String goodsBrand;
	//小类名称
	private String kindName;
	//大类编号
	private String groupUnique;
	//大类名称
	private String groupName;
	//备注信息
	private String goodsRemarks;
	//是否本地商品
	private Integer haveType;
	//库存数量
	private Long goodsCount;
	//商品的预警库存值
	private Double leastStockCount;
	//商品详情
	private List<CloudGoodsDetail> listDetail;
	//赠送商品起售数量
	private  Double goodsCountForPromotion;
	//赠品商品条码
	private String presentBarcode;
	//赠品商品名称
	private String pGoodsName;
	//赠品商品数量
	private Double presentCount;
	//售价和网购价是否同步
	private Integer sameType;
	//称重商品类型：0、按件；1、按重量
	private Integer goodsChengType;
	private Integer kindT;
	
	private String defaultSupplierUnique;
	//自动采购信息
	private List<Map<String ,Object>> auto;
	
	//最低库存量，若库存量低于该值，自动进货
	private int outStockCount;
	private String goodsPicturePathA;
	private String goodsPicturePathB;
	private String goodsPicturePathC;
	private String sameTime;
	private Integer goodsLife;

	public String getGoodsPicturePathA() {
		return goodsPicturePathA;
	}
	public void setGoodsPicturePathA(String goodsPicturePathA) {
		this.goodsPicturePathA = goodsPicturePathA;
	}
	public String getGoodsPicturePathB() {
		return goodsPicturePathB;
	}
	public void setGoodsPicturePathB(String goodsPicturePathB) {
		this.goodsPicturePathB = goodsPicturePathB;
	}
	public String getGoodsPicturePathC() {
		return goodsPicturePathC;
	}
	public void setGoodsPicturePathC(String goodsPicturePathC) {
		this.goodsPicturePathC = goodsPicturePathC;
	}
	public String getDefaultSupplierUnique() {
		return defaultSupplierUnique;
	}
	public void setDefaultSupplierUnique(String defaultSupplierUnique) {
		this.defaultSupplierUnique = defaultSupplierUnique;
	}
	public Integer getGoodsChengType() {
		return goodsChengType;
	}
	public void setGoodsChengType(Integer goodsChengType) {
		this.goodsChengType = goodsChengType;
	}
	public int getOutStockCount() {
		return outStockCount;
	}
	public void setOutStockCount(int outStockCount) {
		this.outStockCount = outStockCount;
	}
	public List<Map<String ,Object>> getAuto() {
		return auto;
	}
	public void setAuto(List<Map<String ,Object>> auto) {
		this.auto = auto;
	}
	public String getForeignKey() {
		return foreignKey;
	}
	public void setForeignKey(String foreignKey) {
		this.foreignKey = foreignKey;
	}
	public Integer getSameType() {
		return sameType;
	}
	public void setSameType(Integer sameType) {
		this.sameType = sameType;
	}
	public String getKindUnique() {
		return kindUnique;
	}
	public void setKindUnique(String kindUnique) {
		this.kindUnique = kindUnique;
	}
	public String getKindName() {
		return kindName;
	}
	public void setKindName(String kindName) {
		this.kindName = kindName;
	}
	public String getGroupUnique() {
		return groupUnique;
	}
	public void setGroupUnique(String groupUnique) {
		this.groupUnique = groupUnique;
	}
	public String getGroupName() {
		return groupName;
	}
	public void setGroupName(String groupName) {
		this.groupName = groupName;
	}
	public String getGoodsRemarks() {
		return goodsRemarks;
	}
	public void setGoodsRemarks(String goodsRemarks) {
		this.goodsRemarks = goodsRemarks;
	}
	public List<CloudGoodsDetail> getListDetail() {
		return listDetail;
	}
	public void setListDetail(List<CloudGoodsDetail> listDetail) {
		this.listDetail = listDetail;
	}
	public String getGoodsBrand() {
		return goodsBrand;
	}
	public void setGoodsBrand(String goodsBrand) {
		this.goodsBrand = goodsBrand;
	}
	public Integer getHaveType() {
		return haveType;
	}
	public void setHaveType(Integer haveType) {
		this.haveType = haveType;
	}
	public Long getGoodsCount() {
		return goodsCount;
	}
	public void setGoodsCount(Long goodsCount) {
		this.goodsCount = goodsCount;
	}
	public Double getGoodsCountForPromotion() {
		return goodsCountForPromotion;
	}
	public void setGoodsCountForPromotion(Double goodsCountForPromotion) {
		this.goodsCountForPromotion = goodsCountForPromotion;
	}
	public String getPresentBarcode() {
		return presentBarcode;
	}
	public void setPresentBarcode(String presentBarcode) {
		this.presentBarcode = presentBarcode;
	}
	public String getpGoodsName() {
		return pGoodsName;
	}
	public void setpGoodsName(String pGoodsName) {
		this.pGoodsName = pGoodsName;
	}
	public Double getPresentCount() {
		return presentCount;
	}
	public void setPresentCount(Double presentCount) {
		this.presentCount = presentCount;
	}
	public Double getLeastStockCount() {
		return leastStockCount;
	}
	public void setLeastStockCount(Double leastStockCount) {
		this.leastStockCount = leastStockCount;
	}
	public Integer getKindT() {
		return kindT;
	}
	public void setKindT(Integer kindT) {
		this.kindT = kindT;
	}

	public String getSameTime() {
		return sameTime;
	}

	public void setSameTime(String sameTime) {
		this.sameTime = sameTime;
	}

	public Integer getGoodsLife() {
		return goodsLife;
	}

	public void setGoodsLife(Integer goodsLife) {
		this.goodsLife = goodsLife;
	}

	@Override
	public String toString() {
		return "CloudGoodsMain [foreignKey=" + foreignKey + ", kindUnique=" + kindUnique + ", goodsBrand=" + goodsBrand
				+ ", kindName=" + kindName + ", groupUnique=" + groupUnique + ", groupName=" + groupName
				+ ", goodsRemarks=" + goodsRemarks + ", haveType=" + haveType + ", goodsCount=" + goodsCount
				+ ", leastStockCount=" + leastStockCount + ", listDetail=" + listDetail + ", goodsCountForPromotion="
				+ goodsCountForPromotion + ", presentBarcode=" + presentBarcode + ", pGoodsName=" + pGoodsName
				+ ", presentCount=" + presentCount + ", kindT=" + kindT + ", auto=" + auto + ", outStockCount="
				+ outStockCount + "]";
	}
}
