package org.haier.shop.entity;

/**
 * 店铺订单量达到指定数量后，给出的提示信息
 * <AUTHOR>
 *
 */
public class ListPrompt {
	private Integer id;//ID
	private Integer listCount;//达到的订单数量
	private String promptInfo;//提示的信息
	private Integer staffId;//维护者员工编号
	private String staffName;//维护者名称
	private String createTime;//创建时间
	private String remarks;//备注信息
	private Integer validType;//有效性标识
	public Integer getId() {
		return id;
	}
	public void setId(Integer id) {
		this.id = id;
	}
	public Integer getListCount() {
		return listCount;
	}
	public void setListCount(Integer listCount) {
		this.listCount = listCount;
	}
	public String getPromptInfo() {
		return promptInfo;
	}
	public void setPromptInfo(String promptInfo) {
		this.promptInfo = promptInfo;
	}
	public Integer getStaffId() {
		return staffId;
	}
	public void setStaffId(Integer staffId) {
		this.staffId = staffId;
	}
	public String getStaffName() {
		return staffName;
	}
	public void setStaffName(String staffName) {
		this.staffName = staffName;
	}
	public String getCreateTime() {
		return createTime;
	}
	public void setCreateTime(String createTime) {
		this.createTime = createTime;
	}
	public String getRemarks() {
		return remarks;
	}
	public void setRemarks(String remarks) {
		this.remarks = remarks;
	}
	public Integer getValidType() {
		return validType;
	}
	public void setValidType(Integer validType) {
		this.validType = validType;
	}
	@Override
	public String toString() {
		return "ListPrompt [id=" + id + ", listCount=" + listCount + ", promptInfo=" + promptInfo + ", staffId="
				+ staffId + ", staffName=" + staffName + ", createTime=" + createTime + ", remarks=" + remarks
				+ ", validType=" + validType + "]";
	}
}
