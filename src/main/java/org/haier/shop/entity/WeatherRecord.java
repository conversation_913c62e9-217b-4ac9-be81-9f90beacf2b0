package org.haier.shop.entity;

public class WeatherRecord {
	//编号
	private Integer id;
	//城市编号
	private String areaDictNum;
	//城市名称
	private String cityName;
	//查询日期
	private String date;
	//查询星期
	private String week;
	//天气状况
	private String weather;
	//最高气温；
	private Double temphigh;
	//最低气温
	private Double templow;
	//相对湿度
	private Double humidity;
	//相对气压
	private Double pressure;
	//风速
	private Double windspeed;
	//风向；
	private String winddirect;
	//风力
	private String windpower;
	public Integer getId() {
		return id;
	}
	public void setId(Integer id) {
		this.id = id;
	}
	public void setAreaDictNum(String areaDictNum) {
		this.areaDictNum = areaDictNum;
	}
	public String getAreaDictNum() {
		return areaDictNum;
	}
	public String getCityName() {
		return cityName;
	}
	public void setCityName(String cityName) {
		this.cityName = cityName;
	}
	public String getDate() {
		return date;
	}
	public void setDate(String date) {
		this.date = date;
	}
	public String getWeek() {
		return week;
	}
	public void setWeek(String week) {
		this.week = week;
	}
	public String getWeather() {
		return weather;
	}
	public void setWeather(String weather) {
		this.weather = weather;
	}
	public Double getTemphigh() {
		return temphigh;
	}
	public void setTemphigh(Double temphigh) {
		this.temphigh = temphigh;
	}
	public Double getTemplow() {
		return templow;
	}
	public void setTemplow(Double templow) {
		this.templow = templow;
	}
	public Double getHumidity() {
		return humidity;
	}
	public void setHumidity(Double humidity) {
		this.humidity = humidity;
	}
	public Double getPressure() {
		return pressure;
	}
	public void setPressure(Double pressure) {
		this.pressure = pressure;
	}
	public Double getWindspeed() {
		return windspeed;
	}
	public void setWindspeed(Double windspeed) {
		this.windspeed = windspeed;
	}
	public String getWinddirect() {
		return winddirect;
	}
	public void setWinddirect(String winddirect) {
		this.winddirect = winddirect;
	}
	public String getWindpower() {
		return windpower;
	}
	public void setWindpower(String windpower) {
		this.windpower = windpower;
	}
	@Override
	public String toString() {
		return "WeatherRecord [id=" + id + ", areaDictNum=" + areaDictNum + ", cityName=" + cityName + ", date=" + date
				+ ", week=" + week + ", weather=" + weather + ", temphigh=" + temphigh + ", templow=" + templow
				+ ", humidity=" + humidity + ", pressure=" + pressure + ", windspeed=" + windspeed + ", winddirect="
				+ winddirect + ", windpower=" + windpower + "]";
	}
}
