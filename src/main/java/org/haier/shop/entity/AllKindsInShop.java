package org.haier.shop.entity;

import java.util.List;

public class AllKindsInShop {
	//分类编号
	private String groupUnique;
	//分类名称
	private String groupName;
	//子分类详情
	private List<AllDetailKindsInShop> listDetail;
	public String getGroupUnique() {
		return groupUnique;
	}
	public void setGroupUnique(String groupUnique) {
		this.groupUnique = groupUnique;
	}
	public String getGroupName() {
		return groupName;
	}
	public void setGroupName(String groupName) {
		this.groupName = groupName;
	}
	public List<AllDetailKindsInShop> getListDetail() {
		return listDetail;
	}
	public void setListDetail(List<AllDetailKindsInShop> listDetail) {
		this.listDetail = listDetail;
	}
	@Override
	public String toString() {
		return "AllKindsInShop [groupUnique=" + groupUnique + ", groupName=" + groupName + ", listDetail=" + listDetail
				+ "]";
	}
}
