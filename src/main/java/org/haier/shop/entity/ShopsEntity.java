package org.haier.shop.entity;

import java.math.BigDecimal;
import java.util.Date;

/**
 * @description 超市信息表,记录超市的基本注册信息,地理信息,店铺简介,店铺相关的其他相对固定信息
 */
public class ShopsEntity {
    /**
    * id
    */
    private Integer shopId;

    /**
    * 唯一标识
    */
    private Long shopUnique;

    /**
    * 超市名称
    */
    private String shopName;

    /**
    * 店铺审核状态：1，未提交申请；2，已提交申请；3，审核未通过；4，审核通过；5：已撤回
    */
    private Integer examinestatus;

    /**
    * 店铺类型:1、便利店；2、水果店；3、母婴店；4、益农中心站；5、益农标准站、简易站、专业站；6宁宇总店、7宁宇分店、8五金店 9学校、10、机关食堂0：其他 11:加油站；12、餐饮店；13、拼团店
    */
    private Integer shopType;

    /**
    * 管理员唯一标识符
    */
    private String managerUnique;

    /**
    * 店铺管理员账号
    */
    private String managerAccount;

    /**
    * 店铺管理员密码
    */
    private String managerPwd;

    /**
    * 超市首字母缩写
    */
    private String shopAlias;

    /**
    * 超市所在商圈
    */
    private String tradeAreaUnique;

    /**
    * 店铺所在纬度
    */
    private BigDecimal shopLatitude;

    /**
    * 店铺所在经度
    */
    private BigDecimal shopLongitude;

    /**
    * 百度纬度
    */
    private BigDecimal shopLatitudeBaidu;

    /**
    * 百度经度
    */
    private BigDecimal shopLongitudeBaidu;

    /**
    * 卖家帐号
    */
    private String shopSellerEmail;

    /**
    * 超市详细地址
    */
    private String shopAddressDetail;

    /**
    * 联系电话
    */
    private String shopPhone;

    /**
    * 超市备注
    */
    private String shopRemark;

    /**
    * 超市图片路径
    */
    private String shopImagePath;

    /**
    * 合作身份者id，签约账号
    */
    private String shopPartner;

    /**
    * 支付宝私钥
    */
    private String alipayPrivateKey;

    /**
    * 支付宝公钥
    */
    private String alipayPublicKey;

    /**
    * 微信支付开发平台id
    */
    private String shopAppId;

    /**
    * 微信支付商业号mch_id
    */
    private String shopMchId;

    /**
    * 微信支付api_key
    */
    private String shopApiKey;

    /**
    * 商铺所在区县编号
    */
    private String areaDictNum;

    /**
    * 店铺设置，供货端是否可以查看店铺销售信息，1：默认能查看；2：不可以查看
    */
    private Integer powerSupplier;

    /**
    * 店铺审核不通过原因
    */
    private String examinestatusReason;

    /**
    * 营业时间
    */
    private String shopHours;

    /**
    * 店铺营业状态：1营业 2不营业
    */
    private Integer shopStatus;

    /**
    * 店铺计数器-用于统计每天的订单数，0点清零
    */
    private Integer shopCounter;

    /**
    * 推送id
    */
    private String channelId;

    /**
    * 登录来源：1、pc；4、ios；3、andriod
    */
    private Integer shopSource;

    /**
    * 1：pc已同步；2：pc未同步
    */
    private Integer sametype;

    /**
    * 管理员名称
    */
    private String managerName;

    /**
    * 低于成本价销售：1、允许，2、不允许
    */
    private Integer belowCostSale;

    /**
    * 电信翼支付店铺编号
    */
    private Integer storeId;

    /**
    * 配送范围，单位米
    */
    private Integer distributionScope;

    /**
    * 创建时间
    */
    private Date registrationDate;

    /**
    * 0：不是自营 1:自营
    */
    private Integer shopsType;

    /**
    * 美团外卖门店映射绑定授权token
    */
    private String takeoutAppauthtoken;

    /**
    * 饿了么店铺授权token
    */
    private String eleToken;

    /**
    * token失效时间
    */
    private Date eleTokenFailure;

    /**
    * 刷新token
    */
    private String eleRefreshToken;

    /**
    * 饿了么店铺id
    */
    private String eleShopid;

    /**
    * 配送方式：0自配送 1美团配送 2一刻钟配送
    */
    private Integer deliveryType;

    /**
    * 极光推送id
    */
    private String registrationId;

    /**
    * 系统类型 1:ios 2:android
    */
    private Integer registrationPhoneType;

    /**
    * 店铺评分
    */
    private BigDecimal shopScore;

    /**
    * 店铺关注数量
    */
    private Integer shopAttention;

    /**
    * 店铺公告
    */
    private String shopAnnouncement;

    /**
    * 起送费（元）
    */
    private BigDecimal takeFee;

    /**
    * 免配送费价格（元）
    */
    private BigDecimal takeFreePrice;

    /**
    * 配送预计时长（分钟）
    */
    private Integer takeEstimateTime;

    /**
    * 商家自配送配送费
    */
    private BigDecimal shopTakePrice;

    /**
    * 是否自动接单：0是 1否
    */
    private Integer isOrderTaking;

    /**
    * 人脸支付 0：未开通 1:已开通
    */
    private Integer facePayStatus;

    /**
    * 剩余百货豆
    */
    private Long shopBeans;

    /**
    * 剩余金额
    */
    private BigDecimal shopBalance;

    /**
    * 0、未开通（未签订协议）；1、开通
    */
    private Integer beansAgreement;

    /**
    * 百货豆不足发送短信状态0:未发送 1:已发送
    */
    private Integer beansSendMsgStatus;

    /**
    * 关联供货商注册code
    */
    private String registerCode;

    /**
    * 0 关闭自动补货 1开启自动补货
    */
    private Integer autoPurchase;

    /**
    * 注册使用的名称
    */
    private String loginName;

    /**
    * 分类类型：1、公共分类；2、自定义分类；
    */
    private Integer kindType;

    /**
    * 店铺分类：0普通商家；1：连锁；2加盟 3系统平台 4普通商家分店 5代理商店铺
    */
    private Integer shopClass;

    /**
    * 连锁加盟总店code，关联供货商数据库zz_sup_companyinfo，普通商家为空
    */
    private String companyCode;

    /**
    * 连锁加盟店是否允许像其他供货商采购：0不允许 1允许
    */
    private String isOtherPurchase;

    /**
    * 微信小程序开通状态：0:不显示 1:显示 2:审核中 3:审核失败
    */
    private Integer showBuyStatus;

    /**
    * 供货商赠送的百货豆
    */
    private Integer supGiveBeans;

    /**
    * 微信小程序审核失败原因
    */
    private String showBuyFailReason;

    /**
    * 开启代收快递：1不开启 2开启
    */
    private Integer isCollectingExpress;

    /**
    * 开启代送快递：1不开启 2开启
    */
    private Integer isSendExpress;

    /**
    * 每单补贴配送费
    */
    private BigDecimal subsidyDeliveryPrice;

    /**
    * 代理商编码
    */
    private Integer agencyCode;

    /**
    * 是否开启分销：1不开启 2开启
    */
    private Integer isDis;

    /**
    * 店铺拉卡拉余额
    */
    private BigDecimal lklBalance;

    /**
    * 连锁店会员同步状态 0:不同步 1:同步
    */
    private Integer cusSynchroStatus;

    /**
    * 是否开启代扣 1:是 2:否
    */
    private Integer isDaikou;

    /**
    * 订单倒计时时间(秒)
    */
    private Integer orderTime;

    /**
    * 安卓收银机极光推送id
    */
    private String pcRegistrationId;

    /**
    * 地图街道信息code
    */
    private String townCode;

    public Integer getShopId() {
        return shopId;
    }

    public void setShopId(Integer shopId) {
        this.shopId = shopId;
    }

    public Long getShopUnique() {
        return shopUnique;
    }

    public void setShopUnique(Long shopUnique) {
        this.shopUnique = shopUnique;
    }

    public String getShopName() {
        return shopName;
    }

    public void setShopName(String shopName) {
        this.shopName = shopName;
    }

    public Integer getExaminestatus() {
        return examinestatus;
    }

    public void setExaminestatus(Integer examinestatus) {
        this.examinestatus = examinestatus;
    }

    public Integer getShopType() {
        return shopType;
    }

    public void setShopType(Integer shopType) {
        this.shopType = shopType;
    }

    public String getManagerUnique() {
        return managerUnique;
    }

    public void setManagerUnique(String managerUnique) {
        this.managerUnique = managerUnique;
    }

    public String getManagerAccount() {
        return managerAccount;
    }

    public void setManagerAccount(String managerAccount) {
        this.managerAccount = managerAccount;
    }

    public String getManagerPwd() {
        return managerPwd;
    }

    public void setManagerPwd(String managerPwd) {
        this.managerPwd = managerPwd;
    }

    public String getShopAlias() {
        return shopAlias;
    }

    public void setShopAlias(String shopAlias) {
        this.shopAlias = shopAlias;
    }

    public String getTradeAreaUnique() {
        return tradeAreaUnique;
    }

    public void setTradeAreaUnique(String tradeAreaUnique) {
        this.tradeAreaUnique = tradeAreaUnique;
    }

    public BigDecimal getShopLatitude() {
        return shopLatitude;
    }

    public void setShopLatitude(BigDecimal shopLatitude) {
        this.shopLatitude = shopLatitude;
    }

    public BigDecimal getShopLongitude() {
        return shopLongitude;
    }

    public void setShopLongitude(BigDecimal shopLongitude) {
        this.shopLongitude = shopLongitude;
    }

    public BigDecimal getShopLatitudeBaidu() {
        return shopLatitudeBaidu;
    }

    public void setShopLatitudeBaidu(BigDecimal shopLatitudeBaidu) {
        this.shopLatitudeBaidu = shopLatitudeBaidu;
    }

    public BigDecimal getShopLongitudeBaidu() {
        return shopLongitudeBaidu;
    }

    public void setShopLongitudeBaidu(BigDecimal shopLongitudeBaidu) {
        this.shopLongitudeBaidu = shopLongitudeBaidu;
    }

    public String getShopSellerEmail() {
        return shopSellerEmail;
    }

    public void setShopSellerEmail(String shopSellerEmail) {
        this.shopSellerEmail = shopSellerEmail;
    }

    public String getShopAddressDetail() {
        return shopAddressDetail;
    }

    public void setShopAddressDetail(String shopAddressDetail) {
        this.shopAddressDetail = shopAddressDetail;
    }

    public String getShopPhone() {
        return shopPhone;
    }

    public void setShopPhone(String shopPhone) {
        this.shopPhone = shopPhone;
    }

    public String getShopRemark() {
        return shopRemark;
    }

    public void setShopRemark(String shopRemark) {
        this.shopRemark = shopRemark;
    }

    public String getShopImagePath() {
        return shopImagePath;
    }

    public void setShopImagePath(String shopImagePath) {
        this.shopImagePath = shopImagePath;
    }

    public String getShopPartner() {
        return shopPartner;
    }

    public void setShopPartner(String shopPartner) {
        this.shopPartner = shopPartner;
    }

    public String getAlipayPrivateKey() {
        return alipayPrivateKey;
    }

    public void setAlipayPrivateKey(String alipayPrivateKey) {
        this.alipayPrivateKey = alipayPrivateKey;
    }

    public String getAlipayPublicKey() {
        return alipayPublicKey;
    }

    public void setAlipayPublicKey(String alipayPublicKey) {
        this.alipayPublicKey = alipayPublicKey;
    }

    public String getShopAppId() {
        return shopAppId;
    }

    public void setShopAppId(String shopAppId) {
        this.shopAppId = shopAppId;
    }

    public String getShopMchId() {
        return shopMchId;
    }

    public void setShopMchId(String shopMchId) {
        this.shopMchId = shopMchId;
    }

    public String getShopApiKey() {
        return shopApiKey;
    }

    public void setShopApiKey(String shopApiKey) {
        this.shopApiKey = shopApiKey;
    }

    public String getAreaDictNum() {
        return areaDictNum;
    }

    public void setAreaDictNum(String areaDictNum) {
        this.areaDictNum = areaDictNum;
    }

    public Integer getPowerSupplier() {
        return powerSupplier;
    }

    public void setPowerSupplier(Integer powerSupplier) {
        this.powerSupplier = powerSupplier;
    }

    public String getExaminestatusReason() {
        return examinestatusReason;
    }

    public void setExaminestatusReason(String examinestatusReason) {
        this.examinestatusReason = examinestatusReason;
    }

    public String getShopHours() {
        return shopHours;
    }

    public void setShopHours(String shopHours) {
        this.shopHours = shopHours;
    }

    public Integer getShopStatus() {
        return shopStatus;
    }

    public void setShopStatus(Integer shopStatus) {
        this.shopStatus = shopStatus;
    }

    public Integer getShopCounter() {
        return shopCounter;
    }

    public void setShopCounter(Integer shopCounter) {
        this.shopCounter = shopCounter;
    }

    public String getChannelId() {
        return channelId;
    }

    public void setChannelId(String channelId) {
        this.channelId = channelId;
    }

    public Integer getShopSource() {
        return shopSource;
    }

    public void setShopSource(Integer shopSource) {
        this.shopSource = shopSource;
    }

    public Integer getSametype() {
        return sametype;
    }

    public void setSametype(Integer sametype) {
        this.sametype = sametype;
    }

    public String getManagerName() {
        return managerName;
    }

    public void setManagerName(String managerName) {
        this.managerName = managerName;
    }

    public Integer getBelowCostSale() {
        return belowCostSale;
    }

    public void setBelowCostSale(Integer belowCostSale) {
        this.belowCostSale = belowCostSale;
    }

    public Integer getStoreId() {
        return storeId;
    }

    public void setStoreId(Integer storeId) {
        this.storeId = storeId;
    }

    public Integer getDistributionScope() {
        return distributionScope;
    }

    public void setDistributionScope(Integer distributionScope) {
        this.distributionScope = distributionScope;
    }

    public Date getRegistrationDate() {
        return registrationDate;
    }

    public void setRegistrationDate(Date registrationDate) {
        this.registrationDate = registrationDate;
    }

    public Integer getShopsType() {
        return shopsType;
    }

    public void setShopsType(Integer shopsType) {
        this.shopsType = shopsType;
    }

    public String getTakeoutAppauthtoken() {
        return takeoutAppauthtoken;
    }

    public void setTakeoutAppauthtoken(String takeoutAppauthtoken) {
        this.takeoutAppauthtoken = takeoutAppauthtoken;
    }

    public String getEleToken() {
        return eleToken;
    }

    public void setEleToken(String eleToken) {
        this.eleToken = eleToken;
    }

    public Date getEleTokenFailure() {
        return eleTokenFailure;
    }

    public void setEleTokenFailure(Date eleTokenFailure) {
        this.eleTokenFailure = eleTokenFailure;
    }

    public String getEleRefreshToken() {
        return eleRefreshToken;
    }

    public void setEleRefreshToken(String eleRefreshToken) {
        this.eleRefreshToken = eleRefreshToken;
    }

    public String getEleShopid() {
        return eleShopid;
    }

    public void setEleShopid(String eleShopid) {
        this.eleShopid = eleShopid;
    }

    public Integer getDeliveryType() {
        return deliveryType;
    }

    public void setDeliveryType(Integer deliveryType) {
        this.deliveryType = deliveryType;
    }

    public String getRegistrationId() {
        return registrationId;
    }

    public void setRegistrationId(String registrationId) {
        this.registrationId = registrationId;
    }

    public Integer getRegistrationPhoneType() {
        return registrationPhoneType;
    }

    public void setRegistrationPhoneType(Integer registrationPhoneType) {
        this.registrationPhoneType = registrationPhoneType;
    }

    public BigDecimal getShopScore() {
        return shopScore;
    }

    public void setShopScore(BigDecimal shopScore) {
        this.shopScore = shopScore;
    }

    public Integer getShopAttention() {
        return shopAttention;
    }

    public void setShopAttention(Integer shopAttention) {
        this.shopAttention = shopAttention;
    }

    public String getShopAnnouncement() {
        return shopAnnouncement;
    }

    public void setShopAnnouncement(String shopAnnouncement) {
        this.shopAnnouncement = shopAnnouncement;
    }

    public BigDecimal getTakeFee() {
        return takeFee;
    }

    public void setTakeFee(BigDecimal takeFee) {
        this.takeFee = takeFee;
    }

    public BigDecimal getTakeFreePrice() {
        return takeFreePrice;
    }

    public void setTakeFreePrice(BigDecimal takeFreePrice) {
        this.takeFreePrice = takeFreePrice;
    }

    public Integer getTakeEstimateTime() {
        return takeEstimateTime;
    }

    public void setTakeEstimateTime(Integer takeEstimateTime) {
        this.takeEstimateTime = takeEstimateTime;
    }

    public BigDecimal getShopTakePrice() {
        return shopTakePrice;
    }

    public void setShopTakePrice(BigDecimal shopTakePrice) {
        this.shopTakePrice = shopTakePrice;
    }

    public Integer getIsOrderTaking() {
        return isOrderTaking;
    }

    public void setIsOrderTaking(Integer isOrderTaking) {
        this.isOrderTaking = isOrderTaking;
    }

    public Integer getFacePayStatus() {
        return facePayStatus;
    }

    public void setFacePayStatus(Integer facePayStatus) {
        this.facePayStatus = facePayStatus;
    }

    public Long getShopBeans() {
        return shopBeans;
    }

    public void setShopBeans(Long shopBeans) {
        this.shopBeans = shopBeans;
    }

    public BigDecimal getShopBalance() {
        return shopBalance;
    }

    public void setShopBalance(BigDecimal shopBalance) {
        this.shopBalance = shopBalance;
    }

    public Integer getBeansAgreement() {
        return beansAgreement;
    }

    public void setBeansAgreement(Integer beansAgreement) {
        this.beansAgreement = beansAgreement;
    }

    public Integer getBeansSendMsgStatus() {
        return beansSendMsgStatus;
    }

    public void setBeansSendMsgStatus(Integer beansSendMsgStatus) {
        this.beansSendMsgStatus = beansSendMsgStatus;
    }

    public String getRegisterCode() {
        return registerCode;
    }

    public void setRegisterCode(String registerCode) {
        this.registerCode = registerCode;
    }

    public Integer getAutoPurchase() {
        return autoPurchase;
    }

    public void setAutoPurchase(Integer autoPurchase) {
        this.autoPurchase = autoPurchase;
    }

    public String getLoginName() {
        return loginName;
    }

    public void setLoginName(String loginName) {
        this.loginName = loginName;
    }

    public Integer getKindType() {
        return kindType;
    }

    public void setKindType(Integer kindType) {
        this.kindType = kindType;
    }

    public Integer getShopClass() {
        return shopClass;
    }

    public void setShopClass(Integer shopClass) {
        this.shopClass = shopClass;
    }

    public String getCompanyCode() {
        return companyCode;
    }

    public void setCompanyCode(String companyCode) {
        this.companyCode = companyCode;
    }

    public String getIsOtherPurchase() {
        return isOtherPurchase;
    }

    public void setIsOtherPurchase(String isOtherPurchase) {
        this.isOtherPurchase = isOtherPurchase;
    }

    public Integer getShowBuyStatus() {
        return showBuyStatus;
    }

    public void setShowBuyStatus(Integer showBuyStatus) {
        this.showBuyStatus = showBuyStatus;
    }

    public Integer getSupGiveBeans() {
        return supGiveBeans;
    }

    public void setSupGiveBeans(Integer supGiveBeans) {
        this.supGiveBeans = supGiveBeans;
    }

    public String getShowBuyFailReason() {
        return showBuyFailReason;
    }

    public void setShowBuyFailReason(String showBuyFailReason) {
        this.showBuyFailReason = showBuyFailReason;
    }

    public Integer getIsCollectingExpress() {
        return isCollectingExpress;
    }

    public void setIsCollectingExpress(Integer isCollectingExpress) {
        this.isCollectingExpress = isCollectingExpress;
    }

    public Integer getIsSendExpress() {
        return isSendExpress;
    }

    public void setIsSendExpress(Integer isSendExpress) {
        this.isSendExpress = isSendExpress;
    }

    public BigDecimal getSubsidyDeliveryPrice() {
        return subsidyDeliveryPrice;
    }

    public void setSubsidyDeliveryPrice(BigDecimal subsidyDeliveryPrice) {
        this.subsidyDeliveryPrice = subsidyDeliveryPrice;
    }

    public Integer getAgencyCode() {
        return agencyCode;
    }

    public void setAgencyCode(Integer agencyCode) {
        this.agencyCode = agencyCode;
    }

    public Integer getIsDis() {
        return isDis;
    }

    public void setIsDis(Integer isDis) {
        this.isDis = isDis;
    }

    public BigDecimal getLklBalance() {
        return lklBalance;
    }

    public void setLklBalance(BigDecimal lklBalance) {
        this.lklBalance = lklBalance;
    }

    public Integer getCusSynchroStatus() {
        return cusSynchroStatus;
    }

    public void setCusSynchroStatus(Integer cusSynchroStatus) {
        this.cusSynchroStatus = cusSynchroStatus;
    }

    public Integer getIsDaikou() {
        return isDaikou;
    }

    public void setIsDaikou(Integer isDaikou) {
        this.isDaikou = isDaikou;
    }

    public Integer getOrderTime() {
        return orderTime;
    }

    public void setOrderTime(Integer orderTime) {
        this.orderTime = orderTime;
    }

    public String getPcRegistrationId() {
        return pcRegistrationId;
    }

    public void setPcRegistrationId(String pcRegistrationId) {
        this.pcRegistrationId = pcRegistrationId;
    }

    public String getTownCode() {
        return townCode;
    }

    public void setTownCode(String townCode) {
        this.townCode = townCode;
    }
}