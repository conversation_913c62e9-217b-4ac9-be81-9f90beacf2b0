package org.haier.shop.entity;

import java.io.Serializable;

public class CustomerForImport implements Serializable{
	
	/**
	 * 
	 */
	private static final long serialVersionUID = 1L;
	//会员id
	private Integer cus_id;
	//商品导入成功标识:1、成功；2、会员信息不全；3、会员信息有误；
	private Integer sucSign;
	//会员编号
	private String cusUnique;
	//会员性别
	private String cusSex;
	//所属店铺编号
	private String shopUnique;
	//会员生日信息
	private String cusRegeditDate;
	//会员姓名	
	private String cusName;
	//会员电话
	private String cusPhone;
	//会员消费总金额
	private Double cusTotal=0.0;
	//会员累计积分
	private Double totalPoints=0.0;
	//剩余积分
	private Double cusPoints=0.0;
	//消费者已用积分
	private Double cusUsePoints=0.0;
	//会员消费次数
	private Integer cusCount=0;
	//会员充值总额
	private Double cusAmount=0.0;
	//储值卡已用金额
	private Double cusUse=0.0;
	//储值卡余额
	private Double cusBalance=0.0;
	//储值卡类型
	private String cusType;
	//会员等级
	private Integer cusLevelId;
	//会员地址信息
	private String cusAddress;
	//会员名称拼音码
	private String cusAlias;
	//会员生日
	private String cusBirthday;
	//若导入失败，记录导入失败的原因，并返回
	private String errorReason;
	//赠送的余额
	private Double giveBalance;
	
	public Double getGiveBalance() {
		return giveBalance;
	}
	public void setGiveBalance(Double giveBalance) {
		this.giveBalance = giveBalance;
	}
	public Integer getCus_id() {
		return cus_id;
	}
	public void setCus_id(Integer cus_id) {
		this.cus_id = cus_id;
	}
	public Integer getSucSign() {
		return sucSign;
	}
	public void setSucSign(Integer sucSign) {
		this.sucSign = sucSign;
	}
	public String getCusUnique() {
		return cusUnique;
	}
	public String getShopUnique() {
		return shopUnique;
	}
	public void setShopUnique(String shopUnique) {
		this.shopUnique = shopUnique;
	}
	public void setCusUnique(String cusUnique) {
		this.cusUnique = cusUnique;
	}
	public String getCusName() {
		return cusName;
	}
	public void setCusName(String cusName) {
		this.cusName = cusName;
	}
	public String getCusPhone() {
		return cusPhone;
	}
	public void setCusPhone(String cusPhone) {
		this.cusPhone = cusPhone;
	}
	public Double getCusTotal() {
		return cusTotal;
	}
	public void setCusTotal(Double cusTotal) {
		this.cusTotal = cusTotal;
	}
	public Double getTotalPoints() {
		return totalPoints;
	}
	public void setTotalPoints(Double totalPoints) {
		this.totalPoints = totalPoints;
	}
	public Double getCusPoints() {
		return cusPoints;
	}
	public void setCusPoints(Double cusPoints) {
		this.cusPoints = cusPoints;
	}
	public String getCusRegeditDate() {
		return cusRegeditDate;
	}
	public void setCusRegeditDate(String cusRegeditDate) {
		this.cusRegeditDate = cusRegeditDate;
	}
	public Double getCusUsePoints() {
		return cusUsePoints;
	}
	public void setCusUsePoints(Double cusUsePoints) {
		this.cusUsePoints = cusUsePoints;
	}
	public Integer getCusCount() {
		return cusCount;
	}
	public void setCusCount(Integer cusCount) {
		this.cusCount = cusCount;
	}
	public Double getCusAmount() {
		return cusAmount;
	}
	public void setCusAmount(Double cusAmount) {
		this.cusAmount = cusAmount;
	}
	public Double getCusUse() {
		return cusUse;
	}
	public void setCusUse(Double cusUse) {
		this.cusUse = cusUse;
	}
	public Double getCusBalance() {
		return cusBalance;
	}
	public void setCusBalance(Double cusBalance) {
		this.cusBalance = cusBalance;
	}
	public String getCusType() {
		return cusType;
	}
	public void setCusType(String cusType) {
		this.cusType = cusType;
	}
	public Integer getCusLevelId() {
		return cusLevelId;
	}
	public void setCusLevelId(Integer cusLevelId) {
		this.cusLevelId = cusLevelId;
	}
	public String getCusAddress() {
		return cusAddress;
	}
	public void setCusAddress(String cusAddress) {
		this.cusAddress = cusAddress;
	}
	public String getCusAlias() {
		return cusAlias;
	}
	public void setCusAlias(String cusAlias) {
		this.cusAlias = cusAlias;
	}
	public String getErrorReason() {
		return errorReason;
	}
	public void setErrorReason(String errorReason) {
		this.errorReason = errorReason;
	}
	
	public String getCusSex() {
		return cusSex;
	}
	public void setCusSex(String cusSex) {
		this.cusSex = cusSex;
	}
	public static long getSerialversionuid() {
		return serialVersionUID;
	}
	public String getCusBirthday() {
		return cusBirthday;
	}
	public void setCusBirthday(String cusBirthday) {
		this.cusBirthday = cusBirthday;
	}
	@Override
	public String toString() {
		return "CustomerForImport [sucSign=" + sucSign + ", cusUnique=" + cusUnique + ", shopUnique=" + shopUnique
				+ ", cusRegeditDate=" + cusRegeditDate + ", cusName=" + cusName + ", cusPhone=" + cusPhone
				+ ", cusTotal=" + cusTotal + ", totalPoints=" + totalPoints + ", cusPoints=" + cusPoints
				+ ", cusUsePoints=" + cusUsePoints + ", cusCount=" + cusCount + ", cusAmount=" + cusAmount + ", cusUse="
				+ cusUse + ", cusBalance=" + cusBalance + ", cusType=" + cusType + ", cusLevelId=" + cusLevelId
				+ ", cusAddress=" + cusAddress + ", cusAlias=" + cusAlias + ", cusBirthday=" + cusBirthday
				+ ", errorReason=" + errorReason + "]";
	}
}
