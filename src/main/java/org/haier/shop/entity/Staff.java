package org.haier.shop.entity;

import java.io.Serializable;
import java.sql.Timestamp;

/**
 * 店铺员工
 * <AUTHOR>
 */
public class Staff implements Serializable{
	/**
	 * 
	 */
	private static final long serialVersionUID = 1L;
	//员工编号
	private Integer staff_id;
	//员工登录账号
	private String staff_account;
	//员工登录密码
	private String staff_pwd;
	//员工管理员账号，仅限管理员
	private Long shop_unique;
	//员工职位 ，1：收银员；2：送货员；3：店主
	private Integer staff_position;
	//员工电话
	private String staff_phone;
	//管理员密码：确认过的
	private String pwd_ok;
	//管理员密钥
	private String manager_unique;
	//员工名称，赞设店铺名称
	private String staff_name;
	//yuangong shengri 
	private Timestamp staff_birthday;
	private Integer kind_type;
	
	private Integer bean;//百货豆配置和提现处理权限：1有0没有
	
	private Integer shop_type;//店铺类型:1、便利店；2、水果店；3、母婴店；4、其他
	private Integer shop_class;//店铺分类：0普通商家；1：连锁；2加盟 3系统平台
	
	private Integer examinestatus;//店铺审核状态：1，未提交申请；2，已提交申请；3，审核未通过；4，审核通过；5：已撤回
	
	private Integer show_buy_status;//是否开通微信小程序 0:不显示 1:显示
	
	private String area_dict_num;
	
	private Integer is_dis;//是否开启分销：1不开启 2开启
	
	private String shop_name;//店铺名称
	
	private String shop_address_detail;
	private String shop_phone;
	private String manager_name;
	private String county;

	private String roleType;


	
	
	public String getCounty() {
		return county;
	}
	public void setCounty(String county) {
		this.county = county;
	}
	public String getShop_name() {
		return shop_name;
	}
	public void setShop_name(String shop_name) {
		this.shop_name = shop_name;
	}
	public String getShop_address_detail() {
		return shop_address_detail;
	}
	public void setShop_address_detail(String shop_address_detail) {
		this.shop_address_detail = shop_address_detail;
	}
	public String getShop_phone() {
		return shop_phone;
	}
	public void setShop_phone(String shop_phone) {
		this.shop_phone = shop_phone;
	}
	public String getManager_name() {
		return manager_name;
	}
	public void setManager_name(String manager_name) {
		this.manager_name = manager_name;
	}
	public Integer getIs_dis() {
		return is_dis;
	}
	public void setIs_dis(Integer is_dis) {
		this.is_dis = is_dis;
	}
	public Integer getShow_buy_status() {
		return show_buy_status;
	}
	public void setShow_buy_status(Integer show_buy_status) {
		this.show_buy_status = show_buy_status;
	}
	public Integer getExaminestatus() {
		return examinestatus;
	}
	public void setExaminestatus(Integer examinestatus) {
		this.examinestatus = examinestatus;
	}
	public Integer getShop_type() {
		return shop_type;
	}
	public void setShop_type(Integer shop_type) {
		this.shop_type = shop_type;
	}
	public Integer getShop_class() {
		return shop_class;
	}
	public void setShop_class(Integer shop_class) {
		this.shop_class = shop_class;
	}
	public Integer getBean() {
		return bean;
	}
	public void setBean(Integer bean) {
		this.bean = bean;
	}
	public Integer getStaff_id() {
		return staff_id;
	}
	public void setStaff_id(Integer staff_id) {
		this.staff_id = staff_id;
	}
	public String getStaff_account() {
		return staff_account;
	}
	public void setStaff_account(String staff_account) {
		this.staff_account = staff_account;
	}
	public String getStaff_pwd() {
		return staff_pwd;
	}
	public void setStaff_pwd(String staff_pwd) {
		this.staff_pwd = staff_pwd;
	}
	public Long getShop_unique() {
		return shop_unique;
	}
	public void setShop_unique(Long shop_unique) {
		this.shop_unique = shop_unique;
	}
	public Integer getStaff_position() {
		return staff_position;
	}
	public void setStaff_position(Integer staff_position) {
		this.staff_position = staff_position;
	}
	public String getStaff_phone() {
		return staff_phone;
	}
	public void setStaff_phone(String staff_phone) {
		this.staff_phone = staff_phone;
	}
	
	public String getStaff_ok() {
		return pwd_ok;
	}
	public void setStaff_ok(String pwd_ok) {
		this.pwd_ok = pwd_ok;
	}
	
	public String getPwd_ok() {
		return pwd_ok;
	}
	public void setPwd_ok(String pwd_ok) {
		this.pwd_ok = pwd_ok;
	}
	public String getManager_unique() {
		return manager_unique;
	}
	public void setManager_unique(String manager_unique) {
		this.manager_unique = manager_unique;
	}

	public String getRoleType() {
		return roleType;
	}

	public void setRoleType(String roleType) {
		this.roleType = roleType;
	}

	public Integer getKind_type() {
		return kind_type;
	}
	public void setKind_type(Integer kind_type) {
		this.kind_type = kind_type;
	}
	@Override
	public String toString() {
		return "Staff [staff_id=" + staff_id + ", staff_account=" + staff_account + ", staff_pwd=" + staff_pwd
				+ ", shop_unique=" + shop_unique + ", staff_position=" + staff_position + ", staff_phone=" + staff_phone
				+ ", pwd_ok=" + pwd_ok + ", manager_unique=" + manager_unique + ", staff_name=" + staff_name
				+ ", staff_birthday=" + staff_birthday + ", bean=" + bean + ", shop_type=" + shop_type + ", shop_class="
				+ shop_class + ", examinestatus=" + examinestatus + ", show_buy_status=" + show_buy_status
				+ ", area_dict_num=" + area_dict_num + ", is_dis=" + is_dis + ", shop_name=" + shop_name
				+ ",kind_type=" + kind_type
				+ ", shop_address_detail=" + shop_address_detail + ", shop_phone=" + shop_phone + ", manager_name="
				+ manager_name+",roleType="+roleType + "]";
	}
	public String getStaff_name() {
		return staff_name;
	}
	public void setStaff_name(String staff_name) {
		this.staff_name = staff_name;
	}
	
	public Timestamp getStaff_birthday() {
		return staff_birthday;
	}
	public void setStaff_birthday(Timestamp staff_birthday) {
		this.staff_birthday = staff_birthday;
	}
	public String getArea_dict_num() {
		return area_dict_num;
	}
	public void setArea_dict_num(String area_dict_num) {
		this.area_dict_num = area_dict_num;
	}
	
}
