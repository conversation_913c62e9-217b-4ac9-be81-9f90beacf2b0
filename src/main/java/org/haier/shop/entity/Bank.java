package org.haier.shop.entity;

/**
 * 银行卡信息
 * <AUTHOR>
 *
 */
public class Bank {
	private Integer bankId;//编号
	private String bankName;//银行名称
	private String bankImg;//背景图
	private String bankLogo;//Logo
	private String modifyTime;//更新时间
	private Integer staffId;//员工编号
	private String staffName;//员工名称
	private Integer validType;//有效标识；1、有效；2、无效
	private String domainName;//域名
	public Integer getBankId() {
		return bankId;
	}
	public void setBankId(Integer bankId) {
		this.bankId = bankId;
	}
	public String getBankName() {
		return bankName;
	}
	public void setBankName(String bankName) {
		this.bankName = bankName;
	}
	public String getBankImg() {
		return bankImg;
	}
	public void setBankImg(String bankImg) {
		this.bankImg = bankImg;
	}
	public String getBankLogo() {
		return bankLogo;
	}
	public void setBankLogo(String bankLogo) {
		this.bankLogo = bankLogo;
	}
	public String getModifyTime() {
		return modifyTime;
	}
	public void setModifyTime(String modifyTime) {
		this.modifyTime = modifyTime;
	}
	public Integer getStaffId() {
		return staffId;
	}
	public void setStaffId(Integer staffId) {
		this.staffId = staffId;
	}
	public String getStaffName() {
		return staffName;
	}
	public void setStaffName(String staffName) {
		this.staffName = staffName;
	}
	public Integer getValidType() {
		return validType;
	}
	public void setValidType(Integer validType) {
		this.validType = validType;
	}
	public String getDomainName() {
		return domainName;
	}
	public void setDomainName(String domainName) {
		this.domainName = domainName;
	}
	@Override
	public String toString() {
		return "Bank [bankId=" + bankId + ", bankName=" + bankName + ", bankImg=" + bankImg + ", bankLogo=" + bankLogo
				+ ", modifyTime=" + modifyTime + ", staffId=" + staffId + ", staffName=" + staffName + ", validType="
				+ validType + ", domainName=" + domainName + "]";
	}
}
