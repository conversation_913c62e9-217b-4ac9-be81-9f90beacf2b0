package org.haier.shop.entity;

import java.io.Serializable;

/**
 * 商品导入专用
 * <AUTHOR>
 *
 */
public class GoodsForImport implements Serializable{
	
	/**
	 * 
	 */
	private static final long serialVersionUID = 1L;
	//商品导入成功标识:1、成功；2、商品信息不全；3、商品信息有误；
	private Integer sucSign;
	//店铺编号
	private String shopUnique;
	//商品条码
	private String goodsBarcode;
	//商品名称
	private String goodsName;
	//商品分类
	private String goodsKindsUnique;
	//包装单位
	private String goodsUnit;
	//商品规格
	private String goodsStandard;
	//商品进价
	private Double goodsInPrice;
	//商品售价
	private Double goodsSalePrice;
	//商品包装外键
	private String foreignKey;
	//商品会员价
	private Double goodsCusPrice;
	//库存数量
	private Double goodsCount;
	//商品简称
	private String goodsAlias;
	//商品分类
	private String goodsKindUnique;
	//导入失败原因
	private String errorReason;
	//商品承重类型
	private Integer goodsChengType;
	//商品调后售价
	private Double goodsSalePriceModify;
	//商品调后会员价
	private Double goodsCusPriceModify;
	//商品网购价
	private Double goodsWebSalePrice;
	//供货商编号
	private String supplierUnique;
	
	
	
	public Double getGoodsWebSalePrice() {
		return goodsWebSalePrice;
	}
	public void setGoodsWebSalePrice(Double goodsWebSalePrice) {
		this.goodsWebSalePrice = goodsWebSalePrice;
	}
	public Double getGoodsSalePriceModify() {
		return goodsSalePriceModify;
	}
	public void setGoodsSalePriceModify(Double goodsSalePriceModify) {
		this.goodsSalePriceModify = goodsSalePriceModify;
	}
	public Double getGoodsCusPriceModify() {
		return goodsCusPriceModify;
	}
	public void setGoodsCusPriceModify(Double goodsCusPriceModify) {
		this.goodsCusPriceModify = goodsCusPriceModify;
	}
	public Integer getSucSign() {
		return sucSign;
	}
	public void setSucSign(Integer sucSign) {
		this.sucSign = sucSign;
	}
	public String getShopUnique() {
		return shopUnique;
	}
	public void setShopUnique(String shopUnique) {
		this.shopUnique = shopUnique;
	}
	public String getGoodsBarcode() {
		return goodsBarcode;
	}
	public void setGoodsBarcode(String goodsBarcode) {
		this.goodsBarcode = goodsBarcode;
	}
	public String getGoodsName() {
		return goodsName;
	}
	public void setGoodsName(String goodsName) {
		this.goodsName = goodsName;
	}
	public String getGoodsKindsUnique() {
		return goodsKindsUnique;
	}
	public void setGoodsKindsUnique(String goodsKindsUnique) {
		this.goodsKindsUnique = goodsKindsUnique;
	}
	public String getGoodsUnit() {
		return goodsUnit;
	}
	public void setGoodsUnit(String goodsUnit) {
		this.goodsUnit = goodsUnit;
	}
	public String getGoodsStandard() {
		return goodsStandard;
	}
	public void setGoodsStandard(String goodsStandard) {
		this.goodsStandard = goodsStandard;
	}
	public Double getGoodsInPrice() {
		return goodsInPrice;
	}
	public void setGoodsInPrice(Double goodsInPrice) {
		this.goodsInPrice = goodsInPrice;
	}
	public Double getGoodsSalePrice() {
		return goodsSalePrice;
	}
	public String getGoodsKindUnique() {
		return goodsKindUnique;
	}
	public void setGoodsKindUnique(String goodsKindUnique) {
		this.goodsKindUnique = goodsKindUnique;
	}
	public void setGoodsSalePrice(Double goodsSalePrice) {
		this.goodsSalePrice = goodsSalePrice;
	}
	public String getForeignKey() {
		return foreignKey;
	}
	public void setForeignKey(String foreignKey) {
		this.foreignKey = foreignKey;
	}
	public Double getGoodsCusPrice() {
		return goodsCusPrice;
	}
	public void setGoodsCusPrice(Double goodsCusPrice) {
		this.goodsCusPrice = goodsCusPrice;
	}
	public Double getGoodsCount() {
		return goodsCount;
	}
	public void setGoodsCount(Double goodsCount) {
		this.goodsCount = goodsCount;
	}
	public String getGoodsAlias() {
		return goodsAlias;
	}
	
	public void setGoodsAlias(String goodsAlias) {
		this.goodsAlias = goodsAlias;
	}
	public String getErrorReason() {
		return errorReason;
	}
	public void setErrorReason(String errorReason) {
		this.errorReason = errorReason;
	}
	public Integer getGoodsChengType() {
		return goodsChengType;
	}
	public void setGoodsChengType(Integer goodsChengType) {
		this.goodsChengType = goodsChengType;
	}
	public String getSupplierUnique() {
		return supplierUnique;
	}
	public void setSupplierUnique(String supplierUnique) {
		this.supplierUnique = supplierUnique;
	}
	@Override
	public String toString() {
		return "GoodsForImport [sucSign=" + sucSign + ", shopUnique=" + shopUnique + ", goodsBarcode=" + goodsBarcode
				+ ", goodsName=" + goodsName + ", goodsKindsUnique=" + goodsKindsUnique + ", goodsUnit=" + goodsUnit
				+ ", goodsStandard=" + goodsStandard + ", goodsInPrice=" + goodsInPrice + ", goodsSalePrice="
				+ goodsSalePrice + ", foreignKey=" + foreignKey + ", goodsCusPrice=" + goodsCusPrice + ", goodsCount="
				+ goodsCount + ", goodsAlias=" + goodsAlias + ", goodsKindUnique=" + goodsKindUnique + ", errorReason="
				+ errorReason + ", goodsChengType=" + goodsChengType + ", goodsSalePriceModify=" + goodsSalePriceModify
				+ ", goodsCusPriceModify=" + goodsCusPriceModify + ", goodsWebSalePrice=" + goodsWebSalePrice
				+ ", supplierUnique=" + supplierUnique + "]";
	}
}
