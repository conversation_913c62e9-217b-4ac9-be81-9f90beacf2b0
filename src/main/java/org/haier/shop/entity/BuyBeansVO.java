package org.haier.shop.entity;

import java.io.Serializable;

/**
 * <AUTHOR>
 *
 */
public class BuyBeansVO implements Serializable{
	
	/**
	 * 
	 */
	private static final long serialVersionUID = 2899212963720648956L;
	private String productId;
	private String beans_old_count;
	private String payMoeny;
	private String out_trade_no;
	private String shop_unique;
	
	public String getProductId() {
		return productId;
	}
	public void setProductId(String productId) {
		this.productId = productId;
	}
	public String getBeans_old_count() {
		return beans_old_count;
	}
	public void setBeans_old_count(String beans_old_count) {
		this.beans_old_count = beans_old_count;
	}
	public String getPayMoeny() {
		return payMoeny;
	}
	public void setPayMoeny(String payMoeny) {
		this.payMoeny = payMoeny;
	}
	public String getOut_trade_no() {
		return out_trade_no;
	}
	public void setOut_trade_no(String out_trade_no) {
		this.out_trade_no = out_trade_no;
	}
	public String getShop_unique() {
		return shop_unique;
	}
	public void setShop_unique(String shop_unique) {
		this.shop_unique = shop_unique;
	}
	
}
