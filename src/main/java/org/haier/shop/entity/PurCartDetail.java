package org.haier.shop.entity;

public class PurCartDetail {
	//子订单编号
	private Integer purchase_list_detail_id;
	//商品名称
	private String goods_name;
	//商品条码
	private String goods_barcode;
	//商品进价
	private String goods_price;
	//商品规格
	private String goods_standard;
	//图片路径
	private String goods_picturepath;
	//商品数量
	private Double purchase_list_detail_count;
	public Integer getPurchase_list_detail_id() {
		return purchase_list_detail_id;
	}
	public void setPurchase_list_detail_id(Integer purchase_list_detail_id) {
		this.purchase_list_detail_id = purchase_list_detail_id;
	}
	public String getGoods_name() {
		return goods_name;
	}
	public void setGoods_name(String goods_name) {
		this.goods_name = goods_name;
	}
	public String getGoods_barcode() {
		return goods_barcode;
	}
	public void setGoods_barcode(String goods_barcode) {
		this.goods_barcode = goods_barcode;
	}
	public String getGoods_price() {
		return goods_price;
	}
	public void setGoods_price(String goods_price) {
		this.goods_price = goods_price;
	}
	public String getGoods_standard() {
		return goods_standard;
	}
	public void setGoods_standard(String goods_standard) {
		this.goods_standard = goods_standard;
	}
	public Double getPurchase_list_detail_count() {
		return purchase_list_detail_count;
	}
	public void setPurchase_list_detail_count(Double purchase_list_detail_count) {
		this.purchase_list_detail_count = purchase_list_detail_count;
	}
	public String getGoods_picturepath() {
		return goods_picturepath;
	}
	public void setGoods_picturepath(String goods_picturepath) {
		this.goods_picturepath = goods_picturepath;
	}
	
	
}
