package org.haier.shop.entity;

public class ShopsYN {
	private String shopUnique;
	private String shopName;
	private String shopAddress;
	private String shopPhone;
	private Integer shopType;
	private Double shopLongitude;
	private Double shopLatitude;
	
	
	
	public String getShopUnique() {
		return shopUnique;
	}
	public void setShopUnique(String shopUnique) {
		this.shopUnique = shopUnique;
	}
	public String getShopName() {
		return shopName;
	}
	public void setShopName(String shopName) {
		this.shopName = shopName;
	}
	public String getShopAddress() {
		return shopAddress;
	}
	public void setShopAddress(String shopAddress) {
		this.shopAddress = shopAddress;
	}
	public String getShopPhone() {
		return shopPhone;
	}
	public void setShopPhone(String shopPhone) {
		this.shopPhone = shopPhone;
	}
	public Integer getShopType() {
		return shopType;
	}
	public void setShopType(Integer shopType) {
		this.shopType = shopType;
	}
	public Double getShopLongitude() {
		return shopLongitude;
	}
	public void setShopLongitude(Double shopLongitude) {
		this.shopLongitude = shopLongitude;
	}
	public Double getShopLatitude() {
		return shopLatitude;
	}
	public void setShopLatitude(Double shopLatitude) {
		this.shopLatitude = shopLatitude;
	}
	@Override
	public String toString() {
		return "ShopsYN [shopUnique=" + shopUnique + ", shopName=" + shopName + ", shopAddress=" + shopAddress
				+ ", shopPhone=" + shopPhone + ", shopType=" + shopType + ", shopLongitude=" + shopLongitude
				+ ", shopLatitude=" + shopLatitude + "]";
	}
}
