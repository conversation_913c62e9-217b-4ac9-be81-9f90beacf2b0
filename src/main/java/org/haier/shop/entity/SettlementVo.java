package org.haier.shop.entity;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

public class SettlementVo {

	private List<Map<String, Object>> settlementList;//商品列表
	private BigDecimal sum_amt_all;//商品总额
	private BigDecimal loan_amt_all;//赊账总金额
	private BigDecimal should_amt_all;//应付金额
	private BigDecimal deduct_amt_all;//理论可抵扣金圈币数量
	private BigDecimal jqb_count=new BigDecimal("0");//店铺金圈币可用数量 
	private BigDecimal jqb_max_count=new BigDecimal("0");//本次可抵扣最多金圈币
	private BigDecimal sum_delivery_price;//总配送费
	private BigDecimal total_money;//订单总金金额：配送费+商品金额-跨店满减-优惠券-金圈币
	private String pay_type;//最终支付方式：未支付、微信、支付宝、云闪付、赊销
	private BigDecimal pay_money;//实际支付的金额，不含跨店满减，优惠券，金圈币，包含微信，支付宝，云闪付，合利宝，赊销
	private int sum_count;
	private BigDecimal admin_coupon;
	private BigDecimal coupon_amount;
	private String adminCouponId;
	private String adminRecordId;
	

	public BigDecimal getPay_money() {
		return pay_money;
	}
	public void setPay_money(BigDecimal pay_money) {
		this.pay_money = pay_money;
	}
	public BigDecimal getCoupon_amount() {
		return coupon_amount;
	}
	public void setCoupon_amount(BigDecimal coupon_amount) {
		this.coupon_amount = coupon_amount;
	}
	public BigDecimal getTotal_money() {
		return total_money;
	}
	public void setTotal_money(BigDecimal total_money) {
		this.total_money = total_money;
	}

	public String getPay_type() {
		return pay_type;
	}
	public void setPay_type(String pay_type) {
		this.pay_type = pay_type;
	}
	public List<Map<String, Object>> getSettlementList() {
		return settlementList;
	}

	public BigDecimal getLoan_amt_all() {
		return loan_amt_all;
	}

	public void setLoan_amt_all(BigDecimal loan_amt_all) {
		this.loan_amt_all = loan_amt_all;
	}

	public void setSettlementList(List<Map<String, Object>> settlementList) {
		this.settlementList = settlementList;
	}

	public BigDecimal getSum_amt_all() {
		return sum_amt_all;
	}

	public void setSum_amt_all(BigDecimal sum_amt_all) {
		this.sum_amt_all = sum_amt_all;
	}

	public BigDecimal getShould_amt_all() {
		return should_amt_all;
	}

	public void setShould_amt_all(BigDecimal should_amt_all) {
		this.should_amt_all = should_amt_all;
	}

	public BigDecimal getDeduct_amt_all() {
		return deduct_amt_all;
	}

	public void setDeduct_amt_all(BigDecimal deduct_amt_all) {
		this.deduct_amt_all = deduct_amt_all;
	}

	public int getSum_count() {
		return sum_count;
	}

	public void setSum_count(int sum_count) {
		this.sum_count = sum_count;
	}

	public BigDecimal getJqb_count() {
		return jqb_count;
	}

	public void setJqb_count(BigDecimal jqb_count) {
		this.jqb_count = jqb_count;
	}

	public BigDecimal getJqb_max_count() {
		return jqb_max_count;
	}

	public void setJqb_max_count(BigDecimal jqb_max_count) {
		this.jqb_max_count = jqb_max_count;
	}

	public BigDecimal getSum_delivery_price() {
		return sum_delivery_price;
	}

	public void setSum_delivery_price(BigDecimal sum_delivery_price) {
		this.sum_delivery_price = sum_delivery_price;
	}

	public BigDecimal getAdmin_coupon() {
		return admin_coupon;
	}

	public void setAdmin_coupon(BigDecimal admin_coupon) {
		this.admin_coupon = admin_coupon;
	}

	public String getAdminCouponId() {
		return adminCouponId;
	}

	public void setAdminCouponId(String adminCouponId) {
		this.adminCouponId = adminCouponId;
	}

	public String getAdminRecordId() {
		return adminRecordId;
	}

	public void setAdminRecordId(String adminRecordId) {
		this.adminRecordId = adminRecordId;
	}

}
