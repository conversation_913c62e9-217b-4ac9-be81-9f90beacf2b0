package org.haier.shop.entity;

import java.io.Serializable;

public class Shop<PERSON>  implements Serializable{
	
	
	/**
	 * 
	 */
	private static final long serialVersionUID = 1498976268785581027L;
	//商家余额
	private Double balance;
	//商家余额-线上会员扫码支付
	private Double shop_balance;
	//商家余额-顾客通过微信支付宝支付 
	private Double lkl_balance;
	//商家百货豆数量
	private int shop_beans;
	private int rate;
	private Integer appRate;
	
	public Integer getAppRate() {
		return appRate;
	}

	public void setAppRate(Integer appRate) {
		this.appRate = appRate;
	}

	private int rate1;

	public Double getBalance() {
		return balance;
	}

	public void setBalance(Double balance) {
		this.balance = balance;
	}

	public int getRate() {
		return rate;
	}

	public void setRate(int rate) {
		this.rate = rate;
	}

	public int getRate1() {
		return rate1;
	}

	public void setRate1(int rate1) {
		this.rate1 = rate1;
	}

	public Double getShop_balance() {
		return shop_balance;
	}

	public void setShop_balance(Double shop_balance) {
		this.shop_balance = shop_balance;
	}

	public int getShop_beans() {
		return shop_beans;
	}

	public void setShop_beans(int shop_beans) {
		this.shop_beans = shop_beans;
	}

	public Double getLkl_balance() {
		return lkl_balance;
	}

	public void setLkl_balance(Double lkl_balance) {
		this.lkl_balance = lkl_balance;
	}
	
	
	

}
