package org.haier.shop.entity;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 商品出库批次表
 * <AUTHOR> 
 * @ClassName GoodsSaleBatch
 * @Date 2024-04-28
 **/

public class GoodsSaleBatch {
	private Long goodsSaleBatchId;

	/**
	* 店铺编码
	*/
	private Long shopUnique;

	/**
	* 批次号
	*/
	private String batchUnique;

	/**
	* 出库单号
	*/
	private String stockListUnique;

	/**
	* 商品条码
	*/
	private String goodsBarcode;

	/**
	* 商品进价
	*/
	private BigDecimal goodsInPrice;

	/**
	* 商品销售价
	*/
	private BigDecimal goodsOutPrice;

	/**
	* 商品出库数量
	*/
	private BigDecimal goodsOutCount;

	/**
	* 创建时间
	*/
	private Date createTime;

	/**
	* 创建人ID
	*/
	private Long createId;

	/**
	* 修改时间
	*/
	private Date updateTime;

	/**
	* 修改人ID
	*/
	private Long updateId;

	public Long getGoodsSaleBatchId() {
		return goodsSaleBatchId;
	}

	public void setGoodsSaleBatchId(Long goodsSaleBatchId) {
		this.goodsSaleBatchId = goodsSaleBatchId;
	}

	public Long getShopUnique() {
		return shopUnique;
	}

	public void setShopUnique(Long shopUnique) {
		this.shopUnique = shopUnique;
	}

	public String getBatchUnique() {
		return batchUnique;
	}

	public void setBatchUnique(String batchUnique) {
		this.batchUnique = batchUnique;
	}

	public String getStockListUnique() {
		return stockListUnique;
	}

	public void setStockListUnique(String stockListUnique) {
		this.stockListUnique = stockListUnique;
	}

	public String getGoodsBarcode() {
		return goodsBarcode;
	}

	public void setGoodsBarcode(String goodsBarcode) {
		this.goodsBarcode = goodsBarcode;
	}

	public BigDecimal getGoodsInPrice() {
		return goodsInPrice;
	}

	public void setGoodsInPrice(BigDecimal goodsInPrice) {
		this.goodsInPrice = goodsInPrice;
	}

	public BigDecimal getGoodsOutPrice() {
		return goodsOutPrice;
	}

	public void setGoodsOutPrice(BigDecimal goodsOutPrice) {
		this.goodsOutPrice = goodsOutPrice;
	}

	public BigDecimal getGoodsOutCount() {
		return goodsOutCount;
	}

	public void setGoodsOutCount(BigDecimal goodsOutCount) {
		this.goodsOutCount = goodsOutCount;
	}

	public Date getCreateTime() {
		return createTime;
	}

	public void setCreateTime(Date createTime) {
		this.createTime = createTime;
	}

	public Long getCreateId() {
		return createId;
	}

	public void setCreateId(Long createId) {
		this.createId = createId;
	}

	public Date getUpdateTime() {
		return updateTime;
	}

	public void setUpdateTime(Date updateTime) {
		this.updateTime = updateTime;
	}

	public Long getUpdateId() {
		return updateId;
	}

	public void setUpdateId(Long updateId) {
		this.updateId = updateId;
	}
}