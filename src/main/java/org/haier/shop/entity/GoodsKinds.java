package org.haier.shop.entity;

public class GoodsKinds {
		//子分类ID
		private String goods_kind_id;
		//子商品分类
		private String shop_unique;
		//子商品分类编号
		private String goods_kind_unique;
		//子商品分类名称
		private String goods_kind_name;
		//子商品分类图片
		private String goods_kind_picture;
		public String getGoods_kind_id() {
			return goods_kind_id;
		}
		public void setGoods_kind_id(String goods_kind_id) {
			this.goods_kind_id = goods_kind_id;
		}
		public String getGoods_kind_unique() {
			return goods_kind_unique;
		}
		public void setGoods_kind_unique(String goods_kind_unique) {
			this.goods_kind_unique = goods_kind_unique;
		}
		public String getGoods_kind_name() {
			return goods_kind_name;
		}
		public void setGoods_kind_name(String goods_kind_name) {
			this.goods_kind_name = goods_kind_name;
		}
		public String getGoods_kind_picture() {
			return goods_kind_picture;
		}
		public void setGoods_kind_picture(String goods_kind_picture) {
			this.goods_kind_picture = goods_kind_picture;
		}
		public String getShop_unique() {
			return shop_unique;
		}
		public void setShop_unique(String shop_unique) {
			this.shop_unique = shop_unique;
		}
}
