package org.haier.shop.entity.inventoryTask;

import org.haier.shop.util.PurResult;

import java.util.List;

public class TaskListResp {

    //返回状态
    private Integer status;
    //返回提示信息
    private String msg;

    private TaskListResult data;

    private Integer count;

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public String getMsg() {
        return msg;
    }

    public void setMsg(String msg) {
        this.msg = msg;
    }

    public TaskListResult getData() {
        return data;
    }

    public void setData(TaskListResult data) {
        this.data = data;
    }

    public Integer getCount() {
        return count;
    }

    public void setCount(Integer count) {
        this.count = count;
    }
}
