package org.haier.shop.entity.inventoryTask;

import java.util.List;

public class TaskListTask {

    private Long taskId; //任务id

    private String taskName; //任务名称

    private String createTime; //创建时间

    private Integer taskStatus; //盘点任务状态:1待提交2已盘点

    private List<TaskListGoodsList> goodsList; //商品信息

    public Long getTaskId() {
        return taskId;
    }

    public void setTaskId(Long taskId) {
        this.taskId = taskId;
    }

    public String getTaskName() {
        return taskName;
    }

    public void setTaskName(String taskName) {
        this.taskName = taskName;
    }

    public String getCreateTime() {
        return createTime;
    }

    public void setCreateTime(String createTime) {
        this.createTime = createTime;
    }

    public Integer getTaskStatus() {
        return taskStatus;
    }

    public void setTaskStatus(Integer taskStatus) {
        this.taskStatus = taskStatus;
    }

    public List<TaskListGoodsList> getGoodsList() {
        return goodsList;
    }

    public void setGoodsList(List<TaskListGoodsList> goodsList) {
        this.goodsList = goodsList;
    }

}
