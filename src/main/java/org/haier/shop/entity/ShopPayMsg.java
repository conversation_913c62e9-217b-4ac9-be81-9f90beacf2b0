package org.haier.shop.entity;

public class ShopPayMsg {
	private Integer id;//ID
	private String shopUnique;//店铺编号
	private String datetime;//申请时间
	private String legalPerson;//法人
	private String userPhone;//法人电话
	private String license;//营业执照保存图片路径
	private String doorPhoto;//门头照
	private String identityFront;//身份证正面
	private String identityBlack;//身份证反面
	private String bankCardFront;//银行卡正面
	private String bankCardBlack;//银行卡反面
	private String examineStatus;//审核状态1、待审核；2、审核不通过；3、重新上传；4、审核通过；5、拒绝
	private String examineRemarks;//审核意见
	private String examineDate;//审核日期
	private String validType;//有效状态;1、有效；2、无效；
	private String shopAddress;//店铺注册地址
	private String email;//接收邮件的地址
	private String subBranch;//支行信息
	
	public Integer getId() {
		return id;
	}
	public void setId(Integer id) {
		this.id = id;
	}
	public String getShopUnique() {
		return shopUnique;
	}
	public void setShopUnique(String shopUnique) {
		this.shopUnique = shopUnique;
	}
	public String getDatetime() {
		return datetime;
	}
	public void setDatetime(String datetime) {
		this.datetime = datetime;
	}
	public String getLegalPerson() {
		return legalPerson;
	}
	public void setLegalPerson(String legalPerson) {
		this.legalPerson = legalPerson;
	}
	public String getUserPhone() {
		return userPhone;
	}
	public void setUserPhone(String userPhone) {
		this.userPhone = userPhone;
	}
	public String getLicense() {
		return license;
	}
	public void setLicense(String license) {
		this.license = license;
	}
	public String getDoorPhoto() {
		return doorPhoto;
	}
	public String getExamineRemarks() {
		return examineRemarks;
	}
	public void setExamineRemarks(String examineRemarks) {
		this.examineRemarks = examineRemarks;
	}
	public void setDoorPhoto(String doorPhoto) {
		this.doorPhoto = doorPhoto;
	}
	public String getIdentityFront() {
		return identityFront;
	}
	public void setIdentityFront(String identityFront) {
		this.identityFront = identityFront;
	}
	public String getIdentityBlack() {
		return identityBlack;
	}
	public void setIdentityBlack(String identityBlack) {
		this.identityBlack = identityBlack;
	}
	public String getBankCardFront() {
		return bankCardFront;
	}
	public void setBankCardFront(String bankCardFront) {
		this.bankCardFront = bankCardFront;
	}
	public String getBankCardBlack() {
		return bankCardBlack;
	}
	public void setBankCardBlack(String bankCardBlack) {
		this.bankCardBlack = bankCardBlack;
	}
	public String getExamineStatus() {
		return examineStatus;
	}
	public void setExamineStatus(String examineStatus) {
		this.examineStatus = examineStatus;
	}
	public String getExamineDate() {
		return examineDate;
	}
	public void setExamineDate(String examineDate) {
		this.examineDate = examineDate;
	}
	public String getValidType() {
		return validType;
	}
	public void setValidType(String validType) {
		this.validType = validType;
	}
	public String getShopAddress() {
		return shopAddress;
	}
	public void setShopAddress(String shopAddress) {
		this.shopAddress = shopAddress;
	}
	public String getEmail() {
		return email;
	}
	public void setEmail(String email) {
		this.email = email;
	}
	public String getSubBranch() {
		return subBranch;
	}
	public void setSubBranch(String subBranch) {
		this.subBranch = subBranch;
	}
}
