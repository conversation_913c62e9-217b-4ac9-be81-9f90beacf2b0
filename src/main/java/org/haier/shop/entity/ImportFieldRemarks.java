package org.haier.shop.entity;

public class ImportFieldRemarks {
	//字段名
	private String fileName;
	//是否必须按
	private boolean flag;
	//类型:1、Long(1);2、String("sd";3、Double(2.02);4、date("2018-02-25");5、datetime("2015-05-05 23:23:33");
	private Integer filedType;
	//对应的字段
	private String filed;
	public ImportFieldRemarks() {
		super();
	}
	public ImportFieldRemarks(String fileName, boolean flag, Integer filedType) {
		super();
		this.fileName = fileName;
		this.flag = flag;
		this.filedType = filedType;
	}
	public String getFileName() {
		return fileName;
	}
	public void setFileName(String fileName) {
		this.fileName = fileName;
	}
	public boolean isFlag() {
		return flag;
	}
	public void setFlag(boolean flag) {
		this.flag = flag;
	}
	public Integer getFiledType() {
		return filedType;
	}
	public void setFiledType(Integer filedType) {
		this.filedType = filedType;
	}
	public String getFiled() {
		return filed;
	}
	public void setFiled(String filed) {
		this.filed = filed;
	}
	@Override
	public String toString() {
		return "ImportFieldRemarks [fileName=" + fileName + ", flag=" + flag + ", filedType=" + filedType + ", filed="
				+ filed + "]";
	}
}
