package org.haier.shop.entity;

public class ShopCourier {
	private Integer id;//配送员id
	private String shop_unique;//店铺唯一标示
	private String courier_name;//配送员姓名
	private String courier_phone;//配送员电话
	private String create_times;//创建时间
	private String update_times;//修改时间
	private Integer startNum;
	private Integer pageSize;
	
	public Integer getStartNum() {
		return startNum;
	}
	public void setStartNum(Integer startNum) {
		this.startNum = startNum;
	}
	public Integer getPageSize() {
		return pageSize;
	}
	public void setPageSize(Integer pageSize) {
		this.pageSize = pageSize;
	}
	public Integer getId() {
		return id;
	}
	public void setId(Integer id) {
		this.id = id;
	}
	public String getShop_unique() {
		return shop_unique;
	}
	public void setShop_unique(String shop_unique) {
		this.shop_unique = shop_unique;
	}
	public String getCourier_name() {
		return courier_name;
	}
	public void setCourier_name(String courier_name) {
		this.courier_name = courier_name;
	}
	public String getCourier_phone() {
		return courier_phone;
	}
	public void setCourier_phone(String courier_phone) {
		this.courier_phone = courier_phone;
	}
	public String getCreate_times() {
		return create_times;
	}
	public void setCreate_times(String create_times) {
		this.create_times = create_times;
	}
	public String getUpdate_times() {
		return update_times;
	}
	public void setUpdate_times(String update_times) {
		this.update_times = update_times;
	}
	
}
