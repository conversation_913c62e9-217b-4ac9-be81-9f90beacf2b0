package org.haier.shop.entity.recharge;

import java.util.List;

public class RechargeOffline {
	private String rechargeConfigId;
	private Double money;
	private String createTime;
	private Integer delete_status;
	private String deleteStatus;
	private String rechargeName;
	private String startTime;
	private String endTime;
	private Integer isCoupon;
	private Integer isPoint;
	private Double addPoint;
	private Integer isGoods;
	private String isBalance;
	private Double addBalance;
	private Integer isCusLevel;
	private String cusLevelId;
	private String cusLevelName;
	private String shopUnique;
	private List<RechargeOfflineCoupon> couponList;
	private List<RechargeOfflineGoods> goodsList;
	
	public String getCusLevelName() {
		return cusLevelName;
	}
	public void setCusLevelName(String cusLevelName) {
		this.cusLevelName = cusLevelName;
	}
	public List<RechargeOfflineCoupon> getCouponList() {
		return couponList;
	}
	public void setCouponList(List<RechargeOfflineCoupon> couponList) {
		this.couponList = couponList;
	}
	public List<RechargeOfflineGoods> getGoodsList() {
		return goodsList;
	}
	public void setGoodsList(List<RechargeOfflineGoods> goodsList) {
		this.goodsList = goodsList;
	}
	public String getRechargeConfigId() {
		return rechargeConfigId;
	}
	public void setRechargeConfigId(String rechargeConfigId) {
		this.rechargeConfigId = rechargeConfigId;
	}
	public Double getMoney() {
		return money;
	}
	public void setMoney(Double money) {
		this.money = money;
	}
	public String getCreateTime() {
		return createTime;
	}
	public void setCreateTime(String createTime) {
		this.createTime = createTime;
	}
	public Integer getDelete_status() {
		return delete_status;
	}
	public void setDelete_status(Integer delete_status) {
		this.delete_status = delete_status;
	}
	public String getDeleteStatus() {
		return deleteStatus;
	}
	public void setDeleteStatus(String deleteStatus) {
		this.deleteStatus = deleteStatus;
	}
	public String getRechargeName() {
		return rechargeName;
	}
	public void setRechargeName(String rechargeName) {
		this.rechargeName = rechargeName;
	}
	public String getStartTime() {
		return startTime;
	}
	public void setStartTime(String startTime) {
		this.startTime = startTime;
	}
	public String getEndTime() {
		return endTime;
	}
	public void setEndTime(String endTime) {
		this.endTime = endTime;
	}
	public Integer getIsCoupon() {
		return isCoupon;
	}
	public void setIsCoupon(Integer isCoupon) {
		this.isCoupon = isCoupon;
	}
	public Integer getIsPoint() {
		return isPoint;
	}
	public void setIsPoint(Integer isPoint) {
		this.isPoint = isPoint;
	}
	public Double getAddPoint() {
		return addPoint;
	}
	public void setAddPoint(Double addPoint) {
		this.addPoint = addPoint;
	}
	public Integer getIsGoods() {
		return isGoods;
	}
	public void setIsGoods(Integer isGoods) {
		this.isGoods = isGoods;
	}
	public String getIsBalance() {
		return isBalance;
	}
	public void setIsBalance(String isBalance) {
		this.isBalance = isBalance;
	}
	public Double getAddBalance() {
		return addBalance;
	}
	public void setAddBalance(Double addBalance) {
		this.addBalance = addBalance;
	}
	public Integer getIsCusLevel() {
		return isCusLevel;
	}
	public void setIsCusLevel(Integer isCusLevel) {
		this.isCusLevel = isCusLevel;
	}
	public String getCusLevelId() {
		return cusLevelId;
	}
	public void setCusLevelId(String cusLevelId) {
		this.cusLevelId = cusLevelId;
	}
	public String getShopUnique() {
		return shopUnique;
	}
	public void setShopUnique(String shopUnique) {
		this.shopUnique = shopUnique;
	}
}
