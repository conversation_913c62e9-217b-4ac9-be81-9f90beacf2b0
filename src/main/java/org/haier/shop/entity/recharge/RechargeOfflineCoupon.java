package org.haier.shop.entity.recharge;

public class RechargeOfflineCoupon {
	private Long rechargeOfflineCouponId;
	private Long rechargeConfigId;
	private Long shopCouponId;
	private String couponName;
	private Integer couponCount;
	public String getCouponName() {
		return couponName;
	}
	public void setCouponName(String couponName) {
		this.couponName = couponName;
	}
	public Long getRechargeOfflineCouponId() {
		return rechargeOfflineCouponId;
	}
	public void setRechargeOfflineCouponId(Long rechargeOfflineCouponId) {
		this.rechargeOfflineCouponId = rechargeOfflineCouponId;
	}
	public Long getRechargeConfigId() {
		return rechargeConfigId;
	}
	public void setRechargeConfigId(Long rechargeConfigId) {
		this.rechargeConfigId = rechargeConfigId;
	}
	public Long getShopCouponId() {
		return shopCouponId;
	}
	public void setShopCouponId(Long shopCouponId) {
		this.shopCouponId = shopCouponId;
	}
	public Integer getCouponCount() {
		return couponCount;
	}
	public void setCouponCount(Integer couponCount) {
		this.couponCount = couponCount;
	}
}
