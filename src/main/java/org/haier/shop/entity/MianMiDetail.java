package org.haier.shop.entity;

public class MianMiDetail {
	//日期
	private String date;
	//免密金额
	private Double listSum;
	//免密单数
	private Integer listCount;
	//总订单数
	private Integer allCount;
	//总订单额
	private Double allSum;
	public String getDate() {
		return date;
	}
	public void setDate(String date) {
		this.date = date;
	}
	public Double getListSum() {
		return listSum;
	}
	public void setListSum(Double listSum) {
		this.listSum = listSum;
	}
	public Integer getListCount() {
		return listCount;
	}
	public void setListCount(Integer listCount) {
		this.listCount = listCount;
	}
	public Integer getAllCount() {
		return allCount;
	}
	public void setAllCount(Integer allCount) {
		this.allCount = allCount;
	}
	public Double getAllSum() {
		return allSum;
	}
	public void setAllSum(Double allSum) {
		this.allSum = allSum;
	}
	@Override
	public String toString() {
		return "MianMiDetail [date=" + date + ", listSum=" + listSum + ", listCount=" + listCount + ", allCount="
				+ allCount + ", allSum=" + allSum + "]";
	}
}
