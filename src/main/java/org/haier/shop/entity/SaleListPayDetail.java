package org.haier.shop.entity;

/**
 * 订单支付详情
 * <AUTHOR>
 *
 */
public class SaleListPayDetail {
	//订单支付详情编号
	private Integer payMethodId;
	//收款方式
	private Integer serverType;
	//订单支付方式编号
	private Integer payMethodCode;
	//订单支付方式
	private String payMethod;
	//支付金额
	private Double payMoney;
	public Integer getPayMethodId() {
		return payMethodId;
	}
	public void setPayMethodId(Integer payMethodId) {
		this.payMethodId = payMethodId;
	}
	public Integer getPayMethodCode() {
		return payMethodCode;
	}
	public void setPayMethodCode(Integer payMethodCode) {
		this.payMethodCode = payMethodCode;
	}
	public String getPayMethod() {
		return payMethod;
	}
	public void setPayMethod(String payMethod) {
		this.payMethod = payMethod;
	}
	public Double getPayMoney() {
		return payMoney;
	}
	public void setPayMoney(Double payMoney) {
		this.payMoney = payMoney;
	}

	public Integer getServerType() {
		return serverType;
	}

	public void setServerType(Integer serverType) {
		this.serverType = serverType;
	}

	@Override
	public String toString() {
		return "SaleListPayDetail [payMethodId=" + payMethodId + ", payMethodCode=" + payMethodCode + ", payMethod="
				+ payMethod + ", payMoney=" + payMoney + "]";
	}
}
