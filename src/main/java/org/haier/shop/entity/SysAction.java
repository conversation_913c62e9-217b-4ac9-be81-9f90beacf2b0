package org.haier.shop.entity;

import org.haier.shop.entity.beans.BaseModel;

@SuppressWarnings("serial")
public class SysAction extends BaseModel{
	
	private Integer id;
	private String menu_code;//此操作关联的菜单ID
	private String action_name;//操作名称
	private String action_param;//请求action,不需要参数，如果还有多个请求用，隔开
	private String code;
	public Integer getId() {
		return id;
	}
	public void setId(Integer id) {
		this.id = id;
	}
	public String getMenu_code() {
		return menu_code;
	}
	public void setMenu_code(String menu_code) {
		this.menu_code = menu_code;
	}
	public String getAction_name() {
		return action_name;
	}
	public void setAction_name(String action_name) {
		this.action_name = action_name;
	}
	public String getAction_param() {
		return action_param;
	}
	public void setAction_param(String action_param) {
		this.action_param = action_param;
	}
	public String getCode() {
		return code;
	}
	public void setCode(String code) {
		this.code = code;
	}
}
