package org.haier.shop.entity;

import java.math.BigDecimal;

/**
 * 自采购进货单详情
 * <AUTHOR> 
 * @ClassName SelfPurchaseDetail
 * @Date 2024-05-18
 **/

public class SelfPurchaseDetail {
	/**
	* 自采购进货单详情id
	*/
	private Long selfPurchaseDetailId;

	/**
	* 自采购进货单唯一标示
	*/
	private String selfPurchaseUnique;

	/**
	* 商品名称
	*/
	private String goodsName;

	/**
	* 商品编码
	*/
	private String goodsBarcode;

	/**
	* 采购价，进价
	*/
	private BigDecimal goodsInPrice;

	/**
	* 商品数量
	*/
	private BigDecimal goodsCount;

	/**
	* 单位
	*/
	private String unitName;

	/**
	* 是否赠品：1非赠品 2赠品
	*/
	private Integer giftType;

	public Long getSelfPurchaseDetailId() {
		return selfPurchaseDetailId;
	}

	public void setSelfPurchaseDetailId(Long selfPurchaseDetailId) {
		this.selfPurchaseDetailId = selfPurchaseDetailId;
	}

	public String getSelfPurchaseUnique() {
		return selfPurchaseUnique;
	}

	public void setSelfPurchaseUnique(String selfPurchaseUnique) {
		this.selfPurchaseUnique = selfPurchaseUnique;
	}

	public String getGoodsName() {
		return goodsName;
	}

	public void setGoodsName(String goodsName) {
		this.goodsName = goodsName;
	}

	public String getGoodsBarcode() {
		return goodsBarcode;
	}

	public void setGoodsBarcode(String goodsBarcode) {
		this.goodsBarcode = goodsBarcode;
	}

	public BigDecimal getGoodsInPrice() {
		return goodsInPrice;
	}

	public void setGoodsInPrice(BigDecimal goodsInPrice) {
		this.goodsInPrice = goodsInPrice;
	}

	public BigDecimal getGoodsCount() {
		return goodsCount;
	}

	public void setGoodsCount(BigDecimal goodsCount) {
		this.goodsCount = goodsCount;
	}

	public String getUnitName() {
		return unitName;
	}

	public void setUnitName(String unitName) {
		this.unitName = unitName;
	}

	public Integer getGiftType() {
		return giftType;
	}

	public void setGiftType(Integer giftType) {
		this.giftType = giftType;
	}
}