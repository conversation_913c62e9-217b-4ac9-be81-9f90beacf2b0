package org.haier.shop.entity;

import java.util.Date;

public class FarmProductShelf {

	private int id;
	private String good_id;
	private Date create_time;
	private String shop_unique;
	private int shelf_status;
	private int flag;

	public int getId() {
		return id;
	}

	public void setId(int id) {
		this.id = id;
	}

	public String getGood_id() {
		return good_id;
	}

	public void setGood_id(String good_id) {
		this.good_id = good_id;
	}

	public Date getCreate_time() {
		return create_time;
	}

	public void setCreate_time(Date create_time) {
		this.create_time = create_time;
	}

	public String getShop_unique() {
		return shop_unique;
	}

	public void setShop_unique(String shop_unique) {
		this.shop_unique = shop_unique;
	}

	public int getShelf_status() {
		return shelf_status;
	}

	public void setShelf_status(int shelf_status) {
		this.shelf_status = shelf_status;
	}

	public int getFlag() {
		return flag;
	}

	public void setFlag(int flag) {
		this.flag = flag;
	}

}
