package org.haier.shop.entity;

import java.util.List;

import org.haier.shop.entity.beans.BaseModel;

@SuppressWarnings("serial")
public class SysPermission extends BaseModel{
	
	private Integer id;
	private String code;
	private String name;//权限名称
	private String parent_code;//上级权限编码（如果为最高级别，上级编码为-1）
	private String level;//资源级别,从1开始
	private String url;//资源url
	private String icon;//菜单图标
	private Integer seq;//菜单顺序
	private String version;//权限归属版本：admin系统平台 ordinary普通商家 chain连锁商家 join加盟商家
	private String terminal;//终端类型：web 商家后台管理系统 app手机端 pc收银端
	private String type;//菜单类型:1、便利店；2、水果店；3、母婴店；4、益农中心站；5、益农普通站点；6、宁宇；7、其他
	private String remark;
	private String create_times;
	private String update_times;
	private Integer del_flag;//逻辑删除：0，正常，1逻辑删除
	private String parent_name;//父级菜单名称
	private List<SysPermission> listLevelTwo;//下级菜单列表
	
	public List<SysPermission> getListLevelTwo() {
		return listLevelTwo;
	}
	public void setListLevelTwo(List<SysPermission> listLevelTwo) {
		this.listLevelTwo = listLevelTwo;
	}
	public String getTerminal() {
		return terminal;
	}
	public void setTerminal(String terminal) {
		this.terminal = terminal;
	}
	public String getType() {
		return type;
	}
	public void setType(String type) {
		this.type = type;
	}
	private List<SysPermission> children;//子集菜单
	
	public List<SysPermission> getChildren() {
		return children;
	}
	public void setChildren(List<SysPermission> children) {
		this.children = children;
	}
	public Integer getDel_flag() {
		return del_flag;
	}
	public void setDel_flag(Integer del_flag) {
		this.del_flag = del_flag;
	}
	public String getVersion() {
		return version;
	}
	public void setVersion(String version) {
		this.version = version;
	}
	public String getParent_name() {
		return parent_name;
	}
	public void setParent_name(String parent_name) {
		this.parent_name = parent_name;
	}
	public Integer getId() {
		return id;
	}
	public void setId(Integer id) {
		this.id = id;
	}
	public String getCode() {
		return code;
	}
	public void setCode(String code) {
		this.code = code;
	}
	public String getName() {
		return name;
	}
	public void setName(String name) {
		this.name = name;
	}
	public String getParent_code() {
		return parent_code;
	}
	public void setParent_code(String parent_code) {
		this.parent_code = parent_code;
	}
	public String getLevel() {
		return level;
	}
	public void setLevel(String level) {
		this.level = level;
	}
	public String getUrl() {
		return url;
	}
	public void setUrl(String url) {
		this.url = url;
	}
	public String getIcon() {
		return icon;
	}
	public void setIcon(String icon) {
		this.icon = icon;
	}
	public Integer getSeq() {
		return seq;
	}
	public void setSeq(Integer seq) {
		this.seq = seq;
	}
	public String getRemark() {
		return remark;
	}
	public void setRemark(String remark) {
		this.remark = remark;
	}
	public String getCreate_times() {
		return create_times;
	}
	public void setCreate_times(String create_times) {
		this.create_times = create_times;
	}
	public String getUpdate_times() {
		return update_times;
	}
	public void setUpdate_times(String update_times) {
		this.update_times = update_times;
	}
}
