package org.haier.shop.entity;

import java.util.List;

public class GoodsSupplier {
	
	//店铺序号
	private Long supplierUnique;
	//店铺名称
	private String supplierName;
	//商品详情
	private List<SupplierGoods> listDetail;
	public Long getSupplierUnique() {
		return supplierUnique;
	}
	public void setSupplierUnique(Long supplierUnique) {
		this.supplierUnique = supplierUnique;
	}
	public String getSupplierName() {
		return supplierName;
	}
	public void setSupplierName(String supplierName) {
		this.supplierName = supplierName;
	}
	public List<SupplierGoods> getListDetail() {
		return listDetail;
	}
	public void setListDetail(List<SupplierGoods> listDetail) {
		this.listDetail = listDetail;
	}
	
}
