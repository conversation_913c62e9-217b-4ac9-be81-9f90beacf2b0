package org.haier.shop.entity;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 店铺配置
 * <AUTHOR> 
 * @ClassName ShopsConfig
 * @Date 2024-04-08
 **/

public class ShopsConfig {
	/**
	* ID
	*/
	private Integer id;

	/**
	* 店铺编号
	*/
	private String shopUnique;

	/**
	* 微信收款码图片路径
	*/
	private String wechatPic;

	/**
	* 支付宝图片上传路径
	*/
	private String alipayPic;

	/**
	* 免密支付申请状态：1、未提交申请；2、已提交申请；3、已安装；4、审核未通过；5、拒绝；6、审核通过未安装；
	*/
	private Integer mianmiStatus;

	/**
	* 免密支付失败原因
	*/
	private String mianmiRemarks;

	/**
	* 申请时间
	*/
	private Date mianmiApplyDatetime;

	/**
	* 审核时间
	*/
	private Date mianmiExamineDatetime;

	/**
	* teamviewer远程账号
	*/
	private String teamviewer;

	/**
	* anydesk远程账号
	*/
	private String anydesk;

	/**
	* 分店是否同步主店信息：0、不同步；1、同步；（修改主店信息时，同步修改分店信息，反之无效）
	*/
	private Integer sameGoods;

	/**
	* 会员信息同步：0、不同步；1、同步；
	*/
	private Integer sameCus;

	/**
	* 店铺绑定的推广员信息
	*/
	private String cusUnique;

	/**
	* 小程序类型：1、一刻钟到家；2、沃商赢
	*/
	private Integer smallType;

	/**
	* 商品进价类型：0-最近入库价，1-移动加权平均，2-先进先出
	*/
	private Integer goodsInPriceType;

	/**
	 * 是否出入库审核：0-否、1-是
	 */
	private Integer isIoboundInspect;
	/**
	 * 匹配商品逻辑：0-通用类型，1-根据订单金额匹配商品
	 */
	private Integer matchGoodsType;
	/**
	 * 是否同步餐饮订单: 0-否，1-是
	 */
	private Integer syncCanyinData;
	/**
	 * 是否同步零售订单: 0-否，1-是
	 */
	private Integer syncBuyhooData;

	//是否设置了会员日：1、设置；0、未设置
	private Integer memberDay;
	//会员日设置的类型：1、每周；2、每月；3、每年
	private Integer memberDayType;
	//设置的会员日:1、按周：0-6；2、每月：1-31；3、每年：03-25
	private String memberDayValue;
	//会员日，积分的倍数，整数，1-10。
	private BigDecimal memberPointMultiple;

	public Integer getId() {
		return id;
	}

	public void setId(Integer id) {
		this.id = id;
	}

	public String getShopUnique() {
		return shopUnique;
	}

	public void setShopUnique(String shopUnique) {
		this.shopUnique = shopUnique;
	}

	public String getWechatPic() {
		return wechatPic;
	}

	public void setWechatPic(String wechatPic) {
		this.wechatPic = wechatPic;
	}

	public String getAlipayPic() {
		return alipayPic;
	}

	public void setAlipayPic(String alipayPic) {
		this.alipayPic = alipayPic;
	}

	public Integer getMianmiStatus() {
		return mianmiStatus;
	}

	public void setMianmiStatus(Integer mianmiStatus) {
		this.mianmiStatus = mianmiStatus;
	}

	public String getMianmiRemarks() {
		return mianmiRemarks;
	}

	public void setMianmiRemarks(String mianmiRemarks) {
		this.mianmiRemarks = mianmiRemarks;
	}

	public Date getMianmiApplyDatetime() {
		return mianmiApplyDatetime;
	}

	public void setMianmiApplyDatetime(Date mianmiApplyDatetime) {
		this.mianmiApplyDatetime = mianmiApplyDatetime;
	}

	public Date getMianmiExamineDatetime() {
		return mianmiExamineDatetime;
	}

	public void setMianmiExamineDatetime(Date mianmiExamineDatetime) {
		this.mianmiExamineDatetime = mianmiExamineDatetime;
	}

	public String getTeamviewer() {
		return teamviewer;
	}

	public void setTeamviewer(String teamviewer) {
		this.teamviewer = teamviewer;
	}

	public String getAnydesk() {
		return anydesk;
	}

	public void setAnydesk(String anydesk) {
		this.anydesk = anydesk;
	}

	public Integer getSameGoods() {
		return sameGoods;
	}

	public void setSameGoods(Integer sameGoods) {
		this.sameGoods = sameGoods;
	}

	public Integer getSameCus() {
		return sameCus;
	}

	public void setSameCus(Integer sameCus) {
		this.sameCus = sameCus;
	}

	public String getCusUnique() {
		return cusUnique;
	}

	public void setCusUnique(String cusUnique) {
		this.cusUnique = cusUnique;
	}

	public Integer getSmallType() {
		return smallType;
	}

	public void setSmallType(Integer smallType) {
		this.smallType = smallType;
	}

	public Integer getGoodsInPriceType() {
		return goodsInPriceType;
	}

	public void setGoodsInPriceType(Integer goodsInPriceType) {
		this.goodsInPriceType = goodsInPriceType;
	}

	public Integer getIsIoboundInspect() {
		return isIoboundInspect;
	}

	public void setIsIoboundInspect(Integer isIoboundInspect) {
		this.isIoboundInspect = isIoboundInspect;
	}

	public Integer getMatchGoodsType() {
		return matchGoodsType;
	}

	public void setMatchGoodsType(Integer matchGoodsType) {
		this.matchGoodsType = matchGoodsType;
	}

	public Integer getSyncCanyinData() {
		return syncCanyinData;
	}

	public void setSyncCanyinData(Integer syncCanyinData) {
		this.syncCanyinData = syncCanyinData;
	}

	public Integer getSyncBuyhooData() {
		return syncBuyhooData;
	}

	public void setSyncBuyhooData(Integer syncBuyhooData) {
		this.syncBuyhooData = syncBuyhooData;
	}

	public Integer getMemberDay() {
		return memberDay;
	}

	public void setMemberDay(Integer memberDay) {
		this.memberDay = memberDay;
	}

	public Integer getMemberDayType() {
		return memberDayType;
	}

	public void setMemberDayType(Integer memberDayType) {
		this.memberDayType = memberDayType;
	}

	public String getMemberDayValue() {
		return memberDayValue;
	}

	public void setMemberDayValue(String memberDayValue) {
		this.memberDayValue = memberDayValue;
	}

	public BigDecimal getMemberPointMultiple() {
		return memberPointMultiple;
	}

	public void setMemberPointMultiple(BigDecimal memberPointMultiple) {
		this.memberPointMultiple = memberPointMultiple;
	}
}