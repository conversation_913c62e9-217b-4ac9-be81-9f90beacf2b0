package org.haier.shop.entity;

import org.haier.shop.entity.beans.BaseModel;

@SuppressWarnings("serial")
public class SysActionRole extends BaseModel{
	
	private Integer id;
	private String role_code;//角色code
	private String action_code;//权限code
	private String shop_unique;
	public Integer getId() {
		return id;
	}
	public void setId(Integer id) {
		this.id = id;
	}
	public String getRole_code() {
		return role_code;
	}
	public void setRole_code(String role_code) {
		this.role_code = role_code;
	}
	public String getAction_code() {
		return action_code;
	}
	public void setAction_code(String action_code) {
		this.action_code = action_code;
	}
	public String getShop_unique() {
		return shop_unique;
	}
	public void setShop_unique(String shop_unique) {
		this.shop_unique = shop_unique;
	}
	
}
