package org.haier.shop.entity.globalSelect;
import java.io.Serializable;
import java.util.List;

import org.haier.shop.entity.beans.PageData;
/**
  * 全球精选
 * <AUTHOR>
 */
public class FarmDetailVO implements Serializable{
	
	private static final long serialVersionUID = 2596954780945079591L;
	private String id;
	//
	private String shop_unique;
	
	private String barcode;
	//
	private String shop_name;
	
	private String goods_name;
	//
	private String unit;
	//
	private String goods_image;
	
	private String detail_image;
	
	private String goods_video;
	
	private String village_remarks;
	//
	private String start_time;
	//
	private String 	end_time;
	
	private String 	poor_good;
	
	private String 	allInventory;
	
	private String 	secretary_name;
	//
	private String logistics_type;
	//
	private String logistics_money;
	//
	private String secretary_id;
	
	private String source_address;
	//规格
	private List<PageData> specList;
	
	private List<PageData> specList_sup;
	//农户
	private List<PageData> peopleList;
	
	private List<PageData> areaList;
	
	private String audit_reason;
	
	private int audit_status;
	
	private String kind_name;
	
	private String p_kind_name;
	
	private String goods_kind_parunique;
	
	private String goods_kind_unique;
	
	private String shop_share_ratio;
	
	private String shelf_status;
	
	private String sup_shelf_status;
	//视频号状态：：0、未上架（草稿）；1、已上架；
	private int vedio_shelf_status;
	
	
	public String getDetail_image() {
		return detail_image;
	}
	public void setDetail_image(String detail_image) {
		this.detail_image = detail_image;
	}
	public int getVedio_shelf_status() {
		return vedio_shelf_status;
	}
	public void setVedio_shelf_status(int vedio_shelf_status) {
		this.vedio_shelf_status = vedio_shelf_status;
	}
	public String getVillage_remarks() {
		return village_remarks;
	}
	public void setVillage_remarks(String village_remarks) {
		this.village_remarks = village_remarks;
	}
	public String getGoods_video() {
		return goods_video;
	}
	public void setGoods_video(String goods_video) {
		this.goods_video = goods_video;
	}
	public static long getSerialversionuid() {
		return serialVersionUID;
	}
	public String getShelf_status() {
		return shelf_status;
	}
	public void setShelf_status(String shelf_status) {
		this.shelf_status = shelf_status;
	}
	public String getSup_shelf_status() {
		return sup_shelf_status;
	}
	public void setSup_shelf_status(String sup_shelf_status) {
		this.sup_shelf_status = sup_shelf_status;
	}
	public String getPoor_good() {
		return poor_good;
	}
	public void setPoor_good(String poor_good) {
		this.poor_good = poor_good;
	}
	public String getGoods_kind_parunique() {
		return goods_kind_parunique;
	}
	public void setGoods_kind_parunique(String goods_kind_parunique) {
		this.goods_kind_parunique = goods_kind_parunique;
	}
	public String getGoods_kind_unique() {
		return goods_kind_unique;
	}
	public void setGoods_kind_unique(String goods_kind_unique) {
		this.goods_kind_unique = goods_kind_unique;
	}
	public String getKind_name() {
		return kind_name;
	}
	public void setKind_name(String kind_name) {
		this.kind_name = kind_name;
	}
	public String getP_kind_name() {
		return p_kind_name;
	}
	public void setP_kind_name(String p_kind_name) {
		this.p_kind_name = p_kind_name;
	}
	public List<PageData> getAreaList() {
		return areaList;
	}
	public void setAreaList(List<PageData> areaList) {
		this.areaList = areaList;
	}

	public int getAudit_status() {
		return audit_status;
	}
	public void setAudit_status(int audit_status) {
		this.audit_status = audit_status;
	}
	public String getAudit_reason() {
		return audit_reason;
	}
	public void setAudit_reason(String audit_reason) {
		this.audit_reason = audit_reason;
	}
	public String getId() {
		return id;
	}
	public void setId(String id) {
		this.id = id;
	}
	public String getShop_unique() {
		return shop_unique;
	}
	public void setShop_unique(String shop_unique) {
		this.shop_unique = shop_unique;
	}
	public String getBarcode() {
		return barcode;
	}
	public void setBarcode(String barcode) {
		this.barcode = barcode;
	}
	public String getGoods_name() {
		return goods_name;
	}
	public void setGoods_name(String goods_name) {
		this.goods_name = goods_name;
	}
	public String getUnit() {
		return unit;
	}
	public void setUnit(String unit) {
		this.unit = unit;
	}
	public String getGoods_image() {
		return goods_image;
	}
	public void setGoods_image(String goods_image) {
		this.goods_image = goods_image;
	}
	public String getStart_time() {
		return start_time;
	}
	public void setStart_time(String start_time) {
		this.start_time = start_time;
	}
	public String getEnd_time() {
		return end_time;
	}
	public void setEnd_time(String end_time) {
		this.end_time = end_time;
	}
	public String getLogistics_type() {
		return logistics_type;
	}
	public void setLogistics_type(String logistics_type) {
		this.logistics_type = logistics_type;
	}
	public String getLogistics_money() {
		return logistics_money;
	}
	public void setLogistics_money(String logistics_money) {
		this.logistics_money = logistics_money;
	}
	public String getSecretary_id() {
		return secretary_id;
	}
	public void setSecretary_id(String secretary_id) {
		this.secretary_id = secretary_id;
	}
	public String getSource_address() {
		return source_address;
	}
	public void setSource_address(String source_address) {
		this.source_address = source_address;
	}
	public List<PageData> getSpecList() {
		return specList;
	}
	public void setSpecList(List<PageData> specList) {
		this.specList = specList;
	}
	public List<PageData> getPeopleList() {
		return peopleList;
	}
	public void setPeopleList(List<PageData> peopleList) {
		this.peopleList = peopleList;
	}
	public String getAllInventory() {
		return allInventory;
	}
	public void setAllInventory(String allInventory) {
		this.allInventory = allInventory;
	}
	public String getSecretary_name() {
		return secretary_name;
	}
	public void setSecretary_name(String secretary_name) {
		this.secretary_name = secretary_name;
	}
	public String getShop_name() {
		return shop_name;
	}
	public void setShop_name(String shop_name) {
		this.shop_name = shop_name;
	}
	public String getShop_share_ratio() {
		return shop_share_ratio;
	}
	public void setShop_share_ratio(String shop_share_ratio) {
		this.shop_share_ratio = shop_share_ratio;
	}
	public List<PageData> getSpecList_sup() {
		return specList_sup;
	}
	public void setSpecList_sup(List<PageData> specList_sup) {
		this.specList_sup = specList_sup;
	}

	
}
