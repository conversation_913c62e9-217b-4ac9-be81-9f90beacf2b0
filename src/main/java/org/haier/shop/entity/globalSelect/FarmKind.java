package org.haier.shop.entity.globalSelect;

import java.io.Serializable;
import java.util.List;

/**
 * 农产品上行分类信息
 * <AUTHOR>
 *
 */
public class FarmKind implements Serializable{
	/**
	 * 
	 */
	private static final long serialVersionUID = 1L;
	private Integer id;
	private Integer parentId;
	private String kindName;
	private String kindImage;
	private String kindStatus;
	private String parentKindName;
	private Integer goodsCount;
	private List<FarmKind> list;
	
	public Integer getId() {
		return id;
	}
	public void setId(Integer id) {
		this.id = id;
	}
	public Integer getParentId() {
		return parentId;
	}
	public void setParentId(Integer parentId) {
		this.parentId = parentId;
	}
	public String getKindName() {
		return kindName;
	}
	public void setKindName(String kindName) {
		this.kindName = kindName;
	}
	public String getParentKindName() {
		return parentKindName;
	}
	public void setParentKindName(String parentKindName) {
		this.parentKindName = parentKindName;
	}
	public String getKindImage() {
		return kindImage;
	}
	public void setKindImage(String kindImage) {
		this.kindImage = kindImage;
	}
	public String getKindStatus() {
		return kindStatus;
	}
	public void setKindStatus(String kindStatus) {
		this.kindStatus = kindStatus;
	}
	public List<FarmKind> getList() {
		return list;
	}
	public void setList(List<FarmKind> list) {
		this.list = list;
	}
	public Integer getGoodsCount() {
		return goodsCount;
	}
	public void setGoodsCount(Integer goodsCount) {
		this.goodsCount = goodsCount;
	}
}
