package org.haier.shop.entity.globalSelect;
import java.io.Serializable;
import java.util.List;

import org.haier.shop.entity.beans.PageData;
/**
  * 全球精选
 * <AUTHOR>
 */
public class GlobalDetailVO implements Serializable{
	
	private static final long serialVersionUID = 2596954780945079591L;
	private String id;
	//
	private String shop_unique;
	//精选主题
	private String theme_name;
	//开始时间
	private String valid_start;
	//结束时间
	private String valid_end;
	//创建时间
	private String create_time;
	//基础运费
	private String 	base_freight;
	//包邮价格(-1不包邮)
	private String free_cost;
	//审核状态 0未审核 1通过 2审核不通过
	private String audit_status;
	//1 正常 2停用 3删除
	private String theme_status;
	//区域
	private List<PageData> areaList;
	//商品
	private List<PageData> goodsList;
	
	private String audit_reason;
	
	private String poor_good;
	
	private String area_dict;
	
	private String area_dict_parent;
	
	private String secretary_name;
	
	
	public String getPoor_good() {
		return poor_good;
	}
	public void setPoor_good(String poor_good) {
		this.poor_good = poor_good;
	}
	public String getArea_dict() {
		return area_dict;
	}
	public void setArea_dict(String area_dict) {
		this.area_dict = area_dict;
	}
	public String getArea_dict_parent() {
		return area_dict_parent;
	}
	public void setArea_dict_parent(String area_dict_parent) {
		this.area_dict_parent = area_dict_parent;
	}
	public String getSecretary_name() {
		return secretary_name;
	}
	public void setSecretary_name(String secretary_name) {
		this.secretary_name = secretary_name;
	}
	public String getAudit_reason() {
		return audit_reason;
	}
	public void setAudit_reason(String audit_reason) {
		this.audit_reason = audit_reason;
	}
	public String getId() {
		return id;
	}
	public void setId(String id) {
		this.id = id;
	}
	public String getShop_unique() {
		return shop_unique;
	}
	public void setShop_unique(String shop_unique) {
		this.shop_unique = shop_unique;
	}
	public String getTheme_name() {
		return theme_name;
	}
	public void setTheme_name(String theme_name) {
		this.theme_name = theme_name;
	}
	public String getValid_start() {
		return valid_start;
	}
	public void setValid_start(String valid_start) {
		this.valid_start = valid_start;
	}
	public String getValid_end() {
		return valid_end;
	}
	public void setValid_end(String valid_end) {
		this.valid_end = valid_end;
	}
	public String getCreate_time() {
		return create_time;
	}
	public void setCreate_time(String create_time) {
		this.create_time = create_time;
	}
	public String getBase_freight() {
		return base_freight;
	}
	public void setBase_freight(String base_freight) {
		this.base_freight = base_freight;
	}
	public String getFree_cost() {
		return free_cost;
	}
	public void setFree_cost(String free_cost) {
		this.free_cost = free_cost;
	}
	public String getAudit_status() {
		return audit_status;
	}
	public void setAudit_status(String audit_status) {
		this.audit_status = audit_status;
	}
	public String getTheme_status() {
		return theme_status;
	}
	public void setTheme_status(String theme_status) {
		this.theme_status = theme_status;
	}
	public List<PageData> getAreaList() {
		return areaList;
	}
	public void setAreaList(List<PageData> areaList) {
		this.areaList = areaList;
	}
	public List<PageData> getGoodsList() {
		return goodsList;
	}
	public void setGoodsList(List<PageData> goodsList) {
		this.goodsList = goodsList;
	}
	
}
