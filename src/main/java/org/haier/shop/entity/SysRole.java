package org.haier.shop.entity;

import org.haier.shop.entity.beans.BaseModel;

@SuppressWarnings("serial")
public class SysRole extends BaseModel{

	private Integer id;
	private String code;//
	private String name;//角色名称
	private String remark;//备注
	private String shop_unique;//店铺唯一标示
	private String create_times;
	private String update_times;
	private String create_code;//创建人code
	private String update_code;//更新人code
	private String create_name;//创建人名称
	private String update_name;//更新人名称
	private Integer del_flag;//逻辑删除：0，正常，1逻辑删除
	
	private Integer shop_class;
	private String shop_name;
	
	public String getShop_unique() {
		return shop_unique;
	}
	public void setShop_unique(String shop_unique) {
		this.shop_unique = shop_unique;
	}
	public String getCreate_code() {
		return create_code;
	}
	public void setCreate_code(String create_code) {
		this.create_code = create_code;
	}
	public String getUpdate_code() {
		return update_code;
	}
	public void setUpdate_code(String update_code) {
		this.update_code = update_code;
	}
	public String getCreate_name() {
		return create_name;
	}
	public void setCreate_name(String create_name) {
		this.create_name = create_name;
	}
	public String getUpdate_name() {
		return update_name;
	}
	public void setUpdate_name(String update_name) {
		this.update_name = update_name;
	}
	public Integer getDel_flag() {
		return del_flag;
	}
	public void setDel_flag(Integer del_flag) {
		this.del_flag = del_flag;
	}
	public Integer getId() {
		return id;
	}
	public void setId(Integer id) {
		this.id = id;
	}
	public String getCode() {
		return code;
	}
	public void setCode(String code) {
		this.code = code;
	}
	public String getName() {
		return name;
	}
	public void setName(String name) {
		this.name = name;
	}
	public String getRemark() {
		return remark;
	}
	public void setRemark(String remark) {
		this.remark = remark;
	}
	public String getCreate_times() {
		return create_times;
	}
	public void setCreate_times(String create_times) {
		this.create_times = create_times;
	}
	public String getUpdate_times() {
		return update_times;
	}
	public void setUpdate_times(String update_times) {
		this.update_times = update_times;
	}
	public Integer getShop_class() {
		return shop_class;
	}
	public void setShop_class(Integer shop_class) {
		this.shop_class = shop_class;
	}
	public String getShop_name() {
		return shop_name;
	}
	public void setShop_name(String shop_name) {
		this.shop_name = shop_name;
	}
	
}
