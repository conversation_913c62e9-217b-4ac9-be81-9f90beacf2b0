package org.haier.shop.entity;

import com.alipay.api.domain.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.util.Date;

/**
 * 监控设备信息对象 monitor_info
 *
 * <AUTHOR>
 * @date 2022-06-07
 */
public class MonitorInfo
{
    private static final long serialVersionUID = 1L;

    /** 主键 */
    private Long monitorInfoId;

    /** 序列号 */
    private String serialNumber;

    /** 商品ID */
    private String monitorName;

    /** 状态 0启用 1停用 */
    private Integer state;
    /**
     * 新增时间
     */
    private Date createTime;

    /**
     * 修改时间
     */
    private Date updateTime;

    public void setMonitorInfoId(Long monitorInfoId)
    {
        this.monitorInfoId = monitorInfoId;
    }

    public Long getMonitorInfoId()
    {
        return monitorInfoId;
    }
    public void setSerialNumber(String serialNumber)
    {
        this.serialNumber = serialNumber;
    }

    public String getSerialNumber()
    {
        return serialNumber;
    }
    public void setMonitorName(String monitorName)
    {
        this.monitorName = monitorName;
    }

    public String getMonitorName()
    {
        return monitorName;
    }
    public void setState(Integer state)
    {
        this.state = state;
    }

    public Integer getState()
    {
        return state;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
                .append("monitorInfoId", getMonitorInfoId())
                .append("serialNumber", getSerialNumber())
                .append("monitorName", getMonitorName())
                .append("state", getState())
                .append("updateTime", getUpdateTime())
                .append("createTime", getCreateTime())
                .toString();
    }
}
