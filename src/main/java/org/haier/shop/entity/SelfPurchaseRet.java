package org.haier.shop.entity;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 进货订单退货表
 * <AUTHOR> 
 * @ClassName SelfPurchaseRet
 * @Date 2024-05-20
 **/
public class SelfPurchaseRet {
	private Integer id;

	/**
	* 进货订单号
	*/
	private String selfPurchaseUnique;

	/**
	* 退货订单号
	*/
	private String selfPurchaseRetUnique;

	/**
	* 店铺编号，方便统计退货信息
	*/
	private Long shopUnique;

	/**
	* 供货商编号
	*/
	private Long supplierUnique;

	/**
	* 本次退货商品总数量
	*/
	private BigDecimal totalCount;

	/**
	* 本次退货总金额
	*/
	private BigDecimal totalPrice;

	/**
	* 本次退货备注
	*/
	private String remark;

	/**
	* 退货时间
	*/
	private Date createTime;

	/**
	* 退货单更新时间
	*/
	private Date updateTime;

	/**
	* 退货申请员工编号
	*/
	private String createStaffId;

	/**
	* 退货订单有效标识：1、有效；2、无效删除
	*/
	private Integer delFlag;

	public Integer getId() {
		return id;
	}

	public void setId(Integer id) {
		this.id = id;
	}

	public String getSelfPurchaseUnique() {
		return selfPurchaseUnique;
	}

	public void setSelfPurchaseUnique(String selfPurchaseUnique) {
		this.selfPurchaseUnique = selfPurchaseUnique;
	}

	public String getSelfPurchaseRetUnique() {
		return selfPurchaseRetUnique;
	}

	public void setSelfPurchaseRetUnique(String selfPurchaseRetUnique) {
		this.selfPurchaseRetUnique = selfPurchaseRetUnique;
	}

	public Long getShopUnique() {
		return shopUnique;
	}

	public void setShopUnique(Long shopUnique) {
		this.shopUnique = shopUnique;
	}

	public Long getSupplierUnique() {
		return supplierUnique;
	}

	public void setSupplierUnique(Long supplierUnique) {
		this.supplierUnique = supplierUnique;
	}

	public BigDecimal getTotalCount() {
		return totalCount;
	}

	public void setTotalCount(BigDecimal totalCount) {
		this.totalCount = totalCount;
	}

	public BigDecimal getTotalPrice() {
		return totalPrice;
	}

	public void setTotalPrice(BigDecimal totalPrice) {
		this.totalPrice = totalPrice;
	}

	public String getRemark() {
		return remark;
	}

	public void setRemark(String remark) {
		this.remark = remark;
	}

	public Date getCreateTime() {
		return createTime;
	}

	public void setCreateTime(Date createTime) {
		this.createTime = createTime;
	}

	public Date getUpdateTime() {
		return updateTime;
	}

	public void setUpdateTime(Date updateTime) {
		this.updateTime = updateTime;
	}

	public String getCreateStaffId() {
		return createStaffId;
	}

	public void setCreateStaffId(String createStaffId) {
		this.createStaffId = createStaffId;
	}

	public Integer getDelFlag() {
		return delFlag;
	}

	public void setDelFlag(Integer delFlag) {
		this.delFlag = delFlag;
	}
}