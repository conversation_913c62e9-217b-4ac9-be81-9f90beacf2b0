package org.haier.shop.entity;

import java.util.List;


/**
 * 类名:com.palmshop.online.entity.SaleList;
 * 描述:销售单的实体类
 * <AUTHOR>
 *
 */
public class SaleList {

	
	
	private Integer saleListId;//订单id
	private Long saleListUnique;//订单唯一性标识
	
	private Long shopUnique;//店铺唯一性标识
	
	private String saleListDatetime;//订单生成时间
	private Double saleListTotal;//销售金额总计
	
	private Double saleListPur;//订单进货价
	
	private Integer saleListTotalCount;//销售商品总数量
	
	private String cusUnique;//用户id
	
	private Integer saleType;//订单类型（0，实体店销售；1，网上订单）
	
	
	private String saleListName;//订单收货人姓名
	
	private String saleListPhone;//订单收货人联系电话
	
	private String saleListAddress;//订单送货地址，实体店销售为空，网店按实际填写
	
	private Double saleListDelfee;//外送费
	private Double saleListDiscount;//订单折扣率
	
	private Integer saleListState;//付款状态，2付款，1未付款
	private Integer saleListHandlestate;//发货状态：1-未发货，2-已发货,3-已收货
	private Integer saleListPayment;//支付方式：0-现金，1-支付宝，2-微信，3-银行卡
	private String saleListRemarks;//订单的备注信息
	
	private Integer saleListNumber;//订单的每日序号
	
	private Integer saleListCashier;//订单的 收银员id
	private Integer machineNum;//订单的收银机器编号ID
	
	private Integer saleListSameType;//1：PC已同步；2：PC未同步;
	
	private Double saleListActuallyReceived;//订单实际收到金额
	
	private Double memberCard;//会员卡支付金额
	
	private List<SaleListDetail>listDetails;//订单详情
	
	private Double commission_sum;//总提成
	private String payTime;//支付时间格式为yyyy-MM-dd HH:mm:ss
	private String tradeNo;//易流水号，支付宝或者微信返回交易号
	private Double platform_shop_beans;//平台赠送商家百货豆
	
	
	
	
	/**
	 * @return the platform_shop_beans
	 */
	public Double getPlatform_shop_beans() {
		return platform_shop_beans;
	}
	/**
	 * @param platform_shop_beans the platform_shop_beans to set
	 */
	public void setPlatform_shop_beans(Double platform_shop_beans) {
		this.platform_shop_beans = platform_shop_beans;
	}
	private Integer cus_id;
	
	public String getPayTime() {
		return payTime;
	}
	public void setPayTime(String payTime) {
		this.payTime = payTime;
	}
	public String getTradeNo() {
		return tradeNo;
	}
	public void setTradeNo(String tradeNo) {
		this.tradeNo = tradeNo;
	}
	/**
	 * @return the saleListActuallyReceived
	 */
	public Double getSaleListActuallyReceived() {
		return saleListActuallyReceived;
	}
	/**
	 * @param saleListActuallyReceived the saleListActuallyReceived to set
	 */
	public void setSaleListActuallyReceived(Double saleListActuallyReceived) {
		this.saleListActuallyReceived = saleListActuallyReceived;
	}
	/**
	 * @return the saleListSameType
	 */
	public Integer getSaleListSameType() {
		return saleListSameType;
	}
	/**
	 * @param saleListSameType the saleListSameType to set
	 */
	public void setSaleListSameType(Integer saleListSameType) {
		this.saleListSameType = saleListSameType;
	}
	/**
	 * @return the saleListCashier
	 */
	public Integer getSaleListCashier() {
		
		if(saleListCashier==null){
			
			saleListCashier=0;
			
		}
		
		return saleListCashier;
	}
	/**
	 * @param saleListCashier the saleListCashier to set
	 */
	public void setSaleListCashier(Integer saleListCashier) {
		this.saleListCashier = saleListCashier;
	}
	/**
	 * @return the saleListPur
	 */
	public Double getSaleListPur() {
		return saleListPur;
	}
	/**
	 * @param saleListPur the saleListPur to set
	 */
	public void setSaleListPur(Double saleListPur) {
		this.saleListPur = saleListPur;
	}
	/**
	 * @return the saleListNumber
	 */
	public Integer getSaleListNumber() {
		return saleListNumber;
	}
	/**
	 * @param saleListNumber the saleListNumber to set
	 */
	public void setSaleListNumber(Integer saleListNumber) {
		this.saleListNumber = saleListNumber;
	}
	/**
	 * @return the saleListName
	 */
	public String getSaleListName() {
		return saleListName;
	}
	/**
	 * @param saleListName the saleListName to set
	 */
	public void setSaleListName(String saleListName) {
		this.saleListName = saleListName;
	}
	/**
	 * @return the saleListPhone
	 */
	public String getSaleListPhone() {
		return saleListPhone;
	}
	/**
	 * @param saleListPhone the saleListPhone to set
	 */
	public void setSaleListPhone(String saleListPhone) {
		this.saleListPhone = saleListPhone;
	}
	/**
	 * @return the saleListTotalCount
	 */
	public Integer getSaleListTotalCount() {
		return saleListTotalCount;
	}
	/**
	 * @param saleListTotalCount the saleListTotalCount to set
	 */
	public void setSaleListTotalCount(Integer saleListTotalCount) {
		this.saleListTotalCount = saleListTotalCount;
	}
	/**
	 * @return the listDetails
	 */
	public List<SaleListDetail> getListDetails() {
		return listDetails;
	}
	/**
	 * @param listDetails the listDetails to set
	 */
	public void setListDetails(List<SaleListDetail> listDetails) {
		this.listDetails = listDetails;
	}
	public Long getShopUnique() {
		return shopUnique;
	}
	public void setShopUnique(Long shopUnique) {
		this.shopUnique = shopUnique;
	}
	public String getSaleListRemarks() {
		return saleListRemarks;
	}
	public void setSaleListRemarks(String saleListRemarks) {
		this.saleListRemarks = saleListRemarks;
	}
	public Integer getSaleListId() {
		return saleListId;
	}
	public void setSaleListId(Integer saleListId) {
		this.saleListId = saleListId;
	}
	public Long getSaleListUnique() {
		return saleListUnique;
	}
	public void setSaleListUnique(Long saleListUnique) {
		this.saleListUnique = saleListUnique;
	}
	public String getSaleListDatetime() {
		return saleListDatetime;
	}
	public void setSaleListDatetime(String saleListDatetime) {
		this.saleListDatetime = saleListDatetime;
	}
	public Double getSaleListTotal() {
		return saleListTotal;
	}
	public void setSaleListTotal(Double saleListTotal) {
		this.saleListTotal = saleListTotal;
	}
	public String getCusUnique() {
		return cusUnique;
	}
	public void setCusUnique(String cusUnique) {
		this.cusUnique = cusUnique;
	}
	public Integer getSaleType() {
		return saleType;
	}
	public void setSaleType(Integer saleType) {
		this.saleType = saleType;
	}
	public String getSaleListAddress() {
		return saleListAddress;
	}
	public void setSaleListAddress(String saleListAddress) {
		this.saleListAddress = saleListAddress;
	}
	public Double getSaleListDelfee() {
		return saleListDelfee;
	}
	public void setSaleListDelfee(Double saleListDelfee) {
		this.saleListDelfee = saleListDelfee;
	}
	public Double getSaleListDiscount() {
		return saleListDiscount;
	}
	public void setSaleListDiscount(Double saleListDiscount) {
		this.saleListDiscount = saleListDiscount;
	}
	public Integer getSaleListState() {
		return saleListState;
	}
	public void setSaleListState(Integer saleListState) {
		this.saleListState = saleListState;
	}
	public Integer getSaleListHandlestate() {
		return saleListHandlestate;
	}
	public void setSaleListHandlestate(Integer saleListHandlestate) {
		this.saleListHandlestate = saleListHandlestate;
	}
	public Integer getSaleListPayment() {
		return saleListPayment;
	}
	public void setSaleListPayment(Integer saleListPayment) {
		this.saleListPayment = saleListPayment;
	}
	/**
	 * @return the machineId
	 */
	/**
	 * @return the machineNum
	 */
	public Integer getMachineNum() {
		return machineNum;
	}
	/**
	 * @param machineNum the machineNum to set
	 */
	public void setMachineNum(Integer machineNum) {
		this.machineNum = machineNum;
	}
	
	/**
	 * @return the memberCard
	 */
	public Double getMemberCard() {
		return memberCard;
	}
	/**
	 * @param memberCard the memberCard to set
	 */
	public void setMemberCard(Double memberCard) {
		this.memberCard = memberCard;
	}
	
	
	/**
	 * @return the commission_sum
	 */
	public Double getCommission_sum() {
		return commission_sum;
	}
	/**
	 * @param commission_sum the commission_sum to set
	 */
	public void setCommission_sum(Double commission_sum) {
		this.commission_sum = commission_sum;
	}
	
	
	/**
	 * @return the cus_id
	 */
	public Integer getCus_id() {
		return cus_id;
	}
	/**
	 * @param cus_id the cus_id to set
	 */
	public void setCus_id(Integer cus_id) {
		this.cus_id = cus_id;
	}
	/* (non-Javadoc)
	 * @see java.lang.Object#toString()
	 */
	@Override
	public String toString() {
		return "SaleList [saleListId=" + saleListId + ", saleListUnique=" + saleListUnique + ", shopUnique=" + shopUnique + ", saleListDatetime=" + saleListDatetime + ", saleListTotal=" + saleListTotal + ", saleListPur=" + saleListPur + ", saleListTotalCount=" + saleListTotalCount + ", cusUnique="
						+ cusUnique + ", saleType=" + saleType + ", saleListName=" + saleListName + ", saleListPhone=" + saleListPhone + ", saleListAddress=" + saleListAddress + ", saleListDelfee=" + saleListDelfee + ", saleListDiscount=" + saleListDiscount + ", saleListState=" + saleListState
						+ ", saleListHandlestate=" + saleListHandlestate + ", saleListPayment=" + saleListPayment + ", saleListRemarks=" + saleListRemarks + ", saleListNumber=" + saleListNumber + ", saleListCashier=" + saleListCashier + ", machineNum=" + machineNum + ", saleListSameType="
						+ saleListSameType + ", saleListActuallyReceived=" + saleListActuallyReceived + ", listDetails=" + listDetails+ ", memberCard=" + memberCard + "]";
	}
}
