package org.haier.shop.entity;

public class LoginSignOutStatisticsDetail {
	public Integer payMethod;//支付方式
	public String payMent;//支付方式说明
	public Double paymentTotal;//支付总额
	
	public LoginSignOutStatisticsDetail() {
		super();
	}
	public LoginSignOutStatisticsDetail(String payMent, Double paymentTotal) {
		super();
		this.payMent = payMent;
		this.paymentTotal = paymentTotal;
	}
	public LoginSignOutStatisticsDetail(Integer payMethod, String payMent, Double paymentTotal) {
		super();
		this.payMethod = payMethod;
		this.payMent = payMent;
		this.paymentTotal = paymentTotal;
	}
	
	
	public Integer getPayMethod() {
		return payMethod;
	}
	public void setPayMethod(Integer payMethod) {
		this.payMethod = payMethod;
	}
	public String getPayMent() {
		return payMent;
	}
	public void setPayMent(String payMent) {
		this.payMent = payMent;
	}
	public Double getPaymentTotal() {
		return paymentTotal;
	}
	public void setPaymentTotal(Double paymentTotal) {
		this.paymentTotal = paymentTotal;
	}
	@Override
	public String toString() {
		return "LoginSignOutStatisticsDetail [payMethod=" + payMethod + ", payMent=" + payMent + ", paymentTotal="
				+ paymentTotal + "]";
	}
}
