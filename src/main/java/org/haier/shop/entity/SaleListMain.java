package org.haier.shop.entity;

import org.haier.shop.params.goodsBatch.GoodsSaleBatchData;

import java.util.List;
import java.util.Map;

/**
 * 订单管理员信息
 * <AUTHOR>
 */
public class SaleListMain {
	
	//店铺编号
	private String shopUnique;
	//订单编号
	private String saleListUnique;
	//订单生成时间
	private String saleListDatetime;
	//订单完成时间
	private String receiptDatetime;
	//会员编号
	private String cusUnique;
	//顾客姓名
	private String cusName;
	//联系方式
	private String cusPhone;
	//应收金额
	private Double saleListTotal;
	//付款状态
	private String saleListState;
	//订单付款状态
	private String saleListStateCode;
	//实收金额
	private Double saleListActuallyReceived;
	//订单商品总数量
	private Double saleListTotalCount;
	//订单商品数量
	private Double saleListCount;
	//订单支付方式
	private Integer saleListPayment;
	//员工姓名
	private String saleListName;
	//员工id
	private String sale_list_cashier;
	//支付方式
	private String payType;
	//支付状态
	private String payStatus;
	//订单支付详情
	private Double actuallyReceived;
	//提成总和
	private Double commission_sum;
	//订单处理状态
	private Integer saleListHandleStateCode;
	//订单处理状态
	private String saleListHandleState;
	//员工名称
	private String staffName;
	//订单总进价
	private Double saleListPur;
	//订单备注
	private String saleListRemarks;
	//配送费
	private Double saleListDelfee;
	//商家补贴配送费
	private Double shopSubsidyDelfee;
	//优惠券优惠金额
	private Double couponAmount;
	//店铺优惠券金额
	private Double shopCouponAmount;
	//收货人电话
	private String saleListPhone;
	//收货地址
	private String saleListAddress;
	//配送方式0：自配送 1：美团配送 2:一刻钟配送
	private Integer deliveryType;
	//百货豆抵扣金额
	private Double beansMoney;
	//订单类型
	private Integer saleType;
	//百货豆使用数量
	private Integer beansUse;
	//订单详情
	private List<SaleListDetail> listDetail;
	//订单支付详情
	private List<SaleListPayDetail> payDetail;
	//商品导入专用；1：成功；2：失败
	private Integer errorCode;
	//商品导入专用；当失败的时候，给出提示信息
	private String errorMsg;
	//核单退还差价
	private Double returnPrice;
	//订单核实信息
	List<Map<String ,Object>> saleListVerifyList;
	//分销佣金
	private Double commission;
	//红包抵扣金额
	private Double red_use;
	//赊销还款额
	private Double return_money;
	//赊销还款本金
	private Double principal_money;
	//赊销还款状态
	private Integer pay_status;
	//支付终端
	private Integer source_type;
	private String sourceType;

	/**
	 * 出库批次
	 */
	private List<GoodsSaleBatchData> goodsSaleBatchList;


	public List<GoodsSaleBatchData> getGoodsSaleBatchList() {
		return goodsSaleBatchList;
	}

	public void setGoodsSaleBatchList(List<GoodsSaleBatchData> goodsSaleBatchList) {
		this.goodsSaleBatchList = goodsSaleBatchList;
	}

	public String getReceiptDatetime() {
		return receiptDatetime;
	}
	public void setReceiptDatetime(String receiptDatetime) {
		this.receiptDatetime = receiptDatetime;
	}
	public Double getShopCouponAmount() {
		return shopCouponAmount;
	}
	public void setShopCouponAmount(Double shopCouponAmount) {
		this.shopCouponAmount = shopCouponAmount;
	}
	public String getSaleListStateCode() {
		return saleListStateCode;
	}
	public void setSaleListStateCode(String saleListStateCode) {
		this.saleListStateCode = saleListStateCode;
	}
	public Double getRed_use() {
		return red_use;
	}
	public void setRed_use(Double red_use) {
		this.red_use = red_use;
	}
	public Double getCommission() {
		return commission;
	}
	public void setCommission(Double commission) {
		this.commission = commission;
	}
	public List<Map<String, Object>> getSaleListVerifyList() {
		return saleListVerifyList;
	}
	public void setSaleListVerifyList(List<Map<String, Object>> saleListVerifyList) {
		this.saleListVerifyList = saleListVerifyList;
	}
	public Double getReturnPrice() {
		return returnPrice;
	}
	public void setReturnPrice(Double returnPrice) {
		this.returnPrice = returnPrice;
	}
	public Double getShopSubsidyDelfee() {
		return shopSubsidyDelfee;
	}
	public void setShopSubsidyDelfee(Double shopSubsidyDelfee) {
		this.shopSubsidyDelfee = shopSubsidyDelfee;
	}
	public Integer getSaleListPayment() {
		return saleListPayment;
	}
	public void setSaleListPayment(Integer saleListPayment) {
		this.saleListPayment = saleListPayment;
	}
	public String getSaleListState() {
		return saleListState;
	}
	public void setSaleListState(String saleListState) {
		this.saleListState = saleListState;
	}
	public String getShopUnique() {
		return shopUnique;
	}
	public void setShopUnique(String shopUnique) {
		this.shopUnique = shopUnique;
	}
	public String getCusUnique() {
		return cusUnique;
	}
	public void setCusUnique(String cusUnique) {
		this.cusUnique = cusUnique;
	}
	public Integer getBeansUse() {
		return beansUse;
	}
	public void setBeansUse(Integer beansUse) {
		this.beansUse = beansUse;
	}
	public Integer getSaleType() {
		return saleType;
	}
	public void setSaleType(Integer saleType) {
		this.saleType = saleType;
	}
	public String getSale_list_cashier() {
		return sale_list_cashier;
	}
	public void setSale_list_cashier(String sale_list_cashier) {
		this.sale_list_cashier = sale_list_cashier;
	}
	public Double getBeansMoney() {
		return beansMoney;
	}
	public void setBeansMoney(Double beansMoney) {
		this.beansMoney = beansMoney;
	}
	public String getSaleListPhone() {
		return saleListPhone;
	}
	public void setSaleListPhone(String saleListPhone) {
		this.saleListPhone = saleListPhone;
	}
	public String getSaleListAddress() {
		return saleListAddress;
	}
	public void setSaleListAddress(String saleListAddress) {
		this.saleListAddress = saleListAddress;
	}
	public Integer getDeliveryType() {
		return deliveryType;
	}
	public void setDeliveryType(Integer deliveryType) {
		this.deliveryType = deliveryType;
	}
	public Double getCouponAmount() {
		return couponAmount;
	}
	public void setCouponAmount(Double couponAmount) {
		this.couponAmount = couponAmount;
	}
	public Double getSaleListDelfee() {
		return saleListDelfee;
	}
	public void setSaleListDelfee(Double saleListDelfee) {
		this.saleListDelfee = saleListDelfee;
	}
	public String getSaleListUnique() {
		return saleListUnique;
	}
	public void setSaleListUnique(String saleListUnique) {
		this.saleListUnique = saleListUnique;
	}
	public String getSaleListDatetime() {
		return saleListDatetime;
	}
	public void setSaleListDatetime(String saleListDatetime) {
		this.saleListDatetime = saleListDatetime;
	}
	public String getCusName() {
		return cusName;
	}
	public void setCusName(String cusName) {
		this.cusName = cusName;
	}
	public String getCusPhone() {
		return cusPhone;
	}
	public void setCusPhone(String cusPhone) {
		this.cusPhone = cusPhone;
	}
	public Double getSaleListTotal() {
		return saleListTotal;
	}
	public void setSaleListTotal(Double saleListTotal) {
		this.saleListTotal = saleListTotal;
	}
	public Double getSaleListActuallyReceived() {
		return saleListActuallyReceived;
	}
	public void setSaleListActuallyReceived(Double saleListActuallyReceived) {
		this.saleListActuallyReceived = saleListActuallyReceived;
	}
	public Double getSaleListCount() {
		return saleListCount;
	}
	public void setSaleListCount(Double saleListCount) {
		this.saleListCount = saleListCount;
	}
	public String getPayType() {
		return payType;
	}
	public void setPayType(String payType) {
		this.payType = payType;
	}
	public String getPayStatus() {
		return payStatus;
	}
	public void setPayStatus(String payStatus) {
		this.payStatus = payStatus;
	}
	public List<SaleListDetail> getListDetail() {
		return listDetail;
	}
	public void setListDetail(List<SaleListDetail> listDetail) {
		this.listDetail = listDetail;
	}
	
	public Double getCommission_sum() {
		return commission_sum;
	}
	public void setCommission_sum(Double commission_sum) {
		this.commission_sum = commission_sum;
	}
	public List<SaleListPayDetail> getPayDetail() {
		return payDetail;
	}
	public void setPayDetail(List<SaleListPayDetail> payDetail) {
		this.payDetail = payDetail;
	}
	public String getStaffName() {
		return staffName;
	}
	public void setStaffName(String staffName) {
		this.staffName = staffName;
	}
	public String getSaleListName() {
		return saleListName;
	}
	public void setSaleListName(String saleListName) {
		this.saleListName = saleListName;
	}
	public Integer getSaleListHandleStateCode() {
		return saleListHandleStateCode;
	}
	public void setSaleListHandleStateCode(Integer saleListHandleStateCode) {
		this.saleListHandleStateCode = saleListHandleStateCode;
	}
	public String getSaleListHandleState() {
		return saleListHandleState;
	}
	public void setSaleListHandleState(String saleListHandleState) {
		this.saleListHandleState = saleListHandleState;
	}
	public String getSaleListRemarks() {
		return saleListRemarks;
	}
	public void setSaleListRemarks(String saleListRemarks) {
		this.saleListRemarks = saleListRemarks;
	}
	public Double getActuallyReceived() {
		return actuallyReceived;
	}
	public void setActuallyReceived(Double actuallyReceived) {
		this.actuallyReceived = actuallyReceived;
	}
	public Double getSaleListTotalCount() {
		return saleListTotalCount;
	}
	public void setSaleListTotalCount(Double saleListTotalCount) {
		this.saleListTotalCount = saleListTotalCount;
	}
	public Double getSaleListPur() {
		return saleListPur;
	}
	public void setSaleListPur(Double saleListPur) {
		this.saleListPur = saleListPur;
	}
	public Integer getErrorCode() {
		return errorCode;
	}
	public void setErrorCode(Integer errorCode) {
		this.errorCode = errorCode;
	}
	public String getErrorMsg() {
		return errorMsg;
	}
	public void setErrorMsg(String errorMsg) {
		this.errorMsg = errorMsg;
	}
	public Double getReturn_money() {
		return return_money;
	}
	public void setReturn_money(Double return_money) {
		this.return_money = return_money;
	}
	public Double getPrincipal_money() {
		return principal_money;
	}
	public void setPrincipal_money(Double principal_money) {
		this.principal_money = principal_money;
	}
	public Integer getPay_status() {
		return pay_status;
	}
	public void setPay_status(Integer pay_status) {
		this.pay_status = pay_status;
	}

	public Integer getSource_type() {
		return source_type;
	}

	public void setSource_type(Integer source_type) {
		this.source_type = source_type;
	}

	public String getSourceType() {
		return sourceType;
	}

	public void setSourceType(String sourceType) {
		this.sourceType = sourceType;
	}

	@Override
	public String toString() {
		return "SaleListMain [shopUnique=" + shopUnique + ", saleListUnique=" + saleListUnique + ", saleListDatetime="
				+ saleListDatetime + ", cusUnique=" + cusUnique + ", cusName=" + cusName + ", cusPhone=" + cusPhone
				+ ", saleListTotal=" + saleListTotal + ", saleListActuallyReceived=" + saleListActuallyReceived
				+ ", saleListTotalCount=" + saleListTotalCount + ", saleListCount=" + saleListCount + ", saleListName="
				+ saleListName + ", sale_list_cashier=" + sale_list_cashier + ", payType=" + payType + ", payStatus="
				+ payStatus + ", actuallyReceived=" + actuallyReceived + ", commission_sum=" + commission_sum
				+ ", saleListHandleStateCode=" + saleListHandleStateCode + ", saleListHandleState="
				+ saleListHandleState + ", staffName=" + staffName + ", saleListPur=" + saleListPur
				+ ", saleListRemarks=" + saleListRemarks + ", saleListDelfee=" + saleListDelfee + ", couponAmount="
				+ couponAmount + ", saleListPhone=" + saleListPhone + ", saleListAddress=" + saleListAddress
				+ ", deliveryType=" + deliveryType + ", beansMoney=" + beansMoney + ", saleType=" + saleType
				+ ", beansUse=" + beansUse + ", listDetail=" + listDetail + ", payDetail=" + payDetail + ", errorCode="
				+ errorCode + ", errorMsg=" + errorMsg + "]";
	}
}
