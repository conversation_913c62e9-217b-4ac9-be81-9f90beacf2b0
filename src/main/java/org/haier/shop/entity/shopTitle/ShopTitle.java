package org.haier.shop.entity.shopTitle;

public class ShopTitle {
    //ID
    private Long id;
    //店铺编号
    private Long shopUnique;
    //标题
    private String titleName;
    //图像地址
    private String titleImg;
    //排序
    private Integer titleSort;
    //模块编号
    private Integer modularNum;
    private String addTime;
    //是否主页显示：1、显示；2、不显示；
    private Integer showType;
    //是否有效：1、有效；2、无效；
    private Integer validType;
    //所属模块：1、店铺管理；2、商品管理；3、其他
    private Integer modularType;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getShopUnique() {
        return shopUnique;
    }

    public void setShopUnique(Long shopUnique) {
        this.shopUnique = shopUnique;
    }

    public String getTitleName() {
        return titleName;
    }

    public void setTitleName(String titleName) {
        this.titleName = titleName;
    }

    public String getTitleImg() {
        return titleImg;
    }

    public void setTitleImg(String titleImg) {
        this.titleImg = titleImg;
    }

    public Integer getTitleSort() {
        return titleSort;
    }

    public void setTitleSort(Integer titleSort) {
        this.titleSort = titleSort;
    }

    public Integer getModularNum() {
        return modularNum;
    }

    public void setModularNum(Integer modularNum) {
        this.modularNum = modularNum;
    }

    public String getAddTime() {
        return addTime;
    }

    public void setAddTime(String addTime) {
        this.addTime = addTime;
    }

    public Integer getShowType() {
        return showType;
    }

    public void setShowType(Integer showType) {
        this.showType = showType;
    }

    public Integer getValidType() {
        return validType;
    }

    public void setValidType(Integer validType) {
        this.validType = validType;
    }

    public Integer getModularType() {
        return modularType;
    }

    public void setModularType(Integer modularType) {
        this.modularType = modularType;
    }
}
