package org.haier.shop.entity;

import java.util.List;

public class LoginSignOutStatistics {

	private String staffId;//收银员编号
	private String staffName;//收银员名称
	private String shopUnique;//店铺编号
	private List<LoginSignOutStatisticsDetail> list;//支付详情
	public String getStaffId() {
		return staffId;
	}
	public void setStaffId(String staffId) {
		this.staffId = staffId;
	}
	public String getStaffName() {
		return staffName;
	}
	public void setStaffName(String staffName) {
		this.staffName = staffName;
	}
	public String getShopUnique() {
		return shopUnique;
	}
	public void setShopUnique(String shopUnique) {
		this.shopUnique = shopUnique;
	}
	public List<LoginSignOutStatisticsDetail> getList() {
		return list;
	}
	public void setList(List<LoginSignOutStatisticsDetail> list) {
		this.list = list;
	}
	@Override
	public String toString() {
		return "LoginSignOutStatistics [staffId=" + staffId + ", staffName=" + staffName + ", shopUnique=" + shopUnique
				+ ", list=" + list + "]";
	}
}
