package org.haier.shop.entity;

import java.math.BigDecimal;

public class SaleListDetail {
	
	
	private Integer saleListDetailId;//订单id
	
	private Long saleListUnique;//订单唯一性标识
	
	private String saleListUniques;//订单唯一性编号
	
	private double saleListDetailCount;//商品数量
	
	private Double saleListDetailPrice;//商品价格
	
	private Double goodsOldPrice;//商品原价
	
	private String goodsUnique;//商品唯一性标识
	
	private Long goodsId;//商品id
	
	private Double goodsInPrice;
	
	private Double commission_total;//提成小计
	//商品条码
	private String goodsBarcode;
	//商品名称
	private String goodsName;
	//商品数量
	private Double goodsCount;
	//商品售价
	private Double goodsSalePrice;
	//商品图片信息
	private String goodsPicturepath;
	//小计
	private Double subtotal;
	//提成小计
	private Double commissionTotal;
	
	//商品进价
	private Double goodsPurprice;
	
	private Double retListDetailCount;

	private String firstKindName;
	private String secondKindName;
	private BigDecimal stockPrice;

	private BigDecimal goodsAvgOutPrice;

	public BigDecimal getGoodsAvgOutPrice() {
		return goodsAvgOutPrice;
	}

	public void setGoodsAvgOutPrice(BigDecimal goodsAvgOutPrice) {
		this.goodsAvgOutPrice = goodsAvgOutPrice;
	}

	public Double getRetListDetailCount() {
		return retListDetailCount;
	}
	public void setRetListDetailCount(Double retListDetailCount) {
		this.retListDetailCount = retListDetailCount;
	}
	public String getSaleListUniques() {
		return saleListUniques;
	}
	public void setSaleListUniques(String saleListUniques) {
		this.saleListUniques = saleListUniques;
	}
	
	public String getGoodsBarcode() {
		return goodsBarcode;
	}
	public void setGoodsBarcode(String goodsBarcode) {
		this.goodsBarcode = goodsBarcode;
	}
	public String getGoodsName() {
		return goodsName;
	}
	public void setGoodsName(String goodsName) {
		this.goodsName = goodsName;
	}
	public Double getGoodsCount() {
		return goodsCount;
	}
	public void setGoodsCount(Double goodsCount) {
		this.goodsCount = goodsCount;
	}
	public Double getGoodsSalePrice() {
		return goodsSalePrice;
	}
	public void setGoodsSalePrice(Double goodsSalePrice) {
		this.goodsSalePrice = goodsSalePrice;
	}
	public Double getSubtotal() {
		return subtotal;
	}
	public void setSubtotal(Double subtotal) {
		this.subtotal = subtotal;
	}
	public String getGoodsPicturepath() {
		return goodsPicturepath;
	}
	public void setGoodsPicturepath(String goodsPicturepath) {
		this.goodsPicturepath = goodsPicturepath;
	}
	public Double getCommissionTotal() {
		return commissionTotal;
	}
	public void setCommissionTotal(Double commissionTotal) {
		this.commissionTotal = commissionTotal;
	}
	public Double getGoodsOldPrice() {
		return goodsOldPrice;
	}
	public void setGoodsOldPrice(Double goodsOldPrice) {
		this.goodsOldPrice = goodsOldPrice;
	}
	public Integer getSaleListDetailId() {
		return saleListDetailId;
	}
	public void setSaleListDetailId(Integer saleListDetailId) {
		this.saleListDetailId = saleListDetailId;
	}
	public Long getSaleListUnique() {
		return saleListUnique;
	}
	public void setSaleListUnique(Long saleListUnique) {
		this.saleListUnique = saleListUnique;
	}
	public double getSaleListDetailCount() {
		return saleListDetailCount;
	}
	public void setSaleListDetailCount(double saleListDetailCount) {
		this.saleListDetailCount = saleListDetailCount;
	}
	public Double getSaleListDetailPrice() {
		return saleListDetailPrice;
	}
	public void setSaleListDetailPrice(Double saleListDetailPrice) {
		this.saleListDetailPrice = saleListDetailPrice;
	}
	public String getGoodsUnique() {
		return goodsUnique;
	}
	public void setGoodsUnique(String goodsUnique) {
		this.goodsUnique = goodsUnique;
	}
	public Long getGoodsId() {
		return goodsId;
	}
	public void setGoodsId(Long goodsId) {
		this.goodsId = goodsId;
	}
	public Double getGoodsInPrice() {
		return goodsInPrice;
	}
	public void setGoodsInPrice(Double goodsInPrice) {
		this.goodsInPrice = goodsInPrice;
	}
	public Double getCommission_total() {
		return commission_total;
	}
	public void setCommission_total(Double commission_total) {
		this.commission_total = commission_total;
	}
	public Double getGoodsPurprice() {
		return goodsPurprice;
	}
	public void setGoodsPurprice(Double goodsPurprice) {
		this.goodsPurprice = goodsPurprice;
	}

	public String getFirstKindName() {
		return firstKindName;
	}

	public void setFirstKindName(String firstKindName) {
		this.firstKindName = firstKindName;
	}


	public BigDecimal getStockPrice() {
		return stockPrice;
	}

	public void setStockPrice(BigDecimal stockPrice) {
		this.stockPrice = stockPrice;
	}

	public String getSecondKindName() {
		return secondKindName;
	}

	public void setSecondKindName(String secondKindName) {
		this.secondKindName = secondKindName;
	}

	@Override
	public String toString() {
		return "SaleListDetail [saleListDetailId=" + saleListDetailId + ", saleListUnique=" + saleListUnique
				+ ", saleListUniques=" + saleListUniques + ", saleListDetailCount=" + saleListDetailCount
				+ ", saleListDetailPrice=" + saleListDetailPrice + ", goodsUnique=" + goodsUnique + ", goodsId="
				+ goodsId + ", goodsInPrice=" + goodsInPrice + ", commission_total=" + commission_total
				+ ", goodsBarcode=" + goodsBarcode + ", goodsName=" + goodsName + ", goodsCount=" + goodsCount
				+ ", goodsSalePrice=" + goodsSalePrice + ", goodsPicturepath=" + goodsPicturepath + ", subtotal="
				+ subtotal + ", commissionTotal=" + commissionTotal + ", goodsOldPrice=" + goodsOldPrice
				+ ", goodsPurprice=" + goodsPurprice + "]";
	}

}
