package org.haier.shop.entity;

public class GoodsInfo {	
	private String goodsBarcode;
	private String goodsName;
	private String goodsCount;
	private String stockCount;
	private String stockTime;
	private String listUnique;
	private String stockType;
	private String stockResource;
	
	private String goods_sold;
	private String goods_unit;
	private String goods_in_price;
	private String goods_sale_price;
	private String goods_cus_price;
	private String shop_unique;
	private String shop_name;
	
	//商品调拨
	private String purchase_list_unique;
	private String purchase_list_date;
	private String storehouse_name_in;
	private String storehouse_name_out;

	private String goodsKindNameFirst;
	private String goodsKindNameSecond;
	
	public String getPurchase_list_unique() {
		return purchase_list_unique;
	}
	public void setPurchase_list_unique(String purchase_list_unique) {
		this.purchase_list_unique = purchase_list_unique;
	}
	public String getPurchase_list_date() {
		return purchase_list_date;
	}
	public void setPurchase_list_date(String purchase_list_date) {
		this.purchase_list_date = purchase_list_date;
	}
	public String getStorehouse_name_in() {
		return storehouse_name_in;
	}
	public void setStorehouse_name_in(String storehouse_name_in) {
		this.storehouse_name_in = storehouse_name_in;
	}
	public String getStorehouse_name_out() {
		return storehouse_name_out;
	}
	public void setStorehouse_name_out(String storehouse_name_out) {
		this.storehouse_name_out = storehouse_name_out;
	}
	public String getGoodsBarcode() {
		return goodsBarcode;
	}
	public void setGoodsBarcode(String goodsBarcode) {
		this.goodsBarcode = goodsBarcode;
	}
	public String getGoodsName() {
		return goodsName;
	}
	public void setGoodsName(String goodsName) {
		this.goodsName = goodsName;
	}
	public String getGoodsCount() {
		return goodsCount;
	}
	public void setGoodsCount(String goodsCount) {
		this.goodsCount = goodsCount;
	}
	public String getStockCount() {
		return stockCount;
	}
	public void setStockCount(String stockCount) {
		this.stockCount = stockCount;
	}
	public String getStockTime() {
		return stockTime;
	}
	public void setStockTime(String stockTime) {
		this.stockTime = stockTime;
	}
	public String getListUnique() {
		return listUnique;
	}
	public void setListUnique(String listUnique) {
		this.listUnique = listUnique;
	}
	public String getStockType() {
		return stockType;
	}
	public void setStockType(String stockType) {
		this.stockType = stockType;
	}
	public String getStockResource() {
		return stockResource;
	}
	public void setStockResource(String stockResource) {
		this.stockResource = stockResource;
	}
	public String getGoods_sold() {
		return goods_sold;
	}
	public void setGoods_sold(String goods_sold) {
		this.goods_sold = goods_sold;
	}
	public String getGoods_unit() {
		return goods_unit;
	}
	public void setGoods_unit(String goods_unit) {
		this.goods_unit = goods_unit;
	}
	public String getGoods_in_price() {
		return goods_in_price;
	}
	public void setGoods_in_price(String goods_in_price) {
		this.goods_in_price = goods_in_price;
	}
	public String getGoods_sale_price() {
		return goods_sale_price;
	}
	public void setGoods_sale_price(String goods_sale_price) {
		this.goods_sale_price = goods_sale_price;
	}
	public String getShop_unique() {
		return shop_unique;
	}
	public void setShop_unique(String shop_unique) {
		this.shop_unique = shop_unique;
	}
	public String getShop_name() {
		return shop_name;
	}
	public void setShop_name(String shop_name) {
		this.shop_name = shop_name;
	}
	
	
	public String getGoods_cus_price() {
		return goods_cus_price;
	}
	public void setGoods_cus_price(String goods_cus_price) {
		this.goods_cus_price = goods_cus_price;
	}

	public String getGoodsKindNameFirst() {
		return goodsKindNameFirst;
	}

	public void setGoodsKindNameFirst(String goodsKindNameFirst) {
		this.goodsKindNameFirst = goodsKindNameFirst;
	}

	public String getGoodsKindNameSecond() {
		return goodsKindNameSecond;
	}

	public void setGoodsKindNameSecond(String goodsKindNameSecond) {
		this.goodsKindNameSecond = goodsKindNameSecond;
	}

	@Override
	public String toString() {
		return "GoodsInfo [goodsBarcode=" + goodsBarcode + ", goodsName=" + goodsName + ", goodsCount=" + goodsCount
				+ ", stockCount=" + stockCount + ", stockTime=" + stockTime + ", listUnique=" + listUnique
				+ ", stockType=" + stockType + ", stockResource=" + stockResource + "]";
	}
	
}
