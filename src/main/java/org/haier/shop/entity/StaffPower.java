package org.haier.shop.entity;

public class StaffPower {
	//员工编号
	private Integer staffId;
	//员工名称
	private String staffName;
	//价格修改权限
	private Integer powerPrice;
	//库存数量修改权限
	private Integer powerCount;
	//供应商修改权限
	private Integer powerSupplier;
	//商品分类修改权限
	private Integer powerKind;
	//商品进价修改权限
	private Integer powerInPrice;
	//商品名称修改权限
	private Integer powerName;
	//商品删除权限；
	private Integer powerDelete;
	//商品采购权限
	private Integer powerPur;
	//商品新添权限
	private Integer powerAdd;
	//充值权限
	private Integer powerRecharge;
	public Integer getStaffId() {
		return staffId;
	}
	public void setStaffId(Integer staffId) {
		this.staffId = staffId;
	}
	public String getStaffName() {
		return staffName;
	}
	public void setStaffName(String staffName) {
		this.staffName = staffName;
	}
	public Integer getPowerPrice() {
		return powerPrice;
	}
	public void setPowerPrice(Integer powerPrice) {
		this.powerPrice = powerPrice;
	}
	public Integer getPowerCount() {
		return powerCount;
	}
	public void setPowerCount(Integer powerCount) {
		this.powerCount = powerCount;
	}
	public Integer getPowerSupplier() {
		return powerSupplier;
	}
	public void setPowerSupplier(Integer powerSupplier) {
		this.powerSupplier = powerSupplier;
	}
	public Integer getPowerKind() {
		return powerKind;
	}
	public void setPowerKind(Integer powerKind) {
		this.powerKind = powerKind;
	}
	public Integer getPowerInPrice() {
		return powerInPrice;
	}
	public void setPowerInPrice(Integer powerInPrice) {
		this.powerInPrice = powerInPrice;
	}
	public Integer getPowerName() {
		return powerName;
	}
	public void setPowerName(Integer powerName) {
		this.powerName = powerName;
	}
	public Integer getPowerDelete() {
		return powerDelete;
	}
	public void setPowerDelete(Integer powerDelete) {
		this.powerDelete = powerDelete;
	}
	public Integer getPowerPur() {
		return powerPur;
	}
	public void setPowerPur(Integer powerPur) {
		this.powerPur = powerPur;
	}
	public Integer getPowerAdd() {
		return powerAdd;
	}
	public void setPowerAdd(Integer powerAdd) {
		this.powerAdd = powerAdd;
	}
	public Integer getPowerRecharge() {
		return powerRecharge;
	}
	public void setPowerRecharge(Integer powerRecharge) {
		this.powerRecharge = powerRecharge;
	}
}
