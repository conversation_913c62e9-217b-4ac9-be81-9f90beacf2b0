package org.haier.shop.entity;

import java.util.List;

public class PurCart{
	private Integer purchase_list_id;
	private String supplier_name;
	private String purchase_list_unique;
//	private Integer purchase_list_sum;
//	private Double purchase_list_total;
//	private String purchase_list_remark;
	private String supplier_unique;
	private List<PurCartDetail> listDetail;
	public String getPurchase_list_unique() {
		return purchase_list_unique;
	}
	public void setPurchase_list_unique(String purchase_list_unique) {
		this.purchase_list_unique = purchase_list_unique;
	}
	public Integer getPurchase_list_id() {
		return purchase_list_id;
	}
	public void setPurchase_list_id(Integer purchase_list_id) {
		this.purchase_list_id = purchase_list_id;
	}
	public String getSupplier_name() {
		return supplier_name;
	}
	public void setSupplier_name(String supplier_name) {
		this.supplier_name = supplier_name;
	}
//	public Integer getPurchase_list_sum() {
//		return purchase_list_sum;
//	}
//	public void setPurchase_list_sum(Integer purchase_list_sum) {
//		this.purchase_list_sum = purchase_list_sum;
//	}
//	public Double getPurchase_list_total() {
//		return purchase_list_total;
//	}
//	public void setPurchase_list_total(Double purchase_list_total) {
//		this.purchase_list_total = purchase_list_total;
//	}
//	public String getPurchase_list_remark() {
//		return purchase_list_remark;
//	}
//	public void setPurchase_list_remark(String purchase_list_remark) {
//		this.purchase_list_remark = purchase_list_remark;
//	}
	public String getSupplier_unique() {
		return supplier_unique;
	}
	public void setSupplier_unique(String supplier_unique) {
		this.supplier_unique = supplier_unique;
	}
	public List<PurCartDetail> getListDetail() {
		return listDetail;
	}
	public void setListDetail(List<PurCartDetail> listDetail) {
		this.listDetail = listDetail;
	}
	
	
}
