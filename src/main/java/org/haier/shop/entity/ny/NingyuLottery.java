package org.haier.shop.entity.ny;

import java.io.Serializable;
import java.util.List;

/**
 * 宁宇周年活动抽奖
 * <AUTHOR>
 *
 */
public class NingyuLottery implements Serializable{
	/**
	 * 
	 */
	private static final long serialVersionUID = 1L;
	private String id;
	private String startTime;
	private String endTime;
	private Double ratio;
	private Integer lotteryType;
	private List<NingyuLotteryList> list;
	public String getId() {
		return id;
	}
	public void setId(String id) {
		this.id = id;
	}
	public String getStartTime() {
		return startTime;
	}
	public void setStartTime(String startTime) {
		this.startTime = startTime;
	}
	public String getEndTime() {
		return endTime;
	}
	public void setEndTime(String endTime) {
		this.endTime = endTime;
	}
	public Double getRatio() {
		return ratio;
	}
	public void setRatio(Double ratio) {
		this.ratio = ratio;
	}
	public Integer getLotteryType() {
		return lotteryType;
	}
	public void setLotteryType(Integer lotteryType) {
		this.lotteryType = lotteryType;
	}
	public List<NingyuLotteryList> getList() {
		return list;
	}
	public void setList(List<NingyuLotteryList> list) {
		this.list = list;
	}
}
