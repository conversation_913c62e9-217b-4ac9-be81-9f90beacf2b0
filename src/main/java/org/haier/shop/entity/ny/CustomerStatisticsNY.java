package org.haier.shop.entity.ny;

import java.math.BigDecimal;

public class CustomerStatisticsNY {
    //ID
    private Integer id;
    //统计的日期，一般为数据的下一个自然日
    private String date;
    //店铺编号
    private String shopUnique;
    //余额
    private BigDecimal balance;
    //赠送余额
    private BigDecimal giveBalance;
    //会员人数
    private Integer count;

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getDate() {
        return date;
    }

    public void setDate(String date) {
        this.date = date;
    }

    public String getShopUnique() {
        return shopUnique;
    }

    public void setShopUnique(String shopUnique) {
        this.shopUnique = shopUnique;
    }

    public BigDecimal getBalance() {
        return balance;
    }

    public void setBalance(BigDecimal balance) {
        this.balance = balance;
    }

    public BigDecimal getGiveBalance() {
        return giveBalance;
    }

    public void setGiveBalance(BigDecimal giveBalance) {
        this.giveBalance = giveBalance;
    }

    public Integer getCount() {
        return count;
    }

    public void setCount(Integer count) {
        this.count = count;
    }

    @Override
    public String toString() {
        return "CustomerStatisticsNY{" +
                "id=" + id +
                ", date='" + date + '\'' +
                ", shopUnique='" + shopUnique + '\'' +
                ", balance=" + balance +
                ", giveBalance=" + giveBalance +
                ", count=" + count +
                '}';
    }
}
