package org.haier.shop.entity.ny;

public class CustomerSummaryByGroup {
	//店铺名称或日期
	private String name;
	//充值金额
	private Double rechargeMoney;
	//充值赠送金额
	private Double giveMoney;
	//充值总额
	private Double rechargeTotal;
	//活动赠送本金
	private Double actMoney;
	//活动赠送赠额
	private Double actGiveMoney;
	
	
	//消费金额
	private Double consumptionMoney;
	//消费赠送金额
	private Double consumptionGiveMoney;
	//消费总额
	private Double consumptionTotal;
	
	
	//退卡金额
	private Double refundsMoney;
	//退卡赠送金额
	private Double refundsGiveMoney;
	//退卡总额
	private Double refundsTotal;
	//汇总
	private Double allTotal;
	
	
	
	public Double getActMoney() {
		return actMoney;
	}
	public void setActMoney(Double actMoney) {
		this.actMoney = actMoney;
	}
	public Double getActGiveMoney() {
		return actGiveMoney;
	}
	public void setActGiveMoney(Double actGiveMoney) {
		this.actGiveMoney = actGiveMoney;
	}
	public String getName() {
		return name;
	}
	public void setName(String name) {
		this.name = name;
	}
	public Double getRechargeMoney() {
		return rechargeMoney;
	}
	public void setRechargeMoney(Double rechargeMoney) {
		this.rechargeMoney = rechargeMoney;
	}
	public Double getGiveMoney() {
		return giveMoney;
	}
	public void setGiveMoney(Double giveMoney) {
		this.giveMoney = giveMoney;
	}
	public Double getRechargeTotal() {
		return rechargeTotal;
	}
	public void setRechargeTotal(Double rechargeTotal) {
		this.rechargeTotal = rechargeTotal;
	}
	public Double getConsumptionMoney() {
		return consumptionMoney;
	}
	public void setConsumptionMoney(Double consumptionMoney) {
		this.consumptionMoney = consumptionMoney;
	}
	public Double getConsumptionGiveMoney() {
		return consumptionGiveMoney;
	}
	public void setConsumptionGiveMoney(Double consumptionGiveMoney) {
		this.consumptionGiveMoney = consumptionGiveMoney;
	}
	public Double getConsumptionTotal() {
		return consumptionTotal;
	}
	public void setConsumptionTotal(Double consumptionTotal) {
		this.consumptionTotal = consumptionTotal;
	}
	public Double getRefundsMoney() {
		return refundsMoney;
	}
	public void setRefundsMoney(Double refundsMoney) {
		this.refundsMoney = refundsMoney;
	}
	public Double getRefundsGiveMoney() {
		return refundsGiveMoney;
	}
	public void setRefundsGiveMoney(Double refundsGiveMoney) {
		this.refundsGiveMoney = refundsGiveMoney;
	}
	public Double getRefundsTotal() {
		return refundsTotal;
	}
	public void setRefundsTotal(Double refundsTotal) {
		this.refundsTotal = refundsTotal;
	}
	public Double getAllTotal() {
		return allTotal;
	}
	public void setAllTotal(Double allTotal) {
		this.allTotal = allTotal;
	}
}
