package org.haier.shop.entity.wj;

import java.io.Serializable;

public class GoodsSpec implements Serializable{
	
	/**
	 * 
	 */
	private static final long serialVersionUID = 1L;
	private Integer id;//详情ID
	private Integer specs_id;
	private Double price;//五金商品售价
	private String spec_name;//商品名称
	private String spec_img;//商品图片信息
	private Double goods_count;//商品名称；
	private Double available_stock_count;//可用商品库存
	
	public Integer getId() {
		return id;
	}
	public void setId(Integer id) {
		this.id = id;
	}
	public Double getPrice() {
		return price;
	}
	public void setPrice(Double price) {
		this.price = price;
	}
	public String getSpec_img() {
		return spec_img;
	}
	public void setSpec_img(String spec_img) {
		this.spec_img = spec_img;
	}
	public Integer getSpecs_id() {
		return specs_id;
	}
	public void setSpecs_id(Integer specs_id) {
		this.specs_id = specs_id;
	}
	public String getSpec_name() {
		return spec_name;
	}
	public void setSpec_name(String spec_name) {
		this.spec_name = spec_name;
	}
	public Double getGoods_count() {
		return goods_count;
	}
	public void setGoods_count(Double goods_count) {
		this.goods_count = goods_count;
	}
	public Double getAvailable_stock_count() {
		return available_stock_count;
	}
	public void setAvailable_stock_count(Double available_stock_count) {
		this.available_stock_count = available_stock_count;
	}
	@Override
	public String toString() {
		return "{\"id\":\"" + id + "\", \"specs_id\":\"" + specs_id + "\", \"price\":\"" + price + "\", \"spec_name\":\"" + spec_name
				+ "\", \"spec_img\":\"" + spec_img + "\", \"goods_count\":\"" + goods_count + "\", \"available_stock_count\":\""
				+ available_stock_count + "\"}";
	}
}
