package org.haier.shop.entity.wj;

import java.io.Serializable;

public class DeliveryInfo  extends Goodsfile implements Serializable{
	/**
	 * 
	 */
	private static final long serialVersionUID = -2776229258324791955L;
	private Integer delivery_id;//配送信息编号
	private String company_code;//公司编号
	private Integer delivery_price_type;//配送费收取方式：1、按商品件数；2、按订单
	private Double send_price;//订单起送价
	private Double free_delivery_price;//免配送费价格
	private String create_time;
	private String update_time;
	public Integer getDelivery_id() {
		return delivery_id;
	}
	public void setDelivery_id(Integer delivery_id) {
		this.delivery_id = delivery_id;
	}
	public String getCompany_code() {
		return company_code;
	}
	public void setCompany_code(String company_code) {
		this.company_code = company_code;
	}
	public Integer getDelivery_price_type() {
		return delivery_price_type;
	}
	public void setDelivery_price_type(Integer delivery_price_type) {
		this.delivery_price_type = delivery_price_type;
	}
	public Double getSend_price() {
		return send_price;
	}
	public void setSend_price(Double send_price) {
		this.send_price = send_price;
	}
	public Double getFree_delivery_price() {
		return free_delivery_price;
	}
	public void setFree_delivery_price(Double free_delivery_price) {
		this.free_delivery_price = free_delivery_price;
	}
	public String getCreate_time() {
		return create_time;
	}
	public void setCreate_time(String create_time) {
		this.create_time = create_time;
	}
	public String getUpdate_time() {
		return update_time;
	}
	public void setUpdate_time(String update_time) {
		this.update_time = update_time;
	}
	
}
