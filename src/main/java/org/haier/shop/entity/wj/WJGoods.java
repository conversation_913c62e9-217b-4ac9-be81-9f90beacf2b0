package org.haier.shop.entity.wj;

import java.util.List;

/**
 * 供货商城-五金店铺信息查询
 * <AUTHOR>
 *
 */
public class WJGoods extends DeliveryInfo{
	/**
	 * 
	 */
	private static final long serialVersionUID = 1L;
	private Double delivery_price;//配送费用
	private String goodsunit_name;//单位信息
	private String company_name;//供货商名称
	private String service_phone;//服务电话
	private Double stock_count;//库存
	private Integer goods_id;//goodsId
	private String goods_barcode;//商品条码
	private String company_code;//公司code
	private String goods_name;//商品名称
	private String brand_id;//商品品牌ID
	private String brandName;
	private String unit_id;//单位ID
	private String smallunit_id;//最小单位ID
	private String quality_period;//保质期
	private String class_id;//分类ID
	private String class_parentid;//大类ID
	private String goods_img;//商品主图路径
	private String goods_resume;//商品描述
	private String base_barcode;//基本单位商品条码
	private Integer goods_type;//1、称重商品；2、计件商品
	private Integer proportion_num;//换算比例
	private Integer goods_integral;//商品积分
	private String supplier_code;//供货商编码
	private String goods_price;//成本价
	private String wholesale_price;//销售价
	private Double weight;//单品重量
	private Double volume;//单品体积
	private Integer start_order;//最低批发数量
	private String goods_label;//商品标签：1：新品上架；2：销售火热；3：促销优惠；4：赠品
	private Double supply_price;//供货价
	private Double online_price;//商城售价
	private Double platform_cut;//平台抽成
	private Double purchase_cut;//供货商抽成
	private Double gold_deduct;//金圈币抵扣金额
	private Integer audit_status;//审核状态：1、未审核；2、审核通过；3、审核不通过；4、停用
	private Integer auto_fxiaoshou;//是否允许负库存销售；1、允许；2、不允许
	private Double daixiao_code;//代销供货商编号
	private Integer sort;//商品序号，按从小到大排列
	private String address;//产地
	private String goods_detail;//商品详情
	private Double promotionPrice;//促销价
	private String activity_desc;//活动说明
	private Integer sale_count;//销量
	private Double sale_price;//销售价格
	
	private List<GoodsSpec> list;//商品规格详情
	
	public String getBrandName() {
		return brandName;
	}
	public void setBrandName(String brandName) {
		this.brandName = brandName;
	}
	public String getGoods_detail() {
		return goods_detail;
	}
	public void setGoods_detail(String goods_detail) {
		this.goods_detail = goods_detail;
	}
	public Double getDelivery_price() {
		return delivery_price;
	}
	public void setDelivery_price(Double delivery_price) {
		this.delivery_price = delivery_price;
	}
	public String getGoodsunit_name() {
		return goodsunit_name;
	}
	public void setGoodsunit_name(String goodsunit_name) {
		this.goodsunit_name = goodsunit_name;
	}
	public String getCompany_name() {
		return company_name;
	}
	public void setCompany_name(String company_name) {
		this.company_name = company_name;
	}
	public String getService_phone() {
		return service_phone;
	}
	public void setService_phone(String service_phone) {
		this.service_phone = service_phone;
	}
	public Double getStock_count() {
		return stock_count;
	}
	public void setStock_count(Double stock_count) {
		this.stock_count = stock_count;
	}
	public Integer getGoods_id() {
		return goods_id;
	}
	public void setGoods_id(Integer goods_id) {
		this.goods_id = goods_id;
	}
	public String getGoods_barcode() {
		return goods_barcode;
	}
	public void setGoods_barcode(String goods_barcode) {
		this.goods_barcode = goods_barcode;
	}
	public String getCompany_code() {
		return company_code;
	}
	public void setCompany_code(String company_code) {
		this.company_code = company_code;
	}
	public String getGoods_name() {
		return goods_name;
	}
	public void setGoods_name(String goods_name) {
		this.goods_name = goods_name;
	}
	public String getBrand_id() {
		return brand_id;
	}
	public void setBrand_id(String brand_id) {
		this.brand_id = brand_id;
	}
	public String getUnit_id() {
		return unit_id;
	}
	public void setUnit_id(String unit_id) {
		this.unit_id = unit_id;
	}
	public String getSmallunit_id() {
		return smallunit_id;
	}
	public void setSmallunit_id(String smallunit_id) {
		this.smallunit_id = smallunit_id;
	}
	public String getQuality_period() {
		return quality_period;
	}
	public void setQuality_period(String quality_period) {
		this.quality_period = quality_period;
	}
	public String getClass_id() {
		return class_id;
	}
	public void setClass_id(String class_id) {
		this.class_id = class_id;
	}
	public String getClass_parentid() {
		return class_parentid;
	}
	public void setClass_parentid(String class_parentid) {
		this.class_parentid = class_parentid;
	}
	public String getGoods_img() {
		return goods_img;
	}
	public void setGoods_img(String goods_img) {
		this.goods_img = goods_img;
	}
	public String getGoods_resume() {
		return goods_resume;
	}
	public void setGoods_resume(String goods_resume) {
		this.goods_resume = goods_resume;
	}
	public String getBase_barcode() {
		return base_barcode;
	}
	public void setBase_barcode(String base_barcode) {
		this.base_barcode = base_barcode;
	}
	public Integer getGoods_type() {
		return goods_type;
	}
	public void setGoods_type(Integer goods_type) {
		this.goods_type = goods_type;
	}
	public Integer getProportion_num() {
		return proportion_num;
	}
	public void setProportion_num(Integer proportion_num) {
		this.proportion_num = proportion_num;
	}
	public Integer getGoods_integral() {
		return goods_integral;
	}
	public void setGoods_integral(Integer goods_integral) {
		this.goods_integral = goods_integral;
	}
	public String getSupplier_code() {
		return supplier_code;
	}
	public void setSupplier_code(String supplier_code) {
		this.supplier_code = supplier_code;
	}
	public String getGoods_price() {
		return goods_price;
	}
	public void setGoods_price(String goods_price) {
		this.goods_price = goods_price;
	}
	public String getWholesale_price() {
		return wholesale_price;
	}
	public void setWholesale_price(String wholesale_price) {
		this.wholesale_price = wholesale_price;
	}
	public Double getWeight() {
		return weight;
	}
	public void setWeight(Double weight) {
		this.weight = weight;
	}
	public Double getVolume() {
		return volume;
	}
	public void setVolume(Double volume) {
		this.volume = volume;
	}
	public Integer getStart_order() {
		return start_order;
	}
	public void setStart_order(Integer start_order) {
		this.start_order = start_order;
	}
	public String getGoods_label() {
		return goods_label;
	}
	public void setGoods_label(String goods_label) {
		this.goods_label = goods_label;
	}
	public Double getSupply_price() {
		return supply_price;
	}
	public void setSupply_price(Double supply_price) {
		this.supply_price = supply_price;
	}
	public Double getOnline_price() {
		return online_price;
	}
	public void setOnline_price(Double online_price) {
		this.online_price = online_price;
	}
	public Double getPlatform_cut() {
		return platform_cut;
	}
	public void setPlatform_cut(Double platform_cut) {
		this.platform_cut = platform_cut;
	}
	public Double getPurchase_cut() {
		return purchase_cut;
	}
	public void setPurchase_cut(Double purchase_cut) {
		this.purchase_cut = purchase_cut;
	}
	public Double getGold_deduct() {
		return gold_deduct;
	}
	public void setGold_deduct(Double gold_deduct) {
		this.gold_deduct = gold_deduct;
	}
	public Integer getAudit_status() {
		return audit_status;
	}
	public void setAudit_status(Integer audit_status) {
		this.audit_status = audit_status;
	}
	public Integer getAuto_fxiaoshou() {
		return auto_fxiaoshou;
	}
	public void setAuto_fxiaoshou(Integer auto_fxiaoshou) {
		this.auto_fxiaoshou = auto_fxiaoshou;
	}
	public Double getDaixiao_code() {
		return daixiao_code;
	}
	public void setDaixiao_code(Double daixiao_code) {
		this.daixiao_code = daixiao_code;
	}
	public Integer getSort() {
		return sort;
	}
	public void setSort(Integer sort) {
		this.sort = sort;
	}
	public String getAddress() {
		return address;
	}
	public void setAddress(String address) {
		this.address = address;
	}
	public Double getPromotionPrice() {
		return promotionPrice;
	}
	public void setPromotionPrice(Double promotionPrice) {
		this.promotionPrice = promotionPrice;
	}
	public String getActivity_desc() {
		return activity_desc;
	}
	public void setActivity_desc(String activity_desc) {
		this.activity_desc = activity_desc;
	}
	public Integer getSale_count() {
		return sale_count;
	}
	public void setSale_count(Integer sale_count) {
		this.sale_count = sale_count;
	}
	public Double getSale_price() {
		return sale_price;
	}
	public void setSale_price(Double sale_price) {
		this.sale_price = sale_price;
	}
	public List<GoodsSpec> getList() {
		return list;
	}
	public void setList(List<GoodsSpec> list) {
		this.list = list;
	}
}
