package org.haier.shop.entity.wj;

import java.io.Serializable;
import java.util.List;

public class WJCartGoods implements Serializable{
	/**
	 * 
	 */
	private static final long serialVersionUID = 1L;
	private Integer good_id;
	private String company_code;
	private Integer is_order;
	private String order_no;
	private String goods_name;
	private Double start_order = 0.00;
	private List<WJCartGoodsDetail> list;
	private Double delivery_price;
	
	public Double getStart_order() {
		return start_order;
	}
	public void setStart_order(Double start_order) {
		this.start_order = start_order;
	}
	public Integer getGood_id() {
		return good_id;
	}
	public void setGood_id(Integer good_id) {
		this.good_id = good_id;
	}
	public String getCompany_code() {
		return company_code;
	}
	public Double getDelivery_price() {
		return delivery_price;
	}
	public void setDelivery_price(Double delivery_price) {
		this.delivery_price = delivery_price;
	}
	public void setCompany_code(String company_code) {
		this.company_code = company_code;
	}
	public Integer getIs_order() {
		return is_order;
	}
	public void setIs_order(Integer is_order) {
		this.is_order = is_order;
	}
	public String getOrder_no() {
		return order_no;
	}
	public void setOrder_no(String order_no) {
		this.order_no = order_no;
	}
	public String getGoods_name() {
		return goods_name;
	}
	public void setGoods_name(String goods_name) {
		this.goods_name = goods_name;
	}
	public List<WJCartGoodsDetail> getList() {
		return list;
	}
	public void setList(List<WJCartGoodsDetail> list) {
		this.list = list;
	}
	
}
