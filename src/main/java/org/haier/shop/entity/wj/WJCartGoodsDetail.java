package org.haier.shop.entity.wj;

import java.io.Serializable;

public class WJCartGoodsDetail implements Serializable{
	
	/**
	 * 
	 */
	private static final long serialVersionUID = 1L;
	private Integer cart_id;//购物车的商品ID
	private Integer spec_id;
	private String spec_name;//
	private Double good_count;
	private Integer is_check;//
	private Double online_price;//
	private Double stock_count;
	private Double available_stock_count;
	private Double sum_amt;
	private String spec_img;
	private Integer start_order;
	public Integer getCart_id() {
		return cart_id;
	}
	public void setCart_id(Integer cart_id) {
		this.cart_id = cart_id;
	}
	public Double getGood_count() {
		return good_count;
	}
	public void setGood_count(Double good_count) {
		this.good_count = good_count;
	}
	public Integer getSpec_id() {
		return spec_id;
	}
	public void setSpec_id(Integer spec_id) {
		this.spec_id = spec_id;
	}
	public String getSpec_name() {
		return spec_name;
	}
	public void setSpec_name(String spec_name) {
		this.spec_name = spec_name;
	}
	public Integer getIs_check() {
		return is_check;
	}
	public void setIs_check(Integer is_check) {
		this.is_check = is_check;
	}
	public Double getOnline_price() {
		return online_price;
	}
	public void setOnline_price(Double online_price) {
		this.online_price = online_price;
	}
	public Double getStock_count() {
		return stock_count;
	}
	public void setStock_count(Double stock_count) {
		this.stock_count = stock_count;
	}
	public Double getAvailable_stock_count() {
		return available_stock_count;
	}
	public void setAvailable_stock_count(Double available_stock_count) {
		this.available_stock_count = available_stock_count;
	}
	public Double getSum_amt() {
		return sum_amt;
	}
	public void setSum_amt(Double sum_amt) {
		this.sum_amt = sum_amt;
	}
	public String getSpec_img() {
		return spec_img;
	}
	public void setSpec_img(String spec_img) {
		this.spec_img = spec_img;
	}
	public Integer getStart_order() {
		return start_order;
	}
	public void setStart_order(Integer start_order) {
		this.start_order = start_order;
	}
}
