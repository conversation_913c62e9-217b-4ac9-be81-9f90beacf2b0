package org.haier.shop.entity.wj;

import java.io.Serializable;

/**
 * 五金建材分类子分类
 * <AUTHOR>
 *
 */
public class WjGoodsKindSub implements Serializable{
	/**
	 * 
	 */
	private static final long serialVersionUID = 1L;
	private Integer kindId;
	private String subName;
	public Integer getKindId() {
		return kindId;
	}
	public void setKindId(Integer kindId) {
		this.kindId = kindId;
	}
	public String getSubName() {
		return subName;
	}
	public void setSubName(String subName) {
		this.subName = subName;
	}
	@Override
	public String toString() {
		return "WJGoodsKindSub [kindId=" + kindId + ", subName=" + subName + "]";
	}
}
