package org.haier.shop.entity.wj;

import java.io.Serializable;
import java.util.List;

/**
 * 五金建材分类父类
 * <AUTHOR>
 *
 */
public class WJGoodsKindParent implements Serializable{
	/**
	 * 
	 */
	private static final long serialVersionUID = 1L;
	private Integer parentId;
	private String parentName;
	private List<WjGoodsKindSub> subList;
	public Integer getParentId() {
		return parentId;
	}
	public void setParentId(Integer parentId) {
		this.parentId = parentId;
	}
	public String getParentName() {
		return parentName;
	}
	public void setParentName(String parentName) {
		this.parentName = parentName;
	}
	public List<WjGoodsKindSub> getSubList() {
		return subList;
	}
	public void setSubList(List<WjGoodsKindSub> subList) {
		this.subList = subList;
	}
	@Override
	public String toString() {
		return "WJGoodsKindParent [parentId=" + parentId + ", parentName=" + parentName + ", subList=" + subList + "]";
	}
}
