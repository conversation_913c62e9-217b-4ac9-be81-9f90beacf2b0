package org.haier.shop.entity.shop;

import java.io.Serializable;

/**
 * 
 */
public class ShopList implements Serializable{
	/**
	 * 
	 */
	private static final long serialVersionUID = 1L;
	private String order_code;
	private String order_remarks;
	private String create_date;
	private String orderStatus;
	private String collect_name;
	private String collect_phone;
	private String collect_address;
	private String pay_date;
	private Double order_money;
	private Double real_pay_money;
	private String receipt_date;
	private String company_name;
	private String duty_ren;
	private String service_phone;
	public String getOrder_code() {
		return order_code;
	}
	public void setOrder_code(String order_code) {
		this.order_code = order_code;
	}
	public String getOrder_remarks() {
		return order_remarks;
	}
	public void setOrder_remarks(String order_remarks) {
		this.order_remarks = order_remarks;
	}
	public String getCreate_date() {
		return create_date;
	}
	public void setCreate_date(String create_date) {
		this.create_date = create_date;
	}
	public String getOrderStatus() {
		return orderStatus;
	}
	public void setOrderStatus(String orderStatus) {
		this.orderStatus = orderStatus;
	}
	public String getCollect_name() {
		return collect_name;
	}
	public void setCollect_name(String collect_name) {
		this.collect_name = collect_name;
	}
	public String getCollect_phone() {
		return collect_phone;
	}
	public void setCollect_phone(String collect_phone) {
		this.collect_phone = collect_phone;
	}
	public String getCollect_address() {
		return collect_address;
	}
	public void setCollect_address(String collect_address) {
		this.collect_address = collect_address;
	}
	public String getPay_date() {
		return pay_date;
	}
	public void setPay_date(String pay_date) {
		this.pay_date = pay_date;
	}
	public Double getOrder_money() {
		return order_money;
	}
	public void setOrder_money(Double order_money) {
		this.order_money = order_money;
	}
	public Double getReal_pay_money() {
		return real_pay_money;
	}
	public void setReal_pay_money(Double real_pay_money) {
		this.real_pay_money = real_pay_money;
	}
	public String getReceipt_date() {
		return receipt_date;
	}
	public void setReceipt_date(String receipt_date) {
		this.receipt_date = receipt_date;
	}
	public String getCompany_name() {
		return company_name;
	}
	public void setCompany_name(String company_name) {
		this.company_name = company_name;
	}
	public String getDuty_ren() {
		return duty_ren;
	}
	public void setDuty_ren(String duty_ren) {
		this.duty_ren = duty_ren;
	}
	public String getService_phone() {
		return service_phone;
	}
	public void setService_phone(String service_phone) {
		this.service_phone = service_phone;
	}
	
	
}
