package org.haier.shop.entity.shop;

import java.io.Serializable;

/**
 * @Description 企业纳统
 * @ClassName StatisticShopVO
 * <AUTHOR>
 * @Date 2023/7/26 14:09
 **/
public class StatisticShopVO implements Serializable {

    /**
     * 商家唯一标识
     */
    private Long shopUnique;

    /**
     * 商家名称
     */
    private String shopName;

    /**
     * 店铺类型:1、便利店；2、水果店；3、母婴店；4、益农中心站；5、益农标准站、简易站、专业站；6宁宇总店、7宁宇分店、8五金店 9学校、10、机关食堂0：其他 11:加油站；12、餐饮店；13、拼团店统一社会信用代码
     */
    private Integer shopType;

    /**
     * 详细地址
     */
    private String address;

    /**
     * 联系电话
     */
    private String shopPhone;

    /**
     * 区县编码
     */
    private String countyCode;

    /**
     * 村编码
     */
    private String townCode;

    /**
     * 有效状态:1正常0无效
     */
    private Integer enableStatus;

    /**
     * 邀请码
     */
    private String invitationCode;

    /**
     * 法人姓名
     */
    private String legalPerson;

    /**
     * 法人手机号
     */
    private String legalPhone;

    public StatisticShopVO() {
    }

    public Long getShopUnique() {
        return shopUnique;
    }

    public void setShopUnique(Long shopUnique) {
        this.shopUnique = shopUnique;
    }

    public String getShopName() {
        return shopName;
    }

    public void setShopName(String shopName) {
        this.shopName = shopName;
    }

    public Integer getShopType() {
        return shopType;
    }

    public void setShopType(Integer shopType) {
        this.shopType = shopType;
    }

    public String getAddress() {
        return address;
    }

    public void setAddress(String address) {
        this.address = address;
    }

    public String getShopPhone() {
        return shopPhone;
    }

    public void setShopPhone(String shopPhone) {
        this.shopPhone = shopPhone;
    }

    public String getCountyCode() {
        return countyCode;
    }

    public void setCountyCode(String countyCode) {
        this.countyCode = countyCode;
    }

    public String getTownCode() {
        return townCode;
    }

    public void setTownCode(String townCode) {
        this.townCode = townCode;
    }

    public Integer getEnableStatus() {
        return enableStatus;
    }

    public void setEnableStatus(Integer enableStatus) {
        this.enableStatus = enableStatus;
    }

    public String getInvitationCode() {
        return invitationCode;
    }

    public void setInvitationCode(String invitationCode) {
        this.invitationCode = invitationCode;
    }

    public String getLegalPerson() {
        return legalPerson;
    }

    public void setLegalPerson(String legalPerson) {
        this.legalPerson = legalPerson;
    }

    public String getLegalPhone() {
        return legalPhone;
    }

    public void setLegalPhone(String legalPhone) {
        this.legalPhone = legalPhone;
    }
}
