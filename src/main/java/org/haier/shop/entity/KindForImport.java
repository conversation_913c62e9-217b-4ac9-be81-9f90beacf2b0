package org.haier.shop.entity;

/**
 * 商品分类导入专用
 * <AUTHOR>
 *
 */
public class KindForImport {
	private String shopUnique;
	private String goodsKindUnique;
	private String goodsKindParunique;
	private String goodsKindName;
	private String kindType;
	public String getShopUnique() {
		return shopUnique;
	}
	public void setShopUnique(String shopUnique) {
		this.shopUnique = shopUnique;
	}
	public String getGoodsKindUnique() {
		return goodsKindUnique;
	}
	public void setGoodsKindUnique(String goodsKindUnique) {
		this.goodsKindUnique = goodsKindUnique;
	}
	public String getGoodsKindParunique() {
		return goodsKindParunique;
	}
	public void setGoodsKindParunique(String goodsKindParunique) {
		this.goodsKindParunique = goodsKindParunique;
	}
	public String getGoodsKindName() {
		return goodsKindName;
	}
	public void setGoodsKindName(String goodsKindName) {
		this.goodsKindName = goodsKindName;
	}
	public String getKindType() {
		return kindType;
	}
	public void setKindType(String kindType) {
		this.kindType = kindType;
	}
}
