package org.haier.shop.interceptor;

import javax.servlet.http.HttpServletRequest;  
import javax.servlet.http.HttpServletResponse;  
import javax.servlet.http.HttpSession;  
  
import org.springframework.web.servlet.HandlerInterceptor;  
import org.springframework.web.servlet.ModelAndView;  
/** 
 * 登录认证的拦截器 
 */  
public class LoginInterceptor implements HandlerInterceptor{  
  
    /** 
     * Handler执行完成之后调用这个方法 
     */  
    public void afterCompletion(HttpServletRequest request,  
            HttpServletResponse response, Object handler, Exception exc)  
            throws Exception {  
          
    }  
  
    /** 
     * Handler执行之后，ModelAndView返回之前调用这个方法 
     */  
    public void postHandle(HttpServletRequest request, HttpServletResponse response,  
            Object handler, ModelAndView modelAndView) throws Exception {  
    }  
  
    /** 
     * Handler执行之前调用这个方法 
     */  
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response,  
            Object handler) throws Exception {  
        //获取请求的URL  
        String url = request.getRequestURI();  
//        System.out.println(url);
//        URL:login.jsp是公开的;这个demo是除了login.jsp是可以公开访问的，其它的URL都进行拦截控制  
        if(url.indexOf("loginPage.do")>=0||url.indexOf("staffLoginByAccountPwd.do")>=0||url.indexOf("register.do")>=0||url.indexOf("queryArea.do")>=0 
        		||url.indexOf("/ele/auth/callback.do")>=0 ||url.indexOf("/ele/order/")>=0
        		||url.indexOf("dataSearch")>=0||url.indexOf("data")>=0||url.indexOf("logistics")>=0
        		||url.indexOf("peisong")>=0||url.indexOf("html\test")>=0
        		){  
            return true;  
        }  
//        request.getSession(true)：若存在会话则返回该会话，否则新建一个会话。
//        request.getSession(false)：若存在会话则返回该会话，否则返回NULL
        HttpSession session=  request.getSession(true);
//        Cookie [] cookies= request.getCookies();
//		if(cookies!=null&&cookies.length>0){
//			for (Cookie cookie : cookies) {
//				if(cookie.getName().equals("shop_unique")){
//					if(!cookie.getValue().equals("")&&!cookie.getValue().equals("null")&&!cookie.getValue().equals("0")){
//						session.setAttribute("shop_unique",cookie.getValue() );
//						return true;
//					}
//				}
//			}
//		}
		if(session.getAttribute("shop_unique")!=null&&!"".equals(session.getAttribute("shop_unique"))){
			return true;
		}
        //不符合条件的，跳转到登录界面  
		request.getRequestDispatcher("/WEB-INF/login_new.jsp").forward(request, response); 
        return false;  
    }  
}  