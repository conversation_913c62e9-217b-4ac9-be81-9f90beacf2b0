package org.haier.shop.dao;

import java.util.List;
import java.util.Map;

public interface BusinessDao {
	
	/**
	 * 查询列表
	 * @return
	 */
	public List<Map<String ,Object>> queryBusinessList(Map<String,Object> params);
	
	public List<Map<String ,Object>> queryRedOrderList(Map<String,Object> params);
	
	public List<Map<String ,Object>> queryRedOrderStatistics(Map<String,Object> params);
	
	public Integer queryRedOrderListCount(Map<String,Object> params);
	
	/**
	 * 查询列表总条数
	 * @return
	 */
	public Integer queryBusinessListCount(Map<String,Object> params);
	
	/**
	 * 添加
	 * @return
	 */
	public Integer addBusiness(Map<String,Object> params);
	
	/**
	 * 获取详情
	 * @return
	 */
	public Map<String ,Object> queryBusinessDetail(String id);
	
	/**
	 * 修改
	 * @return
	 */
	public Integer updateBusiness(Map<String,Object> params);
	
	
	/**
	 * 根据手机号获取用户信息
	 * @return
	 */
	public Map<String ,Object> queryCusInfoByPhone(Map<String,Object> params);
	
	
	/**
	 * 根据套餐金额和业务类型获取红包期数和红包余额
	 * @return
	 */
	public Map<String ,Object> queryBusinessByTypeAndMoney(Map<String,Object> params);
	
	/**
	 * 添加会员业务红包信息
	 * @return
	 */
	public Integer addRedProvide(List<Map<String,Object>> list);
	
	
	/**
	 * 添加会员红包记录
	 * @return
	 */
	public Integer addRedPacket(List<Map<String,Object>> list);
	
	/**
	 * 查询会员业务红包信息
	 * @return
	 */
	public Map<String ,Object> queryRedProvide(Map<String,Object> params);
	
	/**
	 * 修改会员业务红包信息为无效
	 * @return
	 */
	public Map<String ,Object> updateRedProvide(Map<String,Object> params);
	
	/**
	 * 获取能人信息
	 * @return
	 */
	public Map<String ,Object> queryRedPerson(Map<String,Object> params);
	
	/**
	 * 添加能人信息
	 * @return
	 */
	public Integer addRedPerson(Map<String,Object> params);
	
	/**
	 * 修改能人信息
	 * @return
	 */
	public Integer updateRedPerson(Map<String,Object> params);
	
	/**
	 * 添加能人佣金记录表
	 * @return
	 */
	public Integer addRedCommission(List<Map<String,Object>> params);
	
	/**
	 * 查询能人列表
	 * @return
	 */
	public List<Map<String ,Object>> queryRedPersonList(Map<String,Object> params);
	
	/**
	 * 查询能人列表总条数
	 * @return
	 */
	public Integer queryRedPersonListCount(Map<String,Object> params);
	
	/**
	 * 查询能人佣金列表
	 * @return
	 */
	public List<Map<String ,Object>> queryRedPersonCommissionList(Map<String,Object> params);
	
	/**
	 * 查询能人佣金列表总条数
	 * @return
	 */
	public Integer queryRedPersonCommissionListCount(Map<String,Object> params);
	
	/**
	 * 查询能人提现列表
	 * @return
	 */
	public List<Map<String ,Object>> queryRedPersonWidthList(Map<String,Object> params);
	
	/**
	 * 查询能人提现列表总条数
	 * @return
	 */
	public Integer queryRedPersonWidthListCount(Map<String,Object> params);
	
	/**
	 * 查询会员可发放红包信息列表
	 * @return
	 */
	public List<Map<String ,Object>> queryRedProvideList();
	
	/**
	 * 修改会员剩余发放红包次数
	 * @return
	 */
	public Integer updateSurplusCountById(List<Map<String,Object>> list);
	
	/**
	 * 修改过期红包
	 * @return
	 */
	public Integer updateRedStatus();
	
	/**
	 * 获取提现信息
	 * @return
	 */
	public Map<String ,Object> queryWidthInfo(String withd_id);
	
	/**
	 * 修改提现信息
	 * @return
	 */
	public Integer updateWidthInfo(Map<String ,Object> params);
	
	/**
	 * 查询红包发放记录列表
	 * @return
	 */
	public List<Map<String ,Object>> queryRedPacketList(Map<String,Object> params);
	
	/**
	 * 查询红包发放记录列表总条数
	 * @return
	 */
	public Integer queryRedPacketListCount(Map<String,Object> params);
	
	/**
	 * 总平台-待办事项
	 */
	public List<Map<String ,Object>> queryPlatformMsg(Map<String,Object> params);
	
	public Integer queryPlatformMsgCount(Map<String,Object> params);
	
}
