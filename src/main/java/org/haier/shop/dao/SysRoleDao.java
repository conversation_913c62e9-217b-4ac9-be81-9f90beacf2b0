package org.haier.shop.dao;

import java.util.List;
import java.util.Map;

import org.apache.ibatis.annotations.Param;
import org.haier.shop.entity.SysAction;
import org.haier.shop.entity.SysActionRole;
import org.haier.shop.entity.SysPermission;
import org.haier.shop.entity.SysRole;
import org.haier.shop.entity.SysRolePermission;

public interface SysRoleDao {
	public List<SysRole> quertRoleList(Map<String ,Object> params);//获取角色列表
	public int quertRoleListCount(Map<String ,Object> params);//获取角色列表总条数
	public void insert(SysRole role);//添加角色
	public void update(SysRole role);//修改角色
	public SysRole getRole(SysRole role);//获取角色详情
	public List<SysPermission> getMenuListByRoleCode(Map<String ,Object> params);//获取角色菜单列表
	public List<SysAction> getActionListByRoleCode(Map<String ,Object> params);//获取角色权限操作列表
	public void deleteActionRole(Map<String ,Object> params);//删除角色操作
	public void addActionRole(@Param("actionRoleList")List<SysActionRole> actionRoleList);//添加角色操作
	public void deleteRolePermission(Map<String ,Object> params);//删除角色权限
	public void addRolePermission(@Param("rolePermissionList")List<SysRolePermission> rolePermissionList);//添加角色权限
	public int getStaffByRoleCodeCount(Map<String ,Object> params);//获取该角色下员工数量
	public Map<String ,Object> getRoleByStaffId(Integer staff_id);//获取该员工角色
	public List<SysAction> getActionList(Map<String ,Object> params);//获取该权限归属所有操作列表
}
