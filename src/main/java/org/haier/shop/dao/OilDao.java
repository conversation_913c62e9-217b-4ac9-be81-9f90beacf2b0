package org.haier.shop.dao;

import java.util.List;
import java.util.Map;

public interface OilDao {
	
	/**
	 * 更新、新增店铺优惠活动
	 * @param map
	 * @return
	 */
	public Integer addNewShopDis(Map<String,Object> map);
	public Integer updateShopDis(Map<String,Object> map);
	
	public Integer queryShopDisCount(Map<String,Object> map);
	public List<Map<String,Object>> queryShopDisList(Map<String,Object> map);
	/**
	 * 删除油品关联关系
	 * @return
	 */
	public Integer deleteOilRelation(Map<String,Object> map);
	/**
	 * 如果油品信息被删除了，需要删除当前油价信息，防止重复添加；
	 * @param map
	 * @return
	 */
	public Integer deleteOilInterPrice(Map<String,Object> map);
	/**
	 * 更新油价信息
	 * @param map
	 * @return
	 */
	public Integer updateOilInterPrice(Map<String,Object> map);
	/**
	 * 添加油品的国标价格信息
	 * @param map
	 * @return
	 */
	public Integer addNewInterPrice(Map<String,Object> map);
	/**
	 * 更新油品、油枪信息
	 * @param map
	 * @return
	 */
	public Integer updateOilMsg(Map<String,Object> map);
	/**
	 * 添加新的油枪油号信息
	 * @param map
	 * @return
	 */
	public Integer addNewMsg(Map<String,Object> map);
	/**
	 * 查询油枪或者油号列表信息
	 * @param map
	 * @return
	 */
	public List<Map<String,Object>> queryOilMsgList(Map<String,Object> map);
	
}
