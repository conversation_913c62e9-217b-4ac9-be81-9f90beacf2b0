package org.haier.shop.dao;

import java.util.List;
import java.util.Map;

public interface NoticeDao {
	
	public Integer queryNoticeCount(String shop_unique);
	/**
	 * 查询列表
	 * @return
	 */
	public List<Map<String ,Object>> queryNoticeList(Map<String,Object> params);
	
	/**
	 * 查询列表总条数
	 * @return
	 */
	public Integer queryNoticeListCount(Map<String,Object> params);
	
	/**
	 * 添加
	 * @return
	 */
	public Integer addNotice(Map<String,Object> params);
	
	/**
	 * 添加店铺通知
	 * @return
	 */
	public Integer addNoticeShopList(List<Map<String,Object>> list);
	
	/**
	 * 获取详情
	 * @return
	 */
	public Map<String ,Object> queryNoticeDetail(String notice_id);
	
	/**
	 * 修改
	 * @return
	 */
	public Integer updateNotice(Map<String,Object> params);
	
	/**
	 * 删除
	 * @return
	 */
	public Integer deleteNotice(String notice_id);
	
	/**
	 *  删除店铺通知
	 * @return
	 */
	public Integer deleteNoticeShop(String notice_id);
	
	/**
	 * 获取店铺列表
	 * @return
	 */
	public List<Map<String ,Object>> queryShopList(Map<String,Object> params);

}
