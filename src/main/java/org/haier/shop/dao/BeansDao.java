package org.haier.shop.dao;


import java.util.List;
import java.util.Map;

import org.haier.customer.entity.SystemConfigVO;
import org.haier.shop.entity.ShopBeansVO;
import org.haier.shop.entity.TiCashVO;
import org.haier.shop.entity.beans.PageData;
import org.haier.shop.util.BeansRule;
import org.haier.shop.util.Page;
import org.haier.shop.util.PageQuery;


public interface BeansDao {
	
	
	public int getAgreement(String shop_unique);
	
	public int updateProtocol(PageData pd);
	
	
	public PageData getBeans(String shop_unique);
	
	public PageData getPtBeans(String shop_unique);
	
	public PageData queryCashDetail(PageData pd);
	
	public String queryShop_balance(PageData pd);
	
	
	public int getTransactionListCount(Page page);
	public List<PageData> getTransactionList(Page page);
	
	public List<PageData> getCusList(Page pd);
	
	/**
	 * 根据key获取系统配置 
	 */
	public SystemConfigVO querySystemConfig(SystemConfigVO systemConfigVO);
	
	public int addBenasRule(PageData pd);

	
	public BeansRule queryBenasRule(PageData pd);
	
	public int deleteBeansRule(PageData pd);
	
	public int updateBeansRule(PageData pd);
	
	public PageData queryBeansDiKu(PageData pd);
	
	public int queryeDikou(PageData pd);
	
	public int updateDikou(PageData pd);
	
	public int addDikou(PageData pd);
	
	public int addCash(PageData pd);
	
	public int updateBeanCount(PageData pd);
	
	public int addBeanOrder(PageData pd);
	
	public PageData findOneByTradeCode(String pd);
	
	public int payOrder(PageData pd);
	
	public int updateShopBeans(PageData pd);
	
	public List<PageData> queryCashList(PageData pd);
	
	public Integer queryTransactionList(PageQuery pageQuery);
	
	public Map<String,Object> queryTransactionList2(PageQuery pageQuery);
	
	public List<Map<String,Object>> queryTransactionListPage(PageQuery pageQuery);
	
	public List<Map<String,Object>> queryTransactionListPage2(PageQuery pageQuery);

	public int queryPayBeans(PageData pd);
	
	public Integer queryCusCount(PageData pd);
	
	public Map<String,Object> queryOrderListByPageCount(PageQuery pd);
	
	public Map<String,Object> queryOrderListByPageCount2(PageQuery pd);
	
	public List<Map<String,Object>> queryOrderListByPage(PageQuery pageQuery);
	
	public List<Map<String,Object>> queryOrderListByPage2(PageQuery pageQuery);
	
	public List<Map<String,Object>> queryPtLi3(Map<String,Object> map);
	
	public List<Map<String,Object>> queryDrawCashList(Map<String,Object> map);
	
	public int queryDrawCashListCount(Map<String,Object> map);
	
	
	
	public int queryPtLi3Count(Map<String,Object> map);
	
	public List<Map<String,Object>> queryCard(String shopUnique);
	
	public List<PageData> queryCardMain(PageData pd);
	
	public List<Map<String,Object>> queryBankName();
	
	public int addbankCard(PageData pd);
	
	public int updateBankCard(PageData pd);
	
	public int deleteBankCard(PageData pd);
	
	public List<Map<String,Object>> queryBankCard(String shop_unique);
	
	public ShopBeansVO getShopBeans(String shop_unique);
	
	public int addCardRecord(TiCashVO pd);
	
	public int updateCashOrder(PageData pd);
	
	public int shopBeansTixian(TiCashVO pd);
	
	public int updatePtBeans(PageData pd);
	
	public int addPtBeans(PageData pd);
	
	public int updatePtRule(PageData pd);
	
	public int updatePtGiveBeans(Integer give_beans);
	
	public int updateBoHuiCash(PageData pd);
	
	public int queryTransactionCount(String shop_unique, int tx_dtae);
	
	public int queryTransactionCount2(String shop_unique, int tx_dtae);
	
	public int queryTransactionCountPt(String shop_unique, int tx_dtae);
	
	public int queryTransactionCountPt2(String shop_unique, int tx_dtae);
	
	public PageData queryPtCashDetail(PageData pd);
	
	public int updateDrawCash(PageData pd);
	
	public int updateShopCash(PageData pd);
	
	public int updateShopCashNew(PageData pd);
	
	public int reduceShopCash(PageData pd);
	
	public int addDrawCash(PageData pd);
	
	/**
	 * 平台策略，查询平台内所有店铺的百货豆的抵扣和赠送情况
	 * @param pd
	 * @return
	 */
	public List<Map<String,Object>> queryShopsBeansListByPage(PageQuery  pd);
	
	public Integer queryShopsBeansListPages(PageQuery pageQuery);
	
	public Map<String,Object> getBeansTotal(PageQuery  pd);
	
	//获取店铺余额
	public Double getShopBalance(String shop_unique);
	
	public List<Map<String,Object>> queryShopBeansPromation(PageQuery  pd);
	
	public Integer queryShopBeansPromationCount(PageQuery pageQuery);
	
	public int queryShopBeanPromationCount(PageData pd);
	
	public int updateShopBeanPromation(PageData pd);
	
	public int addShopBeanPromation(PageData pd);
	
	public int updateShopBeanPromationStatus(PageData pd);
	
	public int addShopBeanPromationStatus(PageData pd);

	public Map<String,Object> queryBeansDiKuValidate(PageData pd);
	
}
