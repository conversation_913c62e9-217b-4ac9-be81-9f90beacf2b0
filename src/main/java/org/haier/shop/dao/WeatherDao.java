package org.haier.shop.dao;

import java.util.List;
import java.util.Map;

import org.haier.shop.entity.WeatherRecord;

public interface WeatherDao {
	
	/**
	 * 查询城市列表，用来查询信息
	 * @return
	 */
	public List<Map<String,Object>> queryCitiesList();

	
	/**
	 * 插入某市的在某天的天气信息
	 * @param map
	 * @return
	 */
	public int insertNewWeatherRocord(WeatherRecord wea);
	/**
	 * 添加新的天气详情记录
	 * @param map
	 * @return
	 */
	public int insertNewWeatherDetail(Map<String,Object> map);
}
