package org.haier.shop.dao;

import java.util.List;
import java.util.Map;

public interface AdDao {
	
	/**
	 * 查询广告列表
	 * @return
	 */
	public List<Map<String ,Object>> queryAdList(Map<String,Object> params);
	
	/**
	 * 查询广告列表总条数
	 * @return
	 */
	public Integer queryAdListCount(Map<String,Object> params);
	
	/**
	 * 添加广告
	 * @return
	 */
	public Integer addAd(Map<String,Object> params);
	
	/**
	 * 添加广告投放区域
	 * @return
	 */
	public Integer addAdAreaList(List<Map<String,Object>> list);
	
	/**
	 * 添加广告内容文件
	 * @return
	 */
	public Integer addAdContentFileList(List<Map<String,Object>> list);
	
	/**
	 * 添加广告优惠券
	 * @return
	 */
	public Integer addAdCouponList(List<Map<String,Object>> list);
	
	/**
	 * 获取广告详情
	 * @return
	 */
	public Map<String ,Object> queryAdDetail(String sys_ad_id);
	
	/**
	 * 获取广告投放区域列表
	 * @return
	 */
	public List<Map<String ,Object>> queryAdAreaList(String sys_ad_id);
	
	/**
	 * 获取广告投放内容列表
	 * @return
	 */
	public List<Map<String ,Object>> queryAdContentFileList(String sys_ad_id);
	
	/**
	 * 获取广告优惠券列表
	 * @return
	 */
	public List<Map<String ,Object>> queryAdCouponList(String sys_ad_id);
	
	/**
	 * 获取广告投放详情列表
	 * @return
	 */
	public List<Map<String ,Object>> queryAdLogList(String sys_ad_id);
	
	/**
	 * 修改广告
	 * @return
	 */
	public Integer updateAd(Map<String,Object> params);
	
	/**
	 * 删除广告
	 * @return
	 */
	public Integer deleteAd(String sys_ad_id);
	
	/**
	 *  删除广告区域
	 * @return
	 */
	public Integer deleteAdArea(String sys_ad_id);
	
	/**
	 * 删除广告内容文件
	 * @return
	 */
	public Integer deleteAdContentFile(String sys_ad_content_file_id);
	
	/**
	 * 删除广告优惠券
	 * @return
	 */
	public Integer deleteAdCoupon(String sys_ad_id);
	
	/**
	 * 获取省市区列表
	 * @return
	 */
	public List<Map<String ,Object>> queryAreaDictList(Map<String ,Object> params);
}
