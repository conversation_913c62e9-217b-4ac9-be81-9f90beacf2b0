package org.haier.shop.dao;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * 商家自采购进货单dao
 */
public interface SelfPurchaseDao {
	/**
	 * 1、自定义退货，查询退货商品的信息和库存
	 */
	public List<Map<String,Object>> queryGoodsListMsg(Map<String,Object> map);
	public List<Map<String,Object>> getSelfRetPurchaseDetail(Map<String,Object> map);
	public Map<String,Object> getSelfRetPurchase(Map<String,Object> map);
	/**
	 * 查询退款列表数量
	 * @param map
	 * @return
	 */
	public Integer getSupRetOrderListCount(Map<String,Object> map);	
	/**
	 * 查询退款列表详情
	 * @param map
	 * @return
	 */
	public List<Map<String,Object>> getSupRetOrderList(Map<String,Object> map);
	/**
	 * 添加退货出入库详情
	 * @param list
	 * @return
	 */
	public Integer addShopStockDetail(Map<String,Object> map);
	/**
	 * 添加退款订单详情
	 * @param list
	 * @return
	 */
	public Integer addNewReturnPurDetail(List<Map<String,Object>> list);
	/**
	 * 添加退款订单
	 * @param map
	 * @return
	 */
	public Integer addNewReturnPurList(Map<String,Object> map);
	/**
	 * 添加商品库存信息
	 */
	public Integer addShopStock(List<Map<String,Object>> list);
	/**
	 * 批量修改商品库存信息
	 * @param list
	 * @return
	 */
	public Integer modifyGoodsStock(List<Map<String,Object>> list);
	/**
	 * 查询订单的退货详情，要不要写在同一sql
	 * @param map
	 * @return
	 */
	public List<Map<String,Object>> getSelfPurchaseReturnDetailList(Map<String,Object> map);
		
	public List<Map<String,Object>> querySaleList(Map<String,Object> map);
	/**
	 * 查询进货订单列表
	 * @param shop_unique
	 * @param search_str 订单编号/供货商名称
	 * @param start_date 查询开始时间
	 * @param end_date 查询结束时间
	 * @param pay_status 支付状态：0全部支付 1欠款
	 * @param purchase_status 采购单状态：0待收货 1已完成 2已取消
	 * @param startNum
	 * @param pageSize
	 * @return
	 */	
	public List<Map<String ,Object>> getSelfPurchaseList(Map<String ,Object> params);
		
	/**
	 * 查询进货订单列表总条数
	 * @param shop_unique
	 * @param search_str 订单编号/供货商名称
	 * @param start_date 查询开始时间
	 * @param end_date 查询结束时间
	 * @param pay_status 支付状态：0全部支付 1欠款
	 * @param purchase_status 采购单状态：0待收货 1已完成 2已取消
	 * @return
	 */	
	public Integer getSelfPurchaseListCount(Map<String ,Object> params);
		
	/**
	 * 查询订单详情
	 * @param shop_unique 店铺唯一标示
	 * @param self_purchase_unique 订单编号
	 * @return
	 */	
	public Map<String ,Object> getSelfPurchase(Map<String ,Object> params);
		
	/**
	 * 查询订单子单列表
	 * @param shop_unique 店铺唯一标示
	 * @param self_purchase_unique 订单编号
	 * @return
	 */	
	public List<Map<String ,Object>> getSelfPurchaseDetailList(Map<String ,Object> params);
	
	/**
	 * 查询订单支付详情列表
	 * @param shop_unique 店铺唯一标示
	 * @param self_purchase_unique 订单编号
	 * @return
	 */	
	public List<Map<String ,Object>> getSelfPurchasePayList(Map<String ,Object> params);
	
	/**
	 * 添加进货单信息
	 * @param self_purchase_unique 自采购进货单唯一标示
	 * @param shop_unique 店铺唯一标示
	 * @param supplier_unique 供货商唯一标示
	 * @param total_count 进货单总数量
	 * @param total_price 进货单总金额
	 * @param arrears_price 欠款金额
	 * @param pay_status 支付状态：0全部支付 1欠款
	 * @param purchase_status 采购单状态：0待收货 1已完成 2已取消
	 * @param remark 订单备注
	 * @param create_staff_id 下单员工编号
	 * @param update_staff_id 修改员工编号
	 * @return
	 */	
	public Integer insertSelfPurchase(Map<String ,Object> params);
	
	/**
	 * 添加进货单详情信息
	 * @param self_purchase_unique 自采购进货单唯一标示
	 * @param goods_name 商品名称
	 * @param goods_barcode 商品编码
	 * @param goods_in_price 采购价，进价
	 * @param goods_count 商品数量
	 * @param gift_type 是否赠品：1非赠品 2赠品
	 * @return
	 */	
	public Integer insertSelfPurchaseDetail(List<Map<String ,Object>> list);
	
	/**
	 * 添加进货单支付记录信息
	 * @param self_purchase_unique 自采购进货单唯一标示
	 * @param need_topay 剩余未付金额
	 * @param pay_money 本次支付金额
	 * @param staff_id 操作员工编号
	 * @param source_type 操作终端：1商家后台 2Android 3ios
	 * @param network_ip 操作网络ip
	 * @return
	 */	
	public Integer insertSelfPurchasePay(Map<String ,Object> params);
	
	/**
	 * 修改进货单信息
	 * @param self_purchase_unique 自采购进货单唯一标示
	 * @param shop_unique 店铺唯一标示
	 * @param arrears_price 欠款金额
	 * @param pay_status 支付状态：0全部支付 1欠款
	 * @param purchase_status 采购单状态：0待收货 1已完成 2已取消
	 * @param update_staff_id 修改员工编号
	 * @return
	 */	
	public Integer updateSelfPurchase(Map<String ,Object> params);
	
	/**
	 * 删除进货单信息
	 * @param self_purchase_unique 自采购进货单唯一标示
	 * @param shop_unique 店铺唯一标示
	 * @param del_flag 删除标示:0正常 1删除
	 * @return
	 */	
	public Integer deleteSelfPurchase(Map<String ,Object> params);
}
