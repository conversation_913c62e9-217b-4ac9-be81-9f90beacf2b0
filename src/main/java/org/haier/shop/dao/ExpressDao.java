package org.haier.shop.dao;

import java.util.List;
import java.util.Map;

public interface ExpressDao {
	
	/**
	 * 查询快递公司列表
	 * @return
	 */
	public List<Map<String ,Object>> getExpressList(Map<String ,Object> params);
	
	/**
	 * 查询快递公司列表总条数
	 * @return
	 */
	public Integer getExpressListCount(Map<String ,Object> params);
	
	/**
	 * 添加快递公司信息
	 * @return
	 */
	public void addExpress(Map<String ,Object> params);
	
	/**
	 * 获取快递公司信息
	 * @return
	 */
	public Map<String ,Object> getExpress(String express_id);
	
	/**
	 * 修改快递公司信息
	 * @return
	 */
	public void updateExpress(Map<String ,Object> params);
	
	/**
	 * 删除快递公司信息
	 * @return
	 */
	public void deleteExpress(String express_id);

	public List<Map<String, Object>> queryExpressPriceList(Map<String, Object> params);

	public Integer queryExpressPriceListCount(Map<String, Object> params);

	public List<Map<String, Object>> queryParentAddress();

	public void addExpressPrice(Map<String, Object> params);

	public void addExpressPriceAddress(Map<String, Object> params);

	public Map<String, Object> queryExpressPriceDetail(Map<String, Object> params);

	public List<Map<String, Object>> queryExpressPriceAddressList(Map<String, Object> params);

	public void editExpressPrice(Map<String, Object> params);

	public void deleteExpressPriceAddress(Map<String, Object> params);

	public void deleteExpressPrice(Map<String, Object> params);

	public List<Map<String, Object>> queryRiderCountList(Map<String, Object> params);

	public Integer queryRiderCountListCount(Map<String, Object> params);

	public List<Map<String, Object>> queryRiderOrderList(Map<String, Object> params);

	public Integer queryRiderOrderListCount(Map<String, Object> params);

	public Map<String, Object> queryRiderOrderDetail(Map<String, Object> params);

	public List<Map<String, Object>> queryExpressCountList(Map<String, Object> params);

	public Integer queryExpressCountListCount(Map<String, Object> params);
}
