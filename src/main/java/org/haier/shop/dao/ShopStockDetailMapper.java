package org.haier.shop.dao;

import org.apache.ibatis.annotations.Param;
import org.haier.shop.entity.ShopStockDetail;
import org.haier.shop.params.stock.ShopStockDetailQueryParam;
import org.haier.shop.result.stock.ShopStockDetailVO;

import java.util.List;

/**
* @Description 库存主表
* @ClassName ShopStockDetail
* <AUTHOR> 
* @Date 2024-04-29
**/
public interface ShopStockDetailMapper {

    ShopStockDetail selectByListUnique(@Param("listUnique") String listUnique);

    /**
     * 出入库列表
     * @param params
     * @return
     */
    List<ShopStockDetailVO> listPage(@Param("params") ShopStockDetailQueryParam params);

    Integer listPageCount(@Param("params") ShopStockDetailQueryParam params);

    ShopStockDetail selectById(Long shopStockDetailId);

    int insertShopStockDetail(ShopStockDetail shopStockDetail);

    int updateById(ShopStockDetail shopStockDetail);
}