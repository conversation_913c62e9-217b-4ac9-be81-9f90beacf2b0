package org.haier.shop.dao;

import org.haier.shop.entity.speech.SpeechCmdEntity;
import org.haier.shop.params.speech.QuerySpeechCmdListParams;

import java.util.List;

public interface SpeechCmdDao {

    /**
     * 新增一条语音指令
     * @param speechCmdEntity
     * @return
     */
    public Integer addSpeechCmd(SpeechCmdEntity speechCmdEntity);
    /**
     * 查询一条语音指令
     * @param speechCmdEntity
     * @return
     */
    public SpeechCmdEntity querySpeechCmdEntity(SpeechCmdEntity speechCmdEntity);

    /**
     * 查询通用语音指令
     * @param speechCmdEntity
     * @return
     */
    public List<SpeechCmdEntity> queryCommonSpeechList(SpeechCmdEntity speechCmdEntity);

    public List<SpeechCmdEntity> querySpeechCmdList(QuerySpeechCmdListParams params);
    public Integer querySpeechCmdListCount(QuerySpeechCmdListParams params);

}
