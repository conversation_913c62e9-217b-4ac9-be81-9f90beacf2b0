package org.haier.shop.dao;

import java.util.List;
import java.util.Map;

public interface InfoPushDao {
	
	/**
	 * 查询列表
	 * @return
	 */
	public List<Map<String ,Object>> queryInfoList(Map<String,Object> params);
	
	/**
	 * 查询列表总条数
	 * @return
	 */
	public Integer queryInfoListCount(Map<String,Object> params);
	
	/**
	 * 添加
	 * @return
	 */
	public Integer addInfo(Map<String,Object> params);
	
	/**
	 * 添加推送区域
	 * @return
	 */
	public Integer addInfoPushAreaList(List<Map<String,Object>> list);
	
	/**
	 * 获取详情
	 * @return
	 */
	public Map<String ,Object> queryInfoDetail(String info_id);
	
	/**
	 * 获取推送区域列表
	 * @return
	 */
	public List<Map<String ,Object>> queryInfoPushAreaList(String info_id);
	
	/**
	 * 修改
	 * @return
	 */
	public Integer updateInfo(Map<String,Object> params);
	
	/**
	 * 删除
	 * @return
	 */
	public Integer deleteInfo(String info_id);
	
	/**
	 *  删除推送区域
	 * @return
	 */
	public Integer deleteInfoPushArea(String info_id);
	
	/**
	 * 添加会员推送信息
	 * @return
	 */
	public Integer addInfoPushMemberList(List<Map<String,Object>> list);
	
	/**
	 * 获取所有会员列表
	 * @return
	 */
	public List<Map<String ,Object>> queryAllMemberList();
	
	/**
	 * 获取推送区域内会员列表
	 * @return
	 */
	public List<Map<String ,Object>> queryMemberListByArea(String info_id);
	
	/**
	 * 查询会员消息列表
	 * @return
	 */
	public List<Map<String ,Object>> getInfoPushMemberList(Map<String,Object> params);
	
	/**
	 * 获取会员消息详情
	 * @return
	 */
	public Map<String ,Object> getInfoPushMember(String id);
	
	/**
	 * 修改会员消息已读
	 * @return
	 */
	public Integer updateInfoPushMember(Map<String,Object> params);

	public List<Map<String, Object>> queryShopList(Map<String, Object> params);
}
