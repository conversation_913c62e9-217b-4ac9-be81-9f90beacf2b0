package org.haier.shop.dao;

import java.util.List;
import java.util.Map;

/**
 * 供货商城持久层
 * @author: yuliangliang
 * @date: 2019年10月24日
 */
public interface SupplierShoppingDao {
	
	public Integer inserPromotionUnionpays(List<Map<String,Object>> list);
	
	public Map<String ,Object> queryGoldConfig(String deduction);
	
	public Map<String ,Object> queryGoldByShop(Map<String ,Object> params);
	
	public Map<String ,Object> queryGoldByShop2(Map<String ,Object> params);
	
	public List<Map<String ,Object>> queryGoldRewardDetail(Map<String ,Object> params);
	
	public int queryGoldRewardDetailCount(Map<String ,Object> params);
	
	public int updateShopGold(Map<String ,Object> params);
	
	public int addShopGold(Map<String ,Object> params);
	
	public int insertPromotionUnionpay(Map<String ,Object> params);
}
