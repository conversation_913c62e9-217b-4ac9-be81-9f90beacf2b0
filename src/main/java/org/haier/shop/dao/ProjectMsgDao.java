package org.haier.shop.dao;

import java.util.List;
import java.util.Map;

/**
* @author: 作者:王恩龙
* @version: 2023年6月14日 下午2:48:40
*
*/
public interface ProjectMsgDao {
	
	
	/**
	 * 添加新的项目
	 * @param map
	 * @return
	 */
	public Integer addNewProjectMsg(Map<String,Object> map);
	/**
	 * 获取项目列表
	 * @param map
	 * @return
	 */
	public List<Map<String,Object>> queryProjectMsgList(Map<String,Object> map);
	/**
	 * 获取项目数量
	 * @return
	 */
	public Integer queryProjectMsgCount(Map<String,Object> map);

	public List<Map<String,Object>> queryShopsDeviceVeriosn(Map<String,Object> map);

	public Integer queryShopsDeviceVeriosnCount(Map<String,Object> map);

	public Integer updateShopsVersion(Map<String,Object> map);


}
