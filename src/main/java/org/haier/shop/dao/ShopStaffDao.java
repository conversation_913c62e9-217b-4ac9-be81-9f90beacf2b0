package org.haier.shop.dao;

import java.util.List;
import java.util.Map;

import org.haier.shop.entity.Staff;
import org.haier.shop.pojo.QueryShopInfoPo;
import org.haier.shop.result.card.Personnel;

/**
 * 员工相关接口
 * <AUTHOR>
 *
 */
public interface ShopStaffDao {

	public List<Personnel> queryStaffList(Map<String,Object> map);
	/**
	 * 添加新员工
	 * @param staff
	 * @return
	 */
	public Integer newStaff(Staff staff);
	
	/**
	 * 管理员权限添加
	 * @param map
	 * @return
	 */
	public Integer newStaffPower(Map<String,Object> map);
	
	/**
	 * 登录接口，查询管理员信息
	 * <AUTHOR>
	 * @param map
	 * @return
	 */
	public Map<String, Object> staffLoginByAccountPwd(Map<String,Object> map);
	/**
	 * 登录后，查询管理员管理的店铺信息
	 * <AUTHOR>
	 * @param map
	 * @return
	 */
	public List<Map<String,Object>> queryShopsByManager(Map<String,Object> map);
	
	/**
	 * 查询管理员下面的所有员工权限信息
	 * @param map
	 * @return
	 */
	public List<Map<String ,Object>> queryShopManager(Map<String,Object> map);
	/**
	 * 修改员工权限
	 * @param map
	 * @return
	 */
	public int modifyStaffPower(Map<String,Object> map);
	
	/**
	 * 获取员工列表信息
	 * @param map
	 * @return
	 */
	public List<Map<String,Object>> queryShopManagers(Map<String,Object> map);
	/**
	 * 根据员工编号获取相关权限
	 * @param map
	 * @return
	 */
	public Map<String,Object> queryManagerPower(Map<String,Object> map);
	/**
	 * 管理员账户信息查询（用于添加评论）
	 * @param map
	 * @return
	 */
	public Map<String,Object> queryManagerMessage(Map<String,Object> map);
	
	/**
	 * 员工分页数量查询
	 * @param map
	 * @return
	 */
	public int queryStaffsPages(Map<String,Object> map);
	/**
	 * 员工信息查询
	 * @param map
	 * @return
	 */
	public List<Map<String,Object>> queryStaffByPage(Map<String,Object> map);
	
	/**
	 * 
	 * 员工详情查询
	 * @param map
	 * @return
	 */
	public Map<String,Object> queryStaffDetailMessage(Map<String,Object> map);
	
	/**
	 * 更新员工基本信息
	 * @param map
	 * @return
	 */
	public int updateStaffBaseMessage(Map<String,Object> map);
	
	/**
	 * 查询店铺的提醒方式
	 * @param map
	 * @return
	 */
	public Integer getShopsSet(Map<String,Object> map);
	/**
	 * 管理员旗下店铺信息查询
	 * @param map
	 * @return
	 */
	public Integer queryAllShopsPages(Map<String,Object> map);
	/**
	 * 分页查询同管理员店铺信息
	 * @param map
	 * @return
	 */
	public List<Map<String,Object>> queryAllShopsByPage(Map<String,Object> map);
	/**
	 * 创建新的管理员子店铺信息
	 * @param map
	 * @return
	 */
	public int createNewShop(Map<String,Object> map);
	/**
	 * 业绩页数查询
	 * @param map
	 * @return
	 */
	public Integer queryGoodsSaleByStaff(Map<String,Object> map);
	/**
	 * 分页业绩查询详情
	 * @param map
	 * @return
	 */
	public List<Map<String,Object>> queryGoodsSaleByStaffPage(Map<String,Object> map);

	public Map<String, Object> getStaffById(Map<String, Object> map);

	public Map<String, Object> queryShopInfoByShopUnique(Map<String, Object> map);

	public void saveShopImage(Map<String, Object> imageMap);

	public List<Map<String, Object>> queryShopImageList(Map<String, Object> map);

	public void updateShopImage(Map<String, Object> map);
	
	//根据登录账户获取登录用户信息
	public Staff getStaffByAccount(String staff_account);
	
	//添加员工与角色关联表
	public void addStaffRole(Map<String ,Object> params);
	
	//修改员工与角色关联表
	public void updateStaffRole(Map<String ,Object> params);
	
	//获取员工与角色关联信息
	public Map<String ,Object> getStaffRole(Map<String ,Object> params);
	
	//删除员工信息
	public void deleteStaff(String staffId);
	
	//删除员工角色信息
	public void deleteStaffRole(String staffId);

	Map<String, Object> queryStaffByParams(QueryShopInfoPo po);
}
