package org.haier.shop.dao;

import java.util.List;
import java.util.Map;

import org.haier.shop.entity.dataScreen.AreaYN;

public interface DataScreenDao {
	
	/**
	 * 定时刷新每周新增店铺的数量
	 * @param count
	 * @return
	 */
	public Integer newShopCount(Integer count);
	/**
	 * 
	 * @return
	 */
	public Integer queryOnlineCount();
	/**
	 * 查询快递服务总数量
	 * @return
	 */
	public List<Integer> queryExpressCount();
	
	/**
	 * 获取最新注册的店铺数量
	 * @param map
	 * @return
	 */
	public Integer newShopsCountYN(Map<String,Object> map);
	/**
	 * 销量前五的商品信息查询
	 * @return
	 */
	public List<Map<String,Object>> top5GoodsMessage();
	/**
	 * 各类消费方式支付占比
	 * @return
	 */
	public List<Map<String,Object>> payTypeMessage();
	
	/**
	 * 查询订达成率
	 * @return
	 */
	public Map<String,Object> proportionOfOrder();
	
	/**
	 * 查询指定时间（300秒）内的订单信息
	 */
	public List<Map<String,Object>> getSaleListTotalMessageBySecond();
	
	public List<Map<String,Object>> queryEvaluateList();
	
	/**
	 * 线上销售额昨日对比
	 * @return
	 */
	public Map<String,Object> onLineSaleComparsionYesterday();
	
	/**
	 * 排名前五十的商品信息
	 * @return
	 */
	public List<Map<String,Object>> top50characterCloud();
	/**
	 * 系统当前时间
	 * @return
	 */
	public String selectNow();
	/**
	 * 当日营业流水
	 * @return
	 */
	public Map<String,Object> getDailyTurnover();
	
	//昨日流水
	public Map<String,Object> getYestodayOrder();
	
	/**
	 * 本周新增商店数量
	 * @param map
	 * @return
	 */
	public int newShopsCount(Map<String,Object> map);
	
	/**
	 * 每小时内的订单信息查询
	 * @param map
	 * @return
	 */
	public List<Map<String,Object>> orderTotalByHours(Map<String,Object> map);
	
	
	/**
	 * 最新订单信息
	 * @return
	 */
	public List<Map<String,Object>> lastestOrder();
	
	public List<Map<String,Object>> lastestOrder2();
	
	/**
	 * 销量前五店铺销售信息
	 * @return
	 */
	public List<Map<String,Object>> top5ShopsList();
	/**
	 * 商品销售商品分类
	 * @return
	 */
	public List<Map<String,Object>> kindSaleRatio();
	/**
	 * 近一月系统销售额
	 * @return
	 */
	public List<Map<String,Object>> lastMonthSaleTotal(Map<String,Object> map);
	
	/**
	 * 近一月消费额益农
	 * @param map
	 * @return
	 */
	public List<Map<String,Object>> lastMonthSaleTotalYN(Map<String,Object> map);
	/**
	 * 线上采购量昨日比
	 * @return
	 */
	public Map<String,Object > onLinePurComparsionYesterday();
	/**
	 * 60S内每秒的订单数量
	 * @return
	 */
	public List<Map<String,Object>> getPurListTotalMessageBySecond();
	
	/**
	 * 根据已选择的省份，选择器对应的城市列表
	 * @param map
	 * @return
	 */
	public List<Map<String,Object>> getCitiesInProvince(Map<String,Object> map);
	
	/**
	 * 根据选择的省市信息，选定在线的数据信息
	 * @param map
	 * @return
	 */
	public List<Map<String,Object>> getShopsInProvince(Map<String,Object> map);
	
	/**
	 * 查询今日销售的毛利润top3商品
	 * @param map
	 * @return
	 */
	public List<Map<String,Object>> queryTop3GrossProfit(Map<String,Object> map);
	
	/**
	 * 各类型店铺数量统计
	 * @return
	 */
	public List<Map<String,Object>> queryShopCountByType();
	
	public List<Map<String,Object>> getAllShopYN();
	public List<Map<String,Object>> getAllShop();
	
	public String[] getCusName();
	
	/**
	 * 每日定时统计昨日的订单金额(测试数据专用)
	 */
	public Integer statisticsTotal();
	
	public void addNewOnlineCount(List<Map<String,Object>> list);
	
	/**
	 * 获取各区县的站点数量
	 * @return
	 */
	public List<AreaYN> queryYNshopByAreaAll(String areaDictNum);
	
	public List<Map<String,Object>> queryYNshopByArea(String areaDictNum);
}
