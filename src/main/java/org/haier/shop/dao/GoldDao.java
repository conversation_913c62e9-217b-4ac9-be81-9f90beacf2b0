package org.haier.shop.dao;

import java.util.List;
import java.util.Map;

public interface GoldDao {
	

	public List<Map<String ,Object>> queryGoldShopList(Map<String ,Object> params);

	public Integer queryGoldShopCount(Map<String ,Object> params);
	
	public List<Map<String ,Object>> queryGoldDeviceList(Map<String ,Object> params);

	public Integer queryGoldDeviceCount(Map<String ,Object> params);
	
	public List<Map<String ,Object>> queryGoldOrderList(Map<String ,Object> params);

	public Integer queryGoldOrderCount(Map<String ,Object> params);
	
	public Map<String ,Object>  queryGoldRule();
	
	public Integer updatePtRule(Map<String ,Object> params);
	
	public Integer updatePtRule2(Map<String ,Object> params);
	
	public Integer updatePtRule3(Map<String ,Object> params);
	
	public Integer updatePtRule4(Map<String ,Object> params);
	
	public List<Map<String ,Object>> queryGoldDevice(String shop_unique);
	
	public Map<String ,Object> queryGoldOrder(String shop_unique);
	
	public Integer addGlodOrder(Map<String ,Object> params);
	
	public Integer updateGlodOrder(Map<String ,Object> params);
	
	public Integer addGlodDevice(List<Map<String ,Object>> params);
	
	public Integer updateGlodDevice(List<Map<String ,Object>> params);
	
	public List<Map<String, Object>> queryGoldShop();
	
	public Integer addGlodShop(Map<String ,Object> params);
	
	public List<Map<String ,Object>> queryGoldShopNeedList(Map<String ,Object> params);
	
	public List<Map<String ,Object>> queryGoldShopNeedList2(Map<String ,Object> params);
	
	public Integer updateShopGold(List<Map<String ,Object>> params);
	
	public Integer addShopGoldRewards(List<Map<String ,Object>> params);
	
	public List<Map<String ,Object>> getGoldDeviceRewardList(Map<String ,Object> params);
	
	public Integer delGoldDetail(Map<String ,Object> params);
	
}
