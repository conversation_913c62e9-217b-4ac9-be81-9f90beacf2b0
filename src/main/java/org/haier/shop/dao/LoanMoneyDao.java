package org.haier.shop.dao;

import java.util.List;
import java.util.Map;

public interface LoanMoneyDao {

	/**
	 * 恢复店铺赊销额度
	 * @param map
	 * @return
	 */
	public Integer rebackShopLoanMsg(Map<String,Object> map);
	/**
	 * 1更新分期还款日志
	 * @param list
	 * @return
	 */
	public Integer refreshFenqiLog(List<Map<String,Object>> list);
	/**
	 * 查询店铺未完成的还款信息
	 * @param map
	 * @return
	 */
	public List<Map<String,Object>> queryUnDoFenQiLog(Map<String,Object> map);
	/**
	 * 统计指定时间段内还款的金额统计
	 * @param map
	 * @return
	 */
	public Map<String,Object> queryLoanReturnStatistics(Map<String,Object> map);
	public Map<String, Object> queryIsOpenLoan(Map<String, Object> params);

	public void editOpenLoan(Map<String, Object> map);

	public void addOpenLoan(Map<String, Object> map);

	public void saveReturnMoney(Map<String, Object> params);

	public Map<String, Object> queryReturnMoneyOrder(Map<String, Object> params);

	public void updateReturnMoneyOrderStatus(Map<String, Object> params);

	public void updateShopReturnMoney(Map<String, Object> order);

	public List<Map<String, Object>> queryLoanReturnList(Map<String, Object> map);

	public Integer queryLoanReturnListCount(Map<String, Object> map);

	public List<Map<String, Object>> queryLoanList(Map<String, Object> map);

	public Integer queryLoanListCount(Map<String, Object> map);

	public Map<String, Object> queryAdvanceMoney(Map<String, Object> params);
	public Map<String, Object> queryShopJGId(Map<String, Object> order);
	
}
