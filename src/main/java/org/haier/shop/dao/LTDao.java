package org.haier.shop.dao;

import java.util.List;
import java.util.Map;

public interface LTDao {

	/*
	 * 查询一刻钟到家小程序是否有相同手机号会员
	 */
	public Map<String,Object> querySamePhoneCus(Map<String,Object> map);

	public List<Map<String, Object>> queryCusList(Map<String, Object> map);

	public Integer queryCusListCount(Map<String, Object> map);

	public void updateCusStatus(Map<String, Object> map);

	public Map<String, Object> queryReturnMoneyConfig(Map<String, Object> map);

	public void updateCommissioner(Map<String, Object> map);

	public Map<String, Object> queryCommissionerById(Map<String, Object> map);

	public void updateCommYongJin(Map<String, Object> data);

	public void updateCommYongJinTwo(Map<String, Object> data);

	public List<Map<String, Object>> queryTpyList(Map<String, Object> map);

	public Integer queryTpyListCount(Map<String, Object> map);

	public void updateTpyInfo(Map<String, Object> map);

	public Integer queryTeamListCount(Map<String, Object> map);

	public List<Map<String, Object>> queryTeamList(Map<String, Object> map);

	public Map<String, Object> queryUserYJAllAndBalance(Map<String, Object> params);

	public List<Map<String, Object>> queryYJDetailList(Map<String, Object> map);

	public Integer queryYJDetailListCount(Map<String, Object> map);

	public void updateCommissBalanceWithdrawal(Map<String, Object> params);

	public void saveRecharge(Map<String, Object> params);

	public Map<String, Object> queryCusDetail(Map<String, Object> params);

	public List<Map<String, Object>> queryCusFile(Map<String, Object> params);

	public Map<String, Object> queryStaffAreaCode(Map<String, Object> map);

	public List<Map<String, Object>> queryYjMoneyList(Map<String, Object> map);

	public Map<String, Object> queryYjMoneyListCount(Map<String, Object> map);

	public List<Map<String, Object>> queryFinanceList(Map<String, Object> map);

	public Integer queryFinanceListCount(Map<String, Object> map);

	public void updateReturnMoneyConfig(Map<String, Object> map);

	public List<Map<String, Object>> queryCashList(Map<String, Object> map);

	public Integer queryCashListCount(Map<String, Object> map);

	public void updateCashStatus(Map<String, Object> map);

	public List<Map<String, Object>> queryLTReturnMonthMoneyList();

	public void updateBusinessReturnMoney(Map<String, Object> map);

	public Map<String, Object> queryCashInfo(Map<String, Object> map);

	public void updateCommissCashReteturn(Map<String, Object> data);

	public void updateListStatus(Map<String, Object> map);

	public void addReturnMoneyConfig(Map<String, Object> mapBean);

	public List<Map<String, Object>> queryReturnMoneyConfigList();

	public void updateCommissioner2(Map<String, Object> map);

	public void deleteReturnMoneyConfigList(String id);

	public void saveReturnParentMoney(Map<String, Object> parent_params);

	public List<Map<String, Object>> queryLTReturnParentMonthMoneyList();

	public void updateCommissionerYongJin(Map<String, Object> map);

	public void updateLTReturnParentLogStatus(Map<String, Object> map);

	public List<Map<String, Object>> queryLTReturnCouponList();

	public void addShopCoupon(Map<String, Object> coupon);

	public void addShopCouponCus(Map<String, Object> coupon);

	public void updateBusinessReturnMoney2(Map<String, Object> map);
	
}
