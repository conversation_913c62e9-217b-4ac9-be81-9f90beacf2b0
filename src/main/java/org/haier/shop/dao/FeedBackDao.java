package org.haier.shop.dao;

import java.util.List;
import java.util.Map;

public interface FeedBackDao {

	Map<String, Object> queryFeedBackListCount(Map<String, Object> map);

	List<Map<String, Object>> queryFeedBackList(Map<String, Object> map);

	void targetFeedBack(Map<String, Object> map);
	
	Map<String ,Object> queryFeedBackDetail(String feed_back_id);
	List<Map<String ,Object>> queryFeedBackImageList(String feed_back_id);

	void saveChuLi(Map<String, Object> map);

	Map<String, Object> queryFeedBackPhoneListCount(Map<String, Object> map);

	List<Map<String, Object>> queryFeedBackPhoneList(Map<String, Object> map);

	void addFeedBackPhone(Map<String, Object> map);

	Map<String, Object> getFeedBackPhoneInfo(Map<String, Object> map);

	void editFeedBackPhone(Map<String, Object> map);

	void deleteFeedBackPhone(Map<String, Object> map);

	Map<String, Object> queryShopQualificationListCount(Map<String, Object> map);

	List<Map<String, Object>> queryShopQualificationList(Map<String, Object> map);

	List<Map<String, Object>> queryShopQualificationDetail(Map<String, Object> map);

	Map<String, Object> queryShopExamineListCount(Map<String, Object> map);

	List<Map<String, Object>> queryShopExamineList(Map<String, Object> map);

	Map<String, Object> queryShopExamineDetail(Map<String, Object> map);
	
	Map<String, Object> queryShopPayInfo(String shop_unique);//获取店铺支付信息
	
	Integer updateShopPayInfo(Map<String ,Object> params);//修改店铺支付信息

	Map<String, Object> querySupplierExamineDetail(Map<String, Object> map);

	void updateShopExamine(Map<String, Object> map);

	void updateSupplierExamine(Map<String, Object> map);

	Map<String, Object> queryGoodsKindImageListCount(Map<String, Object> map);

	List<Map<String, Object>> queryGoodsKindImageList(Map<String, Object> map);

	Map<String, Object> queryGoodsKindImageDetail(Map<String, Object> map);

	int editGoodsKindImage(Map<String, Object> map);
	
	int addMemReward(Map<String, Object> map);
	


}
