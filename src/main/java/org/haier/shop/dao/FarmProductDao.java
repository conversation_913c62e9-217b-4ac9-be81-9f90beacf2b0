package org.haier.shop.dao;

import java.util.List;
import java.util.Map;

import org.haier.shop.entity.FarmProductShelf;
import org.haier.shop.entity.globalSelect.FarmKind;
import org.haier.shop.entity.globalSelect.GlobalDetailVO;

public interface FarmProductDao{
	
	/**
	 * 更新farmKind
	 * @param farmKind
	 * @return
	 */
	public Integer modifyFarmMsg(FarmKind farmKind);
	public Integer addNewFarmKind(FarmKind farmKind);//添加新的farmKind
	/**
	 * 查询农产品上架分类信息
	 * @return
	 */
	public List<FarmKind> queryFarmKind(Map<String,Object> map);
	public Integer queryFarmKindCount(Map<String,Object> map);
	public List<Map<String ,Object>> queryFarmProductAuditList(Map<String,Object> params);
	
	public List<Map<String ,Object>> getFarmProductSpecList(Map<String,Object> params);

	public int queryFarmProductAuditCount(Map<String,Object> params);
	
	public int updateFarmProductStatus(Map<String,Object> params);
	
	public int insertFarmProductAudit(Map<String,Object> params);
	
	public int insertShelfPrice(Map<String,Object> params);
	
	public List<Map<String ,Object>> queryFarmProductShelfList(Map<String,Object> params);
	
	public List<Map<String ,Object>> getFarmProductShelfDetail(Map<String,Object> params);
	
	public int queryFarmProductShelfCount(Map<String,Object> params);
	
	public int insertFarmProductShelf(FarmProductShelf shelf);
	
	public int delete_shopping_cart(Map<String,Object> params);
	
	public int updateGoodShelfStatus(Map<String,Object> params);
	
	public List<Map<String ,Object>> queryFarmOrderList(Map<String,Object> params);
	
	public List<Map<String ,Object>> queryFarmOrderExcel(Map<String,Object> params);

	public int queryFarmOrderListCount(Map<String,Object> params);
	
	public int updateFarmExpress(Map<String,Object> params);
	
	public int updateFarmSubOrder(Map<String,Object> params);
	
	public int updateFarmSubOrderBattch(Map<String,Object> params);
	
	public int updateSalePrice(Map<String,Object> params);
	
	public List<Map<String ,Object>> getOrderDetail(Map<String,Object> params);
	
	public List<Map<String ,Object>> getProvinceList(List<Map<String ,Object>> list);//获取有商家的省份列表
	public List<Map<String ,Object>> getCityList(List<Map<String ,Object>> list);//获取有商家的城市列表
	public List<Map<String ,Object>> getAreaList(Map<String,Object> map);//获取有商家的区域列表	
	
	
	
	public GlobalDetailVO queryGlobalThemeDetail(Map<String,Object> map);//精选详情
	
	public List<Map<String ,Object>> queryPTGGShelfList(Map<String,Object> params);

	public int queryPTGGShelfCount(Map<String,Object> params);
	
	public List<Map<String ,Object>> queryPTGGShelf2List(Map<String,Object> params);//从未上架的店铺
	public int addGoodShelfList(List<Map<String ,Object>> list);//全部上架
	public int updateGoodShelf(Map<String,Object> params);//全部下架
	
	public int updateGoodShelfCompulsory(Map<String,Object> params);//强制上架
	
	public List<Map<String ,Object>> queryGlobalSecretaryList(Map<String,Object> params);

	public int queryGlobalSecretaryCount(Map<String,Object> params);
	
	public int addSecretary(Map<String,Object> params);
	
	public int deleteGlobalSecretary(Map<String,Object> params);
	
	public Map<String ,Object> querySecretary(String map);
	
	public int updateSecretary(Map<String,Object> params);
	
	public List<Map<String ,Object>> queryArea(Map<String,Object> params);
	
	public int batchUpdateFarmLogistics(List<Map<String ,Object>> list);
	
	public int updateFarmSalePrice(Map<String,Object> params);
	public void updateFarmSalePriceSup(Map<String, Object> params);
	public List<Map<String, Object>> queryAreaShop(Map<String, Object> map);
	public void delete_farm_product_shelf(Map<String, Object> map);
	
	public List<Map<String ,Object>> queryFarmInformation();
	
	public int addFarmInformation(Map<String,Object> params);
	public List<Map<String, Object>> queryFarmOrderListAll(Map<String, Object> map);
	public Integer queryFarmOrderListAllCount(Map<String, Object> map);
	public Map<String, Object> queryFarmOrderDetail(Map<String, Object> map);
	public List<Map<String, Object>> queryFarmOrderDetailD(Map<String, Object> map);
	public Map<String, Object> queryFarmOrderDetailSum(Map<String, Object> map);
	
}
