package org.haier.shop.dao;

import java.util.List;
import java.util.Map;

public interface DataReadDao {
	
	/**
	 * 定时修改到店人数
	 */
	public void peopleArrivingAdd();
	
	/**
	 * 定时修改店铺在线数量
	 */
	public void resetShopOnlineCount(List<Map<String,Object>> list);
	
	/**
	 * 定时获取各店铺的销售额信息
	 * @return
	 */
	public List<Map<String,Object>> getStarShop();
	
	//明星店铺
	/**
	 * 定时将获取的店铺销售额信息更新到shop_start表
	 * @param list
	 * @return
	 */
	public Integer resetStartShop(List<Map<String,Object>> list);
	
	/**
	 * 更新统计的数据
	 * @param list
	 */
	public void resetStartShopOne(Map<String,Object> list);
	/**
	 * 每天凌晨将店铺信息插入到shop_start表中
	 */
	public void addShopStartRecord();
	
	
	//更新各区间段内的订单数量
	/**
	 * 查询区间段信息
	 * @return
	 */
	public List<Map<String,Object>> getListStage();
	/**
	 * 获取各区间段内的订单数量信息
	 * @param map
	 * @return
	 */
	public Map<String,Object> getListIncomeStage(Map<String,Object> map);
	/**
	 * 将查询到的订单数量更新到list_count_stage
	 * @param list
	 * @return
	 */
	public Integer resetListCountStage(List<Map<String,Object>> list);
	
	
	/**
	 * 清空备份
	 */
	public void clearHeatMap();
	/**
	 * 新建备份
	 */
	public void addNewHeatMap();
	
	public void addTopGoods();
	
	/**
	 * 备份-
	 */
	public void queryGroupByAgeNum();
	
	/**
	 * 定时备份会员各年龄段人数
	 */
	public void queryGroupByAgeCount();
	
	
}
