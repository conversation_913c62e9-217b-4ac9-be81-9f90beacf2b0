package org.haier.shop.dao;

import java.util.List;
import java.util.Map;

import org.apache.ibatis.annotations.Param;
import org.haier.shop.entity.ShopPayMsg;
import org.haier.shop.entity.publicEntity.PageSearch;
import org.haier.shop.params.SetSubAccountParams;

public interface PayTypeDao {
	
	public List<Map<String,Object>> queryShopPayMsgExamineStatus();//获取店铺支付信息提交审核的所有审核状态
	public Integer queryShopPayMsgCount(Map<String,Object> map);
	public List<Map<String,Object>> queryShopPayMsgByPage(@Param(value="map")Map<String,Object> map ,@Param(value="ps")PageSearch pageSearch);
	
	public Map<String,Object> queryShopYiTongPayMsg(String shopUnique);
	
	/**
	 * 查询店铺所有的支付方式
	 * @param map
	 * @return
	 */
	public List<Map<String,Object>> queryPayType(Map<String,Object> map);
	
	/**
	 * 修改支付方式的内容或默认状况
	 * @param map
	 * @return
	 */
	public Integer modiyfPayType(Map<String,Object> map);
	
	/**
	 * 查询平台所有的支付方式
	 * @param map
	 * @return
	 */
	public List<Map<String,Object>> queryPlatPayType(Map<String,Object> map);
	
	/**
	 * 更新平台支付方式信息
	 * @param map
	 * @return
	 */
	public Integer modifyPlatPayType(Map<String,Object> map);
	
	/**
	 * 查询该支付方式所有店铺列表
	 * @param pay_type 支付方式
	 * @param shop_message 店铺名称/店铺编号
	 * @return
	 */
	public List<Map<String,Object>> queryPayTypeShopList(Map<String,Object> map);
	
	/**
	 * 查询该支付方式所有店铺列表总条数
	 * @param pay_type 支付方式
	 * @param shop_message 店铺名称/店铺编号
	 * @return
	 */
	public Integer queryPayTypeShopListCount(Map<String,Object> map);
	
	/**
	 * 添加店铺支付方式
	 * @return
	 */
	public Integer addShopPayType(Map<String,Object> map);
	
	/**
	 * 获取店铺支付方式详情
	 * @param shop_pay_type_id
	 * @return
	 */
	public Map<String ,Object> queryShopPayType(String shop_pay_type_id);
	
	/**
	 * 验证是否已添加该支付信息，防止重复添加
	 * @param map
	 * @return
	 */
	public Integer queryPayTypeHave(Map<String,Object> map);
	
	/**
	 * 添加新的资料信息
	 * @param shopPayMsg
	 * @return
	 */
	public Integer addNewShopPayMsg(ShopPayMsg shopPayMsg);
	/**
	 * 更新资料信息
	 * @param shopPayMsg
	 * @return
	 */
	public Integer modifyShopPayMsg(ShopPayMsg shopPayMsg);
	
	/**
	 * 获取店铺当前提交的信息资料
	 * @param shopPayMsg
	 * @return
	 */
	public ShopPayMsg getShopPayMsg(ShopPayMsg shopPayMsg);

	/**
	 * 设置子账户
	 * @param params
	 */

	void setSubAccountByShopUnique(SetSubAccountParams params);

	Map<String,Object> getShopPayMsgBySubAccount(String subAccount);
}
