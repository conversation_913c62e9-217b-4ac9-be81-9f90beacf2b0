package org.haier.shop.dao;

import java.util.List;
import java.util.Map;

public interface TestDao {
	
	public Integer updateShopMsg(Map<String,Object> map);
	
	public List<Map<String,Object>> queryShopsList();
	

	public int updateDict(Map<String,Object> list);
	
	public List<Map<String,Object>> queryGoods();
	
	/**
	 * 测试批量更新
	 * @param list
	 * @return
	 */
	public int piling(List<Integer> list);
	
	public List<Map<String,Object>> queryIdPath(String shop_unique);
	
	public int huifuBarcode(List<Map<String,String>> list);
	
	public List<String> querySameGoods();
	public int deleteSameGoods(String goods_id);
	
	/**
	 * 多果超市供货商信息列表
	 * @return
	 */
	public List<Map<String,Object>> queryGoodsSupplierMsg();
	public Integer updateGoodsMsg(List<Map<String,Object>> list);
}
