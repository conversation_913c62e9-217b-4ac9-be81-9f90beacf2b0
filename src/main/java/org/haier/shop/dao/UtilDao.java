package org.haier.shop.dao;

import java.util.List;
import java.util.Map;

import org.haier.shop.entity.ShopsYN;

public interface UtilDao {

	/**
	 * 查询满足条件的店铺列表
	 * @param map
	 * @return
	 */
	public List<Map<String, Object>> queryShopMacList(Map<String, Object> map);
	/**
	 * 查询满足条件的店铺数量
	 * @param map
	 * @return
	 */
	public Integer queryShopMacCount(Map<String, Object> map);
	public List<Map<String,Object>> queryEquipmentList();
	/**
	 * 获取店铺小程序类型
	 * @param shop_unique
	 * @return
	 */
	public Integer getShopType(String shop_unique);
	
	/**
	 * 每天统计一次上周的销售数据
	 */
	public void clearGoodsSale();
	public void lastWeekGoodsSaleStatistics();
	
	/**
	 * 查询所有长度不为7的商品分类信息
	 * @return
	 */
	public List<String> queryKindUnqualified();
	
	/**
	 * 将所有商品分类长度不为7的商品信息更新为蔬菜
	 * @param list
	 * @return
	 */
	public Integer updateKindUnqualified(List<String> list);
	
	/**
	 * 恢复该店数据
	 * @param shopUnique
	 */
	public void addGoodsGoods(String shopUnique);
	
	public int updateForerign(String shopUnique);
	
	public List<Integer> queryNoUseGoods(Map<String,Object>  map);
	
	public int deleteNoUse(Map<String,Object> map);
	
	/**
	 * 拷贝一份已有店铺数据至新店铺
	 */
	public int copyGoodsToNewShop(Map<String,Object> map);
	
	//删除已有的文件升级信息
	public int deleteUpdateFilesMessage();
	//添加已有的文件信息
	/**
	 * 批量加入分类信息
	 * @param list
	 * @return
	 */
	public  int addUpdateFilesMessage(List<Map<String,Object>> list);

	public  int addUpdateFilesMessageNew(List<Map<String,Object>> list);



	public int updateFilesMessage(Map<String,Object> map);
	/**
	 * 添加新的升级记录
	 * @param map
	 * @return
	 */
	public int addNewCashUpdateRecord(Map<String,Object> map);
	
	/**
	 *  历史版本信息查询
	 * @param map
	 * @return
	 */
	public List<Map<String,Object>> queryHistoricalVersion(Map<String,Object> map);
	
	/**
	 * 查询满足条件的店铺总数量
	 * @param map
	 * @return
	 */
	public int queryShopsCount(Map<String,Object> map);
	
	/**
	 * 查询店铺版本的信息
	 * @param map
	 * @return
	 */
	public List<Map<String,Object>> queryShopsMessage(Map<String,Object> map);
	/**
	 * 查询最后一个上传的版本
	 * @return
	 */
	public String theLastVersion();
	
	/**
	 * 批量设置更新
	 * @param map
	 * @return
	 */
	public Integer modifyShopsVersion(Map<String,Object> map);
	/**
	 * 将当前未更新的升级设置为已过期更新
	 * @return
	 */
	public Integer setVersionNumberOverdue();
	/**
	 * 店铺的最后版本信息
	 * @param map
	 * @return
	 */
	public List<Map<String,Object>> queryLastVersionNumber(Map<String,Object> map);
	
	public int addNewVersionForUpdate(Map<String,Object> map);
	
	/**
	 * 库存商品数量统计
	 */
	public Integer statisticsShopGoodsCount();
	
	//以下接口用于商品规格信息整理
	public List<Map<String,Object>> queryGoodsForeignKeys();
	//设置云库商品的数据
	public Integer sameDictForeignKey(Map<String,Object> map);
	//设置各店铺的商品数据，需后期修改近包含中规格的商品
	public Integer sameGoodsForeignKey(Map<String,Object> map);
	//将超市中只包含某商品中规格的店铺查询出来，用于后期商品信息的设置
	public List<String> queryGoodsContainOne(Map<String,Object> map);
	//将商品信息更新
	public Integer resetContain(Map<String,Object> map);
	
	public Integer resetContainOne(List<Map<String,Object>> lists);
	public Integer resetContainTwo(List<Map<String,Object>> lists);
	
	
	public List<String> queryList();
	public Integer addShopTitle(List<String> list);
	
	/**
	 * 更新商品信息
	 * @param list
	 * @return
	 */
	public Integer modifyGoodsPic(List<Map<String,Object>> list);
	
	/**
	 * 向测试店增加测试商品
	 * @param list
	 * @return
	 */
	public Integer addGoods(List<String>list);
	
	/**
	 * 向测试店增加新商品
	 * @param shopUnique
	 * @return
	 */
	public Integer addNewGoodsAll(String shopUnique);
	
	/**
	 * 获取所有需要添加商品的店铺
	 * @return
	 */
	public List<String> getShopList();
	
	public List<Map<String,Object>> getNullList();
	
	public Integer modifyNullList(List<Map<String,Object>> list);
	
	public Integer deleteListDetail();
	
	/**
	 * 查询需要导入新分类的店铺编号
	 * @return
	 */
	public List<String> queryShopNeedSelfKind();
	/**
	 * 添加自定义分类98001
	 * @param list 
	 * @return
	 */
	public Integer addSelfKind(List<String> list);
	
	/**
	 * 统计昨日店铺百货豆的营收情况
	 * @return
	 */
	public Integer statisticsGoodsBeansMsg(Map<String,Object> map);
	
	/**
	 * 查询满足条件的会员信息
	 * @param map
	 * @return
	 */
	public List<Map<String,Object>> getAllCusMsg(Map<String,Object> map);
	
	/**
	 * 批量修改会员的别名
	 * @param list
	 * @return
	 */
	public Integer updateAllCusAlias(List<Map<String,Object>> list);
	
	public Integer addNewShopYN(List<ShopsYN> list);
}
