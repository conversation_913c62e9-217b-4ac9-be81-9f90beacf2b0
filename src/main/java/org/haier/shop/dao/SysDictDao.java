package org.haier.shop.dao;

import java.util.List;
import java.util.Map;

public interface SysDictDao {

	List<Map<String, Object>> queryDictList(Map<String, Object> params);

	Integer queryDictListPageCount(Map<String, Object> params);

	List<Map<String, Object>> queryParentDictList(Map<String, Object> params);

	void addDict(Map<String, Object> params);

	void deleteDict(Map<String, Object> params);

	Map<String, Object> queryDictById(Map<String, Object> params);

	void updateDict(Map<String, Object> params);
	
 }
