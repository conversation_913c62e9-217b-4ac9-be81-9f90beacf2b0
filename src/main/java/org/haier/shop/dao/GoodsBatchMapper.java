package org.haier.shop.dao;

import org.apache.ibatis.annotations.Param;
import org.haier.shop.entity.GoodsBatch;
import org.haier.shop.params.goodsBatch.GoodsBatchChooseParams;
import org.haier.shop.params.goodsBatch.GoodsBatchExportParams;
import org.haier.shop.params.goodsBatch.GoodsBatchQueryParams;
import org.haier.shop.result.goodsBatch.GoodsBatchVO;

import java.util.List;
import java.util.Set;

/**
* @Description 商品入库批次表
* @ClassName GoodsBatch
* <AUTHOR> 
* @Date 2024-04-28
**/
public interface GoodsBatchMapper {

    int insertBatch(List<GoodsBatch> goodsBatches);

    List<GoodsBatchVO> selectPage(@Param("params") GoodsBatchQueryParams params);

    List<GoodsBatchVO> selectListAll(@Param("params") GoodsBatchExportParams params);

    Long selectPageCount(@Param("params") GoodsBatchQueryParams params);

    List<GoodsBatch> selectAvailableList(@Param("shopUnique") Long shopUnique, @Param("goodsBarcode") String goodsBarcode);

    GoodsBatch selectByBatchUnique(@Param("shopUnique") Long shopUnique, @Param("batchUnique") String batchUnique);

    int updateBatchCount(@Param("goodsBatchUpdateList") List<GoodsBatch> goodsBatchUpdateList);

    List<GoodsBatch> selectBatchSelectList(@Param("params") GoodsBatchChooseParams params);

    List<GoodsBatch> selectList(@Param("shopUnique") Long shopUnique, @Param("batchUniqueList") Set<String> batchUniqueList);

    List<GoodsBatch> selectBySourceUnique(@Param("shopUnique") Long shopUnique, @Param("sourceUnique") String sourceUnique);
}