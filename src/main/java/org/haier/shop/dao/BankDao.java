package org.haier.shop.dao;

import java.util.List;
import java.util.Map;

public interface BankDao {
	
	/**
	 * 查询银行列表
	 * @return
	 */
	public List<Map<String ,Object>> getBankList(Map<String ,Object> params);
	
	/**
	 * 查询银行列表总条数
	 * @return
	 */
	public Integer getBankListCount(Map<String ,Object> params);
	
	/**
	 * 添加银行信息
	 * @return
	 */
	public void addBank(Map<String ,Object> params);
	
	/**
	 * 获取银行信息
	 * @return
	 */
	public Map<String ,Object> getBank(String bank_id);
	
	/**
	 * 修改银行信息
	 * @return
	 */
	public void updateBank(Map<String ,Object> params);
	
	/**
	 * 删除银行信息
	 * @return
	 */
	public void deleteBank(String bank_id);
}
