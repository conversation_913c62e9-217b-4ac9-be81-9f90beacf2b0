package org.haier.shop.dao;

import java.util.List;

import org.haier.shop.entity.ListPrompt;

public interface ListPromptDao {
	/**
	 * 获取全部的有效规则信息
	 * @return
	 */
	public List<ListPrompt> getValidPromptRule(ListPrompt listPrompt);
	
	/**
	 * 将规则设置为失效状态
	 * @param map
	 * @return
	 */
//	public Integer deleteListPrompt(ListPrompt listPrompt);
	
	/**
	 * 添加新的订单数量提示信息
	 * @param listPrompt
	 * @return
	 */
	public Integer addListPrompt(ListPrompt listPrompt);
	
	/**
	 * 修改规则信息
	 * @param listPrompt
	 * @return
	 */
	public Integer updateListPrompt(ListPrompt listPrompt);
	
	/**
	 * 清空现有规则信息
	 * @return
	 */
	public Integer clearListPrompt();
}
