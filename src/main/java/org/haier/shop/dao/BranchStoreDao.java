package org.haier.shop.dao;

import java.util.List;
import java.util.Map;

public interface BranchStoreDao {
	
	/**
	 * 总店查询分店列表
	 * @param company_code
	 * @param shop_name 
	 * @param startNum
	 * @param pageSize
	 * @return
	 */
	public List<Map<String ,Object>> getBranchStoreList(Map<String ,Object> params);
	
	/**
	 * 总店查询分店列表总条数
	 * @param company_code
	 * @param shop_name 
	 * @return
	 */
	public Integer getBranchStoreListCount(Map<String ,Object> params);
	
	/**
	 * 查询分店详情
	 * @param shop_unique 店铺唯一编号
	 * @return
	 */
	public Map<String ,Object> getBranchStore(String shop_unique);
	
	/**
	 * 删除分店信息，逻辑删除
	 * @param shop_unique 店铺唯一编号
	 * @return
	 */
	public void deleteBranchStore(String shop_unique);
}
