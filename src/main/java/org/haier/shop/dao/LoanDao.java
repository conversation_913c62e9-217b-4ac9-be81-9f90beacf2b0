package org.haier.shop.dao;

import java.util.List;
import java.util.Map;

/**
 * 贷款相关
 * <AUTHOR>
 *
 */
public interface LoanDao {
	/**
	 * 取消赊销未完成订单
	 * @param map
	 * @return
	 */
	public Integer cancelOrderLoan(Map<String,Object> map);
	/**
	 * 订单生效后，如果有赊销信息，增加店铺的未还赊销额，减少可用额度
	 * @param map
	 * @return
	 */
	public Integer addSXNotReturnMoney(Map<String,Object> map);
	/**
	 * 查询订单中赊销信息
	 * @return
	 */
	public Map<String,Object> querySXOrderMsg(String main_order_no);
	/**
	 * 订单支付成功后，将对应的赊销信息修改为有效状态
	 * @param main_order_no
	 * @return
	 */
	public Integer makeEffectiveSXOrder(String main_order_no);
	/**
	 * 添加分期信息
	 * @param list
	 * @return
	 */
	public Integer addNewFenQiLog(List<Map<String,Object>> list);
	/**
	 * 订单支付完成后，修改赊销订单状态
	 */
	public Integer modifyOrderLoan(Map<String,Object> map);
	/**
	 * 订单支付完成后，查询是否有赊销订单信息
	 * @param orderNo
	 * @return
	 */
	public Map<String,Object> queryOrderLoanMsg(String orderNo);
	/**
	 * 查询店铺数量
	 * @param map
	 * @return
	 */
	public Integer queryShopLoanListCount(Map<String,Object> map);
	/**
	 * 查询店铺贷款列表	
	 * @param map
	 * @return
	 */
	public List<Map<String,Object>> queryShopLoanList(Map<String,Object> map);
	/**
	 * 查询借款规则信息
	 * @param sxRuleId
	 * @return
	 */
	public List<Map<String,Object>> querySxRuleMsg(Integer sxRuleId);
	/**
	 * 添加新的借款详情信息
	 * @param list
	 * @return
	 */
	public Integer addNewLoanDetail(List<Map<String,Object>> list);
	/**
	 * 添加新的借款信息
	 * @param map
	 * @return
	 */
	public Integer addNewLoanOrder(Map<String,Object> map);
	/**
	 * 更新店铺审核信息
	 * @param map
	 * @return
	 */
	public Integer updateShopLoanMsg(Map<String,Object> map);
	/**
	 * 查询店铺的审核状态
	 * @param map
	 * @return
	 */
	public Map<String,Object> queryShopLoanDetail(Map<String,Object> map);
	/**
	 * 查询待审核的店铺数量
	 * @return
	 */
	public Integer queryLoanShopCount();
	/**
	 * 查询借款推荐规则
	 * @return
	 */
	public Map<String,Object> queryLoanPolicy();
	/**
	 * 查询待审核的店铺的推荐信息
	 * @param map
	 * @return
	 */
	public List<Map<String,Object>> queryLoanShopList(Map<String,Object> map);
	
	public int updateRules (Map<String,Object> map);
	
	public int updateRules2 (Map<String,Object> map);
	
	public List<Map<String,Object>> queryRuleList();
	
	public List<Map<String,Object>> queryWindControl(Map<String,Object> map);
	
	public int queryWindControlCount(Map<String,Object> map);
	
	public List<Map<String,Object>> queryWindControlDetail(Map<String,Object> map);
	
	public int queryWindControlDetailCount(Map<String,Object> map);
	
	public List<Map<String,Object>> queryWindControlDetail2(Map<String,Object> map);
	
	public int queryWindControlDetail2Count(Map<String,Object> map);
	
	public List<Map<String,Object>> queryWindControlDetail3(Map<String,Object> map);
	
	public int queryWindControlDetail3Count(Map<String,Object> map);
	
	public List<Map<String,Object>> queryLoanOrderList(Map<String,Object> map);
	
	public int queryLoanOrderCount(Map<String,Object> map);
	
	
	
	
}
