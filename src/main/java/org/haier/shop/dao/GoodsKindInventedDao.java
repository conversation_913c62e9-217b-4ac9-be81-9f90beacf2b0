package org.haier.shop.dao;

import java.util.List;
import java.util.Map;

/**
* @author: 作者:王恩龙
* @version: 2023年3月29日 下午5:24:16
*
*/
public interface GoodsKindInventedDao {
	
	/**
	 * 查询该虚拟分类下商品数量
	 * @param map
	 * @return
	 */
	public Integer queryGoodsKindInventedGoodsCount(Map<String,Object> map);
	/**
	 * 查询虚拟分类详情
	 * @param goods_kind_invented_id
	 * @return
	 */
	public Map<String,Object> queryGoodsInventedDetail(String goods_kind_invented_id);
	/**
	 * 删除一个虚拟分类后，将后面的虚拟分类排序减一
	 * @param map
	 * @return
	 */
	public Integer subGoodsKindSort(Map<String,Object> map);
	/**
	 * 获取下一次新增虚拟分类的排序
	 * @param shop_unique
	 * @return
	 */
	public Integer queryMaxShopSort(String shop_unique);
	/**
	 * 批量修改虚拟分类的排序
	 * @param list
	 * @return
	 */
	public Integer modifyGoodsKindInventedSort(List<Map<String,Object>> list);
	/**
	 * 查询虚拟分类中不包含的商品信息
	 * @param map
	 * @return
	 */
	public List<Map<String,Object>> queryGoodsList(Map<String,Object> map);
	/**
	 * 
	 * @param map
	 * @return
	 */
	public Integer queryGoodsCount(Map<String,Object> map);
	/**
	 * 查询上一个虚拟分类编号
	 * @param map
	 * @return
	 */
	public Integer queryMaxGoodsKindUnique(Map<String,Object> map);
	/**
	 * 添加新的虚拟商品分类信息
	 * @param map
	 */
	public void addNewGoodsKindInventedMsg(Map<String,Object> map);
	/**
	 * 更新当前虚拟分类信息
	 * @param map
	 */
	public void updateGoodsKindInventedMsg(Map<String,Object> map);
	
	/**
	 * 给当前虚拟分类添加商品信息
	 * @param map
	 */
	public void addGoodsKindInventedGoodsList(Map<String,Object> map);
	/**
	 * 删除当前虚拟分类的商品信息
	 * @param map
	 */
	public void deleteGoodsKindInventedGoods(Map<String,Object> map);
	
	/**
	 * 查询虚拟分类下的商品列表
	 * @param map
	 * @return
	 */
	public List<Map<String,Object>> queryGoodsKindInventedGoodsList(Map<String,Object> map);
	/**
	 * 查询店铺虚拟分类信息
	 * @param map
	 * @return
	 */
	public List<Map<String,Object>> queryGoodsKindInventedList(Map<String,Object> map);
	/**
	 * 查询
	 * @param map
	 * @return
	 */
	public Integer queryGoodsKindInventedCount(Map<String,Object> map);
}
