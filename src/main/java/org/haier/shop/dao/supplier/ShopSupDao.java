package org.haier.shop.dao.supplier;

import org.haier.shop.entity.supplier.ShopSupGoodsEntity;

import java.util.List;
import java.util.Map;

public interface ShopSupDao {

	List<Map<String,Object>> querySupplierList(Map<String, Object> params);

    Map<String,Object> querySupGoodByBarcode(Map<String, Object> params);
    Map<String,Object>queryGoodByBarcode(Map<String, Object> params);
    int addSupGood(Map<String, Object> params);
    int updateSupplier(Map<String, Object> params);
    Map<String,Object> queryShopSupSupplierExamineEntity(Map<String, Object> params);
    /**
     * 根据店铺编号、供货商编号、商品条码查询商品信息
     * @param map
     * @return
     */
    ShopSupGoodsEntity queryShopSupGoodsByGoodsBarcode(Map<String,Object> map);
 }
