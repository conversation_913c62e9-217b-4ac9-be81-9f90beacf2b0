package org.haier.shop.dao;

import java.util.List;
import java.util.Map;

import org.apache.ibatis.annotations.Param;
import org.haier.shop.entity.beans.PageData;
import org.haier.shop.entity.globalSelect.FarmDetailVO;
import org.haier.shop.entity.globalSelect.GlobalDetailVO;

public interface GlobalThemeDao {
	
	public Integer addNewSaleList(Map<String,Object> map);
	public Integer addNewSaleListDetail(List<Map<String,Object>> list);
	public Integer addFarmProductShelfPriceList(Map<String,Object> map);
	/**
	 * 批量添加店铺的全球精选上架信息
	 * @param map
	 * @return
	 */
	public Integer addFarmProductShelfList(Map<String,Object> map);
	public Integer addProductStatistics(String id);
	public List<Map<String ,Object>> queryGlobalSupplierList(Map<String,Object> params);

	public int queryGlobalSupplierCount(Map<String,Object> params);
	
	public int updateGlobalThemeStatus(Map<String,Object> params);
	
	public int insertThemeAudit(Map<String,Object> params);
	
	public List<Map<String ,Object>> queryGlobalGoodShelfList(Map<String,Object> params);
	
	public int queryGlobalGoodShelfCount(Map<String,Object> params);
	
	public int insertGlobalShelf(Map<String,Object> params);
	
	public int updateGoodShelfStatus(Map<String,Object> params);
	
	public List<Map<String ,Object>> getGlobalOrderList(Map<String,Object> params);

	public int getGlobalOrderListCount(Map<String,Object> params);
	
	public int insertExpress(Map<String,Object> params);
	
	public int updateGlobalSubList(Map<String,Object> params);
	
	public List<Map<String ,Object>> getGlobalOrderDetail(Map<String,Object> params);
	
	public List<Map<String ,Object>> getProvinceList(List<Map<String ,Object>> list);//获取有商家的省份列表
	public List<Map<String ,Object>> getCityList(List<Map<String ,Object>> list);//获取有商家的城市列表
	public List<Map<String ,Object>> getAreaList(Map<String,Object> map);//获取有商家的区域列表	
	public List<Map<String ,Object>> getFarmAreaList(Map<String,Object> map);//获取有商家的区域列表
	
	public List<Map<String ,Object>> getFarmAuditList(Map<String,Object> map);//农产品审核记录
	
	public int addGlobalTheme(Map<String,Object> params);//添加精选
	public int addGlobalThemeArea(List<PageData> list);//添加精选区域
	public int addGlobalThemeGoodDetail(List<PageData> list);//添加精选商品
	
	public GlobalDetailVO queryGlobalThemeDetail(Map<String,Object> map);//精选详情
	
	public List<Map<String ,Object>> queryPTGGShelfList(Map<String,Object> params);
	
	public int queryPTGGShelfCount(Map<String,Object> params);
	
	public List<Map<String ,Object>> queryPTGGShelf2List(Map<String,Object> params);//从未上架的店铺
	public int addGoodShelfList(List<Map<String ,Object>> list);//全部上架
	public int updateGoodShelf(Map<String,Object> params);//全部下架
	
	public int updateGoodShelfCompulsory(Map<String,Object> params);//强制上架
	
	public List<Map<String ,Object>> queryGlobalSecretaryList(Map<String,Object> params);

	public int queryGlobalSecretaryCount(Map<String,Object> params);
	
	public int addSecretary(Map<String,Object> params);
	
	public int deleteGlobalSecretary(Map<String,Object> params);
	
	public Map<String ,Object> querySecretary(String map);
	
	public int updateSecretary(Map<String,Object> params);
	
	public List<Map<String ,Object>> queryArea(Map<String,Object> params);
	
	public List<Map<String ,Object>> queryProductsList(Map<String,Object> params);

	public int queryProductsCount(Map<String,Object> params);
	
	public int queryShop(Map<String,Object> params);
	
	public int addProduct(Map<String,Object> params);
	
	public int addProductSpec(List<PageData> list);
	
	public int addProductPeople(List<PageData> list);
	
	public int addProductArea(List<PageData> list);
	
	public List<Map<String, Object>> querySecretaryList();
	
	public FarmDetailVO queryFarmDetail(Map<String,Object> params);
	
	public int updateFarmShopStatus(Map<String,Object> params);
	
	public int updateFarmStatus(Map<String,Object> params);
	
	public int updateAllFarmShopStatus(@Param("data")Map<String,Object> params,@Param("data2")List<PageData> goodsList);
	
	public int updateAllFarmStatus(@Param("data")Map<String,Object> params,@Param("data2")List<PageData> goodsList);

	public List<Map<String, Object>> queryGoodsKind(Map<String,Object> map);
	
	public int updateProduct(Map<String,Object> params);
	
	public int delProductPeople(Map<String,Object> params);
	
	public int delProductSpec(Map<String,Object> params);
	
	public int delProductArea(Map<String,Object> params);
	
	public List<PageData> quertPeolpleList(Map<String,Object> params);
	public void addProductSpec_sup(List<PageData> goodsList_sup);
	public void delProductSpec_sup(Map<String, Object> map);
	public Map<String, Object> queryPlatformAddMoney();
	public void updateAddMoney(Map<String, Object> params);
	public void updateProductSpecAddMoney(Map<String, Object> params);
	public void updateProductSpecSupAddMoney(Map<String, Object> params);
	public List<Map<String, Object>> getGoodListYN(Map<String, Object> params);
	public Integer getGoodListYNCount(Map<String, Object> params);
	public int queryShoppingCartYNCount(Map<String, Object> params);
	public List<Map<String, Object>> querySupplierListYN(Map<String, Object> params);
	public void insertShoppingCartYN(Map<String, Object> params);
	public List<Map<String, Object>> querySupplierByCartYN(Map<String, Object> params);
	public List<Map<String, Object>> querySupplierGoodListNewYN(@Param("company_code")String company_code,@Param("area_dict_num")String area_dict_num,@Param("shop_unique")String shop_unique);
	public void delete_shopping_cart(Map<String, Object> params);
	public void delete_shopping_cart_more(String[] id);
	public void update_shopping_cart(Map<String, Object> params);
	public List<Map<String, Object>> querySupplierByCartByIdsYN(@Param("ids")String[] ids, @Param("shop_unique")String shop_unique);
	public List<Map<String, Object>> querySupplierGoodListNewYNByIds(@Param("company_code")String company_code,@Param("area_dict_num")String area_dict_num,@Param("ids")String[] ids,@Param("shop_unique") String shop_unique);
	public void insertOrder(Map<String, Object> paramsOrder);
	public void insertOrderDetail(Map<String, Object> paramsOrderdetail);
	public void insertOrderMain(Map<String, Object> paramsShop);
	public void updateShoppingOrderYN(Map<String, Object> params);
	public Map<String, Object> queryYNOrderStatus(Map<String, Object> params);
	public List<Map<String, Object>> getMyOrderList(Map<String, Object> params);
	public Integer getMyOrderListCount(Map<String, Object> params);
	public void sendYNOrder(Map<String, Object> params);
	public void confirmYNOrder(Map<String, Object> params);
	public List<Map<String, Object>> queryOrderDetail(Map<String, Object> params);
	public List<Map<String, Object>> queryOrderDetailGoodList(Map<String, Object> map);
	public Map<String, Object> querySupOrderMain(Map<String, Object> params);
	public Map<String, Object> querySupAddMoney(Map<String, Object> params);
	public void addShopBalance(Map<String, Object> order);
	public Map<String, Object> queryOrderInfo(Map<String, Object> params);
	
	
}
