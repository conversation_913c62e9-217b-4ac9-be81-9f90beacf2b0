package org.haier.shop.dao;

import java.util.List;
import java.util.Map;

public interface ShopCouponCashCountDao {

	public Map<String, Object> queryAllShopBalanceCount(Map<String, Object> map);

	public Map<String, Object> queryAllShopAlreadyCashCount(Map<String, Object> map);

	public Map<String, Object> queryAllShopWaitCashCount(Map<String, Object> map);

	public Map<String, Object> queryShopOrderInfo(Map<String, Object> map);

	public Map<String, Object> queryShopOrderPlatformCouponInfo(Map<String, Object> map);

	public Map<String, Object> queryShopOrderShopCouponInfo(Map<String, Object> map);

	public Map<String, Object> queryShopOrderCountInfo(Map<String, Object> map);

	public Map<String, Object> queryShopBeansCountInfo(Map<String, Object> map);

	public Map<String, Object> queryShopPointCountInfo(Map<String, Object> map);
	
	public Map<String, Object> queryShopOutSend(Map<String, Object> map);

	public Map<String, Object> queryShopDeliveryCountInfo(Map<String, Object> map);

	public Map<String, Object> queryPlatformCouponOrderCountInfo(Map<String, Object> map);

	public Map<String, Object> queryShopCouponOrderCountInfo(Map<String, Object> map);

	public Map<String, Object> queryShopCashLogCount(Map<String, Object> map);

	public List<Map<String, Object>> queryShopCashLog(Map<String, Object> map);

	public Map<String, Object> queryOnlineOrderListCount(Map<String, Object> map);

	public List<Map<String, Object>> queryOnlineOrderList(Map<String, Object> map);


	
}
