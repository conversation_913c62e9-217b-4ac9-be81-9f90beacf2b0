package org.haier.shop.dao;

import java.util.List;
import java.util.Map;

public interface CountMsgNingyuDao {


	List<Map<String, Object>> queryNYsale(Map<String, Object> params);
	
	List<Map<String, Object>> queryNYsaleArea(Map<String, Object> params);
	
	Map<String,Object> queryNYsaleCount(Map<String, Object> params);
	
	Map<String,Object> queryNYsaleAreaCount(Map<String, Object> params);
	
	List<Map<String, Object>> queryNYBar(Map<String, Object> params);
	
	List<Map<String, Object>> queryNYBarArea(Map<String, Object> params);
	
	List<Map<String, Object>> querykindBar(Map<String, Object> params);
	
	

	

}
