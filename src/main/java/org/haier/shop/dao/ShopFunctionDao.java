package org.haier.shop.dao;

import java.util.Map;

/**
 * 店铺功能相关SQL
 * <AUTHOR>
 */
public interface ShopFunctionDao {
	/**
	 * 新店铺添加时，自动添加相关功能模块
	 * @param shop_unique
	 * @return
	 */
	public int addNewShopFunction(String shop_unique);
	/**
	 * 主界面更换店铺后，查询店铺相关功能设置
	 * @param map
	 * @return
	 */
	public Map<String,Object> queryShopFunction(Map<String,Object> map);
	
	/**
	 * 添加新店铺时，添加店铺配置信息，二维码信息及免密配置相关信息
	 * @param map
	 * @return
	 */
	public Integer addNewShopsConfig(Map<String,Object> map);
	
}
