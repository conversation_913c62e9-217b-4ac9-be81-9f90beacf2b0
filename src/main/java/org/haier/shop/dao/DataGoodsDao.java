package org.haier.shop.dao;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

public interface DataGoodsDao {

	
	public List<Map<String, Object>> queryDataGoodsHotMap();

	public Integer queryDataGoodCount();

	public List<Map<String, Object>> queryDataGoodsTopByCount();

	public Map<String, Object> queryDataGoodsSaleGoodsMax();

	public Map<String, Object> queryDataGoodsLiRunMax();

	public Integer queryGroupByAge(HashMap<String, Object> params);

	public Double queryBuyMoneyGroupByAge(HashMap<String, Object> params);

	public List<Map<String, Object>> queryDataGoodsTopByCity();

	public List<Map<String, Object>> querySaleMoneyTopByCity();
	
	public List<Map<String,Object>> queryBuyMoneyGroupByAgeNum();
	
	public List<Map<String,Object>> queryGroupByAgeCount();
}
