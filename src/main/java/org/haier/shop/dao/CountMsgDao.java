package org.haier.shop.dao;

import java.util.List;
import java.util.Map;

public interface CountMsgDao {

	public Map<String,Object> queryCountStatisticsSameGoods(Map<String, Object> map);
	/**
	 * 统计退款
	 * @param map
	 * @return
	 */
	public Map<String,Object> queryCountStatisticsReturn(Map<String, Object> map);
	/**
	 * 查询统计信息
	 * @param map
	 * @return
	 */
	public Map<String,Object> queryCountStatisticsSameTime(Map<String, Object> map);
	
	public Map<String, Object> queryTurnover(Map<String, Object> map);

	public Map<String, Object> queryOrderCount(Map<String, Object> map);

	public Map<String, Object> queryMoneyAlready(Map<String, Object> map);

	public Map<String, Object> queryMoneyNotAlready(Map<String, Object> map);

	public List<Map<String, Object>> queryKindSaleProportion(Map<String, Object> map);

	public List<Map<String, Object>> querySaleTrend(Map<String, Object> map);

	public List<Map<String, Object>> queryShopPurchaseTo5(Map<String, Object> map);

	public List<Map<String, Object>> queryGoodsSaleTop5(Map<String, Object> map);

	public List<Map<String, Object>> querySaleTrendToday(Map<String, Object> map);

	public List<Map<String, Object>> queryShopPurchase(Map<String, Object> map);

	public Map<String, Object> queryGoodsSaleAll(Map<String, Object> map);

	public List<Map<String, Object>> querySalesmanTop5(Map<String, Object> map);

	public List<Map<String, Object>> querySalesmanAll(Map<String, Object> map);

	public Map<String, Object> queryGrossProfit(Map<String, Object> map);

	public Map<String, Object> queryUnitPrice(Map<String, Object> map);

	public List<Map<String, Object>> queryPayProportion(Map<String, Object> map);

	public List<Map<String, Object>> orderTotalByHours(Map<String, Object> map);

	public Map<String,Object> queryShopStatisticsMsgPage(Map<String,Object> map);
	
	public Map<String,Object> queryShopStatisticsMsgPagePoints(Map<String,Object> map);
	
	public List<Map<String, Object>> queryBeansAccountByDays(Map<String, Object> map);
	
	public List<Map<String,Object>> pointsStatisticsByDay(Map<String,Object> map);
	
	public Map<String,Object> queryCouponPublicStatistics(Map<String,Object> map);
	
	public List<Map<String,Object>> queryPubCouponMsg(Map<String,Object> map);

	public Map<String,Object> ourShopCouponMsg(Map<String,Object> map);
	
	public List<Map<String,Object>> ourCouponUseMsgByDay(Map<String,Object> map);

	public List<Map<String,Object>> accountStatistics(Map<String,Object> map);
	
	public List<Map<String,Object>> queryTakeCashList(Map<String,Object> map);
	
	public List<Map<String,Object>> queryOrderListNetWork(Map<String,Object> map);
	
	public Integer queryOrderListNetWorkPages(Map<String,Object> map);

}
