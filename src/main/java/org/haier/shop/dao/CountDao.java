package org.haier.shop.dao;

import java.util.List;
import java.util.Map;

public interface CountDao {
	/**
	 * 各时间段内注册用户数量 
	 * @param map
	 * @return
	 */
	public Map<String,Object> cusCountByTime(Map<String,Object> map);
	/**
	 * 各种机型用户数据量统计
	 * @param map
	 * @return
	 */
	public List<Map<String,Object>> cusCountByPhone(Map<String,Object> map);
	
	/**
	 *  各时间段内活跃用户数量(所有地区用户)
	 * @param map
	 * @return
	 */
	public Map<String,Object> cusCountByActive(Map<String,Object> map);
	/**
	 * 各时间段内活跃用户数量(按区域)
	 * @param map
	 * @return
	 */
	public List<Map<String,Object>> cusCountByActArea(Map<String,Object> map);
	/**
	 * 阶段内客户购买力(分区县,分时间)
	 * @param map
	 * @return
	 */
	public List<Map<String,Object>> cusPurPowerByArea(Map<String,Object> map);
	/**
	 * 所有地区客户购买能力
	 * @param map
	 * @return
	 */
	public Map<String,Object> cusPurPower(Map<String,Object> map);
	/**
	 * 各地区商户注册量
	 * @param map
	 * @return
	 */
	public List<Map<String,Object>> shopCountByArea(Map<String,Object> map);
	/**
	 * 各地区的订单量，订单金额统计，商品销售数量统计
	 * @param map
	 * @return
	 */
	public List<Map<String,Object>> statisByArea(Map<String,Object> map);
}
