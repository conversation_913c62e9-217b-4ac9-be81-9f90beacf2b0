package org.haier.shop.dao;

import java.util.List;
import java.util.Map;

public interface InventoryDao {
	/**
	 * 盘库记录查询
	 * @param map
	 * @return
	 */
	public List<Map<String,Object>> queryInventoryRecord(Map<String,Object> map);
	
	/**
	 * 盘库记录页数查询
	 * @param map
	 * @return
	 */
	public Integer queryInventoryRecordPages(Map<String,Object> map);
	/**
	 * 盘点详情查询
	 * @param map
	 * @return
	 */
	public Map<String,Object> queryInventoryDetail(Map<String,Object> map);
	
	public List<Map<String,Object>> queryInventoryGoodList(Map<String,Object> map);
}
