package org.haier.shop.dao;

import java.util.List;
import java.util.Map;

import org.haier.shop.entity.globalSelect.FarmKind;
import org.haier.shop.util.weishi.ProductInfo;

public interface WeishiDao{

	public Integer modifyFarmMsg(FarmKind farmKind);
	
	public Map<String,Object> queryWeishiProduct(Map<String,Object> data);

	public List<Map<String ,Object>> queryProductsList(Map<String,Object> params);

	public int queryProductsCount(Map<String,Object> params);
	
	public List<ProductInfo> queryWeishiProductInfo(Map<String,Object> data);
	
	public int addOrderDelivery(Map<String,Object> params);
	
	public int updateOrder(Map<String,Object> params);
	
	public int updateSubOrder(Map<String,Object> params);
}
