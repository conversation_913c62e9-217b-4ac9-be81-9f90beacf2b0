package org.haier.shop.dao;

import java.util.List;
import java.util.Map;

import org.haier.shop.entity.Binding;

/**
 * 商品
 * <AUTHOR>
 */
public interface GoodsBindingDao {
	
	/**
	 * 查询店铺所有捆绑商品的信息
	 */
	public List<Binding> queryShopsBinding(Map<String,Object> map);
	
	/**
	 * 查询店铺所有捆绑商品总条数
	 */
	public Integer queryShopsBindingCount(Map<String,Object> map);
	
	/**
	 * 商品捆绑信息删除
	 * @param map
	 * @return
	 */
	public int deleteBindingGoods(Map<String,Object> map);
	/**
	 * 修改商品捆绑信息使用状态
	 * @param map
	 * @return
	 */
	public int modifyBinding(Map<String,Object> map);
	
	/**
	 * 添加新的商品捆绑关系
	 * @param list
	 * @return
	 */
	public int newBindingGoods(List<Map<String,Object>> list);
}
