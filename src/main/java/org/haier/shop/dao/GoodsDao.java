package org.haier.shop.dao;

import java.util.List;
import java.util.Map;
import java.util.Set;

import org.apache.ibatis.annotations.Param;
import org.haier.ele.entity.Goods;
import org.haier.shop.entity.BaseGoods;
//import org.haier.shop.entity.GoodsSupplier;
import org.haier.shop.entity.CloudGoodsMain;
import org.haier.shop.entity.GoodsEntity;
import org.haier.shop.entity.GoodsInfo;
import org.haier.shop.entity.goodsRecord.RecordGoods;
import org.haier.shop.params.goods.InStockGoodsData;

public interface GoodsDao {

	/**
	 * 查询商品的基本信息
	 * @param map
	 * @return
	 */
	public Map<String,Object> selectBaseGoodsMsg(Map<String,Object> map);
	/**
	 * 1、新增或更新商品换算比例信息，update goods_assist
	 * @param map
	 * @return
	 */
	public Integer updateGoodsContain(Map<String,Object> map);
	public Integer updateGoodsKind(Map<String,Object> map);
	/**
	 * 查询分类信息
	 * @param map
	 * @return
	 */
	public List<Map<String,Object>> queryShopKinds(Map<String,Object> map);
	/**
	 * 查询商品基本信息
	 * @param map
	 * @return
	 */
	public List<Map<String,Object>> queryGoodsKindMsg(Map<String,Object> map);
	
	public List<String> queryGoodsBaseRepeat(Map<String,Object> map);
	/**
	 * 查询商品的基本库存信息
	 * @param map
	 * @return
	 */
	public List<Map<String,Object>> queryGoodsBaseMsgForStock(Map<String,Object> map);
	
	/**
	 * 导入商品价格，名称信息
	 * @param map
	 * @return
	 */
	public Integer importGoodsMsg(Map<String,Object> map);
	/**
	 * 修改商品的库存，价格信息
	 * @param map
	 * @return
	 */
	public Integer importGoodsBaseMsg(Map<String,Object> map);
	/**
	 * 查询商品基础信息列表
	 * @param map
	 * @return
	 */
	public List<Map<String,Object>> queryGoodsBaseMsg(Map<String,Object> map);
	/**
	 * 查询店铺默认分类信息
	 * @param shop_unique
	 * @return
	 */
	public String queryFenLeiLimit(String shop_unique);
	/**
	 * 从大库中查询的商品数量
	 * @param map
	 * @return
	 */
	public Integer queryBaseGoodsCount(Map<String,Object> map);
	/**
	 * 从大库读取用户搜索的商品信息
	 * @param map
	 * @return
	 */
	public List<Map<String,Object>> queryBaseGoods(Map<String,Object> map);
	
	/**
	 * 删除现有商品
	 * @param map
	 * @return
	 */
	public Integer deleteGoodsIndex(Map<String,Object> map);
	/**
	 * 添加新的商品
	 * @param map
	 * @return
	 */
	public Integer addNewGoodsIndex(Map<String,Object> map);
	/**
	 * 
	 * @param map
	 * @return
	 */
	public List<Map<String,Object>> queryGoodsIndexSort(Map<String,Object> map);
	/**
	 * 修改店铺油枪和油品的对应关系
	 * @param map
	 * @return
	 */
	public Integer modifyOilGoodsRelation(Map<String,Object> map);
	/**
	 * 添加新的油品关系
	 * @param map
	 * @return
	 */
	public Integer addNewOilRelation(Map<String,Object> map);
	/**
	 * 
	 * @param map
	 * @return
	 */
	public List<Map<String,Object>> queryUseAbleOilList(Map<String,Object> map);
	/**
	 * 查询当前店铺
	 * @param map
	 * @return
	 */
	public Integer queryOilGoodsCount(Map<String,Object> map);
	/**
	 * 查询油品列表
	 * @return
	 */
	public List<Map<String,Object>> queryOilGoodsList(Map<String,Object> map);
	
	public Integer modifyGoodsMonitor(Map<String,Object> map);
	/**
	 * 获取商品摄像头列表
	 * @return
	 */
	public List<Map<String,Object>> queryMonitorList(Map<String,Object> map);
	/**
	 * 批量更新商品图片
	 * @param map
	 * @return
	 */
	public Integer modifyGoodsPicList(Map<String,Object> map);
	/**
	 * 查询商品的线上起定量等配置信息
	 * @param map
	 * @return
	 */
	public Map<String,Object> queryGoodsOnlineSaleMsg(Map<String,Object> map);
	/**
	 * 更新商品的线上销售限制信息
	 * @param map
	 * @return
	 */
	public Integer modifyGoodsOnlineSetting(Map<String,Object> map);
	
	/**
	 * 添加商品的线上销售限制信息
	 * @param map
	 * @return
	 */
	public Integer addNewGoodsOnlineSetting(Map<String,Object> map);
	/**
	 * 批量修改商品默认供货商信息
	 * @param map
	 * @return
	 */
	public Integer modifyGoodsSupplier(Map<String,Object> map);
	/**
	 * 商品查询
	 * @param map
	 * @return
	 */
	public List<Map<String,Object>> getGoodList(Map<String,Object> map);
	
	public Integer getGoodListCount(Map<String,Object> map);
	
	/**
	 * 价签打印商品查询
	 * @param map
	 * @return
	 */
	public List<Map<String,Object>> getGoodPrintList(Map<String,Object> map);
	
	public Integer getGoodPrintListCount(Map<String,Object> map);
	
	/**
	 * 查询商品详情
	 * @param map
	 * @return
	 */
	public Map<String,Object> queryGoodsDetail(Map<String,Object> map);
	/**
	 * 更新商品信息！
	 * @param map
	 * @return
	 */
	
	public Integer updateGoodsMessage(Map<String,Object> map);
	/**
	 * 商品供应商查询
	 * @param map
	 * @return
	 */
	public  List<Map<String,Object>> queryGoodsSupplier(Map<String,Object> map);
	
	
	/**
	 * 
	 * 添加新商品
	 * @param map
	 * @return
	 */
	public int addNewGoods(Map<String,Object> map);
	/**
	 * 测试
	 * @param map
	 * @return
	 */
	public List<Map<String,Object>> goodsTest(Map<String,Object> map);
	public List<Map<String,Object>> goodsTest1(Map<String,Object> map);
	/**
	 * 商品查询数量
	 * @param map
	 * @return
	 * 
	 */
	public int  queryGoodsCount(Map<String,Object> map);
	
	/**
	 * 一下用于清空重复内容
	 * @return
	 */
	public List<String> testSame();
	public int deleteSame(List<String> list);
	public List<Map<String,Object>> selectName(Map<String,Object> map);
	public List<Map<String,Object>> selectGoods(Map<String,Object> map);
	
	/**
	 * 用于饼状图制作，查询各状况的商品数量
	 * @param map
	 * @return
	 */
	public List<Map<String,Object>> goodsCount(Map<String,Object> map);
	
	/**
	 * 用于饼状图制作，查询各状况商品的库存金额
	 * @param map
	 * @return
	 */
	public List<Map<String,Object>> goodsAmount(Map<String,Object> map);
	/**
	 * 商品查询总页数查询
	 * @param map
	 * @return
	 */
	public Integer queryGoodsPages(Map<String,Object> map);
	/**
	 * 商品信息查询（添加商品捆绑关系）
	 * @param map
	 * @return
	 */
	public List<Map<String,Object>> bindGoodsAddSearch(Map<String,Object> map);
	
	/**
	 * 促销商品总数量查询
	 * @param map
	 * @return
	 */
	public int promotionGoodsPages(Map<String,Object> map);
	/**
	 * 商品信息查询（促销活动）
	 * @param map
	 * @return
	 */
	public List<Map<String,Object>> promotionGoodsSearchByPage(Map<String,Object> map);
	/**
	 * 取消促销
	 * @param map
	 * @return
	 */
	public int cancelPromotion(Map<String,Object> map);
	/**
	 * 商品详情查询
	 * @param map
	 * @return
	 */
	public BaseGoods goodsDetail(Map<String,Object> map);
	/**
	 * 商品供货商供应商品信息查询
	 * @param map
	 * @return
	 */
	public List<Map<String,Object>> querySupplierGoods(Map<String,Object> map);
	
	/**
	 * 添加商品信息
	 * @param map
	 * @return
	 */
	public int newGoodsMessage(Map<String,Object> map);
	
	/**
	 * 更新所有店铺商品的foreign_key
	 * @param list
	 * @return
	 */
	public int updateGoodsForeignKey(Map<String,Object> map);
	/**
	 * 更新已有商品信息
	 * @param map
	 * @return
	 */
	public int toUpdateGoodsMessage(Map<String,Object> map);
	/**
	 * 扫码，输入商品条码后查询商品信息
	 * @param map
	 * @return
	 */
	public BaseGoods queryBaseGoodsMessage(Map<String,Object> map);
	/**
	 * 查询需要自动订货的店铺及店铺的相关设置信息
	 * @return
	 */
	public List<Map<String,Object>> queryShopUniqueNeedAutoPur();
	
	/**
	 * 查询每个店铺中，按照日均销量采购的商品列表
	 * @param map
	 * @return
	 */
	public List<Map<String,Object>> queryGoodsSaleMessage(Map<String,Object> map);
	/**
	 * 查询每个店铺中，按照日均销量采购的商品列表
	 * @param map
	 * @return
	 */
	public List<Map<String,Object>> queryGoodsSaleMessageByCount(Map<String,Object> map);
	
	/**
	 * 批量插入订单信息
	 * @param list
	 * @return
	 */
	public int  addNewAutoPurList(List<Map<String,Object>> list);
	/**
	 * 批量插入订单详情！
	 * @param list
	 * @return
	 */
	public Integer addNewAutoPurDetail(List<Map<String,Object>> list);
	/**
	 * 创建自动采购订单信息后，修改店铺的自动采购时间
	 * @param list
	 * @return
	 */
	public Integer updateLastPurDate(List<Map<String,Object>> list);
	
	/**
	 * 查询今日卖出商品的商品种类数量
	 * @param map
	 * @return
	 */
	public Integer queryPagesForRanking(Map<String,Object> map);
	/**
	 * 商品日销量：分页查询商品销售信息
	 * @param map
	 * @return
	 */
	public List<Map<String,Object>> queryGoodsRanking(Map<String,Object> map);
	/**
	 * 商品日销量：商品销量详情
	 * @param map
	 * @return
	 */
	public List<Map<String,Object>> queryRankingDetail(Map<String,Object> map);
	
	/**
	 * 出入库记录：页数查询
	 * @param map
	 * @return
	 */
	public Integer queryStockRecordPages(Map<String,Object> map);
	
	/**
	 * 商品出入库记录查询
	 */
	public List<Map<String,Object>> queryGoodsRecordByPage(Map<String,Object> map);
	
	public List<GoodsInfo> ExcelGoodsRecord(Map<String,Object> map);
	
	public GoodsInfo ExcelGoodsRecordCount(Map<String,Object> map);
	
	public List<GoodsInfo> ExcelGoodsAlloration(Map<String,Object> map);
	
	public List<GoodsInfo> ExcelGoodsInfo(Map<String,Object> map);
	
	/**
	 * 批量更新商品信息（自动进货时，修改商品进价用）
	 * @param list
	 * @return
	 */
	public int updateGoodsMessageList(List<Map<String,Object>> list);
	
	/**
	 * 商品采购界面商品查询
	 * @param map
	 * @return
	 */
	public List<Map<String,Object>> querySupGoodsForCart(Map<String,Object> map);
	
	/**
	 * 店铺所有商品的商品进货量查询
	 * @param map
	 * @return
	 */
	public List<Map<String,Object>> goodsCostQuery(Map<String,Object> map);
	
	/**
	 * 查询店铺的重复商品信息
	 * @param shopUnique
	 * @return
	 */
	public List<Map<String,Object>> querySameGoodsMessage(String shopUnique);
	/**
	 * 删除店铺的重复商品，仅保留最大ID的商品信息
	 * @param list
	 * @return
	 */
	public Integer deleteSameGoods(Map<String,Object> map); 
	
	/**
	 * 商品销售排行总页数查询
	 * @param map
	 * @return
	 */
	public int queryGoodsSaleMessagePages(Map<String,Object> map);
	/**
	 * 商品销售排行总页数查询
	 * @param map
	 * @return
	 */
	public List<Map<String,Object>> queryGoodsByPage(Map<String,Object> map);
	
	/**
	 * 商品横向比较页数查询
	 * @param map
	 * @return
	 */
	public Integer queryGoodsLateralSalePages(Map<String,Object> map);
	
	
	public List<Map<String,Object>> queryGoodsLateralSaleByPage(Map<String,Object> map);
	
	public Integer queryGoodsProtraitPages(Map<String,Object> map);
	
	public List<Map<String,Object>> queryGoodsProtraitByPage(Map<String,Object> map);
	public List<Map<String, Object>> queryGoodsKinds(Map<String, Object> map);
	
	/**
	 * 输入商品条码后，自动获取商品信息
	 * @param map
	 * @return
	 */
	public CloudGoodsMain getCloudMessage(Map<String,Object> map);
	
	public List<Map<String,Object>> getGoodsBaseMessage(Map<String,Object> map);
	
	/**
	 * 添加新的商品修改记录
	 * @param map
	 * @return
	 */
	public Integer addNewGoodsRecord(Map<String,Object> map);
	
	/**
	 * 查询商品销售统计数据 不分页
	 * @param map
	 * @return
	 */
	public List<Map<String,Object>> queryGoodsSaleStatistics(Map<String,Object> map);
	/**
	 * 查询商品销售统计数据
	 * @param map
	 * @return
	 */
	public List<Map<String,Object>> queryGoodsSalePage(Map<String,Object> map);

	/**
	 * 商品销售明细列表
	 * @param goods_barcode 商品编码
	 * @param shop_unique 店铺编码
	 * @param startTime 开始时间
	 * @param endTime 结束时间
	 * @return
	 */
	public List<Map<String,Object>> queryGoodsSaleDetail(Map<String,Object> params);
	
	/**
	 * 商品销售明细总条数
	 * @param goods_barcode 商品编码
	 * @param shop_unique 店铺编码
	 * @param startTime 开始时间
	 * @param endTime 结束时间
	 * @return
	 */
	public Integer queryGoodsSaleDetailCount(Map<String,Object> params);
	
	/**
	 * 下载全部销售数据
	 * @param map
	 * @return
	 */
	public List<Map<String,Object>> querySaleAll(Map<String,Object> map);
	
	/**
	 * 查询商品销售统计数据页数
	 * @param map
	 * @return
	 */
	public Integer goodsSaleStatisticsPages(Map<String,Object> map);
	
	public List<String> queryGoodsSaleStatisticsOrderBySale(Map<String,Object> map);
	/**
	 * 商品销售统计界面详情总数量查询
	 * @param map
	 * @return
	 */
	public Integer queryGoodsStatisticsDetailPageCount(Map<String,Object> map);
	
	/**
	 * 商品出入库详情分页查询
	 * @param map
	 * @return
	 */
	public List<Map<String,Object>> queryGoodsStatisticsDetail(Map<String,Object> map);
	
	public Map<String,Object> downloadSaleStatisticsCount(Map<String,Object> map);
	
	
	/**
	 * 商品出入库明细界面查询商品信息
	 * @param map
	 * @return
	 */
	public List<Map<String,Object>> queryGoodsMessageForStatisticsDetail(Map<String,Object> map);
	
	/**
	 * 商品销售数据统计
	 * @param map
	 * @return
	 */
	public List<Map<String,Object>> downloadGoodsSaleStatisticsExcel(Map<String,Object> map);
	
	/**
	 * 商品销售数据统计-宁宇全部店铺
	 * @param map
	 * @return
	 */
	public List<Map<String,Object>> downloadGoodsSaleStatisticsExcel_NYALL(Map<String,Object> map);
	
	/**
	 * 删除商品界面，查询分页数量信息
	 * @param map
	 * @return
	 */
	public Integer toQueryGoodsPage(Map<String,Object> map);
	
	/**
	 * 删除店铺中的商品信息
	 * @param map
	 * @return
	 */
	public Integer deleteGoodsList(Map<String,Object> map);
	/**
	 * 删除商品时，保留商品删除记录，方便同步商品信息
	 * @param map
	 * @return
	 */
	public Integer addNewGoodsDelete(Map<String,Object> map);
	
	/**
	 * 查询需要删除的商品列表信息
	 * @param map
	 * @return
	 */
	public List<String> queryGoodsForDelete(Map<String,Object> map);
	
	/**
	 * 开启、关闭自动补货
	 * @param shop_unique 商家唯一标示
	 * @param auto_purchase 0 关闭自动补货 1开启自动补货
	 * @return
	 */
	public void updateAutoPurchase(Map<String, Object> params);
	
	/**
	 * 修改商品已采购待入库数量
	 * @param shop_unique 商家唯一标示
	 * @param goods_barcode 商品编码
	 * @param goods_count 待入库数量
	 * @return
	 */
	public void updateStayStockCount(Map<String, Object> params);
	
	/**
	 * 添加商品自动采购设置
	 * @param shop_unique 店铺唯一标示
	 * @param goods_barcode 商品唯一标示
	 * @param company_code 供货商code
	 * @param company_name 供货商名称
	 * @param goods_spec_name 商品规格名称
	 * @param compose_specs_id 规格组合id
	 * @param goods_count 商品数量
	 * @param goodsunit_name 商品单位名称
	 * @param sup_goods_id 供货商商品id
	 * @param sup_goods_barcode 供货商商品编码
	 * @param staff_id 创建人
	 * @return
	 */
	public void addGoodsAutoPurchase(@Param("paramsList")List<Map<String, Object>> paramsList);
	
	/**
	 * 获取开启自动补货的商家商品列表
	 * @return
	 */
	public List<Map<String,Object>> getAutoPurchaseGoodsList();
	
	
	/**
	 * 获取店铺默认分类
	 * @return
	 */
	public String getGoodsKindUnique(String shop_unique);
	
	/**
	 * 获取商品自动采购设置
	 * @param shop_unique 店铺唯一标示
	 * @param goods_barcode 商品唯一标示
	 * @return
	 */
	public List<Map<String ,Object>> getGoodsAutoPurchase(Map<String, Object> params);
	
	/**
	 * 删除商品自动补货设置
	 * @return
	 */
	public void deleteGoodsAuto(Map<String, Object> params);
	
	/**
	 * 获取店铺是否开启自动补货
	 * @return
	 */
	public Integer getAutoPurchase(String shop_unique);
	
	/**
	 * 烟草快速导入
	 * @param map
	 * @return
	 */
	public Integer yancaoInsert(Map<String,Object> map);
	
	/**
	 * 新商品的添加数量统计
	 * @param map
	 * @return
	 */
	public List<Map<String,Object>> queryGoodsNewCountForShop(Map<String,Object> map);
	public Integer queryGoodsNewCountForShopAllCount(Map<String,Object> map);
	
	public Map<String,Object> queryGoodsSaleStatis(Map<String,Object> map);
	
	/**
	 * 查询商品是否本店商品
	 * @param map
	 * @return
	 */
	public String queryGoodsBarcodeSameForeignkey(Map<String,Object> map);
	
	/**
	 * 查询商品是否字典表商品
	 * @param map
	 * @return
	 */
	public String queryGoodsDictBarcodeSameForeignkey(Map<String,Object> map);
	
	/**
	 * 查询本店关于该商品的详情信息
	 * @param map
	 * @return
	 */
	public  CloudGoodsMain getGoodsDetailInShop(Map<String,Object> map);
	
	/**
	 * 查询大库与本店相关的产品信息
	 * @param map
	 * @return
	 */
	public CloudGoodsMain getGoodsDetailInDict(Map<String,Object> map);
	
	/**
	 * 查询商品的其他规格信息
	 * @param map
	 * @return
	 */
	public List<Map<String,Object>> queryGoodsStandardMsg(Map<String,Object> map);
	
	/**
     * 获取所有审核通过店铺列表
     * @return
     */
    public List<Map<String ,Object>> getAllShopList();
    
    /**
     * 获取商品详情信息
     * @param goods_id 商品id
     * @return
     */
    public List<Map<String ,Object>> getGoodsDetails(Map<String ,Object> params);
    
    //删掉该商品全部轮播图信息
    public void deleteGoodsDetailImg(String goods_id);
  //删掉该商品全部轮播图信息
    public void deleteGoodsDetailVedio(String goods_id);
    
    //添加图片详情图片
    public void addGoodsDetailImg(Map<String ,Object> params);
    
    //修改图片详情图片
    public void updateGoodsDetailImg(Map<String ,Object> params);
    
    //添加库存记录
    public void addShopStock(Map<String ,Object> params);
    
    /**
	 * 批量更新库存
	 * @param map
	 * @return
	 */
	public Integer batchUpdateCount(Map<String,Object> map);
	
	/**
	 * 查询店铺自定义供货商信息
	 * @param shopUnique
	 * @return
	 */
	public List<Map<String,Object>> getGoodsSupplierMsg(String shopUnique);
	/**
	 * 商品统计界面导出Excel
	 * @param map
	 * @return
	 */
	public  List<Map<String,Object>> queryGoodsSaleStatisticsExcel(Map<String,Object> map);

	public int queryShopClass(Map<String, Object> map);

	public List<Map<String, Object>> downloadSingleGoodsExcel(Map<String, Object> map);
	
	public Integer addGgoodsCertif(Map<String,Object> map);
	
	public Integer delGgoodsCertif(Map<String,Object> map);
	
	public Integer quertGgoodsCertif(Map<String,Object> map);
	
	public  List<Map<String,Object>> querySecretaryList();
	
	public int updatePhoto(Map<String, Object> map);

	/**
	 * 根据商品条码查询
	 * @param map
	 * @return
	 */
	public List<GoodsInfo> queryGoodsCountByBarcodeList(Map<String,Object> map);

	RecordGoods selectSourceGoods(Map<String, Object> params);

	List<RecordGoods> selectGoodsShelfState(@Param("goods_id_list") List<String> goods_id_list);

	/**
	 * 根据商品id集合查询
	 * @param map
	 * @return
	 */
	List<GoodsEntity> queryGoodsByParam(Map<String,Object> map);

	/**
	 * 根据商品id查询
	 * @param map
	 * @return
	 */
	GoodsEntity queryOneByParam(Map<String,Object> map);

	List<Map<String,Object>> getGoodBatchList(Map<String,Object> map);

	/**
	 * 批量查询商品进价
	 * @param shopUnique
	 * @param goodsBarcodeList
	 * @return
	 */
    List<GoodsEntity> selectGoodsInPriceList(@Param("shopUnique") Long shopUnique, @Param("goodsBarcodeList") Set<String> goodsBarcodeList);

	/**
	 * 批量更新商品库存
	 * @param goodsUpdateList
	 * @return
	 */
	int updateGoodsCount(@Param("goodsUpdateList") List<GoodsEntity> goodsUpdateList);

	/**
	 * 批量更新商品大规格进价
	 * @param goodsUpdateList
	 * @return
	 */
	int updateGoodsInPrice(@Param("goodsUpdateList") List<GoodsEntity> goodsUpdateList);

	/**
	 * 查询商品上级规格
	 * @param goodsQuery
	 * @return
	 */
	GoodsEntity queryParantGoods(Map<String, Object> goodsQuery);
}
