package org.haier.shop.dao;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

public interface SupplierDao {
	
	/**
	 * 删除指定商品的供货商信息
	 * @param map
	 * @return
	 */
	public Integer deleteAllGoodsSupplier(Map<String,Object> map);
	
	Map<String, Object> querySupplierListCount(Map<String, Object> map);

	List<Map<String, Object>> querySupplierList(Map<String, Object> map);

	Map<String, Object> getMapCenter(Map<String, Object> map);

	Map<String, Object> queryDistrict(Map<String, Object> map);

	int saveSupplier(Map<String, Object> map);

	Map<String, Object> getSupplierById(Map<String, Object> map);

	Map<String, Object> getDistrict(Map<String, Object> map);

	Map<String, Object> getCity(Map<String, Object> map);

	int editSupplier(Map<String, Object> map);

	int deleteP(Map<String, Object> map);

	List<Map<String, Object>> querySupplierByContries(Map<String, Object> map);

	List<Map<String, Object>> querySupplierByName(Map<String, Object> map);

	Map<String, Object> querySupplierBySupplierUnique(Map<String,Object> map);

	int submitOrder(Map<String, Object> map);

	void addPurchaseListDetail(Map<String, Object> map2);

	Map<String, Object> getGoodsInfo(HashMap<String, Object> params);

	void addNewGoods(Map<String, Object> goodsInfoSource);

	void addShopStock(Map<String, Object> smap);

	void updateGoodsCount(Map<String, Object> goodsInfo);
	
	/**
	 * 供货商供应商品查询页面-分页数量
	 * @param map
	 * @return
	 */
	public Integer querySupGoodsPages(Map<String,Object> map);
	
	/**
	 * 供应商供应商品查询界面-分页查询
	 * @param map
	 * @return
	 */
	public List<Map<String,Object>> querySupGoodsByPage(Map<String,Object> map);
	/**
	 * 清除已有商品的供货商信息
	 * @param map
	 * @return
	 */
	public Integer clearSupGoodsMsgPage(Map<String,Object> map);
	/**
	 * 商品信息更新
	 * @param map
	 * @return
	 */
	public Integer saveGoodsInPrice(Map<String,Object> map);

	List<Map<String, Object>> querySupplierKindList(Map<String, Object> map);

	Map<String, Object> querySupplierKindListCount(Map<String, Object> map);

	int saveSupplierKind(Map<String, Object> map);

	void deleteSupplierKind(Map<String, Object> map);

	Map<String, Object> getSupplierKindById(Map<String, Object> map);

	void editSupplierKind(Map<String, Object> map);

	List<Map<String, Object>> querySupplierKindByShopUnique(Map<String, Object> map);
}
