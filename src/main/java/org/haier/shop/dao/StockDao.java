package org.haier.shop.dao;

import java.util.List;
import java.util.Map;

/**
 * 出入库记录
 * <AUTHOR>
 */
public interface StockDao {
	
	/**
	 * 
	 * @param map
	 * @return
	 */
	public List<Map<String,Object>> queryGoodsStockStatistics(Map<String,Object> map);
	/**
	 * 商品出入库记录添加
	 * @param map
	 * @return
	 */
	public int newStockRecord(Map<String,Object> map);
	/**
	 * 批量插入订单信息！
	 * @param list
	 * @return
	 */
	public int newStockRecords(List<Map<String,Object>> list);
	/**
	 * 查询店铺的出入库记录
	 * @param map
	 * @return
	 */
	public List<Map<String,Object>> queryShopStockRecord(Map<String,Object> map);
	/**
	 * 商品出入库，查询商品的基本信息
	 * @param map
	 * @return
	 */
	public Map<String,Object> getBottomGoodsMessage(Map<String,Object> map);
	
	/**
	 * 出入库时，其他规格商品查询
	 * @param map
	 * @return
	 */
	public List<Map<String,Object>> queryGoodsStand(Map<String,Object> map);
	
	/**
	 * 
	 * @param map
	 * @return
	 */
	public Integer modifyGoodsCount(Map<String,Object> map);
	public List<Map<String, Object>> queryShopStockRecordList(Map<String, Object> map);
	public List<Map<String, Object>> queryGoodsImageByFailId(Map<String, Object> map2);
	public Map<String, Object> queryShopStockRecordDetail(Map<String, Object> params);
	public Map<String, Object> queryGoodsStockLast(Map<String, Object> params);
	public int queryStockListUnique(Map<String, Object> params);
	public void addIntoStock(Map<String, Object> map);
	public List<Map<String, Object>> queryGoodsStatisticsDetail(Map<String, Object> params);
	public Integer queryGoodsStatisticsDetailPageCount(Map<String, Object> params);
	public void deleteStock(Map<String, Object> map);
	public List<Map<String, Object>> queryStockDetail(Map<String, Object> map);
	public int querStockAuditStatus(Map<String, Object> params);
	public int modifyGoodsCount2(Map<String, Object> map);
	public void updateStock(Map<String, Object> map);
	public void updateStockStatus(Map<String, Object> params);
	public void updateIntoStock(Map<String, Object> map);
	public int queryStockGoods(Map<String, Object> map);
	public void addIntoStockDetail(Map<String, Object> params);
	public void deleteStockDetail(Map<String, Object> map);
	public List<Map<String, Object>> queryGoodsModifyPriceList(Map<String, Object> params);
	public Integer queryGoodsModifyPriceListPageCount(Map<String, Object> params);
	public void addGoodsModifyList(Map<String, Object> params);
	public void addGoodsModify(Map<String, Object> map);
	public void deleteGoodsModify(Map<String, Object> map);
	public void deleteGoodsModifyList(Map<String, Object> map);
	public List<Map<String, Object>> queryGoodsModifyDetail(Map<String, Object> map);
	public Map<String, Object> queryGoodsModifyList(Map<String, Object> map);
	public void addAuditGoodsModify(Map<String, Object> params);
	public void updateBatchModifyPrice(List<Map<String, Object>> list);
	public void updateGoodsModifyList(Map<String, Object> params);
	public Map<String, Object> queryExistGoods(Map<String, Object> map);
	public void updateGoodsModify(Map<String, Object> map);
	public List<Map<String, Object>> queryGoodsModifyInPriceList(Map<String, Object> params);
	public Integer queryGoodsModifyInPriceListPageCount(Map<String, Object> params);
	public void updateBatchModifyInPrice(List<Map<String, Object>> list);
	public void updateGoodsModifyInPrice(Map<String, Object> map);
	public Map<String, Object> queryAveragePrice(Map<String, Object> map);
	/**
	 * 批量更新库存
	 * @param map
	 */
	public void batchUpdateGoodsCount(Map<String,Object> map);

	public void updateStockSourceUnique(Map<String, Object> map);
}
