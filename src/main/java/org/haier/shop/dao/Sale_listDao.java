package org.haier.shop.dao;

import org.haier.shop.entity.SaleListMain;
import org.haier.shop.entity.ret.ReturnMain;

import java.util.List;
import java.util.Map;

public interface Sale_listDao {
	
	/**
	 * 1、查询各分店的营业额信息
	 * @param map
	 * @return
	 */
	public List<Map<String,Object>> statisticsForShopByPage(Map<String,Object> map);
	/**
	 * 
	 * @param map
	 * @return
	 */
	public Integer statisticsForShopCount(Map<String,Object> map);
	
	public List<Map<String,Object>> queryGoodsCount();
	public Integer updateGoodsList(List<Map<String,Object>> list);
	public Integer queryRewardList(String shop_unique);
	/**
	 * 添加入库记录
	 * @param map
	 * @return
	 */
	public Integer addShopStockDetail(Map<String,Object> map);
	/**
	 * 添加商品库存修改记录
	 * @param list
	 * @return
	 */
	public Integer addShopStockList(Map<String,Object> map);
	/**
	 * 
	 * @param list
	 * @return
	 */
	public Integer modifyGoodsMsg(List<Map<String,Object>> list);
	
	/**
	 * 查询退款商品的小规格信息
	 * @param map
	 * @return
	 */
	public List<Map<String,Object>> querySmallGoodsMsg(Map<String,Object> map);
	/**
	 * 修改店铺的百货豆，余额，lkl余额信息
	 * @param map
	 * @return
	 */
	public Integer modifyShopsMsg(Map<String,Object> map);
	/**
	 * 添加会员余额，百货豆变动记录
	 * @param list
	 * @return
	 */
	public Integer addCusChangRecord(List<Map<String,Object>> list);
	/**
	 * 查询退款订单详情
	 * @param map
	 * @return
	 */
	public ReturnMain queryReturnDetail(Map<String,Object> map);
	/**
	 * 
	 * @param map
	 * @return
	 */
	public Integer modifyReturnMsg(Map<String,Object> map);
	
	/**
	 * 查询各种退款方式的金额小计
	 * @param map
	 * @return
	 */
	public List<Map<String,Object>> queryRetStatisticsDetail(Map<String,Object> map);
	/**
	 * 统计退款信息
	 * @param map
	 * @return
	 */
	public Map<String,Object> queryRetStatistics(Map<String,Object> map);
	/**
	 * 查询申请退款的订单数量
	 * @param map
	 * @return
	 */
	public Integer queryRetListsCount(Map<String,Object> map);
	/**
	 * 查询退款申请的退货订单
	 * @param map
	 * @return
	 */
	public List<Map<String,Object>> queryRetLists(Map<String,Object> map);
	
	public Map<String,Object> queryOrderDetailJY(Map<String,Object> map);
	public List<SaleListMain> saleListExcelDetailClass(Map<String,Object> map);
	/**
	 * 查询店铺的退款信息
	 * @param map
	 * @return
	 */
	public List<Map<String,Object>> getReturnListMsg(Map<String,Object> map);
	
	public List<Map<String,Object>> queryCustmerRechargeStatistics(Map<String,Object> map);
	public Map<String,Object> payTypeStatisticsOnline(Map<String,Object> map);
	public List<Map<String,Object>> payTypeStatistics(Map<String,Object> map);
	/**
	 * 订单查询
	 * @param map
	 * @return
	 */
	public List<Map<String,Object>> querySaleLists(Map<String,Object> map);
	
	public List<Map<String,Object>> querySaleNYLists(Map<String,Object> map);
	/**
	 * 订单查询总条数
	 * @param map
	 * @return
	 */
	public Integer querySaleListsCount(Map<String,Object> map);
	
	public Map<String,Object> querySaleNYListsCount(Map<String,Object> map);
	
	public List<Map<String,Object>> queryShopOrderList(Map<String,Object> map);
	
	public int queryShopOrderListPage(Map<String,Object> map);
	
	/**
	 * EXCEL下载用订单查询
	 * @param map
	 * @return
	 */
	public List<Map<String,Object>> querySaleListsForExcel(Map<String,Object> map);
	
	public List<Map<String,Object>> querySaleListsNYForExcel(Map<String,Object> map);
	
	public List<Map<String,Object>> saleListDetailYNExcel(Map<String,Object> map);
	
	/**
	 * 订单总信息查询
	 * @param map
	 * @return
	 */
	public Map<String,Object> queryOrderTotalNew(Map<String,Object> map);
	/**
	 * 订单总信息查询
	 * @param map
	 * @return
	 */
	public Map<String,Object> queryOrderTotal(Map<String,Object> map);
	
	/**
	 * 查询订单详情！
	 * @param map
	 * @return
	 */
	public SaleListMain queryOrderDetail(Map<String,Object> map);
	
	/**
	 * 更新订单状态
	 * @param map
	 * @return
	 */
	public int updateSaleList(Map<String,Object> map);
	
	/**
	 * 查询店铺销售信息
	 * @param map
	 * @return
	 */
	public Map<String,Object> querySaleMessage(Map<String,Object> map); 
//	public List<Map<String,Object>> querySaleMessage(Map<String,Object> map);

	/**
	 * 查询库存商品的滞销量和缺货量
	 * @param map
	 * @return
	 */
	public List<Map<String,Object>> warningCount(Map<String,Object> map);
	
	
	public Map<String,Object> testT(Map<String,Object> map);
	/**
	 * 店铺周期内销售信息查询
	 * @param map
	 * @return
	 */
	public List<Map<String,Object>> salesTurnoverStatistics(Map<String,Object> map);
	
	/**
	 * 店铺各月内，各分类商品销售情况汇总
	 * @param map
	 * @return
	 */
	public List<Map<String,Object>> typeSaleByTime(Map<String,Object> map);
	
	/**
	 * 周期时间内各评分的订单数量比
	 * @param map
	 * @return
	 */
	public List<Map<String,Object>> saleListEvaluateQuery(Map<String,Object> map);
	
	/**
	 * 店铺活跃用户查询
	 * @param map
	 * @return
	 */
	public List<Map<String,Object>> cusActivityQuery(Map<String,Object> map);
	/**
	 * 各状态订单数量查询
	 * @param map
	 * @return
	 */
	public int newOrdersCount(Map<String,Object> map);
	/**
	 * 销售订单excel表下载
	 * @param map
	 * @return
	 */
	public List<SaleListMain> saleListExcel(Map<String,Object> map);
	
	/**
	 * 店铺订单总览界面：页数查询
	 * @param map
	 * @return
	 */
	public int queryListPages(Map<String,Object> map);
	
	/**
	 * 店铺订单总览界面：分页查询订单
	 * @param map
	 * @return
	 */
	public List<Map<String,Object>> queryListByPage(Map<String,Object> map);
	
	/**
	 * 订单详情列表
	 * @param map
	 * @return
	 */
	public List<Map<String,Object>> queryListDetail(Map<String,Object> map);
	
	/**
	 * 
	 * @param map
	 * @return
	 */
	public List<Map<String,Object>> saleListExcelDetail(Map<String,Object> map);
	
	public List<Map<String,Object>> getEvaluateList(Map<String,Object> map);
	
	public int getEvaluateListPage(Map<String,Object> map);
	
	/**
	 * 评价回复
	 */
	public int updateEvaluate(Map<String,Object> map);
	
	public List<Map<String,Object>> getEvaluateImage(Map<String,Object> map);
	
	//查询订单核实商品信息
	public List<Map<String ,Object>> getSaleListVerifyList(Map<String ,Object> params);

	public List<Map<String, Object>> queryCusRenewCount(Map<String, Object> map);
	
	public List<Map<String, Object>> querySaleListsNY(Map<String, Object> map);
	
	public int querySaleListsNYCount(Map<String,Object> map);
	public List<Map<String, Object>> querySelectPeiSong(Map<String, Object> m);
	public void modifyPlatcusMsg(Map<String, Object> cusMap);
	
	List<Map<String, Object>> queryXXsale(Map<String, Object> params);
	
	Map<String,Object> queryXXsaleCount(Map<String, Object> params);
	
	List<Map<String, Object>> statisticsForShopByTime(Map<String, Object> params);
	
	List<Map<String, Object>> statisticsForShopByDay(Map<String, Object> params);

	Map<String, String> selectPayConfig(Map<String, Object> payConfigParams);

    List<Map<String, Object>> queryGoodsDetailInfo(Map<String, Object> map);
}
