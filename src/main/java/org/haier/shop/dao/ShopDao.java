package org.haier.shop.dao;

import java.util.List;
import java.util.Map;

import org.haier.shop.entity.*;
import org.haier.shop.pojo.QueryShopInfoPo;
import org.haier.shop.result.shop.ShopConfigQueryResult;

public interface ShopDao {
	/**
	 * 查询需要确认收货的订单
	 * @param map
	 * @return
	 */
	public List<String> queryWaitingConfirmOrderList(Map<String,Object> map);
	/**
	 * 查询设置了自动收货的店铺信息
	 * @return
	 */
	public List<Map<String,Object>> queryWaitingConfirmShopList();
	/**
	 * 查询餐厅的充值情况
	 * @param map
	 * @return
	 */
	public Map<String,Object> queryCanTingAccount(Map<String,Object> map);
	/**
	 * 查询线下收入的统计信息
	 * @param map
	 * @return
	 */
	public List<Map<String,Object>> queryUnlineAccount(Map<String,Object> map);
	/**
	 * 查询线上收入的统计信息
	 * @param map
	 * @return
	 */
	public List<Map<String,Object>> queryOnlineAccount(Map<String,Object> map);
	/**
	 * 查询退款订单的统计信息
	 * @param map
	 * @return
	 */
	public List<Map<String,Object>> queryReturnListByShop(Map<String,Object> map);
	
	public Map<String,Object> queryNeedReturnMoneyByMainOrderNo(String main_order_no);
	public Integer addNewGrantDetail(String shop_unique);
	/**
	 * 查询店铺各种支付方式的统计
	 * @param map
	 * @return
	 */
	public Map<String,Object> queryLoginBusinessDetail(Map<String,Object> map);
	/**
	 * 查询满足条件的店铺数量
	 * @param map
	 * @return
	 */
	public Integer queryLoginBusinessMsgCount(Map<String,Object> map);
	
	public Map<String,Object> queryLoginCusRefundMsg(Map<String,Object> map);
	
	public List<Map<String,Object>> queryLoginCusRechargeMsg(Map<String,Object> map);
	
	public Map<String,Object> queryLoginBusinessMsg(Map<String,Object> map);
	
	public List<Map<String,Object>> queryLoginRecord(Map<String,Object> map);
	
	/**
	 * 新增店铺金圈币信息
	 * @param map
	 * @return
	 */
	public Integer addNewShopGold(Map<String,Object> map);
	/**
	 * 修改店铺金圈币余额
	 * @param map
	 * @return
	 */
	public Integer modifyShopGold(Map<String,Object> map);
	
	/**
	 * 修改领取状态
	 * @param map
	 * @return
	 */
	public Integer grantGoldById(Map<String,Object> map);
	
	/**
	 * 获取未发放的金圈币信息
	 * @param map
	 * @return
	 */
	public Map<String,Object> queryGoldGrant(Map<String,Object> map);
	/**
	 * 获取当前店铺的金圈币余额
	 * @param map
	 * @return
	 */
	public Double queryShopJQB(Map<String,Object> map);
	/**
	 * 查询中心站下各店铺的营业状况数量总和
	 */
	public Integer queryShopByAreaCount(Map<String,Object> map);
	/**
	 * 查询中心站下各店铺的营业状况
	 * @param map
	 * @return
	 */
	public List<Map<String,Object>> queryShopByArea(Map<String,Object> map);
	/**
	 * 更新店铺信息
	 * @param map
	 * @return
	 */
	public Integer modifyShopsConfig(Map<String,Object> map);
	
	public Map<String,Object> queryCusOnLineMsg(String staffPhone);
	/**
	 * 查询店铺基本信息
	 * @param map
	 * @return
	 */
	public Map<String,Object> queryShopMessage(Map<String,Object> map);
	/**
	 * 更新店铺信息
	 * @param map
	 * @return
	 */
	public int updateShopDetail(Map<String,Object> map);
	/**
	 * 查询店铺功能设置
	 * @param map
	 * @return
	 */
	public Map<String,Object> queryShopFunction(Map<String,Object> map);
	/**
	 * 更新店铺功能设置
	 * @param map
	 * @return
	 */
	public int updateShopFunction(Map<String,Object> map);
	/**
	 * 登录检查
	 * @param map
	 * @return
	 */
	public Map<String,Object> login(Map<String,Object> map);
	/**
	 * 统计店铺周期内的销售业绩（按月，按年，或按日）
	 * @param map
	 * @return
	 */
	public List<Map<String,Object>> turnCount(Map<String,Object> map);
	
	/**
	 * 统计店铺周期内的订单量（按日，按月，按年）
	 * @param map
	 * @return
	 */
	
	public List<Map<String,Object>> saleListCount(Map<String,Object> map);
	
	/**
	 * 店铺客户访问量统计
	 * @param map
	 * @return
	 */
	public List<Map<String,Object>> visitCount(Map<String,Object> map);
	
	/**
	 * 
	 * 查询店铺周期内库存总额
	 * @param map
	 * @return
	 */
	public List<Map<String,Object>> queryStockValue(Map<String,Object> map);
	
	/**
	 * 查询营业天数
	 * @param map
	 * @return
	 */
	public int turnCountDays(Map<String,Object> map);
	/**
	 * 浏览量天数
	 * @param map
	 * @return
	 */
	public int visitCountDays(Map<String,Object> map);
	/**
	 * 库存天数
	 * @param map
	 * @return
	 */
	public int stockDays(Map<String,Object> map);
	/**
	 * 注册新用户
	 * @param map
	 * @return
	 */
	public int register(Map<String,Object> map);
	
	/**
	 * 记录店铺机器在线
	 * @param map
	 * @return
	 */
	public int shopOnLine(Map<String,Object> map);
	/**
	 * 在线店铺测试
	 * @param map
	 * @return
	 */
	public List<Map<String,Object>> countStroeTurnover(Map<String,Object> map);
	public void  createTemTable(Map<String,Object> map);
	public Integer addTemData(Map<String,Object> map);
	public List<Map<String,Object>> queryTemData(Map<String,Object> map);
	public List<Map<String,Object>> mapTest(Map<String,Object> map);
	
	/**
	 * 店铺数量页数查询
	 * @param map
	 * @return
	 */
	public Integer countStroeTurnoverPages(Map<String,Object> map);
	
	/**
	 * 查询所有店铺的信息
	 * @param map
	 * @return
	 */
	public Integer queryShopsPages(Map<String,Object> map);
	
	/**
	 * 商品销量总览界面-店铺列表查询
	 * @param map
	 * @return
	 */
	public List<Map<String,Object>> queryShopsList(Map<String,Object> map);
	
	/**
	 * 店铺销量总览界面最大销量查询
	 * @param map
	 * @return
	 */
	public Double biggestSaleTotal(Map<String,Object> map);
	/**
	 * 查询某区县内所有店铺信息
	 * @param areaDictNum
	 * @return
	 */
	public List<Map<String,Object>> queryShopsListForGoods(String areaDictNum);
	
	/**
	 * 新店铺添加时，添加新的店铺升级记录
	 * @param map
	 * @return
	 */
	public int newShopUpDateRecord(Map<String,Object> map);
	
	public List<Map<String,Object>> getShopList(Map<String,Object> map);
	public List<Map<String,Object>> getGoods();
	
	public void batchAddSaleDetail(List<SaleListDetail> listDet);

	public Integer addrSaleList(List<SaleList> salelist);
	
	public int addSaleListPayment(List<Map<String,Object>> map);
	
	/**
	 * 添加店铺图标信息（）
	 * @param map
	 * @return
	 */
	public Integer addShopTitle(Map<String,Object> map);
	public void batchAddAgeList(List<Map<String, Object>> ageList);
	
	public List<Map<String,Object>> countStoreTurnoverShop(Map<String,Object> map);
	
	//查询商家结算列表
	public List<Map<String,Object>> settlementList(Map<String,Object> params);
	
	//查询商家结算列表总条数
	public Integer settlementListCount(Map<String,Object> params);
	//宁宇分店结算列表
	public List<Map<String,Object>> settlementListNY(Map<String,Object> params);
	
	public Integer settlementNYListCount(Map<String,Object> params);
	
	//查询商家结算记录列表
	public List<Map<String,Object>> shopCouponCashList(Map<String,Object> params);
		
	//查询商家结算记录列表总条数
	public Integer shopCouponCashListCount(Map<String,Object> params);
	
	//获取商家余额
	public ShopVO getShopBalance(String shop_unique);
	//商家当日收益
	public Double getShopBalanceDay(String shop_unique);
	//商家当日收益2
	public Double getShopBalanceDay2(String shop_unique);
	//商家易通当日收益2
	public Double getShopBalanceDay3(String shop_unique);
	
	//修改商家余额
	public void updateShopBalance(Map<String ,Object> params);
	
	//添加结算记录
	public void addShopCouponCash(Map<String ,Object> params);
	
	//获取商家银行卡信息
	public Map<String ,Object> getShopCardInfo(String shop_unique);
	
	//获取结算费率，千分之
	public ShopVO getSysRate(String shop_unique);
	
	//修改店铺小程序开通状态
	public void updateShowBuyStatus(Map<String ,Object> params);
	
	//查询小程序审核列表
	public List<Map<String,Object>> queryWechatExamineList(Map<String,Object> params);
			
	//查询小程序审核列表总条数
	public Integer queryWechatExamineListCount(Map<String,Object> params);
	
	//查询店铺管理列表
	public List<Map<String,Object>> getshopList(Map<String,Object> params);
				
	//查询店铺 管理列表总条数
	public Integer getshopListCount(Map<String,Object> params);
	
	//获取商家详细信息
	public Map<String ,Object> getShopDetail(String shop_unique);
	
	//获取店铺快递信息
	public Map<String ,Object> getShopExpress(Map<String ,Object> params);
	
	//添加店铺快递信息
	public Integer addShopExpress(Map<String ,Object> params);
	
	//修改店铺快递信息
	public Integer updateShopExpress(Map<String ,Object> params);
	
	//删除店铺快递信息
	public Integer deleteShopExpress(Map<String ,Object> params);
	
	//获取店铺关联快递公司列表
	public List<Map<String ,Object>> getShopExpressRelationList(String shop_unique);
	
	//删除店铺关联快递公司信息
	public Integer deleteShopExpressRelation(String shop_unique);
	
	//添加店铺关联快递公司信息
	public Integer addShopExpressRelation(List<Map<String ,Object>> list);
	
	//获取店铺信息
	public Map<String ,Object> getShopMsgDetail(Map<String ,Object> params);
	
	//获取默认店铺分销等级设置信息
	public List<Map<String ,Object>> getDisLevelList(Map<String ,Object> params);
	
	//添加店铺分销等级设置信息
	public Integer addDisLevel(List<Map<String ,Object>> list);
	//新增虚拟分类
	public Integer addNewGoodsKindInventedMsg(Map<String,Object> map);
	
	//查询商家分销商等级详情
	public Map<String ,Object> queryShopDisLevel(String dis_level_id);
	
	//查询商家分销等级升级详情列表
	public List<Map<String ,Object>> queryDisLevelConditionList(Map<String ,Object> params);
	
	//获取该店铺是否有会员自动成为分销商的等级设置
	public Map<String ,Object> queryShopUpIsAuto(String shop_unique);
	
	//修改商家分销商等级
	public Integer updateDisLevel(Map<String,Object> params);
	
	//删除商家分销等级升级详情
	public Integer deleteDisLevelCondition(String dis_level_id);
	
	//添加商家分销等级升级详情
	public Integer addDisLevelCondition(List<Map<String ,Object>> list);
	
	//查询店铺软件收费开始时间
	public Map<String ,Object> queryShopSoftChargeTime();
	
	//获取店铺关联快递公司列表
	public List<Map<String ,Object>> getShopHoursList(String shop_unique);
	
	//删除店铺营业时间
	public Integer deleteShopHours(String shop_unique);
		
	//添加店铺营业时间
	public Integer addShopHours(List<Map<String ,Object>> list);

	public void addDisGoods(List<Map<String, Object>> goods_list);

	public void deleteDisGoods(Map<String, Object> params);

	public List<Map<String, Object>> queryDisGoodsList(Map<String, Object> params);

	public void deleteDisGoodsKind(Map<String, Object> params);

	public void addDisGoodsKind(List<Map<String, Object>> goods_list);

	public List<Map<String, Object>> queryDisGoodsKindList(Map<String, Object> params);

	public int queryDifferentDisLevelCount(Map<String, Object> params);

	public Map<String, Object> querySetCommissionRatio(Map<String, Object> params);


	Map<String, Object> queryShopInfoByParams(QueryShopInfoPo queryPo);
	
	public List<Map<String, Object>> getTogetherCode(Map<String, Object> params);

	public int getTogetherCodeCount(Map<String, Object> params);
	
	public int updateTogetherCode(Map<String, Object> params);
	
	public int updateTogetherCodeDetail(Map<String, Object> params);
	
	//获取商家聚合码详细信息 getShopDetail
	public Map<String ,Object> getTogetherCodeInfo(String shop_unique);
	
	public ShopQualificationInfoEntity queryTogetherCodeInfo(Map<String, Object> params);
	
	public int insertQualificationInfo(ShopQualificationInfoEntity params);

	ShopConfigQueryResult queryShopConfig(String shopUnique);

	void updateShopConfig(ShopConfigQueryResult params);

	void insertShopConfig(ShopConfigQueryResult params);

    List<ShopsEntity> createBatchShopPayCode(Map<String, Object> params);
}
