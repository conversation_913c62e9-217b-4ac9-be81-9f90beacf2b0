package org.haier.shop.dao;

import java.util.List;
import java.util.Map;

public interface GoodsShelfStateDao {
	
	/**
	 * 商品信息查询
	 * @param shop_unique 店铺编号
	 * @param goods_message 输入的商品信息
	 * @param goods_kind_unique 商品分类编号
	 * @param shelf_state 上架状态：1、已上架；2、已下架
	 * @param time 时长
	 */
	public List<Map<String,Object>> queryShelfStateGoodsMessage(Map<String,Object> params);
	
	/**
	 * 修改指定商品的上下架信息
	 * @param shopUnique 店铺编号
	 * @param goodsMessage 输入的商品信息
	 * @param kindUnique 商品分类编号
	 * @param groupUnique 商品大类编号
	 * @param time 时长
	 * @return
	 */
	public Integer downQueryShelfStateGoodsMessage(Map<String,Object> params);
	
	/**
	 * 商品信息查询总条数
	 * @param shop_unique 店铺编号
	 * @param goods_message 输入的商品信息
	 * @param goods_kind_unique 商品分类编号
	 * @param shelf_state 上架状态：1、已上架；2、已下架
	 * @param time 时长
	 */
	public Integer queryShelfStateGoodsMessageCount(Map<String,Object> params);
	
	/**
	 * 修改商品上下架状态
	 * @param goods_ids 商品id集合，已逗号隔开
	 * @param shelf_state 上架状态：1、已上架；2、已下架
	 * @return
	 */
	public Integer updateShelfState(Map<String,Object> params);
	
	/**
	 * 修改商品售价
	 * @param goods_id 商品id
	 * @param goods_sale_price 售价
	 * @param goods_web_sale_price 网购价
	 * @return
	 */
	public Integer updateGoodsSalePrice(Map<String,Object> params);
	
	/**
	 * 获取店铺商品售价为负的商品信息
	 * @param shop_unique 店铺编号
	 * @param type  1网购价 2售价
	 * @return
	 */
	public List<Map<String ,Object>> getGoodsSalePriceFu(Map<String,Object> params);
	
	/**
	 * 修改全部上下架状态
	 * @param shop_unique 店铺编号
	 * @param state 上架状态：1、已上架；2、已下架
	 * @param type 修改类型：1、线上  2线下收银
	 * @return
	 */
	public Integer updateAllShelfState(Map<String,Object> params);
 }
