package org.haier.shop.dao;

import org.apache.ibatis.annotations.Param;
import org.haier.shop.entity.AllKindsInShop;
import org.haier.shop.entity.GoodsGroups;

import java.util.List;
import java.util.Map;

public interface GoodsKindIconDao {
	/**
	 * 上传图标时将图标保存到图标库中
	 * @param map
	 * @return
	 */
	public int addNewGoodsKindIcon(Map<String,Object> map);

	public List<Map<String,Object>> queryListByIconTypeAndShopUnique(Map<String,Object> map);

	public List<Map<String,Object>> queryListByIconType(Map<String,Object> map);

	}
