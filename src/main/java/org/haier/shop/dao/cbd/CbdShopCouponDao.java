package org.haier.shop.dao.cbd;

import org.haier.shop.params.cbd.CbdShopCouponListParams;
import org.haier.shop.result.cbd.CbdShopCouponCountDtoResult;
import org.haier.shop.result.cbd.CbdShopCouponDtoResult;

import java.util.List;

public interface CbdShopCouponDao {
    List<CbdShopCouponDtoResult> queryCouponRecordList(CbdShopCouponListParams params);

    Integer queryCouponRecordListCount(CbdShopCouponListParams params);

    List<CbdShopCouponCountDtoResult> queryCouponRecordCount(CbdShopCouponListParams params);
}
