package org.haier.shop.dao;

import org.haier.shop.entity.speech.SpeechListEntity;
import org.haier.shop.params.speech.QuerySpeechListParams;
import org.haier.shop.result.speech.QuerySpeechListByParamResult;

import java.util.List;

public interface SpeechListDao {
    public List<SpeechListEntity> querySpeechList(QuerySpeechListParams querySpeechListParams);

    public SpeechListEntity querySpeechEntity(SpeechListEntity speechListEntity);

    public List<QuerySpeechListByParamResult> querySpeechListByParam(QuerySpeechListParams querySpeechListParams);

    public Integer querySpeechListCount(QuerySpeechListParams querySpeechListParams);
}
