package org.haier.shop.dao;

import java.util.List;
import java.util.Map;

public interface BeanRechargeConfigDao {
	
	/**
	 * 查询列表
	 * @return
	 */
	public List<Map<String ,Object>> getList(Map<String ,Object> params);
	
	/**
	 * 查询列表总条数
	 * @return
	 */
	public Integer getListCount(Map<String ,Object> params);
	
	/**
	 * 添加
	 * @return
	 */
	public void add(Map<String ,Object> params);
	
	/**
	 * 详情
	 * @return
	 */
	public Map<String ,Object> getInfo(String beans_recharge_config);
	
	/**
	 * 修改
	 * @return
	 */
	public void update(Map<String ,Object> params);
	
	/**
	 * 删除
	 * @return
	 */
	public void delete(String beans_recharge_config);
}
