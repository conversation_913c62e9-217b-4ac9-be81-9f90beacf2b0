package org.haier.shop.dao;

import org.haier.shop.entity.shopTitle.ShopTitle;

import java.util.List;
import java.util.Map;

public interface ShopTitleDao {

    /**
     * 查询满足条件的标题列表
     *
     * @param shopTitle
     * @return
     */
    List<ShopTitle> queryShopTitleList(Map<String, Object> shopTitle);

    /**
     * 查询满足条件的标题数量
     *
     * @param shopTitle
     * @return
     */
    Integer queryShopTitleCount(Map<String, Object> shopTitle);

    /**
     * 查询满足条件的最大标题ID
     *
     * @param shopTitle
     * @return
     */
    Integer queryMaxId(Map<String, Object> shopTitle);

    Integer addShopTitle(ShopTitle shopTitle);

    Integer batchAddShopTitle(List<ShopTitle> list);

    List<Map<String, Object>> queryShopList(Map<String, Object> map);

    List<ShopTitle> queryShopTitleInfo(Map<String, Object> map);

    /**
     * 查询满足条件的标题数量
     *
     * @param shopTitle
     * @return
     */
    Integer queryShopTitleInfoCount(Map<String, Object> shopTitle);

    Integer deleteShopTitle(Integer id);

    ShopTitle queryShopTitleInfoById(Long id);

    Integer modifyShopTitleValidTypeBatch(List<Map<String, Object>> list);

    Integer modifyShopTitle(ShopTitle shopTitle);

}
