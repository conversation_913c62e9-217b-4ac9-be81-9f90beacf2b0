package org.haier.shop.dao;

import java.util.List;
import java.util.Map;

public interface RotationDao {
	
	public Integer deleteRotationImg(Map<String,Object> map);
	/**
	 * 查询轮播图信息
	 * @param map
	 * @return
	 */
	public List<Map<String,Object>> queryRotationList(Map<String,Object> map);
	public Integer queryRotationListCount(Map<String,Object> map);
	/**
	 * 查询加油站品牌列表
	 * @return
	 */
	public List<Map<String,Object>> queryBrandList();
	
	/**
	 * 更新轮播图
	 * @param map
	 * @return
	 */
	public Integer updateRotationImg(Map<String,Object> map);
	/**
	 * 新增轮播图
	 * @param map
	 * @return
	 */
	public Integer addNewRotationImg(Map<String,Object> map);
}
