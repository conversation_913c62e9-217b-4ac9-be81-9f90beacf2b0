package org.haier.shop.dao;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

import org.haier.customer.entity.Order;
import org.haier.customer.entity.Shops;
import org.haier.shop.entity.CY.CustomerSummaryCYByGroup;
import org.haier.shop.entity.ny.CustomerSummaryByGroup;
import org.haier.shop.entity.ny.NingyuLottery;
import org.haier.shop.entity.ny.NingyuLotteryList;
import org.haier.shop.params.customer.RechargeForNYCusDetailParams;
import org.haier.shop.result.customer.QueryNYCusDetailResult;


public interface ManagerDao {

	/**
	 * 更新用户信用权限
	 * @param map
	 * @return
	 */
	Integer updateCusSet(Map<String,Object> map);
	/**
	 * 添加新的用户信用权限
	 * @param map
	 * @return
	 */
	Integer addNewCusSet(Map<String,Object> map);
	/**
	 * 查询会员的信用配置
	 * @param cusId
	 * @return
	 */
	Map<String,Object> queryCusSet(Integer cusId);
	/**
	 * 根据会员ID，查询对应的储值卡ID
	 * @param cusId
	 * @return
	 */
	Integer queryCusChuId(Integer cusId);
	/**
	 * 查询退款订单的金额
	 * @param rechargeId
	 * @return
	 */
	public Map<String,Object> queryRefundDetail(String rechargeId);
	/**
	 * 远见餐厅，查询退款成功信息
	 * @param map
	 * @return
	 */
	public Map<String,Object> queryCusYJRefundsStatis(Map<String,Object> map);

	public List<Map<String,Object>> queryYJCusRechargeList(Map<String,Object> map);
	/**
	 * 
	 * 查询店铺的默认APP支付信息
	 * @param shopUnique 店铺编号
	 * @param map
	 * @return
	 */
	public Map<String,Object> queryShopPayMsg(Map<String,Object> map);
	/**
	 * 根据rechargeId查询本次充值的金额
	 * @param rechargeId 充值申请ID
	 * @param map
	 * @return
	 */
	public Double queryCusRechargeDetail(Map<String,Object> map);
	/**
	 * 退款成功或者失败后，修改对应的退款申请信息
	 * @param rechargeId 充值申请ID
	 * @param rechargeStatus 1：退款成功；2：待退款；3：驳回；0：退款失败
	 * @param rechargeMethod 1：现金；2：微信；3：支付宝；4：银行卡；5：储值卡；7：小程序或者公众号；
	 * @param map
	 * @return
	 */
	public Integer modifyRefundMsg(Map<String,Object> map);
	
	/**
	 * 
	 * @param rechargeId 本次退款申请的申请ID
	 * @param rechargeStatus 本次操作的结果：1、成功，扣除待提现金额；3：驳回，将提现金额增加回余额；其他：不做处理
	 * @param map
	 * @return
	 */
	public Integer returnWithdrawal(Map<String,Object> map);
	/**
	 * 根据退款记录，查询是否有小程序充值的记录，并限制为1条，仅允许最后一次充值退款，如果超过这个金额，走线下
	 * @param map
	 * @return
	 */
	public Map<String,Object> querySmallRechargeMsg(Map<String,Object> map);
	/**
	 * 会员充值记录查询
	 * @param map
	 * @return
	 */
	public List<Map<String,Object>> queryCusRefundList(Map<String,Object> map);
	
	/**
	 * 会员充值记录查询，数量查询
	 * @param map
	 * @return
	 */
	public Integer queryCusRefundListCount(Map<String,Object> map);
	
	public List<Map<String,Object>> queryCusRechargeGroupYJ(Map<String,Object> map);
	/**
	 * 禁止，启用会员提现功能
	 * @param map
	 * @return
	 */
	public Integer modifyCusMsg(Map<String,Object> map);
	/**
	 * 修改会员余额信息
	 * @param map
	 * @return
	 */
	public Integer modifyCusBalance(Map<String,Object> map);
	/**
	 * 添加会员充值记录
	 * @param map
	 * @return
	 */
	public Integer addNewRechargeRecord(Map<String,Object> map);
	/**
	 * 各个员工每天的销售统计
	 * @param map
	 * @return
	 */
	public List<CustomerSummaryCYByGroup> customerSummaryCYByGroup(Map<String,Object> map);
	/**
	 * 各个员工每天的销售统计数量
	 * @param map
	 * @return
	 */
	public Integer customerSummaryCYByGroupCount(Map<String,Object> map);
	
	/**
	 * 添加充值记录
	 * @param map
	 * @return
	 */
	public Integer addCusRechargeMsg(Map<String,Object> map);
	/**
	 * 添加充值使用记录
	 * @param map
	 * @return
	 */
	public Integer addCusRechargeUseMsg(Map<String,Object> map);
	/**
	 * 清空积分，添加积分使用记录；
	 * 添加赠送金额，添加充值累加金额
	 * @param map
	 * @return
	 */
	public Integer clearCusPoints(Map<String,Object> map);
	
	/**
	 * 获取满足条件的会员信息
	 * @param map
	 * @return
	 */
	public List<Map<String,Object>> downLoadRedeenPointsDetail(Map<String,Object> map);
	/**
	 * 查询指定时间内的充值记录
	 * @param rmap
	 * @return
	 */
	public List<Map<String,Object>> queryOnlinePlatRechargeListDetail(Map<String,Object> rmap);
	/**
	 * 查询指定时间内的充值记录
	 * @param rmap
	 * @return
	 */
	public List<Map<String,Object>> queryOnlineCusRechargeList(Map<String,Object> rmap);
	
	public Integer addNewPlatCurRecharge(Map<String,Object> rmap);
	/**
	 * 查询本月是否有充值记录
	 * @return
	 */
	public List<Map<String,Object>> queryPlatcusRechargeMsg(Map<String,Object> map);
	/**
	 * 依次添加会员充值记录
	 * @param list
	 * @return
	 */
	public Integer addCusRechargeRecord(List<Map<String,Object>> list);
	/**
	 * 依次更新会员余额信息
	 * @param list
	 * @return
	 */
	public Integer cusRechargept(List<Map<String,Object>> list);
	/**
	 * 根据会员编号查询会员信息
	 * @param cusAccountList
	 * @return
	 */
	public List<Map<String,Object>> queryOnlineCusMsgByCusAccount(List<String> cusAccountList);
	public Integer modifyLotterySet(NingyuLottery lottery);
	public Integer modifyLotteryMoney(List<NingyuLotteryList> list);
	public Map<String,Object> getLotterySet();
	public List<Map<String,Object>> queryNYLotteryMsg();
	public List<Map<String,Object>> queryCusConsumptionListret(List<String> list);
	public Map<String,Object> queryCusConsumptionListCountret(Map<String,Object> map);
	public Integer customerSummaryByGroupCount(Map<String,Object> map);
	public List<CustomerSummaryByGroup> customerSummaryByGroupForShop(Map<String,Object> map);
	public List<CustomerSummaryByGroup> customerSummaryByGroupForDate(Map<String,Object> map);
	public Map<String,Object> queryLastCusbalance(Map<String,Object> map);
	public Map<String,Object> queryNowCusbalance(Map<String,Object> map);
	public Integer queryCusRechargeStaticByShopCount(Map<String,Object> map);
	public List<Map<String,Object>> queryCusRechargeStaticByShop(Map<String,Object> map);
	public List<Map<String,Object>> queryCusRechargeGroup(Map<String,Object> map);
	public Map<String,Object> queryCusRefundsStatic(Map<String,Object> map);
	public Map<String,Object> queryCusRechargeStatic(Map<String,Object> map);
	/**
	 * 查询各种支付方式的收入汇总
	 * @param map
	 * @return
	 */
	public List<Map<String,Object>> queryCusRechargeStatisByMethod(Map<String,Object> map);
	
	/**
	 * 会员充值记录查询
	 * @param map
	 * @return
	 */
	public List<Map<String,Object>> queryCusRechargeList(Map<String,Object> map);
	public Map<String,Object> queryCusRechargeListCount(Map<String,Object> map);
	public List<Map<String,Object>> queryWJCusCheckOut(Map<String,Object> map);
	public Integer queryWJCusCheckOutCount (Map<String,Object> map);
	
	public Integer queryCusSaleMsgByMonthCount(Map<String,Object> map);
	public List<Map<String,Object>> queryCusSaleMsgByMonth(Map<String,Object> map);
	public List<Map<String,Object>> queryCusConsumptionList(Map<String,Object> map);
	public Map<String,Object> queryCusConsumptionListCount(Map<String,Object> map);
	/**
	 * 登录查询
	 * @param map
	 * @return
	 */
	public List<Map<String, Object>> login(Map<String,Object> map);
	/**
	 * 添加新的管理员
	 * @param map
	 * @return
	 */
	public int addNewManager(Map<String,Object> map);
	/**
	 * 查询管理员旗下所有店铺的机器编号
	 * @param map
	 * @return
	 */
	public List<Shops> queryMachineNums(Map<String,Object> map);
	/**
	 * 查询店内所有商品信息
	 * @param map
	 * @return
	 */
	public List<Map<String,Object >>queryShopsGoods(Map<String,Object> map);
	/**
	 * 查询店铺
	 * @param map
	 * @return
	 */
	public Integer queryGoodsPages(Map<String,Object> map);
	
	/**
	 * 查询管理员管理的所有店铺列表信息
	 * @param map
	 * @return
	 */
	public List<Map<String,Object>>  queryShops(Map<String,Object> map);
	/**
	 * 查询管理员旗下所有商品数量
	 * @param map
	 * @return
	 */
	public Integer queryManagerPages(Map<String,Object> map);
	/**
	 * 查询管理员旗下所有商品
	 * @param map
	 * @return
	 */
	public List<Map<String,Object>> queryManagerGoods(Map<String,Object> map);
	/**
	 * 查询商品订单信息
	 * @param map
	 * @return
	 */
	public List<Order> queryOrderLists(Map<String,Object> map);
	/**
	 * 查询店铺或管理员旗下店铺订单数量
	 * @param map
	 * @return
	 */
	public int queryOrderCount(Map<String,Object> map);
	
	/**
	 * 商品查询页数
	 * @param map
	 * @return
	 */
	
	public int queryGoodsPageCounts(Map<String,Object> map);
	/**
	 * 商品分页查询
	 * @param map
	 * @return
	 */
	public List<Map<String,Object>> queryGoodsByPage(Map<String,Object> map);
	/**
	 * 会员信息分页查询
	 * @param map
	 * @return
	 */
	public List<Map<String,Object>> queryCusCheckOut(Map<String,Object> map);
	/**
	 * 会员信息总数量查询
	 * @param map
	 * @return
	 */
	public int cusCheckOutPages(Map<String,Object> map);
	/**
	 * 会员详情查询
	 * @param map
	 * @return
	 */
	public Map<String,Object> cusDetailMessageQuery(Map<String,Object> map);
	
	public Map<String,Object> getMemberLevelById(Map<String,Object> map);
	public QueryNYCusDetailResult queryNYCusDetail(RechargeForNYCusDetailParams params);
	/**
	 * 查询会员详情..
	 * @param map
	 * @return
	 */
	public Map<String,Object> queryCusDetail(Map<String,Object> map);
	/**
	 * 更新会员信息
	 * @param map
	 * @return
	 */
	public int saveCusMessage(Map<String,Object> map);
	
	public int updateMemberLevel(Map<String,Object> map);
	/**
	 * 会员充值记录查询/取现记录
	 * @param map
	 * @return
	 */
	public List<Map<String,Object>> queryRechargeRecord(Map<String,Object> map);
	
	
	public List<Map<String,Object>> queryBuyRecord(Map<String,Object> map);
	
	public int queryBuyRecordCount(Map<String,Object> map);
	
	/**
	 * 会员充值/取现记录页数查询
	 * @param map
	 * @return
	 */
	public int rechargeRecordPages(Map<String,Object> map);
	
	/**
	 * 会员消费记录查询（分页）
	 * @param map
	 * @return
	 */
	public List<Map<String,Object>> queryComsumptionRecord(Map<String,Object> map);
	/**
	 * 会员消费记录查询
	 * @param map
	 * @return
	 */
	public Integer queryComsumptionRecordPages(Map<String,Object> map);
	
	/**
	 * 添加新的会员信息
	 * @param map
	 * @return
	 */
	public int addNewCus(Map<String,Object> map);
	/**
	 * 会员充值
	 * @param map
	 * @return
	 */
	public int cusRecharge(Map<String,Object> map);
	/**
	 * 添加会员充值记录
	 * @param map
	 * @return
	 */
	public int newRechareRecord(Map<String,Object> map); 
	/**
	 * 会员余额查询
	 * @param map
	 * @return
	 */
	public Double getCusMoney(Map<String,Object> map);
	/**
	 * 取现减少会员余额
	 * @param map
	 * @return
	 */
	public int sureTakeNow(Map<String,Object> map);
	
	/**
	 * 查询管理员所有店铺的员工
	 * @param map
	 * @return
	 */
	public List<Map<String,Object>> queryManagerStaffs(Map<String,Object> map);
	/**
	 * 店铺会员等级信息查询
	 * @param map
	 * @return
	 */
	public List<Map<String,Object>> queryCusLevel(Map<String,Object> map);
	/**
	 * 备份将被删除的会员信息
	 * @param map
	 * @return
	 */
	public int copyDeleteCusMessage(Map<String,Object> map);
	/**
	 * 删除需要删除的用户数据
	 * @param map
	 * @return
	 */
	public int deleteShopCus(Map<String,Object> map);
	
	//获取平台会员信息
	public Map<String ,Object> platformCustomerDetail(String cus_unique);
	
	//平台会员充值记录总条数
	public int rechargeRecordPlatformPages(Map<String, Object> map);
	//平台会员充值记录查询
	public List<Map<String, Object>> queryRechargeRecordPlatform(Map<String, Object> map);
	
	public int queryComsumptionRecordPlatformPages(Map<String, Object> map);
	public List<Map<String, Object>> queryComsumptionRecordPlatform(Map<String, Object> map);
	
	/**
	 * 删除店铺内的会员信息（设置为不可用状态）
	 * @param map
	 * @return
	 */
	public int deleteCustomer(Map<String,Object> map);
	
	/**
	 * 店内会员信息查询，下载
	 * @param map
	 * @return
	 */
	public List<Map<String,Object>> getCustomerMsgForDown(Map<String,Object> map);
	
	/**
	 * 会员等级
	 */
	public List<Map<String,Object>> getMemberLevel(Map<String,Object> map);
	/**
	 * 线上会员
	 */
	List<Map<String,Object>>  queryCusOnline(Map<String,Object> map);
	
	int queryCusOnlinePages(Map<String,Object> map);
	
	List<Map<String,Object>>  queryCusOnlinePt(Map<String,Object> map);
	
	int queryCusOnlinePagesPt(Map<String,Object> map);
	
	public List<Map<String,Object>> queryComsumptionRecord2(Map<String,Object> map);

	public Integer queryComsumptionRecordPages2(Map<String,Object> map);
	
	public Map<String, Object> queryCusLifeCycle(int id);
	
	public int updateCusLifeCycle(Map<String,Object> map);
	
	public int updateCusActivity(Map<String,Object> map);
	
	public Map<String, Object> queryCusConfig();
	public List<Map<String, Object>> queryCusRenewList(Map<String, Object> params);
	public Integer queryCusRenewListPageCount(Map<String, Object> params);
	
	public List<Map<String,Object>> queryBackRecord(Map<String,Object> map);
	
	public int queryBackRecordCount(Map<String,Object> map);
	
	public int auditWJCusCheckOut(Map<String,Object> map);
	
	public int updateWJCusCheckOut(Map<String,Object> map);
	public List<Map<String, Object>> queryCusPointHistory(Map<String, Object> map);
	public Integer queryCusPointHistoryCount(Map<String, Object> map);
	public Map<String, Object> queryPointClear(Map<String, Object> map);
	public void updatePointClearConfig(Map<String, Object> map);
	public void savePointClearConfig(Map<String, Object> map);
	public List<Map<String, Object>> queryClearCusPointShop();
	public List<Map<String, Object>> queryCusList(Map<String, Object> map);
	public void savePointClearLog(Map<String, Object> cus);
	public void updateClearPoint(Map<String, Object> map);
	
	
}
