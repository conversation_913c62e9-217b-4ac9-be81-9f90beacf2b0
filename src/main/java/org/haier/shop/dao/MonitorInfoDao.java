package org.haier.shop.dao;


import java.util.List;
import java.util.Map;

public interface MonitorInfoDao {
	
	/**
	 * 查询6个摄像头，用于展示用
	 * @param map
	 * @return
	 */
	List<Map<String,Object>> queryMonitorList(Map<String,Object> map);
	
    List<Map<String, Object>> queryUnicomMonitorList(Map<String, Object> params);

    Integer queryUnicomMonitorListCount(Map<String, Object> params);

    Integer addMonitorInfo(Map<String, Object> params);
   Map<String ,Object> selectMonitorInfoByMonitorInfoId(Long monitorInfoId);

    void updateMonitorInfo(Map<String, Object> params);

}
