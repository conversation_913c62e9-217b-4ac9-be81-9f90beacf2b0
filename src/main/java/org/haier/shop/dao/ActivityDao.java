package org.haier.shop.dao;

import java.util.List;
import java.util.Map;

import org.haier.shop.entity.beans.PageData;
import org.haier.shop.entity.recharge.RechargeOffline;

public interface ActivityDao {
	/**
	 * 查询该店铺相同管理员的店铺
	 * @param map
	 * @return
	 */
	public List<Map<String,Object>> selectShopsList(Map<String,Object> map);
	public Integer deleteRechargeOffline(Long recharge_config_id);
	/**
	 * 查询重新信息详情
	 * @param recharge_config_id
	 * @return
	 */
	public RechargeOffline queryOilRechargeConfigDetail(Long recharge_config_id);
	/**
	 * 查询店铺的充值配置列表数量
	 * @param map
	 * @return
	 */
	public Integer queryOilRechargeConfigListCount(Map<String,Object> map);
	/**
	 * 查询店铺的充电配置列表
	 * @param map
	 * @return
	 */
	public List<Map<String,Object>> queryOilRechargeConfigList(Map<String,Object> map);
	/**
	 * 添加线下会员领取记录
	 * @param map
	 * @return
	 */
	public Integer addNewShopCouponCusOffline(Map<String,Object> map);
	
	public Integer addShopCouponEffective(List<Map<String,Object>> list);
	/**
	 * 新增线上商品促销时，查询促销信息，防止重复设置；
	 * @param map
	 * @return
	 */
	public Integer getTimeGoodsPromotionOnline(Map<String,Object> map); 

	public List<Map<String, Object>> queryPromotionList(Map<String, Object> map);
	
	public Integer queryPromotionListCount(Map<String, Object> map);

	public List<Map<String, Object>> queryGoodsByPage(Map<String, Object> map);

	public int submitSupplierStorageOrder(Map<String, Object> map);

	public Integer getTimeCoincideGoodsMarkdown(Map<String ,Object> params);
	
	public void addPromotionGoodsMarkdown(List<Map<String ,Object>> list);

	public Integer getTimeCoincideGoodsGift(Map<String ,Object> params);
	
	public void addGoodsGift(List<Map<String ,Object>> list);
	
	public Integer getTimeCoincideOrderMarkdown(Map<String ,Object> params);

	public void addOrderMarkdown(Map<String, Object> map3);

	public void deleteActivity(Map<String, Object> map);
	
	public void updateActivityStatus(Map<String, Object> map);

	public void deleteGoodsMarkdown(Map<String, Object> map);

	public void deleteGoodsGift(Map<String, Object> map);

	public void deleteOrderMarkdown(Map<String, Object> map);

	public List<Map<String, Object>> queryGoodsMarkdownDetail(Map<String, Object> map);

	public List<Map<String, Object>> queryGoodsGiftDetail(Map<String, Object> map);

	public List<Map<String, Object>> queryOrderMarkdownDetail(Map<String, Object> map);

	public int submitGoodsKindPoint(Map<String, Object> map);

	public int submitGoodsPoint(Map<String, Object> map);

	public int submitGoodsKindCommission(Map<String, Object> map);

	public int submitGoodsCommission(Map<String, Object> map3);

	public Map<String, Object> queryGoodsCount(Map<String, Object> map);

	public Map<String, Object> queryGoodsKindPointsAndCommissionCount(Map<String, Object> map);

	public List<Map<String, Object>> queryGoodsKindPointsAndCommissionByPage(Map<String, Object> map);

	public Integer getTimeCoincideGoodsPromotion(Map<String ,Object> params);
	
	public void submitSingleGoodsPromotion(Map<String, Object> map3);
	
	//添加单品促销（新）
	public Integer addPromotionGoodsSingle(List<Map<String ,Object>> list);

	public List<Map<String, Object>> querySingleGoodsPromotionDetail(Map<String, Object> map);
	
	public List<Map<String, Object>> queryFlashSaleDetail(Map<String, Object> map);

	public void deleteSingleGoodsMarkdown(Map<String, Object> map);

	public Map<String, Object> queryShopCouponListCount(Map<String, Object> map);

	public List<Map<String, Object>> queryShopCouponList(Map<String, Object> map);

	public int addShopCoupon(Map<String, Object> params);
	
	public List<Map<String ,Object>> getCusList();//获取所有会员列表
	
	public Integer addShopCouponCusList(List<Map<String ,Object>> list);//添加优惠券与会员关联表
	
	public int updateShopCoupon(Map<String, Object> params);
	
	public void addShopCouponTime(Map<String, Object> params);
	
	public void deleteShopCouponTime(String shop_coupon_id);

	public void deleteShopCouponEffective(String shop_coupon_id);

	public void deleteShopCoupon(Map<String, Object> params);
	
	public Map<String ,Object> getShopCoupon(String shop_coupon_id);
	
	public List<Map<String ,Object>> getShopCouponTimes(String shop_coupon_id);

	public List<Map<String ,Object>> getShopCouponEffective(String shop_coupon_id);

	public Map<String, Object> queryRechargeConfigListCount(Map<String, Object> map);

	public List<Map<String, Object>> queryRechargeConfigList(Map<String, Object> map);

	public void addRechargeConfig(Map<String, Object> params);
	
	public void updateRechargeConfig(Map<String ,Object> params);
	
	public Map<String ,Object> getRechargeConfig(String platform_recharge_config_id);

	public void deleteRechargeConfig(Map<String, Object> map);

	public void setPointUse(Map<String, Object> map);

	public Map<String, Object> querySetPointUse(Map<String, Object> map);

	public void updateSetPointUse(Map<String, Object> map);

	public List<Map<String, Object>> queryClassThemeList(Map<String, Object> map);

	public Map<String, Object> queryClassThemeListCount(Map<String, Object> map);

	public Map<String, Object> queryClassThemeById(Map<String, Object> map);

	public int editClassTheme(Map<String, Object> map);

	public List<Map<String, Object>> queryClassThemeGoodsList(Map<String, Object> map);

	public void deleteClassThemeGoodsByGoodsBarcode(Map<String, Object> map);

	public void addClassThemeGoodsList(Map<String, Object> map);

	public Integer queryPlatformCusListCount(Map<String, Object> map);

	public List<Map<String, Object>> queryPlatformCusList(Map<String, Object> map);
	
	public List<Map<String, Object>> queryBootImg();

	public int updateBootImg(Map<String, Object> map);
	
	
	public List<Map<String, Object>> queryRewardList(Map<String, Object> map);

	public int queryRewardListCount(Map<String, Object> map);
	
	public List<PageData> queryRewardRepeat(List<PageData> data2);
	
	public int addRewardRepeat(List<PageData> data2);
	
	public int deleteReward(String id);
	
	public int updateReward(Map<String, Object> map);
	
	public int editReward(Map<String, Object> map);
	
	public Integer queryCouponOffRecordListCount(Map<String, Object> map);
	
	public List<Map<String, Object>> queryCouponOffRecordList(Map<String, Object> map);
	
	public Integer queryCouponRecordListCount(Map<String, Object> map);

	public List<Map<String, Object>> queryCouponRecordList(Map<String, Object> map);
	
	/**
	 * 搜索商品信息
	 * @param map
	 * @return
	 */
	public List<Map<String,Object>> queryGoodsMsgList(Map<String,Object> map);

	public Map<String, Object> queryGoodsKindCount(Map<String, Object> map);

	public List<Map<String, Object>> queryGoodsKindByPage(Map<String, Object> map);
	
	public List<Map<String, Object>> queryEshowTVList(Map<String, Object> map);

	public int queryEshowTVListCount(Map<String, Object> map);
	
	public List<Map<String, Object>> queryCoudflashGoods(Map<String, Object> map);

	public int queryCoudGoodsCount(Map<String, Object> map);
	
	public void addCoudflashGoods(List<Map<String ,Object>> list);
	
	public int updateYSFStatus(Map<String, Object> map);

	public List<Map<String, Object>> queryFoodManagerList(Map<String, Object> map);

	public Integer queryFoodManagerListCount(Map<String, Object> map);

	public int addFoodManager(Map<String, Object> map);

	public Map<String, Object> queryFoodManager(Map<String, Object> map);

	public void editFoodManager(Map<String, Object> map);

	public void deleteFoodManager(Map<String, Object> map);

	public List<Map<String, Object>> queryNewsManagerList(Map<String, Object> map);

	public Integer queryNewsManagerListCount(Map<String, Object> map);

	public void addNewsManager(Map<String, Object> map);

	public Map<String, Object> queryNewsManager(Map<String, Object> map);

	public void editNewsManager(Map<String, Object> map);

	public void deleteNewsManager(Map<String, Object> map);

	public List<Map<String, Object>> queryReortConfigList(Map<String, Object> map);

	public Integer queryReortConfigListCount(Map<String, Object> map);

	public void addReportConfig(Map<String, Object> map);

	public Map<String, Object> queryReportConfig(Map<String, Object> map);

	public void editReportConfig(Map<String, Object> map);

	public void deleteReportConfig(Map<String, Object> map);

	public List<Map<String, Object>> queryServerEndReortList();

	public void updateReportStatus(Map<String, Object> map);

	public void addShopCouponGoods(Map<String, Object> goods);

	public void updateSingleShopCoupon(Map<String, Object> params);

	public void updateShopCouponGoods(Map<String, Object> goods);
	
	public int updateSupplierStorageOrder(Map<String, Object> map);
	
	public int updateSinglePromotionDetail(Map<String, Object> map);
	
}
