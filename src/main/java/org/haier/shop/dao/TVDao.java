package org.haier.shop.dao;


import java.util.List;
import java.util.Map;

import org.haier.util.eshow.AreaVO;
import org.haier.util.eshow.Downdefault;
import org.haier.util.eshow.TvModelVO;

public interface TVDao {
	
	public List<Map<String,Object>> queryAreaPalyList(Map<String,Object> map);
	
	public Integer addNewAdShowSourceList(Map<String,Object> map);
	
	public Integer addNewAdShowListMsg(Map<String,Object> map);
	
	public List<Map<String,Object>> queryPeopleList(Map<String,Object> map);
	
	public int queryPeopleListCount(Map<String,Object> map);

	public List<Map<String,Object>> querySourceList(Map<String,Object> map);
	
	public int querySourceListCount(Map<String,Object> map);

	public int updateRegistrationId(Map<String,Object> map);
	
	public int updateTVstatus();

	public List<Map<String, Object>> queryAllShops();

	public void addAdPeople(Map<String, Object> params);

	public Map<String, Object> queryAdPeopleDetail(Map<String, Object> params);

	public void updateAdPeople(Map<String, Object> params);

	public void updateAdPeopleInfo(Map<String, Object> params);
	
	public int deleteTV(String map);

	public int updateTV(Map<String,Object> map);
	
	public Map<String,Object> getTV(String map);

	public List<Map<String, Object>> queryShopAreaList(Map<String, Object> params);
	
	public int updateLockStatus(Map<String, Object> params);
	
	public Downdefault queryEshow();
	
	public int queryEshowList(String md5);
	
	public String getUploadRoute(String code);//获取图片上传访问路径
	
	public List<Map<String,Object>> queryTVList(Map<String,Object> map);
	
	public int queryTVListCount(Map<String,Object> map);
	
	public List<AreaVO> queryAreaList(Map<String,Object> map);
	
	public List<TvModelVO> queryModelList(Map<String,Object> map);
	
	public String queryAreaDict(AreaVO md5);
	
	public int queryArea(AreaVO md5);
	
	public int addArea(AreaVO md5);

	public void addTVSource(Map<String, Object> params);

	public int deleteTVSource(String id);

	public int updateLockStatusSource(Map<String, Object> params);
	
	public List<Map<String,Object>> queryPalyList(Map<String,Object> map);
	
	public int queryPalyCount(Map<String,Object> map);

	public List<Map<String, Object>> queryAllSource();

	public List<Map<String, Object>> queryAllAreaList();

	public List<Map<String, Object>> queryAllCityList();

	public void addAreaPlay(Map<String, Object> params);

	public void addAreaPlaySource(Map<String, Object> params);

	public void addAreaPlaySourceQuYu(Map<String, Object> area_params);

	public int updatePlayStatus(Map<String, Object> params);
	
	public int updateTVTiming(Map<String, Object> params);

	public int deleteAreaPlay(String id);

	public void deletePlaySuCai(String id);

	public void deletePlayQuYu(String id);

	public Map<String, Object> queryPlayInfo(Map<String, Object> params);

	public String[] queryPlaySourceList(Map<String, Object> params);

	public String[] queryPlayQuYuList(Map<String, Object> params);

	public void updateAreaPlay(Map<String, Object> params);

	public void addAreaCode(List<Map<String, Object>> list);

	public void updateAreaCount(Map<String, Object> params);

	public List<Map<String, String>> queryOnlineTV();

	public List<Map<String, Object>> queryNotAreaCodePhone();

	public void updateAreadCodeById(Map<String, Object> params);
	
}
