package org.haier.shop.dao;

import org.haier.shop.vo.CustomerRefundsVO;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
* @Description 退费记录
* @ClassName CustomerRefunds
* <AUTHOR> 
* @Date 2024-04-15
**/
public interface CustomerRefundsMapper  {

    List<CustomerRefundsVO> pageList(Map<String, Object> map);

    Integer pageListCount(Map<String, Object> map);

    /**
     * 退费总金额
     * @param map
     * @return
     */
    BigDecimal queryRefundTotalMoney(Map<String, Object> map);

    List<CustomerRefundsVO> selectList(Map<String, Object> paramsMap);
}