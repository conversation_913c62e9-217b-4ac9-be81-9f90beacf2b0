package org.haier.shop.dao;

import java.util.List;
import java.util.Map;

public interface GoodsDictDao {
	/**
	 * 根据商品条码，查询商品信息
	 * @param map
	 * @return
	 */
	public Map<String,Object> queryGoodsDictMessage(Map<String,Object> map);
	/**
	 * 添加新商品
	 * @param map
	 * @return
	 */
	public int addNewGoodsDict(Map<String,Object> map);
	/**
	 * 更新已有商品信息
	 * @param map
	 * @return
	 */
	public int updateGoodsDictMsg(Map<String,Object> map);
	/**
	 * 更新字典数据库中的商品信息
	 * @param map
	 * @return
	 */
	public int moifyGoodsDictMessage(Map<String,Object> map);
	
	/**
	 * 添加新的商品字典信息
	 * @param map
	 * @return
	 */
	public int newGoodsDict(Map<String,Object> map);
	
	public List<Map<String,Object>> getGoodsDictList(Map<String,Object> map);
	
	public int getGoodsDictListCount(Map<String,Object> map);
	
	public int queryGoodsDictCunZai(Map<String,Object> map);
	
}
