package org.haier.shop.dao;

import java.util.List;
import java.util.Map;

public interface UnicomAbleDao {
	
	/**
	 * 查询能人列表
	 * @return
	 */
	public List<Map<String ,Object>> queryUnicomAbleList(Map<String,Object> params);
	
	/**
	 * 查询能人列表总条数
	 * @return
	 */
	public Integer queryUnicomAbleListCount(Map<String,Object> params);
	
	/**
	 * 添加能人
	 * @return
	 */
	public Integer addUnicomAble(Map<String,Object> params);
	
	/**
	 * 获取能人详情
	 * @return
	 */
	public Map<String ,Object> queryUnicomAbleDetail(Map<String,Object> params);
	
	/**
	 * 修改能人
	 * @return
	 */
	public Integer updateUnicomAble(Map<String,Object> params);
	
	/**
	 * 删除能人
	 * @return
	 */
	public Integer deleteUnicomAble(String unicom_able_id);
	
	/**
	 * 查询能人签单列表
	 * @return
	 */
	public List<Map<String ,Object>> queryUnicomAbleEsignList(Map<String,Object> params);
	
	/**
	 * 查询能人签单列表总条数
	 * @return
	 */
	public Integer queryUnicomAbleEsignListCount(Map<String,Object> params);
	
	/**
	 * 获取能人签单记录详情
	 * @return
	 */
	public Map<String ,Object> queryUnicomAbleEsign(String esign_num);
	
	/**
	 * 添加能人签单记录
	 * @return
	 */
	public Integer addUnicomAbleEsign(Map<String,Object> params);
	
	/**
	 * 修改能人签单记录
	 * @return
	 */
	public Integer updateUnicomAbleEsign(Map<String,Object> params);
	
	/**
	 * 获取电子合同信息
	 * @return
	 */
	public Map<String ,Object> queryUnicomAbleContract();
}
