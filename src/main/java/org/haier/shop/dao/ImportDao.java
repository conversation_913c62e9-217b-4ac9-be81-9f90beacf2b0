package org.haier.shop.dao;

import java.util.List;
import java.util.Map;

import org.haier.shop.entity.CustomerForImport;
import org.haier.shop.entity.GoodsForImport;
import org.haier.shop.entity.KindForImport;
import org.haier.shop.entity.SaleListDetail;
import org.haier.shop.entity.SaleListMain;
import org.haier.shop.entity.importMsg.ImportSupplierGoods;

public interface ImportDao {

	/**
	 * 查询商品列表信息
	 * @param map
	 * @return
	 */
	List<Map<String, Object>> queryGoodsMsg(Map<String, Object> map);
	public Integer updateGoodsSupplier(ImportSupplierGoods importSupplierGoods);
	/**
	 * 添加新的会员充值记录，并返回充值ID号
	 * @return
	 */
	public Integer addNewCustomerRecharge(Map<String,Object> map);
	/**
	 * 添加新的充值使用记录
	 * @return
	 */
	public Integer addNewCustomerRechargeUse(Map<String,Object> map);
	
	public Integer importNewGoodsKind(List<KindForImport> list);
	/**
	 * 去掉数据库中已存在的数据，防止插入时失败
	 */
	public Integer screenGoods(Map<String,Object> map);
	/**
	 * 将excel表中的数据导入数据库
	 * @param list
	 * @return
	 */
	public Integer importGoods(List<GoodsForImport> list);
	
	/**
	 * 将云库中不存在的商品信息导入云库中
	 * @param map
	 * @return
	 */
	public Integer addNewCloudGoods(Map<String,Object> map);
	
	/**
	 * 由EXCEL表导入数据后，查询店铺商品信息在大库中的外键信息，用于规格关联更新
	 * @return
	 */
	public List<Map<String,Object>> queryGoodsNewForeignKey(Map<String,Object> map);
	
	/**
	 * 依次将店内所有的商品foreignKey及goodsContain更新为合理的值 
	 * @param list
	 * @return
	 */
	public Integer updateNewShopsGoodsMessage(Map<String,Object> map);
	/**
	 * 查询会员等级信息
	 * @param map
	 * @return
	 */
	public Map<String,Object> queryCusLevelIdInShops(Map<String,Object> map);
	
	/**
	 * 导入会员信息前，删除已有的会员信息
	 * @param map
	 * @return
	 */
	public Integer screenCustomer(Map<String,Object> map);
	/**
	 * 导入新的会员信息
	 * @param map
	 * @return
	 */
	public Integer importCustomers(CustomerForImport customerForImport);
	
	/**
	 * 查询店铺的分类使用情况，用于导入时添加商品默认分类
	 * @param shopUnique
	 * @return
	 */
	public Integer queryShopKindType(String shopUnique);
	
	/**
	 * 添加充值消费记录
	 * @param params
	 * @return
	 */
	public Integer insertCustomerRecharge(Map<String ,Object> params);
	
	/**
	 * 添加订单信息
	 * @param list
	 * @return
	 */
	public Integer addNewSaleList(List<SaleListMain> list);
	
	/**
	 * 添加订单详情信息
	 * @param list
	 * @return
	 */
	public Integer addNewSaleListDetail(List<SaleListDetail> list);
}
