package org.haier.shop.dao;

import java.util.List;
import java.util.Map;

/**
 * 数据可视化大屏信息查询
 * <AUTHOR>
 *
 */
public interface DataSearchDao {

	/**
	 * 查询到店人数
	 */
	public Map<String,Object> peopleArrivingSearch(Map<String,Object> map);
	/**
	 * 各状态在线店铺数量
	 * @return
	 */
	public List<Map<String, Object>> shopOnLineStatis();
	/**
	 * 每日明星店铺查询
	 * @return
	 */
	public List<Map<String,Object>> theBestSallerShop();
	
	/**
	 * 各收款额阶段数量查询
	 * @return
	 */
	public List<Map<String,Object>> getListIncomeStage();
	
	/**
	 * 近一月商品价格走势图
	 * @return
	 */
	public List<Map<String,Object>> goodsPriceTrend(Map<String,Object> map);
	/**
	 * 获取猪肉近三月的价格
	 * @param map
	 * @return
	 */
	public Double getGoodsAverPrice();
	
	/**
	 * 近三个月猪肉的价格变化
	 * @param map
	 * @return
	 */
	public List<Map<String,Double>> getPriceFloat(Map<String,Object> map);
	
	/**
	 * 获取指定商品近一个月的价格变化趋势
	 * @param map
	 * @return
	 */
	public List<Map<String,Object>> getPriceDiff(Map<String,Object> map);
	
	/**
	 * 当日销售额总计及人均可单值统计
	 * @return
	 */
	public Map<String,Object> queryTotalStatistics();
	
	/**
	 * 统计各分类销售数量
	 * @return
	 */
	public List<Map<String,Object>> kindSaleRatio();
	
	/**
	 *  商品销售各区间数量 
	 */
	public List<Map<String,Object>> goodSalePriceCount();
	
	public List<Map<String,Object>> getOrderList();
	
	public List<Map<String,Object>> getShopList();
	
	public List<Map<String,Object>> getRiderList();
	
	/**
	 * gezhifu 
	 */
	public List<Map<String,Object>> payTypeMessage();
	
	/**
	 * 小程序在线用户量
	 * @return
	 */
	public List<Map<String,Object>> queryOnlineUserCount();
	
	/**
	 * 未来一小时的订单量
	 * @return
	 */
	public List<Map<String,Object>> queryOnlineUserCountNew();
	
	/**
	 * 过去一周线上线下订单量对比
	 * @return
	 */
	public List<Map<String,Object>> queryLineCount();
	
	/**
	 * 查询店铺电话信息
	 * @param type
	 * @return
	 */
	public List<Map<String,Object>> getShopListMsg(Map<String,Object> type);
	
	/**
	 * 更新店铺的电话信息
	 * @param list
	 * @return
	 */
	public int updateShopMsg(List<Map<String,Object>> list);
	
	/**
	 * 更新用户姓名
	 * @param list
	 * @return
	 */
	public int updateShopName(List<Map<String,Object>> list);
}
