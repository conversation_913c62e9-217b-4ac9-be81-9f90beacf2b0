package org.haier.shop.dao;

import java.util.List;
import java.util.Map;

public interface LogisticsDao {
	
	public List<Map<String,Object>> getSexList();
		
	public List<Map<String,Object>> getAgeList();
	
	public List<Map<String,Object>> getPeiSongList();
	
	public Map<String,Object> getOneMinute();
	
	public List<Map<String,Object>> getPeiSongTypeList();
	
	public List<Map<String,Object>> getNowPeisongCount();
	
	public String[] getDateList();
	
	public String[] getOrderList(Map<String,Object> params);
		
	public int getNowCount();
	
	public int getYesterdayCount();
	
	public int insertOrder(Map<String,Object> params);
	
	public int updateOrder(Map<String,Object> params);
	
	public int updateOneMinute(Map<String,Object> params);
	
	public int updateOneMinuteZero();
	
	public int updatePeisongType(Map<String,Object> params);
	
	public int updatePeisongTimeDistance(Map<String,Object> params);
	
	public Map<String,Object> getMonthActivity();
}
