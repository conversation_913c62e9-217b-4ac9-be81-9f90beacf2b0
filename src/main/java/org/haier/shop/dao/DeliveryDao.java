package org.haier.shop.dao;

import java.util.List;
import java.util.Map;

import org.haier.shop.entity.ShopCourier;

public interface DeliveryDao {
	
	//获取店铺配送信息
	public Map<String, Object> queryShopDelivery(String shop_unique);

	//修改店铺配送信息
	public int updateShopDelivery(Map<String ,Object> params);
	
	//获取店铺骑手列表
	public List<ShopCourier> getShopCourierList(ShopCourier shopCourier);
	
	//获取店铺骑手列表总条数
	public int getShopCourierListCount(ShopCourier shopCourier);
	
	//添加店铺骑手信息
	public int addShopCourier(ShopCourier shopCourier);
	
	//获取店铺骑手详情
	public ShopCourier getShopCourier(String courier_id);
	
	//修改店铺骑手信息
	public int updateShopCourier(ShopCourier shopCourier);
	
	//删除店铺骑手
	public int deleteShopCourier(String courier_id);
	
	//获取店铺商品列表
	public List<Map<String ,Object>> getShopSpecialList(Map<String ,Object> params);
		
	//获取店铺商品列表总条数
	public int getShopSpecialListCount(Map<String ,Object> params);
		
	//修改店铺特殊商品标记
	public int updateShopSpecial(Map<String ,Object> params);
	
}
