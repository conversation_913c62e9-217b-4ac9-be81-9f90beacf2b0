package org.haier.shop.dao;

import java.util.List;
import java.util.Map;

import org.apache.ibatis.annotations.Param;
import org.haier.shop.entity.AllKindsInShop;
import org.haier.shop.entity.GoodsGroups;

public interface Goods_kindDao {
	/**
	 * 
	 * 商品分类查询
	 * @param shop_unique
	 * @param goods_kind_parunique
	 * @return
	 */
	public List<Map<String,Object>> queryGoodsKind(Map<String,Object> map);
	
	/**
	 * 商品查询（包含子类）
	 * @param map
	 * @return
	 */
	public List<GoodsGroups> queryGoodsGroups(Map<String,Object> map);
	/**
	 * 删除已有店铺分类信息
	 * @param map
	 * @return
	 */
	public int deleteGoodsKinds(Map<String,Object> map);
	
	public int deleteGoodsKind(Map<String,Object> map);
	/**
	 * 将新选中的商品信息添加
	 * @param map
	 * @return
	 */
	public int addGoodsKinds(Map<String,Object> map);
	/**
	 * 新店铺添加时，添加商品分类信息
	 * @param shop_unique
	 * @return
	 */
	public int addNewGoodsKinds(String shop_unique);
	
	public int addNewGoodsKindsWJ(String shop_unique);
	
	/**
	 * 添加新的商品分类信息申请
	 * @param map
	 * @return
	 */
	public int sureNewKind(Map<String,Object> map);
	
	public List<Map<String, Object>> queryGoodsKindsByGoodsKindUnique(Map<String, Object> map);
	
	public int addNewGoodsKind(Map<String, Object> params);
	
	public List<Map<String, Object>> queryAllGoodsKinds(Map<String, Object> map);
	
	/**
	 * 查询店铺所有一二级分类信息
	 * @param map
	 * @return
	 */
	public List<AllKindsInShop> queryAllKindsInShops(Map<String, Object> map);
	
	/**
	 * 商品分类信息查询
	 * @param map
	 * @return
	 */
	public List<Map<String,Object>> queryGoodsKindsWithGoodsCount(Map<String,Object> map);
	
	/**
	 * 总页数查询
	 * @param map
	 * @return
	 */
	public Integer queryGoodsKindsCount(Map<String,Object> map);
	
	/**
	 * 更新分类名称
	 * @param map
	 * @return
	 */
	public Integer modifyGoodsKindMsg(Map<String,Object> map);
	
	/**
	 * 查询商品分类的子分类ID编号
	 * @param map
	 * @return
	 */
	public List<Map<String,Object>> queryKindMsg(Map<String,Object> map);
	
	/**
	 * 查询商品分类的副分类ID编号
	 * @param map
	 * @return
	 */
	public List<Map<String,Object>> queryGroupMsg(Map<String,Object> map);
	
	/**
	 * 批量更新分类信息
	 * @param list
	 * @return
	 */
	public Integer modifyGoodsKindMsgs(List<Map<String,Object>> list);
	
	/**
	 * 将商品分类信息修改为默认分类
	 * @param map
	 * @return
	 */
	public Integer modifyGoodsKindForKindDelete(Map<String,Object> map);
	
	/**
	 * 查询店铺一级分类信息
	 * @param map
	 * @return
	 */
	public List<Map<String,Object>> queryAllGroupMsgByType(Map<String,Object> map);
	
	/**
	 * 查询指定店铺和大分类情况下的情况下，某分类的最大编号
	 * @param map
	 * @return
	 */
	public Long queryNowMaxUnique(Map<String,Object> map);
	/**
	 * 添加新的+商品分类
	 * @param map
	 * @return
	 */
	public Integer addNewGoodsKindLay(Map<String,Object> map);
	
	/**
	 * 查询店铺当前使用的分类状态
	 * @param map
	 * @return
	 */
	public Integer getShopNowKindType(String shopUnique) ;
	
	public Integer rebackSystemKind(List<Map<String,Object>> list);
	/**
	 * 更新店铺分类信息
	 * @param map
	 * @return
	 */
	public Integer updateShopsMessage(Map<String,Object> map);
	
	/**
	 * 查询店铺内商品的默认分类信息
	 * @param map
	 * @return
	 */
	public List<Map<String,Object>> queryGoodsDictKind(Map<String,Object> map);
	
	/**
	 * 
	 * @param map
	 * @return
	 */
	public Integer useCustomeKind(Map<String,Object> map);
	
	public List<Map<String,Object>> getGoodskindList(Map<String,Object> map);
	
	public List<Map<String,Object>> getGoodskindList_food(Map<String,Object> map);
	
	public List<Map<String,Object>> getGoodskindTwo(Map<String,Object> map);

	public List<Map<String, Object>> getGoodskindListByYN(Map<String, Object> params);

	public List<Map<String, Object>> getGoodskindTwoByYN(Map<String, Object> map);
	
	public List<Map<String,Object>> queryMqttKind(Map<String,Object> map);

	/**
	 * 根据商铺标识和类型分类编号查询
	 */
	public List<GoodsGroups> getKindByShopuniqueAndKindUnique(@Param("shopUnique") String shop_unique, @Param("goodsKindUnique") List<Long> goodsKindUnique);

	/**
	 * 查询一条分类记录
	 */
	public List<Map<String,Object>> getAllGoodsKind(Map<String,Object> map);
	
}
