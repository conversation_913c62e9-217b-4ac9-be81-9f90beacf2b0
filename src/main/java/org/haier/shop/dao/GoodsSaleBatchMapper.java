package org.haier.shop.dao;

import org.apache.ibatis.annotations.Param;
import org.haier.shop.entity.GoodsSaleBatch;
import org.haier.shop.params.goodsBatch.GoodsSaleBatchData;

import java.util.List;

/**
* @Description 商品出库批次表
* @ClassName GoodsSaleBatch
* <AUTHOR> 
* @Date 2024-04-28
**/
public interface GoodsSaleBatchMapper {

    List<GoodsSaleBatch> findList(@Param("params") GoodsSaleBatch goodsSaleBatch);
    int insertBatch(@Param("saleBatchList") List<GoodsSaleBatch> saleBatchList);

    void deleteByListUnique(@Param("shopUnique") Long shopUnique, @Param("listUnique") String listUnique);

    List<GoodsSaleBatchData> selectSaleBatchList(@Param("shopUnique") Long shopUnique, @Param("listUnique") String listUnique);
}