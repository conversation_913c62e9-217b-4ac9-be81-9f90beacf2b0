package org.haier.shop.dao;

import java.util.List;
import java.util.Map;

import org.haier.shop.entity.MianMiMain;
import org.haier.shop.entity.ny.CustomerStatisticsNY;

public interface ShopStatisticsDao {
	public CustomerStatisticsNY queryCusStatisticsNy(String shopUnique);
	public Integer addNewStatisticsData(CustomerStatisticsNY customerStatisticsNY);
	public void statisticsTotalYn();
	public List<Map<String,Object>> queryPayMethodByDay(Map<String,Object> map);
	
	public List<Map<String,Object>> getPayMethodList(Map<String,Object> map);
	
	public List<Map<String,Object>> queryGoodsSaleTopByDay(Map<String,Object> map);
	
	/**
	 * 免密支付订单信息统计（按店铺）
	 * @param map
	 * @return
	 */
	public List<Map<String,Object>> payMethodDetail(Map<String,Object> map);

	/**
	 * 查询使用免密支付的订单总数量
	 * @param map
	 * @return
	 */
	public List<Map<String,Object>> payMethodDetailPages(Map<String,Object> map);
	/**
	 * 查询免密支付各状态的商家数量
	 * @param map
	 * @return
	 */
	public List<Map<String,Object>> queryMianmiStatus();
	
	/**
	 * 查询满足条件的店铺数量
	 * @param map
	 * @return
	 */
	public Integer mianmiDetailShopsListPages(Map<String,Object> map);
	
	/**
	 * 免密支付店铺列表详情
	 * @param map
	 * @return
	 */
	public List<Map<String,Object>> mianmiDetailShopsList(Map<String,Object> map);
	
	/**
	 * 店铺周期内订单列表查询统计信息
	 * @param map
	 * @return
	 */
	public Map<String,Object> getShopListPageMsg(Map<String,Object> map);
	
	/**
	 * 订单列表查询
	 * @param map
	 * @return
	 */
	public List<Map<String,Object>> getShopListByPage(Map<String,Object> map);
	
	/**
	 * 订单详情
	 * @param map
	 * @return
	 */
	public List<Map<String,Object>> showListDetail(Map<String,Object> map);
	/**
	 * 更新店铺的免密状态
	 * @param map
	 * @return
	 */
	public Integer updateShopMianmiStatus(Map<String,Object> map);
	
	/**
	 * 免密统计EXCEL下载-订单总量统计
	 * @param map
	 * @return
	 */
	public Map<String,Object> queryListStatistics(Map<String,Object> map);
	
	/**
	 * 免密统计EXCEL下载-详情统计
	 * @param map
	 * @return
	 */
	public List<MianMiMain> queryShopsMianmiStatistics(Map<String,Object> map);
	
	/**
	 * 免密统计EXCEL下载-全部详情统计
	 * @param map
	 * @return
	 */
	public List<MianMiMain> queryShopsAllStatistics(Map<String,Object> map);
	
	public int insertPayMethodBatch(List<Map<String,Object>> list);
	
	public int insertGoodsHotBatch(List<Map<String,Object>> list);
}
