package org.haier.shop.dao;

import java.util.List;
import java.util.Map;

/**
 * 系统管理下相关页面接口
 * <AUTHOR>
 *
 */
public interface SystemManagerDao {
	public Integer cancelOldRecord(Map<String,Object> map);
	public Integer queryMaxId();
	/**
	 * 添加新的APP更新记录
	 * @param map
	 * @return
	 */
	public Integer addAppUpdateRecord(Map<String,Object> map);
	
	public Integer queryAppVersionCount(Map<String,Object> map);
	
	/**
	 * 获取APP历史版本信息
	 * @param map
	 * @return
	 */
	public List<Map<String,Object>> queryAppVersionList(Map<String,Object> map);
	/**
	 * 消费方式统计界面-->免密使用走势图界面
	 * 店铺免密信息统计（全）
	 * @return
	 */
	public List<Map<String,Object>> queryShopsUserMsg();
	
	/**
	 * 消费方式统计界面-->免密使用走势图界面
	 * 周期内免密使用情况
	 * @return
	 */
	public Map<String,Object> mianmiStatisQuery(Map<String,Object> map);
	
	/**
	 * 免密支付使用商家数
	 * @param map
	 * @return
	 */
	public Integer mianmiUsesCount(Map<String,Object> map);
	
	/**
	 * 免密支付走势图数据列表查询
	 * @param map
	 * @return
	 */
	public List<Map<String,Object>> mianmiStatisticsPic(Map<String,Object> map);

	
	/**
	 * 使用店铺数量
	 * @param map
	 * @return
	 */
	public List<Map<String,Object>> mianmiStatisticsShops(Map<String,Object> map);

	public List<Map<String,Object>> queryAppUploadDetail(String map);

}
