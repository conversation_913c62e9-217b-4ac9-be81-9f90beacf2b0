package org.haier.shop.dao;

import java.util.List;
import java.util.Map;

import org.haier.shop.entity.SqlPc;

public interface UpdateDao {
	//最新的系统版本号
	public Map<String,Object> theLaseVersionNumber();
	//有效机器数量查询
	public Integer queryUpdatePageCount(Map<String,Object> map);
	//当前条件下的店铺机器列表
	public List<Map<String,Object>> queryUpdateMessageByPage(Map<String,Object> map);
	
	//取消需要创建新指令的店铺的已有命令
	public Integer cancelOperateUnexecuted(Map<String,Object> map);
	//创建新的操作名
	public Integer addNewOperate(Map<String,Object> map);
	
	//新版：升级文件上传后，将未升级的店铺设置为取消升级状态
	public Integer setUpdateStatusOverDue(Map<String,Object> map);
	
	//新版，升级文件上传后，查询所有店铺机器，用来添加新的店铺版本升级记录
	public List<Map<String,Object>> queryMacNeedUpdate(Map<String,Object> map);
	
	//新版，添加升级记录到数据库
	public Integer addNewEdition(Map<String,Object> map);
	
	public List<String> queryShopsNeedUpdate(Integer pageSize);
	
	public Integer overUpdate(Map<String,Object> map);
	
	/**
	 * 获取所有店铺的当前版本号信息
	 * @return
	 */
	public List<String> queryVersionNumberList();
	
	/**
	 * 
	 * @param sqlPc
	 * @return
	 */
	public Integer saveSqlCmd(SqlPc sqlPc);
}
