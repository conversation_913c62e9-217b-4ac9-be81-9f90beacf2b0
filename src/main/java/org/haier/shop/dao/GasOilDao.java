package org.haier.shop.dao;

import java.util.List;
import java.util.Map;

import org.haier.shop.oil.RechargeDetail;

public interface GasOilDao {

	List<Map<String,Object>> queryShopsOilList(Map<String, Object> map);
	List<Map<String, Object>> queryManagerOils(Map<String, Object> map);

	List<Map<String, Object>> queryManagerOilgun(Map<String, Object> map);

	List<Map<String, Object>> queryOilSaleList(Map<String, Object> map);

	int queryOilSaleListCount(Map<String, Object> map);

	String queryOilsaleTotal(Map<String, Object> map);

	List<String> queryOilcustomerFlow(Map<String, Object> map);

	Map<String, Object> queryGasOrderDetail(Map<String, Object> map);

	RechargeDetail queryGasRechargeDetail(Map<String, Object> map);

	List<RechargeDetail> queryGasRechargeList(Map<String, Object> map);

	int queryGasRechargeListCount(Map<String, Object> map);

	List<Map<String, Object>> queryCouponName(Map<String, Object> params);

	List<Map<String, Object>> queryCouponCount(Map<String, Object> params);

	List<Map<String, Object>> queryGoodsName(Map<String, Object> params);

	List<Map<String, Object>> queryGoodsCount(Map<String, Object> params);

	List<Map<String, Object>> queryAddBalance(Map<String, Object> params);

	List<Map<String, Object>> queryCusLevelName(Map<String, Object> params);



}
