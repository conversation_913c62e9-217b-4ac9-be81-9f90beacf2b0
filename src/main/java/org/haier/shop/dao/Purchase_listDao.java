package org.haier.shop.dao;

import java.util.List;
import java.util.Map;

import org.haier.shop.entity.PurCart;
import org.haier.shop.entity.PurListMain;
import org.haier.shop.entity.TurnOverMain;

public interface Purchase_listDao {
	/**
	 * 进货订单查询
	 * @param map
	 * @return
	 */
	public List<Map<String,Object>> queryPurLists(Map<String,Object> map);
	/**
	 * 进货订单总金额查询
	 * @param map
	 * @return
	 */
	public Map<String,Object> queryPurTotal(Map<String,Object> map);
	/**
	 * 查询订单详情
	 * @param map
	 * @return
	 */
	public List<Map<String,Object>> queryOrderDetail(Map<String,Object> map);
	/**
	 * 向购物车新加商品
	 * @param map
	 * @return
	 */
	public int addCartGoods(Map<String,Object> map);
	/**
	 * 更新购物车详情
	 * @param map
	 * @return
	 */
	public int updateCartDetail(Map<String,Object> map);
	/**
	 * 删除购物车中数量为0的商品
	 * @param map
	 * @return
	 */
	public int deleteCartGoods(Map<String,Object> map);
	
	/**
	 * 查询店铺购物车编号及其中商品数量
	 * @param map
	 * @return
	 */
	public List<Map<String,Object>> queryCartGoodsUnique(Map<String,Object> map);
	/**
	 * 创建新的购物车订单
	 * @param map
	 * @return
	 */
	public int createPurCart(Map<String,Object> map);
	
	/**
	 * 查询购物车详情
	 * @param map
	 * @return
	 */
	public List<PurCart> queryPurCartGoods(Map<String,Object> map);
	/**
	 * 更新购物车详情
	 * @param map
	 * @return
	 */
	public Integer modifyCartDetail(Map<String,Object> map);
	
	/**
	 * 移除购物车商品
	 */
	public Integer deleteFromCart(Map<String,Object> map);
	/**
	 * 订单提交时，查询提交商品的商品信息
	 * @param map
	 * @return
	 */
	public List<Map<String,Object>> queryGoodsMessage(Map<String,Object> map);
	
	/**
	 * 订单提交时，更新主订单信息
	 * @param map
	 * @return
	 */
	public int updateMainPurList(Map<String,Object> map);
	/**
	 * 订单提交时，批量插入子订单
	 * @param map
	 * @return
	 */
	public int addSubPurLists(List<Map<String,Object>> purlists);
	
	/**
	 * 批量更新购物车商品
	 * @param list
	 * @return
	 */
	public int updateCartDetail1(List<Map<String,Object>> list);
	/**
	 * 批量更新购物车商品至新购物车
	 * @param list
	 * @return
	 */
	public int updateToCartDetail(Map<String,Object> nmap);
	
	/**
	 * 供货商对账
	 * @param map
	 * @return
	 */
	public List<TurnOverMain> queryTurnOver(Map<String,Object> map);
	
	public List<Map<String,Object>> test(Map<String,Object> map);
	
	/**
	 * 批量向购物车添加商品
	 * @param map
	 * @return
	 */
	public int addCartDetail(List<Map<String,Object>> list);
	
	/**
	 * 批量更新购物车商品
	 */
	public int updateCartDetails(List<Map<String,Object>> list);
	/**
	 * 下载进货订单EXCEL
	 * @param map
	 * @return
	 */
	public List<PurListMain> purListExcel(Map<String,Object> map);
	
	public Integer queryOrderCount(Map<String, Object> map);
	
	public List<Map<String, Object>> queryPurListsPages(Map<String, Object> map);
	
	public List<Map<String, Object>> queryGoodsByPage(Map<String, Object> map);
	
	public Map<String, Object> queryGoodsByPages(Map<String, Object> map);
	
	int submitAllorationStorage(Map<String, Object> map);

	void addAllorationListDetail(Map<String, Object> map2);
	
	Map<String, Object> queryStorehouseGoodsExist(Map<String, Object> storehouseMap);
	
	Map<String, Object> queryGoodsDetail(Map<String, Object> storehouseMap);
	
	int updateStorehouseGoods(Map<String, Object> storehouseMap);
	
	int addNewGoods(Map<String, Object> storehouseMap);
	
	List<Map<String, Object>> queryAllorationDetail(Map<String, Object> map);
	
	List<Map<String, Object>> getGoodsTopList(Map<String, Object> map);
	
	List<Map<String, Object>> getGoodsListNoSale(Map<String, Object> map);
	
	int getGoodsListNoSaleCount(Map<String, Object> storehouseMap);
	
	Map<String, Object> getShopById(Map<String, Object> map);
	
	List<Map<String, Object>> getGoodsNearbyList(Map<String, Object> map);
	
	List<Map<String, Object>> getGoodsTopOrderList(Map<String, Object> map);
	
	int getGoodsTopOrderListCount(Map<String, Object> map);
	
	int insertNoNeedGoods(Map<String, Object> map);
	
	int deleteGoods_top_no_need(Map<String, Object> map);
	
	int insertGoods_top_order(Map<String, Object> map);
	
	int insertGoods(Map<String, Object> map);
}
