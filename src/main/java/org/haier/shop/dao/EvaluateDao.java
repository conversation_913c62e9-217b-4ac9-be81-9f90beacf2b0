package org.haier.shop.dao;

import java.util.List;
import java.util.Map;

import org.haier.shop.entity.Evaluate;

public interface EvaluateDao {
	
	//评论相关SQL
	public  int evaluateListPagesQuery(Map<String,Object> map);
	
	
	/**
	 * 评论列表查询
	 * @param map
	 * @return
	 */
	
	public List<Map<String,Object>> evaluateListQuery(Map<String,Object> map);
	/**
	 * 查询评论详情
	 * @param map
	 * @return
	 */
	public List<Evaluate> queryEvaluateDetail(Map<String,Object> map);
	
	/**
	 * 删除管理员评论
	 * @param map
	 * @return
	 */
	public int deleteManagerEvaluate(Map<String,Object> map);
	/**
	 * 添加新的订单评论回复
	 * @param map
	 * @return
	 */
	public int responeEvaluate(Map<String,Object> map);
	
	/**
	 * 订单评论回复状态更新
	 * @param map
	 * @return
	 */
	public int responeEvaluateStatus(Map<String,Object> map);
}
