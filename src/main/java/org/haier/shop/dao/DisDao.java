package org.haier.shop.dao;

import java.util.List;
import java.util.Map;

public interface DisDao {
	public Integer modifyWaitDisOrderList(List<Map<String,Object>> list);
	
	/**
	 * 定时任务：将统计的数据更新到会员信息
	 */
	public Integer modifyDisRelationList(List<Map<String,Object>> list);
	
	/**
	 * 查询周期内需要确认的分销信息
	 * @param map
	 * @return
	 */
	public List<Map<String,Object>> getWaitDisOrderList();
	
	/**
	 * 查询店铺分销商列表
	 * @return
	 */
	public List<Map<String,Object>> queryShopDisList(Map<String,Object> params);
	
	/**
	 * 查询店铺分销商列表总条数
	 * @return
	 */
	public Integer queryShopDisListCount(Map<String,Object> params);
	
	/**
	 * 查询分销商下级会员列表
	 * @return
	 */
	public List<Map<String,Object>> queryShopDisLowerList(Map<String,Object> params);
	
	/**
	 * 查询分销商下级会员列表总条数
	 * @return
	 */
	public Integer queryShopDisLowerListCount(Map<String,Object> params);
	
	/**
	 * 查询会员列表
	 * @return
	 */
	public List<Map<String,Object>> queryCustomerList(Map<String,Object> params);
	
	/**
	 * 新增分销商
	 * @return
	 */
	public Integer addDisRelation(Map<String,Object> params);
	
	/**
	 * 获取分销商详情
	 * @return
	 */
	public Map<String ,Object> queryDisRelation(Map<String,Object> params);
	
	/**
	 * 编辑分销商
	 * @return
	 */
	public Integer updateDisRelation(Map<String ,Object> params);
	
	/**
	 * 查询分销商账单明细列表
	 * @return
	 */
	public List<Map<String,Object>> disTradingList(Map<String,Object> params);
	
	/**
	 * 查询分销商账单明细列表总条数
	 * @return
	 */
	public Integer disTradingListCount(Map<String,Object> params);
	
	/**
	 * 详情统计-查询分销商佣金金额
	 * @return
	 */
	public Double queryCommission(Map<String,Object> params);
	
	/**
	 * 查询店铺佣金明细列表
	 * @return
	 */
	public List<Map<String,Object>> queryCommissionList(Map<String,Object> params);
	
	/**
	 * 查询店铺佣金明细列表总条数
	 * @return
	 */
	public Integer queryCommissionListCount(Map<String,Object> params);
	
	/**
	 * 店铺佣金统计-查询全部、昨日、上月佣金发放总和
	 * @return
	 */
	public Double querySumCommission(Map<String,Object> params);
	
	/**
	 * 店铺收益统计-查询全部、昨日、上月收益总和
	 * @return
	 */
	public Double querySumGoodsProfit(Map<String,Object> params);
	
	/**
	 * 查询店铺佣金收益统计
	 * @return
	 */
	public List<Map<String,Object>> queryCommissionStatisticsList(Map<String,Object> params);
	
	/**
	 * 查询店铺分销订单列表
	 * @return
	 */
	public List<Map<String,Object>> queryDisOrderList(Map<String,Object> params);
	
	/**
	 * 查询店铺分销订单列表总条数
	 * @return
	 */
	public Integer queryDisOrderListCount(Map<String,Object> params);
	
	/**
	 * 查询店铺分销订单基本信息
	 * @return
	 */
	public Map<String,Object> queryDisOrder(String sale_list_unique);
	
	/**
	 * 查询店铺分销订单分销商品信息
	 * @return
	 */
	public List<Map<String,Object>> queryDisOrderGoodsList(String sale_list_unique);
	
	/**
	 * 查询所有分销商列表
	 * @return
	 */
	public List<Map<String,Object>> queryAllDisList();
	
	/**
	 * 查询所有分销商等级列表
	 * @return
	 */
	public List<Map<String,Object>> queryAllDisLevelList();
	
	/**
	 * 查询分销商发展分销商数量
	 * @return
	 */
	public Integer queryDisNextDisCount(Map<String,Object> params);
	
	/**
	 * 查询分销商发展下级数量
	 * @return
	 */
	public Integer queryDisNextCount(Map<String,Object> params);
	
	/**
	 * 修改分销商等级
	 * @return
	 */
	public Integer updateDisLevel(Map<String,Object> params);
	
	/**
	 * 查询分销商提现列表
	 * @return
	 */
	public List<Map<String,Object>> disWithdList(Map<String,Object> params);
	
	/**
	 * 查询分销商提现列表总条数
	 * @return
	 */
	public Integer disWithdListCount(Map<String,Object> params);
	
	/**
	 * 修改分销商提现记录状态
	 * @return
	 */
	public Integer updateDisTradingStatus(Map<String,Object> params);
	
	/**
	 * 查询订单分销佣金总额
	 * @return
	 */
	public Double queryCommissionBySaleListUnique(String sale_list_unique);

	public Integer queryChildDisCount(Map<String, Object> lowerParams);

	public List<Map<String, Object>> queryShopDisChildCusList(Map<String, Object> params);

	public Integer queryShopDisChildCusListCount(Map<String, Object> params);

	public List<Map<String, Object>> queryShopDisTradingAmountList(Map<String, Object> params);

	public Integer queryShopDisTradingAmountListCount(Map<String, Object> params);

	public List<Map<String, Object>> queryDisGoodsList(Map<String, Object> params);

	public Integer queryDisGoodsListCount(Map<String, Object> params);

	public Double querySumCashMoney(Map<String, Object> params);

	public List<Map<String, Object>> queryCusRelationList(Map<String, Object> params);

	public Integer queryCusRelationListCount(Map<String, Object> params);

	public int addSetCommissionRatio(Map<String, Object> params);

	public Map<String, Object> queryDisWithShopName(Map<String, Object> map);

	public List<Map<String, Object>> queryShopCashMoney(Map<String, Object> params);

	public List<Map<String, Object>> queryShopDisCashAmountList(Map<String, Object> params);

	public Integer queryShopDisCashAmountListCount(Map<String, Object> params);

	public int updateSetCommissionRatio(Map<String, Object> params);
}
