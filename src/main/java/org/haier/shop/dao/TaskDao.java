package org.haier.shop.dao;

import java.util.List;
import java.util.Map;

public interface TaskDao {
	
	public List<Map<String,Object>> getListUnpaid();
	
	/**
	 * 查询订单信息
	 * @param map 订单唯一性标识
	 * @return SaleList
	 */
	public Map<String ,Object> getSaleList(Map<String, Object> map);
	
	/**
	 * 保存支付方式
	 * @param map
	 * @return
	 */
	public Integer savePayMethod(Map<String,Object> map);
	
	/**
	 * 查询优惠券
	 * @param map
	 * @return
	 */
	public Map<String, Object> queryShopCouponById(Map<String, Object> map);
	
	public void updateShopPlatformCouponCount(Map<String, Object> shopCouponMap);
	
	public List<Map<String, Object>> querySaleListDetailGoods(Map<String, Object> map);
	
	public Map<String, Object> validateCus(Map<String, Object> map);
	
	public 	Map<String, Object> findGoodsPointSet(Map<String, Object> params);
	
	public Map<String, Object> queryShopPointsByCusUnique(Map<String, Object> map);
	
	public void updateShopPointsByCusJia(Map<String, Object> map);
	
	public void insertShopPointsByCusUnique(Map<String, Object> map);
	
	public 	Map<String, Object> queryBeansGetByMoney(Map<String, Object> map);
	
	public Map<String, Object> queryOpenBeansPayStatus(Map<String, Object> map);
	
	Map<String, Object> queryShopBeansCan(Map<String, Object> map);
	void updateShopBeans(Map<String, Object> map);
	
	void updateSendBeansMsgStataus(Map<String, Object> map);
	void updatePointsBeansByCusJia(Map<String, Object> map);
	void saveBatchSaleLog(List<Map<String, Object>> logList);
	

	Map<String, Object> getSmallGoodsInfo2(Map<String, Object> maps);

	Map<String, Object> queryGoodsInShopBeansGoods(Map<String, Object> maps);

	void updateJianShopGiveBeans(Map<String, Object> map);

	void updateJiaCusGiveBeans(Map<String, Object> map);

	void batchDeleteCartByShoppingCartId2(Map<String, Object> params);
	
	int newStockRecords(List<Map<String, Object>> stockList);
	
	int batchStocks(List<Map<String, Object>> editGoodsCountList);
	Map<String, Object> getSmallGoodsInfo(Map<String, Object> maps);
	
	Map<String, Object> queryGoodsInSupBeansGoods(Map<String, Object> goods);
	
	Integer queryBeansGiveSaleCount(Map<String, Object> maps);
	void updateGoodsInSupBeansGoods(Map<String, Object> goods);
	public void updateSaleListBalancePay(Map<String, Object> map);
	public void updateSaleListDetailBeans(Map<String, Object> goods);
	public Map<String, Object> queryShopCouponShopUnique(Map<String, Object> saleMap);
	public Map<String, Object> getPeiSongMoney(Map<String, Object> map);

	public List<Map<String, Object>> queryPayMethodMoney(Map<String, Object> map);
	Map<String, Object> queryBeansConfig();
	public void updateBeansJia(Map<String, Object> result);
	
	public double queryOrderYanMoney(Map<String, Object> map);
	public void updateShopBalance(Map<String, Object> saleMap);
	public void updateBalanceJia(Map<String, Object> params);

	public Map<String, Object> getOpenid(Map<String, Object> paramsMap);
	Map<String, Object> queryShopIsGiveConfig(Map<String, Object> params);
}
		