package org.haier.shop.dao;

import java.util.List;
import java.util.Map;

public interface CustomerRechargeConfigDao {

	/**
	 * 添加充值信息记录
	 * @return
	 */
	public Integer addCusOffRechargeConfig(Map<String,Object> map);
	/**
	 * 添加新的线下充值赠送商品信息
	 * @param list
	 * @return
	 */
	public Integer addCusOffRechargeGoods(List<Map<String,Object>> list);
	/**
	 * 添加新的线下充值赠送优惠券信息
	 * @param list
	 * @return
	 */
	public Integer addCusOffRechargeCoupon(List<Map<String,Object>> list);
	
	public List<Map<String,Object>> queryRechargeConfig(Map<String,Object> map);
	
	public int queryRechargeConfigPages(Map<String,Object> map);
	
	public int insertCustomer_recharge_config(Map<String,Object> map);
	
	public int updateCustomer_recharge_config(Map<String,Object> map);
}
