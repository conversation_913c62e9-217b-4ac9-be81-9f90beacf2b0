package org.haier.shop.dao;

import java.util.List;
import java.util.Map;

public interface ShopAddressDao {
	
	/**
	 * 更新地址信息
	 * @param map
	 * @return
	 */
	public Integer modifyShopAddressStatus(Map<String,Object> map);
	/**
	 * 添加新的地址信息
	 * @param map
	 * @return
	 */
	public Integer addNewShopAddress(Map<String,Object> map);
	/**
	 * 查询店铺的地址信息
	 * @param shopUnique
	 * @return
	 */
	public List<Map<String,Object>> queryShopAddressList(String shopUnique);
}
