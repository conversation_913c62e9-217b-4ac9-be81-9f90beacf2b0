package org.haier.shop.dao;

import org.haier.shop.entity.ShopsConfig;

/**
 * @Description
 * @ClassName ShopsConfigDao
 * <AUTHOR>
 * @Date 2024/4/8 16:01
 **/
public interface ShopsConfigDao {

    /**
     * 更新店铺配置
     * @param shopsConfig
     * @return
     */
    public int updateShopsConfig(ShopsConfig shopsConfig);

    /**
     * 新增店铺配置
     * @param shopsConfig
     * @return
     */
    public int addNewShopsConfig(ShopsConfig shopsConfig);

    /**
     * 查询店铺配置
     * @param shopUnique
     * @return
     */
    public ShopsConfig selectByShopUnique(String shopUnique);
}
