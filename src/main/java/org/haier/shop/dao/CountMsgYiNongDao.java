package org.haier.shop.dao;

import java.util.List;
import java.util.Map;

public interface CountMsgYiNongDao {

	Map<String, Object> queryShopIsCenter(Map<String, Object> params);

	List<Map<String, Object>> queryChildSaleCount(Map<String, Object> params);

	Map<String, Object> queryOrderMsg(Map<String, Object> params);

	List<Map<String, Object>> queryHotSaleGoods(Map<String, Object> params);

	List<Map<String, Object>> querySaleTrend(Map<String, Object> map);

	List<Map<String, Object>> queryCenterSaleCount(Map<String, Object> params);

	

}
