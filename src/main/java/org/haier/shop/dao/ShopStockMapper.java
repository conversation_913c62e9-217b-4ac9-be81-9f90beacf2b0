package org.haier.shop.dao;

import org.apache.ibatis.annotations.Param;
import org.haier.shop.entity.ShopStock;
import org.haier.shop.entity.ShopStockDetail;

import java.util.List;

/**
* @Description 库存明细表
* @ClassName ShopStock
* <AUTHOR> 
* @Date 2024-04-29
**/
public interface ShopStockMapper {

    public List<ShopStock> findList(@Param("params") ShopStock shopStock);

    int insertBatch(@Param("shopStockList") List<ShopStock> shopStockList);

    void deleteByListUnique(@Param("listUnique") String listUnique);

    int updateBatchGoodsCount(@Param("shopStockList") List<ShopStock> shopStockList);
}