package org.haier.shop.config;

import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.io.Serializable;

/**
 * @description: 系统配置
 * @version: 1.0.0-SNAPSHOT
 * @author: spb
 * @createTime: 2025-05-09 10:05
 **/
@Component
public class ProjectConfig implements Serializable {

    /**
     * 项目地址
     */
    private String projectUrl;

    /**
     * 纳统地址
     */
    private String statisticUrl;

    public static String PROJECT_URL;
    public static String STATISTIC_URL;

    @PostConstruct
    public void init() {
        PROJECT_URL = this.projectUrl;
        STATISTIC_URL = this.statisticUrl;
    }

    public String getStatisticUrl() {
        return statisticUrl;
    }

    public void setStatisticUrl(String statisticUrl) {
        this.statisticUrl = statisticUrl;
    }

    public String getProjectUrl() {
        return projectUrl;
    }

    public void setProjectUrl(String projectUrl) {
        this.projectUrl = projectUrl;
    }
}
