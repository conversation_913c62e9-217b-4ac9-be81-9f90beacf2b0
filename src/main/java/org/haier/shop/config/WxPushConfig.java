package org.haier.shop.config;


import java.util.concurrent.atomic.AtomicBoolean;

public class WxPushConfig {

    private String ningYuRedisCode;
    private String memberCardChange;
    private String rechargeTemplateId;
    private String sendMsgUrl;
    private String errListCode;
    private String successListCode;
    private Integer ONEDAY;
    private Integer ONEWEEK;
    public static AtomicBoolean isRecharge = new AtomicBoolean(false);

    public String getNingYuRedisCode() {
        return ningYuRedisCode;
    }

    public void setNingYuRedisCode(String ningYuRedisCode) {
        this.ningYuRedisCode = ningYuRedisCode;
    }

    public String getMemberCardChange() {
        return memberCardChange;
    }

    public void setMemberCardChange(String memberCardChange) {
        this.memberCardChange = memberCardChange;
    }

    public String getRechargeTemplateId() {
        return rechargeTemplateId;
    }

    public void setRechargeTemplateId(String rechargeTemplateId) {
        this.rechargeTemplateId = rechargeTemplateId;
    }

    public String getSendMsgUrl() {
        return sendMsgUrl;
    }

    public void setSendMsgUrl(String sendMsgUrl) {
        this.sendMsgUrl = sendMsgUrl;
    }

    public String getErrListCode() {
        return errListCode;
    }

    public void setErrListCode(String errListCode) {
        this.errListCode = errListCode;
    }

    public String getSuccessListCode() {
        return successListCode;
    }

    public void setSuccessListCode(String successListCode) {
        this.successListCode = successListCode;
    }

    public Integer getONEWEEK() {
        return ONEWEEK;
    }

    public void setONEWEEK(Integer ONEWEEK) {
        this.ONEWEEK = ONEWEEK;
    }

    public Integer getONEDAY() {
        return ONEDAY;
    }

    public void setONEDAY(Integer ONEDAY) {
        this.ONEDAY = ONEDAY;
    }
}
