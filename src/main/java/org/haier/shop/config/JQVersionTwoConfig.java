package org.haier.shop.config;

import javax.annotation.PostConstruct;

public class JQVersionTwoConfig {
    //基础路径
    private String JQVERSIONTWOBASEURLSET;
    private static String JQVersionBaseUrl;
    // 添加名片
    public static String addCard = "/base/card/addCard";
    //修改名片
    public static String updateCard = "/base/card/editCard";
    //删除名片
    public static String deleteCard = "/base/card/deleteCard";
    //查询名片列表
    public static String getCardList = "/base/card/list";
    //名片详情
    public static String getCardDetail = "/base/card/getInfo";
    //文件上传
    public static String uploadFile = "/base/file/noAuth/upload";

    //金圈2.0成功编码
    public static String SUCCESSCODE = "200";
    public static String FAILCODE = "500";


    @PostConstruct
    public void init() {
        System.out.println("初始化金圈2.0数据");
        JQVersionBaseUrl = JQVERSIONTWOBASEURLSET;
        addCard = JQVersionBaseUrl + addCard;
        updateCard = JQVersionBaseUrl + updateCard;
        deleteCard = JQVersionBaseUrl + deleteCard;
        getCardList = JQVersionBaseUrl + getCardList;
        getCardDetail = JQVersionBaseUrl + getCardDetail;
        uploadFile = JQVersionBaseUrl + uploadFile;
    }

    public String getJQVERSIONTWOBASEURLSET() {
        return JQVERSIONTWOBASEURLSET;
    }

    public void setJQVERSIONTWOBASEURLSET(String JQVERSIONTWOBASEURLSET) {
        this.JQVERSIONTWOBASEURLSET = JQVERSIONTWOBASEURLSET;
    }
}
