package org.haier.shop.config;

import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;

@Component
public class SysConfig {

    /**
     * 正式环境标志，通过REDISTEST值为FORMAL则为正式环境
     */
    public final static String FORMAL = "FORMAL";
    public final static String TEST = "REDISFORTEST";
    public final static String PUBLICSHOP = "8302016134121";
    //微信支付类型
    public final static String WXPAY = "WXPAY";
    public final static String ALIPAY = "ALIPAY";
    public final static String UNIONPAY = "UNIONPAY";
    //合利宝商品账号
    public static String MCHID = "E1802781288";
    //合利宝商户密钥
    public static String MCHKEY = "sSO3EWRyaQtETPpBmcAu2Zttj1ph8HFM";

    //是否测试环境
    private String REDISTESTCode;
    private String URLPATHCode;
    private String sendJGPushToAppCode;
    private String FILEPATHCode;
    private String confirmReceiptUrlCode;
    private String HELIBAOIMGURLCode;
    private String publicMqttUrlCode;
    private String ykz_cancelDeliveryCode;
    private String HELIBAONOTIFYURLCode;
    private String HELIBAONOTIFYURL_YNCode;
    private String HELIBAOWXPAYURLCode;
    private String HELIBAONOTIFYURL_RETURN_MONEYCode;
    private String CONFIRMRECEIPTURLCode;
    private String AGENCYQUERYSHOPUSERURLCode;
    private String AGENCYADDCUSTOMERORDERURLCode;
    private String BINDNEWSHOPEXAMINECode;
    private String SORTINGURLCode;
    private String DELIVERYCREATEORDERCode;

    public static String REDISTEST;
    public static String URLPATH;
    public static String sendJGPushToApp;
    public static String FILEPATH;
    public static String confirmReceiptUrl;
    public static String HELIBAOIMGURL;
    public static String publicMqttUrl;
    public static String ykz_cancelDelivery;
    public static String HELIBAONOTIFYURL;
    public static String HELIBAONOTIFYURL_YN;
    public static String HELIBAOWXPAYURL;
    public static String HELIBAONOTIFYURL_RETURN_MONEY;
    public static String CONFIRMRECEIPTURL;
    public static String AGENCYQUERYSHOPUSERURL;
    public static String AGENCYADDCUSTOMERORDERURL;
    public static String BINDNEWSHOPEXAMINE;
    public static String SORTINGURL;
    public static String DELIVERYCREATEORDER;



    @PostConstruct
    public void init() {
        REDISTEST = this.REDISTESTCode;
        URLPATH = this.URLPATHCode;
        sendJGPushToApp = this.sendJGPushToAppCode;
        FILEPATH = this.FILEPATHCode;
        confirmReceiptUrl = this.confirmReceiptUrlCode;
        HELIBAOIMGURL = this.HELIBAOIMGURLCode;
        publicMqttUrl = this.publicMqttUrlCode;
        ykz_cancelDelivery = this.ykz_cancelDeliveryCode;
        HELIBAONOTIFYURL = this.HELIBAONOTIFYURLCode;
        HELIBAONOTIFYURL_YN = this.HELIBAONOTIFYURL_YNCode;
        HELIBAOWXPAYURL = this.HELIBAOWXPAYURLCode;
        HELIBAONOTIFYURL_RETURN_MONEY = this.HELIBAONOTIFYURL_RETURN_MONEYCode;
        CONFIRMRECEIPTURL = this.CONFIRMRECEIPTURLCode;
        AGENCYQUERYSHOPUSERURL = this.AGENCYQUERYSHOPUSERURLCode;
        AGENCYADDCUSTOMERORDERURL = this.AGENCYADDCUSTOMERORDERURLCode;
        BINDNEWSHOPEXAMINE = this.BINDNEWSHOPEXAMINECode;
        SORTINGURL = this.SORTINGURLCode;
        DELIVERYCREATEORDER = this.DELIVERYCREATEORDERCode;
    }


    public String getREDISTESTCode() {
        return REDISTESTCode;
    }

    public void setREDISTESTCode(String REDISTESTCode) {
        this.REDISTESTCode = REDISTESTCode;
    }

    public String getURLPATHCode() {
        return URLPATHCode;
    }

    public void setURLPATHCode(String URLPATHCode) {
        this.URLPATHCode = URLPATHCode;
    }

    public String getSendJGPushToAppCode() {
        return sendJGPushToAppCode;
    }

    public void setSendJGPushToAppCode(String sendJGPushToAppCode) {
        this.sendJGPushToAppCode = sendJGPushToAppCode;
    }

    public String getFILEPATHCode() {
        return FILEPATHCode;
    }

    public void setFILEPATHCode(String FILEPATHCode) {
        this.FILEPATHCode = FILEPATHCode;
    }

    public String getConfirmReceiptUrlCode() {
        return confirmReceiptUrlCode;
    }

    public void setConfirmReceiptUrlCode(String confirmReceiptUrlCode) {
        this.confirmReceiptUrlCode = confirmReceiptUrlCode;
    }

    public String getHELIBAOIMGURLCode() {
        return HELIBAOIMGURLCode;
    }

    public void setHELIBAOIMGURLCode(String HELIBAOIMGURLCode) {
        this.HELIBAOIMGURLCode = HELIBAOIMGURLCode;
    }

    public String getPublicMqttUrlCode() {
        return publicMqttUrlCode;
    }

    public void setPublicMqttUrlCode(String publicMqttUrlCode) {
        this.publicMqttUrlCode = publicMqttUrlCode;
    }

    public String getYkz_cancelDeliveryCode() {
        return ykz_cancelDeliveryCode;
    }

    public void setYkz_cancelDeliveryCode(String ykz_cancelDeliveryCode) {
        this.ykz_cancelDeliveryCode = ykz_cancelDeliveryCode;
    }

    public String getHELIBAONOTIFYURLCode() {
        return HELIBAONOTIFYURLCode;
    }

    public void setHELIBAONOTIFYURLCode(String HELIBAONOTIFYURLCode) {
        this.HELIBAONOTIFYURLCode = HELIBAONOTIFYURLCode;
    }

    public String getHELIBAOWXPAYURLCode() {
        return HELIBAOWXPAYURLCode;
    }

    public void setHELIBAOWXPAYURLCode(String HELIBAOWXPAYURLCode) {
        this.HELIBAOWXPAYURLCode = HELIBAOWXPAYURLCode;
    }

    public String getHELIBAONOTIFYURL_RETURN_MONEYCode() {
        return HELIBAONOTIFYURL_RETURN_MONEYCode;
    }

    public void setHELIBAONOTIFYURL_RETURN_MONEYCode(String HELIBAONOTIFYURL_RETURN_MONEYCode) {
        this.HELIBAONOTIFYURL_RETURN_MONEYCode = HELIBAONOTIFYURL_RETURN_MONEYCode;
    }

    public String getCONFIRMRECEIPTURLCode() {
        return CONFIRMRECEIPTURLCode;
    }

    public void setCONFIRMRECEIPTURLCode(String CONFIRMRECEIPTURLCode) {
        this.CONFIRMRECEIPTURLCode = CONFIRMRECEIPTURLCode;
    }

    public String getAGENCYQUERYSHOPUSERURLCode() {
        return AGENCYQUERYSHOPUSERURLCode;
    }

    public void setAGENCYQUERYSHOPUSERURLCode(String AGENCYQUERYSHOPUSERURLCode) {
        this.AGENCYQUERYSHOPUSERURLCode = AGENCYQUERYSHOPUSERURLCode;
    }

    public String getAGENCYADDCUSTOMERORDERURLCode() {
        return AGENCYADDCUSTOMERORDERURLCode;
    }

    public void setAGENCYADDCUSTOMERORDERURLCode(String AGENCYADDCUSTOMERORDERURLCode) {
        this.AGENCYADDCUSTOMERORDERURLCode = AGENCYADDCUSTOMERORDERURLCode;
    }

    public String getBINDNEWSHOPEXAMINECode() {
        return BINDNEWSHOPEXAMINECode;
    }

    public void setBINDNEWSHOPEXAMINECode(String BINDNEWSHOPEXAMINECode) {
        this.BINDNEWSHOPEXAMINECode = BINDNEWSHOPEXAMINECode;
    }

    public String getSORTINGURLCode() {
        return SORTINGURLCode;
    }

    public void setSORTINGURLCode(String SORTINGURLCode) {
        this.SORTINGURLCode = SORTINGURLCode;
    }

    public String getHELIBAONOTIFYURL_YNCode() {
        return HELIBAONOTIFYURL_YNCode;
    }

    public void setHELIBAONOTIFYURL_YNCode(String HELIBAONOTIFYURL_YNCode) {
        this.HELIBAONOTIFYURL_YNCode = HELIBAONOTIFYURL_YNCode;
    }

    public String getDELIVERYCREATEORDERCode() {
        return DELIVERYCREATEORDERCode;
    }

    public void setDELIVERYCREATEORDERCode(String DELIVERYCREATEORDERCode) {
        this.DELIVERYCREATEORDERCode = DELIVERYCREATEORDERCode;
    }
}
