package org.haier.customer.entity;

import java.util.List;

public class Order {
	//订单编号
	private String OrderCode;
	//店铺编号
	private String ShopID;
	//销售人ID
	private String UserID;
	//原价总结
	private Double TotalPrice;
	//折扣价总计
	private Double DiscountPrice ;
	//销售时间
	private String TakeTime;
	//订单详情
	private List<OrderDetail> Items;
	public String getOrderCode() {
		return OrderCode;
	}
	public void setOrderCode(String orderCode) {
		OrderCode = orderCode;
	}
	public String getShopID() {
		return ShopID;
	}
	public void setShopID(String shopID) {
		ShopID = shopID;
	}
	public String getUserID() {
		return UserID;
	}
	public void setUserID(String userID) {
		UserID = userID;
	}
	public Double getTotalPrice() {
		return TotalPrice;
	}
	public void setTotalPrice(Double totalPrice) {
		TotalPrice = totalPrice;
	}
	public Double getDiscountPrice() {
		return DiscountPrice;
	}
	public void setDiscountPrice(Double discountPrice) {
		DiscountPrice = discountPrice;
	}
	public String getTakeTime() {
		return TakeTime;
	}
	public void setTakeTime(String takeTime) {
		TakeTime = takeTime;
	}
	public List<OrderDetail> getItems() {
		return Items;
	}
	public void setItems(List<OrderDetail> items) {
		Items = items;
	}
	
	
}
