package org.haier.customer.entity;

public class OrderDetail {
	//商品条码
	private String SkuCode;
	//销售数量
	private Integer Quantity;
	//原价
	private Double OriginalPrice;
	//销售价格
	private Double SellingPrice;
	public String getSkuCode() {
		return SkuCode;
	}
	public void setSkuCode(String skuCode) {
		SkuCode = skuCode;
	}
	public Integer getQuantity() {
		return Quantity;
	}
	public void setQuantity(Integer quantity) {
		Quantity = quantity;
	}
	public Double getOriginalPrice() {
		return OriginalPrice;
	}
	public void setOriginalPrice(Double originalPrice) {
		OriginalPrice = originalPrice;
	}
	public Double getSellingPrice() {
		return SellingPrice;
	}
	public void setSellingPrice(Double sellingPrice) {
		SellingPrice = sellingPrice;
	}
	
}
