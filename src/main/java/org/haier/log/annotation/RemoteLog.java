package org.haier.log.annotation;

import org.haier.log.enums.BusinessType;
import org.haier.log.enums.OperatorType;

import java.lang.annotation.*;

/**
 * @Description 自定义操作日志记录注解
 * @ClassName RemoteLog
 * <AUTHOR>
 * @Date 2023/11/29 13:46
 **/
@Target({ElementType.PARAMETER, ElementType.METHOD})
@Retention(RetentionPolicy.RUNTIME)
@Documented
public @interface RemoteLog {

    /**
     * 模块
     */
    String title() default "";

    /**
     * 功能
     */
    BusinessType businessType() default BusinessType.OTHER;

    /**
     * 操作人类别
     */
    OperatorType operatorType() default OperatorType.MANAGE;

    /**
     * 是否保存请求头信息
     */
    boolean isSaveRequestHeader() default true;

    /**
     * 是否保存请求的参数
     */
    boolean isSaveRequestData() default true;

    /**
     * 是否保存响应的参数
     */
    boolean isSaveResponseData() default true;

    /**
     * 是否发送钉钉机器人消息
     * @return
     */
    boolean isSendDingDingTalk() default false;

    /**
     * 排除指定的请求参数
     */
    String[] excludeParamNames() default {};
}
