package org.haier.log.aspect;

import java.io.Serializable;

/**
 * @Description
 * @ClassName RemoteLogConfig
 * <AUTHOR>
 * @Date 2023/12/4 16:17
 **/
public class RemoteLogConfig implements Serializable {

    private static final long serialVersionUID = 1L;
    /**
     * 日志服务地址
     */
    private String url;

    /**
     * 所属项目编码
     */
    private String projectCode;

    /**
     * 运行环境
     */
    private String env;

    public String getUrl() {
        return url;
    }

    public void setUrl(String url) {
        this.url = url;
    }

    public String getProjectCode() {
        return projectCode;
    }

    public void setProjectCode(String projectCode) {
        this.projectCode = projectCode;
    }

    public String getEnv() {
        return env;
    }

    public void setEnv(String env) {
        this.env = env;
    }
}
