package org.haier.log.aspect;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.lang.Dict;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.ArrayUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.servlet.ServletUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import org.apache.commons.lang3.StringUtils;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.annotation.AfterReturning;
import org.aspectj.lang.annotation.AfterThrowing;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Before;
import org.haier.log.annotation.RemoteLog;
import org.haier.log.enums.BusinessStatus;
import org.haier.log.event.OperLogEvent;
import org.haier.log.util.IpUtils;
import org.haier.log.util.ServletUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.core.NamedThreadLocal;
import org.springframework.http.HttpMethod;
import org.springframework.stereotype.Component;
import org.springframework.validation.BindingResult;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.*;

/**
 * @Description 操作日志记录处理
 * @ClassName RemoteLogAspect
 * <AUTHOR>
 * @Date 2023/11/29 13:49
 **/
@Aspect
@Component
public class RemoteLogAspect {

    private final Logger logger = LoggerFactory.getLogger(this.getClass());

    /**
     * 排除敏感属性字段
     */
    public static final String[] EXCLUDE_PROPERTIES = {"password", "oldPassword", "newPassword", "confirmPassword"};

    public static final String[] EXCLUDE_HEADERS = {"accept", "cookie", "connection", "accept-encoding", "accept-language", "cache-control", "content-Length", "origin", "referer"};

    @Resource
    private final ApplicationEventPublisher publisher;

    /**
     * 计算操作消耗时间
     */
    private static final ThreadLocal<Long> TIME_THREADLOCAL = new NamedThreadLocal<Long>("Cost Time");

    public RemoteLogAspect(ApplicationEventPublisher publisher) {
        this.publisher = publisher;
    }

    /**
     * 处理请求前执行
     */
    @Before(value = "@annotation(controllerLog)")
    public void boBefore(JoinPoint joinPoint, RemoteLog controllerLog) {
        TIME_THREADLOCAL.set(DateUtil.current());
    }

    /**
     * 处理完请求后执行
     *
     * @param joinPoint 切点
     */
    @AfterReturning(pointcut = "@annotation(controllerLog)", returning = "jsonResult")
    public void doAfterReturning(JoinPoint joinPoint, RemoteLog controllerLog, Object jsonResult) {
        // 请求的地址
        HttpServletRequest request = ((ServletRequestAttributes) RequestContextHolder.getRequestAttributes()).getRequest();
        handleLog(joinPoint, controllerLog, null, jsonResult, request);
    }

    /**
     * 拦截异常操作
     *
     * @param joinPoint 切点
     * @param e         异常
     */
    @AfterThrowing(value = "@annotation(controllerLog)", throwing = "e")
    public void doAfterThrowing(JoinPoint joinPoint, RemoteLog controllerLog, Exception e) {
        // 请求的地址
        HttpServletRequest request = ((ServletRequestAttributes) RequestContextHolder.getRequestAttributes()).getRequest();
        handleLog(joinPoint, controllerLog, e, null, request);
    }

    protected void handleLog(final JoinPoint joinPoint, RemoteLog controllerLog, final Exception e, Object jsonResult, HttpServletRequest request) {
        // *========数据库日志=========*//
        try {
            OperLogEvent operLog = new OperLogEvent();
            operLog.setStatus(BusinessStatus.SUCCESS.ordinal());
            String ip = IpUtils.getIpAddr(request);
            operLog.setOperIp(ip);
            operLog.setOperUrl(StringUtils.substring(request.getRequestURI(), 0, 255));

            operLog.setSendDingTalk(controllerLog.isSendDingDingTalk());
            if (e != null) {
                operLog.setStatus(BusinessStatus.FAIL.ordinal());
                operLog.setErrorMsg(StringUtils.substring(e.getMessage(), 0, 2000));
            } else {
                operLog.setSendDingTalk(false);
            }
            // 设置方法名称
            String className = joinPoint.getTarget().getClass().getName();
            String methodName = joinPoint.getSignature().getName();
            operLog.setMethod(className + "." + methodName + "()");
            // 设置请求方式
            operLog.setRequestMethod(request.getMethod());
            // 处理设置注解上的参数
            getControllerMethodDescription(joinPoint, controllerLog, operLog, jsonResult, request);
            // 设置消耗时间
            if (ObjectUtil.isNotNull(TIME_THREADLOCAL.get())) {
                operLog.setCostTime(DateUtil.current() - TIME_THREADLOCAL.get());
            } else {
                operLog.setCostTime(0L);
            }
            operLog.setOperTime(DateUtil.date());
            //保存数据库
            publisher.publishEvent(operLog);
        } catch (Exception ex) {
            logger.error("日志记录失败:{}", ex.getMessage());
        } finally {
            if (TIME_THREADLOCAL != null) {
                TIME_THREADLOCAL.remove();
            }
        }
    }

    /**
     * 获取注解中对方法的描述信息 用于Controller层注解
     *
     * @param log     日志
     * @param operLog 操作日志
     * @throws Exception
     */
    public void getControllerMethodDescription(JoinPoint joinPoint, RemoteLog log, OperLogEvent operLog, Object jsonResult, HttpServletRequest request) throws Exception {
        // 设置action动作
        operLog.setBusinessType(log.businessType().ordinal());
        // 设置标题
        operLog.setTitle(log.title());
        // 设置操作人类别
        operLog.setOperatorType(log.operatorType().ordinal());
        // 是否需要保存request请求头
        if (log.isSaveRequestHeader()) {
            // 获取参数的信息，传入到数据库中。
            setHeaderValue(operLog, request);
        }
        // 是否需要保存request，参数和值
        if (log.isSaveRequestData()) {
            // 获取参数的信息，传入到数据库中。
            setRequestValue(joinPoint, operLog, log.excludeParamNames());
        }
        // 是否需要保存response，参数和值
        if (log.isSaveResponseData() && ObjectUtil.isNotNull(jsonResult)) {
            operLog.setJsonResult(StringUtils.substring(JSONUtil.toJsonStr(jsonResult), 0, 2000));
        }
    }


    /**
     * 获取请求的头部信息，放到log中
     *
     * @param operLog 操作日志
     * @throws Exception 异常
     */
    private void setHeaderValue(OperLogEvent operLog, HttpServletRequest request) {
        Map<String, String> headerMap = ServletUtil.getHeaderMap(request);
        if (MapUtil.isNotEmpty(headerMap)) {
            Iterator<String> it = headerMap.keySet().iterator();
            while (it.hasNext()) {
                String key = it.next();
                if (ArrayUtil.containsIgnoreCase(EXCLUDE_HEADERS, key)) {
                    it.remove();
                }
            }
            String params = JSONUtil.toJsonStr(headerMap);
            operLog.setOperHeader(StringUtils.substring(params, 0, 1000));
            if (StrUtil.isBlank(operLog.getShopUnique()) && (headerMap.containsKey("shop_unique") || headerMap.containsValue("shopUnique"))) {
                String shopUnique = headerMap.get("shop_unique");
                if (StrUtil.isBlank(shopUnique)) {
                    shopUnique = headerMap.get("shopUnique");
                }
                operLog.setShopUnique(shopUnique);

            }
        }
    }


    /**
     * 获取请求的参数，放到log中
     *
     * @param operLog 操作日志
     * @throws Exception 异常
     */
    private void setRequestValue(JoinPoint joinPoint, OperLogEvent operLog, String[] excludeParamNames) {
        Map<String, String> paramsMap = ServletUtils.getParamMap(ServletUtils.getRequest());
        String requestMethod = operLog.getRequestMethod();
        if (HttpMethod.PUT.name().equals(requestMethod) || HttpMethod.POST.name().equals(requestMethod)) {
            StringBuffer stringBuffer = new StringBuffer();
            if (ObjectUtil.isNotNull(paramsMap) && paramsMap.size() > 0) {
                if (StrUtil.isBlank(operLog.getShopUnique()) && (paramsMap.containsKey("shop_unique") || paramsMap.containsKey("shopUnique"))) {
                    String shopUnique = paramsMap.get("shop_unique");
                    if (StrUtil.isBlank(shopUnique)) {
                        shopUnique = paramsMap.get("shopUnique");
                    }
                    operLog.setShopUnique(shopUnique);
                }
                stringBuffer.append("request:").append(JSONUtil.toJsonStr(paramsMap));
            }
            if (ObjectUtil.isNotNull(joinPoint.getArgs())) {
                String params = argsArrayToString(joinPoint.getArgs(), excludeParamNames);
                if (StrUtil.isNotBlank(params)) {
                    if (stringBuffer.length() > 0) {
                        stringBuffer.append(", ");
                    }
                    if (StrUtil.isBlank(operLog.getShopUnique()) && JSONUtil.isTypeJSONObject(params)) {
                        JSONObject paramJson = JSONUtil.parseObj(params);
                        if (paramJson.containsKey("shop_unique")) {
                            operLog.setShopUnique(paramJson.getStr("shop_unique"));
                        } else if (paramJson.containsKey("shopUnique")) {
                            operLog.setShopUnique(paramJson.getStr("shopUnique"));
                        }
                    }
                    stringBuffer.append("body:").append(params);
                }
            }
            operLog.setOperParam(StringUtils.substring(stringBuffer.toString(), 0, 2000));
        } else {
            operLog.setOperParam(StringUtils.substring(JSONUtil.toJsonStr(paramsMap), 0, 2000));
        }
    }

    /**
     * 参数拼装
     */
    private String argsArrayToString(Object[] paramsArray, String[] excludeParamNames) {
        StringJoiner params = new StringJoiner(" ");
        if (ArrayUtil.isEmpty(paramsArray)) {
            return params.toString();
        }
        for (Object o : paramsArray) {
            if (ObjectUtil.isNotNull(o) && !isFilterObject(o)) {
                String str = JSONUtil.toJsonStr(o);
                if (StrUtil.startWith(str, "[") && StrUtil.endWith(str, "]")) {
                    List<Dict> dictList = JSONUtil.toList(str, Dict.class);
                    dictList.forEach(dict -> {
                        if (MapUtil.isNotEmpty(dict)) {
                            MapUtil.removeAny(dict, EXCLUDE_PROPERTIES);
                            MapUtil.removeAny(dict, excludeParamNames);
                        }
                    });
                    str = JSONUtil.toJsonStr(dictList);
                    params.add(str);
                } else if (StrUtil.startWith(str, "{") && StrUtil.endWith(str, "}")){
                    Dict dict = JSONUtil.toBean(str, Dict.class);
                    if (MapUtil.isNotEmpty(dict)) {
                        MapUtil.removeAny(dict, EXCLUDE_PROPERTIES);
                        MapUtil.removeAny(dict, excludeParamNames);
                        str = JSONUtil.toJsonStr(dict);
                    }
                    params.add(str);
                }
            }
        }
        return params.toString();
    }

    /**
     * 判断是否需要过滤的对象。
     *
     * @param o 对象信息。
     * @return 如果是需要过滤的对象，则返回true；否则返回false。
     */
    @SuppressWarnings("rawtypes")
    public boolean isFilterObject(final Object o) {
        Class<?> clazz = o.getClass();
        if (clazz.isArray()) {
            return clazz.getComponentType().isAssignableFrom(MultipartFile.class);
        } else if (Collection.class.isAssignableFrom(clazz)) {
            Collection collection = (Collection) o;
            for (Object value : collection) {
                return value instanceof MultipartFile;
            }
        } else if (Map.class.isAssignableFrom(clazz)) {
            Map map = (Map) o;
            for (Object value : map.values()) {
                return value instanceof MultipartFile;
            }
        }
        return o instanceof MultipartFile || o instanceof HttpServletRequest || o instanceof HttpServletResponse
                || o instanceof BindingResult;
    }
}
