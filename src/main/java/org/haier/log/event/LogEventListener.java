package org.haier.log.event;

import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpUtil;
import cn.hutool.json.JSONUtil;
import org.apache.log4j.Logger;
import org.haier.log.aspect.RemoteLogConfig;
import org.springframework.context.event.EventListener;
import org.springframework.scheduling.annotation.Async;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * @Description
 * @ClassName LogEventListener
 * <AUTHOR>
 * @Date 2023/11/30 17:00
 **/
@EnableAsync
@Component
public class LogEventListener {

    private final Logger logger = Logger.getLogger(this.getClass());
    @Resource
    private RemoteLogConfig remoteLogConfig;

    /**
     * 保存系统日志记录
     */
    @Async
    @EventListener
    public void onApplicationEvent(OperLogEvent operLogEvent) {
        operLogEvent.setProjectCode(remoteLogConfig.getProjectCode());
        operLogEvent.setEnv(remoteLogConfig.getEnv());
        String body = JSONUtil.toJsonStr(operLogEvent);
        String url = remoteLogConfig.getUrl();
        try {
            if (StrUtil.isNotBlank(url)) {
                logger.info("---存储日志接口：----url: " + url + "----body:--" + body);
                String result = HttpUtil.post(url, body);
                logger.info("--------存储日志接口返回值：" + result);
            }
        } catch (Exception e) {
            logger.error("---存储日志接口异常----url: " + url + "----body:--" + body + "-----" + e.getMessage());
        }
    }
}
