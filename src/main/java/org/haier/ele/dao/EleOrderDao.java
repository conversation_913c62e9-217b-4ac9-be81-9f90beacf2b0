package org.haier.ele.dao;

import java.util.List;
import java.util.Map;

import org.apache.ibatis.annotations.Param;
import org.haier.ele.entity.SaleList;
import org.haier.ele.entity.SaleListDetail;


/**
 * 描述：饿了么外卖订单dao
 * <AUTHOR>
 * @version v1.0
 *
 */
public interface EleOrderDao {
	
	/**
	 * 添加一条销售信息
	 * @param saleList:订单实体类
	 * @return int
	 */
	public int addSaleList(SaleList saleList);
	
	/**
	 * 添加一条销售单明细信息
	 * @param saleDetail：订单明细实体类
	 * @return int
	 */
	public int addSaleDetail(@Param("saleDetailList")List<SaleListDetail> saleDetailList);
	
	/**
	 * 获取商品信息
	 * @param 
	 * @return 
	 */
	public Map<String ,Object> getGoodsInfo(String goods_id);
	
	/**
	 * 修改销售发货状态信息
	 * @param saleList:订单实体类
	 * @return int
	 */
	public int updatSaleList(SaleList saleList);
	
	/**
	 * 修改销售付款状态信息
	 * @param saleList:订单实体类
	 * @return int
	 */
	public int updatSaleListState(SaleList saleList);
	
	/**
	 * 根据订单编号获取店铺饿了么授权token
	 * @param sale_list_unique:订单编号
	 * @return String
	 */
	public String getEleToken(String saleListUnique);
	
	/**
	 * 根据店铺唯一编号获取店铺饿了么ele_shopId和授权token
	 * @param shop_unique:店铺唯一编号
	 * @return String
	 */
	public Map<String ,Object> getEleShopId(String shopUnique);
	
	/**
	 * 根据店铺唯一编号获取店铺所有商品列表
	 * @param shop_unique:店铺唯一编号
	 */
	public List<Map<String ,Object>> getGoodsList(String shopUnique);
}
