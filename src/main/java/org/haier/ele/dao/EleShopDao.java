package org.haier.ele.dao;

import java.util.List;
import java.util.Map;

public interface EleShopDao {
	
	/**
	 * 获取商户信息
	 * @param shop_unique 店铺编号
	 * @return
	 */
	public Map<String ,Object> getShopInfo(String shop_unique);
	
	/**
	 * 修改商户饿了么授权token
	 * @param shop_unique 店铺编号
	 * @param ele_token 授权token
	 * @return
	 */
	public int updateShopEleToken(Map<String ,Object> params);
	
	/**
	 * 获取饿了么授权token失效时间为当天的记录
	 * @return
	 */
	public List<Map<String ,Object>> getAuthList();
}
