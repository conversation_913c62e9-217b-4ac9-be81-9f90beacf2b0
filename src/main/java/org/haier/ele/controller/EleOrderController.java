package org.haier.ele.controller;

import javax.annotation.Resource;

import org.haier.ele.service.EleOrderService;
import org.haier.ele.util.Qutil;
import org.haier.shop.util.ShopsResult;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

/**
 * 饿了么订单接口
 */
@Controller
@RequestMapping("/ele/order")
public class EleOrderController {
	
	@Resource
	private EleOrderService eleOrderService;
	
	/**
	 * 查询店铺当前生效合同类型
	 * @param shopUnique 商家唯一标示
	 * @return 
	 */	
	@RequestMapping("/getEffectServicePackContract.do")
	@ResponseBody
	public ShopsResult getEffectServicePackContract(String shopUnique) {
		Qutil.getLogger().info("查询店铺当前生效合同类型，shopUnique："+shopUnique);
		
		ShopsResult ShopsResult = eleOrderService.getEffectServicePackContract(shopUnique);
		
		return ShopsResult;
	}
	
	/**
	 * 查询店铺未处理订单
	 * @param shopUnique 商家唯一标示
	 * @return 
	 */	
	@RequestMapping("/getUnprocessOrders.do")
	@ResponseBody
	public ShopsResult getUnprocessOrders(String shopUnique) {
		Qutil.getLogger().info("查询店铺未处理订单，shopUnique："+shopUnique);
		
		ShopsResult ShopsResult = eleOrderService.getUnprocessOrders(shopUnique);
		
		return ShopsResult;
	}
	
	/**
	 * 获取饿了么订单详情
	 * @param orderId 订单编号
	 * @return 
	 */	
	@RequestMapping("/getOrder.do")
	@ResponseBody
	public ShopsResult getOrder(String orderId) {
		Qutil.getLogger().info("获取饿了么订单详情，orderId："+orderId);

		ShopsResult ShopsResult = eleOrderService.getOrder(orderId);
		
		return ShopsResult;
	}
	
	/**
	 * 商家确认订单
	 * @param orderId 订单编号
	 * @return 
	 */	
	@RequestMapping("/confirmOrderLite.do")
	@ResponseBody
	public ShopsResult confirmOrderLite(String orderId) {
		Qutil.getLogger().info("商家确认订单，orderId："+orderId);

		ShopsResult ShopsResult = eleOrderService.confirmOrderLite(orderId);
		
		return ShopsResult;
	}
	
	
	/**
	 * 商家取消订单
	 * @param orderId 订单编号
	 * @param type 取消原因
	 * @param remark 备注说明
	 * @return 
	 */	
	@RequestMapping("/cancelOrderLite.do")
	@ResponseBody
	public ShopsResult cancelOrderLite(String orderId,String type,String remark) {
		Qutil.getLogger().info("商家取消订单，orderId："+orderId+",type:"+type+",remark："+remark);

		ShopsResult ShopsResult = eleOrderService.cancelOrderLite(orderId, type, remark);
		
		return ShopsResult;
	}
	
	/**
	 * 商家同意退单/同意取消单
	 * @param orderId 订单编号
	 * @return 
	 */	
	@RequestMapping("/agreeRefundLite.do")
	@ResponseBody
	public ShopsResult agreeRefundLite(String orderId) {
		Qutil.getLogger().info("商家同意退单/同意取消单，orderId："+orderId);

		ShopsResult ShopsResult = eleOrderService.agreeRefundLite(orderId);
		
		return ShopsResult;
	}
	
	/**
	 * 商家不同意退单/不同意取消单
	 * @param orderId 订单编号
	 * @param reason 不同意原因
	 * @return 
	 */	
	@RequestMapping("/disagreeRefundLite.do")
	@ResponseBody
	public ShopsResult disagreeRefundLite(String orderId,String reason) {
		Qutil.getLogger().info("商家不同意退单/不同意取消单，orderId："+orderId+",reason:"+reason);

		ShopsResult ShopsResult = eleOrderService.disagreeRefundLite(orderId,reason);
		
		return ShopsResult;
	}
	
	/**
	 * 获取订单配送记录
	 * @param orderId 订单编号
	 * @return 
	 */	
	@RequestMapping("/getDeliveryStateRecord.do")
	@ResponseBody
	public ShopsResult getDeliveryStateRecord(String orderId) {
		Qutil.getLogger().info("获取订单配送记录，orderId："+orderId);

		ShopsResult ShopsResult = eleOrderService.getDeliveryStateRecord(orderId);
		
		return ShopsResult;
	}
	
	/**
	 * 配送异常或者物流拒单后选择自行配送
	 * @param orderId 订单编号
	 * @return 
	 */	
	@RequestMapping("/deliveryBySelfLite.do")
	@ResponseBody
	public ShopsResult deliveryBySelfLite(String orderId) {
		Qutil.getLogger().info("配送异常或者物流拒单后选择自行配送，orderId："+orderId);

		ShopsResult ShopsResult = eleOrderService.deliveryBySelfLite(orderId);
		
		return ShopsResult;
	}
	
	/**
	 * 配送异常或者物流拒单后选择不再配送
	 * @param orderId 订单编号
	 * @return 
	 */	
	@RequestMapping("/noMoreDeliveryLite.do")
	@ResponseBody
	public ShopsResult noMoreDeliveryLite(String orderId) {
		Qutil.getLogger().info("配送异常或者物流拒单后选择不再配送，orderId："+orderId);

		ShopsResult ShopsResult = eleOrderService.noMoreDeliveryLite(orderId);
		
		return ShopsResult;
	}
	
	/**
	 * 订单确认送达
	 * @param orderId 订单编号
	 * @return 
	 */	
	@RequestMapping("/receivedOrderLite.do")
	@ResponseBody
	public ShopsResult receivedOrderLite(String orderId) {
		Qutil.getLogger().info("订单确认送达，orderId："+orderId);

		ShopsResult ShopsResult = eleOrderService.receivedOrderLite(orderId);
		
		return ShopsResult;
	}
	
	/**
	 * 回复催单
	 * @param remindId 催单id
	 * @param orderId 订单di
	 * @param type 回复类别
	 * @param content 回复内容
	 * @return 
	 */	
	@RequestMapping("/replyReminder.do")
	@ResponseBody
	public ShopsResult replyReminder(String remindId,String orderId,String type,String content) {
		Qutil.getLogger().info("回复催单，remindId："+remindId+",orderId:"+orderId);

		ShopsResult ShopsResult = eleOrderService.replyReminder(remindId,orderId, type, content);
		
		return ShopsResult;
	}
	
	/**
	 * 获取订单退款信息
	 * @param orderId 订单编号
	 * @return 
	 */	
	@RequestMapping("/getRefundOrder.do")
	@ResponseBody
	public ShopsResult getRefundOrder(String orderId) {
		Qutil.getLogger().info("获取订单退款信息，orderId："+orderId);

		ShopsResult ShopsResult = eleOrderService.getRefundOrder(orderId);
		
		return ShopsResult;
	}
	
	/**
	 * 呼叫配送
	 * @param orderId 订单编号
	 * @param fee 小费 1-8的整数
	 * @return 
	 */	
	@RequestMapping("/callDelivery.do")
	@ResponseBody
	public ShopsResult callDelivery(String orderId,String fee) {
		Qutil.getLogger().info("呼叫配送，orderId："+orderId+",fee"+fee);

		ShopsResult ShopsResult = eleOrderService.callDelivery(orderId,fee);
		
		return ShopsResult;
	}
	
	/**
	 * 取消呼叫配送
	 * @param orderId 订单编号
	 * @return 
	 */	
	@RequestMapping("/cancelDelivery.do")
	@ResponseBody
	public ShopsResult cancelDelivery(String orderId) {
		Qutil.getLogger().info("取消呼叫配送，orderId："+orderId);

		ShopsResult ShopsResult = eleOrderService.cancelDelivery(orderId);
		
		return ShopsResult;
	}
	
	/**
	 * 获取店铺未回复的催单
	 * @param shopUnique 店铺标示
	 * @return 
	 */	
	@RequestMapping("/getUnreplyReminders.do")
	@ResponseBody
	public ShopsResult getUnreplyReminders(String shopUnique) {
		Qutil.getLogger().info("获取店铺未回复的催单，orderId："+shopUnique);

		ShopsResult ShopsResult = eleOrderService.getUnreplyReminders(shopUnique);
		
		return ShopsResult;
	}
	
	/**
	 * 查询店铺未处理的取消单
	 * @param shopUnique 店铺标示
	 * @return 
	 */	
	@RequestMapping("/getCancelOrders.do")
	@ResponseBody
	public ShopsResult getCancelOrders(String shopUnique) {
		Qutil.getLogger().info("查询店铺未处理的取消单，orderId："+shopUnique);

		ShopsResult ShopsResult = eleOrderService.getCancelOrders(shopUnique);
		
		return ShopsResult;
	}
	
	/**
	 * 查询店铺未处理的退单
	 * @param shopUnique 店铺标示
	 * @return 
	 */	
	@RequestMapping("/getRefundOrders.do")
	@ResponseBody
	public ShopsResult getRefundOrders(String shopUnique) {
		Qutil.getLogger().info("查询店铺未处理的退单，orderId："+shopUnique);

		ShopsResult ShopsResult = eleOrderService.getRefundOrders(shopUnique);
		
		return ShopsResult;
	}
	
	/**
	 * 查询全部订单
	 * @param shopUnique 店铺标示
	 * @param pageNo 页码
	 * @param pageSize 每页获取条数
	 * @param date 日期,默认当天,格式:yyyy-MM-dd
	 * @return 
	 */	
	@RequestMapping("/getAllOrders.do")
	@ResponseBody
	public ShopsResult getAllOrders(String shopUnique,int pageNo,int pageSize,String date) {
		Qutil.getLogger().info("查询全部订单，orderId："+shopUnique+",pageNo:"+pageNo+",pageSize:"+pageSize+",date"+date);

		ShopsResult ShopsResult = eleOrderService.getAllOrders(shopUnique, pageNo, pageSize, date);
		
		return ShopsResult;
	}
	
	
	/**
	 * 众包订单询价，获取配送费
	 * @param orderId 订单编号
	 * @return 
	 */	
	@RequestMapping("/getDeliveryFeeForCrowd.do")
	@ResponseBody
	public ShopsResult getDeliveryFeeForCrowd(String orderId) {
		Qutil.getLogger().info("众包订单询价，获取配送费，orderId："+orderId);

		ShopsResult ShopsResult = eleOrderService.getDeliveryFeeForCrowd(orderId);
		
		return ShopsResult;
	}
	
	
	/**
	 * 订单加小费
	 * @param orderId 订单编号
	 * @param tip 小费金额，整数
	 * @return 
	 */	
	@RequestMapping("/addDeliveryTipByOrderId.do")
	@ResponseBody
	public ShopsResult addDeliveryTipByOrderId(String orderId,int tip) {
		Qutil.getLogger().info("订单加小费，orderId："+orderId+",tip:"+tip);

		ShopsResult ShopsResult = eleOrderService.addDeliveryTipByOrderId(orderId, tip);
		
		return ShopsResult;
	}
	
	/**
	 * 非自配送餐厅标记已出餐
	 * @param orderId 订单编号
	 * @return 
	 */	
	@RequestMapping("/setOrderPrepared.do")
	@ResponseBody
	public ShopsResult setOrderPrepared(String orderId) {
		Qutil.getLogger().info("非自配送餐厅标记已出餐，orderId："+orderId);

		ShopsResult ShopsResult = eleOrderService.setOrderPrepared(orderId);
		
		return ShopsResult;
	}
	
}
