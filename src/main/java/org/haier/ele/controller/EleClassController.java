package org.haier.ele.controller;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.logging.Logger;

import javax.annotation.Resource;

import org.haier.ele.service.EleShopService;
import org.haier.ele.util.EleConfig;
import org.haier.meituan.util.MUtil;
import org.haier.shop.util.ShopsResult;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import com.alibaba.fastjson.JSON;

import eleme.openapi.sdk.api.entity.product.OCategory;
import eleme.openapi.sdk.api.exception.ServiceException;
import eleme.openapi.sdk.api.service.ProductService;
import eleme.openapi.sdk.config.Config;
import eleme.openapi.sdk.oauth.OAuthClient;
import eleme.openapi.sdk.oauth.response.Token;

@Controller
@RequestMapping("/ele/class")
public class EleClassController {
	
	private static final Logger logger = Logger.getGlobal();
	
	@Resource
	private EleShopService eleShopService;
	
	Config config = new Config(EleConfig.isSandbox, EleConfig.key, EleConfig.secret);
	
	/**
	 * 商品分类列表
	 * @param shop_unique 店铺编号
	 * @return
	 * @throws ServiceException 
	 */
	@RequestMapping("/getClassList.do")
	public String authorize(String shop_unique,Model model) throws ServiceException{
		logger.info("获取饿了么店铺商品分类列表");
		try {
			Map<String ,Object> shopInfo = eleShopService.getShopInfo(shop_unique);
			String ele_token = MUtil.strObject(shopInfo.get("ele_token"));
			String ele_shopId = MUtil.strObject(shopInfo.get("ele_shopId"));
			if(ele_token != null && !ele_token.equals("")){//已授权
				Token token = new Token();
				token.setAccessToken(ele_token);
				//获取分类列表
				ProductService productService = new ProductService(config, token);
				List<OCategory> classList = productService.getShopCategories(Long.valueOf(ele_shopId));
				model.addAttribute("classList", classList);
				model.addAttribute("ele_token", ele_token);
				model.addAttribute("ele_shopId", ele_shopId);
				return "/html/ele/classList.jsp";
			}else{//未授权
				Map<String ,Object> params = new HashMap<String, Object>();
				params.put("shop_unique", shop_unique);
				params.put("url", "class/getClassList.do");
				OAuthClient client = new OAuthClient(config);
				String authUrl = client.getAuthUrl(EleConfig.redirect_uri, "all", JSON.toJSONString(params));
				model.addAttribute("authUrl", authUrl);
				return "/html/ele/shopAuth.jsp";
			}
		} catch (Exception e) {
			OAuthClient client = new OAuthClient(config);
			Map<String ,Object> params = new HashMap<String, Object>();
			params.put("shop_unique", shop_unique);
			params.put("url", "class/getClassList.do");
			String authUrl = client.getAuthUrl(EleConfig.redirect_uri, "all", JSON.toJSONString(params));
			model.addAttribute("authUrl", authUrl);
			return "/html/ele/shopAuth.jsp";
		}
	}
	
	/**
	 * 跳转添加商品分类页面
	 * @param ele_shopId 分类id
	 * @param ele_token 授权token
	 * @return
	 * @throws ServiceException 
	 */
	@RequestMapping("/addClassPage.do")
	public String addClassPage(String ele_shopId,String ele_token,Model model) throws ServiceException{
		logger.info("跳转添加商品分类页面");
		model.addAttribute("ele_shopId", ele_shopId);
		model.addAttribute("ele_token", ele_token);
		return "/html/ele/classForm.jsp";
	}
	
	/**
	 * 跳转修改商品分类页面
	 * @param categoryId 分类id
	 * @param ele_token 授权token
	 * @return
	 * @throws ServiceException 
	 */
	@RequestMapping("/updateClassPage.do")
	public String updateClassPage(String categoryId,String ele_token,Model model) throws ServiceException{
		logger.info("跳转修改商品分类页面");
		//获取商品分类详情
		Token token = new Token();
		token.setAccessToken(ele_token);
		ProductService productService = new ProductService(config, token);
		OCategory oCategory = productService.getCategory(Long.valueOf(categoryId));
		model.addAttribute("oCategory", oCategory);
		model.addAttribute("categoryId", categoryId);
		model.addAttribute("ele_token", ele_token);
		return "/html/ele/classForm.jsp";
	}
	
	/**
	 * 保存/修改
	 * @param categoryId 分类id
	 * @param ele_token 授权token
	 * @param ele_shopId 店铺id
	 * @return
	 * @throws ServiceException 
	 */
	@RequestMapping("/saveClass.do")
	@ResponseBody
	public ShopsResult saveClass(String ele_token,String ele_shopId,String categoryId,String name,String description) throws ServiceException{
		logger.info("保存/修改商品分类");
		ShopsResult shopsResult = new ShopsResult();
		try {
			Token token = new Token();
			token.setAccessToken(ele_token);
			ProductService productService = new ProductService(config, token);
			if(ele_shopId != null && !ele_shopId.equals("")){//保存
				productService.createCategory(Long.valueOf(ele_shopId), name, description);
			}
			if(categoryId != null && !categoryId.equals("")){//修改
				productService.updateCategory(Long.valueOf(categoryId), name, description);
			}
			shopsResult.setStatus(1);
			shopsResult.setMsg("成功");		
		} catch (ServiceException e) {
			shopsResult.setStatus(0);
			shopsResult.setMsg("异常："+e.getMessage());		
		}
		return shopsResult;
	}
	
	/**
	 * 删除
	 * @param categoryId 分类id
	 * @param ele_token 授权token
	 * @return
	 * @throws ServiceException 
	 */
	@RequestMapping("/deleteClass.do")
	@ResponseBody
	public ShopsResult deleteClass(String categoryId,String ele_token) throws ServiceException{
		logger.info("删除商品分类");
		ShopsResult shopsResult = new ShopsResult();
		try {
			Token token = new Token();
			token.setAccessToken(ele_token);
			ProductService productService = new ProductService(config, token);
			productService.removeCategory(Long.valueOf(categoryId));
			shopsResult.setStatus(1);
			shopsResult.setMsg("成功");		
		} catch (ServiceException e) {
			shopsResult.setStatus(0);
			shopsResult.setMsg("异常："+e.getMessage());		
		}
		return shopsResult;
	}
}
