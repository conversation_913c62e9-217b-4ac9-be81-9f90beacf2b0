package org.haier.ele.controller;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.logging.Logger;

import javax.annotation.Resource;

import org.haier.ele.entity.Goods;
import org.haier.ele.service.EleGoodsService;
import org.haier.ele.service.EleOrderService;
import org.haier.ele.service.EleShopService;
import org.haier.ele.util.EleConfig;
import org.haier.meituan.util.MUtil;
import org.haier.shop.util.ShopsResult;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;

import eleme.openapi.sdk.api.entity.product.OCategory;
import eleme.openapi.sdk.api.entity.product.OItem;
import eleme.openapi.sdk.api.entity.product.OItemIdWithSpecIds;
import eleme.openapi.sdk.api.exception.ServiceException;
import eleme.openapi.sdk.api.service.ProductService;
import eleme.openapi.sdk.config.Config;
import eleme.openapi.sdk.oauth.OAuthClient;
import eleme.openapi.sdk.oauth.response.Token;

@Controller
@RequestMapping("/ele/goods")
public class EleGoodsController {
	
	private static final Logger logger = Logger.getGlobal();
	
	@Resource
	private EleShopService eleShopService;
	
	@Resource
	private EleOrderService eleOrderService;
	
	@Resource
	private EleGoodsService eleGoodsService;
	
	Config config = new Config(EleConfig.isSandbox, EleConfig.key, EleConfig.secret);
	
	/**
	 * 商品列表
	 * @param shop_unique 店铺编号
	 * @return
	 * @throws ServiceException 
	 */
	@RequestMapping("/getGoodsList.do")
	public String getGoodsList(String shop_unique,Model model) throws ServiceException{
		logger.info("获取饿了么店铺商品列表");
		try {
			Map<String ,Object> shopInfo = eleShopService.getShopInfo(shop_unique);
			String ele_token = MUtil.strObject(shopInfo.get("ele_token"));
			String ele_shopId = MUtil.strObject(shopInfo.get("ele_shopId"));
			if(ele_token != null && !ele_token.equals("")){//已授权
				Token token = new Token();
				token.setAccessToken(ele_token);
				//获取分类列表
				ProductService productService = new ProductService(config, token);
				List<OCategory> classList = productService.getShopCategories(Long.valueOf(ele_shopId));
				for(int i=0;i<classList.size();i++){
					Map<Long,OItem> resultMap = productService.getItemsByCategoryId(classList.get(i).getId());
					//map的value值转list
					List<OItem> goodsList = new ArrayList<OItem>();
					Iterator it = resultMap.keySet().iterator();
					while (it.hasNext()) {
						Long key = (Long) it.next();
						OItem oItem = resultMap.get(key);
						goodsList.add(oItem);
					}
					classList.get(i).setIsValid(goodsList.size());
				}
				model.addAttribute("classList", classList);
				model.addAttribute("ele_token", ele_token);
				model.addAttribute("ele_shopId", ele_shopId);
				model.addAttribute("shop_unique", shop_unique);
				return "/html/ele/goodsList.jsp";
			}else{//未授权
				Map<String ,Object> params = new HashMap<String, Object>();
				params.put("shop_unique", shop_unique);
				params.put("url", "goods/getGoodsList.do");
				OAuthClient client = new OAuthClient(config);
				String authUrl = client.getAuthUrl(EleConfig.redirect_uri, "all", JSON.toJSONString(params));
				model.addAttribute("authUrl", authUrl);
				return "/html/ele/shopAuth.jsp";
			}
		} catch (Exception e) {
			Map<String ,Object> params = new HashMap<String, Object>();
			params.put("shop_unique", shop_unique);
			params.put("url", "goods/getGoodsList.do");
			OAuthClient client = new OAuthClient(config);
			String authUrl = client.getAuthUrl(EleConfig.redirect_uri, "all", JSON.toJSONString(params));
			model.addAttribute("authUrl", authUrl);
			return "/html/ele/shopAuth.jsp";
		}
	}
	
	/**
	 *  获取一个分类下的所有商品
	 * @param categoryId 分类id
	 * @param ele_token 授权token
	 * @return
	 */
	@RequestMapping("/getGoodsListByCategoryId.do")
	@ResponseBody
	public ShopsResult getGoodsListByCategoryId(String ele_token,String categoryId){
		logger.info(" 获取一个分类下的所有商品");
		ShopsResult shopsResult = new ShopsResult();
		try {
			Token token = new Token();
			token.setAccessToken(ele_token);
			ProductService productService = new ProductService(config, token);
			Map<Long,OItem> resultMap = productService.getItemsByCategoryId(Long.valueOf(categoryId));
			//map的value值转list
			List<OItem> goodsList = new ArrayList<OItem>();
			Iterator it = resultMap.keySet().iterator();
			while (it.hasNext()) {
				Long key = (Long) it.next();
				OItem oItem = resultMap.get(key);
				goodsList.add(oItem);
			}
			shopsResult.setData(goodsList);
			shopsResult.setStatus(1);
			shopsResult.setMsg("成功");		
		} catch (ServiceException e) {
			shopsResult.setStatus(0);
			shopsResult.setMsg("异常："+e.getMessage());		
		}
		return shopsResult;
	}
	
	/**
	 * 添加商品页面
	 * @param ele_token 授权token
	 * @param ele_shopId 店铺id
	 * @return
	 * @throws ServiceException 
	 */
	@RequestMapping("/addGoodsPage.do")
	public String addGoodsPage(String ele_token,String ele_shopId,String shop_unique,Model model) throws ServiceException{
		logger.info("跳转添加商品页面");
		Token token = new Token();
		token.setAccessToken(ele_token);
		//获取分类列表
		ProductService productService = new ProductService(config, token);
		List<OCategory> classList = productService.getShopCategories(Long.valueOf(ele_shopId));
		//获取商家所有商品列表
		List<Map<String ,Object>> goodsList = eleOrderService.getGoodsList(shop_unique);
		model.addAttribute("goodsList", goodsList);
		model.addAttribute("classList", classList);
		model.addAttribute("ele_token", ele_token);
		return "/html/ele/addGoods.jsp";
	}
	
	/**
	 * 修改商品页面
	 * @param ele_token 授权token
	 * @return
	 * @throws ServiceException 
	 */
	@RequestMapping("/updateGoodsPage.do")
	public String updateGoodsPage(String ele_token,String ele_shopId,String itemId,String shop_unique,Model model) throws ServiceException{
		logger.info("跳转修改商品页面");
		Token token = new Token();
		token.setAccessToken(ele_token);
		//获取商品详情
		ProductService productService = new ProductService(config, token);
		OItem oItem = productService.getItem(Long.valueOf(itemId));
		model.addAttribute("oItem", oItem);
		//获取分类列表
		List<OCategory> classList = productService.getShopCategories(Long.valueOf(ele_shopId));
		//获取商家所有商品列表
		List<Map<String ,Object>> goodsList = eleOrderService.getGoodsList(shop_unique);
		model.addAttribute("goodsList", goodsList);
		model.addAttribute("classList", classList);
		model.addAttribute("ele_token", ele_token);
		return "/html/ele/updateGoods.jsp";
	}
	
	/**
	 *  添加、更新
	 * @param ele_token 授权token
	 * @return
	 */
	@RequestMapping("/saveGoods.do")
	@ResponseBody
	public ShopsResult saveGoods(String ele_token,Goods goods,String imgBase64){
		logger.info("添加更新商品");
		ShopsResult shopsResult = new ShopsResult();
		try {
			Token token = new Token();
			token.setAccessToken(ele_token);
			if(goods.getItemId() != null && !goods.getItemId().equals("")){//修改
				shopsResult = eleGoodsService.updateGoods(config, token, goods, imgBase64);
			}else{//保存
				shopsResult = eleGoodsService.addGoods(config, token, goods, imgBase64);
			}
		} catch (ServiceException e) {
			shopsResult.setStatus(0);
			shopsResult.setMsg("异常："+e.getMessage());		
		}
		return shopsResult;
	}
	
	/**
	 *  批量估清
	 * @param specIdsJson 商品及商品规格的字符串
	 * @param ele_token 授权token
	 * @return
	 */
	@RequestMapping("/batchClearStock.do")
	@ResponseBody
	public ShopsResult batchClearStock(String ele_token,String specIdsJson){
		logger.info("批量估清");
		ShopsResult shopsResult = new ShopsResult();
		try {
			Token token = new Token();
			token.setAccessToken(ele_token);
			ProductService productService = new ProductService(config, token);
			List<OItemIdWithSpecIds> specIds = JSONArray.parseArray(specIdsJson, OItemIdWithSpecIds.class);
			productService.batchClearStock(specIds);
			shopsResult.setStatus(1);
			shopsResult.setMsg("成功");		
		} catch (ServiceException e) {
			shopsResult.setStatus(0);
			shopsResult.setMsg("异常："+e.getMessage());		
		}
		return shopsResult;
	}
	
	/**
	 *  批量置满
	 * @param specIdsJson 商品及商品规格的字符串
	 * @param ele_token 授权token
	 * @return
	 */
	@RequestMapping("/batchFillStock.do")
	@ResponseBody
	public ShopsResult batchFillStock(String ele_token,String specIdsJson){
		logger.info("批量置满");
		ShopsResult shopsResult = new ShopsResult();
		try {
			Token token = new Token();
			token.setAccessToken(ele_token);
			ProductService productService = new ProductService(config, token);
			List<OItemIdWithSpecIds> specIds = JSONArray.parseArray(specIdsJson, OItemIdWithSpecIds.class);
			productService.batchFillStock(specIds);
			shopsResult.setStatus(1);
			shopsResult.setMsg("成功");		
		} catch (ServiceException e) {
			shopsResult.setStatus(0);
			shopsResult.setMsg("异常："+e.getMessage());		
		}
		return shopsResult;
	}
	
	/**
	 *  批量上架
	 * @param itemIdsJson 商品id列表字符串
	 * @param ele_token 授权token
	 * @return
	 */
	@RequestMapping("/batchListItems.do")
	@ResponseBody
	public ShopsResult batchListItems(String ele_token,String itemIdsJson){
		logger.info("批量上架");
		ShopsResult shopsResult = new ShopsResult();
		try {
			Token token = new Token();
			token.setAccessToken(ele_token);
			ProductService productService = new ProductService(config, token);
			List<Long> itemIds = JSONArray.parseArray(itemIdsJson, Long.class);

			productService.batchListItems(itemIds);
			shopsResult.setStatus(1);
			shopsResult.setMsg("成功");		
		} catch (ServiceException e) {
			shopsResult.setStatus(0);
			shopsResult.setMsg("异常："+e.getMessage());		
		}
		return shopsResult;
	}
	
	
	/**
	 *  批量下架
	 * @param itemIdsJson 商品id列表字符串
	 * @param ele_token 授权token
	 * @return
	 */
	@RequestMapping("/batchDelistItems.do")
	@ResponseBody
	public ShopsResult batchDelistItems(String ele_token,String itemIdsJson){
		logger.info("批量下架");
		ShopsResult shopsResult = new ShopsResult();
		try {
			Token token = new Token();
			token.setAccessToken(ele_token);
			ProductService productService = new ProductService(config, token);
			List<Long> itemIds = JSONArray.parseArray(itemIdsJson, Long.class);

			productService.batchDelistItems(itemIds);
			shopsResult.setStatus(1);
			shopsResult.setMsg("成功");		
		} catch (ServiceException e) {
			shopsResult.setStatus(0);
			shopsResult.setMsg("异常："+e.getMessage());		
		}
		return shopsResult;
	}
	
	/**
	 *  批量删除
	 * @param itemIdsJson 商品id列表字符串
	 * @param ele_token 授权token
	 * @return
	 */
	@RequestMapping("/batchRemoveItems.do")
	@ResponseBody
	public ShopsResult batchRemoveItems(String ele_token,String itemIdsJson){
		logger.info("批量删除");
		ShopsResult shopsResult = new ShopsResult();
		try {
			Token token = new Token();
			token.setAccessToken(ele_token);
			ProductService productService = new ProductService(config, token);
			List<Long> itemIds = JSONArray.parseArray(itemIdsJson, Long.class);

			productService.batchRemoveItems(itemIds);
			shopsResult.setStatus(1);
			shopsResult.setMsg("成功");		
		} catch (ServiceException e) {
			shopsResult.setStatus(0);
			shopsResult.setMsg("异常："+e.getMessage());		
		}
		return shopsResult;
	}
}
