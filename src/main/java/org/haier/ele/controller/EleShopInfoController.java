package org.haier.ele.controller;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.logging.Logger;

import javax.annotation.Resource;

import org.haier.ele.entity.ShopPropperty;
import org.haier.ele.service.EleShopService;
import org.haier.ele.util.EleConfig;
import org.haier.meituan.util.MUtil;
import org.haier.shop.util.ShopsResult;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import com.alibaba.fastjson.JSON;

import eleme.openapi.sdk.api.entity.shop.OShop;
import eleme.openapi.sdk.api.entity.user.OAuthorizedShop;
import eleme.openapi.sdk.api.entity.user.OUser;
import eleme.openapi.sdk.api.exception.ServiceException;
import eleme.openapi.sdk.api.service.ShopService;
import eleme.openapi.sdk.api.service.UserService;
import eleme.openapi.sdk.config.Config;
import eleme.openapi.sdk.oauth.OAuthClient;
import eleme.openapi.sdk.oauth.response.Token;

@Controller
@RequestMapping("/ele/shop")
public class EleShopInfoController {
	
	private static final Logger logger = Logger.getGlobal();
	
	@Resource
	private EleShopService eleShopService;
	
	Config config = new Config(EleConfig.isSandbox, EleConfig.key, EleConfig.secret);
	
	/**
	 * 获取饿了么店铺信息
	 * @param shop_unique 店铺编号
	 * @return
	 * @throws ServiceException 
	 */
	@RequestMapping("/getInfo.do")
	public String authorize(String shop_unique,Model model) throws ServiceException{
		logger.info("获取饿了么店铺信息");
		try {
			Map<String ,Object> shopInfo = eleShopService.getShopInfo(shop_unique);
			String ele_token = MUtil.strObject(shopInfo.get("ele_token"));
			if(ele_token != null && !ele_token.equals("")){//已授权
				Token token = new Token();
				token.setAccessToken(ele_token);
				UserService userService = new UserService(config, token);
				OUser oUser = userService.getUser();
				List<OAuthorizedShop> authorizedShops = oUser.getAuthorizedShops();
				model.addAttribute("shops", authorizedShops);
				model.addAttribute("shopInfo", shopInfo);
				model.addAttribute("shop_unique", shop_unique);
				return "/html/ele/shopInfo.jsp";
			}else{//未授权
				Map<String ,Object> params = new HashMap<String, Object>();
				params.put("shop_unique", shop_unique);
				params.put("url", "shop/getInfo.do");
				OAuthClient client = new OAuthClient(config);
				String authUrl = client.getAuthUrl(EleConfig.redirect_uri, "all", JSON.toJSONString(params));
				model.addAttribute("authUrl", authUrl);
				return "/html/ele/shopAuth.jsp";
			}
		} catch (Exception e) {
			Map<String ,Object> params = new HashMap<String, Object>();
			params.put("shop_unique", shop_unique);
			params.put("url", "shop/getInfo.do");
			OAuthClient client = new OAuthClient(config);
			String authUrl = client.getAuthUrl(EleConfig.redirect_uri, "all", JSON.toJSONString(params));
			model.addAttribute("authUrl", authUrl);
			return "/html/ele/shopAuth.jsp";
		}
	}
	
	/**
	 * 查看店铺详情
	 * @param shop_unique 店铺编号
	 * @return
	 * @throws NumberFormatException 
	 * @throws ServiceException 
	 */
	@RequestMapping("/getShopDetail.do")
	public String getShopDetail(String ele_token,String shopId,Model model) throws ServiceException{
		logger.info("查看店铺详情");
		Token token = new Token();
		token.setAccessToken(ele_token);
		ShopService shopService = new ShopService(config, token);
		OShop oShop = shopService.getShop(Long.valueOf(shopId));
		model.addAttribute("oShop", oShop);
		return "/html/ele/shopDetail.jsp";
	}
	
	/**
	 * 跳转店铺修改页面
	 * @param shop_unique 店铺编号
	 * @return
	 * @throws NumberFormatException 
	 * @throws ServiceException 
	 */
	@RequestMapping("/updateShopDetailPage.do")
	public String updateShopDetailPage(String ele_token,String shopId,String shop_unique,Model model) throws ServiceException{
		logger.info("跳转店铺修改页面");
		Token token = new Token();
		token.setAccessToken(ele_token);
		ShopService shopService = new ShopService(config, token);
		OShop oShop = shopService.getShop(Long.valueOf(shopId));
		model.addAttribute("oShop", oShop);
		model.addAttribute("ele_token", ele_token);
		model.addAttribute("shopId", shopId);
		model.addAttribute("shop_unique", shop_unique);
		return "/html/ele/updateShopDetail.jsp";
	}
	
	/**
	 * 店铺信息修改
	 * @param 
	 * @return 
	 */	
	@RequestMapping("/updateShopDetail.do")
	@ResponseBody
	public ShopsResult updateShopDetail(String ele_token,String shopId,String shop_unique,ShopPropperty shopPropperty,String ImgBase64) {
		logger.info("店铺信息修改");
		ShopsResult shopsResult = new ShopsResult();
		try {
			Token token = new Token();
			token.setAccessToken(ele_token);
			shopsResult = eleShopService.updateShopDetail(config, token, shopId,shop_unique, shopPropperty,ImgBase64);
			
		} catch (ServiceException e) {
			shopsResult.setStatus(0);
			shopsResult.setMsg("异常："+e.getMessage());		
		}
		return shopsResult;
	}
	
	/**
	 * 跳转店铺预订单设置页面
	 * @param shop_unique 店铺编号
	 * @return
	 * @throws NumberFormatException 
	 * @throws ServiceException 
	 */
	@RequestMapping("/setBookingStatusPage.do")
	public String setBookingStatusPage(String ele_token,String shopId,Model model) throws ServiceException{
		logger.info("跳转店铺预订单设置页面");
		Token token = new Token();
		token.setAccessToken(ele_token);
		ShopService shopService = new ShopService(config, token);
		OShop oShop = shopService.getShop(Long.valueOf(shopId));
		model.addAttribute("oShop", oShop);
		model.addAttribute("ele_token", ele_token);
		model.addAttribute("shopId", shopId);
		return "/html/ele/setBookingStatus.jsp";
	}
	
	/**
	 * 店铺预订单设置
	 * @param 
	 * @return 
	 */	
	@RequestMapping("/setBookingStatus.do")
	@ResponseBody
	public ShopsResult setBookingStatus(String ele_token,String shopId,String enabled,String maxBookingDays) {
		logger.info("店铺预订单设置");
		ShopsResult shopsResult = new ShopsResult();
		try {
			Token token = new Token();
			token.setAccessToken(ele_token);
			shopsResult = eleShopService.setBookingStatus(config, token, shopId, enabled, maxBookingDays);
			
		} catch (ServiceException e) {
			shopsResult.setStatus(0);
			shopsResult.setMsg("异常："+e.getMessage());		
		}
		return shopsResult;
	}
}
