package org.haier.ele.controller;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.logging.Logger;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;

import org.haier.ele.service.EleShopService;
import org.haier.ele.util.EleConfig;
import org.haier.meituan.util.MUtil;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.RequestMapping;

import eleme.openapi.sdk.api.entity.user.OAuthorizedShop;
import eleme.openapi.sdk.api.entity.user.OUser;
import eleme.openapi.sdk.api.exception.ServiceException;
import eleme.openapi.sdk.api.service.UserService;
import eleme.openapi.sdk.config.Config;
import eleme.openapi.sdk.oauth.OAuthClient;
import eleme.openapi.sdk.oauth.response.Token;

@Controller
@RequestMapping("/ele/auth")
public class AuthController {
	
	private static final Logger logger = Logger.getGlobal();
	
	@Resource
	private EleShopService eleShopService;
	
	/**
	 * 商户授权回调方法
	 * @return
	 */
	@RequestMapping("/callback.do")
	public String callback(HttpServletRequest request,Model model){
		logger.info("商户授权回调方法");
		String code = request.getParameter("code");
		String state = request.getParameter("state");
		
		Map<String ,Object> stateMap = MUtil.jsonToMap(state);
		String shop_unique = MUtil.strObject(stateMap.get("shop_unique"));
		model.addAttribute("shop_unique", shop_unique);
    	model.addAttribute("url", MUtil.strObject(stateMap.get("url")));
		try {
	        Config config = new Config(EleConfig.isSandbox, EleConfig.key, EleConfig.secret);
			OAuthClient client = new OAuthClient(config);
	        Token token = client.getTokenByCode(code, EleConfig.redirect_uri);
	        if (token.isSuccess()) {
	            //更新商户授权token
	        	Map<String ,Object> params = new HashMap<String, Object>();
	        	params.put("ele_token", token.getAccessToken());
	        	params.put("shop_unique", shop_unique);
	        	params.put("ele_refresh_token", token.getRefreshToken());
	        	Date date = new Date();
	        	long seconds = date.getTime()+token.getExpires()*1000;
	        	String pattern = "yyyy-MM-dd HH:mm:ss";  
	        	SimpleDateFormat format = new SimpleDateFormat(pattern);  
	        	String ele_token_failure = format.format(new Date(seconds));  
	        	params.put("ele_token_failure", ele_token_failure);
	        	//获取店铺id
	        	UserService userService = new UserService(config, token);
	        	OUser oUser = userService.getUser();
				List<OAuthorizedShop> authorizedShops = oUser.getAuthorizedShops();
				params.put("ele_shopId", authorizedShops.get(0).getId());
	        	
				eleShopService.updateShopEleToken(params);
				request.getSession().setAttribute("shop_unique", shop_unique);
	        	return "/html/ele/shopAuth.jsp";
	        }else{
	        	return "/html/ele/shopAuth.jsp";
	        }
		} catch (ServiceException e) {
			return "/html/ele/shopAuth.jsp";
		}
	}
	
}
