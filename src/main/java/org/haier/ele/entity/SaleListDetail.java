package org.haier.ele.entity;


/**
 * 类名:com.palmshop.online.entity.SaleListDetail;
 * 描述:订单销售明细的实体类
 * <AUTHOR>
 *
 */
public class SaleListDetail {

	private Integer saleListDetailId;//订单id
	private Long saleListUnique;//销售单唯一标识
	private String goodsBarcode;//商品条形码
	private String goodsName;//商品名称
	private String goodsPicturepath;//商品图片路径
	private double saleListDetailCount;//商品数量
	private Double goodsOldPrice;//商品原价
	private Double saleListDetailPrice;//商品售价
	private Double saleListDetailSubtotal;//金额小计
	private Long goodsId;//商品id
	private Double goodsPurprice;//商品进价
	private Double commissionTotal;//提成小计
	public Integer getSaleListDetailId() {
		return saleListDetailId;
	}
	public void setSaleListDetailId(Integer saleListDetailId) {
		this.saleListDetailId = saleListDetailId;
	}
	public Long getSaleListUnique() {
		return saleListUnique;
	}
	public void setSaleListUnique(Long saleListUnique) {
		this.saleListUnique = saleListUnique;
	}
	public String getGoodsBarcode() {
		return goodsBarcode;
	}
	public void setGoodsBarcode(String goodsBarcode) {
		this.goodsBarcode = goodsBarcode;
	}
	public String getGoodsName() {
		return goodsName;
	}
	public void setGoodsName(String goodsName) {
		this.goodsName = goodsName;
	}
	public String getGoodsPicturepath() {
		return goodsPicturepath;
	}
	public void setGoodsPicturepath(String goodsPicturepath) {
		this.goodsPicturepath = goodsPicturepath;
	}
	public double getSaleListDetailCount() {
		return saleListDetailCount;
	}
	public void setSaleListDetailCount(double saleListDetailCount) {
		this.saleListDetailCount = saleListDetailCount;
	}
	public Double getGoodsOldPrice() {
		return goodsOldPrice;
	}
	public void setGoodsOldPrice(Double goodsOldPrice) {
		this.goodsOldPrice = goodsOldPrice;
	}
	public Double getSaleListDetailPrice() {
		return saleListDetailPrice;
	}
	public void setSaleListDetailPrice(Double saleListDetailPrice) {
		this.saleListDetailPrice = saleListDetailPrice;
	}
	public Double getSaleListDetailSubtotal() {
		return saleListDetailSubtotal;
	}
	public void setSaleListDetailSubtotal(Double saleListDetailSubtotal) {
		this.saleListDetailSubtotal = saleListDetailSubtotal;
	}
	public Long getGoodsId() {
		return goodsId;
	}
	public void setGoodsId(Long goodsId) {
		this.goodsId = goodsId;
	}
	public Double getGoodsPurprice() {
		return goodsPurprice;
	}
	public void setGoodsPurprice(Double goodsPurprice) {
		this.goodsPurprice = goodsPurprice;
	}
	public Double getCommissionTotal() {
		return commissionTotal;
	}
	public void setCommissionTotal(Double commissionTotal) {
		this.commissionTotal = commissionTotal;
	}
}
