package org.haier.ele.entity;

/**
 * 饿了么店铺信息实体类
 */
public class ShopPropperty {
		
	/**
	 * 店铺地址
	 */
	private String addressText;
	
	/**
	 * 经纬度，longitude和latitude用英文逗号分隔
	 */
	private String geo;
	   
	/**
	 * 配送费
	 */
	private Double agentFee;
	    
	/**
	 * 关店描述信息
	 */
	private String closeDescription;
	    
	/**
	 * 配送区域说明
	 */
	private String deliverDescription;
	 
	/**
	 * 配送范围
	 */
	private String deliverGeoJson;
	  
	/**
	 * 店铺简介
	 */
	private String description;
	   
	/**
	 * 店铺名称
	 */
	private String name;
	    
	/**
	 * 是否接受预定 0 不接受预定，1 接受预定
	 */
	private Integer isBookable;
	    
	/**
	 * 店铺营业时间
	 */
	private String openTime;
	    
	/**
	 * 店铺联系电话
	 */
	private String phone;
	    
	/**
	 * 店铺公告信息
	 */
	private String promotionInfo;
	    
	/**
	 * 店铺Logo的图片
	 */
	private String logoImageHash;
	    
	/**
	 * 是否支持开发票
	 */
	private Integer invoice;
	    
	/**
	 * 支持的最小发票金额
	 */
	private Double invoiceMinAmount;
	    
	/**
	 * 满xx元免配送费
	 */
	private Double noAgentFeeTotal;
	   
	/**
	 * 是否营业 1表示营业，0表示不营业
	 */
	private Integer isOpen;
	    
	/**
	 * 订单打包费
	 */
	private Double packingFee;
	   
	/**
	 * 餐厅的外部唯一标识
	 */
	private String openId;

	public String getAddressText() {
		return addressText;
	}

	public void setAddressText(String addressText) {
		this.addressText = addressText;
	}

	public String getGeo() {
		return geo;
	}

	public void setGeo(String geo) {
		this.geo = geo;
	}

	public Double getAgentFee() {
		return agentFee;
	}

	public void setAgentFee(Double agentFee) {
		this.agentFee = agentFee;
	}

	public String getCloseDescription() {
		return closeDescription;
	}

	public void setCloseDescription(String closeDescription) {
		this.closeDescription = closeDescription;
	}

	public String getDeliverDescription() {
		return deliverDescription;
	}

	public void setDeliverDescription(String deliverDescription) {
		this.deliverDescription = deliverDescription;
	}

	public String getDeliverGeoJson() {
		return deliverGeoJson;
	}

	public void setDeliverGeoJson(String deliverGeoJson) {
		this.deliverGeoJson = deliverGeoJson;
	}

	public String getDescription() {
		return description;
	}

	public void setDescription(String description) {
		this.description = description;
	}

	public String getName() {
		return name;
	}

	public void setName(String name) {
		this.name = name;
	}

	public Integer getIsBookable() {
		return isBookable;
	}

	public void setIsBookable(Integer isBookable) {
		this.isBookable = isBookable;
	}

	public String getOpenTime() {
		return openTime;
	}

	public void setOpenTime(String openTime) {
		this.openTime = openTime;
	}

	public String getPhone() {
		return phone;
	}

	public void setPhone(String phone) {
		this.phone = phone;
	}

	public String getPromotionInfo() {
		return promotionInfo;
	}

	public void setPromotionInfo(String promotionInfo) {
		this.promotionInfo = promotionInfo;
	}

	public String getLogoImageHash() {
		return logoImageHash;
	}

	public void setLogoImageHash(String logoImageHash) {
		this.logoImageHash = logoImageHash;
	}

	public Integer getInvoice() {
		return invoice;
	}

	public void setInvoice(Integer invoice) {
		this.invoice = invoice;
	}

	public Double getInvoiceMinAmount() {
		return invoiceMinAmount;
	}

	public void setInvoiceMinAmount(Double invoiceMinAmount) {
		this.invoiceMinAmount = invoiceMinAmount;
	}

	public Double getNoAgentFeeTotal() {
		return noAgentFeeTotal;
	}

	public void setNoAgentFeeTotal(Double noAgentFeeTotal) {
		this.noAgentFeeTotal = noAgentFeeTotal;
	}

	public Integer getIsOpen() {
		return isOpen;
	}

	public void setIsOpen(Integer isOpen) {
		this.isOpen = isOpen;
	}

	public Double getPackingFee() {
		return packingFee;
	}

	public void setPackingFee(Double packingFee) {
		this.packingFee = packingFee;
	}

	public String getOpenId() {
		return openId;
	}

	public void setOpenId(String openId) {
		this.openId = openId;
	}
	    
}
