package org.haier.ele.entity;

/**
 * 饿了么商品信息实体类
 */
public class Goods {
	
	private String itemId;//商品id
	
	private String categoryId;//商品分类id
	
	private String name;//商品名称
	
	private String description;//商品描述
	
	private String minPurchaseQuantity;//商品最小起购量
	
	private String unit;//商品单位
	
	private String specs;//商品规格，json字符串
	
	private String imageHash;//商品图片

	public String getItemId() {
		return itemId;
	}

	public void setItemId(String itemId) {
		this.itemId = itemId;
	}

	public String getImageHash() {
		return imageHash;
	}

	public void setImageHash(String imageHash) {
		this.imageHash = imageHash;
	}

	public String getCategoryId() {
		return categoryId;
	}

	public void setCategoryId(String categoryId) {
		this.categoryId = categoryId;
	}

	public String getName() {
		return name;
	}

	public void setName(String name) {
		this.name = name;
	}

	public String getDescription() {
		return description;
	}

	public void setDescription(String description) {
		this.description = description;
	}

	public String getMinPurchaseQuantity() {
		return minPurchaseQuantity;
	}

	public void setMinPurchaseQuantity(String minPurchaseQuantity) {
		this.minPurchaseQuantity = minPurchaseQuantity;
	}

	public String getUnit() {
		return unit;
	}

	public void setUnit(String unit) {
		this.unit = unit;
	}

	public String getSpecs() {
		return specs;
	}

	public void setSpecs(String specs) {
		this.specs = specs;
	}
	
}
