package org.haier.ele.service;

import org.haier.ele.entity.Goods;
import org.haier.shop.util.ShopsResult;

import eleme.openapi.sdk.api.exception.ServiceException;
import eleme.openapi.sdk.config.Config;
import eleme.openapi.sdk.oauth.response.Token;

public interface EleGoodsService {

	/**
	 * 添加商品
	 * @return
	 */
	public ShopsResult addGoods(Config config,Token token,Goods goods,String imgBase64) throws ServiceException;
	
	/**
	 * 修改商品
	 * @return
	 */
	public ShopsResult updateGoods(Config config,Token token,Goods goods,String imgBase64) throws ServiceException;
}
