package org.haier.ele.service.impl;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.haier.ele.entity.Goods;
import org.haier.ele.service.EleGoodsService;
import org.haier.shop.util.ShopsResult;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.alibaba.fastjson.JSONArray;

import eleme.openapi.sdk.api.entity.product.OSpec;
import eleme.openapi.sdk.api.enumeration.product.OItemCreateProperty;
import eleme.openapi.sdk.api.enumeration.product.OItemUpdateProperty;
import eleme.openapi.sdk.api.exception.ServiceException;
import eleme.openapi.sdk.api.service.ProductService;
import eleme.openapi.sdk.config.Config;
import eleme.openapi.sdk.oauth.response.Token;

@Service
@Transactional
public class EleGoodsServiceImpl implements EleGoodsService{
	
	@Override
	public ShopsResult addGoods(Config config, Token token, Goods goods,String imgBase64) throws ServiceException {
		ShopsResult shopsResult = new ShopsResult();
		ProductService productService = new ProductService(config, token);
		//上传图片
		if(imgBase64 != null && !imgBase64.equals("")){
			imgBase64 = imgBase64.substring(imgBase64.indexOf(",")+1, imgBase64.length());
			String imageHash = productService.uploadImage(imgBase64);
			goods.setImageHash(imageHash);
		}
		//添加商品
		Map<OItemCreateProperty,Object> properties = new HashMap<OItemCreateProperty,Object>();
		properties.put(OItemCreateProperty.name,goods.getName());
		properties.put(OItemCreateProperty.description,goods.getDescription());
		properties.put(OItemCreateProperty.imageHash,goods.getImageHash());
		properties.put(OItemCreateProperty.minPurchaseQuantity,goods.getMinPurchaseQuantity());
		properties.put(OItemCreateProperty.unit,goods.getUnit());
		
		List<OSpec> oSpecs = JSONArray.parseArray(goods.getSpecs(), OSpec.class);
		
		properties.put(OItemCreateProperty.specs,oSpecs);
		
		productService.createItem(Long.valueOf(goods.getCategoryId()), properties);
		shopsResult.setStatus(1);
		shopsResult.setMsg("成功");
		return shopsResult;
	}

	@Override
	public ShopsResult updateGoods(Config config, Token token, Goods goods,String imgBase64) throws ServiceException {
		ShopsResult shopsResult = new ShopsResult();
		ProductService productService = new ProductService(config, token);
		//上传图片
		if(imgBase64 != null && !imgBase64.equals("")){
			imgBase64 = imgBase64.substring(imgBase64.indexOf(",")+1, imgBase64.length());
			String imageHash = productService.uploadImage(imgBase64);
			goods.setImageHash(imageHash);
		}
		//修改商品
		Map<OItemUpdateProperty,Object> properties = new HashMap<OItemUpdateProperty,Object>();
		properties.put(OItemUpdateProperty.name,goods.getName());
		properties.put(OItemUpdateProperty.description,goods.getDescription());
		properties.put(OItemUpdateProperty.imageHash,goods.getImageHash());
		properties.put(OItemUpdateProperty.minPurchaseQuantity,goods.getMinPurchaseQuantity());
		properties.put(OItemUpdateProperty.unit,goods.getUnit());
		List<OSpec> oSpecs = JSONArray.parseArray(goods.getSpecs(), OSpec.class);
		
		properties.put(OItemUpdateProperty.specs,oSpecs);
		
		productService.updateItem(Long.valueOf(goods.getItemId()), Long.valueOf(goods.getCategoryId()), properties);
		shopsResult.setStatus(1);
		shopsResult.setMsg("成功");
		return shopsResult;
	}
	
}
