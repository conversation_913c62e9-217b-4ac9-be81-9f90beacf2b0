package org.haier.ele.service.impl;

import java.util.HashMap;
import java.util.Map;

import javax.annotation.Resource;

import org.haier.ele.dao.EleShopDao;
import org.haier.ele.entity.ShopPropperty;
import org.haier.ele.service.EleShopService;
import org.haier.shop.util.ShopsResult;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import eleme.openapi.sdk.api.enumeration.shop.OShopProperty;
import eleme.openapi.sdk.api.exception.ServiceException;
import eleme.openapi.sdk.api.service.ProductService;
import eleme.openapi.sdk.api.service.ShopService;
import eleme.openapi.sdk.config.Config;
import eleme.openapi.sdk.oauth.response.Token;

@Service
@Transactional
public class EleShopServiceImpl implements EleShopService{
	
	@Resource
	private EleShopDao authDao;

	@Override
	public Map<String, Object> getShopInfo(String shop_unique) {
		return authDao.getShopInfo(shop_unique);
	}

	@Override
	public int updateShopEleToken(Map<String, Object> params) {
		return authDao.updateShopEleToken(params);
	}

	@Override
	public ShopsResult updateShopDetail(Config config,Token token,String shopId,String shop_unique,ShopPropperty shopPropperty,String ImgBase64) throws ServiceException {
		ShopsResult shopsResult = new ShopsResult();
		//上传图片
		if(ImgBase64 != null && !ImgBase64.equals("")){
			ImgBase64 = ImgBase64.substring(ImgBase64.indexOf(",")+1, ImgBase64.length());
			ProductService productService = new ProductService(config, token);
			String logoImageHash = productService.uploadImage(ImgBase64);
			shopPropperty.setLogoImageHash(logoImageHash);
		}
		
		ShopService shopService = new ShopService(config, token);
		Map<OShopProperty,Object> properties = new HashMap<OShopProperty,Object>();
		properties.put(OShopProperty.addressText,shopPropperty.getAddressText());
		properties.put(OShopProperty.agentFee,shopPropperty.getAgentFee().intValue());
		properties.put(OShopProperty.closeDescription,shopPropperty.getCloseDescription());
		properties.put(OShopProperty.deliverDescription,shopPropperty.getDeliverDescription());
		properties.put(OShopProperty.description,shopPropperty.getDescription());
		properties.put(OShopProperty.name,shopPropperty.getName());
		properties.put(OShopProperty.isBookable,shopPropperty.getIsBookable());
		properties.put(OShopProperty.phone,shopPropperty.getPhone());
		properties.put(OShopProperty.promotionInfo,shopPropperty.getPromotionInfo());
		properties.put(OShopProperty.logoImageHash,shopPropperty.getLogoImageHash());
		properties.put(OShopProperty.invoice,shopPropperty.getInvoice());
		properties.put(OShopProperty.invoiceMinAmount,shopPropperty.getInvoiceMinAmount());
		properties.put(OShopProperty.noAgentFeeTotal,shopPropperty.getNoAgentFeeTotal());
		properties.put(OShopProperty.isOpen,shopPropperty.getIsOpen());
		properties.put(OShopProperty.packingFee,shopPropperty.getPackingFee());
		properties.put(OShopProperty.openId, shop_unique);
		shopService.updateShop(Long.valueOf(shopId), properties);
		shopsResult.setStatus(1);
		shopsResult.setMsg("成功");
		return shopsResult;
	}

	@Override
	public ShopsResult setBookingStatus(Config config, Token token,String shopId, String enabled, String maxBookingDays)throws ServiceException {
		ShopsResult shopsResult = new ShopsResult();
		ShopService shopService = new ShopService(config, token);
		boolean is_enabled = true;
		if(enabled.equals("1")){//0支持  1不支持
			is_enabled = false;
		}
		shopService.setBookingStatus(Long.valueOf(shopId), is_enabled, Integer.parseInt(maxBookingDays));
		shopsResult.setStatus(1);
		shopsResult.setMsg("成功");
		return shopsResult;
	}
}
