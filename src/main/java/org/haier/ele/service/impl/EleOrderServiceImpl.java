package org.haier.ele.service.impl;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;

import org.haier.ele.dao.EleOrderDao;
import org.haier.ele.entity.SaleList;
import org.haier.ele.entity.SaleListDetail;
import org.haier.ele.service.EleOrderService;
import org.haier.ele.util.EleConfig;
import org.haier.ele.util.Qutil;
import org.haier.meituan.util.MUtil;
import org.haier.shop.util.ShopsResult;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import eleme.openapi.sdk.api.entity.order.ODeliveryRecord;
import eleme.openapi.sdk.api.entity.order.OOrder;
import eleme.openapi.sdk.api.entity.order.ORefundOrder;
import eleme.openapi.sdk.api.entity.order.OReminder;
import eleme.openapi.sdk.api.entity.order.OrderList;
import eleme.openapi.sdk.api.entity.packs.ShopContract;
import eleme.openapi.sdk.api.enumeration.order.OInvalidateType;
import eleme.openapi.sdk.api.enumeration.order.ReplyReminderType;
import eleme.openapi.sdk.api.service.OrderService;
import eleme.openapi.sdk.api.service.PacksService;
import eleme.openapi.sdk.config.Config;
import eleme.openapi.sdk.oauth.response.Token;


/**
 * 描述：饿了么外卖订单service实现类
 * <AUTHOR>
 *
 */
@Service
@Transactional
public class EleOrderServiceImpl implements EleOrderService {
	
	Config config = new Config(EleConfig.isSandbox, EleConfig.key, EleConfig.secret);
	
	@Resource
	private EleOrderDao eleOrderDao;

	@Override
	@Transactional
	public ShopsResult addSaleList(Map<String ,Object> orderMap) {
		ShopsResult shopsResult = new ShopsResult();
		SaleList saleList = new SaleList();
		Long orderId = Long.valueOf(MUtil.strObject(orderMap.get("orderId")));
		String ele_token = eleOrderDao.getEleToken(MUtil.strObject(orderMap.get("orderId")));
		if(ele_token != null && !ele_token.equals("")){
			shopsResult.setStatus(1);
			shopsResult.setMsg("已经添加过订单");
			return shopsResult;
		}
		saleList.setShopUnique(Long.valueOf(MUtil.strObject(orderMap.get("openId"))));//店铺唯一标示
		saleList.setSaleListUnique(orderId);//订单编号
		saleList.setSaleListDatetime(Qutil.getTime());//订单生成时间
		saleList.setSaleListTotal(Double.valueOf(MUtil.strObject(orderMap.get("originalPrice"))));//销售单金额（商品）
		saleList.setCusUnique("");//用户的唯一性标识
		saleList.setSaleType(5);//订单类型（5-饿了么外卖订单）
		saleList.setSaleListName(MUtil.strObject(orderMap.get("consignee")));//收货人姓名
		String phones = MUtil.strObject(orderMap.get("phoneList"));
		saleList.setSaleListPhone(phones.split(",")[0]);//收货人联系电话
		saleList.setSaleListAddress(MUtil.strObject(orderMap.get("deliveryPoiAddress")));//订单送货地址
		saleList.setSaleListDelfee(Double.valueOf(MUtil.strObject(orderMap.get("deliverFee"))));// 外送费
		saleList.setSaleListDiscount(1.0);// 折扣率
		saleList.setSaleListState(3);// 已付款
		saleList.setSaleListHandlestate(1);// 发货状态；1-无效订单 2-未发货 3-已发货 4-已收货
		saleList.setSaleListPayment(7);//饿了么外卖
		saleList.setSaleListRemarks(MUtil.strObject(orderMap.get("description")));//订单备注
		saleList.setSaleListActuallyReceived(Double.valueOf(MUtil.strObject(orderMap.get("totalPrice"))));//订单实际收到金额

		List<Map<String ,Object>> groups = (List<Map<String, Object>>) orderMap.get("groups");
		List<SaleListDetail> saleDetailList = new ArrayList<SaleListDetail>();
		Integer saleListTotalCount = 0;//商品总数量
	    for (int i = 0; i < groups.size(); i++) {
	    	List<Map<String ,Object>> items = (List<Map<String, Object>>) groups.get(i).get("items");
	    	for(int j = 0; j < items.size(); j++){
	    		Map<String ,Object> oGoodsItem = items.get(j);
	    		saleListTotalCount = saleListTotalCount + Integer.parseInt(MUtil.strObject(oGoodsItem.get("quantity")));
	    		String goods_id = MUtil.strObject(oGoodsItem.get("extendCode"));// 商品id
		    	String food_name = MUtil.strObject(oGoodsItem.get("name"));//菜品名
		    	int quantity = Integer.parseInt(MUtil.strObject(oGoodsItem.get("quantity")));//商品数量
		    	String price = MUtil.strObject(oGoodsItem.get("price"));//商品原价
		    	Map<String, Object> goodsInfo = eleOrderDao.getGoodsInfo(goods_id);
		    	if(goodsInfo != null){
		    		SaleListDetail saleListDetail = new SaleListDetail();
			    	saleListDetail.setSaleListUnique(orderId);
			    	saleListDetail.setGoodsBarcode(MUtil.strObject(goodsInfo.get("goods_barcode")));
			    	saleListDetail.setGoodsName(food_name);
			    	saleListDetail.setGoodsPicturepath(MUtil.strObject(goodsInfo.get("goods_picturepath")));
			    	saleListDetail.setSaleListDetailCount(quantity);
			    	saleListDetail.setSaleListDetailPrice(Double.valueOf(price));
			    	//金额小计
			    	String sale_list_detail_subtotal = MUtil.strObject(oGoodsItem.get("total"));
			    	saleListDetail.setSaleListDetailSubtotal(Double.valueOf(sale_list_detail_subtotal));
			    	saleListDetail.setGoodsId(Long.valueOf(MUtil.strObject(goodsInfo.get("goods_id"))));
			    	saleListDetail.setGoodsPurprice(Double.valueOf(MUtil.strObject(goodsInfo.get("goods_in_price"))));
			    	
					saleDetailList.add(saleListDetail);
		    	}
	    	}
	    }
	    saleList.setSaleListTotalCount(saleListTotalCount);//商品总数量
	    int saleCount = eleOrderDao.addSaleList(saleList);
	    int detailCount = eleOrderDao.addSaleDetail(saleDetailList);
		
		if (saleCount > 0 && detailCount > 0) {
			shopsResult.setStatus(1);
			shopsResult.setMsg("成功");
		}
		
		return shopsResult;
	}
	
	@Override
	public ShopsResult updateSaleList(Map<String, Object> orderMap,int saleListHandlestate) {
		ShopsResult shopsResult = new ShopsResult();
		try {
			Long saleListUnique = Long.valueOf(MUtil.strObject(orderMap.get("orderId")));
			
			SaleList saleList = new SaleList();
			saleList.setSaleListHandlestate(saleListHandlestate);
			saleList.setSaleListUnique(saleListUnique);
			eleOrderDao.updatSaleList(saleList);
			shopsResult.setStatus(1);
			shopsResult.setMsg("成功");
		} catch (Exception e) {
			shopsResult.setStatus(0);
			shopsResult.setMsg("异常："+e.getMessage());
		}
		return shopsResult;
	}

	@Override
	public ShopsResult updatSaleListState(Map<String, Object> orderMap,int saleListState) {
		ShopsResult shopsResult = new ShopsResult();
		try {
			Long orderId = Long.valueOf(MUtil.strObject(orderMap.get("orderId")));
			Double totalPrice = Double.valueOf(MUtil.strObject(orderMap.get("totalPrice")));
			String reason = MUtil.strObject(orderMap.get("reason"));
			//修改订单付款状态
			SaleList saleList = new SaleList();
			saleList.setSaleListUnique(orderId);
			saleList.setRefundMoney(totalPrice);
			saleList.setSaleListState(saleListState);
			if(saleListState == 5){//申请退款
				saleList.setRefundReason(reason);
			}else if(saleListState == 6 || saleListState == 7){
				saleList.setRefuntOperateReason(reason);
			}
			
			eleOrderDao.updatSaleListState(saleList);
			shopsResult.setStatus(1);
			shopsResult.setMsg("成功");
		} catch (Exception e) {
			shopsResult.setStatus(0);
			shopsResult.setMsg("异常："+e.getMessage());
		}
		return shopsResult;
	}
	
	@Override
	public ShopsResult getEffectServicePackContract(String shopUnique) {
		ShopsResult shopsResult = new ShopsResult();
		try {
			Map<String ,Object> resultMap = eleOrderDao.getEleShopId(shopUnique);
			String ele_token = MUtil.strObject(resultMap.get("ele_token"));
			String ele_shopId = MUtil.strObject(resultMap.get("ele_shopId"));
			Token token = new Token();
			token.setAccessToken(ele_token);
			PacksService packsService = new PacksService(config, token);
			ShopContract shopContract = packsService.getEffectServicePackContract(Long.valueOf(ele_shopId));
			
			shopsResult.setData(shopContract.getContractTypeName());
			shopsResult.setStatus(1);
			shopsResult.setMsg("成功");
		} catch (Exception e) {
			shopsResult.setStatus(0);
			shopsResult.setMsg("异常："+e.getMessage());
		}
		return shopsResult;
	}
	
	@Override
	public ShopsResult getUnprocessOrders(String shopUnique) {
		ShopsResult shopsResult = new ShopsResult();
		try {
			Map<String ,Object> resultMap = eleOrderDao.getEleShopId(shopUnique);
			String ele_token = MUtil.strObject(resultMap.get("ele_token"));
			String ele_shopId = MUtil.strObject(resultMap.get("ele_shopId"));
			Token token = new Token();
			token.setAccessToken(ele_token);
			OrderService orderService = new OrderService(config, token);
			List<String> orderIds = orderService.getUnprocessOrders(Long.valueOf(ele_shopId));
			Map<String,OOrder> orders = orderService.mgetOrders(orderIds);
			//map的value值转list
			List<OOrder> orderList = new ArrayList<OOrder>();
			Iterator it = orders.keySet().iterator();
			while (it.hasNext()) {
				String key = (String) it.next();
				OOrder oOrder = orders.get(key);
				orderList.add(oOrder);
			}
			shopsResult.setData(orderList);
			shopsResult.setStatus(1);
			shopsResult.setMsg("成功");
		} catch (Exception e) {
			shopsResult.setStatus(0);
			shopsResult.setMsg("异常："+e.getMessage());
		}
		return shopsResult;
	}
	
	@Override
	public ShopsResult getOrder(String orderId) {
		ShopsResult ShopsResult = new ShopsResult();
		try {
			String ele_token = eleOrderDao.getEleToken(orderId);
			Token token = new Token();
			token.setAccessToken(ele_token);
			OrderService orderService = new OrderService(config, token);
			OOrder oOrder = orderService.getOrder(orderId);
			ShopsResult.setData(oOrder);
			ShopsResult.setStatus(1);
			ShopsResult.setMsg("成功");
		} catch (Exception e) {
			ShopsResult.setStatus(0);
			ShopsResult.setMsg("获取数据异常："+e.getMessage());
		}
		return ShopsResult;
	}


	@Override
	public ShopsResult confirmOrderLite(String orderId) {
		ShopsResult ShopsResult = new ShopsResult();
		try {
			String ele_token = eleOrderDao.getEleToken(orderId);
			Token token = new Token();
			token.setAccessToken(ele_token);
			OrderService orderService = new OrderService(config, token);
			orderService.confirmOrderLite(orderId);
			ShopsResult.setStatus(1);
			ShopsResult.setMsg("成功");
		} catch (Exception e) {
			ShopsResult.setStatus(0);
			ShopsResult.setMsg("异常："+e.getMessage());
		}
		return ShopsResult;
	}


	@Override
	public ShopsResult cancelOrderLite(String orderId,String type,String remark) {
		ShopsResult ShopsResult = new ShopsResult();
		try {
			String ele_token = eleOrderDao.getEleToken(orderId);
			Token token = new Token();
			token.setAccessToken(ele_token);
			OrderService orderService = new OrderService(config, token);
			orderService.cancelOrderLite(orderId, OInvalidateType.valueOf(type), remark);
			ShopsResult.setStatus(1);
			ShopsResult.setMsg("成功");
		} catch (Exception e) {
			ShopsResult.setStatus(0);
			ShopsResult.setMsg("异常："+e.getMessage());
		}
		return ShopsResult;
	}

	@Override
	public ShopsResult agreeRefundLite(String orderId) {
		ShopsResult ShopsResult = new ShopsResult();
		try {
			String ele_token = eleOrderDao.getEleToken(orderId);
			Token token = new Token();
			token.setAccessToken(ele_token);
			OrderService orderService = new OrderService(config, token);
			orderService.agreeRefundLite(orderId);
			ShopsResult.setStatus(1);
			ShopsResult.setMsg("成功");
		} catch (Exception e) {
			ShopsResult.setStatus(0);
			ShopsResult.setMsg("异常："+e.getMessage());
		}
		return ShopsResult;
	}
	
	@Override
	public ShopsResult disagreeRefundLite(String orderId,String reason) {
		ShopsResult ShopsResult = new ShopsResult();
		try {
			String ele_token = eleOrderDao.getEleToken(orderId);
			Token token = new Token();
			token.setAccessToken(ele_token);
			OrderService orderService = new OrderService(config, token);
			orderService.disagreeRefundLite(orderId,reason);
			ShopsResult.setStatus(1);
			ShopsResult.setMsg("成功");
		} catch (Exception e) {
			ShopsResult.setStatus(0);
			ShopsResult.setMsg("异常："+e.getMessage());
		}
		return ShopsResult;
	}
	
	@Override
	public ShopsResult getDeliveryStateRecord(String orderId) {
		ShopsResult ShopsResult = new ShopsResult();
		try {
			String ele_token = eleOrderDao.getEleToken(orderId);
			Token token = new Token();
			token.setAccessToken(ele_token);
			OrderService orderService = new OrderService(config, token);
			List<ODeliveryRecord> list = orderService.getDeliveryStateRecord(orderId);
			ShopsResult.setData(list);
			ShopsResult.setStatus(1);
			ShopsResult.setMsg("成功");
		} catch (Exception e) {
			ShopsResult.setStatus(0);
			ShopsResult.setMsg("异常："+e.getMessage());
		}
		return ShopsResult;
	}
	
	@Override
	public ShopsResult deliveryBySelfLite(String orderId) {
		ShopsResult ShopsResult = new ShopsResult();
		try {
			String ele_token = eleOrderDao.getEleToken(orderId);
			Token token = new Token();
			token.setAccessToken(ele_token);
			OrderService orderService = new OrderService(config, token);
			orderService.deliveryBySelfLite(orderId);
			ShopsResult.setStatus(1);
			ShopsResult.setMsg("成功");
		} catch (Exception e) {
			ShopsResult.setStatus(0);
			ShopsResult.setMsg("异常："+e.getMessage());
		}
		return ShopsResult;
	}
	
	@Override
	public ShopsResult noMoreDeliveryLite(String orderId) {
		ShopsResult ShopsResult = new ShopsResult();
		try {
			String ele_token = eleOrderDao.getEleToken(orderId);
			Token token = new Token();
			token.setAccessToken(ele_token);
			OrderService orderService = new OrderService(config, token);
			orderService.noMoreDeliveryLite(orderId);
			ShopsResult.setStatus(1);
			ShopsResult.setMsg("成功");
		} catch (Exception e) {
			ShopsResult.setStatus(0);
			ShopsResult.setMsg("异常："+e.getMessage());
		}
		return ShopsResult;
	}
	
	@Override
	public ShopsResult receivedOrderLite(String orderId) {
		ShopsResult ShopsResult = new ShopsResult();
		try {
			String ele_token = eleOrderDao.getEleToken(orderId);
			Token token = new Token();
			token.setAccessToken(ele_token);
			OrderService orderService = new OrderService(config, token);
			orderService.receivedOrderLite(orderId);
			ShopsResult.setStatus(1);
			ShopsResult.setMsg("成功");
		} catch (Exception e) {
			ShopsResult.setStatus(0);
			ShopsResult.setMsg("异常："+e.getMessage());
		}
		return ShopsResult;
	}
	
	@Override
	public ShopsResult replyReminder(String remindId,String orderId,String type,String content){
		ShopsResult ShopsResult = new ShopsResult();
		try {
			String ele_token = eleOrderDao.getEleToken(orderId);
			Token token = new Token();
			token.setAccessToken(ele_token);
			OrderService orderService = new OrderService(config, token);
			orderService.replyReminder(remindId, ReplyReminderType.valueOf(type), content);
			ShopsResult.setStatus(1);
			ShopsResult.setMsg("成功");
		} catch (Exception e) {
			ShopsResult.setStatus(0);
			ShopsResult.setMsg("异常："+e.getMessage());
		}
		return ShopsResult;
	}
	
	@Override
	public ShopsResult getRefundOrder(String orderId){
		ShopsResult ShopsResult = new ShopsResult();
		try {
			String ele_token = eleOrderDao.getEleToken(orderId);
			Token token = new Token();
			token.setAccessToken(ele_token);
			OrderService orderService = new OrderService(config, token);
			ORefundOrder oRefundOrder = orderService.getRefundOrder(orderId);
			ShopsResult.setData(oRefundOrder);
			ShopsResult.setStatus(1);
			ShopsResult.setMsg("成功");
		} catch (Exception e) {
			ShopsResult.setStatus(0);
			ShopsResult.setMsg("异常："+e.getMessage());
		}
		return ShopsResult;
	}
	
	@Override
	public ShopsResult callDelivery(String orderId,String fee){
		ShopsResult ShopsResult = new ShopsResult();
		try {
			String ele_token = eleOrderDao.getEleToken(orderId);
			Token token = new Token();
			token.setAccessToken(ele_token);
			OrderService orderService = new OrderService(config, token);
			orderService.callDelivery(orderId, Integer.parseInt(fee));
			ShopsResult.setStatus(1);
			ShopsResult.setMsg("成功");
		} catch (Exception e) {
			ShopsResult.setStatus(0);
			ShopsResult.setMsg("异常："+e.getMessage());
		}
		return ShopsResult;
	}
	
	@Override
	public ShopsResult cancelDelivery(String orderId){
		ShopsResult ShopsResult = new ShopsResult();
		try {
			String ele_token = eleOrderDao.getEleToken(orderId);
			Token token = new Token();
			token.setAccessToken(ele_token);
			OrderService orderService = new OrderService(config, token);
			orderService.cancelDelivery(orderId);
			ShopsResult.setStatus(1);
			ShopsResult.setMsg("成功");
		} catch (Exception e) {
			ShopsResult.setStatus(0);
			ShopsResult.setMsg("异常："+e.getMessage());
		}
		return ShopsResult;
	}

	@Override
	public ShopsResult getUnreplyReminders(String shopUnique) {
		ShopsResult ShopsResult = new ShopsResult();
		try {
			Map<String ,Object> resultMap = eleOrderDao.getEleShopId(shopUnique);
			String ele_token = MUtil.strObject(resultMap.get("ele_token"));
			String ele_shopId = MUtil.strObject(resultMap.get("ele_shopId"));
			Token token = new Token();
			token.setAccessToken(ele_token);
			OrderService orderService = new OrderService(config, token);
			List<OReminder> list = orderService.getUnreplyReminders(Long.valueOf(ele_shopId));
			List<Map<String ,Object>> resultList = new ArrayList<Map<String,Object>>(); 
			for(int i=0;i<list.size();i++){
				Map<String ,Object> map = new HashMap<String, Object>();
				map.put("reminderId", list.get(i).getReminderId());
				map.put("orderId", list.get(i).getOrderId());
				map.put("orderInfo", orderService.getOrder(list.get(i).getOrderId()));
				resultList.add(map);
			}
			ShopsResult.setData(resultList);
			ShopsResult.setStatus(1);
			ShopsResult.setMsg("成功");
		} catch (Exception e) {
			ShopsResult.setStatus(0);
			ShopsResult.setMsg("异常："+e.getMessage());
		}
		return ShopsResult;
	}
	
	@Override
	public ShopsResult getCancelOrders(String shopUnique) {
		ShopsResult ShopsResult = new ShopsResult();
		try {
			Map<String ,Object> resultMap = eleOrderDao.getEleShopId(shopUnique);
			String ele_token = MUtil.strObject(resultMap.get("ele_token"));
			String ele_shopId = MUtil.strObject(resultMap.get("ele_shopId"));
			Token token = new Token();
			token.setAccessToken(ele_token);
			OrderService orderService = new OrderService(config, token);
			List<String> orderIds = orderService.getCancelOrders(Long.valueOf(ele_shopId));
			Map<String,OOrder> orders = orderService.mgetOrders(orderIds);
			//map的value值转list
			List<OOrder> orderList = new ArrayList<OOrder>();
			Iterator it = orders.keySet().iterator();
			while (it.hasNext()) {
				String key = (String) it.next();
				OOrder oOrder = orders.get(key);
				orderList.add(oOrder);
			}
			ShopsResult.setData(orderList);
			ShopsResult.setStatus(1);
			ShopsResult.setMsg("成功");
		} catch (Exception e) {
			ShopsResult.setStatus(0);
			ShopsResult.setMsg("异常："+e.getMessage());
		}
		return ShopsResult;
	}
	
	@Override
	public ShopsResult getRefundOrders(String shopUnique) {
		ShopsResult ShopsResult = new ShopsResult();
		try {
			Map<String ,Object> resultMap = eleOrderDao.getEleShopId(shopUnique);
			String ele_token = MUtil.strObject(resultMap.get("ele_token"));
			String ele_shopId = MUtil.strObject(resultMap.get("ele_shopId"));
			Token token = new Token();
			token.setAccessToken(ele_token);
			OrderService orderService = new OrderService(config, token);
			List<String> orderIds = orderService.getRefundOrders(Long.valueOf(ele_shopId));
			Map<String,OOrder> orders = orderService.mgetOrders(orderIds);
			//map的value值转list
			List<OOrder> orderList = new ArrayList<OOrder>();
			Iterator it = orders.keySet().iterator();
			while (it.hasNext()) {
				String key = (String) it.next();
				OOrder oOrder = orders.get(key);
				orderList.add(oOrder);
			}
			ShopsResult.setData(orderList);
			ShopsResult.setStatus(1);
			ShopsResult.setMsg("成功");
		} catch (Exception e) {
			ShopsResult.setStatus(0);
			ShopsResult.setMsg("异常："+e.getMessage());
		}
		return ShopsResult;
	}
	
	@Override
	public ShopsResult getAllOrders(String shopUnique,int pageNo,int pageSize,String date) {
		ShopsResult ShopsResult = new ShopsResult();
		try {
			Map<String ,Object> resultMap = eleOrderDao.getEleShopId(shopUnique);
			String ele_token = MUtil.strObject(resultMap.get("ele_token"));
			String ele_shopId = MUtil.strObject(resultMap.get("ele_shopId"));
			Token token = new Token();
			token.setAccessToken(ele_token);
			OrderService orderService = new OrderService(config, token);
			OrderList orderList = orderService.getAllOrders(Long.valueOf(ele_shopId), pageNo, pageSize, date);
			ShopsResult.setData(orderList);
			ShopsResult.setStatus(1);
			ShopsResult.setMsg("成功");
		} catch (Exception e) {
			ShopsResult.setStatus(0);
			ShopsResult.setMsg("异常："+e.getMessage());
		}
		return ShopsResult;
	}
	
	@Override
	public ShopsResult getDeliveryFeeForCrowd(String orderId){
		ShopsResult ShopsResult = new ShopsResult();
		try {
			String ele_token = eleOrderDao.getEleToken(orderId);
			Token token = new Token();
			token.setAccessToken(ele_token);
			OrderService orderService = new OrderService(config, token);
			double fee = orderService.getDeliveryFeeForCrowd(orderId);
			ShopsResult.setData(fee);
			ShopsResult.setStatus(1);
			ShopsResult.setMsg("成功");
		} catch (Exception e) {
			ShopsResult.setStatus(0);
			ShopsResult.setMsg("异常："+e.getMessage());
		}
		return ShopsResult;
	}
	
	@Override
	public ShopsResult addDeliveryTipByOrderId(String orderId,int tip){
		ShopsResult ShopsResult = new ShopsResult();
		try {
			String ele_token = eleOrderDao.getEleToken(orderId);
			Token token = new Token();
			token.setAccessToken(ele_token);
			OrderService orderService = new OrderService(config, token);
			orderService.addDeliveryTipByOrderId(orderId, tip);
			ShopsResult.setStatus(1);
			ShopsResult.setMsg("成功");
		} catch (Exception e) {
			ShopsResult.setStatus(0);
			ShopsResult.setMsg("异常："+e.getMessage());
		}
		return ShopsResult;
	}
	
	@Override
	public ShopsResult setOrderPrepared(String orderId){
		ShopsResult ShopsResult = new ShopsResult();
		try {
			String ele_token = eleOrderDao.getEleToken(orderId);
			Token token = new Token();
			token.setAccessToken(ele_token);
			OrderService orderService = new OrderService(config, token);
			orderService.setOrderPrepared(orderId);
			ShopsResult.setStatus(1);
			ShopsResult.setMsg("成功");
		} catch (Exception e) {
			ShopsResult.setStatus(0);
			ShopsResult.setMsg("异常："+e.getMessage());
		}
		return ShopsResult;
	}

	@Override
	public List<Map<String, Object>> getGoodsList(String shopUnique) {
		return eleOrderDao.getGoodsList(shopUnique);
	}

}
