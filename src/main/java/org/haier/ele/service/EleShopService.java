package org.haier.ele.service;

import java.util.Map;

import org.haier.ele.entity.ShopPropperty;
import org.haier.shop.util.ShopsResult;

import eleme.openapi.sdk.api.exception.ServiceException;
import eleme.openapi.sdk.config.Config;
import eleme.openapi.sdk.oauth.response.Token;

public interface EleShopService {

	/**
	 * 获取商户信息
	 * @param shop_unique 店铺编号
	 * @return
	 */
	public Map<String ,Object> getShopInfo(String shop_unique);
	
	/**
	 * 修改商户饿了么授权token
	 * @param shop_unique 店铺编号
	 * @param ele_token 授权token
	 * @return
	 */
	public int updateShopEleToken(Map<String ,Object> params);
	
	
	/**
	 * 修改饿了么店铺信息
	 * @return
	 */
	public ShopsResult updateShopDetail(Config config,Token token,String shopId,String shop_unique,ShopPropperty shopPropperty,String ImgBase64) throws ServiceException;
	
	/**
	 * 店铺预订单设置
	 * @return
	 */
	public ShopsResult setBookingStatus(Config config,Token token,String shopId,String enabled,String maxBookingDays) throws ServiceException;
}
