package org.haier.ele.service;

import java.util.List;
import java.util.Map;

import org.haier.shop.util.ShopsResult;

/**
 * 描述:饿了么外卖订单service
 * <AUTHOR>
 *
 */
public interface EleOrderService {
	
	/**
	 * 添加一条销售信息
	 */
	public ShopsResult addSaleList(Map<String ,Object> orderMap);
	
	/**
	 * 修改订单信息
	 */
	public ShopsResult updateSaleList(Map<String ,Object> orderMap,int saleListHandlestate);
	
	/**
	 * 修改销售付款状态信息
	 * @param saleList:订单实体类
	 * @return int
	 */
	public ShopsResult updatSaleListState(Map<String, Object> orderMap,int saleListState);
	
	/**
	 * 查询店铺当前生效合同类型
	 * @return 
	 */
	public ShopsResult getEffectServicePackContract(String shopUnique); 
	
	/**
	 * 查询店铺未处理订单
	 * @return 
	 */
	public ShopsResult getUnprocessOrders(String shopUnique); 
	
	/**
	 * 获取饿了么订单详情
	 * @return 
	 */
	public ShopsResult getOrder(String orderId); 

	/**
	 * 商家确认订单
	 * @return 
	 */
	public ShopsResult confirmOrderLite(String orderId); 
	
	/**
	 * 商家取消订单
	 */
	public ShopsResult cancelOrderLite(String orderId,String type,String remark); 
	
	/**
	 * 商家同意退单/同意取消单
	 * @return 
	 */
	public ShopsResult agreeRefundLite(String orderId);
	
	/**
	 * 商家不同意退单/不同意取消单
	 * @return 
	 */
	public ShopsResult disagreeRefundLite(String orderId,String reason);
	
	/**
	 * 获取订单配送记录
	 * @return 
	 */
	public ShopsResult getDeliveryStateRecord(String orderId);
	
	/**
	 * 配送异常或者物流拒单后选择自行配送
	 * @return 
	 */
	public ShopsResult deliveryBySelfLite(String orderId);
	
	/**
	 * 配送异常或者物流拒单后选择不再配送
	 * @return 
	 */
	public ShopsResult noMoreDeliveryLite(String orderId);
	
	/**
	 * 订单确认送达
	 * @return 
	 */
	public ShopsResult receivedOrderLite(String orderId);
	
	/**
	 * 回复催单
	 * @return 
	 */
	public ShopsResult replyReminder(String remindId,String orderId,String type,String content);
	
	/**
	 * 获取订单退款信息
	 * @return 
	 */
	public ShopsResult getRefundOrder(String orderId);
	
	/**
	 * 呼叫配送
	 * @return 
	 */
	public ShopsResult callDelivery(String orderId,String fee);
	
	/**
	 * 取消呼叫配送
	 * @return 
	 */
	public ShopsResult cancelDelivery(String orderId);
	
	/**
	 * 获取店铺未回复的催单
	 * @return 
	 */
	public ShopsResult getUnreplyReminders(String shopUnique); 
	
	/**
	 * 查询店铺未处理的取消单
	 * @return 
	 */
	public ShopsResult getCancelOrders(String shopUnique);
	
	/**
	 * 查询店铺未处理的退单
	 * @return 
	 */
	public ShopsResult getRefundOrders(String shopUnique);
	
	/**
	 * 查询全部订单
	 * @return 
	 */
	public ShopsResult getAllOrders(String shopUnique,int pageNo,int pageSize,String date);
	
	/**
	 * 众包订单询价，获取配送费
	 * @return 
	 */
	public ShopsResult getDeliveryFeeForCrowd(String orderId);
	
	/**
	 * 订单加小费
	 * @return 
	 */
	public ShopsResult addDeliveryTipByOrderId(String orderId,int tip);
	
	/**
	 * 非自配送餐厅标记已出餐
	 * @return 
	 */
	public ShopsResult setOrderPrepared(String orderId);
	
	/**
	 * 根据店铺唯一编号获取店铺所有商品列表
	 * @param shop_unique:店铺唯一编号
	 */
	public List<Map<String ,Object>> getGoodsList(String shopUnique);
}
