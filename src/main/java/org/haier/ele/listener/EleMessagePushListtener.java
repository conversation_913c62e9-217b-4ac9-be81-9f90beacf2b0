package org.haier.ele.listener;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import javax.servlet.ServletContextEvent;
import javax.servlet.ServletContextListener;

import org.haier.ele.service.EleOrderService;
import org.haier.ele.util.EleConfig;
import org.haier.ele.util.Qutil;
import org.haier.meituan.util.MUtil;
import org.haier.shop.util.ShopsResult;
import org.springframework.web.context.support.WebApplicationContextUtils;

import eleme.openapi.ws.sdk.Bootstrap;
import eleme.openapi.ws.sdk.config.BusinessHandle;
import eleme.openapi.ws.sdk.config.Config;
import eleme.openapi.ws.sdk.config.ElemeSdkLogger;
import eleme.openapi.ws.sdk.entity.Account;
import eleme.openapi.ws.sdk.exception.UnableConnectionException;

/**
 * 饿了么-消息推送监听
 * 
 */
public class EleMessagePushListtener implements ServletContextListener{
	
	@Override
	public void contextInitialized(ServletContextEvent sce) {
		
		final EleOrderService eleOrderService = WebApplicationContextUtils.getWebApplicationContext(sce.getServletContext()).getBean(EleOrderService.class);
		
		Account account = new Account(EleConfig.key, EleConfig.secret);
        List<Account> accounts = new ArrayList<Account>();
        accounts.add(account);
        Config config = new Config(accounts,
        	new BusinessHandle() {
				@Override
				public boolean onMessage(String message) {
					//处理业务
					Map<String ,Object> pushMap = MUtil.jsonToMap(message);
					//System.out.println(pushMap);
					String type = MUtil.strObject(pushMap.get("type"));
					ShopsResult shopsResult = new ShopsResult();
					if(type != null && type.equals("10")){//订单生效，店铺可以看到新订单,收银系统创建订单
						Map<String ,Object> orderMap =  MUtil.jsonToMap(pushMap.get("message").toString());
						shopsResult = eleOrderService.addSaleList(orderMap);
					}
					if(type != null && type.equals("12")){//商户已经接单,修改订单状态为2 待发货
						Map<String ,Object> orderMap =  MUtil.jsonToMap(pushMap.get("message").toString());
						shopsResult = eleOrderService.updateSaleList(orderMap, 2);
					}
					if(type != null && type.equals("52")){//待分配配送员,修改订单状态为 7配送单待确认
						Map<String ,Object> orderMap =  MUtil.jsonToMap(pushMap.get("message").toString());
						shopsResult = eleOrderService.updateSaleList(orderMap, 7);
					}
					if(type != null && type.equals("53")){//已分配给配送员，配送员取餐中,修改订单状态为 2 待发货
						Map<String ,Object> orderMap =  MUtil.jsonToMap(pushMap.get("message").toString());
						shopsResult = eleOrderService.updateSaleList(orderMap, 2);
					}
					if(type != null && type.equals("55")){//配送员已取餐，配送中,修改订单状态为 3待收货
						Map<String ,Object> orderMap =  MUtil.jsonToMap(pushMap.get("message").toString());
						shopsResult = eleOrderService.updateSaleList(orderMap, 3);
					}
					if(type != null && type.equals("56")){//配送成功,修改订单状态为 4已完成
						Map<String ,Object> orderMap =  MUtil.jsonToMap(pushMap.get("message").toString());
						shopsResult = eleOrderService.updateSaleList(orderMap, 4);
					}
					if(type != null && type.equals("14") || type.equals("15") || type.equals("17")){//修改订单状态为5 已取消
						Map<String ,Object> orderMap =  MUtil.jsonToMap(pushMap.get("message").toString());
						shopsResult = eleOrderService.updateSaleList(orderMap, 5);
					}
					if(type != null && type.equals("18")){//订单完结,修改订单状态为4 已完成
						Map<String ,Object> orderMap =  MUtil.jsonToMap(pushMap.get("message").toString());
						shopsResult = eleOrderService.updateSaleList(orderMap, 4);
					}
					if(type != null && type.equals("30")){//用户申请退单,修改订单付款状态为5申请退款
						Map<String ,Object> orderMap =  MUtil.jsonToMap(pushMap.get("message").toString());
						shopsResult = eleOrderService.updatSaleListState(orderMap, 5);
					}
					if(type != null && type.equals("31")){//用户取消申请退单,修改订单付款状态为3已付款
						Map<String ,Object> orderMap =  MUtil.jsonToMap(pushMap.get("message").toString());
						shopsResult = eleOrderService.updatSaleListState(orderMap, 3);
					}
					if(type != null && type.equals("32") || type.equals("36")){//修改订单付款状态为7拒绝退款
						Map<String ,Object> orderMap =  MUtil.jsonToMap(pushMap.get("message").toString());
						shopsResult = eleOrderService.updatSaleListState(orderMap, 7);
					}
					if(type != null && type.equals("33") || type.equals("35")){//修改订单付款状态为6同意退款
						Map<String ,Object> orderMap =  MUtil.jsonToMap(pushMap.get("message").toString());
						shopsResult = eleOrderService.updatSaleListState(orderMap, 6);
					}
					if(shopsResult.getStatus()==1){
						return true;
					}else{
						return false;
					}
				}
			}, new ElemeSdkLogger() {
				@Override
				public void info(String message) {
					Qutil.getLogger().info("饿了么消息推送监听："+message);
				}
				
				@Override
				public void error(String message) {
					Qutil.getLogger().info("饿了么消息推送监听异常："+message);
				}
			});
        try {
            Bootstrap.start(config);
        } catch (UnableConnectionException e) {
            e.printStackTrace();
        }
	}
	
	@Override
	public void contextDestroyed(ServletContextEvent arg0) {
		
	}

}
