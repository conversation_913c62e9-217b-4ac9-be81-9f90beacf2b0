package org.haier.ele.util;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.logging.Logger;

import javax.annotation.Resource;

import org.haier.ele.dao.EleShopDao;
import org.haier.meituan.util.MUtil;

import eleme.openapi.sdk.config.Config;
import eleme.openapi.sdk.oauth.OAuthClient;
import eleme.openapi.sdk.oauth.response.Token;

/**
 * 定时任务，更新饿了么授权token
 *
 */
public class EleTokenUtil {
	
	private static final Logger logger = Logger.getGlobal();
	
	@Resource
	public  EleShopDao eleShopDao;
	
	/**
	 * 获取饿了么授权token失效时间为当天的记录，判断是否即将过期，对即将过期的token进行刷新
	 */
	public void updateEleAuthToken(){
		logger.info("定时任务，更新饿了么授权token开始");
		try {
			Config config = new Config(EleConfig.isSandbox, EleConfig.key, EleConfig.secret);
			OAuthClient client = new OAuthClient(config);
			List<Map<String ,Object>> list = eleShopDao.getAuthList();
			for(int i=0;i<list.size();i++){
				Map<String ,Object> resultMap = list.get(i);
				String shop_unique = MUtil.strObject(resultMap.get("shop_unique"));
				String ele_refresh_token = MUtil.strObject(resultMap.get("ele_refresh_token"));
				String ele_token_failure = MUtil.strObject(resultMap.get("ele_token_failure"));
				ele_token_failure = ele_token_failure.substring(0, ele_token_failure.indexOf("."));
				SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
				Date failure_date = sdf.parse(ele_token_failure);
				Date date = new Date();
				if(date.getTime()+60*60*1000 >= failure_date.getTime()){//当前时间+1小时 大于等于失效时间，刷新token
					Token token = client.getTokenByRefreshToken(ele_refresh_token);
					//更新商户授权token
		        	Map<String ,Object> params = new HashMap<String, Object>();
		        	params.put("ele_token", token.getAccessToken());
		        	params.put("shop_unique", shop_unique);
		        	params.put("ele_refresh_token", token.getRefreshToken());
		        	long seconds = date.getTime()+token.getExpires()*1000;
		        	String pattern = "yyyy-MM-dd HH:mm:ss";  
		        	SimpleDateFormat format = new SimpleDateFormat(pattern);  
		        	params.put("ele_token_failure", format.format(new Date(seconds)));
		        	eleShopDao.updateShopEleToken(params);
				}
			}
		} catch (ParseException e) {
			e.printStackTrace();
		}
		logger.info("定时任务，更新饿了么授权token结束");
	}
}
