package org.haier.ele.util;

import eleme.openapi.sdk.config.Config;

/**
 * 饿了么-工具类
 * 
 */
public class EleUtil {
	
	/**
     * 获取配置类
     * @param isSandbox 是否沙箱
     * @return
     */
    public static Config getConfig(boolean isSandbox){
    	Config config = null;
        if(isSandbox==true){
        	config= new Config(isSandbox,EleConfig.key,EleConfig.secret);
        }else{
            // TODO 填充正式环境数据
        }
        return config;
    }
}
