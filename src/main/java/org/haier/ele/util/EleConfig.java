package org.haier.ele.util;

/**
 * 饿了么-配置类
 * 
 */
public class EleConfig {
	
	// 设置是否沙箱环境
	public final static boolean isSandbox = true;
    // 设置APPKEY
    public final static String key = "ScmUbJrUwj";
    // 设置APPSECRET
    public final static String secret = "79455a4287782f3812e99514494b332608c14c3b";
    
    //授权url
    public final static String auth_url = "https://open-api-sandbox.shop.ele.me/authorize";//沙箱环境
    //public final static auth_url = "https://open-api.shop.ele.me/authorize";//正式环境	
    
    //获取访问令牌（access token）
    public final static String token = "https://open-api-sandbox.shop.ele.me/token";//沙箱环境
    //public final static String token = "https://open-api.shop.ele.me/token";//正式环境
    											
    public final static String redirect_uri = "http://buyhoo.cc/shop/ele/auth/callback.do";//回调地址
//    public final static String redirect_uri = "http://e0ce40ea.ngrok.io/shop/ele/auth/callback.do";//回调地址
}
